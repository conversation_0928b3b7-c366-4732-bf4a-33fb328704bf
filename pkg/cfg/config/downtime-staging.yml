# Time should be in HH:MM 24-hour format IST
DailyCsis:
  IsEnable: false
  StartTime: "00:30"
  EndTime: "04:30"
  Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."

# Time should be in HH:MM 24-hour format IST
ExtendedMonthlyCsis:
  IsEnable: false
  # StartsMonthsLastDay should be true if the startTime resides in the last date of the month or else it is assumed that startTime starts from 1st of the next month
  StartsMonthsLastDay: false
  StartTime: "00:30"
  EndTime: "04:30"
  Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."

# Time should be in HH:MM 24-hour format IST
ExtendedQuarterlyCsis:
  IsEnable: false
  # StartsMonthsLastDay should be true if the startTime resides in the last date of the month or else it is assumed that startTime starts from 1st of the next month
  StartsMonthsLastDay: false
  StartTime: "00:30"
  EndTime: "04:30"
  Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."

# Time should be in HH:MM 24-hour format IST
DailyBankDownTime:
  IsEnable: false
  StartTime: "00:30"
  EndTime: "04:30"
  Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."

# Time should be in HH:MM 24-hour format IST
ExtendedMonthlyBankDownTime:
  IsEnable: false
  # StartsMonthsLastDay should be true if the startTime resides in the last date of the month or else it is assumed that startTime starts from 1st of the next month
  StartsMonthsLastDay: false
  StartTime: "00:30"
  EndTime: "04:30"
  Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."

# Time should be in HH:MM 24-hour format IST
ExtendedQuarterlyBankDownTime:
  IsEnable: false
  # StartsMonthsLastDay should be true if the startTime resides in the last date of the month or else it is assumed that startTime starts from 1st of the next month
  StartsMonthsLastDay: false
  StartTime: "00:30"
  EndTime: "04:30"
  Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."

# Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
TimestampBasedBankDownTime:
  IsEnable: false
  StartTimestamp: "2021-11-01 15:04:05"
  EndTimestamp: "2021-11-02 15:04:05"
  Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."

# Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
TimestampBasedCsisDowntime:
  IsEnable: false
  StartTimestamp: "2021-11-01 15:04:05"
  EndTimestamp: "2021-11-02 15:04:05"
  Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."

# Checks if Csis should be fetched from HealthEngine or not
IsCsisEnabledFromHealthEngine: true
