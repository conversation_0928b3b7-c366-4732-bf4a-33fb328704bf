package producer

import "github.com/IBM/sarama"

type DummySyncProducer struct{}

func NewDummySyncProducer() *DummySyncProducer {
	mock := &DummySyncProducer{}
	return mock
}

func (m *DummySyncProducer) SendMessage(msg *sarama.ProducerMessage) (partition int32, offset int64, err error) {
	return 0, 0, nil
}

func (m *DummySyncProducer) SendMessages(msgs []*sarama.ProducerMessage) error {
	return nil
}

func (m *DummySyncProducer) Close() error {
	return nil
}

func (m *DummySyncProducer) TxnStatus() sarama.ProducerTxnStatusFlag {
	return 0
}

func (m *DummySyncProducer) IsTransactional() bool {
	return true
}

func (m *DummySyncProducer) BeginTxn() error {
	return nil
}

func (m *DummySyncProducer) CommitTxn() error {
	return nil
}

func (m *DummySyncProducer) AbortTxn() error {
	return nil
}

func (m *DummySyncProducer) AddOffsetsToTxn(offsets map[string][]*sarama.PartitionOffsetMetadata, groupId string) error {
	return nil
}

func (m *DummySyncProducer) AddMessageToTxn(msg *sarama.ConsumerMessage, groupId string, metadata *string) error {
	return nil
}
