package vendorapi

import (
	"errors"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
)

// GetStatusFromError translates error into rpc.Status
// Currently, only deadline-exceeded and context-cancelled error translations are supported
// Common errors returned from HTTPRequestHandler like vendor-timeouts, context-cancellations, etc. are mapped to their own status codes below
// Errors that are specific to Response interface implementations should be wrapped using rpc.StatusAsError
// These implementation-specific errors are then unwrapped to their corresponding status codes below using rpc.StatusFromErrorWithDefaultInternal
func GetStatusFromError(err error) *rpc.Status {
	if errors.Is(err, epifierrors.ErrVendorApiTimeout) {
		return rpc.StatusDeadlineExceeded()
	}

	if errors.Is(err, epifierrors.ErrContextCanceled) {
		return rpc.StatusCancelled()
	}

	// TODO(Brijesh): Reach out to respective codeowners to return status-wrapped-errors from within
	//  response interface implementations to ultimately remove this condition
	if errors.Is(err, epifierrors.ErrInvalidArgument) {
		return rpc.StatusInvalidArgument()
	}

	if IsRequestMarshallingError(err) {
		return rpc.StatusInvalidArgumentWithDebugMsg(err.Error())
	}

	if err != nil {
		return rpc.StatusFromErrorWithDefaultInternal(err)
	}

	return rpc.StatusOk()
}
