package vendorapi

import (
	"bytes"
	"context"
	"crypto/rsa"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/beevik/etree"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/rpc/code"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/vendorgateway"
	vendorsRedactor "github.com/epifi/be-common/api/vendors/redactor"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/crypto/jws"
	"github.com/epifi/be-common/pkg/epifierrors"
	dsig "github.com/epifi/be-common/pkg/goxmldsig"
	"github.com/epifi/be-common/pkg/hystrix"
	"github.com/epifi/be-common/pkg/logger"
	redactLogger "github.com/epifi/be-common/pkg/logger/redact_logger"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/vendorapi/config/genconf"
	"github.com/epifi/be-common/pkg/vendorapi/metrics"
)

type Signature string

const (
	None                          = ""
	XmlDigitalSignature Signature = "XmlDigitalSignature"
	logLimit                      = 30000
	reqMarshalErrTitle            = "error in vendorapi request marshaling"
)

const (
	ContentTypeText           = "text/plain"
	ContentTypeJSON           = "application/json"
	ContentTypeXML            = "application/xml"
	ContentTypeTextXML        = "text/xml"
	ContentTypeFormUrlEncoded = "application/x-www-form-urlencoded"
	ContentTypeOctetStream    = "application/octet-stream"
	ContentTypeTextHTML       = "text/html"
	ContentTypeImageJPEG      = "image/jpeg"
)

var (
	HttpCodeToGrpcCodeMapping = map[int]code.Code{
		http.StatusOK:                  code.Code_OK,
		http.StatusCreated:             code.Code_OK,
		http.StatusAccepted:            code.Code_OK,
		http.StatusNoContent:           code.Code_OK,
		http.StatusBadRequest:          code.Code_INVALID_ARGUMENT,
		http.StatusNotFound:            code.Code_NOT_FOUND,
		http.StatusUnauthorized:        code.Code_UNAUTHENTICATED,
		http.StatusForbidden:           code.Code_PERMISSION_DENIED,
		http.StatusNotImplemented:      code.Code_UNIMPLEMENTED,
		http.StatusServiceUnavailable:  code.Code_UNAVAILABLE,
		http.StatusTooManyRequests:     code.Code_RESOURCE_EXHAUSTED,
		http.StatusConflict:            code.Code_ALREADY_EXISTS,
		http.StatusInternalServerError: code.Code_INTERNAL,
	}
	HttpCodeToRpcStatusMapping = map[int]*rpc.Status{
		http.StatusOK:                  rpc.StatusOk(),
		http.StatusCreated:             rpc.StatusOk(),
		http.StatusAccepted:            rpc.StatusOk(),
		http.StatusNoContent:           rpc.StatusOk(),
		http.StatusBadRequest:          rpc.StatusInvalidArgument(),
		http.StatusNotFound:            rpc.StatusRecordNotFound(),
		http.StatusUnauthorized:        rpc.StatusUnauthenticated(),
		http.StatusForbidden:           rpc.StatusPermissionDenied(),
		http.StatusNotImplemented:      rpc.StatusUnimplemented(),
		http.StatusServiceUnavailable:  rpc.StatusUnavailable(),
		http.StatusTooManyRequests:     rpc.StatusResourceExhausted(),
		http.StatusConflict:            rpc.StatusAlreadyExists(),
		http.StatusInternalServerError: rpc.StatusInternal(),
	}
)

// All vendor specific code will need to define a method with this signature.
type SyncRequestFactory func(proto.Message) SyncRequest

// Request interface defines methods to be implemented by all the types for vendor API requests.
type Request interface {
	HTTPMethod() string
	URL() string
	GetResponse() Response
}

// VendorMetricsProvider interface should be implemented by requests that want to provide vendor api metrics-related information
type VendorMetricsProvider interface {
	// GetAPIIdentifier returns a URL template that uniquely identifies vendor API calls.
	// This method is required only when the URL() method returns a dynamic URL with path parameters.
	// Example: For a dynamic URL like "https://vendorname.com/api/v1/merchant/123", this would return "/api/v1/merchant/{id}"
	GetAPIIdentifier() string
}

// MetricsEvaluator is an interface to be implemented by response implementations
// to determine whether metrics should be triggered for a specific vendor status
type MetricsEvaluator interface {
	// EvaluateAndGetMetricErrorCode examines the vendor status and returns:
	// 1. A boolean indicating whether vendor API failure metrics should be triggered.
	// 2. The corresponding error code to be used in the metrics.
	//
	// Notes:
	// - All vendor API implementations must implement this interface to evaluate whether
	//   metrics should be triggered based on the vendor status.
	// - Each vendor API can define up to 50 distinct error codes (see MaxAllowedErrorCode).
	//   To stay within this limit, related vendor error codes with the same alerting behavior
	//   can be grouped together and mapped to a common error code.
	EvaluateAndGetMetricErrorCode(status *vendorgateway.VendorStatus) (shouldTrackMetric bool, errCode string)
}

type VendorStatus interface {
	GetVendorStatus() *vendorgateway.VendorStatus
}

// Loggable should be implemented by requests which wants to customize request & response logging in secure logs
type Loggable interface {
	// CanLogUnredactedEncryptedPayload returns true if it is ok to log unredacted encrypted request or response payload
	// to secure logs, false if secure logs redaction strategy is to be used before logging to secure logs.
	CanLogUnredactedEncryptedPayload() bool
}

// RequestURLRedactor should be implemented for customizing request URL redaction strategy before logging
// Note: URLs are not redacted when logging to "secure-logs"
type RequestURLRedactor interface {
	// RedactRequestURL strips PII information from the input URL
	RedactRequestURL(string) string
}

// RequestBodyRedactor should be implemented for customizing request body redaction strategy before logging
// Note: Requests are not redacted when logging to "secure-logs"
type RequestBodyRedactor interface {
	RedactRequestBody(ctx context.Context, requestBody []byte, contentType string) ([]byte, error)
}

// ResponseBodyRedactor should be implemented for customizing response body redaction strategy before logging
// Note: Responses are not redacted when logging to "secure-logs"
type ResponseBodyRedactor interface {
	RedactResponseBody(ctx context.Context, responseBody []byte, contentType string) ([]byte, error)
}

// LogRawDataRequest interface should be implemented by requests that want to control how the raw request is logged
// The override is required to prevent or customise logging of request body
type LogRawDataRequest interface {
	LogRawRequest(ctx context.Context, requestBody []byte, contentType string)
}

// LogRawDataResponse interface should be implemented by request that wants to control how the raw response is logged
// The override is used to prevent or customise logging of response body
type LogRawDataResponse interface {
	LogRawResponse(ctx context.Context, responseBody []byte, contentType string)
}

// SyncRequest is implemented by requests whose actual responses are returned in the same vendor API calls.
// This is opposite of AsyncRequest whose actual responses are actually returned by vendor
// through callbacks to vendor notification service.
type SyncRequest interface {
	Request
	Marshal() ([]byte, error)
}

// interface to be implemented by request that needs to be signed before sending it to vendors
// Deprecated: please use SecureExchange instead
type RequestSignature interface {
	// Returns the signing technique to be used in order to sign the request
	GetSigningTechnique() Signature
}

// interface to be implemented by requests and responses that use IV(initialization vector aka nonce) for encryption
// and decryption of request/response data. IV is an arbitrary string that is used in
// cryptographic communications.
type IVGetter interface {
	GetIV() string
}

// interface to be implemented by requests and responses that needs to be secured using a cryptic scheme
type SecureExchange interface {
	// TODO: Return error as well for the GetCryptor method
	GetCryptor() crypto.Cryptor
	GetRequestProcessingMethod() vendorgateway.RequestProcessingMethod
	GetResponseProcessingMethod() vendorgateway.ResponseProcessingMethod
}

type HeaderAdder interface {
	// Adds the required headers to the http request
	Add(r *http.Request) *http.Request
}

// Requests looking to override default content type header same as ContentTypeGetter
// should implement this.
type CustomRequestContentTypeHeaderGetter interface {
	// Overrides content type header in request to custom value
	CustomRequestContentTypeHeaderString() string
}

type setAuthentication interface {
	// Sets the authentication on the http request if required
	SetAuth(r *http.Request) *http.Request
}

// Requests looking to override default content type application/json
// should implement this. If Cryptor is set, then, text/plain is used.
type ContentTypeGetter interface {
	ContentTypeString() string
}

// Responses looking to override response content type or
// the default content type application/json.
type ResponseContentTypeGetter interface {
	GetResponseContentTypeString() string
}

// Deprecated: please use ResponseV2 instead
type Response interface {
	Unmarshal(b []byte) (proto.Message, error)
}

// Response interface defines methods to be implemented by all the types for vendor API responses.
type ResponseV2 interface {
	UnmarshalV2(ctx context.Context, b []byte) (proto.Message, error)
}

// interface to be implemented by responses that needs custom error handling for http errors
type ResponseWithErrorHandling interface {
	// handle response in case of http error
	HandleHttpError(ctx context.Context, httpStatusCode int, responseBody []byte) (proto.Message, error)
}

// handle http headers in response
type HandleResponseWithHeader interface {
	ResponseWithHeader(ctx context.Context, resp http.Header, responseBody []byte, signer ISigner) (proto.Message, error)
}

// HttpDoer is an interface to perform HTTP requests.
type HttpDoer interface {
	Do(*http.Request) (*http.Response, error)
}

// Interface which requests can implement to tell whether they need to re use the TCP connection by client or not
// By default go will try to re use TCP connection, in case server side is not suppporting this, requests can specify false
type RequestTcpConn interface {
	ReUseTcpConnection() bool
}

// HTTPRequestHandler orchestrates all outgoing http requests. Provides orchestration
// of request serialization with encryption if required, calling an http endpoint,
// logging and response deserialization with decryption if required.
type HTTPRequestHandler struct {
	client HttpDoer
	// Deprecated: please use cryptor injection through SecureExchange interface
	xmlSigner *dsig.SigningContext

	conf                *genconf.Config
	httpContentRedactor *httpcontentredactor.HTTPContentRedactor
	env                 string
}

// Instantiates a new HTTPRequestHandler. This requires an HttpClient and a xmlSigner
// The latter can be nil.
func New(cl HttpDoer, xmlSigner *dsig.SigningContext, h *httpcontentredactor.HTTPContentRedactor, conf *genconf.Config, env string) *HTTPRequestHandler {

	return &HTTPRequestHandler{
		client:              cl,
		xmlSigner:           xmlSigner,
		conf:                conf,
		httpContentRedactor: h,
		env:                 env,
	}

}

// RequestWithHeader defines interface for requests to Vendor Gateway
// This interface helps to fetch header and avoids reflection
// It assumes that each request proto buffer has a field named Header of type vendorgateway.RequestHeader
type RequestWithHeader interface {
	GetHeader() *vendorgateway.RequestHeader
}

// NewVendorRequest takes a request proto buffer and returns a vendorapi.SyncRequest
// for the Vendor indicated in the request proto buffer. It assumes that each
// request proto buffer has a field named Header that contains a Vendor field.
func NewVendorRequest(req RequestWithHeader, requestFactoryMap map[vendorgateway.Vendor]SyncRequestFactory) (SyncRequest, error) {
	v := req.GetHeader().GetVendor()
	vendor, ok := requestFactoryMap[v]
	if !ok {
		return nil, fmt.Errorf("invalid Vendor: %v", v)
	}
	// req is originally a proto.Message which implements RequestWithHeader interface
	// Hence type casting validation is skipped
	vendorReq := vendor(req.(proto.Message))
	if vendorReq == nil {
		return nil, fmt.Errorf("invalid request: %v", req)
	}
	return vendorReq, nil
}

// returns rpc status object corresponding to given http code
func GetRpcStatusFromHttpCode(ctx context.Context, httpStatus int) *rpc.Status {
	rpcStatus, found := HttpCodeToRpcStatusMapping[httpStatus]
	if !found {
		logger.Info(ctx, fmt.Sprintf("No RPC status found for the Http status code: %d", httpStatus))
		return rpc.StatusInternal()
	}
	return rpcStatus
}

// returns rpc status object with debug message corresponding to given http code
func GetRpcStatusFromHttpCodeWithDebugMsg(ctx context.Context, httpStatus int, debugMsg string) *rpc.Status {
	rpcStatus := GetRpcStatusFromHttpCode(ctx, httpStatus)
	rpcStatus.SetDebugMessage(debugMsg)
	return rpcStatus
}

// Handle implements the orchestration functionality of the HTTPRequestHandler.
// It deals with abstract Request and Response interfaces.
// nolint:funlen
func (h *HTTPRequestHandler) Handle(ctx context.Context, req SyncRequest) (resMessage proto.Message, err error) {
	if req == nil {
		err = fmt.Errorf("req is nil")
		return
	}

	uri, err := url.Parse(req.URL())
	if err != nil {
		err = fmt.Errorf("URL could not be parsed: %w", err)
		return
	}

	redactedURL := h.GetRedactedURL(ctx, req, uri)
	if customURLRedactor, ok := req.(RequestURLRedactor); ok {
		redactedURL = customURLRedactor.RedactRequestURL(req.URL())
	}

	// create request body and encrypt if needed
	requestBody, err := h.marshal(ctx, req, redactedURL)
	if err != nil {
		return
	}

	return handleWithCircuitBreaker(ctx, req, func(ctx context.Context) (proto.Message, error) {
		return h.handle(ctx, req, requestBody, redactedURL, uri)
	})
}

func (h *HTTPRequestHandler) GetRedactedURL(ctx context.Context, req Request, uri *url.URL) string {
	redactedURL := req.URL()
	redactedReq, redactionErr := h.httpContentRedactor.Redact(ctx, []byte(redactedURL), httpcontentredactor.ContentTypeURL, vendorsRedactor.Config)
	if redactionErr != nil {
		logger.Error(ctx, "failed to redact request url", zap.Error(redactionErr))
		redactedURL = getUrlWithoutQueryParams(uri)
	} else {
		redactedURL = string(redactedReq)
	}
	return redactedURL
}

func getUrlWithoutQueryParams(uri *url.URL) string {
	return fmt.Sprintf("%v://%v/%v", uri.Scheme, uri.Host, uri.Path)
}

func (h *HTTPRequestHandler) HandleWithMarshalledReq(ctx context.Context, req AsyncRequest, requestBody []byte, redactedUrl string, uri *url.URL) (resMessage proto.Message, err error) {
	return h.handle(ctx, req, requestBody, redactedUrl, uri)
}

//nolint:funlen
func (h *HTTPRequestHandler) handle(ctx context.Context, req Request, requestBody []byte, redactedUrl string, uri *url.URL) (proto.Message, error) {
	var (
		responseBody []byte
		response     *http.Response
		resp         proto.Message
		err          error
	)

	if req == nil {
		return nil, fmt.Errorf("req is nil")
	}

	env, _ := cfg.GetEnvironment()
	if cfg.IsProdEnv(env) && !strings.HasPrefix(req.URL(), "https://") {
		return nil, fmt.Errorf("invalid url for client api call. Only https is supported")
	}

	httpRequest, err := http.NewRequestWithContext(ctx, req.HTTPMethod(), uri.String(), bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("error encountered creating http httpRequest: %w", err)
	}
	// If request implements this interface
	if reqTcpConn, ok := req.(RequestTcpConn); ok {
		if !reqTcpConn.ReUseTcpConnection() {
			httpRequest.Close = true
		}
	}

	// Add headers to the request if the optional interface is present
	if requestWithHeader, present := req.(HeaderAdder); present {
		httpRequest = requestWithHeader.Add(httpRequest)
	}

	// Set authentication to the request if the optional interface is present
	if requestWithAuth, present := req.(setAuthentication); present {
		httpRequest = requestWithAuth.SetAuth(httpRequest)
	}

	// get content type. if content type is not set, default is
	// application/JSON for unencrypted request and text/plain for encrypted
	contentType := getRequestContentType(ctx, req)

	requestContentTypeHeader := contentType
	if reqWithCustomContentTypeHeader, present := req.(CustomRequestContentTypeHeaderGetter); present {
		requestContentTypeHeader = reqWithCustomContentTypeHeader.CustomRequestContentTypeHeaderString()
	}

	httpRequest.Header.Add("Content-Type", requestContentTypeHeader)

	if len(requestBody) < logLimit && canLogEncryptedRequestToSecureLogs(req) {
		uriBytes := []byte(uri.String())
		redactLogger.LogSecure(zap.InfoLevel, ctx,
			"Sending http request",
			[]*redactLogger.RedactionCandidateField{redactLogger.NewRedactionCandidateField(httpcontentredactor.ContentTypeURL, logger.URL, &uriBytes)},
			zap.String("method", req.HTTPMethod()), zap.Int(logger.LENGTH, len(requestBody)))
	}

	if h.conf != nil && !cfg.IsProdEnv(h.env) && h.conf.Flags().LogAllAPIParams() {
		logger.Info(ctx, "Sending http request", zap.String("method", req.HTTPMethod()), zap.String(logger.URL, redactedUrl),
			zap.String(logger.REQUEST, string(requestBody)))
	} else {
		// Log this only at Debug level in prod. Redacted requests are logged at info regardless and so are raw requests
		// in secure logs to aid in debugging.
		logger.Debug(ctx, "Sending http request", zap.String("method", req.HTTPMethod()), zap.String(logger.URL, redactedUrl),
			zap.Int(logger.LENGTH, len(requestBody)))
	}
	curTime := time.Now()
	response, err = h.client.Do(httpRequest)
	if err != nil {
		return handleRequestErrors(ctx, err, req, redactedUrl, h.conf)
	}
	// record vendor response time in case request implements instrumentation interface
	if instrumentVendorApi, ok := req.(metrics.InstrumentVendorApi); ok {
		if response != nil {
			instrumentVendorApi.RecordVendorApiResponseTime(time.Since(curTime), response.StatusCode)
		}
	}

	defer func() { _ = response.Body.Close() }()

	responseBody, err = ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, hystrix.NewCircuitOpenError(fmt.Errorf("error reading HTTP response body: %w", err))
	}

	if len(responseBody) < logLimit && canLogEncryptedResponseToSecureLogs(req.GetResponse()) {
		urlBytes := []byte(req.URL())
		redactLogger.LogSecure(zap.InfoLevel, ctx, "Got http response",
			[]*redactLogger.RedactionCandidateField{
				redactLogger.NewRedactionCandidateField(httpcontentredactor.ContentTypeURL, logger.URL, &urlBytes)}, zap.Int(logger.LENGTH, len(responseBody)))
	}
	if h.conf != nil && !cfg.IsProdEnv(h.env) && h.conf.Flags().LogAllAPIParams() {
		logger.Info(ctx, "Got http response", zap.String(logger.URL, redactedUrl), zap.String(logger.RESPONSE, string(responseBody)),
			zap.String(logger.STATUS, strconv.Itoa(response.StatusCode)), zap.Int(logger.LENGTH, len(responseBody)))
	} else {
		// Log at debug level in prod. Redacted response is logged additionally and raw responses are present in secure logs.
		// So this log do not add much value at info level
		logger.Debug(ctx, "Got http response", zap.String(logger.URL, redactedUrl), zap.Int(logger.LENGTH, len(responseBody)),
			zap.String(logger.STATUS, strconv.Itoa(response.StatusCode)), zap.Int(logger.LENGTH, len(responseBody)))
	}

	vendorApiIdentifier := getVendorApiIdentifier(req, h.conf)
	metrics.RecordVendorApiHttpCode(vendorApiIdentifier, response.StatusCode)
	if !GetRpcStatusFromHttpCode(ctx, response.StatusCode).IsSuccess() {
		redactLogger.LogSecure(zap.ErrorLevel, ctx, "Http Error in vendor gateway api",
			[]*redactLogger.RedactionCandidateField{redactLogger.NewRedactionCandidateField(contentType, logger.PAYLOAD, &responseBody)},
			zap.Int("ErrorCode", response.StatusCode), zap.String(logger.URL, req.URL()), zap.Int(logger.LENGTH, len(responseBody)))

		redactedResponseBody, _ := h.httpContentRedactor.Redact(ctx, responseBody, contentType, vendorsRedactor.Config)
		logger.Error(ctx, "Redacted raw response with non ok status code", zap.String(logger.URL, redactedUrl),
			zap.String(logger.STATUS, strconv.Itoa(response.StatusCode)), zap.String(logger.PAYLOAD, string(redactedResponseBody)),
			zap.String(logger.REFERENCE_ID, vendorApiIdentifier), zap.Int(logger.LENGTH, len(redactedResponseBody)))

		return h.handleHttpError(ctx, req, response.StatusCode, responseBody)
	}

	resp, err = h.useResponseWithHeader(ctx, req, response.Header, responseBody)
	if err != nil {
		logger.Error(ctx, "error in using response with header", zap.Error(err))
		return resp, err
	}

	if resp, err = h.unmarshal(ctx, req, responseBody, redactedUrl); err != nil {
		return nil, err
	}

	if h.conf != nil && !cfg.IsProdEnv(h.env) && h.conf.Flags().LogAllAPIParams() {
		logger.Info(ctx, "proto response after unmarshalling http response", zap.String("method", req.HTTPMethod()), zap.String(logger.URL, redactedUrl),
			zap.Any(logger.RESPONSE, resp))
	}
	return resp, nil
}

// https://stackoverflow.com/questions/23494950/specifically-check-for-timeout-error/23497404
// This function returns true all timeout errors including the value context.DeadlineExceeded.
// That value satisfies the net.Error interface and has a Timeout method that always returns true.
func isTimeoutError(err error) bool {
	var nwErr net.Error
	ok := errors.As(err, &nwErr)
	return ok && nwErr.Timeout()
}

func isContextCancellationError(err error) bool {
	return errors.Is(err, context.Canceled)
}

func handleRequestErrors(ctx context.Context, err error, req Request, redactedUrl string, config *genconf.Config) (proto.Message, error) {
	if isTimeoutError(err) {
		vendorApiIdentifier := getVendorApiIdentifier(req, config)
		logger.Error(ctx, "vendor api timeout", zap.String("url", redactedUrl), zap.String(logger.REFERENCE_ID, vendorApiIdentifier))
		metrics.RecordVendorApiTimeout(vendorApiIdentifier)
		return nil, hystrix.NewCircuitOpenError(epifierrors.ErrVendorApiTimeout)
	}
	if isContextCancellationError(err) {
		return nil, epifierrors.ErrContextCanceled
	}

	return nil, hystrix.NewCircuitOpenError(fmt.Errorf("error encountered calling http endpoint: %w", err))
}

// getResponseContentType deduces the content type based on the
// implementation of ResponseContentTypeGetter Interface by the request
// If interface not implemented, getRequestContentType function is invoked which returns request content type
// If no request content type is foun, application/JSON is used for unencrypted request and text/plain for encrypted requests
func getResponseContentType(ctx context.Context, req Request) string {
	if responseContentTypeGetter, ok := req.(ResponseContentTypeGetter); ok {
		if ct := responseContentTypeGetter.GetResponseContentTypeString(); ct != "" {
			logger.Debug(ctx, fmt.Sprintf("Setting content type of response to %v", ct))
			return ct
		}
	}

	return getRequestContentType(ctx, req)
}

// getRequestContentType deduces the content type based on Cryptor and
// implementation of ContentTypeGetter Interface by the request.
// If interface not implemented, defaults to application/JSON for
// unencrypted request. For encrypted requests, the content type is text/plain.
func getRequestContentType(ctx context.Context, req Request) string {
	if contentTypeGetter, ok := req.(ContentTypeGetter); ok {
		if ct := contentTypeGetter.ContentTypeString(); ct != "" {
			logger.Debug(ctx, fmt.Sprintf("Setting content type of request to %v", ct))
			return ct
		}
	}

	if _, ok := req.(SecureExchange); ok {
		logger.Info(ctx, fmt.Sprintf("Using default content type of request as %v", ContentTypeText))
		return ContentTypeText
	}

	logger.Debug(ctx, fmt.Sprintf("Using default content type of request as %v", ContentTypeJSON))
	return ContentTypeJSON
}

// marshalSyncReq orchestrates sync request serialisation
func (h *HTTPRequestHandler) marshal(c context.Context, req SyncRequest, redactedUrl string) ([]byte, error) {
	requestBody, err := req.Marshal()
	if err != nil {
		return nil, fmt.Errorf("%s: %w", reqMarshalErrTitle, err)
	}
	return h.processRequestBody(c, req, requestBody, redactedUrl)
}

// processRequestBody checks the request nature and if the cryptor is set,
// it additionally encrypts and signs the request body
func (h *HTTPRequestHandler) processRequestBody(c context.Context, req Request, requestBody []byte, redactedUrl string) ([]byte, error) {
	// get content type. if content type is not set, default is
	// application/JSON for unencrypted request and text/plain for encrypted
	contentType := getRequestContentType(c, req)

	if reqLog, ok := req.(LogRawDataRequest); ok {
		reqLog.LogRawRequest(c, requestBody, contentType)
	} else {
		logRawRequestToSecureLogs(c, req, contentType, requestBody)
	}
	// Logging redacted request body
	var (
		redactedRequestBody []byte
		redactionErr        error
	)
	if reqBodyRedactor, ok := req.(RequestBodyRedactor); ok {
		redactedRequestBody, redactionErr = reqBodyRedactor.RedactRequestBody(c, requestBody, contentType)
	} else {
		redactedRequestBody, redactionErr = h.httpContentRedactor.Redact(c, requestBody, contentType, vendorsRedactor.Config)
	}
	if redactionErr != nil {
		logger.Error(c, "failed to redact request body", zap.Error(redactionErr), zap.String(logger.CONTENT_TYPE, contentType))
		// TODO: remove this after this is completed - https://monorail.pointz.in/p/fi-app/issues/detail?id=61941
	} else if h.conf == nil || h.conf.RedactedRawRequestLogExceptionList() == nil || !lo.Contains(h.conf.RedactedRawRequestLogExceptionList(), req.URL()) {
		logger.Info(c, "Redacted raw request", zap.String("method", req.HTTPMethod()),
			zap.String(logger.URL, redactedUrl), zap.String(logger.PAYLOAD, string(redactedRequestBody)), zap.String(logger.CONTENT_TYPE, contentType), zap.Int(logger.LENGTH,
				len(redactedRequestBody)))
	}

	if secureRequest, ok := req.(SecureExchange); ok {
		secureRequestBody, err := h.processSecureRequest(c, requestBody, secureRequest)
		if err != nil {
			return nil, err
		}
		// TODO(nitesh): return statement can be removed once below methods are fully migrated
		return secureRequestBody, nil
	}

	if reqSignature, ok := req.(RequestSignature); ok {
		signedRequestBody, err := h.signPayload(reqSignature, requestBody)
		if err != nil {
			return nil, err
		}

		return signedRequestBody, nil
	}

	return requestBody, nil
}

// handleError orchestrates response de-serialisation. if the cryptor is set,
// it additionally decrypts and verifies the responseBody
func (h *HTTPRequestHandler) handleHttpError(ctx context.Context, req Request, httpStatusCode int, responseBody []byte) (proto.Message, error) {
	var (
		err error
	)

	// get content type. if content type is not set, default is
	// application/JSON for unencrypted request and text/plain for encrypted
	contentType := getResponseContentType(ctx, req)

	if secureRequest, ok := req.(SecureExchange); ok {
		responseBody, err = h.processSecureResponse(ctx, responseBody, secureRequest)
		if err != nil {
			// Log the response payload to secure logs to aid in debugging of cryptor error
			if reqLog, ok := req.(LogRawDataResponse); ok {
				reqLog.LogRawResponse(ctx, responseBody, contentType)
			} else {
				logRawResponseToSecureLogs(ctx, req, contentType, responseBody)
			}
			// being pessimistic and opening the circuit here to protect the system
			// even if response decryption fails
			return nil, hystrix.NewCircuitOpenError(err)
		}
	}

	if responseWithErrorHandling, ok := req.GetResponse().(ResponseWithErrorHandling); ok {
		return responseWithErrorHandling.HandleHttpError(ctx, httpStatusCode, responseBody)
	}

	return nil, hystrix.NewCircuitOpenError(fmt.Errorf("HTTP status not OK. Received %d", httpStatusCode))
}

// signer to sign payload and verify using public key
// for example in AA requests, signature is generated and sent in http request header
// and verified in http response header on receiving response
type ISigner interface {
	GetSigner() *jws.RsaJwsSigner
	GetPublicKey() *rsa.PublicKey
	IsSignatureNeeded() bool
}

type Signer struct {
	Sig          *jws.RsaJwsSigner
	PublicKey    *rsa.PublicKey
	IsSignNeeded bool
}

func (s *Signer) GetSigner() *jws.RsaJwsSigner {
	if s == nil {
		return nil
	}
	return s.Sig
}
func (s *Signer) GetPublicKey() *rsa.PublicKey {
	if s == nil {
		return nil
	}
	return s.PublicKey
}

func (s *Signer) IsSignatureNeeded() bool {
	if s == nil {
		return false
	}
	return s.IsSignNeeded
}

// Returns nil, nil because unmarshal is called after this
func (h *HTTPRequestHandler) useResponseWithHeader(ctx context.Context, req Request, resp http.Header, responseBody []byte) (proto.Message, error) {
	if handleResponseHeaders, present := req.GetResponse().(HandleResponseWithHeader); present {
		signer, ok := req.(ISigner)
		if ok {
			return handleResponseHeaders.ResponseWithHeader(ctx, resp, responseBody, signer)
		}
		return handleResponseHeaders.ResponseWithHeader(ctx, resp, responseBody, nil)
	}
	return nil, nil
}

// unmarshall orchestrates response de-serialisation. if the cryptor is set,
// it additionally decrypts and verifies the responseBody
func (h *HTTPRequestHandler) unmarshal(c context.Context, req Request, responseBody []byte, redactedUrl string) (proto.Message, error) {
	var (
		protoRes proto.Message
		err      error
	)

	// get content type. if content type is not set, default is
	// application/JSON for unencrypted request and text/plain for encrypted
	contentType := getResponseContentType(c, req)

	if secureRequest, ok := req.(SecureExchange); ok {
		responseBody, err = h.processSecureResponse(c, responseBody, secureRequest)
		if err != nil {
			// Log the response payload to secure logs to aid in debugging of cryptor error
			if reqLog, ok := req.(LogRawDataResponse); ok {
				reqLog.LogRawResponse(c, responseBody, contentType)
			} else {
				logRawResponseToSecureLogs(c, req, contentType, responseBody)
			}
			return nil, err
		}
	}

	if reqLog, ok := req.(LogRawDataResponse); ok {
		reqLog.LogRawResponse(c, responseBody, contentType)
	} else {
		logRawResponseToSecureLogs(c, req, contentType, responseBody)
	}

	// Logging redacted response body
	var (
		redactedResponseBody []byte
		redactionErr         error
		unmarshalErr         error
	)

	// redaction of response body
	if resBodyRedactor, ok := req.GetResponse().(ResponseBodyRedactor); ok {
		redactedResponseBody, redactionErr = resBodyRedactor.RedactResponseBody(c, responseBody, contentType)
	} else {
		redactedResponseBody, redactionErr = h.httpContentRedactor.Redact(c, responseBody, contentType, vendorsRedactor.Config)
	}

	// Get the response handler from the request and determine which unmarshal method to use
	// based on whether it implements the newer ResponseV2 interface or the deprecated Response interface
	callbackResp := req.GetResponse()
	callbackRespV2, ok := callbackResp.(ResponseV2)

	if ok {
		// Use the newer UnmarshalV2 method which accepts context
		protoRes, unmarshalErr = callbackRespV2.UnmarshalV2(c, responseBody)
	} else {
		// Fall back to the deprecated Unmarshal method
		protoRes, unmarshalErr = callbackResp.Unmarshal(responseBody)
	}

	// evaluate metric trigger logic and get metric error code
	shouldTriggerMetrics, vendorErrorCode := evaluateAndGetMetricErrorCode(protoRes, req)
	vendorApiIdentifier := getVendorApiIdentifier(req, h.conf)

	// Log the redacted response body
	if redactionErr != nil {
		logger.Error(c, "failed to redact response body", zap.Error(redactionErr), zap.String(logger.CONTENT_TYPE, contentType), zap.String(logger.REFERENCE_ID, vendorApiIdentifier),
			zap.String(logger.STATUS, vendorErrorCode))
	} else if h.conf == nil || h.conf.RedactedRawResponseLogExceptionList() == nil || !lo.Contains(h.conf.RedactedRawResponseLogExceptionList(), req.URL()) {
		logger.Info(c, "Redacted raw response", zap.String(logger.URL, redactedUrl), zap.String(logger.CONTENT_TYPE, contentType),
			zap.String(logger.PAYLOAD, string(redactedResponseBody)), zap.String(logger.REFERENCE_ID, vendorApiIdentifier),
			zap.String(logger.STATUS, vendorErrorCode))
	}

	if shouldTriggerMetrics && ShouldMonitorErrorCode(vendorApiIdentifier, vendorErrorCode) {
		metrics.RecordVendorApiCustomError(vendorApiIdentifier, vendorErrorCode)
	}

	if unmarshalErr != nil {
		if redactedResponseBody == nil {
			return nil, fmt.Errorf("could not unmarshal response: %w", unmarshalErr)
		}
		return nil, fmt.Errorf("could not unmarshal response, %s: %w", string(redactedResponseBody), unmarshalErr)
	}

	return protoRes, nil
}

// signPayload signs the request payload before sending it to the vendors
// Deprecated: please secure request interface to inject cryptor
func (h *HTTPRequestHandler) signPayload(reqSignature RequestSignature, payload []byte) ([]byte, error) {
	switch reqSignature.GetSigningTechnique() {
	case None:
		return payload, nil

	// TODO(nitesh): define a cryptor for this xml signature and do the refactor once we have defined
	// API specific encryption approach in VG
	case XmlDigitalSignature:
		doc := etree.NewDocument()
		err := doc.ReadFromBytes(payload)
		if err != nil {
			return nil, fmt.Errorf("failed to parse xml payload: %w", err)
		}

		signedPayload, err := h.xmlSigner.SignEnveloped(doc.Root())
		if err != nil {
			return nil, fmt.Errorf("failed to sign the xml payload: %w", err)
		}

		doc = etree.NewDocument()
		doc.SetRoot(signedPayload)
		return doc.WriteToBytes()
	default:
		return nil, fmt.Errorf("unknown signing technique: %s", reqSignature.GetSigningTechnique())
	}
}

// processSecureRequest secures a request with a cryptic scheme defined before sending to the vendors
// to be used for requests that needs server-client communication validation, authorization and integrity
// nolint:dupl
func (h *HTTPRequestHandler) processSecureRequest(ctx context.Context, requestBody []byte, secureRequest SecureExchange) ([]byte, error) {
	var (
		securedRequest []byte
		err            error
		iv             string
	)

	// IV(initialization vector aka nonce) is an arbitary number that is used in cryptographic communications
	if ivGetter, ok := secureRequest.(IVGetter); ok {
		iv = ivGetter.GetIV()
	}

	switch secureRequest.GetRequestProcessingMethod() {
	case vendorgateway.RequestProcessingMethod_ENCRYPT:
		securedRequest, err = secureRequest.GetCryptor().Encrypt(ctx, requestBody, iv)
	case vendorgateway.RequestProcessingMethod_SIGN:
		securedRequest, err = secureRequest.GetCryptor().Sign(ctx, requestBody, iv)
	case vendorgateway.RequestProcessingMethod_SIGN_AND_ENCRYPT:
		securedRequest, err = secureRequest.GetCryptor().EncryptAndSign(ctx, requestBody, iv)
	default:
		return nil, fmt.Errorf("unknown secure request processing method: %s", secureRequest.GetRequestProcessingMethod())
	}

	if err != nil {
		return nil, fmt.Errorf("failed to process secure request: %w", err)
	}

	return securedRequest, nil
}

// processSecureResponse validates incoming response based on request defined response processing scheme
// It either accepts or rejects the message's claim to authenticity.
// nolint:dupl
func (h *HTTPRequestHandler) processSecureResponse(ctx context.Context, responseBody []byte, secureRequest SecureExchange) ([]byte, error) {
	var (
		verifiedResponse []byte
		err              error
		iv               string
	)

	// IV(initialization vector aka nonce) is an arbitary number that is used in cryptographic communications
	if ivGetter, ok := secureRequest.(IVGetter); ok {
		iv = ivGetter.GetIV()
	}

	switch secureRequest.GetResponseProcessingMethod() {
	case vendorgateway.ResponseProcessingMethod_VERIFY:
		// TODO(Nitesh/vivek): Since verify interface signature is changed and now it return bool value, discuss impact here
		_, err = secureRequest.GetCryptor().Verify(ctx, responseBody, iv)
	case vendorgateway.ResponseProcessingMethod_DECRYPT:
		verifiedResponse, err = secureRequest.GetCryptor().Decrypt(ctx, responseBody, iv)
	case vendorgateway.ResponseProcessingMethod_DECRYPT_AND_VERIFY:
		verifiedResponse, err = secureRequest.GetCryptor().DecryptAndVerify(ctx, responseBody, iv)
	default:
		return nil, fmt.Errorf("unknown secure response processing method: %s", secureRequest.GetResponseProcessingMethod())
	}

	if err != nil {
		return nil, fmt.Errorf("failed to process secure response: %w", err)
	}

	return verifiedResponse, nil
}

func canLogEncryptedRequestToSecureLogs(req Request) bool {
	// if request implements Loggable interface and does not suggest logging
	v, ok := req.(Loggable)
	if ok {
		return v.CanLogUnredactedEncryptedPayload()
	}
	// default value is true
	return true
}

func canLogEncryptedResponseToSecureLogs(res Response) bool {
	// if response implements Loggable interface and does not suggest logging
	v, ok := res.(Loggable)
	if ok {
		return v.CanLogUnredactedEncryptedPayload()
	}
	// default value is true
	return true
}

func logRawRequestToSecureLogs(ctx context.Context, req Request, contentType string, body []byte) {
	urlBytes := []byte(req.URL())
	redactLogger.LogSecure(zap.InfoLevel, ctx, "Raw request:",
		[]*redactLogger.RedactionCandidateField{redactLogger.NewRedactionCandidateField(httpcontentredactor.ContentTypeURL, logger.URL, &urlBytes),
			redactLogger.NewRedactionCandidateField(contentType, logger.PAYLOAD, &body)}, zap.Int(logger.LENGTH, len(body)))
}

func logRawResponseToSecureLogs(ctx context.Context, req Request, contentType string, body []byte) {
	urlBytes := []byte(req.URL())
	redactLogger.LogSecure(zap.InfoLevel, ctx, "Raw response:",
		[]*redactLogger.RedactionCandidateField{redactLogger.NewRedactionCandidateField(httpcontentredactor.ContentTypeURL, logger.URL, &urlBytes),
			redactLogger.NewRedactionCandidateField(contentType, logger.PAYLOAD, &body)}, zap.Int(logger.LENGTH, len(body)))
}

func IsRequestMarshallingError(err error) bool {
	if err == nil {
		return false
	}
	return strings.Contains(err.Error(), reqMarshalErrTitle)
}

func getVendorApiIdentifier(req Request, conf *genconf.Config) string {
	if lo.Contains(conf.APIMetricsEnabledURLList(), req.URL()) {
		return req.URL()
	}

	// fallback check for vendor api identifier (in case when url have dynamic param)
	if metricsProvider, ok := req.(VendorMetricsProvider); ok {
		apiTemplate := metricsProvider.GetAPIIdentifier()
		// checking if apiTemplate is whitelisted in config
		if lo.Contains(conf.APIMetricsEnabledURLList(), apiTemplate) {
			return apiTemplate
		}
		return ""
	}
	return ""
}

func evaluateAndGetMetricErrorCode(protoResp proto.Message, req Request) (bool, string) {
	if vendorStatus, ok := protoResp.(VendorStatus); ok {
		// First check if response implements MetricsEvaluator interface
		if metricsEvaluator, ok := req.GetResponse().(MetricsEvaluator); ok {
			// Use the evaluator to determine if metrics should be triggered
			return metricsEvaluator.EvaluateAndGetMetricErrorCode(vendorStatus.GetVendorStatus())
		}
	}
	return false, ""
}
