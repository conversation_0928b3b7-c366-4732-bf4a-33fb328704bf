package vendorapi

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/hystrix"
	"github.com/epifi/be-common/pkg/logger"
)

// RequestWithCircuitBreaker interface defines methods to be implemented by all the types for vendor API requests
// that needs to be run within circuit
type RequestWithCircuitBreaker interface {
	GetCircuitCommandName() string
	GetFallbackResponse(ctx context.Context, err error) (proto.Message, error)
}

// handleWithCircuitBreaker executes runnerFn within circuit in case req implements RequestWithCircuitBreaker
func handleWithCircuitBreaker(
	ctx context.Context,
	req Request,
	runnerFn func(context.Context) (proto.Message, error),
) (proto.Message, error) {
	var (
		resp proto.Message
		err  error
	)

	cbReq, ok := req.(RequestWithCircuitBreaker)
	if !ok {
		return runnerFn(ctx)
	}

	err = hystrix.Run(ctx, cbReq.GetCircuitCommandName(), func(ctx context.Context) error {
		resp, err = runnerFn(ctx)
		if err != nil {
			return err
		}

		return nil
	}, func(ctx context.Context, runnerErr error) error {
		logger.Error(ctx, "got circuit open error returning fallback response",
			zap.Bool("isCircuitOpen", hystrix.IsCircuitOpen(cbReq.GetCircuitCommandName())),
			zap.String("command", cbReq.GetCircuitCommandName()), zap.Error(runnerErr))
		resp, err = cbReq.GetFallbackResponse(ctx, runnerErr)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	return resp, nil
}
