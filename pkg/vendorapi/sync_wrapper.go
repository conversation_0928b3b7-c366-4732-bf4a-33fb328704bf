package vendorapi

import (
	"context"
	"fmt"
	"net/url"
	"time"

	cmap "github.com/orcaman/concurrent-map"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/vendorgateway"
	vendorsRedactor "github.com/epifi/be-common/api/vendors/redactor"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/hystrix"
	"github.com/epifi/be-common/pkg/logger"
	redactLogger "github.com/epifi/be-common/pkg/logger/redact_logger"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/syncwrapper/request"
	"github.com/epifi/be-common/pkg/vendorapi/metrics"
)

var ErrInternal = fmt.Errorf("internal error")

type AsyncRequestFactory func(proto.Message) AsyncRequest

type AsyncRequest interface {
	Request
	MarshalWithIDHandler(handler request.AsyncReqIDHandler) ([]byte, error)
	// RequestId should return a unique ID through which a request can be identified
	// Note that unique ID should be part of callback message,
	// otherwise we will not be able to map it back to the request
	// e.g., MessageID of UPI Request Header will be part of Response payload
	// as ReqMsgID and helps to uniquely identify a request
	RequestId() string
	// Return API that is consuming the request
	Api() vendorgateway.SyncWrappedApi

	// Returns the Acknowledgement response
	// Response.Unmarshal should translate the Ack response to the final response
	// proto and set a corresponding rpc.Status
	// Request Handler will wait for callback event only if response.Status == rpc.StatusOk()
	// GetResponse() Response // Included in Request interface

	// Returns the final response that is received through callback
	GetCallBackResponse() CallBackResponse
}

type CallBackResponse interface {
	Response
}

// NewVendorAsyncRequest takes a request proto buffer and returns a vendorapi.AsyncRequest
// for the Vendor indicated in the request proto buffer. It assumes that each
// request proto buffer has a field named Header that contains a Vendor field.
func NewVendorAsyncRequest(req RequestWithHeader, requestFactoryMap map[vendorgateway.Vendor]AsyncRequestFactory) (AsyncRequest, error) {
	v := req.GetHeader().GetVendor()
	vendor, ok := requestFactoryMap[v]
	if !ok {
		return nil, fmt.Errorf("invalid Vendor: %v", v)
	}
	// req is originally a proto.Message which implements RequestWithHeader interface
	// Hence type casting validation is skipped
	vendorReq := vendor(req.(proto.Message))
	if vendorReq == nil {
		return nil, fmt.Errorf("invalid request: %v", req)
	}
	return vendorReq, nil
}

// AsyncRequestHandler orchestrates all outgoing http Async requests.
// Provides a sync wrapped abstraction layer over the functionality of Requesthandler
type AsyncRequestHandler struct {
	handler             *HTTPRequestHandler
	syncWrapperChannels *cmap.ConcurrentMap
	syncWrapperTimeout  time.Duration
	idHandler           request.AsyncReqIDHandler
	httpContentRedactor *httpcontentredactor.HTTPContentRedactor
}

func NewAsyncRequestHandler(h *HTTPRequestHandler, syncWrapperTimeoutInSeconds int,
	idHandler request.AsyncReqIDHandler, httpContentRedactor *httpcontentredactor.HTTPContentRedactor) *AsyncRequestHandler {
	return &AsyncRequestHandler{
		handler:             h,
		syncWrapperChannels: GetSyncWrapperChannelsInstance(),
		syncWrapperTimeout:  time.Duration(syncWrapperTimeoutInSeconds) * time.Second,
		idHandler:           idHandler,
		httpContentRedactor: httpContentRedactor,
	}
}

// Handle adds a sync wrapping abstraction layer over the
// functionality of the HTTPRequestHandler.
//
// Handler execution does the following:
//  1. Create a channel and add it to map with request id as key
//  2. Make a call to API
//  3. If ack response is not success, return error message
//  4. Else Read response form channel
//  5. Return timeout if no response is read from channel
//
// nolint: funlen
func (h *AsyncRequestHandler) Handle(ctx context.Context, req AsyncRequest) (proto.Message, error) {
	// MarshalWithIDHandler is called before forming channelId to populate RequestId in the
	// input request.

	uri, err := url.Parse(req.URL())
	if err != nil {
		err = fmt.Errorf("URL could not be parsed: %w", err)
		return nil, err
	}

	redactedURL := h.handler.GetRedactedURL(ctx, req, uri)

	requestBody, err := req.MarshalWithIDHandler(h.idHandler)
	if err != nil {
		return nil, fmt.Errorf("error encountered in Request marshaling: %w", err)
	}

	requestBody, err = h.handler.processRequestBody(ctx, req, requestBody, redactedURL)
	if err != nil {
		return nil, err
	}

	// TODO(pruthvi): Add vendor to channel Id to avoid conflicts??
	channelId := fmt.Sprintf("%v-%v", req.Api(), req.RequestId())

	// Channel need not be closed once we are finished with it.
	// It's only necessary to close a channel when it is important to tell the
	// receiving goroutines that all data have been sent.
	//
	// A channel that the garbage collector determines to be unreachable will have
	// its resources reclaimed whether or not it is closed.
	channel := make(chan string, 1)
	success := h.syncWrapperChannels.SetIfAbsent(channelId, channel)
	if !success {
		logger.Error(ctx, fmt.Sprintf("Channel ID %v already exists - exiting", channelId))
		return nil, ErrInternal
	}
	defer h.syncWrapperChannels.Remove(channelId)

	return handleWithCircuitBreaker(ctx, req, func(ctx context.Context) (proto.Message, error) {
		ackResp, ackErr := h.handler.HandleWithMarshalledReq(ctx, req, requestBody, redactedURL, uri)
		if ackErr != nil {
			return nil, ackErr
		}

		respWithStatus, ok := ackResp.(ResponseWithStatus)
		if !ok || !respWithStatus.GetStatus().IsSuccess() {
			return ackResp, nil
		}

		logger.Debug(ctx, fmt.Sprintf("Waiting for response on channel %v", channelId))
		select {
		case response := <-channel:
			// get content type. if content type is not set, default is
			// application/JSON for unencrypted request and text/plain for encrypted
			contentType := getResponseContentType(ctx, req)

			if canLogEncryptedResponseToSecureLogs(req.GetCallBackResponse()) {
				urlBytes := []byte(req.URL())
				responseBytes := []byte(response)
				redactLogger.LogSecure(zap.InfoLevel, ctx, "Raw callback response:",
					[]*redactLogger.RedactionCandidateField{redactLogger.NewRedactionCandidateField(httpcontentredactor.ContentTypeURL, logger.URL, &urlBytes),
						redactLogger.NewRedactionCandidateField(contentType, logger.PAYLOAD, &responseBytes)})
			}

			redactedResponseBody, redactionErr := h.handler.httpContentRedactor.Redact(ctx, []byte(response), contentType, vendorsRedactor.Config)
			if redactionErr != nil {
				logger.Error(ctx, "failed to redact callback response", zap.Error(redactionErr))
			}

			return h.unmarshal(ctx, req, []byte(response), redactedResponseBody)
		case <-time.After(h.syncWrapperTimeout):
			logger.Error(ctx, fmt.Sprintf("didnt get callback even after waiting for : %s", h.syncWrapperTimeout))
			return nil, hystrix.NewCircuitOpenError(epifierrors.ErrVendorApiTimeout)
		}
	})
}

// Unmarshal orchestrates response de-serialisation
func (h *AsyncRequestHandler) unmarshal(ctx context.Context, req AsyncRequest, responseBody []byte, redactedResponseBody []byte) (proto.Message, error) {
	callbackResp := req.GetCallBackResponse()
	var (
		resMessage proto.Message
		err        error
	)
	// Check if callback response implements the newer ResponseV2 interface, otherwise fallback to the deprecated Response interface
	callbackRespV2, ok := callbackResp.(ResponseV2)
	if ok {
		resMessage, err = callbackRespV2.UnmarshalV2(ctx, responseBody)
	} else {
		resMessage, err = callbackResp.Unmarshal(responseBody)
	}
	shouldTriggerMetrics, vendorErrorCode := evaluateAndGetMetricErrorCodeForCallback(resMessage, callbackResp)
	vendorApiIdentifier := getVendorApiIdentifier(req, h.handler.conf)

	logger.Info(ctx, fmt.Sprintf("Raw callback response: %s", redactedResponseBody), zap.String("url", req.URL()),
		zap.String(logger.REFERENCE_ID, vendorApiIdentifier),
		zap.String(logger.STATUS, vendorErrorCode))

	if shouldTriggerMetrics && ShouldMonitorCallbackErrorCode(vendorApiIdentifier, vendorErrorCode) {
		metrics.RecordVendorApiCustomError(metrics.GetVendorCallbackApiIdentifier(vendorApiIdentifier), vendorErrorCode)
	}

	if err != nil {
		return nil, fmt.Errorf("could not unmarshal response, %s: %w", string(responseBody), err)
	}
	return resMessage, nil
}

func evaluateAndGetMetricErrorCodeForCallback(protoResp proto.Message, resp CallBackResponse) (bool, string) {
	if vendorStatus, ok := protoResp.(VendorStatus); ok {
		// First check if response implements MetricsEvaluator interface
		if metricsEvaluator, ok := resp.(MetricsEvaluator); ok {
			// Use the evaluator to determine if metrics should be triggered
			return metricsEvaluator.EvaluateAndGetMetricErrorCode(vendorStatus.GetVendorStatus())
		}
	}
	return false, ""
}

// Interface definition to avoid reflection
type ResponseWithStatus interface {
	GetStatus() *rpc.Status
}
