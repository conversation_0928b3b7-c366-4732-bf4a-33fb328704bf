package vendorapi

import (
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/syncmap"
	"github.com/epifi/be-common/pkg/vendorapi/metrics"
)

// MaxAllowedErrorCode limits the number of error codes per api to prevent excessive Prometheus metrics cardinality,
// which could lead to performance issues and increased memory consumption in the monitoring system.
const MaxAllowedErrorCode = 50

// ErrorCodeRegistry manages vendor-specific error codes to be monitored
type ErrorCodeRegistry struct {
	codes *syncmap.Map[string, []string] // map[APIIdentifier][]errorCodes
}

// NewErrorCodeRegistry creates a new registry instance
func NewErrorCodeRegistry() *ErrorCodeRegistry {
	return &ErrorCodeRegistry{
		codes: &syncmap.Map[string, []string]{},
	}
}

// MonitoredVendorErrorCodeRegistry is the global singleton instance of the registry
// containing all vendor error codes that should trigger API error metrics
var MonitoredVendorErrorCodeRegistry = NewErrorCodeRegistry()

// Register adds error codes for a specific vendor
// apiIdentifier can be either a static URL (if the URL is fixed)
// or a URL template (if the URL is dynamic)
func (r *ErrorCodeRegistry) Register(apiIdentifier string, errorCodes []string) error {
	// Check if error codes exceed maximum allowed limit to maintain low cardinality in Prometheus metrics
	if len(errorCodes) > MaxAllowedErrorCode {
		logger.ErrorNoCtx("Error codes exceed maximum allowed limit", zap.String(logger.STATUS, apiIdentifier))
		return fmt.Errorf("error codes count exceed maximum allowed limit")
	}

	// Create copy of slice to avoid external modifications
	codesCopy := make([]string, len(errorCodes))
	copy(codesCopy, errorCodes)

	r.codes.Store(apiIdentifier, codesCopy)
	return nil
}

func (r *ErrorCodeRegistry) RegisterCallbackErrorCode(apiIdentifier string, errorCodes []string) error {
	// Check if error codes exceed maximum allowed limit to maintain low cardinality in Prometheus metrics
	if len(errorCodes) > MaxAllowedErrorCode {
		logger.ErrorNoCtx("callback error codes exceed maximum allowed limit", zap.String(logger.STATUS, apiIdentifier))
		return fmt.Errorf("callback error codes count exceed maximum allowed limit")
	}

	// Create copy of slice to avoid external modifications
	codesCopy := make([]string, len(errorCodes))
	copy(codesCopy, errorCodes)

	r.codes.Store(metrics.GetVendorCallbackApiIdentifier(apiIdentifier), codesCopy)
	return nil
}

// GetCodesForVendor returns the monitored error codes for a specific vendor
// Returns empty slice if vendor not found
func (r *ErrorCodeRegistry) GetCodesForVendor(apiIdentifier string) []string {
	// Return copy to prevent external modification
	codes := r.codes.Get(apiIdentifier)
	result := make([]string, len(codes))
	copy(result, codes)

	return result
}

// ShouldMonitorErrorCode checks if a specific error code should be monitored for a vendor
func ShouldMonitorErrorCode(apiIdentifier, errorCode string) bool {
	codes := MonitoredVendorErrorCodeRegistry.GetCodesForVendor(apiIdentifier)
	return lo.Contains(codes, errorCode)
}

func ShouldMonitorCallbackErrorCode(apiIdentifier, errorCode string) bool {
	codes := MonitoredVendorErrorCodeRegistry.GetCodesForVendor(metrics.GetVendorCallbackApiIdentifier(apiIdentifier))
	return lo.Contains(codes, errorCode)
}
