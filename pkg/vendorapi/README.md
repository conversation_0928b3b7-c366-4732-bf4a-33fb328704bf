# Vendorapi

This package contains common code which is used to perform vendor calls. It handles encryption, logging, marshalling and unmarshalling of requests and responses, and making the call.
The package can be used:
1. directly if communicating with inhouse vendors (either self-hosted vendors or data science services)
2. Via vendorgateway if communicating with external vendor (e.g federal, loan partner, karza, etc.)

The pkg should not contain any vendor specific implementation. This is a driver code, in case of need of a specific handling, create a common interface for request/response, add handling for the interface and implement that interface for required requests/responses.
