package vendorapi

import (
	"sync"

	cmap "github.com/orcaman/concurrent-map"
)

// Map between unique request ID and channel to facilitate
// the functioning of sync wrapper for async requests
//
// Key:
// should be API name + "-" + a unique request ID e.g. "Get<PERSON>ey-ReqID007"
// Note that the unique ID should also be part of callback message,
// failing which we will not be able to map it back to the request
//
// e.g., MessageID of UPI Request Header will be part of Response payload
// as ReqMsgID and helps to uniquely identify a request
//
// Value:
// A channel of string(XML or JSON Response)
//
// A thread-safe concurrent map for Go is used here
// Implementation scales better compared to sync.Map with the help of sharding
// Note that number of shards is fixed at 32 and cannot be customized
//
// In case, concurrent map becomes a bottle neck, try consider playing with
// number of shards
var syncWrapperChannels cmap.ConcurrentMap
var once sync.Once

func GetSyncWrapperChannelsInstance() *cmap.ConcurrentMap {
	once.Do(func() {
		syncWrapperChannels = cmap.New()
	})
	return &syncWrapperChannels
}
