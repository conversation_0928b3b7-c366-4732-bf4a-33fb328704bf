package vendorapi

import (
	"context"
	"fmt"

	cmap "github.com/orcaman/concurrent-map"

	queuePb "github.com/epifi/be-common/api/queue"
	vgPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"
)

// TODO(pruthvi): Add tests when session stickiness is implemented

type SyncWrapperConsumer struct {
	// UnimplementedUpiResConsumerServer is embedded to have forward compatible implementations
	vgPb.UnimplementedSyncWrapperConsumerServer
	syncWrapperChannels *cmap.ConcurrentMap
}

func (s *SyncWrapperConsumer) ProcessEvent(ctx context.Context, req *vgPb.ProcessEventRequest) (
	*vgPb.ProcessEventResponse, error) {
	res := &vgPb.ProcessEventResponse{}
	res.ResponseHeader = &queuePb.ConsumerResponseHeader{}

	channelId := fmt.Sprintf("%v-%v", req.GetApi(), req.GetRequestId())

	if channel, ok := s.syncWrapperChannels.Get(channelId); !ok {
		logger.Error(ctx, fmt.Sprintf("Channel with ID %v doesn't exist - couldn't process the response :%v",
			channelId, string(req.GetRawData())))
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return res, nil
	} else {
		channel1, ok := channel.(chan string)
		if !ok {
			logger.Error(ctx, fmt.Sprintf("Expected `chan string` but got %T for channelID %v: %v",
				channel, channelId, string(req.GetRawData())))
		}
		channel1 <- string(req.GetRawData())
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS
		return res, nil
	}
}

// Factory method for creating an instance of SyncWrapper's consumer service
// This method will be used by the injector when providing the dependencies at initialization time
func NewSyncWrapperConsumer() *SyncWrapperConsumer {
	return &SyncWrapperConsumer{
		syncWrapperChannels: GetSyncWrapperChannelsInstance(),
	}
}
