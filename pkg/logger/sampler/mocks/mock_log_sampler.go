package mocks

import (
	"math/rand"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger/sampler"
)

var samplingDescriptorsSingleton sync.Once

const (
	MockMainRpcName = "/my-rpc"
	MockRpcOneName  = "/rpc-1"
	MockRpcTwoName  = "/rpc-2"
)

// MockRngSource is a mock random number generator source that returns
// the same value again and again, based on the seed
type mockRngSource struct {
	seed int64
}

func (s *mockRngSource) Seed(seed int64) {
	s.seed = seed
}

func (s *mockRngSource) Int63() int64 {
	return s.seed
}

func newMockRngSource(seed int64) *mockRngSource {
	return &mockRngSource{seed: seed}
}

// InitailiseMockLogSampler creates a mock log-sampler with certain parameters & mock random number source.
func InitailiseMockLogSampler() sampler.RPCLogSampler {
	mockRndNumGen := rand.New(newMockRngSource(1 << 61)) // Seed with 1 << 61 to get 0.25 in rand.Float32

	// Currently we test only with the following assumptions about the logSampler.
	logSampler := sampler.InitRandomLogSamplerWithCustomRNG(&cfg.LogSamplerOptions{
		OverwriteParentSampling: false,
		SamplingRate:            0.7,
	}, mockRndNumGen)
	return logSampler
}

// InitialiseMockSamplingDescriptors initialises the sampling descriptors
func InitialiseMockSamplingDescriptors() epifigrpc.RpcLogSamplingRatesMap {
	mockSamplingDescriptors := epifigrpc.GetRPCLogSamplingRatesMapInstance()

	samplingDescriptorsSingleton.Do(func() {
		mockSamplingDescriptors[MockRpcOneName] = &sampler.LogSamplingDescriptor{
			SamplingRate:            0.9,
			OverwriteParentSampling: false,
		}
		mockSamplingDescriptors[MockRpcTwoName] = &sampler.LogSamplingDescriptor{
			SamplingRate:            0.1,
			OverwriteParentSampling: true,
		}
	})
	return mockSamplingDescriptors
}
