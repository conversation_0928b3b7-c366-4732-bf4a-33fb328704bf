variable "env" {
  description = "Name of the Environment [staging,qa,uat,prod]"
  type        = string
  default     = "aa-prod"
}

variable "tenant" {
  description = "Name of the Tenant [epifi,stockguardian]"
  type        = string
}

variable "vpc_name" {
  description = "Name of the VPC"
  type        = string
  default     = "aa-prod"
}

variable "region" {
  description = "aws region"
  type        = string
  default     = "ap-south-1"
}

variable "use_nat_instance" {
  type        = bool
  default     = true
  description = "use self managed nat instance, instead of aws managed nat gateway"
}

variable "private_subnets" { type = list(string) }
variable "public_subnets" { type = list(string) }
variable "owner" { type = string }

variable "vpc_cidr" {
  description = "vpc cidr"
  type        = string
}
