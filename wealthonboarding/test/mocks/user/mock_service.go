// Code generated by MockGen. DO NOT EDIT.
// Source: wealthonboarding/user/service.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	user "github.com/epifi/gamma/api/wealthonboarding/user"
	user0 "github.com/epifi/gamma/wealthonboarding/user"
	gomock "github.com/golang/mock/gomock"
)

// MockIService is a mock of IService interface.
type MockIService struct {
	ctrl     *gomock.Controller
	recorder *MockIServiceMockRecorder
}

// MockIServiceMockRecorder is the mock recorder for MockIService.
type MockIServiceMockRecorder struct {
	mock *MockIService
}

// NewMockIService creates a new mock instance.
func NewMockIService(ctrl *gomock.Controller) *MockIService {
	mock := &MockIService{ctrl: ctrl}
	mock.recorder = &MockIServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIService) EXPECT() *MockIServiceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockIService) Create(ctx context.Context, newUser *user.User) (*user.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, newUser)
	ret0, _ := ret[0].(*user.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockIServiceMockRecorder) Create(ctx, newUser interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIService)(nil).Create), ctx, newUser)
}

// CreateOrUpdateUser mocks base method.
func (m *MockIService) CreateOrUpdateUser(ctx context.Context, newUser *user.User, forceUpdateFields []user0.ForceUpdateField) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdateUser", ctx, newUser, forceUpdateFields)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOrUpdateUser indicates an expected call of CreateOrUpdateUser.
func (mr *MockIServiceMockRecorder) CreateOrUpdateUser(ctx, newUser, forceUpdateFields interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateUser", reflect.TypeOf((*MockIService)(nil).CreateOrUpdateUser), ctx, newUser, forceUpdateFields)
}

// GetByActorId mocks base method.
func (m *MockIService) GetByActorId(ctx context.Context, actorId string) (*user.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", ctx, actorId)
	ret0, _ := ret[0].(*user.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockIServiceMockRecorder) GetByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockIService)(nil).GetByActorId), ctx, actorId)
}
