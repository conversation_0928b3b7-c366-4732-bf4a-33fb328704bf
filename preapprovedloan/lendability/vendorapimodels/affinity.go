package vendorapimodels

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	commonTypes "github.com/epifi/be-common/api/typesv2/common"

	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/vendorapi"

	ldbtPb "github.com/epifi/gamma/api/preapprovedloan/lendability"
	vendorInhousePb "github.com/epifi/gamma/api/vendors/inhouse"

	"github.com/epifi/gamma/preapprovedloan/config/genconf"
)

type GetLoanAffinityRequest struct {
	Req     *ldbtPb.AffinityApiRequest
	GenConf *genconf.Config
}

type GetLoanAffinityResponse struct {
}

func (r *GetLoanAffinityRequest) HTTPMethod() string {
	return http.MethodPost
}

func (r *GetLoanAffinityRequest) URL() string {
	return r.GenConf.Lendability().Url() + "/v1/loan_affinity"
}

func (r *GetLoanAffinityRequest) GetResponse() vendorapi.Response {
	return &GetLoanAffinityResponse{}
}

func (r *GetLoanAffinityRequest) Marshal() ([]byte, error) {
	req := r.Req
	if req == nil {
		return nil, errors.New("nil request")
	}

	// this is done as per the model expectation to avoid unnecessary failures
	// currently the model expects "EMPLOYMENT_TYPE_UNSPECIFIED" as the default value
	// and the other values with "EMPLOYMENT_TYPE_" prefix removed.
	employmentType := req.GetEmploymentDetails().GetEmploymentType().String()
	if employmentType != commonTypes.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED.String() {
		employmentType = strings.TrimPrefix(employmentType, "EMPLOYMENT_TYPE_")
	}

	requestPayload := &vendorInhousePb.GetLoanAffinityScoreRequest{
		ActorId:      req.GetActorId(),
		CreditReport: req.GetRawCreditReport(),
		Context: &vendorInhousePb.Context{
			ModelContext: "loan_affinity",
			ModelVersion: "v1",
		},
		UserDetails: &vendorInhousePb.GetLoanAffinityScoreRequest_UserDetails{
			Dob: datetimePkg.DateToString(req.GetUserDetails().GetDob(), datetimePkg.DATE_LAYOUT_DDMMYYYY, datetimePkg.IST),
		},
		DeviceDetails: &vendorInhousePb.GetLoanAffinityScoreRequest_DeviceDetails{
			Model:        req.GetDeviceDetails().GetModel(),
			Manufacturer: req.GetDeviceDetails().GetManufacturer(),
		},
		EmploymentDetails: &vendorInhousePb.GetLoanAffinityScoreRequest_EmploymentDetails{
			EmploymentType: employmentType,
		},
	}

	// Only set lat/long if they are non-zero
	// Using EmitUnpopulated: false to ensure that unset fields are not included in the JSON output
	if latLng := req.GetUserDetails().GetLatLong(); latLng != nil {
		lat := latLng.GetLatitude()
		long := latLng.GetLongitude()
		if lat != 0.0 || long != 0.0 {
			requestPayload.UserDetails.Latitude = lat
			requestPayload.UserDetails.Longitude = long
		}
	}

	a, _ := protojson.MarshalOptions{EmitUnpopulated: false}.Marshal(requestPayload)
	return a, nil
}

func (c *GetLoanAffinityResponse) Unmarshal(b []byte) (proto.Message, error) {
	vRes := vendorInhousePb.GetLoanAffinityScoreResponse{}

	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &vRes)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshal byte array to inhouse lendability proto message")
	}

	return &ldbtPb.AffinityApiResponse{
		Score:      vRes.GetScore(),
		ApiVersion: vRes.GetModelVersion(),
	}, nil
}

func (c *GetLoanAffinityResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in GetLoanAffinity API", zap.String("error", string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))
	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}

// RedactRequestBody overrides the redaction method. Redacted logging is not required for this API hence returning
// empty response to avoid logging redacted request
func (c *GetLoanAffinityRequest) RedactRequestBody(ctx context.Context, requestBody []byte, contentType string) ([]byte, error) {
	return httpcontentredactor.GetInstance().Redact(ctx, requestBody, contentType, map[string]mask.MaskingStrategy{
		"credit_report": mask.MaskAllChars,
		"user_details":  mask.MaskAllChars,
	})
}
