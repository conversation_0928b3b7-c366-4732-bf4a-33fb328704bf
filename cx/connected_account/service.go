package connected_account

import (
	"context"
	"sort"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/external"
	cxConnectedAccountPb "github.com/epifi/gamma/api/cx/connected_account"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	cxLogger "github.com/epifi/gamma/cx/logger"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

type Service struct {
	caClient   connectedAccountPb.ConnectedAccountClient
	authEngine auth_engine.IAuthEngine
}

func NewConnectedAccountService(caClient connectedAccountPb.ConnectedAccountClient, authEngine auth_engine.IAuthEngine) *Service {
	return &Service{
		caClient:   caClient,
		authEngine: authEngine,
	}
}

func (s *Service) GetConnectedAccountDetails(ctx context.Context, req *cxConnectedAccountPb.GetConnectedAccountDetailsRequest) (*cxConnectedAccountPb.GetConnectedAccountDetailsResponse, error) {

	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxConnectedAccountPb.GetConnectedAccountDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	actorId := req.GetHeader().GetActor().GetId()

	if actorId == "" {
		cxLogger.Error(ctx, "actor id in cx header cannot be empty")
		return &cxConnectedAccountPb.GetConnectedAccountDetailsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor id in cx header cannot be empty"),
		}, nil
	}

	resp, err := s.caClient.GetAccounts(ctx, &connectedAccountPb.GetAccountsRequest{
		ActorId:           actorId,
		AccountFilterList: []external.AccountFilter{external.AccountFilter_ACCOUNT_FILTER_ALL},
	})

	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "cannot get connected account details for an actor", zap.Any("actor_id", actorId), zap.Error(te))
		return &cxConnectedAccountPb.GetConnectedAccountDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching connected account details"),
		}, nil
	}

	cxAccDetails, err := buildAccountDetailsList(resp.GetAccountDetailsList())
	if err != nil {
		cxLogger.Error(ctx, "error while building response account details list", zap.Error(err))
		return &cxConnectedAccountPb.GetConnectedAccountDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching bank name"),
		}, nil
	}

	if len(cxAccDetails) == 0 {
		cxLogger.Error(ctx, "record not found for actor", zap.Any("actor_id", actorId))
		return &cxConnectedAccountPb.GetConnectedAccountDetailsResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("no connected account record found for actor"),
		}, nil
	}

	return &cxConnectedAccountPb.GetConnectedAccountDetailsResponse{
		Status:         rpcPb.StatusOk(),
		AccountDetails: cxAccDetails,
	}, nil
}

func buildAccountDetailsList(caAccDetails []*external.AccountDetails) ([]*cxConnectedAccountPb.AccountDetails, error) {

	var resultAccDetailsList []*cxConnectedAccountPb.AccountDetails

	for _, acc := range caAccDetails {
		accDetails := &cxConnectedAccountPb.AccountDetails{
			AccountId:             acc.GetMaskedAccountNumber(),
			AccountInstrumentType: acc.GetAccInstrumentType().String(),
			AccountStatus:         acc.GetAccountStatus().String(),
			AccountSubStatus:      acc.GetAccountSubStatus().String(),
			ConnectedAt:           acc.GetConnectedAt(),
			LastSyncedAt:          acc.GetLastSyncedAt(),
		}

		fipMeta, err := caPkg.GetFipMetaById(acc.GetFipId())
		if err != nil {
			return nil, err
		}
		accDetails.BankName = fipMeta.Name
		resultAccDetailsList = append(resultAccDetailsList, accDetails)
	}

	sort.SliceStable(resultAccDetailsList, func(i, j int) bool {
		return resultAccDetailsList[i].GetConnectedAt().GetSeconds() > resultAccDetailsList[j].GetConnectedAt().GetSeconds()
	})

	return resultAccDetailsList, nil
}
