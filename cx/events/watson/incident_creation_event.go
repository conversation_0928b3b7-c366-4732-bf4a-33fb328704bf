package watson

import (
	"time"

	watsonPb "github.com/epifi/gamma/api/cx/watson"
	eventConstants "github.com/epifi/gamma/cx/events"
	watsonInternal "github.com/epifi/gamma/cx/internal/watson"

	"github.com/epifi/be-common/pkg/events"

	"github.com/fatih/structs"
	"github.com/google/uuid"
)

type IncidentCreationEvent struct {
	ActorId     string
	SessionId   string
	ProspectId  string
	Timestamp   time.Time
	EventId     string
	EventType   string
	EventName   string
	ServiceName string

	// custom properties
	ClientRequestId        string
	IncidentId             string
	IncidentState          string
	Client                 string
	IdentifiedAt           time.Time
	ProductCategory        string
	ProductCategoryDetails string
	SubCategory            string
}

func NewIncidentCreationEvent(incident *watsonPb.Incident) *IncidentCreationEvent {
	// No error handling required, if we don't get incident category it won't be populated in event
	incidentCategory, _ := watsonInternal.GetIncidentCategoryFromIncidentCategoryId(incident.GetIncidentCategoryId())
	return &IncidentCreationEvent{
		ActorId:                incident.GetActorId(),
		Timestamp:              time.Now(),
		EventId:                uuid.New().String(),
		EventType:              events.EventTrack,
		EventName:              eventConstants.IncidentCreationEventName,
		ServiceName:            eventConstants.CxServiceName,
		ClientRequestId:        incident.GetClientRequestId(),
		IncidentId:             incident.GetId(),
		IncidentState:          incident.GetIncidentState().String(),
		Client:                 incident.GetClient().String(),
		IdentifiedAt:           incident.GetIdentifiedAt().AsTime(),
		ProductCategory:        incidentCategory.GetProductCategory().String(),
		ProductCategoryDetails: incidentCategory.GetProductCategoryDetails().String(),
	}
}

func (s *IncidentCreationEvent) GetEventType() string {
	return s.EventType
}

func (s *IncidentCreationEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *IncidentCreationEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *IncidentCreationEvent) GetEventId() string {
	return s.EventId
}

func (s *IncidentCreationEvent) GetUserId() string {
	return s.ActorId
}

func (s *IncidentCreationEvent) GetProspectId() string {
	return s.ProspectId
}

func (s *IncidentCreationEvent) GetEventName() string {
	return s.EventName
}
