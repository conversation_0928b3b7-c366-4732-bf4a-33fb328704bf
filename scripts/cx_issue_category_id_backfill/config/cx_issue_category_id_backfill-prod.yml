Application:
  Environment: "prod"
  Name: "cx_issue_category_id_backfill"

EpifiDb:
  AppName: "cx"
  StatementTimeout: 5s
  Name: "sherlock"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbUsernamePassword: "prod/rds/epifimetis/sherlock"

