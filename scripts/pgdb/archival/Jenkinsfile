@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools{
        go 'Go stable'
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
        timestamps()
    }
    parameters {
        choice(name: 'ENV', choices: ['staging','uat','qa', 'demo'], description: 'Environment to run on.')
        choice(name: 'TASK', choices: ['StartSchedule','TriggerSchedule','StopSchedule','ResumeSchedule'], description: 'Task to be executed.')
        string(name: 'DB_NAME', defaultValue:"", description: 'DB Name for the table to archive.')
        string(name: 'TABLE_NAME', defaultValue:"", description: 'Table Name to archive.')
        choice(name: 'ARCHIVAL_CONSTRAINT', choices: ['UPDATED_AT','DELETED_AT','DELETED_AT_UNIX'], description:'Column to use for archival')
        string(name: 'SCHEDULE_CRON', defaultValue:"0 1 * * *", description:'Cron description for the schedule. This is in UTC')
        string(name: 'RETENTION_TIME', defaultValue:"4320h", description: 'Retention time for target table. This is based on updatedAt. (Default - 6 months)')
        string(name: 'BATCH_DURATION', defaultValue:"1h", description: 'Batch duration. This is the time interval based on updated at which is copied to S3 and deleted from PGDB in a single transaction.')
        string(name: 'BATCH_DELAY', defaultValue:"1s", description: 'Time to sleep after each batch. This will allow DB to catch up.')
        booleanParam(name: 'REFRESH', defaultValue: false, description: 'Devs, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    environment {
        ENV = "${params.ENV}"
        TASK = "${params.TASK}"
        DB_NAME = "${params.DB_NAME}"
        TABLE_NAME = "${params.TABLE_NAME}"
        ARCHIVAL_CONSTRAINT="${params.ARCHIVAL_CONSTRAINT}"
        SCHEDULE_CRON = "${params.SCHEDULE_CRON}"
        RETENTION_TIME = "${params.RETENTION_TIME}"
        BATCH_DURATION = "${params.BATCH_DURATION}"
        BATCH_DELAY = "${params.BATCH_DELAY}"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true }
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.description = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Assume Role') {
            steps {
                script {
                    assumeRole()
                }
            }
        }
        stage("Trigger/Schedule Workflow") {
            steps {
                dir("gamma") {
                    script {
                        sh """
                            set +x

                            . ${WORKSPACE}/.env

                            aws s3 cp "s3://epifi-${ENV}-jenkins-job-binaries/scripts/nebula/schedule_pgdb_expiry_workflow" . --recursive
                            
                            chmod u+x ./nebula/schedule_pgdb_expiry_workflow_bin 

                            DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP=true ENVIRONMENT=${ENV} ./nebula/schedule_pgdb_expiry_workflow_bin --task=${TASK} --task=${TASK} --db-name=${DB_NAME} --table-name=${TABLE_NAME} --archival-constraint=${ARCHIVAL_CONSTRAINT} "--schedule-cron=${SCHEDULE_CRON}" --retention-time=${RETENTION_TIME} --batch-duration=${BATCH_DURATION} --batch-delay=${BATCH_DELAY}
                            
                            set -x
                        """
                    }
                }
            }
        }
    }
}
