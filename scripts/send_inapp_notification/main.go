package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"strings"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	actorPb "github.com/epifi/gamma/api/actor"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/frontend/deeplink"
	fcmPb "github.com/epifi/gamma/api/frontend/fcm"
	segmentPb "github.com/epifi/gamma/api/segment"

	"go.uber.org/zap"

	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
)

var (
	title              = flag.String("title", "", "title of the in app notification")
	body               = flag.String("body", "", "body of the in app notification")
	iconUrl            = flag.String("iconUrl", "", "icon url which be displayed along with body in notification")
	deeplinkJson       = flag.String("deeplinkJson", "", "json of the deeplink to be used in notification for redirection")
	campaignNameString = flag.String("campaignName", "", "campaign name enum to be used for this notification, eg: CAMPAIGN_NAME_FI_STORE_GIFT_CARDS")
	priorityString     = flag.String("priority", "", "priority of the notification, values: HIGH, LOW, NORMAL")
	segmentIdsString   = flag.String("segmentIds", "", "segment ids to send notifications separated by ,")
	expiryDate         = flag.String("expiryDate", "", "optional expiry date for the notification in yyyy-MM-dd format")
)

func main() {
	flag.Parse()

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	if *title == "" || *body == "" || *iconUrl == "" {
		logger.Panic("title, body and icon url should not be empty")
	}

	if *segmentIdsString == "" {
		logger.Panic("segment ids should not be empty")
	}

	dl, err := getDeeplinkFromJson(*deeplinkJson)
	if err != nil {
		logger.Panic("error while unmarshalling deeplink json", zap.String("deeplink", *deeplinkJson), zap.Error(err))
	}

	campaignName := commsPb.CampaignName(commsPb.CampaignName_value[*campaignNameString])
	if campaignName == commsPb.CampaignName_CAMPAIGN_NAME_UNSPECIFIED {
		logger.Panic("unknown campaign name specified")
	}
	priority := commsPb.NotificationPriority(commsPb.NotificationPriority_value[*priorityString])
	if priority == commsPb.NotificationPriority_PRIORITY_UNSPECIFIED {
		logger.Panic("unknown priority specified")
	}

	// init external services
	segmentSvcConn := epifigrpc.NewConnByService(cfg.SEGMENT_SERVICE)
	defer epifigrpc.CloseConn(segmentSvcConn)
	segmentClient := segmentPb.NewSegmentationServiceClient(segmentSvcConn)

	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actorPb.NewActorClient(actorConn)

	commsConn := epifigrpc.NewConnByService(cfg.COMMS_SERVICE)
	defer epifigrpc.CloseConn(commsConn)
	commsClient := commsPb.NewCommsClient(commsConn)

	ctx, cancelFn := context.WithTimeout(context.Background(), 3*time.Hour)
	defer cancelFn()

	currentTime := timestampPb.Now()
	segmentIds := strings.Split(strings.TrimSpace(*segmentIdsString), ",")

	var expiryTime *timestampPb.Timestamp
	if *expiryDate != "" {
		expiryTimeParsed, parseErr := time.ParseInLocation("2006-01-02", *expiryDate, datetime.IST)
		if parseErr != nil {
			logger.Panic("failed to parse expiry date: %w", zap.Error(parseErr))
		}
		expiryTime = timestampPb.New(expiryTimeParsed)
	}
	for _, segmentId := range segmentIds {
		err = sendNotificationsForGivenSegment(ctx, segmentClient, actorClient, commsClient, segmentId, dl, campaignName, priority, currentTime, expiryTime)
		if err != nil {
			if errors.Is(err, epifierrors.ErrPermanent) {
				logger.PanicWithCtx(ctx, "permanent error while sending in app notification to segment users", zap.String("segment", segmentId), zap.Error(err))
			}
			logger.Error(ctx, "error while sending in app notification to segment users", zap.String("segment", segmentId), zap.Error(err))
		}
	}
}

func getDeeplinkFromJson(jsonString string) (*deeplink.Deeplink, error) {
	jsonByteArr := []byte(jsonString)
	deeplinkObj := &deeplink.Deeplink{}
	err := protojson.Unmarshal(jsonByteArr, deeplinkObj)
	if err != nil {
		logger.ErrorNoCtx("failed to un marshal deeplink json string to deeplink obj", zap.Error(err))
		return nil, fmt.Errorf("failed to un marshal deeplink json string to deeplink obj: %w", err)
	}

	return deeplinkObj, nil
}

// nolint: funlen
func sendNotificationsForGivenSegment(
	ctx context.Context,
	segmentClient segmentPb.SegmentationServiceClient, actorClient actorPb.ActorClient, commsClient commsPb.CommsClient,
	segmentId string, dl *deeplink.Deeplink, campaignName commsPb.CampaignName, priority commsPb.NotificationPriority,
	currentTime *timestampPb.Timestamp,
	expiryTime *timestampPb.Timestamp) error {
	const (
		batchSize           = 100
		delayBetweenBatches = 1 * time.Second
	)
	pageToken := &rpc.PageContextRequest_AfterToken{}
	totalSuccessNotificationCount := 0
	totalFcmTokenNotFoundErrCount := 0
	totalFcmTokenInActiveErrCount := 0
	totalFailureNotificationCount := 0
	totalNotFoundActorIdsInSegmentCount := 0
	// Adding batchCount as 20000, which implies 20000*100 => 20,00,000 users.
	for batchCount := 1; batchCount <= 20000; batchCount++ {
		getMembersRequest := &segmentPb.GetMembersRequest{
			SegmentId: segmentId,
			LatestBy:  currentTime,
			PageContext: &rpc.PageContextRequest{
				Token:    pageToken,
				PageSize: batchSize,
			},
		}
		getMembersRes, err := segmentClient.GetMembers(ctx, getMembersRequest)
		if te := epifigrpc.RPCError(getMembersRes, err); te != nil {
			logger.Error(ctx, "segmentClient.GetMembers rpc call failed", zap.Error(te))
			if getMembersRes.GetStatus().IsRecordNotFound() {
				return fmt.Errorf("segment instance not found: %w", epifierrors.ErrPermanent)
			}
			return errors.Join(fmt.Errorf("failed to get members for segment: %w", te), epifierrors.ErrPermanent)
		}

		batchedSuccessNotificationCount := 0
		for _, actorId := range getMembersRes.GetActorIds() {
			entityDetailsRes, err := actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: actorId})
			if te := epifigrpc.RPCError(entityDetailsRes, err); te != nil {
				logger.Error(ctx, "actorClient.GetEntityDetailsByActorId rpc call failed", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(te))
				if entityDetailsRes.GetStatus().IsRecordNotFound() {
					totalNotFoundActorIdsInSegmentCount++
				}
				continue
			}
			sendNotificationErr := sendNotification(ctx, commsClient, entityDetailsRes.GetEntityId(), dl, campaignName, priority, expiryTime)
			switch {
			case errors.Is(sendNotificationErr, epifierrors.ErrRecordNotFound):
				totalFcmTokenNotFoundErrCount++
			case errors.Is(sendNotificationErr, epifierrors.ErrFailedPrecondition):
				totalFcmTokenInActiveErrCount++
			case sendNotificationErr != nil:
				totalFailureNotificationCount++
				logger.Error(ctx, "error while sending silent notification", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.USER_ID, entityDetailsRes.GetEntityId()), zap.Error(sendNotificationErr))
			default:
				batchedSuccessNotificationCount++
			}
		}
		logger.Info(ctx, "successfully sent silent notifications for batch", zap.Int("batch_count", batchCount), zap.Int("batch_size", batchSize), zap.Int("success_notification_count", batchedSuccessNotificationCount))
		totalSuccessNotificationCount += batchedSuccessNotificationCount

		if !getMembersRes.GetPageContext().GetHasAfter() {
			break
		}
		pageToken.AfterToken = getMembersRes.GetPageContext().GetAfterToken()

		// sleep between consecutive batches to avoid overloading the comms server
		time.Sleep(delayBetweenBatches)
	}

	logger.Info(ctx, "successfully sent in app notifications for given segment", zap.String(logger.SEGMENT_ID, segmentId), zap.Int("totalSuccessNotificationCount", totalSuccessNotificationCount), zap.Int("totalFcmTokenNotFoundErrCount", totalFcmTokenNotFoundErrCount), zap.Int("totalFailureNotificationCount", totalFailureNotificationCount), zap.Int("totalFcmTokenInActiveErrCount", totalFcmTokenInActiveErrCount), zap.Int("totalNotFoundActorIdsInSegmentCount", totalNotFoundActorIdsInSegmentCount))
	return nil
}

func sendNotification(ctx context.Context,
	commsClient commsPb.CommsClient,
	userId string, dl *deeplink.Deeplink,
	campaignName commsPb.CampaignName,
	priority commsPb.NotificationPriority,
	expiryTime *timestampPb.Timestamp) error {
	sendMessageRes, err := commsClient.SendMessage(ctx, &commsPb.SendMessageRequest{
		Type:           commsPb.QoS_GUARANTEED,
		UserIdentifier: &commsPb.SendMessageRequest_UserId{UserId: userId},
		Medium:         commsPb.Medium_NOTIFICATION,
		CampaignName:   campaignName,
		Message: &commsPb.SendMessageRequest_Notification{
			Notification: &commsPb.NotificationMessage{
				Priority: priority,
				Notification: &fcmPb.Notification{
					NotificationType: fcmPb.NotificationType_IN_APP,
					NotificationTemplates: &fcmPb.Notification_InAppTemplate{
						InAppTemplate: &fcmPb.InAppTemplate{
							CommonTemplateFields: &fcmPb.CommonTemplateFields{
								Title: *title,
								Body:  *body,
								IconAttributes: &fcmPb.IconAttributes{
									IconUrl: *iconUrl,
								},
								Deeplink: dl,
								ExpireAt: expiryTime,
							},
						},
					},
				},
			},
		},
	})
	if te := epifigrpc.RPCError(sendMessageRes, err); te != nil {
		switch {
		// FCM Token not found in db
		case sendMessageRes.GetStatus().IsRecordNotFound():
			return epifierrors.ErrRecordNotFound
		//  FCM token is inactive
		case sendMessageRes.GetStatus().IsFailedPrecondition() && sendMessageRes.GetStatus().GetDebugMessage() == "FCM token is inactive":
			return epifierrors.ErrFailedPrecondition
		default:
			return te
		}
	}
	return nil
}
