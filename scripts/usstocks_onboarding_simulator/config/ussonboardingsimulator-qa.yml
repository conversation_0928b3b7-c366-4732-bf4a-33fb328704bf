Application:
  Environment: "qa"


Aws:
  Region: "ap-south-1"

DbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    AppName: "ussonboardingsimulator"
    StatementTimeout: 1s
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 50
    MaxIdleConn: 14
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  US_STOCKS_ALPACA:
    DbType: "CRDB"
    AppName: "ussonboardingsimulator"
    StatementTimeout: 1s
    Username: "usstocks_alpaca_dev_user"
    Password: ""
    Name: "usstocks_alpaca"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.usstocks_alpaca_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.usstocks_alpaca_dev_user.key"
    MaxOpenConn: 20
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
      EnableMultiDBSupport: true
      DBResolverList:
        - Alias: "usstocks_alpaca_pgdb"
          DbDsn:
            DbType: "PGDB"
            Name: "usstocks_alpaca"
            SSLMode: "disable"
            SecretName: "qa/rds/epifimetis/usstocks_alpaca_dev_user"

  EPIFI_WEALTH:
    AppName: "ussonboardingsimulator"
    DbType: "CRDB"
    StatementTimeout: 1s
    Username: "epifi_wealth_dev_user"
    Password: ""
    Name: "epifi_wealth"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.epifi_wealth_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.epifi_wealth_dev_user.key"
    MaxOpenConn: 20
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

ActorDb:
  Name: "actor"
  DbType: "PGDB"
  DbServerAlias: "PLUTUS_RDS"
  EnableDebug: false
  SSLMode: "disable"
  AppName: "actor"
  SecretName: "qa/rds/epifiplutus/actor_dev_user"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

SimulatorDb:
  DbType: "CRDB"
  Username: "simulator_dev_user"
  Password: ""
  Name: "simulator"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "./crdb/qa/"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.simulator_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.simulator_dev_user.key"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

UserSavingsRedis:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 13
