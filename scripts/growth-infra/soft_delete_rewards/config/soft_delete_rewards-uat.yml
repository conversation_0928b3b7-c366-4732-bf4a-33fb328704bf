Application:
  Environment: "uat"
  Name: "soft_delete_rewards"

RewardsDb:
  Name: "rewards"
  EnableDebug: false
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbCredentials: "uat/rds/postgres"

RewardStatusUpdateEventSnsPublisher:
  TopicName: "uat-rewards-reward-status-update-event-topic"

ProjectionEventSnsPublisher:
  TopicName: "uat-rewards-projection-event-topic"
