package main

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"flag"
	"fmt"

	"go.uber.org/zap"

	holdingsimporterPb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/holdingsimporter"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

var (
	payload = flag.String("payload", "", "payload which needs to be encrypted")
)

func main() {
	flag.Parse()
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(fmt.Errorf("error loading env config: %v", err))
	}
	logger.Init(env)

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)
	mfHoldingsImporterClient := holdingsimporterPb.NewHoldingImporterClient(vgConn)

	res, err := mfHoldingsImporterClient.EncryptAndSign(context.Background(), &holdingsimporterPb.EncryptAndSignRequest{
		Header:  &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MF_CENTRAL},
		Payload: *payload,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Fatal("failed to encrypt mf central payload", zap.Error(rpcErr))
	}

	fmt.Println("encryted payload : ", res.GetEncryptedPayload())
	fmt.Println("signature: ", res.GetSignature())
}
