package impl

import (
	"context"
	"errors"
	"time"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/aml/dao"
	"github.com/epifi/gamma/aml/dao/model"
	"github.com/epifi/gamma/api/aml"

	gormv2 "gorm.io/gorm"
)

var _ dao.AmlFileGenerationAttemptsDao = &CrdbAmlFileGenerationAttemptsDao{}

type CrdbAmlFileGenerationAttemptsDao struct {
	db cmdtypes.EpifiCRDB
}

func NewCrdbAmlFileGenerationAttemptsDao(db cmdtypes.EpifiCRDB) *CrdbAmlFileGenerationAttemptsDao {
	return &CrdbAmlFileGenerationAttemptsDao{db: db}
}

func (d *CrdbAmlFileGenerationAttemptsDao) Create(ctx context.Context, fileGenerationAttempt *aml.AmlFileGenerationAttempt) (*aml.AmlFileGenerationAttempt, error) {
	defer metric_util.TrackDuration("aml/dao/impl", "CrdbAmlFileGenerationAttemptsDao", "Create", time.Now())
	attemptModel := model.NewAmlFileGenerationAttempt(fileGenerationAttempt)
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	res := db.Create(attemptModel)
	if res.Error != nil {
		return nil, res.Error
	}
	return attemptModel.GetProto(), nil
}

func (d *CrdbAmlFileGenerationAttemptsDao) GetById(ctx context.Context, id string) (*aml.AmlFileGenerationAttempt, error) {
	defer metric_util.TrackDuration("aml/dao/impl", "CrdbAmlFileGenerationAttemptsDao", "GetById", time.Now())
	if id == "" {
		return nil, errors.New("id cannot be empty")
	}
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	var model model.AmlFileGenerationAttempt
	if err := db.Where("id = ?", id).Take(&model).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return model.GetProto(), nil
}

func (d *CrdbAmlFileGenerationAttemptsDao) GetByClientRequestId(ctx context.Context, clientReqId string) (*aml.AmlFileGenerationAttempt, error) {
	defer metric_util.TrackDuration("aml/dao/impl", "CrdbAmlFileGenerationAttemptsDao", "GetByClientRequestId", time.Now())
	if clientReqId == "" {
		return nil, errors.New("clientReqId cannot be empty")
	}
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	var m model.AmlFileGenerationAttempt
	if err := db.Where("client_request_id = ?", clientReqId).Take(&m).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return m.GetProto(), nil
}

func (d *CrdbAmlFileGenerationAttemptsDao) GetByUpdatedAt(ctx context.Context, startTime time.Time, endTime time.Time) ([]*aml.AmlFileGenerationAttempt, error) {
	defer metric_util.TrackDuration("aml/dao/impl", "CrdbAmlFileGenerationAttemptsDao", "GetByUpdatedAt", time.Now())
	var m []*model.AmlFileGenerationAttempt
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	if err := db.Where("updated_at >= ? and updated_at <= ?", startTime, endTime).Order("updated_at desc").Find(&m).Error; err != nil {
		return nil, err
	}
	if len(m) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	var ret []*aml.AmlFileGenerationAttempt
	for _, model := range m {
		ret = append(ret, model.GetProto())
	}
	return ret, nil
}
