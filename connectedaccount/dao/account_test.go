package dao

import (
	"context"
	"fmt"
	"sort"
	"testing"
	"time"

	"github.com/pkg/errors"

	"github.com/stretchr/testify/assert"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epifierrors"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	caPb "github.com/epifi/gamma/api/connected_account"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/connectedaccount/config"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
)

type AaAccountDaoTestSuite struct {
	db           *gormv2.DB
	aaAccountDao AaAccountDao
	conf         *config.Config
	dbName       string
	gconf        *genconf.Config
}

var (
	adts    AaAccountDaoTestSuite
	account = &caPb.AaAccount{
		ActorId:             "actor_1",
		MaskedAccountNumber: "masked_acc_1",
		LinkedAccountRef:    "linked_ref_1",
		Version:             "1.1",
		AccInstrumentType:   caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
		Profile: &caPb.Profile{Holders: &caPb.Holders{
			HolderType: caEnumPb.HolderType_HOLDER_TYPE_SINGLE,
			Holder: []*caPb.Holder{
				{
					Address:        "address_1",
					CkycCompliance: false,
					Dob:            timestampPb.New(time.Now()),
					Email:          "email_1",
					Landline:       "landline_1",
					Mobile:         "mobile_1",
					Name:           "name_1",
					Nominee:        caEnumPb.Nominee_NOMINEE_TYPE_REGISTERED,
					Pan:            "pan_1",
				},
			},
		}},
		FipId:            "finsharebank",
		AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
		AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
		AccountSubType: &caPb.AccountSubType{SubType: &caPb.AccountSubType_DepositAccountType{
			DepositAccountType: caEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS}},
		LastSyncedAt: timestampPb.Now(),
		AaEntity:     caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
	}
	accountFix1 = &caPb.AaAccount{
		Id:                  "8568c12f-1ac3-47f1-9208-3fde2000cda4",
		ActorId:             "actor_test_1",
		MaskedAccountNumber: "masked_acc_test_1",
		LinkedAccountRef:    "linked_acc_test_1",
		AccInstrumentType:   caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT,
		FipId:               "HDFC",
	}
)

func TestAccountDaoCrdb_Create(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx       context.Context
		aaAccount *caPb.AaAccount
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "successfully create record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:       context.Background(),
				aaAccount: account,
			},
			wantErr: false,
		},
		{
			name: "failure create record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:       context.Background(),
				aaAccount: &caPb.AaAccount{MaskedAccountNumber: "random_masked_acc_number"},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := adts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, adts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			c := &AaAccountDaoImpl{
				Pgdb: tx,
			}
			got, err := c.Create(tt.args.ctx, tt.args.aaAccount)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestAaAccountDaoCrdb_GetByLinkedAccountRef(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx              context.Context
		linkedAccountRef string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "success in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:              context.Background(),
				linkedAccountRef: "linked_acc_test_1",
			},
			wantErr: false,
		},
		{
			name: "failure in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:              context.Background(),
				linkedAccountRef: "random_linked_acc_test",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := adts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, adts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			c := &AaAccountDaoImpl{
				Pgdb: tx,
			}
			got, err := c.GetByLinkedAccountRef(tt.args.ctx, tt.args.linkedAccountRef)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByLinkedAccountRef() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestAaAccountDaoCrdb_UpdateAccountByLinkRefNumber(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx              context.Context
		account          *caPb.AaAccount
		accountFieldMask []caPb.AaAccountFieldMask
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "success in updating record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
				account: &caPb.AaAccount{
					ActorId:             "actor_1",
					MaskedAccountNumber: "masked_acc_1",
					LinkedAccountRef:    "linked_acc_test_1",
					Version:             "1.3",
					AccInstrumentType:   caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
					Profile: &caPb.Profile{Holders: &caPb.Holders{
						HolderType: caEnumPb.HolderType_HOLDER_TYPE_SINGLE,
						Holder: []*caPb.Holder{
							{
								Address:        "address_1",
								CkycCompliance: false,
								Dob:            timestampPb.New(time.Now()),
								Email:          "email_1",
								Landline:       "landline_1",
								Mobile:         "mobile_1",
								Name:           "name_1",
								Nominee:        caEnumPb.Nominee_NOMINEE_TYPE_REGISTERED,
								Pan:            "pan_1",
							},
						},
					}},
				},
				accountFieldMask: []caPb.AaAccountFieldMask{caPb.AaAccountFieldMask_AA_ACCOUNT_FIELD_MASK_VERSION, caPb.AaAccountFieldMask_AA_ACCOUNT_FIELD_MASK_PROFILE},
			},
			wantErr: false,
		},
		{
			name: "failure in updating record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
				account: &caPb.AaAccount{
					MaskedAccountNumber: "masked_acc_test_1",
					AccInstrumentType:   caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := adts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, adts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			c := &AaAccountDaoImpl{
				Pgdb: tx,
			}
			err := c.UpdateAccountByLinkRefNumber(tt.args.ctx, tt.args.account, tt.args.accountFieldMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateAccountByLinkRefNumber() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestAccountDaoCrdb_CreateOrUpdate(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx       context.Context
		aaAccount *caPb.AaAccount
	}
	tests := []struct {
		name      string
		fields    fields
		args      args
		wantErr   bool
		isCreated bool
		want      *caPb.AaAccount
	}{
		{
			name: "successfully create record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:       context.Background(),
				aaAccount: account,
			},
			wantErr:   false,
			isCreated: true,
			want:      &caPb.AaAccount{Id: "9ef656fc-3e12-4214-838b-f62485655e40", ActorId: "test-actor"},
		},
		{
			name: "successfully get record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:       context.Background(),
				aaAccount: &caPb.AaAccount{LinkedAccountRef: "linked_acc_test_1", ActorId: "actor_test_1"},
			},
			wantErr:   false,
			isCreated: false,
			want:      &caPb.AaAccount{Id: "8568c12f-1ac3-47f1-9208-3fde2000cda4", ActorId: "actor_test_1"},
		},
		{
			name: "failure create record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:       context.Background(),
				aaAccount: &caPb.AaAccount{LinkedAccountRef: ""},
			},
			wantErr:   true,
			isCreated: false,
			want:      &caPb.AaAccount{Id: "8568c12f-1ac3-47f1-9208-3fde2000cda4"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := adts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, adts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			c := &AaAccountDaoImpl{
				Pgdb: tx,
			}
			got, err := c.CreateOrUpdate(tt.args.ctx, tt.args.aaAccount)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOrUpdateAccount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				if tt.isCreated {
					assert.NotEqual(t, got.Id, tt.want.Id)
				} else {
					assert.Equal(t, got.Id, tt.want.Id)
				}
			}
		})
	}
}

func TestAaAccountDaoCrdb_Update(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx        context.Context
		account    *caPb.AaAccount
		updateMask []caPb.AaAccountFieldMask
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "empty id update failure",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:     context.Background(),
				account: nil,
			},
			wantErr: true,
		},
		{
			name: "successfully update record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
				account: &caPb.AaAccount{
					Id:      accountFix1.GetId(),
					Version: "1.1",
					Profile: &caPb.Profile{Holders: &caPb.Holders{HolderType: caEnumPb.HolderType_HOLDER_TYPE_SINGLE,
						Holder: []*caPb.Holder{
							{
								Address:        "",
								CkycCompliance: true,
							},
						}}},
					ConsentReferenceId: "0a62428f-0f5c-4280-8cff-7cd86f467bcb",
				},
				updateMask: []caPb.AaAccountFieldMask{caPb.AaAccountFieldMask_AA_ACCOUNT_FIELD_MASK_PROFILE,
					caPb.AaAccountFieldMask_AA_ACCOUNT_FIELD_MASK_VERSION},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := adts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, adts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaAccountDaoImpl{
				Pgdb: tx,
			}
			_, err := a.Update(tt.args.ctx, tt.args.account, tt.args.updateMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestAaAccountDaoCrdb_SoftDelete(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx     context.Context
		account *caPb.AaAccount
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "delete failed due to empty id",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "delete failed on non existent id",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:     context.Background(),
				account: &caPb.AaAccount{Id: "67ebf15b-6a3b-4fa2-92cd-d6abf209c38a", LinkedAccountRef: "test"},
			},
			wantErr: false,
		},
		{
			name: "delete successful for account id",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:     context.Background(),
				account: accountFix1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := adts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, adts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaAccountDaoImpl{
				Pgdb: tx,
			}
			err := a.SoftDelete(tt.args.ctx, tt.args.account.GetId())
			if (err != nil) != tt.wantErr {
				t.Errorf("SoftDelete() error = %v, wantErr %v", err, tt.wantErr)
			}
			if err == nil {
				got, err := a.GetByLinkedAccountRef(tt.args.ctx, tt.args.account.GetLinkedAccountRef())
				assert.Equal(t, err, epifierrors.ErrRecordNotFound)
				assert.Nil(t, got)
			}
		})
	}
}

func TestAaAccountDaoCrdb_GetByActorIdAndLinkedAccountRef(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx             context.Context
		actorId         string
		linkedRefNumber string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caPb.AaAccount
		wantErr bool
	}{
		{
			name: "success in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:             context.Background(),
				linkedRefNumber: "linked_acc_test_1",
				actorId:         "actor_test_1",
			},
			wantErr: false,
		},
		{
			name: "mandatory params missing in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:             context.Background(),
				linkedRefNumber: "random_linked_acc_test",
			},
			wantErr: true,
		},
		{
			name: "mandatory params missing in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "failure in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:             context.Background(),
				linkedRefNumber: "random_linked_acc_test",
				actorId:         "actor_test_1",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := adts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, adts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaAccountDaoImpl{
				Pgdb: tx,
			}
			got, err := a.GetByActorIdAndLinkedAccountRef(tt.args.ctx, tt.args.actorId, tt.args.linkedRefNumber)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorIdAndLinkedAccountRef() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestAaAccountDaoCrdb_GetByActorId(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx        context.Context
		actorId    string
		onlyActive bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*caPb.AaAccount
		wantErr bool
	}{
		{
			name: "mandatory params missing in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "success in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:        context.Background(),
				actorId:    "actor_test_1",
				onlyActive: false,
			},
			wantErr: false,
		},
		{
			name: "failure in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:        context.Background(),
				actorId:    "random_actor_test_1",
				onlyActive: false,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := adts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, adts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaAccountDaoImpl{
				Pgdb: tx,
			}
			got, err := a.GetByActorId(tt.args.ctx, tt.args.actorId, tt.args.onlyActive)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestAaAccountDaoCrdb_GetById(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx        context.Context
		id         string
		selectMask []caPb.AaAccountFieldMask
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caPb.AaAccount
		wantErr bool
	}{
		{
			name: "mandatory params missing in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "success in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:        context.Background(),
				id:         "8568c12f-1ac3-47f1-9208-3fde2000cda4",
				selectMask: []caPb.AaAccountFieldMask{},
			},
			wantErr: false,
		},
		{
			name: "failure in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:        context.Background(),
				id:         "random-nonexistent-id",
				selectMask: []caPb.AaAccountFieldMask{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := adts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, adts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaAccountDaoImpl{
				Pgdb: tx,
			}
			got, err := a.GetById(tt.args.ctx, tt.args.id, tt.args.selectMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestAaAccountDaoCrdb_GetByActorIdAndStatus(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx             context.Context
		actorId         string
		accountStatuses []caEnumPb.AccountStatus
		accInstType     []caEnumPb.AccInstrumentType
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*caPb.AaAccount
		wantErr bool
	}{
		{
			name: "mandatory params missing in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "failure in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:             context.Background(),
				actorId:         "random-actor-id",
				accountStatuses: []caEnumPb.AccountStatus{0, 1, 2},
			},
			wantErr: true,
		},
		{
			name: "success in getting record",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:             context.Background(),
				actorId:         "actor_test_3",
				accountStatuses: []caEnumPb.AccountStatus{caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON},
			},
			wantErr: false,
		},
		{
			name: "success in getting record, added account inst type filter",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:             context.Background(),
				actorId:         "actor_test_16",
				accountStatuses: []caEnumPb.AccountStatus{caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON},
				accInstType: []caEnumPb.AccInstrumentType{caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
					caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
			},
			want: []*caPb.AaAccount{
				{Id: "ef896a94-d5a6-414b-abcd-644f27825cf1"}, {Id: "2be8907c-e18f-42bf-bf3a-a35498f7e3b0"},
			},
			wantErr: false,
		},
		{
			name: "success in getting record, added account inst type filter",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:             context.Background(),
				actorId:         "actor_test_16",
				accountStatuses: []caEnumPb.AccountStatus{caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON},
				accInstType: []caEnumPb.AccInstrumentType{caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
					caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
			},
			want: []*caPb.AaAccount{
				{Id: "ef896a94-d5a6-414b-abcd-644f27825cf1"}, {Id: "2be8907c-e18f-42bf-bf3a-a35498f7e3b0"},
			},
			wantErr: false,
		},
		{
			name: "success in getting record, added account inst type filter",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:             context.Background(),
				actorId:         "actor_test_16",
				accountStatuses: []caEnumPb.AccountStatus{caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON},
				accInstType: []caEnumPb.AccInstrumentType{caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
					caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
			},
			want: []*caPb.AaAccount{
				{Id: "ef896a94-d5a6-414b-abcd-644f27825cf1"}, {Id: "2be8907c-e18f-42bf-bf3a-a35498f7e3b0"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := adts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, adts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaAccountDaoImpl{
				Pgdb: tx,
			}
			got, err := a.GetByActorIdAndStatus(tt.args.ctx, tt.args.actorId, tt.args.accountStatuses, tt.args.accInstType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorIdAndStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
			if tt.want != nil {
				compareErr := compareAaAccountsGetByActorIdAndStatusGetByActorIdAndStatus(tt.want, got)
				if compareErr != nil {
					t.Errorf("GetByActorIdAndStatus() compareErr = %v, got and want results are not same", compareErr)
					return
				}
			}
		})
	}
}

func TestAaAccountDaoCrdb_GetBulkById(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx    context.Context
		idList []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*caPb.AaAccount
		wantErr bool
	}{
		{
			name: "success - multiple correct account_ids",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:    context.Background(),
				idList: []string{"8568c12f-1ac3-47f1-9208-3fde2000cda4", "9ef656fc-3e12-4214-838b-f62485655e40"},
			},
			wantErr: false,
		},
		{
			name: "success - multiple correct account_ids with duplicates",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:    context.Background(),
				idList: []string{"8568c12f-1ac3-47f1-9208-3fde2000cda4", "8568c12f-1ac3-47f1-9208-3fde2000cda4"},
			},
			wantErr: false,
		},
		{
			name: "success - 1 correct and 1 incorrect account_id",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
				// 2nd id is a random uuid
				idList: []string{"8568c12f-1ac3-47f1-9208-3fde2000cda4", "a343349b-590c-434a-b3cb-0b2edb8e7b39"},
			},
			wantErr: false,
		},
		{
			name: "fail - two non uuids",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
				// random strings
				idList: []string{"random-id-1", "random-id-2"},
			},
			wantErr: true,
		},
		{
			name: "fail - missing account_id list",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "fail - two incorrect account_ids",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
				// random uuid
				idList: []string{"a343349b-590c-434a-b3cb-0b2edb8e7b39", "57f4aab5-c530-460f-8522-81274ca98ac9"},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := adts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, adts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaAccountDaoImpl{
				Pgdb: tx,
			}
			got, err := a.GetBulkById(tt.args.ctx, tt.args.idList)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBulkById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestAaAccountDaoCrdb_GetByActorIdFipIdAccInstrumentType(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx         context.Context
		actorId     string
		fipId       string
		accInstType caEnumPb.AccInstrumentType
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*caPb.AaAccount
		wantErr bool
	}{
		{
			name: "#1 record found",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:         context.Background(),
				actorId:     "actor_test_1",
				fipId:       "HDFC-FIP",
				accInstType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
			},
			wantErr: false,
		},
		{
			name: "#2 actor id empty",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:         context.Background(),
				actorId:     "",
				fipId:       "HDFC-FIP",
				accInstType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
			},
			wantErr: true,
		},
		{
			name: "#3 fip id empty",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:         context.Background(),
				actorId:     "actor_test_1",
				fipId:       "",
				accInstType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
			},
			wantErr: true,
		},
		{
			name: "#4 accInstType is unspecified",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:         context.Background(),
				actorId:     "actor_test_1",
				fipId:       "HDFC-FIP",
				accInstType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TYPE_UNSPECIFIED,
			},
			wantErr: true,
		},
		{
			name: "#4 record not found",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx:         context.Background(),
				actorId:     "random-actor-id",
				fipId:       "AXIS001",
				accInstType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := adts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, adts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaAccountDaoImpl{
				Pgdb: tx,
			}
			got, err := a.GetByActorIdFipIdAccInstrumentType(tt.args.ctx, tt.args.actorId, tt.args.fipId, tt.args.accInstType, true)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorIdFipIdAccInstrumentType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestAaAccountDaoCrdb_CreateOrUpdateV2(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx     context.Context
		account *caPb.AaAccount
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want1   *caPb.AaAccount
		wantErr bool
	}{
		{
			name: "#1 account not found in list - create and return",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
				account: &caPb.AaAccount{
					ActorId:             "actor_test_1",
					FipId:               "HDFC-FIP",
					AccInstrumentType:   caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
					MaskedAccountNumber: "masked_acc_test_4",
					LinkedAccountRef:    "linked_account_ref_x",
					AaEntity:            caEnumPb.AaEntity_AA_ENTITY_AA_FINVU,
				},
			},
			wantErr: false,
		},
		{
			name: "#2 account found in list - update and return",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
				account: &caPb.AaAccount{
					ActorId:             "actor_test_1",
					FipId:               "HDFC-FIP",
					AccInstrumentType:   caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
					MaskedAccountNumber: "masked_acc_test_1",
					LinkedAccountRef:    "linked_account_ref_x",
					AaEntity:            caEnumPb.AaEntity_AA_ENTITY_AA_FINVU,
					AccountSubType: &caPb.AccountSubType{
						SubType: &caPb.AccountSubType_DepositAccountType{
							DepositAccountType: caEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS,
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "#3 error - account found in list - update and return - but link ref number missing",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
				account: &caPb.AaAccount{
					ActorId:             "actor_test_1",
					FipId:               "HDFC-FIP",
					AccInstrumentType:   caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
					MaskedAccountNumber: "masked_acc_test_1",
					AaEntity:            caEnumPb.AaEntity_AA_ENTITY_AA_FINVU,
				},
			},
			wantErr: true,
		},
		{
			name: "#4 list not found - create and return",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
				account: &caPb.AaAccount{
					ActorId:             "actor_test_1",
					FipId:               "AXIS001",
					AccInstrumentType:   caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
					MaskedAccountNumber: "masked_acc_test_5",
					LinkedAccountRef:    "linked_account_ref_x",
					AaEntity:            caEnumPb.AaEntity_AA_ENTITY_AA_FINVU,
				},
			},
			wantErr: false,
		},
		{
			name: "#5 error - list not found - create and return - but link ref num missing",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
				account: &caPb.AaAccount{
					ActorId:             "actor_test_1",
					FipId:               "test-fip",
					AccInstrumentType:   caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
					MaskedAccountNumber: "masked_acc_test_6",
					AaEntity:            caEnumPb.AaEntity_AA_ENTITY_AA_FINVU,
				},
			},
			wantErr: true,
		},
		{
			name: "#6 account found in list - sub type mismatch - create and return",
			fields: fields{
				DB:    adts.db,
				gconf: adts.gconf,
			},
			args: args{
				ctx: context.Background(),
				account: &caPb.AaAccount{
					ActorId:             "actor_test_1",
					FipId:               "HDFC-FIP",
					AccInstrumentType:   caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
					MaskedAccountNumber: "masked_acc_test_1",
					LinkedAccountRef:    "linked_account_ref_x",
					AaEntity:            caEnumPb.AaEntity_AA_ENTITY_AA_FINVU,
					AccountSubType: &caPb.AccountSubType{
						SubType: &caPb.AccountSubType_DepositAccountType{
							DepositAccountType: caEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_CURRENT,
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := adts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, adts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaAccountDaoImpl{
				Pgdb: tx,
			}
			got, err := a.CreateOrUpdateV2(tt.args.ctx, tt.args.account)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOrUpdateV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func compareAaAccountsGetByActorIdAndStatusGetByActorIdAndStatus(want, got []*caPb.AaAccount) error {
	if len(got) != len(want) {
		return errors.New("got and want data len is not equal")
	}
	sort.Slice(want, func(i, j int) bool {
		return want[i].GetId() < want[j].GetId()
	})
	sort.Slice(got, func(i, j int) bool {
		return want[i].GetId() < want[j].GetId()
	})
	for index := range got {
		if got[index].GetId() != want[index].GetId() {
			return errors.New(fmt.Sprintf("Id mismatch, want Id: %v, got Id: %v", want[index].GetId(), got[index].GetId()))
		}
	}
	return nil
}
