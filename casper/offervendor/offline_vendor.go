package offervendor

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	casperPb "github.com/epifi/gamma/api/casper"
	redemptionPb "github.com/epifi/gamma/api/casper/redemption"
	"github.com/epifi/gamma/casper/redemption/dao"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

// Used to handle offline vendors where vendor apis will not be called for redemption.
// Rather the data dump would be shared with the vendor offline.
type offlineVendor struct {
	offlineRedemptionDao dao.IOfflineRedemptionDao
}

func NewOfflineVendor(offlineRedemptionDao dao.IOfflineRedemptionDao) *offlineVendor {
	return &offlineVendor{offlineRedemptionDao: offlineRedemptionDao}
}

// compile time check to make sure offlineVendor implements IOfferVendor
var _ IOfferVendor = &offlineVendor{}

func (o *offlineVendor) RedeemOffer(ctx context.Context, req *RedeemOfferRequest) (*RedeemOfferResponse, error) {
	vendorName := req.OfferVendor
	if vendorName == casperPb.OfferVendor_UNSPECIFIED {
		return &RedeemOfferResponse{
			RedemptionStatus: FAILED,
		}, nil
	}

	// create entry in offline redemption table
	_, err := o.getOrCreateOfflineRedemptionEntry(ctx, req, vendorName)
	if err != nil {
		// return unspecified status if error is returned while creating entry
		logger.Error(ctx, "error creating offline redemption entry", zap.Error(err))
		return &RedeemOfferResponse{
			RedemptionStatus: IN_PROGRESS,
			OfferDetails:     nil,
		}, nil
	}

	resp := &RedeemOfferResponse{
		RedemptionStatus: SUCCESS,
	}

	// add offer type specific details in response
	switch req.OfferType {
	case casperPb.OfferType_PHYSICAL_MERCHANDISE:
		resp.OfferDetails = &RedeemedOfferDetails{
			PhysicalMerchandiseDetails: &casperPb.PhysicalMerchandiseDetails{
				ShippingAddress: req.RedemptionRequestMetadata.ShippingAddress,
			},
		}

	case casperPb.OfferType_CHARITY:
		resp.OfferDetails = &RedeemedOfferDetails{
			CharityDetails: &casperPb.CharityDetails{
				// todo (utkarsh) : decide if anything is to be added here
			},
		}
	// added default case to avoid lint exhaustive error
	default:
	}

	return resp, nil
}

func (o *offlineVendor) getOrCreateOfflineRedemptionEntry(ctx context.Context, req *RedeemOfferRequest, vendorName casperPb.OfferVendor) (*redemptionPb.OfflineRedemption, error) {
	alreadyCreatedEntry, err := o.offlineRedemptionDao.GetByRefId(ctx, req.ReferenceId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "creating offline redemption entry", zap.String(logger.REDEEMED_OFFER_ID, req.ReferenceId))
	case err != nil:
		return nil, errors.Wrap(err, "error fetching offline redemption entry")
	default:
		logger.Info(ctx, "offline redemption entry already exists", zap.String(logger.REDEEMED_OFFER_ID, req.ReferenceId))
		return alreadyCreatedEntry, nil
	}
	// concurrency issue is handled by uniqueness constraint on RefId
	createReq := &redemptionPb.CreateOfflineRedemptionReq{
		RefId:         req.ReferenceId,
		ActorId:       req.ActorId,
		VendorName:    vendorName,
		OfferType:     req.OfferType,
		RequestSource: req.RequestSource,
	}
	createReq.Metadata = &redemptionPb.OfflineRedemptionMetadata{
		VendorProductMetadata: &redemptionPb.OfflineRedemptionMetadata_VendorProductMetadata{
			ProductId: req.VendorOfferMetadata.GetOfflineVendorOfferMetadata().GetProductId(),
		},
	}

	// add offer type specific metadata
	switch req.OfferType {
	// if offer is of PHYSICAL_MERCHANDISE type then add shipping address to metadata
	case casperPb.OfferType_PHYSICAL_MERCHANDISE:
		createReq.Metadata.ShippingAddress = req.RedemptionRequestMetadata.ShippingAddress
	// added default case to avoid lint exhaustive error
	default:
	}

	newlyCreatedEntry, err := o.offlineRedemptionDao.Create(ctx, createReq)
	if err != nil {
		return nil, errors.Wrap(err, "error creating offline redemption entry")
	}
	return newlyCreatedEntry, nil
}

func (o *offlineVendor) GetRedeemedOffersDetailsRedirectionUrl(ctx context.Context, req *RedeemedOffersDetailsRedirectionUrlRequest) (string, error) {
	return "", fmt.Errorf("vendor does not supports redeemed offer details page redirection url")
}
