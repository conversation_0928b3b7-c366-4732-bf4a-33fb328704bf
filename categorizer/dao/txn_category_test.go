package dao_test

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	categorizerPb "github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/categorizer/dao"
	"github.com/epifi/gamma/categorizer/dao/model"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
)

func TestTxnCategoryDaoImpl_CreateCategories(t *testing.T) {
	testCategory := &categorizerPb.TxnCategory{
		TxnId:                "uuid123",
		ActorId:              "uuuid123",
		OntologyId:           "ontology-id-1",
		ConfidenceScore:      0.1,
		Provenance:           categorizerPb.Provenance_DS,
		DsCategorisationTime: timestampPb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST)),
		ModelVersion:         "1.0.0",
		CreatedAt:            timestampPb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST)),
		UpdatedAt:            timestampPb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST)),
		IsDisplayEnabled:     true,
		DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_FI,
	}
	type args struct {
		ctx           context.Context
		categories    []*categorizerPb.TxnCategory
		filterOptions []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    []*categorizerPb.TxnCategory
		wantErr bool
	}{
		{
			name: "Should successfully create a new category",
			args: args{
				ctx:        context.Background(),
				categories: []*categorizerPb.TxnCategory{testCategory},
			},
			want:    []*categorizerPb.TxnCategory{testCategory},
			wantErr: false,
		},
		{
			name: "categorisation time is null",
			args: args{
				ctx: context.Background(),
				categories: []*categorizerPb.TxnCategory{
					{
						TxnId:                "uuid123",
						ActorId:              "uuid123",
						OntologyId:           "ontology-id-1",
						ConfidenceScore:      0.1,
						Provenance:           categorizerPb.Provenance_DS,
						DsCategorisationTime: nil,
						ModelVersion:         "1.0.0",
						CreatedAt:            timestampPb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST)),
						UpdatedAt:            timestampPb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST)),
						IsDisplayEnabled:     true,
						DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_FI,
					},
				},
			},
			want: []*categorizerPb.TxnCategory{
				{
					TxnId:            "uuid123",
					ActorId:          "uuid123",
					OntologyId:       "ontology-id-1",
					ConfidenceScore:  0.1,
					Provenance:       categorizerPb.Provenance_DS,
					ModelVersion:     "1.0.0",
					CreatedAt:        timestampPb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST)),
					UpdatedAt:        timestampPb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST)),
					IsDisplayEnabled: true,
					DataChannel:      categorizerPb.DataChannel_DATA_CHANNEL_FI,
				},
			},
			wantErr: false,
		},
		{
			name: "duplicate (actor_id,txn_id,ontology_id) category",
			args: args{
				ctx: context.Background(),
				categories: []*categorizerPb.TxnCategory{
					{
						TxnId:                "txn-id-1",
						ActorId:              "actor-id-1",
						OntologyId:           "ontology-id-1",
						ConfidenceScore:      0.1,
						Provenance:           categorizerPb.Provenance_DS,
						DsCategorisationTime: timestampPb.Now(),
						ModelVersion:         "1.0.0",
						CreatedAt:            timestampPb.Now(),
						IsDisplayEnabled:     true,
						DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_FI,
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "successfully add multiple txn categories",
			args: args{
				ctx: context.Background(),
				categories: []*categorizerPb.TxnCategory{
					{
						TxnId:           "uuid5",
						ActorId:         "uuid5",
						OntologyId:      "ontology-id-1",
						ConfidenceScore: 0.1,
						Provenance:      categorizerPb.Provenance_DS,
						CreatedAt:       timestampPb.New(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)),
						UpdatedAt:       timestampPb.New(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)),
					},
					{
						TxnId:           "uuid6",
						ActorId:         "uuid6",
						OntologyId:      "ontology-id-2",
						ConfidenceScore: 0.1,
						Provenance:      categorizerPb.Provenance_DS,

						CreatedAt: timestampPb.New(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)),
						UpdatedAt: timestampPb.New(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)),
					},
				},
			},
			want: []*categorizerPb.TxnCategory{
				{
					TxnId:           "uuid5",
					ActorId:         "uuid5",
					OntologyId:      "ontology-id-1",
					ConfidenceScore: 0.1,
					Provenance:      categorizerPb.Provenance_DS,

					CreatedAt: timestampPb.New(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)),
					UpdatedAt: timestampPb.New(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)),
				},
				{
					TxnId:           "uuid6",
					ActorId:         "uuid6",
					OntologyId:      "ontology-id-2",
					ConfidenceScore: 0.1,
					Provenance:      categorizerPb.Provenance_DS,

					CreatedAt: timestampPb.New(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)),
					UpdatedAt: timestampPb.New(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)),
				},
			},
			wantErr: false,
		},
		{
			name: "upsert txn categories on conflict",
			args: args{
				ctx: context.Background(),
				categories: []*categorizerPb.TxnCategory{
					{
						TxnId:           "txn-id-2",
						ActorId:         "actor-id-8",
						OntologyId:      "ontology-id-1",
						ConfidenceScore: 0.1,
						Provenance:      categorizerPb.Provenance_DS,
						DataChannel:     categorizerPb.DataChannel_DATA_CHANNEL_FI,
					},
					{
						TxnId:           "txn-id-2",
						ActorId:         "actor-id-8",
						OntologyId:      "ontology-id-5",
						ConfidenceScore: 0.1,
						Provenance:      categorizerPb.Provenance_DS,
					},
				},
				filterOptions: []storagev2.FilterOption{
					dao.ClauseOnConflictDoUpdateWithTargetWhere([]string{"txn_id", "actor_id", "ontology_id", "provenance"}, true, nil, "deleted_at is null"),
				},
			},
			want: []*categorizerPb.TxnCategory{
				{
					TxnId:           "txn-id-2",
					ActorId:         "actor-id-8",
					OntologyId:      "ontology-id-1",
					ConfidenceScore: 0.1,
					Provenance:      categorizerPb.Provenance_DS,
					DataChannel:     categorizerPb.DataChannel_DATA_CHANNEL_FI,
				},
				{
					TxnId:            "txn-id-2",
					ActorId:          "actor-id-8",
					OntologyId:       "ontology-id-2",
					IsDisplayEnabled: true,
					ConfidenceScore:  0.7,
					Provenance:       categorizerPb.Provenance_DS,
					DataChannel:      categorizerPb.DataChannel_DATA_CHANNEL_FI,
				},
				{
					TxnId:           "txn-id-2",
					ActorId:         "actor-id-8",
					OntologyId:      "ontology-id-5",
					ConfidenceScore: 0.1,
					Provenance:      categorizerPb.Provenance_DS,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				pkgTest.TruncateAndPopulateRdsFixtures(t, ts.pgdb, ts.conf.CategorizerDB.GetName(), affectedTestTables)
				_, err := dao.NewTxnCategoryDaoImpl(ts.pgdb, ts.idGen).CreateCategories(tt.args.ctx, tt.args.categories, tt.args.filterOptions...)
				if (err != nil) != tt.wantErr {
					t.Errorf("CreateCategories() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !tt.wantErr {
					var got []*categorizerPb.TxnCategory
					var txnActorPair []*dao.TxnIdActorIdPair
					for _, createReq := range tt.args.categories {
						txnActorPair = append(txnActorPair, &dao.TxnIdActorIdPair{
							TxnId:   createReq.GetTxnId(),
							ActorId: createReq.GetActorId(),
						})
					}
					got, err = dao.NewTxnCategoryDaoImpl(ts.pgdb, ts.idGen).BatchGetByTxnIDAndActorID(tt.args.ctx, txnActorPair, []categorizerPb.TxnCategoryFieldMask{
						categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_TXN_ID,
						categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ACTOR_ID,
						categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ONTOLOGY_ID,
						categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_PROVENANCE,
						categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_CATEGORISATION_SOURCE,
						categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_MODEL_VERSION,
						categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_DS_CATEGORISATION_TIME,
						categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_IS_DISPLAY_ENABLED,
						categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_DATA_CHANNEL,
						categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_CONFIDENCE_SCORE,
						categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_CREATED_AT,
						categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_UPDATED_AT,
						categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_DELETED_AT,
					})
					if err != nil {
						t.Errorf("failed to get txn categories: %v", err)
					}
					if len(got) == len(tt.want) {
						for i, gotCategory := range got {
							tt.want[i].CreatedAt = gotCategory.CreatedAt
							tt.want[i].UpdatedAt = gotCategory.UpdatedAt
							tt.want[i].DeletedAt = gotCategory.DeletedAt
							tt.want[i].DsCategorisationTime = gotCategory.DsCategorisationTime
						}
					}
					if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
						t.Errorf("CreateCategories() got = %v,\n want %v \n diff : %v", got, tt.want, diff)
					}
				}
			},
		)
	}
}

func TestTxnCategoryDaoImpl_GetByTxnIDAndActorID(t *testing.T) {
	type args struct {
		ctx      context.Context
		category *categorizerPb.TxnCategory
	}
	tests := []struct {
		name    string
		args    args
		want    []*categorizerPb.TxnCategory
		wantErr bool
	}{
		{
			name: "should successfully fetch ontology-id",
			args: args{
				ctx: context.Background(),
				category: &categorizerPb.TxnCategory{
					TxnId:   "txn-id-1",
					ActorId: "actor-id-1",
				},
			},
			want: []*categorizerPb.TxnCategory{
				{
					Id:                   "d09e5b74-9abf-11ec-b909-0242ac120002",
					TxnId:                "txn-id-1",
					ActorId:              "actor-id-1",
					OntologyId:           "ontology-id-1",
					ConfidenceScore:      0.7,
					Provenance:           categorizerPb.Provenance_DS,
					DsCategorisationTime: timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					CreatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					UpdatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					DeletedAt:            nil,
					ModelVersion:         "",
					IsDisplayEnabled:     true,
					DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_FI,
					CategorisationSource: categorizerPb.CategorisationSource_CATEGORISATION_SOURCE_UNSPECIFIED,
				},
			},
			wantErr: false,
		},
		{
			name: "no ontology-id found",
			args: args{
				ctx: context.Background(),
				category: &categorizerPb.TxnCategory{
					TxnId:   "txn-id-2",
					ActorId: "actor-id-1",
				},
			},

			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				pkgTest.TruncateAndPopulateRdsFixtures(t, ts.pgdb, ts.conf.CategorizerDB.GetName(), affectedTestTables)
				got, err := dao.NewTxnCategoryDaoImpl(ts.pgdb, ts.idGen).GetByTxnIDAndActorID(
					tt.args.ctx, tt.args.category.TxnId, tt.args.category.ActorId,
				)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetByTxnIDAndActorID() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
					t.Errorf("CreateCategories() got = %v,\n want %v \n diff : %v", got, tt.want, diff)
				}
			},
		)
	}
}

func TestTxnCategoryDaoImpl_UpdateByTxnIdAndActorId(t *testing.T) {

	type args struct {
		ctx                context.Context
		txnId              string
		actorId            string
		fieldMasks         []categorizerPb.TxnCategoryFieldMask
		updatedTxnCategory *categorizerPb.TxnCategory
		filterOption       []storagev2.FilterOption
	}
	tests := []struct {
		name                         string
		args                         args
		wantErr                      bool
		want                         []*categorizerPb.TxnCategory
		GetByTxnIdActorIdValidateErr error
	}{
		{
			name: "successfully update provenance scores of all categories of actor1 and txn1",
			args: args{
				ctx:     context.Background(),
				txnId:   "txn-id-1",
				actorId: "actor-id-1",
				fieldMasks: []categorizerPb.TxnCategoryFieldMask{
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_CONFIDENCE_SCORE,
				},
				updatedTxnCategory: &categorizerPb.TxnCategory{
					ConfidenceScore: 0.79,
				},
				filterOption: nil,
			},
			want: []*categorizerPb.TxnCategory{
				{
					Id:                   "d09e5b74-9abf-11ec-b909-0242ac120002",
					TxnId:                "txn-id-1",
					ActorId:              "actor-id-1",
					OntologyId:           "ontology-id-1",
					ConfidenceScore:      0.79,
					Provenance:           categorizerPb.Provenance_DS,
					ModelVersion:         "",
					IsDisplayEnabled:     true,
					DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_FI,
					CategorisationSource: 0,
					DsCategorisationTime: timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					CreatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					UpdatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
				},
			},
			wantErr: false,
		},
		{
			name: "successfully soft delete all categories of actor1 and txn1",
			args: args{
				ctx:     context.Background(),
				txnId:   "txn-id-1",
				actorId: "actor-id-1",
				fieldMasks: []categorizerPb.TxnCategoryFieldMask{
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_UPDATED_AT,
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_DELETED_AT,
				},
				updatedTxnCategory: &categorizerPb.TxnCategory{
					UpdatedAt: timestampPb.New(time.Date(2022, 06, 11, 00, 00, 00, 0, time.UTC)),
					DeletedAt: timestampPb.New(time.Date(2022, 06, 11, 00, 00, 00, 0, time.UTC)),
				},
				filterOption: nil,
			},
			GetByTxnIdActorIdValidateErr: epifierrors.ErrRecordNotFound,
			want:                         nil,
			wantErr:                      false,
		},
		{
			name: "Fail if no field mask is sent in request",
			args: args{
				ctx:                context.Background(),
				txnId:              "txn-id-1",
				actorId:            "actor-id-1",
				fieldMasks:         []categorizerPb.TxnCategoryFieldMask{},
				updatedTxnCategory: nil,
				filterOption:       nil,
			},
			wantErr: true,
		},
		{
			name: "Fail if txnId is empty",
			args: args{
				ctx:                context.Background(),
				txnId:              "",
				actorId:            "actor-id-1",
				fieldMasks:         []categorizerPb.TxnCategoryFieldMask{},
				updatedTxnCategory: nil,
				filterOption:       nil,
			},
			wantErr: true,
		},
		{
			name: "Fail if actorId is empty",
			args: args{
				ctx:                context.Background(),
				txnId:              "txn-id-1",
				actorId:            "",
				fieldMasks:         []categorizerPb.TxnCategoryFieldMask{},
				updatedTxnCategory: nil,
				filterOption:       nil,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				pkgTest.TruncateAndPopulateRdsFixtures(t, ts.pgdb, ts.conf.CategorizerDB.GetName(), affectedTestTables)
				err := dao.NewTxnCategoryDaoImpl(ts.pgdb, ts.idGen).UpdateByTxnIdAndActorId(tt.args.ctx, tt.args.txnId, tt.args.actorId,
					tt.args.fieldMasks, tt.args.updatedTxnCategory, tt.args.filterOption...)
				if (err != nil) != tt.wantErr {
					t.Errorf("UpdateByTxnIdAndActorId() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !tt.wantErr {
					got, err2 := dao.NewTxnCategoryDaoImpl(ts.pgdb, ts.idGen).GetByTxnIDAndActorID(context.Background(), tt.args.txnId, tt.args.actorId)
					if !errors.Is(err2, tt.GetByTxnIdActorIdValidateErr) {
						t.Errorf("UpdateByTxnIdAndActorId() falied validate using GetByTxnIDAndActorID() gotErr : %v. wantErr : %v", err2, tt.GetByTxnIdActorIdValidateErr)
					}
					if len(got) == len(tt.want) {
						for i, gotCategory := range got {
							tt.want[i].UpdatedAt = gotCategory.UpdatedAt
						}
					}
					if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
						t.Errorf("UpdateByTxnIdAndActorId() got = %v,\n want %v \n diff : %v", got, tt.want, diff)
					}
				}
			})
	}
}

func TestTxnCategoryDaoImpl_DeleteByTxnIdAndActorId(t *testing.T) {
	type args struct {
		ctx          context.Context
		txnId        string
		actorId      string
		filterOption []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "successfully delete categories for txn1 and actor1",
			args: args{
				ctx:          context.Background(),
				txnId:        "txn-id-1",
				actorId:      "actor-id-1",
				filterOption: nil,
			},
			wantErr: false,
		},
		{
			name: "fail if txnId is empty",
			args: args{
				ctx:          context.Background(),
				txnId:        "",
				actorId:      "actor-1",
				filterOption: nil,
			},
			wantErr: true,
		},
		{
			name: "fail if actorId is empty ",
			args: args{
				ctx:          context.Background(),
				txnId:        "txn-1",
				actorId:      "",
				filterOption: nil,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, ts.pgdb, ts.conf.CategorizerDB.GetName(), affectedTestTables)
			err := dao.NewTxnCategoryDaoImpl(ts.pgdb, ts.idGen).DeleteByTxnIdAndActorId(tt.args.ctx, tt.args.txnId, tt.args.actorId,
				tt.args.filterOption...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Delete() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				var txnCategories []*model.TxnCategory
				var db *gorm.DB
				db = ts.pgdb
				err2 := db.Raw("select * from txn_categories where txn_id = ? AND actor_id = ?", tt.args.txnId, tt.args.actorId).Scan(&txnCategories).Error
				if err2 != nil {
					t.Errorf("DeleteByTxnIdAndActorId() failed to validate if entries were successfully soft deleted : %v", err2)
					return
				}
				if len(txnCategories) == 0 {
					t.Errorf("DeleteByTxnIdAndActorId() found no entry in raw query response, error")
					return
				}
				validateErr := validateTxnCategoriesAreSoftDeleted(txnCategories)
				if validateErr != nil {
					t.Errorf("DeleteByTxnIdAndActorId() : %v", validateErr)
					return
				}
			}
		})
	}
}

func TestTxnCategoryDaoImpl_BatchGetByTxnIDAndActorID(t *testing.T) {
	type args struct {
		ctx                  context.Context
		txnIdAndActorIdPairs []*dao.TxnIdActorIdPair
		fieldMasks           []categorizerPb.TxnCategoryFieldMask
		filterOption         []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    []*categorizerPb.TxnCategory
		wantErr bool
	}{
		{
			name: "successfully get categories for multiple txnIds and actorIds pair",
			args: args{
				ctx: context.Background(),
				txnIdAndActorIdPairs: []*dao.TxnIdActorIdPair{
					{
						TxnId:   "txn-id-1",
						ActorId: "actor-id-1",
					},
					{
						TxnId:   "txn-id-2",
						ActorId: "actor-id-2",
					},
				},
				fieldMasks: []categorizerPb.TxnCategoryFieldMask{
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_TXN_ID,
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ACTOR_ID,
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ONTOLOGY_ID,
				},
				filterOption: nil,
			},
			want: []*categorizerPb.TxnCategory{
				{
					TxnId:      "txn-id-1",
					ActorId:    "actor-id-1",
					OntologyId: "ontology-id-1",
					CreatedAt:  timestampPb.New(time.Date(1, 1, 1, 0, 0, 0, 0, time.UTC)),
					UpdatedAt:  timestampPb.New(time.Date(1, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			wantErr: false,
		},
		{
			name: "Successfully fetch empty response for uncategorized txns",
			args: args{
				ctx: context.Background(),
				txnIdAndActorIdPairs: []*dao.TxnIdActorIdPair{
					{
						TxnId:   "txn-id-3",
						ActorId: "actor-id-1",
					},
					{
						TxnId:   "txn-id-2",
						ActorId: "actor-id-2",
					},
				},
				fieldMasks: []categorizerPb.TxnCategoryFieldMask{
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_TXN_ID,
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ACTOR_ID,
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ONTOLOGY_ID,
				},
				filterOption: nil,
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "successfully return empty list of txnCategories for empty txnIdActorIdPair in request",
			args: args{
				ctx:                  context.Background(),
				txnIdAndActorIdPairs: []*dao.TxnIdActorIdPair{},
				fieldMasks: []categorizerPb.TxnCategoryFieldMask{
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_TXN_ID,
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ACTOR_ID,
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ONTOLOGY_ID,
				},
				filterOption: nil,
			},
			want:    []*categorizerPb.TxnCategory{},
			wantErr: false,
		},
		{
			name: "failure on passing empty fieldMasks",
			args: args{
				ctx: context.Background(),
				txnIdAndActorIdPairs: []*dao.TxnIdActorIdPair{
					{
						TxnId:   "txn-id-3",
						ActorId: "actor-id-1",
					},
				},
				fieldMasks:   []categorizerPb.TxnCategoryFieldMask{},
				filterOption: nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "successfully fetch DS categories using filter options",
			args: args{
				ctx: context.Background(),
				txnIdAndActorIdPairs: []*dao.TxnIdActorIdPair{
					{
						TxnId:   "txn-id-1",
						ActorId: "actor-id-1",
					},
				},
				fieldMasks: []categorizerPb.TxnCategoryFieldMask{
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_TXN_ID,
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ACTOR_ID,
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ONTOLOGY_ID,
				},
				filterOption: []storagev2.FilterOption{
					dao.WithProvenance(categorizerPb.Provenance_DS),
				},
			},
			want: []*categorizerPb.TxnCategory{
				{
					TxnId:      "txn-id-1",
					ActorId:    "actor-id-1",
					OntologyId: "ontology-id-1",
					CreatedAt:  timestampPb.New(time.Date(1, 1, 1, 0, 0, 0, 0, time.UTC)),
					UpdatedAt:  timestampPb.New(time.Date(1, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			wantErr: false,
		},
		{
			name: "successfully fetch zero user categories using filter options",
			args: args{
				ctx: context.Background(),
				txnIdAndActorIdPairs: []*dao.TxnIdActorIdPair{
					{
						TxnId:   "txn-id-1",
						ActorId: "actor-id-1",
					},
				},
				fieldMasks: []categorizerPb.TxnCategoryFieldMask{
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_TXN_ID,
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ACTOR_ID,
					categorizerPb.TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ONTOLOGY_ID,
				},
				filterOption: []storagev2.FilterOption{
					dao.WithProvenance(categorizerPb.Provenance_USER),
				},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, ts.pgdb, ts.conf.CategorizerDB.GetName(), affectedTestTables)
			got, err := dao.NewTxnCategoryDaoImpl(ts.pgdb, ts.idGen).BatchGetByTxnIDAndActorID(tt.args.ctx, tt.args.txnIdAndActorIdPairs,
				tt.args.fieldMasks, tt.args.filterOption...)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetByTxnIDAndActorID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("BatchGetByTxnIDAndActorID() got = %v,\n want %v \n diff : %v", got, tt.want, diff)
			}
		})
	}
}

func TestTxnCategoryDaoImpl_BatchDeleteByTxnIdAndActorId(t *testing.T) {
	type args struct {
		ctx               context.Context
		txnIdActorIdPairs []*dao.TxnIdActorIdPair
		filterOption      []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "successfully delete all categories for a txnId and ActorId",
			args: args{
				ctx: context.Background(),
				txnIdActorIdPairs: []*dao.TxnIdActorIdPair{
					{
						TxnId:   "txn-id-1",
						ActorId: "actor-id-1",
					},
				},
				filterOption: nil,
			},
			wantErr: false,
		},
		{
			name: "successfully delete all categories for txnIds and ActorIds",
			args: args{
				ctx: context.Background(),
				txnIdActorIdPairs: []*dao.TxnIdActorIdPair{
					{
						TxnId:   "txn-id-1",
						ActorId: "actor-id-1",
					},
					{
						TxnId:   "txn-id-2",
						ActorId: "actor-id-2",
					},
					{
						TxnId:   "txn-id-2",
						ActorId: "actor-id-8",
					},
					{
						TxnId:   "txn-id-6",
						ActorId: "actor-id-8",
					},
				},
				filterOption: nil,
			},
			wantErr: false,
		},
		{
			name: "successfully delete multiple DS categories by txnIds and ActorIds",
			args: args{
				ctx: context.Background(),
				txnIdActorIdPairs: []*dao.TxnIdActorIdPair{
					{
						TxnId:   "txn-id-1",
						ActorId: "actor-id-1",
					},
					{
						TxnId:   "txn-id-2",
						ActorId: "actor-id-2",
					},
					{
						TxnId:   "txn-id-2",
						ActorId: "actor-id-8",
					},
					{
						TxnId:   "txn-id-6",
						ActorId: "actor-id-8",
					},
				},
				filterOption: []storagev2.FilterOption{dao.WithProvenance(categorizerPb.Provenance_DS)},
			},
			wantErr: false,
		},
		{
			name: "Failure to delete txn categories for empty txnIdAndActorIdPairs",
			args: args{
				ctx:               context.Background(),
				txnIdActorIdPairs: []*dao.TxnIdActorIdPair{},
				filterOption:      nil,
			},
			wantErr: true,
		},
		{
			name: "Failure to delete txn categories for empty txnId id in txnIdAndActorIdPairs",
			args: args{
				ctx: context.Background(),
				txnIdActorIdPairs: []*dao.TxnIdActorIdPair{
					{
						TxnId:   "",
						ActorId: "actor-id-1",
					},
				},
				filterOption: nil,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, ts.pgdb, ts.conf.CategorizerDB.GetName(), affectedTestTables)
			if err := dao.NewTxnCategoryDaoImpl(ts.pgdb, ts.idGen).BatchDeleteByTxnIdAndActorId(tt.args.ctx, tt.args.txnIdActorIdPairs,
				tt.args.filterOption...); (err != nil) != tt.wantErr {
				t.Errorf("BatchDeleteByTxnIdAndActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				var listOfTxnIdActorIdsToDelete [][]interface{}
				for _, entry := range tt.args.txnIdActorIdPairs {
					listOfTxnIdActorIdsToDelete = append(listOfTxnIdActorIdsToDelete, []interface{}{entry.TxnId, entry.ActorId})
				}
				var txnCategories []*model.TxnCategory
				var db *gorm.DB
				db = ts.pgdb
				err2 := db.Raw("select * from txn_categories where (txn_id, actor_id) IN (?)", listOfTxnIdActorIdsToDelete).Scan(&txnCategories).Error
				if err2 != nil {
					t.Errorf("BatchDeleteByTxnIdAndActorId() failed to validate if entries were successfully soft deleted : %v", err2)
					return
				}
				if len(txnCategories) == 0 {
					t.Errorf("BatchDeleteByTxnIdAndActorId() found no entry in raw query response, error")
					return
				}
				validateErr := validateTxnCategoriesAreSoftDeleted(txnCategories)
				if validateErr != nil {
					t.Errorf("BatchDeleteByTxnIdAndActorId() : %v", validateErr)
					return
				}
			}
		})
	}
}

func TestTxnCategoryDaoImpl_BatchGetByActorId(t *testing.T) {
	type args struct {
		ctx          context.Context
		actorId      string
		filterOption []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    []*categorizerPb.TxnCategory
		wantErr bool
	}{
		{
			name: "Successfully get all the txns for an actor",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-id-1",
				filterOption: nil,
			},
			want: []*categorizerPb.TxnCategory{
				{
					Id:                   "d09e5b74-9abf-11ec-b909-0242ac120002",
					TxnId:                "txn-id-1",
					ActorId:              "actor-id-1",
					OntologyId:           "ontology-id-1",
					ConfidenceScore:      0.7,
					Provenance:           categorizerPb.Provenance_DS,
					IsDisplayEnabled:     true,
					DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_FI,
					DsCategorisationTime: timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					CreatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					UpdatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
				},
			},
			wantErr: false,
		},
		{
			name: "Get zero txn categories for an actor",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-id-2",
				filterOption: nil,
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "Get txn categories for an actor with provenance, data_channel and updated_at filter",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-8",
				filterOption: []storagev2.FilterOption{
					dao.WithProvenance(categorizerPb.Provenance_DS),
					dao.WithDataChannel(categorizerPb.DataChannel_DATA_CHANNEL_FI),
					dao.GreaterThanUpdatedAtTime(timestampPb.New(time.Date(2022, 6, 24, 0, 0, 0, 0, datetime.IST))),
				},
			},
			want: []*categorizerPb.TxnCategory{
				{
					Id:                   "1",
					TxnId:                "txn-id-2",
					ActorId:              "actor-id-8",
					OntologyId:           "ontology-id-1",
					ConfidenceScore:      0.7,
					Provenance:           categorizerPb.Provenance_DS,
					IsDisplayEnabled:     true,
					DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_FI,
					DsCategorisationTime: timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					CreatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					UpdatedAt:            timestampPb.New(time.Date(2022, 6, 25, 11, 0, 0, 0, time.Local)),
				},
				{
					Id:                   "4",
					TxnId:                "txn-id-5",
					ActorId:              "actor-id-8",
					OntologyId:           "ontology-id-1",
					ConfidenceScore:      0.7,
					Provenance:           categorizerPb.Provenance_DS,
					IsDisplayEnabled:     false,
					DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_FI,
					DsCategorisationTime: timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					CreatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					UpdatedAt:            timestampPb.New(time.Date(2022, 6, 25, 11, 0, 0, 0, time.Local)),
				},
			},
			wantErr: false,
		},
		{
			name: "Get txn categories for an actor with provenance, data_channel, display enabled and updated_at filter",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-8",
				filterOption: []storagev2.FilterOption{
					dao.WithProvenance(categorizerPb.Provenance_DS),
					dao.WithDataChannel(categorizerPb.DataChannel_DATA_CHANNEL_FI),
					dao.GreaterThanUpdatedAtTime(timestampPb.New(time.Date(2022, 6, 24, 0, 0, 0, 0, datetime.IST))),
					dao.WithIsDisplayEnabled(true),
				},
			},
			want: []*categorizerPb.TxnCategory{
				{
					Id:                   "1",
					TxnId:                "txn-id-2",
					ActorId:              "actor-id-8",
					OntologyId:           "ontology-id-1",
					ConfidenceScore:      0.7,
					Provenance:           categorizerPb.Provenance_DS,
					IsDisplayEnabled:     true,
					DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_FI,
					DsCategorisationTime: timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					CreatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					UpdatedAt:            timestampPb.New(time.Date(2022, 6, 25, 11, 0, 0, 0, time.Local)),
				},
			},
			wantErr: false,
		},
		{
			name: "Get txn categories for an actor with user provenance, AA data_channel, display enabled and updated_at filter",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-8",
				filterOption: []storagev2.FilterOption{
					dao.WithProvenance(categorizerPb.Provenance_USER),
					dao.WithDataChannel(categorizerPb.DataChannel_DATA_CHANNEL_AA),
					dao.GreaterThanUpdatedAtTime(timestampPb.New(time.Date(2022, 6, 24, 0, 0, 0, 0, datetime.IST))),
					dao.WithIsDisplayEnabled(true),
				},
			},
			want: []*categorizerPb.TxnCategory{
				{
					Id:                   "6",
					TxnId:                "txn-id-8",
					ActorId:              "actor-id-8",
					OntologyId:           "ontology-id-1",
					ConfidenceScore:      0.7,
					Provenance:           categorizerPb.Provenance_USER,
					IsDisplayEnabled:     true,
					DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_AA,
					DsCategorisationTime: timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					CreatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					UpdatedAt:            timestampPb.New(time.Date(2022, 6, 25, 11, 0, 0, 0, time.Local)),
				},
			},
			wantErr: false,
		},
		{
			name: "Get txn categories for an actor with  updated_at filter",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-8",
				filterOption: []storagev2.FilterOption{
					dao.GreaterThanUpdatedAtTime(timestampPb.New(time.Date(2022, 6, 24, 0, 0, 0, 0, datetime.IST))),
				},
			},
			want: []*categorizerPb.TxnCategory{
				{
					Id:                   "1",
					TxnId:                "txn-id-2",
					ActorId:              "actor-id-8",
					OntologyId:           "ontology-id-1",
					ConfidenceScore:      0.7,
					Provenance:           categorizerPb.Provenance_DS,
					IsDisplayEnabled:     true,
					DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_FI,
					DsCategorisationTime: timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					CreatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					UpdatedAt:            timestampPb.New(time.Date(2022, 6, 25, 11, 0, 0, 0, time.Local)),
				},
				{
					Id:                   "4",
					TxnId:                "txn-id-5",
					ActorId:              "actor-id-8",
					OntologyId:           "ontology-id-1",
					ConfidenceScore:      0.7,
					Provenance:           categorizerPb.Provenance_DS,
					IsDisplayEnabled:     false,
					DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_FI,
					DsCategorisationTime: timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					CreatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					UpdatedAt:            timestampPb.New(time.Date(2022, 6, 25, 11, 0, 0, 0, time.Local)),
				},
				{
					Id:                   "5",
					TxnId:                "txn-id-6",
					ActorId:              "actor-id-8",
					OntologyId:           "ontology-id-1",
					ConfidenceScore:      0.7,
					Provenance:           categorizerPb.Provenance_DS,
					IsDisplayEnabled:     true,
					DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_AA,
					DsCategorisationTime: timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					CreatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					UpdatedAt:            timestampPb.New(time.Date(2022, 6, 25, 11, 0, 0, 0, time.Local)),
				},
				{
					Id:                   "6",
					TxnId:                "txn-id-8",
					ActorId:              "actor-id-8",
					OntologyId:           "ontology-id-1",
					ConfidenceScore:      0.7,
					Provenance:           categorizerPb.Provenance_USER,
					IsDisplayEnabled:     true,
					DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_AA,
					DsCategorisationTime: timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					CreatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					UpdatedAt:            timestampPb.New(time.Date(2022, 6, 25, 11, 0, 0, 0, time.Local)),
				},
				{
					Id:                   "8",
					TxnId:                "txn-id-9",
					ActorId:              "actor-id-8",
					OntologyId:           "ontology-id-1",
					ConfidenceScore:      0.7,
					Provenance:           categorizerPb.Provenance_DS,
					IsDisplayEnabled:     true,
					DataChannel:          categorizerPb.DataChannel_DATA_CHANNEL_AA,
					DsCategorisationTime: timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					CreatedAt:            timestampPb.New(time.Date(2022, 1, 28, 11, 0, 0, 0, time.Local)),
					UpdatedAt:            timestampPb.New(time.Date(2022, 6, 25, 11, 0, 0, 0, time.Local)),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, ts.pgdb, ts.conf.CategorizerDB.GetName(), affectedTestTables)
			got, err := dao.NewTxnCategoryDaoImpl(ts.pgdb, ts.idGen).BatchGetByActorId(tt.args.ctx, tt.args.actorId, tt.args.filterOption...)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if len(got) != len(tt.want) {
				t.Errorf("BatchGetByActorId() got = %v, want %v", got, tt.want)
				return
			}

			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("BatchGetByActorId() got = %v,\n want %v \n diff : %v", got, tt.want, diff)
			}
		})
	}
}

func validateTxnCategoriesAreSoftDeleted(txnCategories []*model.TxnCategory) error {
	for _, entry := range txnCategories {
		if entry == nil {
			return fmt.Errorf("found empty entry in dao resp")
		}
		updatedAt := entry.UpdatedAt
		deletedAt := entry.DeletedAt
		if !deletedAt.Valid {
			return fmt.Errorf("deletedAt is not valid")
		}
		if updatedAt.Unix() < deletedAt.Time.Unix() {
			return fmt.Errorf("updatedAt : %v cannot be before DeletedAt : %v", updatedAt, deletedAt.Time)
		}
		curTime := time.Now()
		timeDiff := curTime.Unix() - deletedAt.Time.Unix()
		if timeDiff < 0 {
			return fmt.Errorf("cur time : %v cannot be before deletedAt : %v", curTime, deletedAt.Time)
		}
		if timeDiff > int64(60*10) {
			return fmt.Errorf("cur time : %v and deletedAt : %v differ by more than 10 min", curTime, deletedAt.Time)
		}
	}
	return nil
}
