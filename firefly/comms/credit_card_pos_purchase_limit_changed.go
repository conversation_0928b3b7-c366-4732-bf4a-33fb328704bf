// nolint
package comms

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	commsPb "github.com/epifi/gamma/api/comms"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffCommsPb "github.com/epifi/gamma/api/firefly/comms"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/firefly/helper"
	ffHelper "github.com/epifi/gamma/firefly/helper"
)

var (
	posLimitChangedEventType = "POS purchase limit changed"
	newLimitText             = "NEW LIMIT"
	greenTickIconUrl         = "https://epifi-icons.pointz.in/credit_card_images/cc_comms_green_tick"
)

type CreditCardPosPurchaseLimitChangedRule struct {
	commsDataHelper *ffHelper.CommsDataHelper
}

func NewCreditCardPosPurchaseLimitChangedRule(commsDataHelper *ffHelper.CommsDataHelper) *CreditCardPosPurchaseLimitChangedRule {
	return &CreditCardPosPurchaseLimitChangedRule{
		commsDataHelper: commsDataHelper,
	}
}

func (s *CreditCardPosPurchaseLimitChangedRule) GetComms(ctx context.Context, data IActionData) (res []commsPb.CommMessage, resErr error) {
	cardRequestActionData, ok := data.(*ffCommsPb.ActionData_CardRequestActionData)
	if !ok {
		return
	}
	if cardRequestActionData.CardRequestActionData.GetCardRequest().GetWorkflow() == ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_LIMIT_CHANGE &&
		cardRequestActionData.CardRequestActionData.GetCardRequest().GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS &&
		cardRequestActionData.CardRequestActionData.GetCardRequest().GetRequestDetails().GetLimitChangeRequestDetails().GetControlType() == ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS {

		getCcReq := &ffPb.GetCreditCardRequest{
			GetBy:            &ffPb.GetCreditCardRequest_ActorId{ActorId: cardRequestActionData.GetActorId()},
			SelectFieldMasks: []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO},
		}

		if cardRequestActionData.CardRequestActionData.GetCardRequest().GetCardId() != "" {
			getCcReq.GetBy = &ffPb.GetCreditCardRequest_CreditCardId{CreditCardId: cardRequestActionData.CardRequestActionData.GetCardRequest().GetCardId()}
		}
		creditCardDetails, err := s.commsDataHelper.GetCreditCardDetails(ctx, getCcReq)
		if err != nil {
			logger.Error(ctx, "error while fetching credit card details", zap.Error(err))
			return nil, err
		}
		ccLastFourDigits, err := ffHelper.GetCreditCardLastKDigits(creditCardDetails.GetCreditCard().GetBasicInfo().GetMaskedCardNumber(), 4)
		if err != nil {
			logger.Error(ctx, "error while fetching credit card last four digits", zap.Error(err))
			return nil, err
		}

		// sms comms
		if !helper.IsSmsDisabledForSmsType(commsPb.SmsType_CREDIT_CARD_POS_PURCHASE_LIMIT_CHANGED) {
			res = append(res, &commsPb.SendMessageRequest_Sms{
				Sms: &commsPb.SMSMessage{
					SmsOption: &commsPb.SmsOption{
						Option: &commsPb.SmsOption_CreditCardPosPurchaseLimitChangedSmsOption{
							CreditCardPosPurchaseLimitChangedSmsOption: &commsPb.CreditCardPosPurchaseLimitChangedSmsOption{
								SmsType: commsPb.SmsType_CREDIT_CARD_POS_PURCHASE_LIMIT_CHANGED,
								Option: &commsPb.CreditCardPosPurchaseLimitChangedSmsOption_CreditCardPosPurchaseLimitChangedSmsOptionV1{
									CreditCardPosPurchaseLimitChangedSmsOptionV1: &commsPb.CreditCardPosPurchaseLimitChangedSmsOptionV1{
										TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
										HelplineNumber:  helplineNumber,
									},
								},
							},
						},
					},
				},
			})
		}
		res = append(res, getPosLimitChangedEmailMessage(cardRequestActionData, ccLastFourDigits))
	}
	return
}

func getPosLimitChangedEmailMessage(cardRequestActionData *ffCommsPb.ActionData_CardRequestActionData, ccLastFourDigits string) commsPb.CommMessage {

	commsEmailOption := &commsPb.CreditCardControlsEmailOption_CreditCardControlsEmailOptionV2{
		CreditCardControlsEmailOptionV2: &commsPb.CreditCardControlsEmailOptionV2{
			TemplateVersion:      commsPb.TemplateVersion_VERSION_V2,
			CardControlEventType: settingsUpdateEventType,
			LastFourDigits:       ccLastFourDigits,
			IconUrl:              greenTickIconUrl,
			Heading:              posLimitChangedEventType,
			Description:          fmt.Sprintf("Purchase limit of your Credit Card ending with xx%s for POS transactions has successfully been modified.", ccLastFourDigits),
			BoxTitle:             newLimitText,
			BoxDescription:       moneyPkg.ToDisplayStringInIndianFormat(cardRequestActionData.CardRequestActionData.GetCardRequest().GetRequestDetails().GetLimitChangeRequestDetails().GetUpdatedLimitValue(), 0, true),
			BottomDescription:    fmt.Sprintf("If you didn't do this, report it via: %s", helplineNumber),
			BoxColorClass:        bgLightGray,
		},
	}
	return ffHelper.GetCardControlsEmailMessage(commsEmailOption)
}
