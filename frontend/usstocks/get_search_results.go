// nolint: funlen
// nolint: dupl
package usstocks

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	headerFe "github.com/epifi/gamma/api/frontend/header"
	usstocksFePb "github.com/epifi/gamma/api/frontend/usstocks"
	usstocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	usstocksUi "github.com/epifi/gamma/frontend/usstocks/ui"
	"github.com/epifi/gamma/frontend/usstocks/utils"
)

const (
	NoStocksFoundImageUrl = "https://epifi-icons.pointz.in/usstocks_images/NotePad.png"
	NoStocksFoundTitle    = "No stocks found!"
	NoStocksFoundSubtitle = "Please modify your search and try again"
)

func (s *Service) GetSearchResults(ctx context.Context, req *usstocksFePb.GetSearchResultsRequest) (*usstocksFePb.GetSearchResultsResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	platform := req.GetReq().GetAuth().GetDevice().GetPlatform()
	version := req.GetReq().GetAppVersionCode()
	sortOption := req.GetFilter().GetSortOption()
	if sortOption == usstocksFePb.SortOptionType_SORT_OPTION_TYPE_UNSPECIFIED {
		sortOption = usstocksFePb.SortOptionType_SORT_OPTION_TYPE_RELEVANCY
	}
	beSortOption := utils.ConvertFeSortOptionTypeToBe(sortOption)

	sortOrder := usstocksCatalogPb.SearchStocksRequest_DESCENDING
	if beSortOption == usstocksCatalogPb.SortOptionType_SORT_OPTION_TYPE_TRACKING_ERROR || beSortOption == usstocksCatalogPb.SortOptionType_SORT_OPTION_TYPE_EXPENSE_RATIO {
		sortOrder = usstocksCatalogPb.SearchStocksRequest_ASCENDING
	}
	resp, err := s.catalogManagerClient.SearchStocks(ctx, &usstocksCatalogPb.SearchStocksRequest{
		PageContext: req.GetPageContext(),
		SortParam:   beSortOption,
		SortOrder:   sortOrder,
		ActorId:     req.GetReq().GetAuth().GetActorId(),
		SearchText:  req.GetFilter().GetSearchQuery(),
		Platform:    platform,
		Version:     version,
	})

	if grpcError := epifigrpc.RPCError(resp, err); grpcError != nil {
		if resp.GetStatus().IsResourceExhausted() {
			logger.Error(ctx, "resource exhausted in SearchStocks rpc", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(grpcError))
			return &usstocksFePb.GetSearchResultsResponse{
				RespHeader: &headerFe.ResponseHeader{Status: rpc.StatusResourceExhausted()},
			}, nil
		}
		logger.Error(ctx, "error in SearchStocks rpc", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(grpcError))
		return &usstocksFePb.GetSearchResultsResponse{
			RespHeader: &headerFe.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error in SearchStocks rpc")},
		}, nil
	}

	s.publishSearchResultedEvent(ctx, actorId, platform, version, req.GetFilter().GetSearchQuery(), int32(resp.GetTotalStocks()), sortOption.String(), resp.GetStocks())

	if len(resp.GetStocks()) == 0 {
		return &usstocksFePb.GetSearchResultsResponse{
			RespHeader:  &headerFe.ResponseHeader{Status: rpc.StatusOk()},
			PageContext: resp.GetPageContext(),
			Container: &usstocksFePb.GetSearchResultsResponse_Info{Info: &usstocksFePb.SearchResultsInfoSection{
				Title: &commontypes.Text{
					FontColor:    usstocksUi.Night,
					DisplayValue: &commontypes.Text_PlainString{PlainString: NoStocksFoundTitle},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
				},
				Subtitle: &commontypes.Text{
					FontColor:    usstocksUi.Lead,
					DisplayValue: &commontypes.Text_PlainString{PlainString: NoStocksFoundSubtitle},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3_PARA},
				},
				Image: &commontypes.Image{
					ImageUrl: NoStocksFoundImageUrl,
					Width:    107,
					Height:   121,
				},
			}},
		}, nil

	}

	getWatchlistStockMappingsByActorIdRes, err := s.catalogManagerClient.GetWatchlistStockMappingsByActorId(ctx,
		&usstocksCatalogPb.GetWatchlistStockMappingsByActorIdRequest{
			ActorId: actorId,
		})
	if err = epifigrpc.RPCError(getWatchlistStockMappingsByActorIdRes, err); err != nil {
		logger.Error(ctx, "error in GetWatchlistStockMappingsByActorId rpc", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(err))
		return &usstocksFePb.GetSearchResultsResponse{
			RespHeader: &headerFe.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error in GetWatchlistStockMappingsByActorId rpc")},
		}, nil
	}

	marketCategoryVendorIds := utils.GetMarketCategoryVendorIdsFromStocks(resp.GetStocks())
	// fetch market categories for all the stocks to populate stock list item tags
	getMarketCategoriesRes, err := s.catalogManagerClient.GetMarketCategories(ctx,
		&usstocksCatalogPb.GetMarketCategoriesRequest{
			VendorIds: marketCategoryVendorIds,
		})
	if err = epifigrpc.RPCError(getMarketCategoriesRes, err); err != nil {
		logger.Error(ctx, "error while calling GetMarketCategories RPC", zap.Error(err))
		return &usstocksFePb.GetSearchResultsResponse{
			RespHeader: &headerFe.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error  while calling GetMarketCategories RPC")},
		}, nil
	}

	isCompanyTypeStocksPresent, isETFTypeStocksPresent := utils.GetStocksTypes(resp.GetStocks())

	var stockListItems []*usstocksFePb.StockListItem
	for _, stock := range resp.GetStocks() {
		stockListItems = append(stockListItems, utils.GetStockListItemFromStock(ctx, stock, getMarketCategoriesRes.GetMarketCategories(), sortOption))
	}

	watchlistStockMappingsMap := utils.GetWatchlistStockMappingsMap(getWatchlistStockMappingsByActorIdRes.GetWatchlistStockMappings())
	stockListItems = utils.PopulateWatchlistDetailsInStockListItems(stockListItems, watchlistStockMappingsMap)

	container := &usstocksFePb.GetSearchResultsResponse_Results{
		Results: &usstocksFePb.SearchResultsSection{
			SortOptions: &usstocksFePb.SortSection{
				Title:              usstocksUi.GetText(utils.GetStockCountText(int(resp.GetTotalStocks())), "#000000", commontypes.FontStyle_OVERLINE_2XS_CAPS),
				SelectedSortOption: sortOption,
				SortOptions:        utils.GetSearchSortOptions(isETFTypeStocksPresent, isCompanyTypeStocksPresent),
			},
			Stocks: stockListItems,
		},
	}

	return &usstocksFePb.GetSearchResultsResponse{
		RespHeader:  &headerFe.ResponseHeader{Status: rpc.StatusOk()},
		PageContext: resp.GetPageContext(),
		Container:   container,
	}, nil
}
