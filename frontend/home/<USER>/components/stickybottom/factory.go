package stickybottom

import (
	"fmt"

	"github.com/google/wire"

	"github.com/epifi/gamma/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/home/<USER>/metrics"
)

var ComponentGeneratorFactoryWireSet = wire.NewSet(NewComponentGeneratorFactory, wire.Bind(new(IComponentGeneratorFactory), new(*ComponentGeneratorFactory)))
var AllComponentGeneratorWireSet = wire.NewSet(
	NewBottomNavigationBarComponent,
)

type IComponentGeneratorFactory interface {
	GetComponentGenerator(componentId string) (ComponentGenerator, error)
}

type ComponentGeneratorFactory struct {
	bottomNavigationBarComponent *BottomNavigationBarComponent
}

func NewComponentGeneratorFactory(bottomNavigationBarComponent *BottomNavigationBarComponent) *ComponentGeneratorFactory {
	return &ComponentGeneratorFactory{
		bottomNavigationBarComponent: bottomNavigationBarComponent,
	}
}

func (c *ComponentGeneratorFactory) GetComponentGenerator(componentId string) (ComponentGenerator, error) {
	switch componentId {
	case constants.BottomNavBarSectionComponentId.String():
		return c.bottomNavigationBarComponent, nil
	default:
		metrics.Recorder.RecordUnhandledComponentIdError(componentId, "")
		return nil, fmt.Errorf("invalid component id for sticky bottom component generator: %s", componentId)
	}
}
