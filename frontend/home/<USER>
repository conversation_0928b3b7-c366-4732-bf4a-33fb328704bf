package home

import (
	"context"

	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/home"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config"
)

//go:generate stringer -type=UserType
type Priority int

const (
	P0 Priority = 0
	P1 Priority = 1
	P2 Priority = 2
	P3 Priority = 3
)

func (s *Service) evaluateSpacerComponent(widgetType home.HomeWidget_WidgetType, previousWidgetType home.HomeWidget_WidgetType) components.Spacing {
	switch widgetType {
	case home.HomeWidget_WIDGET_TYPE_HELP:
		switch previousWidgetType {
		case home.HomeWidget_WIDGET_TYPE_PROMOTIONAL_BANNER_2,
			home.HomeWidget_WIDGET_TYPE_SALARY_ACCOUNT, home.HomeWidget_WIDGET_TYPE_REFERRAL:
			return components.Spacing_SPACING_UNSPECIFIED
		default:
			return components.Spacing_SPACING_XXL
		}
	default:
		// no-op
	}
	return components.Spacing_SPACING_UNSPECIFIED
}

func isDevicePlatformVersionValidForHomeShortcuts(ctx context.Context, deviceDetails *cfg.PlatformVersionCheck) bool {
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	if version == 0 || platform == commontypes.Platform_PLATFORM_UNSPECIFIED {
		return false
	}
	if (deviceDetails.IsEnableOnAndroid && platform == commontypes.Platform_ANDROID && version >= deviceDetails.MinAndroidVersion) ||
		(deviceDetails.IsEnableOnIos && platform == commontypes.Platform_IOS && version >= deviceDetails.MinIosVersion) {
		return true
	}
	return false
}

func isDevicePlatformVersionValidForDashboardV2(ctx context.Context, releaseConfig *cfg.PlatformVersionCheck) bool {
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	if version == 0 || platform == commontypes.Platform_PLATFORM_UNSPECIFIED {
		return false
	}
	if (releaseConfig.IsEnableOnAndroid && platform == commontypes.Platform_ANDROID && version >= releaseConfig.MinAndroidVersion) ||
		(releaseConfig.IsEnableOnIos && platform == commontypes.Platform_IOS && version >= releaseConfig.MinIosVersion) {
		return true
	}
	return false
}

func isDevicePlatformVersionValidForAnalyserScreenDeeplink(ctx context.Context) bool {
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	if version == 0 || platform == commontypes.Platform_PLATFORM_UNSPECIFIED {
		return false
	}
	if (platform == commontypes.Platform_ANDROID && version < 294) || (platform == commontypes.Platform_IOS && version < 410) {
		return true
	}
	return false
}

func isDevicePlatformVersionValidForScreenElementProperties(ctx context.Context, screenElement *config.ScreenElementProperties) bool {
	if screenElement != nil && screenElement.IconWithVersionConstraints.VersionConstraints != nil {
		releaseConfig := screenElement.IconWithVersionConstraints.VersionConstraints
		platform, version := epificontext.AppPlatformAndVersion(ctx)
		if version == 0 || platform == commontypes.Platform_PLATFORM_UNSPECIFIED {
			return false
		}
		if (releaseConfig.IsEnableOnAndroid && platform == commontypes.Platform_ANDROID && version >= releaseConfig.MinAndroidVersion) ||
			(releaseConfig.IsEnableOnIos && platform == commontypes.Platform_IOS && version >= releaseConfig.MinIosVersion) {
			return true
		}
	}
	return false
}

// helper function which returns if the actor id passed belongs to a fi lite user
func isFiLiteUser(ctx context.Context, onbClient onboarding.OnboardingClient, actorId string) (bool, onboarding.FeatureStatus, error) {
	getFeatResp, errGetFeat := onbClient.GetFeatureDetails(ctx, &onboarding.GetFeatureDetailsRequest{
		ActorId: actorId,
		Feature: onboarding.Feature_FEATURE_CC,
	})
	if grpcErr := epifigrpc.RPCError(getFeatResp, errGetFeat); grpcErr != nil {
		logger.Error(ctx, "failed to get feature details from onboarding", zap.Error(grpcErr))
		return false, onboarding.FeatureStatus_FEATURE_STATUS_UNSPECIFIED, grpcErr
	}
	return getFeatResp.GetIsFiLiteUser(), getFeatResp.GetFeatureInfo().GetFeatureStatus(), nil
}

func (s *Service) fetchDerivedSoftIntentCategoryBasedOnPriority(userSelectedSoftIntents []onboarding.OnboardingSoftIntent, priority Priority) onboarding.OnboardingSoftIntentCategory {
	var (
		softIntentDerivedData = onboarding.GetSoftIntentDerivedData(userSelectedSoftIntents)
		softIntentCategory    string
	)

	switch priority {
	case P0:
		softIntentCategory = softIntentDerivedData.P0SoftIntentCategory
	case P1:
		softIntentCategory = softIntentDerivedData.P1SoftIntentCategory
	case P2:
		softIntentCategory = softIntentDerivedData.P2SoftIntentCategory
	case P3:
		softIntentCategory = softIntentDerivedData.P3SoftIntentCategory
	}

	return onboarding.OnboardingSoftIntentCategory(onboarding.OnboardingSoftIntentCategory_value[softIntentCategory])
}
