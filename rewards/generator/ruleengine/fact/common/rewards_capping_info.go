package common

import (
	"context"
	"fmt"
	"time"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/rewards/config/genconf"
)

func GetRewardsCappingInfosForRewardOfferType(ctx context.Context, conf *genconf.Config, offerType rewardsPb.RewardOfferType) ([]*RewardsCappingInfo, error) {

	var (
		res              []*RewardsCappingInfo
		getCappingError  error
		periodicCappings = conf.RewardsPeriodicCappingsPerOfferType()
	)

	periodicCappings.Range(func(_ string, value *genconf.RewardsPeriodicCappingInfo) (continueRange bool) {
		if !value.Active() || rewardsPb.RewardOfferType(rewardsPb.RewardOfferType_value[value.OfferType()]) != offerType {
			return true
		}

		cappingEffectiveDate, err := getRewardsCappingEffectiveDate(ctx, value)
		if err != nil {
			getCappingError = fmt.Errorf("error getting rewards capping effective date from periodic capping info : %w", err)
			return false
		}

		res = append(res, &RewardsCappingInfo{
			CappingEffectiveDate: cappingEffectiveDate,
			CappingPeriod:        value.CappingPeriod(),
			CappingDuration:      value.CappingDuration(),
			CapCount:             value.RewardsCap(),
		})

		return true
	})

	if getCappingError != nil {
		return nil, getCappingError
	}

	return res, nil
}

func getRewardsCappingEffectiveDate(_ context.Context, periodicCappingInfo *genconf.RewardsPeriodicCappingInfo) (time.Time, error) {
	period := periodicCappingInfo.CappingPeriod()
	duration := periodicCappingInfo.CappingDuration()
	switch duration {
	case "CALENDAR_MONTH":
		return datetime.StartOfMonth(time.Now().AddDate(0, 1-period, 0)), nil
	case "CALENDAR_WEEK":
		return datetime.StartOfWeek(time.Now().AddDate(0, 0, 7*(1-period)), time.Sunday), nil
	case "CALENDAR_DAY":
		return datetime.GetTimeAtStartOfTheDay(time.Now().AddDate(0, 0, 1-period)), nil
	}

	return time.Time{}, fmt.Errorf("error in getting rewards capping date for generic config, duration %s is unhandled", duration)
}
