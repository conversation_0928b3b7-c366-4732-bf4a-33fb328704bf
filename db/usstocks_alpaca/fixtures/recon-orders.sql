-- moving states of the workflow manually to success [Current state: FOREIGN_FUND_TRANSFER, MANUAL_INTERVENTION] --
-- actual state: order fulfilled at vendor, swift transfer completed --
update workflow_requests set stage = 'RELEASE_SELL_LOCK', status = 'SUCCESSFUL', updated_at = now() where id in ('WFR230103u6me3RcmRuGuBbgKqRC2rA==','WFR2301036L6dQhSbSKyhsssRDABLvA==','WFR230103B7Le+GngRP+qmWSVStBruQ==','WFR230103gQuoxqXwRsmRIxIA2dVqhg==','WFR230103abLAgKh6Sd6Jr66K7rMGPw==','WFR230103nzUb4+lhTB+SDZ3THPX6Tg==','WFR230103iznatlvBTbyIW16JP0lc6Q==','WFR230117pZYSVHHuTJOFdZB01rTgHA==','WFR230118PGeAxK4JQy++c79lkHzQ5A==','WFR230119OS+gqrG7TBKXj0EvDBntDQ==','WFR230117TBnI374dT3ajvIFVp0Mp1w==','WFR230119EgtnrrSJSoaeclodvr7KYQ==','WFR230120yFVtxRqrTD2krPeUdJdHkg==','WFR230120UclUA5xQQdqqgeCYB9/C7Q==');
update workflow_histories set status = 'SUCCESSFUL', updated_at = now() where stage = 'FOREIGN_FUND_TRANSFER' AND wf_req_id in ('WFR230103u6me3RcmRuGuBbgKqRC2rA==','WFR2301036L6dQhSbSKyhsssRDABLvA==','WFR230103B7Le+GngRP+qmWSVStBruQ==','WFR230103gQuoxqXwRsmRIxIA2dVqhg==','WFR230103abLAgKh6Sd6Jr66K7rMGPw==','WFR230103nzUb4+lhTB+SDZ3THPX6Tg==','WFR230103iznatlvBTbyIW16JP0lc6Q==','WFR230117pZYSVHHuTJOFdZB01rTgHA==','WFR230118PGeAxK4JQy++c79lkHzQ5A==','WFR230119OS+gqrG7TBKXj0EvDBntDQ==','WFR230117TBnI374dT3ajvIFVp0Mp1w==','WFR230119EgtnrrSJSoaeclodvr7KYQ==','WFR230120yFVtxRqrTD2krPeUdJdHkg==','WFR230120UclUA5xQQdqqgeCYB9/C7Q==');
update orders set state = 'ORDER_SUCCESS', updated_at = now() where wf_req_id in ('WFR230103u6me3RcmRuGuBbgKqRC2rA==','WFR2301036L6dQhSbSKyhsssRDABLvA==','WFR230103B7Le+GngRP+qmWSVStBruQ==','WFR230103gQuoxqXwRsmRIxIA2dVqhg==','WFR230103abLAgKh6Sd6Jr66K7rMGPw==','WFR230103nzUb4+lhTB+SDZ3THPX6Tg==','WFR230103iznatlvBTbyIW16JP0lc6Q==','WFR230117pZYSVHHuTJOFdZB01rTgHA==','WFR230118PGeAxK4JQy++c79lkHzQ5A==','WFR230119OS+gqrG7TBKXj0EvDBntDQ==','WFR230117TBnI374dT3ajvIFVp0Mp1w==','WFR230119EgtnrrSJSoaeclodvr7KYQ==','WFR230120yFVtxRqrTD2krPeUdJdHkg==','WFR230120UclUA5xQQdqqgeCYB9/C7Q==');
insert into workflow_histories (id, wf_req_id, stage, status, completed_at) values
            ('f9f3042c-73b8-45aa-b4b3-05ca598a1bfb', 'WFR230103u6me3RcmRuGuBbgKqRC2rA==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
            ('867fe4fa-d759-42d8-b841-0aaf7f61ba50', 'WFR2301036L6dQhSbSKyhsssRDABLvA==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
            ('f7574455-2bb1-437a-9ecc-10998b9870de', 'WFR230103B7Le+GngRP+qmWSVStBruQ==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
            ('6d53e92a-4013-4257-9d05-4e578cd973f7', 'WFR230103gQuoxqXwRsmRIxIA2dVqhg==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
            ('6a7085fb-63ce-4bbc-a807-61a23d74bf3f', 'WFR230103abLAgKh6Sd6Jr66K7rMGPw==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
            ('2664ade1-fcac-4b01-94d7-0a709e65c344', 'WFR230103nzUb4+lhTB+SDZ3THPX6Tg==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
            ('5aceed74-d502-40c1-a64b-051d266a1ee9', 'WFR230103iznatlvBTbyIW16JP0lc6Q==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
            ('8dfac8f5-134d-47ba-a348-86c0081f633f', 'WFR230117pZYSVHHuTJOFdZB01rTgHA==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
            ('875a5966-8ef9-4da4-a115-627523519271', 'WFR230118PGeAxK4JQy++c79lkHzQ5A==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
            ('91ea06b7-538a-41bd-8bba-ab91e0f9b812', 'WFR230119OS+gqrG7TBKXj0EvDBntDQ==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
            ('97b0d141-8d78-47f7-9c74-f5f03ceb2664', 'WFR230117TBnI374dT3ajvIFVp0Mp1w==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
            ('9a6b93bc-09d4-402d-884a-1e210a0bfa8b', 'WFR230119EgtnrrSJSoaeclodvr7KYQ==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
            ('0b34ed09-7b1c-4e38-9afb-aa639a5eded9', 'WFR230120yFVtxRqrTD2krPeUdJdHkg==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
            ('44211597-11d5-4b85-af77-2abdeb268839', 'WFR230120UclUA5xQQdqqgeCYB9/C7Q==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now());


-- moving states of the workflow manually to success [Current state: SEND_SELL_ORDER, MANUAL_INTERVENTION] --
-- actual state, order not placed with vendor
update workflow_requests set stage = 'SEND_SELL_ORDER', status = 'FAILED', updated_at = now() where id in ('WFR2303215FYY7YzzTqGeGSYJp3hf3w==','WFR230321G0YRHnzeQOWwlR0z7T0vZQ==','WFR230321RM+Bn02bTdqys4+68A/OeA==','WFR23032091fePTdDTl6w9P2943OFHg==');
update workflow_histories set status = 'FAILED', updated_at = now() where stage = 'SEND_SELL_ORDER' AND wf_req_id in ('WFR2303215FYY7YzzTqGeGSYJp3hf3w==','WFR230321G0YRHnzeQOWwlR0z7T0vZQ==','WFR230321RM+Bn02bTdqys4+68A/OeA==','WFR23032091fePTdDTl6w9P2943OFHg==');
update orders set state = 'ORDER_FAILED', updated_at = now() where wf_req_id in ('WFR2303215FYY7YzzTqGeGSYJp3hf3w==','WFR230321G0YRHnzeQOWwlR0z7T0vZQ==','WFR230321RM+Bn02bTdqys4+68A/OeA==','WFR23032091fePTdDTl6w9P2943OFHg==');

-- workflow initiation failed for order, moving to relevant state --
update orders set state = 'ORDER_INITIATION_FAILED', updated_at = now() where id = 'USSO2302272CNnLJwaAz';

-- payment failed, order placement with vendor failed --
-- actual state: order not placed with vendor --
update workflow_requests set stage = 'SEND_BUY_ORDER', status = 'FAILED', updated_at = now() where id in ('WFR221228q/CCrxAUQW+RLDqovxMTAg==','WFR221228r5Frr+TWTq+KS4ILH4LrLQ==','WFR221227S98dPJLySneJUhl4U+CjsQ==','WFR221227HKN9dc5fRge4kJKteGz3pA==');
update workflow_histories set status = 'FAILED', updated_at = now() where stage = 'SEND_BUY_ORDER' AND wf_req_id in ('WFR221228q/CCrxAUQW+RLDqovxMTAg==','WFR221228r5Frr+TWTq+KS4ILH4LrLQ==','WFR221227S98dPJLySneJUhl4U+CjsQ==','WFR221227HKN9dc5fRge4kJKteGz3pA==');
update orders set state = 'ORDER_FAILED', updated_at = now() where wf_req_id in ('WFR221228q/CCrxAUQW+RLDqovxMTAg==','WFR221228r5Frr+TWTq+KS4ILH4LrLQ==','WFR221227S98dPJLySneJUhl4U+CjsQ==','WFR221227HKN9dc5fRge4kJKteGz3pA==');
