// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/mfcentral/update_mobile.proto

package mfcentral

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UpdateMobileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateMobileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateMobileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateMobileRequestMultiError, or nil if none found.
func (m *UpdateMobileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateMobileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientReferenceNumber

	// no validation rules for PanNumber

	// no validation rules for Pekrn

	// no validation rules for MobileNumber

	// no validation rules for EmailId

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateMobileRequestValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateMobileRequestValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateMobileRequestValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateMobileRequestMultiError(errors)
	}

	return nil
}

// UpdateMobileRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateMobileRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateMobileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateMobileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateMobileRequestMultiError) AllErrors() []error { return m }

// UpdateMobileRequestValidationError is the validation error returned by
// UpdateMobileRequest.Validate if the designated constraints aren't met.
type UpdateMobileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateMobileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateMobileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateMobileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateMobileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateMobileRequestValidationError) ErrorName() string {
	return "UpdateMobileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateMobileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateMobileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateMobileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateMobileRequestValidationError{}

// Validate checks the field values on UpdateFoliosMobileData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFoliosMobileData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFoliosMobileData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFoliosMobileDataMultiError, or nil if none found.
func (m *UpdateFoliosMobileData) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFoliosMobileData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Amc

	// no validation rules for Folio

	// no validation rules for NewMobile

	// no validation rules for MobileRelationship

	if len(errors) > 0 {
		return UpdateFoliosMobileDataMultiError(errors)
	}

	return nil
}

// UpdateFoliosMobileDataMultiError is an error wrapping multiple validation
// errors returned by UpdateFoliosMobileData.ValidateAll() if the designated
// constraints aren't met.
type UpdateFoliosMobileDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFoliosMobileDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFoliosMobileDataMultiError) AllErrors() []error { return m }

// UpdateFoliosMobileDataValidationError is the validation error returned by
// UpdateFoliosMobileData.Validate if the designated constraints aren't met.
type UpdateFoliosMobileDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFoliosMobileDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFoliosMobileDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFoliosMobileDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFoliosMobileDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFoliosMobileDataValidationError) ErrorName() string {
	return "UpdateFoliosMobileDataValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFoliosMobileDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFoliosMobileData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFoliosMobileDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFoliosMobileDataValidationError{}

// Validate checks the field values on UpdateMobileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateMobileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateMobileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateMobileResponseMultiError, or nil if none found.
func (m *UpdateMobileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateMobileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for OtpReference

	// no validation rules for UserSubjectReference

	// no validation rules for ClientReferenceNumber

	for idx, item := range m.GetErrors() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateMobileResponseValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateMobileResponseValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateMobileResponseValidationError{
					field:  fmt.Sprintf("Errors[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Response

	if len(errors) > 0 {
		return UpdateMobileResponseMultiError(errors)
	}

	return nil
}

// UpdateMobileResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateMobileResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateMobileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateMobileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateMobileResponseMultiError) AllErrors() []error { return m }

// UpdateMobileResponseValidationError is the validation error returned by
// UpdateMobileResponse.Validate if the designated constraints aren't met.
type UpdateMobileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateMobileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateMobileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateMobileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateMobileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateMobileResponseValidationError) ErrorName() string {
	return "UpdateMobileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateMobileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateMobileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateMobileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateMobileResponseValidationError{}
