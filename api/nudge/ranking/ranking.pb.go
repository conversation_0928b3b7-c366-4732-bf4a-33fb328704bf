// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/nudge/ranking/ranking.proto

package ranking

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ActorSpecificPopularity consists of actor's affinity to a nudge or area-category
type ActorSpecificPopularity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ranked dictionary of nudge index to popularity score
	RankedNudges map[int32]float32 `protobuf:"bytes,1,rep,name=ranked_nudges,json=rankedNudges,proto3" json:"ranked_nudges,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	// ranked dictionary of area category index to popularity score
	RankedAreaCategories map[int32]float32 `protobuf:"bytes,2,rep,name=ranked_area_categories,json=rankedAreaCategories,proto3" json:"ranked_area_categories,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
}

func (x *ActorSpecificPopularity) Reset() {
	*x = ActorSpecificPopularity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_ranking_ranking_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActorSpecificPopularity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActorSpecificPopularity) ProtoMessage() {}

func (x *ActorSpecificPopularity) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_ranking_ranking_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActorSpecificPopularity.ProtoReflect.Descriptor instead.
func (*ActorSpecificPopularity) Descriptor() ([]byte, []int) {
	return file_api_nudge_ranking_ranking_proto_rawDescGZIP(), []int{0}
}

func (x *ActorSpecificPopularity) GetRankedNudges() map[int32]float32 {
	if x != nil {
		return x.RankedNudges
	}
	return nil
}

func (x *ActorSpecificPopularity) GetRankedAreaCategories() map[int32]float32 {
	if x != nil {
		return x.RankedAreaCategories
	}
	return nil
}

// NudgeMetadata consists of information related to nudge ranking i.e. index and global popularity
type NudgeMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// index is used as a proxy for the nudge id
	// instead of storing nudge id, we'll store this index at all places
	// this is done to optimise for storage since ids take more space
	Index int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	// global popularity probability associated with the nudge
	// this defines the overall affinity of the nudge to the user
	Prob float32 `protobuf:"fixed32,2,opt,name=prob,proto3" json:"prob,omitempty"`
}

func (x *NudgeMetadata) Reset() {
	*x = NudgeMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_ranking_ranking_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NudgeMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NudgeMetadata) ProtoMessage() {}

func (x *NudgeMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_ranking_ranking_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NudgeMetadata.ProtoReflect.Descriptor instead.
func (*NudgeMetadata) Descriptor() ([]byte, []int) {
	return file_api_nudge_ranking_ranking_proto_rawDescGZIP(), []int{1}
}

func (x *NudgeMetadata) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *NudgeMetadata) GetProb() float32 {
	if x != nil {
		return x.Prob
	}
	return 0
}

// AreaCategoryMetadata consists of information related to area-category ranking i.e. index and global popularity
// this will be used for new nudges if they don't have any data in the actor/global popularity maps
type AreaCategoryMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// index is used as a proxy for the area-category pair
	// instead of storing area-category name, we'll store this index at all places
	// this is done to optimise for storage since area-category names take more space
	Index int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	// global popularity probability associated with the area-category
	// this defines the overall affinity of the area-category to the user
	Prob float32 `protobuf:"fixed32,2,opt,name=prob,proto3" json:"prob,omitempty"`
}

func (x *AreaCategoryMetadata) Reset() {
	*x = AreaCategoryMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_ranking_ranking_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AreaCategoryMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AreaCategoryMetadata) ProtoMessage() {}

func (x *AreaCategoryMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_ranking_ranking_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AreaCategoryMetadata.ProtoReflect.Descriptor instead.
func (*AreaCategoryMetadata) Descriptor() ([]byte, []int) {
	return file_api_nudge_ranking_ranking_proto_rawDescGZIP(), []int{2}
}

func (x *AreaCategoryMetadata) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *AreaCategoryMetadata) GetProb() float32 {
	if x != nil {
		return x.Prob
	}
	return 0
}

var File_api_nudge_ranking_ranking_proto protoreflect.FileDescriptor

var file_api_nudge_ranking_ranking_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2f, 0x72, 0x61, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0d, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x22, 0xfa, 0x02, 0x0a, 0x17, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x50, 0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x12, 0x5d, 0x0a, 0x0d,
	0x72, 0x61, 0x6e, 0x6b, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2e, 0x72, 0x61, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x50, 0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x61, 0x6e, 0x6b,
	0x65, 0x64, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x72,
	0x61, 0x6e, 0x6b, 0x65, 0x64, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x73, 0x12, 0x76, 0x0a, 0x16, 0x72,
	0x61, 0x6e, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6e, 0x75,
	0x64, 0x67, 0x65, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x50, 0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x65, 0x64, 0x41, 0x72, 0x65, 0x61, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x14, 0x72,
	0x61, 0x6e, 0x6b, 0x65, 0x64, 0x41, 0x72, 0x65, 0x61, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x1a, 0x3f, 0x0a, 0x11, 0x52, 0x61, 0x6e, 0x6b, 0x65, 0x64, 0x4e, 0x75, 0x64,
	0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x47, 0x0a, 0x19, 0x52, 0x61, 0x6e, 0x6b, 0x65, 0x64, 0x41, 0x72,
	0x65, 0x61, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x39, 0x0a,
	0x0d, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x72, 0x6f, 0x62, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x04, 0x70, 0x72, 0x6f, 0x62, 0x22, 0x40, 0x0a, 0x14, 0x41, 0x72, 0x65, 0x61,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x72, 0x6f, 0x62, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x70, 0x72, 0x6f, 0x62, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2e, 0x72,
	0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_nudge_ranking_ranking_proto_rawDescOnce sync.Once
	file_api_nudge_ranking_ranking_proto_rawDescData = file_api_nudge_ranking_ranking_proto_rawDesc
)

func file_api_nudge_ranking_ranking_proto_rawDescGZIP() []byte {
	file_api_nudge_ranking_ranking_proto_rawDescOnce.Do(func() {
		file_api_nudge_ranking_ranking_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_nudge_ranking_ranking_proto_rawDescData)
	})
	return file_api_nudge_ranking_ranking_proto_rawDescData
}

var file_api_nudge_ranking_ranking_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_nudge_ranking_ranking_proto_goTypes = []interface{}{
	(*ActorSpecificPopularity)(nil), // 0: nudge.ranking.ActorSpecificPopularity
	(*NudgeMetadata)(nil),           // 1: nudge.ranking.NudgeMetadata
	(*AreaCategoryMetadata)(nil),    // 2: nudge.ranking.AreaCategoryMetadata
	nil,                             // 3: nudge.ranking.ActorSpecificPopularity.RankedNudgesEntry
	nil,                             // 4: nudge.ranking.ActorSpecificPopularity.RankedAreaCategoriesEntry
}
var file_api_nudge_ranking_ranking_proto_depIdxs = []int32{
	3, // 0: nudge.ranking.ActorSpecificPopularity.ranked_nudges:type_name -> nudge.ranking.ActorSpecificPopularity.RankedNudgesEntry
	4, // 1: nudge.ranking.ActorSpecificPopularity.ranked_area_categories:type_name -> nudge.ranking.ActorSpecificPopularity.RankedAreaCategoriesEntry
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_nudge_ranking_ranking_proto_init() }
func file_api_nudge_ranking_ranking_proto_init() {
	if File_api_nudge_ranking_ranking_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_nudge_ranking_ranking_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActorSpecificPopularity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_nudge_ranking_ranking_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NudgeMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_nudge_ranking_ranking_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AreaCategoryMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_nudge_ranking_ranking_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_nudge_ranking_ranking_proto_goTypes,
		DependencyIndexes: file_api_nudge_ranking_ranking_proto_depIdxs,
		MessageInfos:      file_api_nudge_ranking_ranking_proto_msgTypes,
	}.Build()
	File_api_nudge_ranking_ranking_proto = out.File
	file_api_nudge_ranking_ranking_proto_rawDesc = nil
	file_api_nudge_ranking_ranking_proto_goTypes = nil
	file_api_nudge_ranking_ranking_proto_depIdxs = nil
}
