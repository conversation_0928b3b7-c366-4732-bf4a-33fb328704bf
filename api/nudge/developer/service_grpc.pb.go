// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/nudge/developer/service.proto

package developer

import (
	context "context"
	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NudgeDbStates_GetEntityList_FullMethodName    = "/nudge.developer.NudgeDbStates/GetEntityList"
	NudgeDbStates_GetParameterList_FullMethodName = "/nudge.developer.NudgeDbStates/GetParameterList"
	NudgeDbStates_GetData_FullMethodName          = "/nudge.developer.NudgeDbStates/GetData"
)

// NudgeDbStatesClient is the client API for NudgeDbStates service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NudgeDbStatesClient interface {
	// service to fetch list of entities for which nudge can return the data
	GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error)
	// For each entity as defined above, the parameter required to fetch that data will be different
	// This service will return appropriate params based on entity passed in request
	GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error)
	// The actual get data API call where we will make a DB call to get the required data
	GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error)
}

type nudgeDbStatesClient struct {
	cc grpc.ClientConnInterface
}

func NewNudgeDbStatesClient(cc grpc.ClientConnInterface) NudgeDbStatesClient {
	return &nudgeDbStatesClient{cc}
}

func (c *nudgeDbStatesClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	out := new(db_state.GetEntityListResponse)
	err := c.cc.Invoke(ctx, NudgeDbStates_GetEntityList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nudgeDbStatesClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	out := new(db_state.GetParameterListResponse)
	err := c.cc.Invoke(ctx, NudgeDbStates_GetParameterList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nudgeDbStatesClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	out := new(db_state.GetDataResponse)
	err := c.cc.Invoke(ctx, NudgeDbStates_GetData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NudgeDbStatesServer is the server API for NudgeDbStates service.
// All implementations should embed UnimplementedNudgeDbStatesServer
// for forward compatibility
type NudgeDbStatesServer interface {
	// service to fetch list of entities for which nudge can return the data
	GetEntityList(context.Context, *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error)
	// For each entity as defined above, the parameter required to fetch that data will be different
	// This service will return appropriate params based on entity passed in request
	GetParameterList(context.Context, *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error)
	// The actual get data API call where we will make a DB call to get the required data
	GetData(context.Context, *db_state.GetDataRequest) (*db_state.GetDataResponse, error)
}

// UnimplementedNudgeDbStatesServer should be embedded to have forward compatible implementations.
type UnimplementedNudgeDbStatesServer struct {
}

func (UnimplementedNudgeDbStatesServer) GetEntityList(context.Context, *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntityList not implemented")
}
func (UnimplementedNudgeDbStatesServer) GetParameterList(context.Context, *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetParameterList not implemented")
}
func (UnimplementedNudgeDbStatesServer) GetData(context.Context, *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetData not implemented")
}

// UnsafeNudgeDbStatesServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NudgeDbStatesServer will
// result in compilation errors.
type UnsafeNudgeDbStatesServer interface {
	mustEmbedUnimplementedNudgeDbStatesServer()
}

func RegisterNudgeDbStatesServer(s grpc.ServiceRegistrar, srv NudgeDbStatesServer) {
	s.RegisterService(&NudgeDbStates_ServiceDesc, srv)
}

func _NudgeDbStates_GetEntityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetEntityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NudgeDbStatesServer).GetEntityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NudgeDbStates_GetEntityList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NudgeDbStatesServer).GetEntityList(ctx, req.(*db_state.GetEntityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NudgeDbStates_GetParameterList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetParameterListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NudgeDbStatesServer).GetParameterList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NudgeDbStates_GetParameterList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NudgeDbStatesServer).GetParameterList(ctx, req.(*db_state.GetParameterListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NudgeDbStates_GetData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NudgeDbStatesServer).GetData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NudgeDbStates_GetData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NudgeDbStatesServer).GetData(ctx, req.(*db_state.GetDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NudgeDbStates_ServiceDesc is the grpc.ServiceDesc for NudgeDbStates service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NudgeDbStates_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "nudge.developer.NudgeDbStates",
	HandlerType: (*NudgeDbStatesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEntityList",
			Handler:    _NudgeDbStates_GetEntityList_Handler,
		},
		{
			MethodName: "GetParameterList",
			Handler:    _NudgeDbStates_GetParameterList_Handler,
		},
		{
			MethodName: "GetData",
			Handler:    _NudgeDbStates_GetData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/nudge/developer/service.proto",
}
