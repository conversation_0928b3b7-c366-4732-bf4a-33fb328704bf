// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/data_collector/account/account.proto

package account

import (
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	accounts "github.com/epifi/gamma/api/accounts"
	transaction "github.com/epifi/gamma/api/cx/data_collector/transaction"
	ticket "github.com/epifi/gamma/api/cx/ticket"
	deposit "github.com/epifi/gamma/api/deposit"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DepositAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Internal ID referencing the account.
	DepositId string `protobuf:"bytes,1,opt,name=deposit_id,json=depositId,proto3" json:"deposit_id,omitempty"`
	// Account number of the deposit account. Will be provided by partner banks after successful creation of deposits.
	AccountNumber string `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// Type of the deposit account. Can be FIXED DEPOSIT, RECURRING DEPOSIT, or SMART DEPOSIT.
	Type accounts.Type `protobuf:"varint,3,opt,name=type,proto3,enum=accounts.Type" json:"type,omitempty"`
	// State of the deposit account
	State deposit.DepositState `protobuf:"varint,4,opt,name=state,proto3,enum=deposit.DepositState" json:"state,omitempty"`
	// The date on which the deposit account will mature.
	// This is set to nil initially when deposit account is created at our end
	// and updated later on receiving successful callback
	MaturityDate *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=maturity_date,json=maturityDate,proto3" json:"maturity_date,omitempty"`
	// Interest rate applicable on the deposit account. Will be provided by partner banks.
	InterestRate string `protobuf:"bytes,6,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	// Timestamp of the moment account was created
	CreationDate   *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=creation_date,json=creationDate,proto3" json:"creation_date,omitempty"`
	AccountDetails *DepositAccountDetails `protobuf:"bytes,8,opt,name=account_details,json=accountDetails,proto3" json:"account_details,omitempty"`
	// AttachEntityMeta containing meta info to be attached to a ticket
	// server will populate DepositAccountMeta field to enrich necessary fields
	// client should not modify this field and send it as it is to AttachEntity RPC
	AttachEntityMeta *ticket.AttachEntityMeta `protobuf:"bytes,9,opt,name=attach_entity_meta,json=attachEntityMeta,proto3" json:"attach_entity_meta,omitempty"`
}

func (x *DepositAccount) Reset() {
	*x = DepositAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_account_account_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositAccount) ProtoMessage() {}

func (x *DepositAccount) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_account_account_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositAccount.ProtoReflect.Descriptor instead.
func (*DepositAccount) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_account_account_proto_rawDescGZIP(), []int{0}
}

func (x *DepositAccount) GetDepositId() string {
	if x != nil {
		return x.DepositId
	}
	return ""
}

func (x *DepositAccount) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *DepositAccount) GetType() accounts.Type {
	if x != nil {
		return x.Type
	}
	return accounts.Type(0)
}

func (x *DepositAccount) GetState() deposit.DepositState {
	if x != nil {
		return x.State
	}
	return deposit.DepositState(0)
}

func (x *DepositAccount) GetMaturityDate() *timestamppb.Timestamp {
	if x != nil {
		return x.MaturityDate
	}
	return nil
}

func (x *DepositAccount) GetInterestRate() string {
	if x != nil {
		return x.InterestRate
	}
	return ""
}

func (x *DepositAccount) GetCreationDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreationDate
	}
	return nil
}

func (x *DepositAccount) GetAccountDetails() *DepositAccountDetails {
	if x != nil {
		return x.AccountDetails
	}
	return nil
}

func (x *DepositAccount) GetAttachEntityMeta() *ticket.AttachEntityMeta {
	if x != nil {
		return x.AttachEntityMeta
	}
	return nil
}

type DepositAccountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Primary identifier of request
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Request id which is sent to vendor
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// This is the request_id received from client, which in this case is order service
	// Order orchestrates on the basis of this id.
	ClientRequestId string `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// Reference to deposit accounts
	// For CREATE type, this will initially be empty and updated once deposit is created
	// For PRECLOSE type, this will be set to deposit account id
	DepositAccountId string `protobuf:"bytes,4,opt,name=deposit_account_id,json=depositAccountId,proto3" json:"deposit_account_id,omitempty"`
	// Type of the request
	Type string `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	// State of the request
	State string `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	// For requests of type CREATE, it stores all the info that is needed to create a deposit
	// For requests of type PRECLOSE, this will be empty
	DepositInfo *deposit.DepositInfo `protobuf:"bytes,7,opt,name=deposit_info,json=depositInfo,proto3" json:"deposit_info,omitempty"`
	// vendor bank through which deposit is being created/held
	PartnerBank vendorgateway.Vendor `protobuf:"varint,8,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// the actor id to whom the deposit create/close request belongs to
	ActorId string `protobuf:"bytes,9,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// signifies if this was the last attempt by orchestrator
	// This is useful if request state is REQUEST_FAILED to understand that there won't be anymore retries
	LastAttempt bool `protobuf:"varint,10,opt,name=last_attempt,json=lastAttempt,proto3" json:"last_attempt,omitempty"`
	// Detailed status for each call made to vendor
	DetailedStatus *deposit.DepositRequestDetailedStatus `protobuf:"bytes,16,opt,name=detailed_status,json=detailedStatus,proto3" json:"detailed_status,omitempty"`
	// Timestamp of the moment account was created
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Timestamp of the moment account was last updated
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Timestamp of the moment account was deleted
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *DepositAccountDetails) Reset() {
	*x = DepositAccountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_account_account_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositAccountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositAccountDetails) ProtoMessage() {}

func (x *DepositAccountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_account_account_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositAccountDetails.ProtoReflect.Descriptor instead.
func (*DepositAccountDetails) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_account_account_proto_rawDescGZIP(), []int{1}
}

func (x *DepositAccountDetails) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DepositAccountDetails) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DepositAccountDetails) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *DepositAccountDetails) GetDepositAccountId() string {
	if x != nil {
		return x.DepositAccountId
	}
	return ""
}

func (x *DepositAccountDetails) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DepositAccountDetails) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *DepositAccountDetails) GetDepositInfo() *deposit.DepositInfo {
	if x != nil {
		return x.DepositInfo
	}
	return nil
}

func (x *DepositAccountDetails) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *DepositAccountDetails) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DepositAccountDetails) GetLastAttempt() bool {
	if x != nil {
		return x.LastAttempt
	}
	return false
}

func (x *DepositAccountDetails) GetDetailedStatus() *deposit.DepositRequestDetailedStatus {
	if x != nil {
		return x.DetailedStatus
	}
	return nil
}

func (x *DepositAccountDetails) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DepositAccountDetails) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *DepositAccountDetails) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

type TransactionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// payment instrument from which transaction is initiated
	PiFrom string `protobuf:"bytes,2,opt,name=pi_from,json=piFrom,proto3" json:"pi_from,omitempty"`
	// payment instrument to which transaction is made
	PiTo string `protobuf:"bytes,3,opt,name=pi_to,json=piTo,proto3" json:"pi_to,omitempty"`
	// Unique Transaction Reference for Online Txns like NEFT/IMPS/UPI.
	// A customer gets this id on initiating a transaction and can refer to the same
	// in case of any query regarding his/her transaction.
	// A utr is same across all the parties of a transaction.
	//
	// For NEFT/RTGS/INTRA fund transfer protocols this refers to UTR
	// For IMPS/UPI/CARD fund transfer protocol this refers to RRN
	Utr string `protobuf:"bytes,5,opt,name=utr,proto3" json:"utr,omitempty"`
	// vendor bank through which transaction is done
	PartnerBank vendorgateway.Vendor `protobuf:"varint,6,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// the state of a transaction
	Status          string `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
	PaymentProtocol string `protobuf:"bytes,11,opt,name=payment_protocol,json=paymentProtocol,proto3" json:"payment_protocol,omitempty"`
	// timestamp referring to moment when entry was created in the DB.
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// timestamp referring to moment when entry was last updated in the DB.
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	FromPiDetails *transaction.PIDetails `protobuf:"bytes,16,opt,name=from_pi_details,json=fromPiDetails,proto3" json:"from_pi_details,omitempty"`
	ToPiDetails   *transaction.PIDetails `protobuf:"bytes,17,opt,name=to_pi_details,json=toPiDetails,proto3" json:"to_pi_details,omitempty"`
	// external is internal id equivalent for an order,
	// that can be shared with actor or any other external system inorder to identify an order uniquely in the system.
	// It can be typically used in places where for
	// security reasons we don't want to expose internal id to the outside world
	OrderExternalId string `protobuf:"bytes,18,opt,name=order_external_id,json=orderExternalId,proto3" json:"order_external_id,omitempty"`
}

func (x *TransactionDetails) Reset() {
	*x = TransactionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_account_account_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionDetails) ProtoMessage() {}

func (x *TransactionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_account_account_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionDetails.ProtoReflect.Descriptor instead.
func (*TransactionDetails) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_account_account_proto_rawDescGZIP(), []int{2}
}

func (x *TransactionDetails) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TransactionDetails) GetPiFrom() string {
	if x != nil {
		return x.PiFrom
	}
	return ""
}

func (x *TransactionDetails) GetPiTo() string {
	if x != nil {
		return x.PiTo
	}
	return ""
}

func (x *TransactionDetails) GetUtr() string {
	if x != nil {
		return x.Utr
	}
	return ""
}

func (x *TransactionDetails) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *TransactionDetails) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TransactionDetails) GetPaymentProtocol() string {
	if x != nil {
		return x.PaymentProtocol
	}
	return ""
}

func (x *TransactionDetails) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TransactionDetails) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *TransactionDetails) GetFromPiDetails() *transaction.PIDetails {
	if x != nil {
		return x.FromPiDetails
	}
	return nil
}

func (x *TransactionDetails) GetToPiDetails() *transaction.PIDetails {
	if x != nil {
		return x.ToPiDetails
	}
	return nil
}

func (x *TransactionDetails) GetOrderExternalId() string {
	if x != nil {
		return x.OrderExternalId
	}
	return ""
}

type AccountFreezeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional: Lien balance is the amount which the bank has put a hold on. Can happen for failed payments
	LienBalance *money.Money `protobuf:"bytes,1,opt,name=lien_balance,json=lienBalance,proto3" json:"lien_balance,omitempty"`
	// Optional:  There can be freeze conditions with respect to an account, freeze status gives a standard code about it
	FreezeStatus string `protobuf:"bytes,2,opt,name=freeze_status,json=freezeStatus,proto3" json:"freeze_status,omitempty"`
	// Optional:  Reason for the freeze status
	FreezeReason string `protobuf:"bytes,3,opt,name=freeze_reason,json=freezeReason,proto3" json:"freeze_reason,omitempty"`
	// available balance in the account
	AvailableBalance *money.Money `protobuf:"bytes,4,opt,name=available_balance,json=availableBalance,proto3" json:"available_balance,omitempty"`
}

func (x *AccountFreezeInfo) Reset() {
	*x = AccountFreezeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_account_account_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountFreezeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountFreezeInfo) ProtoMessage() {}

func (x *AccountFreezeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_account_account_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountFreezeInfo.ProtoReflect.Descriptor instead.
func (*AccountFreezeInfo) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_account_account_proto_rawDescGZIP(), []int{3}
}

func (x *AccountFreezeInfo) GetLienBalance() *money.Money {
	if x != nil {
		return x.LienBalance
	}
	return nil
}

func (x *AccountFreezeInfo) GetFreezeStatus() string {
	if x != nil {
		return x.FreezeStatus
	}
	return ""
}

func (x *AccountFreezeInfo) GetFreezeReason() string {
	if x != nil {
		return x.FreezeReason
	}
	return ""
}

func (x *AccountFreezeInfo) GetAvailableBalance() *money.Money {
	if x != nil {
		return x.AvailableBalance
	}
	return nil
}

type FreezeStatusDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	FreezeType string `protobuf:"bytes,2,opt,name=freeze_type,json=freezeType,proto3" json:"freeze_type,omitempty"`
	UpdatedAt  string `protobuf:"bytes,3,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Advice     string `protobuf:"bytes,4,opt,name=advice,proto3" json:"advice,omitempty"`
}

func (x *FreezeStatusDetails) Reset() {
	*x = FreezeStatusDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_account_account_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FreezeStatusDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FreezeStatusDetails) ProtoMessage() {}

func (x *FreezeStatusDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_account_account_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FreezeStatusDetails.ProtoReflect.Descriptor instead.
func (*FreezeStatusDetails) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_account_account_proto_rawDescGZIP(), []int{4}
}

func (x *FreezeStatusDetails) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *FreezeStatusDetails) GetFreezeType() string {
	if x != nil {
		return x.FreezeType
	}
	return ""
}

func (x *FreezeStatusDetails) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *FreezeStatusDetails) GetAdvice() string {
	if x != nil {
		return x.Advice
	}
	return ""
}

type AccountStatusDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status    string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	UpdatedAt string `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Advice    string `protobuf:"bytes,3,opt,name=advice,proto3" json:"advice,omitempty"`
}

func (x *AccountStatusDetails) Reset() {
	*x = AccountStatusDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_account_account_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountStatusDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountStatusDetails) ProtoMessage() {}

func (x *AccountStatusDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_account_account_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountStatusDetails.ProtoReflect.Descriptor instead.
func (*AccountStatusDetails) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_account_account_proto_rawDescGZIP(), []int{5}
}

func (x *AccountStatusDetails) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AccountStatusDetails) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *AccountStatusDetails) GetAdvice() string {
	if x != nil {
		return x.Advice
	}
	return ""
}

var File_api_cx_data_collector_account_account_proto protoreflect.FileDescriptor

var file_api_cx_data_collector_account_account_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x63,
	0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x64,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70,
	0x69, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x19, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x2f, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x78, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2f, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf4, 0x03,
	0x0a, 0x0e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x64, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a,
	0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x59,
	0x0a, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x49, 0x0a, 0x12, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x10, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x4d, 0x65, 0x74, 0x61, 0x22, 0xfc, 0x04, 0x0a, 0x15, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a,
	0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x37, 0x0a, 0x0c, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x64,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a, 0x0c, 0x70, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72,
	0x42, 0x61, 0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x12, 0x4e, 0x0a, 0x0f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x64, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0e, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x22, 0xa3, 0x04, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x69,
	0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x69, 0x46,
	0x72, 0x6f, 0x6d, 0x12, 0x13, 0x0a, 0x05, 0x70, 0x69, 0x5f, 0x74, 0x6f, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x70, 0x69, 0x54, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x74, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x74, 0x72, 0x12, 0x38, 0x0a, 0x0c, 0x70, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72,
	0x42, 0x61, 0x6e, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x10,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x50, 0x0a,
	0x0f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x70, 0x69, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x49, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x0d, 0x66, 0x72, 0x6f, 0x6d, 0x50, 0x69, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x4c, 0x0a, 0x0d, 0x74, 0x6f, 0x5f, 0x70, 0x69, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x49, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x0b, 0x74, 0x6f, 0x50, 0x69, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2a, 0x0a,
	0x11, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x45,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x22, 0xd5, 0x01, 0x0a, 0x11, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x35, 0x0a, 0x0c, 0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x6c, 0x69, 0x65, 0x6e, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66,
	0x72, 0x65, 0x65, 0x7a, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x66,
	0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x3f, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x22, 0x85, 0x01, 0x0a, 0x13, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x64, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x61, 0x64, 0x76, 0x69, 0x63, 0x65, 0x22, 0x65, 0x0a, 0x14, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x64, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x64, 0x76, 0x69, 0x63, 0x65,
	0x42, 0x6c, 0x0a, 0x34, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63,
	0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_data_collector_account_account_proto_rawDescOnce sync.Once
	file_api_cx_data_collector_account_account_proto_rawDescData = file_api_cx_data_collector_account_account_proto_rawDesc
)

func file_api_cx_data_collector_account_account_proto_rawDescGZIP() []byte {
	file_api_cx_data_collector_account_account_proto_rawDescOnce.Do(func() {
		file_api_cx_data_collector_account_account_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_data_collector_account_account_proto_rawDescData)
	})
	return file_api_cx_data_collector_account_account_proto_rawDescData
}

var file_api_cx_data_collector_account_account_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_cx_data_collector_account_account_proto_goTypes = []interface{}{
	(*DepositAccount)(nil),                       // 0: cx.data_collector.account.DepositAccount
	(*DepositAccountDetails)(nil),                // 1: cx.data_collector.account.DepositAccountDetails
	(*TransactionDetails)(nil),                   // 2: cx.data_collector.account.TransactionDetails
	(*AccountFreezeInfo)(nil),                    // 3: cx.data_collector.account.AccountFreezeInfo
	(*FreezeStatusDetails)(nil),                  // 4: cx.data_collector.account.FreezeStatusDetails
	(*AccountStatusDetails)(nil),                 // 5: cx.data_collector.account.AccountStatusDetails
	(accounts.Type)(0),                           // 6: accounts.Type
	(deposit.DepositState)(0),                    // 7: deposit.DepositState
	(*timestamppb.Timestamp)(nil),                // 8: google.protobuf.Timestamp
	(*ticket.AttachEntityMeta)(nil),              // 9: cx.ticket.AttachEntityMeta
	(*deposit.DepositInfo)(nil),                  // 10: deposit.DepositInfo
	(vendorgateway.Vendor)(0),                    // 11: vendorgateway.Vendor
	(*deposit.DepositRequestDetailedStatus)(nil), // 12: deposit.DepositRequestDetailedStatus
	(*transaction.PIDetails)(nil),                // 13: cx.data_collector.transaction.PIDetails
	(*money.Money)(nil),                          // 14: google.type.Money
}
var file_api_cx_data_collector_account_account_proto_depIdxs = []int32{
	6,  // 0: cx.data_collector.account.DepositAccount.type:type_name -> accounts.Type
	7,  // 1: cx.data_collector.account.DepositAccount.state:type_name -> deposit.DepositState
	8,  // 2: cx.data_collector.account.DepositAccount.maturity_date:type_name -> google.protobuf.Timestamp
	8,  // 3: cx.data_collector.account.DepositAccount.creation_date:type_name -> google.protobuf.Timestamp
	1,  // 4: cx.data_collector.account.DepositAccount.account_details:type_name -> cx.data_collector.account.DepositAccountDetails
	9,  // 5: cx.data_collector.account.DepositAccount.attach_entity_meta:type_name -> cx.ticket.AttachEntityMeta
	10, // 6: cx.data_collector.account.DepositAccountDetails.deposit_info:type_name -> deposit.DepositInfo
	11, // 7: cx.data_collector.account.DepositAccountDetails.partner_bank:type_name -> vendorgateway.Vendor
	12, // 8: cx.data_collector.account.DepositAccountDetails.detailed_status:type_name -> deposit.DepositRequestDetailedStatus
	8,  // 9: cx.data_collector.account.DepositAccountDetails.created_at:type_name -> google.protobuf.Timestamp
	8,  // 10: cx.data_collector.account.DepositAccountDetails.updated_at:type_name -> google.protobuf.Timestamp
	8,  // 11: cx.data_collector.account.DepositAccountDetails.deleted_at:type_name -> google.protobuf.Timestamp
	11, // 12: cx.data_collector.account.TransactionDetails.partner_bank:type_name -> vendorgateway.Vendor
	8,  // 13: cx.data_collector.account.TransactionDetails.created_at:type_name -> google.protobuf.Timestamp
	8,  // 14: cx.data_collector.account.TransactionDetails.updated_at:type_name -> google.protobuf.Timestamp
	13, // 15: cx.data_collector.account.TransactionDetails.from_pi_details:type_name -> cx.data_collector.transaction.PIDetails
	13, // 16: cx.data_collector.account.TransactionDetails.to_pi_details:type_name -> cx.data_collector.transaction.PIDetails
	14, // 17: cx.data_collector.account.AccountFreezeInfo.lien_balance:type_name -> google.type.Money
	14, // 18: cx.data_collector.account.AccountFreezeInfo.available_balance:type_name -> google.type.Money
	19, // [19:19] is the sub-list for method output_type
	19, // [19:19] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_api_cx_data_collector_account_account_proto_init() }
func file_api_cx_data_collector_account_account_proto_init() {
	if File_api_cx_data_collector_account_account_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_cx_data_collector_account_account_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_account_account_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositAccountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_account_account_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_account_account_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountFreezeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_account_account_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FreezeStatusDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_account_account_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountStatusDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_data_collector_account_account_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_data_collector_account_account_proto_goTypes,
		DependencyIndexes: file_api_cx_data_collector_account_account_proto_depIdxs,
		MessageInfos:      file_api_cx_data_collector_account_account_proto_msgTypes,
	}.Build()
	File_api_cx_data_collector_account_account_proto = out.File
	file_api_cx_data_collector_account_account_proto_rawDesc = nil
	file_api_cx_data_collector_account_account_proto_goTypes = nil
	file_api_cx_data_collector_account_account_proto_depIdxs = nil
}
