package referrals

import alPb "github.com/epifi/gamma/api/cx/audit_log"

func (m *GetReferralDetailsForActorRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetReferralDetailsForActorRequest) GetObject() alPb.Object {
	return alPb.Object_REFERRAL_DETAILS
}

func (m *GetReferrerDetailsRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetReferrerDetailsRequest) GetObject() alPb.Object {
	return alPb.Object_REFERRAL_DETAILS
}

func (m *GetRefereesForActorRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetRefereesForActorRequest) GetObject() alPb.Object {
	return alPb.Object_REFERRAL_DETAILS
}

func (m *GetRefereeDetailsRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetRefereeDetailsRequest) GetObject() alPb.Object {
	return alPb.Object_REFERRAL_DETAILS
}
