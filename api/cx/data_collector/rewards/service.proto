syntax = "proto3";

package cx.data_collector.rewards;

import "api/casper/exchanger/exchanger_offer_order.proto";
import "api/casper/external_vendor_redemption/external_vendor_redemption.proto";
import "api/casper/offer_catalog.proto";
import "api/casper/redemption/redeemed_offer.proto";
import "api/cx/customer_auth/customer_auth.proto";
import "api/cx/data_collector/rewards/rewards.proto";
import "api/cx/header.proto";
import "api/cx/method_options.proto";
import "api/rewards/reward.proto";
import "api/rewards/service.proto";
import "api/rpc/status.proto";
import "api/typesv2/webui/table.proto";
import "api/typesv2/webui/text.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/data_collector/rewards";
option java_package = "com.github.epifi.gamma.api.cx.data_collector.rewards";

message GetRewardOffersRequest {
  // agent email and access token are mandatory
  // ticket id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];
  bool fetch_offers_only_applicable_to_user = 2;
  // for fetching reward offers whose active till > passed date
  google.protobuf.Timestamp date = 3;
  // for fetching reward offers whose active since <= passed date
  google.protobuf.Timestamp from_time = 4;
}

message GetRewardOffersResponse {
  rpc.Status status = 1;

  customer_auth.SherlockDeepLink sherlock_deep_link = 2;

  // list of reward offers
  repeated RewardOffer reward_offers = 3;
}

message GetOffersRequest {
  // agent email and access token are mandatory
  // ticket id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // mode through which offer is redeemable FI_COINS/FI_CARD etc
  OfferRedemptionMode redemption_mode = 2;

  // page size is fixed for now just pass the page tokens for now
  cx.PageContextRequest page_context = 3;

  // card offer type if selected FI_CARD as offer redemption mode.
  casper.CardOfferType card_offers_type = 4;

  // for fetching offers whose active till > passed date
  google.protobuf.Timestamp date = 5;
  // for fetching offers whose active since <= passed date
  google.protobuf.Timestamp from_time = 6;
}

message GetOffersResponse {
  rpc.Status status = 1;

  customer_auth.SherlockDeepLink sherlock_deep_link = 2;

  // list of offers
  repeated Offer offers = 3;

  // page size is fixed for now just pass the page tokens for now
  cx.PageContextResponse page_context = 4;
}

message GetUserRewardsRequest {
  // agent email and access token are mandatory
  // ticket id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  string reward_id = 2;

  .rewards.RewardType reward_type = 3;

  .rewards.VisibilityType visibility_type = 4;

  google.protobuf.Timestamp start_date = 5;

  google.protobuf.Timestamp end_date = 6;

  cx.PageContextRequest page_context = 7;

  string reward_offer_id = 8;
}

message GetUserRewardsResponse {
  rpc.Status status = 1;

  cx.PageContextResponse page_context = 2;

  repeated .rewards.Reward rewards = 3 [deprecated = true];

  customer_auth.SherlockDeepLink sherlock_deep_link = 4;

  // cx reward msg
  repeated RewardCxWrapper reward_cx_wrapper_list = 5;
}

message GetRedeemedOffersRequest {
  // agent email and access token are mandatory
  // ticket id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  casper.OfferType offer_type = 2;

  casper.OfferVendor vendor = 3;

  casper.OfferRedemptionState redemption_state = 4;

  google.protobuf.Timestamp from_date = 5;

  google.protobuf.Timestamp upto_date = 6;

  // some offers like EGV can expire.
  enum ExpiryStatus {
    UNSPECIFIED_EXPIRY_STATUS = 0;
    // denotes offer has expired
    EXPIRED = 2;
    // denotes ofer is still active
    NOT_EXPIRED = 3;
  }

  // if expiry_status is not specified both expired
  // and not expired offers will be fetched.
  ExpiryStatus expiry_status = 7;

  string redeemed_offer_id = 8;

  cx.PageContextRequest page_context = 9;
}

message GetRedeemedOffersResponse {
  rpc.Status status = 1;

  cx.PageContextResponse page_context = 2;

  repeated casper.RedeemedOffer redeemed_offers = 3 [deprecated = true];

  customer_auth.SherlockDeepLink sherlock_deep_link = 4;

  // cx redeemed offer msg
  repeated RedeemedOfferCxWrapper redeemed_offer_cx_wrapper_list = 5;
}

message GetRewardOfferByIdRequest {
  // ticket id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // reward offer id for which details needs to be fetched
  string reward_offer_id = 2;
}

message GetRewardOfferByIdResponse {
  // will return
  // OK for success
  // Internal for server errors
  rpc.Status status = 1;

  customer_auth.SherlockDeepLink sherlock_deep_link = 2;

  // Reward offer details
  RewardOffer reward_offer = 3;
}

message GetExchangerOffersRequest {
  // agent email and access token are mandatory
  // ticket id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];
  // for fetching reward offers whose active till > passed date
  google.protobuf.Timestamp date = 2;
  // for fetching reward offers whose active since <= passed date
  google.protobuf.Timestamp from_time = 4;
}

message GetExchangerOffersResponse {
  rpc.Status status = 1;

  customer_auth.SherlockDeepLink sherlock_deep_link = 2;

  // list of exchanger offers
  repeated ExchangerOffer exchanger_offers = 3;
}

message GetExchangerOfferOrdersRequest {
  // agent email and access token are mandatory
  // ticket id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  casper.exchanger.ExchangerOfferOrderState redemption_state = 2;

  google.protobuf.Timestamp from_date = 3;

  google.protobuf.Timestamp upto_date = 4;

  cx.PageContextRequest page_context = 5;
}

message GetExchangerOfferOrdersResponse {
  rpc.Status status = 1;

  cx.PageContextResponse page_context = 2;

  repeated casper.exchanger.ExchangerOfferOrder exchanger_offer_orders = 3 [deprecated = true];

  customer_auth.SherlockDeepLink sherlock_deep_link = 4;

  // cx ExchangerOfferOrders proto
  repeated ExchangerOfferOrderCxWrapper exchanger_offer_order_cx_wrapper_list = 5;
}

message GetRewardUnitsUtilisationForActorAndOfferInMonthRequest {
  // agent email and access token are mandatory
  // ticket id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];
  string reward_offer_id = 2;
  // any date of the month in which utilization is to be checked
  google.protobuf.Timestamp date = 3;
}

message GetRewardUnitsUtilisationForActorAndOfferInMonthResponse {
  rpc.Status status = 1;
  // number of units utilized for the particular reward type
  message UnitsUtilizedAndCapping {
    .rewards.RewardType reward_type = 1;
    uint32 units_utilized = 2;
    uint32 capping = 3;
  }
  repeated UnitsUtilizedAndCapping units_utilized_and_capping_list = 2;
  uint32 reward_count = 3;
  api.typesv2.webui.Table reward_type_utilization_and_capping_table = 4;
}

message GetRewardOfferTypesOptionsRequest {
  // agent email and access token are mandatory
  // ticket id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetRewardOfferTypesOptionsResponse {
  rpc.Status status = 1;
  // key will be enum name as string and value will be actual text that we want to show in dropdown
  map<string, string> offer_types = 2;
}

// RewardOfferTypesOptions will solely used for fetching reward details for particular reward offer type.
// **NOTE** this reward offer type is completely different from reward offer type in rewards service.
enum RewardOfferTypesOptions {
  REWARD_OFFER_TYPES_OPTIONS_UNSPECIFIED = 0;
  REWARD_OFFER_TYPES_OPTIONS_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER = 1;
  REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER = 2;
  REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER = 3;
  REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER = 4;
  REWARD_OFFER_TYPES_OPTIONS_SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER = 5;
  REWARD_OFFER_TYPES_OPTIONS_PLUS_TIER_1_PERCENT_CASHBACK_OFFER = 6;
  REWARD_OFFER_TYPES_OPTIONS_INFINITE_TIER_2_PERCENT_CASHBACK_OFFER = 7;
  REWARD_OFFER_TYPES_OPTIONS_SALARY_TIER_2_PERCENT_CASHBACK_OFFER = 8;
}

message GetRewardDetailsRequest {
  // agent email and access token are mandatory
  // ticket id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // reward offer type
  string reward_offer_type = 2;

  /* Note: Either of date range or external txn id is mandatory */
  // from and upto date - max date range allowed is 31 days
  google.protobuf.Timestamp from_date = 3;
  google.protobuf.Timestamp to_date = 4;

  // external txn id to check reward txn eligibility.
  string external_txn_id = 5;
}

message GetRewardDetailsResponse {
  rpc.Status status = 1;
  // table contains the reward details for given request
  // Columns Headers -
  // 1. Reward offer id
  // 2. Txn Id/ Ref Id
  // 3. Projected Reward Cash Back
  // 4. Actual Reward Cash Back
  // 5. Projected Reward Fi Coins
  // 6. Actual Reward Fi Coins
  // 7. Timestamp
  api.typesv2.webui.Table reward_details_table = 4;
}

message GetFiStoreRedemptionsDetailsRequest {
  // agent email and access token are mandatory
  // ticket id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // vendor reference id of the order
  string order_id = 2;
  // category of the fi store orders
  casper.external_vendor_redemption.Category category = 3;
  // from and upto date - time range window of created_at
  google.protobuf.Timestamp from_date = 4;
  google.protobuf.Timestamp to_date = 5;

  // page context for pagination
  cx.PageContextRequest page_context = 6;
}

message GetFiStoreRedemptionsDetailsResponse {
  rpc.Status status = 1;
  // table contains the fi store details for given request
  // for each tableRow, id of the redemption is sent in meta field.
  api.typesv2.webui.Table fi_store_reward_details_table = 2;
  // page context for pagination
  cx.PageContextResponse page_context = 3;
}

message GetFiStoreRedemptionAdditionalDetailsRequest {
  // agent email and access token are mandatory
  // ticket id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];
  // id of the fi store redemption
  string redemption_id = 2;
}

message GetFiStoreRedemptionAdditionalDetailsResponse {
  rpc.Status status = 1;
  // contains label value pairs of fi store redemption details.
  repeated api.typesv2.webui.LabelValueV2 reward_details = 2;
}

message GetFiCoinsFiPointsDetailsRequest {
  // agent email and access token are mandatory
  // ticket id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetFiCoinsFiPointsDetailsResponse {
  rpc.Status status = 1;
  // Total Fi Coins migrated to Fi Points (31st July)
  int32 fi_coins_migrated = 2;
  //  Total Fi Points migrated from Fi Coins (31st July)
  int32 fi_points_migrated = 3;
  // Current Fi Points balance
  int32 fi_points_current = 4;
  // Conversion rule text (i.e. from FiCoins to INR and FiPoints to INR) based on user's credit card history.
  string conversion_rule = 5;
}

service Rewards {
  // returns list of all active reward offers for current actor
  // ticket validation, enrichment options are set to true and information level is set to MODERATELY_SENSITIVE
  // since this is part of user information flow
  // will return status ok with sherlock deeplink if customer auth is not satisfied
  rpc GetRewardOffers (GetRewardOffersRequest) returns (GetRewardOffersResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }

  // returns list of all active offers for current actor
  // ticket validation, enrichment options are set to true and information level is set to MODERATELY_SENSITIVE
  // since this is part of user information flow
  // will return status ok with sherlock deeplink if customer auth is not satisfied
  rpc GetOffers (GetOffersRequest) returns (GetOffersResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }

  // returns list of earned rewards for current actor
  // ticket validation, enrichment options are set to true and information level is set to MODERATELY_SENSITIVE
  // since this is part of user information flow
  // will return status ok with sherlock deeplink if customer auth is not satisfied
  rpc GetUserRewards (GetUserRewardsRequest) returns (GetUserRewardsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }

  // returns list of all redeemed offers for current actor
  // ticket validation, enrichment options are set to true and information level is set to MODERATELY_SENSITIVE
  // since this is part of user information flow
  // will return status ok with sherlock deeplink if customer auth is not satisfied
  rpc GetRedeemedOffers (GetRedeemedOffersRequest) returns (GetRedeemedOffersResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }

  // returns reward offer details by id
  // ticket validation, enrichment options are set to true and information level is set to MODERATELY_SENSITIVE
  // since this is part of user information flow
  // will return status ok with sherlock deeplink if customer auth is not satisfied
  rpc GetRewardOfferById (GetRewardOfferByIdRequest) returns (GetRewardOfferByIdResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }

  // returns list of all active exchanger offers for current actor
  // ticket validation, enrichment options are set to true and information level is set to MODERATELY_SENSITIVE
  // since this is part of user information flow
  // will return status ok with sherlock deeplink if customer auth is not satisfied
  rpc GetExchangerOffers (GetExchangerOffersRequest) returns (GetExchangerOffersResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }

  // returns list of all redeemed exchanger offers for current actor
  // ticket validation, enrichment options are set to true and information level is set to MODERATELY_SENSITIVE
  // since this is part of user information flow
  // will return status ok with sherlock deeplink if customer auth is not satisfied
  rpc GetExchangerOfferOrders (GetExchangerOfferOrdersRequest) returns (GetExchangerOfferOrdersResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }

  // GetRewardUnitsUtilisationForActorAndOfferInMonth gives reward offer utilization for actor in a given month
  rpc GetRewardUnitsUtilisationForActorAndOfferInMonth (GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) returns (GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }

  // GetRewardOfferTypesOptions returns list of offer api.typesv2.
  rpc GetRewardOfferTypesOptions (GetRewardOfferTypesOptionsRequest) returns (GetRewardOfferTypesOptionsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }

  // GetRewardDetails returns projection reward transactions for actor in given time range or given ref id.
  rpc GetRewardDetails (GetRewardDetailsRequest) returns (GetRewardDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }

  rpc GetFiStoreRedemptionsDetails (GetFiStoreRedemptionsDetailsRequest) returns (GetFiStoreRedemptionsDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }

  rpc GetFiStoreRedemptionAdditionalDetails (GetFiStoreRedemptionAdditionalDetailsRequest) returns (GetFiStoreRedemptionAdditionalDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }

  // Returns Fi Coins (till 31st July), Fi Points (Migrated) and current value of Fi Points details for current actor
  rpc GetFiCoinsFiPointsDetails (GetFiCoinsFiPointsDetailsRequest) returns (GetFiCoinsFiPointsDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }
}
