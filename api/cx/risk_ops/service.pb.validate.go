// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/risk_ops/service.proto

package risk_ops

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	case_management "github.com/epifi/gamma/api/risk/case_management"

	common "github.com/epifi/be-common/api/typesv2/common"

	liveness "github.com/epifi/gamma/api/auth/liveness"

	persistentqueue "github.com/epifi/gamma/api/persistentqueue"

	review "github.com/epifi/gamma/api/risk/case_management/review"

	risk "github.com/epifi/gamma/api/risk"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = case_management.RuleGroup(0)

	_ = common.BooleanEnum(0)

	_ = liveness.Verdict(0)

	_ = persistentqueue.ReviewReason(0)

	_ = review.ReviewType(0)

	_ = risk.RiskParam(0)
)

// Validate checks the field values on
// GetDataForCrossValidationManualReviewRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetDataForCrossValidationManualReviewRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetDataForCrossValidationManualReviewRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetDataForCrossValidationManualReviewRequestMultiError, or nil if none found.
func (m *GetDataForCrossValidationManualReviewRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDataForCrossValidationManualReviewRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetDataForCrossValidationManualReviewRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDataForCrossValidationManualReviewRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDataForCrossValidationManualReviewRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDataForCrossValidationManualReviewRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetDataForCrossValidationManualReviewRequestMultiError(errors)
	}

	return nil
}

// GetDataForCrossValidationManualReviewRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetDataForCrossValidationManualReviewRequest.ValidateAll() if the
// designated constraints aren't met.
type GetDataForCrossValidationManualReviewRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDataForCrossValidationManualReviewRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDataForCrossValidationManualReviewRequestMultiError) AllErrors() []error { return m }

// GetDataForCrossValidationManualReviewRequestValidationError is the
// validation error returned by
// GetDataForCrossValidationManualReviewRequest.Validate if the designated
// constraints aren't met.
type GetDataForCrossValidationManualReviewRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDataForCrossValidationManualReviewRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDataForCrossValidationManualReviewRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDataForCrossValidationManualReviewRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDataForCrossValidationManualReviewRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDataForCrossValidationManualReviewRequestValidationError) ErrorName() string {
	return "GetDataForCrossValidationManualReviewRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDataForCrossValidationManualReviewRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDataForCrossValidationManualReviewRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDataForCrossValidationManualReviewRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDataForCrossValidationManualReviewRequestValidationError{}

// Validate checks the field values on
// GetDataForCrossValidationManualReviewResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetDataForCrossValidationManualReviewResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetDataForCrossValidationManualReviewResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetDataForCrossValidationManualReviewResponseMultiError, or nil if none found.
func (m *GetDataForCrossValidationManualReviewResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDataForCrossValidationManualReviewResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDataForCrossValidationManualReviewResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDataForCrossValidationManualReviewResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDataForCrossValidationManualReviewResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CrossValidationData

	// no validation rules for Base64Images

	if len(errors) > 0 {
		return GetDataForCrossValidationManualReviewResponseMultiError(errors)
	}

	return nil
}

// GetDataForCrossValidationManualReviewResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetDataForCrossValidationManualReviewResponse.ValidateAll() if the
// designated constraints aren't met.
type GetDataForCrossValidationManualReviewResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDataForCrossValidationManualReviewResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDataForCrossValidationManualReviewResponseMultiError) AllErrors() []error { return m }

// GetDataForCrossValidationManualReviewResponseValidationError is the
// validation error returned by
// GetDataForCrossValidationManualReviewResponse.Validate if the designated
// constraints aren't met.
type GetDataForCrossValidationManualReviewResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDataForCrossValidationManualReviewResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDataForCrossValidationManualReviewResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetDataForCrossValidationManualReviewResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDataForCrossValidationManualReviewResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDataForCrossValidationManualReviewResponseValidationError) ErrorName() string {
	return "GetDataForCrossValidationManualReviewResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDataForCrossValidationManualReviewResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDataForCrossValidationManualReviewResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDataForCrossValidationManualReviewResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDataForCrossValidationManualReviewResponseValidationError{}

// Validate checks the field values on ListAllowedAnnotationsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAllowedAnnotationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAllowedAnnotationsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListAllowedAnnotationsRequestMultiError, or nil if none found.
func (m *ListAllowedAnnotationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAllowedAnnotationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := ListAllowedAnnotationsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAllowedAnnotationsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAllowedAnnotationsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAllowedAnnotationsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetFilters() == nil {
		err := ListAllowedAnnotationsRequestValidationError{
			field:  "Filters",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAllowedAnnotationsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAllowedAnnotationsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAllowedAnnotationsRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAllowedAnnotationsRequestMultiError(errors)
	}

	return nil
}

// ListAllowedAnnotationsRequestMultiError is an error wrapping multiple
// validation errors returned by ListAllowedAnnotationsRequest.ValidateAll()
// if the designated constraints aren't met.
type ListAllowedAnnotationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAllowedAnnotationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAllowedAnnotationsRequestMultiError) AllErrors() []error { return m }

// ListAllowedAnnotationsRequestValidationError is the validation error
// returned by ListAllowedAnnotationsRequest.Validate if the designated
// constraints aren't met.
type ListAllowedAnnotationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAllowedAnnotationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAllowedAnnotationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAllowedAnnotationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAllowedAnnotationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAllowedAnnotationsRequestValidationError) ErrorName() string {
	return "ListAllowedAnnotationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAllowedAnnotationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAllowedAnnotationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAllowedAnnotationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAllowedAnnotationsRequestValidationError{}

// Validate checks the field values on ListAllowedAnnotationsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAllowedAnnotationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAllowedAnnotationsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListAllowedAnnotationsResponseMultiError, or nil if none found.
func (m *ListAllowedAnnotationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAllowedAnnotationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAllowedAnnotationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAllowedAnnotationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAllowedAnnotationsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Response.(type) {
	case *ListAllowedAnnotationsResponse_UiElementAllowedAnnotations:
		if v == nil {
			err := ListAllowedAnnotationsResponseValidationError{
				field:  "Response",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUiElementAllowedAnnotations()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAllowedAnnotationsResponseValidationError{
						field:  "UiElementAllowedAnnotations",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAllowedAnnotationsResponseValidationError{
						field:  "UiElementAllowedAnnotations",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUiElementAllowedAnnotations()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAllowedAnnotationsResponseValidationError{
					field:  "UiElementAllowedAnnotations",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ListAllowedAnnotationsResponseMultiError(errors)
	}

	return nil
}

// ListAllowedAnnotationsResponseMultiError is an error wrapping multiple
// validation errors returned by ListAllowedAnnotationsResponse.ValidateAll()
// if the designated constraints aren't met.
type ListAllowedAnnotationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAllowedAnnotationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAllowedAnnotationsResponseMultiError) AllErrors() []error { return m }

// ListAllowedAnnotationsResponseValidationError is the validation error
// returned by ListAllowedAnnotationsResponse.Validate if the designated
// constraints aren't met.
type ListAllowedAnnotationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAllowedAnnotationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAllowedAnnotationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAllowedAnnotationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAllowedAnnotationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAllowedAnnotationsResponseValidationError) ErrorName() string {
	return "ListAllowedAnnotationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAllowedAnnotationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAllowedAnnotationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAllowedAnnotationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAllowedAnnotationsResponseValidationError{}

// Validate checks the field values on GetAllTagsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetAllTagsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllTagsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllTagsRequestMultiError, or nil if none found.
func (m *GetAllTagsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllTagsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetAllTagsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllTagsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllTagsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllTagsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAllTagsRequestMultiError(errors)
	}

	return nil
}

// GetAllTagsRequestMultiError is an error wrapping multiple validation errors
// returned by GetAllTagsRequest.ValidateAll() if the designated constraints
// aren't met.
type GetAllTagsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllTagsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllTagsRequestMultiError) AllErrors() []error { return m }

// GetAllTagsRequestValidationError is the validation error returned by
// GetAllTagsRequest.Validate if the designated constraints aren't met.
type GetAllTagsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllTagsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllTagsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllTagsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllTagsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllTagsRequestValidationError) ErrorName() string {
	return "GetAllTagsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllTagsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllTagsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllTagsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllTagsRequestValidationError{}

// Validate checks the field values on GetAllTagsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllTagsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllTagsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllTagsResponseMultiError, or nil if none found.
func (m *GetAllTagsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllTagsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllTagsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllTagsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllTagsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAllTagsResponseMultiError(errors)
	}

	return nil
}

// GetAllTagsResponseMultiError is an error wrapping multiple validation errors
// returned by GetAllTagsResponse.ValidateAll() if the designated constraints
// aren't met.
type GetAllTagsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllTagsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllTagsResponseMultiError) AllErrors() []error { return m }

// GetAllTagsResponseValidationError is the validation error returned by
// GetAllTagsResponse.Validate if the designated constraints aren't met.
type GetAllTagsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllTagsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllTagsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllTagsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllTagsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllTagsResponseValidationError) ErrorName() string {
	return "GetAllTagsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllTagsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllTagsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllTagsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllTagsResponseValidationError{}

// Validate checks the field values on GetUserProductsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserProductsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserProductsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserProductsRequestMultiError, or nil if none found.
func (m *GetUserProductsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserProductsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetUserProductsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserProductsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserProductsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserProductsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetCaseId()) < 1 {
		err := GetUserProductsRequestValidationError{
			field:  "CaseId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetUserProductsRequestMultiError(errors)
	}

	return nil
}

// GetUserProductsRequestMultiError is an error wrapping multiple validation
// errors returned by GetUserProductsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUserProductsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserProductsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserProductsRequestMultiError) AllErrors() []error { return m }

// GetUserProductsRequestValidationError is the validation error returned by
// GetUserProductsRequest.Validate if the designated constraints aren't met.
type GetUserProductsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserProductsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserProductsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserProductsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserProductsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserProductsRequestValidationError) ErrorName() string {
	return "GetUserProductsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserProductsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserProductsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserProductsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserProductsRequestValidationError{}

// Validate checks the field values on GetUserProductsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserProductsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserProductsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserProductsResponseMultiError, or nil if none found.
func (m *GetUserProductsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserProductsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserProductsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserProductsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserProductsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserProductsResponseMultiError(errors)
	}

	return nil
}

// GetUserProductsResponseMultiError is an error wrapping multiple validation
// errors returned by GetUserProductsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetUserProductsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserProductsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserProductsResponseMultiError) AllErrors() []error { return m }

// GetUserProductsResponseValidationError is the validation error returned by
// GetUserProductsResponse.Validate if the designated constraints aren't met.
type GetUserProductsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserProductsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserProductsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserProductsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserProductsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserProductsResponseValidationError) ErrorName() string {
	return "GetUserProductsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserProductsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserProductsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserProductsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserProductsResponseValidationError{}

// Validate checks the field values on GetProductUserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetProductUserInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetProductUserInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetProductUserInfoRequestMultiError, or nil if none found.
func (m *GetProductUserInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProductUserInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetProductUserInfoRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetProductUserInfoRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetProductUserInfoRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetProductUserInfoRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetCaseId()) < 1 {
		err := GetProductUserInfoRequestValidationError{
			field:  "CaseId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetProduct()) < 1 {
		err := GetProductUserInfoRequestValidationError{
			field:  "Product",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetProductUserInfoRequestMultiError(errors)
	}

	return nil
}

// GetProductUserInfoRequestMultiError is an error wrapping multiple validation
// errors returned by GetProductUserInfoRequest.ValidateAll() if the
// designated constraints aren't met.
type GetProductUserInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProductUserInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProductUserInfoRequestMultiError) AllErrors() []error { return m }

// GetProductUserInfoRequestValidationError is the validation error returned by
// GetProductUserInfoRequest.Validate if the designated constraints aren't met.
type GetProductUserInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProductUserInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProductUserInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetProductUserInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProductUserInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProductUserInfoRequestValidationError) ErrorName() string {
	return "GetProductUserInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetProductUserInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProductUserInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProductUserInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProductUserInfoRequestValidationError{}

// Validate checks the field values on GetProductUserInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetProductUserInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetProductUserInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetProductUserInfoResponseMultiError, or nil if none found.
func (m *GetProductUserInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProductUserInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetProductUserInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetProductUserInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetProductUserInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetReviewSections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetProductUserInfoResponseValidationError{
						field:  fmt.Sprintf("ReviewSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetProductUserInfoResponseValidationError{
						field:  fmt.Sprintf("ReviewSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetProductUserInfoResponseValidationError{
					field:  fmt.Sprintf("ReviewSections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetProductUserInfoResponseMultiError(errors)
	}

	return nil
}

// GetProductUserInfoResponseMultiError is an error wrapping multiple
// validation errors returned by GetProductUserInfoResponse.ValidateAll() if
// the designated constraints aren't met.
type GetProductUserInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProductUserInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProductUserInfoResponseMultiError) AllErrors() []error { return m }

// GetProductUserInfoResponseValidationError is the validation error returned
// by GetProductUserInfoResponse.Validate if the designated constraints aren't met.
type GetProductUserInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProductUserInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProductUserInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetProductUserInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProductUserInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProductUserInfoResponseValidationError) ErrorName() string {
	return "GetProductUserInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetProductUserInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProductUserInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProductUserInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProductUserInfoResponseValidationError{}

// Validate checks the field values on GetRelatedCasesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRelatedCasesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRelatedCasesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRelatedCasesRequestMultiError, or nil if none found.
func (m *GetRelatedCasesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRelatedCasesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetRelatedCasesRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRelatedCasesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRelatedCasesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRelatedCasesRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetPageContextRequest() == nil {
		err := GetRelatedCasesRequestValidationError{
			field:  "PageContextRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPageContextRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRelatedCasesRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRelatedCasesRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRelatedCasesRequestValidationError{
				field:  "PageContextRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Identifier.(type) {
	case *GetRelatedCasesRequest_CaseId:
		if v == nil {
			err := GetRelatedCasesRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if utf8.RuneCountInString(m.GetCaseId()) < 1 {
			err := GetRelatedCasesRequestValidationError{
				field:  "CaseId",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *GetRelatedCasesRequest_ActorId:
		if v == nil {
			err := GetRelatedCasesRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if utf8.RuneCountInString(m.GetActorId()) < 1 {
			err := GetRelatedCasesRequestValidationError{
				field:  "ActorId",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetRelatedCasesRequestMultiError(errors)
	}

	return nil
}

// GetRelatedCasesRequestMultiError is an error wrapping multiple validation
// errors returned by GetRelatedCasesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRelatedCasesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRelatedCasesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRelatedCasesRequestMultiError) AllErrors() []error { return m }

// GetRelatedCasesRequestValidationError is the validation error returned by
// GetRelatedCasesRequest.Validate if the designated constraints aren't met.
type GetRelatedCasesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRelatedCasesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRelatedCasesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRelatedCasesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRelatedCasesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRelatedCasesRequestValidationError) ErrorName() string {
	return "GetRelatedCasesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRelatedCasesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRelatedCasesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRelatedCasesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRelatedCasesRequestValidationError{}

// Validate checks the field values on GetRelatedCasesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRelatedCasesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRelatedCasesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRelatedCasesResponseMultiError, or nil if none found.
func (m *GetRelatedCasesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRelatedCasesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRelatedCasesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRelatedCasesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRelatedCasesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRelatedCasesResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRelatedCasesResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRelatedCasesResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContextResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRelatedCasesResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRelatedCasesResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRelatedCasesResponseValidationError{
				field:  "PageContextResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRelatedCasesResponseMultiError(errors)
	}

	return nil
}

// GetRelatedCasesResponseMultiError is an error wrapping multiple validation
// errors returned by GetRelatedCasesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRelatedCasesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRelatedCasesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRelatedCasesResponseMultiError) AllErrors() []error { return m }

// GetRelatedCasesResponseValidationError is the validation error returned by
// GetRelatedCasesResponse.Validate if the designated constraints aren't met.
type GetRelatedCasesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRelatedCasesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRelatedCasesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRelatedCasesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRelatedCasesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRelatedCasesResponseValidationError) ErrorName() string {
	return "GetRelatedCasesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRelatedCasesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRelatedCasesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRelatedCasesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRelatedCasesResponseValidationError{}

// Validate checks the field values on GetPrioritizedCaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPrioritizedCaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPrioritizedCaseRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPrioritizedCaseRequestMultiError, or nil if none found.
func (m *GetPrioritizedCaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPrioritizedCaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetPrioritizedCaseRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPrioritizedCaseRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPrioritizedCaseRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPrioritizedCaseRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReviewType

	if len(errors) > 0 {
		return GetPrioritizedCaseRequestMultiError(errors)
	}

	return nil
}

// GetPrioritizedCaseRequestMultiError is an error wrapping multiple validation
// errors returned by GetPrioritizedCaseRequest.ValidateAll() if the
// designated constraints aren't met.
type GetPrioritizedCaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPrioritizedCaseRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPrioritizedCaseRequestMultiError) AllErrors() []error { return m }

// GetPrioritizedCaseRequestValidationError is the validation error returned by
// GetPrioritizedCaseRequest.Validate if the designated constraints aren't met.
type GetPrioritizedCaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPrioritizedCaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPrioritizedCaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPrioritizedCaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPrioritizedCaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPrioritizedCaseRequestValidationError) ErrorName() string {
	return "GetPrioritizedCaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPrioritizedCaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPrioritizedCaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPrioritizedCaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPrioritizedCaseRequestValidationError{}

// Validate checks the field values on GetPrioritizedCaseResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPrioritizedCaseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPrioritizedCaseResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPrioritizedCaseResponseMultiError, or nil if none found.
func (m *GetPrioritizedCaseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPrioritizedCaseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPrioritizedCaseResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPrioritizedCaseResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPrioritizedCaseResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPrioritizedCaseResponseValidationError{
					field:  "Case",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPrioritizedCaseResponseValidationError{
					field:  "Case",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPrioritizedCaseResponseValidationError{
				field:  "Case",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPrioritizedCaseResponseMultiError(errors)
	}

	return nil
}

// GetPrioritizedCaseResponseMultiError is an error wrapping multiple
// validation errors returned by GetPrioritizedCaseResponse.ValidateAll() if
// the designated constraints aren't met.
type GetPrioritizedCaseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPrioritizedCaseResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPrioritizedCaseResponseMultiError) AllErrors() []error { return m }

// GetPrioritizedCaseResponseValidationError is the validation error returned
// by GetPrioritizedCaseResponse.Validate if the designated constraints aren't met.
type GetPrioritizedCaseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPrioritizedCaseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPrioritizedCaseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPrioritizedCaseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPrioritizedCaseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPrioritizedCaseResponseValidationError) ErrorName() string {
	return "GetPrioritizedCaseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPrioritizedCaseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPrioritizedCaseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPrioritizedCaseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPrioritizedCaseResponseValidationError{}

// Validate checks the field values on ListRulesRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListRulesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListRulesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListRulesRequestMultiError, or nil if none found.
func (m *ListRulesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListRulesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := ListRulesRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListRulesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListRulesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListRulesRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FilterBy

	switch v := m.FilterByValue.(type) {
	case *ListRulesRequest_ExternalId:
		if v == nil {
			err := ListRulesRequestValidationError{
				field:  "FilterByValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ExternalId
	case *ListRulesRequest_RuleName:
		if v == nil {
			err := ListRulesRequestValidationError{
				field:  "FilterByValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for RuleName
	case *ListRulesRequest_RuleGroup:
		if v == nil {
			err := ListRulesRequestValidationError{
				field:  "FilterByValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for RuleGroup
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ListRulesRequestMultiError(errors)
	}

	return nil
}

// ListRulesRequestMultiError is an error wrapping multiple validation errors
// returned by ListRulesRequest.ValidateAll() if the designated constraints
// aren't met.
type ListRulesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListRulesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListRulesRequestMultiError) AllErrors() []error { return m }

// ListRulesRequestValidationError is the validation error returned by
// ListRulesRequest.Validate if the designated constraints aren't met.
type ListRulesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListRulesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListRulesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListRulesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListRulesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListRulesRequestValidationError) ErrorName() string { return "ListRulesRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListRulesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListRulesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListRulesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListRulesRequestValidationError{}

// Validate checks the field values on ListRulesResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListRulesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListRulesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListRulesResponseMultiError, or nil if none found.
func (m *ListRulesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListRulesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListRulesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListRulesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListRulesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListRulesResponseValidationError{
						field:  fmt.Sprintf("Rules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListRulesResponseValidationError{
						field:  fmt.Sprintf("Rules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListRulesResponseValidationError{
					field:  fmt.Sprintf("Rules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListRulesResponseMultiError(errors)
	}

	return nil
}

// ListRulesResponseMultiError is an error wrapping multiple validation errors
// returned by ListRulesResponse.ValidateAll() if the designated constraints
// aren't met.
type ListRulesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListRulesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListRulesResponseMultiError) AllErrors() []error { return m }

// ListRulesResponseValidationError is the validation error returned by
// ListRulesResponse.Validate if the designated constraints aren't met.
type ListRulesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListRulesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListRulesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListRulesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListRulesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListRulesResponseValidationError) ErrorName() string {
	return "ListRulesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListRulesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListRulesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListRulesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListRulesResponseValidationError{}

// Validate checks the field values on CreateRuleRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateRuleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRuleRequestMultiError, or nil if none found.
func (m *CreateRuleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRuleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := CreateRuleRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRuleRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRuleRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRuleRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRuleRequestValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRuleRequestValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRuleRequestValidationError{
				field:  "Rule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRuleRequestMultiError(errors)
	}

	return nil
}

// CreateRuleRequestMultiError is an error wrapping multiple validation errors
// returned by CreateRuleRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateRuleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRuleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRuleRequestMultiError) AllErrors() []error { return m }

// CreateRuleRequestValidationError is the validation error returned by
// CreateRuleRequest.Validate if the designated constraints aren't met.
type CreateRuleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRuleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRuleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRuleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRuleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRuleRequestValidationError) ErrorName() string {
	return "CreateRuleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRuleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRuleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRuleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRuleRequestValidationError{}

// Validate checks the field values on CreateRuleResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRuleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRuleResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRuleResponseMultiError, or nil if none found.
func (m *CreateRuleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRuleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRuleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRuleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRuleResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRuleResponseMultiError(errors)
	}

	return nil
}

// CreateRuleResponseMultiError is an error wrapping multiple validation errors
// returned by CreateRuleResponse.ValidateAll() if the designated constraints
// aren't met.
type CreateRuleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRuleResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRuleResponseMultiError) AllErrors() []error { return m }

// CreateRuleResponseValidationError is the validation error returned by
// CreateRuleResponse.Validate if the designated constraints aren't met.
type CreateRuleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRuleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRuleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRuleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRuleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRuleResponseValidationError) ErrorName() string {
	return "CreateRuleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRuleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRuleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRuleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRuleResponseValidationError{}

// Validate checks the field values on GetAvailableReviewActionsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAvailableReviewActionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAvailableReviewActionsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAvailableReviewActionsRequestMultiError, or nil if none found.
func (m *GetAvailableReviewActionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAvailableReviewActionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetAvailableReviewActionsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAvailableReviewActionsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAvailableReviewActionsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAvailableReviewActionsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetCaseId()) < 1 {
		err := GetAvailableReviewActionsRequestValidationError{
			field:  "CaseId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetAvailableReviewActionsRequestMultiError(errors)
	}

	return nil
}

// GetAvailableReviewActionsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAvailableReviewActionsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAvailableReviewActionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAvailableReviewActionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAvailableReviewActionsRequestMultiError) AllErrors() []error { return m }

// GetAvailableReviewActionsRequestValidationError is the validation error
// returned by GetAvailableReviewActionsRequest.Validate if the designated
// constraints aren't met.
type GetAvailableReviewActionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAvailableReviewActionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAvailableReviewActionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAvailableReviewActionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAvailableReviewActionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAvailableReviewActionsRequestValidationError) ErrorName() string {
	return "GetAvailableReviewActionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAvailableReviewActionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAvailableReviewActionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAvailableReviewActionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAvailableReviewActionsRequestValidationError{}

// Validate checks the field values on GetAvailableReviewActionsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAvailableReviewActionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAvailableReviewActionsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAvailableReviewActionsResponseMultiError, or nil if none found.
func (m *GetAvailableReviewActionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAvailableReviewActionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAvailableReviewActionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAvailableReviewActionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAvailableReviewActionsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAvailableReviewActionsResponseMultiError(errors)
	}

	return nil
}

// GetAvailableReviewActionsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetAvailableReviewActionsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAvailableReviewActionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAvailableReviewActionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAvailableReviewActionsResponseMultiError) AllErrors() []error { return m }

// GetAvailableReviewActionsResponseValidationError is the validation error
// returned by GetAvailableReviewActionsResponse.Validate if the designated
// constraints aren't met.
type GetAvailableReviewActionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAvailableReviewActionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAvailableReviewActionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAvailableReviewActionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAvailableReviewActionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAvailableReviewActionsResponseValidationError) ErrorName() string {
	return "GetAvailableReviewActionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAvailableReviewActionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAvailableReviewActionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAvailableReviewActionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAvailableReviewActionsResponseValidationError{}

// Validate checks the field values on GetReviewActionFormElementsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetReviewActionFormElementsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReviewActionFormElementsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetReviewActionFormElementsRequestMultiError, or nil if none found.
func (m *GetReviewActionFormElementsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReviewActionFormElementsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetReviewActionFormElementsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReviewActionFormElementsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReviewActionFormElementsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReviewActionFormElementsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetReviewAction()) < 1 {
		err := GetReviewActionFormElementsRequestValidationError{
			field:  "ReviewAction",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetCaseId()) < 1 {
		err := GetReviewActionFormElementsRequestValidationError{
			field:  "CaseId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetReviewActionFormElementsRequestMultiError(errors)
	}

	return nil
}

// GetReviewActionFormElementsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetReviewActionFormElementsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetReviewActionFormElementsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReviewActionFormElementsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReviewActionFormElementsRequestMultiError) AllErrors() []error { return m }

// GetReviewActionFormElementsRequestValidationError is the validation error
// returned by GetReviewActionFormElementsRequest.Validate if the designated
// constraints aren't met.
type GetReviewActionFormElementsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReviewActionFormElementsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReviewActionFormElementsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReviewActionFormElementsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReviewActionFormElementsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReviewActionFormElementsRequestValidationError) ErrorName() string {
	return "GetReviewActionFormElementsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetReviewActionFormElementsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReviewActionFormElementsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReviewActionFormElementsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReviewActionFormElementsRequestValidationError{}

// Validate checks the field values on GetReviewActionFormElementsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetReviewActionFormElementsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReviewActionFormElementsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetReviewActionFormElementsResponseMultiError, or nil if none found.
func (m *GetReviewActionFormElementsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReviewActionFormElementsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReviewActionFormElementsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReviewActionFormElementsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReviewActionFormElementsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFormElements() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetReviewActionFormElementsResponseValidationError{
						field:  fmt.Sprintf("FormElements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetReviewActionFormElementsResponseValidationError{
						field:  fmt.Sprintf("FormElements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetReviewActionFormElementsResponseValidationError{
					field:  fmt.Sprintf("FormElements[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFormFields() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetReviewActionFormElementsResponseValidationError{
						field:  fmt.Sprintf("FormFields[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetReviewActionFormElementsResponseValidationError{
						field:  fmt.Sprintf("FormFields[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetReviewActionFormElementsResponseValidationError{
					field:  fmt.Sprintf("FormFields[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetReviewActionFormElementsResponseMultiError(errors)
	}

	return nil
}

// GetReviewActionFormElementsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetReviewActionFormElementsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetReviewActionFormElementsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReviewActionFormElementsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReviewActionFormElementsResponseMultiError) AllErrors() []error { return m }

// GetReviewActionFormElementsResponseValidationError is the validation error
// returned by GetReviewActionFormElementsResponse.Validate if the designated
// constraints aren't met.
type GetReviewActionFormElementsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReviewActionFormElementsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReviewActionFormElementsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReviewActionFormElementsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReviewActionFormElementsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReviewActionFormElementsResponseValidationError) ErrorName() string {
	return "GetReviewActionFormElementsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetReviewActionFormElementsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReviewActionFormElementsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReviewActionFormElementsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReviewActionFormElementsResponseValidationError{}

// Validate checks the field values on PerformReviewActionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerformReviewActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerformReviewActionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerformReviewActionRequestMultiError, or nil if none found.
func (m *PerformReviewActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PerformReviewActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := PerformReviewActionRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerformReviewActionRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerformReviewActionRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerformReviewActionRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetCaseId()) < 1 {
		err := PerformReviewActionRequestValidationError{
			field:  "CaseId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetReviewAction()) < 1 {
		err := PerformReviewActionRequestValidationError{
			field:  "ReviewAction",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetActionParameters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerformReviewActionRequestValidationError{
						field:  fmt.Sprintf("ActionParameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerformReviewActionRequestValidationError{
						field:  fmt.Sprintf("ActionParameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerformReviewActionRequestValidationError{
					field:  fmt.Sprintf("ActionParameters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PerformReviewActionRequestMultiError(errors)
	}

	return nil
}

// PerformReviewActionRequestMultiError is an error wrapping multiple
// validation errors returned by PerformReviewActionRequest.ValidateAll() if
// the designated constraints aren't met.
type PerformReviewActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerformReviewActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerformReviewActionRequestMultiError) AllErrors() []error { return m }

// PerformReviewActionRequestValidationError is the validation error returned
// by PerformReviewActionRequest.Validate if the designated constraints aren't met.
type PerformReviewActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerformReviewActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerformReviewActionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerformReviewActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerformReviewActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerformReviewActionRequestValidationError) ErrorName() string {
	return "PerformReviewActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PerformReviewActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerformReviewActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerformReviewActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerformReviewActionRequestValidationError{}

// Validate checks the field values on PerformReviewActionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerformReviewActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerformReviewActionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerformReviewActionResponseMultiError, or nil if none found.
func (m *PerformReviewActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PerformReviewActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerformReviewActionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerformReviewActionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerformReviewActionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActionResponses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerformReviewActionResponseValidationError{
						field:  fmt.Sprintf("ActionResponses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerformReviewActionResponseValidationError{
						field:  fmt.Sprintf("ActionResponses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerformReviewActionResponseValidationError{
					field:  fmt.Sprintf("ActionResponses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PerformReviewActionResponseMultiError(errors)
	}

	return nil
}

// PerformReviewActionResponseMultiError is an error wrapping multiple
// validation errors returned by PerformReviewActionResponse.ValidateAll() if
// the designated constraints aren't met.
type PerformReviewActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerformReviewActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerformReviewActionResponseMultiError) AllErrors() []error { return m }

// PerformReviewActionResponseValidationError is the validation error returned
// by PerformReviewActionResponse.Validate if the designated constraints
// aren't met.
type PerformReviewActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerformReviewActionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerformReviewActionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerformReviewActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerformReviewActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerformReviewActionResponseValidationError) ErrorName() string {
	return "PerformReviewActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PerformReviewActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerformReviewActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerformReviewActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerformReviewActionResponseValidationError{}

// Validate checks the field values on DownloadFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DownloadFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DownloadFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DownloadFileRequestMultiError, or nil if none found.
func (m *DownloadFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DownloadFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DownloadFileRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DownloadFileRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DownloadFileRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Path

	if len(errors) > 0 {
		return DownloadFileRequestMultiError(errors)
	}

	return nil
}

// DownloadFileRequestMultiError is an error wrapping multiple validation
// errors returned by DownloadFileRequest.ValidateAll() if the designated
// constraints aren't met.
type DownloadFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DownloadFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DownloadFileRequestMultiError) AllErrors() []error { return m }

// DownloadFileRequestValidationError is the validation error returned by
// DownloadFileRequest.Validate if the designated constraints aren't met.
type DownloadFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DownloadFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DownloadFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DownloadFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DownloadFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DownloadFileRequestValidationError) ErrorName() string {
	return "DownloadFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DownloadFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDownloadFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DownloadFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DownloadFileRequestValidationError{}

// Validate checks the field values on DownloadFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DownloadFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DownloadFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DownloadFileResponseMultiError, or nil if none found.
func (m *DownloadFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DownloadFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DownloadFileResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DownloadFileResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DownloadFileResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FileData

	// no validation rules for ContentDisposition

	// no validation rules for ContentType

	// no validation rules for DownloadedFileName

	if len(errors) > 0 {
		return DownloadFileResponseMultiError(errors)
	}

	return nil
}

// DownloadFileResponseMultiError is an error wrapping multiple validation
// errors returned by DownloadFileResponse.ValidateAll() if the designated
// constraints aren't met.
type DownloadFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DownloadFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DownloadFileResponseMultiError) AllErrors() []error { return m }

// DownloadFileResponseValidationError is the validation error returned by
// DownloadFileResponse.Validate if the designated constraints aren't met.
type DownloadFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DownloadFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DownloadFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DownloadFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DownloadFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DownloadFileResponseValidationError) ErrorName() string {
	return "DownloadFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DownloadFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDownloadFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DownloadFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DownloadFileResponseValidationError{}

// Validate checks the field values on CreateCommentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCommentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCommentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCommentRequestMultiError, or nil if none found.
func (m *CreateCommentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCommentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := CreateCommentRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCommentRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCommentRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCommentRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetComment() == nil {
		err := CreateCommentRequestValidationError{
			field:  "Comment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetComment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCommentRequestValidationError{
					field:  "Comment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCommentRequestValidationError{
					field:  "Comment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetComment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCommentRequestValidationError{
				field:  "Comment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCommentRequestMultiError(errors)
	}

	return nil
}

// CreateCommentRequestMultiError is an error wrapping multiple validation
// errors returned by CreateCommentRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateCommentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCommentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCommentRequestMultiError) AllErrors() []error { return m }

// CreateCommentRequestValidationError is the validation error returned by
// CreateCommentRequest.Validate if the designated constraints aren't met.
type CreateCommentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCommentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCommentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCommentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCommentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCommentRequestValidationError) ErrorName() string {
	return "CreateCommentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCommentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCommentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCommentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCommentRequestValidationError{}

// Validate checks the field values on CreateCommentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCommentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCommentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCommentResponseMultiError, or nil if none found.
func (m *CreateCommentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCommentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCommentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCommentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCommentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCommentResponseMultiError(errors)
	}

	return nil
}

// CreateCommentResponseMultiError is an error wrapping multiple validation
// errors returned by CreateCommentResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateCommentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCommentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCommentResponseMultiError) AllErrors() []error { return m }

// CreateCommentResponseValidationError is the validation error returned by
// CreateCommentResponse.Validate if the designated constraints aren't met.
type CreateCommentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCommentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCommentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCommentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCommentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCommentResponseValidationError) ErrorName() string {
	return "CreateCommentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCommentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCommentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCommentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCommentResponseValidationError{}

// Validate checks the field values on CreateAllowedAnnotationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAllowedAnnotationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAllowedAnnotationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateAllowedAnnotationRequestMultiError, or nil if none found.
func (m *CreateAllowedAnnotationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAllowedAnnotationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := CreateAllowedAnnotationRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAllowedAnnotationRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAllowedAnnotationRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAllowedAnnotationRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAllowedAnnotation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAllowedAnnotationRequestValidationError{
					field:  "AllowedAnnotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAllowedAnnotationRequestValidationError{
					field:  "AllowedAnnotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAllowedAnnotation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAllowedAnnotationRequestValidationError{
				field:  "AllowedAnnotation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAllowedAnnotationRequestMultiError(errors)
	}

	return nil
}

// CreateAllowedAnnotationRequestMultiError is an error wrapping multiple
// validation errors returned by CreateAllowedAnnotationRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateAllowedAnnotationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAllowedAnnotationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAllowedAnnotationRequestMultiError) AllErrors() []error { return m }

// CreateAllowedAnnotationRequestValidationError is the validation error
// returned by CreateAllowedAnnotationRequest.Validate if the designated
// constraints aren't met.
type CreateAllowedAnnotationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAllowedAnnotationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAllowedAnnotationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAllowedAnnotationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAllowedAnnotationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAllowedAnnotationRequestValidationError) ErrorName() string {
	return "CreateAllowedAnnotationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAllowedAnnotationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAllowedAnnotationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAllowedAnnotationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAllowedAnnotationRequestValidationError{}

// Validate checks the field values on CreateAllowedAnnotationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAllowedAnnotationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAllowedAnnotationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateAllowedAnnotationResponseMultiError, or nil if none found.
func (m *CreateAllowedAnnotationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAllowedAnnotationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAllowedAnnotationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAllowedAnnotationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAllowedAnnotationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAllowedAnnotationResponseMultiError(errors)
	}

	return nil
}

// CreateAllowedAnnotationResponseMultiError is an error wrapping multiple
// validation errors returned by CreateAllowedAnnotationResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateAllowedAnnotationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAllowedAnnotationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAllowedAnnotationResponseMultiError) AllErrors() []error { return m }

// CreateAllowedAnnotationResponseValidationError is the validation error
// returned by CreateAllowedAnnotationResponse.Validate if the designated
// constraints aren't met.
type CreateAllowedAnnotationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAllowedAnnotationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAllowedAnnotationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAllowedAnnotationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAllowedAnnotationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAllowedAnnotationResponseValidationError) ErrorName() string {
	return "CreateAllowedAnnotationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAllowedAnnotationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAllowedAnnotationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAllowedAnnotationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAllowedAnnotationResponseValidationError{}

// Validate checks the field values on GetAllowedAnnotationsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllowedAnnotationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllowedAnnotationsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllowedAnnotationsRequestMultiError, or nil if none found.
func (m *GetAllowedAnnotationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllowedAnnotationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetAllowedAnnotationsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllowedAnnotationsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllowedAnnotationsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllowedAnnotationsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntityType

	if all {
		switch v := interface{}(m.GetAnnotationType()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllowedAnnotationsRequestValidationError{
					field:  "AnnotationType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllowedAnnotationsRequestValidationError{
					field:  "AnnotationType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnotationType()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllowedAnnotationsRequestValidationError{
				field:  "AnnotationType",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAllowedAnnotationsRequestMultiError(errors)
	}

	return nil
}

// GetAllowedAnnotationsRequestMultiError is an error wrapping multiple
// validation errors returned by GetAllowedAnnotationsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAllowedAnnotationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllowedAnnotationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllowedAnnotationsRequestMultiError) AllErrors() []error { return m }

// GetAllowedAnnotationsRequestValidationError is the validation error returned
// by GetAllowedAnnotationsRequest.Validate if the designated constraints
// aren't met.
type GetAllowedAnnotationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllowedAnnotationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllowedAnnotationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllowedAnnotationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllowedAnnotationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllowedAnnotationsRequestValidationError) ErrorName() string {
	return "GetAllowedAnnotationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllowedAnnotationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllowedAnnotationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllowedAnnotationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllowedAnnotationsRequestValidationError{}

// Validate checks the field values on GetAllowedAnnotationsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllowedAnnotationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllowedAnnotationsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAllowedAnnotationsResponseMultiError, or nil if none found.
func (m *GetAllowedAnnotationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllowedAnnotationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllowedAnnotationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllowedAnnotationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllowedAnnotationsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAllowedAnnotations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllowedAnnotationsResponseValidationError{
						field:  fmt.Sprintf("AllowedAnnotations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllowedAnnotationsResponseValidationError{
						field:  fmt.Sprintf("AllowedAnnotations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllowedAnnotationsResponseValidationError{
					field:  fmt.Sprintf("AllowedAnnotations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAllowedAnnotationsResponseMultiError(errors)
	}

	return nil
}

// GetAllowedAnnotationsResponseMultiError is an error wrapping multiple
// validation errors returned by GetAllowedAnnotationsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetAllowedAnnotationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllowedAnnotationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllowedAnnotationsResponseMultiError) AllErrors() []error { return m }

// GetAllowedAnnotationsResponseValidationError is the validation error
// returned by GetAllowedAnnotationsResponse.Validate if the designated
// constraints aren't met.
type GetAllowedAnnotationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllowedAnnotationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllowedAnnotationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllowedAnnotationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllowedAnnotationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllowedAnnotationsResponseValidationError) ErrorName() string {
	return "GetAllowedAnnotationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllowedAnnotationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllowedAnnotationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllowedAnnotationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllowedAnnotationsResponseValidationError{}

// Validate checks the field values on ListAnnotationsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAnnotationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAnnotationsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAnnotationsRequestMultiError, or nil if none found.
func (m *ListAnnotationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAnnotationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := ListAnnotationsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAnnotationsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAnnotationsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAnnotationsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetQuery()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAnnotationsRequestValidationError{
					field:  "Query",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAnnotationsRequestValidationError{
					field:  "Query",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuery()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAnnotationsRequestValidationError{
				field:  "Query",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAnnotationsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAnnotationsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAnnotationsRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAnnotationsRequestMultiError(errors)
	}

	return nil
}

// ListAnnotationsRequestMultiError is an error wrapping multiple validation
// errors returned by ListAnnotationsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListAnnotationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAnnotationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAnnotationsRequestMultiError) AllErrors() []error { return m }

// ListAnnotationsRequestValidationError is the validation error returned by
// ListAnnotationsRequest.Validate if the designated constraints aren't met.
type ListAnnotationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAnnotationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAnnotationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAnnotationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAnnotationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAnnotationsRequestValidationError) ErrorName() string {
	return "ListAnnotationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAnnotationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAnnotationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAnnotationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAnnotationsRequestValidationError{}

// Validate checks the field values on ListAnnotationsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAnnotationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAnnotationsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAnnotationsResponseMultiError, or nil if none found.
func (m *ListAnnotationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAnnotationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAnnotationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAnnotationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAnnotationsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAnnotations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAnnotationsResponseValidationError{
						field:  fmt.Sprintf("Annotations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAnnotationsResponseValidationError{
						field:  fmt.Sprintf("Annotations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAnnotationsResponseValidationError{
					field:  fmt.Sprintf("Annotations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListAnnotationsResponseMultiError(errors)
	}

	return nil
}

// ListAnnotationsResponseMultiError is an error wrapping multiple validation
// errors returned by ListAnnotationsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListAnnotationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAnnotationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAnnotationsResponseMultiError) AllErrors() []error { return m }

// ListAnnotationsResponseValidationError is the validation error returned by
// ListAnnotationsResponse.Validate if the designated constraints aren't met.
type ListAnnotationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAnnotationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAnnotationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAnnotationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAnnotationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAnnotationsResponseValidationError) ErrorName() string {
	return "ListAnnotationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAnnotationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAnnotationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAnnotationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAnnotationsResponseValidationError{}

// Validate checks the field values on ListCommentsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCommentsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCommentsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCommentsRequestMultiError, or nil if none found.
func (m *ListCommentsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCommentsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := ListCommentsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCommentsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCommentsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCommentsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetQuery()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCommentsRequestValidationError{
					field:  "Query",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCommentsRequestValidationError{
					field:  "Query",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuery()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCommentsRequestValidationError{
				field:  "Query",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCommentsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCommentsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCommentsRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListCommentsRequestMultiError(errors)
	}

	return nil
}

// ListCommentsRequestMultiError is an error wrapping multiple validation
// errors returned by ListCommentsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListCommentsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCommentsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCommentsRequestMultiError) AllErrors() []error { return m }

// ListCommentsRequestValidationError is the validation error returned by
// ListCommentsRequest.Validate if the designated constraints aren't met.
type ListCommentsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCommentsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCommentsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCommentsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCommentsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCommentsRequestValidationError) ErrorName() string {
	return "ListCommentsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCommentsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCommentsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCommentsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCommentsRequestValidationError{}

// Validate checks the field values on ListCommentsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCommentsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCommentsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCommentsResponseMultiError, or nil if none found.
func (m *ListCommentsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCommentsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCommentsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCommentsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCommentsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetComments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCommentsResponseValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCommentsResponseValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCommentsResponseValidationError{
					field:  fmt.Sprintf("Comments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListCommentsResponseMultiError(errors)
	}

	return nil
}

// ListCommentsResponseMultiError is an error wrapping multiple validation
// errors returned by ListCommentsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListCommentsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCommentsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCommentsResponseMultiError) AllErrors() []error { return m }

// ListCommentsResponseValidationError is the validation error returned by
// ListCommentsResponse.Validate if the designated constraints aren't met.
type ListCommentsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCommentsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCommentsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCommentsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCommentsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCommentsResponseValidationError) ErrorName() string {
	return "ListCommentsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCommentsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCommentsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCommentsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCommentsResponseValidationError{}

// Validate checks the field values on CreateAnnotationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAnnotationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAnnotationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAnnotationRequestMultiError, or nil if none found.
func (m *CreateAnnotationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAnnotationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := CreateAnnotationRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAnnotationRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAnnotationRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAnnotationRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CaseId

	if _, ok := _CreateAnnotationRequest_EntityType_NotInLookup[m.GetEntityType()]; ok {
		err := CreateAnnotationRequestValidationError{
			field:  "EntityType",
			reason: "value must not be in list [REVIEW_ENTITY_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetEntityId()) < 1 {
		err := CreateAnnotationRequestValidationError{
			field:  "EntityId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAllowedAnnotationId()) < 1 {
		err := CreateAnnotationRequestValidationError{
			field:  "AllowedAnnotationId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAddedByEmail()) < 1 {
		err := CreateAnnotationRequestValidationError{
			field:  "AddedByEmail",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateAnnotationRequestMultiError(errors)
	}

	return nil
}

// CreateAnnotationRequestMultiError is an error wrapping multiple validation
// errors returned by CreateAnnotationRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAnnotationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAnnotationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAnnotationRequestMultiError) AllErrors() []error { return m }

// CreateAnnotationRequestValidationError is the validation error returned by
// CreateAnnotationRequest.Validate if the designated constraints aren't met.
type CreateAnnotationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAnnotationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAnnotationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAnnotationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAnnotationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAnnotationRequestValidationError) ErrorName() string {
	return "CreateAnnotationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAnnotationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAnnotationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAnnotationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAnnotationRequestValidationError{}

var _CreateAnnotationRequest_EntityType_NotInLookup = map[review.ReviewEntityType]struct{}{
	0: {},
}

// Validate checks the field values on CreateAnnotationResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAnnotationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAnnotationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAnnotationResponseMultiError, or nil if none found.
func (m *CreateAnnotationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAnnotationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAnnotationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAnnotationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAnnotationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAnnotationResponseMultiError(errors)
	}

	return nil
}

// CreateAnnotationResponseMultiError is an error wrapping multiple validation
// errors returned by CreateAnnotationResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateAnnotationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAnnotationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAnnotationResponseMultiError) AllErrors() []error { return m }

// CreateAnnotationResponseValidationError is the validation error returned by
// CreateAnnotationResponse.Validate if the designated constraints aren't met.
type CreateAnnotationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAnnotationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAnnotationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAnnotationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAnnotationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAnnotationResponseValidationError) ErrorName() string {
	return "CreateAnnotationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAnnotationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAnnotationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAnnotationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAnnotationResponseValidationError{}

// Validate checks the field values on CreateAnnotationsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAnnotationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAnnotationsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAnnotationsRequestMultiError, or nil if none found.
func (m *CreateAnnotationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAnnotationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := CreateAnnotationsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAnnotationsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAnnotationsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAnnotationsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetAnnotations()) < 1 {
		err := CreateAnnotationsRequestValidationError{
			field:  "Annotations",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetAnnotations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateAnnotationsRequestValidationError{
						field:  fmt.Sprintf("Annotations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateAnnotationsRequestValidationError{
						field:  fmt.Sprintf("Annotations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateAnnotationsRequestValidationError{
					field:  fmt.Sprintf("Annotations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateAnnotationsRequestMultiError(errors)
	}

	return nil
}

// CreateAnnotationsRequestMultiError is an error wrapping multiple validation
// errors returned by CreateAnnotationsRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAnnotationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAnnotationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAnnotationsRequestMultiError) AllErrors() []error { return m }

// CreateAnnotationsRequestValidationError is the validation error returned by
// CreateAnnotationsRequest.Validate if the designated constraints aren't met.
type CreateAnnotationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAnnotationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAnnotationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAnnotationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAnnotationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAnnotationsRequestValidationError) ErrorName() string {
	return "CreateAnnotationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAnnotationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAnnotationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAnnotationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAnnotationsRequestValidationError{}

// Validate checks the field values on CreateAnnotationsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAnnotationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAnnotationsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAnnotationsResponseMultiError, or nil if none found.
func (m *CreateAnnotationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAnnotationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAnnotationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAnnotationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAnnotationsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAnnotationsResponseMultiError(errors)
	}

	return nil
}

// CreateAnnotationsResponseMultiError is an error wrapping multiple validation
// errors returned by CreateAnnotationsResponse.ValidateAll() if the
// designated constraints aren't met.
type CreateAnnotationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAnnotationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAnnotationsResponseMultiError) AllErrors() []error { return m }

// CreateAnnotationsResponseValidationError is the validation error returned by
// CreateAnnotationsResponse.Validate if the designated constraints aren't met.
type CreateAnnotationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAnnotationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAnnotationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAnnotationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAnnotationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAnnotationsResponseValidationError) ErrorName() string {
	return "CreateAnnotationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAnnotationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAnnotationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAnnotationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAnnotationsResponseValidationError{}

// Validate checks the field values on GetCaseDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCaseDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCaseDetailsRequestMultiError, or nil if none found.
func (m *GetCaseDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CaseId

	if len(errors) > 0 {
		return GetCaseDetailsRequestMultiError(errors)
	}

	return nil
}

// GetCaseDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetCaseDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCaseDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseDetailsRequestMultiError) AllErrors() []error { return m }

// GetCaseDetailsRequestValidationError is the validation error returned by
// GetCaseDetailsRequest.Validate if the designated constraints aren't met.
type GetCaseDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseDetailsRequestValidationError) ErrorName() string {
	return "GetCaseDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCaseDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseDetailsRequestValidationError{}

// Validate checks the field values on GetCaseDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCaseDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCaseDetailsResponseMultiError, or nil if none found.
func (m *GetCaseDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAlerts()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "Alerts",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "Alerts",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAlerts()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseDetailsResponseValidationError{
				field:  "Alerts",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRelatedCases()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "RelatedCases",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "RelatedCases",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRelatedCases()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseDetailsResponseValidationError{
				field:  "RelatedCases",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPastActions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "PastActions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "PastActions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPastActions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseDetailsResponseValidationError{
				field:  "PastActions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "Case",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "Case",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseDetailsResponseValidationError{
				field:  "Case",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FirehoseId

	if all {
		switch v := interface{}(m.GetEscalationTickets()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "EscalationTickets",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "EscalationTickets",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEscalationTickets()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseDetailsResponseValidationError{
				field:  "EscalationTickets",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCaseDetailsResponseMultiError(errors)
	}

	return nil
}

// GetCaseDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetCaseDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCaseDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseDetailsResponseMultiError) AllErrors() []error { return m }

// GetCaseDetailsResponseValidationError is the validation error returned by
// GetCaseDetailsResponse.Validate if the designated constraints aren't met.
type GetCaseDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseDetailsResponseValidationError) ErrorName() string {
	return "GetCaseDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCaseDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseDetailsResponseValidationError{}

// Validate checks the field values on GetCaseFiltersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCaseFiltersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseFiltersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCaseFiltersRequestMultiError, or nil if none found.
func (m *GetCaseFiltersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseFiltersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseFiltersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseFiltersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseFiltersRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCaseFiltersRequestMultiError(errors)
	}

	return nil
}

// GetCaseFiltersRequestMultiError is an error wrapping multiple validation
// errors returned by GetCaseFiltersRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCaseFiltersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseFiltersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseFiltersRequestMultiError) AllErrors() []error { return m }

// GetCaseFiltersRequestValidationError is the validation error returned by
// GetCaseFiltersRequest.Validate if the designated constraints aren't met.
type GetCaseFiltersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseFiltersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseFiltersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseFiltersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseFiltersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseFiltersRequestValidationError) ErrorName() string {
	return "GetCaseFiltersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCaseFiltersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseFiltersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseFiltersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseFiltersRequestValidationError{}

// Validate checks the field values on GetCaseFiltersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCaseFiltersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseFiltersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCaseFiltersResponseMultiError, or nil if none found.
func (m *GetCaseFiltersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseFiltersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseFiltersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCaseFiltersResponseValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCaseFiltersResponseValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCaseFiltersResponseValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCaseFiltersResponseMultiError(errors)
	}

	return nil
}

// GetCaseFiltersResponseMultiError is an error wrapping multiple validation
// errors returned by GetCaseFiltersResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCaseFiltersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseFiltersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseFiltersResponseMultiError) AllErrors() []error { return m }

// GetCaseFiltersResponseValidationError is the validation error returned by
// GetCaseFiltersResponse.Validate if the designated constraints aren't met.
type GetCaseFiltersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseFiltersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseFiltersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseFiltersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseFiltersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseFiltersResponseValidationError) ErrorName() string {
	return "GetCaseFiltersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCaseFiltersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseFiltersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseFiltersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseFiltersResponseValidationError{}

// Validate checks the field values on ListAssignedCasesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAssignedCasesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAssignedCasesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAssignedCasesRequestMultiError, or nil if none found.
func (m *ListAssignedCasesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAssignedCasesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAssignedCasesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAssignedCasesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAssignedCasesRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAssignedCasesRequestValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAssignedCasesRequestValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAssignedCasesRequestValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAssignedCasesRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAssignedCasesRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAssignedCasesRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAssignedCasesRequestMultiError(errors)
	}

	return nil
}

// ListAssignedCasesRequestMultiError is an error wrapping multiple validation
// errors returned by ListAssignedCasesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListAssignedCasesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAssignedCasesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAssignedCasesRequestMultiError) AllErrors() []error { return m }

// ListAssignedCasesRequestValidationError is the validation error returned by
// ListAssignedCasesRequest.Validate if the designated constraints aren't met.
type ListAssignedCasesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAssignedCasesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAssignedCasesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAssignedCasesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAssignedCasesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAssignedCasesRequestValidationError) ErrorName() string {
	return "ListAssignedCasesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAssignedCasesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAssignedCasesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAssignedCasesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAssignedCasesRequestValidationError{}

// Validate checks the field values on ListAssignedCasesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAssignedCasesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAssignedCasesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAssignedCasesResponseMultiError, or nil if none found.
func (m *ListAssignedCasesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAssignedCasesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAssignedCasesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAssignedCasesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAssignedCasesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAssignedCasesResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAssignedCasesResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAssignedCasesResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAssignedCasesResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAssignedCasesResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAssignedCasesResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAssignedCasesResponseMultiError(errors)
	}

	return nil
}

// ListAssignedCasesResponseMultiError is an error wrapping multiple validation
// errors returned by ListAssignedCasesResponse.ValidateAll() if the
// designated constraints aren't met.
type ListAssignedCasesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAssignedCasesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAssignedCasesResponseMultiError) AllErrors() []error { return m }

// ListAssignedCasesResponseValidationError is the validation error returned by
// ListAssignedCasesResponse.Validate if the designated constraints aren't met.
type ListAssignedCasesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAssignedCasesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAssignedCasesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAssignedCasesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAssignedCasesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAssignedCasesResponseValidationError) ErrorName() string {
	return "ListAssignedCasesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAssignedCasesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAssignedCasesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAssignedCasesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAssignedCasesResponseValidationError{}

// Validate checks the field values on GetTransactionFiltersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionFiltersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionFiltersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTransactionFiltersRequestMultiError, or nil if none found.
func (m *GetTransactionFiltersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionFiltersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionFiltersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionFiltersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionFiltersRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CaseId

	if len(errors) > 0 {
		return GetTransactionFiltersRequestMultiError(errors)
	}

	return nil
}

// GetTransactionFiltersRequestMultiError is an error wrapping multiple
// validation errors returned by GetTransactionFiltersRequest.ValidateAll() if
// the designated constraints aren't met.
type GetTransactionFiltersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionFiltersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionFiltersRequestMultiError) AllErrors() []error { return m }

// GetTransactionFiltersRequestValidationError is the validation error returned
// by GetTransactionFiltersRequest.Validate if the designated constraints
// aren't met.
type GetTransactionFiltersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionFiltersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionFiltersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionFiltersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionFiltersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionFiltersRequestValidationError) ErrorName() string {
	return "GetTransactionFiltersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionFiltersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionFiltersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionFiltersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionFiltersRequestValidationError{}

// Validate checks the field values on GetTransactionFiltersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionFiltersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionFiltersResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTransactionFiltersResponseMultiError, or nil if none found.
func (m *GetTransactionFiltersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionFiltersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionFiltersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTransactionFiltersResponseValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTransactionFiltersResponseValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTransactionFiltersResponseValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTransactionFiltersResponseMultiError(errors)
	}

	return nil
}

// GetTransactionFiltersResponseMultiError is an error wrapping multiple
// validation errors returned by GetTransactionFiltersResponse.ValidateAll()
// if the designated constraints aren't met.
type GetTransactionFiltersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionFiltersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionFiltersResponseMultiError) AllErrors() []error { return m }

// GetTransactionFiltersResponseValidationError is the validation error
// returned by GetTransactionFiltersResponse.Validate if the designated
// constraints aren't met.
type GetTransactionFiltersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionFiltersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionFiltersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionFiltersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionFiltersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionFiltersResponseValidationError) ErrorName() string {
	return "GetTransactionFiltersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionFiltersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionFiltersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionFiltersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionFiltersResponseValidationError{}

// Validate checks the field values on GetTransactionsTableRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionsTableRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionsTableRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTransactionsTableRequestMultiError, or nil if none found.
func (m *GetTransactionsTableRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionsTableRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionsTableRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionsTableRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionsTableRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _GetTransactionsTableRequest_SortBy_InLookup[m.GetSortBy()]; !ok {
		err := GetTransactionsTableRequestValidationError{
			field:  "SortBy",
			reason: "value must be in list [TRANSACTION_TABLE_FIELD_MASK_UNSPECIFIED TRANSACTION_TABLE_FIELD_MASK_ACTOR_FROM TRANSACTION_TABLE_FIELD_MASK_ACTOR_TO TRANSACTION_TABLE_FIELD_MASK_AMOUNT TRANSACTION_TABLE_FIELD_MASK_ORDER_WORKFLOW TRANSACTION_TABLE_FIELD_MASK_STATUS TRANSACTION_TABLE_FIELD_MASK_ACCOUNTING_ENTRY TRANSACTION_TABLE_FIELD_MASK_CREATED_AT TRANSACTION_TABLE_FIELD_MASK_PROVENANCE]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTransactionsTableRequestValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTransactionsTableRequestValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTransactionsTableRequestValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionsTableRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionsTableRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionsTableRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Identifier.(type) {
	case *GetTransactionsTableRequest_ActorId:
		if v == nil {
			err := GetTransactionsTableRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	case *GetTransactionsTableRequest_CaseId:
		if v == nil {
			err := GetTransactionsTableRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for CaseId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetTransactionsTableRequestMultiError(errors)
	}

	return nil
}

// GetTransactionsTableRequestMultiError is an error wrapping multiple
// validation errors returned by GetTransactionsTableRequest.ValidateAll() if
// the designated constraints aren't met.
type GetTransactionsTableRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionsTableRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionsTableRequestMultiError) AllErrors() []error { return m }

// GetTransactionsTableRequestValidationError is the validation error returned
// by GetTransactionsTableRequest.Validate if the designated constraints
// aren't met.
type GetTransactionsTableRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionsTableRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionsTableRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionsTableRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionsTableRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionsTableRequestValidationError) ErrorName() string {
	return "GetTransactionsTableRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionsTableRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionsTableRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionsTableRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionsTableRequestValidationError{}

var _GetTransactionsTableRequest_SortBy_InLookup = map[TransactionTableFieldMask]struct{}{
	0:  {},
	2:  {},
	3:  {},
	6:  {},
	8:  {},
	10: {},
	17: {},
	18: {},
	19: {},
}

// Validate checks the field values on GetTransactionsTableResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionsTableResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionsTableResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTransactionsTableResponseMultiError, or nil if none found.
func (m *GetTransactionsTableResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionsTableResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionsTableResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionsTableResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionsTableResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionsTableResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionsTableResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionsTableResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionsTableResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionsTableResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionsTableResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTransactionsTableResponseMultiError(errors)
	}

	return nil
}

// GetTransactionsTableResponseMultiError is an error wrapping multiple
// validation errors returned by GetTransactionsTableResponse.ValidateAll() if
// the designated constraints aren't met.
type GetTransactionsTableResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionsTableResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionsTableResponseMultiError) AllErrors() []error { return m }

// GetTransactionsTableResponseValidationError is the validation error returned
// by GetTransactionsTableResponse.Validate if the designated constraints
// aren't met.
type GetTransactionsTableResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionsTableResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionsTableResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionsTableResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionsTableResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionsTableResponseValidationError) ErrorName() string {
	return "GetTransactionsTableResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionsTableResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionsTableResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionsTableResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionsTableResponseValidationError{}

// Validate checks the field values on QueueElement with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QueueElement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueueElement with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QueueElementMultiError, or
// nil if none found.
func (m *QueueElement) ValidateAll() error {
	return m.validate(true)
}

func (m *QueueElement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	switch v := m.Payload.(type) {
	case *QueueElement_LivenessReview:
		if v == nil {
			err := QueueElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLivenessReview()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "LivenessReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "LivenessReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLivenessReview()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueueElementValidationError{
					field:  "LivenessReview",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *QueueElement_FacematchReview:
		if v == nil {
			err := QueueElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFacematchReview()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "FacematchReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "FacematchReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFacematchReview()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueueElementValidationError{
					field:  "FacematchReview",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *QueueElement_LivenessSampleReview:
		if v == nil {
			err := QueueElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLivenessSampleReview()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "LivenessSampleReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "LivenessSampleReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLivenessSampleReview()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueueElementValidationError{
					field:  "LivenessSampleReview",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *QueueElement_AfuLivenessReview:
		if v == nil {
			err := QueueElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAfuLivenessReview()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "AfuLivenessReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "AfuLivenessReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAfuLivenessReview()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueueElementValidationError{
					field:  "AfuLivenessReview",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *QueueElement_AfuFacematchReview:
		if v == nil {
			err := QueueElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAfuFacematchReview()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "AfuFacematchReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "AfuFacematchReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAfuFacematchReview()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueueElementValidationError{
					field:  "AfuFacematchReview",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *QueueElement_NamematchReview:
		if v == nil {
			err := QueueElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNamematchReview()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "NamematchReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "NamematchReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNamematchReview()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueueElementValidationError{
					field:  "NamematchReview",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *QueueElement_FacematchSampleReview:
		if v == nil {
			err := QueueElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFacematchSampleReview()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "FacematchSampleReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "FacematchSampleReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFacematchSampleReview()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueueElementValidationError{
					field:  "FacematchSampleReview",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *QueueElement_PanNameSampleReview:
		if v == nil {
			err := QueueElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPanNameSampleReview()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "PanNameSampleReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "PanNameSampleReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPanNameSampleReview()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueueElementValidationError{
					field:  "PanNameSampleReview",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *QueueElement_LivenessAndFacematchReview:
		if v == nil {
			err := QueueElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLivenessAndFacematchReview()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "LivenessAndFacematchReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "LivenessAndFacematchReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLivenessAndFacematchReview()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueueElementValidationError{
					field:  "LivenessAndFacematchReview",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *QueueElement_AfuLivenessFacematchReview:
		if v == nil {
			err := QueueElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAfuLivenessFacematchReview()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "AfuLivenessFacematchReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "AfuLivenessFacematchReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAfuLivenessFacematchReview()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueueElementValidationError{
					field:  "AfuLivenessFacematchReview",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *QueueElement_UserReview:
		if v == nil {
			err := QueueElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUserReview()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "UserReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "UserReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUserReview()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueueElementValidationError{
					field:  "UserReview",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *QueueElement_StockGuardianCkycDocuments:
		if v == nil {
			err := QueueElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStockGuardianCkycDocuments()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "StockGuardianCkycDocuments",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueueElementValidationError{
						field:  "StockGuardianCkycDocuments",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStockGuardianCkycDocuments()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueueElementValidationError{
					field:  "StockGuardianCkycDocuments",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return QueueElementMultiError(errors)
	}

	return nil
}

// QueueElementMultiError is an error wrapping multiple validation errors
// returned by QueueElement.ValidateAll() if the designated constraints aren't met.
type QueueElementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueueElementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueueElementMultiError) AllErrors() []error { return m }

// QueueElementValidationError is the validation error returned by
// QueueElement.Validate if the designated constraints aren't met.
type QueueElementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueueElementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueueElementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueueElementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueueElementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueueElementValidationError) ErrorName() string { return "QueueElementValidationError" }

// Error satisfies the builtin error interface
func (e QueueElementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueueElement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueueElementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueueElementValidationError{}

// Validate checks the field values on GetLivenessAndFacematchQueueRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLivenessAndFacematchQueueRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLivenessAndFacematchQueueRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetLivenessAndFacematchQueueRequestMultiError, or nil if none found.
func (m *GetLivenessAndFacematchQueueRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLivenessAndFacematchQueueRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetLivenessAndFacematchQueueRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessAndFacematchQueueRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessAndFacematchQueueRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessAndFacematchQueueRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PayloadType

	// no validation rules for Limit

	// no validation rules for PageNum

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessAndFacematchQueueRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessAndFacematchQueueRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessAndFacematchQueueRequestValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessAndFacematchQueueRequestValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessAndFacematchQueueRequestValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessAndFacematchQueueRequestValidationError{
				field:  "ToTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsFifo

	if len(errors) > 0 {
		return GetLivenessAndFacematchQueueRequestMultiError(errors)
	}

	return nil
}

// GetLivenessAndFacematchQueueRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetLivenessAndFacematchQueueRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLivenessAndFacematchQueueRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLivenessAndFacematchQueueRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLivenessAndFacematchQueueRequestMultiError) AllErrors() []error { return m }

// GetLivenessAndFacematchQueueRequestValidationError is the validation error
// returned by GetLivenessAndFacematchQueueRequest.Validate if the designated
// constraints aren't met.
type GetLivenessAndFacematchQueueRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLivenessAndFacematchQueueRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLivenessAndFacematchQueueRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLivenessAndFacematchQueueRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLivenessAndFacematchQueueRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLivenessAndFacematchQueueRequestValidationError) ErrorName() string {
	return "GetLivenessAndFacematchQueueRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLivenessAndFacematchQueueRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLivenessAndFacematchQueueRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLivenessAndFacematchQueueRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLivenessAndFacematchQueueRequestValidationError{}

// Validate checks the field values on GetLivenessAndFacematchQueueResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetLivenessAndFacematchQueueResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLivenessAndFacematchQueueResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetLivenessAndFacematchQueueResponseMultiError, or nil if none found.
func (m *GetLivenessAndFacematchQueueResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLivenessAndFacematchQueueResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessAndFacematchQueueResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessAndFacematchQueueResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessAndFacematchQueueResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetElements() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLivenessAndFacematchQueueResponseValidationError{
						field:  fmt.Sprintf("Elements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLivenessAndFacematchQueueResponseValidationError{
						field:  fmt.Sprintf("Elements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLivenessAndFacematchQueueResponseValidationError{
					field:  fmt.Sprintf("Elements[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetLivenessAndFacematchQueueResponseMultiError(errors)
	}

	return nil
}

// GetLivenessAndFacematchQueueResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetLivenessAndFacematchQueueResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLivenessAndFacematchQueueResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLivenessAndFacematchQueueResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLivenessAndFacematchQueueResponseMultiError) AllErrors() []error { return m }

// GetLivenessAndFacematchQueueResponseValidationError is the validation error
// returned by GetLivenessAndFacematchQueueResponse.Validate if the designated
// constraints aren't met.
type GetLivenessAndFacematchQueueResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLivenessAndFacematchQueueResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLivenessAndFacematchQueueResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLivenessAndFacematchQueueResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLivenessAndFacematchQueueResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLivenessAndFacematchQueueResponseValidationError) ErrorName() string {
	return "GetLivenessAndFacematchQueueResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLivenessAndFacematchQueueResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLivenessAndFacematchQueueResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLivenessAndFacematchQueueResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLivenessAndFacematchQueueResponseValidationError{}

// Validate checks the field values on DeleteLivenessAndFacematchQueueRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DeleteLivenessAndFacematchQueueRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DeleteLivenessAndFacematchQueueRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// DeleteLivenessAndFacematchQueueRequestMultiError, or nil if none found.
func (m *DeleteLivenessAndFacematchQueueRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteLivenessAndFacematchQueueRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := DeleteLivenessAndFacematchQueueRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteLivenessAndFacematchQueueRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteLivenessAndFacematchQueueRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteLivenessAndFacematchQueueRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetId()) < 1 {
		err := DeleteLivenessAndFacematchQueueRequestValidationError{
			field:  "Id",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := DeleteLivenessAndFacematchQueueRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PayloadType

	if len(errors) > 0 {
		return DeleteLivenessAndFacematchQueueRequestMultiError(errors)
	}

	return nil
}

// DeleteLivenessAndFacematchQueueRequestMultiError is an error wrapping
// multiple validation errors returned by
// DeleteLivenessAndFacematchQueueRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteLivenessAndFacematchQueueRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteLivenessAndFacematchQueueRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteLivenessAndFacematchQueueRequestMultiError) AllErrors() []error { return m }

// DeleteLivenessAndFacematchQueueRequestValidationError is the validation
// error returned by DeleteLivenessAndFacematchQueueRequest.Validate if the
// designated constraints aren't met.
type DeleteLivenessAndFacematchQueueRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteLivenessAndFacematchQueueRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteLivenessAndFacematchQueueRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteLivenessAndFacematchQueueRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteLivenessAndFacematchQueueRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteLivenessAndFacematchQueueRequestValidationError) ErrorName() string {
	return "DeleteLivenessAndFacematchQueueRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteLivenessAndFacematchQueueRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteLivenessAndFacematchQueueRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteLivenessAndFacematchQueueRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteLivenessAndFacematchQueueRequestValidationError{}

// Validate checks the field values on DeleteLivenessAndFacematchQueueResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DeleteLivenessAndFacematchQueueResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DeleteLivenessAndFacematchQueueResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// DeleteLivenessAndFacematchQueueResponseMultiError, or nil if none found.
func (m *DeleteLivenessAndFacematchQueueResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteLivenessAndFacematchQueueResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteLivenessAndFacematchQueueResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteLivenessAndFacematchQueueResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteLivenessAndFacematchQueueResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteLivenessAndFacematchQueueResponseMultiError(errors)
	}

	return nil
}

// DeleteLivenessAndFacematchQueueResponseMultiError is an error wrapping
// multiple validation errors returned by
// DeleteLivenessAndFacematchQueueResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteLivenessAndFacematchQueueResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteLivenessAndFacematchQueueResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteLivenessAndFacematchQueueResponseMultiError) AllErrors() []error { return m }

// DeleteLivenessAndFacematchQueueResponseValidationError is the validation
// error returned by DeleteLivenessAndFacematchQueueResponse.Validate if the
// designated constraints aren't met.
type DeleteLivenessAndFacematchQueueResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteLivenessAndFacematchQueueResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteLivenessAndFacematchQueueResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteLivenessAndFacematchQueueResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteLivenessAndFacematchQueueResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteLivenessAndFacematchQueueResponseValidationError) ErrorName() string {
	return "DeleteLivenessAndFacematchQueueResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteLivenessAndFacematchQueueResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteLivenessAndFacematchQueueResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteLivenessAndFacematchQueueResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteLivenessAndFacematchQueueResponseValidationError{}

// Validate checks the field values on GetPendingReviewCountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPendingReviewCountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPendingReviewCountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPendingReviewCountRequestMultiError, or nil if none found.
func (m *GetPendingReviewCountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPendingReviewCountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetPendingReviewCountRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPendingReviewCountRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPendingReviewCountRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPendingReviewCountRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PayloadType

	if len(errors) > 0 {
		return GetPendingReviewCountRequestMultiError(errors)
	}

	return nil
}

// GetPendingReviewCountRequestMultiError is an error wrapping multiple
// validation errors returned by GetPendingReviewCountRequest.ValidateAll() if
// the designated constraints aren't met.
type GetPendingReviewCountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPendingReviewCountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPendingReviewCountRequestMultiError) AllErrors() []error { return m }

// GetPendingReviewCountRequestValidationError is the validation error returned
// by GetPendingReviewCountRequest.Validate if the designated constraints
// aren't met.
type GetPendingReviewCountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPendingReviewCountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPendingReviewCountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPendingReviewCountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPendingReviewCountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPendingReviewCountRequestValidationError) ErrorName() string {
	return "GetPendingReviewCountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPendingReviewCountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPendingReviewCountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPendingReviewCountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPendingReviewCountRequestValidationError{}

// Validate checks the field values on GetPendingReviewCountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPendingReviewCountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPendingReviewCountResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetPendingReviewCountResponseMultiError, or nil if none found.
func (m *GetPendingReviewCountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPendingReviewCountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPendingReviewCountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPendingReviewCountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPendingReviewCountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Count

	if len(errors) > 0 {
		return GetPendingReviewCountResponseMultiError(errors)
	}

	return nil
}

// GetPendingReviewCountResponseMultiError is an error wrapping multiple
// validation errors returned by GetPendingReviewCountResponse.ValidateAll()
// if the designated constraints aren't met.
type GetPendingReviewCountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPendingReviewCountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPendingReviewCountResponseMultiError) AllErrors() []error { return m }

// GetPendingReviewCountResponseValidationError is the validation error
// returned by GetPendingReviewCountResponse.Validate if the designated
// constraints aren't met.
type GetPendingReviewCountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPendingReviewCountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPendingReviewCountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPendingReviewCountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPendingReviewCountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPendingReviewCountResponseValidationError) ErrorName() string {
	return "GetPendingReviewCountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPendingReviewCountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPendingReviewCountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPendingReviewCountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPendingReviewCountResponseValidationError{}

// Validate checks the field values on AuditLogMediaDownloadRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuditLogMediaDownloadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuditLogMediaDownloadRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuditLogMediaDownloadRequestMultiError, or nil if none found.
func (m *AuditLogMediaDownloadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AuditLogMediaDownloadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := AuditLogMediaDownloadRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuditLogMediaDownloadRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuditLogMediaDownloadRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuditLogMediaDownloadRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetElementId()) < 1 {
		err := AuditLogMediaDownloadRequestValidationError{
			field:  "ElementId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := AuditLogMediaDownloadRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAgentName()) < 1 {
		err := AuditLogMediaDownloadRequestValidationError{
			field:  "AgentName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for VideoLocation

	// no validation rules for VideoFrameLocation

	// no validation rules for KycImageLocation

	// no validation rules for PayloadType

	if len(errors) > 0 {
		return AuditLogMediaDownloadRequestMultiError(errors)
	}

	return nil
}

// AuditLogMediaDownloadRequestMultiError is an error wrapping multiple
// validation errors returned by AuditLogMediaDownloadRequest.ValidateAll() if
// the designated constraints aren't met.
type AuditLogMediaDownloadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuditLogMediaDownloadRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuditLogMediaDownloadRequestMultiError) AllErrors() []error { return m }

// AuditLogMediaDownloadRequestValidationError is the validation error returned
// by AuditLogMediaDownloadRequest.Validate if the designated constraints
// aren't met.
type AuditLogMediaDownloadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuditLogMediaDownloadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuditLogMediaDownloadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuditLogMediaDownloadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuditLogMediaDownloadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuditLogMediaDownloadRequestValidationError) ErrorName() string {
	return "AuditLogMediaDownloadRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AuditLogMediaDownloadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuditLogMediaDownloadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuditLogMediaDownloadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuditLogMediaDownloadRequestValidationError{}

// Validate checks the field values on AuditLogMediaDownloadResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuditLogMediaDownloadResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuditLogMediaDownloadResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AuditLogMediaDownloadResponseMultiError, or nil if none found.
func (m *AuditLogMediaDownloadResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AuditLogMediaDownloadResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuditLogMediaDownloadResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuditLogMediaDownloadResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuditLogMediaDownloadResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuditLogMediaDownloadResponseMultiError(errors)
	}

	return nil
}

// AuditLogMediaDownloadResponseMultiError is an error wrapping multiple
// validation errors returned by AuditLogMediaDownloadResponse.ValidateAll()
// if the designated constraints aren't met.
type AuditLogMediaDownloadResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuditLogMediaDownloadResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuditLogMediaDownloadResponseMultiError) AllErrors() []error { return m }

// AuditLogMediaDownloadResponseValidationError is the validation error
// returned by AuditLogMediaDownloadResponse.Validate if the designated
// constraints aren't met.
type AuditLogMediaDownloadResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuditLogMediaDownloadResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuditLogMediaDownloadResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuditLogMediaDownloadResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuditLogMediaDownloadResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuditLogMediaDownloadResponseValidationError) ErrorName() string {
	return "AuditLogMediaDownloadResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AuditLogMediaDownloadResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuditLogMediaDownloadResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuditLogMediaDownloadResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuditLogMediaDownloadResponseValidationError{}

// Validate checks the field values on GetUserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserInfoRequestMultiError, or nil if none found.
func (m *GetUserInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetUserInfoRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetUserInfoRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetUserInfoRequestMultiError(errors)
	}

	return nil
}

// GetUserInfoRequestMultiError is an error wrapping multiple validation errors
// returned by GetUserInfoRequest.ValidateAll() if the designated constraints
// aren't met.
type GetUserInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserInfoRequestMultiError) AllErrors() []error { return m }

// GetUserInfoRequestValidationError is the validation error returned by
// GetUserInfoRequest.Validate if the designated constraints aren't met.
type GetUserInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserInfoRequestValidationError) ErrorName() string {
	return "GetUserInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserInfoRequestValidationError{}

// Validate checks the field values on GetUserInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserInfoResponseMultiError, or nil if none found.
func (m *GetUserInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBasicDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "BasicDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "BasicDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBasicDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoResponseValidationError{
				field:  "BasicDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserEnteredDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "UserEnteredDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "UserEnteredDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserEnteredDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoResponseValidationError{
				field:  "UserEnteredDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmploymentDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "EmploymentDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "EmploymentDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmploymentDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoResponseValidationError{
				field:  "EmploymentDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNameMatchDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "NameMatchDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "NameMatchDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNameMatchDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoResponseValidationError{
				field:  "NameMatchDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLivenessFacematchDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "LivenessFacematchDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "LivenessFacematchDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLivenessFacematchDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoResponseValidationError{
				field:  "LivenessFacematchDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRiskSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "RiskSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "RiskSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRiskSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoResponseValidationError{
				field:  "RiskSummary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeviceDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoResponseValidationError{
				field:  "DeviceDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccountStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "AccountStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "AccountStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoResponseValidationError{
				field:  "AccountStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScreenerCheckDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "ScreenerCheckDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoResponseValidationError{
					field:  "ScreenerCheckDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenerCheckDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoResponseValidationError{
				field:  "ScreenerCheckDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserInfoResponseMultiError(errors)
	}

	return nil
}

// GetUserInfoResponseMultiError is an error wrapping multiple validation
// errors returned by GetUserInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetUserInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserInfoResponseMultiError) AllErrors() []error { return m }

// GetUserInfoResponseValidationError is the validation error returned by
// GetUserInfoResponse.Validate if the designated constraints aren't met.
type GetUserInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserInfoResponseValidationError) ErrorName() string {
	return "GetUserInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserInfoResponseValidationError{}

// Validate checks the field values on UpdateRuleRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateRuleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateRuleRequestMultiError, or nil if none found.
func (m *UpdateRuleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRuleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := UpdateRuleRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRuleRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRuleRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRuleRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRuleRequestValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRuleRequestValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRuleRequestValidationError{
				field:  "Rule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateRuleRequestMultiError(errors)
	}

	return nil
}

// UpdateRuleRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateRuleRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateRuleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRuleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRuleRequestMultiError) AllErrors() []error { return m }

// UpdateRuleRequestValidationError is the validation error returned by
// UpdateRuleRequest.Validate if the designated constraints aren't met.
type UpdateRuleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRuleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRuleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRuleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRuleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRuleRequestValidationError) ErrorName() string {
	return "UpdateRuleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateRuleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRuleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRuleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRuleRequestValidationError{}

// Validate checks the field values on UpdateRuleResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateRuleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRuleResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateRuleResponseMultiError, or nil if none found.
func (m *UpdateRuleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRuleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRuleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRuleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRuleResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateRuleResponseMultiError(errors)
	}

	return nil
}

// UpdateRuleResponseMultiError is an error wrapping multiple validation errors
// returned by UpdateRuleResponse.ValidateAll() if the designated constraints
// aren't met.
type UpdateRuleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRuleResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRuleResponseMultiError) AllErrors() []error { return m }

// UpdateRuleResponseValidationError is the validation error returned by
// UpdateRuleResponse.Validate if the designated constraints aren't met.
type UpdateRuleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRuleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRuleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRuleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRuleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRuleResponseValidationError) ErrorName() string {
	return "UpdateRuleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateRuleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRuleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRuleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRuleResponseValidationError{}

// Validate checks the field values on ScreenerCheckDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScreenerCheckDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreenerCheckDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScreenerCheckDetailsMultiError, or nil if none found.
func (m *ScreenerCheckDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreenerCheckDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ScreenerAttemptResult

	if len(errors) > 0 {
		return ScreenerCheckDetailsMultiError(errors)
	}

	return nil
}

// ScreenerCheckDetailsMultiError is an error wrapping multiple validation
// errors returned by ScreenerCheckDetails.ValidateAll() if the designated
// constraints aren't met.
type ScreenerCheckDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreenerCheckDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreenerCheckDetailsMultiError) AllErrors() []error { return m }

// ScreenerCheckDetailsValidationError is the validation error returned by
// ScreenerCheckDetails.Validate if the designated constraints aren't met.
type ScreenerCheckDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreenerCheckDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreenerCheckDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreenerCheckDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreenerCheckDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreenerCheckDetailsValidationError) ErrorName() string {
	return "ScreenerCheckDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ScreenerCheckDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreenerCheckDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreenerCheckDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreenerCheckDetailsValidationError{}

// Validate checks the field values on BasicDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BasicDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BasicDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BasicDetailsMultiError, or
// nil if none found.
func (m *BasicDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *BasicDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReviewReason

	// no validation rules for DaysSinceAccountCreation

	// no validation rules for AccountCreationDate

	// no validation rules for CurrentOnboardingStage

	// no validation rules for SignupEmail

	// no validation rules for SignupEmailName

	if all {
		switch v := interface{}(m.GetBankDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BasicDetailsValidationError{
					field:  "BankDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BasicDetailsValidationError{
					field:  "BankDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BasicDetailsValidationError{
				field:  "BankDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BasicDetailsMultiError(errors)
	}

	return nil
}

// BasicDetailsMultiError is an error wrapping multiple validation errors
// returned by BasicDetails.ValidateAll() if the designated constraints aren't met.
type BasicDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BasicDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BasicDetailsMultiError) AllErrors() []error { return m }

// BasicDetailsValidationError is the validation error returned by
// BasicDetails.Validate if the designated constraints aren't met.
type BasicDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BasicDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BasicDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BasicDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BasicDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BasicDetailsValidationError) ErrorName() string { return "BasicDetailsValidationError" }

// Error satisfies the builtin error interface
func (e BasicDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBasicDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BasicDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BasicDetailsValidationError{}

// Validate checks the field values on BankDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BankDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BankDetailsMultiError, or
// nil if none found.
func (m *BankDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *BankDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsEtbCustomer

	// no validation rules for KycPath

	// no validation rules for KycLevel

	// no validation rules for KycDob

	if len(errors) > 0 {
		return BankDetailsMultiError(errors)
	}

	return nil
}

// BankDetailsMultiError is an error wrapping multiple validation errors
// returned by BankDetails.ValidateAll() if the designated constraints aren't met.
type BankDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankDetailsMultiError) AllErrors() []error { return m }

// BankDetailsValidationError is the validation error returned by
// BankDetails.Validate if the designated constraints aren't met.
type BankDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankDetailsValidationError) ErrorName() string { return "BankDetailsValidationError" }

// Error satisfies the builtin error interface
func (e BankDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankDetailsValidationError{}

// Validate checks the field values on UserEnteredDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UserEnteredDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserEnteredDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserEnteredDetailsMultiError, or nil if none found.
func (m *UserEnteredDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *UserEnteredDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RetriesOfDob

	// no validation rules for MotherName

	// no validation rules for FatherName

	// no validation rules for ShippingAddress

	// no validation rules for CommunicationAddress

	if len(errors) > 0 {
		return UserEnteredDetailsMultiError(errors)
	}

	return nil
}

// UserEnteredDetailsMultiError is an error wrapping multiple validation errors
// returned by UserEnteredDetails.ValidateAll() if the designated constraints
// aren't met.
type UserEnteredDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserEnteredDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserEnteredDetailsMultiError) AllErrors() []error { return m }

// UserEnteredDetailsValidationError is the validation error returned by
// UserEnteredDetails.Validate if the designated constraints aren't met.
type UserEnteredDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserEnteredDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserEnteredDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserEnteredDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserEnteredDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserEnteredDetailsValidationError) ErrorName() string {
	return "UserEnteredDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e UserEnteredDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserEnteredDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserEnteredDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserEnteredDetailsValidationError{}

// Validate checks the field values on EmploymentDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EmploymentDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmploymentDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EmploymentDetailMultiError, or nil if none found.
func (m *EmploymentDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *EmploymentDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyName

	// no validation rules for EmploymentType

	// no validation rules for IncomeRange

	// no validation rules for AbsoluteIncome

	// no validation rules for GmailInsightCriteriaForPassing

	if len(errors) > 0 {
		return EmploymentDetailMultiError(errors)
	}

	return nil
}

// EmploymentDetailMultiError is an error wrapping multiple validation errors
// returned by EmploymentDetail.ValidateAll() if the designated constraints
// aren't met.
type EmploymentDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmploymentDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmploymentDetailMultiError) AllErrors() []error { return m }

// EmploymentDetailValidationError is the validation error returned by
// EmploymentDetail.Validate if the designated constraints aren't met.
type EmploymentDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmploymentDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmploymentDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmploymentDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmploymentDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmploymentDetailValidationError) ErrorName() string { return "EmploymentDetailValidationError" }

// Error satisfies the builtin error interface
func (e EmploymentDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmploymentDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmploymentDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmploymentDetailValidationError{}

// Validate checks the field values on NameMatchDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NameMatchDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NameMatchDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NameMatchDetailMultiError, or nil if none found.
func (m *NameMatchDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *NameMatchDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PanName

	// no validation rules for KycName

	// no validation rules for EmailNamePanNameMatchScore

	// no validation rules for KycNamePanNameMatchScore

	if len(errors) > 0 {
		return NameMatchDetailMultiError(errors)
	}

	return nil
}

// NameMatchDetailMultiError is an error wrapping multiple validation errors
// returned by NameMatchDetail.ValidateAll() if the designated constraints
// aren't met.
type NameMatchDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NameMatchDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NameMatchDetailMultiError) AllErrors() []error { return m }

// NameMatchDetailValidationError is the validation error returned by
// NameMatchDetail.Validate if the designated constraints aren't met.
type NameMatchDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NameMatchDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NameMatchDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NameMatchDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NameMatchDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NameMatchDetailValidationError) ErrorName() string { return "NameMatchDetailValidationError" }

// Error satisfies the builtin error interface
func (e NameMatchDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNameMatchDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NameMatchDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NameMatchDetailValidationError{}

// Validate checks the field values on LivenessFacematchDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LivenessFacematchDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessFacematchDetail with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessFacematchDetailMultiError, or nil if none found.
func (m *LivenessFacematchDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessFacematchDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OnboardingLivenessImage

	// no validation rules for KycImage

	if len(errors) > 0 {
		return LivenessFacematchDetailMultiError(errors)
	}

	return nil
}

// LivenessFacematchDetailMultiError is an error wrapping multiple validation
// errors returned by LivenessFacematchDetail.ValidateAll() if the designated
// constraints aren't met.
type LivenessFacematchDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessFacematchDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessFacematchDetailMultiError) AllErrors() []error { return m }

// LivenessFacematchDetailValidationError is the validation error returned by
// LivenessFacematchDetail.Validate if the designated constraints aren't met.
type LivenessFacematchDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessFacematchDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessFacematchDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessFacematchDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessFacematchDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessFacematchDetailValidationError) ErrorName() string {
	return "LivenessFacematchDetailValidationError"
}

// Error satisfies the builtin error interface
func (e LivenessFacematchDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessFacematchDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessFacematchDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessFacematchDetailValidationError{}

// Validate checks the field values on RiskSummary with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RiskSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskSummary with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RiskSummaryMultiError, or
// nil if none found.
func (m *RiskSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RiskScore

	// no validation rules for AadharPhoneOnbMatches

	for idx, item := range m.GetRiskData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskSummaryValidationError{
						field:  fmt.Sprintf("RiskData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskSummaryValidationError{
						field:  fmt.Sprintf("RiskData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskSummaryValidationError{
					field:  fmt.Sprintf("RiskData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RiskSummaryMultiError(errors)
	}

	return nil
}

// RiskSummaryMultiError is an error wrapping multiple validation errors
// returned by RiskSummary.ValidateAll() if the designated constraints aren't met.
type RiskSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskSummaryMultiError) AllErrors() []error { return m }

// RiskSummaryValidationError is the validation error returned by
// RiskSummary.Validate if the designated constraints aren't met.
type RiskSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskSummaryValidationError) ErrorName() string { return "RiskSummaryValidationError" }

// Error satisfies the builtin error interface
func (e RiskSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskSummaryValidationError{}

// Validate checks the field values on RiskData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RiskData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RiskDataMultiError, or nil
// if none found.
func (m *RiskData) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RiskParam

	// no validation rules for Result

	// no validation rules for Score

	if len(errors) > 0 {
		return RiskDataMultiError(errors)
	}

	return nil
}

// RiskDataMultiError is an error wrapping multiple validation errors returned
// by RiskData.ValidateAll() if the designated constraints aren't met.
type RiskDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskDataMultiError) AllErrors() []error { return m }

// RiskDataValidationError is the validation error returned by
// RiskData.Validate if the designated constraints aren't met.
type RiskDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskDataValidationError) ErrorName() string { return "RiskDataValidationError" }

// Error satisfies the builtin error interface
func (e RiskDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskDataValidationError{}

// Validate checks the field values on DeviceDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeviceDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeviceDetailsMultiError, or
// nil if none found.
func (m *DeviceDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeviceModelAndManufacturer

	// no validation rules for DeviceLanguage

	// no validation rules for LatLng

	// no validation rules for LocationPincode

	// no validation rules for LocationCity

	// no validation rules for LocationState

	// no validation rules for MaliciousAppList

	if len(errors) > 0 {
		return DeviceDetailsMultiError(errors)
	}

	return nil
}

// DeviceDetailsMultiError is an error wrapping multiple validation errors
// returned by DeviceDetails.ValidateAll() if the designated constraints
// aren't met.
type DeviceDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceDetailsMultiError) AllErrors() []error { return m }

// DeviceDetailsValidationError is the validation error returned by
// DeviceDetails.Validate if the designated constraints aren't met.
type DeviceDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceDetailsValidationError) ErrorName() string { return "DeviceDetailsValidationError" }

// Error satisfies the builtin error interface
func (e DeviceDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceDetailsValidationError{}

// Validate checks the field values on AccountStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AccountStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AccountStatusMultiError, or
// nil if none found.
func (m *AccountStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInAppStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountStatusValidationError{
					field:  "InAppStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountStatusValidationError{
					field:  "InAppStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInAppStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountStatusValidationError{
				field:  "InAppStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccessRevokeDetailsHistory() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AccountStatusValidationError{
						field:  fmt.Sprintf("AccessRevokeDetailsHistory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AccountStatusValidationError{
						field:  fmt.Sprintf("AccessRevokeDetailsHistory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AccountStatusValidationError{
					field:  fmt.Sprintf("AccessRevokeDetailsHistory[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AccountStatusMultiError(errors)
	}

	return nil
}

// AccountStatusMultiError is an error wrapping multiple validation errors
// returned by AccountStatus.ValidateAll() if the designated constraints
// aren't met.
type AccountStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountStatusMultiError) AllErrors() []error { return m }

// AccountStatusValidationError is the validation error returned by
// AccountStatus.Validate if the designated constraints aren't met.
type AccountStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountStatusValidationError) ErrorName() string { return "AccountStatusValidationError" }

// Error satisfies the builtin error interface
func (e AccountStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountStatusValidationError{}

// Validate checks the field values on InAppStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InAppStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InAppStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in InAppStatusMultiError, or
// nil if none found.
func (m *InAppStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *InAppStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccessRevokeStatus

	// no validation rules for Reason

	// no validation rules for Remarks

	if len(errors) > 0 {
		return InAppStatusMultiError(errors)
	}

	return nil
}

// InAppStatusMultiError is an error wrapping multiple validation errors
// returned by InAppStatus.ValidateAll() if the designated constraints aren't met.
type InAppStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InAppStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InAppStatusMultiError) AllErrors() []error { return m }

// InAppStatusValidationError is the validation error returned by
// InAppStatus.Validate if the designated constraints aren't met.
type InAppStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InAppStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InAppStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InAppStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InAppStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InAppStatusValidationError) ErrorName() string { return "InAppStatusValidationError" }

// Error satisfies the builtin error interface
func (e InAppStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInAppStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InAppStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InAppStatusValidationError{}

// Validate checks the field values on GetL2DetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetL2DetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetL2DetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetL2DetailsRequestMultiError, or nil if none found.
func (m *GetL2DetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetL2DetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetL2DetailsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetL2DetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetL2DetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetL2DetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetL2DetailsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PayloadType

	// no validation rules for LivenessRequestId

	// no validation rules for FacematchRequestId

	if len(errors) > 0 {
		return GetL2DetailsRequestMultiError(errors)
	}

	return nil
}

// GetL2DetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetL2DetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetL2DetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetL2DetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetL2DetailsRequestMultiError) AllErrors() []error { return m }

// GetL2DetailsRequestValidationError is the validation error returned by
// GetL2DetailsRequest.Validate if the designated constraints aren't met.
type GetL2DetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetL2DetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetL2DetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetL2DetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetL2DetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetL2DetailsRequestValidationError) ErrorName() string {
	return "GetL2DetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetL2DetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetL2DetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetL2DetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetL2DetailsRequestValidationError{}

// Validate checks the field values on GetL2DetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetL2DetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetL2DetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetL2DetailsResponseMultiError, or nil if none found.
func (m *GetL2DetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetL2DetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetL2DetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetL2DetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetL2DetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccessRevokeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetL2DetailsResponseValidationError{
					field:  "AccessRevokeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetL2DetailsResponseValidationError{
					field:  "AccessRevokeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccessRevokeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetL2DetailsResponseValidationError{
				field:  "AccessRevokeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLivenessAnnotation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetL2DetailsResponseValidationError{
					field:  "LivenessAnnotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetL2DetailsResponseValidationError{
					field:  "LivenessAnnotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLivenessAnnotation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetL2DetailsResponseValidationError{
				field:  "LivenessAnnotation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFacematchAnnotation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetL2DetailsResponseValidationError{
					field:  "FacematchAnnotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetL2DetailsResponseValidationError{
					field:  "FacematchAnnotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFacematchAnnotation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetL2DetailsResponseValidationError{
				field:  "FacematchAnnotation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetL2DetailsResponseMultiError(errors)
	}

	return nil
}

// GetL2DetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetL2DetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetL2DetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetL2DetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetL2DetailsResponseMultiError) AllErrors() []error { return m }

// GetL2DetailsResponseValidationError is the validation error returned by
// GetL2DetailsResponse.Validate if the designated constraints aren't met.
type GetL2DetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetL2DetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetL2DetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetL2DetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetL2DetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetL2DetailsResponseValidationError) ErrorName() string {
	return "GetL2DetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetL2DetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetL2DetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetL2DetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetL2DetailsResponseValidationError{}

// Validate checks the field values on ConnectedAccountInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConnectedAccountInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConnectedAccountInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConnectedAccountInfoMultiError, or nil if none found.
func (m *ConnectedAccountInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ConnectedAccountInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectedAccountInfoValidationError{
					field:  "AccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectedAccountInfoValidationError{
					field:  "AccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectedAccountInfoValidationError{
				field:  "AccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProfileDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectedAccountInfoValidationError{
					field:  "ProfileDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectedAccountInfoValidationError{
					field:  "ProfileDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProfileDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectedAccountInfoValidationError{
				field:  "ProfileDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConnectedAccountInfoMultiError(errors)
	}

	return nil
}

// ConnectedAccountInfoMultiError is an error wrapping multiple validation
// errors returned by ConnectedAccountInfo.ValidateAll() if the designated
// constraints aren't met.
type ConnectedAccountInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConnectedAccountInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConnectedAccountInfoMultiError) AllErrors() []error { return m }

// ConnectedAccountInfoValidationError is the validation error returned by
// ConnectedAccountInfo.Validate if the designated constraints aren't met.
type ConnectedAccountInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConnectedAccountInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConnectedAccountInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConnectedAccountInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConnectedAccountInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConnectedAccountInfoValidationError) ErrorName() string {
	return "ConnectedAccountInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ConnectedAccountInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConnectedAccountInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConnectedAccountInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConnectedAccountInfoValidationError{}

// Validate checks the field values on ReviewElement with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReviewElement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewElement with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReviewElementMultiError, or
// nil if none found.
func (m *ReviewElement) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewElement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Payload.(type) {
	case *ReviewElement_LivenessReview:
		if v == nil {
			err := ReviewElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLivenessReview()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReviewElementValidationError{
						field:  "LivenessReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReviewElementValidationError{
						field:  "LivenessReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLivenessReview()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReviewElementValidationError{
					field:  "LivenessReview",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ReviewElementMultiError(errors)
	}

	return nil
}

// ReviewElementMultiError is an error wrapping multiple validation errors
// returned by ReviewElement.ValidateAll() if the designated constraints
// aren't met.
type ReviewElementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewElementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewElementMultiError) AllErrors() []error { return m }

// ReviewElementValidationError is the validation error returned by
// ReviewElement.Validate if the designated constraints aren't met.
type ReviewElementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewElementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewElementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewElementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewElementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewElementValidationError) ErrorName() string { return "ReviewElementValidationError" }

// Error satisfies the builtin error interface
func (e ReviewElementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewElement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewElementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewElementValidationError{}

// Validate checks the field values on GetReviewElementsByActorIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetReviewElementsByActorIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReviewElementsByActorIdRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetReviewElementsByActorIdRequestMultiError, or nil if none found.
func (m *GetReviewElementsByActorIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReviewElementsByActorIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetReviewElementsByActorIdRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReviewElementsByActorIdRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReviewElementsByActorIdRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReviewElementsByActorIdRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PayloadType

	// no validation rules for Limit

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetReviewElementsByActorIdRequestMultiError(errors)
	}

	return nil
}

// GetReviewElementsByActorIdRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetReviewElementsByActorIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetReviewElementsByActorIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReviewElementsByActorIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReviewElementsByActorIdRequestMultiError) AllErrors() []error { return m }

// GetReviewElementsByActorIdRequestValidationError is the validation error
// returned by GetReviewElementsByActorIdRequest.Validate if the designated
// constraints aren't met.
type GetReviewElementsByActorIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReviewElementsByActorIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReviewElementsByActorIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReviewElementsByActorIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReviewElementsByActorIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReviewElementsByActorIdRequestValidationError) ErrorName() string {
	return "GetReviewElementsByActorIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetReviewElementsByActorIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReviewElementsByActorIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReviewElementsByActorIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReviewElementsByActorIdRequestValidationError{}

// Validate checks the field values on GetReviewElementsByActorIdResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetReviewElementsByActorIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReviewElementsByActorIdResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetReviewElementsByActorIdResponseMultiError, or nil if none found.
func (m *GetReviewElementsByActorIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReviewElementsByActorIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReviewElementsByActorIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReviewElementsByActorIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReviewElementsByActorIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetReviewElements() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetReviewElementsByActorIdResponseValidationError{
						field:  fmt.Sprintf("ReviewElements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetReviewElementsByActorIdResponseValidationError{
						field:  fmt.Sprintf("ReviewElements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetReviewElementsByActorIdResponseValidationError{
					field:  fmt.Sprintf("ReviewElements[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetReviewElementsByActorIdResponseMultiError(errors)
	}

	return nil
}

// GetReviewElementsByActorIdResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetReviewElementsByActorIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetReviewElementsByActorIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReviewElementsByActorIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReviewElementsByActorIdResponseMultiError) AllErrors() []error { return m }

// GetReviewElementsByActorIdResponseValidationError is the validation error
// returned by GetReviewElementsByActorIdResponse.Validate if the designated
// constraints aren't met.
type GetReviewElementsByActorIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReviewElementsByActorIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReviewElementsByActorIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReviewElementsByActorIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReviewElementsByActorIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReviewElementsByActorIdResponseValidationError) ErrorName() string {
	return "GetReviewElementsByActorIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetReviewElementsByActorIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReviewElementsByActorIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReviewElementsByActorIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReviewElementsByActorIdResponseValidationError{}

// Validate checks the field values on ReReviewRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReReviewRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReReviewRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReReviewRequestMultiError, or nil if none found.
func (m *ReReviewRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReReviewRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := ReReviewRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReReviewRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReReviewRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReReviewRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetElementId()) < 1 {
		err := ReReviewRequestValidationError{
			field:  "ElementId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := ReReviewRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for LivenessRequestId

	// no validation rules for FacematchRequestId

	// no validation rules for ReReviewLivenessVerdict

	// no validation rules for ReReviewFacematchVerdict

	// no validation rules for ReReviewedBy

	// no validation rules for ReReviewRemarks

	// no validation rules for PayloadType

	// no validation rules for ReReviewErrorType

	if len(errors) > 0 {
		return ReReviewRequestMultiError(errors)
	}

	return nil
}

// ReReviewRequestMultiError is an error wrapping multiple validation errors
// returned by ReReviewRequest.ValidateAll() if the designated constraints
// aren't met.
type ReReviewRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReReviewRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReReviewRequestMultiError) AllErrors() []error { return m }

// ReReviewRequestValidationError is the validation error returned by
// ReReviewRequest.Validate if the designated constraints aren't met.
type ReReviewRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReReviewRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReReviewRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReReviewRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReReviewRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReReviewRequestValidationError) ErrorName() string { return "ReReviewRequestValidationError" }

// Error satisfies the builtin error interface
func (e ReReviewRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReReviewRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReReviewRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReReviewRequestValidationError{}

// Validate checks the field values on ReReviewResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReReviewResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReReviewResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReReviewResponseMultiError, or nil if none found.
func (m *ReReviewResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReReviewResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReReviewResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReReviewResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReReviewResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReReviewResponseMultiError(errors)
	}

	return nil
}

// ReReviewResponseMultiError is an error wrapping multiple validation errors
// returned by ReReviewResponse.ValidateAll() if the designated constraints
// aren't met.
type ReReviewResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReReviewResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReReviewResponseMultiError) AllErrors() []error { return m }

// ReReviewResponseValidationError is the validation error returned by
// ReReviewResponse.Validate if the designated constraints aren't met.
type ReReviewResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReReviewResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReReviewResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReReviewResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReReviewResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReReviewResponseValidationError) ErrorName() string { return "ReReviewResponseValidationError" }

// Error satisfies the builtin error interface
func (e ReReviewResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReReviewResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReReviewResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReReviewResponseValidationError{}

// Validate checks the field values on GetUserRiskDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserRiskDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserRiskDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserRiskDataRequestMultiError, or nil if none found.
func (m *GetUserRiskDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserRiskDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetUserRiskDataRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for CaseId

	if len(errors) > 0 {
		return GetUserRiskDataRequestMultiError(errors)
	}

	return nil
}

// GetUserRiskDataRequestMultiError is an error wrapping multiple validation
// errors returned by GetUserRiskDataRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUserRiskDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserRiskDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserRiskDataRequestMultiError) AllErrors() []error { return m }

// GetUserRiskDataRequestValidationError is the validation error returned by
// GetUserRiskDataRequest.Validate if the designated constraints aren't met.
type GetUserRiskDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserRiskDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserRiskDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserRiskDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserRiskDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserRiskDataRequestValidationError) ErrorName() string {
	return "GetUserRiskDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserRiskDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserRiskDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserRiskDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserRiskDataRequestValidationError{}

// Validate checks the field values on GetUserRiskDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserRiskDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserRiskDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserRiskDataResponseMultiError, or nil if none found.
func (m *GetUserRiskDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserRiskDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBasicDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "BasicDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "BasicDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBasicDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "BasicDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRiskSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "RiskSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "RiskSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRiskSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "RiskSummary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetKYCDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "KYCDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "KYCDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKYCDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "KYCDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLivenessDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "LivenessDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "LivenessDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLivenessDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "LivenessDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFacematchDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "FacematchDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "FacematchDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFacematchDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "FacematchDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReviewReason()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "ReviewReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "ReviewReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReviewReason()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "ReviewReason",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeviceDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "DeviceDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmploymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "EmploymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "EmploymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmploymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "EmploymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddressDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "AddressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "AddressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddressDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "AddressDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRiskActionHistory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "RiskActionHistory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "RiskActionHistory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRiskActionHistory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "RiskActionHistory",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLivenessAttemptsHistory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "LivenessAttemptsHistory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "LivenessAttemptsHistory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLivenessAttemptsHistory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "LivenessAttemptsHistory",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFacematchAttemptsHistory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "FacematchAttemptsHistory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "FacematchAttemptsHistory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFacematchAttemptsHistory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "FacematchAttemptsHistory",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOutcallInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "OutcallInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "OutcallInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOutcallInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "OutcallInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExistingProduct()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "ExistingProduct",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "ExistingProduct",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExistingProduct()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "ExistingProduct",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRiskScreenerChecks()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "RiskScreenerChecks",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "RiskScreenerChecks",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRiskScreenerChecks()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "RiskScreenerChecks",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSocialMediaDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "SocialMediaDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "SocialMediaDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSocialMediaDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "SocialMediaDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContactAssociations()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "ContactAssociations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "ContactAssociations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContactAssociations()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "ContactAssociations",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReferralAssociations()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "ReferralAssociations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "ReferralAssociations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReferralAssociations()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "ReferralAssociations",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInstalledAppsDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "InstalledAppsDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "InstalledAppsDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInstalledAppsDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "InstalledAppsDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWatchlistDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "WatchlistDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "WatchlistDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWatchlistDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "WatchlistDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLeaHistory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "LeaHistory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRiskDataResponseValidationError{
					field:  "LeaHistory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeaHistory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRiskDataResponseValidationError{
				field:  "LeaHistory",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserRiskDataResponseMultiError(errors)
	}

	return nil
}

// GetUserRiskDataResponseMultiError is an error wrapping multiple validation
// errors returned by GetUserRiskDataResponse.ValidateAll() if the designated
// constraints aren't met.
type GetUserRiskDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserRiskDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserRiskDataResponseMultiError) AllErrors() []error { return m }

// GetUserRiskDataResponseValidationError is the validation error returned by
// GetUserRiskDataResponse.Validate if the designated constraints aren't met.
type GetUserRiskDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserRiskDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserRiskDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserRiskDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserRiskDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserRiskDataResponseValidationError) ErrorName() string {
	return "GetUserRiskDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserRiskDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserRiskDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserRiskDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserRiskDataResponseValidationError{}

// Validate checks the field values on GetAFUDataRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetAFUDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAFUDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAFUDataRequestMultiError, or nil if none found.
func (m *GetAFUDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAFUDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetAFUDataRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAFUDataRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAFUDataRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAFUDataRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Identifier.(type) {
	case *GetAFUDataRequest_CaseId:
		if v == nil {
			err := GetAFUDataRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if utf8.RuneCountInString(m.GetCaseId()) < 1 {
			err := GetAFUDataRequestValidationError{
				field:  "CaseId",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *GetAFUDataRequest_ActorId:
		if v == nil {
			err := GetAFUDataRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if utf8.RuneCountInString(m.GetActorId()) < 1 {
			err := GetAFUDataRequestValidationError{
				field:  "ActorId",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetAFUDataRequestMultiError(errors)
	}

	return nil
}

// GetAFUDataRequestMultiError is an error wrapping multiple validation errors
// returned by GetAFUDataRequest.ValidateAll() if the designated constraints
// aren't met.
type GetAFUDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAFUDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAFUDataRequestMultiError) AllErrors() []error { return m }

// GetAFUDataRequestValidationError is the validation error returned by
// GetAFUDataRequest.Validate if the designated constraints aren't met.
type GetAFUDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAFUDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAFUDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAFUDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAFUDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAFUDataRequestValidationError) ErrorName() string {
	return "GetAFUDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAFUDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAFUDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAFUDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAFUDataRequestValidationError{}

// Validate checks the field values on GetAFUDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAFUDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAFUDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAFUDataResponseMultiError, or nil if none found.
func (m *GetAFUDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAFUDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAFUDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAFUDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAFUDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetReviewSections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAFUDataResponseValidationError{
						field:  fmt.Sprintf("ReviewSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAFUDataResponseValidationError{
						field:  fmt.Sprintf("ReviewSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAFUDataResponseValidationError{
					field:  fmt.Sprintf("ReviewSections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAFUDataResponseMultiError(errors)
	}

	return nil
}

// GetAFUDataResponseMultiError is an error wrapping multiple validation errors
// returned by GetAFUDataResponse.ValidateAll() if the designated constraints
// aren't met.
type GetAFUDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAFUDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAFUDataResponseMultiError) AllErrors() []error { return m }

// GetAFUDataResponseValidationError is the validation error returned by
// GetAFUDataResponse.Validate if the designated constraints aren't met.
type GetAFUDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAFUDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAFUDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAFUDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAFUDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAFUDataResponseValidationError) ErrorName() string {
	return "GetAFUDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAFUDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAFUDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAFUDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAFUDataResponseValidationError{}

// Validate checks the field values on GetTransactionReviewDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTransactionReviewDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionReviewDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetTransactionReviewDetailsRequestMultiError, or nil if none found.
func (m *GetTransactionReviewDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionReviewDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetTransactionReviewDetailsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionReviewDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionReviewDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionReviewDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetTransactionReviewDetailsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFilters() == nil {
		err := GetTransactionReviewDetailsRequestValidationError{
			field:  "Filters",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionReviewDetailsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionReviewDetailsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionReviewDetailsRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CaseId

	if len(errors) > 0 {
		return GetTransactionReviewDetailsRequestMultiError(errors)
	}

	return nil
}

// GetTransactionReviewDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetTransactionReviewDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionReviewDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionReviewDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionReviewDetailsRequestMultiError) AllErrors() []error { return m }

// GetTransactionReviewDetailsRequestValidationError is the validation error
// returned by GetTransactionReviewDetailsRequest.Validate if the designated
// constraints aren't met.
type GetTransactionReviewDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionReviewDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionReviewDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionReviewDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionReviewDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionReviewDetailsRequestValidationError) ErrorName() string {
	return "GetTransactionReviewDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionReviewDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionReviewDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionReviewDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionReviewDetailsRequestValidationError{}

// Validate checks the field values on GetTransactionReviewDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTransactionReviewDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionReviewDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetTransactionReviewDetailsResponseMultiError, or nil if none found.
func (m *GetTransactionReviewDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionReviewDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionReviewDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionReviewDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionReviewDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTransactionReviewDetailsResponseValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTransactionReviewDetailsResponseValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTransactionReviewDetailsResponseValidationError{
					field:  fmt.Sprintf("Transactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTransactionReviewDetailsResponseMultiError(errors)
	}

	return nil
}

// GetTransactionReviewDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetTransactionReviewDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionReviewDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionReviewDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionReviewDetailsResponseMultiError) AllErrors() []error { return m }

// GetTransactionReviewDetailsResponseValidationError is the validation error
// returned by GetTransactionReviewDetailsResponse.Validate if the designated
// constraints aren't met.
type GetTransactionReviewDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionReviewDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionReviewDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionReviewDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionReviewDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionReviewDetailsResponseValidationError) ErrorName() string {
	return "GetTransactionReviewDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionReviewDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionReviewDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionReviewDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionReviewDetailsResponseValidationError{}

// Validate checks the field values on GetScreenerCheckDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetScreenerCheckDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScreenerCheckDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetScreenerCheckDetailsRequestMultiError, or nil if none found.
func (m *GetScreenerCheckDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScreenerCheckDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetScreenerCheckDetailsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetScreenerCheckDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetScreenerCheckDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetScreenerCheckDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ResultId

	if len(errors) > 0 {
		return GetScreenerCheckDetailsRequestMultiError(errors)
	}

	return nil
}

// GetScreenerCheckDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by GetScreenerCheckDetailsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetScreenerCheckDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScreenerCheckDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScreenerCheckDetailsRequestMultiError) AllErrors() []error { return m }

// GetScreenerCheckDetailsRequestValidationError is the validation error
// returned by GetScreenerCheckDetailsRequest.Validate if the designated
// constraints aren't met.
type GetScreenerCheckDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScreenerCheckDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScreenerCheckDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScreenerCheckDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScreenerCheckDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScreenerCheckDetailsRequestValidationError) ErrorName() string {
	return "GetScreenerCheckDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetScreenerCheckDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScreenerCheckDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScreenerCheckDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScreenerCheckDetailsRequestValidationError{}

// Validate checks the field values on GetScreenerCheckDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetScreenerCheckDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScreenerCheckDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetScreenerCheckDetailsResponseMultiError, or nil if none found.
func (m *GetScreenerCheckDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScreenerCheckDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetScreenerCheckDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetScreenerCheckDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetScreenerCheckDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAdditionalCheckDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetScreenerCheckDetailsResponseValidationError{
					field:  "AdditionalCheckDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetScreenerCheckDetailsResponseValidationError{
					field:  "AdditionalCheckDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalCheckDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetScreenerCheckDetailsResponseValidationError{
				field:  "AdditionalCheckDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetScreenerCheckDetailsResponseMultiError(errors)
	}

	return nil
}

// GetScreenerCheckDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by GetScreenerCheckDetailsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetScreenerCheckDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScreenerCheckDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScreenerCheckDetailsResponseMultiError) AllErrors() []error { return m }

// GetScreenerCheckDetailsResponseValidationError is the validation error
// returned by GetScreenerCheckDetailsResponse.Validate if the designated
// constraints aren't met.
type GetScreenerCheckDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScreenerCheckDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScreenerCheckDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScreenerCheckDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScreenerCheckDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScreenerCheckDetailsResponseValidationError) ErrorName() string {
	return "GetScreenerCheckDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetScreenerCheckDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScreenerCheckDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScreenerCheckDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScreenerCheckDetailsResponseValidationError{}

// Validate checks the field values on ListFormsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListFormsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFormsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListFormsRequestMultiError, or nil if none found.
func (m *ListFormsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFormsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := ListFormsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFormsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFormsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFormsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetFormFilters() == nil {
		err := ListFormsRequestValidationError{
			field:  "FormFilters",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFormFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFormsRequestValidationError{
					field:  "FormFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFormsRequestValidationError{
					field:  "FormFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFormFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFormsRequestValidationError{
				field:  "FormFilters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListFormsRequestMultiError(errors)
	}

	return nil
}

// ListFormsRequestMultiError is an error wrapping multiple validation errors
// returned by ListFormsRequest.ValidateAll() if the designated constraints
// aren't met.
type ListFormsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFormsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFormsRequestMultiError) AllErrors() []error { return m }

// ListFormsRequestValidationError is the validation error returned by
// ListFormsRequest.Validate if the designated constraints aren't met.
type ListFormsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFormsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFormsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFormsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFormsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFormsRequestValidationError) ErrorName() string { return "ListFormsRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListFormsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFormsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFormsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFormsRequestValidationError{}

// Validate checks the field values on ListFormsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListFormsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFormsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListFormsResponseMultiError, or nil if none found.
func (m *ListFormsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFormsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFormsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFormsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFormsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetForms() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListFormsResponseValidationError{
						field:  fmt.Sprintf("Forms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListFormsResponseValidationError{
						field:  fmt.Sprintf("Forms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListFormsResponseValidationError{
					field:  fmt.Sprintf("Forms[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListFormsResponseMultiError(errors)
	}

	return nil
}

// ListFormsResponseMultiError is an error wrapping multiple validation errors
// returned by ListFormsResponse.ValidateAll() if the designated constraints
// aren't met.
type ListFormsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFormsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFormsResponseMultiError) AllErrors() []error { return m }

// ListFormsResponseValidationError is the validation error returned by
// ListFormsResponse.Validate if the designated constraints aren't met.
type ListFormsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFormsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFormsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFormsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFormsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFormsResponseValidationError) ErrorName() string {
	return "ListFormsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListFormsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFormsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFormsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFormsResponseValidationError{}

// Validate checks the field values on UploadManuallyReviewedDocumentsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UploadManuallyReviewedDocumentsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UploadManuallyReviewedDocumentsRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UploadManuallyReviewedDocumentsRequestMultiError, or nil if none found.
func (m *UploadManuallyReviewedDocumentsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadManuallyReviewedDocumentsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := UploadManuallyReviewedDocumentsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadManuallyReviewedDocumentsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadManuallyReviewedDocumentsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadManuallyReviewedDocumentsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicationId

	// no validation rules for CkycReferenceId

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UploadManuallyReviewedDocumentsRequestValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UploadManuallyReviewedDocumentsRequestValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UploadManuallyReviewedDocumentsRequestValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UploadManuallyReviewedDocumentsRequestMultiError(errors)
	}

	return nil
}

// UploadManuallyReviewedDocumentsRequestMultiError is an error wrapping
// multiple validation errors returned by
// UploadManuallyReviewedDocumentsRequest.ValidateAll() if the designated
// constraints aren't met.
type UploadManuallyReviewedDocumentsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadManuallyReviewedDocumentsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadManuallyReviewedDocumentsRequestMultiError) AllErrors() []error { return m }

// UploadManuallyReviewedDocumentsRequestValidationError is the validation
// error returned by UploadManuallyReviewedDocumentsRequest.Validate if the
// designated constraints aren't met.
type UploadManuallyReviewedDocumentsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadManuallyReviewedDocumentsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadManuallyReviewedDocumentsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadManuallyReviewedDocumentsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadManuallyReviewedDocumentsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadManuallyReviewedDocumentsRequestValidationError) ErrorName() string {
	return "UploadManuallyReviewedDocumentsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadManuallyReviewedDocumentsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadManuallyReviewedDocumentsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadManuallyReviewedDocumentsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadManuallyReviewedDocumentsRequestValidationError{}

// Validate checks the field values on UploadManuallyReviewedDocumentsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UploadManuallyReviewedDocumentsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UploadManuallyReviewedDocumentsResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UploadManuallyReviewedDocumentsResponseMultiError, or nil if none found.
func (m *UploadManuallyReviewedDocumentsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadManuallyReviewedDocumentsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadManuallyReviewedDocumentsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadManuallyReviewedDocumentsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadManuallyReviewedDocumentsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UploadManuallyReviewedDocumentsResponseMultiError(errors)
	}

	return nil
}

// UploadManuallyReviewedDocumentsResponseMultiError is an error wrapping
// multiple validation errors returned by
// UploadManuallyReviewedDocumentsResponse.ValidateAll() if the designated
// constraints aren't met.
type UploadManuallyReviewedDocumentsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadManuallyReviewedDocumentsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadManuallyReviewedDocumentsResponseMultiError) AllErrors() []error { return m }

// UploadManuallyReviewedDocumentsResponseValidationError is the validation
// error returned by UploadManuallyReviewedDocumentsResponse.Validate if the
// designated constraints aren't met.
type UploadManuallyReviewedDocumentsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadManuallyReviewedDocumentsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadManuallyReviewedDocumentsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadManuallyReviewedDocumentsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadManuallyReviewedDocumentsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadManuallyReviewedDocumentsResponseValidationError) ErrorName() string {
	return "UploadManuallyReviewedDocumentsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadManuallyReviewedDocumentsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadManuallyReviewedDocumentsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadManuallyReviewedDocumentsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadManuallyReviewedDocumentsResponseValidationError{}

// Validate checks the field values on TransactionBlock with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TransactionBlock) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactionBlock with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactionBlockMultiError, or nil if none found.
func (m *TransactionBlock) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactionBlock) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for AlertId

	// no validation rules for AggregatedCredit

	// no validation rules for AggregatedDebit

	if all {
		switch v := interface{}(m.GetDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionBlockValidationError{
					field:  "Duration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionBlockValidationError{
					field:  "Duration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionBlockValidationError{
				field:  "Duration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BlockType

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionBlockValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionBlockValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionBlockValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TransactionBlockMultiError(errors)
	}

	return nil
}

// TransactionBlockMultiError is an error wrapping multiple validation errors
// returned by TransactionBlock.ValidateAll() if the designated constraints
// aren't met.
type TransactionBlockMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactionBlockMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactionBlockMultiError) AllErrors() []error { return m }

// TransactionBlockValidationError is the validation error returned by
// TransactionBlock.Validate if the designated constraints aren't met.
type TransactionBlockValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactionBlockValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactionBlockValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactionBlockValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactionBlockValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactionBlockValidationError) ErrorName() string { return "TransactionBlockValidationError" }

// Error satisfies the builtin error interface
func (e TransactionBlockValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactionBlock.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactionBlockValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactionBlockValidationError{}

// Validate checks the field values on GetTransactionBlocksRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionBlocksRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionBlocksRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTransactionBlocksRequestMultiError, or nil if none found.
func (m *GetTransactionBlocksRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionBlocksRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionBlocksRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionBlocksRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionBlocksRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetLimit() < 0 {
		err := GetTransactionBlocksRequestValidationError{
			field:  "Limit",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for BlockType

	switch v := m.Identifier.(type) {
	case *GetTransactionBlocksRequest_AlertId:
		if v == nil {
			err := GetTransactionBlocksRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if utf8.RuneCountInString(m.GetAlertId()) < 1 {
			err := GetTransactionBlocksRequestValidationError{
				field:  "AlertId",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *GetTransactionBlocksRequest_ActorId:
		if v == nil {
			err := GetTransactionBlocksRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if utf8.RuneCountInString(m.GetActorId()) < 1 {
			err := GetTransactionBlocksRequestValidationError{
				field:  "ActorId",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetTransactionBlocksRequestMultiError(errors)
	}

	return nil
}

// GetTransactionBlocksRequestMultiError is an error wrapping multiple
// validation errors returned by GetTransactionBlocksRequest.ValidateAll() if
// the designated constraints aren't met.
type GetTransactionBlocksRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionBlocksRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionBlocksRequestMultiError) AllErrors() []error { return m }

// GetTransactionBlocksRequestValidationError is the validation error returned
// by GetTransactionBlocksRequest.Validate if the designated constraints
// aren't met.
type GetTransactionBlocksRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionBlocksRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionBlocksRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionBlocksRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionBlocksRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionBlocksRequestValidationError) ErrorName() string {
	return "GetTransactionBlocksRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionBlocksRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionBlocksRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionBlocksRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionBlocksRequestValidationError{}

// Validate checks the field values on GetTransactionBlocksResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionBlocksResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionBlocksResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTransactionBlocksResponseMultiError, or nil if none found.
func (m *GetTransactionBlocksResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionBlocksResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionBlocksResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionBlocksResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionBlocksResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTransactionBlocks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTransactionBlocksResponseValidationError{
						field:  fmt.Sprintf("TransactionBlocks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTransactionBlocksResponseValidationError{
						field:  fmt.Sprintf("TransactionBlocks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTransactionBlocksResponseValidationError{
					field:  fmt.Sprintf("TransactionBlocks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTransactionBlocksResponseMultiError(errors)
	}

	return nil
}

// GetTransactionBlocksResponseMultiError is an error wrapping multiple
// validation errors returned by GetTransactionBlocksResponse.ValidateAll() if
// the designated constraints aren't met.
type GetTransactionBlocksResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionBlocksResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionBlocksResponseMultiError) AllErrors() []error { return m }

// GetTransactionBlocksResponseValidationError is the validation error returned
// by GetTransactionBlocksResponse.Validate if the designated constraints
// aren't met.
type GetTransactionBlocksResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionBlocksResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionBlocksResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionBlocksResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionBlocksResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionBlocksResponseValidationError) ErrorName() string {
	return "GetTransactionBlocksResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionBlocksResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionBlocksResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionBlocksResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionBlocksResponseValidationError{}

// Validate checks the field values on
// UploadManuallyReviewedDocumentsRequest_FileData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UploadManuallyReviewedDocumentsRequest_FileData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UploadManuallyReviewedDocumentsRequest_FileData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// UploadManuallyReviewedDocumentsRequest_FileDataMultiError, or nil if none found.
func (m *UploadManuallyReviewedDocumentsRequest_FileData) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadManuallyReviewedDocumentsRequest_FileData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadManuallyReviewedDocumentsRequest_FileDataValidationError{
					field:  "File",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadManuallyReviewedDocumentsRequest_FileDataValidationError{
					field:  "File",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadManuallyReviewedDocumentsRequest_FileDataValidationError{
				field:  "File",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsRedacted

	if len(errors) > 0 {
		return UploadManuallyReviewedDocumentsRequest_FileDataMultiError(errors)
	}

	return nil
}

// UploadManuallyReviewedDocumentsRequest_FileDataMultiError is an error
// wrapping multiple validation errors returned by
// UploadManuallyReviewedDocumentsRequest_FileData.ValidateAll() if the
// designated constraints aren't met.
type UploadManuallyReviewedDocumentsRequest_FileDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadManuallyReviewedDocumentsRequest_FileDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadManuallyReviewedDocumentsRequest_FileDataMultiError) AllErrors() []error { return m }

// UploadManuallyReviewedDocumentsRequest_FileDataValidationError is the
// validation error returned by
// UploadManuallyReviewedDocumentsRequest_FileData.Validate if the designated
// constraints aren't met.
type UploadManuallyReviewedDocumentsRequest_FileDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadManuallyReviewedDocumentsRequest_FileDataValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e UploadManuallyReviewedDocumentsRequest_FileDataValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e UploadManuallyReviewedDocumentsRequest_FileDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadManuallyReviewedDocumentsRequest_FileDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadManuallyReviewedDocumentsRequest_FileDataValidationError) ErrorName() string {
	return "UploadManuallyReviewedDocumentsRequest_FileDataValidationError"
}

// Error satisfies the builtin error interface
func (e UploadManuallyReviewedDocumentsRequest_FileDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadManuallyReviewedDocumentsRequest_FileData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadManuallyReviewedDocumentsRequest_FileDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadManuallyReviewedDocumentsRequest_FileDataValidationError{}
