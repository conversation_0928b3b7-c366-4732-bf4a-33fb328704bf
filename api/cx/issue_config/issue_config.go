package issue_config

import alPb "github.com/epifi/gamma/api/cx/audit_log"

func (r *GetIssueConfigFilterOptionsRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}
func (r *GetIssueConfigFilterOptionsRequest) GetObject() alPb.Object {
	return alPb.Object_ISSUE_CONFIG
}

func (r *GetIssueConfigHistoryRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}
func (r *GetIssueConfigHistoryRequest) GetObject() alPb.Object {
	return alPb.Object_ISSUE_CONFIG
}

func (r *UpdateIssueConfigRequest) GetAction() alPb.Action {
	return alPb.Action_UPDATE
}
func (r *UpdateIssueConfigRequest) GetObject() alPb.Object {
	return alPb.Object_ISSUE_CONFIG
}

func (r *GetIssueConfigRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH_BULK
}
func (r *GetIssueConfigRequest) GetObject() alPb.Object {
	return alPb.Object_ISSUE_CONFIG
}
