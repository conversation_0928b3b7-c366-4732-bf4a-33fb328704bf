// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/call_ivr/service.proto

package call_ivr

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	_ "github.com/epifi/gamma/api/cx"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InitiateIvrRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// monitor ucid of the call for which IVR is to initiated
	MonitorUcid string `protobuf:"bytes,1,opt,name=monitor_ucid,json=monitorUcid,proto3" json:"monitor_ucid,omitempty"`
	// phone number of the user for which IVR is to be initiated
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *InitiateIvrRequest) Reset() {
	*x = InitiateIvrRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_call_ivr_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateIvrRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateIvrRequest) ProtoMessage() {}

func (x *InitiateIvrRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_call_ivr_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateIvrRequest.ProtoReflect.Descriptor instead.
func (*InitiateIvrRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_call_ivr_service_proto_rawDescGZIP(), []int{0}
}

func (x *InitiateIvrRequest) GetMonitorUcid() string {
	if x != nil {
		return x.MonitorUcid
	}
	return ""
}

func (x *InitiateIvrRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

type InitiateIvrResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// info regarding the current IVR question like question id, IVR type, IVR state etc
	IvrStateInfo *IvrStateInfo `protobuf:"bytes,2,opt,name=ivr_state_info,json=ivrStateInfo,proto3" json:"ivr_state_info,omitempty"`
}

func (x *InitiateIvrResponse) Reset() {
	*x = InitiateIvrResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_call_ivr_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateIvrResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateIvrResponse) ProtoMessage() {}

func (x *InitiateIvrResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_call_ivr_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateIvrResponse.ProtoReflect.Descriptor instead.
func (*InitiateIvrResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_call_ivr_service_proto_rawDescGZIP(), []int{1}
}

func (x *InitiateIvrResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *InitiateIvrResponse) GetIvrStateInfo() *IvrStateInfo {
	if x != nil {
		return x.IvrStateInfo
	}
	return nil
}

type ProcessIvrUserInputRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// phone number of the user that provided IVR input
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// monitor ucid of the call for which response was provided
	MonitorUcid string `protobuf:"bytes,2,opt,name=monitor_ucid,json=monitorUcid,proto3" json:"monitor_ucid,omitempty"`
	// IVR type for which response was provided
	CurrentIvrType IvrType `protobuf:"varint,3,opt,name=current_ivr_type,json=currentIvrType,proto3,enum=cx.call_ivr.IvrType" json:"current_ivr_type,omitempty"`
	// IVR question id for which response was provided
	CurrentIvrQuestionId int32 `protobuf:"varint,4,opt,name=current_ivr_question_id,json=currentIvrQuestionId,proto3" json:"current_ivr_question_id,omitempty"`
	// Response provided by the user
	CurrentIvrQuestionResponse string `protobuf:"bytes,5,opt,name=current_ivr_question_response,json=currentIvrQuestionResponse,proto3" json:"current_ivr_question_response,omitempty"`
}

func (x *ProcessIvrUserInputRequest) Reset() {
	*x = ProcessIvrUserInputRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_call_ivr_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessIvrUserInputRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessIvrUserInputRequest) ProtoMessage() {}

func (x *ProcessIvrUserInputRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_call_ivr_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessIvrUserInputRequest.ProtoReflect.Descriptor instead.
func (*ProcessIvrUserInputRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_call_ivr_service_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessIvrUserInputRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *ProcessIvrUserInputRequest) GetMonitorUcid() string {
	if x != nil {
		return x.MonitorUcid
	}
	return ""
}

func (x *ProcessIvrUserInputRequest) GetCurrentIvrType() IvrType {
	if x != nil {
		return x.CurrentIvrType
	}
	return IvrType_IVR_TYPE_UNSPECIFIED
}

func (x *ProcessIvrUserInputRequest) GetCurrentIvrQuestionId() int32 {
	if x != nil {
		return x.CurrentIvrQuestionId
	}
	return 0
}

func (x *ProcessIvrUserInputRequest) GetCurrentIvrQuestionResponse() string {
	if x != nil {
		return x.CurrentIvrQuestionResponse
	}
	return ""
}

type ProcessIvrUserInputResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// info regarding the current IVR question like question id, IVR type, IVR state etc
	IvrStateInfo *IvrStateInfo `protobuf:"bytes,2,opt,name=ivr_state_info,json=ivrStateInfo,proto3" json:"ivr_state_info,omitempty"`
}

func (x *ProcessIvrUserInputResponse) Reset() {
	*x = ProcessIvrUserInputResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_call_ivr_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessIvrUserInputResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessIvrUserInputResponse) ProtoMessage() {}

func (x *ProcessIvrUserInputResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_call_ivr_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessIvrUserInputResponse.ProtoReflect.Descriptor instead.
func (*ProcessIvrUserInputResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_call_ivr_service_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessIvrUserInputResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ProcessIvrUserInputResponse) GetIvrStateInfo() *IvrStateInfo {
	if x != nil {
		return x.IvrStateInfo
	}
	return nil
}

type GetIvrStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// phone number of the user for which IVR state is needed
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// monitor ucid of the call for which IVR state is needed
	MonitorUcid string `protobuf:"bytes,2,opt,name=monitor_ucid,json=monitorUcid,proto3" json:"monitor_ucid,omitempty"`
	// current IVR type that the user is answering
	CurrentIvrType IvrType `protobuf:"varint,3,opt,name=current_ivr_type,json=currentIvrType,proto3,enum=cx.call_ivr.IvrType" json:"current_ivr_type,omitempty"`
	// current question id
	CurrentIvrQuestionId int32 `protobuf:"varint,4,opt,name=current_ivr_question_id,json=currentIvrQuestionId,proto3" json:"current_ivr_question_id,omitempty"`
}

func (x *GetIvrStateRequest) Reset() {
	*x = GetIvrStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_call_ivr_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIvrStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIvrStateRequest) ProtoMessage() {}

func (x *GetIvrStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_call_ivr_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIvrStateRequest.ProtoReflect.Descriptor instead.
func (*GetIvrStateRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_call_ivr_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetIvrStateRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *GetIvrStateRequest) GetMonitorUcid() string {
	if x != nil {
		return x.MonitorUcid
	}
	return ""
}

func (x *GetIvrStateRequest) GetCurrentIvrType() IvrType {
	if x != nil {
		return x.CurrentIvrType
	}
	return IvrType_IVR_TYPE_UNSPECIFIED
}

func (x *GetIvrStateRequest) GetCurrentIvrQuestionId() int32 {
	if x != nil {
		return x.CurrentIvrQuestionId
	}
	return 0
}

type GetIvrStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// info regarding the current IVR question like question id, IVR type, IVR state etc
	IvrStateInfo *IvrStateInfo `protobuf:"bytes,2,opt,name=ivr_state_info,json=ivrStateInfo,proto3" json:"ivr_state_info,omitempty"`
}

func (x *GetIvrStateResponse) Reset() {
	*x = GetIvrStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_call_ivr_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIvrStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIvrStateResponse) ProtoMessage() {}

func (x *GetIvrStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_call_ivr_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIvrStateResponse.ProtoReflect.Descriptor instead.
func (*GetIvrStateResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_call_ivr_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetIvrStateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetIvrStateResponse) GetIvrStateInfo() *IvrStateInfo {
	if x != nil {
		return x.IvrStateInfo
	}
	return nil
}

var File_api_cx_call_ivr_service_proto protoreflect.FileDescriptor

var file_api_cx_call_ivr_service_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x76,
	0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0b, 0x63, 0x78, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x76, 0x72, 0x1a, 0x1b, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x76, 0x72, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x78, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x76, 0x72, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78,
	0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x7b, 0x0a, 0x12, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x49, 0x76,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x5f, 0x75, 0x63, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x63, 0x69, 0x64, 0x12, 0x42, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22,
	0x7b, 0x0a, 0x13, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x49, 0x76, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x0e, 0x69,
	0x76, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x76,
	0x72, 0x2e, 0x49, 0x76, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c,
	0x69, 0x76, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xbd, 0x02, 0x0a,
	0x1a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x76, 0x72, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x21, 0x0a, 0x0c, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x63, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x63,
	0x69, 0x64, 0x12, 0x3e, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x76,
	0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63,
	0x78, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x76, 0x72, 0x2e, 0x49, 0x76, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x76, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x76,
	0x72, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x76, 0x72, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x1d, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x76, 0x72, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x1a, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x76, 0x72, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x83, 0x01, 0x0a,
	0x1b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x76, 0x72, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x3f, 0x0a, 0x0e, 0x69, 0x76, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x78, 0x2e, 0x63,
	0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x76, 0x72, 0x2e, 0x49, 0x76, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x69, 0x76, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0xf2, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x49, 0x76, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a,
	0x0c, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x63, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x63, 0x69, 0x64,
	0x12, 0x3e, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x76, 0x72, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x78, 0x2e,
	0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x76, 0x72, 0x2e, 0x49, 0x76, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x76, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x35, 0x0a, 0x17, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x76, 0x72, 0x5f,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x76, 0x72, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x7b, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x49, 0x76,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x0e, 0x69, 0x76, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x78,
	0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x76, 0x72, 0x2e, 0x49, 0x76, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x69, 0x76, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x32, 0x86, 0x03, 0x0a, 0x03, 0x49, 0x76, 0x72, 0x12, 0x76, 0x0a, 0x0b,
	0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x49, 0x76, 0x72, 0x12, 0x1f, 0x2e, 0x63, 0x78,
	0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x76, 0x72, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x74, 0x65, 0x49, 0x76, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x63,
	0x78, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x76, 0x72, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69,
	0x61, 0x74, 0x65, 0x49, 0x76, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24,
	0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53,
	0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x00, 0xa8, 0x8d, 0xe8, 0x6a, 0x00, 0xf0,
	0x92, 0xe8, 0x6a, 0x00, 0x12, 0x8e, 0x01, 0x0a, 0x13, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x49, 0x76, 0x72, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x27, 0x2e, 0x63,
	0x78, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x76, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x49, 0x76, 0x72, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x5f,
	0x69, 0x76, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x76, 0x72, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e,
	0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x00, 0xa8, 0x8d, 0xe8, 0x6a, 0x00,
	0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x76, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x49, 0x76, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69,
	0x76, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x76, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x5f,
	0x69, 0x76, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x76, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2,
	0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d,
	0xe8, 0x6a, 0x00, 0xa8, 0x8d, 0xe8, 0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x42, 0x50, 0x0a,
	0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x63,
	0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x76, 0x72, 0x5a, 0x26, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x76, 0x72, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_call_ivr_service_proto_rawDescOnce sync.Once
	file_api_cx_call_ivr_service_proto_rawDescData = file_api_cx_call_ivr_service_proto_rawDesc
)

func file_api_cx_call_ivr_service_proto_rawDescGZIP() []byte {
	file_api_cx_call_ivr_service_proto_rawDescOnce.Do(func() {
		file_api_cx_call_ivr_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_call_ivr_service_proto_rawDescData)
	})
	return file_api_cx_call_ivr_service_proto_rawDescData
}

var file_api_cx_call_ivr_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_cx_call_ivr_service_proto_goTypes = []interface{}{
	(*InitiateIvrRequest)(nil),          // 0: cx.call_ivr.InitiateIvrRequest
	(*InitiateIvrResponse)(nil),         // 1: cx.call_ivr.InitiateIvrResponse
	(*ProcessIvrUserInputRequest)(nil),  // 2: cx.call_ivr.ProcessIvrUserInputRequest
	(*ProcessIvrUserInputResponse)(nil), // 3: cx.call_ivr.ProcessIvrUserInputResponse
	(*GetIvrStateRequest)(nil),          // 4: cx.call_ivr.GetIvrStateRequest
	(*GetIvrStateResponse)(nil),         // 5: cx.call_ivr.GetIvrStateResponse
	(*common.PhoneNumber)(nil),          // 6: api.typesv2.common.PhoneNumber
	(*rpc.Status)(nil),                  // 7: rpc.Status
	(*IvrStateInfo)(nil),                // 8: cx.call_ivr.IvrStateInfo
	(IvrType)(0),                        // 9: cx.call_ivr.IvrType
}
var file_api_cx_call_ivr_service_proto_depIdxs = []int32{
	6,  // 0: cx.call_ivr.InitiateIvrRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	7,  // 1: cx.call_ivr.InitiateIvrResponse.status:type_name -> rpc.Status
	8,  // 2: cx.call_ivr.InitiateIvrResponse.ivr_state_info:type_name -> cx.call_ivr.IvrStateInfo
	6,  // 3: cx.call_ivr.ProcessIvrUserInputRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	9,  // 4: cx.call_ivr.ProcessIvrUserInputRequest.current_ivr_type:type_name -> cx.call_ivr.IvrType
	7,  // 5: cx.call_ivr.ProcessIvrUserInputResponse.status:type_name -> rpc.Status
	8,  // 6: cx.call_ivr.ProcessIvrUserInputResponse.ivr_state_info:type_name -> cx.call_ivr.IvrStateInfo
	6,  // 7: cx.call_ivr.GetIvrStateRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	9,  // 8: cx.call_ivr.GetIvrStateRequest.current_ivr_type:type_name -> cx.call_ivr.IvrType
	7,  // 9: cx.call_ivr.GetIvrStateResponse.status:type_name -> rpc.Status
	8,  // 10: cx.call_ivr.GetIvrStateResponse.ivr_state_info:type_name -> cx.call_ivr.IvrStateInfo
	0,  // 11: cx.call_ivr.Ivr.InitiateIvr:input_type -> cx.call_ivr.InitiateIvrRequest
	2,  // 12: cx.call_ivr.Ivr.ProcessIvrUserInput:input_type -> cx.call_ivr.ProcessIvrUserInputRequest
	4,  // 13: cx.call_ivr.Ivr.GetIvrState:input_type -> cx.call_ivr.GetIvrStateRequest
	1,  // 14: cx.call_ivr.Ivr.InitiateIvr:output_type -> cx.call_ivr.InitiateIvrResponse
	3,  // 15: cx.call_ivr.Ivr.ProcessIvrUserInput:output_type -> cx.call_ivr.ProcessIvrUserInputResponse
	5,  // 16: cx.call_ivr.Ivr.GetIvrState:output_type -> cx.call_ivr.GetIvrStateResponse
	14, // [14:17] is the sub-list for method output_type
	11, // [11:14] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_api_cx_call_ivr_service_proto_init() }
func file_api_cx_call_ivr_service_proto_init() {
	if File_api_cx_call_ivr_service_proto != nil {
		return
	}
	file_api_cx_call_ivr_enums_proto_init()
	file_api_cx_call_ivr_message_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_cx_call_ivr_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateIvrRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_call_ivr_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateIvrResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_call_ivr_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessIvrUserInputRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_call_ivr_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessIvrUserInputResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_call_ivr_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIvrStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_call_ivr_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIvrStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_call_ivr_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_cx_call_ivr_service_proto_goTypes,
		DependencyIndexes: file_api_cx_call_ivr_service_proto_depIdxs,
		MessageInfos:      file_api_cx_call_ivr_service_proto_msgTypes,
	}.Build()
	File_api_cx_call_ivr_service_proto = out.File
	file_api_cx_call_ivr_service_proto_rawDesc = nil
	file_api_cx_call_ivr_service_proto_goTypes = nil
	file_api_cx_call_ivr_service_proto_depIdxs = nil
}
