// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/issue_resolution_feedback/service.proto

package issue_resolution_feedback

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)
)

// Validate checks the field values on
// IngestTicketForIssueResolutionFeedbackRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IngestTicketForIssueResolutionFeedbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// IngestTicketForIssueResolutionFeedbackRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// IngestTicketForIssueResolutionFeedbackRequestMultiError, or nil if none found.
func (m *IngestTicketForIssueResolutionFeedbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IngestTicketForIssueResolutionFeedbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIssuePayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IngestTicketForIssueResolutionFeedbackRequestValidationError{
					field:  "IssuePayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IngestTicketForIssueResolutionFeedbackRequestValidationError{
					field:  "IssuePayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIssuePayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IngestTicketForIssueResolutionFeedbackRequestValidationError{
				field:  "IssuePayload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientRequestId

	if len(errors) > 0 {
		return IngestTicketForIssueResolutionFeedbackRequestMultiError(errors)
	}

	return nil
}

// IngestTicketForIssueResolutionFeedbackRequestMultiError is an error wrapping
// multiple validation errors returned by
// IngestTicketForIssueResolutionFeedbackRequest.ValidateAll() if the
// designated constraints aren't met.
type IngestTicketForIssueResolutionFeedbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IngestTicketForIssueResolutionFeedbackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IngestTicketForIssueResolutionFeedbackRequestMultiError) AllErrors() []error { return m }

// IngestTicketForIssueResolutionFeedbackRequestValidationError is the
// validation error returned by
// IngestTicketForIssueResolutionFeedbackRequest.Validate if the designated
// constraints aren't met.
type IngestTicketForIssueResolutionFeedbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IngestTicketForIssueResolutionFeedbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IngestTicketForIssueResolutionFeedbackRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e IngestTicketForIssueResolutionFeedbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IngestTicketForIssueResolutionFeedbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IngestTicketForIssueResolutionFeedbackRequestValidationError) ErrorName() string {
	return "IngestTicketForIssueResolutionFeedbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IngestTicketForIssueResolutionFeedbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIngestTicketForIssueResolutionFeedbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IngestTicketForIssueResolutionFeedbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IngestTicketForIssueResolutionFeedbackRequestValidationError{}

// Validate checks the field values on
// IngestTicketForIssueResolutionFeedbackResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IngestTicketForIssueResolutionFeedbackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// IngestTicketForIssueResolutionFeedbackResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// IngestTicketForIssueResolutionFeedbackResponseMultiError, or nil if none found.
func (m *IngestTicketForIssueResolutionFeedbackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IngestTicketForIssueResolutionFeedbackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IngestTicketForIssueResolutionFeedbackResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IngestTicketForIssueResolutionFeedbackResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IngestTicketForIssueResolutionFeedbackResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IngestTicketForIssueResolutionFeedbackResponseMultiError(errors)
	}

	return nil
}

// IngestTicketForIssueResolutionFeedbackResponseMultiError is an error
// wrapping multiple validation errors returned by
// IngestTicketForIssueResolutionFeedbackResponse.ValidateAll() if the
// designated constraints aren't met.
type IngestTicketForIssueResolutionFeedbackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IngestTicketForIssueResolutionFeedbackResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IngestTicketForIssueResolutionFeedbackResponseMultiError) AllErrors() []error { return m }

// IngestTicketForIssueResolutionFeedbackResponseValidationError is the
// validation error returned by
// IngestTicketForIssueResolutionFeedbackResponse.Validate if the designated
// constraints aren't met.
type IngestTicketForIssueResolutionFeedbackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IngestTicketForIssueResolutionFeedbackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IngestTicketForIssueResolutionFeedbackResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e IngestTicketForIssueResolutionFeedbackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IngestTicketForIssueResolutionFeedbackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IngestTicketForIssueResolutionFeedbackResponseValidationError) ErrorName() string {
	return "IngestTicketForIssueResolutionFeedbackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IngestTicketForIssueResolutionFeedbackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIngestTicketForIssueResolutionFeedbackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IngestTicketForIssueResolutionFeedbackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IngestTicketForIssueResolutionFeedbackResponseValidationError{}

// Validate checks the field values on PushIssueResolutionFeedbackRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PushIssueResolutionFeedbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushIssueResolutionFeedbackRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PushIssueResolutionFeedbackRequestMultiError, or nil if none found.
func (m *PushIssueResolutionFeedbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PushIssueResolutionFeedbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TicketId

	// no validation rules for Response

	if len(errors) > 0 {
		return PushIssueResolutionFeedbackRequestMultiError(errors)
	}

	return nil
}

// PushIssueResolutionFeedbackRequestMultiError is an error wrapping multiple
// validation errors returned by
// PushIssueResolutionFeedbackRequest.ValidateAll() if the designated
// constraints aren't met.
type PushIssueResolutionFeedbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushIssueResolutionFeedbackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushIssueResolutionFeedbackRequestMultiError) AllErrors() []error { return m }

// PushIssueResolutionFeedbackRequestValidationError is the validation error
// returned by PushIssueResolutionFeedbackRequest.Validate if the designated
// constraints aren't met.
type PushIssueResolutionFeedbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushIssueResolutionFeedbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushIssueResolutionFeedbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushIssueResolutionFeedbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushIssueResolutionFeedbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushIssueResolutionFeedbackRequestValidationError) ErrorName() string {
	return "PushIssueResolutionFeedbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PushIssueResolutionFeedbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushIssueResolutionFeedbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushIssueResolutionFeedbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushIssueResolutionFeedbackRequestValidationError{}

// Validate checks the field values on PushIssueResolutionFeedbackResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PushIssueResolutionFeedbackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushIssueResolutionFeedbackResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PushIssueResolutionFeedbackResponseMultiError, or nil if none found.
func (m *PushIssueResolutionFeedbackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PushIssueResolutionFeedbackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PushIssueResolutionFeedbackResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PushIssueResolutionFeedbackResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PushIssueResolutionFeedbackResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsFeedbackCommsValid

	// no validation rules for IsFeedbackExists

	switch v := m.ResponseMeta.(type) {
	case *PushIssueResolutionFeedbackResponse_Dispute:
		if v == nil {
			err := PushIssueResolutionFeedbackResponseValidationError{
				field:  "ResponseMeta",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDispute()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PushIssueResolutionFeedbackResponseValidationError{
						field:  "Dispute",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PushIssueResolutionFeedbackResponseValidationError{
						field:  "Dispute",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDispute()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PushIssueResolutionFeedbackResponseValidationError{
					field:  "Dispute",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return PushIssueResolutionFeedbackResponseMultiError(errors)
	}

	return nil
}

// PushIssueResolutionFeedbackResponseMultiError is an error wrapping multiple
// validation errors returned by
// PushIssueResolutionFeedbackResponse.ValidateAll() if the designated
// constraints aren't met.
type PushIssueResolutionFeedbackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushIssueResolutionFeedbackResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushIssueResolutionFeedbackResponseMultiError) AllErrors() []error { return m }

// PushIssueResolutionFeedbackResponseValidationError is the validation error
// returned by PushIssueResolutionFeedbackResponse.Validate if the designated
// constraints aren't met.
type PushIssueResolutionFeedbackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushIssueResolutionFeedbackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushIssueResolutionFeedbackResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushIssueResolutionFeedbackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushIssueResolutionFeedbackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushIssueResolutionFeedbackResponseValidationError) ErrorName() string {
	return "PushIssueResolutionFeedbackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PushIssueResolutionFeedbackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushIssueResolutionFeedbackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushIssueResolutionFeedbackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushIssueResolutionFeedbackResponseValidationError{}
