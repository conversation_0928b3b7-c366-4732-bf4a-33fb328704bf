// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/docs/esign/esign_request.pb.go

package esign

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing EsignRequest while reading from DB
func (a *EsignRequest) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the EsignRequest in string format in DB
func (a *EsignRequest) Value() (driver.Value, error) {
	return protojson.Marshal(a)
}

// Marshaler interface implementation for EsignRequest
func (a *EsignRequest) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for EsignRequest
func (a *EsignRequest) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
