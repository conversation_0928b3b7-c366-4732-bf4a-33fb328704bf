// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/docs/esign/esign_request.proto

package esign

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	esign "github.com/epifi/gamma/api/typesv2/esign"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = esign.TemplateType(0)
)

// Validate checks the field values on EsignRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EsignRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EsignRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EsignRequestMultiError, or
// nil if none found.
func (m *EsignRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EsignRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for VendorDocumentId

	// no validation rules for ClientRequestId

	// no validation rules for Client

	// no validation rules for Irn

	// no validation rules for SignUrl

	// no validation rules for Status

	// no validation rules for Vendor

	// no validation rules for TemplateType

	if all {
		switch v := interface{}(m.GetTemplateOption()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EsignRequestValidationError{
					field:  "TemplateOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EsignRequestValidationError{
					field:  "TemplateOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTemplateOption()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EsignRequestValidationError{
				field:  "TemplateOption",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSignedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EsignRequestValidationError{
					field:  "SignedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EsignRequestValidationError{
					field:  "SignedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSignedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EsignRequestValidationError{
				field:  "SignedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpiryAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EsignRequestValidationError{
					field:  "ExpiryAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EsignRequestValidationError{
					field:  "ExpiryAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EsignRequestValidationError{
				field:  "ExpiryAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EsignRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EsignRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EsignRequestValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EsignRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EsignRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EsignRequestValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EsignRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EsignRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EsignRequestValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EsignRequestMultiError(errors)
	}

	return nil
}

// EsignRequestMultiError is an error wrapping multiple validation errors
// returned by EsignRequest.ValidateAll() if the designated constraints aren't met.
type EsignRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EsignRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EsignRequestMultiError) AllErrors() []error { return m }

// EsignRequestValidationError is the validation error returned by
// EsignRequest.Validate if the designated constraints aren't met.
type EsignRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EsignRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EsignRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EsignRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EsignRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EsignRequestValidationError) ErrorName() string { return "EsignRequestValidationError" }

// Error satisfies the builtin error interface
func (e EsignRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEsignRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EsignRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EsignRequestValidationError{}
