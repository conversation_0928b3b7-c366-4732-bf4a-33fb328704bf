// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=EsignRequestClient,EsignRequestStatus,EsignRequestVendor,EsignRequestFieldMask

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/docs/esign/enums.proto

package esign

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Client/Service/Caller which called Esign
type EsignRequestClient int32

const (
	EsignRequestClient_ESIGN_REQUEST_CLIENT_UNSPECIFIED          EsignRequestClient = 0
	EsignRequestClient_ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN    EsignRequestClient = 1
	EsignRequestClient_ESIGN_REQUEST_CLIENT_SAVINGS_ACCOUNT      EsignRequestClient = 2
	EsignRequestClient_ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN_V2 EsignRequestClient = 3
)

// Enum value maps for EsignRequestClient.
var (
	EsignRequestClient_name = map[int32]string{
		0: "ESIGN_REQUEST_CLIENT_UNSPECIFIED",
		1: "ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN",
		2: "ESIGN_REQUEST_CLIENT_SAVINGS_ACCOUNT",
		3: "ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN_V2",
	}
	EsignRequestClient_value = map[string]int32{
		"ESIGN_REQUEST_CLIENT_UNSPECIFIED":          0,
		"ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN":    1,
		"ESIGN_REQUEST_CLIENT_SAVINGS_ACCOUNT":      2,
		"ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN_V2": 3,
	}
)

func (x EsignRequestClient) Enum() *EsignRequestClient {
	p := new(EsignRequestClient)
	*p = x
	return p
}

func (x EsignRequestClient) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EsignRequestClient) Descriptor() protoreflect.EnumDescriptor {
	return file_api_docs_esign_enums_proto_enumTypes[0].Descriptor()
}

func (EsignRequestClient) Type() protoreflect.EnumType {
	return &file_api_docs_esign_enums_proto_enumTypes[0]
}

func (x EsignRequestClient) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EsignRequestClient.Descriptor instead.
func (EsignRequestClient) EnumDescriptor() ([]byte, []int) {
	return file_api_docs_esign_enums_proto_rawDescGZIP(), []int{0}
}

type EsignRequestStatus int32

const (
	EsignRequestStatus_ESIGN_REQUEST_STATUS_UNSPECIFIED     EsignRequestStatus = 0
	EsignRequestStatus_ESIGN_REQUEST_STATUS_CREATED         EsignRequestStatus = 1
	EsignRequestStatus_ESIGN_REQUEST_STATUS_PENDING         EsignRequestStatus = 2
	EsignRequestStatus_ESIGN_REQUEST_STATUS_SUCCESS         EsignRequestStatus = 3
	EsignRequestStatus_ESIGN_REQUEST_STATUS_FAILED          EsignRequestStatus = 4
	EsignRequestStatus_ESIGN_REQUEST_STATUS_PENDING_AT_BANK EsignRequestStatus = 5
)

// Enum value maps for EsignRequestStatus.
var (
	EsignRequestStatus_name = map[int32]string{
		0: "ESIGN_REQUEST_STATUS_UNSPECIFIED",
		1: "ESIGN_REQUEST_STATUS_CREATED",
		2: "ESIGN_REQUEST_STATUS_PENDING",
		3: "ESIGN_REQUEST_STATUS_SUCCESS",
		4: "ESIGN_REQUEST_STATUS_FAILED",
		5: "ESIGN_REQUEST_STATUS_PENDING_AT_BANK",
	}
	EsignRequestStatus_value = map[string]int32{
		"ESIGN_REQUEST_STATUS_UNSPECIFIED":     0,
		"ESIGN_REQUEST_STATUS_CREATED":         1,
		"ESIGN_REQUEST_STATUS_PENDING":         2,
		"ESIGN_REQUEST_STATUS_SUCCESS":         3,
		"ESIGN_REQUEST_STATUS_FAILED":          4,
		"ESIGN_REQUEST_STATUS_PENDING_AT_BANK": 5,
	}
)

func (x EsignRequestStatus) Enum() *EsignRequestStatus {
	p := new(EsignRequestStatus)
	*p = x
	return p
}

func (x EsignRequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EsignRequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_docs_esign_enums_proto_enumTypes[1].Descriptor()
}

func (EsignRequestStatus) Type() protoreflect.EnumType {
	return &file_api_docs_esign_enums_proto_enumTypes[1]
}

func (x EsignRequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EsignRequestStatus.Descriptor instead.
func (EsignRequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_docs_esign_enums_proto_rawDescGZIP(), []int{1}
}

type EsignRequestVendor int32

const (
	EsignRequestVendor_ESIGN_REQUEST_VENDOR_UNSPECIFIED EsignRequestVendor = 0
	EsignRequestVendor_ESIGN_REQUEST_VENDOR_LEEGALITY   EsignRequestVendor = 1
)

// Enum value maps for EsignRequestVendor.
var (
	EsignRequestVendor_name = map[int32]string{
		0: "ESIGN_REQUEST_VENDOR_UNSPECIFIED",
		1: "ESIGN_REQUEST_VENDOR_LEEGALITY",
	}
	EsignRequestVendor_value = map[string]int32{
		"ESIGN_REQUEST_VENDOR_UNSPECIFIED": 0,
		"ESIGN_REQUEST_VENDOR_LEEGALITY":   1,
	}
)

func (x EsignRequestVendor) Enum() *EsignRequestVendor {
	p := new(EsignRequestVendor)
	*p = x
	return p
}

func (x EsignRequestVendor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EsignRequestVendor) Descriptor() protoreflect.EnumDescriptor {
	return file_api_docs_esign_enums_proto_enumTypes[2].Descriptor()
}

func (EsignRequestVendor) Type() protoreflect.EnumType {
	return &file_api_docs_esign_enums_proto_enumTypes[2]
}

func (x EsignRequestVendor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EsignRequestVendor.Descriptor instead.
func (EsignRequestVendor) EnumDescriptor() ([]byte, []int) {
	return file_api_docs_esign_enums_proto_rawDescGZIP(), []int{2}
}

type EsignRequestFieldMask int32

const (
	EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_UNSPECIFIED        EsignRequestFieldMask = 0
	EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_ACTOR_ID           EsignRequestFieldMask = 1
	EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_VENDOR_DOCUMENT_ID EsignRequestFieldMask = 2
	EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_CLIENT_REQUEST_ID  EsignRequestFieldMask = 3
	EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_CLIENT             EsignRequestFieldMask = 4
	EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_IRN                EsignRequestFieldMask = 5
	EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_SIGN_URL           EsignRequestFieldMask = 6
	EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_STATUS             EsignRequestFieldMask = 7
	EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_VENDOR             EsignRequestFieldMask = 8
	EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_TEMPLATE_TYPE      EsignRequestFieldMask = 9
	EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_TEMPLATE_OPTION    EsignRequestFieldMask = 10
	EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_SIGNED_AT          EsignRequestFieldMask = 11
	EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_EXPIRY_AT          EsignRequestFieldMask = 12
)

// Enum value maps for EsignRequestFieldMask.
var (
	EsignRequestFieldMask_name = map[int32]string{
		0:  "ESIGN_REQUEST_FIELD_MASK_UNSPECIFIED",
		1:  "ESIGN_REQUEST_FIELD_MASK_ACTOR_ID",
		2:  "ESIGN_REQUEST_FIELD_MASK_VENDOR_DOCUMENT_ID",
		3:  "ESIGN_REQUEST_FIELD_MASK_CLIENT_REQUEST_ID",
		4:  "ESIGN_REQUEST_FIELD_MASK_CLIENT",
		5:  "ESIGN_REQUEST_FIELD_MASK_IRN",
		6:  "ESIGN_REQUEST_FIELD_MASK_SIGN_URL",
		7:  "ESIGN_REQUEST_FIELD_MASK_STATUS",
		8:  "ESIGN_REQUEST_FIELD_MASK_VENDOR",
		9:  "ESIGN_REQUEST_FIELD_MASK_TEMPLATE_TYPE",
		10: "ESIGN_REQUEST_FIELD_MASK_TEMPLATE_OPTION",
		11: "ESIGN_REQUEST_FIELD_MASK_SIGNED_AT",
		12: "ESIGN_REQUEST_FIELD_MASK_EXPIRY_AT",
	}
	EsignRequestFieldMask_value = map[string]int32{
		"ESIGN_REQUEST_FIELD_MASK_UNSPECIFIED":        0,
		"ESIGN_REQUEST_FIELD_MASK_ACTOR_ID":           1,
		"ESIGN_REQUEST_FIELD_MASK_VENDOR_DOCUMENT_ID": 2,
		"ESIGN_REQUEST_FIELD_MASK_CLIENT_REQUEST_ID":  3,
		"ESIGN_REQUEST_FIELD_MASK_CLIENT":             4,
		"ESIGN_REQUEST_FIELD_MASK_IRN":                5,
		"ESIGN_REQUEST_FIELD_MASK_SIGN_URL":           6,
		"ESIGN_REQUEST_FIELD_MASK_STATUS":             7,
		"ESIGN_REQUEST_FIELD_MASK_VENDOR":             8,
		"ESIGN_REQUEST_FIELD_MASK_TEMPLATE_TYPE":      9,
		"ESIGN_REQUEST_FIELD_MASK_TEMPLATE_OPTION":    10,
		"ESIGN_REQUEST_FIELD_MASK_SIGNED_AT":          11,
		"ESIGN_REQUEST_FIELD_MASK_EXPIRY_AT":          12,
	}
)

func (x EsignRequestFieldMask) Enum() *EsignRequestFieldMask {
	p := new(EsignRequestFieldMask)
	*p = x
	return p
}

func (x EsignRequestFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EsignRequestFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_docs_esign_enums_proto_enumTypes[3].Descriptor()
}

func (EsignRequestFieldMask) Type() protoreflect.EnumType {
	return &file_api_docs_esign_enums_proto_enumTypes[3]
}

func (x EsignRequestFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EsignRequestFieldMask.Descriptor instead.
func (EsignRequestFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_docs_esign_enums_proto_rawDescGZIP(), []int{3}
}

var File_api_docs_esign_enums_proto protoreflect.FileDescriptor

var file_api_docs_esign_enums_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x63, 0x73, 0x2f, 0x65, 0x73, 0x69, 0x67, 0x6e,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x64, 0x6f,
	0x63, 0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2a, 0xbf, 0x01, 0x0a, 0x12, 0x45, 0x73, 0x69,
	0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12,
	0x24, 0x0a, 0x20, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a, 0x26, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x52,
	0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10,
	0x01, 0x12, 0x28, 0x0a, 0x24, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47,
	0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x2d, 0x0a, 0x29, 0x45,
	0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x4c, 0x49,
	0x45, 0x4e, 0x54, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44,
	0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0x03, 0x2a, 0xeb, 0x01, 0x0a, 0x12, 0x45,
	0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x24, 0x0a, 0x20, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x53, 0x49, 0x47, 0x4e,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x53, 0x49,
	0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x45,
	0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x1f, 0x0a,
	0x1b, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x28,
	0x0a, 0x24, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41,
	0x54, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x05, 0x2a, 0x5e, 0x0a, 0x12, 0x45, 0x73, 0x69, 0x67,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x24,
	0x0a, 0x20, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4c, 0x45, 0x45,
	0x47, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x01, 0x2a, 0xab, 0x04, 0x0a, 0x15, 0x45, 0x73, 0x69,
	0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x12, 0x28, 0x0a, 0x24, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21,
	0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49,
	0x44, 0x10, 0x01, 0x12, 0x2f, 0x0a, 0x2b, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x49, 0x44, 0x10, 0x02, 0x12, 0x2e, 0x0a, 0x2a, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x49, 0x44, 0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x53, 0x49,
	0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x52, 0x4e, 0x10, 0x05, 0x12, 0x25, 0x0a, 0x21, 0x45,
	0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x55, 0x52, 0x4c,
	0x10, 0x06, 0x12, 0x23, 0x0a, 0x1f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x07, 0x12, 0x23, 0x0a, 0x1f, 0x45, 0x53, 0x49, 0x47, 0x4e,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x08, 0x12, 0x2a, 0x0a, 0x26,
	0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x09, 0x12, 0x2c, 0x0a, 0x28, 0x45, 0x53, 0x49, 0x47,
	0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0a, 0x12, 0x26, 0x0a, 0x22, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0b, 0x12, 0x26,
	0x0a, 0x22, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52,
	0x59, 0x5f, 0x41, 0x54, 0x10, 0x0c, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x5a,
	0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x63, 0x73,
	0x2f, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_docs_esign_enums_proto_rawDescOnce sync.Once
	file_api_docs_esign_enums_proto_rawDescData = file_api_docs_esign_enums_proto_rawDesc
)

func file_api_docs_esign_enums_proto_rawDescGZIP() []byte {
	file_api_docs_esign_enums_proto_rawDescOnce.Do(func() {
		file_api_docs_esign_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_docs_esign_enums_proto_rawDescData)
	})
	return file_api_docs_esign_enums_proto_rawDescData
}

var file_api_docs_esign_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_docs_esign_enums_proto_goTypes = []interface{}{
	(EsignRequestClient)(0),    // 0: docs.esign.EsignRequestClient
	(EsignRequestStatus)(0),    // 1: docs.esign.EsignRequestStatus
	(EsignRequestVendor)(0),    // 2: docs.esign.EsignRequestVendor
	(EsignRequestFieldMask)(0), // 3: docs.esign.EsignRequestFieldMask
}
var file_api_docs_esign_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_docs_esign_enums_proto_init() }
func file_api_docs_esign_enums_proto_init() {
	if File_api_docs_esign_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_docs_esign_enums_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_docs_esign_enums_proto_goTypes,
		DependencyIndexes: file_api_docs_esign_enums_proto_depIdxs,
		EnumInfos:         file_api_docs_esign_enums_proto_enumTypes,
	}.Build()
	File_api_docs_esign_enums_proto = out.File
	file_api_docs_esign_enums_proto_rawDesc = nil
	file_api_docs_esign_enums_proto_goTypes = nil
	file_api_docs_esign_enums_proto_depIdxs = nil
}
