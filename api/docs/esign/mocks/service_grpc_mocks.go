// Code generated by MockGen. DO NOT EDIT.
// Source: api/docs/esign/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	esign "github.com/epifi/gamma/api/docs/esign"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockESignClient is a mock of ESignClient interface.
type MockESignClient struct {
	ctrl     *gomock.Controller
	recorder *MockESignClientMockRecorder
}

// MockESignClientMockRecorder is the mock recorder for MockESignClient.
type MockESignClientMockRecorder struct {
	mock *MockESignClient
}

// NewMockESignClient creates a new mock instance.
func NewMockESignClient(ctrl *gomock.Controller) *MockESignClient {
	mock := &MockESignClient{ctrl: ctrl}
	mock.recorder = &MockESignClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockESignClient) EXPECT() *MockESignClientMockRecorder {
	return m.recorder
}

// CheckESignStatus mocks base method.
func (m *MockESignClient) CheckESignStatus(ctx context.Context, in *esign.CheckESignStatusRequest, opts ...grpc.CallOption) (*esign.CheckESignStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckESignStatus", varargs...)
	ret0, _ := ret[0].(*esign.CheckESignStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckESignStatus indicates an expected call of CheckESignStatus.
func (mr *MockESignClientMockRecorder) CheckESignStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckESignStatus", reflect.TypeOf((*MockESignClient)(nil).CheckESignStatus), varargs...)
}

// InitiateESign mocks base method.
func (m *MockESignClient) InitiateESign(ctx context.Context, in *esign.InitiateESignRequest, opts ...grpc.CallOption) (*esign.InitiateESignResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateESign", varargs...)
	ret0, _ := ret[0].(*esign.InitiateESignResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateESign indicates an expected call of InitiateESign.
func (mr *MockESignClientMockRecorder) InitiateESign(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateESign", reflect.TypeOf((*MockESignClient)(nil).InitiateESign), varargs...)
}

// MockESignServer is a mock of ESignServer interface.
type MockESignServer struct {
	ctrl     *gomock.Controller
	recorder *MockESignServerMockRecorder
}

// MockESignServerMockRecorder is the mock recorder for MockESignServer.
type MockESignServerMockRecorder struct {
	mock *MockESignServer
}

// NewMockESignServer creates a new mock instance.
func NewMockESignServer(ctrl *gomock.Controller) *MockESignServer {
	mock := &MockESignServer{ctrl: ctrl}
	mock.recorder = &MockESignServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockESignServer) EXPECT() *MockESignServerMockRecorder {
	return m.recorder
}

// CheckESignStatus mocks base method.
func (m *MockESignServer) CheckESignStatus(arg0 context.Context, arg1 *esign.CheckESignStatusRequest) (*esign.CheckESignStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckESignStatus", arg0, arg1)
	ret0, _ := ret[0].(*esign.CheckESignStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckESignStatus indicates an expected call of CheckESignStatus.
func (mr *MockESignServerMockRecorder) CheckESignStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckESignStatus", reflect.TypeOf((*MockESignServer)(nil).CheckESignStatus), arg0, arg1)
}

// InitiateESign mocks base method.
func (m *MockESignServer) InitiateESign(arg0 context.Context, arg1 *esign.InitiateESignRequest) (*esign.InitiateESignResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateESign", arg0, arg1)
	ret0, _ := ret[0].(*esign.InitiateESignResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateESign indicates an expected call of InitiateESign.
func (mr *MockESignServerMockRecorder) InitiateESign(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateESign", reflect.TypeOf((*MockESignServer)(nil).InitiateESign), arg0, arg1)
}

// MockUnsafeESignServer is a mock of UnsafeESignServer interface.
type MockUnsafeESignServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeESignServerMockRecorder
}

// MockUnsafeESignServerMockRecorder is the mock recorder for MockUnsafeESignServer.
type MockUnsafeESignServerMockRecorder struct {
	mock *MockUnsafeESignServer
}

// NewMockUnsafeESignServer creates a new mock instance.
func NewMockUnsafeESignServer(ctrl *gomock.Controller) *MockUnsafeESignServer {
	mock := &MockUnsafeESignServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeESignServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeESignServer) EXPECT() *MockUnsafeESignServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedESignServer mocks base method.
func (m *MockUnsafeESignServer) mustEmbedUnimplementedESignServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedESignServer")
}

// mustEmbedUnimplementedESignServer indicates an expected call of mustEmbedUnimplementedESignServer.
func (mr *MockUnsafeESignServerMockRecorder) mustEmbedUnimplementedESignServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedESignServer", reflect.TypeOf((*MockUnsafeESignServer)(nil).mustEmbedUnimplementedESignServer))
}
