// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/docs/esign/service.proto

package esign

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ESign_InitiateESign_FullMethodName    = "/docs.esign.ESign/InitiateESign"
	ESign_CheckESignStatus_FullMethodName = "/docs.esign.ESign/CheckESignStatus"
)

// ESignClient is the client API for ESign service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ESignClient interface {
	// RPC to initiate E-Sign of a document with a vendor.
	// E-Sign can be performed over a document or over a template for which document already exists with the vendor.
	// The response includes the signed URL which can be used by client to perform E-sign by the user
	InitiateESign(ctx context.Context, in *InitiateESignRequest, opts ...grpc.CallOption) (*InitiateESignResponse, error)
	// RPC to check the status of E-Signature. The RPC takes in request the client request Id for which status check
	// is to be performed.
	CheckESignStatus(ctx context.Context, in *CheckESignStatusRequest, opts ...grpc.CallOption) (*CheckESignStatusResponse, error)
}

type eSignClient struct {
	cc grpc.ClientConnInterface
}

func NewESignClient(cc grpc.ClientConnInterface) ESignClient {
	return &eSignClient{cc}
}

func (c *eSignClient) InitiateESign(ctx context.Context, in *InitiateESignRequest, opts ...grpc.CallOption) (*InitiateESignResponse, error) {
	out := new(InitiateESignResponse)
	err := c.cc.Invoke(ctx, ESign_InitiateESign_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSignClient) CheckESignStatus(ctx context.Context, in *CheckESignStatusRequest, opts ...grpc.CallOption) (*CheckESignStatusResponse, error) {
	out := new(CheckESignStatusResponse)
	err := c.cc.Invoke(ctx, ESign_CheckESignStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ESignServer is the server API for ESign service.
// All implementations should embed UnimplementedESignServer
// for forward compatibility
type ESignServer interface {
	// RPC to initiate E-Sign of a document with a vendor.
	// E-Sign can be performed over a document or over a template for which document already exists with the vendor.
	// The response includes the signed URL which can be used by client to perform E-sign by the user
	InitiateESign(context.Context, *InitiateESignRequest) (*InitiateESignResponse, error)
	// RPC to check the status of E-Signature. The RPC takes in request the client request Id for which status check
	// is to be performed.
	CheckESignStatus(context.Context, *CheckESignStatusRequest) (*CheckESignStatusResponse, error)
}

// UnimplementedESignServer should be embedded to have forward compatible implementations.
type UnimplementedESignServer struct {
}

func (UnimplementedESignServer) InitiateESign(context.Context, *InitiateESignRequest) (*InitiateESignResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateESign not implemented")
}
func (UnimplementedESignServer) CheckESignStatus(context.Context, *CheckESignStatusRequest) (*CheckESignStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckESignStatus not implemented")
}

// UnsafeESignServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ESignServer will
// result in compilation errors.
type UnsafeESignServer interface {
	mustEmbedUnimplementedESignServer()
}

func RegisterESignServer(s grpc.ServiceRegistrar, srv ESignServer) {
	s.RegisterService(&ESign_ServiceDesc, srv)
}

func _ESign_InitiateESign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateESignRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESignServer).InitiateESign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ESign_InitiateESign_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESignServer).InitiateESign(ctx, req.(*InitiateESignRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESign_CheckESignStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckESignStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESignServer).CheckESignStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ESign_CheckESignStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESignServer).CheckESignStatus(ctx, req.(*CheckESignStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ESign_ServiceDesc is the grpc.ServiceDesc for ESign service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ESign_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "docs.esign.ESign",
	HandlerType: (*ESignServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InitiateESign",
			Handler:    _ESign_InitiateESign_Handler,
		},
		{
			MethodName: "CheckESignStatus",
			Handler:    _ESign_CheckESignStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/docs/esign/service.proto",
}
