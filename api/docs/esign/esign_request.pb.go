//go:generate gen_sql -types=EsignRequest

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/docs/esign/esign_request.proto

package esign

import (
	esign "github.com/epifi/gamma/api/typesv2/esign"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Table to keep a track of any esign related requests
type EsignRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId          string                 `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	VendorDocumentId string                 `protobuf:"bytes,3,opt,name=vendor_document_id,json=vendorDocumentId,proto3" json:"vendor_document_id,omitempty"` // Document Id returned by vendor
	ClientRequestId  string                 `protobuf:"bytes,4,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`    // Id generated from Client, sent to Esign
	Client           EsignRequestClient     `protobuf:"varint,5,opt,name=client,proto3,enum=docs.esign.EsignRequestClient" json:"client,omitempty"`           // Client/Service/Caller which called Esign
	Irn              string                 `protobuf:"bytes,6,opt,name=irn,proto3" json:"irn,omitempty"`                                                     // Internal Reference Number, generated and sent from Esign to Vendor
	SignUrl          string                 `protobuf:"bytes,7,opt,name=sign_url,json=signUrl,proto3" json:"sign_url,omitempty"`                              // document url to send to vendor
	Status           EsignRequestStatus     `protobuf:"varint,8,opt,name=status,proto3,enum=docs.esign.EsignRequestStatus" json:"status,omitempty"`
	Vendor           EsignRequestVendor     `protobuf:"varint,9,opt,name=vendor,proto3,enum=docs.esign.EsignRequestVendor" json:"vendor,omitempty"`
	TemplateType     esign.TemplateType     `protobuf:"varint,10,opt,name=template_type,json=templateType,proto3,enum=api.typesv2.esign.TemplateType" json:"template_type,omitempty"`
	TemplateOption   *esign.TemplateOptions `protobuf:"bytes,11,opt,name=template_option,json=templateOption,proto3" json:"template_option,omitempty"`
	SignedAt         *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=signed_at,json=signedAt,proto3" json:"signed_at,omitempty"`
	ExpiryAt         *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=expiry_at,json=expiryAt,proto3" json:"expiry_at,omitempty"`
	CreatedAt        *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt        *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt        *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *EsignRequest) Reset() {
	*x = EsignRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_docs_esign_esign_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EsignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EsignRequest) ProtoMessage() {}

func (x *EsignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_docs_esign_esign_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EsignRequest.ProtoReflect.Descriptor instead.
func (*EsignRequest) Descriptor() ([]byte, []int) {
	return file_api_docs_esign_esign_request_proto_rawDescGZIP(), []int{0}
}

func (x *EsignRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EsignRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *EsignRequest) GetVendorDocumentId() string {
	if x != nil {
		return x.VendorDocumentId
	}
	return ""
}

func (x *EsignRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *EsignRequest) GetClient() EsignRequestClient {
	if x != nil {
		return x.Client
	}
	return EsignRequestClient_ESIGN_REQUEST_CLIENT_UNSPECIFIED
}

func (x *EsignRequest) GetIrn() string {
	if x != nil {
		return x.Irn
	}
	return ""
}

func (x *EsignRequest) GetSignUrl() string {
	if x != nil {
		return x.SignUrl
	}
	return ""
}

func (x *EsignRequest) GetStatus() EsignRequestStatus {
	if x != nil {
		return x.Status
	}
	return EsignRequestStatus_ESIGN_REQUEST_STATUS_UNSPECIFIED
}

func (x *EsignRequest) GetVendor() EsignRequestVendor {
	if x != nil {
		return x.Vendor
	}
	return EsignRequestVendor_ESIGN_REQUEST_VENDOR_UNSPECIFIED
}

func (x *EsignRequest) GetTemplateType() esign.TemplateType {
	if x != nil {
		return x.TemplateType
	}
	return esign.TemplateType(0)
}

func (x *EsignRequest) GetTemplateOption() *esign.TemplateOptions {
	if x != nil {
		return x.TemplateOption
	}
	return nil
}

func (x *EsignRequest) GetSignedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SignedAt
	}
	return nil
}

func (x *EsignRequest) GetExpiryAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryAt
	}
	return nil
}

func (x *EsignRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *EsignRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *EsignRequest) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_docs_esign_esign_request_proto protoreflect.FileDescriptor

var file_api_docs_esign_esign_request_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x63, 0x73, 0x2f, 0x65, 0x73, 0x69, 0x67, 0x6e,
	0x2f, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e,
	0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x63, 0x73, 0x2f, 0x65, 0x73, 0x69, 0x67, 0x6e,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2f, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x9e, 0x06, 0x0a, 0x0c, 0x45, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x65, 0x73, 0x69,
	0x67, 0x6e, 0x2e, 0x45, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x69, 0x72, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x72, 0x6e, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x69, 0x67, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x64, 0x6f, 0x63,
	0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e, 0x45, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x36, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e,
	0x45, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x44, 0x0a, 0x0d, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x4b, 0x0a, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0e, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a,
	0x09, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x41, 0x74, 0x12, 0x37, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79,
	0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x6f, 0x63, 0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x63, 0x73, 0x2f, 0x65, 0x73, 0x69, 0x67, 0x6e,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_docs_esign_esign_request_proto_rawDescOnce sync.Once
	file_api_docs_esign_esign_request_proto_rawDescData = file_api_docs_esign_esign_request_proto_rawDesc
)

func file_api_docs_esign_esign_request_proto_rawDescGZIP() []byte {
	file_api_docs_esign_esign_request_proto_rawDescOnce.Do(func() {
		file_api_docs_esign_esign_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_docs_esign_esign_request_proto_rawDescData)
	})
	return file_api_docs_esign_esign_request_proto_rawDescData
}

var file_api_docs_esign_esign_request_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_docs_esign_esign_request_proto_goTypes = []interface{}{
	(*EsignRequest)(nil),          // 0: docs.esign.EsignRequest
	(EsignRequestClient)(0),       // 1: docs.esign.EsignRequestClient
	(EsignRequestStatus)(0),       // 2: docs.esign.EsignRequestStatus
	(EsignRequestVendor)(0),       // 3: docs.esign.EsignRequestVendor
	(esign.TemplateType)(0),       // 4: api.typesv2.esign.TemplateType
	(*esign.TemplateOptions)(nil), // 5: api.typesv2.esign.TemplateOptions
	(*timestamppb.Timestamp)(nil), // 6: google.protobuf.Timestamp
}
var file_api_docs_esign_esign_request_proto_depIdxs = []int32{
	1,  // 0: docs.esign.EsignRequest.client:type_name -> docs.esign.EsignRequestClient
	2,  // 1: docs.esign.EsignRequest.status:type_name -> docs.esign.EsignRequestStatus
	3,  // 2: docs.esign.EsignRequest.vendor:type_name -> docs.esign.EsignRequestVendor
	4,  // 3: docs.esign.EsignRequest.template_type:type_name -> api.typesv2.esign.TemplateType
	5,  // 4: docs.esign.EsignRequest.template_option:type_name -> api.typesv2.esign.TemplateOptions
	6,  // 5: docs.esign.EsignRequest.signed_at:type_name -> google.protobuf.Timestamp
	6,  // 6: docs.esign.EsignRequest.expiry_at:type_name -> google.protobuf.Timestamp
	6,  // 7: docs.esign.EsignRequest.created_at:type_name -> google.protobuf.Timestamp
	6,  // 8: docs.esign.EsignRequest.updated_at:type_name -> google.protobuf.Timestamp
	6,  // 9: docs.esign.EsignRequest.deleted_at:type_name -> google.protobuf.Timestamp
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_api_docs_esign_esign_request_proto_init() }
func file_api_docs_esign_esign_request_proto_init() {
	if File_api_docs_esign_esign_request_proto != nil {
		return
	}
	file_api_docs_esign_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_docs_esign_esign_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EsignRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_docs_esign_esign_request_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_docs_esign_esign_request_proto_goTypes,
		DependencyIndexes: file_api_docs_esign_esign_request_proto_depIdxs,
		MessageInfos:      file_api_docs_esign_esign_request_proto_msgTypes,
	}.Build()
	File_api_docs_esign_esign_request_proto = out.File
	file_api_docs_esign_esign_request_proto_rawDesc = nil
	file_api_docs_esign_esign_request_proto_goTypes = nil
	file_api_docs_esign_esign_request_proto_depIdxs = nil
}
