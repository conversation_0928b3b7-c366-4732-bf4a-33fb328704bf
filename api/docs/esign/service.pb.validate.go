// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/docs/esign/service.proto

package esign

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	esign "github.com/epifi/gamma/api/typesv2/esign"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = esign.TemplateType(0)
)

// Validate checks the field values on InitiateESignRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateESignRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateESignRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateESignRequestMultiError, or nil if none found.
func (m *InitiateESignRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateESignRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ClientRequestId

	// no validation rules for TemplateType

	if all {
		switch v := interface{}(m.GetTemplateOption()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateESignRequestValidationError{
					field:  "TemplateOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateESignRequestValidationError{
					field:  "TemplateOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTemplateOption()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateESignRequestValidationError{
				field:  "TemplateOption",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	// no validation rules for Client

	// no validation rules for InternalReferenceNumber

	if len(errors) > 0 {
		return InitiateESignRequestMultiError(errors)
	}

	return nil
}

// InitiateESignRequestMultiError is an error wrapping multiple validation
// errors returned by InitiateESignRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateESignRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateESignRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateESignRequestMultiError) AllErrors() []error { return m }

// InitiateESignRequestValidationError is the validation error returned by
// InitiateESignRequest.Validate if the designated constraints aren't met.
type InitiateESignRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateESignRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateESignRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateESignRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateESignRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateESignRequestValidationError) ErrorName() string {
	return "InitiateESignRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateESignRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateESignRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateESignRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateESignRequestValidationError{}

// Validate checks the field values on InitiateESignResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateESignResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateESignResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateESignResponseMultiError, or nil if none found.
func (m *InitiateESignResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateESignResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateESignResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateESignResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateESignResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SignUrl

	if all {
		switch v := interface{}(m.GetExpiryAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateESignResponseValidationError{
					field:  "ExpiryAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateESignResponseValidationError{
					field:  "ExpiryAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateESignResponseValidationError{
				field:  "ExpiryAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateESignResponseMultiError(errors)
	}

	return nil
}

// InitiateESignResponseMultiError is an error wrapping multiple validation
// errors returned by InitiateESignResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateESignResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateESignResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateESignResponseMultiError) AllErrors() []error { return m }

// InitiateESignResponseValidationError is the validation error returned by
// InitiateESignResponse.Validate if the designated constraints aren't met.
type InitiateESignResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateESignResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateESignResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateESignResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateESignResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateESignResponseValidationError) ErrorName() string {
	return "InitiateESignResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateESignResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateESignResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateESignResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateESignResponseValidationError{}

// Validate checks the field values on CheckESignStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckESignStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckESignStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckESignStatusRequestMultiError, or nil if none found.
func (m *CheckESignStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckESignStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientRequestId

	// no validation rules for Client

	// no validation rules for Vendor

	if len(errors) > 0 {
		return CheckESignStatusRequestMultiError(errors)
	}

	return nil
}

// CheckESignStatusRequestMultiError is an error wrapping multiple validation
// errors returned by CheckESignStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckESignStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckESignStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckESignStatusRequestMultiError) AllErrors() []error { return m }

// CheckESignStatusRequestValidationError is the validation error returned by
// CheckESignStatusRequest.Validate if the designated constraints aren't met.
type CheckESignStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckESignStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckESignStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckESignStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckESignStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckESignStatusRequestValidationError) ErrorName() string {
	return "CheckESignStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckESignStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckESignStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckESignStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckESignStatusRequestValidationError{}

// Validate checks the field values on CheckESignStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckESignStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckESignStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckESignStatusResponseMultiError, or nil if none found.
func (m *CheckESignStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckESignStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckESignStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckESignStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckESignStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ESignStatus

	if all {
		switch v := interface{}(m.GetSignedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckESignStatusResponseValidationError{
					field:  "SignedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckESignStatusResponseValidationError{
					field:  "SignedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSignedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckESignStatusResponseValidationError{
				field:  "SignedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SignUrl

	if len(errors) > 0 {
		return CheckESignStatusResponseMultiError(errors)
	}

	return nil
}

// CheckESignStatusResponseMultiError is an error wrapping multiple validation
// errors returned by CheckESignStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckESignStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckESignStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckESignStatusResponseMultiError) AllErrors() []error { return m }

// CheckESignStatusResponseValidationError is the validation error returned by
// CheckESignStatusResponse.Validate if the designated constraints aren't met.
type CheckESignStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckESignStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckESignStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckESignStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckESignStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckESignStatusResponseValidationError) ErrorName() string {
	return "CheckESignStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckESignStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckESignStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckESignStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckESignStatusResponseValidationError{}
