// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/docs/esign/service.proto

package esign

import (
	rpc "github.com/epifi/be-common/api/rpc"
	esign "github.com/epifi/gamma/api/typesv2/esign"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InitiateESignRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor who has to perform e-sign
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// request id of client calling e-sign service
	ClientRequestId string `protobuf:"bytes,2,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// template type of the e-sign document.
	TemplateType            esign.TemplateType     `protobuf:"varint,3,opt,name=template_type,json=templateType,proto3,enum=api.typesv2.esign.TemplateType" json:"template_type,omitempty"`
	TemplateOption          *esign.TemplateOptions `protobuf:"bytes,4,opt,name=template_option,json=templateOption,proto3" json:"template_option,omitempty"`
	Vendor                  EsignRequestVendor     `protobuf:"varint,5,opt,name=vendor,proto3,enum=docs.esign.EsignRequestVendor" json:"vendor,omitempty"`
	Client                  EsignRequestClient     `protobuf:"varint,6,opt,name=client,proto3,enum=docs.esign.EsignRequestClient" json:"client,omitempty"`
	InternalReferenceNumber string                 `protobuf:"bytes,7,opt,name=internal_reference_number,json=internalReferenceNumber,proto3" json:"internal_reference_number,omitempty"`
}

func (x *InitiateESignRequest) Reset() {
	*x = InitiateESignRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_docs_esign_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateESignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateESignRequest) ProtoMessage() {}

func (x *InitiateESignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_docs_esign_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateESignRequest.ProtoReflect.Descriptor instead.
func (*InitiateESignRequest) Descriptor() ([]byte, []int) {
	return file_api_docs_esign_service_proto_rawDescGZIP(), []int{0}
}

func (x *InitiateESignRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *InitiateESignRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *InitiateESignRequest) GetTemplateType() esign.TemplateType {
	if x != nil {
		return x.TemplateType
	}
	return esign.TemplateType(0)
}

func (x *InitiateESignRequest) GetTemplateOption() *esign.TemplateOptions {
	if x != nil {
		return x.TemplateOption
	}
	return nil
}

func (x *InitiateESignRequest) GetVendor() EsignRequestVendor {
	if x != nil {
		return x.Vendor
	}
	return EsignRequestVendor_ESIGN_REQUEST_VENDOR_UNSPECIFIED
}

func (x *InitiateESignRequest) GetClient() EsignRequestClient {
	if x != nil {
		return x.Client
	}
	return EsignRequestClient_ESIGN_REQUEST_CLIENT_UNSPECIFIED
}

func (x *InitiateESignRequest) GetInternalReferenceNumber() string {
	if x != nil {
		return x.InternalReferenceNumber
	}
	return ""
}

type InitiateESignResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SignUrl  string                 `protobuf:"bytes,2,opt,name=sign_url,json=signUrl,proto3" json:"sign_url,omitempty"`
	ExpiryAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=expiry_at,json=expiryAt,proto3" json:"expiry_at,omitempty"`
}

func (x *InitiateESignResponse) Reset() {
	*x = InitiateESignResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_docs_esign_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateESignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateESignResponse) ProtoMessage() {}

func (x *InitiateESignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_docs_esign_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateESignResponse.ProtoReflect.Descriptor instead.
func (*InitiateESignResponse) Descriptor() ([]byte, []int) {
	return file_api_docs_esign_service_proto_rawDescGZIP(), []int{1}
}

func (x *InitiateESignResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *InitiateESignResponse) GetSignUrl() string {
	if x != nil {
		return x.SignUrl
	}
	return ""
}

func (x *InitiateESignResponse) GetExpiryAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryAt
	}
	return nil
}

type CheckESignStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientRequestId string             `protobuf:"bytes,1,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	Client          EsignRequestClient `protobuf:"varint,2,opt,name=client,proto3,enum=docs.esign.EsignRequestClient" json:"client,omitempty"`
	Vendor          EsignRequestVendor `protobuf:"varint,3,opt,name=vendor,proto3,enum=docs.esign.EsignRequestVendor" json:"vendor,omitempty"`
}

func (x *CheckESignStatusRequest) Reset() {
	*x = CheckESignStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_docs_esign_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckESignStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckESignStatusRequest) ProtoMessage() {}

func (x *CheckESignStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_docs_esign_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckESignStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckESignStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_docs_esign_service_proto_rawDescGZIP(), []int{2}
}

func (x *CheckESignStatusRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *CheckESignStatusRequest) GetClient() EsignRequestClient {
	if x != nil {
		return x.Client
	}
	return EsignRequestClient_ESIGN_REQUEST_CLIENT_UNSPECIFIED
}

func (x *CheckESignStatusRequest) GetVendor() EsignRequestVendor {
	if x != nil {
		return x.Vendor
	}
	return EsignRequestVendor_ESIGN_REQUEST_VENDOR_UNSPECIFIED
}

type CheckESignStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ESignStatus EsignRequestStatus     `protobuf:"varint,2,opt,name=e_sign_status,json=eSignStatus,proto3,enum=docs.esign.EsignRequestStatus" json:"e_sign_status,omitempty"`
	SignedAt    *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=signed_at,json=signedAt,proto3" json:"signed_at,omitempty"`
	SignUrl     string                 `protobuf:"bytes,4,opt,name=sign_url,json=signUrl,proto3" json:"sign_url,omitempty"`
}

func (x *CheckESignStatusResponse) Reset() {
	*x = CheckESignStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_docs_esign_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckESignStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckESignStatusResponse) ProtoMessage() {}

func (x *CheckESignStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_docs_esign_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckESignStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckESignStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_docs_esign_service_proto_rawDescGZIP(), []int{3}
}

func (x *CheckESignStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckESignStatusResponse) GetESignStatus() EsignRequestStatus {
	if x != nil {
		return x.ESignStatus
	}
	return EsignRequestStatus_ESIGN_REQUEST_STATUS_UNSPECIFIED
}

func (x *CheckESignStatusResponse) GetSignedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SignedAt
	}
	return nil
}

func (x *CheckESignStatusResponse) GetSignUrl() string {
	if x != nil {
		return x.SignUrl
	}
	return ""
}

var File_api_docs_esign_service_proto protoreflect.FileDescriptor

var file_api_docs_esign_service_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x63, 0x73, 0x2f, 0x65, 0x73, 0x69, 0x67, 0x6e,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a,
	0x64, 0x6f, 0x63, 0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f,
	0x64, 0x6f, 0x63, 0x73, 0x2f, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2f, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x9c, 0x03, 0x0a, 0x14, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x45, 0x53, 0x69, 0x67,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x44, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x65, 0x73, 0x69,
	0x67, 0x6e, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x0e, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e,
	0x45, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x36, 0x0a, 0x06, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x64, 0x6f, 0x63,
	0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e, 0x45, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x19, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x90,
	0x01, 0x0a, 0x15, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x45, 0x53, 0x69, 0x67, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x69, 0x67, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x37, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x79, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x41,
	0x74, 0x22, 0xb5, 0x01, 0x0a, 0x17, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x53, 0x69, 0x67, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a,
	0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x06, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x64, 0x6f, 0x63, 0x73,
	0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e, 0x45, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x12, 0x36, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1e, 0x2e, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e, 0x45,
	0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x22, 0xd7, 0x01, 0x0a, 0x18, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x42, 0x0a, 0x0d, 0x65,
	0x5f, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e,
	0x45, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0b, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x37, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x69, 0x67, 0x6e,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x69, 0x67, 0x6e,
	0x55, 0x72, 0x6c, 0x32, 0xc0, 0x01, 0x0a, 0x05, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x56, 0x0a,
	0x0d, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x20,
	0x2e, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x74, 0x65, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x21, 0x2e, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e, 0x49, 0x6e,
	0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x10, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x53,
	0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x2e, 0x64, 0x6f, 0x63, 0x73,
	0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x53, 0x69, 0x67,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24,
	0x2e, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x45, 0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x5a,
	0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x63, 0x73,
	0x2f, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_docs_esign_service_proto_rawDescOnce sync.Once
	file_api_docs_esign_service_proto_rawDescData = file_api_docs_esign_service_proto_rawDesc
)

func file_api_docs_esign_service_proto_rawDescGZIP() []byte {
	file_api_docs_esign_service_proto_rawDescOnce.Do(func() {
		file_api_docs_esign_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_docs_esign_service_proto_rawDescData)
	})
	return file_api_docs_esign_service_proto_rawDescData
}

var file_api_docs_esign_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_docs_esign_service_proto_goTypes = []interface{}{
	(*InitiateESignRequest)(nil),     // 0: docs.esign.InitiateESignRequest
	(*InitiateESignResponse)(nil),    // 1: docs.esign.InitiateESignResponse
	(*CheckESignStatusRequest)(nil),  // 2: docs.esign.CheckESignStatusRequest
	(*CheckESignStatusResponse)(nil), // 3: docs.esign.CheckESignStatusResponse
	(esign.TemplateType)(0),          // 4: api.typesv2.esign.TemplateType
	(*esign.TemplateOptions)(nil),    // 5: api.typesv2.esign.TemplateOptions
	(EsignRequestVendor)(0),          // 6: docs.esign.EsignRequestVendor
	(EsignRequestClient)(0),          // 7: docs.esign.EsignRequestClient
	(*rpc.Status)(nil),               // 8: rpc.Status
	(*timestamppb.Timestamp)(nil),    // 9: google.protobuf.Timestamp
	(EsignRequestStatus)(0),          // 10: docs.esign.EsignRequestStatus
}
var file_api_docs_esign_service_proto_depIdxs = []int32{
	4,  // 0: docs.esign.InitiateESignRequest.template_type:type_name -> api.typesv2.esign.TemplateType
	5,  // 1: docs.esign.InitiateESignRequest.template_option:type_name -> api.typesv2.esign.TemplateOptions
	6,  // 2: docs.esign.InitiateESignRequest.vendor:type_name -> docs.esign.EsignRequestVendor
	7,  // 3: docs.esign.InitiateESignRequest.client:type_name -> docs.esign.EsignRequestClient
	8,  // 4: docs.esign.InitiateESignResponse.status:type_name -> rpc.Status
	9,  // 5: docs.esign.InitiateESignResponse.expiry_at:type_name -> google.protobuf.Timestamp
	7,  // 6: docs.esign.CheckESignStatusRequest.client:type_name -> docs.esign.EsignRequestClient
	6,  // 7: docs.esign.CheckESignStatusRequest.vendor:type_name -> docs.esign.EsignRequestVendor
	8,  // 8: docs.esign.CheckESignStatusResponse.status:type_name -> rpc.Status
	10, // 9: docs.esign.CheckESignStatusResponse.e_sign_status:type_name -> docs.esign.EsignRequestStatus
	9,  // 10: docs.esign.CheckESignStatusResponse.signed_at:type_name -> google.protobuf.Timestamp
	0,  // 11: docs.esign.ESign.InitiateESign:input_type -> docs.esign.InitiateESignRequest
	2,  // 12: docs.esign.ESign.CheckESignStatus:input_type -> docs.esign.CheckESignStatusRequest
	1,  // 13: docs.esign.ESign.InitiateESign:output_type -> docs.esign.InitiateESignResponse
	3,  // 14: docs.esign.ESign.CheckESignStatus:output_type -> docs.esign.CheckESignStatusResponse
	13, // [13:15] is the sub-list for method output_type
	11, // [11:13] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_api_docs_esign_service_proto_init() }
func file_api_docs_esign_service_proto_init() {
	if File_api_docs_esign_service_proto != nil {
		return
	}
	file_api_docs_esign_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_docs_esign_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateESignRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_docs_esign_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateESignResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_docs_esign_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckESignStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_docs_esign_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckESignStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_docs_esign_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_docs_esign_service_proto_goTypes,
		DependencyIndexes: file_api_docs_esign_service_proto_depIdxs,
		MessageInfos:      file_api_docs_esign_service_proto_msgTypes,
	}.Build()
	File_api_docs_esign_service_proto = out.File
	file_api_docs_esign_service_proto_rawDesc = nil
	file_api_docs_esign_service_proto_goTypes = nil
	file_api_docs_esign_service_proto_depIdxs = nil
}
