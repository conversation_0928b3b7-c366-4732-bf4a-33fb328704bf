// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/docs/esign/enums.pb.go

package esign

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the EsignRequestClient in string format in DB
func (p EsignRequestClient) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing EsignRequestClient while reading from DB
func (p *EsignRequestClient) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := EsignRequestClient_value[val]
	if !ok {
		return fmt.Errorf("unexpected EsignRequestClient value: %s", val)
	}
	*p = EsignRequestClient(valInt)
	return nil
}

// Marshaler interface implementation for EsignRequestClient
func (x EsignRequestClient) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for EsignRequestClient
func (x *EsignRequestClient) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = EsignRequestClient(EsignRequestClient_value[val])
	return nil
}

// Valuer interface implementation for storing the EsignRequestStatus in string format in DB
func (p EsignRequestStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing EsignRequestStatus while reading from DB
func (p *EsignRequestStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := EsignRequestStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected EsignRequestStatus value: %s", val)
	}
	*p = EsignRequestStatus(valInt)
	return nil
}

// Marshaler interface implementation for EsignRequestStatus
func (x EsignRequestStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for EsignRequestStatus
func (x *EsignRequestStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = EsignRequestStatus(EsignRequestStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the EsignRequestVendor in string format in DB
func (p EsignRequestVendor) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing EsignRequestVendor while reading from DB
func (p *EsignRequestVendor) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := EsignRequestVendor_value[val]
	if !ok {
		return fmt.Errorf("unexpected EsignRequestVendor value: %s", val)
	}
	*p = EsignRequestVendor(valInt)
	return nil
}

// Marshaler interface implementation for EsignRequestVendor
func (x EsignRequestVendor) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for EsignRequestVendor
func (x *EsignRequestVendor) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = EsignRequestVendor(EsignRequestVendor_value[val])
	return nil
}

// Valuer interface implementation for storing the EsignRequestFieldMask in string format in DB
func (p EsignRequestFieldMask) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing EsignRequestFieldMask while reading from DB
func (p *EsignRequestFieldMask) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := EsignRequestFieldMask_value[val]
	if !ok {
		return fmt.Errorf("unexpected EsignRequestFieldMask value: %s", val)
	}
	*p = EsignRequestFieldMask(valInt)
	return nil
}

// Marshaler interface implementation for EsignRequestFieldMask
func (x EsignRequestFieldMask) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for EsignRequestFieldMask
func (x *EsignRequestFieldMask) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = EsignRequestFieldMask(EsignRequestFieldMask_value[val])
	return nil
}
