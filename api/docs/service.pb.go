// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/docs/service.proto

package docs

import (
	rpc "github.com/epifi/be-common/api/rpc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FileNamePrefix int32

const (
	FileNamePrefix_FILE_NAME_PREFIX_UNSPECIFIED FileNamePrefix = 0
	// file prefix for statement files
	FileNamePrefix_STATEMENT FileNamePrefix = 1
	// file prefix for wealth kra form files
	FileNamePrefix_KRA_FORM FileNamePrefix = 2
	// file prefix for mutual funds nft files.
	FileNamePrefix_MUTUAL_FUNDS_NFT FileNamePrefix = 3
	// file prefix for federal bank A2 Form files
	FileNamePrefix_FEDERAL_BANK_A2_FORM FileNamePrefix = 4
	// file prefix for savings account summary files
	FileNamePrefix_SAVINGS_ACCOUNT_SUMMARY FileNamePrefix = 5
)

// Enum value maps for FileNamePrefix.
var (
	FileNamePrefix_name = map[int32]string{
		0: "FILE_NAME_PREFIX_UNSPECIFIED",
		1: "STATEMENT",
		2: "KRA_FORM",
		3: "MUTUAL_FUNDS_NFT",
		4: "FEDERAL_BANK_A2_FORM",
		5: "SAVINGS_ACCOUNT_SUMMARY",
	}
	FileNamePrefix_value = map[string]int32{
		"FILE_NAME_PREFIX_UNSPECIFIED": 0,
		"STATEMENT":                    1,
		"KRA_FORM":                     2,
		"MUTUAL_FUNDS_NFT":             3,
		"FEDERAL_BANK_A2_FORM":         4,
		"SAVINGS_ACCOUNT_SUMMARY":      5,
	}
)

func (x FileNamePrefix) Enum() *FileNamePrefix {
	p := new(FileNamePrefix)
	*p = x
	return p
}

func (x FileNamePrefix) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileNamePrefix) Descriptor() protoreflect.EnumDescriptor {
	return file_api_docs_service_proto_enumTypes[0].Descriptor()
}

func (FileNamePrefix) Type() protoreflect.EnumType {
	return &file_api_docs_service_proto_enumTypes[0]
}

func (x FileNamePrefix) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileNamePrefix.Descriptor instead.
func (FileNamePrefix) EnumDescriptor() ([]byte, []int) {
	return file_api_docs_service_proto_rawDescGZIP(), []int{0}
}

type GeneratePdfResponse_Status int32

const (
	GeneratePdfResponse_OK GeneratePdfResponse_Status = 0
	// Invalid argument passed in the request.
	// i.e. data passed in request to generate pdf is not as per required by the template.
	GeneratePdfResponse_INVALID_ARGUMENT GeneratePdfResponse_Status = 3
	// internal error
	GeneratePdfResponse_INTERNAL GeneratePdfResponse_Status = 13
)

// Enum value maps for GeneratePdfResponse_Status.
var (
	GeneratePdfResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	GeneratePdfResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x GeneratePdfResponse_Status) Enum() *GeneratePdfResponse_Status {
	p := new(GeneratePdfResponse_Status)
	*p = x
	return p
}

func (x GeneratePdfResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GeneratePdfResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_docs_service_proto_enumTypes[1].Descriptor()
}

func (GeneratePdfResponse_Status) Type() protoreflect.EnumType {
	return &file_api_docs_service_proto_enumTypes[1]
}

func (x GeneratePdfResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GeneratePdfResponse_Status.Descriptor instead.
func (GeneratePdfResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_docs_service_proto_rawDescGZIP(), []int{1, 0}
}

type GeneratePdfRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Template to use for building pdf file.
	PdfTemplate PDFTemplate `protobuf:"varint,1,opt,name=pdf_template,json=pdfTemplate,proto3,enum=docs.PDFTemplate" json:"pdf_template,omitempty"`
	// data for building template. It should be json format bytes of data.
	// If there is no data require for PDF rendering pass bytes of curly braces string i.e. []bytes("{}")
	Data []byte `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	// owner password to protect file from change, copy etc.
	// User will require owner password to make any change in file.
	// If left blank no owner password will be set.
	// Alphanumeric with space not allowed.
	OwnerPassword string `protobuf:"bytes,3,opt,name=owner_password,json=ownerPassword,proto3" json:"owner_password,omitempty"`
	// password to protect file to view.
	// If not provided (i.e. blank string is passed), file will not be password protected.
	// Alphanumeric with space not allowed.
	UserPassword string `protobuf:"bytes,4,opt,name=user_password,json=userPassword,proto3" json:"user_password,omitempty"`
	// time in second after which aws url in response will expire.
	// if no expiry time is provided (i.e. expiry_time = 0) then default expiry time of one week will be set.
	ExpiryTimeInSeconds int64 `protobuf:"varint,5,opt,name=expiry_time_in_seconds,json=expiryTimeInSeconds,proto3" json:"expiry_time_in_seconds,omitempty"`
	// File will be created using this name. This should be unique name for each file generated
	// otherwise it will fail.
	FileName string `protobuf:"bytes,6,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	// File name prefix will be used to categorised files. All file will get created in s3 bucket with this prefix.
	// Ex. for prefix 'statement' -> file will be created with 'statement/fileName.pdf'
	// Based on prefix different deletion policy will be applied.
	// For example: all file with prefix 'statement/somefile.pdf' will get automatically
	// deleted after one day to adhere compliance.
	FileNamePrefix FileNamePrefix `protobuf:"varint,7,opt,name=file_name_prefix,json=fileNamePrefix,proto3,enum=docs.FileNamePrefix" json:"file_name_prefix,omitempty"`
}

func (x *GeneratePdfRequest) Reset() {
	*x = GeneratePdfRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_docs_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeneratePdfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneratePdfRequest) ProtoMessage() {}

func (x *GeneratePdfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_docs_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneratePdfRequest.ProtoReflect.Descriptor instead.
func (*GeneratePdfRequest) Descriptor() ([]byte, []int) {
	return file_api_docs_service_proto_rawDescGZIP(), []int{0}
}

func (x *GeneratePdfRequest) GetPdfTemplate() PDFTemplate {
	if x != nil {
		return x.PdfTemplate
	}
	return PDFTemplate_PDF_TEMPLATE_UNSPECIFIED
}

func (x *GeneratePdfRequest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GeneratePdfRequest) GetOwnerPassword() string {
	if x != nil {
		return x.OwnerPassword
	}
	return ""
}

func (x *GeneratePdfRequest) GetUserPassword() string {
	if x != nil {
		return x.UserPassword
	}
	return ""
}

func (x *GeneratePdfRequest) GetExpiryTimeInSeconds() int64 {
	if x != nil {
		return x.ExpiryTimeInSeconds
	}
	return 0
}

func (x *GeneratePdfRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *GeneratePdfRequest) GetFileNamePrefix() FileNamePrefix {
	if x != nil {
		return x.FileNamePrefix
	}
	return FileNamePrefix_FILE_NAME_PREFIX_UNSPECIFIED
}

type GeneratePdfResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// aws url of generated file.
	FileUrl string `protobuf:"bytes,2,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
}

func (x *GeneratePdfResponse) Reset() {
	*x = GeneratePdfResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_docs_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeneratePdfResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneratePdfResponse) ProtoMessage() {}

func (x *GeneratePdfResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_docs_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneratePdfResponse.ProtoReflect.Descriptor instead.
func (*GeneratePdfResponse) Descriptor() ([]byte, []int) {
	return file_api_docs_service_proto_rawDescGZIP(), []int{1}
}

func (x *GeneratePdfResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GeneratePdfResponse) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

type GeneratePdfWithStreamRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileChunk []byte `protobuf:"bytes,1,opt,name=file_chunk,json=fileChunk,proto3" json:"file_chunk,omitempty"`
}

func (x *GeneratePdfWithStreamRequest) Reset() {
	*x = GeneratePdfWithStreamRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_docs_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeneratePdfWithStreamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneratePdfWithStreamRequest) ProtoMessage() {}

func (x *GeneratePdfWithStreamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_docs_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneratePdfWithStreamRequest.ProtoReflect.Descriptor instead.
func (*GeneratePdfWithStreamRequest) Descriptor() ([]byte, []int) {
	return file_api_docs_service_proto_rawDescGZIP(), []int{2}
}

func (x *GeneratePdfWithStreamRequest) GetFileChunk() []byte {
	if x != nil {
		return x.FileChunk
	}
	return nil
}

var File_api_docs_service_proto protoreflect.FileDescriptor

var file_api_docs_service_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x63, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x64, 0x6f, 0x63, 0x73, 0x1a, 0x14,
	0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x63, 0x73, 0x2f, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbc,
	0x02, 0x0a, 0x12, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50, 0x64, 0x66, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x0c, 0x70, 0x64, 0x66, 0x5f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x64, 0x6f,
	0x63, 0x73, 0x2e, 0x50, 0x44, 0x46, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x0b,
	0x70, 0x64, 0x66, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x25, 0x0a, 0x0e, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x50, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x75,
	0x73, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x33, 0x0a, 0x16, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3e, 0x0a,
	0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69,
	0x78, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x46,
	0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x52, 0x0e, 0x66,
	0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x22, 0x8b, 0x01,
	0x0a, 0x13, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50, 0x64, 0x66, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69,
	0x6c, 0x65, 0x55, 0x72, 0x6c, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x3d, 0x0a, 0x1c, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50, 0x64, 0x66, 0x57, 0x69, 0x74, 0x68, 0x53, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x2a, 0x9c, 0x01, 0x0a, 0x0e, 0x46,
	0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x20, 0x0a,
	0x1c, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x49,
	0x58, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x0c,
	0x0a, 0x08, 0x4b, 0x52, 0x41, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10,
	0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x4e, 0x46, 0x54,
	0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x42, 0x41,
	0x4e, 0x4b, 0x5f, 0x41, 0x32, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x17,
	0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x10, 0x05, 0x32, 0xa8, 0x01, 0x0a, 0x04, 0x44, 0x6f,
	0x63, 0x73, 0x12, 0x44, 0x0a, 0x0b, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50, 0x64,
	0x66, 0x12, 0x18, 0x2e, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x50, 0x64, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x64, 0x6f,
	0x63, 0x73, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50, 0x64, 0x66, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x15, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x50, 0x64, 0x66, 0x57, 0x69, 0x74, 0x68, 0x53, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x12, 0x22, 0x2e, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x50, 0x64, 0x66, 0x57, 0x69, 0x74, 0x68, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x50, 0x64, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x28, 0x01, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x6f, 0x63, 0x73, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x63, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_docs_service_proto_rawDescOnce sync.Once
	file_api_docs_service_proto_rawDescData = file_api_docs_service_proto_rawDesc
)

func file_api_docs_service_proto_rawDescGZIP() []byte {
	file_api_docs_service_proto_rawDescOnce.Do(func() {
		file_api_docs_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_docs_service_proto_rawDescData)
	})
	return file_api_docs_service_proto_rawDescData
}

var file_api_docs_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_docs_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_docs_service_proto_goTypes = []interface{}{
	(FileNamePrefix)(0),                  // 0: docs.FileNamePrefix
	(GeneratePdfResponse_Status)(0),      // 1: docs.GeneratePdfResponse.Status
	(*GeneratePdfRequest)(nil),           // 2: docs.GeneratePdfRequest
	(*GeneratePdfResponse)(nil),          // 3: docs.GeneratePdfResponse
	(*GeneratePdfWithStreamRequest)(nil), // 4: docs.GeneratePdfWithStreamRequest
	(PDFTemplate)(0),                     // 5: docs.PDFTemplate
	(*rpc.Status)(nil),                   // 6: rpc.Status
}
var file_api_docs_service_proto_depIdxs = []int32{
	5, // 0: docs.GeneratePdfRequest.pdf_template:type_name -> docs.PDFTemplate
	0, // 1: docs.GeneratePdfRequest.file_name_prefix:type_name -> docs.FileNamePrefix
	6, // 2: docs.GeneratePdfResponse.status:type_name -> rpc.Status
	2, // 3: docs.Docs.GeneratePdf:input_type -> docs.GeneratePdfRequest
	4, // 4: docs.Docs.GeneratePdfWithStream:input_type -> docs.GeneratePdfWithStreamRequest
	3, // 5: docs.Docs.GeneratePdf:output_type -> docs.GeneratePdfResponse
	3, // 6: docs.Docs.GeneratePdfWithStream:output_type -> docs.GeneratePdfResponse
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_docs_service_proto_init() }
func file_api_docs_service_proto_init() {
	if File_api_docs_service_proto != nil {
		return
	}
	file_api_docs_templates_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_docs_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeneratePdfRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_docs_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeneratePdfResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_docs_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeneratePdfWithStreamRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_docs_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_docs_service_proto_goTypes,
		DependencyIndexes: file_api_docs_service_proto_depIdxs,
		EnumInfos:         file_api_docs_service_proto_enumTypes,
		MessageInfos:      file_api_docs_service_proto_msgTypes,
	}.Build()
	File_api_docs_service_proto = out.File
	file_api_docs_service_proto_rawDesc = nil
	file_api_docs_service_proto_goTypes = nil
	file_api_docs_service_proto_depIdxs = nil
}
