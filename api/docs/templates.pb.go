// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/docs/templates.proto

package docs

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PDFTemplate will hold all template created for generating pdf.
// For each template enum there will be a template file created in comms server (docs service) config/pdf-templates/ folder.
// File name should be <enum in uppercase>.html
type PDFTemplate int32

const (
	PDFTemplate_PDF_TEMPLATE_UNSPECIFIED PDFTemplate = 0
	// Test template to run test for pdf generation
	PDFTemplate_TEST PDFTemplate = 1
	// Savings statement template for pdf generation
	PDFTemplate_SAVINGS_STATEMENT PDFTemplate = 2
	// Wealth template for Kra form pdf generation
	PDFTemplate_WEALTH_KRA PDFTemplate = 3
	// Wealth template for agreement pdf generation
	PDFTemplate_WEALTH_AGREEMENT_SIGNATURE PDFTemplate = 4
	// Wealth template for Aadhaar Card in pdf form generation
	PDFTemplate_AADHAAR_CARD_DIGILOCKER PDFTemplate = 5
	// Wealth template for rendering multiple images as pdf
	PDFTemplate_WEALTH_MULTIPLE_IMAGES_CONVERSION PDFTemplate = 6
	// p2p vendor liquiloans agreement which need to be generated for all the users those are investing
	PDFTemplate_P2P_INVESTMENT_LIQUILOANS_AGREEMENT PDFTemplate = 7
	// Mutual fund related pdf that needs to be generated for the users who wants to change their mobile numbers associated with a folio.
	PDFTemplate_MF_MOBILE_NUMBER_CHANGE_ACKNOWLEDGEMENT PDFTemplate = 8
	// A2 form template for international fund transfer via Federal Bank
	PDFTemplate_INTERNATIONAL_FUND_TRANSFER_FEDERAL_BANK_A2_FORM PDFTemplate = 9
	// Connected Account Statement template for pdf generation of connected account transaction summary
	PDFTemplate_CONNECTED_ACCOUNT_STATEMENT PDFTemplate = 10
	// Credit card statement template for pdf generation
	PDFTemplate_CREDIT_CARD_STATEMENT PDFTemplate = 11
	// Tax Statement template for pdf generation for mutual funds
	PDFTemplate_MF_TAX_STATEMENT PDFTemplate = 12
	// Credit card statement template v2
	PDFTemplate_CREDIT_CARD_STATEMENT_V2 PDFTemplate = 13
	// Deposit statement template for pdf generation
	PDFTemplate_DEPOSIT_STATEMENT PDFTemplate = 14
	// https://drive.google.com/file/d/1J6Ry7ng0n_dU1YM-hojxYv1Nj2x0t6q7/view
	PDFTemplate_P2P_MATURITY_ACTION_CONSENT_LETTER PDFTemplate = 15
	// loan agreement document for IDFC pre-approved loans
	PDFTemplate_PL_IDFC_LOAN_AGREEMENT PDFTemplate = 16
	// loan agreement document for Liquiloans EarlySalary program
	PDFTemplate_PL_LL_ES_LOAN_AGREEMENT PDFTemplate = 17
	// Federal Savings Account summary sent to users after successful account creation.
	PDFTemplate_FEDERAL_SAVINGS_ACCOUNT_SUMMARY PDFTemplate = 18
	// loan agreement document for ABFL pre-approved loans
	PDFTemplate_PL_ABFL_LOAN_AGREEMENT PDFTemplate = 19
	// savings statement template with tiering rewards summary
	PDFTemplate_SAVINGS_STATEMENT_WITH_TIERING_REWARDS_SUMMARY PDFTemplate = 20
)

// Enum value maps for PDFTemplate.
var (
	PDFTemplate_name = map[int32]string{
		0:  "PDF_TEMPLATE_UNSPECIFIED",
		1:  "TEST",
		2:  "SAVINGS_STATEMENT",
		3:  "WEALTH_KRA",
		4:  "WEALTH_AGREEMENT_SIGNATURE",
		5:  "AADHAAR_CARD_DIGILOCKER",
		6:  "WEALTH_MULTIPLE_IMAGES_CONVERSION",
		7:  "P2P_INVESTMENT_LIQUILOANS_AGREEMENT",
		8:  "MF_MOBILE_NUMBER_CHANGE_ACKNOWLEDGEMENT",
		9:  "INTERNATIONAL_FUND_TRANSFER_FEDERAL_BANK_A2_FORM",
		10: "CONNECTED_ACCOUNT_STATEMENT",
		11: "CREDIT_CARD_STATEMENT",
		12: "MF_TAX_STATEMENT",
		13: "CREDIT_CARD_STATEMENT_V2",
		14: "DEPOSIT_STATEMENT",
		15: "P2P_MATURITY_ACTION_CONSENT_LETTER",
		16: "PL_IDFC_LOAN_AGREEMENT",
		17: "PL_LL_ES_LOAN_AGREEMENT",
		18: "FEDERAL_SAVINGS_ACCOUNT_SUMMARY",
		19: "PL_ABFL_LOAN_AGREEMENT",
		20: "SAVINGS_STATEMENT_WITH_TIERING_REWARDS_SUMMARY",
	}
	PDFTemplate_value = map[string]int32{
		"PDF_TEMPLATE_UNSPECIFIED":                         0,
		"TEST":                                             1,
		"SAVINGS_STATEMENT":                                2,
		"WEALTH_KRA":                                       3,
		"WEALTH_AGREEMENT_SIGNATURE":                       4,
		"AADHAAR_CARD_DIGILOCKER":                          5,
		"WEALTH_MULTIPLE_IMAGES_CONVERSION":                6,
		"P2P_INVESTMENT_LIQUILOANS_AGREEMENT":              7,
		"MF_MOBILE_NUMBER_CHANGE_ACKNOWLEDGEMENT":          8,
		"INTERNATIONAL_FUND_TRANSFER_FEDERAL_BANK_A2_FORM": 9,
		"CONNECTED_ACCOUNT_STATEMENT":                      10,
		"CREDIT_CARD_STATEMENT":                            11,
		"MF_TAX_STATEMENT":                                 12,
		"CREDIT_CARD_STATEMENT_V2":                         13,
		"DEPOSIT_STATEMENT":                                14,
		"P2P_MATURITY_ACTION_CONSENT_LETTER":               15,
		"PL_IDFC_LOAN_AGREEMENT":                           16,
		"PL_LL_ES_LOAN_AGREEMENT":                          17,
		"FEDERAL_SAVINGS_ACCOUNT_SUMMARY":                  18,
		"PL_ABFL_LOAN_AGREEMENT":                           19,
		"SAVINGS_STATEMENT_WITH_TIERING_REWARDS_SUMMARY":   20,
	}
)

func (x PDFTemplate) Enum() *PDFTemplate {
	p := new(PDFTemplate)
	*p = x
	return p
}

func (x PDFTemplate) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PDFTemplate) Descriptor() protoreflect.EnumDescriptor {
	return file_api_docs_templates_proto_enumTypes[0].Descriptor()
}

func (PDFTemplate) Type() protoreflect.EnumType {
	return &file_api_docs_templates_proto_enumTypes[0]
}

func (x PDFTemplate) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PDFTemplate.Descriptor instead.
func (PDFTemplate) EnumDescriptor() ([]byte, []int) {
	return file_api_docs_templates_proto_rawDescGZIP(), []int{0}
}

var File_api_docs_templates_proto protoreflect.FileDescriptor

var file_api_docs_templates_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x63, 0x73, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x64, 0x6f, 0x63, 0x73,
	0x2a, 0xa9, 0x05, 0x0a, 0x0b, 0x50, 0x44, 0x46, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x12, 0x1c, 0x0a, 0x18, 0x50, 0x44, 0x46, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08,
	0x0a, 0x04, 0x54, 0x45, 0x53, 0x54, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x41, 0x56, 0x49,
	0x4e, 0x47, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12,
	0x0e, 0x0a, 0x0a, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4b, 0x52, 0x41, 0x10, 0x03, 0x12,
	0x1e, 0x0a, 0x1a, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x41, 0x47, 0x52, 0x45, 0x45, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x54, 0x55, 0x52, 0x45, 0x10, 0x04, 0x12,
	0x1b, 0x0a, 0x17, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x44, 0x49, 0x47, 0x49, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x52, 0x10, 0x05, 0x12, 0x25, 0x0a, 0x21,
	0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c, 0x45, 0x5f,
	0x49, 0x4d, 0x41, 0x47, 0x45, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f,
	0x4e, 0x10, 0x06, 0x12, 0x27, 0x0a, 0x23, 0x50, 0x32, 0x50, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53,
	0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x4c, 0x4f, 0x41, 0x4e, 0x53,
	0x5f, 0x41, 0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x07, 0x12, 0x2b, 0x0a, 0x27,
	0x4d, 0x46, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52,
	0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x41, 0x43, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45,
	0x44, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x08, 0x12, 0x34, 0x0a, 0x30, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c,
	0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x32, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x10, 0x09, 0x12,
	0x1f, 0x0a, 0x1b, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0a,
	0x12, 0x19, 0x0a, 0x15, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0b, 0x12, 0x14, 0x0a, 0x10, 0x4d,
	0x46, 0x5f, 0x54, 0x41, 0x58, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x0c, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x56, 0x32, 0x10, 0x0d, 0x12,
	0x15, 0x0a, 0x11, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0e, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x32, 0x50, 0x5f, 0x4d, 0x41,
	0x54, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f,
	0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x45, 0x54, 0x54, 0x45, 0x52, 0x10, 0x0f, 0x12, 0x1a,
	0x0a, 0x16, 0x50, 0x4c, 0x5f, 0x49, 0x44, 0x46, 0x43, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x10, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x4c,
	0x5f, 0x4c, 0x4c, 0x5f, 0x45, 0x53, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x47, 0x52, 0x45,
	0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x11, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x45, 0x44, 0x45, 0x52,
	0x41, 0x4c, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x10, 0x12, 0x12, 0x1a, 0x0a, 0x16,
	0x50, 0x4c, 0x5f, 0x41, 0x42, 0x46, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x47, 0x52,
	0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x13, 0x12, 0x32, 0x0a, 0x2e, 0x53, 0x41, 0x56, 0x49,
	0x4e, 0x47, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x57, 0x49,
	0x54, 0x48, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52,
	0x44, 0x53, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x10, 0x14, 0x42, 0x42, 0x0a, 0x1f,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x6f, 0x63, 0x73, 0x5a,
	0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x63, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_docs_templates_proto_rawDescOnce sync.Once
	file_api_docs_templates_proto_rawDescData = file_api_docs_templates_proto_rawDesc
)

func file_api_docs_templates_proto_rawDescGZIP() []byte {
	file_api_docs_templates_proto_rawDescOnce.Do(func() {
		file_api_docs_templates_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_docs_templates_proto_rawDescData)
	})
	return file_api_docs_templates_proto_rawDescData
}

var file_api_docs_templates_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_docs_templates_proto_goTypes = []interface{}{
	(PDFTemplate)(0), // 0: docs.PDFTemplate
}
var file_api_docs_templates_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_docs_templates_proto_init() }
func file_api_docs_templates_proto_init() {
	if File_api_docs_templates_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_docs_templates_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_docs_templates_proto_goTypes,
		DependencyIndexes: file_api_docs_templates_proto_depIdxs,
		EnumInfos:         file_api_docs_templates_proto_enumTypes,
	}.Build()
	File_api_docs_templates_proto = out.File
	file_api_docs_templates_proto_rawDesc = nil
	file_api_docs_templates_proto_goTypes = nil
	file_api_docs_templates_proto_depIdxs = nil
}
