// Code generated by MockGen. DO NOT EDIT.
// Source: api/./docs/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	docs "github.com/epifi/gamma/api/docs"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

// MockDocsClient is a mock of DocsClient interface.
type MockDocsClient struct {
	ctrl     *gomock.Controller
	recorder *MockDocsClientMockRecorder
}

// MockDocsClientMockRecorder is the mock recorder for MockDocsClient.
type MockDocsClientMockRecorder struct {
	mock *MockDocsClient
}

// NewMockDocsClient creates a new mock instance.
func NewMockDocsClient(ctrl *gomock.Controller) *MockDocsClient {
	mock := &MockDocsClient{ctrl: ctrl}
	mock.recorder = &MockDocsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDocsClient) EXPECT() *MockDocsClientMockRecorder {
	return m.recorder
}

// GeneratePdf mocks base method.
func (m *MockDocsClient) GeneratePdf(ctx context.Context, in *docs.GeneratePdfRequest, opts ...grpc.CallOption) (*docs.GeneratePdfResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GeneratePdf", varargs...)
	ret0, _ := ret[0].(*docs.GeneratePdfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GeneratePdf indicates an expected call of GeneratePdf.
func (mr *MockDocsClientMockRecorder) GeneratePdf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePdf", reflect.TypeOf((*MockDocsClient)(nil).GeneratePdf), varargs...)
}

// GeneratePdfWithStream mocks base method.
func (m *MockDocsClient) GeneratePdfWithStream(ctx context.Context, opts ...grpc.CallOption) (docs.Docs_GeneratePdfWithStreamClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GeneratePdfWithStream", varargs...)
	ret0, _ := ret[0].(docs.Docs_GeneratePdfWithStreamClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GeneratePdfWithStream indicates an expected call of GeneratePdfWithStream.
func (mr *MockDocsClientMockRecorder) GeneratePdfWithStream(ctx interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePdfWithStream", reflect.TypeOf((*MockDocsClient)(nil).GeneratePdfWithStream), varargs...)
}

// MockDocs_GeneratePdfWithStreamClient is a mock of Docs_GeneratePdfWithStreamClient interface.
type MockDocs_GeneratePdfWithStreamClient struct {
	ctrl     *gomock.Controller
	recorder *MockDocs_GeneratePdfWithStreamClientMockRecorder
}

// MockDocs_GeneratePdfWithStreamClientMockRecorder is the mock recorder for MockDocs_GeneratePdfWithStreamClient.
type MockDocs_GeneratePdfWithStreamClientMockRecorder struct {
	mock *MockDocs_GeneratePdfWithStreamClient
}

// NewMockDocs_GeneratePdfWithStreamClient creates a new mock instance.
func NewMockDocs_GeneratePdfWithStreamClient(ctrl *gomock.Controller) *MockDocs_GeneratePdfWithStreamClient {
	mock := &MockDocs_GeneratePdfWithStreamClient{ctrl: ctrl}
	mock.recorder = &MockDocs_GeneratePdfWithStreamClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDocs_GeneratePdfWithStreamClient) EXPECT() *MockDocs_GeneratePdfWithStreamClientMockRecorder {
	return m.recorder
}

// CloseAndRecv mocks base method.
func (m *MockDocs_GeneratePdfWithStreamClient) CloseAndRecv() (*docs.GeneratePdfResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseAndRecv")
	ret0, _ := ret[0].(*docs.GeneratePdfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CloseAndRecv indicates an expected call of CloseAndRecv.
func (mr *MockDocs_GeneratePdfWithStreamClientMockRecorder) CloseAndRecv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseAndRecv", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamClient)(nil).CloseAndRecv))
}

// CloseSend mocks base method.
func (m *MockDocs_GeneratePdfWithStreamClient) CloseSend() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseSend")
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseSend indicates an expected call of CloseSend.
func (mr *MockDocs_GeneratePdfWithStreamClientMockRecorder) CloseSend() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseSend", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamClient)(nil).CloseSend))
}

// Context mocks base method.
func (m *MockDocs_GeneratePdfWithStreamClient) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockDocs_GeneratePdfWithStreamClientMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamClient)(nil).Context))
}

// Header mocks base method.
func (m *MockDocs_GeneratePdfWithStreamClient) Header() (metadata.MD, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Header")
	ret0, _ := ret[0].(metadata.MD)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Header indicates an expected call of Header.
func (mr *MockDocs_GeneratePdfWithStreamClientMockRecorder) Header() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Header", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamClient)(nil).Header))
}

// RecvMsg mocks base method.
func (m_2 *MockDocs_GeneratePdfWithStreamClient) RecvMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "RecvMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockDocs_GeneratePdfWithStreamClientMockRecorder) RecvMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamClient)(nil).RecvMsg), m)
}

// Send mocks base method.
func (m *MockDocs_GeneratePdfWithStreamClient) Send(arg0 *docs.GeneratePdfWithStreamRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Send indicates an expected call of Send.
func (mr *MockDocs_GeneratePdfWithStreamClientMockRecorder) Send(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamClient)(nil).Send), arg0)
}

// SendMsg mocks base method.
func (m_2 *MockDocs_GeneratePdfWithStreamClient) SendMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "SendMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockDocs_GeneratePdfWithStreamClientMockRecorder) SendMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamClient)(nil).SendMsg), m)
}

// Trailer mocks base method.
func (m *MockDocs_GeneratePdfWithStreamClient) Trailer() metadata.MD {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Trailer")
	ret0, _ := ret[0].(metadata.MD)
	return ret0
}

// Trailer indicates an expected call of Trailer.
func (mr *MockDocs_GeneratePdfWithStreamClientMockRecorder) Trailer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Trailer", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamClient)(nil).Trailer))
}

// MockDocsServer is a mock of DocsServer interface.
type MockDocsServer struct {
	ctrl     *gomock.Controller
	recorder *MockDocsServerMockRecorder
}

// MockDocsServerMockRecorder is the mock recorder for MockDocsServer.
type MockDocsServerMockRecorder struct {
	mock *MockDocsServer
}

// NewMockDocsServer creates a new mock instance.
func NewMockDocsServer(ctrl *gomock.Controller) *MockDocsServer {
	mock := &MockDocsServer{ctrl: ctrl}
	mock.recorder = &MockDocsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDocsServer) EXPECT() *MockDocsServerMockRecorder {
	return m.recorder
}

// GeneratePdf mocks base method.
func (m *MockDocsServer) GeneratePdf(arg0 context.Context, arg1 *docs.GeneratePdfRequest) (*docs.GeneratePdfResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeneratePdf", arg0, arg1)
	ret0, _ := ret[0].(*docs.GeneratePdfResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GeneratePdf indicates an expected call of GeneratePdf.
func (mr *MockDocsServerMockRecorder) GeneratePdf(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePdf", reflect.TypeOf((*MockDocsServer)(nil).GeneratePdf), arg0, arg1)
}

// GeneratePdfWithStream mocks base method.
func (m *MockDocsServer) GeneratePdfWithStream(arg0 docs.Docs_GeneratePdfWithStreamServer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeneratePdfWithStream", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// GeneratePdfWithStream indicates an expected call of GeneratePdfWithStream.
func (mr *MockDocsServerMockRecorder) GeneratePdfWithStream(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePdfWithStream", reflect.TypeOf((*MockDocsServer)(nil).GeneratePdfWithStream), arg0)
}

// MockUnsafeDocsServer is a mock of UnsafeDocsServer interface.
type MockUnsafeDocsServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeDocsServerMockRecorder
}

// MockUnsafeDocsServerMockRecorder is the mock recorder for MockUnsafeDocsServer.
type MockUnsafeDocsServerMockRecorder struct {
	mock *MockUnsafeDocsServer
}

// NewMockUnsafeDocsServer creates a new mock instance.
func NewMockUnsafeDocsServer(ctrl *gomock.Controller) *MockUnsafeDocsServer {
	mock := &MockUnsafeDocsServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeDocsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeDocsServer) EXPECT() *MockUnsafeDocsServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedDocsServer mocks base method.
func (m *MockUnsafeDocsServer) mustEmbedUnimplementedDocsServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedDocsServer")
}

// mustEmbedUnimplementedDocsServer indicates an expected call of mustEmbedUnimplementedDocsServer.
func (mr *MockUnsafeDocsServerMockRecorder) mustEmbedUnimplementedDocsServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedDocsServer", reflect.TypeOf((*MockUnsafeDocsServer)(nil).mustEmbedUnimplementedDocsServer))
}

// MockDocs_GeneratePdfWithStreamServer is a mock of Docs_GeneratePdfWithStreamServer interface.
type MockDocs_GeneratePdfWithStreamServer struct {
	ctrl     *gomock.Controller
	recorder *MockDocs_GeneratePdfWithStreamServerMockRecorder
}

// MockDocs_GeneratePdfWithStreamServerMockRecorder is the mock recorder for MockDocs_GeneratePdfWithStreamServer.
type MockDocs_GeneratePdfWithStreamServerMockRecorder struct {
	mock *MockDocs_GeneratePdfWithStreamServer
}

// NewMockDocs_GeneratePdfWithStreamServer creates a new mock instance.
func NewMockDocs_GeneratePdfWithStreamServer(ctrl *gomock.Controller) *MockDocs_GeneratePdfWithStreamServer {
	mock := &MockDocs_GeneratePdfWithStreamServer{ctrl: ctrl}
	mock.recorder = &MockDocs_GeneratePdfWithStreamServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDocs_GeneratePdfWithStreamServer) EXPECT() *MockDocs_GeneratePdfWithStreamServerMockRecorder {
	return m.recorder
}

// Context mocks base method.
func (m *MockDocs_GeneratePdfWithStreamServer) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockDocs_GeneratePdfWithStreamServerMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamServer)(nil).Context))
}

// Recv mocks base method.
func (m *MockDocs_GeneratePdfWithStreamServer) Recv() (*docs.GeneratePdfWithStreamRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Recv")
	ret0, _ := ret[0].(*docs.GeneratePdfWithStreamRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Recv indicates an expected call of Recv.
func (mr *MockDocs_GeneratePdfWithStreamServerMockRecorder) Recv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Recv", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamServer)(nil).Recv))
}

// RecvMsg mocks base method.
func (m_2 *MockDocs_GeneratePdfWithStreamServer) RecvMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "RecvMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockDocs_GeneratePdfWithStreamServerMockRecorder) RecvMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamServer)(nil).RecvMsg), m)
}

// SendAndClose mocks base method.
func (m *MockDocs_GeneratePdfWithStreamServer) SendAndClose(arg0 *docs.GeneratePdfResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendAndClose", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendAndClose indicates an expected call of SendAndClose.
func (mr *MockDocs_GeneratePdfWithStreamServerMockRecorder) SendAndClose(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAndClose", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamServer)(nil).SendAndClose), arg0)
}

// SendHeader mocks base method.
func (m *MockDocs_GeneratePdfWithStreamServer) SendHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendHeader indicates an expected call of SendHeader.
func (mr *MockDocs_GeneratePdfWithStreamServerMockRecorder) SendHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendHeader", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamServer)(nil).SendHeader), arg0)
}

// SendMsg mocks base method.
func (m_2 *MockDocs_GeneratePdfWithStreamServer) SendMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "SendMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockDocs_GeneratePdfWithStreamServerMockRecorder) SendMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamServer)(nil).SendMsg), m)
}

// SetHeader mocks base method.
func (m *MockDocs_GeneratePdfWithStreamServer) SetHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetHeader indicates an expected call of SetHeader.
func (mr *MockDocs_GeneratePdfWithStreamServerMockRecorder) SetHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHeader", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamServer)(nil).SetHeader), arg0)
}

// SetTrailer mocks base method.
func (m *MockDocs_GeneratePdfWithStreamServer) SetTrailer(arg0 metadata.MD) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetTrailer", arg0)
}

// SetTrailer indicates an expected call of SetTrailer.
func (mr *MockDocs_GeneratePdfWithStreamServerMockRecorder) SetTrailer(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTrailer", reflect.TypeOf((*MockDocs_GeneratePdfWithStreamServer)(nil).SetTrailer), arg0)
}
