// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/docs/service.proto

package docs

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Docs_GeneratePdf_FullMethodName           = "/docs.Docs/GeneratePdf"
	Docs_GeneratePdfWithStream_FullMethodName = "/docs.Docs/GeneratePdfWithStream"
)

// DocsClient is the client API for Docs service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DocsClient interface {
	// GeneratePdf RPC will generate pdf file for requested template and data.
	// It will return aws url of the file with expiry time if provided.
	// Requested file will be password protected if user password is provided in request
	// otherwise file will not protected by password.
	GeneratePdf(ctx context.Context, in *GeneratePdfRequest, opts ...grpc.CallOption) (*GeneratePdfResponse, error)
	// By use of GeneratePdfWithStream rpc we can generate pdf for more then 4MB data
	// here we send request into streaming format
	GeneratePdfWithStream(ctx context.Context, opts ...grpc.CallOption) (Docs_GeneratePdfWithStreamClient, error)
}

type docsClient struct {
	cc grpc.ClientConnInterface
}

func NewDocsClient(cc grpc.ClientConnInterface) DocsClient {
	return &docsClient{cc}
}

func (c *docsClient) GeneratePdf(ctx context.Context, in *GeneratePdfRequest, opts ...grpc.CallOption) (*GeneratePdfResponse, error) {
	out := new(GeneratePdfResponse)
	err := c.cc.Invoke(ctx, Docs_GeneratePdf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *docsClient) GeneratePdfWithStream(ctx context.Context, opts ...grpc.CallOption) (Docs_GeneratePdfWithStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &Docs_ServiceDesc.Streams[0], Docs_GeneratePdfWithStream_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &docsGeneratePdfWithStreamClient{stream}
	return x, nil
}

type Docs_GeneratePdfWithStreamClient interface {
	Send(*GeneratePdfWithStreamRequest) error
	CloseAndRecv() (*GeneratePdfResponse, error)
	grpc.ClientStream
}

type docsGeneratePdfWithStreamClient struct {
	grpc.ClientStream
}

func (x *docsGeneratePdfWithStreamClient) Send(m *GeneratePdfWithStreamRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *docsGeneratePdfWithStreamClient) CloseAndRecv() (*GeneratePdfResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(GeneratePdfResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// DocsServer is the server API for Docs service.
// All implementations should embed UnimplementedDocsServer
// for forward compatibility
type DocsServer interface {
	// GeneratePdf RPC will generate pdf file for requested template and data.
	// It will return aws url of the file with expiry time if provided.
	// Requested file will be password protected if user password is provided in request
	// otherwise file will not protected by password.
	GeneratePdf(context.Context, *GeneratePdfRequest) (*GeneratePdfResponse, error)
	// By use of GeneratePdfWithStream rpc we can generate pdf for more then 4MB data
	// here we send request into streaming format
	GeneratePdfWithStream(Docs_GeneratePdfWithStreamServer) error
}

// UnimplementedDocsServer should be embedded to have forward compatible implementations.
type UnimplementedDocsServer struct {
}

func (UnimplementedDocsServer) GeneratePdf(context.Context, *GeneratePdfRequest) (*GeneratePdfResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GeneratePdf not implemented")
}
func (UnimplementedDocsServer) GeneratePdfWithStream(Docs_GeneratePdfWithStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method GeneratePdfWithStream not implemented")
}

// UnsafeDocsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DocsServer will
// result in compilation errors.
type UnsafeDocsServer interface {
	mustEmbedUnimplementedDocsServer()
}

func RegisterDocsServer(s grpc.ServiceRegistrar, srv DocsServer) {
	s.RegisterService(&Docs_ServiceDesc, srv)
}

func _Docs_GeneratePdf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GeneratePdfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DocsServer).GeneratePdf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Docs_GeneratePdf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DocsServer).GeneratePdf(ctx, req.(*GeneratePdfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Docs_GeneratePdfWithStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(DocsServer).GeneratePdfWithStream(&docsGeneratePdfWithStreamServer{stream})
}

type Docs_GeneratePdfWithStreamServer interface {
	SendAndClose(*GeneratePdfResponse) error
	Recv() (*GeneratePdfWithStreamRequest, error)
	grpc.ServerStream
}

type docsGeneratePdfWithStreamServer struct {
	grpc.ServerStream
}

func (x *docsGeneratePdfWithStreamServer) SendAndClose(m *GeneratePdfResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *docsGeneratePdfWithStreamServer) Recv() (*GeneratePdfWithStreamRequest, error) {
	m := new(GeneratePdfWithStreamRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// Docs_ServiceDesc is the grpc.ServiceDesc for Docs service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Docs_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "docs.Docs",
	HandlerType: (*DocsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GeneratePdf",
			Handler:    _Docs_GeneratePdf_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GeneratePdfWithStream",
			Handler:       _Docs_GeneratePdfWithStream_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "api/docs/service.proto",
}
