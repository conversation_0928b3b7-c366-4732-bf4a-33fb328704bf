// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/docs/upload/enums/enums.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UploadFileFlow int32

const (
	UploadFileFlow_UPLOAD_FILE_FLOW_UNSPECIFIED             UploadFileFlow = 0
	UploadFileFlow_UPLOAD_FILE_FLOW_EPAN                    UploadFileFlow = 1
	UploadFileFlow_UPLOAD_FILE_FLOW_SCREENER_ITR_INTIMATION UploadFileFlow = 2
	UploadFileFlow_UPLOAD_FILE_FLOW_LENDING_ITR_INTIMATION  UploadFileFlow = 3
)

// Enum value maps for UploadFileFlow.
var (
	UploadFileFlow_name = map[int32]string{
		0: "UPLOAD_FILE_FLOW_UNSPECIFIED",
		1: "UPLOAD_FILE_FLOW_EPAN",
		2: "UPLOAD_FILE_FLOW_SCREENER_ITR_INTIMATION",
		3: "UPLOAD_FILE_FLOW_LENDING_ITR_INTIMATION",
	}
	UploadFileFlow_value = map[string]int32{
		"UPLOAD_FILE_FLOW_UNSPECIFIED":             0,
		"UPLOAD_FILE_FLOW_EPAN":                    1,
		"UPLOAD_FILE_FLOW_SCREENER_ITR_INTIMATION": 2,
		"UPLOAD_FILE_FLOW_LENDING_ITR_INTIMATION":  3,
	}
)

func (x UploadFileFlow) Enum() *UploadFileFlow {
	p := new(UploadFileFlow)
	*p = x
	return p
}

func (x UploadFileFlow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UploadFileFlow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_docs_upload_enums_enums_proto_enumTypes[0].Descriptor()
}

func (UploadFileFlow) Type() protoreflect.EnumType {
	return &file_api_docs_upload_enums_enums_proto_enumTypes[0]
}

func (x UploadFileFlow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UploadFileFlow.Descriptor instead.
func (UploadFileFlow) EnumDescriptor() ([]byte, []int) {
	return file_api_docs_upload_enums_enums_proto_rawDescGZIP(), []int{0}
}

var File_api_docs_upload_enums_enums_proto protoreflect.FileDescriptor

var file_api_docs_upload_enums_enums_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x63, 0x73, 0x2f, 0x75, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x11, 0x64, 0x6f, 0x63, 0x73, 0x2e, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a, 0xa8, 0x01, 0x0a, 0x0e, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x46, 0x69, 0x6c, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x20, 0x0a, 0x1c, 0x55, 0x50, 0x4c,
	0x4f, 0x41, 0x44, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x55,
	0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x45, 0x50, 0x41, 0x4e, 0x10, 0x01, 0x12, 0x2c, 0x0a, 0x28, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x45, 0x52, 0x5f, 0x49, 0x54, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x02, 0x12, 0x2b, 0x0a, 0x27, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x49, 0x54, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x03, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x6f, 0x63, 0x73, 0x2e, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x6f,
	0x63, 0x73, 0x2f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_docs_upload_enums_enums_proto_rawDescOnce sync.Once
	file_api_docs_upload_enums_enums_proto_rawDescData = file_api_docs_upload_enums_enums_proto_rawDesc
)

func file_api_docs_upload_enums_enums_proto_rawDescGZIP() []byte {
	file_api_docs_upload_enums_enums_proto_rawDescOnce.Do(func() {
		file_api_docs_upload_enums_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_docs_upload_enums_enums_proto_rawDescData)
	})
	return file_api_docs_upload_enums_enums_proto_rawDescData
}

var file_api_docs_upload_enums_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_docs_upload_enums_enums_proto_goTypes = []interface{}{
	(UploadFileFlow)(0), // 0: docs.upload.enums.UploadFileFlow
}
var file_api_docs_upload_enums_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_docs_upload_enums_enums_proto_init() }
func file_api_docs_upload_enums_enums_proto_init() {
	if File_api_docs_upload_enums_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_docs_upload_enums_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_docs_upload_enums_enums_proto_goTypes,
		DependencyIndexes: file_api_docs_upload_enums_enums_proto_depIdxs,
		EnumInfos:         file_api_docs_upload_enums_enums_proto_enumTypes,
	}.Build()
	File_api_docs_upload_enums_enums_proto = out.File
	file_api_docs_upload_enums_enums_proto_rawDesc = nil
	file_api_docs_upload_enums_enums_proto_goTypes = nil
	file_api_docs_upload_enums_enums_proto_depIdxs = nil
}
