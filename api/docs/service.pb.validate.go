// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/docs/service.proto

package docs

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GeneratePdfRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GeneratePdfRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GeneratePdfRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GeneratePdfRequestMultiError, or nil if none found.
func (m *GeneratePdfRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GeneratePdfRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PdfTemplate

	// no validation rules for Data

	// no validation rules for OwnerPassword

	// no validation rules for UserPassword

	// no validation rules for ExpiryTimeInSeconds

	// no validation rules for FileName

	// no validation rules for FileNamePrefix

	if len(errors) > 0 {
		return GeneratePdfRequestMultiError(errors)
	}

	return nil
}

// GeneratePdfRequestMultiError is an error wrapping multiple validation errors
// returned by GeneratePdfRequest.ValidateAll() if the designated constraints
// aren't met.
type GeneratePdfRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GeneratePdfRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GeneratePdfRequestMultiError) AllErrors() []error { return m }

// GeneratePdfRequestValidationError is the validation error returned by
// GeneratePdfRequest.Validate if the designated constraints aren't met.
type GeneratePdfRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GeneratePdfRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GeneratePdfRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GeneratePdfRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GeneratePdfRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GeneratePdfRequestValidationError) ErrorName() string {
	return "GeneratePdfRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GeneratePdfRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGeneratePdfRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GeneratePdfRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GeneratePdfRequestValidationError{}

// Validate checks the field values on GeneratePdfResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GeneratePdfResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GeneratePdfResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GeneratePdfResponseMultiError, or nil if none found.
func (m *GeneratePdfResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GeneratePdfResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GeneratePdfResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GeneratePdfResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GeneratePdfResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FileUrl

	if len(errors) > 0 {
		return GeneratePdfResponseMultiError(errors)
	}

	return nil
}

// GeneratePdfResponseMultiError is an error wrapping multiple validation
// errors returned by GeneratePdfResponse.ValidateAll() if the designated
// constraints aren't met.
type GeneratePdfResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GeneratePdfResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GeneratePdfResponseMultiError) AllErrors() []error { return m }

// GeneratePdfResponseValidationError is the validation error returned by
// GeneratePdfResponse.Validate if the designated constraints aren't met.
type GeneratePdfResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GeneratePdfResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GeneratePdfResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GeneratePdfResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GeneratePdfResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GeneratePdfResponseValidationError) ErrorName() string {
	return "GeneratePdfResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GeneratePdfResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGeneratePdfResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GeneratePdfResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GeneratePdfResponseValidationError{}

// Validate checks the field values on GeneratePdfWithStreamRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GeneratePdfWithStreamRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GeneratePdfWithStreamRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GeneratePdfWithStreamRequestMultiError, or nil if none found.
func (m *GeneratePdfWithStreamRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GeneratePdfWithStreamRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileChunk

	if len(errors) > 0 {
		return GeneratePdfWithStreamRequestMultiError(errors)
	}

	return nil
}

// GeneratePdfWithStreamRequestMultiError is an error wrapping multiple
// validation errors returned by GeneratePdfWithStreamRequest.ValidateAll() if
// the designated constraints aren't met.
type GeneratePdfWithStreamRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GeneratePdfWithStreamRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GeneratePdfWithStreamRequestMultiError) AllErrors() []error { return m }

// GeneratePdfWithStreamRequestValidationError is the validation error returned
// by GeneratePdfWithStreamRequest.Validate if the designated constraints
// aren't met.
type GeneratePdfWithStreamRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GeneratePdfWithStreamRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GeneratePdfWithStreamRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GeneratePdfWithStreamRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GeneratePdfWithStreamRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GeneratePdfWithStreamRequestValidationError) ErrorName() string {
	return "GeneratePdfWithStreamRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GeneratePdfWithStreamRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGeneratePdfWithStreamRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GeneratePdfWithStreamRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GeneratePdfWithStreamRequestValidationError{}
