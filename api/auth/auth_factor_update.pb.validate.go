// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/auth_factor_update.proto

package auth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	afu "github.com/epifi/gamma/api/auth/afu"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = afu.FailureReason(0)
)

// Validate checks the field values on AuthFactorUpdateNextActionDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *AuthFactorUpdateNextActionDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthFactorUpdateNextActionDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AuthFactorUpdateNextActionDetailsMultiError, or nil if none found.
func (m *AuthFactorUpdateNextActionDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthFactorUpdateNextActionDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NextAction

	// no validation rules for FailureReason

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthFactorUpdateNextActionDetailsValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthFactorUpdateNextActionDetailsValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthFactorUpdateNextActionDetailsValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthFactorUpdateNextActionDetailsMultiError(errors)
	}

	return nil
}

// AuthFactorUpdateNextActionDetailsMultiError is an error wrapping multiple
// validation errors returned by
// AuthFactorUpdateNextActionDetails.ValidateAll() if the designated
// constraints aren't met.
type AuthFactorUpdateNextActionDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthFactorUpdateNextActionDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthFactorUpdateNextActionDetailsMultiError) AllErrors() []error { return m }

// AuthFactorUpdateNextActionDetailsValidationError is the validation error
// returned by AuthFactorUpdateNextActionDetails.Validate if the designated
// constraints aren't met.
type AuthFactorUpdateNextActionDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthFactorUpdateNextActionDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthFactorUpdateNextActionDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthFactorUpdateNextActionDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthFactorUpdateNextActionDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthFactorUpdateNextActionDetailsValidationError) ErrorName() string {
	return "AuthFactorUpdateNextActionDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AuthFactorUpdateNextActionDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthFactorUpdateNextActionDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthFactorUpdateNextActionDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthFactorUpdateNextActionDetailsValidationError{}
