// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/developer/developer.proto

package developer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthEntity int32

const (
	AuthEntity_AUTH_ENTITY_UNSPECIFIED AuthEntity = 0
	AuthEntity_AUTH_FACTOR_UPDATE      AuthEntity = 1
	AuthEntity_TOKEN_STORE             AuthEntity = 2
	AuthEntity_OTP                     AuthEntity = 3
	// Returns token value for phone number or device id.
	// Since, the PII values can't be logged directly, their token values are logged.
	AuthEntity_PII_TOKEN_FOR_LOGS_SEARCH AuthEntity = 4
	// Returns device registration by actor_id or device_id
	AuthEntity_DEVICE_REGISTRATIONS AuthEntity = 5
	// Returns Auth attempts by Phone Number
	AuthEntity_AUTH_ATTEMPTS AuthEntity = 6
	// returns biometric data by actor id
	AuthEntity_BIOMETRICS AuthEntity = 7
	// Returns oauth signup metrics using user's email
	AuthEntity_OAUTH_SIGNUP_DATA AuthEntity = 8
	// Returns oauth device integrity attestation data
	AuthEntity_IOS_DEVICE_ATTESTATION_DATA AuthEntity = 9
	AuthEntity_DEVICE_INTEGRITY_RESULTS    AuthEntity = 10
)

// Enum value maps for AuthEntity.
var (
	AuthEntity_name = map[int32]string{
		0:  "AUTH_ENTITY_UNSPECIFIED",
		1:  "AUTH_FACTOR_UPDATE",
		2:  "TOKEN_STORE",
		3:  "OTP",
		4:  "PII_TOKEN_FOR_LOGS_SEARCH",
		5:  "DEVICE_REGISTRATIONS",
		6:  "AUTH_ATTEMPTS",
		7:  "BIOMETRICS",
		8:  "OAUTH_SIGNUP_DATA",
		9:  "IOS_DEVICE_ATTESTATION_DATA",
		10: "DEVICE_INTEGRITY_RESULTS",
	}
	AuthEntity_value = map[string]int32{
		"AUTH_ENTITY_UNSPECIFIED":     0,
		"AUTH_FACTOR_UPDATE":          1,
		"TOKEN_STORE":                 2,
		"OTP":                         3,
		"PII_TOKEN_FOR_LOGS_SEARCH":   4,
		"DEVICE_REGISTRATIONS":        5,
		"AUTH_ATTEMPTS":               6,
		"BIOMETRICS":                  7,
		"OAUTH_SIGNUP_DATA":           8,
		"IOS_DEVICE_ATTESTATION_DATA": 9,
		"DEVICE_INTEGRITY_RESULTS":    10,
	}
)

func (x AuthEntity) Enum() *AuthEntity {
	p := new(AuthEntity)
	*p = x
	return p
}

func (x AuthEntity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthEntity) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_developer_developer_proto_enumTypes[0].Descriptor()
}

func (AuthEntity) Type() protoreflect.EnumType {
	return &file_api_auth_developer_developer_proto_enumTypes[0]
}

func (x AuthEntity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthEntity.Descriptor instead.
func (AuthEntity) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_developer_developer_proto_rawDescGZIP(), []int{0}
}

var File_api_auth_developer_developer_proto protoreflect.FileDescriptor

var file_api_auth_developer_developer_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x2a, 0x8d, 0x02, 0x0a, 0x0a, 0x41, 0x75, 0x74, 0x68, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x45, 0x4e, 0x54, 0x49,
	0x54, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x16, 0x0a, 0x12, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x4f, 0x4b, 0x45,
	0x4e, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x4f, 0x54, 0x50,
	0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x49, 0x49, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x4c, 0x4f, 0x47, 0x53, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x10,
	0x04, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49,
	0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x53, 0x10, 0x06, 0x12, 0x0e,
	0x0a, 0x0a, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x53, 0x10, 0x07, 0x12, 0x15,
	0x0a, 0x11, 0x4f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x55, 0x50, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x10, 0x08, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x4f, 0x53, 0x5f, 0x44, 0x45, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x53, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x44, 0x41, 0x54, 0x41, 0x10, 0x09, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c,
	0x54, 0x53, 0x10, 0x0a, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x72, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75,
	0x74, 0x68, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_developer_developer_proto_rawDescOnce sync.Once
	file_api_auth_developer_developer_proto_rawDescData = file_api_auth_developer_developer_proto_rawDesc
)

func file_api_auth_developer_developer_proto_rawDescGZIP() []byte {
	file_api_auth_developer_developer_proto_rawDescOnce.Do(func() {
		file_api_auth_developer_developer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_developer_developer_proto_rawDescData)
	})
	return file_api_auth_developer_developer_proto_rawDescData
}

var file_api_auth_developer_developer_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_developer_developer_proto_goTypes = []interface{}{
	(AuthEntity)(0), // 0: auth.developer.AuthEntity
}
var file_api_auth_developer_developer_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_auth_developer_developer_proto_init() }
func file_api_auth_developer_developer_proto_init() {
	if File_api_auth_developer_developer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_developer_developer_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_developer_developer_proto_goTypes,
		DependencyIndexes: file_api_auth_developer_developer_proto_depIdxs,
		EnumInfos:         file_api_auth_developer_developer_proto_enumTypes,
	}.Build()
	File_api_auth_developer_developer_proto = out.File
	file_api_auth_developer_developer_proto_rawDesc = nil
	file_api_auth_developer_developer_proto_goTypes = nil
	file_api_auth_developer_developer_proto_depIdxs = nil
}
