// Code generated by MockGen. DO NOT EDIT.
// Source: api/auth/developer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockDevAuthClient is a mock of DevAuthClient interface.
type MockDevAuthClient struct {
	ctrl     *gomock.Controller
	recorder *MockDevAuthClientMockRecorder
}

// MockDevAuthClientMockRecorder is the mock recorder for MockDevAuthClient.
type MockDevAuthClientMockRecorder struct {
	mock *MockDevAuthClient
}

// NewMockDevAuthClient creates a new mock instance.
func NewMockDevAuthClient(ctrl *gomock.Controller) *MockDevAuthClient {
	mock := &MockDevAuthClient{ctrl: ctrl}
	mock.recorder = &MockDevAuthClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevAuthClient) EXPECT() *MockDevAuthClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDevAuthClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDevAuthClientMockRecorder) GetData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDevAuthClient)(nil).GetData), varargs...)
}

// GetEntityList mocks base method.
func (m *MockDevAuthClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntityList", varargs...)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDevAuthClientMockRecorder) GetEntityList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDevAuthClient)(nil).GetEntityList), varargs...)
}

// GetParameterList mocks base method.
func (m *MockDevAuthClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParameterList", varargs...)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDevAuthClientMockRecorder) GetParameterList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDevAuthClient)(nil).GetParameterList), varargs...)
}

// MockDevAuthServer is a mock of DevAuthServer interface.
type MockDevAuthServer struct {
	ctrl     *gomock.Controller
	recorder *MockDevAuthServerMockRecorder
}

// MockDevAuthServerMockRecorder is the mock recorder for MockDevAuthServer.
type MockDevAuthServerMockRecorder struct {
	mock *MockDevAuthServer
}

// NewMockDevAuthServer creates a new mock instance.
func NewMockDevAuthServer(ctrl *gomock.Controller) *MockDevAuthServer {
	mock := &MockDevAuthServer{ctrl: ctrl}
	mock.recorder = &MockDevAuthServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevAuthServer) EXPECT() *MockDevAuthServerMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDevAuthServer) GetData(arg0 context.Context, arg1 *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDevAuthServerMockRecorder) GetData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDevAuthServer)(nil).GetData), arg0, arg1)
}

// GetEntityList mocks base method.
func (m *MockDevAuthServer) GetEntityList(arg0 context.Context, arg1 *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDevAuthServerMockRecorder) GetEntityList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDevAuthServer)(nil).GetEntityList), arg0, arg1)
}

// GetParameterList mocks base method.
func (m *MockDevAuthServer) GetParameterList(arg0 context.Context, arg1 *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameterList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDevAuthServerMockRecorder) GetParameterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDevAuthServer)(nil).GetParameterList), arg0, arg1)
}

// MockUnsafeDevAuthServer is a mock of UnsafeDevAuthServer interface.
type MockUnsafeDevAuthServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeDevAuthServerMockRecorder
}

// MockUnsafeDevAuthServerMockRecorder is the mock recorder for MockUnsafeDevAuthServer.
type MockUnsafeDevAuthServerMockRecorder struct {
	mock *MockUnsafeDevAuthServer
}

// NewMockUnsafeDevAuthServer creates a new mock instance.
func NewMockUnsafeDevAuthServer(ctrl *gomock.Controller) *MockUnsafeDevAuthServer {
	mock := &MockUnsafeDevAuthServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeDevAuthServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeDevAuthServer) EXPECT() *MockUnsafeDevAuthServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedDevAuthServer mocks base method.
func (m *MockUnsafeDevAuthServer) mustEmbedUnimplementedDevAuthServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedDevAuthServer")
}

// mustEmbedUnimplementedDevAuthServer indicates an expected call of mustEmbedUnimplementedDevAuthServer.
func (mr *MockUnsafeDevAuthServerMockRecorder) mustEmbedUnimplementedDevAuthServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedDevAuthServer", reflect.TypeOf((*MockUnsafeDevAuthServer)(nil).mustEmbedUnimplementedDevAuthServer))
}
