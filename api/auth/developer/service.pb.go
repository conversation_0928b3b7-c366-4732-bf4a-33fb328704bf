// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/developer/service.proto

package developer

import (
	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_api_auth_developer_service_proto protoreflect.FileDescriptor

var file_api_auth_developer_service_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x2f, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2f, 0x64, 0x62,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xca, 0x02, 0x0a,
	0x07, 0x44, 0x65, 0x76, 0x41, 0x75, 0x74, 0x68, 0x12, 0x6c, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2b, 0x2e, 0x63, 0x78, 0x2e, 0x64,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76, 0x65,
	0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x75, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2e, 0x2e, 0x63, 0x78, 0x2e,
	0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x63, 0x78, 0x2e,
	0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a,
	0x07, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x26, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64,
	0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x64, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_api_auth_developer_service_proto_goTypes = []interface{}{
	(*db_state.GetEntityListRequest)(nil),     // 0: cx.developer.db_state.GetEntityListRequest
	(*db_state.GetParameterListRequest)(nil),  // 1: cx.developer.db_state.GetParameterListRequest
	(*db_state.GetDataRequest)(nil),           // 2: cx.developer.db_state.GetDataRequest
	(*db_state.GetEntityListResponse)(nil),    // 3: cx.developer.db_state.GetEntityListResponse
	(*db_state.GetParameterListResponse)(nil), // 4: cx.developer.db_state.GetParameterListResponse
	(*db_state.GetDataResponse)(nil),          // 5: cx.developer.db_state.GetDataResponse
}
var file_api_auth_developer_service_proto_depIdxs = []int32{
	0, // 0: auth.developer.DevAuth.GetEntityList:input_type -> cx.developer.db_state.GetEntityListRequest
	1, // 1: auth.developer.DevAuth.GetParameterList:input_type -> cx.developer.db_state.GetParameterListRequest
	2, // 2: auth.developer.DevAuth.GetData:input_type -> cx.developer.db_state.GetDataRequest
	3, // 3: auth.developer.DevAuth.GetEntityList:output_type -> cx.developer.db_state.GetEntityListResponse
	4, // 4: auth.developer.DevAuth.GetParameterList:output_type -> cx.developer.db_state.GetParameterListResponse
	5, // 5: auth.developer.DevAuth.GetData:output_type -> cx.developer.db_state.GetDataResponse
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_auth_developer_service_proto_init() }
func file_api_auth_developer_service_proto_init() {
	if File_api_auth_developer_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_developer_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_auth_developer_service_proto_goTypes,
		DependencyIndexes: file_api_auth_developer_service_proto_depIdxs,
	}.Build()
	File_api_auth_developer_service_proto = out.File
	file_api_auth_developer_service_proto_rawDesc = nil
	file_api_auth_developer_service_proto_goTypes = nil
	file_api_auth_developer_service_proto_depIdxs = nil
}
