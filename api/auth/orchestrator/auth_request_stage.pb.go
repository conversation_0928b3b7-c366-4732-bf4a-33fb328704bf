//go:generate gen_sql -types=AuthRequestStageStatus,AuthStage

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/orchestrator/internal/auth_request_stage.proto

package orchestrator

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthRequestStageFieldMask int32

const (
	AuthRequestStageFieldMask_AUTH_REQUEST_STAGE_FIELD_MASK_UNSPECIFIED     AuthRequestStageFieldMask = 0
	AuthRequestStageFieldMask_AUTH_REQUEST_STAGE_FIELD_MASK_ID              AuthRequestStageFieldMask = 1
	AuthRequestStageFieldMask_AUTH_REQUEST_STAGE_FIELD_MASK_AUTH_REQUEST_ID AuthRequestStageFieldMask = 2
	AuthRequestStageFieldMask_AUTH_REQUEST_STAGE_FIELD_MASK_AUTH_REF_ID     AuthRequestStageFieldMask = 3
	AuthRequestStageFieldMask_AUTH_REQUEST_STAGE_FIELD_MASK_AUTH_STAGE      AuthRequestStageFieldMask = 4
	AuthRequestStageFieldMask_AUTH_REQUEST_STAGE_FIELD_MASK_STATUS          AuthRequestStageFieldMask = 5
	AuthRequestStageFieldMask_AUTH_REQUEST_STAGE_FIELD_MASK_STALED_AT       AuthRequestStageFieldMask = 6
	AuthRequestStageFieldMask_AUTH_REQUEST_STAGE_FIELD_MASK_COMPLETED_AT    AuthRequestStageFieldMask = 7
	AuthRequestStageFieldMask_AUTH_REQUEST_STAGE_FIELD_MASK_CREATED_AT      AuthRequestStageFieldMask = 8
	AuthRequestStageFieldMask_AUTH_REQUEST_STAGE_FIELD_MASK_UPDATED_AT      AuthRequestStageFieldMask = 9
	AuthRequestStageFieldMask_AUTH_REQUEST_STAGE_FIELD_MASK_DELETED_AT      AuthRequestStageFieldMask = 10
)

// Enum value maps for AuthRequestStageFieldMask.
var (
	AuthRequestStageFieldMask_name = map[int32]string{
		0:  "AUTH_REQUEST_STAGE_FIELD_MASK_UNSPECIFIED",
		1:  "AUTH_REQUEST_STAGE_FIELD_MASK_ID",
		2:  "AUTH_REQUEST_STAGE_FIELD_MASK_AUTH_REQUEST_ID",
		3:  "AUTH_REQUEST_STAGE_FIELD_MASK_AUTH_REF_ID",
		4:  "AUTH_REQUEST_STAGE_FIELD_MASK_AUTH_STAGE",
		5:  "AUTH_REQUEST_STAGE_FIELD_MASK_STATUS",
		6:  "AUTH_REQUEST_STAGE_FIELD_MASK_STALED_AT",
		7:  "AUTH_REQUEST_STAGE_FIELD_MASK_COMPLETED_AT",
		8:  "AUTH_REQUEST_STAGE_FIELD_MASK_CREATED_AT",
		9:  "AUTH_REQUEST_STAGE_FIELD_MASK_UPDATED_AT",
		10: "AUTH_REQUEST_STAGE_FIELD_MASK_DELETED_AT",
	}
	AuthRequestStageFieldMask_value = map[string]int32{
		"AUTH_REQUEST_STAGE_FIELD_MASK_UNSPECIFIED":     0,
		"AUTH_REQUEST_STAGE_FIELD_MASK_ID":              1,
		"AUTH_REQUEST_STAGE_FIELD_MASK_AUTH_REQUEST_ID": 2,
		"AUTH_REQUEST_STAGE_FIELD_MASK_AUTH_REF_ID":     3,
		"AUTH_REQUEST_STAGE_FIELD_MASK_AUTH_STAGE":      4,
		"AUTH_REQUEST_STAGE_FIELD_MASK_STATUS":          5,
		"AUTH_REQUEST_STAGE_FIELD_MASK_STALED_AT":       6,
		"AUTH_REQUEST_STAGE_FIELD_MASK_COMPLETED_AT":    7,
		"AUTH_REQUEST_STAGE_FIELD_MASK_CREATED_AT":      8,
		"AUTH_REQUEST_STAGE_FIELD_MASK_UPDATED_AT":      9,
		"AUTH_REQUEST_STAGE_FIELD_MASK_DELETED_AT":      10,
	}
)

func (x AuthRequestStageFieldMask) Enum() *AuthRequestStageFieldMask {
	p := new(AuthRequestStageFieldMask)
	*p = x
	return p
}

func (x AuthRequestStageFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthRequestStageFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_orchestrator_internal_auth_request_stage_proto_enumTypes[0].Descriptor()
}

func (AuthRequestStageFieldMask) Type() protoreflect.EnumType {
	return &file_api_auth_orchestrator_internal_auth_request_stage_proto_enumTypes[0]
}

func (x AuthRequestStageFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthRequestStageFieldMask.Descriptor instead.
func (AuthRequestStageFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDescGZIP(), []int{0}
}

type AuthRequestStageStatus int32

const (
	AuthRequestStageStatus_AUTH_REQUEST_STAGE_STATUS_UNSPECIFIED         AuthRequestStageStatus = 0
	AuthRequestStageStatus_AUTH_REQUEST_STAGE_STATUS_PENDING             AuthRequestStageStatus = 1
	AuthRequestStageStatus_AUTH_REQUEST_STAGE_STATUS_IN_PROGRESS         AuthRequestStageStatus = 2
	AuthRequestStageStatus_AUTH_REQUEST_STAGE_STATUS_SUCCESS             AuthRequestStageStatus = 3
	AuthRequestStageStatus_AUTH_REQUEST_STAGE_STATUS_FAIL                AuthRequestStageStatus = 4
	AuthRequestStageStatus_AUTH_REQUEST_STAGE_STATUS_MANUAL_INTERVENTION AuthRequestStageStatus = 5
	AuthRequestStageStatus_AUTH_REQUEST_STAGE_STATUS_IN_REVIEW           AuthRequestStageStatus = 6
)

// Enum value maps for AuthRequestStageStatus.
var (
	AuthRequestStageStatus_name = map[int32]string{
		0: "AUTH_REQUEST_STAGE_STATUS_UNSPECIFIED",
		1: "AUTH_REQUEST_STAGE_STATUS_PENDING",
		2: "AUTH_REQUEST_STAGE_STATUS_IN_PROGRESS",
		3: "AUTH_REQUEST_STAGE_STATUS_SUCCESS",
		4: "AUTH_REQUEST_STAGE_STATUS_FAIL",
		5: "AUTH_REQUEST_STAGE_STATUS_MANUAL_INTERVENTION",
		6: "AUTH_REQUEST_STAGE_STATUS_IN_REVIEW",
	}
	AuthRequestStageStatus_value = map[string]int32{
		"AUTH_REQUEST_STAGE_STATUS_UNSPECIFIED":         0,
		"AUTH_REQUEST_STAGE_STATUS_PENDING":             1,
		"AUTH_REQUEST_STAGE_STATUS_IN_PROGRESS":         2,
		"AUTH_REQUEST_STAGE_STATUS_SUCCESS":             3,
		"AUTH_REQUEST_STAGE_STATUS_FAIL":                4,
		"AUTH_REQUEST_STAGE_STATUS_MANUAL_INTERVENTION": 5,
		"AUTH_REQUEST_STAGE_STATUS_IN_REVIEW":           6,
	}
)

func (x AuthRequestStageStatus) Enum() *AuthRequestStageStatus {
	p := new(AuthRequestStageStatus)
	*p = x
	return p
}

func (x AuthRequestStageStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthRequestStageStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_orchestrator_internal_auth_request_stage_proto_enumTypes[1].Descriptor()
}

func (AuthRequestStageStatus) Type() protoreflect.EnumType {
	return &file_api_auth_orchestrator_internal_auth_request_stage_proto_enumTypes[1]
}

func (x AuthRequestStageStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthRequestStageStatus.Descriptor instead.
func (AuthRequestStageStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDescGZIP(), []int{1}
}

type AuthStage int32

const (
	AuthStage_AUTH_STAGE_UNSPECIFIED             AuthStage = 0
	AuthStage_AUTH_STAGE_LIVENESS                AuthStage = 1
	AuthStage_AUTH_STAGE_FACE_MATCH              AuthStage = 2
	AuthStage_AUTH_STAGE_MANUAL_REVIEW           AuthStage = 3
	AuthStage_AUTH_STAGE_LIVENESS_SUMMARY        AuthStage = 4
	AuthStage_AUTH_STAGE_NPCI_SECURE_PIN         AuthStage = 5
	AuthStage_AUTH_STAGE_SMS_OTP                 AuthStage = 6
	AuthStage_AUTH_STAGE_BIOMETRIC_STATUS_CHECK  AuthStage = 7
	AuthStage_AUTH_STAGE_BIOMETRIC_STATUS_UPDATE AuthStage = 8
)

// Enum value maps for AuthStage.
var (
	AuthStage_name = map[int32]string{
		0: "AUTH_STAGE_UNSPECIFIED",
		1: "AUTH_STAGE_LIVENESS",
		2: "AUTH_STAGE_FACE_MATCH",
		3: "AUTH_STAGE_MANUAL_REVIEW",
		4: "AUTH_STAGE_LIVENESS_SUMMARY",
		5: "AUTH_STAGE_NPCI_SECURE_PIN",
		6: "AUTH_STAGE_SMS_OTP",
		7: "AUTH_STAGE_BIOMETRIC_STATUS_CHECK",
		8: "AUTH_STAGE_BIOMETRIC_STATUS_UPDATE",
	}
	AuthStage_value = map[string]int32{
		"AUTH_STAGE_UNSPECIFIED":             0,
		"AUTH_STAGE_LIVENESS":                1,
		"AUTH_STAGE_FACE_MATCH":              2,
		"AUTH_STAGE_MANUAL_REVIEW":           3,
		"AUTH_STAGE_LIVENESS_SUMMARY":        4,
		"AUTH_STAGE_NPCI_SECURE_PIN":         5,
		"AUTH_STAGE_SMS_OTP":                 6,
		"AUTH_STAGE_BIOMETRIC_STATUS_CHECK":  7,
		"AUTH_STAGE_BIOMETRIC_STATUS_UPDATE": 8,
	}
)

func (x AuthStage) Enum() *AuthStage {
	p := new(AuthStage)
	*p = x
	return p
}

func (x AuthStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthStage) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_orchestrator_internal_auth_request_stage_proto_enumTypes[2].Descriptor()
}

func (AuthStage) Type() protoreflect.EnumType {
	return &file_api_auth_orchestrator_internal_auth_request_stage_proto_enumTypes[2]
}

func (x AuthStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthStage.Descriptor instead.
func (AuthStage) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDescGZIP(), []int{2}
}

type AuthRequestStage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// reference to auth requests table
	AuthRequestId string `protobuf:"bytes,2,opt,name=auth_request_id,json=authRequestId,proto3" json:"auth_request_id,omitempty"`
	// reference to auth step(liveness, otp, etc.)
	AuthRefId string `protobuf:"bytes,3,opt,name=auth_ref_id,json=authRefId,proto3" json:"auth_ref_id,omitempty"`
	// auth mechanism
	AuthStage AuthStage `protobuf:"varint,4,opt,name=auth_stage,json=authStage,proto3,enum=auth.orchestrator.AuthStage" json:"auth_stage,omitempty"`
	// status of request
	Status AuthRequestStageStatus `protobuf:"varint,5,opt,name=status,proto3,enum=auth.orchestrator.AuthRequestStageStatus" json:"status,omitempty"`
	// timestamp post which it is stale
	StaledAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=staled_at,json=staledAt,proto3" json:"staled_at,omitempty"`
	// timestamp when stage was completed
	CompletedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt   *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt   *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *AuthRequestStage) Reset() {
	*x = AuthRequestStage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_internal_auth_request_stage_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthRequestStage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthRequestStage) ProtoMessage() {}

func (x *AuthRequestStage) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_internal_auth_request_stage_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthRequestStage.ProtoReflect.Descriptor instead.
func (*AuthRequestStage) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDescGZIP(), []int{0}
}

func (x *AuthRequestStage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AuthRequestStage) GetAuthRequestId() string {
	if x != nil {
		return x.AuthRequestId
	}
	return ""
}

func (x *AuthRequestStage) GetAuthRefId() string {
	if x != nil {
		return x.AuthRefId
	}
	return ""
}

func (x *AuthRequestStage) GetAuthStage() AuthStage {
	if x != nil {
		return x.AuthStage
	}
	return AuthStage_AUTH_STAGE_UNSPECIFIED
}

func (x *AuthRequestStage) GetStatus() AuthRequestStageStatus {
	if x != nil {
		return x.Status
	}
	return AuthRequestStageStatus_AUTH_REQUEST_STAGE_STATUS_UNSPECIFIED
}

func (x *AuthRequestStage) GetStaledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StaledAt
	}
	return nil
}

func (x *AuthRequestStage) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *AuthRequestStage) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AuthRequestStage) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *AuthRequestStage) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_auth_orchestrator_internal_auth_request_stage_proto protoreflect.FileDescriptor

var file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDesc = []byte{
	0x0a, 0x37, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x93, 0x04,
	0x0a, 0x10, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x75, 0x74,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x75, 0x74, 0x68, 0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0a, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x09, 0x61, 0x75,
	0x74, 0x68, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x41, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f,
	0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x73, 0x74,
	0x61, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x73, 0x74, 0x61, 0x6c, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x2a, 0x91, 0x04, 0x0a, 0x19, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x12, 0x2d, 0x0a, 0x29, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x24, 0x0a, 0x20, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x31, 0x0a, 0x2d, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x2d, 0x0a, 0x29, 0x41, 0x55, 0x54,
	0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x52, 0x45, 0x46, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x2c, 0x0a, 0x28, 0x41, 0x55, 0x54, 0x48,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53,
	0x54, 0x41, 0x47, 0x45, 0x10, 0x04, 0x12, 0x28, 0x0a, 0x24, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x05,
	0x12, 0x2b, 0x0a, 0x27, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x53, 0x54, 0x41, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x06, 0x12, 0x2e, 0x0a,
	0x2a, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43,
	0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x07, 0x12, 0x2c, 0x0a,
	0x28, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x08, 0x12, 0x2c, 0x0a, 0x28, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x09, 0x12, 0x2c, 0x0a, 0x28, 0x41, 0x55, 0x54,
	0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54,
	0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0a, 0x2a, 0xbc, 0x02, 0x0a, 0x16, 0x41, 0x75, 0x74, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a,
	0x21, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12,
	0x25, 0x0a, 0x21, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x04, 0x12, 0x31, 0x0a, 0x2d, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x27, 0x0a,
	0x23, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x52, 0x45,
	0x56, 0x49, 0x45, 0x57, 0x10, 0x06, 0x2a, 0xa1, 0x02, 0x0a, 0x09, 0x41, 0x75, 0x74, 0x68, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x17, 0x0a, 0x13, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4c,
	0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x55, 0x54,
	0x48, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57,
	0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52,
	0x59, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x4e, 0x50, 0x43, 0x49, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x49,
	0x4e, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x53, 0x4d, 0x53, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x06, 0x12, 0x25, 0x0a, 0x21, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54,
	0x52, 0x49, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b,
	0x10, 0x07, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x08, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72,
	0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68,
	0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDescOnce sync.Once
	file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDescData = file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDesc
)

func file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDescGZIP() []byte {
	file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDescOnce.Do(func() {
		file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDescData)
	})
	return file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDescData
}

var file_api_auth_orchestrator_internal_auth_request_stage_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_auth_orchestrator_internal_auth_request_stage_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_auth_orchestrator_internal_auth_request_stage_proto_goTypes = []interface{}{
	(AuthRequestStageFieldMask)(0), // 0: auth.orchestrator.AuthRequestStageFieldMask
	(AuthRequestStageStatus)(0),    // 1: auth.orchestrator.AuthRequestStageStatus
	(AuthStage)(0),                 // 2: auth.orchestrator.AuthStage
	(*AuthRequestStage)(nil),       // 3: auth.orchestrator.AuthRequestStage
	(*timestamppb.Timestamp)(nil),  // 4: google.protobuf.Timestamp
}
var file_api_auth_orchestrator_internal_auth_request_stage_proto_depIdxs = []int32{
	2, // 0: auth.orchestrator.AuthRequestStage.auth_stage:type_name -> auth.orchestrator.AuthStage
	1, // 1: auth.orchestrator.AuthRequestStage.status:type_name -> auth.orchestrator.AuthRequestStageStatus
	4, // 2: auth.orchestrator.AuthRequestStage.staled_at:type_name -> google.protobuf.Timestamp
	4, // 3: auth.orchestrator.AuthRequestStage.completed_at:type_name -> google.protobuf.Timestamp
	4, // 4: auth.orchestrator.AuthRequestStage.created_at:type_name -> google.protobuf.Timestamp
	4, // 5: auth.orchestrator.AuthRequestStage.updated_at:type_name -> google.protobuf.Timestamp
	4, // 6: auth.orchestrator.AuthRequestStage.deleted_at:type_name -> google.protobuf.Timestamp
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_auth_orchestrator_internal_auth_request_stage_proto_init() }
func file_api_auth_orchestrator_internal_auth_request_stage_proto_init() {
	if File_api_auth_orchestrator_internal_auth_request_stage_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_orchestrator_internal_auth_request_stage_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthRequestStage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_orchestrator_internal_auth_request_stage_proto_goTypes,
		DependencyIndexes: file_api_auth_orchestrator_internal_auth_request_stage_proto_depIdxs,
		EnumInfos:         file_api_auth_orchestrator_internal_auth_request_stage_proto_enumTypes,
		MessageInfos:      file_api_auth_orchestrator_internal_auth_request_stage_proto_msgTypes,
	}.Build()
	File_api_auth_orchestrator_internal_auth_request_stage_proto = out.File
	file_api_auth_orchestrator_internal_auth_request_stage_proto_rawDesc = nil
	file_api_auth_orchestrator_internal_auth_request_stage_proto_goTypes = nil
	file_api_auth_orchestrator_internal_auth_request_stage_proto_depIdxs = nil
}
