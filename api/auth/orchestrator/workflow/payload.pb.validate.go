// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/orchestrator/workflow/payload.proto

package workflow

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	biometrics "github.com/epifi/gamma/api/auth/biometrics"

	orchestrator "github.com/epifi/gamma/api/auth/orchestrator"

	stage "github.com/epifi/be-common/api/celestial/workflow/stage"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = biometrics.BiometricStatus(0)

	_ = orchestrator.AuthRequestStatus(0)

	_ = stage.Status(0)
)

// Validate checks the field values on UpdateAuthRequestStatusPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAuthRequestStatusPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAuthRequestStatusPayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateAuthRequestStatusPayloadMultiError, or nil if none found.
func (m *UpdateAuthRequestStatusPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAuthRequestStatusPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	if len(errors) > 0 {
		return UpdateAuthRequestStatusPayloadMultiError(errors)
	}

	return nil
}

// UpdateAuthRequestStatusPayloadMultiError is an error wrapping multiple
// validation errors returned by UpdateAuthRequestStatusPayload.ValidateAll()
// if the designated constraints aren't met.
type UpdateAuthRequestStatusPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAuthRequestStatusPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAuthRequestStatusPayloadMultiError) AllErrors() []error { return m }

// UpdateAuthRequestStatusPayloadValidationError is the validation error
// returned by UpdateAuthRequestStatusPayload.Validate if the designated
// constraints aren't met.
type UpdateAuthRequestStatusPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAuthRequestStatusPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAuthRequestStatusPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAuthRequestStatusPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAuthRequestStatusPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAuthRequestStatusPayloadValidationError) ErrorName() string {
	return "UpdateAuthRequestStatusPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAuthRequestStatusPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAuthRequestStatusPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAuthRequestStatusPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAuthRequestStatusPayloadValidationError{}

// Validate checks the field values on OrchestratorWorkflowPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OrchestratorWorkflowPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrchestratorWorkflowPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OrchestratorWorkflowPayloadMultiError, or nil if none found.
func (m *OrchestratorWorkflowPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *OrchestratorWorkflowPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FlowName

	if len(errors) > 0 {
		return OrchestratorWorkflowPayloadMultiError(errors)
	}

	return nil
}

// OrchestratorWorkflowPayloadMultiError is an error wrapping multiple
// validation errors returned by OrchestratorWorkflowPayload.ValidateAll() if
// the designated constraints aren't met.
type OrchestratorWorkflowPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrchestratorWorkflowPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrchestratorWorkflowPayloadMultiError) AllErrors() []error { return m }

// OrchestratorWorkflowPayloadValidationError is the validation error returned
// by OrchestratorWorkflowPayload.Validate if the designated constraints
// aren't met.
type OrchestratorWorkflowPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrchestratorWorkflowPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrchestratorWorkflowPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrchestratorWorkflowPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrchestratorWorkflowPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrchestratorWorkflowPayloadValidationError) ErrorName() string {
	return "OrchestratorWorkflowPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e OrchestratorWorkflowPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrchestratorWorkflowPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrchestratorWorkflowPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrchestratorWorkflowPayloadValidationError{}

// Validate checks the field values on SmsOtpSignalPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SmsOtpSignalPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SmsOtpSignalPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SmsOtpSignalPayloadMultiError, or nil if none found.
func (m *SmsOtpSignalPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *SmsOtpSignalPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SignalType

	// no validation rules for Status

	// no validation rules for Token

	// no validation rules for IsNewToken

	if len(errors) > 0 {
		return SmsOtpSignalPayloadMultiError(errors)
	}

	return nil
}

// SmsOtpSignalPayloadMultiError is an error wrapping multiple validation
// errors returned by SmsOtpSignalPayload.ValidateAll() if the designated
// constraints aren't met.
type SmsOtpSignalPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SmsOtpSignalPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SmsOtpSignalPayloadMultiError) AllErrors() []error { return m }

// SmsOtpSignalPayloadValidationError is the validation error returned by
// SmsOtpSignalPayload.Validate if the designated constraints aren't met.
type SmsOtpSignalPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SmsOtpSignalPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SmsOtpSignalPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SmsOtpSignalPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SmsOtpSignalPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SmsOtpSignalPayloadValidationError) ErrorName() string {
	return "SmsOtpSignalPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e SmsOtpSignalPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSmsOtpSignalPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SmsOtpSignalPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SmsOtpSignalPayloadValidationError{}

// Validate checks the field values on LivenessSignalPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LivenessSignalPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessSignalPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessSignalPayloadMultiError, or nil if none found.
func (m *LivenessSignalPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessSignalPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StageStatus

	if len(errors) > 0 {
		return LivenessSignalPayloadMultiError(errors)
	}

	return nil
}

// LivenessSignalPayloadMultiError is an error wrapping multiple validation
// errors returned by LivenessSignalPayload.ValidateAll() if the designated
// constraints aren't met.
type LivenessSignalPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessSignalPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessSignalPayloadMultiError) AllErrors() []error { return m }

// LivenessSignalPayloadValidationError is the validation error returned by
// LivenessSignalPayload.Validate if the designated constraints aren't met.
type LivenessSignalPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessSignalPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessSignalPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessSignalPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessSignalPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessSignalPayloadValidationError) ErrorName() string {
	return "LivenessSignalPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e LivenessSignalPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessSignalPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessSignalPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessSignalPayloadValidationError{}

// Validate checks the field values on ActivityResponses with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ActivityResponses) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivityResponses with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivityResponsesMultiError, or nil if none found.
func (m *ActivityResponses) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivityResponses) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	switch v := m.Response.(type) {
	case *ActivityResponses_BiometricStatusCheckResponse:
		if v == nil {
			err := ActivityResponsesValidationError{
				field:  "Response",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBiometricStatusCheckResponse()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActivityResponsesValidationError{
						field:  "BiometricStatusCheckResponse",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActivityResponsesValidationError{
						field:  "BiometricStatusCheckResponse",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBiometricStatusCheckResponse()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActivityResponsesValidationError{
					field:  "BiometricStatusCheckResponse",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActivityResponses_BiometricLivenessResponse:
		if v == nil {
			err := ActivityResponsesValidationError{
				field:  "Response",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBiometricLivenessResponse()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActivityResponsesValidationError{
						field:  "BiometricLivenessResponse",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActivityResponsesValidationError{
						field:  "BiometricLivenessResponse",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBiometricLivenessResponse()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActivityResponsesValidationError{
					field:  "BiometricLivenessResponse",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ActivityResponsesMultiError(errors)
	}

	return nil
}

// ActivityResponsesMultiError is an error wrapping multiple validation errors
// returned by ActivityResponses.ValidateAll() if the designated constraints
// aren't met.
type ActivityResponsesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivityResponsesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivityResponsesMultiError) AllErrors() []error { return m }

// ActivityResponsesValidationError is the validation error returned by
// ActivityResponses.Validate if the designated constraints aren't met.
type ActivityResponsesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivityResponsesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivityResponsesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivityResponsesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivityResponsesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivityResponsesValidationError) ErrorName() string {
	return "ActivityResponsesValidationError"
}

// Error satisfies the builtin error interface
func (e ActivityResponsesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivityResponses.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivityResponsesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivityResponsesValidationError{}

// Validate checks the field values on BiometricStatusCheckResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BiometricStatusCheckResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BiometricStatusCheckResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BiometricStatusCheckResponseMultiError, or nil if none found.
func (m *BiometricStatusCheckResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BiometricStatusCheckResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BiometricStatus

	if len(errors) > 0 {
		return BiometricStatusCheckResponseMultiError(errors)
	}

	return nil
}

// BiometricStatusCheckResponseMultiError is an error wrapping multiple
// validation errors returned by BiometricStatusCheckResponse.ValidateAll() if
// the designated constraints aren't met.
type BiometricStatusCheckResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BiometricStatusCheckResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BiometricStatusCheckResponseMultiError) AllErrors() []error { return m }

// BiometricStatusCheckResponseValidationError is the validation error returned
// by BiometricStatusCheckResponse.Validate if the designated constraints
// aren't met.
type BiometricStatusCheckResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BiometricStatusCheckResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BiometricStatusCheckResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BiometricStatusCheckResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BiometricStatusCheckResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BiometricStatusCheckResponseValidationError) ErrorName() string {
	return "BiometricStatusCheckResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BiometricStatusCheckResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBiometricStatusCheckResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BiometricStatusCheckResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BiometricStatusCheckResponseValidationError{}

// Validate checks the field values on BiometricLivenessResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BiometricLivenessResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BiometricLivenessResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BiometricLivenessResponseMultiError, or nil if none found.
func (m *BiometricLivenessResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BiometricLivenessResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsLivenessAttempted

	if len(errors) > 0 {
		return BiometricLivenessResponseMultiError(errors)
	}

	return nil
}

// BiometricLivenessResponseMultiError is an error wrapping multiple validation
// errors returned by BiometricLivenessResponse.ValidateAll() if the
// designated constraints aren't met.
type BiometricLivenessResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BiometricLivenessResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BiometricLivenessResponseMultiError) AllErrors() []error { return m }

// BiometricLivenessResponseValidationError is the validation error returned by
// BiometricLivenessResponse.Validate if the designated constraints aren't met.
type BiometricLivenessResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BiometricLivenessResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BiometricLivenessResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BiometricLivenessResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BiometricLivenessResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BiometricLivenessResponseValidationError) ErrorName() string {
	return "BiometricLivenessResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BiometricLivenessResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBiometricLivenessResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BiometricLivenessResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BiometricLivenessResponseValidationError{}
