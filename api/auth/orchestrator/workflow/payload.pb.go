// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/orchestrator/workflow/payload.proto

package workflow

import (
	stage "github.com/epifi/be-common/api/celestial/workflow/stage"
	biometrics "github.com/epifi/gamma/api/auth/biometrics"
	orchestrator "github.com/epifi/gamma/api/auth/orchestrator"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SmsOtpSignalPayload_SignalType int32

const (
	SmsOtpSignalPayload_SIGNAL_TYPE_UNSPECIFIED SmsOtpSignalPayload_SignalType = 0
	SmsOtpSignalPayload_SIGNAL_TYPE_GENERATE    SmsOtpSignalPayload_SignalType = 1
	SmsOtpSignalPayload_SIGNAL_TYPE_VERIFY      SmsOtpSignalPayload_SignalType = 2
)

// Enum value maps for SmsOtpSignalPayload_SignalType.
var (
	SmsOtpSignalPayload_SignalType_name = map[int32]string{
		0: "SIGNAL_TYPE_UNSPECIFIED",
		1: "SIGNAL_TYPE_GENERATE",
		2: "SIGNAL_TYPE_VERIFY",
	}
	SmsOtpSignalPayload_SignalType_value = map[string]int32{
		"SIGNAL_TYPE_UNSPECIFIED": 0,
		"SIGNAL_TYPE_GENERATE":    1,
		"SIGNAL_TYPE_VERIFY":      2,
	}
)

func (x SmsOtpSignalPayload_SignalType) Enum() *SmsOtpSignalPayload_SignalType {
	p := new(SmsOtpSignalPayload_SignalType)
	*p = x
	return p
}

func (x SmsOtpSignalPayload_SignalType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SmsOtpSignalPayload_SignalType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_orchestrator_workflow_payload_proto_enumTypes[0].Descriptor()
}

func (SmsOtpSignalPayload_SignalType) Type() protoreflect.EnumType {
	return &file_api_auth_orchestrator_workflow_payload_proto_enumTypes[0]
}

func (x SmsOtpSignalPayload_SignalType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SmsOtpSignalPayload_SignalType.Descriptor instead.
func (SmsOtpSignalPayload_SignalType) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_workflow_payload_proto_rawDescGZIP(), []int{2, 0}
}

type SmsOtpSignalPayload_Status int32

const (
	SmsOtpSignalPayload_STATUS_UNSPECIFIED SmsOtpSignalPayload_Status = 0
	SmsOtpSignalPayload_STATUS_SUCCESS     SmsOtpSignalPayload_Status = 1
	SmsOtpSignalPayload_STATUS_FAILURE     SmsOtpSignalPayload_Status = 2
)

// Enum value maps for SmsOtpSignalPayload_Status.
var (
	SmsOtpSignalPayload_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "STATUS_SUCCESS",
		2: "STATUS_FAILURE",
	}
	SmsOtpSignalPayload_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"STATUS_SUCCESS":     1,
		"STATUS_FAILURE":     2,
	}
)

func (x SmsOtpSignalPayload_Status) Enum() *SmsOtpSignalPayload_Status {
	p := new(SmsOtpSignalPayload_Status)
	*p = x
	return p
}

func (x SmsOtpSignalPayload_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SmsOtpSignalPayload_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_orchestrator_workflow_payload_proto_enumTypes[1].Descriptor()
}

func (SmsOtpSignalPayload_Status) Type() protoreflect.EnumType {
	return &file_api_auth_orchestrator_workflow_payload_proto_enumTypes[1]
}

func (x SmsOtpSignalPayload_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SmsOtpSignalPayload_Status.Descriptor instead.
func (SmsOtpSignalPayload_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_workflow_payload_proto_rawDescGZIP(), []int{2, 1}
}

type UpdateAuthRequestStatusPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status orchestrator.AuthRequestStatus `protobuf:"varint,1,opt,name=status,proto3,enum=auth.orchestrator.AuthRequestStatus" json:"status,omitempty"`
}

func (x *UpdateAuthRequestStatusPayload) Reset() {
	*x = UpdateAuthRequestStatusPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_workflow_payload_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAuthRequestStatusPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAuthRequestStatusPayload) ProtoMessage() {}

func (x *UpdateAuthRequestStatusPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_workflow_payload_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAuthRequestStatusPayload.ProtoReflect.Descriptor instead.
func (*UpdateAuthRequestStatusPayload) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_workflow_payload_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateAuthRequestStatusPayload) GetStatus() orchestrator.AuthRequestStatus {
	if x != nil {
		return x.Status
	}
	return orchestrator.AuthRequestStatus(0)
}

type OrchestratorWorkflowPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FlowName orchestrator.FlowName `protobuf:"varint,1,opt,name=flow_name,json=flowName,proto3,enum=auth.orchestrator.FlowName" json:"flow_name,omitempty"`
}

func (x *OrchestratorWorkflowPayload) Reset() {
	*x = OrchestratorWorkflowPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_workflow_payload_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrchestratorWorkflowPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrchestratorWorkflowPayload) ProtoMessage() {}

func (x *OrchestratorWorkflowPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_workflow_payload_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrchestratorWorkflowPayload.ProtoReflect.Descriptor instead.
func (*OrchestratorWorkflowPayload) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_workflow_payload_proto_rawDescGZIP(), []int{1}
}

func (x *OrchestratorWorkflowPayload) GetFlowName() orchestrator.FlowName {
	if x != nil {
		return x.FlowName
	}
	return orchestrator.FlowName(0)
}

type SmsOtpSignalPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignalType SmsOtpSignalPayload_SignalType `protobuf:"varint,1,opt,name=signal_type,json=signalType,proto3,enum=auth.orchestrator.workflow.SmsOtpSignalPayload_SignalType" json:"signal_type,omitempty"`
	Status     SmsOtpSignalPayload_Status     `protobuf:"varint,2,opt,name=status,proto3,enum=auth.orchestrator.workflow.SmsOtpSignalPayload_Status" json:"status,omitempty"`
	Token      string                         `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	IsNewToken bool                           `protobuf:"varint,4,opt,name=is_new_token,json=isNewToken,proto3" json:"is_new_token,omitempty"`
}

func (x *SmsOtpSignalPayload) Reset() {
	*x = SmsOtpSignalPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_workflow_payload_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmsOtpSignalPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmsOtpSignalPayload) ProtoMessage() {}

func (x *SmsOtpSignalPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_workflow_payload_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmsOtpSignalPayload.ProtoReflect.Descriptor instead.
func (*SmsOtpSignalPayload) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_workflow_payload_proto_rawDescGZIP(), []int{2}
}

func (x *SmsOtpSignalPayload) GetSignalType() SmsOtpSignalPayload_SignalType {
	if x != nil {
		return x.SignalType
	}
	return SmsOtpSignalPayload_SIGNAL_TYPE_UNSPECIFIED
}

func (x *SmsOtpSignalPayload) GetStatus() SmsOtpSignalPayload_Status {
	if x != nil {
		return x.Status
	}
	return SmsOtpSignalPayload_STATUS_UNSPECIFIED
}

func (x *SmsOtpSignalPayload) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SmsOtpSignalPayload) GetIsNewToken() bool {
	if x != nil {
		return x.IsNewToken
	}
	return false
}

type LivenessSignalPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StageStatus orchestrator.AuthRequestStageStatus `protobuf:"varint,1,opt,name=stage_status,json=stageStatus,proto3,enum=auth.orchestrator.AuthRequestStageStatus" json:"stage_status,omitempty"`
}

func (x *LivenessSignalPayload) Reset() {
	*x = LivenessSignalPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_workflow_payload_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessSignalPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessSignalPayload) ProtoMessage() {}

func (x *LivenessSignalPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_workflow_payload_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessSignalPayload.ProtoReflect.Descriptor instead.
func (*LivenessSignalPayload) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_workflow_payload_proto_rawDescGZIP(), []int{3}
}

func (x *LivenessSignalPayload) GetStageStatus() orchestrator.AuthRequestStageStatus {
	if x != nil {
		return x.StageStatus
	}
	return orchestrator.AuthRequestStageStatus(0)
}

type ActivityResponses struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status stage.Status `protobuf:"varint,1,opt,name=status,proto3,enum=celestial.workflow.stage.Status" json:"status,omitempty"`
	// Types that are assignable to Response:
	//
	//	*ActivityResponses_BiometricStatusCheckResponse
	//	*ActivityResponses_BiometricLivenessResponse
	Response isActivityResponses_Response `protobuf_oneof:"response"`
}

func (x *ActivityResponses) Reset() {
	*x = ActivityResponses{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_workflow_payload_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityResponses) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityResponses) ProtoMessage() {}

func (x *ActivityResponses) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_workflow_payload_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityResponses.ProtoReflect.Descriptor instead.
func (*ActivityResponses) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_workflow_payload_proto_rawDescGZIP(), []int{4}
}

func (x *ActivityResponses) GetStatus() stage.Status {
	if x != nil {
		return x.Status
	}
	return stage.Status(0)
}

func (m *ActivityResponses) GetResponse() isActivityResponses_Response {
	if m != nil {
		return m.Response
	}
	return nil
}

func (x *ActivityResponses) GetBiometricStatusCheckResponse() *BiometricStatusCheckResponse {
	if x, ok := x.GetResponse().(*ActivityResponses_BiometricStatusCheckResponse); ok {
		return x.BiometricStatusCheckResponse
	}
	return nil
}

func (x *ActivityResponses) GetBiometricLivenessResponse() *BiometricLivenessResponse {
	if x, ok := x.GetResponse().(*ActivityResponses_BiometricLivenessResponse); ok {
		return x.BiometricLivenessResponse
	}
	return nil
}

type isActivityResponses_Response interface {
	isActivityResponses_Response()
}

type ActivityResponses_BiometricStatusCheckResponse struct {
	BiometricStatusCheckResponse *BiometricStatusCheckResponse `protobuf:"bytes,2,opt,name=biometric_status_check_response,json=biometricStatusCheckResponse,proto3,oneof"`
}

type ActivityResponses_BiometricLivenessResponse struct {
	BiometricLivenessResponse *BiometricLivenessResponse `protobuf:"bytes,3,opt,name=biometric_liveness_response,json=biometricLivenessResponse,proto3,oneof"`
}

func (*ActivityResponses_BiometricStatusCheckResponse) isActivityResponses_Response() {}

func (*ActivityResponses_BiometricLivenessResponse) isActivityResponses_Response() {}

type BiometricStatusCheckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BiometricStatus biometrics.BiometricStatus `protobuf:"varint,1,opt,name=biometric_status,json=biometricStatus,proto3,enum=auth.biometrics.BiometricStatus" json:"biometric_status,omitempty"`
}

func (x *BiometricStatusCheckResponse) Reset() {
	*x = BiometricStatusCheckResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_workflow_payload_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BiometricStatusCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BiometricStatusCheckResponse) ProtoMessage() {}

func (x *BiometricStatusCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_workflow_payload_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BiometricStatusCheckResponse.ProtoReflect.Descriptor instead.
func (*BiometricStatusCheckResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_workflow_payload_proto_rawDescGZIP(), []int{5}
}

func (x *BiometricStatusCheckResponse) GetBiometricStatus() biometrics.BiometricStatus {
	if x != nil {
		return x.BiometricStatus
	}
	return biometrics.BiometricStatus(0)
}

type BiometricLivenessResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsLivenessAttempted bool `protobuf:"varint,1,opt,name=is_liveness_attempted,json=isLivenessAttempted,proto3" json:"is_liveness_attempted,omitempty"`
}

func (x *BiometricLivenessResponse) Reset() {
	*x = BiometricLivenessResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_workflow_payload_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BiometricLivenessResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BiometricLivenessResponse) ProtoMessage() {}

func (x *BiometricLivenessResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_workflow_payload_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BiometricLivenessResponse.ProtoReflect.Descriptor instead.
func (*BiometricLivenessResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_workflow_payload_proto_rawDescGZIP(), []int{6}
}

func (x *BiometricLivenessResponse) GetIsLivenessAttempted() bool {
	if x != nil {
		return x.IsLivenessAttempted
	}
	return false
}

var File_api_auth_orchestrator_workflow_payload_proto protoreflect.FileDescriptor

var file_api_auth_orchestrator_workflow_payload_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x75, 0x74, 0x68, 0x2f, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x75, 0x74, 0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74,
	0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x5e, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x12, 0x3c, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x57, 0x0a, 0x1b, 0x4f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12,
	0x38, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x08, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa1, 0x03, 0x0a, 0x13, 0x53, 0x6d,
	0x73, 0x4f, 0x74, 0x70, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x12, 0x5b, 0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72,
	0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x6d, 0x73, 0x4f, 0x74, 0x70, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c,
	0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4e,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x6d, 0x73, 0x4f,
	0x74, 0x70, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x20, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x4e, 0x65,
	0x77, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x5b, 0x0a, 0x0a, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x4c, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x53,
	0x49, 0x47, 0x4e, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x59, 0x10, 0x02, 0x22, 0x48, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a,
	0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x22, 0x65, 0x0a,
	0x15, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x50,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x4c, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0xd6, 0x02, 0x0a, 0x11, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x81, 0x01, 0x0a, 0x1f, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x42, 0x69, 0x6f, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x1c, 0x62, 0x69, 0x6f, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x77, 0x0a, 0x1b, 0x62, 0x69, 0x6f, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x42, 0x69, 0x6f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x19, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x0a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x6b, 0x0a,
	0x1c, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a,
	0x10, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62,
	0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x62, 0x69, 0x6f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x4f, 0x0a, 0x19, 0x42, 0x69,
	0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x73, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x42, 0x6e, 0x0a, 0x35, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f,
	0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_auth_orchestrator_workflow_payload_proto_rawDescOnce sync.Once
	file_api_auth_orchestrator_workflow_payload_proto_rawDescData = file_api_auth_orchestrator_workflow_payload_proto_rawDesc
)

func file_api_auth_orchestrator_workflow_payload_proto_rawDescGZIP() []byte {
	file_api_auth_orchestrator_workflow_payload_proto_rawDescOnce.Do(func() {
		file_api_auth_orchestrator_workflow_payload_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_orchestrator_workflow_payload_proto_rawDescData)
	})
	return file_api_auth_orchestrator_workflow_payload_proto_rawDescData
}

var file_api_auth_orchestrator_workflow_payload_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_auth_orchestrator_workflow_payload_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_auth_orchestrator_workflow_payload_proto_goTypes = []interface{}{
	(SmsOtpSignalPayload_SignalType)(0),      // 0: auth.orchestrator.workflow.SmsOtpSignalPayload.SignalType
	(SmsOtpSignalPayload_Status)(0),          // 1: auth.orchestrator.workflow.SmsOtpSignalPayload.Status
	(*UpdateAuthRequestStatusPayload)(nil),   // 2: auth.orchestrator.workflow.UpdateAuthRequestStatusPayload
	(*OrchestratorWorkflowPayload)(nil),      // 3: auth.orchestrator.workflow.OrchestratorWorkflowPayload
	(*SmsOtpSignalPayload)(nil),              // 4: auth.orchestrator.workflow.SmsOtpSignalPayload
	(*LivenessSignalPayload)(nil),            // 5: auth.orchestrator.workflow.LivenessSignalPayload
	(*ActivityResponses)(nil),                // 6: auth.orchestrator.workflow.ActivityResponses
	(*BiometricStatusCheckResponse)(nil),     // 7: auth.orchestrator.workflow.BiometricStatusCheckResponse
	(*BiometricLivenessResponse)(nil),        // 8: auth.orchestrator.workflow.BiometricLivenessResponse
	(orchestrator.AuthRequestStatus)(0),      // 9: auth.orchestrator.AuthRequestStatus
	(orchestrator.FlowName)(0),               // 10: auth.orchestrator.FlowName
	(orchestrator.AuthRequestStageStatus)(0), // 11: auth.orchestrator.AuthRequestStageStatus
	(stage.Status)(0),                        // 12: celestial.workflow.stage.Status
	(biometrics.BiometricStatus)(0),          // 13: auth.biometrics.BiometricStatus
}
var file_api_auth_orchestrator_workflow_payload_proto_depIdxs = []int32{
	9,  // 0: auth.orchestrator.workflow.UpdateAuthRequestStatusPayload.status:type_name -> auth.orchestrator.AuthRequestStatus
	10, // 1: auth.orchestrator.workflow.OrchestratorWorkflowPayload.flow_name:type_name -> auth.orchestrator.FlowName
	0,  // 2: auth.orchestrator.workflow.SmsOtpSignalPayload.signal_type:type_name -> auth.orchestrator.workflow.SmsOtpSignalPayload.SignalType
	1,  // 3: auth.orchestrator.workflow.SmsOtpSignalPayload.status:type_name -> auth.orchestrator.workflow.SmsOtpSignalPayload.Status
	11, // 4: auth.orchestrator.workflow.LivenessSignalPayload.stage_status:type_name -> auth.orchestrator.AuthRequestStageStatus
	12, // 5: auth.orchestrator.workflow.ActivityResponses.status:type_name -> celestial.workflow.stage.Status
	7,  // 6: auth.orchestrator.workflow.ActivityResponses.biometric_status_check_response:type_name -> auth.orchestrator.workflow.BiometricStatusCheckResponse
	8,  // 7: auth.orchestrator.workflow.ActivityResponses.biometric_liveness_response:type_name -> auth.orchestrator.workflow.BiometricLivenessResponse
	13, // 8: auth.orchestrator.workflow.BiometricStatusCheckResponse.biometric_status:type_name -> auth.biometrics.BiometricStatus
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_api_auth_orchestrator_workflow_payload_proto_init() }
func file_api_auth_orchestrator_workflow_payload_proto_init() {
	if File_api_auth_orchestrator_workflow_payload_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_orchestrator_workflow_payload_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAuthRequestStatusPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_workflow_payload_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrchestratorWorkflowPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_workflow_payload_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmsOtpSignalPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_workflow_payload_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessSignalPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_workflow_payload_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityResponses); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_workflow_payload_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BiometricStatusCheckResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_workflow_payload_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BiometricLivenessResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_auth_orchestrator_workflow_payload_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*ActivityResponses_BiometricStatusCheckResponse)(nil),
		(*ActivityResponses_BiometricLivenessResponse)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_orchestrator_workflow_payload_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_orchestrator_workflow_payload_proto_goTypes,
		DependencyIndexes: file_api_auth_orchestrator_workflow_payload_proto_depIdxs,
		EnumInfos:         file_api_auth_orchestrator_workflow_payload_proto_enumTypes,
		MessageInfos:      file_api_auth_orchestrator_workflow_payload_proto_msgTypes,
	}.Build()
	File_api_auth_orchestrator_workflow_payload_proto = out.File
	file_api_auth_orchestrator_workflow_payload_proto_rawDesc = nil
	file_api_auth_orchestrator_workflow_payload_proto_goTypes = nil
	file_api_auth_orchestrator_workflow_payload_proto_depIdxs = nil
}
