// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/orchestrator/service.proto

package orchestrator

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	liveness "github.com/epifi/gamma/api/auth/liveness"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = liveness.LivenessFlow(0)
)

// Validate checks the field values on InitiateAuthFlowRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateAuthFlowRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateAuthFlowRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateAuthFlowRequestMultiError, or nil if none found.
func (m *InitiateAuthFlowRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateAuthFlowRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := InitiateAuthFlowRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetClientRequestId()) < 1 {
		err := InitiateAuthFlowRequestValidationError{
			field:  "ClientRequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _InitiateAuthFlowRequest_FlowName_NotInLookup[m.GetFlowName()]; ok {
		err := InitiateAuthFlowRequestValidationError{
			field:  "FlowName",
			reason: "value must not be in list [AUTH_REQUEST_FLOW_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRedirectAction() == nil {
		err := InitiateAuthFlowRequestValidationError{
			field:  "RedirectAction",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRedirectAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateAuthFlowRequestValidationError{
					field:  "RedirectAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateAuthFlowRequestValidationError{
					field:  "RedirectAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirectAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateAuthFlowRequestValidationError{
				field:  "RedirectAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _InitiateAuthFlowRequest_Provenance_NotInLookup[m.GetProvenance()]; ok {
		err := InitiateAuthFlowRequestValidationError{
			field:  "Provenance",
			reason: "value must not be in list [PROVENANCE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPreferences()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateAuthFlowRequestValidationError{
					field:  "Preferences",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateAuthFlowRequestValidationError{
					field:  "Preferences",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreferences()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateAuthFlowRequestValidationError{
				field:  "Preferences",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateAuthFlowRequestMultiError(errors)
	}

	return nil
}

// InitiateAuthFlowRequestMultiError is an error wrapping multiple validation
// errors returned by InitiateAuthFlowRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateAuthFlowRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateAuthFlowRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateAuthFlowRequestMultiError) AllErrors() []error { return m }

// InitiateAuthFlowRequestValidationError is the validation error returned by
// InitiateAuthFlowRequest.Validate if the designated constraints aren't met.
type InitiateAuthFlowRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateAuthFlowRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateAuthFlowRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateAuthFlowRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateAuthFlowRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateAuthFlowRequestValidationError) ErrorName() string {
	return "InitiateAuthFlowRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateAuthFlowRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateAuthFlowRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateAuthFlowRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateAuthFlowRequestValidationError{}

var _InitiateAuthFlowRequest_FlowName_NotInLookup = map[FlowName]struct{}{
	0: {},
}

var _InitiateAuthFlowRequest_Provenance_NotInLookup = map[Provenance]struct{}{
	0: {},
}

// Validate checks the field values on InitiateAuthFlowResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateAuthFlowResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateAuthFlowResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateAuthFlowResponseMultiError, or nil if none found.
func (m *InitiateAuthFlowResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateAuthFlowResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateAuthFlowResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateAuthFlowResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateAuthFlowResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateAuthFlowResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateAuthFlowResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateAuthFlowResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateAuthFlowResponseMultiError(errors)
	}

	return nil
}

// InitiateAuthFlowResponseMultiError is an error wrapping multiple validation
// errors returned by InitiateAuthFlowResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateAuthFlowResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateAuthFlowResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateAuthFlowResponseMultiError) AllErrors() []error { return m }

// InitiateAuthFlowResponseValidationError is the validation error returned by
// InitiateAuthFlowResponse.Validate if the designated constraints aren't met.
type InitiateAuthFlowResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateAuthFlowResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateAuthFlowResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateAuthFlowResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateAuthFlowResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateAuthFlowResponseValidationError) ErrorName() string {
	return "InitiateAuthFlowResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateAuthFlowResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateAuthFlowResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateAuthFlowResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateAuthFlowResponseValidationError{}

// Validate checks the field values on GetAuthFlowStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAuthFlowStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthFlowStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAuthFlowStatusRequestMultiError, or nil if none found.
func (m *GetAuthFlowStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthFlowStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientRequestId

	if len(errors) > 0 {
		return GetAuthFlowStatusRequestMultiError(errors)
	}

	return nil
}

// GetAuthFlowStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetAuthFlowStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAuthFlowStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthFlowStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthFlowStatusRequestMultiError) AllErrors() []error { return m }

// GetAuthFlowStatusRequestValidationError is the validation error returned by
// GetAuthFlowStatusRequest.Validate if the designated constraints aren't met.
type GetAuthFlowStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthFlowStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthFlowStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthFlowStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthFlowStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthFlowStatusRequestValidationError) ErrorName() string {
	return "GetAuthFlowStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthFlowStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthFlowStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthFlowStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthFlowStatusRequestValidationError{}

// Validate checks the field values on GetAuthFlowStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAuthFlowStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthFlowStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAuthFlowStatusResponseMultiError, or nil if none found.
func (m *GetAuthFlowStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthFlowStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAuthFlowStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAuthFlowStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAuthFlowStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for AuthStatus

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAuthFlowStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAuthFlowStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAuthFlowStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAuthFlowStatusResponseMultiError(errors)
	}

	return nil
}

// GetAuthFlowStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetAuthFlowStatusResponse.ValidateAll() if the
// designated constraints aren't met.
type GetAuthFlowStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthFlowStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthFlowStatusResponseMultiError) AllErrors() []error { return m }

// GetAuthFlowStatusResponseValidationError is the validation error returned by
// GetAuthFlowStatusResponse.Validate if the designated constraints aren't met.
type GetAuthFlowStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthFlowStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthFlowStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthFlowStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthFlowStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthFlowStatusResponseValidationError) ErrorName() string {
	return "GetAuthFlowStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthFlowStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthFlowStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthFlowStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthFlowStatusResponseValidationError{}

// Validate checks the field values on GenerateOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOtpRequestMultiError, or nil if none found.
func (m *GenerateOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientRequestId

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if !_GenerateOtpRequest_Token_Pattern.MatchString(m.GetToken()) {
		err := GenerateOtpRequestValidationError{
			field:  "Token",
			reason: "value does not match regex pattern \"(^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[8|9|aA|bB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$|^$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GenerateOtpRequestMultiError(errors)
	}

	return nil
}

// GenerateOtpRequestMultiError is an error wrapping multiple validation errors
// returned by GenerateOtpRequest.ValidateAll() if the designated constraints
// aren't met.
type GenerateOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOtpRequestMultiError) AllErrors() []error { return m }

// GenerateOtpRequestValidationError is the validation error returned by
// GenerateOtpRequest.Validate if the designated constraints aren't met.
type GenerateOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOtpRequestValidationError) ErrorName() string {
	return "GenerateOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOtpRequestValidationError{}

var _GenerateOtpRequest_Token_Pattern = regexp.MustCompile("(^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[8|9|aA|bB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$|^$)")

// Validate checks the field values on GenerateOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOtpResponseMultiError, or nil if none found.
func (m *GenerateOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	// no validation rules for RetryTimerSeconds

	if len(errors) > 0 {
		return GenerateOtpResponseMultiError(errors)
	}

	return nil
}

// GenerateOtpResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateOtpResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOtpResponseMultiError) AllErrors() []error { return m }

// GenerateOtpResponseValidationError is the validation error returned by
// GenerateOtpResponse.Validate if the designated constraints aren't met.
type GenerateOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOtpResponseValidationError) ErrorName() string {
	return "GenerateOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOtpResponseValidationError{}

// Validate checks the field values on ValidateNpciPinRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateNpciPinRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateNpciPinRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateNpciPinRequestMultiError, or nil if none found.
func (m *ValidateNpciPinRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateNpciPinRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ClientReqId

	if utf8.RuneCountInString(m.GetTxnId()) < 1 {
		err := ValidateNpciPinRequestValidationError{
			field:  "TxnId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetNpciCredBlock()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateNpciPinRequestValidationError{
					field:  "NpciCredBlock",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateNpciPinRequestValidationError{
					field:  "NpciCredBlock",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNpciCredBlock()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateNpciPinRequestValidationError{
				field:  "NpciCredBlock",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateNpciPinRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateNpciPinRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateNpciPinRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateNpciPinRequestMultiError(errors)
	}

	return nil
}

// ValidateNpciPinRequestMultiError is an error wrapping multiple validation
// errors returned by ValidateNpciPinRequest.ValidateAll() if the designated
// constraints aren't met.
type ValidateNpciPinRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateNpciPinRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateNpciPinRequestMultiError) AllErrors() []error { return m }

// ValidateNpciPinRequestValidationError is the validation error returned by
// ValidateNpciPinRequest.Validate if the designated constraints aren't met.
type ValidateNpciPinRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateNpciPinRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateNpciPinRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateNpciPinRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateNpciPinRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateNpciPinRequestValidationError) ErrorName() string {
	return "ValidateNpciPinRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateNpciPinRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateNpciPinRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateNpciPinRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateNpciPinRequestValidationError{}

// Validate checks the field values on ValidateNpciPinResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateNpciPinResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateNpciPinResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateNpciPinResponseMultiError, or nil if none found.
func (m *ValidateNpciPinResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateNpciPinResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateNpciPinResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateNpciPinResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateNpciPinResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateNpciPinResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateNpciPinResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateNpciPinResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateNpciPinResponseMultiError(errors)
	}

	return nil
}

// ValidateNpciPinResponseMultiError is an error wrapping multiple validation
// errors returned by ValidateNpciPinResponse.ValidateAll() if the designated
// constraints aren't met.
type ValidateNpciPinResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateNpciPinResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateNpciPinResponseMultiError) AllErrors() []error { return m }

// ValidateNpciPinResponseValidationError is the validation error returned by
// ValidateNpciPinResponse.Validate if the designated constraints aren't met.
type ValidateNpciPinResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateNpciPinResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateNpciPinResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateNpciPinResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateNpciPinResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateNpciPinResponseValidationError) ErrorName() string {
	return "ValidateNpciPinResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateNpciPinResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateNpciPinResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateNpciPinResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateNpciPinResponseValidationError{}

// Validate checks the field values on VerifyOtpRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyOtpRequestMultiError, or nil if none found.
func (m *VerifyOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ClientReqId

	// no validation rules for Token

	// no validation rules for Otp

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyOtpRequestMultiError(errors)
	}

	return nil
}

// VerifyOtpRequestMultiError is an error wrapping multiple validation errors
// returned by VerifyOtpRequest.ValidateAll() if the designated constraints
// aren't met.
type VerifyOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyOtpRequestMultiError) AllErrors() []error { return m }

// VerifyOtpRequestValidationError is the validation error returned by
// VerifyOtpRequest.Validate if the designated constraints aren't met.
type VerifyOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyOtpRequestValidationError) ErrorName() string { return "VerifyOtpRequestValidationError" }

// Error satisfies the builtin error interface
func (e VerifyOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyOtpRequestValidationError{}

// Validate checks the field values on VerifyOtpResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyOtpResponseMultiError, or nil if none found.
func (m *VerifyOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyOtpResponseMultiError(errors)
	}

	return nil
}

// VerifyOtpResponseMultiError is an error wrapping multiple validation errors
// returned by VerifyOtpResponse.ValidateAll() if the designated constraints
// aren't met.
type VerifyOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyOtpResponseMultiError) AllErrors() []error { return m }

// VerifyOtpResponseValidationError is the validation error returned by
// VerifyOtpResponse.Validate if the designated constraints aren't met.
type VerifyOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyOtpResponseValidationError) ErrorName() string {
	return "VerifyOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyOtpResponseValidationError{}

// Validate checks the field values on GetLivenessSummaryStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLivenessSummaryStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLivenessSummaryStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLivenessSummaryStatusRequestMultiError, or nil if none found.
func (m *GetLivenessSummaryStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLivenessSummaryStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ClientRequestId

	// no validation rules for LivenessFlow

	if len(errors) > 0 {
		return GetLivenessSummaryStatusRequestMultiError(errors)
	}

	return nil
}

// GetLivenessSummaryStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetLivenessSummaryStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type GetLivenessSummaryStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLivenessSummaryStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLivenessSummaryStatusRequestMultiError) AllErrors() []error { return m }

// GetLivenessSummaryStatusRequestValidationError is the validation error
// returned by GetLivenessSummaryStatusRequest.Validate if the designated
// constraints aren't met.
type GetLivenessSummaryStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLivenessSummaryStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLivenessSummaryStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLivenessSummaryStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLivenessSummaryStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLivenessSummaryStatusRequestValidationError) ErrorName() string {
	return "GetLivenessSummaryStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLivenessSummaryStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLivenessSummaryStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLivenessSummaryStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLivenessSummaryStatusRequestValidationError{}

// Validate checks the field values on GetLivenessSummaryStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLivenessSummaryStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLivenessSummaryStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLivenessSummaryStatusResponseMultiError, or nil if none found.
func (m *GetLivenessSummaryStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLivenessSummaryStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessSummaryStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessSummaryStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessSummaryStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessSummaryStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessSummaryStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessSummaryStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SummaryStatus

	if len(errors) > 0 {
		return GetLivenessSummaryStatusResponseMultiError(errors)
	}

	return nil
}

// GetLivenessSummaryStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetLivenessSummaryStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLivenessSummaryStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLivenessSummaryStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLivenessSummaryStatusResponseMultiError) AllErrors() []error { return m }

// GetLivenessSummaryStatusResponseValidationError is the validation error
// returned by GetLivenessSummaryStatusResponse.Validate if the designated
// constraints aren't met.
type GetLivenessSummaryStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLivenessSummaryStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLivenessSummaryStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLivenessSummaryStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLivenessSummaryStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLivenessSummaryStatusResponseValidationError) ErrorName() string {
	return "GetLivenessSummaryStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLivenessSummaryStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLivenessSummaryStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLivenessSummaryStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLivenessSummaryStatusResponseValidationError{}

// Validate checks the field values on GetAuthStageRefIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAuthStageRefIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthStageRefIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAuthStageRefIdRequestMultiError, or nil if none found.
func (m *GetAuthStageRefIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthStageRefIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ClientRequestId

	// no validation rules for Stage

	if len(errors) > 0 {
		return GetAuthStageRefIdRequestMultiError(errors)
	}

	return nil
}

// GetAuthStageRefIdRequestMultiError is an error wrapping multiple validation
// errors returned by GetAuthStageRefIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAuthStageRefIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthStageRefIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthStageRefIdRequestMultiError) AllErrors() []error { return m }

// GetAuthStageRefIdRequestValidationError is the validation error returned by
// GetAuthStageRefIdRequest.Validate if the designated constraints aren't met.
type GetAuthStageRefIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthStageRefIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthStageRefIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthStageRefIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthStageRefIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthStageRefIdRequestValidationError) ErrorName() string {
	return "GetAuthStageRefIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthStageRefIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthStageRefIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthStageRefIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthStageRefIdRequestValidationError{}

// Validate checks the field values on GetAuthStageRefIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAuthStageRefIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthStageRefIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAuthStageRefIdResponseMultiError, or nil if none found.
func (m *GetAuthStageRefIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthStageRefIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAuthStageRefIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAuthStageRefIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAuthStageRefIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AuthStageRefId

	if len(errors) > 0 {
		return GetAuthStageRefIdResponseMultiError(errors)
	}

	return nil
}

// GetAuthStageRefIdResponseMultiError is an error wrapping multiple validation
// errors returned by GetAuthStageRefIdResponse.ValidateAll() if the
// designated constraints aren't met.
type GetAuthStageRefIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthStageRefIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthStageRefIdResponseMultiError) AllErrors() []error { return m }

// GetAuthStageRefIdResponseValidationError is the validation error returned by
// GetAuthStageRefIdResponse.Validate if the designated constraints aren't met.
type GetAuthStageRefIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthStageRefIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthStageRefIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthStageRefIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthStageRefIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthStageRefIdResponseValidationError) ErrorName() string {
	return "GetAuthStageRefIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthStageRefIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthStageRefIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthStageRefIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthStageRefIdResponseValidationError{}
