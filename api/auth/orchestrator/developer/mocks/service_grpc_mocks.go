// Code generated by MockGen. DO NOT EDIT.
// Source: api/auth/orchestrator/developer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockDevOrchestratorClient is a mock of DevOrchestratorClient interface.
type MockDevOrchestratorClient struct {
	ctrl     *gomock.Controller
	recorder *MockDevOrchestratorClientMockRecorder
}

// MockDevOrchestratorClientMockRecorder is the mock recorder for MockDevOrchestratorClient.
type MockDevOrchestratorClientMockRecorder struct {
	mock *MockDevOrchestratorClient
}

// NewMockDevOrchestratorClient creates a new mock instance.
func NewMockDevOrchestratorClient(ctrl *gomock.Controller) *MockDevOrchestratorClient {
	mock := &MockDevOrchestratorClient{ctrl: ctrl}
	mock.recorder = &MockDevOrchestratorClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevOrchestratorClient) EXPECT() *MockDevOrchestratorClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDevOrchestratorClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDevOrchestratorClientMockRecorder) GetData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDevOrchestratorClient)(nil).GetData), varargs...)
}

// GetEntityList mocks base method.
func (m *MockDevOrchestratorClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntityList", varargs...)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDevOrchestratorClientMockRecorder) GetEntityList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDevOrchestratorClient)(nil).GetEntityList), varargs...)
}

// GetParameterList mocks base method.
func (m *MockDevOrchestratorClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParameterList", varargs...)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDevOrchestratorClientMockRecorder) GetParameterList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDevOrchestratorClient)(nil).GetParameterList), varargs...)
}

// MockDevOrchestratorServer is a mock of DevOrchestratorServer interface.
type MockDevOrchestratorServer struct {
	ctrl     *gomock.Controller
	recorder *MockDevOrchestratorServerMockRecorder
}

// MockDevOrchestratorServerMockRecorder is the mock recorder for MockDevOrchestratorServer.
type MockDevOrchestratorServerMockRecorder struct {
	mock *MockDevOrchestratorServer
}

// NewMockDevOrchestratorServer creates a new mock instance.
func NewMockDevOrchestratorServer(ctrl *gomock.Controller) *MockDevOrchestratorServer {
	mock := &MockDevOrchestratorServer{ctrl: ctrl}
	mock.recorder = &MockDevOrchestratorServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevOrchestratorServer) EXPECT() *MockDevOrchestratorServerMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDevOrchestratorServer) GetData(arg0 context.Context, arg1 *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDevOrchestratorServerMockRecorder) GetData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDevOrchestratorServer)(nil).GetData), arg0, arg1)
}

// GetEntityList mocks base method.
func (m *MockDevOrchestratorServer) GetEntityList(arg0 context.Context, arg1 *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDevOrchestratorServerMockRecorder) GetEntityList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDevOrchestratorServer)(nil).GetEntityList), arg0, arg1)
}

// GetParameterList mocks base method.
func (m *MockDevOrchestratorServer) GetParameterList(arg0 context.Context, arg1 *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameterList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDevOrchestratorServerMockRecorder) GetParameterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDevOrchestratorServer)(nil).GetParameterList), arg0, arg1)
}

// MockUnsafeDevOrchestratorServer is a mock of UnsafeDevOrchestratorServer interface.
type MockUnsafeDevOrchestratorServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeDevOrchestratorServerMockRecorder
}

// MockUnsafeDevOrchestratorServerMockRecorder is the mock recorder for MockUnsafeDevOrchestratorServer.
type MockUnsafeDevOrchestratorServerMockRecorder struct {
	mock *MockUnsafeDevOrchestratorServer
}

// NewMockUnsafeDevOrchestratorServer creates a new mock instance.
func NewMockUnsafeDevOrchestratorServer(ctrl *gomock.Controller) *MockUnsafeDevOrchestratorServer {
	mock := &MockUnsafeDevOrchestratorServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeDevOrchestratorServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeDevOrchestratorServer) EXPECT() *MockUnsafeDevOrchestratorServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedDevOrchestratorServer mocks base method.
func (m *MockUnsafeDevOrchestratorServer) mustEmbedUnimplementedDevOrchestratorServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedDevOrchestratorServer")
}

// mustEmbedUnimplementedDevOrchestratorServer indicates an expected call of mustEmbedUnimplementedDevOrchestratorServer.
func (mr *MockUnsafeDevOrchestratorServerMockRecorder) mustEmbedUnimplementedDevOrchestratorServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedDevOrchestratorServer", reflect.TypeOf((*MockUnsafeDevOrchestratorServer)(nil).mustEmbedUnimplementedDevOrchestratorServer))
}
