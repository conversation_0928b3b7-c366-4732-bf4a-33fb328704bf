//go:generate gen_sql -types=AuthRequestStatus,FlowName,Provenance,Preferences

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/orchestrator/internal/auth_request.proto

package orchestrator

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	auth "github.com/epifi/gamma/api/auth"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthRequestFieldMask int32

const (
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_UNSPECIFIED       AuthRequestFieldMask = 0
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_ID                AuthRequestFieldMask = 1
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_ACTOR_ID          AuthRequestFieldMask = 2
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_CLIENT_REQUEST_ID AuthRequestFieldMask = 3
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_ORCHESTRATION_ID  AuthRequestFieldMask = 4
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_FLOW_NAME         AuthRequestFieldMask = 5
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_NEXT_ACTION       AuthRequestFieldMask = 6
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_STATUS            AuthRequestFieldMask = 7
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_PROVENANCE        AuthRequestFieldMask = 8
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_CREATED_AT        AuthRequestFieldMask = 9
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_UPDATED_AT        AuthRequestFieldMask = 10
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_DELETED_AT        AuthRequestFieldMask = 11
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_PREFERENCES       AuthRequestFieldMask = 12
)

// Enum value maps for AuthRequestFieldMask.
var (
	AuthRequestFieldMask_name = map[int32]string{
		0:  "AUTH_REQUEST_FIELD_MASK_UNSPECIFIED",
		1:  "AUTH_REQUEST_FIELD_MASK_ID",
		2:  "AUTH_REQUEST_FIELD_MASK_ACTOR_ID",
		3:  "AUTH_REQUEST_FIELD_MASK_CLIENT_REQUEST_ID",
		4:  "AUTH_REQUEST_FIELD_MASK_ORCHESTRATION_ID",
		5:  "AUTH_REQUEST_FIELD_MASK_FLOW_NAME",
		6:  "AUTH_REQUEST_FIELD_MASK_NEXT_ACTION",
		7:  "AUTH_REQUEST_FIELD_MASK_STATUS",
		8:  "AUTH_REQUEST_FIELD_MASK_PROVENANCE",
		9:  "AUTH_REQUEST_FIELD_MASK_CREATED_AT",
		10: "AUTH_REQUEST_FIELD_MASK_UPDATED_AT",
		11: "AUTH_REQUEST_FIELD_MASK_DELETED_AT",
		12: "AUTH_REQUEST_FIELD_MASK_PREFERENCES",
	}
	AuthRequestFieldMask_value = map[string]int32{
		"AUTH_REQUEST_FIELD_MASK_UNSPECIFIED":       0,
		"AUTH_REQUEST_FIELD_MASK_ID":                1,
		"AUTH_REQUEST_FIELD_MASK_ACTOR_ID":          2,
		"AUTH_REQUEST_FIELD_MASK_CLIENT_REQUEST_ID": 3,
		"AUTH_REQUEST_FIELD_MASK_ORCHESTRATION_ID":  4,
		"AUTH_REQUEST_FIELD_MASK_FLOW_NAME":         5,
		"AUTH_REQUEST_FIELD_MASK_NEXT_ACTION":       6,
		"AUTH_REQUEST_FIELD_MASK_STATUS":            7,
		"AUTH_REQUEST_FIELD_MASK_PROVENANCE":        8,
		"AUTH_REQUEST_FIELD_MASK_CREATED_AT":        9,
		"AUTH_REQUEST_FIELD_MASK_UPDATED_AT":        10,
		"AUTH_REQUEST_FIELD_MASK_DELETED_AT":        11,
		"AUTH_REQUEST_FIELD_MASK_PREFERENCES":       12,
	}
)

func (x AuthRequestFieldMask) Enum() *AuthRequestFieldMask {
	p := new(AuthRequestFieldMask)
	*p = x
	return p
}

func (x AuthRequestFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthRequestFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_orchestrator_internal_auth_request_proto_enumTypes[0].Descriptor()
}

func (AuthRequestFieldMask) Type() protoreflect.EnumType {
	return &file_api_auth_orchestrator_internal_auth_request_proto_enumTypes[0]
}

func (x AuthRequestFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthRequestFieldMask.Descriptor instead.
func (AuthRequestFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_internal_auth_request_proto_rawDescGZIP(), []int{0}
}

type AuthRequestStatus int32

const (
	AuthRequestStatus_AUTH_REQUEST_STATUS_UNSPECIFIED         AuthRequestStatus = 0
	AuthRequestStatus_AUTH_REQUEST_STATUS_PENDING             AuthRequestStatus = 1
	AuthRequestStatus_AUTH_REQUEST_STATUS_IN_PROGRESS         AuthRequestStatus = 2
	AuthRequestStatus_AUTH_REQUEST_STATUS_SUCCESS             AuthRequestStatus = 3
	AuthRequestStatus_AUTH_REQUEST_STATUS_FAIL                AuthRequestStatus = 4
	AuthRequestStatus_AUTH_REQUEST_STATUS_MANUAL_INTERVENTION AuthRequestStatus = 5
	AuthRequestStatus_AUTH_REQUEST_STATUS_IN_REVIEW           AuthRequestStatus = 6
)

// Enum value maps for AuthRequestStatus.
var (
	AuthRequestStatus_name = map[int32]string{
		0: "AUTH_REQUEST_STATUS_UNSPECIFIED",
		1: "AUTH_REQUEST_STATUS_PENDING",
		2: "AUTH_REQUEST_STATUS_IN_PROGRESS",
		3: "AUTH_REQUEST_STATUS_SUCCESS",
		4: "AUTH_REQUEST_STATUS_FAIL",
		5: "AUTH_REQUEST_STATUS_MANUAL_INTERVENTION",
		6: "AUTH_REQUEST_STATUS_IN_REVIEW",
	}
	AuthRequestStatus_value = map[string]int32{
		"AUTH_REQUEST_STATUS_UNSPECIFIED":         0,
		"AUTH_REQUEST_STATUS_PENDING":             1,
		"AUTH_REQUEST_STATUS_IN_PROGRESS":         2,
		"AUTH_REQUEST_STATUS_SUCCESS":             3,
		"AUTH_REQUEST_STATUS_FAIL":                4,
		"AUTH_REQUEST_STATUS_MANUAL_INTERVENTION": 5,
		"AUTH_REQUEST_STATUS_IN_REVIEW":           6,
	}
)

func (x AuthRequestStatus) Enum() *AuthRequestStatus {
	p := new(AuthRequestStatus)
	*p = x
	return p
}

func (x AuthRequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthRequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_orchestrator_internal_auth_request_proto_enumTypes[1].Descriptor()
}

func (AuthRequestStatus) Type() protoreflect.EnumType {
	return &file_api_auth_orchestrator_internal_auth_request_proto_enumTypes[1]
}

func (x AuthRequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthRequestStatus.Descriptor instead.
func (AuthRequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_internal_auth_request_proto_rawDescGZIP(), []int{1}
}

type FlowName int32

const (
	FlowName_AUTH_REQUEST_FLOW_UNSPECIFIED FlowName = 0
	// deprecated in favour of AUTH_REQUEST_FLOW_LIVENESS_SUMMARY
	//
	// Deprecated: Marked as deprecated in api/auth/orchestrator/internal/auth_request.proto.
	FlowName_AUTH_REQUEST_FLOW_LIVENESS FlowName = 1
	// deprecated in favour of AUTH_REQUEST_FLOW_LIVENESS_SUMMARY
	//
	// Deprecated: Marked as deprecated in api/auth/orchestrator/internal/auth_request.proto.
	FlowName_AUTH_REQUEST_FLOW_LIVENESS_PLUS_FACE_MATCH FlowName = 2
	// Flow for authenticating with liveness summary (both liveness and face match)
	FlowName_AUTH_REQUEST_FLOW_LIVENESS_SUMMARY FlowName = 3
	// Flow for authenticating with NPCI secure pin
	FlowName_AUTH_REQUEST_FLOW_NPCI_SECURE_PIN FlowName = 4
	// deprecated in favour of AUTH_REQUEST_FLOW_SMS_OTP
	//
	// Deprecated: Marked as deprecated in api/auth/orchestrator/internal/auth_request.proto.
	FlowName_AUTH_REQUEST_FLOW_PHONE_OTP_VERIFICATION FlowName = 5
	// Flow for authenticating with sms otp
	FlowName_AUTH_REQUEST_FLOW_SMS_OTP FlowName = 6
	// Flow for authenticating with both liveness summary and sms otp
	FlowName_AUTH_REQUEST_FLOW_LIVENESS_OTP FlowName = 7
	// Flow for authenticating with both npci secure pin and sms otp
	FlowName_AUTH_REQUEST_FLOW_NPCI_PIN_OTP FlowName = 8
	// Flow for biometric re-validation with liveness
	FlowName_AUTH_REQUEST_FLOW_BIOMETRIC_LIVENESS FlowName = 9
	// Flow for biometric re-validation, liveness and sms otp
	FlowName_AUTH_REQUEST_FLOW_BIOMETRIC_LIVENESS_OTP FlowName = 10
)

// Enum value maps for FlowName.
var (
	FlowName_name = map[int32]string{
		0:  "AUTH_REQUEST_FLOW_UNSPECIFIED",
		1:  "AUTH_REQUEST_FLOW_LIVENESS",
		2:  "AUTH_REQUEST_FLOW_LIVENESS_PLUS_FACE_MATCH",
		3:  "AUTH_REQUEST_FLOW_LIVENESS_SUMMARY",
		4:  "AUTH_REQUEST_FLOW_NPCI_SECURE_PIN",
		5:  "AUTH_REQUEST_FLOW_PHONE_OTP_VERIFICATION",
		6:  "AUTH_REQUEST_FLOW_SMS_OTP",
		7:  "AUTH_REQUEST_FLOW_LIVENESS_OTP",
		8:  "AUTH_REQUEST_FLOW_NPCI_PIN_OTP",
		9:  "AUTH_REQUEST_FLOW_BIOMETRIC_LIVENESS",
		10: "AUTH_REQUEST_FLOW_BIOMETRIC_LIVENESS_OTP",
	}
	FlowName_value = map[string]int32{
		"AUTH_REQUEST_FLOW_UNSPECIFIED":              0,
		"AUTH_REQUEST_FLOW_LIVENESS":                 1,
		"AUTH_REQUEST_FLOW_LIVENESS_PLUS_FACE_MATCH": 2,
		"AUTH_REQUEST_FLOW_LIVENESS_SUMMARY":         3,
		"AUTH_REQUEST_FLOW_NPCI_SECURE_PIN":          4,
		"AUTH_REQUEST_FLOW_PHONE_OTP_VERIFICATION":   5,
		"AUTH_REQUEST_FLOW_SMS_OTP":                  6,
		"AUTH_REQUEST_FLOW_LIVENESS_OTP":             7,
		"AUTH_REQUEST_FLOW_NPCI_PIN_OTP":             8,
		"AUTH_REQUEST_FLOW_BIOMETRIC_LIVENESS":       9,
		"AUTH_REQUEST_FLOW_BIOMETRIC_LIVENESS_OTP":   10,
	}
)

func (x FlowName) Enum() *FlowName {
	p := new(FlowName)
	*p = x
	return p
}

func (x FlowName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FlowName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_orchestrator_internal_auth_request_proto_enumTypes[2].Descriptor()
}

func (FlowName) Type() protoreflect.EnumType {
	return &file_api_auth_orchestrator_internal_auth_request_proto_enumTypes[2]
}

func (x FlowName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FlowName.Descriptor instead.
func (FlowName) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_internal_auth_request_proto_rawDescGZIP(), []int{2}
}

type Provenance int32

const (
	Provenance_PROVENANCE_UNSPECIFIED          Provenance = 0
	Provenance_PROVENANCE_CREDIT_CARD          Provenance = 1
	Provenance_PROVENANCE_CREDIT_REPORT        Provenance = 2
	Provenance_PROVENANCE_PRE_APPROVED_LOAN    Provenance = 3
	Provenance_PROVENANCE_DEVICE_BIOMETRIC     Provenance = 4
	Provenance_PROVENANCE_BENEFICIARY_COOLDOWN Provenance = 5
)

// Enum value maps for Provenance.
var (
	Provenance_name = map[int32]string{
		0: "PROVENANCE_UNSPECIFIED",
		1: "PROVENANCE_CREDIT_CARD",
		2: "PROVENANCE_CREDIT_REPORT",
		3: "PROVENANCE_PRE_APPROVED_LOAN",
		4: "PROVENANCE_DEVICE_BIOMETRIC",
		5: "PROVENANCE_BENEFICIARY_COOLDOWN",
	}
	Provenance_value = map[string]int32{
		"PROVENANCE_UNSPECIFIED":          0,
		"PROVENANCE_CREDIT_CARD":          1,
		"PROVENANCE_CREDIT_REPORT":        2,
		"PROVENANCE_PRE_APPROVED_LOAN":    3,
		"PROVENANCE_DEVICE_BIOMETRIC":     4,
		"PROVENANCE_BENEFICIARY_COOLDOWN": 5,
	}
)

func (x Provenance) Enum() *Provenance {
	p := new(Provenance)
	*p = x
	return p
}

func (x Provenance) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Provenance) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_orchestrator_internal_auth_request_proto_enumTypes[3].Descriptor()
}

func (Provenance) Type() protoreflect.EnumType {
	return &file_api_auth_orchestrator_internal_auth_request_proto_enumTypes[3]
}

func (x Provenance) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Provenance.Descriptor instead.
func (Provenance) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_internal_auth_request_proto_rawDescGZIP(), []int{3}
}

type AuthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// actor id of the user
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// client request id
	ClientRequestId string `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// auth flow id
	FlowName FlowName `protobuf:"varint,4,opt,name=flow_name,json=flowName,proto3,enum=auth.orchestrator.FlowName" json:"flow_name,omitempty"`
	// orchestration id of workflow
	OrchId string `protobuf:"bytes,5,opt,name=orch_id,json=orchId,proto3" json:"orch_id,omitempty"`
	// next action to be done by the user
	NextAction *deeplink.Deeplink `protobuf:"bytes,6,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	// redirect DL sent by calling service
	RedirectAction *deeplink.Deeplink `protobuf:"bytes,7,opt,name=redirect_action,json=redirectAction,proto3" json:"redirect_action,omitempty"`
	// status of request
	Status AuthRequestStatus `protobuf:"varint,8,opt,name=status,proto3,enum=auth.orchestrator.AuthRequestStatus" json:"status,omitempty"`
	// provenance(calling service)
	Provenance Provenance             `protobuf:"varint,9,opt,name=provenance,proto3,enum=auth.orchestrator.Provenance" json:"provenance,omitempty"`
	CreatedAt  *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt  *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt  *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// preferences and customisations
	Preferences *Preferences `protobuf:"bytes,13,opt,name=preferences,proto3" json:"preferences,omitempty"`
}

func (x *AuthRequest) Reset() {
	*x = AuthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthRequest) ProtoMessage() {}

func (x *AuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthRequest.ProtoReflect.Descriptor instead.
func (*AuthRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_internal_auth_request_proto_rawDescGZIP(), []int{0}
}

func (x *AuthRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AuthRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *AuthRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *AuthRequest) GetFlowName() FlowName {
	if x != nil {
		return x.FlowName
	}
	return FlowName_AUTH_REQUEST_FLOW_UNSPECIFIED
}

func (x *AuthRequest) GetOrchId() string {
	if x != nil {
		return x.OrchId
	}
	return ""
}

func (x *AuthRequest) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *AuthRequest) GetRedirectAction() *deeplink.Deeplink {
	if x != nil {
		return x.RedirectAction
	}
	return nil
}

func (x *AuthRequest) GetStatus() AuthRequestStatus {
	if x != nil {
		return x.Status
	}
	return AuthRequestStatus_AUTH_REQUEST_STATUS_UNSPECIFIED
}

func (x *AuthRequest) GetProvenance() Provenance {
	if x != nil {
		return x.Provenance
	}
	return Provenance_PROVENANCE_UNSPECIFIED
}

func (x *AuthRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AuthRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *AuthRequest) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *AuthRequest) GetPreferences() *Preferences {
	if x != nil {
		return x.Preferences
	}
	return nil
}

type Preferences struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SmsOtpPreferences   *SmsOtpPreferences   `protobuf:"bytes,1,opt,name=sms_otp_preferences,json=smsOtpPreferences,proto3" json:"sms_otp_preferences,omitempty"`
	LivenessPreferences *LivenessPreferences `protobuf:"bytes,2,opt,name=liveness_preferences,json=livenessPreferences,proto3" json:"liveness_preferences,omitempty"`
	UseLivenessV2       bool                 `protobuf:"varint,3,opt,name=use_liveness_v2,json=useLivenessV2,proto3" json:"use_liveness_v2,omitempty"`
}

func (x *Preferences) Reset() {
	*x = Preferences{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Preferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Preferences) ProtoMessage() {}

func (x *Preferences) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Preferences.ProtoReflect.Descriptor instead.
func (*Preferences) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_internal_auth_request_proto_rawDescGZIP(), []int{1}
}

func (x *Preferences) GetSmsOtpPreferences() *SmsOtpPreferences {
	if x != nil {
		return x.SmsOtpPreferences
	}
	return nil
}

func (x *Preferences) GetLivenessPreferences() *LivenessPreferences {
	if x != nil {
		return x.LivenessPreferences
	}
	return nil
}

func (x *Preferences) GetUseLivenessV2() bool {
	if x != nil {
		return x.UseLivenessV2
	}
	return false
}

type LivenessPreferences struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FacematchImagePath   string              `protobuf:"bytes,1,opt,name=facematch_image_path,json=facematchImagePath,proto3" json:"facematch_image_path,omitempty"`
	Base64EncodedFmImage string              `protobuf:"bytes,2,opt,name=base64_encoded_fm_image,json=base64EncodedFmImage,proto3" json:"base64_encoded_fm_image,omitempty"`
	LivenessScreenData   *LivenessScreenData `protobuf:"bytes,3,opt,name=liveness_screen_data,json=livenessScreenData,proto3" json:"liveness_screen_data,omitempty"`
}

func (x *LivenessPreferences) Reset() {
	*x = LivenessPreferences{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessPreferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessPreferences) ProtoMessage() {}

func (x *LivenessPreferences) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessPreferences.ProtoReflect.Descriptor instead.
func (*LivenessPreferences) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_internal_auth_request_proto_rawDescGZIP(), []int{2}
}

func (x *LivenessPreferences) GetFacematchImagePath() string {
	if x != nil {
		return x.FacematchImagePath
	}
	return ""
}

func (x *LivenessPreferences) GetBase64EncodedFmImage() string {
	if x != nil {
		return x.Base64EncodedFmImage
	}
	return ""
}

func (x *LivenessPreferences) GetLivenessScreenData() *LivenessScreenData {
	if x != nil {
		return x.LivenessScreenData
	}
	return nil
}

type SmsOtpPreferences struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GenerateOtpFlow auth.GenerateOTPFlow `protobuf:"varint,1,opt,name=generate_otp_flow,json=generateOtpFlow,proto3,enum=auth.GenerateOTPFlow" json:"generate_otp_flow,omitempty"`
}

func (x *SmsOtpPreferences) Reset() {
	*x = SmsOtpPreferences{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmsOtpPreferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmsOtpPreferences) ProtoMessage() {}

func (x *SmsOtpPreferences) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmsOtpPreferences.ProtoReflect.Descriptor instead.
func (*SmsOtpPreferences) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_internal_auth_request_proto_rawDescGZIP(), []int{3}
}

func (x *SmsOtpPreferences) GetGenerateOtpFlow() auth.GenerateOTPFlow {
	if x != nil {
		return x.GenerateOtpFlow
	}
	return auth.GenerateOTPFlow(0)
}

// https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=863-20042&mode=design&t=6wIklG8dn6FJZwvO-0
type LivenessScreenData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image     *common.VisualElement    `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	Title     *common.Text             `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle  *common.Text             `protobuf:"bytes,3,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	ListItems []*common.Text           `protobuf:"bytes,4,rep,name=list_items,json=listItems,proto3" json:"list_items,omitempty"`
	BgColor   *widget.BackgroundColour `protobuf:"bytes,5,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *LivenessScreenData) Reset() {
	*x = LivenessScreenData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessScreenData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessScreenData) ProtoMessage() {}

func (x *LivenessScreenData) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessScreenData.ProtoReflect.Descriptor instead.
func (*LivenessScreenData) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_internal_auth_request_proto_rawDescGZIP(), []int{4}
}

func (x *LivenessScreenData) GetImage() *common.VisualElement {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *LivenessScreenData) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *LivenessScreenData) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *LivenessScreenData) GetListItems() []*common.Text {
	if x != nil {
		return x.ListItems
	}
	return nil
}

func (x *LivenessScreenData) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

var File_api_auth_orchestrator_internal_auth_request_proto protoreflect.FileDescriptor

var file_api_auth_orchestrator_internal_auth_request_proto_rawDesc = []byte{
	0x0a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xab, 0x05, 0x0a, 0x0b, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x46, 0x6c,
	0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x08, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x72, 0x63, 0x68, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x0f, 0x72, 0x65, 0x64, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0e, 0x72,
	0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0a,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x40, 0x0a, 0x0b, 0x70,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73,
	0x52, 0x0b, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x22, 0xe6, 0x01,
	0x0a, 0x0b, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x54, 0x0a,
	0x13, 0x73, 0x6d, 0x73, 0x5f, 0x6f, 0x74, 0x70, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x53,
	0x6d, 0x73, 0x4f, 0x74, 0x70, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73,
	0x52, 0x11, 0x73, 0x6d, 0x73, 0x4f, 0x74, 0x70, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x73, 0x12, 0x59, 0x0a, 0x14, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x13, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x26,
	0x0a, 0x0f, 0x75, 0x73, 0x65, 0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x76,
	0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x4c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x56, 0x32, 0x22, 0xd7, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x30,
	0x0a, 0x14, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x66, 0x61,
	0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x35, 0x0a, 0x17, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64,
	0x65, 0x64, 0x5f, 0x66, 0x6d, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x14, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x64,
	0x46, 0x6d, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x57, 0x0a, 0x14, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63,
	0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x12, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x22, 0x56, 0x0a, 0x11, 0x53, 0x6d, 0x73, 0x4f, 0x74, 0x70, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x41, 0x0a, 0x11, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x5f, 0x6f, 0x74, 0x70, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x4f, 0x54, 0x50, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x4f, 0x74, 0x70, 0x46, 0x6c, 0x6f, 0x77, 0x22, 0xb7, 0x02, 0x0a, 0x12, 0x4c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x37, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x37,
	0x0a, 0x0a, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x09, 0x6c, 0x69,
	0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75,
	0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x2a, 0x9f, 0x04, 0x0a, 0x14, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x27, 0x0a, 0x23, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x49, 0x44, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x2d, 0x0a, 0x29, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x2c, 0x0a, 0x28, 0x41, 0x55, 0x54,
	0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x52, 0x43, 0x48, 0x45, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x05, 0x12, 0x27,
	0x0a, 0x23, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4e, 0x45, 0x58, 0x54, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x07, 0x12, 0x26, 0x0a, 0x22, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43,
	0x45, 0x10, 0x08, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x09, 0x12, 0x26, 0x0a, 0x22, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41,
	0x54, 0x10, 0x0a, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44,
	0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0b, 0x12, 0x27, 0x0a, 0x23, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x43,
	0x45, 0x53, 0x10, 0x0c, 0x2a, 0x8d, 0x02, 0x0a, 0x11, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x1f, 0x0a, 0x1b, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01,
	0x12, 0x23, 0x0a, 0x1f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52,
	0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x10, 0x04, 0x12, 0x2b, 0x0a, 0x27, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55,
	0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x05, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x10, 0x06, 0x2a, 0xc5, 0x03, 0x0a, 0x08, 0x46, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1a, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45,
	0x53, 0x53, 0x10, 0x01, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x32, 0x0a, 0x2a, 0x41, 0x55, 0x54, 0x48,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4c, 0x49,
	0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x4c, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x43, 0x45,
	0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x02, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x26, 0x0a, 0x22,
	0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41,
	0x52, 0x59, 0x10, 0x03, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4e, 0x50, 0x43, 0x49, 0x5f, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0x04, 0x12, 0x30, 0x0a, 0x28, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x1d, 0x0a,
	0x19, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x53, 0x4d, 0x53, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x06, 0x12, 0x22, 0x0a, 0x1e,
	0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x07,
	0x12, 0x22, 0x0a, 0x1e, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4e, 0x50, 0x43, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x4f,
	0x54, 0x50, 0x10, 0x08, 0x12, 0x28, 0x0a, 0x24, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54,
	0x52, 0x49, 0x43, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x09, 0x12, 0x2c,
	0x0a, 0x28, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x4c, 0x49,
	0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x0a, 0x2a, 0xca, 0x01, 0x0a,
	0x0a, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x50,
	0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f, 0x56, 0x45,
	0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43,
	0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10,
	0x02, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f,
	0x50, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41,
	0x4e, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43,
	0x45, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52,
	0x49, 0x43, 0x10, 0x04, 0x12, 0x23, 0x0a, 0x1f, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x43, 0x49, 0x41, 0x52, 0x59, 0x5f, 0x43,
	0x4f, 0x4f, 0x4c, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x05, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63,
	0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_orchestrator_internal_auth_request_proto_rawDescOnce sync.Once
	file_api_auth_orchestrator_internal_auth_request_proto_rawDescData = file_api_auth_orchestrator_internal_auth_request_proto_rawDesc
)

func file_api_auth_orchestrator_internal_auth_request_proto_rawDescGZIP() []byte {
	file_api_auth_orchestrator_internal_auth_request_proto_rawDescOnce.Do(func() {
		file_api_auth_orchestrator_internal_auth_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_orchestrator_internal_auth_request_proto_rawDescData)
	})
	return file_api_auth_orchestrator_internal_auth_request_proto_rawDescData
}

var file_api_auth_orchestrator_internal_auth_request_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_auth_orchestrator_internal_auth_request_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_auth_orchestrator_internal_auth_request_proto_goTypes = []interface{}{
	(AuthRequestFieldMask)(0),       // 0: auth.orchestrator.AuthRequestFieldMask
	(AuthRequestStatus)(0),          // 1: auth.orchestrator.AuthRequestStatus
	(FlowName)(0),                   // 2: auth.orchestrator.FlowName
	(Provenance)(0),                 // 3: auth.orchestrator.Provenance
	(*AuthRequest)(nil),             // 4: auth.orchestrator.AuthRequest
	(*Preferences)(nil),             // 5: auth.orchestrator.Preferences
	(*LivenessPreferences)(nil),     // 6: auth.orchestrator.LivenessPreferences
	(*SmsOtpPreferences)(nil),       // 7: auth.orchestrator.SmsOtpPreferences
	(*LivenessScreenData)(nil),      // 8: auth.orchestrator.LivenessScreenData
	(*deeplink.Deeplink)(nil),       // 9: frontend.deeplink.Deeplink
	(*timestamppb.Timestamp)(nil),   // 10: google.protobuf.Timestamp
	(auth.GenerateOTPFlow)(0),       // 11: auth.GenerateOTPFlow
	(*common.VisualElement)(nil),    // 12: api.typesv2.common.VisualElement
	(*common.Text)(nil),             // 13: api.typesv2.common.Text
	(*widget.BackgroundColour)(nil), // 14: api.typesv2.common.ui.widget.BackgroundColour
}
var file_api_auth_orchestrator_internal_auth_request_proto_depIdxs = []int32{
	2,  // 0: auth.orchestrator.AuthRequest.flow_name:type_name -> auth.orchestrator.FlowName
	9,  // 1: auth.orchestrator.AuthRequest.next_action:type_name -> frontend.deeplink.Deeplink
	9,  // 2: auth.orchestrator.AuthRequest.redirect_action:type_name -> frontend.deeplink.Deeplink
	1,  // 3: auth.orchestrator.AuthRequest.status:type_name -> auth.orchestrator.AuthRequestStatus
	3,  // 4: auth.orchestrator.AuthRequest.provenance:type_name -> auth.orchestrator.Provenance
	10, // 5: auth.orchestrator.AuthRequest.created_at:type_name -> google.protobuf.Timestamp
	10, // 6: auth.orchestrator.AuthRequest.updated_at:type_name -> google.protobuf.Timestamp
	10, // 7: auth.orchestrator.AuthRequest.deleted_at:type_name -> google.protobuf.Timestamp
	5,  // 8: auth.orchestrator.AuthRequest.preferences:type_name -> auth.orchestrator.Preferences
	7,  // 9: auth.orchestrator.Preferences.sms_otp_preferences:type_name -> auth.orchestrator.SmsOtpPreferences
	6,  // 10: auth.orchestrator.Preferences.liveness_preferences:type_name -> auth.orchestrator.LivenessPreferences
	8,  // 11: auth.orchestrator.LivenessPreferences.liveness_screen_data:type_name -> auth.orchestrator.LivenessScreenData
	11, // 12: auth.orchestrator.SmsOtpPreferences.generate_otp_flow:type_name -> auth.GenerateOTPFlow
	12, // 13: auth.orchestrator.LivenessScreenData.image:type_name -> api.typesv2.common.VisualElement
	13, // 14: auth.orchestrator.LivenessScreenData.title:type_name -> api.typesv2.common.Text
	13, // 15: auth.orchestrator.LivenessScreenData.subtitle:type_name -> api.typesv2.common.Text
	13, // 16: auth.orchestrator.LivenessScreenData.list_items:type_name -> api.typesv2.common.Text
	14, // 17: auth.orchestrator.LivenessScreenData.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	18, // [18:18] is the sub-list for method output_type
	18, // [18:18] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_api_auth_orchestrator_internal_auth_request_proto_init() }
func file_api_auth_orchestrator_internal_auth_request_proto_init() {
	if File_api_auth_orchestrator_internal_auth_request_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Preferences); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessPreferences); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmsOtpPreferences); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_internal_auth_request_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessScreenData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_orchestrator_internal_auth_request_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_orchestrator_internal_auth_request_proto_goTypes,
		DependencyIndexes: file_api_auth_orchestrator_internal_auth_request_proto_depIdxs,
		EnumInfos:         file_api_auth_orchestrator_internal_auth_request_proto_enumTypes,
		MessageInfos:      file_api_auth_orchestrator_internal_auth_request_proto_msgTypes,
	}.Build()
	File_api_auth_orchestrator_internal_auth_request_proto = out.File
	file_api_auth_orchestrator_internal_auth_request_proto_rawDesc = nil
	file_api_auth_orchestrator_internal_auth_request_proto_goTypes = nil
	file_api_auth_orchestrator_internal_auth_request_proto_depIdxs = nil
}
