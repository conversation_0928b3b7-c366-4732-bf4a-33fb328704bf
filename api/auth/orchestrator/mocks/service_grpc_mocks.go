// Code generated by MockGen. DO NOT EDIT.
// Source: api/auth/orchestrator/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	orchestrator "github.com/epifi/gamma/api/auth/orchestrator"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockOrchestratorClient is a mock of OrchestratorClient interface.
type MockOrchestratorClient struct {
	ctrl     *gomock.Controller
	recorder *MockOrchestratorClientMockRecorder
}

// MockOrchestratorClientMockRecorder is the mock recorder for MockOrchestratorClient.
type MockOrchestratorClientMockRecorder struct {
	mock *MockOrchestratorClient
}

// NewMockOrchestratorClient creates a new mock instance.
func NewMockOrchestratorClient(ctrl *gomock.Controller) *MockOrchestratorClient {
	mock := &MockOrchestratorClient{ctrl: ctrl}
	mock.recorder = &MockOrchestratorClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrchestratorClient) EXPECT() *MockOrchestratorClientMockRecorder {
	return m.recorder
}

// GenerateOtp mocks base method.
func (m *MockOrchestratorClient) GenerateOtp(ctx context.Context, in *orchestrator.GenerateOtpRequest, opts ...grpc.CallOption) (*orchestrator.GenerateOtpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateOtp", varargs...)
	ret0, _ := ret[0].(*orchestrator.GenerateOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateOtp indicates an expected call of GenerateOtp.
func (mr *MockOrchestratorClientMockRecorder) GenerateOtp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateOtp", reflect.TypeOf((*MockOrchestratorClient)(nil).GenerateOtp), varargs...)
}

// GetAuthFlowStatus mocks base method.
func (m *MockOrchestratorClient) GetAuthFlowStatus(ctx context.Context, in *orchestrator.GetAuthFlowStatusRequest, opts ...grpc.CallOption) (*orchestrator.GetAuthFlowStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAuthFlowStatus", varargs...)
	ret0, _ := ret[0].(*orchestrator.GetAuthFlowStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthFlowStatus indicates an expected call of GetAuthFlowStatus.
func (mr *MockOrchestratorClientMockRecorder) GetAuthFlowStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthFlowStatus", reflect.TypeOf((*MockOrchestratorClient)(nil).GetAuthFlowStatus), varargs...)
}

// GetAuthStageRefId mocks base method.
func (m *MockOrchestratorClient) GetAuthStageRefId(ctx context.Context, in *orchestrator.GetAuthStageRefIdRequest, opts ...grpc.CallOption) (*orchestrator.GetAuthStageRefIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAuthStageRefId", varargs...)
	ret0, _ := ret[0].(*orchestrator.GetAuthStageRefIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthStageRefId indicates an expected call of GetAuthStageRefId.
func (mr *MockOrchestratorClientMockRecorder) GetAuthStageRefId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthStageRefId", reflect.TypeOf((*MockOrchestratorClient)(nil).GetAuthStageRefId), varargs...)
}

// GetLivenessSummaryStatus mocks base method.
func (m *MockOrchestratorClient) GetLivenessSummaryStatus(ctx context.Context, in *orchestrator.GetLivenessSummaryStatusRequest, opts ...grpc.CallOption) (*orchestrator.GetLivenessSummaryStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLivenessSummaryStatus", varargs...)
	ret0, _ := ret[0].(*orchestrator.GetLivenessSummaryStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivenessSummaryStatus indicates an expected call of GetLivenessSummaryStatus.
func (mr *MockOrchestratorClientMockRecorder) GetLivenessSummaryStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivenessSummaryStatus", reflect.TypeOf((*MockOrchestratorClient)(nil).GetLivenessSummaryStatus), varargs...)
}

// InitiateAuthFlow mocks base method.
func (m *MockOrchestratorClient) InitiateAuthFlow(ctx context.Context, in *orchestrator.InitiateAuthFlowRequest, opts ...grpc.CallOption) (*orchestrator.InitiateAuthFlowResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateAuthFlow", varargs...)
	ret0, _ := ret[0].(*orchestrator.InitiateAuthFlowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateAuthFlow indicates an expected call of InitiateAuthFlow.
func (mr *MockOrchestratorClientMockRecorder) InitiateAuthFlow(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateAuthFlow", reflect.TypeOf((*MockOrchestratorClient)(nil).InitiateAuthFlow), varargs...)
}

// ValidateNpciPin mocks base method.
func (m *MockOrchestratorClient) ValidateNpciPin(ctx context.Context, in *orchestrator.ValidateNpciPinRequest, opts ...grpc.CallOption) (*orchestrator.ValidateNpciPinResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValidateNpciPin", varargs...)
	ret0, _ := ret[0].(*orchestrator.ValidateNpciPinResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateNpciPin indicates an expected call of ValidateNpciPin.
func (mr *MockOrchestratorClientMockRecorder) ValidateNpciPin(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateNpciPin", reflect.TypeOf((*MockOrchestratorClient)(nil).ValidateNpciPin), varargs...)
}

// VerifyOtp mocks base method.
func (m *MockOrchestratorClient) VerifyOtp(ctx context.Context, in *orchestrator.VerifyOtpRequest, opts ...grpc.CallOption) (*orchestrator.VerifyOtpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyOtp", varargs...)
	ret0, _ := ret[0].(*orchestrator.VerifyOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyOtp indicates an expected call of VerifyOtp.
func (mr *MockOrchestratorClientMockRecorder) VerifyOtp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyOtp", reflect.TypeOf((*MockOrchestratorClient)(nil).VerifyOtp), varargs...)
}

// MockOrchestratorServer is a mock of OrchestratorServer interface.
type MockOrchestratorServer struct {
	ctrl     *gomock.Controller
	recorder *MockOrchestratorServerMockRecorder
}

// MockOrchestratorServerMockRecorder is the mock recorder for MockOrchestratorServer.
type MockOrchestratorServerMockRecorder struct {
	mock *MockOrchestratorServer
}

// NewMockOrchestratorServer creates a new mock instance.
func NewMockOrchestratorServer(ctrl *gomock.Controller) *MockOrchestratorServer {
	mock := &MockOrchestratorServer{ctrl: ctrl}
	mock.recorder = &MockOrchestratorServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrchestratorServer) EXPECT() *MockOrchestratorServerMockRecorder {
	return m.recorder
}

// GenerateOtp mocks base method.
func (m *MockOrchestratorServer) GenerateOtp(arg0 context.Context, arg1 *orchestrator.GenerateOtpRequest) (*orchestrator.GenerateOtpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateOtp", arg0, arg1)
	ret0, _ := ret[0].(*orchestrator.GenerateOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateOtp indicates an expected call of GenerateOtp.
func (mr *MockOrchestratorServerMockRecorder) GenerateOtp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateOtp", reflect.TypeOf((*MockOrchestratorServer)(nil).GenerateOtp), arg0, arg1)
}

// GetAuthFlowStatus mocks base method.
func (m *MockOrchestratorServer) GetAuthFlowStatus(arg0 context.Context, arg1 *orchestrator.GetAuthFlowStatusRequest) (*orchestrator.GetAuthFlowStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthFlowStatus", arg0, arg1)
	ret0, _ := ret[0].(*orchestrator.GetAuthFlowStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthFlowStatus indicates an expected call of GetAuthFlowStatus.
func (mr *MockOrchestratorServerMockRecorder) GetAuthFlowStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthFlowStatus", reflect.TypeOf((*MockOrchestratorServer)(nil).GetAuthFlowStatus), arg0, arg1)
}

// GetAuthStageRefId mocks base method.
func (m *MockOrchestratorServer) GetAuthStageRefId(arg0 context.Context, arg1 *orchestrator.GetAuthStageRefIdRequest) (*orchestrator.GetAuthStageRefIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthStageRefId", arg0, arg1)
	ret0, _ := ret[0].(*orchestrator.GetAuthStageRefIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthStageRefId indicates an expected call of GetAuthStageRefId.
func (mr *MockOrchestratorServerMockRecorder) GetAuthStageRefId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthStageRefId", reflect.TypeOf((*MockOrchestratorServer)(nil).GetAuthStageRefId), arg0, arg1)
}

// GetLivenessSummaryStatus mocks base method.
func (m *MockOrchestratorServer) GetLivenessSummaryStatus(arg0 context.Context, arg1 *orchestrator.GetLivenessSummaryStatusRequest) (*orchestrator.GetLivenessSummaryStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLivenessSummaryStatus", arg0, arg1)
	ret0, _ := ret[0].(*orchestrator.GetLivenessSummaryStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivenessSummaryStatus indicates an expected call of GetLivenessSummaryStatus.
func (mr *MockOrchestratorServerMockRecorder) GetLivenessSummaryStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivenessSummaryStatus", reflect.TypeOf((*MockOrchestratorServer)(nil).GetLivenessSummaryStatus), arg0, arg1)
}

// InitiateAuthFlow mocks base method.
func (m *MockOrchestratorServer) InitiateAuthFlow(arg0 context.Context, arg1 *orchestrator.InitiateAuthFlowRequest) (*orchestrator.InitiateAuthFlowResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateAuthFlow", arg0, arg1)
	ret0, _ := ret[0].(*orchestrator.InitiateAuthFlowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateAuthFlow indicates an expected call of InitiateAuthFlow.
func (mr *MockOrchestratorServerMockRecorder) InitiateAuthFlow(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateAuthFlow", reflect.TypeOf((*MockOrchestratorServer)(nil).InitiateAuthFlow), arg0, arg1)
}

// ValidateNpciPin mocks base method.
func (m *MockOrchestratorServer) ValidateNpciPin(arg0 context.Context, arg1 *orchestrator.ValidateNpciPinRequest) (*orchestrator.ValidateNpciPinResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateNpciPin", arg0, arg1)
	ret0, _ := ret[0].(*orchestrator.ValidateNpciPinResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateNpciPin indicates an expected call of ValidateNpciPin.
func (mr *MockOrchestratorServerMockRecorder) ValidateNpciPin(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateNpciPin", reflect.TypeOf((*MockOrchestratorServer)(nil).ValidateNpciPin), arg0, arg1)
}

// VerifyOtp mocks base method.
func (m *MockOrchestratorServer) VerifyOtp(arg0 context.Context, arg1 *orchestrator.VerifyOtpRequest) (*orchestrator.VerifyOtpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyOtp", arg0, arg1)
	ret0, _ := ret[0].(*orchestrator.VerifyOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyOtp indicates an expected call of VerifyOtp.
func (mr *MockOrchestratorServerMockRecorder) VerifyOtp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyOtp", reflect.TypeOf((*MockOrchestratorServer)(nil).VerifyOtp), arg0, arg1)
}

// MockUnsafeOrchestratorServer is a mock of UnsafeOrchestratorServer interface.
type MockUnsafeOrchestratorServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeOrchestratorServerMockRecorder
}

// MockUnsafeOrchestratorServerMockRecorder is the mock recorder for MockUnsafeOrchestratorServer.
type MockUnsafeOrchestratorServerMockRecorder struct {
	mock *MockUnsafeOrchestratorServer
}

// NewMockUnsafeOrchestratorServer creates a new mock instance.
func NewMockUnsafeOrchestratorServer(ctrl *gomock.Controller) *MockUnsafeOrchestratorServer {
	mock := &MockUnsafeOrchestratorServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeOrchestratorServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeOrchestratorServer) EXPECT() *MockUnsafeOrchestratorServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedOrchestratorServer mocks base method.
func (m *MockUnsafeOrchestratorServer) mustEmbedUnimplementedOrchestratorServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedOrchestratorServer")
}

// mustEmbedUnimplementedOrchestratorServer indicates an expected call of mustEmbedUnimplementedOrchestratorServer.
func (mr *MockUnsafeOrchestratorServerMockRecorder) mustEmbedUnimplementedOrchestratorServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedOrchestratorServer", reflect.TypeOf((*MockUnsafeOrchestratorServer)(nil).mustEmbedUnimplementedOrchestratorServer))
}
