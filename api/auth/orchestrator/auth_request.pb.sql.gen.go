// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/auth/orchestrator/auth_request.pb.go

package orchestrator

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"encoding/json"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Valuer interface implementation for storing the AuthRequestStatus in string format in DB
func (p AuthRequestStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing AuthRequestStatus while reading from DB
func (p *AuthRequestStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := AuthRequestStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected AuthRequestStatus value: %s", val)
	}
	*p = AuthRequestStatus(valInt)
	return nil
}

// Marshaler interface implementation for AuthRequestStatus
func (x AuthRequestStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for AuthRequestStatus
func (x *AuthRequestStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = AuthRequestStatus(AuthRequestStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the FlowName in string format in DB
func (p FlowName) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing FlowName while reading from DB
func (p *FlowName) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := FlowName_value[val]
	if !ok {
		return fmt.Errorf("unexpected FlowName value: %s", val)
	}
	*p = FlowName(valInt)
	return nil
}

// Marshaler interface implementation for FlowName
func (x FlowName) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for FlowName
func (x *FlowName) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = FlowName(FlowName_value[val])
	return nil
}

// Valuer interface implementation for storing the Provenance in string format in DB
func (p Provenance) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Provenance while reading from DB
func (p *Provenance) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Provenance_value[val]
	if !ok {
		return fmt.Errorf("unexpected Provenance value: %s", val)
	}
	*p = Provenance(valInt)
	return nil
}

// Marshaler interface implementation for Provenance
func (x Provenance) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Provenance
func (x *Provenance) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Provenance(Provenance_value[val])
	return nil
}

// Scanner interface implementation for parsing Preferences while reading from DB
func (a *Preferences) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the Preferences in string format in DB
func (a *Preferences) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for Preferences
func (a *Preferences) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for Preferences
func (a *Preferences) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
