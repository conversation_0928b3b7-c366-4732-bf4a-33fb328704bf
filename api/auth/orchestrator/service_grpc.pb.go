// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/auth/orchestrator/service.proto

package orchestrator

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Orchestrator_InitiateAuthFlow_FullMethodName         = "/auth.orchestrator.Orchestrator/InitiateAuthFlow"
	Orchestrator_GetAuthFlowStatus_FullMethodName        = "/auth.orchestrator.Orchestrator/GetAuthFlowStatus"
	Orchestrator_GenerateOtp_FullMethodName              = "/auth.orchestrator.Orchestrator/GenerateOtp"
	Orchestrator_ValidateNpciPin_FullMethodName          = "/auth.orchestrator.Orchestrator/ValidateNpciPin"
	Orchestrator_VerifyOtp_FullMethodName                = "/auth.orchestrator.Orchestrator/VerifyOtp"
	Orchestrator_GetLivenessSummaryStatus_FullMethodName = "/auth.orchestrator.Orchestrator/GetLivenessSummaryStatus"
	Orchestrator_GetAuthStageRefId_FullMethodName        = "/auth.orchestrator.Orchestrator/GetAuthStageRefId"
)

// OrchestratorClient is the client API for Orchestrator service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrchestratorClient interface {
	// InitiateAuthFlow creates an auth request and initiates workflow based on the flow name
	InitiateAuthFlow(ctx context.Context, in *InitiateAuthFlowRequest, opts ...grpc.CallOption) (*InitiateAuthFlowResponse, error)
	// GetAuthFlowStatus gets status of auth request and returns deep link
	GetAuthFlowStatus(ctx context.Context, in *GetAuthFlowStatusRequest, opts ...grpc.CallOption) (*GetAuthFlowStatusResponse, error)
	// GenerateOtp generates otp
	GenerateOtp(ctx context.Context, in *GenerateOtpRequest, opts ...grpc.CallOption) (*GenerateOtpResponse, error)
	// ValidateNpciPin validates npci secure pin
	ValidateNpciPin(ctx context.Context, in *ValidateNpciPinRequest, opts ...grpc.CallOption) (*ValidateNpciPinResponse, error)
	// VerifyOtp verifies otp
	VerifyOtp(ctx context.Context, in *VerifyOtpRequest, opts ...grpc.CallOption) (*VerifyOtpResponse, error)
	// GetLivenessSummaryStatus gets liveness summary and returns corresponding next action and status
	GetLivenessSummaryStatus(ctx context.Context, in *GetLivenessSummaryStatusRequest, opts ...grpc.CallOption) (*GetLivenessSummaryStatusResponse, error)
	// rpc to get referenceId of any auth stage, for eg: OTP, livenessSummary etc
	GetAuthStageRefId(ctx context.Context, in *GetAuthStageRefIdRequest, opts ...grpc.CallOption) (*GetAuthStageRefIdResponse, error)
}

type orchestratorClient struct {
	cc grpc.ClientConnInterface
}

func NewOrchestratorClient(cc grpc.ClientConnInterface) OrchestratorClient {
	return &orchestratorClient{cc}
}

func (c *orchestratorClient) InitiateAuthFlow(ctx context.Context, in *InitiateAuthFlowRequest, opts ...grpc.CallOption) (*InitiateAuthFlowResponse, error) {
	out := new(InitiateAuthFlowResponse)
	err := c.cc.Invoke(ctx, Orchestrator_InitiateAuthFlow_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orchestratorClient) GetAuthFlowStatus(ctx context.Context, in *GetAuthFlowStatusRequest, opts ...grpc.CallOption) (*GetAuthFlowStatusResponse, error) {
	out := new(GetAuthFlowStatusResponse)
	err := c.cc.Invoke(ctx, Orchestrator_GetAuthFlowStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orchestratorClient) GenerateOtp(ctx context.Context, in *GenerateOtpRequest, opts ...grpc.CallOption) (*GenerateOtpResponse, error) {
	out := new(GenerateOtpResponse)
	err := c.cc.Invoke(ctx, Orchestrator_GenerateOtp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orchestratorClient) ValidateNpciPin(ctx context.Context, in *ValidateNpciPinRequest, opts ...grpc.CallOption) (*ValidateNpciPinResponse, error) {
	out := new(ValidateNpciPinResponse)
	err := c.cc.Invoke(ctx, Orchestrator_ValidateNpciPin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orchestratorClient) VerifyOtp(ctx context.Context, in *VerifyOtpRequest, opts ...grpc.CallOption) (*VerifyOtpResponse, error) {
	out := new(VerifyOtpResponse)
	err := c.cc.Invoke(ctx, Orchestrator_VerifyOtp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orchestratorClient) GetLivenessSummaryStatus(ctx context.Context, in *GetLivenessSummaryStatusRequest, opts ...grpc.CallOption) (*GetLivenessSummaryStatusResponse, error) {
	out := new(GetLivenessSummaryStatusResponse)
	err := c.cc.Invoke(ctx, Orchestrator_GetLivenessSummaryStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orchestratorClient) GetAuthStageRefId(ctx context.Context, in *GetAuthStageRefIdRequest, opts ...grpc.CallOption) (*GetAuthStageRefIdResponse, error) {
	out := new(GetAuthStageRefIdResponse)
	err := c.cc.Invoke(ctx, Orchestrator_GetAuthStageRefId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrchestratorServer is the server API for Orchestrator service.
// All implementations should embed UnimplementedOrchestratorServer
// for forward compatibility
type OrchestratorServer interface {
	// InitiateAuthFlow creates an auth request and initiates workflow based on the flow name
	InitiateAuthFlow(context.Context, *InitiateAuthFlowRequest) (*InitiateAuthFlowResponse, error)
	// GetAuthFlowStatus gets status of auth request and returns deep link
	GetAuthFlowStatus(context.Context, *GetAuthFlowStatusRequest) (*GetAuthFlowStatusResponse, error)
	// GenerateOtp generates otp
	GenerateOtp(context.Context, *GenerateOtpRequest) (*GenerateOtpResponse, error)
	// ValidateNpciPin validates npci secure pin
	ValidateNpciPin(context.Context, *ValidateNpciPinRequest) (*ValidateNpciPinResponse, error)
	// VerifyOtp verifies otp
	VerifyOtp(context.Context, *VerifyOtpRequest) (*VerifyOtpResponse, error)
	// GetLivenessSummaryStatus gets liveness summary and returns corresponding next action and status
	GetLivenessSummaryStatus(context.Context, *GetLivenessSummaryStatusRequest) (*GetLivenessSummaryStatusResponse, error)
	// rpc to get referenceId of any auth stage, for eg: OTP, livenessSummary etc
	GetAuthStageRefId(context.Context, *GetAuthStageRefIdRequest) (*GetAuthStageRefIdResponse, error)
}

// UnimplementedOrchestratorServer should be embedded to have forward compatible implementations.
type UnimplementedOrchestratorServer struct {
}

func (UnimplementedOrchestratorServer) InitiateAuthFlow(context.Context, *InitiateAuthFlowRequest) (*InitiateAuthFlowResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateAuthFlow not implemented")
}
func (UnimplementedOrchestratorServer) GetAuthFlowStatus(context.Context, *GetAuthFlowStatusRequest) (*GetAuthFlowStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthFlowStatus not implemented")
}
func (UnimplementedOrchestratorServer) GenerateOtp(context.Context, *GenerateOtpRequest) (*GenerateOtpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateOtp not implemented")
}
func (UnimplementedOrchestratorServer) ValidateNpciPin(context.Context, *ValidateNpciPinRequest) (*ValidateNpciPinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateNpciPin not implemented")
}
func (UnimplementedOrchestratorServer) VerifyOtp(context.Context, *VerifyOtpRequest) (*VerifyOtpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyOtp not implemented")
}
func (UnimplementedOrchestratorServer) GetLivenessSummaryStatus(context.Context, *GetLivenessSummaryStatusRequest) (*GetLivenessSummaryStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLivenessSummaryStatus not implemented")
}
func (UnimplementedOrchestratorServer) GetAuthStageRefId(context.Context, *GetAuthStageRefIdRequest) (*GetAuthStageRefIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthStageRefId not implemented")
}

// UnsafeOrchestratorServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrchestratorServer will
// result in compilation errors.
type UnsafeOrchestratorServer interface {
	mustEmbedUnimplementedOrchestratorServer()
}

func RegisterOrchestratorServer(s grpc.ServiceRegistrar, srv OrchestratorServer) {
	s.RegisterService(&Orchestrator_ServiceDesc, srv)
}

func _Orchestrator_InitiateAuthFlow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateAuthFlowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrchestratorServer).InitiateAuthFlow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Orchestrator_InitiateAuthFlow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrchestratorServer).InitiateAuthFlow(ctx, req.(*InitiateAuthFlowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Orchestrator_GetAuthFlowStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthFlowStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrchestratorServer).GetAuthFlowStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Orchestrator_GetAuthFlowStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrchestratorServer).GetAuthFlowStatus(ctx, req.(*GetAuthFlowStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Orchestrator_GenerateOtp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateOtpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrchestratorServer).GenerateOtp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Orchestrator_GenerateOtp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrchestratorServer).GenerateOtp(ctx, req.(*GenerateOtpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Orchestrator_ValidateNpciPin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateNpciPinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrchestratorServer).ValidateNpciPin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Orchestrator_ValidateNpciPin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrchestratorServer).ValidateNpciPin(ctx, req.(*ValidateNpciPinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Orchestrator_VerifyOtp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyOtpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrchestratorServer).VerifyOtp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Orchestrator_VerifyOtp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrchestratorServer).VerifyOtp(ctx, req.(*VerifyOtpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Orchestrator_GetLivenessSummaryStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLivenessSummaryStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrchestratorServer).GetLivenessSummaryStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Orchestrator_GetLivenessSummaryStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrchestratorServer).GetLivenessSummaryStatus(ctx, req.(*GetLivenessSummaryStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Orchestrator_GetAuthStageRefId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthStageRefIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrchestratorServer).GetAuthStageRefId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Orchestrator_GetAuthStageRefId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrchestratorServer).GetAuthStageRefId(ctx, req.(*GetAuthStageRefIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Orchestrator_ServiceDesc is the grpc.ServiceDesc for Orchestrator service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Orchestrator_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "auth.orchestrator.Orchestrator",
	HandlerType: (*OrchestratorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InitiateAuthFlow",
			Handler:    _Orchestrator_InitiateAuthFlow_Handler,
		},
		{
			MethodName: "GetAuthFlowStatus",
			Handler:    _Orchestrator_GetAuthFlowStatus_Handler,
		},
		{
			MethodName: "GenerateOtp",
			Handler:    _Orchestrator_GenerateOtp_Handler,
		},
		{
			MethodName: "ValidateNpciPin",
			Handler:    _Orchestrator_ValidateNpciPin_Handler,
		},
		{
			MethodName: "VerifyOtp",
			Handler:    _Orchestrator_VerifyOtp_Handler,
		},
		{
			MethodName: "GetLivenessSummaryStatus",
			Handler:    _Orchestrator_GetLivenessSummaryStatus_Handler,
		},
		{
			MethodName: "GetAuthStageRefId",
			Handler:    _Orchestrator_GetAuthStageRefId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/auth/orchestrator/service.proto",
}
