// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/orchestrator/internal/auth_request.proto

package orchestrator

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	auth "github.com/epifi/gamma/api/auth"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = auth.GenerateOTPFlow(0)
)

// Validate checks the field values on AuthRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AuthRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AuthRequestMultiError, or
// nil if none found.
func (m *AuthRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for ClientRequestId

	// no validation rules for FlowName

	// no validation rules for OrchId

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthRequestValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthRequestValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthRequestValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRedirectAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthRequestValidationError{
					field:  "RedirectAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthRequestValidationError{
					field:  "RedirectAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirectAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthRequestValidationError{
				field:  "RedirectAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for Provenance

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthRequestValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthRequestValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthRequestValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPreferences()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthRequestValidationError{
					field:  "Preferences",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthRequestValidationError{
					field:  "Preferences",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreferences()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthRequestValidationError{
				field:  "Preferences",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthRequestMultiError(errors)
	}

	return nil
}

// AuthRequestMultiError is an error wrapping multiple validation errors
// returned by AuthRequest.ValidateAll() if the designated constraints aren't met.
type AuthRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthRequestMultiError) AllErrors() []error { return m }

// AuthRequestValidationError is the validation error returned by
// AuthRequest.Validate if the designated constraints aren't met.
type AuthRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthRequestValidationError) ErrorName() string { return "AuthRequestValidationError" }

// Error satisfies the builtin error interface
func (e AuthRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthRequestValidationError{}

// Validate checks the field values on Preferences with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Preferences) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Preferences with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PreferencesMultiError, or
// nil if none found.
func (m *Preferences) ValidateAll() error {
	return m.validate(true)
}

func (m *Preferences) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSmsOtpPreferences()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreferencesValidationError{
					field:  "SmsOtpPreferences",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreferencesValidationError{
					field:  "SmsOtpPreferences",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSmsOtpPreferences()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreferencesValidationError{
				field:  "SmsOtpPreferences",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLivenessPreferences()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreferencesValidationError{
					field:  "LivenessPreferences",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreferencesValidationError{
					field:  "LivenessPreferences",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLivenessPreferences()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreferencesValidationError{
				field:  "LivenessPreferences",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UseLivenessV2

	if len(errors) > 0 {
		return PreferencesMultiError(errors)
	}

	return nil
}

// PreferencesMultiError is an error wrapping multiple validation errors
// returned by Preferences.ValidateAll() if the designated constraints aren't met.
type PreferencesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreferencesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreferencesMultiError) AllErrors() []error { return m }

// PreferencesValidationError is the validation error returned by
// Preferences.Validate if the designated constraints aren't met.
type PreferencesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreferencesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreferencesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreferencesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreferencesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreferencesValidationError) ErrorName() string { return "PreferencesValidationError" }

// Error satisfies the builtin error interface
func (e PreferencesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreferences.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreferencesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreferencesValidationError{}

// Validate checks the field values on LivenessPreferences with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LivenessPreferences) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessPreferences with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessPreferencesMultiError, or nil if none found.
func (m *LivenessPreferences) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessPreferences) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FacematchImagePath

	// no validation rules for Base64EncodedFmImage

	if all {
		switch v := interface{}(m.GetLivenessScreenData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessPreferencesValidationError{
					field:  "LivenessScreenData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessPreferencesValidationError{
					field:  "LivenessScreenData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLivenessScreenData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessPreferencesValidationError{
				field:  "LivenessScreenData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LivenessPreferencesMultiError(errors)
	}

	return nil
}

// LivenessPreferencesMultiError is an error wrapping multiple validation
// errors returned by LivenessPreferences.ValidateAll() if the designated
// constraints aren't met.
type LivenessPreferencesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessPreferencesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessPreferencesMultiError) AllErrors() []error { return m }

// LivenessPreferencesValidationError is the validation error returned by
// LivenessPreferences.Validate if the designated constraints aren't met.
type LivenessPreferencesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessPreferencesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessPreferencesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessPreferencesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessPreferencesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessPreferencesValidationError) ErrorName() string {
	return "LivenessPreferencesValidationError"
}

// Error satisfies the builtin error interface
func (e LivenessPreferencesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessPreferences.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessPreferencesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessPreferencesValidationError{}

// Validate checks the field values on SmsOtpPreferences with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SmsOtpPreferences) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SmsOtpPreferences with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SmsOtpPreferencesMultiError, or nil if none found.
func (m *SmsOtpPreferences) ValidateAll() error {
	return m.validate(true)
}

func (m *SmsOtpPreferences) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GenerateOtpFlow

	if len(errors) > 0 {
		return SmsOtpPreferencesMultiError(errors)
	}

	return nil
}

// SmsOtpPreferencesMultiError is an error wrapping multiple validation errors
// returned by SmsOtpPreferences.ValidateAll() if the designated constraints
// aren't met.
type SmsOtpPreferencesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SmsOtpPreferencesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SmsOtpPreferencesMultiError) AllErrors() []error { return m }

// SmsOtpPreferencesValidationError is the validation error returned by
// SmsOtpPreferences.Validate if the designated constraints aren't met.
type SmsOtpPreferencesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SmsOtpPreferencesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SmsOtpPreferencesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SmsOtpPreferencesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SmsOtpPreferencesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SmsOtpPreferencesValidationError) ErrorName() string {
	return "SmsOtpPreferencesValidationError"
}

// Error satisfies the builtin error interface
func (e SmsOtpPreferencesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSmsOtpPreferences.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SmsOtpPreferencesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SmsOtpPreferencesValidationError{}

// Validate checks the field values on LivenessScreenData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LivenessScreenData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessScreenData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessScreenDataMultiError, or nil if none found.
func (m *LivenessScreenData) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessScreenData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessScreenDataValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessScreenDataValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessScreenDataValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessScreenDataValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessScreenDataValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessScreenDataValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessScreenDataValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessScreenDataValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessScreenDataValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetListItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LivenessScreenDataValidationError{
						field:  fmt.Sprintf("ListItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LivenessScreenDataValidationError{
						field:  fmt.Sprintf("ListItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LivenessScreenDataValidationError{
					field:  fmt.Sprintf("ListItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessScreenDataValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessScreenDataValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessScreenDataValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LivenessScreenDataMultiError(errors)
	}

	return nil
}

// LivenessScreenDataMultiError is an error wrapping multiple validation errors
// returned by LivenessScreenData.ValidateAll() if the designated constraints
// aren't met.
type LivenessScreenDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessScreenDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessScreenDataMultiError) AllErrors() []error { return m }

// LivenessScreenDataValidationError is the validation error returned by
// LivenessScreenData.Validate if the designated constraints aren't met.
type LivenessScreenDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessScreenDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessScreenDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessScreenDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessScreenDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessScreenDataValidationError) ErrorName() string {
	return "LivenessScreenDataValidationError"
}

// Error satisfies the builtin error interface
func (e LivenessScreenDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessScreenData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessScreenDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessScreenDataValidationError{}
