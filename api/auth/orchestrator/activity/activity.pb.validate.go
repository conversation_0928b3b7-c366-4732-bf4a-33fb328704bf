// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/orchestrator/activity/activity.proto

package activity

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	biometrics "github.com/epifi/gamma/api/auth/biometrics"

	orchestrator "github.com/epifi/gamma/api/auth/orchestrator"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = biometrics.BiometricStatus(0)

	_ = orchestrator.AuthStage(0)
)

// Validate checks the field values on AuthRequestStageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthRequestStageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthRequestStageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthRequestStageRequestMultiError, or nil if none found.
func (m *AuthRequestStageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthRequestStageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthRequestStageRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthRequestStageRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthRequestStageRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Stage

	// no validation rules for Status

	// no validation rules for AuthRefId

	if len(errors) > 0 {
		return AuthRequestStageRequestMultiError(errors)
	}

	return nil
}

// AuthRequestStageRequestMultiError is an error wrapping multiple validation
// errors returned by AuthRequestStageRequest.ValidateAll() if the designated
// constraints aren't met.
type AuthRequestStageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthRequestStageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthRequestStageRequestMultiError) AllErrors() []error { return m }

// AuthRequestStageRequestValidationError is the validation error returned by
// AuthRequestStageRequest.Validate if the designated constraints aren't met.
type AuthRequestStageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthRequestStageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthRequestStageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthRequestStageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthRequestStageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthRequestStageRequestValidationError) ErrorName() string {
	return "AuthRequestStageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AuthRequestStageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthRequestStageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthRequestStageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthRequestStageRequestValidationError{}

// Validate checks the field values on AuthRequestStageResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthRequestStageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthRequestStageResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthRequestStageResponseMultiError, or nil if none found.
func (m *AuthRequestStageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthRequestStageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthRequestStageResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthRequestStageResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthRequestStageResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthRequestStageResponseMultiError(errors)
	}

	return nil
}

// AuthRequestStageResponseMultiError is an error wrapping multiple validation
// errors returned by AuthRequestStageResponse.ValidateAll() if the designated
// constraints aren't met.
type AuthRequestStageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthRequestStageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthRequestStageResponseMultiError) AllErrors() []error { return m }

// AuthRequestStageResponseValidationError is the validation error returned by
// AuthRequestStageResponse.Validate if the designated constraints aren't met.
type AuthRequestStageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthRequestStageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthRequestStageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthRequestStageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthRequestStageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthRequestStageResponseValidationError) ErrorName() string {
	return "AuthRequestStageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AuthRequestStageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthRequestStageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthRequestStageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthRequestStageResponseValidationError{}

// Validate checks the field values on OrchestratorActivityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OrchestratorActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrchestratorActivityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OrchestratorActivityRequestMultiError, or nil if none found.
func (m *OrchestratorActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *OrchestratorActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrchestratorActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrchestratorActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrchestratorActivityRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OrchestratorActivityRequestMultiError(errors)
	}

	return nil
}

// OrchestratorActivityRequestMultiError is an error wrapping multiple
// validation errors returned by OrchestratorActivityRequest.ValidateAll() if
// the designated constraints aren't met.
type OrchestratorActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrchestratorActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrchestratorActivityRequestMultiError) AllErrors() []error { return m }

// OrchestratorActivityRequestValidationError is the validation error returned
// by OrchestratorActivityRequest.Validate if the designated constraints
// aren't met.
type OrchestratorActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrchestratorActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrchestratorActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrchestratorActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrchestratorActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrchestratorActivityRequestValidationError) ErrorName() string {
	return "OrchestratorActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e OrchestratorActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrchestratorActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrchestratorActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrchestratorActivityRequestValidationError{}

// Validate checks the field values on OrchestratorActivityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OrchestratorActivityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrchestratorActivityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OrchestratorActivityResponseMultiError, or nil if none found.
func (m *OrchestratorActivityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *OrchestratorActivityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrchestratorActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrchestratorActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrchestratorActivityResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuthRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrchestratorActivityResponseValidationError{
					field:  "AuthRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrchestratorActivityResponseValidationError{
					field:  "AuthRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrchestratorActivityResponseValidationError{
				field:  "AuthRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OrchestratorActivityResponseMultiError(errors)
	}

	return nil
}

// OrchestratorActivityResponseMultiError is an error wrapping multiple
// validation errors returned by OrchestratorActivityResponse.ValidateAll() if
// the designated constraints aren't met.
type OrchestratorActivityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrchestratorActivityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrchestratorActivityResponseMultiError) AllErrors() []error { return m }

// OrchestratorActivityResponseValidationError is the validation error returned
// by OrchestratorActivityResponse.Validate if the designated constraints
// aren't met.
type OrchestratorActivityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrchestratorActivityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrchestratorActivityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrchestratorActivityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrchestratorActivityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrchestratorActivityResponseValidationError) ErrorName() string {
	return "OrchestratorActivityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e OrchestratorActivityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrchestratorActivityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrchestratorActivityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrchestratorActivityResponseValidationError{}

// Validate checks the field values on LivenessSummaryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LivenessSummaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessSummaryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessSummaryRequestMultiError, or nil if none found.
func (m *LivenessSummaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessSummaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessSummaryRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessSummaryRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessSummaryRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LivenessSummaryRequestMultiError(errors)
	}

	return nil
}

// LivenessSummaryRequestMultiError is an error wrapping multiple validation
// errors returned by LivenessSummaryRequest.ValidateAll() if the designated
// constraints aren't met.
type LivenessSummaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessSummaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessSummaryRequestMultiError) AllErrors() []error { return m }

// LivenessSummaryRequestValidationError is the validation error returned by
// LivenessSummaryRequest.Validate if the designated constraints aren't met.
type LivenessSummaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessSummaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessSummaryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessSummaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessSummaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessSummaryRequestValidationError) ErrorName() string {
	return "LivenessSummaryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LivenessSummaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessSummaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessSummaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessSummaryRequestValidationError{}

// Validate checks the field values on LivenessSummaryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LivenessSummaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessSummaryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessSummaryResponseMultiError, or nil if none found.
func (m *LivenessSummaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessSummaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessSummaryResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessSummaryResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessSummaryResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuthRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessSummaryResponseValidationError{
					field:  "AuthRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessSummaryResponseValidationError{
					field:  "AuthRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessSummaryResponseValidationError{
				field:  "AuthRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuthRequestStage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessSummaryResponseValidationError{
					field:  "AuthRequestStage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessSummaryResponseValidationError{
					field:  "AuthRequestStage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthRequestStage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessSummaryResponseValidationError{
				field:  "AuthRequestStage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LivenessSummaryResponseMultiError(errors)
	}

	return nil
}

// LivenessSummaryResponseMultiError is an error wrapping multiple validation
// errors returned by LivenessSummaryResponse.ValidateAll() if the designated
// constraints aren't met.
type LivenessSummaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessSummaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessSummaryResponseMultiError) AllErrors() []error { return m }

// LivenessSummaryResponseValidationError is the validation error returned by
// LivenessSummaryResponse.Validate if the designated constraints aren't met.
type LivenessSummaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessSummaryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessSummaryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessSummaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessSummaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessSummaryResponseValidationError) ErrorName() string {
	return "LivenessSummaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LivenessSummaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessSummaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessSummaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessSummaryResponseValidationError{}

// Validate checks the field values on ManualReviewRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ManualReviewRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ManualReviewRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ManualReviewRequestMultiError, or nil if none found.
func (m *ManualReviewRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ManualReviewRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ManualReviewRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ManualReviewRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ManualReviewRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuthRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ManualReviewRequestValidationError{
					field:  "AuthRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ManualReviewRequestValidationError{
					field:  "AuthRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ManualReviewRequestValidationError{
				field:  "AuthRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuthRequestStage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ManualReviewRequestValidationError{
					field:  "AuthRequestStage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ManualReviewRequestValidationError{
					field:  "AuthRequestStage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthRequestStage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ManualReviewRequestValidationError{
				field:  "AuthRequestStage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ManualReviewRequestMultiError(errors)
	}

	return nil
}

// ManualReviewRequestMultiError is an error wrapping multiple validation
// errors returned by ManualReviewRequest.ValidateAll() if the designated
// constraints aren't met.
type ManualReviewRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ManualReviewRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ManualReviewRequestMultiError) AllErrors() []error { return m }

// ManualReviewRequestValidationError is the validation error returned by
// ManualReviewRequest.Validate if the designated constraints aren't met.
type ManualReviewRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ManualReviewRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ManualReviewRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ManualReviewRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ManualReviewRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ManualReviewRequestValidationError) ErrorName() string {
	return "ManualReviewRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ManualReviewRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sManualReviewRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ManualReviewRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ManualReviewRequestValidationError{}

// Validate checks the field values on ManualReviewResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ManualReviewResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ManualReviewResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ManualReviewResponseMultiError, or nil if none found.
func (m *ManualReviewResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ManualReviewResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ManualReviewResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ManualReviewResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ManualReviewResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ManualReviewResponseMultiError(errors)
	}

	return nil
}

// ManualReviewResponseMultiError is an error wrapping multiple validation
// errors returned by ManualReviewResponse.ValidateAll() if the designated
// constraints aren't met.
type ManualReviewResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ManualReviewResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ManualReviewResponseMultiError) AllErrors() []error { return m }

// ManualReviewResponseValidationError is the validation error returned by
// ManualReviewResponse.Validate if the designated constraints aren't met.
type ManualReviewResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ManualReviewResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ManualReviewResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ManualReviewResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ManualReviewResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ManualReviewResponseValidationError) ErrorName() string {
	return "ManualReviewResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ManualReviewResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sManualReviewResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ManualReviewResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ManualReviewResponseValidationError{}

// Validate checks the field values on CheckBiometricStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckBiometricStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckBiometricStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckBiometricStatusRequestMultiError, or nil if none found.
func (m *CheckBiometricStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckBiometricStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckBiometricStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckBiometricStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckBiometricStatusRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckBiometricStatusRequestMultiError(errors)
	}

	return nil
}

// CheckBiometricStatusRequestMultiError is an error wrapping multiple
// validation errors returned by CheckBiometricStatusRequest.ValidateAll() if
// the designated constraints aren't met.
type CheckBiometricStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckBiometricStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckBiometricStatusRequestMultiError) AllErrors() []error { return m }

// CheckBiometricStatusRequestValidationError is the validation error returned
// by CheckBiometricStatusRequest.Validate if the designated constraints
// aren't met.
type CheckBiometricStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckBiometricStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckBiometricStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckBiometricStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckBiometricStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckBiometricStatusRequestValidationError) ErrorName() string {
	return "CheckBiometricStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckBiometricStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckBiometricStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckBiometricStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckBiometricStatusRequestValidationError{}

// Validate checks the field values on CheckBiometricStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckBiometricStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckBiometricStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckBiometricStatusResponseMultiError, or nil if none found.
func (m *CheckBiometricStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckBiometricStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckBiometricStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckBiometricStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckBiometricStatusResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BiometricStatus

	if len(errors) > 0 {
		return CheckBiometricStatusResponseMultiError(errors)
	}

	return nil
}

// CheckBiometricStatusResponseMultiError is an error wrapping multiple
// validation errors returned by CheckBiometricStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type CheckBiometricStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckBiometricStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckBiometricStatusResponseMultiError) AllErrors() []error { return m }

// CheckBiometricStatusResponseValidationError is the validation error returned
// by CheckBiometricStatusResponse.Validate if the designated constraints
// aren't met.
type CheckBiometricStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckBiometricStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckBiometricStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckBiometricStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckBiometricStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckBiometricStatusResponseValidationError) ErrorName() string {
	return "CheckBiometricStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckBiometricStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckBiometricStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckBiometricStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckBiometricStatusResponseValidationError{}

// Validate checks the field values on UpdateBiometricStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateBiometricStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBiometricStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateBiometricStatusRequestMultiError, or nil if none found.
func (m *UpdateBiometricStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBiometricStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateBiometricStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateBiometricStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateBiometricStatusRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpdatedBiometricStatus

	if len(errors) > 0 {
		return UpdateBiometricStatusRequestMultiError(errors)
	}

	return nil
}

// UpdateBiometricStatusRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateBiometricStatusRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateBiometricStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBiometricStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBiometricStatusRequestMultiError) AllErrors() []error { return m }

// UpdateBiometricStatusRequestValidationError is the validation error returned
// by UpdateBiometricStatusRequest.Validate if the designated constraints
// aren't met.
type UpdateBiometricStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBiometricStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBiometricStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBiometricStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBiometricStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBiometricStatusRequestValidationError) ErrorName() string {
	return "UpdateBiometricStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBiometricStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBiometricStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBiometricStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBiometricStatusRequestValidationError{}

// Validate checks the field values on UpdateBiometricStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateBiometricStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBiometricStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateBiometricStatusResponseMultiError, or nil if none found.
func (m *UpdateBiometricStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBiometricStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateBiometricStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateBiometricStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateBiometricStatusResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateBiometricStatusResponseMultiError(errors)
	}

	return nil
}

// UpdateBiometricStatusResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateBiometricStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type UpdateBiometricStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBiometricStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBiometricStatusResponseMultiError) AllErrors() []error { return m }

// UpdateBiometricStatusResponseValidationError is the validation error
// returned by UpdateBiometricStatusResponse.Validate if the designated
// constraints aren't met.
type UpdateBiometricStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBiometricStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBiometricStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBiometricStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBiometricStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBiometricStatusResponseValidationError) ErrorName() string {
	return "UpdateBiometricStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBiometricStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBiometricStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBiometricStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBiometricStatusResponseValidationError{}
