// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/orchestrator/activity/activity.proto

package activity

import (
	activity "github.com/epifi/be-common/api/celestial/activity"
	biometrics "github.com/epifi/gamma/api/auth/biometrics"
	orchestrator "github.com/epifi/gamma/api/auth/orchestrator"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthRequestStageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *activity.RequestHeader             `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	Stage         orchestrator.AuthStage              `protobuf:"varint,2,opt,name=stage,proto3,enum=auth.orchestrator.AuthStage" json:"stage,omitempty"`
	Status        orchestrator.AuthRequestStageStatus `protobuf:"varint,3,opt,name=status,proto3,enum=auth.orchestrator.AuthRequestStageStatus" json:"status,omitempty"`
	AuthRefId     string                              `protobuf:"bytes,4,opt,name=auth_ref_id,json=authRefId,proto3" json:"auth_ref_id,omitempty"`
}

func (x *AuthRequestStageRequest) Reset() {
	*x = AuthRequestStageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthRequestStageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthRequestStageRequest) ProtoMessage() {}

func (x *AuthRequestStageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthRequestStageRequest.ProtoReflect.Descriptor instead.
func (*AuthRequestStageRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_activity_activity_proto_rawDescGZIP(), []int{0}
}

func (x *AuthRequestStageRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *AuthRequestStageRequest) GetStage() orchestrator.AuthStage {
	if x != nil {
		return x.Stage
	}
	return orchestrator.AuthStage(0)
}

func (x *AuthRequestStageRequest) GetStatus() orchestrator.AuthRequestStageStatus {
	if x != nil {
		return x.Status
	}
	return orchestrator.AuthRequestStageStatus(0)
}

func (x *AuthRequestStageRequest) GetAuthRefId() string {
	if x != nil {
		return x.AuthRefId
	}
	return ""
}

type AuthRequestStageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *AuthRequestStageResponse) Reset() {
	*x = AuthRequestStageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthRequestStageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthRequestStageResponse) ProtoMessage() {}

func (x *AuthRequestStageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthRequestStageResponse.ProtoReflect.Descriptor instead.
func (*AuthRequestStageResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_activity_activity_proto_rawDescGZIP(), []int{1}
}

func (x *AuthRequestStageResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// Generic activity request and response when no extra data is required
type OrchestratorActivityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *OrchestratorActivityRequest) Reset() {
	*x = OrchestratorActivityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrchestratorActivityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrchestratorActivityRequest) ProtoMessage() {}

func (x *OrchestratorActivityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrchestratorActivityRequest.ProtoReflect.Descriptor instead.
func (*OrchestratorActivityRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_activity_activity_proto_rawDescGZIP(), []int{2}
}

func (x *OrchestratorActivityRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type OrchestratorActivityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader  `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	AuthRequest    *orchestrator.AuthRequest `protobuf:"bytes,2,opt,name=auth_request,json=authRequest,proto3" json:"auth_request,omitempty"`
}

func (x *OrchestratorActivityResponse) Reset() {
	*x = OrchestratorActivityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrchestratorActivityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrchestratorActivityResponse) ProtoMessage() {}

func (x *OrchestratorActivityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrchestratorActivityResponse.ProtoReflect.Descriptor instead.
func (*OrchestratorActivityResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_activity_activity_proto_rawDescGZIP(), []int{3}
}

func (x *OrchestratorActivityResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *OrchestratorActivityResponse) GetAuthRequest() *orchestrator.AuthRequest {
	if x != nil {
		return x.AuthRequest
	}
	return nil
}

type LivenessSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *LivenessSummaryRequest) Reset() {
	*x = LivenessSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessSummaryRequest) ProtoMessage() {}

func (x *LivenessSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessSummaryRequest.ProtoReflect.Descriptor instead.
func (*LivenessSummaryRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_activity_activity_proto_rawDescGZIP(), []int{4}
}

func (x *LivenessSummaryRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type LivenessSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader   *activity.ResponseHeader       `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	AuthRequest      *orchestrator.AuthRequest      `protobuf:"bytes,2,opt,name=auth_request,json=authRequest,proto3" json:"auth_request,omitempty"`
	AuthRequestStage *orchestrator.AuthRequestStage `protobuf:"bytes,3,opt,name=auth_request_stage,json=authRequestStage,proto3" json:"auth_request_stage,omitempty"`
}

func (x *LivenessSummaryResponse) Reset() {
	*x = LivenessSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessSummaryResponse) ProtoMessage() {}

func (x *LivenessSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessSummaryResponse.ProtoReflect.Descriptor instead.
func (*LivenessSummaryResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_activity_activity_proto_rawDescGZIP(), []int{5}
}

func (x *LivenessSummaryResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *LivenessSummaryResponse) GetAuthRequest() *orchestrator.AuthRequest {
	if x != nil {
		return x.AuthRequest
	}
	return nil
}

func (x *LivenessSummaryResponse) GetAuthRequestStage() *orchestrator.AuthRequestStage {
	if x != nil {
		return x.AuthRequestStage
	}
	return nil
}

type ManualReviewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader    *activity.RequestHeader        `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	AuthRequest      *orchestrator.AuthRequest      `protobuf:"bytes,2,opt,name=auth_request,json=authRequest,proto3" json:"auth_request,omitempty"`
	AuthRequestStage *orchestrator.AuthRequestStage `protobuf:"bytes,3,opt,name=auth_request_stage,json=authRequestStage,proto3" json:"auth_request_stage,omitempty"`
}

func (x *ManualReviewRequest) Reset() {
	*x = ManualReviewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualReviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualReviewRequest) ProtoMessage() {}

func (x *ManualReviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualReviewRequest.ProtoReflect.Descriptor instead.
func (*ManualReviewRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_activity_activity_proto_rawDescGZIP(), []int{6}
}

func (x *ManualReviewRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ManualReviewRequest) GetAuthRequest() *orchestrator.AuthRequest {
	if x != nil {
		return x.AuthRequest
	}
	return nil
}

func (x *ManualReviewRequest) GetAuthRequestStage() *orchestrator.AuthRequestStage {
	if x != nil {
		return x.AuthRequestStage
	}
	return nil
}

type ManualReviewResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ManualReviewResponse) Reset() {
	*x = ManualReviewResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualReviewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualReviewResponse) ProtoMessage() {}

func (x *ManualReviewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualReviewResponse.ProtoReflect.Descriptor instead.
func (*ManualReviewResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_activity_activity_proto_rawDescGZIP(), []int{7}
}

func (x *ManualReviewResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type CheckBiometricStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *CheckBiometricStatusRequest) Reset() {
	*x = CheckBiometricStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckBiometricStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckBiometricStatusRequest) ProtoMessage() {}

func (x *CheckBiometricStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckBiometricStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckBiometricStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_activity_activity_proto_rawDescGZIP(), []int{8}
}

func (x *CheckBiometricStatusRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type CheckBiometricStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader  *activity.ResponseHeader   `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	BiometricStatus biometrics.BiometricStatus `protobuf:"varint,2,opt,name=biometric_status,json=biometricStatus,proto3,enum=auth.biometrics.BiometricStatus" json:"biometric_status,omitempty"`
}

func (x *CheckBiometricStatusResponse) Reset() {
	*x = CheckBiometricStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckBiometricStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckBiometricStatusResponse) ProtoMessage() {}

func (x *CheckBiometricStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckBiometricStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckBiometricStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_activity_activity_proto_rawDescGZIP(), []int{9}
}

func (x *CheckBiometricStatusResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *CheckBiometricStatusResponse) GetBiometricStatus() biometrics.BiometricStatus {
	if x != nil {
		return x.BiometricStatus
	}
	return biometrics.BiometricStatus(0)
}

type UpdateBiometricStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader          *activity.RequestHeader    `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	UpdatedBiometricStatus biometrics.BiometricStatus `protobuf:"varint,2,opt,name=updated_biometric_status,json=updatedBiometricStatus,proto3,enum=auth.biometrics.BiometricStatus" json:"updated_biometric_status,omitempty"`
}

func (x *UpdateBiometricStatusRequest) Reset() {
	*x = UpdateBiometricStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBiometricStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBiometricStatusRequest) ProtoMessage() {}

func (x *UpdateBiometricStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBiometricStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateBiometricStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_activity_activity_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateBiometricStatusRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *UpdateBiometricStatusRequest) GetUpdatedBiometricStatus() biometrics.BiometricStatus {
	if x != nil {
		return x.UpdatedBiometricStatus
	}
	return biometrics.BiometricStatus(0)
}

type UpdateBiometricStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *UpdateBiometricStatusResponse) Reset() {
	*x = UpdateBiometricStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBiometricStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBiometricStatusResponse) ProtoMessage() {}

func (x *UpdateBiometricStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_activity_activity_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBiometricStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateBiometricStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_activity_activity_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateBiometricStatusResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_auth_orchestrator_activity_activity_proto protoreflect.FileDescriptor

var file_api_auth_orchestrator_activity_activity_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1a, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x1a, 0x2d, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x75, 0x74, 0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfa, 0x01, 0x0a, 0x17,
	0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x32, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x41, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63,
	0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x68,
	0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x75, 0x74, 0x68, 0x52, 0x65, 0x66, 0x49, 0x64, 0x22, 0x67, 0x0a, 0x18, 0x41, 0x75, 0x74, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0x67, 0x0a, 0x1b, 0x4f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xae, 0x01, 0x0a, 0x1c, 0x4f,
	0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0c, 0x61, 0x75, 0x74, 0x68,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b,
	0x61, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x62, 0x0a, 0x16, 0x4c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22,
	0xfc, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0c, 0x61, 0x75, 0x74, 0x68,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b,
	0x61, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x51, 0x0a, 0x12, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f,
	0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x10, 0x61, 0x75,
	0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x22, 0xf5,
	0x01, 0x0a, 0x13, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x41, 0x0a, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72,
	0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x51, 0x0a, 0x12, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x52, 0x10, 0x61, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x22, 0x63, 0x0a, 0x14, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b,
	0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74,
	0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x67, 0x0a, 0x1b, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x22, 0xb8, 0x01, 0x0a, 0x1c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x69,
	0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x4b, 0x0a, 0x10, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x42,
	0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f,
	0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0xc4, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x5a, 0x0a, 0x18, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x42,
	0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x16,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x6c, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x42, 0x6e, 0x0a, 0x35, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5a, 0x35, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6f,
	0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_orchestrator_activity_activity_proto_rawDescOnce sync.Once
	file_api_auth_orchestrator_activity_activity_proto_rawDescData = file_api_auth_orchestrator_activity_activity_proto_rawDesc
)

func file_api_auth_orchestrator_activity_activity_proto_rawDescGZIP() []byte {
	file_api_auth_orchestrator_activity_activity_proto_rawDescOnce.Do(func() {
		file_api_auth_orchestrator_activity_activity_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_orchestrator_activity_activity_proto_rawDescData)
	})
	return file_api_auth_orchestrator_activity_activity_proto_rawDescData
}

var file_api_auth_orchestrator_activity_activity_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_api_auth_orchestrator_activity_activity_proto_goTypes = []interface{}{
	(*AuthRequestStageRequest)(nil),          // 0: auth.orchestrator.activity.AuthRequestStageRequest
	(*AuthRequestStageResponse)(nil),         // 1: auth.orchestrator.activity.AuthRequestStageResponse
	(*OrchestratorActivityRequest)(nil),      // 2: auth.orchestrator.activity.OrchestratorActivityRequest
	(*OrchestratorActivityResponse)(nil),     // 3: auth.orchestrator.activity.OrchestratorActivityResponse
	(*LivenessSummaryRequest)(nil),           // 4: auth.orchestrator.activity.LivenessSummaryRequest
	(*LivenessSummaryResponse)(nil),          // 5: auth.orchestrator.activity.LivenessSummaryResponse
	(*ManualReviewRequest)(nil),              // 6: auth.orchestrator.activity.ManualReviewRequest
	(*ManualReviewResponse)(nil),             // 7: auth.orchestrator.activity.ManualReviewResponse
	(*CheckBiometricStatusRequest)(nil),      // 8: auth.orchestrator.activity.CheckBiometricStatusRequest
	(*CheckBiometricStatusResponse)(nil),     // 9: auth.orchestrator.activity.CheckBiometricStatusResponse
	(*UpdateBiometricStatusRequest)(nil),     // 10: auth.orchestrator.activity.UpdateBiometricStatusRequest
	(*UpdateBiometricStatusResponse)(nil),    // 11: auth.orchestrator.activity.UpdateBiometricStatusResponse
	(*activity.RequestHeader)(nil),           // 12: celestial.activity.RequestHeader
	(orchestrator.AuthStage)(0),              // 13: auth.orchestrator.AuthStage
	(orchestrator.AuthRequestStageStatus)(0), // 14: auth.orchestrator.AuthRequestStageStatus
	(*activity.ResponseHeader)(nil),          // 15: celestial.activity.ResponseHeader
	(*orchestrator.AuthRequest)(nil),         // 16: auth.orchestrator.AuthRequest
	(*orchestrator.AuthRequestStage)(nil),    // 17: auth.orchestrator.AuthRequestStage
	(biometrics.BiometricStatus)(0),          // 18: auth.biometrics.BiometricStatus
}
var file_api_auth_orchestrator_activity_activity_proto_depIdxs = []int32{
	12, // 0: auth.orchestrator.activity.AuthRequestStageRequest.request_header:type_name -> celestial.activity.RequestHeader
	13, // 1: auth.orchestrator.activity.AuthRequestStageRequest.stage:type_name -> auth.orchestrator.AuthStage
	14, // 2: auth.orchestrator.activity.AuthRequestStageRequest.status:type_name -> auth.orchestrator.AuthRequestStageStatus
	15, // 3: auth.orchestrator.activity.AuthRequestStageResponse.response_header:type_name -> celestial.activity.ResponseHeader
	12, // 4: auth.orchestrator.activity.OrchestratorActivityRequest.request_header:type_name -> celestial.activity.RequestHeader
	15, // 5: auth.orchestrator.activity.OrchestratorActivityResponse.response_header:type_name -> celestial.activity.ResponseHeader
	16, // 6: auth.orchestrator.activity.OrchestratorActivityResponse.auth_request:type_name -> auth.orchestrator.AuthRequest
	12, // 7: auth.orchestrator.activity.LivenessSummaryRequest.request_header:type_name -> celestial.activity.RequestHeader
	15, // 8: auth.orchestrator.activity.LivenessSummaryResponse.response_header:type_name -> celestial.activity.ResponseHeader
	16, // 9: auth.orchestrator.activity.LivenessSummaryResponse.auth_request:type_name -> auth.orchestrator.AuthRequest
	17, // 10: auth.orchestrator.activity.LivenessSummaryResponse.auth_request_stage:type_name -> auth.orchestrator.AuthRequestStage
	12, // 11: auth.orchestrator.activity.ManualReviewRequest.request_header:type_name -> celestial.activity.RequestHeader
	16, // 12: auth.orchestrator.activity.ManualReviewRequest.auth_request:type_name -> auth.orchestrator.AuthRequest
	17, // 13: auth.orchestrator.activity.ManualReviewRequest.auth_request_stage:type_name -> auth.orchestrator.AuthRequestStage
	15, // 14: auth.orchestrator.activity.ManualReviewResponse.response_header:type_name -> celestial.activity.ResponseHeader
	12, // 15: auth.orchestrator.activity.CheckBiometricStatusRequest.request_header:type_name -> celestial.activity.RequestHeader
	15, // 16: auth.orchestrator.activity.CheckBiometricStatusResponse.response_header:type_name -> celestial.activity.ResponseHeader
	18, // 17: auth.orchestrator.activity.CheckBiometricStatusResponse.biometric_status:type_name -> auth.biometrics.BiometricStatus
	12, // 18: auth.orchestrator.activity.UpdateBiometricStatusRequest.request_header:type_name -> celestial.activity.RequestHeader
	18, // 19: auth.orchestrator.activity.UpdateBiometricStatusRequest.updated_biometric_status:type_name -> auth.biometrics.BiometricStatus
	15, // 20: auth.orchestrator.activity.UpdateBiometricStatusResponse.response_header:type_name -> celestial.activity.ResponseHeader
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_api_auth_orchestrator_activity_activity_proto_init() }
func file_api_auth_orchestrator_activity_activity_proto_init() {
	if File_api_auth_orchestrator_activity_activity_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_orchestrator_activity_activity_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthRequestStageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_activity_activity_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthRequestStageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_activity_activity_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrchestratorActivityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_activity_activity_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrchestratorActivityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_activity_activity_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_activity_activity_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_activity_activity_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualReviewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_activity_activity_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualReviewResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_activity_activity_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckBiometricStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_activity_activity_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckBiometricStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_activity_activity_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBiometricStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_activity_activity_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBiometricStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_orchestrator_activity_activity_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_orchestrator_activity_activity_proto_goTypes,
		DependencyIndexes: file_api_auth_orchestrator_activity_activity_proto_depIdxs,
		MessageInfos:      file_api_auth_orchestrator_activity_activity_proto_msgTypes,
	}.Build()
	File_api_auth_orchestrator_activity_activity_proto = out.File
	file_api_auth_orchestrator_activity_activity_proto_rawDesc = nil
	file_api_auth_orchestrator_activity_activity_proto_goTypes = nil
	file_api_auth_orchestrator_activity_activity_proto_depIdxs = nil
}
