// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/orchestrator/service.proto

package orchestrator

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	liveness "github.com/epifi/gamma/api/auth/liveness"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	upi "github.com/epifi/gamma/api/upi"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GenerateOtpResponse_Status int32

const (
	// Success
	GenerateOtpResponse_OK GenerateOtpResponse_Status = 0
	// Input token is not available on the system
	// Generate new token
	GenerateOtpResponse_NOT_FOUND GenerateOtpResponse_Status = 5
	// Internal error
	GenerateOtpResponse_INTERNAL GenerateOtpResponse_Status = 13
	// Input token has been used already and is not active.
	// Cannot reuse it
	GenerateOtpResponse_TOKEN_INACTIVE GenerateOtpResponse_Status = 100
	// Input token is expired and cannot use it
	// Generate new token
	GenerateOtpResponse_TOKEN_EXPIRY GenerateOtpResponse_Status = 101
	// Too many resend attempts
	// Generate new token
	GenerateOtpResponse_RESEND_LIMIT GenerateOtpResponse_Status = 102
	// Resend request too soon
	// Wait for some time and retry
	GenerateOtpResponse_RESEND_REQ_TOO_SOON GenerateOtpResponse_Status = 103
	// The user doesn't have access to the app. Possible due to user not in the waitlist or not given access by ranking.
	GenerateOtpResponse_NO_APP_ACCESS GenerateOtpResponse_Status = 106
)

// Enum value maps for GenerateOtpResponse_Status.
var (
	GenerateOtpResponse_Status_name = map[int32]string{
		0:   "OK",
		5:   "NOT_FOUND",
		13:  "INTERNAL",
		100: "TOKEN_INACTIVE",
		101: "TOKEN_EXPIRY",
		102: "RESEND_LIMIT",
		103: "RESEND_REQ_TOO_SOON",
		106: "NO_APP_ACCESS",
	}
	GenerateOtpResponse_Status_value = map[string]int32{
		"OK":                  0,
		"NOT_FOUND":           5,
		"INTERNAL":            13,
		"TOKEN_INACTIVE":      100,
		"TOKEN_EXPIRY":        101,
		"RESEND_LIMIT":        102,
		"RESEND_REQ_TOO_SOON": 103,
		"NO_APP_ACCESS":       106,
	}
)

func (x GenerateOtpResponse_Status) Enum() *GenerateOtpResponse_Status {
	p := new(GenerateOtpResponse_Status)
	*p = x
	return p
}

func (x GenerateOtpResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GenerateOtpResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_orchestrator_service_proto_enumTypes[0].Descriptor()
}

func (GenerateOtpResponse_Status) Type() protoreflect.EnumType {
	return &file_api_auth_orchestrator_service_proto_enumTypes[0]
}

func (x GenerateOtpResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GenerateOtpResponse_Status.Descriptor instead.
func (GenerateOtpResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{5, 0}
}

type ValidateNpciPinResponse_SecurePinStatus int32

const (
	ValidateNpciPinResponse_OK                   ValidateNpciPinResponse_SecurePinStatus = 0
	ValidateNpciPinResponse_INVALID_SECURE_PIN   ValidateNpciPinResponse_SecurePinStatus = 100
	ValidateNpciPinResponse_PIN_RETRIES_EXCEEDED ValidateNpciPinResponse_SecurePinStatus = 101
	ValidateNpciPinResponse_BUSINESS_FAILURE     ValidateNpciPinResponse_SecurePinStatus = 102
)

// Enum value maps for ValidateNpciPinResponse_SecurePinStatus.
var (
	ValidateNpciPinResponse_SecurePinStatus_name = map[int32]string{
		0:   "OK",
		100: "INVALID_SECURE_PIN",
		101: "PIN_RETRIES_EXCEEDED",
		102: "BUSINESS_FAILURE",
	}
	ValidateNpciPinResponse_SecurePinStatus_value = map[string]int32{
		"OK":                   0,
		"INVALID_SECURE_PIN":   100,
		"PIN_RETRIES_EXCEEDED": 101,
		"BUSINESS_FAILURE":     102,
	}
)

func (x ValidateNpciPinResponse_SecurePinStatus) Enum() *ValidateNpciPinResponse_SecurePinStatus {
	p := new(ValidateNpciPinResponse_SecurePinStatus)
	*p = x
	return p
}

func (x ValidateNpciPinResponse_SecurePinStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ValidateNpciPinResponse_SecurePinStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_orchestrator_service_proto_enumTypes[1].Descriptor()
}

func (ValidateNpciPinResponse_SecurePinStatus) Type() protoreflect.EnumType {
	return &file_api_auth_orchestrator_service_proto_enumTypes[1]
}

func (x ValidateNpciPinResponse_SecurePinStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ValidateNpciPinResponse_SecurePinStatus.Descriptor instead.
func (ValidateNpciPinResponse_SecurePinStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{7, 0}
}

type InitiateAuthFlowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Client request id will be used to keep track of the auth request and it's status
	ClientRequestId string `protobuf:"bytes,2,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// Flow name represents the auth flow that is to be initiated
	FlowName FlowName `protobuf:"varint,3,opt,name=flow_name,json=flowName,proto3,enum=auth.orchestrator.FlowName" json:"flow_name,omitempty"`
	// Redirect action is the screen to be redirected to after auth flow completes
	RedirectAction *deeplink.Deeplink `protobuf:"bytes,4,opt,name=redirect_action,json=redirectAction,proto3" json:"redirect_action,omitempty"`
	// Provenance is used to keep track of the calling service
	Provenance Provenance `protobuf:"varint,5,opt,name=provenance,proto3,enum=auth.orchestrator.Provenance" json:"provenance,omitempty"`
	// Preferences keep track of preferences and customisations needed in auth flow
	Preferences *Preferences `protobuf:"bytes,6,opt,name=preferences,proto3" json:"preferences,omitempty"`
}

func (x *InitiateAuthFlowRequest) Reset() {
	*x = InitiateAuthFlowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateAuthFlowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateAuthFlowRequest) ProtoMessage() {}

func (x *InitiateAuthFlowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateAuthFlowRequest.ProtoReflect.Descriptor instead.
func (*InitiateAuthFlowRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{0}
}

func (x *InitiateAuthFlowRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *InitiateAuthFlowRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *InitiateAuthFlowRequest) GetFlowName() FlowName {
	if x != nil {
		return x.FlowName
	}
	return FlowName_AUTH_REQUEST_FLOW_UNSPECIFIED
}

func (x *InitiateAuthFlowRequest) GetRedirectAction() *deeplink.Deeplink {
	if x != nil {
		return x.RedirectAction
	}
	return nil
}

func (x *InitiateAuthFlowRequest) GetProvenance() Provenance {
	if x != nil {
		return x.Provenance
	}
	return Provenance_PROVENANCE_UNSPECIFIED
}

func (x *InitiateAuthFlowRequest) GetPreferences() *Preferences {
	if x != nil {
		return x.Preferences
	}
	return nil
}

type InitiateAuthFlowResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *InitiateAuthFlowResponse) Reset() {
	*x = InitiateAuthFlowResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateAuthFlowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateAuthFlowResponse) ProtoMessage() {}

func (x *InitiateAuthFlowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateAuthFlowResponse.ProtoReflect.Descriptor instead.
func (*InitiateAuthFlowResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{1}
}

func (x *InitiateAuthFlowResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *InitiateAuthFlowResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type GetAuthFlowStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientRequestId string `protobuf:"bytes,1,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
}

func (x *GetAuthFlowStatusRequest) Reset() {
	*x = GetAuthFlowStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthFlowStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthFlowStatusRequest) ProtoMessage() {}

func (x *GetAuthFlowStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthFlowStatusRequest.ProtoReflect.Descriptor instead.
func (*GetAuthFlowStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetAuthFlowStatusRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

type GetAuthFlowStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ActorId    string             `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AuthStatus AuthRequestStatus  `protobuf:"varint,2,opt,name=auth_status,json=authStatus,proto3,enum=auth.orchestrator.AuthRequestStatus" json:"auth_status,omitempty"`
	NextAction *deeplink.Deeplink `protobuf:"bytes,3,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *GetAuthFlowStatusResponse) Reset() {
	*x = GetAuthFlowStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthFlowStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthFlowStatusResponse) ProtoMessage() {}

func (x *GetAuthFlowStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthFlowStatusResponse.ProtoReflect.Descriptor instead.
func (*GetAuthFlowStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetAuthFlowStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAuthFlowStatusResponse) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAuthFlowStatusResponse) GetAuthStatus() AuthRequestStatus {
	if x != nil {
		return x.AuthStatus
	}
	return AuthRequestStatus_AUTH_REQUEST_STATUS_UNSPECIFIED
}

func (x *GetAuthFlowStatusResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type GenerateOtpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientRequestId string         `protobuf:"bytes,1,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	ActorId         string         `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Device          *common.Device `protobuf:"bytes,3,opt,name=device,proto3" json:"device,omitempty"`
	// Unique identifier of GenerateOtp request(UUID)
	// If token is not sent, a new OTP is generated and sent to the user via SMS
	// If token is sent, request is treated as ResendOtp and existing OTP is sent to the user phone via SMS
	Token string `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *GenerateOtpRequest) Reset() {
	*x = GenerateOtpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateOtpRequest) ProtoMessage() {}

func (x *GenerateOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateOtpRequest.ProtoReflect.Descriptor instead.
func (*GenerateOtpRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{4}
}

func (x *GenerateOtpRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *GenerateOtpRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GenerateOtpRequest) GetDevice() *common.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *GenerateOtpRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// Represents response of GenerateOtp method
type GenerateOtpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Represents if the OTP has been generated and sent without any errors
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Unique identifier of GenerateOtp request
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// A timer(in seconds) for the client post which it should raise a new request for Otp
	// Any attempt prior to this timer will not be honored & will result in error
	RetryTimerSeconds uint32 `protobuf:"varint,3,opt,name=retry_timer_seconds,json=retryTimerSeconds,proto3" json:"retry_timer_seconds,omitempty"`
}

func (x *GenerateOtpResponse) Reset() {
	*x = GenerateOtpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateOtpResponse) ProtoMessage() {}

func (x *GenerateOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateOtpResponse.ProtoReflect.Descriptor instead.
func (*GenerateOtpResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{5}
}

func (x *GenerateOtpResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GenerateOtpResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GenerateOtpResponse) GetRetryTimerSeconds() uint32 {
	if x != nil {
		return x.RetryTimerSeconds
	}
	return 0
}

type ValidateNpciPinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId       string         `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ClientReqId   string         `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	TxnId         string         `protobuf:"bytes,3,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
	NpciCredBlock *upi.CredBlock `protobuf:"bytes,4,opt,name=npci_cred_block,json=npciCredBlock,proto3" json:"npci_cred_block,omitempty"`
	Device        *common.Device `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`
}

func (x *ValidateNpciPinRequest) Reset() {
	*x = ValidateNpciPinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateNpciPinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateNpciPinRequest) ProtoMessage() {}

func (x *ValidateNpciPinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateNpciPinRequest.ProtoReflect.Descriptor instead.
func (*ValidateNpciPinRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{6}
}

func (x *ValidateNpciPinRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ValidateNpciPinRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *ValidateNpciPinRequest) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

func (x *ValidateNpciPinRequest) GetNpciCredBlock() *upi.CredBlock {
	if x != nil {
		return x.NpciCredBlock
	}
	return nil
}

func (x *ValidateNpciPinRequest) GetDevice() *common.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type ValidateNpciPinResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *ValidateNpciPinResponse) Reset() {
	*x = ValidateNpciPinResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateNpciPinResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateNpciPinResponse) ProtoMessage() {}

func (x *ValidateNpciPinResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateNpciPinResponse.ProtoReflect.Descriptor instead.
func (*ValidateNpciPinResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{7}
}

func (x *ValidateNpciPinResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ValidateNpciPinResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type VerifyOtpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId     string         `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ClientReqId string         `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	Token       string         `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	Otp         string         `protobuf:"bytes,4,opt,name=otp,proto3" json:"otp,omitempty"`
	Device      *common.Device `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`
}

func (x *VerifyOtpRequest) Reset() {
	*x = VerifyOtpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyOtpRequest) ProtoMessage() {}

func (x *VerifyOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyOtpRequest.ProtoReflect.Descriptor instead.
func (*VerifyOtpRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{8}
}

func (x *VerifyOtpRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *VerifyOtpRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *VerifyOtpRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *VerifyOtpRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *VerifyOtpRequest) GetDevice() *common.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type VerifyOtpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	// Refer to Status of VerifyOtpResponse in api/auth/service.proto
	Status     *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *VerifyOtpResponse) Reset() {
	*x = VerifyOtpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyOtpResponse) ProtoMessage() {}

func (x *VerifyOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyOtpResponse.ProtoReflect.Descriptor instead.
func (*VerifyOtpResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{9}
}

func (x *VerifyOtpResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *VerifyOtpResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type GetLivenessSummaryStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// client request id of the auth request
	ClientRequestId string                `protobuf:"bytes,2,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	LivenessFlow    liveness.LivenessFlow `protobuf:"varint,4,opt,name=liveness_flow,json=livenessFlow,proto3,enum=auth.liveness.LivenessFlow" json:"liveness_flow,omitempty"`
}

func (x *GetLivenessSummaryStatusRequest) Reset() {
	*x = GetLivenessSummaryStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLivenessSummaryStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLivenessSummaryStatusRequest) ProtoMessage() {}

func (x *GetLivenessSummaryStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLivenessSummaryStatusRequest.ProtoReflect.Descriptor instead.
func (*GetLivenessSummaryStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetLivenessSummaryStatusRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetLivenessSummaryStatusRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *GetLivenessSummaryStatusRequest) GetLivenessFlow() liveness.LivenessFlow {
	if x != nil {
		return x.LivenessFlow
	}
	return liveness.LivenessFlow(0)
}

type GetLivenessSummaryStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NextAction    *deeplink.Deeplink     `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	SummaryStatus liveness.SummaryStatus `protobuf:"varint,3,opt,name=summary_status,json=summaryStatus,proto3,enum=auth.liveness.SummaryStatus" json:"summary_status,omitempty"`
}

func (x *GetLivenessSummaryStatusResponse) Reset() {
	*x = GetLivenessSummaryStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLivenessSummaryStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLivenessSummaryStatusResponse) ProtoMessage() {}

func (x *GetLivenessSummaryStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLivenessSummaryStatusResponse.ProtoReflect.Descriptor instead.
func (*GetLivenessSummaryStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetLivenessSummaryStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLivenessSummaryStatusResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *GetLivenessSummaryStatusResponse) GetSummaryStatus() liveness.SummaryStatus {
	if x != nil {
		return x.SummaryStatus
	}
	return liveness.SummaryStatus(0)
}

type GetAuthStageRefIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// client request id of the auth request
	ClientRequestId string    `protobuf:"bytes,2,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	Stage           AuthStage `protobuf:"varint,3,opt,name=stage,proto3,enum=auth.orchestrator.AuthStage" json:"stage,omitempty"`
}

func (x *GetAuthStageRefIdRequest) Reset() {
	*x = GetAuthStageRefIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthStageRefIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthStageRefIdRequest) ProtoMessage() {}

func (x *GetAuthStageRefIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthStageRefIdRequest.ProtoReflect.Descriptor instead.
func (*GetAuthStageRefIdRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetAuthStageRefIdRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAuthStageRefIdRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *GetAuthStageRefIdRequest) GetStage() AuthStage {
	if x != nil {
		return x.Stage
	}
	return AuthStage_AUTH_STAGE_UNSPECIFIED
}

type GetAuthStageRefIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	AuthStageRefId string      `protobuf:"bytes,2,opt,name=auth_stage_ref_id,json=authStageRefId,proto3" json:"auth_stage_ref_id,omitempty"`
}

func (x *GetAuthStageRefIdResponse) Reset() {
	*x = GetAuthStageRefIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_orchestrator_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthStageRefIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthStageRefIdResponse) ProtoMessage() {}

func (x *GetAuthStageRefIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_orchestrator_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthStageRefIdResponse.ProtoReflect.Descriptor instead.
func (*GetAuthStageRefIdResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_orchestrator_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetAuthStageRefIdResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAuthStageRefIdResponse) GetAuthStageRefId() string {
	if x != nil {
		return x.AuthStageRefId
	}
	return ""
}

var File_api_auth_orchestrator_service_proto protoreflect.FileDescriptor

var file_api_auth_orchestrator_service_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68,
	0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75,
	0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74,
	0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x75, 0x74, 0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x91, 0x03, 0x0a, 0x17, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x41, 0x75, 0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x77,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e,
	0x46, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02,
	0x20, 0x00, 0x52, 0x08, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4e, 0x0a, 0x0f,
	0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x72, 0x65,
	0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x22, 0x7d, 0x0a, 0x18, 0x49, 0x6e, 0x69, 0x74, 0x69,
	0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x46, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74,
	0x68, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0xe0,
	0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0b,
	0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x24, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x8c, 0x02, 0x0a, 0x12, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x74,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x32, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x7b, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x65, 0xfa, 0x42, 0x62, 0x72, 0x60, 0x32, 0x5e, 0x28, 0x5e, 0x5b, 0x61, 0x2d,
	0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x38, 0x7d, 0x2d, 0x5b, 0x61, 0x2d, 0x66,
	0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x34, 0x5b, 0x61, 0x2d, 0x66,
	0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x33, 0x7d, 0x2d, 0x5b, 0x38, 0x7c, 0x39, 0x7c,
	0x61, 0x41, 0x7c, 0x62, 0x42, 0x5d, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39,
	0x5d, 0x7b, 0x33, 0x7d, 0x2d, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d,
	0x7b, 0x31, 0x32, 0x7d, 0x24, 0x7c, 0x5e, 0x24, 0x29, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x22, 0x94, 0x02, 0x0a, 0x13, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x72, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x11, 0x72, 0x65, 0x74, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x73, 0x22, 0x91, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x0d, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x49, 0x4e, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x64, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x4f, 0x4b, 0x45, 0x4e,
	0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x59, 0x10, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x45, 0x53,
	0x45, 0x4e, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x66, 0x12, 0x17, 0x0a, 0x13, 0x52,
	0x45, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x53, 0x4f,
	0x4f, 0x4e, 0x10, 0x67, 0x12, 0x11, 0x0a, 0x0d, 0x4e, 0x4f, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x41,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x6a, 0x22, 0xe3, 0x01, 0x0a, 0x16, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x4e, 0x70, 0x63, 0x69, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49,
	0x64, 0x12, 0x1e, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49,
	0x64, 0x12, 0x36, 0x0a, 0x0f, 0x6e, 0x70, 0x63, 0x69, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x0d, 0x6e, 0x70, 0x63, 0x69,
	0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x32, 0x0a, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x22, 0xdf, 0x01,
	0x0a, 0x17, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x70, 0x63, 0x69, 0x50, 0x69,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c,
	0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x61, 0x0a, 0x0f,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x50, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0x64, 0x12,
	0x18, 0x0a, 0x14, 0x50, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x45,
	0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x42, 0x55, 0x53,
	0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x66, 0x22,
	0xad, 0x01, 0x0a, 0x10, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x32, 0x0a, 0x06, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x22,
	0x76, 0x0a, 0x11, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xaa, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x40, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x66,
	0x6c, 0x6f, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x0c, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x46, 0x6c, 0x6f, 0x77, 0x22, 0xca, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c,
	0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x0e,
	0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0d, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x95, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x66, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68,
	0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x22, 0x6b, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x66, 0x49, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x11, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x66, 0x49, 0x64, 0x32, 0x81, 0x06, 0x0a, 0x0c, 0x4f, 0x72, 0x63, 0x68, 0x65,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x6b, 0x0a, 0x10, 0x49, 0x6e, 0x69, 0x74, 0x69,
	0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x2a, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e,
	0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f,
	0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x49, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x46,
	0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x75, 0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72,
	0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75,
	0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x0b, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x4f, 0x74, 0x70, 0x12, 0x25, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x68, 0x0a, 0x0f, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x70,
	0x63, 0x69, 0x50, 0x69, 0x6e, 0x12, 0x29, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63,
	0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x4e, 0x70, 0x63, 0x69, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2a, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x70, 0x63,
	0x69, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x09,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4f, 0x74, 0x70, 0x12, 0x23, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x32, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63,
	0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x66, 0x49, 0x64, 0x12,
	0x2b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x66, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x66,
	0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6f, 0x72,
	0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6f, 0x72, 0x63, 0x68,
	0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_orchestrator_service_proto_rawDescOnce sync.Once
	file_api_auth_orchestrator_service_proto_rawDescData = file_api_auth_orchestrator_service_proto_rawDesc
)

func file_api_auth_orchestrator_service_proto_rawDescGZIP() []byte {
	file_api_auth_orchestrator_service_proto_rawDescOnce.Do(func() {
		file_api_auth_orchestrator_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_orchestrator_service_proto_rawDescData)
	})
	return file_api_auth_orchestrator_service_proto_rawDescData
}

var file_api_auth_orchestrator_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_auth_orchestrator_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_api_auth_orchestrator_service_proto_goTypes = []interface{}{
	(GenerateOtpResponse_Status)(0),              // 0: auth.orchestrator.GenerateOtpResponse.Status
	(ValidateNpciPinResponse_SecurePinStatus)(0), // 1: auth.orchestrator.ValidateNpciPinResponse.SecurePinStatus
	(*InitiateAuthFlowRequest)(nil),              // 2: auth.orchestrator.InitiateAuthFlowRequest
	(*InitiateAuthFlowResponse)(nil),             // 3: auth.orchestrator.InitiateAuthFlowResponse
	(*GetAuthFlowStatusRequest)(nil),             // 4: auth.orchestrator.GetAuthFlowStatusRequest
	(*GetAuthFlowStatusResponse)(nil),            // 5: auth.orchestrator.GetAuthFlowStatusResponse
	(*GenerateOtpRequest)(nil),                   // 6: auth.orchestrator.GenerateOtpRequest
	(*GenerateOtpResponse)(nil),                  // 7: auth.orchestrator.GenerateOtpResponse
	(*ValidateNpciPinRequest)(nil),               // 8: auth.orchestrator.ValidateNpciPinRequest
	(*ValidateNpciPinResponse)(nil),              // 9: auth.orchestrator.ValidateNpciPinResponse
	(*VerifyOtpRequest)(nil),                     // 10: auth.orchestrator.VerifyOtpRequest
	(*VerifyOtpResponse)(nil),                    // 11: auth.orchestrator.VerifyOtpResponse
	(*GetLivenessSummaryStatusRequest)(nil),      // 12: auth.orchestrator.GetLivenessSummaryStatusRequest
	(*GetLivenessSummaryStatusResponse)(nil),     // 13: auth.orchestrator.GetLivenessSummaryStatusResponse
	(*GetAuthStageRefIdRequest)(nil),             // 14: auth.orchestrator.GetAuthStageRefIdRequest
	(*GetAuthStageRefIdResponse)(nil),            // 15: auth.orchestrator.GetAuthStageRefIdResponse
	(FlowName)(0),                                // 16: auth.orchestrator.FlowName
	(*deeplink.Deeplink)(nil),                    // 17: frontend.deeplink.Deeplink
	(Provenance)(0),                              // 18: auth.orchestrator.Provenance
	(*Preferences)(nil),                          // 19: auth.orchestrator.Preferences
	(*rpc.Status)(nil),                           // 20: rpc.Status
	(AuthRequestStatus)(0),                       // 21: auth.orchestrator.AuthRequestStatus
	(*common.Device)(nil),                        // 22: api.typesv2.common.Device
	(*upi.CredBlock)(nil),                        // 23: upi.CredBlock
	(liveness.LivenessFlow)(0),                   // 24: auth.liveness.LivenessFlow
	(liveness.SummaryStatus)(0),                  // 25: auth.liveness.SummaryStatus
	(AuthStage)(0),                               // 26: auth.orchestrator.AuthStage
}
var file_api_auth_orchestrator_service_proto_depIdxs = []int32{
	16, // 0: auth.orchestrator.InitiateAuthFlowRequest.flow_name:type_name -> auth.orchestrator.FlowName
	17, // 1: auth.orchestrator.InitiateAuthFlowRequest.redirect_action:type_name -> frontend.deeplink.Deeplink
	18, // 2: auth.orchestrator.InitiateAuthFlowRequest.provenance:type_name -> auth.orchestrator.Provenance
	19, // 3: auth.orchestrator.InitiateAuthFlowRequest.preferences:type_name -> auth.orchestrator.Preferences
	20, // 4: auth.orchestrator.InitiateAuthFlowResponse.status:type_name -> rpc.Status
	17, // 5: auth.orchestrator.InitiateAuthFlowResponse.next_action:type_name -> frontend.deeplink.Deeplink
	20, // 6: auth.orchestrator.GetAuthFlowStatusResponse.status:type_name -> rpc.Status
	21, // 7: auth.orchestrator.GetAuthFlowStatusResponse.auth_status:type_name -> auth.orchestrator.AuthRequestStatus
	17, // 8: auth.orchestrator.GetAuthFlowStatusResponse.next_action:type_name -> frontend.deeplink.Deeplink
	22, // 9: auth.orchestrator.GenerateOtpRequest.device:type_name -> api.typesv2.common.Device
	20, // 10: auth.orchestrator.GenerateOtpResponse.status:type_name -> rpc.Status
	23, // 11: auth.orchestrator.ValidateNpciPinRequest.npci_cred_block:type_name -> upi.CredBlock
	22, // 12: auth.orchestrator.ValidateNpciPinRequest.device:type_name -> api.typesv2.common.Device
	20, // 13: auth.orchestrator.ValidateNpciPinResponse.status:type_name -> rpc.Status
	17, // 14: auth.orchestrator.ValidateNpciPinResponse.next_action:type_name -> frontend.deeplink.Deeplink
	22, // 15: auth.orchestrator.VerifyOtpRequest.device:type_name -> api.typesv2.common.Device
	20, // 16: auth.orchestrator.VerifyOtpResponse.status:type_name -> rpc.Status
	17, // 17: auth.orchestrator.VerifyOtpResponse.next_action:type_name -> frontend.deeplink.Deeplink
	24, // 18: auth.orchestrator.GetLivenessSummaryStatusRequest.liveness_flow:type_name -> auth.liveness.LivenessFlow
	20, // 19: auth.orchestrator.GetLivenessSummaryStatusResponse.status:type_name -> rpc.Status
	17, // 20: auth.orchestrator.GetLivenessSummaryStatusResponse.next_action:type_name -> frontend.deeplink.Deeplink
	25, // 21: auth.orchestrator.GetLivenessSummaryStatusResponse.summary_status:type_name -> auth.liveness.SummaryStatus
	26, // 22: auth.orchestrator.GetAuthStageRefIdRequest.stage:type_name -> auth.orchestrator.AuthStage
	20, // 23: auth.orchestrator.GetAuthStageRefIdResponse.status:type_name -> rpc.Status
	2,  // 24: auth.orchestrator.Orchestrator.InitiateAuthFlow:input_type -> auth.orchestrator.InitiateAuthFlowRequest
	4,  // 25: auth.orchestrator.Orchestrator.GetAuthFlowStatus:input_type -> auth.orchestrator.GetAuthFlowStatusRequest
	6,  // 26: auth.orchestrator.Orchestrator.GenerateOtp:input_type -> auth.orchestrator.GenerateOtpRequest
	8,  // 27: auth.orchestrator.Orchestrator.ValidateNpciPin:input_type -> auth.orchestrator.ValidateNpciPinRequest
	10, // 28: auth.orchestrator.Orchestrator.VerifyOtp:input_type -> auth.orchestrator.VerifyOtpRequest
	12, // 29: auth.orchestrator.Orchestrator.GetLivenessSummaryStatus:input_type -> auth.orchestrator.GetLivenessSummaryStatusRequest
	14, // 30: auth.orchestrator.Orchestrator.GetAuthStageRefId:input_type -> auth.orchestrator.GetAuthStageRefIdRequest
	3,  // 31: auth.orchestrator.Orchestrator.InitiateAuthFlow:output_type -> auth.orchestrator.InitiateAuthFlowResponse
	5,  // 32: auth.orchestrator.Orchestrator.GetAuthFlowStatus:output_type -> auth.orchestrator.GetAuthFlowStatusResponse
	7,  // 33: auth.orchestrator.Orchestrator.GenerateOtp:output_type -> auth.orchestrator.GenerateOtpResponse
	9,  // 34: auth.orchestrator.Orchestrator.ValidateNpciPin:output_type -> auth.orchestrator.ValidateNpciPinResponse
	11, // 35: auth.orchestrator.Orchestrator.VerifyOtp:output_type -> auth.orchestrator.VerifyOtpResponse
	13, // 36: auth.orchestrator.Orchestrator.GetLivenessSummaryStatus:output_type -> auth.orchestrator.GetLivenessSummaryStatusResponse
	15, // 37: auth.orchestrator.Orchestrator.GetAuthStageRefId:output_type -> auth.orchestrator.GetAuthStageRefIdResponse
	31, // [31:38] is the sub-list for method output_type
	24, // [24:31] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_api_auth_orchestrator_service_proto_init() }
func file_api_auth_orchestrator_service_proto_init() {
	if File_api_auth_orchestrator_service_proto != nil {
		return
	}
	file_api_auth_orchestrator_internal_auth_request_proto_init()
	file_api_auth_orchestrator_internal_auth_request_stage_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_auth_orchestrator_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateAuthFlowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateAuthFlowResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAuthFlowStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAuthFlowStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateOtpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateOtpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateNpciPinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateNpciPinResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyOtpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyOtpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLivenessSummaryStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLivenessSummaryStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAuthStageRefIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_orchestrator_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAuthStageRefIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_orchestrator_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_auth_orchestrator_service_proto_goTypes,
		DependencyIndexes: file_api_auth_orchestrator_service_proto_depIdxs,
		EnumInfos:         file_api_auth_orchestrator_service_proto_enumTypes,
		MessageInfos:      file_api_auth_orchestrator_service_proto_msgTypes,
	}.Build()
	File_api_auth_orchestrator_service_proto = out.File
	file_api_auth_orchestrator_service_proto_rawDesc = nil
	file_api_auth_orchestrator_service_proto_goTypes = nil
	file_api_auth_orchestrator_service_proto_depIdxs = nil
}
