// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/auth/location/service.proto

package location

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Location_GetCoordinates_FullMethodName   = "/location.Location/GetCoordinates"
	Location_GetOrCreateToken_FullMethodName = "/location.Location/GetOrCreateToken"
)

// LocationClient is the client API for Location service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LocationClient interface {
	// GetCoordinates fetches the coordinates associated with a given location token.
	// The coordinates might not be precise and can differ up to 2kms from the original coordinates.
	// This is in lines with protecting the user's privacy.
	GetCoordinates(ctx context.Context, in *GetCoordinatesRequest, opts ...grpc.CallOption) (*GetCoordinatesResponse, error)
	// This RPC facilitates upserting the location data in the auth service's location schema and return an identifier for the same.
	GetOrCreateToken(ctx context.Context, in *GetOrCreateTokenRequest, opts ...grpc.CallOption) (*GetOrCreateTokenResponse, error)
}

type locationClient struct {
	cc grpc.ClientConnInterface
}

func NewLocationClient(cc grpc.ClientConnInterface) LocationClient {
	return &locationClient{cc}
}

func (c *locationClient) GetCoordinates(ctx context.Context, in *GetCoordinatesRequest, opts ...grpc.CallOption) (*GetCoordinatesResponse, error) {
	out := new(GetCoordinatesResponse)
	err := c.cc.Invoke(ctx, Location_GetCoordinates_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *locationClient) GetOrCreateToken(ctx context.Context, in *GetOrCreateTokenRequest, opts ...grpc.CallOption) (*GetOrCreateTokenResponse, error) {
	out := new(GetOrCreateTokenResponse)
	err := c.cc.Invoke(ctx, Location_GetOrCreateToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LocationServer is the server API for Location service.
// All implementations should embed UnimplementedLocationServer
// for forward compatibility
type LocationServer interface {
	// GetCoordinates fetches the coordinates associated with a given location token.
	// The coordinates might not be precise and can differ up to 2kms from the original coordinates.
	// This is in lines with protecting the user's privacy.
	GetCoordinates(context.Context, *GetCoordinatesRequest) (*GetCoordinatesResponse, error)
	// This RPC facilitates upserting the location data in the auth service's location schema and return an identifier for the same.
	GetOrCreateToken(context.Context, *GetOrCreateTokenRequest) (*GetOrCreateTokenResponse, error)
}

// UnimplementedLocationServer should be embedded to have forward compatible implementations.
type UnimplementedLocationServer struct {
}

func (UnimplementedLocationServer) GetCoordinates(context.Context, *GetCoordinatesRequest) (*GetCoordinatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCoordinates not implemented")
}
func (UnimplementedLocationServer) GetOrCreateToken(context.Context, *GetOrCreateTokenRequest) (*GetOrCreateTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrCreateToken not implemented")
}

// UnsafeLocationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LocationServer will
// result in compilation errors.
type UnsafeLocationServer interface {
	mustEmbedUnimplementedLocationServer()
}

func RegisterLocationServer(s grpc.ServiceRegistrar, srv LocationServer) {
	s.RegisterService(&Location_ServiceDesc, srv)
}

func _Location_GetCoordinates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoordinatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LocationServer).GetCoordinates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Location_GetCoordinates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LocationServer).GetCoordinates(ctx, req.(*GetCoordinatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Location_GetOrCreateToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrCreateTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LocationServer).GetOrCreateToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Location_GetOrCreateToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LocationServer).GetOrCreateToken(ctx, req.(*GetOrCreateTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Location_ServiceDesc is the grpc.ServiceDesc for Location service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Location_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "location.Location",
	HandlerType: (*LocationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCoordinates",
			Handler:    _Location_GetCoordinates_Handler,
		},
		{
			MethodName: "GetOrCreateToken",
			Handler:    _Location_GetOrCreateToken_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/auth/location/service.proto",
}
