// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/location/service.proto

package location

import (
	rpc "github.com/epifi/be-common/api/rpc"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetCoordinatesResponse_Status int32

const (
	GetCoordinatesResponse_OK GetCoordinatesResponse_Status = 0
	// Coordinates not found for a given token
	GetCoordinatesResponse_NOT_FOUND GetCoordinatesResponse_Status = 5
	// Internal Error
	GetCoordinatesResponse_INTERNAL GetCoordinatesResponse_Status = 13
)

// Enum value maps for GetCoordinatesResponse_Status.
var (
	GetCoordinatesResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetCoordinatesResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetCoordinatesResponse_Status) Enum() *GetCoordinatesResponse_Status {
	p := new(GetCoordinatesResponse_Status)
	*p = x
	return p
}

func (x GetCoordinatesResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetCoordinatesResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_location_service_proto_enumTypes[0].Descriptor()
}

func (GetCoordinatesResponse_Status) Type() protoreflect.EnumType {
	return &file_api_auth_location_service_proto_enumTypes[0]
}

func (x GetCoordinatesResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetCoordinatesResponse_Status.Descriptor instead.
func (GetCoordinatesResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_location_service_proto_rawDescGZIP(), []int{1, 0}
}

type GetOrCreateTokenResponse_Status int32

const (
	GetOrCreateTokenResponse_OK GetOrCreateTokenResponse_Status = 0
	// Internal Error
	GetOrCreateTokenResponse_INTERNAL GetOrCreateTokenResponse_Status = 13
)

// Enum value maps for GetOrCreateTokenResponse_Status.
var (
	GetOrCreateTokenResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetOrCreateTokenResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetOrCreateTokenResponse_Status) Enum() *GetOrCreateTokenResponse_Status {
	p := new(GetOrCreateTokenResponse_Status)
	*p = x
	return p
}

func (x GetOrCreateTokenResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetOrCreateTokenResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_location_service_proto_enumTypes[1].Descriptor()
}

func (GetOrCreateTokenResponse_Status) Type() protoreflect.EnumType {
	return &file_api_auth_location_service_proto_enumTypes[1]
}

func (x GetOrCreateTokenResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetOrCreateTokenResponse_Status.Descriptor instead.
func (GetOrCreateTokenResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_location_service_proto_rawDescGZIP(), []int{3, 0}
}

type GetCoordinatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier mapping to a (lat,long) co-ordinates
	LocationToken string `protobuf:"bytes,1,opt,name=location_token,json=locationToken,proto3" json:"location_token,omitempty"`
}

func (x *GetCoordinatesRequest) Reset() {
	*x = GetCoordinatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_location_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCoordinatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCoordinatesRequest) ProtoMessage() {}

func (x *GetCoordinatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_location_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCoordinatesRequest.ProtoReflect.Descriptor instead.
func (*GetCoordinatesRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_location_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetCoordinatesRequest) GetLocationToken() string {
	if x != nil {
		return x.LocationToken
	}
	return ""
}

type GetCoordinatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// latitude and longitude for the given token
	LatLng *latlng.LatLng `protobuf:"bytes,2,opt,name=lat_lng,json=latLng,proto3" json:"lat_lng,omitempty"`
}

func (x *GetCoordinatesResponse) Reset() {
	*x = GetCoordinatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_location_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCoordinatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCoordinatesResponse) ProtoMessage() {}

func (x *GetCoordinatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_location_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCoordinatesResponse.ProtoReflect.Descriptor instead.
func (*GetCoordinatesResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_location_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetCoordinatesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCoordinatesResponse) GetLatLng() *latlng.LatLng {
	if x != nil {
		return x.LatLng
	}
	return nil
}

type GetOrCreateTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// latitude and longitude of the device
	LatLng *latlng.LatLng `protobuf:"bytes,1,opt,name=lat_lng,json=latLng,proto3" json:"lat_lng,omitempty"`
}

func (x *GetOrCreateTokenRequest) Reset() {
	*x = GetOrCreateTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_location_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrCreateTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrCreateTokenRequest) ProtoMessage() {}

func (x *GetOrCreateTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_location_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrCreateTokenRequest.ProtoReflect.Descriptor instead.
func (*GetOrCreateTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_location_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetOrCreateTokenRequest) GetLatLng() *latlng.LatLng {
	if x != nil {
		return x.LatLng
	}
	return nil
}

type GetOrCreateTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// unique identifier mapping to a (lat,long) co-ordinates
	LocationToken string `protobuf:"bytes,2,opt,name=location_token,json=locationToken,proto3" json:"location_token,omitempty"`
}

func (x *GetOrCreateTokenResponse) Reset() {
	*x = GetOrCreateTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_location_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrCreateTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrCreateTokenResponse) ProtoMessage() {}

func (x *GetOrCreateTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_location_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrCreateTokenResponse.ProtoReflect.Descriptor instead.
func (*GetOrCreateTokenResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_location_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetOrCreateTokenResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetOrCreateTokenResponse) GetLocationToken() string {
	if x != nil {
		return x.LocationToken
	}
	return ""
}

var File_api_auth_location_service_proto protoreflect.FileDescriptor

var file_api_auth_location_service_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x14, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c,
	0x61, 0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3e, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x9a, 0x01, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a, 0x07, 0x6c,
	0x61, 0x74, 0x5f, 0x6c, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e,
	0x67, 0x52, 0x06, 0x6c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e,
	0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x47, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4f,
	0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x07, 0x6c, 0x61, 0x74, 0x5f, 0x6c, 0x6e, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x06, 0x6c, 0x61, 0x74, 0x4c, 0x6e,
	0x67, 0x22, 0x86, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x32, 0xba, 0x01, 0x0a, 0x08, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x53, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x2e, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61,
	0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e,
	0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59, 0x0a, 0x10,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x21, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4f,
	0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47,
	0x65, 0x74, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_location_service_proto_rawDescOnce sync.Once
	file_api_auth_location_service_proto_rawDescData = file_api_auth_location_service_proto_rawDesc
)

func file_api_auth_location_service_proto_rawDescGZIP() []byte {
	file_api_auth_location_service_proto_rawDescOnce.Do(func() {
		file_api_auth_location_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_location_service_proto_rawDescData)
	})
	return file_api_auth_location_service_proto_rawDescData
}

var file_api_auth_location_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_auth_location_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_auth_location_service_proto_goTypes = []interface{}{
	(GetCoordinatesResponse_Status)(0),   // 0: location.GetCoordinatesResponse.Status
	(GetOrCreateTokenResponse_Status)(0), // 1: location.GetOrCreateTokenResponse.Status
	(*GetCoordinatesRequest)(nil),        // 2: location.GetCoordinatesRequest
	(*GetCoordinatesResponse)(nil),       // 3: location.GetCoordinatesResponse
	(*GetOrCreateTokenRequest)(nil),      // 4: location.GetOrCreateTokenRequest
	(*GetOrCreateTokenResponse)(nil),     // 5: location.GetOrCreateTokenResponse
	(*rpc.Status)(nil),                   // 6: rpc.Status
	(*latlng.LatLng)(nil),                // 7: google.type.LatLng
}
var file_api_auth_location_service_proto_depIdxs = []int32{
	6, // 0: location.GetCoordinatesResponse.status:type_name -> rpc.Status
	7, // 1: location.GetCoordinatesResponse.lat_lng:type_name -> google.type.LatLng
	7, // 2: location.GetOrCreateTokenRequest.lat_lng:type_name -> google.type.LatLng
	6, // 3: location.GetOrCreateTokenResponse.status:type_name -> rpc.Status
	2, // 4: location.Location.GetCoordinates:input_type -> location.GetCoordinatesRequest
	4, // 5: location.Location.GetOrCreateToken:input_type -> location.GetOrCreateTokenRequest
	3, // 6: location.Location.GetCoordinates:output_type -> location.GetCoordinatesResponse
	5, // 7: location.Location.GetOrCreateToken:output_type -> location.GetOrCreateTokenResponse
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_auth_location_service_proto_init() }
func file_api_auth_location_service_proto_init() {
	if File_api_auth_location_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_location_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCoordinatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_location_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCoordinatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_location_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrCreateTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_location_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrCreateTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_location_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_auth_location_service_proto_goTypes,
		DependencyIndexes: file_api_auth_location_service_proto_depIdxs,
		EnumInfos:         file_api_auth_location_service_proto_enumTypes,
		MessageInfos:      file_api_auth_location_service_proto_msgTypes,
	}.Build()
	File_api_auth_location_service_proto = out.File
	file_api_auth_location_service_proto_rawDesc = nil
	file_api_auth_location_service_proto_goTypes = nil
	file_api_auth_location_service_proto_depIdxs = nil
}
