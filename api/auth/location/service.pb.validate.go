// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/location/service.proto

package location

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetCoordinatesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCoordinatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCoordinatesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCoordinatesRequestMultiError, or nil if none found.
func (m *GetCoordinatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCoordinatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LocationToken

	if len(errors) > 0 {
		return GetCoordinatesRequestMultiError(errors)
	}

	return nil
}

// GetCoordinatesRequestMultiError is an error wrapping multiple validation
// errors returned by GetCoordinatesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCoordinatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCoordinatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCoordinatesRequestMultiError) AllErrors() []error { return m }

// GetCoordinatesRequestValidationError is the validation error returned by
// GetCoordinatesRequest.Validate if the designated constraints aren't met.
type GetCoordinatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCoordinatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCoordinatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCoordinatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCoordinatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCoordinatesRequestValidationError) ErrorName() string {
	return "GetCoordinatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCoordinatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCoordinatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCoordinatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCoordinatesRequestValidationError{}

// Validate checks the field values on GetCoordinatesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCoordinatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCoordinatesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCoordinatesResponseMultiError, or nil if none found.
func (m *GetCoordinatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCoordinatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCoordinatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCoordinatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCoordinatesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLatLng()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCoordinatesResponseValidationError{
					field:  "LatLng",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCoordinatesResponseValidationError{
					field:  "LatLng",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatLng()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCoordinatesResponseValidationError{
				field:  "LatLng",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCoordinatesResponseMultiError(errors)
	}

	return nil
}

// GetCoordinatesResponseMultiError is an error wrapping multiple validation
// errors returned by GetCoordinatesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCoordinatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCoordinatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCoordinatesResponseMultiError) AllErrors() []error { return m }

// GetCoordinatesResponseValidationError is the validation error returned by
// GetCoordinatesResponse.Validate if the designated constraints aren't met.
type GetCoordinatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCoordinatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCoordinatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCoordinatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCoordinatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCoordinatesResponseValidationError) ErrorName() string {
	return "GetCoordinatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCoordinatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCoordinatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCoordinatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCoordinatesResponseValidationError{}

// Validate checks the field values on GetOrCreateTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrCreateTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrCreateTokenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrCreateTokenRequestMultiError, or nil if none found.
func (m *GetOrCreateTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrCreateTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLatLng()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrCreateTokenRequestValidationError{
					field:  "LatLng",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrCreateTokenRequestValidationError{
					field:  "LatLng",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatLng()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrCreateTokenRequestValidationError{
				field:  "LatLng",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOrCreateTokenRequestMultiError(errors)
	}

	return nil
}

// GetOrCreateTokenRequestMultiError is an error wrapping multiple validation
// errors returned by GetOrCreateTokenRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOrCreateTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrCreateTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrCreateTokenRequestMultiError) AllErrors() []error { return m }

// GetOrCreateTokenRequestValidationError is the validation error returned by
// GetOrCreateTokenRequest.Validate if the designated constraints aren't met.
type GetOrCreateTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrCreateTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrCreateTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrCreateTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrCreateTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrCreateTokenRequestValidationError) ErrorName() string {
	return "GetOrCreateTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrCreateTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrCreateTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrCreateTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrCreateTokenRequestValidationError{}

// Validate checks the field values on GetOrCreateTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrCreateTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrCreateTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrCreateTokenResponseMultiError, or nil if none found.
func (m *GetOrCreateTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrCreateTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrCreateTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrCreateTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrCreateTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LocationToken

	if len(errors) > 0 {
		return GetOrCreateTokenResponseMultiError(errors)
	}

	return nil
}

// GetOrCreateTokenResponseMultiError is an error wrapping multiple validation
// errors returned by GetOrCreateTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type GetOrCreateTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrCreateTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrCreateTokenResponseMultiError) AllErrors() []error { return m }

// GetOrCreateTokenResponseValidationError is the validation error returned by
// GetOrCreateTokenResponse.Validate if the designated constraints aren't met.
type GetOrCreateTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrCreateTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrCreateTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrCreateTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrCreateTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrCreateTokenResponseValidationError) ErrorName() string {
	return "GetOrCreateTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrCreateTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrCreateTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrCreateTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrCreateTokenResponseValidationError{}
