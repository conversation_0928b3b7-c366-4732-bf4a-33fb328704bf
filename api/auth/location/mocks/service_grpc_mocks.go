// Code generated by MockGen. DO NOT EDIT.
// Source: api/auth/location/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	location "github.com/epifi/gamma/api/auth/location"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockLocationClient is a mock of LocationClient interface.
type MockLocationClient struct {
	ctrl     *gomock.Controller
	recorder *MockLocationClientMockRecorder
}

// MockLocationClientMockRecorder is the mock recorder for MockLocationClient.
type MockLocationClientMockRecorder struct {
	mock *MockLocationClient
}

// NewMockLocationClient creates a new mock instance.
func NewMockLocationClient(ctrl *gomock.Controller) *MockLocationClient {
	mock := &MockLocationClient{ctrl: ctrl}
	mock.recorder = &MockLocationClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLocationClient) EXPECT() *MockLocationClientMockRecorder {
	return m.recorder
}

// GetCoordinates mocks base method.
func (m *MockLocationClient) GetCoordinates(ctx context.Context, in *location.GetCoordinatesRequest, opts ...grpc.CallOption) (*location.GetCoordinatesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCoordinates", varargs...)
	ret0, _ := ret[0].(*location.GetCoordinatesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoordinates indicates an expected call of GetCoordinates.
func (mr *MockLocationClientMockRecorder) GetCoordinates(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoordinates", reflect.TypeOf((*MockLocationClient)(nil).GetCoordinates), varargs...)
}

// GetOrCreateToken mocks base method.
func (m *MockLocationClient) GetOrCreateToken(ctx context.Context, in *location.GetOrCreateTokenRequest, opts ...grpc.CallOption) (*location.GetOrCreateTokenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOrCreateToken", varargs...)
	ret0, _ := ret[0].(*location.GetOrCreateTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrCreateToken indicates an expected call of GetOrCreateToken.
func (mr *MockLocationClientMockRecorder) GetOrCreateToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrCreateToken", reflect.TypeOf((*MockLocationClient)(nil).GetOrCreateToken), varargs...)
}

// MockLocationServer is a mock of LocationServer interface.
type MockLocationServer struct {
	ctrl     *gomock.Controller
	recorder *MockLocationServerMockRecorder
}

// MockLocationServerMockRecorder is the mock recorder for MockLocationServer.
type MockLocationServerMockRecorder struct {
	mock *MockLocationServer
}

// NewMockLocationServer creates a new mock instance.
func NewMockLocationServer(ctrl *gomock.Controller) *MockLocationServer {
	mock := &MockLocationServer{ctrl: ctrl}
	mock.recorder = &MockLocationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLocationServer) EXPECT() *MockLocationServerMockRecorder {
	return m.recorder
}

// GetCoordinates mocks base method.
func (m *MockLocationServer) GetCoordinates(arg0 context.Context, arg1 *location.GetCoordinatesRequest) (*location.GetCoordinatesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoordinates", arg0, arg1)
	ret0, _ := ret[0].(*location.GetCoordinatesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoordinates indicates an expected call of GetCoordinates.
func (mr *MockLocationServerMockRecorder) GetCoordinates(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoordinates", reflect.TypeOf((*MockLocationServer)(nil).GetCoordinates), arg0, arg1)
}

// GetOrCreateToken mocks base method.
func (m *MockLocationServer) GetOrCreateToken(arg0 context.Context, arg1 *location.GetOrCreateTokenRequest) (*location.GetOrCreateTokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrCreateToken", arg0, arg1)
	ret0, _ := ret[0].(*location.GetOrCreateTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrCreateToken indicates an expected call of GetOrCreateToken.
func (mr *MockLocationServerMockRecorder) GetOrCreateToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrCreateToken", reflect.TypeOf((*MockLocationServer)(nil).GetOrCreateToken), arg0, arg1)
}

// MockUnsafeLocationServer is a mock of UnsafeLocationServer interface.
type MockUnsafeLocationServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeLocationServerMockRecorder
}

// MockUnsafeLocationServerMockRecorder is the mock recorder for MockUnsafeLocationServer.
type MockUnsafeLocationServerMockRecorder struct {
	mock *MockUnsafeLocationServer
}

// NewMockUnsafeLocationServer creates a new mock instance.
func NewMockUnsafeLocationServer(ctrl *gomock.Controller) *MockUnsafeLocationServer {
	mock := &MockUnsafeLocationServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeLocationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeLocationServer) EXPECT() *MockUnsafeLocationServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedLocationServer mocks base method.
func (m *MockUnsafeLocationServer) mustEmbedUnimplementedLocationServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedLocationServer")
}

// mustEmbedUnimplementedLocationServer indicates an expected call of mustEmbedUnimplementedLocationServer.
func (mr *MockUnsafeLocationServerMockRecorder) mustEmbedUnimplementedLocationServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedLocationServer", reflect.TypeOf((*MockUnsafeLocationServer)(nil).mustEmbedUnimplementedLocationServer))
}
