// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/device_integrity_result.proto

package auth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.Platform(0)
)

// Validate checks the field values on DeviceIntegrityResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeviceIntegrityResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceIntegrityResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeviceIntegrityResultMultiError, or nil if none found.
func (m *DeviceIntegrityResult) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceIntegrityResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for DeviceId

	// no validation rules for Attestation

	// no validation rules for DeviceOs

	// no validation rules for Result

	// no validation rules for Advice

	// no validation rules for FailureReason

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceIntegrityResultValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceIntegrityResultValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceIntegrityResultValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceIntegrityResultValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceIntegrityResultValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceIntegrityResultValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceIntegrityResultValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceIntegrityResultValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceIntegrityResultValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeviceIntegrityResultMultiError(errors)
	}

	return nil
}

// DeviceIntegrityResultMultiError is an error wrapping multiple validation
// errors returned by DeviceIntegrityResult.ValidateAll() if the designated
// constraints aren't met.
type DeviceIntegrityResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceIntegrityResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceIntegrityResultMultiError) AllErrors() []error { return m }

// DeviceIntegrityResultValidationError is the validation error returned by
// DeviceIntegrityResult.Validate if the designated constraints aren't met.
type DeviceIntegrityResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceIntegrityResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceIntegrityResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceIntegrityResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceIntegrityResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceIntegrityResultValidationError) ErrorName() string {
	return "DeviceIntegrityResultValidationError"
}

// Error satisfies the builtin error interface
func (e DeviceIntegrityResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceIntegrityResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceIntegrityResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceIntegrityResultValidationError{}
