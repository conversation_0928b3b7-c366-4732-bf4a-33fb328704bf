// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/otp.proto

package auth

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Proto for otp model
type Otp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// phone number of the user corresponding to the given actor
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// phone number of the user corresponding to the given actor
	Device *common.Device `protobuf:"bytes,3,opt,name=device,proto3" json:"device,omitempty"`
	// Number of invalid attempts. Any attempt after `x` invalid attempts are rejected
	VerifyAttempts int32 `protobuf:"varint,4,opt,name=verify_attempts,json=verifyAttempts,proto3" json:"verify_attempts,omitempty"`
	// Number of attempts made to delivery OTP via SMS
	SmsAttempts int32 `protobuf:"varint,5,opt,name=sms_attempts,json=smsAttempts,proto3" json:"sms_attempts,omitempty"`
	// Unique request id of Generate Request
	Token string `protobuf:"bytes,6,opt,name=token,proto3" json:"token,omitempty"`
	// timestamp of most recent SMS
	SmsTimestamp *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=sms_timestamp,json=smsTimestamp,proto3" json:"sms_timestamp,omitempty"`
	// timestamp of most recent OTP
	LastSentAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=last_sent_at,json=lastSentAt,proto3" json:"last_sent_at,omitempty"`
	// Number of attempts made to deliver OTP
	SendAttempts int32 `protobuf:"varint,11,opt,name=send_attempts,json=sendAttempts,proto3" json:"send_attempts,omitempty"`
	// timestamp at which vendor otp generation request was initiated
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// timestamp at which vendor otp generation request was last modified
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// timestamp at which vendor otp generation request was deleted
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// ownership of otp. If otp is requested by epifi_tech services ownership is `EPIFI_TECH`
	// if otp is requested by epifi_wealth services, ownership is `EPIFI_WEALTH`
	Ownership common.Ownership `protobuf:"varint,13,opt,name=ownership,proto3,enum=api.typesv2.common.Ownership" json:"ownership,omitempty"`
}

func (x *Otp) Reset() {
	*x = Otp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_otp_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Otp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Otp) ProtoMessage() {}

func (x *Otp) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_otp_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Otp.ProtoReflect.Descriptor instead.
func (*Otp) Descriptor() ([]byte, []int) {
	return file_api_auth_otp_proto_rawDescGZIP(), []int{0}
}

func (x *Otp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Otp) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *Otp) GetDevice() *common.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *Otp) GetVerifyAttempts() int32 {
	if x != nil {
		return x.VerifyAttempts
	}
	return 0
}

func (x *Otp) GetSmsAttempts() int32 {
	if x != nil {
		return x.SmsAttempts
	}
	return 0
}

func (x *Otp) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *Otp) GetSmsTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.SmsTimestamp
	}
	return nil
}

func (x *Otp) GetLastSentAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSentAt
	}
	return nil
}

func (x *Otp) GetSendAttempts() int32 {
	if x != nil {
		return x.SendAttempts
	}
	return 0
}

func (x *Otp) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Otp) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Otp) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *Otp) GetOwnership() common.Ownership {
	if x != nil {
		return x.Ownership
	}
	return common.Ownership(0)
}

var File_api_auth_otp_proto protoreflect.FileDescriptor

var file_api_auth_otp_proto_rawDesc = []byte{
	0x0a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6f, 0x74, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x61, 0x75, 0x74, 0x68, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x81, 0x05, 0x0a, 0x03, 0x4f, 0x74, 0x70, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x73, 0x6d, 0x73, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x6d, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x3f, 0x0a, 0x0d, 0x73, 0x6d, 0x73,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x73, 0x6d,
	0x73, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x3c, 0x0a, 0x0c, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x6c, 0x61,
	0x73, 0x74, 0x53, 0x65, 0x6e, 0x74, 0x41, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x6e, 0x64,
	0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x73, 0x65, 0x6e, 0x64, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3b,
	0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x42, 0x42, 0x0a, 0x1f, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x5a, 0x1f,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_otp_proto_rawDescOnce sync.Once
	file_api_auth_otp_proto_rawDescData = file_api_auth_otp_proto_rawDesc
)

func file_api_auth_otp_proto_rawDescGZIP() []byte {
	file_api_auth_otp_proto_rawDescOnce.Do(func() {
		file_api_auth_otp_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_otp_proto_rawDescData)
	})
	return file_api_auth_otp_proto_rawDescData
}

var file_api_auth_otp_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_auth_otp_proto_goTypes = []interface{}{
	(*Otp)(nil),                   // 0: auth.Otp
	(*common.PhoneNumber)(nil),    // 1: api.typesv2.common.PhoneNumber
	(*common.Device)(nil),         // 2: api.typesv2.common.Device
	(*timestamppb.Timestamp)(nil), // 3: google.protobuf.Timestamp
	(common.Ownership)(0),         // 4: api.typesv2.common.Ownership
}
var file_api_auth_otp_proto_depIdxs = []int32{
	1, // 0: auth.Otp.phone_number:type_name -> api.typesv2.common.PhoneNumber
	2, // 1: auth.Otp.device:type_name -> api.typesv2.common.Device
	3, // 2: auth.Otp.sms_timestamp:type_name -> google.protobuf.Timestamp
	3, // 3: auth.Otp.last_sent_at:type_name -> google.protobuf.Timestamp
	3, // 4: auth.Otp.created_at:type_name -> google.protobuf.Timestamp
	3, // 5: auth.Otp.updated_at:type_name -> google.protobuf.Timestamp
	3, // 6: auth.Otp.deleted_at:type_name -> google.protobuf.Timestamp
	4, // 7: auth.Otp.ownership:type_name -> api.typesv2.common.Ownership
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_auth_otp_proto_init() }
func file_api_auth_otp_proto_init() {
	if File_api_auth_otp_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_otp_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Otp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_otp_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_otp_proto_goTypes,
		DependencyIndexes: file_api_auth_otp_proto_depIdxs,
		MessageInfos:      file_api_auth_otp_proto_msgTypes,
	}.Build()
	File_api_auth_otp_proto = out.File
	file_api_auth_otp_proto_rawDesc = nil
	file_api_auth_otp_proto_goTypes = nil
	file_api_auth_otp_proto_depIdxs = nil
}
