//    Proto & API definitions for Auth service
//       * User Sign up(& Login) with phonenumber
//            * Generate OTP
//               * Resend OTP
//            * Verify OTP
//       * Link OAuth account
//       * Validate authentication tokens
//       * Logout

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/auth/service.proto

package auth

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Auth_GenerateOtp_FullMethodName                    = "/auth.Auth/GenerateOtp"
	Auth_VerifyOtp_FullMethodName                      = "/auth.Auth/VerifyOtp"
	Auth_CreateToken_FullMethodName                    = "/auth.Auth/CreateToken"
	Auth_ValidateToken_FullMethodName                  = "/auth.Auth/ValidateToken"
	Auth_UpdateToken_FullMethodName                    = "/auth.Auth/UpdateToken"
	Auth_GetDeviceDetails_FullMethodName               = "/auth.Auth/GetDeviceDetails"
	Auth_ValidateOAuthToken_FullMethodName             = "/auth.Auth/ValidateOAuthToken"
	Auth_RegisterDevice_FullMethodName                 = "/auth.Auth/RegisterDevice"
	Auth_GetDeviceBindingSMS_FullMethodName            = "/auth.Auth/GetDeviceBindingSMS"
	Auth_GetDeviceAuth_FullMethodName                  = "/auth.Auth/GetDeviceAuth"
	Auth_RegisterAuthFactorUpdate_FullMethodName       = "/auth.Auth/RegisterAuthFactorUpdate"
	Auth_GetAuthFactorUpdateStatus_FullMethodName      = "/auth.Auth/GetAuthFactorUpdateStatus"
	Auth_UpdateAFUCredentialStatus_FullMethodName      = "/auth.Auth/UpdateAFUCredentialStatus"
	Auth_ConfirmAuthFactorUpdate_FullMethodName        = "/auth.Auth/ConfirmAuthFactorUpdate"
	Auth_GetAuthFactorUpdateRecord_FullMethodName      = "/auth.Auth/GetAuthFactorUpdateRecord"
	Auth_AuthFactorsProfileUpdate_FullMethodName       = "/auth.Auth/AuthFactorsProfileUpdate"
	Auth_ReRegisterDevice_FullMethodName               = "/auth.Auth/ReRegisterDevice"
	Auth_SignOut_FullMethodName                        = "/auth.Auth/SignOut"
	Auth_VerifyDeviceIntegrity_FullMethodName          = "/auth.Auth/VerifyDeviceIntegrity"
	Auth_GetDeviceIntegrityNonce_FullMethodName        = "/auth.Auth/GetDeviceIntegrityNonce"
	Auth_GetFirstDeviceRegAfterTime_FullMethodName     = "/auth.Auth/GetFirstDeviceRegAfterTime"
	Auth_GenerateVendorOtp_FullMethodName              = "/auth.Auth/GenerateVendorOtp"
	Auth_ValidateAuthFactorUpdate_FullMethodName       = "/auth.Auth/ValidateAuthFactorUpdate"
	Auth_RecoverAFUProcessForActor_FullMethodName      = "/auth.Auth/RecoverAFUProcessForActor"
	Auth_GetAuthFactorUpdatesForActor_FullMethodName   = "/auth.Auth/GetAuthFactorUpdatesForActor"
	Auth_GetOtpsByPhoneNumberLimit_FullMethodName      = "/auth.Auth/GetOtpsByPhoneNumberLimit"
	Auth_RecordIosDeviceAttestation_FullMethodName     = "/auth.Auth/RecordIosDeviceAttestation"
	Auth_ReactivateDeviceForActor_FullMethodName       = "/auth.Auth/ReactivateDeviceForActor"
	Auth_StoreAuthAttempt_FullMethodName               = "/auth.Auth/StoreAuthAttempt"
	Auth_BlockExistingAuthFactorUpdates_FullMethodName = "/auth.Auth/BlockExistingAuthFactorUpdates"
	Auth_GetTroubleshootingDetails_FullMethodName      = "/auth.Auth/GetTroubleshootingDetails"
	Auth_GetTokenDetails_FullMethodName                = "/auth.Auth/GetTokenDetails"
	Auth_GetAFUSummaries_FullMethodName                = "/auth.Auth/GetAFUSummaries"
	Auth_RevokeOAuthToken_FullMethodName               = "/auth.Auth/RevokeOAuthToken"
	Auth_GetDeviceIntegrityStatus_FullMethodName       = "/auth.Auth/GetDeviceIntegrityStatus"
	Auth_DeactivateDevice_FullMethodName               = "/auth.Auth/DeactivateDevice"
	Auth_ProcessAFURiskVerdict_FullMethodName          = "/auth.Auth/ProcessAFURiskVerdict"
	Auth_VerifyPassword_FullMethodName                 = "/auth.Auth/VerifyPassword"
	Auth_ResetPassword_FullMethodName                  = "/auth.Auth/ResetPassword"
	Auth_CreateHandshakeToken_FullMethodName           = "/auth.Auth/CreateHandshakeToken"
	Auth_VerifyHandshakeToken_FullMethodName           = "/auth.Auth/VerifyHandshakeToken"
	Auth_SetDefaultPassword_FullMethodName             = "/auth.Auth/SetDefaultPassword"
	Auth_TriggerInitHandshakeComms_FullMethodName      = "/auth.Auth/TriggerInitHandshakeComms"
	Auth_RecordReRegisteredDevice_FullMethodName       = "/auth.Auth/RecordReRegisteredDevice"
	Auth_IsDeviceRegistrationAllowed_FullMethodName    = "/auth.Auth/IsDeviceRegistrationAllowed"
	Auth_GetDeviceRegSMSAckInfo_FullMethodName         = "/auth.Auth/GetDeviceRegSMSAckInfo"
	Auth_BackFillSimInfo_FullMethodName                = "/auth.Auth/BackFillSimInfo"
	Auth_GetAuthFactorCooldownInfo_FullMethodName      = "/auth.Auth/GetAuthFactorCooldownInfo"
)

// AuthClient is the client API for Auth service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AuthClient interface {
	// Generates 6-digit OTP & delivers to phone number specified in the request via SMS
	// Resend the existing OTP if the request is made with same Token.
	//   - Generated OTP is to be valid for a short amount of time - Ex: 5 minutes
	//   - If request is made prior to predefined time(Ex: 30 seconds), request the user/client to wait before second request.
	//   - To maintain exponential timer to prevent brute force attacks
	GenerateOtp(ctx context.Context, in *GenerateOtpRequest, opts ...grpc.CallOption) (*GenerateOtpResponse, error)
	// Verify the OTP against a GenerateOTPRequest instance
	// On three invalid attempts to verify Otp, Otp is expired and client is expected to generate a new Otp for the user
	VerifyOtp(ctx context.Context, in *VerifyOtpRequest, opts ...grpc.CallOption) (*VerifyOtpResponse, error)
	// Creates a authentication token for the user
	// Mechanism to generate the token is decided by the `TOKEN_TYPE` that is sent as part of the request
	// This is a helper function which doesn't perform any persistent storage operations
	// Business logic of handling/expiring the tokens is to be orchestrated elsewhere
	CreateToken(ctx context.Context, in *CreateTokenRequest, opts ...grpc.CallOption) (*CreateTokenResponse, error)
	// Validates the authentication token and returns a identifier for the actor
	// This is a helper function which doesn't perform any persistent storage operations
	ValidateToken(ctx context.Context, in *ValidateTokenRequest, opts ...grpc.CallOption) (*ValidateTokenResponse, error)
	// UpdateToken updates the token store entity status. It can be used to delete or revoke a token.
	UpdateToken(ctx context.Context, in *UpdateTokenRequest, opts ...grpc.CallOption) (*UpdateTokenResponse, error)
	// Deprecated: Do not use.
	// GetDeviceDetails fetches device details from token store for the given actor id.
	// Deprecated: use GetUserDeviceProperties instead
	GetDeviceDetails(ctx context.Context, in *GetDeviceDetailsRequest, opts ...grpc.CallOption) (*GetDeviceDetailsResponse, error)
	// Method to validate a OAuth Token submitted by the client
	// This method would validate the OAuth token with the OAuth provider
	// Implementation is also expected to implement a call back for the OAuth provider, if required & supported by OAuth provider, to push security notifications for the OAuth tokens
	ValidateOAuthToken(ctx context.Context, in *ValidateOAuthTokenRequest, opts ...grpc.CallOption) (*ValidateOAuthTokenResponse, error)
	// RPC method registers user device with vendor
	// This method will communicate through vendorgateway to the vendor and complete first factor authentication (what you have)
	// of user with vendor
	// Method generates unique identifier of a user which will be further used to identify user for all vendor communications
	// This will be synchronous api returning the response with the status of device registration at the vendor
	RegisterDevice(ctx context.Context, in *RegisterDeviceRequest, opts ...grpc.CallOption) (*RegisterDeviceResponse, error)
	// RPC provides encrypted payload for sms, and provides sms endpoint (phone number) at which sms needs to be sent
	// The SMS payload needs to be encrypted using a symmetric key algorithm as per the NPCI guidelines
	// The RPC serves for device registration during onboarding as well as UPI setup.
	// The sms endpoint selection needs to logically choose among multiple sms endpoints, predicting availability of endpoint etc.
	GetDeviceBindingSMS(ctx context.Context, in *GetDeviceBindingSMSRequest, opts ...grpc.CallOption) (*GetDeviceBindingSMSResponse, error)
	// GetDeviceAuth provides device authentication details such as:
	// deviceRegistrationStatus, vendorDeviceId i.e. deviceId registered with vendor,
	// deviceToken, userProfileId,  user device details, simID etc.
	// It takes in request parameters such as actorId/ device, vendor and,
	// returns the respective device authentication details.
	// This RPC returns device details of device used at the time of device registration.
	// If there is no record, neither for registered nor unregistered device,
	// it returns NOT_FOUND status.
	GetDeviceAuth(ctx context.Context, in *GetDeviceAuthRequest, opts ...grpc.CallOption) (*GetDeviceAuthResponse, error)
	// RegisterAuthFactorUpdate creates a new record for auth factor update.
	// It also returns the next credential that needs to be validated along with
	// reference id for further communication. This is the first step in
	// auth factor update or Re-OOBE flow.
	RegisterAuthFactorUpdate(ctx context.Context, in *RegisterAuthFactorUpdateRequest, opts ...grpc.CallOption) (*RegisterAuthFactorUpdateResponse, error)
	// GetAuthFactorUpdateStatus gets the current status of the auth factor update
	// and the next action required to make progress in auth factor update state machine.
	GetAuthFactorUpdateStatus(ctx context.Context, in *GetAuthFactorUpdateStatusRequest, opts ...grpc.CallOption) (*GetAuthFactorUpdateStatusResponse, error)
	// UpdateAFUCredentialStatus updates the AuthFactorUpdate Credential status.
	// The orchestrator calls this method to inform AFU that credential is verified.
	// E.g. of credential verification: Liveness, Face-match check, ATM PIN Validation...
	// This API updates the status in DB and returns next AFU Action to perform.
	UpdateAFUCredentialStatus(ctx context.Context, in *UpdateAFUCredentialStatusRequest, opts ...grpc.CallOption) (*UpdateAFUCredentialStatusResponse, error)
	// ConfirmAuthFactorUpdate is called by the client as a final confirmation when the user
	// selects to update some or all of the auth factors, i.e. Device, Email, Phone number,
	// and has passed all the auth checks. Once the users confirm to update the details,
	// they're logged out of any other existing devices and values updated.
	ConfirmAuthFactorUpdate(ctx context.Context, in *ConfirmAuthFactorUpdateRequest, opts ...grpc.CallOption) (*ConfirmAuthFactorUpdateResponse, error)
	// GetAuthFactorUpdateRecord is a getter API to fetch the AFU record from DB.
	GetAuthFactorUpdateRecord(ctx context.Context, in *GetAuthFactorUpdateRecordRequest, opts ...grpc.CallOption) (*GetAuthFactorUpdateRecordResponse, error)
	// AuthFactorsProfileUpdate API is called by the orchestrator to let
	// the AFU System know that new auth factors have been updated in user profile.
	AuthFactorsProfileUpdate(ctx context.Context, in *AuthFactorsProfileUpdateRequest, opts ...grpc.CallOption) (*AuthFactorsProfileUpdateResponse, error)
	// ReRegisterDevice updates the auth factors of a user with the vendor.
	// It also updates device registration entry of the user with the device
	// token received from vendor. This will be synchronous api returning the
	// response with the status of device registration at the vendor.
	// For new users, RegisterDevice API is to be called.
	ReRegisterDevice(ctx context.Context, in *ReRegisterDeviceRequest, opts ...grpc.CallOption) (*ReRegisterDeviceResponse, error)
	// Method to logout the user by invalidating a user's session token
	SignOut(ctx context.Context, in *SignOutRequest, opts ...grpc.CallOption) (*SignOutResponse, error)
	VerifyDeviceIntegrity(ctx context.Context, in *VerifyDeviceIntegrityRequest, opts ...grpc.CallOption) (*VerifyDeviceIntegrityResponse, error)
	GetDeviceIntegrityNonce(ctx context.Context, in *GetDeviceIntegrityNonceRequest, opts ...grpc.CallOption) (*GetDeviceIntegrityNonceResponse, error)
	// GetFirstDeviceRegAfterTime returns the first device registrations for a given actor Id after the given from time
	// NOTE - from time is mandatory to reduce the load on device_registration table
	GetFirstDeviceRegAfterTime(ctx context.Context, in *GetFirstDeviceRegAfterTimeRequest, opts ...grpc.CallOption) (*GetFirstDeviceRegAfterTimeResponse, error)
	// GenerateVendorOtp rpc is used for generating otp for the registered mobile number
	// at the vendor level, After receiving the request we will call the vendor gateway for sending
	// the otp to registered mobile number.
	GenerateVendorOtp(ctx context.Context, in *GenerateVendorOtpRequest, opts ...grpc.CallOption) (*GenerateVendorOtpResponse, error)
	// It checks if the actor has any currently running AFU process and what was the status of the last AFU request. Following cases are possible:
	//   - If a process is running and it is similar to the current request: In this case, it returns afuID and next_action for the currently running process.
	//   - If a process is running but it's different from the current request: an error is returned
	//   - If the last process which ran on the user is in STUCK state: an error is returned
	//   - If no process is active at the moment: return OK status.
	ValidateAuthFactorUpdate(ctx context.Context, in *ValidateAuthFactorUpdateRequest, opts ...grpc.CallOption) (*ValidateAuthFactorUpdateResponse, error)
	// RecoverAFUProcessForActor rpc is used to recover stuck AFU processes.
	// It takes actorId as input and tries to recover the latest AFU process for that actor.
	// It only processes the request if AFU record is in STUCK state.
	RecoverAFUProcessForActor(ctx context.Context, in *RecoverAFUProcessForActorRequest, opts ...grpc.CallOption) (*RecoverAFUProcessForActorResponse, error)
	// GetAuthFactorUpdatesForActor takes actorId `A` and record_count `n` as input. It returns details of the last `n` re-oobe performed by `A`.
	// It returns an NOT_FOUND status if no record is found.
	GetAuthFactorUpdatesForActor(ctx context.Context, in *GetAuthFactorUpdatesForActorRequest, opts ...grpc.CallOption) (*GetAuthFactorUpdatesForActorResponse, error)
	// GetOtpsByPhoneNumberLimit takes phone number and number of otps x, and returns the latest x otps for the phone number
	// limit cannot be greater than 10
	// It returns a NOT_FOUND status if no record is found for given phone number.
	GetOtpsByPhoneNumberLimit(ctx context.Context, in *GetOtpsByPhoneNumberLimitRequest, opts ...grpc.CallOption) (*GetOtpsByPhoneNumberLimitResponse, error)
	// RecordIosDeviceAttestation is used to verify the attestation object that was generated by Apple servers to attest the validity of the key.
	// It takes the attestation object to be verified, device info, key identifier and nonce used to generate the attestation as input.
	// If the attestation passed as input is valid, then OK status is returned.
	// If the attestation is not valid, then INVALID_ATTESTATION status is returned.
	// INTERNAL status is returned if some unexpected error is encountered while verifying the attestation like in case if DB is unavailable.
	RecordIosDeviceAttestation(ctx context.Context, in *RecordIosDeviceAttestationRequest, opts ...grpc.CallOption) (*RecordIosDeviceAttestationResponse, error)
	// ReactivateDeviceForActor accepts the actorId and reactivates the Device at vendor's end
	ReactivateDeviceForActor(ctx context.Context, in *ReactivateDeviceForActorRequest, opts ...grpc.CallOption) (*ReactivateDeviceForActorResponse, error)
	// StoreAuthAttempt is to store the auth attempt (addOAuthAccount) for a user
	// status, nextaction, actorId and phone number and some metadata is passed to store in the DB
	// only failure attempts are stored for now
	StoreAuthAttempt(ctx context.Context, in *StoreAuthAttemptRequest, opts ...grpc.CallOption) (*StoreAuthAttemptResponse, error)
	// BlockExistingAuthFactorUpdates is to check if we can proceed with onboarding with given set of auth factors.
	// This api checks if there are any afu attempts that are in progress with same auth factors
	// and blocks the attempts if vendor update is not started. if vendor update is started for any one of the attempts,
	// permission denied is returned where onboarding is blocked
	BlockExistingAuthFactorUpdates(ctx context.Context, in *BlockExistingAuthFactorUpdatesRequest, opts ...grpc.CallOption) (*BlockExistingAuthFactorUpdatesResponse, error)
	// GetTroubleshootingDetails takes in actorId as request and,
	// fetches the recent Auth Factor Update Attempts for the actor and,
	// returns the troubleshooting details for the AFU attempt.
	GetTroubleshootingDetails(ctx context.Context, in *GetTroubleshootingDetailsRequest, opts ...grpc.CallOption) (*GetTroubleshootingDetailsResponse, error)
	// GetTokenDetailsForPhoneNumber takes in one of identifier phone number or actor id,
	// and token types as part of request.
	// It returns an array of token details for the given identifier,
	// sorted on created_at field in descending order.
	GetTokenDetails(ctx context.Context, in *GetTokenDetailsRequest, opts ...grpc.CallOption) (*GetTokenDetailsResponse, error)
	// GetAFUSummaries is to get the summaries for all the AFUs done by a user in last X hours
	// This rpc takes actor_id, statuses, from time as the input parameters
	// It returns a list of AFUSummaries
	GetAFUSummaries(ctx context.Context, in *GetAFUSummariesRequest, opts ...grpc.CallOption) (*GetAFUSummariesResponse, error)
	// RevokeOAuthToken is used to revoke oauth token provided in the request.
	// Currently, only Apple oauth provider is supported.
	RevokeOAuthToken(ctx context.Context, in *RevokeOAuthTokenRequest, opts ...grpc.CallOption) (*RevokeOAuthTokenResponse, error)
	// GetDeviceIntegrityStatus gives the status of device integrity based on integrity id or device id
	GetDeviceIntegrityStatus(ctx context.Context, in *GetDeviceIntegrityStatusRequest, opts ...grpc.CallOption) (*GetDeviceIntegrityStatusResponse, error)
	// DeactivateDevice soft deletes registration record and deactivates device for the actor
	DeactivateDevice(ctx context.Context, in *DeactivateDeviceRequest, opts ...grpc.CallOption) (*DeactivateDeviceResponse, error)
	// ProcessAFURiskVerdict is an API to block/unblock re-onboarding journey of a user based on liveness/risk review
	ProcessAFURiskVerdict(ctx context.Context, in *ProcessAFURiskVerdictRequest, opts ...grpc.CallOption) (*ProcessAFURiskVerdictResponse, error)
	// VerifyPassword verifies the credentials of a user with an authentication service provider(currently keycloak)
	VerifyPassword(ctx context.Context, in *VerifyPasswordRequest, opts ...grpc.CallOption) (*VerifyPasswordResponse, error)
	// ResetPassword resets to a new password by verifying the old password
	ResetPassword(ctx context.Context, in *ResetPasswordRequest, opts ...grpc.CallOption) (*ResetPasswordResponse, error)
	CreateHandshakeToken(ctx context.Context, in *CreateHandshakeTokenRequest, opts ...grpc.CallOption) (*CreateHandshakeTokenResponse, error)
	VerifyHandshakeToken(ctx context.Context, in *VerifyHandshakeTokenRequest, opts ...grpc.CallOption) (*VerifyHandshakeTokenResponse, error)
	// SetDefaultPassword creates a new password for a user in keycloak
	// Currently used for Biometric KYC flow
	SetDefaultPassword(ctx context.Context, in *SetDefaultPasswordRequest, opts ...grpc.CallOption) (*SetDefaultPasswordResponse, error)
	// TriggerInitHandshakeComms rpc is called to trigger any comms needed to proceed with the Biometric process.
	TriggerInitHandshakeComms(ctx context.Context, in *TriggerInitHandshakeCommsRequest, opts ...grpc.CallOption) (*TriggerInitHandshakeCommsResponse, error)
	// RecordNewRegisteredDevice rpc is called to update the device registration record for a user
	// It doesn't make any vendor calls, but deletes the old device registration record and creates a new one
	RecordReRegisteredDevice(ctx context.Context, in *RecordReRegisteredDeviceRequest, opts ...grpc.CallOption) (*RecordReRegisteredDeviceResponse, error)
	IsDeviceRegistrationAllowed(ctx context.Context, in *IsDeviceRegistrationAllowedRequest, opts ...grpc.CallOption) (*IsDeviceRegistrationAllowedResponse, error)
	GetDeviceRegSMSAckInfo(ctx context.Context, in *GetDeviceRegSMSAckInfoRequest, opts ...grpc.CallOption) (*GetDeviceRegSMSAckInfoResponse, error)
	// BackFillSimInfo rpc is used to update sim id in device registration entity for already registered users.
	BackFillSimInfo(ctx context.Context, in *BackFillSimInfoRequest, opts ...grpc.CallOption) (*BackFillSimInfoResponse, error)
	// GetAuthFactorCooldownInfo returns cooldown information for auth factors for a given actor
	GetAuthFactorCooldownInfo(ctx context.Context, in *GetAuthFactorCooldownInfoRequest, opts ...grpc.CallOption) (*GetAuthFactorCooldownInfoResponse, error)
}

type authClient struct {
	cc grpc.ClientConnInterface
}

func NewAuthClient(cc grpc.ClientConnInterface) AuthClient {
	return &authClient{cc}
}

func (c *authClient) GenerateOtp(ctx context.Context, in *GenerateOtpRequest, opts ...grpc.CallOption) (*GenerateOtpResponse, error) {
	out := new(GenerateOtpResponse)
	err := c.cc.Invoke(ctx, Auth_GenerateOtp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) VerifyOtp(ctx context.Context, in *VerifyOtpRequest, opts ...grpc.CallOption) (*VerifyOtpResponse, error) {
	out := new(VerifyOtpResponse)
	err := c.cc.Invoke(ctx, Auth_VerifyOtp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) CreateToken(ctx context.Context, in *CreateTokenRequest, opts ...grpc.CallOption) (*CreateTokenResponse, error) {
	out := new(CreateTokenResponse)
	err := c.cc.Invoke(ctx, Auth_CreateToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) ValidateToken(ctx context.Context, in *ValidateTokenRequest, opts ...grpc.CallOption) (*ValidateTokenResponse, error) {
	out := new(ValidateTokenResponse)
	err := c.cc.Invoke(ctx, Auth_ValidateToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) UpdateToken(ctx context.Context, in *UpdateTokenRequest, opts ...grpc.CallOption) (*UpdateTokenResponse, error) {
	out := new(UpdateTokenResponse)
	err := c.cc.Invoke(ctx, Auth_UpdateToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *authClient) GetDeviceDetails(ctx context.Context, in *GetDeviceDetailsRequest, opts ...grpc.CallOption) (*GetDeviceDetailsResponse, error) {
	out := new(GetDeviceDetailsResponse)
	err := c.cc.Invoke(ctx, Auth_GetDeviceDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) ValidateOAuthToken(ctx context.Context, in *ValidateOAuthTokenRequest, opts ...grpc.CallOption) (*ValidateOAuthTokenResponse, error) {
	out := new(ValidateOAuthTokenResponse)
	err := c.cc.Invoke(ctx, Auth_ValidateOAuthToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) RegisterDevice(ctx context.Context, in *RegisterDeviceRequest, opts ...grpc.CallOption) (*RegisterDeviceResponse, error) {
	out := new(RegisterDeviceResponse)
	err := c.cc.Invoke(ctx, Auth_RegisterDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetDeviceBindingSMS(ctx context.Context, in *GetDeviceBindingSMSRequest, opts ...grpc.CallOption) (*GetDeviceBindingSMSResponse, error) {
	out := new(GetDeviceBindingSMSResponse)
	err := c.cc.Invoke(ctx, Auth_GetDeviceBindingSMS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetDeviceAuth(ctx context.Context, in *GetDeviceAuthRequest, opts ...grpc.CallOption) (*GetDeviceAuthResponse, error) {
	out := new(GetDeviceAuthResponse)
	err := c.cc.Invoke(ctx, Auth_GetDeviceAuth_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) RegisterAuthFactorUpdate(ctx context.Context, in *RegisterAuthFactorUpdateRequest, opts ...grpc.CallOption) (*RegisterAuthFactorUpdateResponse, error) {
	out := new(RegisterAuthFactorUpdateResponse)
	err := c.cc.Invoke(ctx, Auth_RegisterAuthFactorUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetAuthFactorUpdateStatus(ctx context.Context, in *GetAuthFactorUpdateStatusRequest, opts ...grpc.CallOption) (*GetAuthFactorUpdateStatusResponse, error) {
	out := new(GetAuthFactorUpdateStatusResponse)
	err := c.cc.Invoke(ctx, Auth_GetAuthFactorUpdateStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) UpdateAFUCredentialStatus(ctx context.Context, in *UpdateAFUCredentialStatusRequest, opts ...grpc.CallOption) (*UpdateAFUCredentialStatusResponse, error) {
	out := new(UpdateAFUCredentialStatusResponse)
	err := c.cc.Invoke(ctx, Auth_UpdateAFUCredentialStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) ConfirmAuthFactorUpdate(ctx context.Context, in *ConfirmAuthFactorUpdateRequest, opts ...grpc.CallOption) (*ConfirmAuthFactorUpdateResponse, error) {
	out := new(ConfirmAuthFactorUpdateResponse)
	err := c.cc.Invoke(ctx, Auth_ConfirmAuthFactorUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetAuthFactorUpdateRecord(ctx context.Context, in *GetAuthFactorUpdateRecordRequest, opts ...grpc.CallOption) (*GetAuthFactorUpdateRecordResponse, error) {
	out := new(GetAuthFactorUpdateRecordResponse)
	err := c.cc.Invoke(ctx, Auth_GetAuthFactorUpdateRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) AuthFactorsProfileUpdate(ctx context.Context, in *AuthFactorsProfileUpdateRequest, opts ...grpc.CallOption) (*AuthFactorsProfileUpdateResponse, error) {
	out := new(AuthFactorsProfileUpdateResponse)
	err := c.cc.Invoke(ctx, Auth_AuthFactorsProfileUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) ReRegisterDevice(ctx context.Context, in *ReRegisterDeviceRequest, opts ...grpc.CallOption) (*ReRegisterDeviceResponse, error) {
	out := new(ReRegisterDeviceResponse)
	err := c.cc.Invoke(ctx, Auth_ReRegisterDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) SignOut(ctx context.Context, in *SignOutRequest, opts ...grpc.CallOption) (*SignOutResponse, error) {
	out := new(SignOutResponse)
	err := c.cc.Invoke(ctx, Auth_SignOut_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) VerifyDeviceIntegrity(ctx context.Context, in *VerifyDeviceIntegrityRequest, opts ...grpc.CallOption) (*VerifyDeviceIntegrityResponse, error) {
	out := new(VerifyDeviceIntegrityResponse)
	err := c.cc.Invoke(ctx, Auth_VerifyDeviceIntegrity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetDeviceIntegrityNonce(ctx context.Context, in *GetDeviceIntegrityNonceRequest, opts ...grpc.CallOption) (*GetDeviceIntegrityNonceResponse, error) {
	out := new(GetDeviceIntegrityNonceResponse)
	err := c.cc.Invoke(ctx, Auth_GetDeviceIntegrityNonce_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetFirstDeviceRegAfterTime(ctx context.Context, in *GetFirstDeviceRegAfterTimeRequest, opts ...grpc.CallOption) (*GetFirstDeviceRegAfterTimeResponse, error) {
	out := new(GetFirstDeviceRegAfterTimeResponse)
	err := c.cc.Invoke(ctx, Auth_GetFirstDeviceRegAfterTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GenerateVendorOtp(ctx context.Context, in *GenerateVendorOtpRequest, opts ...grpc.CallOption) (*GenerateVendorOtpResponse, error) {
	out := new(GenerateVendorOtpResponse)
	err := c.cc.Invoke(ctx, Auth_GenerateVendorOtp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) ValidateAuthFactorUpdate(ctx context.Context, in *ValidateAuthFactorUpdateRequest, opts ...grpc.CallOption) (*ValidateAuthFactorUpdateResponse, error) {
	out := new(ValidateAuthFactorUpdateResponse)
	err := c.cc.Invoke(ctx, Auth_ValidateAuthFactorUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) RecoverAFUProcessForActor(ctx context.Context, in *RecoverAFUProcessForActorRequest, opts ...grpc.CallOption) (*RecoverAFUProcessForActorResponse, error) {
	out := new(RecoverAFUProcessForActorResponse)
	err := c.cc.Invoke(ctx, Auth_RecoverAFUProcessForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetAuthFactorUpdatesForActor(ctx context.Context, in *GetAuthFactorUpdatesForActorRequest, opts ...grpc.CallOption) (*GetAuthFactorUpdatesForActorResponse, error) {
	out := new(GetAuthFactorUpdatesForActorResponse)
	err := c.cc.Invoke(ctx, Auth_GetAuthFactorUpdatesForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetOtpsByPhoneNumberLimit(ctx context.Context, in *GetOtpsByPhoneNumberLimitRequest, opts ...grpc.CallOption) (*GetOtpsByPhoneNumberLimitResponse, error) {
	out := new(GetOtpsByPhoneNumberLimitResponse)
	err := c.cc.Invoke(ctx, Auth_GetOtpsByPhoneNumberLimit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) RecordIosDeviceAttestation(ctx context.Context, in *RecordIosDeviceAttestationRequest, opts ...grpc.CallOption) (*RecordIosDeviceAttestationResponse, error) {
	out := new(RecordIosDeviceAttestationResponse)
	err := c.cc.Invoke(ctx, Auth_RecordIosDeviceAttestation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) ReactivateDeviceForActor(ctx context.Context, in *ReactivateDeviceForActorRequest, opts ...grpc.CallOption) (*ReactivateDeviceForActorResponse, error) {
	out := new(ReactivateDeviceForActorResponse)
	err := c.cc.Invoke(ctx, Auth_ReactivateDeviceForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) StoreAuthAttempt(ctx context.Context, in *StoreAuthAttemptRequest, opts ...grpc.CallOption) (*StoreAuthAttemptResponse, error) {
	out := new(StoreAuthAttemptResponse)
	err := c.cc.Invoke(ctx, Auth_StoreAuthAttempt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) BlockExistingAuthFactorUpdates(ctx context.Context, in *BlockExistingAuthFactorUpdatesRequest, opts ...grpc.CallOption) (*BlockExistingAuthFactorUpdatesResponse, error) {
	out := new(BlockExistingAuthFactorUpdatesResponse)
	err := c.cc.Invoke(ctx, Auth_BlockExistingAuthFactorUpdates_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetTroubleshootingDetails(ctx context.Context, in *GetTroubleshootingDetailsRequest, opts ...grpc.CallOption) (*GetTroubleshootingDetailsResponse, error) {
	out := new(GetTroubleshootingDetailsResponse)
	err := c.cc.Invoke(ctx, Auth_GetTroubleshootingDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetTokenDetails(ctx context.Context, in *GetTokenDetailsRequest, opts ...grpc.CallOption) (*GetTokenDetailsResponse, error) {
	out := new(GetTokenDetailsResponse)
	err := c.cc.Invoke(ctx, Auth_GetTokenDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetAFUSummaries(ctx context.Context, in *GetAFUSummariesRequest, opts ...grpc.CallOption) (*GetAFUSummariesResponse, error) {
	out := new(GetAFUSummariesResponse)
	err := c.cc.Invoke(ctx, Auth_GetAFUSummaries_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) RevokeOAuthToken(ctx context.Context, in *RevokeOAuthTokenRequest, opts ...grpc.CallOption) (*RevokeOAuthTokenResponse, error) {
	out := new(RevokeOAuthTokenResponse)
	err := c.cc.Invoke(ctx, Auth_RevokeOAuthToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetDeviceIntegrityStatus(ctx context.Context, in *GetDeviceIntegrityStatusRequest, opts ...grpc.CallOption) (*GetDeviceIntegrityStatusResponse, error) {
	out := new(GetDeviceIntegrityStatusResponse)
	err := c.cc.Invoke(ctx, Auth_GetDeviceIntegrityStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) DeactivateDevice(ctx context.Context, in *DeactivateDeviceRequest, opts ...grpc.CallOption) (*DeactivateDeviceResponse, error) {
	out := new(DeactivateDeviceResponse)
	err := c.cc.Invoke(ctx, Auth_DeactivateDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) ProcessAFURiskVerdict(ctx context.Context, in *ProcessAFURiskVerdictRequest, opts ...grpc.CallOption) (*ProcessAFURiskVerdictResponse, error) {
	out := new(ProcessAFURiskVerdictResponse)
	err := c.cc.Invoke(ctx, Auth_ProcessAFURiskVerdict_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) VerifyPassword(ctx context.Context, in *VerifyPasswordRequest, opts ...grpc.CallOption) (*VerifyPasswordResponse, error) {
	out := new(VerifyPasswordResponse)
	err := c.cc.Invoke(ctx, Auth_VerifyPassword_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) ResetPassword(ctx context.Context, in *ResetPasswordRequest, opts ...grpc.CallOption) (*ResetPasswordResponse, error) {
	out := new(ResetPasswordResponse)
	err := c.cc.Invoke(ctx, Auth_ResetPassword_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) CreateHandshakeToken(ctx context.Context, in *CreateHandshakeTokenRequest, opts ...grpc.CallOption) (*CreateHandshakeTokenResponse, error) {
	out := new(CreateHandshakeTokenResponse)
	err := c.cc.Invoke(ctx, Auth_CreateHandshakeToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) VerifyHandshakeToken(ctx context.Context, in *VerifyHandshakeTokenRequest, opts ...grpc.CallOption) (*VerifyHandshakeTokenResponse, error) {
	out := new(VerifyHandshakeTokenResponse)
	err := c.cc.Invoke(ctx, Auth_VerifyHandshakeToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) SetDefaultPassword(ctx context.Context, in *SetDefaultPasswordRequest, opts ...grpc.CallOption) (*SetDefaultPasswordResponse, error) {
	out := new(SetDefaultPasswordResponse)
	err := c.cc.Invoke(ctx, Auth_SetDefaultPassword_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) TriggerInitHandshakeComms(ctx context.Context, in *TriggerInitHandshakeCommsRequest, opts ...grpc.CallOption) (*TriggerInitHandshakeCommsResponse, error) {
	out := new(TriggerInitHandshakeCommsResponse)
	err := c.cc.Invoke(ctx, Auth_TriggerInitHandshakeComms_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) RecordReRegisteredDevice(ctx context.Context, in *RecordReRegisteredDeviceRequest, opts ...grpc.CallOption) (*RecordReRegisteredDeviceResponse, error) {
	out := new(RecordReRegisteredDeviceResponse)
	err := c.cc.Invoke(ctx, Auth_RecordReRegisteredDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) IsDeviceRegistrationAllowed(ctx context.Context, in *IsDeviceRegistrationAllowedRequest, opts ...grpc.CallOption) (*IsDeviceRegistrationAllowedResponse, error) {
	out := new(IsDeviceRegistrationAllowedResponse)
	err := c.cc.Invoke(ctx, Auth_IsDeviceRegistrationAllowed_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetDeviceRegSMSAckInfo(ctx context.Context, in *GetDeviceRegSMSAckInfoRequest, opts ...grpc.CallOption) (*GetDeviceRegSMSAckInfoResponse, error) {
	out := new(GetDeviceRegSMSAckInfoResponse)
	err := c.cc.Invoke(ctx, Auth_GetDeviceRegSMSAckInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) BackFillSimInfo(ctx context.Context, in *BackFillSimInfoRequest, opts ...grpc.CallOption) (*BackFillSimInfoResponse, error) {
	out := new(BackFillSimInfoResponse)
	err := c.cc.Invoke(ctx, Auth_BackFillSimInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetAuthFactorCooldownInfo(ctx context.Context, in *GetAuthFactorCooldownInfoRequest, opts ...grpc.CallOption) (*GetAuthFactorCooldownInfoResponse, error) {
	out := new(GetAuthFactorCooldownInfoResponse)
	err := c.cc.Invoke(ctx, Auth_GetAuthFactorCooldownInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuthServer is the server API for Auth service.
// All implementations should embed UnimplementedAuthServer
// for forward compatibility
type AuthServer interface {
	// Generates 6-digit OTP & delivers to phone number specified in the request via SMS
	// Resend the existing OTP if the request is made with same Token.
	//   - Generated OTP is to be valid for a short amount of time - Ex: 5 minutes
	//   - If request is made prior to predefined time(Ex: 30 seconds), request the user/client to wait before second request.
	//   - To maintain exponential timer to prevent brute force attacks
	GenerateOtp(context.Context, *GenerateOtpRequest) (*GenerateOtpResponse, error)
	// Verify the OTP against a GenerateOTPRequest instance
	// On three invalid attempts to verify Otp, Otp is expired and client is expected to generate a new Otp for the user
	VerifyOtp(context.Context, *VerifyOtpRequest) (*VerifyOtpResponse, error)
	// Creates a authentication token for the user
	// Mechanism to generate the token is decided by the `TOKEN_TYPE` that is sent as part of the request
	// This is a helper function which doesn't perform any persistent storage operations
	// Business logic of handling/expiring the tokens is to be orchestrated elsewhere
	CreateToken(context.Context, *CreateTokenRequest) (*CreateTokenResponse, error)
	// Validates the authentication token and returns a identifier for the actor
	// This is a helper function which doesn't perform any persistent storage operations
	ValidateToken(context.Context, *ValidateTokenRequest) (*ValidateTokenResponse, error)
	// UpdateToken updates the token store entity status. It can be used to delete or revoke a token.
	UpdateToken(context.Context, *UpdateTokenRequest) (*UpdateTokenResponse, error)
	// Deprecated: Do not use.
	// GetDeviceDetails fetches device details from token store for the given actor id.
	// Deprecated: use GetUserDeviceProperties instead
	GetDeviceDetails(context.Context, *GetDeviceDetailsRequest) (*GetDeviceDetailsResponse, error)
	// Method to validate a OAuth Token submitted by the client
	// This method would validate the OAuth token with the OAuth provider
	// Implementation is also expected to implement a call back for the OAuth provider, if required & supported by OAuth provider, to push security notifications for the OAuth tokens
	ValidateOAuthToken(context.Context, *ValidateOAuthTokenRequest) (*ValidateOAuthTokenResponse, error)
	// RPC method registers user device with vendor
	// This method will communicate through vendorgateway to the vendor and complete first factor authentication (what you have)
	// of user with vendor
	// Method generates unique identifier of a user which will be further used to identify user for all vendor communications
	// This will be synchronous api returning the response with the status of device registration at the vendor
	RegisterDevice(context.Context, *RegisterDeviceRequest) (*RegisterDeviceResponse, error)
	// RPC provides encrypted payload for sms, and provides sms endpoint (phone number) at which sms needs to be sent
	// The SMS payload needs to be encrypted using a symmetric key algorithm as per the NPCI guidelines
	// The RPC serves for device registration during onboarding as well as UPI setup.
	// The sms endpoint selection needs to logically choose among multiple sms endpoints, predicting availability of endpoint etc.
	GetDeviceBindingSMS(context.Context, *GetDeviceBindingSMSRequest) (*GetDeviceBindingSMSResponse, error)
	// GetDeviceAuth provides device authentication details such as:
	// deviceRegistrationStatus, vendorDeviceId i.e. deviceId registered with vendor,
	// deviceToken, userProfileId,  user device details, simID etc.
	// It takes in request parameters such as actorId/ device, vendor and,
	// returns the respective device authentication details.
	// This RPC returns device details of device used at the time of device registration.
	// If there is no record, neither for registered nor unregistered device,
	// it returns NOT_FOUND status.
	GetDeviceAuth(context.Context, *GetDeviceAuthRequest) (*GetDeviceAuthResponse, error)
	// RegisterAuthFactorUpdate creates a new record for auth factor update.
	// It also returns the next credential that needs to be validated along with
	// reference id for further communication. This is the first step in
	// auth factor update or Re-OOBE flow.
	RegisterAuthFactorUpdate(context.Context, *RegisterAuthFactorUpdateRequest) (*RegisterAuthFactorUpdateResponse, error)
	// GetAuthFactorUpdateStatus gets the current status of the auth factor update
	// and the next action required to make progress in auth factor update state machine.
	GetAuthFactorUpdateStatus(context.Context, *GetAuthFactorUpdateStatusRequest) (*GetAuthFactorUpdateStatusResponse, error)
	// UpdateAFUCredentialStatus updates the AuthFactorUpdate Credential status.
	// The orchestrator calls this method to inform AFU that credential is verified.
	// E.g. of credential verification: Liveness, Face-match check, ATM PIN Validation...
	// This API updates the status in DB and returns next AFU Action to perform.
	UpdateAFUCredentialStatus(context.Context, *UpdateAFUCredentialStatusRequest) (*UpdateAFUCredentialStatusResponse, error)
	// ConfirmAuthFactorUpdate is called by the client as a final confirmation when the user
	// selects to update some or all of the auth factors, i.e. Device, Email, Phone number,
	// and has passed all the auth checks. Once the users confirm to update the details,
	// they're logged out of any other existing devices and values updated.
	ConfirmAuthFactorUpdate(context.Context, *ConfirmAuthFactorUpdateRequest) (*ConfirmAuthFactorUpdateResponse, error)
	// GetAuthFactorUpdateRecord is a getter API to fetch the AFU record from DB.
	GetAuthFactorUpdateRecord(context.Context, *GetAuthFactorUpdateRecordRequest) (*GetAuthFactorUpdateRecordResponse, error)
	// AuthFactorsProfileUpdate API is called by the orchestrator to let
	// the AFU System know that new auth factors have been updated in user profile.
	AuthFactorsProfileUpdate(context.Context, *AuthFactorsProfileUpdateRequest) (*AuthFactorsProfileUpdateResponse, error)
	// ReRegisterDevice updates the auth factors of a user with the vendor.
	// It also updates device registration entry of the user with the device
	// token received from vendor. This will be synchronous api returning the
	// response with the status of device registration at the vendor.
	// For new users, RegisterDevice API is to be called.
	ReRegisterDevice(context.Context, *ReRegisterDeviceRequest) (*ReRegisterDeviceResponse, error)
	// Method to logout the user by invalidating a user's session token
	SignOut(context.Context, *SignOutRequest) (*SignOutResponse, error)
	VerifyDeviceIntegrity(context.Context, *VerifyDeviceIntegrityRequest) (*VerifyDeviceIntegrityResponse, error)
	GetDeviceIntegrityNonce(context.Context, *GetDeviceIntegrityNonceRequest) (*GetDeviceIntegrityNonceResponse, error)
	// GetFirstDeviceRegAfterTime returns the first device registrations for a given actor Id after the given from time
	// NOTE - from time is mandatory to reduce the load on device_registration table
	GetFirstDeviceRegAfterTime(context.Context, *GetFirstDeviceRegAfterTimeRequest) (*GetFirstDeviceRegAfterTimeResponse, error)
	// GenerateVendorOtp rpc is used for generating otp for the registered mobile number
	// at the vendor level, After receiving the request we will call the vendor gateway for sending
	// the otp to registered mobile number.
	GenerateVendorOtp(context.Context, *GenerateVendorOtpRequest) (*GenerateVendorOtpResponse, error)
	// It checks if the actor has any currently running AFU process and what was the status of the last AFU request. Following cases are possible:
	//   - If a process is running and it is similar to the current request: In this case, it returns afuID and next_action for the currently running process.
	//   - If a process is running but it's different from the current request: an error is returned
	//   - If the last process which ran on the user is in STUCK state: an error is returned
	//   - If no process is active at the moment: return OK status.
	ValidateAuthFactorUpdate(context.Context, *ValidateAuthFactorUpdateRequest) (*ValidateAuthFactorUpdateResponse, error)
	// RecoverAFUProcessForActor rpc is used to recover stuck AFU processes.
	// It takes actorId as input and tries to recover the latest AFU process for that actor.
	// It only processes the request if AFU record is in STUCK state.
	RecoverAFUProcessForActor(context.Context, *RecoverAFUProcessForActorRequest) (*RecoverAFUProcessForActorResponse, error)
	// GetAuthFactorUpdatesForActor takes actorId `A` and record_count `n` as input. It returns details of the last `n` re-oobe performed by `A`.
	// It returns an NOT_FOUND status if no record is found.
	GetAuthFactorUpdatesForActor(context.Context, *GetAuthFactorUpdatesForActorRequest) (*GetAuthFactorUpdatesForActorResponse, error)
	// GetOtpsByPhoneNumberLimit takes phone number and number of otps x, and returns the latest x otps for the phone number
	// limit cannot be greater than 10
	// It returns a NOT_FOUND status if no record is found for given phone number.
	GetOtpsByPhoneNumberLimit(context.Context, *GetOtpsByPhoneNumberLimitRequest) (*GetOtpsByPhoneNumberLimitResponse, error)
	// RecordIosDeviceAttestation is used to verify the attestation object that was generated by Apple servers to attest the validity of the key.
	// It takes the attestation object to be verified, device info, key identifier and nonce used to generate the attestation as input.
	// If the attestation passed as input is valid, then OK status is returned.
	// If the attestation is not valid, then INVALID_ATTESTATION status is returned.
	// INTERNAL status is returned if some unexpected error is encountered while verifying the attestation like in case if DB is unavailable.
	RecordIosDeviceAttestation(context.Context, *RecordIosDeviceAttestationRequest) (*RecordIosDeviceAttestationResponse, error)
	// ReactivateDeviceForActor accepts the actorId and reactivates the Device at vendor's end
	ReactivateDeviceForActor(context.Context, *ReactivateDeviceForActorRequest) (*ReactivateDeviceForActorResponse, error)
	// StoreAuthAttempt is to store the auth attempt (addOAuthAccount) for a user
	// status, nextaction, actorId and phone number and some metadata is passed to store in the DB
	// only failure attempts are stored for now
	StoreAuthAttempt(context.Context, *StoreAuthAttemptRequest) (*StoreAuthAttemptResponse, error)
	// BlockExistingAuthFactorUpdates is to check if we can proceed with onboarding with given set of auth factors.
	// This api checks if there are any afu attempts that are in progress with same auth factors
	// and blocks the attempts if vendor update is not started. if vendor update is started for any one of the attempts,
	// permission denied is returned where onboarding is blocked
	BlockExistingAuthFactorUpdates(context.Context, *BlockExistingAuthFactorUpdatesRequest) (*BlockExistingAuthFactorUpdatesResponse, error)
	// GetTroubleshootingDetails takes in actorId as request and,
	// fetches the recent Auth Factor Update Attempts for the actor and,
	// returns the troubleshooting details for the AFU attempt.
	GetTroubleshootingDetails(context.Context, *GetTroubleshootingDetailsRequest) (*GetTroubleshootingDetailsResponse, error)
	// GetTokenDetailsForPhoneNumber takes in one of identifier phone number or actor id,
	// and token types as part of request.
	// It returns an array of token details for the given identifier,
	// sorted on created_at field in descending order.
	GetTokenDetails(context.Context, *GetTokenDetailsRequest) (*GetTokenDetailsResponse, error)
	// GetAFUSummaries is to get the summaries for all the AFUs done by a user in last X hours
	// This rpc takes actor_id, statuses, from time as the input parameters
	// It returns a list of AFUSummaries
	GetAFUSummaries(context.Context, *GetAFUSummariesRequest) (*GetAFUSummariesResponse, error)
	// RevokeOAuthToken is used to revoke oauth token provided in the request.
	// Currently, only Apple oauth provider is supported.
	RevokeOAuthToken(context.Context, *RevokeOAuthTokenRequest) (*RevokeOAuthTokenResponse, error)
	// GetDeviceIntegrityStatus gives the status of device integrity based on integrity id or device id
	GetDeviceIntegrityStatus(context.Context, *GetDeviceIntegrityStatusRequest) (*GetDeviceIntegrityStatusResponse, error)
	// DeactivateDevice soft deletes registration record and deactivates device for the actor
	DeactivateDevice(context.Context, *DeactivateDeviceRequest) (*DeactivateDeviceResponse, error)
	// ProcessAFURiskVerdict is an API to block/unblock re-onboarding journey of a user based on liveness/risk review
	ProcessAFURiskVerdict(context.Context, *ProcessAFURiskVerdictRequest) (*ProcessAFURiskVerdictResponse, error)
	// VerifyPassword verifies the credentials of a user with an authentication service provider(currently keycloak)
	VerifyPassword(context.Context, *VerifyPasswordRequest) (*VerifyPasswordResponse, error)
	// ResetPassword resets to a new password by verifying the old password
	ResetPassword(context.Context, *ResetPasswordRequest) (*ResetPasswordResponse, error)
	CreateHandshakeToken(context.Context, *CreateHandshakeTokenRequest) (*CreateHandshakeTokenResponse, error)
	VerifyHandshakeToken(context.Context, *VerifyHandshakeTokenRequest) (*VerifyHandshakeTokenResponse, error)
	// SetDefaultPassword creates a new password for a user in keycloak
	// Currently used for Biometric KYC flow
	SetDefaultPassword(context.Context, *SetDefaultPasswordRequest) (*SetDefaultPasswordResponse, error)
	// TriggerInitHandshakeComms rpc is called to trigger any comms needed to proceed with the Biometric process.
	TriggerInitHandshakeComms(context.Context, *TriggerInitHandshakeCommsRequest) (*TriggerInitHandshakeCommsResponse, error)
	// RecordNewRegisteredDevice rpc is called to update the device registration record for a user
	// It doesn't make any vendor calls, but deletes the old device registration record and creates a new one
	RecordReRegisteredDevice(context.Context, *RecordReRegisteredDeviceRequest) (*RecordReRegisteredDeviceResponse, error)
	IsDeviceRegistrationAllowed(context.Context, *IsDeviceRegistrationAllowedRequest) (*IsDeviceRegistrationAllowedResponse, error)
	GetDeviceRegSMSAckInfo(context.Context, *GetDeviceRegSMSAckInfoRequest) (*GetDeviceRegSMSAckInfoResponse, error)
	// BackFillSimInfo rpc is used to update sim id in device registration entity for already registered users.
	BackFillSimInfo(context.Context, *BackFillSimInfoRequest) (*BackFillSimInfoResponse, error)
	// GetAuthFactorCooldownInfo returns cooldown information for auth factors for a given actor
	GetAuthFactorCooldownInfo(context.Context, *GetAuthFactorCooldownInfoRequest) (*GetAuthFactorCooldownInfoResponse, error)
}

// UnimplementedAuthServer should be embedded to have forward compatible implementations.
type UnimplementedAuthServer struct {
}

func (UnimplementedAuthServer) GenerateOtp(context.Context, *GenerateOtpRequest) (*GenerateOtpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateOtp not implemented")
}
func (UnimplementedAuthServer) VerifyOtp(context.Context, *VerifyOtpRequest) (*VerifyOtpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyOtp not implemented")
}
func (UnimplementedAuthServer) CreateToken(context.Context, *CreateTokenRequest) (*CreateTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateToken not implemented")
}
func (UnimplementedAuthServer) ValidateToken(context.Context, *ValidateTokenRequest) (*ValidateTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateToken not implemented")
}
func (UnimplementedAuthServer) UpdateToken(context.Context, *UpdateTokenRequest) (*UpdateTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateToken not implemented")
}
func (UnimplementedAuthServer) GetDeviceDetails(context.Context, *GetDeviceDetailsRequest) (*GetDeviceDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeviceDetails not implemented")
}
func (UnimplementedAuthServer) ValidateOAuthToken(context.Context, *ValidateOAuthTokenRequest) (*ValidateOAuthTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateOAuthToken not implemented")
}
func (UnimplementedAuthServer) RegisterDevice(context.Context, *RegisterDeviceRequest) (*RegisterDeviceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterDevice not implemented")
}
func (UnimplementedAuthServer) GetDeviceBindingSMS(context.Context, *GetDeviceBindingSMSRequest) (*GetDeviceBindingSMSResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeviceBindingSMS not implemented")
}
func (UnimplementedAuthServer) GetDeviceAuth(context.Context, *GetDeviceAuthRequest) (*GetDeviceAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeviceAuth not implemented")
}
func (UnimplementedAuthServer) RegisterAuthFactorUpdate(context.Context, *RegisterAuthFactorUpdateRequest) (*RegisterAuthFactorUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterAuthFactorUpdate not implemented")
}
func (UnimplementedAuthServer) GetAuthFactorUpdateStatus(context.Context, *GetAuthFactorUpdateStatusRequest) (*GetAuthFactorUpdateStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthFactorUpdateStatus not implemented")
}
func (UnimplementedAuthServer) UpdateAFUCredentialStatus(context.Context, *UpdateAFUCredentialStatusRequest) (*UpdateAFUCredentialStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAFUCredentialStatus not implemented")
}
func (UnimplementedAuthServer) ConfirmAuthFactorUpdate(context.Context, *ConfirmAuthFactorUpdateRequest) (*ConfirmAuthFactorUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmAuthFactorUpdate not implemented")
}
func (UnimplementedAuthServer) GetAuthFactorUpdateRecord(context.Context, *GetAuthFactorUpdateRecordRequest) (*GetAuthFactorUpdateRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthFactorUpdateRecord not implemented")
}
func (UnimplementedAuthServer) AuthFactorsProfileUpdate(context.Context, *AuthFactorsProfileUpdateRequest) (*AuthFactorsProfileUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthFactorsProfileUpdate not implemented")
}
func (UnimplementedAuthServer) ReRegisterDevice(context.Context, *ReRegisterDeviceRequest) (*ReRegisterDeviceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReRegisterDevice not implemented")
}
func (UnimplementedAuthServer) SignOut(context.Context, *SignOutRequest) (*SignOutResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignOut not implemented")
}
func (UnimplementedAuthServer) VerifyDeviceIntegrity(context.Context, *VerifyDeviceIntegrityRequest) (*VerifyDeviceIntegrityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyDeviceIntegrity not implemented")
}
func (UnimplementedAuthServer) GetDeviceIntegrityNonce(context.Context, *GetDeviceIntegrityNonceRequest) (*GetDeviceIntegrityNonceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeviceIntegrityNonce not implemented")
}
func (UnimplementedAuthServer) GetFirstDeviceRegAfterTime(context.Context, *GetFirstDeviceRegAfterTimeRequest) (*GetFirstDeviceRegAfterTimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFirstDeviceRegAfterTime not implemented")
}
func (UnimplementedAuthServer) GenerateVendorOtp(context.Context, *GenerateVendorOtpRequest) (*GenerateVendorOtpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateVendorOtp not implemented")
}
func (UnimplementedAuthServer) ValidateAuthFactorUpdate(context.Context, *ValidateAuthFactorUpdateRequest) (*ValidateAuthFactorUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateAuthFactorUpdate not implemented")
}
func (UnimplementedAuthServer) RecoverAFUProcessForActor(context.Context, *RecoverAFUProcessForActorRequest) (*RecoverAFUProcessForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecoverAFUProcessForActor not implemented")
}
func (UnimplementedAuthServer) GetAuthFactorUpdatesForActor(context.Context, *GetAuthFactorUpdatesForActorRequest) (*GetAuthFactorUpdatesForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthFactorUpdatesForActor not implemented")
}
func (UnimplementedAuthServer) GetOtpsByPhoneNumberLimit(context.Context, *GetOtpsByPhoneNumberLimitRequest) (*GetOtpsByPhoneNumberLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOtpsByPhoneNumberLimit not implemented")
}
func (UnimplementedAuthServer) RecordIosDeviceAttestation(context.Context, *RecordIosDeviceAttestationRequest) (*RecordIosDeviceAttestationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordIosDeviceAttestation not implemented")
}
func (UnimplementedAuthServer) ReactivateDeviceForActor(context.Context, *ReactivateDeviceForActorRequest) (*ReactivateDeviceForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReactivateDeviceForActor not implemented")
}
func (UnimplementedAuthServer) StoreAuthAttempt(context.Context, *StoreAuthAttemptRequest) (*StoreAuthAttemptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreAuthAttempt not implemented")
}
func (UnimplementedAuthServer) BlockExistingAuthFactorUpdates(context.Context, *BlockExistingAuthFactorUpdatesRequest) (*BlockExistingAuthFactorUpdatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BlockExistingAuthFactorUpdates not implemented")
}
func (UnimplementedAuthServer) GetTroubleshootingDetails(context.Context, *GetTroubleshootingDetailsRequest) (*GetTroubleshootingDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTroubleshootingDetails not implemented")
}
func (UnimplementedAuthServer) GetTokenDetails(context.Context, *GetTokenDetailsRequest) (*GetTokenDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTokenDetails not implemented")
}
func (UnimplementedAuthServer) GetAFUSummaries(context.Context, *GetAFUSummariesRequest) (*GetAFUSummariesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAFUSummaries not implemented")
}
func (UnimplementedAuthServer) RevokeOAuthToken(context.Context, *RevokeOAuthTokenRequest) (*RevokeOAuthTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RevokeOAuthToken not implemented")
}
func (UnimplementedAuthServer) GetDeviceIntegrityStatus(context.Context, *GetDeviceIntegrityStatusRequest) (*GetDeviceIntegrityStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeviceIntegrityStatus not implemented")
}
func (UnimplementedAuthServer) DeactivateDevice(context.Context, *DeactivateDeviceRequest) (*DeactivateDeviceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeactivateDevice not implemented")
}
func (UnimplementedAuthServer) ProcessAFURiskVerdict(context.Context, *ProcessAFURiskVerdictRequest) (*ProcessAFURiskVerdictResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessAFURiskVerdict not implemented")
}
func (UnimplementedAuthServer) VerifyPassword(context.Context, *VerifyPasswordRequest) (*VerifyPasswordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyPassword not implemented")
}
func (UnimplementedAuthServer) ResetPassword(context.Context, *ResetPasswordRequest) (*ResetPasswordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetPassword not implemented")
}
func (UnimplementedAuthServer) CreateHandshakeToken(context.Context, *CreateHandshakeTokenRequest) (*CreateHandshakeTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateHandshakeToken not implemented")
}
func (UnimplementedAuthServer) VerifyHandshakeToken(context.Context, *VerifyHandshakeTokenRequest) (*VerifyHandshakeTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyHandshakeToken not implemented")
}
func (UnimplementedAuthServer) SetDefaultPassword(context.Context, *SetDefaultPasswordRequest) (*SetDefaultPasswordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetDefaultPassword not implemented")
}
func (UnimplementedAuthServer) TriggerInitHandshakeComms(context.Context, *TriggerInitHandshakeCommsRequest) (*TriggerInitHandshakeCommsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerInitHandshakeComms not implemented")
}
func (UnimplementedAuthServer) RecordReRegisteredDevice(context.Context, *RecordReRegisteredDeviceRequest) (*RecordReRegisteredDeviceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordReRegisteredDevice not implemented")
}
func (UnimplementedAuthServer) IsDeviceRegistrationAllowed(context.Context, *IsDeviceRegistrationAllowedRequest) (*IsDeviceRegistrationAllowedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsDeviceRegistrationAllowed not implemented")
}
func (UnimplementedAuthServer) GetDeviceRegSMSAckInfo(context.Context, *GetDeviceRegSMSAckInfoRequest) (*GetDeviceRegSMSAckInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeviceRegSMSAckInfo not implemented")
}
func (UnimplementedAuthServer) BackFillSimInfo(context.Context, *BackFillSimInfoRequest) (*BackFillSimInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BackFillSimInfo not implemented")
}
func (UnimplementedAuthServer) GetAuthFactorCooldownInfo(context.Context, *GetAuthFactorCooldownInfoRequest) (*GetAuthFactorCooldownInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthFactorCooldownInfo not implemented")
}

// UnsafeAuthServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuthServer will
// result in compilation errors.
type UnsafeAuthServer interface {
	mustEmbedUnimplementedAuthServer()
}

func RegisterAuthServer(s grpc.ServiceRegistrar, srv AuthServer) {
	s.RegisterService(&Auth_ServiceDesc, srv)
}

func _Auth_GenerateOtp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateOtpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GenerateOtp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GenerateOtp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GenerateOtp(ctx, req.(*GenerateOtpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_VerifyOtp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyOtpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).VerifyOtp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_VerifyOtp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).VerifyOtp(ctx, req.(*VerifyOtpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_CreateToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).CreateToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_CreateToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).CreateToken(ctx, req.(*CreateTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_ValidateToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).ValidateToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_ValidateToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).ValidateToken(ctx, req.(*ValidateTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_UpdateToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).UpdateToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_UpdateToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).UpdateToken(ctx, req.(*UpdateTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetDeviceDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetDeviceDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetDeviceDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetDeviceDetails(ctx, req.(*GetDeviceDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_ValidateOAuthToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateOAuthTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).ValidateOAuthToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_ValidateOAuthToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).ValidateOAuthToken(ctx, req.(*ValidateOAuthTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_RegisterDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).RegisterDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_RegisterDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).RegisterDevice(ctx, req.(*RegisterDeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetDeviceBindingSMS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceBindingSMSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetDeviceBindingSMS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetDeviceBindingSMS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetDeviceBindingSMS(ctx, req.(*GetDeviceBindingSMSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetDeviceAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetDeviceAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetDeviceAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetDeviceAuth(ctx, req.(*GetDeviceAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_RegisterAuthFactorUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterAuthFactorUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).RegisterAuthFactorUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_RegisterAuthFactorUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).RegisterAuthFactorUpdate(ctx, req.(*RegisterAuthFactorUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetAuthFactorUpdateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthFactorUpdateStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetAuthFactorUpdateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetAuthFactorUpdateStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetAuthFactorUpdateStatus(ctx, req.(*GetAuthFactorUpdateStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_UpdateAFUCredentialStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAFUCredentialStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).UpdateAFUCredentialStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_UpdateAFUCredentialStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).UpdateAFUCredentialStatus(ctx, req.(*UpdateAFUCredentialStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_ConfirmAuthFactorUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmAuthFactorUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).ConfirmAuthFactorUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_ConfirmAuthFactorUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).ConfirmAuthFactorUpdate(ctx, req.(*ConfirmAuthFactorUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetAuthFactorUpdateRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthFactorUpdateRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetAuthFactorUpdateRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetAuthFactorUpdateRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetAuthFactorUpdateRecord(ctx, req.(*GetAuthFactorUpdateRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_AuthFactorsProfileUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthFactorsProfileUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).AuthFactorsProfileUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_AuthFactorsProfileUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).AuthFactorsProfileUpdate(ctx, req.(*AuthFactorsProfileUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_ReRegisterDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReRegisterDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).ReRegisterDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_ReRegisterDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).ReRegisterDevice(ctx, req.(*ReRegisterDeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_SignOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignOutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).SignOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_SignOut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).SignOut(ctx, req.(*SignOutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_VerifyDeviceIntegrity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyDeviceIntegrityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).VerifyDeviceIntegrity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_VerifyDeviceIntegrity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).VerifyDeviceIntegrity(ctx, req.(*VerifyDeviceIntegrityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetDeviceIntegrityNonce_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceIntegrityNonceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetDeviceIntegrityNonce(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetDeviceIntegrityNonce_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetDeviceIntegrityNonce(ctx, req.(*GetDeviceIntegrityNonceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetFirstDeviceRegAfterTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFirstDeviceRegAfterTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetFirstDeviceRegAfterTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetFirstDeviceRegAfterTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetFirstDeviceRegAfterTime(ctx, req.(*GetFirstDeviceRegAfterTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GenerateVendorOtp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateVendorOtpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GenerateVendorOtp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GenerateVendorOtp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GenerateVendorOtp(ctx, req.(*GenerateVendorOtpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_ValidateAuthFactorUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateAuthFactorUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).ValidateAuthFactorUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_ValidateAuthFactorUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).ValidateAuthFactorUpdate(ctx, req.(*ValidateAuthFactorUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_RecoverAFUProcessForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecoverAFUProcessForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).RecoverAFUProcessForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_RecoverAFUProcessForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).RecoverAFUProcessForActor(ctx, req.(*RecoverAFUProcessForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetAuthFactorUpdatesForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthFactorUpdatesForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetAuthFactorUpdatesForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetAuthFactorUpdatesForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetAuthFactorUpdatesForActor(ctx, req.(*GetAuthFactorUpdatesForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetOtpsByPhoneNumberLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOtpsByPhoneNumberLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetOtpsByPhoneNumberLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetOtpsByPhoneNumberLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetOtpsByPhoneNumberLimit(ctx, req.(*GetOtpsByPhoneNumberLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_RecordIosDeviceAttestation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordIosDeviceAttestationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).RecordIosDeviceAttestation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_RecordIosDeviceAttestation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).RecordIosDeviceAttestation(ctx, req.(*RecordIosDeviceAttestationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_ReactivateDeviceForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReactivateDeviceForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).ReactivateDeviceForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_ReactivateDeviceForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).ReactivateDeviceForActor(ctx, req.(*ReactivateDeviceForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_StoreAuthAttempt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreAuthAttemptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).StoreAuthAttempt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_StoreAuthAttempt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).StoreAuthAttempt(ctx, req.(*StoreAuthAttemptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_BlockExistingAuthFactorUpdates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockExistingAuthFactorUpdatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).BlockExistingAuthFactorUpdates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_BlockExistingAuthFactorUpdates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).BlockExistingAuthFactorUpdates(ctx, req.(*BlockExistingAuthFactorUpdatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetTroubleshootingDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTroubleshootingDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetTroubleshootingDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetTroubleshootingDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetTroubleshootingDetails(ctx, req.(*GetTroubleshootingDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetTokenDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTokenDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetTokenDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetTokenDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetTokenDetails(ctx, req.(*GetTokenDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetAFUSummaries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAFUSummariesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetAFUSummaries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetAFUSummaries_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetAFUSummaries(ctx, req.(*GetAFUSummariesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_RevokeOAuthToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeOAuthTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).RevokeOAuthToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_RevokeOAuthToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).RevokeOAuthToken(ctx, req.(*RevokeOAuthTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetDeviceIntegrityStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceIntegrityStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetDeviceIntegrityStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetDeviceIntegrityStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetDeviceIntegrityStatus(ctx, req.(*GetDeviceIntegrityStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_DeactivateDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeactivateDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).DeactivateDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_DeactivateDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).DeactivateDevice(ctx, req.(*DeactivateDeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_ProcessAFURiskVerdict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessAFURiskVerdictRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).ProcessAFURiskVerdict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_ProcessAFURiskVerdict_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).ProcessAFURiskVerdict(ctx, req.(*ProcessAFURiskVerdictRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_VerifyPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyPasswordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).VerifyPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_VerifyPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).VerifyPassword(ctx, req.(*VerifyPasswordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_ResetPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetPasswordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).ResetPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_ResetPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).ResetPassword(ctx, req.(*ResetPasswordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_CreateHandshakeToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateHandshakeTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).CreateHandshakeToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_CreateHandshakeToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).CreateHandshakeToken(ctx, req.(*CreateHandshakeTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_VerifyHandshakeToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyHandshakeTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).VerifyHandshakeToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_VerifyHandshakeToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).VerifyHandshakeToken(ctx, req.(*VerifyHandshakeTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_SetDefaultPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDefaultPasswordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).SetDefaultPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_SetDefaultPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).SetDefaultPassword(ctx, req.(*SetDefaultPasswordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_TriggerInitHandshakeComms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerInitHandshakeCommsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).TriggerInitHandshakeComms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_TriggerInitHandshakeComms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).TriggerInitHandshakeComms(ctx, req.(*TriggerInitHandshakeCommsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_RecordReRegisteredDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordReRegisteredDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).RecordReRegisteredDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_RecordReRegisteredDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).RecordReRegisteredDevice(ctx, req.(*RecordReRegisteredDeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_IsDeviceRegistrationAllowed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsDeviceRegistrationAllowedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).IsDeviceRegistrationAllowed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_IsDeviceRegistrationAllowed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).IsDeviceRegistrationAllowed(ctx, req.(*IsDeviceRegistrationAllowedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetDeviceRegSMSAckInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceRegSMSAckInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetDeviceRegSMSAckInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetDeviceRegSMSAckInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetDeviceRegSMSAckInfo(ctx, req.(*GetDeviceRegSMSAckInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_BackFillSimInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BackFillSimInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).BackFillSimInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_BackFillSimInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).BackFillSimInfo(ctx, req.(*BackFillSimInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetAuthFactorCooldownInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthFactorCooldownInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetAuthFactorCooldownInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetAuthFactorCooldownInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetAuthFactorCooldownInfo(ctx, req.(*GetAuthFactorCooldownInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Auth_ServiceDesc is the grpc.ServiceDesc for Auth service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Auth_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "auth.Auth",
	HandlerType: (*AuthServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateOtp",
			Handler:    _Auth_GenerateOtp_Handler,
		},
		{
			MethodName: "VerifyOtp",
			Handler:    _Auth_VerifyOtp_Handler,
		},
		{
			MethodName: "CreateToken",
			Handler:    _Auth_CreateToken_Handler,
		},
		{
			MethodName: "ValidateToken",
			Handler:    _Auth_ValidateToken_Handler,
		},
		{
			MethodName: "UpdateToken",
			Handler:    _Auth_UpdateToken_Handler,
		},
		{
			MethodName: "GetDeviceDetails",
			Handler:    _Auth_GetDeviceDetails_Handler,
		},
		{
			MethodName: "ValidateOAuthToken",
			Handler:    _Auth_ValidateOAuthToken_Handler,
		},
		{
			MethodName: "RegisterDevice",
			Handler:    _Auth_RegisterDevice_Handler,
		},
		{
			MethodName: "GetDeviceBindingSMS",
			Handler:    _Auth_GetDeviceBindingSMS_Handler,
		},
		{
			MethodName: "GetDeviceAuth",
			Handler:    _Auth_GetDeviceAuth_Handler,
		},
		{
			MethodName: "RegisterAuthFactorUpdate",
			Handler:    _Auth_RegisterAuthFactorUpdate_Handler,
		},
		{
			MethodName: "GetAuthFactorUpdateStatus",
			Handler:    _Auth_GetAuthFactorUpdateStatus_Handler,
		},
		{
			MethodName: "UpdateAFUCredentialStatus",
			Handler:    _Auth_UpdateAFUCredentialStatus_Handler,
		},
		{
			MethodName: "ConfirmAuthFactorUpdate",
			Handler:    _Auth_ConfirmAuthFactorUpdate_Handler,
		},
		{
			MethodName: "GetAuthFactorUpdateRecord",
			Handler:    _Auth_GetAuthFactorUpdateRecord_Handler,
		},
		{
			MethodName: "AuthFactorsProfileUpdate",
			Handler:    _Auth_AuthFactorsProfileUpdate_Handler,
		},
		{
			MethodName: "ReRegisterDevice",
			Handler:    _Auth_ReRegisterDevice_Handler,
		},
		{
			MethodName: "SignOut",
			Handler:    _Auth_SignOut_Handler,
		},
		{
			MethodName: "VerifyDeviceIntegrity",
			Handler:    _Auth_VerifyDeviceIntegrity_Handler,
		},
		{
			MethodName: "GetDeviceIntegrityNonce",
			Handler:    _Auth_GetDeviceIntegrityNonce_Handler,
		},
		{
			MethodName: "GetFirstDeviceRegAfterTime",
			Handler:    _Auth_GetFirstDeviceRegAfterTime_Handler,
		},
		{
			MethodName: "GenerateVendorOtp",
			Handler:    _Auth_GenerateVendorOtp_Handler,
		},
		{
			MethodName: "ValidateAuthFactorUpdate",
			Handler:    _Auth_ValidateAuthFactorUpdate_Handler,
		},
		{
			MethodName: "RecoverAFUProcessForActor",
			Handler:    _Auth_RecoverAFUProcessForActor_Handler,
		},
		{
			MethodName: "GetAuthFactorUpdatesForActor",
			Handler:    _Auth_GetAuthFactorUpdatesForActor_Handler,
		},
		{
			MethodName: "GetOtpsByPhoneNumberLimit",
			Handler:    _Auth_GetOtpsByPhoneNumberLimit_Handler,
		},
		{
			MethodName: "RecordIosDeviceAttestation",
			Handler:    _Auth_RecordIosDeviceAttestation_Handler,
		},
		{
			MethodName: "ReactivateDeviceForActor",
			Handler:    _Auth_ReactivateDeviceForActor_Handler,
		},
		{
			MethodName: "StoreAuthAttempt",
			Handler:    _Auth_StoreAuthAttempt_Handler,
		},
		{
			MethodName: "BlockExistingAuthFactorUpdates",
			Handler:    _Auth_BlockExistingAuthFactorUpdates_Handler,
		},
		{
			MethodName: "GetTroubleshootingDetails",
			Handler:    _Auth_GetTroubleshootingDetails_Handler,
		},
		{
			MethodName: "GetTokenDetails",
			Handler:    _Auth_GetTokenDetails_Handler,
		},
		{
			MethodName: "GetAFUSummaries",
			Handler:    _Auth_GetAFUSummaries_Handler,
		},
		{
			MethodName: "RevokeOAuthToken",
			Handler:    _Auth_RevokeOAuthToken_Handler,
		},
		{
			MethodName: "GetDeviceIntegrityStatus",
			Handler:    _Auth_GetDeviceIntegrityStatus_Handler,
		},
		{
			MethodName: "DeactivateDevice",
			Handler:    _Auth_DeactivateDevice_Handler,
		},
		{
			MethodName: "ProcessAFURiskVerdict",
			Handler:    _Auth_ProcessAFURiskVerdict_Handler,
		},
		{
			MethodName: "VerifyPassword",
			Handler:    _Auth_VerifyPassword_Handler,
		},
		{
			MethodName: "ResetPassword",
			Handler:    _Auth_ResetPassword_Handler,
		},
		{
			MethodName: "CreateHandshakeToken",
			Handler:    _Auth_CreateHandshakeToken_Handler,
		},
		{
			MethodName: "VerifyHandshakeToken",
			Handler:    _Auth_VerifyHandshakeToken_Handler,
		},
		{
			MethodName: "SetDefaultPassword",
			Handler:    _Auth_SetDefaultPassword_Handler,
		},
		{
			MethodName: "TriggerInitHandshakeComms",
			Handler:    _Auth_TriggerInitHandshakeComms_Handler,
		},
		{
			MethodName: "RecordReRegisteredDevice",
			Handler:    _Auth_RecordReRegisteredDevice_Handler,
		},
		{
			MethodName: "IsDeviceRegistrationAllowed",
			Handler:    _Auth_IsDeviceRegistrationAllowed_Handler,
		},
		{
			MethodName: "GetDeviceRegSMSAckInfo",
			Handler:    _Auth_GetDeviceRegSMSAckInfo_Handler,
		},
		{
			MethodName: "BackFillSimInfo",
			Handler:    _Auth_BackFillSimInfo_Handler,
		},
		{
			MethodName: "GetAuthFactorCooldownInfo",
			Handler:    _Auth_GetAuthFactorCooldownInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/auth/service.proto",
}
