//go:generate gen_sql -types=AuthRequestStatus,FlowName,Provenance

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/auth_request.proto

package auth

import (
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthRequestFieldMask int32

const (
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_UNSPECIFIED       AuthRequestFieldMask = 0
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_ID                AuthRequestFieldMask = 1
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_ACTOR_ID          AuthRequestFieldMask = 2
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_CLIENT_REQUEST_ID AuthRequestFieldMask = 3
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_ORCHESTRATION_ID  AuthRequestFieldMask = 4
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_NEXT_ACTION       AuthRequestFieldMask = 5
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_STATUS            AuthRequestFieldMask = 6
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_PROVENANCE        AuthRequestFieldMask = 7
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_CREATED_AT        AuthRequestFieldMask = 8
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_UPDATED_AT        AuthRequestFieldMask = 9
	AuthRequestFieldMask_AUTH_REQUEST_FIELD_MASK_DELETED_AT        AuthRequestFieldMask = 10
)

// Enum value maps for AuthRequestFieldMask.
var (
	AuthRequestFieldMask_name = map[int32]string{
		0:  "AUTH_REQUEST_FIELD_MASK_UNSPECIFIED",
		1:  "AUTH_REQUEST_FIELD_MASK_ID",
		2:  "AUTH_REQUEST_FIELD_MASK_ACTOR_ID",
		3:  "AUTH_REQUEST_FIELD_MASK_CLIENT_REQUEST_ID",
		4:  "AUTH_REQUEST_FIELD_MASK_ORCHESTRATION_ID",
		5:  "AUTH_REQUEST_FIELD_MASK_NEXT_ACTION",
		6:  "AUTH_REQUEST_FIELD_MASK_STATUS",
		7:  "AUTH_REQUEST_FIELD_MASK_PROVENANCE",
		8:  "AUTH_REQUEST_FIELD_MASK_CREATED_AT",
		9:  "AUTH_REQUEST_FIELD_MASK_UPDATED_AT",
		10: "AUTH_REQUEST_FIELD_MASK_DELETED_AT",
	}
	AuthRequestFieldMask_value = map[string]int32{
		"AUTH_REQUEST_FIELD_MASK_UNSPECIFIED":       0,
		"AUTH_REQUEST_FIELD_MASK_ID":                1,
		"AUTH_REQUEST_FIELD_MASK_ACTOR_ID":          2,
		"AUTH_REQUEST_FIELD_MASK_CLIENT_REQUEST_ID": 3,
		"AUTH_REQUEST_FIELD_MASK_ORCHESTRATION_ID":  4,
		"AUTH_REQUEST_FIELD_MASK_NEXT_ACTION":       5,
		"AUTH_REQUEST_FIELD_MASK_STATUS":            6,
		"AUTH_REQUEST_FIELD_MASK_PROVENANCE":        7,
		"AUTH_REQUEST_FIELD_MASK_CREATED_AT":        8,
		"AUTH_REQUEST_FIELD_MASK_UPDATED_AT":        9,
		"AUTH_REQUEST_FIELD_MASK_DELETED_AT":        10,
	}
)

func (x AuthRequestFieldMask) Enum() *AuthRequestFieldMask {
	p := new(AuthRequestFieldMask)
	*p = x
	return p
}

func (x AuthRequestFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthRequestFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_auth_request_proto_enumTypes[0].Descriptor()
}

func (AuthRequestFieldMask) Type() protoreflect.EnumType {
	return &file_api_auth_auth_request_proto_enumTypes[0]
}

func (x AuthRequestFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthRequestFieldMask.Descriptor instead.
func (AuthRequestFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_auth_request_proto_rawDescGZIP(), []int{0}
}

type AuthRequestStatus int32

const (
	AuthRequestStatus_AUTH_REQUEST_STATUS_UNSPECIFIED AuthRequestStatus = 0
	AuthRequestStatus_AUTH_REQUEST_STATUS_PENDING     AuthRequestStatus = 1
	AuthRequestStatus_AUTH_REQUEST_STATUS_IN_PROGRESS AuthRequestStatus = 2
	AuthRequestStatus_AUTH_REQUEST_STATUS_SUCCESS     AuthRequestStatus = 3
	AuthRequestStatus_AUTH_REQUEST_STATUS_FAIL        AuthRequestStatus = 4
)

// Enum value maps for AuthRequestStatus.
var (
	AuthRequestStatus_name = map[int32]string{
		0: "AUTH_REQUEST_STATUS_UNSPECIFIED",
		1: "AUTH_REQUEST_STATUS_PENDING",
		2: "AUTH_REQUEST_STATUS_IN_PROGRESS",
		3: "AUTH_REQUEST_STATUS_SUCCESS",
		4: "AUTH_REQUEST_STATUS_FAIL",
	}
	AuthRequestStatus_value = map[string]int32{
		"AUTH_REQUEST_STATUS_UNSPECIFIED": 0,
		"AUTH_REQUEST_STATUS_PENDING":     1,
		"AUTH_REQUEST_STATUS_IN_PROGRESS": 2,
		"AUTH_REQUEST_STATUS_SUCCESS":     3,
		"AUTH_REQUEST_STATUS_FAIL":        4,
	}
)

func (x AuthRequestStatus) Enum() *AuthRequestStatus {
	p := new(AuthRequestStatus)
	*p = x
	return p
}

func (x AuthRequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthRequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_auth_request_proto_enumTypes[1].Descriptor()
}

func (AuthRequestStatus) Type() protoreflect.EnumType {
	return &file_api_auth_auth_request_proto_enumTypes[1]
}

func (x AuthRequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthRequestStatus.Descriptor instead.
func (AuthRequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_auth_request_proto_rawDescGZIP(), []int{1}
}

type FlowName int32

const (
	FlowName_AUTH_REQUEST_FLOW_UNSPECIFIED              FlowName = 0
	FlowName_AUTH_REQUEST_FLOW_LIVENESS                 FlowName = 1
	FlowName_AUTH_REQUEST_FLOW_LIVENESS_PLUS_FACE_MATCH FlowName = 2
)

// Enum value maps for FlowName.
var (
	FlowName_name = map[int32]string{
		0: "AUTH_REQUEST_FLOW_UNSPECIFIED",
		1: "AUTH_REQUEST_FLOW_LIVENESS",
		2: "AUTH_REQUEST_FLOW_LIVENESS_PLUS_FACE_MATCH",
	}
	FlowName_value = map[string]int32{
		"AUTH_REQUEST_FLOW_UNSPECIFIED":              0,
		"AUTH_REQUEST_FLOW_LIVENESS":                 1,
		"AUTH_REQUEST_FLOW_LIVENESS_PLUS_FACE_MATCH": 2,
	}
)

func (x FlowName) Enum() *FlowName {
	p := new(FlowName)
	*p = x
	return p
}

func (x FlowName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FlowName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_auth_request_proto_enumTypes[2].Descriptor()
}

func (FlowName) Type() protoreflect.EnumType {
	return &file_api_auth_auth_request_proto_enumTypes[2]
}

func (x FlowName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FlowName.Descriptor instead.
func (FlowName) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_auth_request_proto_rawDescGZIP(), []int{2}
}

type Provenance int32

const (
	Provenance_PROVENANCE_UNSPECIFIED Provenance = 0
	Provenance_PROVENANCE_CREDIT_CARD Provenance = 1
)

// Enum value maps for Provenance.
var (
	Provenance_name = map[int32]string{
		0: "PROVENANCE_UNSPECIFIED",
		1: "PROVENANCE_CREDIT_CARD",
	}
	Provenance_value = map[string]int32{
		"PROVENANCE_UNSPECIFIED": 0,
		"PROVENANCE_CREDIT_CARD": 1,
	}
)

func (x Provenance) Enum() *Provenance {
	p := new(Provenance)
	*p = x
	return p
}

func (x Provenance) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Provenance) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_auth_request_proto_enumTypes[3].Descriptor()
}

func (Provenance) Type() protoreflect.EnumType {
	return &file_api_auth_auth_request_proto_enumTypes[3]
}

func (x Provenance) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Provenance.Descriptor instead.
func (Provenance) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_auth_request_proto_rawDescGZIP(), []int{3}
}

type AuthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// actor id of the user
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// client request id
	ClientRequestId string `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// auth flow id
	FlowName FlowName `protobuf:"varint,4,opt,name=flow_name,json=flowName,proto3,enum=auth.FlowName" json:"flow_name,omitempty"`
	// orchestration id of workflow
	OrchId string `protobuf:"bytes,5,opt,name=orch_id,json=orchId,proto3" json:"orch_id,omitempty"`
	// next action to the user
	NextAction *deeplink.Deeplink `protobuf:"bytes,6,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	// status of request
	Status AuthRequestStatus `protobuf:"varint,7,opt,name=status,proto3,enum=auth.AuthRequestStatus" json:"status,omitempty"`
	// provenance(calling service)
	Provenance Provenance             `protobuf:"varint,8,opt,name=provenance,proto3,enum=auth.Provenance" json:"provenance,omitempty"`
	CreatedAt  *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt  *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt  *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *AuthRequest) Reset() {
	*x = AuthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_auth_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthRequest) ProtoMessage() {}

func (x *AuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_auth_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthRequest.ProtoReflect.Descriptor instead.
func (*AuthRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_auth_request_proto_rawDescGZIP(), []int{0}
}

func (x *AuthRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AuthRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *AuthRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *AuthRequest) GetFlowName() FlowName {
	if x != nil {
		return x.FlowName
	}
	return FlowName_AUTH_REQUEST_FLOW_UNSPECIFIED
}

func (x *AuthRequest) GetOrchId() string {
	if x != nil {
		return x.OrchId
	}
	return ""
}

func (x *AuthRequest) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *AuthRequest) GetStatus() AuthRequestStatus {
	if x != nil {
		return x.Status
	}
	return AuthRequestStatus_AUTH_REQUEST_STATUS_UNSPECIFIED
}

func (x *AuthRequest) GetProvenance() Provenance {
	if x != nil {
		return x.Provenance
	}
	return Provenance_PROVENANCE_UNSPECIFIED
}

func (x *AuthRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AuthRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *AuthRequest) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_auth_auth_request_proto protoreflect.FileDescriptor

var file_api_auth_auth_request_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x61,
	0x75, 0x74, 0x68, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfc, 0x03, 0x0a, 0x0b, 0x41,
	0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x2b, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x46, 0x6c, 0x6f, 0x77,
	0x4e, 0x61, 0x6d, 0x65, 0x52, 0x08, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x6f, 0x72, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x72, 0x63, 0x68, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x41, 0x75, 0x74,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0xcf, 0x03, 0x0a, 0x14, 0x41, 0x75,
	0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10,
	0x02, 0x12, 0x2d, 0x0a, 0x29, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4c, 0x49,
	0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x03,
	0x12, 0x2c, 0x0a, 0x28, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x52, 0x43, 0x48,
	0x45, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x27,
	0x0a, 0x23, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4e, 0x45, 0x58, 0x54, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x06, 0x12, 0x26, 0x0a, 0x22, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43,
	0x45, 0x10, 0x07, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x08, 0x12, 0x26, 0x0a, 0x22, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41,
	0x54, 0x10, 0x09, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44,
	0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0a, 0x2a, 0xbd, 0x01, 0x0a, 0x11,
	0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49,
	0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b,
	0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x1c, 0x0a,
	0x18, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x04, 0x2a, 0x7d, 0x0a, 0x08, 0x46,
	0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x2e, 0x0a, 0x2a, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x4c, 0x55, 0x53, 0x5f, 0x46, 0x41,
	0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x02, 0x2a, 0x44, 0x0a, 0x0a, 0x50, 0x72,
	0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f, 0x56,
	0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x01,
	0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x75, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_auth_request_proto_rawDescOnce sync.Once
	file_api_auth_auth_request_proto_rawDescData = file_api_auth_auth_request_proto_rawDesc
)

func file_api_auth_auth_request_proto_rawDescGZIP() []byte {
	file_api_auth_auth_request_proto_rawDescOnce.Do(func() {
		file_api_auth_auth_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_auth_request_proto_rawDescData)
	})
	return file_api_auth_auth_request_proto_rawDescData
}

var file_api_auth_auth_request_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_auth_auth_request_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_auth_auth_request_proto_goTypes = []interface{}{
	(AuthRequestFieldMask)(0),     // 0: auth.AuthRequestFieldMask
	(AuthRequestStatus)(0),        // 1: auth.AuthRequestStatus
	(FlowName)(0),                 // 2: auth.FlowName
	(Provenance)(0),               // 3: auth.Provenance
	(*AuthRequest)(nil),           // 4: auth.AuthRequest
	(*deeplink.Deeplink)(nil),     // 5: frontend.deeplink.Deeplink
	(*timestamppb.Timestamp)(nil), // 6: google.protobuf.Timestamp
}
var file_api_auth_auth_request_proto_depIdxs = []int32{
	2, // 0: auth.AuthRequest.flow_name:type_name -> auth.FlowName
	5, // 1: auth.AuthRequest.next_action:type_name -> frontend.deeplink.Deeplink
	1, // 2: auth.AuthRequest.status:type_name -> auth.AuthRequestStatus
	3, // 3: auth.AuthRequest.provenance:type_name -> auth.Provenance
	6, // 4: auth.AuthRequest.created_at:type_name -> google.protobuf.Timestamp
	6, // 5: auth.AuthRequest.updated_at:type_name -> google.protobuf.Timestamp
	6, // 6: auth.AuthRequest.deleted_at:type_name -> google.protobuf.Timestamp
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_auth_auth_request_proto_init() }
func file_api_auth_auth_request_proto_init() {
	if File_api_auth_auth_request_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_auth_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_auth_request_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_auth_request_proto_goTypes,
		DependencyIndexes: file_api_auth_auth_request_proto_depIdxs,
		EnumInfos:         file_api_auth_auth_request_proto_enumTypes,
		MessageInfos:      file_api_auth_auth_request_proto_msgTypes,
	}.Build()
	File_api_auth_auth_request_proto = out.File
	file_api_auth_auth_request_proto_rawDesc = nil
	file_api_auth_auth_request_proto_goTypes = nil
	file_api_auth_auth_request_proto_depIdxs = nil
}
