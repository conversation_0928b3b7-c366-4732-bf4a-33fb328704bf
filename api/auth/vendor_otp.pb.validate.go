// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/vendor_otp.proto

package auth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on VendorOtp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VendorOtp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VendorOtp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VendorOtpMultiError, or nil
// if none found.
func (m *VendorOtp) ValidateAll() error {
	return m.validate(true)
}

func (m *VendorOtp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetPhone()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VendorOtpValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VendorOtpValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhone()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VendorOtpValidationError{
				field:  "Phone",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	// no validation rules for RequestId

	// no validation rules for RequestType

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VendorOtpValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VendorOtpValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VendorOtpValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VendorOtpValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VendorOtpValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VendorOtpValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VendorOtpMultiError(errors)
	}

	return nil
}

// VendorOtpMultiError is an error wrapping multiple validation errors returned
// by VendorOtp.ValidateAll() if the designated constraints aren't met.
type VendorOtpMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VendorOtpMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VendorOtpMultiError) AllErrors() []error { return m }

// VendorOtpValidationError is the validation error returned by
// VendorOtp.Validate if the designated constraints aren't met.
type VendorOtpValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VendorOtpValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VendorOtpValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VendorOtpValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VendorOtpValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VendorOtpValidationError) ErrorName() string { return "VendorOtpValidationError" }

// Error satisfies the builtin error interface
func (e VendorOtpValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVendorOtp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VendorOtpValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VendorOtpValidationError{}
