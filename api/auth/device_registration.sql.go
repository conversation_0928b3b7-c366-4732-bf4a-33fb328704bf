package auth

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"fmt"

	"database/sql/driver"

	"google.golang.org/protobuf/encoding/protojson"
)

// Valuer interface implementation for storing the data in string format in DB
func (x *DeviceRegistrationMetadata) Value() (driver.Value, error) {
	if x == nil {
		return nil, nil
	}
	return protojson.Marshal(x)
}

// Scanner interface implementation for parsing data while reading from DB
func (x *DeviceRegistrationMetadata) Scan(input interface{}) error {
	val, ok := input.([]byte)
	if !ok {
		err := fmt.Errorf("expected []byte in device reg metadaata, got %T", input)
		return err
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, val, x)
}

func (x *DeviceRegistration) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, x)
}

func (x *DeviceRegistration) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(x)
}
