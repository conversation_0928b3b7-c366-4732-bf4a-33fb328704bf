//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/consumer/auth_factor_update.proto

package consumer

import (
	queue "github.com/epifi/be-common/api/queue"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	notification "github.com/epifi/gamma/api/auth/notification"
	event "github.com/epifi/gamma/api/pay/event"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeviceReRegCallbackConsumerRequest_RequestStatus int32

const (
	DeviceReRegCallbackConsumerRequest_REQUEST_STATUS_UNSPECIFIED DeviceReRegCallbackConsumerRequest_RequestStatus = 0
	DeviceReRegCallbackConsumerRequest_SUCCESS                    DeviceReRegCallbackConsumerRequest_RequestStatus = 1
	DeviceReRegCallbackConsumerRequest_FAILURE                    DeviceReRegCallbackConsumerRequest_RequestStatus = 2
)

// Enum value maps for DeviceReRegCallbackConsumerRequest_RequestStatus.
var (
	DeviceReRegCallbackConsumerRequest_RequestStatus_name = map[int32]string{
		0: "REQUEST_STATUS_UNSPECIFIED",
		1: "SUCCESS",
		2: "FAILURE",
	}
	DeviceReRegCallbackConsumerRequest_RequestStatus_value = map[string]int32{
		"REQUEST_STATUS_UNSPECIFIED": 0,
		"SUCCESS":                    1,
		"FAILURE":                    2,
	}
)

func (x DeviceReRegCallbackConsumerRequest_RequestStatus) Enum() *DeviceReRegCallbackConsumerRequest_RequestStatus {
	p := new(DeviceReRegCallbackConsumerRequest_RequestStatus)
	*p = x
	return p
}

func (x DeviceReRegCallbackConsumerRequest_RequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceReRegCallbackConsumerRequest_RequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_consumer_auth_factor_update_proto_enumTypes[0].Descriptor()
}

func (DeviceReRegCallbackConsumerRequest_RequestStatus) Type() protoreflect.EnumType {
	return &file_api_auth_consumer_auth_factor_update_proto_enumTypes[0]
}

func (x DeviceReRegCallbackConsumerRequest_RequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceReRegCallbackConsumerRequest_RequestStatus.Descriptor instead.
func (DeviceReRegCallbackConsumerRequest_RequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_consumer_auth_factor_update_proto_rawDescGZIP(), []int{2, 0}
}

type ProcessVendorUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	AfuId         string                       `protobuf:"bytes,2,opt,name=afu_id,json=afuId,proto3" json:"afu_id,omitempty"`
	ActorId       string                       `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	CredBlock     string                       `protobuf:"bytes,4,opt,name=cred_block,json=credBlock,proto3" json:"cred_block,omitempty"`
	AccessToken   string                       `protobuf:"bytes,5,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
}

func (x *ProcessVendorUpdateRequest) Reset() {
	*x = ProcessVendorUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessVendorUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessVendorUpdateRequest) ProtoMessage() {}

func (x *ProcessVendorUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessVendorUpdateRequest.ProtoReflect.Descriptor instead.
func (*ProcessVendorUpdateRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_consumer_auth_factor_update_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessVendorUpdateRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessVendorUpdateRequest) GetAfuId() string {
	if x != nil {
		return x.AfuId
	}
	return ""
}

func (x *ProcessVendorUpdateRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ProcessVendorUpdateRequest) GetCredBlock() string {
	if x != nil {
		return x.CredBlock
	}
	return ""
}

func (x *ProcessVendorUpdateRequest) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

type ProcessVendorUpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessVendorUpdateResponse) Reset() {
	*x = ProcessVendorUpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessVendorUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessVendorUpdateResponse) ProtoMessage() {}

func (x *ProcessVendorUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessVendorUpdateResponse.ProtoReflect.Descriptor instead.
func (*ProcessVendorUpdateResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_consumer_auth_factor_update_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessVendorUpdateResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type DeviceReRegCallbackConsumerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// TODO(aditya): rename to status once (RequestStatus status = 2) usage is removed
	// status of the device re-registration request
	AuthFactorUpdateStatus *rpc.Status `protobuf:"bytes,8,opt,name=auth_factor_update_status,json=authFactorUpdateStatus,proto3" json:"auth_factor_update_status,omitempty"`
	// status of the re-reg request
	//
	// Deprecated: Marked as deprecated in api/auth/consumer/auth_factor_update.proto.
	Status DeviceReRegCallbackConsumerRequest_RequestStatus `protobuf:"varint,2,opt,name=status,proto3,enum=consumer.DeviceReRegCallbackConsumerRequest_RequestStatus" json:"status,omitempty"`
	// request_id of the device re-registration request whose callback is received
	RequestId    string                      `protobuf:"bytes,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	DeviceToken  string                      `protobuf:"bytes,5,opt,name=device_token,json=deviceToken,proto3" json:"device_token,omitempty"`
	VendorStatus *vendorgateway.VendorStatus `protobuf:"bytes,9,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
}

func (x *DeviceReRegCallbackConsumerRequest) Reset() {
	*x = DeviceReRegCallbackConsumerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceReRegCallbackConsumerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceReRegCallbackConsumerRequest) ProtoMessage() {}

func (x *DeviceReRegCallbackConsumerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceReRegCallbackConsumerRequest.ProtoReflect.Descriptor instead.
func (*DeviceReRegCallbackConsumerRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_consumer_auth_factor_update_proto_rawDescGZIP(), []int{2}
}

func (x *DeviceReRegCallbackConsumerRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *DeviceReRegCallbackConsumerRequest) GetAuthFactorUpdateStatus() *rpc.Status {
	if x != nil {
		return x.AuthFactorUpdateStatus
	}
	return nil
}

// Deprecated: Marked as deprecated in api/auth/consumer/auth_factor_update.proto.
func (x *DeviceReRegCallbackConsumerRequest) GetStatus() DeviceReRegCallbackConsumerRequest_RequestStatus {
	if x != nil {
		return x.Status
	}
	return DeviceReRegCallbackConsumerRequest_REQUEST_STATUS_UNSPECIFIED
}

func (x *DeviceReRegCallbackConsumerRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DeviceReRegCallbackConsumerRequest) GetDeviceToken() string {
	if x != nil {
		return x.DeviceToken
	}
	return ""
}

func (x *DeviceReRegCallbackConsumerRequest) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

type DeviceReRegCallbackConsumerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *DeviceReRegCallbackConsumerResponse) Reset() {
	*x = DeviceReRegCallbackConsumerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceReRegCallbackConsumerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceReRegCallbackConsumerResponse) ProtoMessage() {}

func (x *DeviceReRegCallbackConsumerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceReRegCallbackConsumerResponse.ProtoReflect.Descriptor instead.
func (*DeviceReRegCallbackConsumerResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_consumer_auth_factor_update_proto_rawDescGZIP(), []int{3}
}

func (x *DeviceReRegCallbackConsumerResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessAFUManualReviewResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessAFUManualReviewResponse) Reset() {
	*x = ProcessAFUManualReviewResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessAFUManualReviewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessAFUManualReviewResponse) ProtoMessage() {}

func (x *ProcessAFUManualReviewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessAFUManualReviewResponse.ProtoReflect.Descriptor instead.
func (*ProcessAFUManualReviewResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_consumer_auth_factor_update_proto_rawDescGZIP(), []int{4}
}

func (x *ProcessAFUManualReviewResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessDevRegSMSAcknowledgementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader    *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	SmsFrom          *common.PhoneNumber          `protobuf:"bytes,2,opt,name=sms_from,json=smsFrom,proto3" json:"sms_from,omitempty"`
	SmsAckAt         *timestamppb.Timestamp       `protobuf:"bytes,3,opt,name=sms_ack_at,json=smsAckAt,proto3" json:"sms_ack_at,omitempty"`
	SmsAckReceivedAt *timestamppb.Timestamp       `protobuf:"bytes,4,opt,name=sms_ack_received_at,json=smsAckReceivedAt,proto3" json:"sms_ack_received_at,omitempty"`
}

func (x *ProcessDevRegSMSAcknowledgementRequest) Reset() {
	*x = ProcessDevRegSMSAcknowledgementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessDevRegSMSAcknowledgementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessDevRegSMSAcknowledgementRequest) ProtoMessage() {}

func (x *ProcessDevRegSMSAcknowledgementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessDevRegSMSAcknowledgementRequest.ProtoReflect.Descriptor instead.
func (*ProcessDevRegSMSAcknowledgementRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_consumer_auth_factor_update_proto_rawDescGZIP(), []int{5}
}

func (x *ProcessDevRegSMSAcknowledgementRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessDevRegSMSAcknowledgementRequest) GetSmsFrom() *common.PhoneNumber {
	if x != nil {
		return x.SmsFrom
	}
	return nil
}

func (x *ProcessDevRegSMSAcknowledgementRequest) GetSmsAckAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SmsAckAt
	}
	return nil
}

func (x *ProcessDevRegSMSAcknowledgementRequest) GetSmsAckReceivedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SmsAckReceivedAt
	}
	return nil
}

type ProcessDevRegSMSAcknowledgementResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessDevRegSMSAcknowledgementResponse) Reset() {
	*x = ProcessDevRegSMSAcknowledgementResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessDevRegSMSAcknowledgementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessDevRegSMSAcknowledgementResponse) ProtoMessage() {}

func (x *ProcessDevRegSMSAcknowledgementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessDevRegSMSAcknowledgementResponse.ProtoReflect.Descriptor instead.
func (*ProcessDevRegSMSAcknowledgementResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_consumer_auth_factor_update_proto_rawDescGZIP(), []int{6}
}

func (x *ProcessDevRegSMSAcknowledgementResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessPinAttemptsExceededEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessPinAttemptsExceededEventResponse) Reset() {
	*x = ProcessPinAttemptsExceededEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessPinAttemptsExceededEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessPinAttemptsExceededEventResponse) ProtoMessage() {}

func (x *ProcessPinAttemptsExceededEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_consumer_auth_factor_update_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessPinAttemptsExceededEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessPinAttemptsExceededEventResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_consumer_auth_factor_update_proto_rawDescGZIP(), []int{7}
}

func (x *ProcessPinAttemptsExceededEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_auth_consumer_auth_factor_update_proto protoreflect.FileDescriptor

var file_api_auth_consumer_auth_factor_update_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68,
	0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x70, 0x61, 0x79, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x69, 0x6e, 0x5f,
	0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x5f, 0x65, 0x78, 0x63, 0x65, 0x65, 0x64, 0x65,
	0x64, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61,
	0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd5, 0x01, 0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x66, 0x75, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x66, 0x75, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x65, 0x0a, 0x1b,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x22, 0xd8, 0x03, 0x0a, 0x22, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x52, 0x65, 0x67, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x46, 0x0a, 0x19, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x16, 0x61, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x56, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x52, 0x65, 0x67, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x49, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x22, 0x6d,
	0x0a, 0x23, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x52, 0x65, 0x67, 0x43, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x68, 0x0a,
	0x1e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x46, 0x55, 0x4d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xae, 0x02, 0x0a, 0x26, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x44, 0x65, 0x76, 0x52, 0x65, 0x67, 0x53, 0x4d, 0x53, 0x41, 0x63, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x08, 0x73, 0x6d, 0x73, 0x5f, 0x66,
	0x72, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x07, 0x73, 0x6d, 0x73, 0x46,
	0x72, 0x6f, 0x6d, 0x12, 0x38, 0x0a, 0x0a, 0x73, 0x6d, 0x73, 0x5f, 0x61, 0x63, 0x6b, 0x5f, 0x61,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x08, 0x73, 0x6d, 0x73, 0x41, 0x63, 0x6b, 0x41, 0x74, 0x12, 0x49, 0x0a,
	0x13, 0x73, 0x6d, 0x73, 0x5f, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x73, 0x6d, 0x73, 0x41, 0x63, 0x6b, 0x52, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x41, 0x74, 0x22, 0x71, 0x0a, 0x27, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x44, 0x65, 0x76, 0x52, 0x65, 0x67, 0x53, 0x4d, 0x53, 0x41, 0x63, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x71, 0x0a, 0x27, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x69, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x73, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0xe7,
	0x04, 0x0a, 0x18, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x12, 0x62, 0x0a, 0x13, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x24, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x7a, 0x0a, 0x1b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x72, 0x65, 0x67, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x12, 0x2c,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x52, 0x65, 0x67, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x52, 0x65, 0x67, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x16, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x46, 0x55, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x4c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x28, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x46, 0x55, 0x4d, 0x61, 0x6e, 0x75,
	0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x86, 0x01, 0x0a, 0x1f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x44, 0x65, 0x76, 0x52,
	0x65, 0x67, 0x53, 0x4d, 0x53, 0x41, 0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x44, 0x65, 0x76, 0x52, 0x65, 0x67, 0x53, 0x4d, 0x53,
	0x41, 0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x44, 0x65, 0x76, 0x52, 0x65, 0x67, 0x53,
	0x4d, 0x53, 0x41, 0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7d, 0x0a, 0x1f, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x50, 0x69, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x45,
	0x78, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x27, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x69, 0x6e,
	0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x31, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x69, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x73, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_consumer_auth_factor_update_proto_rawDescOnce sync.Once
	file_api_auth_consumer_auth_factor_update_proto_rawDescData = file_api_auth_consumer_auth_factor_update_proto_rawDesc
)

func file_api_auth_consumer_auth_factor_update_proto_rawDescGZIP() []byte {
	file_api_auth_consumer_auth_factor_update_proto_rawDescOnce.Do(func() {
		file_api_auth_consumer_auth_factor_update_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_consumer_auth_factor_update_proto_rawDescData)
	})
	return file_api_auth_consumer_auth_factor_update_proto_rawDescData
}

var file_api_auth_consumer_auth_factor_update_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_consumer_auth_factor_update_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_auth_consumer_auth_factor_update_proto_goTypes = []interface{}{
	(DeviceReRegCallbackConsumerRequest_RequestStatus)(0), // 0: consumer.DeviceReRegCallbackConsumerRequest.RequestStatus
	(*ProcessVendorUpdateRequest)(nil),                    // 1: consumer.ProcessVendorUpdateRequest
	(*ProcessVendorUpdateResponse)(nil),                   // 2: consumer.ProcessVendorUpdateResponse
	(*DeviceReRegCallbackConsumerRequest)(nil),            // 3: consumer.DeviceReRegCallbackConsumerRequest
	(*DeviceReRegCallbackConsumerResponse)(nil),           // 4: consumer.DeviceReRegCallbackConsumerResponse
	(*ProcessAFUManualReviewResponse)(nil),                // 5: consumer.ProcessAFUManualReviewResponse
	(*ProcessDevRegSMSAcknowledgementRequest)(nil),        // 6: consumer.ProcessDevRegSMSAcknowledgementRequest
	(*ProcessDevRegSMSAcknowledgementResponse)(nil),       // 7: consumer.ProcessDevRegSMSAcknowledgementResponse
	(*ProcessPinAttemptsExceededEventResponse)(nil),       // 8: consumer.ProcessPinAttemptsExceededEventResponse
	(*queue.ConsumerRequestHeader)(nil),                   // 9: queue.ConsumerRequestHeader
	(*queue.ConsumerResponseHeader)(nil),                  // 10: queue.ConsumerResponseHeader
	(*rpc.Status)(nil),                                    // 11: rpc.Status
	(*vendorgateway.VendorStatus)(nil),                    // 12: vendorgateway.VendorStatus
	(*common.PhoneNumber)(nil),                            // 13: api.typesv2.common.PhoneNumber
	(*timestamppb.Timestamp)(nil),                         // 14: google.protobuf.Timestamp
	(*notification.LivenessManualReviewEvent)(nil),        // 15: auth.LivenessManualReviewEvent
	(*event.PinAttemptsExceededEvent)(nil),                // 16: api.pay.event.PinAttemptsExceededEvent
}
var file_api_auth_consumer_auth_factor_update_proto_depIdxs = []int32{
	9,  // 0: consumer.ProcessVendorUpdateRequest.request_header:type_name -> queue.ConsumerRequestHeader
	10, // 1: consumer.ProcessVendorUpdateResponse.response_header:type_name -> queue.ConsumerResponseHeader
	9,  // 2: consumer.DeviceReRegCallbackConsumerRequest.request_header:type_name -> queue.ConsumerRequestHeader
	11, // 3: consumer.DeviceReRegCallbackConsumerRequest.auth_factor_update_status:type_name -> rpc.Status
	0,  // 4: consumer.DeviceReRegCallbackConsumerRequest.status:type_name -> consumer.DeviceReRegCallbackConsumerRequest.RequestStatus
	12, // 5: consumer.DeviceReRegCallbackConsumerRequest.vendor_status:type_name -> vendorgateway.VendorStatus
	10, // 6: consumer.DeviceReRegCallbackConsumerResponse.response_header:type_name -> queue.ConsumerResponseHeader
	10, // 7: consumer.ProcessAFUManualReviewResponse.response_header:type_name -> queue.ConsumerResponseHeader
	9,  // 8: consumer.ProcessDevRegSMSAcknowledgementRequest.request_header:type_name -> queue.ConsumerRequestHeader
	13, // 9: consumer.ProcessDevRegSMSAcknowledgementRequest.sms_from:type_name -> api.typesv2.common.PhoneNumber
	14, // 10: consumer.ProcessDevRegSMSAcknowledgementRequest.sms_ack_at:type_name -> google.protobuf.Timestamp
	14, // 11: consumer.ProcessDevRegSMSAcknowledgementRequest.sms_ack_received_at:type_name -> google.protobuf.Timestamp
	10, // 12: consumer.ProcessDevRegSMSAcknowledgementResponse.response_header:type_name -> queue.ConsumerResponseHeader
	10, // 13: consumer.ProcessPinAttemptsExceededEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	1,  // 14: consumer.AuthFactorUpdateConsumer.ProcessVendorUpdate:input_type -> consumer.ProcessVendorUpdateRequest
	3,  // 15: consumer.AuthFactorUpdateConsumer.DeviceReregCallbackConsumer:input_type -> consumer.DeviceReRegCallbackConsumerRequest
	15, // 16: consumer.AuthFactorUpdateConsumer.ProcessAFUManualReview:input_type -> auth.LivenessManualReviewEvent
	6,  // 17: consumer.AuthFactorUpdateConsumer.ProcessDevRegSMSAcknowledgement:input_type -> consumer.ProcessDevRegSMSAcknowledgementRequest
	16, // 18: consumer.AuthFactorUpdateConsumer.ProcessPinAttemptsExceededEvent:input_type -> api.pay.event.PinAttemptsExceededEvent
	2,  // 19: consumer.AuthFactorUpdateConsumer.ProcessVendorUpdate:output_type -> consumer.ProcessVendorUpdateResponse
	4,  // 20: consumer.AuthFactorUpdateConsumer.DeviceReregCallbackConsumer:output_type -> consumer.DeviceReRegCallbackConsumerResponse
	5,  // 21: consumer.AuthFactorUpdateConsumer.ProcessAFUManualReview:output_type -> consumer.ProcessAFUManualReviewResponse
	7,  // 22: consumer.AuthFactorUpdateConsumer.ProcessDevRegSMSAcknowledgement:output_type -> consumer.ProcessDevRegSMSAcknowledgementResponse
	8,  // 23: consumer.AuthFactorUpdateConsumer.ProcessPinAttemptsExceededEvent:output_type -> consumer.ProcessPinAttemptsExceededEventResponse
	19, // [19:24] is the sub-list for method output_type
	14, // [14:19] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_api_auth_consumer_auth_factor_update_proto_init() }
func file_api_auth_consumer_auth_factor_update_proto_init() {
	if File_api_auth_consumer_auth_factor_update_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_consumer_auth_factor_update_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessVendorUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_consumer_auth_factor_update_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessVendorUpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_consumer_auth_factor_update_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceReRegCallbackConsumerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_consumer_auth_factor_update_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceReRegCallbackConsumerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_consumer_auth_factor_update_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessAFUManualReviewResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_consumer_auth_factor_update_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessDevRegSMSAcknowledgementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_consumer_auth_factor_update_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessDevRegSMSAcknowledgementResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_consumer_auth_factor_update_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessPinAttemptsExceededEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_consumer_auth_factor_update_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_auth_consumer_auth_factor_update_proto_goTypes,
		DependencyIndexes: file_api_auth_consumer_auth_factor_update_proto_depIdxs,
		EnumInfos:         file_api_auth_consumer_auth_factor_update_proto_enumTypes,
		MessageInfos:      file_api_auth_consumer_auth_factor_update_proto_msgTypes,
	}.Build()
	File_api_auth_consumer_auth_factor_update_proto = out.File
	file_api_auth_consumer_auth_factor_update_proto_rawDesc = nil
	file_api_auth_consumer_auth_factor_update_proto_goTypes = nil
	file_api_auth_consumer_auth_factor_update_proto_depIdxs = nil
}
