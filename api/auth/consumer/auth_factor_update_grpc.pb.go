//go:generate gen_queue_pb

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/auth/consumer/auth_factor_update.proto

package consumer

import (
	context "context"
	notification "github.com/epifi/gamma/api/auth/notification"
	event "github.com/epifi/gamma/api/pay/event"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AuthFactorUpdateConsumer_ProcessVendorUpdate_FullMethodName             = "/consumer.AuthFactorUpdateConsumer/ProcessVendorUpdate"
	AuthFactorUpdateConsumer_DeviceReregCallbackConsumer_FullMethodName     = "/consumer.AuthFactorUpdateConsumer/DeviceReregCallbackConsumer"
	AuthFactorUpdateConsumer_ProcessAFUManualReview_FullMethodName          = "/consumer.AuthFactorUpdateConsumer/ProcessAFUManualReview"
	AuthFactorUpdateConsumer_ProcessDevRegSMSAcknowledgement_FullMethodName = "/consumer.AuthFactorUpdateConsumer/ProcessDevRegSMSAcknowledgement"
	AuthFactorUpdateConsumer_ProcessPinAttemptsExceededEvent_FullMethodName = "/consumer.AuthFactorUpdateConsumer/ProcessPinAttemptsExceededEvent"
)

// AuthFactorUpdateConsumerClient is the client API for AuthFactorUpdateConsumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AuthFactorUpdateConsumerClient interface {
	// This methods is used to process the authFactorUpdate processes which are stuck at vendor's end for processing.
	// If Auth factors are successfully updated at vendor's end, then it completes the AFU process at our end.
	// If update fails at vendor's end, then it marks the afu record as failed.
	// If all the retries are exhausted and process is still stuck at vendor's end, then it marks the process as blocked/stuck.
	ProcessVendorUpdate(ctx context.Context, in *ProcessVendorUpdateRequest, opts ...grpc.CallOption) (*ProcessVendorUpdateResponse, error)
	// This method is used to process the device re-registration callback received from federal
	DeviceReregCallbackConsumer(ctx context.Context, in *DeviceReRegCallbackConsumerRequest, opts ...grpc.CallOption) (*DeviceReRegCallbackConsumerResponse, error)
	// This method is used to process the manual review of liveness in AFU and sends out a notification to user;
	ProcessAFUManualReview(ctx context.Context, in *notification.LivenessManualReviewEvent, opts ...grpc.CallOption) (*ProcessAFUManualReviewResponse, error)
	ProcessDevRegSMSAcknowledgement(ctx context.Context, in *ProcessDevRegSMSAcknowledgementRequest, opts ...grpc.CallOption) (*ProcessDevRegSMSAcknowledgementResponse, error)
	ProcessPinAttemptsExceededEvent(ctx context.Context, in *event.PinAttemptsExceededEvent, opts ...grpc.CallOption) (*ProcessPinAttemptsExceededEventResponse, error)
}

type authFactorUpdateConsumerClient struct {
	cc grpc.ClientConnInterface
}

func NewAuthFactorUpdateConsumerClient(cc grpc.ClientConnInterface) AuthFactorUpdateConsumerClient {
	return &authFactorUpdateConsumerClient{cc}
}

func (c *authFactorUpdateConsumerClient) ProcessVendorUpdate(ctx context.Context, in *ProcessVendorUpdateRequest, opts ...grpc.CallOption) (*ProcessVendorUpdateResponse, error) {
	out := new(ProcessVendorUpdateResponse)
	err := c.cc.Invoke(ctx, AuthFactorUpdateConsumer_ProcessVendorUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authFactorUpdateConsumerClient) DeviceReregCallbackConsumer(ctx context.Context, in *DeviceReRegCallbackConsumerRequest, opts ...grpc.CallOption) (*DeviceReRegCallbackConsumerResponse, error) {
	out := new(DeviceReRegCallbackConsumerResponse)
	err := c.cc.Invoke(ctx, AuthFactorUpdateConsumer_DeviceReregCallbackConsumer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authFactorUpdateConsumerClient) ProcessAFUManualReview(ctx context.Context, in *notification.LivenessManualReviewEvent, opts ...grpc.CallOption) (*ProcessAFUManualReviewResponse, error) {
	out := new(ProcessAFUManualReviewResponse)
	err := c.cc.Invoke(ctx, AuthFactorUpdateConsumer_ProcessAFUManualReview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authFactorUpdateConsumerClient) ProcessDevRegSMSAcknowledgement(ctx context.Context, in *ProcessDevRegSMSAcknowledgementRequest, opts ...grpc.CallOption) (*ProcessDevRegSMSAcknowledgementResponse, error) {
	out := new(ProcessDevRegSMSAcknowledgementResponse)
	err := c.cc.Invoke(ctx, AuthFactorUpdateConsumer_ProcessDevRegSMSAcknowledgement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authFactorUpdateConsumerClient) ProcessPinAttemptsExceededEvent(ctx context.Context, in *event.PinAttemptsExceededEvent, opts ...grpc.CallOption) (*ProcessPinAttemptsExceededEventResponse, error) {
	out := new(ProcessPinAttemptsExceededEventResponse)
	err := c.cc.Invoke(ctx, AuthFactorUpdateConsumer_ProcessPinAttemptsExceededEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuthFactorUpdateConsumerServer is the server API for AuthFactorUpdateConsumer service.
// All implementations should embed UnimplementedAuthFactorUpdateConsumerServer
// for forward compatibility
type AuthFactorUpdateConsumerServer interface {
	// This methods is used to process the authFactorUpdate processes which are stuck at vendor's end for processing.
	// If Auth factors are successfully updated at vendor's end, then it completes the AFU process at our end.
	// If update fails at vendor's end, then it marks the afu record as failed.
	// If all the retries are exhausted and process is still stuck at vendor's end, then it marks the process as blocked/stuck.
	ProcessVendorUpdate(context.Context, *ProcessVendorUpdateRequest) (*ProcessVendorUpdateResponse, error)
	// This method is used to process the device re-registration callback received from federal
	DeviceReregCallbackConsumer(context.Context, *DeviceReRegCallbackConsumerRequest) (*DeviceReRegCallbackConsumerResponse, error)
	// This method is used to process the manual review of liveness in AFU and sends out a notification to user;
	ProcessAFUManualReview(context.Context, *notification.LivenessManualReviewEvent) (*ProcessAFUManualReviewResponse, error)
	ProcessDevRegSMSAcknowledgement(context.Context, *ProcessDevRegSMSAcknowledgementRequest) (*ProcessDevRegSMSAcknowledgementResponse, error)
	ProcessPinAttemptsExceededEvent(context.Context, *event.PinAttemptsExceededEvent) (*ProcessPinAttemptsExceededEventResponse, error)
}

// UnimplementedAuthFactorUpdateConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedAuthFactorUpdateConsumerServer struct {
}

func (UnimplementedAuthFactorUpdateConsumerServer) ProcessVendorUpdate(context.Context, *ProcessVendorUpdateRequest) (*ProcessVendorUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessVendorUpdate not implemented")
}
func (UnimplementedAuthFactorUpdateConsumerServer) DeviceReregCallbackConsumer(context.Context, *DeviceReRegCallbackConsumerRequest) (*DeviceReRegCallbackConsumerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceReregCallbackConsumer not implemented")
}
func (UnimplementedAuthFactorUpdateConsumerServer) ProcessAFUManualReview(context.Context, *notification.LivenessManualReviewEvent) (*ProcessAFUManualReviewResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessAFUManualReview not implemented")
}
func (UnimplementedAuthFactorUpdateConsumerServer) ProcessDevRegSMSAcknowledgement(context.Context, *ProcessDevRegSMSAcknowledgementRequest) (*ProcessDevRegSMSAcknowledgementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessDevRegSMSAcknowledgement not implemented")
}
func (UnimplementedAuthFactorUpdateConsumerServer) ProcessPinAttemptsExceededEvent(context.Context, *event.PinAttemptsExceededEvent) (*ProcessPinAttemptsExceededEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessPinAttemptsExceededEvent not implemented")
}

// UnsafeAuthFactorUpdateConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuthFactorUpdateConsumerServer will
// result in compilation errors.
type UnsafeAuthFactorUpdateConsumerServer interface {
	mustEmbedUnimplementedAuthFactorUpdateConsumerServer()
}

func RegisterAuthFactorUpdateConsumerServer(s grpc.ServiceRegistrar, srv AuthFactorUpdateConsumerServer) {
	s.RegisterService(&AuthFactorUpdateConsumer_ServiceDesc, srv)
}

func _AuthFactorUpdateConsumer_ProcessVendorUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessVendorUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthFactorUpdateConsumerServer).ProcessVendorUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthFactorUpdateConsumer_ProcessVendorUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthFactorUpdateConsumerServer).ProcessVendorUpdate(ctx, req.(*ProcessVendorUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthFactorUpdateConsumer_DeviceReregCallbackConsumer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceReRegCallbackConsumerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthFactorUpdateConsumerServer).DeviceReregCallbackConsumer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthFactorUpdateConsumer_DeviceReregCallbackConsumer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthFactorUpdateConsumerServer).DeviceReregCallbackConsumer(ctx, req.(*DeviceReRegCallbackConsumerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthFactorUpdateConsumer_ProcessAFUManualReview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(notification.LivenessManualReviewEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthFactorUpdateConsumerServer).ProcessAFUManualReview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthFactorUpdateConsumer_ProcessAFUManualReview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthFactorUpdateConsumerServer).ProcessAFUManualReview(ctx, req.(*notification.LivenessManualReviewEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthFactorUpdateConsumer_ProcessDevRegSMSAcknowledgement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessDevRegSMSAcknowledgementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthFactorUpdateConsumerServer).ProcessDevRegSMSAcknowledgement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthFactorUpdateConsumer_ProcessDevRegSMSAcknowledgement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthFactorUpdateConsumerServer).ProcessDevRegSMSAcknowledgement(ctx, req.(*ProcessDevRegSMSAcknowledgementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthFactorUpdateConsumer_ProcessPinAttemptsExceededEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(event.PinAttemptsExceededEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthFactorUpdateConsumerServer).ProcessPinAttemptsExceededEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthFactorUpdateConsumer_ProcessPinAttemptsExceededEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthFactorUpdateConsumerServer).ProcessPinAttemptsExceededEvent(ctx, req.(*event.PinAttemptsExceededEvent))
	}
	return interceptor(ctx, in, info, handler)
}

// AuthFactorUpdateConsumer_ServiceDesc is the grpc.ServiceDesc for AuthFactorUpdateConsumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AuthFactorUpdateConsumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "consumer.AuthFactorUpdateConsumer",
	HandlerType: (*AuthFactorUpdateConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessVendorUpdate",
			Handler:    _AuthFactorUpdateConsumer_ProcessVendorUpdate_Handler,
		},
		{
			MethodName: "DeviceReregCallbackConsumer",
			Handler:    _AuthFactorUpdateConsumer_DeviceReregCallbackConsumer_Handler,
		},
		{
			MethodName: "ProcessAFUManualReview",
			Handler:    _AuthFactorUpdateConsumer_ProcessAFUManualReview_Handler,
		},
		{
			MethodName: "ProcessDevRegSMSAcknowledgement",
			Handler:    _AuthFactorUpdateConsumer_ProcessDevRegSMSAcknowledgement_Handler,
		},
		{
			MethodName: "ProcessPinAttemptsExceededEvent",
			Handler:    _AuthFactorUpdateConsumer_ProcessPinAttemptsExceededEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/auth/consumer/auth_factor_update.proto",
}
