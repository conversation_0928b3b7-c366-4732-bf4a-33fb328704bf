// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/auth/consumer
package consumer

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessVendorUpdateMethod             = "ProcessVendorUpdate"
	DeviceReregCallbackConsumerMethod     = "DeviceReregCallbackConsumer"
	ProcessAFUManualReviewMethod          = "ProcessAFUManualReview"
	ProcessDevRegSMSAcknowledgementMethod = "ProcessDevRegSMSAcknowledgement"
	ProcessPinAttemptsExceededEventMethod = "ProcessPinAttemptsExceededEvent"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &ProcessVendorUpdateRequest{}
var _ queue.ConsumerRequest = &DeviceReRegCallbackConsumerRequest{}
var _ queue.ConsumerRequest = &ProcessDevRegSMSAcknowledgementRequest{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessVendorUpdateRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *DeviceReRegCallbackConsumerRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessDevRegSMSAcknowledgementRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterProcessVendorUpdateMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessVendorUpdateMethodToSubscriber(subscriber queue.Subscriber, srv AuthFactorUpdateConsumerServer) {
	subscriber.RegisterService(&AuthFactorUpdateConsumer_ServiceDesc, srv, ProcessVendorUpdateMethod)
}

// RegisterDeviceReregCallbackConsumerMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterDeviceReregCallbackConsumerMethodToSubscriber(subscriber queue.Subscriber, srv AuthFactorUpdateConsumerServer) {
	subscriber.RegisterService(&AuthFactorUpdateConsumer_ServiceDesc, srv, DeviceReregCallbackConsumerMethod)
}

// RegisterProcessAFUManualReviewMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessAFUManualReviewMethodToSubscriber(subscriber queue.Subscriber, srv AuthFactorUpdateConsumerServer) {
	subscriber.RegisterService(&AuthFactorUpdateConsumer_ServiceDesc, srv, ProcessAFUManualReviewMethod)
}

// RegisterProcessDevRegSMSAcknowledgementMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessDevRegSMSAcknowledgementMethodToSubscriber(subscriber queue.Subscriber, srv AuthFactorUpdateConsumerServer) {
	subscriber.RegisterService(&AuthFactorUpdateConsumer_ServiceDesc, srv, ProcessDevRegSMSAcknowledgementMethod)
}

// RegisterProcessPinAttemptsExceededEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessPinAttemptsExceededEventMethodToSubscriber(subscriber queue.Subscriber, srv AuthFactorUpdateConsumerServer) {
	subscriber.RegisterService(&AuthFactorUpdateConsumer_ServiceDesc, srv, ProcessPinAttemptsExceededEventMethod)
}
