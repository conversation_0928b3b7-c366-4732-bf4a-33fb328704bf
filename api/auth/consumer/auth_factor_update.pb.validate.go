// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/consumer/auth_factor_update.proto

package consumer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ProcessVendorUpdateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessVendorUpdateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessVendorUpdateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessVendorUpdateRequestMultiError, or nil if none found.
func (m *ProcessVendorUpdateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessVendorUpdateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessVendorUpdateRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessVendorUpdateRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessVendorUpdateRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AfuId

	// no validation rules for ActorId

	// no validation rules for CredBlock

	// no validation rules for AccessToken

	if len(errors) > 0 {
		return ProcessVendorUpdateRequestMultiError(errors)
	}

	return nil
}

// ProcessVendorUpdateRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessVendorUpdateRequest.ValidateAll() if
// the designated constraints aren't met.
type ProcessVendorUpdateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessVendorUpdateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessVendorUpdateRequestMultiError) AllErrors() []error { return m }

// ProcessVendorUpdateRequestValidationError is the validation error returned
// by ProcessVendorUpdateRequest.Validate if the designated constraints aren't met.
type ProcessVendorUpdateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessVendorUpdateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessVendorUpdateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessVendorUpdateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessVendorUpdateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessVendorUpdateRequestValidationError) ErrorName() string {
	return "ProcessVendorUpdateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessVendorUpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessVendorUpdateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessVendorUpdateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessVendorUpdateRequestValidationError{}

// Validate checks the field values on ProcessVendorUpdateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessVendorUpdateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessVendorUpdateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessVendorUpdateResponseMultiError, or nil if none found.
func (m *ProcessVendorUpdateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessVendorUpdateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessVendorUpdateResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessVendorUpdateResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessVendorUpdateResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessVendorUpdateResponseMultiError(errors)
	}

	return nil
}

// ProcessVendorUpdateResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessVendorUpdateResponse.ValidateAll() if
// the designated constraints aren't met.
type ProcessVendorUpdateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessVendorUpdateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessVendorUpdateResponseMultiError) AllErrors() []error { return m }

// ProcessVendorUpdateResponseValidationError is the validation error returned
// by ProcessVendorUpdateResponse.Validate if the designated constraints
// aren't met.
type ProcessVendorUpdateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessVendorUpdateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessVendorUpdateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessVendorUpdateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessVendorUpdateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessVendorUpdateResponseValidationError) ErrorName() string {
	return "ProcessVendorUpdateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessVendorUpdateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessVendorUpdateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessVendorUpdateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessVendorUpdateResponseValidationError{}

// Validate checks the field values on DeviceReRegCallbackConsumerRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeviceReRegCallbackConsumerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceReRegCallbackConsumerRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeviceReRegCallbackConsumerRequestMultiError, or nil if none found.
func (m *DeviceReRegCallbackConsumerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceReRegCallbackConsumerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceReRegCallbackConsumerRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceReRegCallbackConsumerRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceReRegCallbackConsumerRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuthFactorUpdateStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceReRegCallbackConsumerRequestValidationError{
					field:  "AuthFactorUpdateStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceReRegCallbackConsumerRequestValidationError{
					field:  "AuthFactorUpdateStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthFactorUpdateStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceReRegCallbackConsumerRequestValidationError{
				field:  "AuthFactorUpdateStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for RequestId

	// no validation rules for DeviceToken

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceReRegCallbackConsumerRequestValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceReRegCallbackConsumerRequestValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceReRegCallbackConsumerRequestValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeviceReRegCallbackConsumerRequestMultiError(errors)
	}

	return nil
}

// DeviceReRegCallbackConsumerRequestMultiError is an error wrapping multiple
// validation errors returned by
// DeviceReRegCallbackConsumerRequest.ValidateAll() if the designated
// constraints aren't met.
type DeviceReRegCallbackConsumerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceReRegCallbackConsumerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceReRegCallbackConsumerRequestMultiError) AllErrors() []error { return m }

// DeviceReRegCallbackConsumerRequestValidationError is the validation error
// returned by DeviceReRegCallbackConsumerRequest.Validate if the designated
// constraints aren't met.
type DeviceReRegCallbackConsumerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceReRegCallbackConsumerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceReRegCallbackConsumerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceReRegCallbackConsumerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceReRegCallbackConsumerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceReRegCallbackConsumerRequestValidationError) ErrorName() string {
	return "DeviceReRegCallbackConsumerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeviceReRegCallbackConsumerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceReRegCallbackConsumerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceReRegCallbackConsumerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceReRegCallbackConsumerRequestValidationError{}

// Validate checks the field values on DeviceReRegCallbackConsumerResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeviceReRegCallbackConsumerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceReRegCallbackConsumerResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeviceReRegCallbackConsumerResponseMultiError, or nil if none found.
func (m *DeviceReRegCallbackConsumerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceReRegCallbackConsumerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceReRegCallbackConsumerResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceReRegCallbackConsumerResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceReRegCallbackConsumerResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeviceReRegCallbackConsumerResponseMultiError(errors)
	}

	return nil
}

// DeviceReRegCallbackConsumerResponseMultiError is an error wrapping multiple
// validation errors returned by
// DeviceReRegCallbackConsumerResponse.ValidateAll() if the designated
// constraints aren't met.
type DeviceReRegCallbackConsumerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceReRegCallbackConsumerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceReRegCallbackConsumerResponseMultiError) AllErrors() []error { return m }

// DeviceReRegCallbackConsumerResponseValidationError is the validation error
// returned by DeviceReRegCallbackConsumerResponse.Validate if the designated
// constraints aren't met.
type DeviceReRegCallbackConsumerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceReRegCallbackConsumerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceReRegCallbackConsumerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceReRegCallbackConsumerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceReRegCallbackConsumerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceReRegCallbackConsumerResponseValidationError) ErrorName() string {
	return "DeviceReRegCallbackConsumerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeviceReRegCallbackConsumerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceReRegCallbackConsumerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceReRegCallbackConsumerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceReRegCallbackConsumerResponseValidationError{}

// Validate checks the field values on ProcessAFUManualReviewResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessAFUManualReviewResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessAFUManualReviewResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessAFUManualReviewResponseMultiError, or nil if none found.
func (m *ProcessAFUManualReviewResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessAFUManualReviewResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessAFUManualReviewResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessAFUManualReviewResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessAFUManualReviewResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessAFUManualReviewResponseMultiError(errors)
	}

	return nil
}

// ProcessAFUManualReviewResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessAFUManualReviewResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessAFUManualReviewResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessAFUManualReviewResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessAFUManualReviewResponseMultiError) AllErrors() []error { return m }

// ProcessAFUManualReviewResponseValidationError is the validation error
// returned by ProcessAFUManualReviewResponse.Validate if the designated
// constraints aren't met.
type ProcessAFUManualReviewResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessAFUManualReviewResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessAFUManualReviewResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessAFUManualReviewResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessAFUManualReviewResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessAFUManualReviewResponseValidationError) ErrorName() string {
	return "ProcessAFUManualReviewResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessAFUManualReviewResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessAFUManualReviewResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessAFUManualReviewResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessAFUManualReviewResponseValidationError{}

// Validate checks the field values on ProcessDevRegSMSAcknowledgementRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessDevRegSMSAcknowledgementRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessDevRegSMSAcknowledgementRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ProcessDevRegSMSAcknowledgementRequestMultiError, or nil if none found.
func (m *ProcessDevRegSMSAcknowledgementRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessDevRegSMSAcknowledgementRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessDevRegSMSAcknowledgementRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessDevRegSMSAcknowledgementRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessDevRegSMSAcknowledgementRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSmsFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessDevRegSMSAcknowledgementRequestValidationError{
					field:  "SmsFrom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessDevRegSMSAcknowledgementRequestValidationError{
					field:  "SmsFrom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSmsFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessDevRegSMSAcknowledgementRequestValidationError{
				field:  "SmsFrom",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSmsAckAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessDevRegSMSAcknowledgementRequestValidationError{
					field:  "SmsAckAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessDevRegSMSAcknowledgementRequestValidationError{
					field:  "SmsAckAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSmsAckAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessDevRegSMSAcknowledgementRequestValidationError{
				field:  "SmsAckAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSmsAckReceivedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessDevRegSMSAcknowledgementRequestValidationError{
					field:  "SmsAckReceivedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessDevRegSMSAcknowledgementRequestValidationError{
					field:  "SmsAckReceivedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSmsAckReceivedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessDevRegSMSAcknowledgementRequestValidationError{
				field:  "SmsAckReceivedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessDevRegSMSAcknowledgementRequestMultiError(errors)
	}

	return nil
}

// ProcessDevRegSMSAcknowledgementRequestMultiError is an error wrapping
// multiple validation errors returned by
// ProcessDevRegSMSAcknowledgementRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessDevRegSMSAcknowledgementRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessDevRegSMSAcknowledgementRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessDevRegSMSAcknowledgementRequestMultiError) AllErrors() []error { return m }

// ProcessDevRegSMSAcknowledgementRequestValidationError is the validation
// error returned by ProcessDevRegSMSAcknowledgementRequest.Validate if the
// designated constraints aren't met.
type ProcessDevRegSMSAcknowledgementRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessDevRegSMSAcknowledgementRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessDevRegSMSAcknowledgementRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessDevRegSMSAcknowledgementRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessDevRegSMSAcknowledgementRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessDevRegSMSAcknowledgementRequestValidationError) ErrorName() string {
	return "ProcessDevRegSMSAcknowledgementRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessDevRegSMSAcknowledgementRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessDevRegSMSAcknowledgementRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessDevRegSMSAcknowledgementRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessDevRegSMSAcknowledgementRequestValidationError{}

// Validate checks the field values on ProcessDevRegSMSAcknowledgementResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessDevRegSMSAcknowledgementResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessDevRegSMSAcknowledgementResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ProcessDevRegSMSAcknowledgementResponseMultiError, or nil if none found.
func (m *ProcessDevRegSMSAcknowledgementResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessDevRegSMSAcknowledgementResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessDevRegSMSAcknowledgementResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessDevRegSMSAcknowledgementResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessDevRegSMSAcknowledgementResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessDevRegSMSAcknowledgementResponseMultiError(errors)
	}

	return nil
}

// ProcessDevRegSMSAcknowledgementResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessDevRegSMSAcknowledgementResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessDevRegSMSAcknowledgementResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessDevRegSMSAcknowledgementResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessDevRegSMSAcknowledgementResponseMultiError) AllErrors() []error { return m }

// ProcessDevRegSMSAcknowledgementResponseValidationError is the validation
// error returned by ProcessDevRegSMSAcknowledgementResponse.Validate if the
// designated constraints aren't met.
type ProcessDevRegSMSAcknowledgementResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessDevRegSMSAcknowledgementResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessDevRegSMSAcknowledgementResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessDevRegSMSAcknowledgementResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessDevRegSMSAcknowledgementResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessDevRegSMSAcknowledgementResponseValidationError) ErrorName() string {
	return "ProcessDevRegSMSAcknowledgementResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessDevRegSMSAcknowledgementResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessDevRegSMSAcknowledgementResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessDevRegSMSAcknowledgementResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessDevRegSMSAcknowledgementResponseValidationError{}

// Validate checks the field values on ProcessPinAttemptsExceededEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessPinAttemptsExceededEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessPinAttemptsExceededEventResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ProcessPinAttemptsExceededEventResponseMultiError, or nil if none found.
func (m *ProcessPinAttemptsExceededEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessPinAttemptsExceededEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessPinAttemptsExceededEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessPinAttemptsExceededEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessPinAttemptsExceededEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessPinAttemptsExceededEventResponseMultiError(errors)
	}

	return nil
}

// ProcessPinAttemptsExceededEventResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessPinAttemptsExceededEventResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessPinAttemptsExceededEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessPinAttemptsExceededEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessPinAttemptsExceededEventResponseMultiError) AllErrors() []error { return m }

// ProcessPinAttemptsExceededEventResponseValidationError is the validation
// error returned by ProcessPinAttemptsExceededEventResponse.Validate if the
// designated constraints aren't met.
type ProcessPinAttemptsExceededEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessPinAttemptsExceededEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessPinAttemptsExceededEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessPinAttemptsExceededEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessPinAttemptsExceededEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessPinAttemptsExceededEventResponseValidationError) ErrorName() string {
	return "ProcessPinAttemptsExceededEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessPinAttemptsExceededEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessPinAttemptsExceededEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessPinAttemptsExceededEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessPinAttemptsExceededEventResponseValidationError{}
