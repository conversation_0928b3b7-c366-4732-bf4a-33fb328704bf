// Code generated by MockGen. DO NOT EDIT.
// Source: api/auth/consumer/auth_factor_update_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	consumer "github.com/epifi/gamma/api/auth/consumer"
	notification "github.com/epifi/gamma/api/auth/notification"
	event "github.com/epifi/gamma/api/pay/event"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAuthFactorUpdateConsumerClient is a mock of AuthFactorUpdateConsumerClient interface.
type MockAuthFactorUpdateConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockAuthFactorUpdateConsumerClientMockRecorder
}

// MockAuthFactorUpdateConsumerClientMockRecorder is the mock recorder for MockAuthFactorUpdateConsumerClient.
type MockAuthFactorUpdateConsumerClientMockRecorder struct {
	mock *MockAuthFactorUpdateConsumerClient
}

// NewMockAuthFactorUpdateConsumerClient creates a new mock instance.
func NewMockAuthFactorUpdateConsumerClient(ctrl *gomock.Controller) *MockAuthFactorUpdateConsumerClient {
	mock := &MockAuthFactorUpdateConsumerClient{ctrl: ctrl}
	mock.recorder = &MockAuthFactorUpdateConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAuthFactorUpdateConsumerClient) EXPECT() *MockAuthFactorUpdateConsumerClientMockRecorder {
	return m.recorder
}

// DeviceReregCallbackConsumer mocks base method.
func (m *MockAuthFactorUpdateConsumerClient) DeviceReregCallbackConsumer(ctx context.Context, in *consumer.DeviceReRegCallbackConsumerRequest, opts ...grpc.CallOption) (*consumer.DeviceReRegCallbackConsumerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeviceReregCallbackConsumer", varargs...)
	ret0, _ := ret[0].(*consumer.DeviceReRegCallbackConsumerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeviceReregCallbackConsumer indicates an expected call of DeviceReregCallbackConsumer.
func (mr *MockAuthFactorUpdateConsumerClientMockRecorder) DeviceReregCallbackConsumer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeviceReregCallbackConsumer", reflect.TypeOf((*MockAuthFactorUpdateConsumerClient)(nil).DeviceReregCallbackConsumer), varargs...)
}

// ProcessAFUManualReview mocks base method.
func (m *MockAuthFactorUpdateConsumerClient) ProcessAFUManualReview(ctx context.Context, in *notification.LivenessManualReviewEvent, opts ...grpc.CallOption) (*consumer.ProcessAFUManualReviewResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessAFUManualReview", varargs...)
	ret0, _ := ret[0].(*consumer.ProcessAFUManualReviewResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAFUManualReview indicates an expected call of ProcessAFUManualReview.
func (mr *MockAuthFactorUpdateConsumerClientMockRecorder) ProcessAFUManualReview(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAFUManualReview", reflect.TypeOf((*MockAuthFactorUpdateConsumerClient)(nil).ProcessAFUManualReview), varargs...)
}

// ProcessDevRegSMSAcknowledgement mocks base method.
func (m *MockAuthFactorUpdateConsumerClient) ProcessDevRegSMSAcknowledgement(ctx context.Context, in *consumer.ProcessDevRegSMSAcknowledgementRequest, opts ...grpc.CallOption) (*consumer.ProcessDevRegSMSAcknowledgementResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessDevRegSMSAcknowledgement", varargs...)
	ret0, _ := ret[0].(*consumer.ProcessDevRegSMSAcknowledgementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessDevRegSMSAcknowledgement indicates an expected call of ProcessDevRegSMSAcknowledgement.
func (mr *MockAuthFactorUpdateConsumerClientMockRecorder) ProcessDevRegSMSAcknowledgement(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessDevRegSMSAcknowledgement", reflect.TypeOf((*MockAuthFactorUpdateConsumerClient)(nil).ProcessDevRegSMSAcknowledgement), varargs...)
}

// ProcessPinAttemptsExceededEvent mocks base method.
func (m *MockAuthFactorUpdateConsumerClient) ProcessPinAttemptsExceededEvent(ctx context.Context, in *event.PinAttemptsExceededEvent, opts ...grpc.CallOption) (*consumer.ProcessPinAttemptsExceededEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessPinAttemptsExceededEvent", varargs...)
	ret0, _ := ret[0].(*consumer.ProcessPinAttemptsExceededEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessPinAttemptsExceededEvent indicates an expected call of ProcessPinAttemptsExceededEvent.
func (mr *MockAuthFactorUpdateConsumerClientMockRecorder) ProcessPinAttemptsExceededEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessPinAttemptsExceededEvent", reflect.TypeOf((*MockAuthFactorUpdateConsumerClient)(nil).ProcessPinAttemptsExceededEvent), varargs...)
}

// ProcessVendorUpdate mocks base method.
func (m *MockAuthFactorUpdateConsumerClient) ProcessVendorUpdate(ctx context.Context, in *consumer.ProcessVendorUpdateRequest, opts ...grpc.CallOption) (*consumer.ProcessVendorUpdateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessVendorUpdate", varargs...)
	ret0, _ := ret[0].(*consumer.ProcessVendorUpdateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessVendorUpdate indicates an expected call of ProcessVendorUpdate.
func (mr *MockAuthFactorUpdateConsumerClientMockRecorder) ProcessVendorUpdate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessVendorUpdate", reflect.TypeOf((*MockAuthFactorUpdateConsumerClient)(nil).ProcessVendorUpdate), varargs...)
}

// MockAuthFactorUpdateConsumerServer is a mock of AuthFactorUpdateConsumerServer interface.
type MockAuthFactorUpdateConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockAuthFactorUpdateConsumerServerMockRecorder
}

// MockAuthFactorUpdateConsumerServerMockRecorder is the mock recorder for MockAuthFactorUpdateConsumerServer.
type MockAuthFactorUpdateConsumerServerMockRecorder struct {
	mock *MockAuthFactorUpdateConsumerServer
}

// NewMockAuthFactorUpdateConsumerServer creates a new mock instance.
func NewMockAuthFactorUpdateConsumerServer(ctrl *gomock.Controller) *MockAuthFactorUpdateConsumerServer {
	mock := &MockAuthFactorUpdateConsumerServer{ctrl: ctrl}
	mock.recorder = &MockAuthFactorUpdateConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAuthFactorUpdateConsumerServer) EXPECT() *MockAuthFactorUpdateConsumerServerMockRecorder {
	return m.recorder
}

// DeviceReregCallbackConsumer mocks base method.
func (m *MockAuthFactorUpdateConsumerServer) DeviceReregCallbackConsumer(arg0 context.Context, arg1 *consumer.DeviceReRegCallbackConsumerRequest) (*consumer.DeviceReRegCallbackConsumerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeviceReregCallbackConsumer", arg0, arg1)
	ret0, _ := ret[0].(*consumer.DeviceReRegCallbackConsumerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeviceReregCallbackConsumer indicates an expected call of DeviceReregCallbackConsumer.
func (mr *MockAuthFactorUpdateConsumerServerMockRecorder) DeviceReregCallbackConsumer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeviceReregCallbackConsumer", reflect.TypeOf((*MockAuthFactorUpdateConsumerServer)(nil).DeviceReregCallbackConsumer), arg0, arg1)
}

// ProcessAFUManualReview mocks base method.
func (m *MockAuthFactorUpdateConsumerServer) ProcessAFUManualReview(arg0 context.Context, arg1 *notification.LivenessManualReviewEvent) (*consumer.ProcessAFUManualReviewResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessAFUManualReview", arg0, arg1)
	ret0, _ := ret[0].(*consumer.ProcessAFUManualReviewResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAFUManualReview indicates an expected call of ProcessAFUManualReview.
func (mr *MockAuthFactorUpdateConsumerServerMockRecorder) ProcessAFUManualReview(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAFUManualReview", reflect.TypeOf((*MockAuthFactorUpdateConsumerServer)(nil).ProcessAFUManualReview), arg0, arg1)
}

// ProcessDevRegSMSAcknowledgement mocks base method.
func (m *MockAuthFactorUpdateConsumerServer) ProcessDevRegSMSAcknowledgement(arg0 context.Context, arg1 *consumer.ProcessDevRegSMSAcknowledgementRequest) (*consumer.ProcessDevRegSMSAcknowledgementResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessDevRegSMSAcknowledgement", arg0, arg1)
	ret0, _ := ret[0].(*consumer.ProcessDevRegSMSAcknowledgementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessDevRegSMSAcknowledgement indicates an expected call of ProcessDevRegSMSAcknowledgement.
func (mr *MockAuthFactorUpdateConsumerServerMockRecorder) ProcessDevRegSMSAcknowledgement(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessDevRegSMSAcknowledgement", reflect.TypeOf((*MockAuthFactorUpdateConsumerServer)(nil).ProcessDevRegSMSAcknowledgement), arg0, arg1)
}

// ProcessPinAttemptsExceededEvent mocks base method.
func (m *MockAuthFactorUpdateConsumerServer) ProcessPinAttemptsExceededEvent(arg0 context.Context, arg1 *event.PinAttemptsExceededEvent) (*consumer.ProcessPinAttemptsExceededEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessPinAttemptsExceededEvent", arg0, arg1)
	ret0, _ := ret[0].(*consumer.ProcessPinAttemptsExceededEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessPinAttemptsExceededEvent indicates an expected call of ProcessPinAttemptsExceededEvent.
func (mr *MockAuthFactorUpdateConsumerServerMockRecorder) ProcessPinAttemptsExceededEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessPinAttemptsExceededEvent", reflect.TypeOf((*MockAuthFactorUpdateConsumerServer)(nil).ProcessPinAttemptsExceededEvent), arg0, arg1)
}

// ProcessVendorUpdate mocks base method.
func (m *MockAuthFactorUpdateConsumerServer) ProcessVendorUpdate(arg0 context.Context, arg1 *consumer.ProcessVendorUpdateRequest) (*consumer.ProcessVendorUpdateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessVendorUpdate", arg0, arg1)
	ret0, _ := ret[0].(*consumer.ProcessVendorUpdateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessVendorUpdate indicates an expected call of ProcessVendorUpdate.
func (mr *MockAuthFactorUpdateConsumerServerMockRecorder) ProcessVendorUpdate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessVendorUpdate", reflect.TypeOf((*MockAuthFactorUpdateConsumerServer)(nil).ProcessVendorUpdate), arg0, arg1)
}

// MockUnsafeAuthFactorUpdateConsumerServer is a mock of UnsafeAuthFactorUpdateConsumerServer interface.
type MockUnsafeAuthFactorUpdateConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAuthFactorUpdateConsumerServerMockRecorder
}

// MockUnsafeAuthFactorUpdateConsumerServerMockRecorder is the mock recorder for MockUnsafeAuthFactorUpdateConsumerServer.
type MockUnsafeAuthFactorUpdateConsumerServerMockRecorder struct {
	mock *MockUnsafeAuthFactorUpdateConsumerServer
}

// NewMockUnsafeAuthFactorUpdateConsumerServer creates a new mock instance.
func NewMockUnsafeAuthFactorUpdateConsumerServer(ctrl *gomock.Controller) *MockUnsafeAuthFactorUpdateConsumerServer {
	mock := &MockUnsafeAuthFactorUpdateConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAuthFactorUpdateConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAuthFactorUpdateConsumerServer) EXPECT() *MockUnsafeAuthFactorUpdateConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAuthFactorUpdateConsumerServer mocks base method.
func (m *MockUnsafeAuthFactorUpdateConsumerServer) mustEmbedUnimplementedAuthFactorUpdateConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAuthFactorUpdateConsumerServer")
}

// mustEmbedUnimplementedAuthFactorUpdateConsumerServer indicates an expected call of mustEmbedUnimplementedAuthFactorUpdateConsumerServer.
func (mr *MockUnsafeAuthFactorUpdateConsumerServerMockRecorder) mustEmbedUnimplementedAuthFactorUpdateConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAuthFactorUpdateConsumerServer", reflect.TypeOf((*MockUnsafeAuthFactorUpdateConsumerServer)(nil).mustEmbedUnimplementedAuthFactorUpdateConsumerServer))
}
