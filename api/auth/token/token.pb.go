// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/token/token.proto

package token

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// JWT tokens have a set of registered claims and custom claims
// This message defines the custom claims passed while creating token
type CustomTokenClaims struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The subject field in jwt registered claims can have values of various types - actor id, ph number, email, ...
	// It depends on the type of token and also when the token is created
	// To identify the type of value being passed, this field needs to be specified
	// string type of the enum SubjectType (api/auth/token/enums.proto)
	SubjectType string `protobuf:"bytes,1,opt,name=subject_type,json=subjectType,proto3" json:"subject_type,omitempty"`
}

func (x *CustomTokenClaims) Reset() {
	*x = CustomTokenClaims{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_token_token_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomTokenClaims) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomTokenClaims) ProtoMessage() {}

func (x *CustomTokenClaims) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_token_token_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomTokenClaims.ProtoReflect.Descriptor instead.
func (*CustomTokenClaims) Descriptor() ([]byte, []int) {
	return file_api_auth_token_token_proto_rawDescGZIP(), []int{0}
}

func (x *CustomTokenClaims) GetSubjectType() string {
	if x != nil {
		return x.SubjectType
	}
	return ""
}

var File_api_auth_token_token_proto protoreflect.FileDescriptor

var file_api_auth_token_token_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x22, 0x36, 0x0a, 0x11, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x4e, 0x0a, 0x25, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_auth_token_token_proto_rawDescOnce sync.Once
	file_api_auth_token_token_proto_rawDescData = file_api_auth_token_token_proto_rawDesc
)

func file_api_auth_token_token_proto_rawDescGZIP() []byte {
	file_api_auth_token_token_proto_rawDescOnce.Do(func() {
		file_api_auth_token_token_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_token_token_proto_rawDescData)
	})
	return file_api_auth_token_token_proto_rawDescData
}

var file_api_auth_token_token_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_auth_token_token_proto_goTypes = []interface{}{
	(*CustomTokenClaims)(nil), // 0: token.CustomTokenClaims
}
var file_api_auth_token_token_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_auth_token_token_proto_init() }
func file_api_auth_token_token_proto_init() {
	if File_api_auth_token_token_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_token_token_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomTokenClaims); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_token_token_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_token_token_proto_goTypes,
		DependencyIndexes: file_api_auth_token_token_proto_depIdxs,
		MessageInfos:      file_api_auth_token_token_proto_msgTypes,
	}.Build()
	File_api_auth_token_token_proto = out.File
	file_api_auth_token_token_proto_rawDesc = nil
	file_api_auth_token_token_proto_goTypes = nil
	file_api_auth_token_token_proto_depIdxs = nil
}
