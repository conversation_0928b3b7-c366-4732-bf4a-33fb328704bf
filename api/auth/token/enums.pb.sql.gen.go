// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/auth/token/enums.pb.go

package token

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the SubjectType in string format in DB
func (p SubjectType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing SubjectType while reading from DB
func (p *SubjectType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := SubjectType_value[val]
	if !ok {
		return fmt.Errorf("unexpected SubjectType value: %s", val)
	}
	*p = SubjectType(valInt)
	return nil
}

// Marshaler interface implementation for SubjectType
func (x SubjectType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for SubjectType
func (x *SubjectType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = SubjectType(SubjectType_value[val])
	return nil
}
