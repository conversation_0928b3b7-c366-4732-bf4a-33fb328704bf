//go:generate gen_sql -types=SubjectType

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/token/enums.proto

package token

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The subject field in jwt registered claims can have values of various types - actor id, ph number, email, ...
// This enum denotes the type of value being passed in subject while creating the token
type SubjectType int32

const (
	SubjectType_SUBJECT_TYPE_UNSPECIFIED  SubjectType = 0
	SubjectType_SUBJECT_TYPE_ACTOR_ID     SubjectType = 1
	SubjectType_SUBJECT_TYPE_PHONE_NUMBER SubjectType = 2
	SubjectType_SUBJECT_TYPE_EMAIL_ID     SubjectType = 3
)

// Enum value maps for SubjectType.
var (
	SubjectType_name = map[int32]string{
		0: "SUBJECT_TYPE_UNSPECIFIED",
		1: "SUBJECT_TYPE_ACTOR_ID",
		2: "SUBJECT_TYPE_PHONE_NUMBER",
		3: "SUBJECT_TYPE_EMAIL_ID",
	}
	SubjectType_value = map[string]int32{
		"SUBJECT_TYPE_UNSPECIFIED":  0,
		"SUBJECT_TYPE_ACTOR_ID":     1,
		"SUBJECT_TYPE_PHONE_NUMBER": 2,
		"SUBJECT_TYPE_EMAIL_ID":     3,
	}
)

func (x SubjectType) Enum() *SubjectType {
	p := new(SubjectType)
	*p = x
	return p
}

func (x SubjectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubjectType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_token_enums_proto_enumTypes[0].Descriptor()
}

func (SubjectType) Type() protoreflect.EnumType {
	return &file_api_auth_token_enums_proto_enumTypes[0]
}

func (x SubjectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubjectType.Descriptor instead.
func (SubjectType) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_token_enums_proto_rawDescGZIP(), []int{0}
}

var File_api_auth_token_enums_proto protoreflect.FileDescriptor

var file_api_auth_token_enums_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x2a, 0x80, 0x01, 0x0a, 0x0b, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x55, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x55, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19,
	0x53, 0x55, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x48, 0x4f,
	0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x53,
	0x55, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5a,
	0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68,
	0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_token_enums_proto_rawDescOnce sync.Once
	file_api_auth_token_enums_proto_rawDescData = file_api_auth_token_enums_proto_rawDesc
)

func file_api_auth_token_enums_proto_rawDescGZIP() []byte {
	file_api_auth_token_enums_proto_rawDescOnce.Do(func() {
		file_api_auth_token_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_token_enums_proto_rawDescData)
	})
	return file_api_auth_token_enums_proto_rawDescData
}

var file_api_auth_token_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_token_enums_proto_goTypes = []interface{}{
	(SubjectType)(0), // 0: token.SubjectType
}
var file_api_auth_token_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_auth_token_enums_proto_init() }
func file_api_auth_token_enums_proto_init() {
	if File_api_auth_token_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_token_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_token_enums_proto_goTypes,
		DependencyIndexes: file_api_auth_token_enums_proto_depIdxs,
		EnumInfos:         file_api_auth_token_enums_proto_enumTypes,
	}.Build()
	File_api_auth_token_enums_proto = out.File
	file_api_auth_token_enums_proto_rawDesc = nil
	file_api_auth_token_enums_proto_goTypes = nil
	file_api_auth_token_enums_proto_depIdxs = nil
}
