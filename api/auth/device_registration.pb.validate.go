// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/internal/device_registration.proto

package auth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on DeviceRegistration with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeviceRegistration) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceRegistration with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeviceRegistrationMultiError, or nil if none found.
func (m *DeviceRegistration) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceRegistration) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeviceId

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceRegistrationValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceRegistrationValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceRegistrationValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeviceToken

	// no validation rules for UserProfileId

	// no validation rules for Status

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceRegistrationValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceRegistrationValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceRegistrationValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceRegistrationValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceRegistrationValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceRegistrationValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceRegistrationValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceRegistrationValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceRegistrationValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AttemptId

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceRegistrationValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceRegistrationValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceRegistrationValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeletedAtUnix

	// no validation rules for SimId

	// no validation rules for VendorDeviceId

	// no validation rules for Vendor

	if len(errors) > 0 {
		return DeviceRegistrationMultiError(errors)
	}

	return nil
}

// DeviceRegistrationMultiError is an error wrapping multiple validation errors
// returned by DeviceRegistration.ValidateAll() if the designated constraints
// aren't met.
type DeviceRegistrationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceRegistrationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceRegistrationMultiError) AllErrors() []error { return m }

// DeviceRegistrationValidationError is the validation error returned by
// DeviceRegistration.Validate if the designated constraints aren't met.
type DeviceRegistrationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceRegistrationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceRegistrationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceRegistrationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceRegistrationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceRegistrationValidationError) ErrorName() string {
	return "DeviceRegistrationValidationError"
}

// Error satisfies the builtin error interface
func (e DeviceRegistrationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceRegistration.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceRegistrationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceRegistrationValidationError{}

// Validate checks the field values on DeviceRegistrationMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeviceRegistrationMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceRegistrationMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeviceRegistrationMetadataMultiError, or nil if none found.
func (m *DeviceRegistrationMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceRegistrationMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	if len(errors) > 0 {
		return DeviceRegistrationMetadataMultiError(errors)
	}

	return nil
}

// DeviceRegistrationMetadataMultiError is an error wrapping multiple
// validation errors returned by DeviceRegistrationMetadata.ValidateAll() if
// the designated constraints aren't met.
type DeviceRegistrationMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceRegistrationMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceRegistrationMetadataMultiError) AllErrors() []error { return m }

// DeviceRegistrationMetadataValidationError is the validation error returned
// by DeviceRegistrationMetadata.Validate if the designated constraints aren't met.
type DeviceRegistrationMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceRegistrationMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceRegistrationMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceRegistrationMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceRegistrationMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceRegistrationMetadataValidationError) ErrorName() string {
	return "DeviceRegistrationMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e DeviceRegistrationMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceRegistrationMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceRegistrationMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceRegistrationMetadataValidationError{}
