// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/auth_factor_update.proto

package auth

import (
	afu "github.com/epifi/gamma/api/auth/afu"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents next action to be taken by the caller in
// Auth Factor Update Process.
type AuthFactorUpdateAction int32

const (
	AuthFactorUpdateAction_ACTION_UNSPECIFIED AuthFactorUpdateAction = 0
	// No action to take
	AuthFactorUpdateAction_NO_ACTION AuthFactorUpdateAction = 1
	// Do Liveness & face match Validation
	AuthFactorUpdateAction_LIVENESS_AND_FACEMATCH AuthFactorUpdateAction = 2
	// Do ATM PIN Validation
	AuthFactorUpdateAction_ATM_PIN_VALIDATION AuthFactorUpdateAction = 4
	// Do user confirmation. This action is performed after all credentials are verified.
	// Screen is opened on client asking the user to confirm the changes.
	// [Screen](https://docs.google.com/document/d/1V83WIwb8JDQyVfmvfbEm9RstciDwvmxG4XTmdbVYAKk/edit#bookmark=id.wjmw9e234dvg)
	AuthFactorUpdateAction_USER_CONFIRMATION AuthFactorUpdateAction = 5
	// Do device registration
	AuthFactorUpdateAction_DEVICE_REGISTRATION AuthFactorUpdateAction = 6
	// This action is for the orchestrator(Frontend svc) and NOT for the client.
	// The action means new values have to be updated in the user profile after
	// the AFU verification process was successful.
	AuthFactorUpdateAction_UPDATE_USER_PROFILE AuthFactorUpdateAction = 7
	// Update Auth factor details at vendor end
	AuthFactorUpdateAction_VENDOR_UPDATE AuthFactorUpdateAction = 8
	// Update device details in our DB
	AuthFactorUpdateAction_UPDATE_DEVICE AuthFactorUpdateAction = 9
	// Delete current device details. These action is performed before starting VENDOR_UPDATE action.
	// This is done in order to restrict user from logging into the account while AFU is in progress.
	AuthFactorUpdateAction_DEREGISTER_CURRENT_DEVICE AuthFactorUpdateAction = 10
	// publish an event to notify subscribers about afu
	AuthFactorUpdateAction_PUBLISH_AFU_COMPLETION_EVENT AuthFactorUpdateAction = 11
	// Updates user's card state as active upon successful AFU
	// This action is performed only if card was previously in inactive state
	AuthFactorUpdateAction_UPDATE_USER_CARD_STATE AuthFactorUpdateAction = 12
	// This action is to display validation options in the actor authentication flow
	AuthFactorUpdateAction_ACTOR_AUTHENTICATION_LEVEL AuthFactorUpdateAction = 13
	// This action is used to show terminal/intermediate failures
	AuthFactorUpdateAction_SHOW_FAILURE AuthFactorUpdateAction = 14
	// Do EKYC for user
	AuthFactorUpdateAction_EKYC AuthFactorUpdateAction = 15
)

// Enum value maps for AuthFactorUpdateAction.
var (
	AuthFactorUpdateAction_name = map[int32]string{
		0:  "ACTION_UNSPECIFIED",
		1:  "NO_ACTION",
		2:  "LIVENESS_AND_FACEMATCH",
		4:  "ATM_PIN_VALIDATION",
		5:  "USER_CONFIRMATION",
		6:  "DEVICE_REGISTRATION",
		7:  "UPDATE_USER_PROFILE",
		8:  "VENDOR_UPDATE",
		9:  "UPDATE_DEVICE",
		10: "DEREGISTER_CURRENT_DEVICE",
		11: "PUBLISH_AFU_COMPLETION_EVENT",
		12: "UPDATE_USER_CARD_STATE",
		13: "ACTOR_AUTHENTICATION_LEVEL",
		14: "SHOW_FAILURE",
		15: "EKYC",
	}
	AuthFactorUpdateAction_value = map[string]int32{
		"ACTION_UNSPECIFIED":           0,
		"NO_ACTION":                    1,
		"LIVENESS_AND_FACEMATCH":       2,
		"ATM_PIN_VALIDATION":           4,
		"USER_CONFIRMATION":            5,
		"DEVICE_REGISTRATION":          6,
		"UPDATE_USER_PROFILE":          7,
		"VENDOR_UPDATE":                8,
		"UPDATE_DEVICE":                9,
		"DEREGISTER_CURRENT_DEVICE":    10,
		"PUBLISH_AFU_COMPLETION_EVENT": 11,
		"UPDATE_USER_CARD_STATE":       12,
		"ACTOR_AUTHENTICATION_LEVEL":   13,
		"SHOW_FAILURE":                 14,
		"EKYC":                         15,
	}
)

func (x AuthFactorUpdateAction) Enum() *AuthFactorUpdateAction {
	p := new(AuthFactorUpdateAction)
	*p = x
	return p
}

func (x AuthFactorUpdateAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthFactorUpdateAction) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_auth_factor_update_proto_enumTypes[0].Descriptor()
}

func (AuthFactorUpdateAction) Type() protoreflect.EnumType {
	return &file_api_auth_auth_factor_update_proto_enumTypes[0]
}

func (x AuthFactorUpdateAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthFactorUpdateAction.Descriptor instead.
func (AuthFactorUpdateAction) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_auth_factor_update_proto_rawDescGZIP(), []int{0}
}

type AuthFactorUpdateNextActionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Client's next action deeplink is derived from this.
	// Returns the next AFU action that is required to progress AFU flow
	NextAction AuthFactorUpdateAction `protobuf:"varint,1,opt,name=next_action,json=nextAction,proto3,enum=auth.AuthFactorUpdateAction" json:"next_action,omitempty"`
	// Sub actions are used to build the CTA deeplinks in the UI
	// to govern the direction of AFU flow
	NextSubActions []AuthFactorUpdateAction `protobuf:"varint,2,rep,packed,name=next_sub_actions,json=nextSubActions,proto3,enum=auth.AuthFactorUpdateAction" json:"next_sub_actions,omitempty"`
	// Represents the reason for the attempt failure.
	// It provides necessary hints to build the UI with custom messages
	FailureReason afu.FailureReason  `protobuf:"varint,3,opt,name=failure_reason,json=failureReason,proto3,enum=auth.afu.FailureReason" json:"failure_reason,omitempty"`
	Deeplink      *deeplink.Deeplink `protobuf:"bytes,4,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *AuthFactorUpdateNextActionDetails) Reset() {
	*x = AuthFactorUpdateNextActionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_auth_factor_update_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthFactorUpdateNextActionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthFactorUpdateNextActionDetails) ProtoMessage() {}

func (x *AuthFactorUpdateNextActionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_auth_factor_update_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthFactorUpdateNextActionDetails.ProtoReflect.Descriptor instead.
func (*AuthFactorUpdateNextActionDetails) Descriptor() ([]byte, []int) {
	return file_api_auth_auth_factor_update_proto_rawDescGZIP(), []int{0}
}

func (x *AuthFactorUpdateNextActionDetails) GetNextAction() AuthFactorUpdateAction {
	if x != nil {
		return x.NextAction
	}
	return AuthFactorUpdateAction_ACTION_UNSPECIFIED
}

func (x *AuthFactorUpdateNextActionDetails) GetNextSubActions() []AuthFactorUpdateAction {
	if x != nil {
		return x.NextSubActions
	}
	return nil
}

func (x *AuthFactorUpdateNextActionDetails) GetFailureReason() afu.FailureReason {
	if x != nil {
		return x.FailureReason
	}
	return afu.FailureReason(0)
}

func (x *AuthFactorUpdateNextActionDetails) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

var File_api_auth_auth_factor_update_proto protoreflect.FileDescriptor

var file_api_auth_auth_factor_update_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x04, 0x61, 0x75, 0x74, 0x68, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x75, 0x74, 0x68, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x61, 0x75, 0x74,
	0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa3, 0x02, 0x0a, 0x21,
	0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x3d, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x41, 0x75,
	0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x46, 0x0a, 0x10, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x6e, 0x65, 0x78, 0x74, 0x53, 0x75,
	0x62, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3e, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2a, 0xfb, 0x02, 0x0a, 0x16, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x12,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f,
	0x41, 0x4e, 0x44, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x02, 0x12,
	0x16, 0x0a, 0x12, 0x41, 0x54, 0x4d, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x17,
	0x0a, 0x13, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x07,
	0x12, 0x11, 0x0a, 0x0d, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x08, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45,
	0x56, 0x49, 0x43, 0x45, 0x10, 0x09, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x45, 0x52, 0x45, 0x47, 0x49,
	0x53, 0x54, 0x45, 0x52, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x56,
	0x49, 0x43, 0x45, 0x10, 0x0a, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48,
	0x5f, 0x41, 0x46, 0x55, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x45, 0x56, 0x45, 0x4e, 0x54, 0x10, 0x0b, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x10, 0x0c, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x41, 0x55, 0x54,
	0x48, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45,
	0x4c, 0x10, 0x0d, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x48, 0x4f, 0x57, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x55, 0x52, 0x45, 0x10, 0x0e, 0x12, 0x08, 0x0a, 0x04, 0x45, 0x4b, 0x59, 0x43, 0x10, 0x0f, 0x42,
	0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x75, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_auth_factor_update_proto_rawDescOnce sync.Once
	file_api_auth_auth_factor_update_proto_rawDescData = file_api_auth_auth_factor_update_proto_rawDesc
)

func file_api_auth_auth_factor_update_proto_rawDescGZIP() []byte {
	file_api_auth_auth_factor_update_proto_rawDescOnce.Do(func() {
		file_api_auth_auth_factor_update_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_auth_factor_update_proto_rawDescData)
	})
	return file_api_auth_auth_factor_update_proto_rawDescData
}

var file_api_auth_auth_factor_update_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_auth_factor_update_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_auth_auth_factor_update_proto_goTypes = []interface{}{
	(AuthFactorUpdateAction)(0),               // 0: auth.AuthFactorUpdateAction
	(*AuthFactorUpdateNextActionDetails)(nil), // 1: auth.AuthFactorUpdateNextActionDetails
	(afu.FailureReason)(0),                    // 2: auth.afu.FailureReason
	(*deeplink.Deeplink)(nil),                 // 3: frontend.deeplink.Deeplink
}
var file_api_auth_auth_factor_update_proto_depIdxs = []int32{
	0, // 0: auth.AuthFactorUpdateNextActionDetails.next_action:type_name -> auth.AuthFactorUpdateAction
	0, // 1: auth.AuthFactorUpdateNextActionDetails.next_sub_actions:type_name -> auth.AuthFactorUpdateAction
	2, // 2: auth.AuthFactorUpdateNextActionDetails.failure_reason:type_name -> auth.afu.FailureReason
	3, // 3: auth.AuthFactorUpdateNextActionDetails.deeplink:type_name -> frontend.deeplink.Deeplink
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_auth_auth_factor_update_proto_init() }
func file_api_auth_auth_factor_update_proto_init() {
	if File_api_auth_auth_factor_update_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_auth_factor_update_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthFactorUpdateNextActionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_auth_factor_update_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_auth_factor_update_proto_goTypes,
		DependencyIndexes: file_api_auth_auth_factor_update_proto_depIdxs,
		EnumInfos:         file_api_auth_auth_factor_update_proto_enumTypes,
		MessageInfos:      file_api_auth_auth_factor_update_proto_msgTypes,
	}.Build()
	File_api_auth_auth_factor_update_proto = out.File
	file_api_auth_auth_factor_update_proto_rawDesc = nil
	file_api_auth_auth_factor_update_proto_goTypes = nil
	file_api_auth_auth_factor_update_proto_depIdxs = nil
}
