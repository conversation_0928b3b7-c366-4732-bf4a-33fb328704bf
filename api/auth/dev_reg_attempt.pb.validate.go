// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/internal/dev_reg_attempt.proto

package auth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on DeviceRegistrationAttempt with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeviceRegistrationAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceRegistrationAttempt with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeviceRegistrationAttemptMultiError, or nil if none found.
func (m *DeviceRegistrationAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceRegistrationAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttemptId

	// no validation rules for DeviceId

	// no validation rules for ActorId

	// no validation rules for PhoneNumber

	// no validation rules for RequestId

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetSmsInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceRegistrationAttemptValidationError{
					field:  "SmsInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceRegistrationAttemptValidationError{
					field:  "SmsInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSmsInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceRegistrationAttemptValidationError{
				field:  "SmsInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceRegistrationAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceRegistrationAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceRegistrationAttemptValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceRegistrationAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceRegistrationAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceRegistrationAttemptValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeviceRegistrationAttemptMultiError(errors)
	}

	return nil
}

// DeviceRegistrationAttemptMultiError is an error wrapping multiple validation
// errors returned by DeviceRegistrationAttempt.ValidateAll() if the
// designated constraints aren't met.
type DeviceRegistrationAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceRegistrationAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceRegistrationAttemptMultiError) AllErrors() []error { return m }

// DeviceRegistrationAttemptValidationError is the validation error returned by
// DeviceRegistrationAttempt.Validate if the designated constraints aren't met.
type DeviceRegistrationAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceRegistrationAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceRegistrationAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceRegistrationAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceRegistrationAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceRegistrationAttemptValidationError) ErrorName() string {
	return "DeviceRegistrationAttemptValidationError"
}

// Error satisfies the builtin error interface
func (e DeviceRegistrationAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceRegistrationAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceRegistrationAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceRegistrationAttemptValidationError{}

// Validate checks the field values on DeviceRegistrationAttempt_SmsInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeviceRegistrationAttempt_SmsInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceRegistrationAttempt_SmsInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeviceRegistrationAttempt_SmsInfoMultiError, or nil if none found.
func (m *DeviceRegistrationAttempt_SmsInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceRegistrationAttempt_SmsInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSmsAckAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceRegistrationAttempt_SmsInfoValidationError{
					field:  "SmsAckAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceRegistrationAttempt_SmsInfoValidationError{
					field:  "SmsAckAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSmsAckAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceRegistrationAttempt_SmsInfoValidationError{
				field:  "SmsAckAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSmsAckNotifiedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceRegistrationAttempt_SmsInfoValidationError{
					field:  "SmsAckNotifiedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceRegistrationAttempt_SmsInfoValidationError{
					field:  "SmsAckNotifiedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSmsAckNotifiedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceRegistrationAttempt_SmsInfoValidationError{
				field:  "SmsAckNotifiedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeviceRegistrationAttempt_SmsInfoMultiError(errors)
	}

	return nil
}

// DeviceRegistrationAttempt_SmsInfoMultiError is an error wrapping multiple
// validation errors returned by
// DeviceRegistrationAttempt_SmsInfo.ValidateAll() if the designated
// constraints aren't met.
type DeviceRegistrationAttempt_SmsInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceRegistrationAttempt_SmsInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceRegistrationAttempt_SmsInfoMultiError) AllErrors() []error { return m }

// DeviceRegistrationAttempt_SmsInfoValidationError is the validation error
// returned by DeviceRegistrationAttempt_SmsInfo.Validate if the designated
// constraints aren't met.
type DeviceRegistrationAttempt_SmsInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceRegistrationAttempt_SmsInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceRegistrationAttempt_SmsInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceRegistrationAttempt_SmsInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceRegistrationAttempt_SmsInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceRegistrationAttempt_SmsInfoValidationError) ErrorName() string {
	return "DeviceRegistrationAttempt_SmsInfoValidationError"
}

// Error satisfies the builtin error interface
func (e DeviceRegistrationAttempt_SmsInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceRegistrationAttempt_SmsInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceRegistrationAttempt_SmsInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceRegistrationAttempt_SmsInfoValidationError{}
