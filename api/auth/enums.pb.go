//go:generate gen_sql -types=TokenDeletionReason

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/enums.proto

package auth

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TokenDeletionReason int32

const (
	TokenDeletionReason_TOKEN_DELETION_REASON_UNSPECIFIED    TokenDeletionReason = 0
	TokenDeletionReason_TOKEN_DELETION_REASON_EXPIRY         TokenDeletionReason = 1
	TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED TokenDeletionReason = 2
	// current token is deleted, to create a new token
	TokenDeletionReason_TOKEN_DELETION_REASON_TOKEN_REFRESH TokenDeletionReason = 3
	TokenDeletionReason_TOKEN_DELETION_REASON_SIGN_OUT      TokenDeletionReason = 4
	// current token is deleted to delete the current user
	TokenDeletionReason_TOKEN_DELETION_REASON_RESET_USER TokenDeletionReason = 5
	// Deleting the current token to create a new token with higher access level during onboarding
	TokenDeletionReason_TOKEN_DELETION_REASON_ONBOARDING_TOKEN_UPGRADE TokenDeletionReason = 6
	// Deleting the current token to create a new token with higher access level during re-onboarding
	TokenDeletionReason_TOKEN_DELETION_REASON_REONBOARDING_TOKEN_UPGRADE TokenDeletionReason = 7
	// In case of updating phone number in AFU (phone update, phone + device update), delete refresh token of user with old phone
	// This is handled in all other cases where we use same phone, as old refresh tokens are deleted
	TokenDeletionReason_TOKEN_DELETION_REASON_SIGN_OUT_OLD_PHONE_NUM_IN_AFU TokenDeletionReason = 8
)

// Enum value maps for TokenDeletionReason.
var (
	TokenDeletionReason_name = map[int32]string{
		0: "TOKEN_DELETION_REASON_UNSPECIFIED",
		1: "TOKEN_DELETION_REASON_EXPIRY",
		2: "TOKEN_DELETION_REASON_ACCESS_REVOKED",
		3: "TOKEN_DELETION_REASON_TOKEN_REFRESH",
		4: "TOKEN_DELETION_REASON_SIGN_OUT",
		5: "TOKEN_DELETION_REASON_RESET_USER",
		6: "TOKEN_DELETION_REASON_ONBOARDING_TOKEN_UPGRADE",
		7: "TOKEN_DELETION_REASON_REONBOARDING_TOKEN_UPGRADE",
		8: "TOKEN_DELETION_REASON_SIGN_OUT_OLD_PHONE_NUM_IN_AFU",
	}
	TokenDeletionReason_value = map[string]int32{
		"TOKEN_DELETION_REASON_UNSPECIFIED":                   0,
		"TOKEN_DELETION_REASON_EXPIRY":                        1,
		"TOKEN_DELETION_REASON_ACCESS_REVOKED":                2,
		"TOKEN_DELETION_REASON_TOKEN_REFRESH":                 3,
		"TOKEN_DELETION_REASON_SIGN_OUT":                      4,
		"TOKEN_DELETION_REASON_RESET_USER":                    5,
		"TOKEN_DELETION_REASON_ONBOARDING_TOKEN_UPGRADE":      6,
		"TOKEN_DELETION_REASON_REONBOARDING_TOKEN_UPGRADE":    7,
		"TOKEN_DELETION_REASON_SIGN_OUT_OLD_PHONE_NUM_IN_AFU": 8,
	}
)

func (x TokenDeletionReason) Enum() *TokenDeletionReason {
	p := new(TokenDeletionReason)
	*p = x
	return p
}

func (x TokenDeletionReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TokenDeletionReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_enums_proto_enumTypes[0].Descriptor()
}

func (TokenDeletionReason) Type() protoreflect.EnumType {
	return &file_api_auth_enums_proto_enumTypes[0]
}

func (x TokenDeletionReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TokenDeletionReason.Descriptor instead.
func (TokenDeletionReason) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_enums_proto_rawDescGZIP(), []int{0}
}

var File_api_auth_enums_proto protoreflect.FileDescriptor

var file_api_auth_enums_proto_rawDesc = []byte{
	0x0a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x61, 0x75, 0x74, 0x68, 0x2a, 0x9e, 0x03, 0x0a,
	0x13, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x21, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x44, 0x45,
	0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x54,
	0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x59, 0x10, 0x01, 0x12, 0x28, 0x0a,
	0x24, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45,
	0x56, 0x4f, 0x4b, 0x45, 0x44, 0x10, 0x02, 0x12, 0x27, 0x0a, 0x23, 0x54, 0x4f, 0x4b, 0x45, 0x4e,
	0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48, 0x10, 0x03,
	0x12, 0x22, 0x0a, 0x1e, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x4f,
	0x55, 0x54, 0x10, 0x04, 0x12, 0x24, 0x0a, 0x20, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x44, 0x45,
	0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x53, 0x45, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x05, 0x12, 0x32, 0x0a, 0x2e, 0x54, 0x4f,
	0x4b, 0x45, 0x4e, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x54,
	0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x10, 0x06, 0x12, 0x34,
	0x0a, 0x30, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41,
	0x44, 0x45, 0x10, 0x07, 0x12, 0x37, 0x0a, 0x33, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x44, 0x45,
	0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x53, 0x49,
	0x47, 0x4e, 0x5f, 0x4f, 0x55, 0x54, 0x5f, 0x4f, 0x4c, 0x44, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45,
	0x5f, 0x4e, 0x55, 0x4d, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x46, 0x55, 0x10, 0x08, 0x42, 0x42, 0x0a,
	0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74,
	0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_enums_proto_rawDescOnce sync.Once
	file_api_auth_enums_proto_rawDescData = file_api_auth_enums_proto_rawDesc
)

func file_api_auth_enums_proto_rawDescGZIP() []byte {
	file_api_auth_enums_proto_rawDescOnce.Do(func() {
		file_api_auth_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_enums_proto_rawDescData)
	})
	return file_api_auth_enums_proto_rawDescData
}

var file_api_auth_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_enums_proto_goTypes = []interface{}{
	(TokenDeletionReason)(0), // 0: auth.TokenDeletionReason
}
var file_api_auth_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_auth_enums_proto_init() }
func file_api_auth_enums_proto_init() {
	if File_api_auth_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_enums_proto_goTypes,
		DependencyIndexes: file_api_auth_enums_proto_depIdxs,
		EnumInfos:         file_api_auth_enums_proto_enumTypes,
	}.Build()
	File_api_auth_enums_proto = out.File
	file_api_auth_enums_proto_rawDesc = nil
	file_api_auth_enums_proto_goTypes = nil
	file_api_auth_enums_proto_depIdxs = nil
}
