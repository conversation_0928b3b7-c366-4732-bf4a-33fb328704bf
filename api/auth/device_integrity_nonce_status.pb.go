// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/device_integrity_nonce_status.proto

package auth

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// NonceStatus represents the status of a nonce generated for device integrity verification.
// Nonce generated for device verification can only be used once and have a limited validity period after which they can't be used.
// When a nonce is generated, it is in `ACTIVE` state. In case, client uses this nonce during device verification, nonce's state is changed to `USED`
// If a nonce isn't used during its validity, its state is changed to `EXPIRED`
type NonceStatus int32

const (
	NonceStatus_NONCE_STATUS_UNSPECIFIED NonceStatus = 0
	NonceStatus_ACTIVE                   NonceStatus = 1
	NonceStatus_USED                     NonceStatus = 2
	NonceStatus_EXPIRED                  NonceStatus = 3
)

// Enum value maps for NonceStatus.
var (
	NonceStatus_name = map[int32]string{
		0: "NONCE_STATUS_UNSPECIFIED",
		1: "ACTIVE",
		2: "USED",
		3: "EXPIRED",
	}
	NonceStatus_value = map[string]int32{
		"NONCE_STATUS_UNSPECIFIED": 0,
		"ACTIVE":                   1,
		"USED":                     2,
		"EXPIRED":                  3,
	}
)

func (x NonceStatus) Enum() *NonceStatus {
	p := new(NonceStatus)
	*p = x
	return p
}

func (x NonceStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NonceStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_device_integrity_nonce_status_proto_enumTypes[0].Descriptor()
}

func (NonceStatus) Type() protoreflect.EnumType {
	return &file_api_auth_device_integrity_nonce_status_proto_enumTypes[0]
}

func (x NonceStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NonceStatus.Descriptor instead.
func (NonceStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_device_integrity_nonce_status_proto_rawDescGZIP(), []int{0}
}

var File_api_auth_device_integrity_nonce_status_proto protoreflect.FileDescriptor

var file_api_auth_device_integrity_nonce_status_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6e, 0x6f, 0x6e, 0x63,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04,
	0x61, 0x75, 0x74, 0x68, 0x2a, 0x4e, 0x0a, 0x0b, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x18, 0x4e, 0x4f, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x08, 0x0a,
	0x04, 0x55, 0x53, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50, 0x49, 0x52,
	0x45, 0x44, 0x10, 0x03, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_device_integrity_nonce_status_proto_rawDescOnce sync.Once
	file_api_auth_device_integrity_nonce_status_proto_rawDescData = file_api_auth_device_integrity_nonce_status_proto_rawDesc
)

func file_api_auth_device_integrity_nonce_status_proto_rawDescGZIP() []byte {
	file_api_auth_device_integrity_nonce_status_proto_rawDescOnce.Do(func() {
		file_api_auth_device_integrity_nonce_status_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_device_integrity_nonce_status_proto_rawDescData)
	})
	return file_api_auth_device_integrity_nonce_status_proto_rawDescData
}

var file_api_auth_device_integrity_nonce_status_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_device_integrity_nonce_status_proto_goTypes = []interface{}{
	(NonceStatus)(0), // 0: auth.NonceStatus
}
var file_api_auth_device_integrity_nonce_status_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_auth_device_integrity_nonce_status_proto_init() }
func file_api_auth_device_integrity_nonce_status_proto_init() {
	if File_api_auth_device_integrity_nonce_status_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_device_integrity_nonce_status_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_device_integrity_nonce_status_proto_goTypes,
		DependencyIndexes: file_api_auth_device_integrity_nonce_status_proto_depIdxs,
		EnumInfos:         file_api_auth_device_integrity_nonce_status_proto_enumTypes,
	}.Build()
	File_api_auth_device_integrity_nonce_status_proto = out.File
	file_api_auth_device_integrity_nonce_status_proto_rawDesc = nil
	file_api_auth_device_integrity_nonce_status_proto_goTypes = nil
	file_api_auth_device_integrity_nonce_status_proto_depIdxs = nil
}
