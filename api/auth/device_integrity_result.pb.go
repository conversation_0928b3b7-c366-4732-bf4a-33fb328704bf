// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/device_integrity_result.proto

package auth

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Result int32

const (
	Result_RESULT_UNSPECIFIED Result = 0
	// device passed all the checks
	Result_RESULT_PASSED Result = 1
	// device check is marked as passed when user accepts a warning consent
	Result_RESULT_PASSED_WITH_CONSENT Result = 2
	// device failed verification
	Result_RESULT_FAILED Result = 3
)

// Enum value maps for Result.
var (
	Result_name = map[int32]string{
		0: "RESULT_UNSPECIFIED",
		1: "RESULT_PASSED",
		2: "RESULT_PASSED_WITH_CONSENT",
		3: "RESULT_FAILED",
	}
	Result_value = map[string]int32{
		"RESULT_UNSPECIFIED":         0,
		"RESULT_PASSED":              1,
		"RESULT_PASSED_WITH_CONSENT": 2,
		"RESULT_FAILED":              3,
	}
)

func (x Result) Enum() *Result {
	p := new(Result)
	*p = x
	return p
}

func (x Result) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Result) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_device_integrity_result_proto_enumTypes[0].Descriptor()
}

func (Result) Type() protoreflect.EnumType {
	return &file_api_auth_device_integrity_result_proto_enumTypes[0]
}

func (x Result) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Result.Descriptor instead.
func (Result) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_device_integrity_result_proto_rawDescGZIP(), []int{0}
}

type FailureReason int32

const (
	FailureReason_FAILURE_REASON_UNSPECIFIED FailureReason = 0
	// error while extracting attestaion payload data
	FailureReason_ERROR_EXTRACTING_ATTESTATION_PAYLOAD FailureReason = 1
	// attestaion creation failed at safetynet service end
	FailureReason_SAFETYNET_ATTESTATION_CREATION_FAILURE FailureReason = 2
	// device failed basic integrity test
	FailureReason_SAFETYNET_BASIC_INTEGRITY_TEST_FAILED FailureReason = 3
	// attestation has expired
	FailureReason_ATTESTATION_EXPIRED FailureReason = 4
	// nonce is either not present in DB or has expired
	FailureReason_ACTIVE_NONCE_NOT_FOUND        FailureReason = 5
	FailureReason_APK_PKG_NAME_DOES_NOT_MATCH   FailureReason = 6
	FailureReason_APK_CERT_ARRAY_EMPTY          FailureReason = 7
	FailureReason_APK_CERT_ARRAY_DOES_NOT_MATCH FailureReason = 8
	// device failed cts profile match test
	FailureReason_SAFETYNET_CTS_PROFILE_MATCH_TEST_FAILED FailureReason = 9
	// public key not found for device
	FailureReason_PUBLIC_KEY_NOT_FOUND FailureReason = 10
	// unexpected error in fetching public key from DB
	FailureReason_ERROR_FETCHING_PUBLIC_KEY FailureReason = 11
	// error while unmarshalling public key
	FailureReason_ERROR_UNMARSHALLING_PUBLIC_KEY FailureReason = 12
	// error in decoding CBOR-encoded assertion object
	FailureReason_ERROR_DECODING_ASSERTION_OBJECT FailureReason = 13
	// failed to validate assertion signature
	FailureReason_ASSERTION_SIGNATURE_VALIDATION_FAILED FailureReason = 14
	// failed to validate auth data present in assertion object
	FailureReason_ASSERTION_AUTH_DATA_VALIDATION_FAILED FailureReason = 15
	// error in decoding nonce from attestation object
	FailureReason_ERROR_DECODING_NONCE_FROM_ATTESTATION FailureReason = 16
	// android device failed play integrity basic integrity test
	FailureReason_PLAY_INTEGRITY_BASIC_INTEGRITY_TEST_FAILED FailureReason = 17
	// android device failed play integrity cts profile match test
	FailureReason_PLAY_INTEGRITY_CTS_PROFILE_MATCH_TEST_FAILED FailureReason = 18
)

// Enum value maps for FailureReason.
var (
	FailureReason_name = map[int32]string{
		0:  "FAILURE_REASON_UNSPECIFIED",
		1:  "ERROR_EXTRACTING_ATTESTATION_PAYLOAD",
		2:  "SAFETYNET_ATTESTATION_CREATION_FAILURE",
		3:  "SAFETYNET_BASIC_INTEGRITY_TEST_FAILED",
		4:  "ATTESTATION_EXPIRED",
		5:  "ACTIVE_NONCE_NOT_FOUND",
		6:  "APK_PKG_NAME_DOES_NOT_MATCH",
		7:  "APK_CERT_ARRAY_EMPTY",
		8:  "APK_CERT_ARRAY_DOES_NOT_MATCH",
		9:  "SAFETYNET_CTS_PROFILE_MATCH_TEST_FAILED",
		10: "PUBLIC_KEY_NOT_FOUND",
		11: "ERROR_FETCHING_PUBLIC_KEY",
		12: "ERROR_UNMARSHALLING_PUBLIC_KEY",
		13: "ERROR_DECODING_ASSERTION_OBJECT",
		14: "ASSERTION_SIGNATURE_VALIDATION_FAILED",
		15: "ASSERTION_AUTH_DATA_VALIDATION_FAILED",
		16: "ERROR_DECODING_NONCE_FROM_ATTESTATION",
		17: "PLAY_INTEGRITY_BASIC_INTEGRITY_TEST_FAILED",
		18: "PLAY_INTEGRITY_CTS_PROFILE_MATCH_TEST_FAILED",
	}
	FailureReason_value = map[string]int32{
		"FAILURE_REASON_UNSPECIFIED":                   0,
		"ERROR_EXTRACTING_ATTESTATION_PAYLOAD":         1,
		"SAFETYNET_ATTESTATION_CREATION_FAILURE":       2,
		"SAFETYNET_BASIC_INTEGRITY_TEST_FAILED":        3,
		"ATTESTATION_EXPIRED":                          4,
		"ACTIVE_NONCE_NOT_FOUND":                       5,
		"APK_PKG_NAME_DOES_NOT_MATCH":                  6,
		"APK_CERT_ARRAY_EMPTY":                         7,
		"APK_CERT_ARRAY_DOES_NOT_MATCH":                8,
		"SAFETYNET_CTS_PROFILE_MATCH_TEST_FAILED":      9,
		"PUBLIC_KEY_NOT_FOUND":                         10,
		"ERROR_FETCHING_PUBLIC_KEY":                    11,
		"ERROR_UNMARSHALLING_PUBLIC_KEY":               12,
		"ERROR_DECODING_ASSERTION_OBJECT":              13,
		"ASSERTION_SIGNATURE_VALIDATION_FAILED":        14,
		"ASSERTION_AUTH_DATA_VALIDATION_FAILED":        15,
		"ERROR_DECODING_NONCE_FROM_ATTESTATION":        16,
		"PLAY_INTEGRITY_BASIC_INTEGRITY_TEST_FAILED":   17,
		"PLAY_INTEGRITY_CTS_PROFILE_MATCH_TEST_FAILED": 18,
	}
)

func (x FailureReason) Enum() *FailureReason {
	p := new(FailureReason)
	*p = x
	return p
}

func (x FailureReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FailureReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_device_integrity_result_proto_enumTypes[1].Descriptor()
}

func (FailureReason) Type() protoreflect.EnumType {
	return &file_api_auth_device_integrity_result_proto_enumTypes[1]
}

func (x FailureReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FailureReason.Descriptor instead.
func (FailureReason) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_device_integrity_result_proto_rawDescGZIP(), []int{1}
}

type DeviceIntegrityResultFieldMask int32

const (
	DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_FIELD_MASK_UNSPECIFIED DeviceIntegrityResultFieldMask = 0
	DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_ID                     DeviceIntegrityResultFieldMask = 1
	DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_ACTOR_ID               DeviceIntegrityResultFieldMask = 2
	DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_DEVICE_ID              DeviceIntegrityResultFieldMask = 3
	DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_ATTESTATION            DeviceIntegrityResultFieldMask = 4
	DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_RESULT                 DeviceIntegrityResultFieldMask = 5
	DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_ADVICE                 DeviceIntegrityResultFieldMask = 6
	DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_FAILURE_REASON         DeviceIntegrityResultFieldMask = 7
	DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_DEVICE_OS              DeviceIntegrityResultFieldMask = 8
	DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_CREATED_AT             DeviceIntegrityResultFieldMask = 9
	DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_UPDATED_AT             DeviceIntegrityResultFieldMask = 10
	DeviceIntegrityResultFieldMask_DEVICE_INTEGRITY_RESULT_DELETED_AT             DeviceIntegrityResultFieldMask = 11
)

// Enum value maps for DeviceIntegrityResultFieldMask.
var (
	DeviceIntegrityResultFieldMask_name = map[int32]string{
		0:  "DEVICE_INTEGRITY_RESULT_FIELD_MASK_UNSPECIFIED",
		1:  "DEVICE_INTEGRITY_RESULT_ID",
		2:  "DEVICE_INTEGRITY_RESULT_ACTOR_ID",
		3:  "DEVICE_INTEGRITY_RESULT_DEVICE_ID",
		4:  "DEVICE_INTEGRITY_RESULT_ATTESTATION",
		5:  "DEVICE_INTEGRITY_RESULT_RESULT",
		6:  "DEVICE_INTEGRITY_RESULT_ADVICE",
		7:  "DEVICE_INTEGRITY_RESULT_FAILURE_REASON",
		8:  "DEVICE_INTEGRITY_RESULT_DEVICE_OS",
		9:  "DEVICE_INTEGRITY_RESULT_CREATED_AT",
		10: "DEVICE_INTEGRITY_RESULT_UPDATED_AT",
		11: "DEVICE_INTEGRITY_RESULT_DELETED_AT",
	}
	DeviceIntegrityResultFieldMask_value = map[string]int32{
		"DEVICE_INTEGRITY_RESULT_FIELD_MASK_UNSPECIFIED": 0,
		"DEVICE_INTEGRITY_RESULT_ID":                     1,
		"DEVICE_INTEGRITY_RESULT_ACTOR_ID":               2,
		"DEVICE_INTEGRITY_RESULT_DEVICE_ID":              3,
		"DEVICE_INTEGRITY_RESULT_ATTESTATION":            4,
		"DEVICE_INTEGRITY_RESULT_RESULT":                 5,
		"DEVICE_INTEGRITY_RESULT_ADVICE":                 6,
		"DEVICE_INTEGRITY_RESULT_FAILURE_REASON":         7,
		"DEVICE_INTEGRITY_RESULT_DEVICE_OS":              8,
		"DEVICE_INTEGRITY_RESULT_CREATED_AT":             9,
		"DEVICE_INTEGRITY_RESULT_UPDATED_AT":             10,
		"DEVICE_INTEGRITY_RESULT_DELETED_AT":             11,
	}
)

func (x DeviceIntegrityResultFieldMask) Enum() *DeviceIntegrityResultFieldMask {
	p := new(DeviceIntegrityResultFieldMask)
	*p = x
	return p
}

func (x DeviceIntegrityResultFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceIntegrityResultFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_device_integrity_result_proto_enumTypes[2].Descriptor()
}

func (DeviceIntegrityResultFieldMask) Type() protoreflect.EnumType {
	return &file_api_auth_device_integrity_result_proto_enumTypes[2]
}

func (x DeviceIntegrityResultFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceIntegrityResultFieldMask.Descriptor instead.
func (DeviceIntegrityResultFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_device_integrity_result_proto_rawDescGZIP(), []int{2}
}

// Proto for DeviceIntegrityResult model
type DeviceIntegrityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// uuid based unique id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// primary identifier to the actor table. device integrity verification was done for this entity.
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// identifier for the device on which this test was done.
	DeviceId string `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// attestation received from client
	Attestation string `protobuf:"bytes,4,opt,name=attestation,proto3" json:"attestation,omitempty"`
	// os type of client ex. android, ios
	DeviceOs common.Platform `protobuf:"varint,5,opt,name=device_os,json=deviceOs,proto3,enum=api.typesv2.common.Platform" json:"device_os,omitempty"`
	// outcome of the verification
	Result Result `protobuf:"varint,6,opt,name=result,proto3,enum=auth.Result" json:"result,omitempty"`
	// suggestion for how to get a device back into a good state
	Advice string `protobuf:"bytes,7,opt,name=advice,proto3" json:"advice,omitempty"`
	// reason why device verification failed. it will be UNSPECIFIED if device passes verification
	FailureReason FailureReason          `protobuf:"varint,8,opt,name=failure_reason,json=failureReason,proto3,enum=auth.FailureReason" json:"failure_reason,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt     *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *DeviceIntegrityResult) Reset() {
	*x = DeviceIntegrityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_device_integrity_result_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceIntegrityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceIntegrityResult) ProtoMessage() {}

func (x *DeviceIntegrityResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_device_integrity_result_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceIntegrityResult.ProtoReflect.Descriptor instead.
func (*DeviceIntegrityResult) Descriptor() ([]byte, []int) {
	return file_api_auth_device_integrity_result_proto_rawDescGZIP(), []int{0}
}

func (x *DeviceIntegrityResult) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeviceIntegrityResult) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DeviceIntegrityResult) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *DeviceIntegrityResult) GetAttestation() string {
	if x != nil {
		return x.Attestation
	}
	return ""
}

func (x *DeviceIntegrityResult) GetDeviceOs() common.Platform {
	if x != nil {
		return x.DeviceOs
	}
	return common.Platform(0)
}

func (x *DeviceIntegrityResult) GetResult() Result {
	if x != nil {
		return x.Result
	}
	return Result_RESULT_UNSPECIFIED
}

func (x *DeviceIntegrityResult) GetAdvice() string {
	if x != nil {
		return x.Advice
	}
	return ""
}

func (x *DeviceIntegrityResult) GetFailureReason() FailureReason {
	if x != nil {
		return x.FailureReason
	}
	return FailureReason_FAILURE_REASON_UNSPECIFIED
}

func (x *DeviceIntegrityResult) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DeviceIntegrityResult) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *DeviceIntegrityResult) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_auth_device_integrity_result_proto protoreflect.FileDescriptor

var file_api_auth_device_integrity_result_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x61, 0x75, 0x74, 0x68, 0x1a, 0x1f,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xe7, 0x03, 0x0a, 0x15, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x74, 0x74, 0x65, 0x73, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x65, 0x73, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6f,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x12,
	0x24, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x64, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x64, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a,
	0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0x66, 0x0a, 0x06, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d,
	0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x45, 0x44, 0x10, 0x01, 0x12,
	0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x45, 0x44,
	0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12,
	0x11, 0x0a, 0x0d, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x03, 0x2a, 0xd5, 0x05, 0x0a, 0x0d, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x28, 0x0a, 0x24, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x45, 0x58,
	0x54, 0x52, 0x41, 0x43, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x53, 0x54, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x01, 0x12, 0x2a,
	0x0a, 0x26, 0x53, 0x41, 0x46, 0x45, 0x54, 0x59, 0x4e, 0x45, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x45,
	0x53, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x12, 0x29, 0x0a, 0x25, 0x53, 0x41,
	0x46, 0x45, 0x54, 0x59, 0x4e, 0x45, 0x54, 0x5f, 0x42, 0x41, 0x53, 0x49, 0x43, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x54, 0x54, 0x45, 0x53, 0x54, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x04, 0x12, 0x1a,
	0x0a, 0x16, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x43, 0x45, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x50,
	0x4b, 0x5f, 0x50, 0x4b, 0x47, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x44, 0x4f, 0x45, 0x53, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x41,
	0x50, 0x4b, 0x5f, 0x43, 0x45, 0x52, 0x54, 0x5f, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x45, 0x4d,
	0x50, 0x54, 0x59, 0x10, 0x07, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x50, 0x4b, 0x5f, 0x43, 0x45, 0x52,
	0x54, 0x5f, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x44, 0x4f, 0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x08, 0x12, 0x2b, 0x0a, 0x27, 0x53, 0x41, 0x46, 0x45,
	0x54, 0x59, 0x4e, 0x45, 0x54, 0x5f, 0x43, 0x54, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0x09, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f,
	0x4b, 0x45, 0x59, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x0a, 0x12,
	0x1d, 0x0a, 0x19, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x49, 0x4e,
	0x47, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x4b, 0x45, 0x59, 0x10, 0x0b, 0x12, 0x22,
	0x0a, 0x1e, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x4d, 0x41, 0x52, 0x53, 0x48, 0x41,
	0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x4b, 0x45, 0x59,
	0x10, 0x0c, 0x12, 0x23, 0x0a, 0x1f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x43, 0x4f,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x52, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f,
	0x42, 0x4a, 0x45, 0x43, 0x54, 0x10, 0x0d, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x53, 0x53, 0x45, 0x52,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x0e, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x53, 0x53, 0x45, 0x52, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x41, 0x55, 0x54, 0x48, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x0f, 0x12, 0x29, 0x0a,
	0x25, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x43, 0x4f, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x4e, 0x4f, 0x4e, 0x43, 0x45, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x53,
	0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x10, 0x12, 0x2e, 0x0a, 0x2a, 0x50, 0x4c, 0x41, 0x59,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x42, 0x41, 0x53, 0x49, 0x43,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x45, 0x53, 0x54, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x11, 0x12, 0x30, 0x0a, 0x2c, 0x50, 0x4c, 0x41, 0x59,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x54, 0x53, 0x5f, 0x50,
	0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x45, 0x53,
	0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x12, 0x2a, 0xfd, 0x03, 0x0a, 0x1e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x32, 0x0a,
	0x2e, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x49, 0x54,
	0x59, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x49, 0x44, 0x10,
	0x01, 0x12, 0x24, 0x0a, 0x20, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x41, 0x43, 0x54,
	0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21, 0x44, 0x45, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x53, 0x55,
	0x4c, 0x54, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x27,
	0x0a, 0x23, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x49,
	0x54, 0x59, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x53, 0x54,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x44, 0x45, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x53, 0x55,
	0x4c, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x10, 0x05, 0x12, 0x22, 0x0a, 0x1e, 0x44,
	0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x49, 0x54, 0x59, 0x5f,
	0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x41, 0x44, 0x56, 0x49, 0x43, 0x45, 0x10, 0x06, 0x12,
	0x2a, 0x0a, 0x26, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52,
	0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55,
	0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x25, 0x0a, 0x21, 0x44,
	0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x49, 0x54, 0x59, 0x5f,
	0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4f, 0x53,
	0x10, 0x08, 0x12, 0x26, 0x0a, 0x22, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x54,
	0x45, 0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x09, 0x12, 0x26, 0x0a, 0x22, 0x44, 0x45,
	0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x52,
	0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54,
	0x10, 0x0a, 0x12, 0x26, 0x0a, 0x22, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x54,
	0x45, 0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x44, 0x45,
	0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0b, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x5a, 0x1f, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_device_integrity_result_proto_rawDescOnce sync.Once
	file_api_auth_device_integrity_result_proto_rawDescData = file_api_auth_device_integrity_result_proto_rawDesc
)

func file_api_auth_device_integrity_result_proto_rawDescGZIP() []byte {
	file_api_auth_device_integrity_result_proto_rawDescOnce.Do(func() {
		file_api_auth_device_integrity_result_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_device_integrity_result_proto_rawDescData)
	})
	return file_api_auth_device_integrity_result_proto_rawDescData
}

var file_api_auth_device_integrity_result_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_auth_device_integrity_result_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_auth_device_integrity_result_proto_goTypes = []interface{}{
	(Result)(0),                         // 0: auth.Result
	(FailureReason)(0),                  // 1: auth.FailureReason
	(DeviceIntegrityResultFieldMask)(0), // 2: auth.DeviceIntegrityResultFieldMask
	(*DeviceIntegrityResult)(nil),       // 3: auth.DeviceIntegrityResult
	(common.Platform)(0),                // 4: api.typesv2.common.Platform
	(*timestamppb.Timestamp)(nil),       // 5: google.protobuf.Timestamp
}
var file_api_auth_device_integrity_result_proto_depIdxs = []int32{
	4, // 0: auth.DeviceIntegrityResult.device_os:type_name -> api.typesv2.common.Platform
	0, // 1: auth.DeviceIntegrityResult.result:type_name -> auth.Result
	1, // 2: auth.DeviceIntegrityResult.failure_reason:type_name -> auth.FailureReason
	5, // 3: auth.DeviceIntegrityResult.created_at:type_name -> google.protobuf.Timestamp
	5, // 4: auth.DeviceIntegrityResult.updated_at:type_name -> google.protobuf.Timestamp
	5, // 5: auth.DeviceIntegrityResult.deleted_at:type_name -> google.protobuf.Timestamp
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_auth_device_integrity_result_proto_init() }
func file_api_auth_device_integrity_result_proto_init() {
	if File_api_auth_device_integrity_result_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_device_integrity_result_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceIntegrityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_device_integrity_result_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_device_integrity_result_proto_goTypes,
		DependencyIndexes: file_api_auth_device_integrity_result_proto_depIdxs,
		EnumInfos:         file_api_auth_device_integrity_result_proto_enumTypes,
		MessageInfos:      file_api_auth_device_integrity_result_proto_msgTypes,
	}.Build()
	File_api_auth_device_integrity_result_proto = out.File
	file_api_auth_device_integrity_result_proto_rawDesc = nil
	file_api_auth_device_integrity_result_proto_goTypes = nil
	file_api_auth_device_integrity_result_proto_depIdxs = nil
}
