// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/internal/troubleshooting.proto

package afu

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	card "github.com/epifi/gamma/api/card"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = card.CardForm(0)
)

// Validate checks the field values on StageInvestigatorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StageInvestigatorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StageInvestigatorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StageInvestigatorRequestMultiError, or nil if none found.
func (m *StageInvestigatorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StageInvestigatorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAfuRecord()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageInvestigatorRequestValidationError{
					field:  "AfuRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageInvestigatorRequestValidationError{
					field:  "AfuRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAfuRecord()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageInvestigatorRequestValidationError{
				field:  "AfuRecord",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Stage

	if len(errors) > 0 {
		return StageInvestigatorRequestMultiError(errors)
	}

	return nil
}

// StageInvestigatorRequestMultiError is an error wrapping multiple validation
// errors returned by StageInvestigatorRequest.ValidateAll() if the designated
// constraints aren't met.
type StageInvestigatorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StageInvestigatorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StageInvestigatorRequestMultiError) AllErrors() []error { return m }

// StageInvestigatorRequestValidationError is the validation error returned by
// StageInvestigatorRequest.Validate if the designated constraints aren't met.
type StageInvestigatorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StageInvestigatorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StageInvestigatorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StageInvestigatorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StageInvestigatorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StageInvestigatorRequestValidationError) ErrorName() string {
	return "StageInvestigatorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StageInvestigatorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStageInvestigatorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StageInvestigatorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StageInvestigatorRequestValidationError{}

// Validate checks the field values on StageInvestigatorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StageInvestigatorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StageInvestigatorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StageInvestigatorResponseMultiError, or nil if none found.
func (m *StageInvestigatorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StageInvestigatorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Stage

	// no validation rules for StageStatus

	// no validation rules for DiagnosisCode

	// no validation rules for DiagnosisReport

	if len(errors) > 0 {
		return StageInvestigatorResponseMultiError(errors)
	}

	return nil
}

// StageInvestigatorResponseMultiError is an error wrapping multiple validation
// errors returned by StageInvestigatorResponse.ValidateAll() if the
// designated constraints aren't met.
type StageInvestigatorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StageInvestigatorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StageInvestigatorResponseMultiError) AllErrors() []error { return m }

// StageInvestigatorResponseValidationError is the validation error returned by
// StageInvestigatorResponse.Validate if the designated constraints aren't met.
type StageInvestigatorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StageInvestigatorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StageInvestigatorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StageInvestigatorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StageInvestigatorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StageInvestigatorResponseValidationError) ErrorName() string {
	return "StageInvestigatorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StageInvestigatorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStageInvestigatorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StageInvestigatorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StageInvestigatorResponseValidationError{}

// Validate checks the field values on RemedyGeneratorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemedyGeneratorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemedyGeneratorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemedyGeneratorRequestMultiError, or nil if none found.
func (m *RemedyGeneratorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RemedyGeneratorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Stage

	// no validation rules for StageStatus

	// no validation rules for DiagnosisCode

	// no validation rules for DiagnosisReport

	if len(errors) > 0 {
		return RemedyGeneratorRequestMultiError(errors)
	}

	return nil
}

// RemedyGeneratorRequestMultiError is an error wrapping multiple validation
// errors returned by RemedyGeneratorRequest.ValidateAll() if the designated
// constraints aren't met.
type RemedyGeneratorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemedyGeneratorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemedyGeneratorRequestMultiError) AllErrors() []error { return m }

// RemedyGeneratorRequestValidationError is the validation error returned by
// RemedyGeneratorRequest.Validate if the designated constraints aren't met.
type RemedyGeneratorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemedyGeneratorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemedyGeneratorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemedyGeneratorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemedyGeneratorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemedyGeneratorRequestValidationError) ErrorName() string {
	return "RemedyGeneratorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RemedyGeneratorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemedyGeneratorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemedyGeneratorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemedyGeneratorRequestValidationError{}

// Validate checks the field values on RemedyGeneratorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemedyGeneratorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemedyGeneratorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemedyGeneratorResponseMultiError, or nil if none found.
func (m *RemedyGeneratorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RemedyGeneratorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRemedy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RemedyGeneratorResponseValidationError{
					field:  "Remedy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RemedyGeneratorResponseValidationError{
					field:  "Remedy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRemedy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RemedyGeneratorResponseValidationError{
				field:  "Remedy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RemedyGeneratorResponseMultiError(errors)
	}

	return nil
}

// RemedyGeneratorResponseMultiError is an error wrapping multiple validation
// errors returned by RemedyGeneratorResponse.ValidateAll() if the designated
// constraints aren't met.
type RemedyGeneratorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemedyGeneratorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemedyGeneratorResponseMultiError) AllErrors() []error { return m }

// RemedyGeneratorResponseValidationError is the validation error returned by
// RemedyGeneratorResponse.Validate if the designated constraints aren't met.
type RemedyGeneratorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemedyGeneratorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemedyGeneratorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemedyGeneratorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemedyGeneratorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemedyGeneratorResponseValidationError) ErrorName() string {
	return "RemedyGeneratorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RemedyGeneratorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemedyGeneratorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemedyGeneratorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemedyGeneratorResponseValidationError{}

// Validate checks the field values on Remedy with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Remedy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Remedy with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in RemedyMultiError, or nil if none found.
func (m *Remedy) ValidateAll() error {
	return m.validate(true)
}

func (m *Remedy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Advice

	// no validation rules for NonProdAdvice

	if len(errors) > 0 {
		return RemedyMultiError(errors)
	}

	return nil
}

// RemedyMultiError is an error wrapping multiple validation errors returned by
// Remedy.ValidateAll() if the designated constraints aren't met.
type RemedyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemedyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemedyMultiError) AllErrors() []error { return m }

// RemedyValidationError is the validation error returned by Remedy.Validate if
// the designated constraints aren't met.
type RemedyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemedyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemedyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemedyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemedyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemedyValidationError) ErrorName() string { return "RemedyValidationError" }

// Error satisfies the builtin error interface
func (e RemedyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemedy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemedyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemedyValidationError{}

// Validate checks the field values on AfuTroubleshootingDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AfuTroubleshootingDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AfuTroubleshootingDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AfuTroubleshootingDetailsMultiError, or nil if none found.
func (m *AfuTroubleshootingDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AfuTroubleshootingDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Stage

	// no validation rules for StageStatus

	// no validation rules for DiagnosisCode

	// no validation rules for DiagnosisReport

	if all {
		switch v := interface{}(m.GetRemedy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AfuTroubleshootingDetailsValidationError{
					field:  "Remedy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AfuTroubleshootingDetailsValidationError{
					field:  "Remedy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRemedy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AfuTroubleshootingDetailsValidationError{
				field:  "Remedy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardForm

	if len(errors) > 0 {
		return AfuTroubleshootingDetailsMultiError(errors)
	}

	return nil
}

// AfuTroubleshootingDetailsMultiError is an error wrapping multiple validation
// errors returned by AfuTroubleshootingDetails.ValidateAll() if the
// designated constraints aren't met.
type AfuTroubleshootingDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AfuTroubleshootingDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AfuTroubleshootingDetailsMultiError) AllErrors() []error { return m }

// AfuTroubleshootingDetailsValidationError is the validation error returned by
// AfuTroubleshootingDetails.Validate if the designated constraints aren't met.
type AfuTroubleshootingDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AfuTroubleshootingDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AfuTroubleshootingDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AfuTroubleshootingDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AfuTroubleshootingDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AfuTroubleshootingDetailsValidationError) ErrorName() string {
	return "AfuTroubleshootingDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AfuTroubleshootingDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAfuTroubleshootingDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AfuTroubleshootingDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AfuTroubleshootingDetailsValidationError{}
