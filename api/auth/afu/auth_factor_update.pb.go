// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/internal/auth_factor_update.proto

package afu

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	card "github.com/epifi/gamma/api/card"
	frontend "github.com/epifi/gamma/api/frontend"
	product "github.com/epifi/gamma/api/product"
	risk "github.com/epifi/gamma/api/risk"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AuthFactor are auth factors that can be updated by the user
// as the part of Re-OOBE flow.
type AuthFactor int32

const (
	AuthFactor_AUTH_FACTOR_UNSPECIFIED AuthFactor = 0
	// Device ID of the user's handset.
	AuthFactor_DEVICE AuthFactor = 1
	// validated phone number.
	AuthFactor_PHONE_NUM AuthFactor = 2
	// validated email.
	AuthFactor_EMAIL AuthFactor = 3
	// Sim hash on client. It changes when sim is changed.
	AuthFactor_SIM AuthFactor = 4
)

// Enum value maps for AuthFactor.
var (
	AuthFactor_name = map[int32]string{
		0: "AUTH_FACTOR_UNSPECIFIED",
		1: "DEVICE",
		2: "PHONE_NUM",
		3: "EMAIL",
		4: "SIM",
	}
	AuthFactor_value = map[string]int32{
		"AUTH_FACTOR_UNSPECIFIED": 0,
		"DEVICE":                  1,
		"PHONE_NUM":               2,
		"EMAIL":                   3,
		"SIM":                     4,
	}
)

func (x AuthFactor) Enum() *AuthFactor {
	p := new(AuthFactor)
	*p = x
	return p
}

func (x AuthFactor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthFactor) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[0].Descriptor()
}

func (AuthFactor) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[0]
}

func (x AuthFactor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthFactor.Descriptor instead.
func (AuthFactor) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{0}
}

// Combination is the list of update scenarios with combination of
// Phone Number, Email, Device & Sim auth factors defined above.
// It represents the auth factors that can be updated in a single flow.
type Combination int32

const (
	Combination_UPDATE_UNSPECIFIED Combination = 0
	// User wants to update Device only.
	Combination_UPDATE_DEVICE Combination = 1
	// User wants to update Phone Number only.
	Combination_UPDATE_PHONE_NUM Combination = 2
	// User wants to update Email only
	Combination_UPDATE_EMAIL Combination = 3
	// User wants to update both Phone Number & Email
	Combination_UPDATE_PHONE_NUM_EMAIL Combination = 4
	// User wants to update Phone Number on a new device
	Combination_UPDATE_DEVICE_PHONE_NUM Combination = 5
	// User wants to update Email on a new device
	Combination_UPDATE_DEVICE_EMAIL Combination = 6
	// Mobile SIM update. For rest the combinations,
	// the above defined combination scenarios takeover.
	Combination_UPDATE_SIM Combination = 7
	// User wants to update both Email and Mobile SIM
	Combination_UPDATE_EMAIL_SIM Combination = 8
)

// Enum value maps for Combination.
var (
	Combination_name = map[int32]string{
		0: "UPDATE_UNSPECIFIED",
		1: "UPDATE_DEVICE",
		2: "UPDATE_PHONE_NUM",
		3: "UPDATE_EMAIL",
		4: "UPDATE_PHONE_NUM_EMAIL",
		5: "UPDATE_DEVICE_PHONE_NUM",
		6: "UPDATE_DEVICE_EMAIL",
		7: "UPDATE_SIM",
		8: "UPDATE_EMAIL_SIM",
	}
	Combination_value = map[string]int32{
		"UPDATE_UNSPECIFIED":      0,
		"UPDATE_DEVICE":           1,
		"UPDATE_PHONE_NUM":        2,
		"UPDATE_EMAIL":            3,
		"UPDATE_PHONE_NUM_EMAIL":  4,
		"UPDATE_DEVICE_PHONE_NUM": 5,
		"UPDATE_DEVICE_EMAIL":     6,
		"UPDATE_SIM":              7,
		"UPDATE_EMAIL_SIM":        8,
	}
)

func (x Combination) Enum() *Combination {
	p := new(Combination)
	*p = x
	return p
}

func (x Combination) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Combination) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[1].Descriptor()
}

func (Combination) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[1]
}

func (x Combination) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Combination.Descriptor instead.
func (Combination) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{1}
}

// Credential that can be verified as user's proof of identity.
// They're combination of what-they-know and what-they-have assets.
type Credential int32

const (
	Credential_CREDENTIAL_UNSPECIFIED Credential = 0
	// Validation of user's registered email through oauth
	Credential_EMAIL_VALIDATION Credential = 1
	// Validation of user's phone number through OTP
	Credential_PHONE_NUM_VALIDATION Credential = 2
	// Validation of user's registered device
	Credential_REGISTERED_DEVICE_VALIDATION Credential = 3
	// Validation of user using Liveness and face match
	Credential_LIVENESS_FM_VALIDATION Credential = 4
	// Validation of user using ATM Card PIN
	Credential_ATM_PIN_VALIDATION Credential = 5
	// Validation of logged-in user. For user
	// updating auth factor from inside the app.
	Credential_LOGGED_IN_VALIDATION Credential = 6
	// explicit state for no credential to be verified
	Credential_NO_CREDENTIAL Credential = 7
	// Aadhaar mobile validation via EKYC for phone update
	Credential_AADHAAR_MOBILE_VALIDATION Credential = 8
)

// Enum value maps for Credential.
var (
	Credential_name = map[int32]string{
		0: "CREDENTIAL_UNSPECIFIED",
		1: "EMAIL_VALIDATION",
		2: "PHONE_NUM_VALIDATION",
		3: "REGISTERED_DEVICE_VALIDATION",
		4: "LIVENESS_FM_VALIDATION",
		5: "ATM_PIN_VALIDATION",
		6: "LOGGED_IN_VALIDATION",
		7: "NO_CREDENTIAL",
		8: "AADHAAR_MOBILE_VALIDATION",
	}
	Credential_value = map[string]int32{
		"CREDENTIAL_UNSPECIFIED":       0,
		"EMAIL_VALIDATION":             1,
		"PHONE_NUM_VALIDATION":         2,
		"REGISTERED_DEVICE_VALIDATION": 3,
		"LIVENESS_FM_VALIDATION":       4,
		"ATM_PIN_VALIDATION":           5,
		"LOGGED_IN_VALIDATION":         6,
		"NO_CREDENTIAL":                7,
		"AADHAAR_MOBILE_VALIDATION":    8,
	}
)

func (x Credential) Enum() *Credential {
	p := new(Credential)
	*p = x
	return p
}

func (x Credential) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Credential) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[2].Descriptor()
}

func (Credential) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[2]
}

func (x Credential) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Credential.Descriptor instead.
func (Credential) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{2}
}

// Keeps track of overall status of the Auth Factor update attempt
type OverallStatus int32

const (
	OverallStatus_OVERALL_STATUS_UNSPECIFIED OverallStatus = 0
	// Auth factor update process in progress
	OverallStatus_OVERALL_STATUS_IN_PROGRESS OverallStatus = 1
	// Auth factor update process is completed i.e. credentials are
	// verified, partner banks and epifi user profile updated
	OverallStatus_OVERALL_STATUS_COMPLETED OverallStatus = 2
	// Auth factor update process failed with reasons
	// such as failed credentials verification etc.
	OverallStatus_OVERALL_STATUS_FAILED OverallStatus = 3
	// AFU process is stuck and need manual intervention. Can be due to:
	// 1. Device Re-registration blocked at vendor end
	// 2. Device Re-registation completed at vendor end but we failed to update auth factors at our end.
	// 3. Device reactivation not working.
	OverallStatus_OVERALL_STATUS_STUCK OverallStatus = 4
)

// Enum value maps for OverallStatus.
var (
	OverallStatus_name = map[int32]string{
		0: "OVERALL_STATUS_UNSPECIFIED",
		1: "OVERALL_STATUS_IN_PROGRESS",
		2: "OVERALL_STATUS_COMPLETED",
		3: "OVERALL_STATUS_FAILED",
		4: "OVERALL_STATUS_STUCK",
	}
	OverallStatus_value = map[string]int32{
		"OVERALL_STATUS_UNSPECIFIED": 0,
		"OVERALL_STATUS_IN_PROGRESS": 1,
		"OVERALL_STATUS_COMPLETED":   2,
		"OVERALL_STATUS_FAILED":      3,
		"OVERALL_STATUS_STUCK":       4,
	}
)

func (x OverallStatus) Enum() *OverallStatus {
	p := new(OverallStatus)
	*p = x
	return p
}

func (x OverallStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OverallStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[3].Descriptor()
}

func (OverallStatus) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[3]
}

func (x OverallStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OverallStatus.Descriptor instead.
func (OverallStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{3}
}

// UserConfirmationStatus represents whether new values have been
// confirmed by user for update.
type UserConfirmationStatus int32

const (
	UserConfirmationStatus_USER_CONFIRMATION_STATUS_UNSPECIFIED UserConfirmationStatus = 0
	// User has accepted the updates
	UserConfirmationStatus_USER_ACCEPTED UserConfirmationStatus = 1
	// User has rejected the updates
	UserConfirmationStatus_USER_REJECTED UserConfirmationStatus = 2
)

// Enum value maps for UserConfirmationStatus.
var (
	UserConfirmationStatus_name = map[int32]string{
		0: "USER_CONFIRMATION_STATUS_UNSPECIFIED",
		1: "USER_ACCEPTED",
		2: "USER_REJECTED",
	}
	UserConfirmationStatus_value = map[string]int32{
		"USER_CONFIRMATION_STATUS_UNSPECIFIED": 0,
		"USER_ACCEPTED":                        1,
		"USER_REJECTED":                        2,
	}
)

func (x UserConfirmationStatus) Enum() *UserConfirmationStatus {
	p := new(UserConfirmationStatus)
	*p = x
	return p
}

func (x UserConfirmationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserConfirmationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[4].Descriptor()
}

func (UserConfirmationStatus) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[4]
}

func (x UserConfirmationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserConfirmationStatus.Descriptor instead.
func (UserConfirmationStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{4}
}

// RequestStatus represents any request that's made outside the
// Auth Factor Update System such as requests to vendors or other services
type RequestStatus int32

const (
	RequestStatus_REQUEST_STATUS_UNSPECIFIED RequestStatus = 0
	// Request returned OK response
	RequestStatus_REQUEST_STATUS_SUCCESS RequestStatus = 1
	// update request rejected by the sever.
	RequestStatus_REQUEST_STATUS_FAIL RequestStatus = 2
	// Request returned internal error.
	RequestStatus_REQUEST_STATUS_ERROR RequestStatus = 3
)

// Enum value maps for RequestStatus.
var (
	RequestStatus_name = map[int32]string{
		0: "REQUEST_STATUS_UNSPECIFIED",
		1: "REQUEST_STATUS_SUCCESS",
		2: "REQUEST_STATUS_FAIL",
		3: "REQUEST_STATUS_ERROR",
	}
	RequestStatus_value = map[string]int32{
		"REQUEST_STATUS_UNSPECIFIED": 0,
		"REQUEST_STATUS_SUCCESS":     1,
		"REQUEST_STATUS_FAIL":        2,
		"REQUEST_STATUS_ERROR":       3,
	}
)

func (x RequestStatus) Enum() *RequestStatus {
	p := new(RequestStatus)
	*p = x
	return p
}

func (x RequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[5].Descriptor()
}

func (RequestStatus) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[5]
}

func (x RequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RequestStatus.Descriptor instead.
func (RequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{5}
}

// VerificationStatus represents the status of Credential verification
type VerificationStatus int32

const (
	VerificationStatus_VERIFICATION_STATUS_UNSPECIFIED VerificationStatus = 0
	// Credentials verified successfully
	VerificationStatus_VERIFICATION_SUCCESS VerificationStatus = 1
	// credential verification failed
	VerificationStatus_VERIFICATION_FAIL VerificationStatus = 2
	// there was a technical error in verification process
	VerificationStatus_VERIFICATION_ERROR VerificationStatus = 3
	// credential verification is in progress
	VerificationStatus_VERIFICATION_IN_PROGRESS VerificationStatus = 4
	// credential verification is in review
	VerificationStatus_VERIFICATION_IN_REVIEW VerificationStatus = 5
	// status recorded implies that input for verification is received
	VerificationStatus_VERIFICATION_STATUS_RECORDED VerificationStatus = 6
	// status dodged when a verification method is dodged by the user in preference to alternate method
	VerificationStatus_VERIFICATION_STATUS_SKIPPED VerificationStatus = 7
	// status terminally failed when verification for the method fails with a non-retryable reason
	VerificationStatus_VERIFICATION_STATUS_TERMINALLY_FAILED VerificationStatus = 8
)

// Enum value maps for VerificationStatus.
var (
	VerificationStatus_name = map[int32]string{
		0: "VERIFICATION_STATUS_UNSPECIFIED",
		1: "VERIFICATION_SUCCESS",
		2: "VERIFICATION_FAIL",
		3: "VERIFICATION_ERROR",
		4: "VERIFICATION_IN_PROGRESS",
		5: "VERIFICATION_IN_REVIEW",
		6: "VERIFICATION_STATUS_RECORDED",
		7: "VERIFICATION_STATUS_SKIPPED",
		8: "VERIFICATION_STATUS_TERMINALLY_FAILED",
	}
	VerificationStatus_value = map[string]int32{
		"VERIFICATION_STATUS_UNSPECIFIED":       0,
		"VERIFICATION_SUCCESS":                  1,
		"VERIFICATION_FAIL":                     2,
		"VERIFICATION_ERROR":                    3,
		"VERIFICATION_IN_PROGRESS":              4,
		"VERIFICATION_IN_REVIEW":                5,
		"VERIFICATION_STATUS_RECORDED":          6,
		"VERIFICATION_STATUS_SKIPPED":           7,
		"VERIFICATION_STATUS_TERMINALLY_FAILED": 8,
	}
)

func (x VerificationStatus) Enum() *VerificationStatus {
	p := new(VerificationStatus)
	*p = x
	return p
}

func (x VerificationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerificationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[6].Descriptor()
}

func (VerificationStatus) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[6]
}

func (x VerificationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerificationStatus.Descriptor instead.
func (VerificationStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{6}
}

// FailureReason stores the low level failure reasons to be shown to the user
type FailureReason int32

const (
	FailureReason_FAILURE_REASON_UNSPECIFIED FailureReason = 0
	// All liveness/face match attempts exhausted; manual review required
	FailureReason_LIVENESS_FM_MAX_RETRIES FailureReason = 1
	// User entered incorrect ATM PIN
	FailureReason_WRONG_ATM_PIN FailureReason = 2
	// User has entered incorrect ATM PIN max times.
	// No more retries allowed.
	FailureReason_ATM_PIN_MAX_RETRIES FailureReason = 3
	// PIN Validation not allowed after Pin Reset (Should do some other card txn before this)
	FailureReason_CARD_TXN_REQUIRED FailureReason = 4
	// CAF status = 0 or 9 (Card Status Invalid/Issued status).
	// User needs to activate and set pin first via IVR.
	FailureReason_CARD_INACTIVE FailureReason = 5
	// Device Registration related error
	FailureReason_NO_SMS_RECEIVED FailureReason = 6
	// Device Registration related error
	FailureReason_MOBILE_MISMATCH FailureReason = 7
	// Device Registration related error
	FailureReason_PAYLOAD_MISMATCH FailureReason = 8
	// Internal error in device re-registration
	// could be due to cred block decryption
	FailureReason_INTERNAL_ERROR_AT_VENDOR FailureReason = 9
	// Error parsing cred block at vendor
	FailureReason_INVALID_CRED_BLOCK FailureReason = 10
	// Auth factors are conflicting with onboarding attempt
	FailureReason_ONBOARDING_CONFLICT FailureReason = 11
	// user's kyc details not updated
	FailureReason_KYC_NON_COMPLIANT FailureReason = 12
	// card is not linked to old number
	FailureReason_CARD_NOT_LINKED_TO_OLD_NUMBER FailureReason = 13
	// card is hot listed
	FailureReason_LOST_CARD FailureReason = 14
	// when liveness_fm is failed manually
	FailureReason_LIVENESS_FM_MANUALLY_FAILED FailureReason = 15
	// when liveness_fm is expired
	FailureReason_LIVENESS_FM_EXPIRED FailureReason = 16
	// when liveness attempt fails, but further attempts are allowed
	FailureReason_LIVENESS_FM_ATTEMPT_FAILURE FailureReason = 17
	// when liveness or FM are stuck in manual review
	FailureReason_LIVENESS_FM_STUCK_IN_MANUAL_REVIEW FailureReason = 18
	// when vendor fails to accept the AFU request
	FailureReason_REQUEST_DID_NOT_REACH_VENDOR FailureReason = 19
	// when card not linked to customer
	FailureReason_CARD_NOT_LINKED_TO_CUSTOMER FailureReason = 20
	// when no card record
	FailureReason_NO_CARD_RECORD FailureReason = 21
	// when account is inactive
	FailureReason_ACCOUNT_INACTIVE FailureReason = 22
	// user failed through afu risk model check
	FailureReason_AFU_RISK_MODEL_FAILURE FailureReason = 23
	// user has changed the mobile number independently with bank/vendor and on Fi we have the old number
	FailureReason_DEVICE_TEMPORARILY_DEACTIVATED FailureReason = 24
	// Auth factors in new AFU attempt are conflicting with existing AFU attempts
	FailureReason_AFU_CONFLICT FailureReason = 25
	// Mobile number linked to aadhaar is not same as the new number user is trying to reonboard with
	FailureReason_AADHAAR_MOBILE_MISMATCH FailureReason = 26
	// Internal exception occurred at vendor in device reregistration enquiry
	FailureReason_DEVREREG_ENQUIRY_INTERNAL_ERR_AT_VENDOR FailureReason = 27
	// generic failure reason for EKYC failures in AFU
	// Use it only as a fallback
	FailureReason_UNEXPECTED_EKYC_FAILURE FailureReason = 28
	// Unmapped failures for which there is no specific handling.
	// These failures will have generic error screens.
	FailureReason_UNMAPPED_DEVREG_INIT_FAILURE    FailureReason = 998
	FailureReason_UNMAPPED_DEVREG_ENQUIRY_FAILURE FailureReason = 999
	FailureReason_AFU_FAILED_AT_VENDOR            FailureReason = 1000
)

// Enum value maps for FailureReason.
var (
	FailureReason_name = map[int32]string{
		0:    "FAILURE_REASON_UNSPECIFIED",
		1:    "LIVENESS_FM_MAX_RETRIES",
		2:    "WRONG_ATM_PIN",
		3:    "ATM_PIN_MAX_RETRIES",
		4:    "CARD_TXN_REQUIRED",
		5:    "CARD_INACTIVE",
		6:    "NO_SMS_RECEIVED",
		7:    "MOBILE_MISMATCH",
		8:    "PAYLOAD_MISMATCH",
		9:    "INTERNAL_ERROR_AT_VENDOR",
		10:   "INVALID_CRED_BLOCK",
		11:   "ONBOARDING_CONFLICT",
		12:   "KYC_NON_COMPLIANT",
		13:   "CARD_NOT_LINKED_TO_OLD_NUMBER",
		14:   "LOST_CARD",
		15:   "LIVENESS_FM_MANUALLY_FAILED",
		16:   "LIVENESS_FM_EXPIRED",
		17:   "LIVENESS_FM_ATTEMPT_FAILURE",
		18:   "LIVENESS_FM_STUCK_IN_MANUAL_REVIEW",
		19:   "REQUEST_DID_NOT_REACH_VENDOR",
		20:   "CARD_NOT_LINKED_TO_CUSTOMER",
		21:   "NO_CARD_RECORD",
		22:   "ACCOUNT_INACTIVE",
		23:   "AFU_RISK_MODEL_FAILURE",
		24:   "DEVICE_TEMPORARILY_DEACTIVATED",
		25:   "AFU_CONFLICT",
		26:   "AADHAAR_MOBILE_MISMATCH",
		27:   "DEVREREG_ENQUIRY_INTERNAL_ERR_AT_VENDOR",
		28:   "UNEXPECTED_EKYC_FAILURE",
		998:  "UNMAPPED_DEVREG_INIT_FAILURE",
		999:  "UNMAPPED_DEVREG_ENQUIRY_FAILURE",
		1000: "AFU_FAILED_AT_VENDOR",
	}
	FailureReason_value = map[string]int32{
		"FAILURE_REASON_UNSPECIFIED":              0,
		"LIVENESS_FM_MAX_RETRIES":                 1,
		"WRONG_ATM_PIN":                           2,
		"ATM_PIN_MAX_RETRIES":                     3,
		"CARD_TXN_REQUIRED":                       4,
		"CARD_INACTIVE":                           5,
		"NO_SMS_RECEIVED":                         6,
		"MOBILE_MISMATCH":                         7,
		"PAYLOAD_MISMATCH":                        8,
		"INTERNAL_ERROR_AT_VENDOR":                9,
		"INVALID_CRED_BLOCK":                      10,
		"ONBOARDING_CONFLICT":                     11,
		"KYC_NON_COMPLIANT":                       12,
		"CARD_NOT_LINKED_TO_OLD_NUMBER":           13,
		"LOST_CARD":                               14,
		"LIVENESS_FM_MANUALLY_FAILED":             15,
		"LIVENESS_FM_EXPIRED":                     16,
		"LIVENESS_FM_ATTEMPT_FAILURE":             17,
		"LIVENESS_FM_STUCK_IN_MANUAL_REVIEW":      18,
		"REQUEST_DID_NOT_REACH_VENDOR":            19,
		"CARD_NOT_LINKED_TO_CUSTOMER":             20,
		"NO_CARD_RECORD":                          21,
		"ACCOUNT_INACTIVE":                        22,
		"AFU_RISK_MODEL_FAILURE":                  23,
		"DEVICE_TEMPORARILY_DEACTIVATED":          24,
		"AFU_CONFLICT":                            25,
		"AADHAAR_MOBILE_MISMATCH":                 26,
		"DEVREREG_ENQUIRY_INTERNAL_ERR_AT_VENDOR": 27,
		"UNEXPECTED_EKYC_FAILURE":                 28,
		"UNMAPPED_DEVREG_INIT_FAILURE":            998,
		"UNMAPPED_DEVREG_ENQUIRY_FAILURE":         999,
		"AFU_FAILED_AT_VENDOR":                    1000,
	}
)

func (x FailureReason) Enum() *FailureReason {
	p := new(FailureReason)
	*p = x
	return p
}

func (x FailureReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FailureReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[7].Descriptor()
}

func (FailureReason) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[7]
}

func (x FailureReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FailureReason.Descriptor instead.
func (FailureReason) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{7}
}

type AuthFactorUpdateFieldMask int32

const (
	AuthFactorUpdateFieldMask_AUTH_FACTOR_UPDATE_FIELD_MASK_UNSPECIFIED      AuthFactorUpdateFieldMask = 0
	AuthFactorUpdateFieldMask_AUTH_FACTOR_UPDATE_FIELD_MASK_OVERALL_STATUS   AuthFactorUpdateFieldMask = 1
	AuthFactorUpdateFieldMask_AUTH_FACTOR_UPDATE_FIELD_MASK_CONTEXT          AuthFactorUpdateFieldMask = 2
	AuthFactorUpdateFieldMask_AUTH_FACTOR_UPDATE_FIELD_MASK_VENDOR_CONTEXT   AuthFactorUpdateFieldMask = 3
	AuthFactorUpdateFieldMask_AUTH_FACTOR_UPDATE_FIELD_MASK_FAILURE_REASON   AuthFactorUpdateFieldMask = 4
	AuthFactorUpdateFieldMask_AUTH_FACTOR_UPDATE_FIELD_MASK_ATTEMPTS_HISTORY AuthFactorUpdateFieldMask = 5
)

// Enum value maps for AuthFactorUpdateFieldMask.
var (
	AuthFactorUpdateFieldMask_name = map[int32]string{
		0: "AUTH_FACTOR_UPDATE_FIELD_MASK_UNSPECIFIED",
		1: "AUTH_FACTOR_UPDATE_FIELD_MASK_OVERALL_STATUS",
		2: "AUTH_FACTOR_UPDATE_FIELD_MASK_CONTEXT",
		3: "AUTH_FACTOR_UPDATE_FIELD_MASK_VENDOR_CONTEXT",
		4: "AUTH_FACTOR_UPDATE_FIELD_MASK_FAILURE_REASON",
		5: "AUTH_FACTOR_UPDATE_FIELD_MASK_ATTEMPTS_HISTORY",
	}
	AuthFactorUpdateFieldMask_value = map[string]int32{
		"AUTH_FACTOR_UPDATE_FIELD_MASK_UNSPECIFIED":      0,
		"AUTH_FACTOR_UPDATE_FIELD_MASK_OVERALL_STATUS":   1,
		"AUTH_FACTOR_UPDATE_FIELD_MASK_CONTEXT":          2,
		"AUTH_FACTOR_UPDATE_FIELD_MASK_VENDOR_CONTEXT":   3,
		"AUTH_FACTOR_UPDATE_FIELD_MASK_FAILURE_REASON":   4,
		"AUTH_FACTOR_UPDATE_FIELD_MASK_ATTEMPTS_HISTORY": 5,
	}
)

func (x AuthFactorUpdateFieldMask) Enum() *AuthFactorUpdateFieldMask {
	p := new(AuthFactorUpdateFieldMask)
	*p = x
	return p
}

func (x AuthFactorUpdateFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthFactorUpdateFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[8].Descriptor()
}

func (AuthFactorUpdateFieldMask) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[8]
}

func (x AuthFactorUpdateFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthFactorUpdateFieldMask.Descriptor instead.
func (AuthFactorUpdateFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{8}
}

type ActorAuthStatus int32

const (
	ActorAuthStatus_ACTOR_AUTH_STATUS_UNSPECIFIED ActorAuthStatus = 0
	ActorAuthStatus_ACTOR_AUTH_STATUS_IN_PROGRESS ActorAuthStatus = 1
	ActorAuthStatus_ACTOR_AUTH_STATUS_SUCCESS     ActorAuthStatus = 2
	ActorAuthStatus_ACTOR_AUTH_STATUS_FAILURE     ActorAuthStatus = 3
)

// Enum value maps for ActorAuthStatus.
var (
	ActorAuthStatus_name = map[int32]string{
		0: "ACTOR_AUTH_STATUS_UNSPECIFIED",
		1: "ACTOR_AUTH_STATUS_IN_PROGRESS",
		2: "ACTOR_AUTH_STATUS_SUCCESS",
		3: "ACTOR_AUTH_STATUS_FAILURE",
	}
	ActorAuthStatus_value = map[string]int32{
		"ACTOR_AUTH_STATUS_UNSPECIFIED": 0,
		"ACTOR_AUTH_STATUS_IN_PROGRESS": 1,
		"ACTOR_AUTH_STATUS_SUCCESS":     2,
		"ACTOR_AUTH_STATUS_FAILURE":     3,
	}
)

func (x ActorAuthStatus) Enum() *ActorAuthStatus {
	p := new(ActorAuthStatus)
	*p = x
	return p
}

func (x ActorAuthStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActorAuthStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[9].Descriptor()
}

func (ActorAuthStatus) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[9]
}

func (x ActorAuthStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActorAuthStatus.Descriptor instead.
func (ActorAuthStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{9}
}

type UpdateVendorState int32

const (
	UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED UpdateVendorState = 0
	// auth factor update at vendor's end has started.
	// In this state, we send a request to deactivate user's device.
	// If device is deactivated successfully, state is changed to DEACTIVATED.
	// In case of Permanent failure, state is changed to FAILED with failure_type as DEACTIVATION_FAILED.
	UpdateVendorState_INITIATED UpdateVendorState = 1
	// Once user's device is successfully deactivated, state is changed to DEACTIVATED.
	// In this state, we send a request to re-register the device with new auth factors.
	// If request is accepted by vendor, state is changed to REREGISTRATION_REQUESTED.
	// In case of Permanent failure, state is changed to FAILED with failure_type as REREGISTRATION_FAILED.
	UpdateVendorState_DEACTIVATED UpdateVendorState = 2
	// In DEACTIVATED state, if device re-registration is successful, state is changed to REREGISTERED.
	// In this state, we send a request to re-activate the device.
	// If re-activation is successful, state is changed to COMPLETED.
	// If re-activation is ambiguous, state is changed to AMBIGUOUS_REACTIVATION.
	// In case of Permanent failure, state is changed to FAILED with failure_type as REACTIVATION_FAILED.
	UpdateVendorState_REREGISTERED UpdateVendorState = 3
	// In DEACTIVATED state, if we receive a successful acknowledgment for re-registration request, state is changed to REREGISTRATION_REQUESTED.
	// In this state, we try to check the status of the re-registration request.
	// if re-registration is successful, state is changed to REREGISTERED.
	// if re-registration was failed, state is changed to FAILED with failure_type as REREGISTRATION_FAILED
	// In case of Permanent failure in enquiry API, state is changed to FAILED with failure_type as REREG_ENQUIRY_FAILED.
	UpdateVendorState_REREGISTRATION_REQUESTED UpdateVendorState = 4
	// COMPLETED state means that the vendor update process has been completed successfully
	UpdateVendorState_COMPLETED UpdateVendorState = 6
	// FAILED state means that update process can't be completed because of some permanent failure.
	UpdateVendorState_FAILED UpdateVendorState = 7
	// In REGISTERED state, if we receive ambiguous response to device reactivation request, state is changed to AMBIGUOUS_REACTIVATION
	// Ambiguous responses while reactivation are like- invalid device token, where state of reactivation is not known.
	// In this state, we attempt device deactivation
	// If deactivation is successful, state is changed based on re-registration status
	// In case of permanent failure, state is changed to FAILED with failure_type as REACTIVATION_RECOVERY_FAILED.
	UpdateVendorState_AMBIGUOUS_REACTIVATION UpdateVendorState = 8
)

// Enum value maps for UpdateVendorState.
var (
	UpdateVendorState_name = map[int32]string{
		0: "UPDATE_VENDOR_STATE_UNSPECIFIED",
		1: "INITIATED",
		2: "DEACTIVATED",
		3: "REREGISTERED",
		4: "REREGISTRATION_REQUESTED",
		6: "COMPLETED",
		7: "FAILED",
		8: "AMBIGUOUS_REACTIVATION",
	}
	UpdateVendorState_value = map[string]int32{
		"UPDATE_VENDOR_STATE_UNSPECIFIED": 0,
		"INITIATED":                       1,
		"DEACTIVATED":                     2,
		"REREGISTERED":                    3,
		"REREGISTRATION_REQUESTED":        4,
		"COMPLETED":                       6,
		"FAILED":                          7,
		"AMBIGUOUS_REACTIVATION":          8,
	}
)

func (x UpdateVendorState) Enum() *UpdateVendorState {
	p := new(UpdateVendorState)
	*p = x
	return p
}

func (x UpdateVendorState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateVendorState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[10].Descriptor()
}

func (UpdateVendorState) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[10]
}

func (x UpdateVendorState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateVendorState.Descriptor instead.
func (UpdateVendorState) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{10}
}

// Types of failures that might lead to FAILED state in vendor update process
type UpdateVendorFailureType int32

const (
	UpdateVendorFailureType_UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED UpdateVendorFailureType = 0
	// error encountered while deactivating device.
	UpdateVendorFailureType_DEACTIVATION_FAILED UpdateVendorFailureType = 1
	// error encountered while re-registering device.
	UpdateVendorFailureType_REREGISTRATION_FAILED UpdateVendorFailureType = 2
	// error encountered while reactivating device.
	UpdateVendorFailureType_REACTIVATION_FAILED UpdateVendorFailureType = 3
	// error encountered while checking the status of re-registration request.
	UpdateVendorFailureType_REREG_ENQUIRY_FAILED UpdateVendorFailureType = 4
	// error encountered while deactivating device in ambiguous_reactivation state.
	UpdateVendorFailureType_REACTIVATION_RECOVERY_FAILED UpdateVendorFailureType = 5
)

// Enum value maps for UpdateVendorFailureType.
var (
	UpdateVendorFailureType_name = map[int32]string{
		0: "UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED",
		1: "DEACTIVATION_FAILED",
		2: "REREGISTRATION_FAILED",
		3: "REACTIVATION_FAILED",
		4: "REREG_ENQUIRY_FAILED",
		5: "REACTIVATION_RECOVERY_FAILED",
	}
	UpdateVendorFailureType_value = map[string]int32{
		"UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED": 0,
		"DEACTIVATION_FAILED":                    1,
		"REREGISTRATION_FAILED":                  2,
		"REACTIVATION_FAILED":                    3,
		"REREG_ENQUIRY_FAILED":                   4,
		"REACTIVATION_RECOVERY_FAILED":           5,
	}
)

func (x UpdateVendorFailureType) Enum() *UpdateVendorFailureType {
	p := new(UpdateVendorFailureType)
	*p = x
	return p
}

func (x UpdateVendorFailureType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateVendorFailureType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[11].Descriptor()
}

func (UpdateVendorFailureType) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[11]
}

func (x UpdateVendorFailureType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateVendorFailureType.Descriptor instead.
func (UpdateVendorFailureType) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{11}
}

type UpdateVendorAction int32

const (
	UpdateVendorAction_UPDATE_VENDOR_ACTION_UNSPECIFIED UpdateVendorAction = 0
	UpdateVendorAction_CONTINUE_AFU                     UpdateVendorAction = 1
	UpdateVendorAction_REACTIVATE_DEVICE                UpdateVendorAction = 2
	UpdateVendorAction_MARK_AS_FAILED                   UpdateVendorAction = 3
	UpdateVendorAction_MARK_AS_STUCK                    UpdateVendorAction = 4
	UpdateVendorAction_NO_ACTION                        UpdateVendorAction = 5
)

// Enum value maps for UpdateVendorAction.
var (
	UpdateVendorAction_name = map[int32]string{
		0: "UPDATE_VENDOR_ACTION_UNSPECIFIED",
		1: "CONTINUE_AFU",
		2: "REACTIVATE_DEVICE",
		3: "MARK_AS_FAILED",
		4: "MARK_AS_STUCK",
		5: "NO_ACTION",
	}
	UpdateVendorAction_value = map[string]int32{
		"UPDATE_VENDOR_ACTION_UNSPECIFIED": 0,
		"CONTINUE_AFU":                     1,
		"REACTIVATE_DEVICE":                2,
		"MARK_AS_FAILED":                   3,
		"MARK_AS_STUCK":                    4,
		"NO_ACTION":                        5,
	}
)

func (x UpdateVendorAction) Enum() *UpdateVendorAction {
	p := new(UpdateVendorAction)
	*p = x
	return p
}

func (x UpdateVendorAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateVendorAction) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[12].Descriptor()
}

func (UpdateVendorAction) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[12]
}

func (x UpdateVendorAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateVendorAction.Descriptor instead.
func (UpdateVendorAction) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{12}
}

type AFUStage int32

const (
	AFUStage_STAGE_UNSPECIFIED                       AFUStage = 0 // pre-AFU stages:
	AFUStage_LIVENESS_CHECK                          AFUStage = 310
	AFUStage_DEVICE_DEACTIVATION_AT_VENDOR           AFUStage = 320
	AFUStage_DEVICE_REREGISTRATION_INIT_AT_VENDOR    AFUStage = 330
	AFUStage_DEVICE_REREGISTRATION_ENQUIRY_AT_VENDOR AFUStage = 340
	AFUStage_DEVICE_REACTIVATION_AT_VENDOR           AFUStage = 350
	AFUStage_FI_DEVICE_UPDATE                        AFUStage = 360
	AFUStage_AFU_COMPLETED                           AFUStage = 370
)

// Enum value maps for AFUStage.
var (
	AFUStage_name = map[int32]string{
		0:   "STAGE_UNSPECIFIED",
		310: "LIVENESS_CHECK",
		320: "DEVICE_DEACTIVATION_AT_VENDOR",
		330: "DEVICE_REREGISTRATION_INIT_AT_VENDOR",
		340: "DEVICE_REREGISTRATION_ENQUIRY_AT_VENDOR",
		350: "DEVICE_REACTIVATION_AT_VENDOR",
		360: "FI_DEVICE_UPDATE",
		370: "AFU_COMPLETED",
	}
	AFUStage_value = map[string]int32{
		"STAGE_UNSPECIFIED":                       0,
		"LIVENESS_CHECK":                          310,
		"DEVICE_DEACTIVATION_AT_VENDOR":           320,
		"DEVICE_REREGISTRATION_INIT_AT_VENDOR":    330,
		"DEVICE_REREGISTRATION_ENQUIRY_AT_VENDOR": 340,
		"DEVICE_REACTIVATION_AT_VENDOR":           350,
		"FI_DEVICE_UPDATE":                        360,
		"AFU_COMPLETED":                           370,
	}
)

func (x AFUStage) Enum() *AFUStage {
	p := new(AFUStage)
	*p = x
	return p
}

func (x AFUStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AFUStage) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[13].Descriptor()
}

func (AFUStage) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[13]
}

func (x AFUStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AFUStage.Descriptor instead.
func (AFUStage) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{13}
}

type StageStatus int32

const (
	StageStatus_STAGE_STATUS_UNSPECIFIED StageStatus = 0
	// Stage is in progress
	StageStatus_STAGE_STATUS_IN_PROGRESS StageStatus = 1
	// Stage is completed
	StageStatus_STAGE_STATUS_COMPLETED StageStatus = 2
	// Stage is failed
	StageStatus_STAGE_STATUS_FAILED StageStatus = 3
	// Stage is skipped
	StageStatus_STAGE_STATUS_SKIPPED StageStatus = 4
)

// Enum value maps for StageStatus.
var (
	StageStatus_name = map[int32]string{
		0: "STAGE_STATUS_UNSPECIFIED",
		1: "STAGE_STATUS_IN_PROGRESS",
		2: "STAGE_STATUS_COMPLETED",
		3: "STAGE_STATUS_FAILED",
		4: "STAGE_STATUS_SKIPPED",
	}
	StageStatus_value = map[string]int32{
		"STAGE_STATUS_UNSPECIFIED": 0,
		"STAGE_STATUS_IN_PROGRESS": 1,
		"STAGE_STATUS_COMPLETED":   2,
		"STAGE_STATUS_FAILED":      3,
		"STAGE_STATUS_SKIPPED":     4,
	}
)

func (x StageStatus) Enum() *StageStatus {
	p := new(StageStatus)
	*p = x
	return p
}

func (x StageStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StageStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[14].Descriptor()
}

func (StageStatus) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[14]
}

func (x StageStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StageStatus.Descriptor instead.
func (StageStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{14}
}

type Identifier int32

const (
	Identifier_IDENTIFIER_UNSPECIFIED    Identifier = 0
	Identifier_IDENTIFIER_NEW_PHONE      Identifier = 1
	Identifier_IDENTIFIER_NEW_EMAIL      Identifier = 2
	Identifier_IDENTIFIER_NEW_DEVICE     Identifier = 3
	Identifier_IDENTIFIER_CURRENT_PHONE  Identifier = 4
	Identifier_IDENTIFIER_CURRENT_EMAIL  Identifier = 5
	Identifier_IDENTIFIER_CURRENT_DEVICE Identifier = 6
)

// Enum value maps for Identifier.
var (
	Identifier_name = map[int32]string{
		0: "IDENTIFIER_UNSPECIFIED",
		1: "IDENTIFIER_NEW_PHONE",
		2: "IDENTIFIER_NEW_EMAIL",
		3: "IDENTIFIER_NEW_DEVICE",
		4: "IDENTIFIER_CURRENT_PHONE",
		5: "IDENTIFIER_CURRENT_EMAIL",
		6: "IDENTIFIER_CURRENT_DEVICE",
	}
	Identifier_value = map[string]int32{
		"IDENTIFIER_UNSPECIFIED":    0,
		"IDENTIFIER_NEW_PHONE":      1,
		"IDENTIFIER_NEW_EMAIL":      2,
		"IDENTIFIER_NEW_DEVICE":     3,
		"IDENTIFIER_CURRENT_PHONE":  4,
		"IDENTIFIER_CURRENT_EMAIL":  5,
		"IDENTIFIER_CURRENT_DEVICE": 6,
	}
)

func (x Identifier) Enum() *Identifier {
	p := new(Identifier)
	*p = x
	return p
}

func (x Identifier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Identifier) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[15].Descriptor()
}

func (Identifier) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[15]
}

func (x Identifier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Identifier.Descriptor instead.
func (Identifier) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{15}
}

type Context_CardState int32

const (
	Context_CARD_STATE_UNSPECIFIED Context_CardState = 0
	// user does not have a card in a valid state
	Context_CARD_STATE_INVALID Context_CardState = 1
	// user has activated their card
	Context_CARD_STATE_ACTIVE Context_CardState = 2
	// user has not activated their card or no valid card exist
	Context_CARD_STATE_INACTIVE Context_CardState = 3
	// user's card is updated to active
	Context_CARD_STATE_UPDATED_TO_ACTIVE Context_CardState = 4
)

// Enum value maps for Context_CardState.
var (
	Context_CardState_name = map[int32]string{
		0: "CARD_STATE_UNSPECIFIED",
		1: "CARD_STATE_INVALID",
		2: "CARD_STATE_ACTIVE",
		3: "CARD_STATE_INACTIVE",
		4: "CARD_STATE_UPDATED_TO_ACTIVE",
	}
	Context_CardState_value = map[string]int32{
		"CARD_STATE_UNSPECIFIED":       0,
		"CARD_STATE_INVALID":           1,
		"CARD_STATE_ACTIVE":            2,
		"CARD_STATE_INACTIVE":          3,
		"CARD_STATE_UPDATED_TO_ACTIVE": 4,
	}
)

func (x Context_CardState) Enum() *Context_CardState {
	p := new(Context_CardState)
	*p = x
	return p
}

func (x Context_CardState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Context_CardState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_auth_factor_update_proto_enumTypes[16].Descriptor()
}

func (Context_CardState) Type() protoreflect.EnumType {
	return &file_api_auth_internal_auth_factor_update_proto_enumTypes[16]
}

func (x Context_CardState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Context_CardState.Descriptor instead.
func (Context_CardState) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{3, 0}
}

// Primary Data Model. It tracks the state of the auth factor updates
// from initiation to completion
type AuthFactorUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor_id of the user who is performing the update
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// unique ID for every attempt for update
	Id string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	// Keeps track of overall status of the Auth Factor update attempt
	OverallStatus OverallStatus `protobuf:"varint,3,opt,name=overall_status,json=overallStatus,proto3,enum=auth.afu.OverallStatus" json:"overall_status,omitempty"`
	// Data required to process intermediate states i.e. non-terminal OverallStatus
	Context *Context `protobuf:"bytes,4,opt,name=context,proto3" json:"context,omitempty"`
	// Standard timestamp fields
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Data required to process auth factor update at vendor's end
	VendorContext *VendorContext `protobuf:"bytes,7,opt,name=vendor_context,json=vendorContext,proto3" json:"vendor_context,omitempty"`
	// failure reason to be shown to the user
	FailureReason FailureReason `protobuf:"varint,8,opt,name=failure_reason,json=failureReason,proto3,enum=auth.afu.FailureReason" json:"failure_reason,omitempty"`
	// attempts_history captures all the vendor attempts related to device re registration
	AttemptsHistory *AttemptsHistory `protobuf:"bytes,9,opt,name=attempts_history,json=attemptsHistory,proto3" json:"attempts_history,omitempty"`
}

func (x *AuthFactorUpdate) Reset() {
	*x = AuthFactorUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthFactorUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthFactorUpdate) ProtoMessage() {}

func (x *AuthFactorUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthFactorUpdate.ProtoReflect.Descriptor instead.
func (*AuthFactorUpdate) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{0}
}

func (x *AuthFactorUpdate) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *AuthFactorUpdate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AuthFactorUpdate) GetOverallStatus() OverallStatus {
	if x != nil {
		return x.OverallStatus
	}
	return OverallStatus_OVERALL_STATUS_UNSPECIFIED
}

func (x *AuthFactorUpdate) GetContext() *Context {
	if x != nil {
		return x.Context
	}
	return nil
}

func (x *AuthFactorUpdate) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AuthFactorUpdate) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *AuthFactorUpdate) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *AuthFactorUpdate) GetVendorContext() *VendorContext {
	if x != nil {
		return x.VendorContext
	}
	return nil
}

func (x *AuthFactorUpdate) GetFailureReason() FailureReason {
	if x != nil {
		return x.FailureReason
	}
	return FailureReason_FAILURE_REASON_UNSPECIFIED
}

func (x *AuthFactorUpdate) GetAttemptsHistory() *AttemptsHistory {
	if x != nil {
		return x.AttemptsHistory
	}
	return nil
}

// AttemptsHistory stores all the vendor attempts related data that are done during AFU
type AttemptsHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// re registration attempt
	ReRegAttempts []*ReRegAttempt `protobuf:"bytes,1,rep,name=re_reg_attempts,json=reRegAttempts,proto3" json:"re_reg_attempts,omitempty"`
}

func (x *AttemptsHistory) Reset() {
	*x = AttemptsHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttemptsHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttemptsHistory) ProtoMessage() {}

func (x *AttemptsHistory) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttemptsHistory.ProtoReflect.Descriptor instead.
func (*AttemptsHistory) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{1}
}

func (x *AttemptsHistory) GetReRegAttempts() []*ReRegAttempt {
	if x != nil {
		return x.ReRegAttempts
	}
	return nil
}

type ReRegAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// requestID of the vendor call made
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// failure reason for the re registration attempt
	FailureReason FailureReason `protobuf:"varint,2,opt,name=failure_reason,json=failureReason,proto3,enum=auth.afu.FailureReason" json:"failure_reason,omitempty"`
	// time at which the attempt got failed
	FailedAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=failed_at,json=failedAt,proto3" json:"failed_at,omitempty"`
}

func (x *ReRegAttempt) Reset() {
	*x = ReRegAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReRegAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReRegAttempt) ProtoMessage() {}

func (x *ReRegAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReRegAttempt.ProtoReflect.Descriptor instead.
func (*ReRegAttempt) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{2}
}

func (x *ReRegAttempt) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ReRegAttempt) GetFailureReason() FailureReason {
	if x != nil {
		return x.FailureReason
	}
	return FailureReason_FAILURE_REASON_UNSPECIFIED
}

func (x *ReRegAttempt) GetFailedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FailedAt
	}
	return nil
}

// Context maintains the data required to process the update request
// by the client. It contains statuses of various intermediate actions
// and request parameters for subsequent upstream requests to vendors
// in the workflow of auth factor update. Most of the business logic is
// driven through data inside this.
type Context struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// what's being updated
	AuthFactors []AuthFactor `protobuf:"varint,1,rep,packed,name=auth_factors,json=authFactors,proto3,enum=auth.afu.AuthFactor" json:"auth_factors,omitempty"`
	// New values of auth factor that user wants
	NewValues *AuthFactorValues `protobuf:"bytes,2,opt,name=new_values,json=newValues,proto3" json:"new_values,omitempty"`
	// encrypted payload to be sent to partner bank with request
	EncryptedPayload string `protobuf:"bytes,3,opt,name=encrypted_payload,json=encryptedPayload,proto3" json:"encrypted_payload,omitempty"`
	// Status of credential validation
	CredentialStatuses []*CredentialStatus `protobuf:"bytes,4,rep,name=credential_statuses,json=credentialStatuses,proto3" json:"credential_statuses,omitempty"`
	// If new values has been confirmed by user for update
	UserConfirmationStatus UserConfirmationStatus `protobuf:"varint,5,opt,name=user_confirmation_status,json=userConfirmationStatus,proto3,enum=auth.afu.UserConfirmationStatus" json:"user_confirmation_status,omitempty"`
	// If new values have been updated at partner bank
	VendorRequestStatuses []*VendorRequestStatus `protobuf:"bytes,6,rep,name=vendor_request_statuses,json=vendorRequestStatuses,proto3" json:"vendor_request_statuses,omitempty"`
	// status of email & phone number update in user profile.
	EpifiEmailPhoneNumUpdate RequestStatus `protobuf:"varint,7,opt,name=epifi_email_phone_num_update,json=epifiEmailPhoneNumUpdate,proto3,enum=auth.afu.RequestStatus" json:"epifi_email_phone_num_update,omitempty"`
	// status of device token update in device registration.
	EpifiDeviceUpdate RequestStatus `protobuf:"varint,8,opt,name=epifi_device_update,json=epifiDeviceUpdate,proto3,enum=auth.afu.RequestStatus" json:"epifi_device_update,omitempty"`
	// vendor card ID to be sent to partner bank with request
	VendorCardId string `protobuf:"bytes,9,opt,name=vendor_card_id,json=vendorCardId,proto3" json:"vendor_card_id,omitempty"`
	// current values of auth factor for user
	CurrentValues *AuthFactorValues `protobuf:"bytes,10,opt,name=current_values,json=currentValues,proto3" json:"current_values,omitempty"`
	// debit card pin cred block
	CredBlock string `protobuf:"bytes,11,opt,name=cred_block,json=credBlock,proto3" json:"cred_block,omitempty"`
	// device on which user is going through re-oobe flow
	NewDevice *common.Device `protobuf:"bytes,12,opt,name=new_device,json=newDevice,proto3" json:"new_device,omitempty"`
	// epifi device registration entry deletion status
	DeregisterCurrentDevice RequestStatus `protobuf:"varint,13,opt,name=deregister_current_device,json=deregisterCurrentDevice,proto3,enum=auth.afu.RequestStatus" json:"deregister_current_device,omitempty"`
	// if afu event is published, then value of boolean is true otherwise false.
	AfuCompletionEventPublished bool              `protobuf:"varint,14,opt,name=afu_completion_event_published,json=afuCompletionEventPublished,proto3" json:"afu_completion_event_published,omitempty"`
	CardState                   Context_CardState `protobuf:"varint,15,opt,name=card_state,json=cardState,proto3,enum=auth.afu.Context_CardState" json:"card_state,omitempty"`
	ActorAuthState              *ActorAuthState   `protobuf:"bytes,16,opt,name=actor_auth_state,json=actorAuthState,proto3" json:"actor_auth_state,omitempty"`
	// Check if afu attempt has to undergo manual review based on red list check
	IsLivenessManualReview common.BooleanEnum `protobuf:"varint,17,opt,name=is_liveness_manual_review,json=isLivenessManualReview,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_liveness_manual_review,omitempty"`
	// Card form of the user: Physical or Digital
	CardForm card.CardForm `protobuf:"varint,18,opt,name=card_form,json=cardForm,proto3,enum=card.CardForm" json:"card_form,omitempty"`
	// stores red-list results for auth factors
	AuthFactorsRiskAssessment map[string]risk.Result `protobuf:"bytes,19,rep,name=auth_factors_risk_assessment,json=authFactorsRiskAssessment,proto3" json:"auth_factors_risk_assessment,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=risk.Result"`
	// Sim ID from which Device Registration SMS was sent.
	SimId string `protobuf:"bytes,20,opt,name=sim_id,json=simId,proto3" json:"sim_id,omitempty"`
	// List of products on which user is active at the time of starting AFU attempt
	ExistingActiveProducts []product.ProductType `protobuf:"varint,21,rep,packed,name=existing_active_products,json=existingActiveProducts,proto3,enum=product.ProductType" json:"existing_active_products,omitempty"`
	EkycInfo               *Context_EkycInfo     `protobuf:"bytes,22,opt,name=ekyc_info,json=ekycInfo,proto3" json:"ekyc_info,omitempty"`
	// ATM PIN is required only based on certain criteria, this flag stores if ATM PIN is required on not at the time of AFU attempt
	IsAtmPinRequired common.BooleanEnum `protobuf:"varint,23,opt,name=is_atm_pin_required,json=isAtmPinRequired,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_atm_pin_required,omitempty"`
}

func (x *Context) Reset() {
	*x = Context{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Context) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Context) ProtoMessage() {}

func (x *Context) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Context.ProtoReflect.Descriptor instead.
func (*Context) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{3}
}

func (x *Context) GetAuthFactors() []AuthFactor {
	if x != nil {
		return x.AuthFactors
	}
	return nil
}

func (x *Context) GetNewValues() *AuthFactorValues {
	if x != nil {
		return x.NewValues
	}
	return nil
}

func (x *Context) GetEncryptedPayload() string {
	if x != nil {
		return x.EncryptedPayload
	}
	return ""
}

func (x *Context) GetCredentialStatuses() []*CredentialStatus {
	if x != nil {
		return x.CredentialStatuses
	}
	return nil
}

func (x *Context) GetUserConfirmationStatus() UserConfirmationStatus {
	if x != nil {
		return x.UserConfirmationStatus
	}
	return UserConfirmationStatus_USER_CONFIRMATION_STATUS_UNSPECIFIED
}

func (x *Context) GetVendorRequestStatuses() []*VendorRequestStatus {
	if x != nil {
		return x.VendorRequestStatuses
	}
	return nil
}

func (x *Context) GetEpifiEmailPhoneNumUpdate() RequestStatus {
	if x != nil {
		return x.EpifiEmailPhoneNumUpdate
	}
	return RequestStatus_REQUEST_STATUS_UNSPECIFIED
}

func (x *Context) GetEpifiDeviceUpdate() RequestStatus {
	if x != nil {
		return x.EpifiDeviceUpdate
	}
	return RequestStatus_REQUEST_STATUS_UNSPECIFIED
}

func (x *Context) GetVendorCardId() string {
	if x != nil {
		return x.VendorCardId
	}
	return ""
}

func (x *Context) GetCurrentValues() *AuthFactorValues {
	if x != nil {
		return x.CurrentValues
	}
	return nil
}

func (x *Context) GetCredBlock() string {
	if x != nil {
		return x.CredBlock
	}
	return ""
}

func (x *Context) GetNewDevice() *common.Device {
	if x != nil {
		return x.NewDevice
	}
	return nil
}

func (x *Context) GetDeregisterCurrentDevice() RequestStatus {
	if x != nil {
		return x.DeregisterCurrentDevice
	}
	return RequestStatus_REQUEST_STATUS_UNSPECIFIED
}

func (x *Context) GetAfuCompletionEventPublished() bool {
	if x != nil {
		return x.AfuCompletionEventPublished
	}
	return false
}

func (x *Context) GetCardState() Context_CardState {
	if x != nil {
		return x.CardState
	}
	return Context_CARD_STATE_UNSPECIFIED
}

func (x *Context) GetActorAuthState() *ActorAuthState {
	if x != nil {
		return x.ActorAuthState
	}
	return nil
}

func (x *Context) GetIsLivenessManualReview() common.BooleanEnum {
	if x != nil {
		return x.IsLivenessManualReview
	}
	return common.BooleanEnum(0)
}

func (x *Context) GetCardForm() card.CardForm {
	if x != nil {
		return x.CardForm
	}
	return card.CardForm(0)
}

func (x *Context) GetAuthFactorsRiskAssessment() map[string]risk.Result {
	if x != nil {
		return x.AuthFactorsRiskAssessment
	}
	return nil
}

func (x *Context) GetSimId() string {
	if x != nil {
		return x.SimId
	}
	return ""
}

func (x *Context) GetExistingActiveProducts() []product.ProductType {
	if x != nil {
		return x.ExistingActiveProducts
	}
	return nil
}

func (x *Context) GetEkycInfo() *Context_EkycInfo {
	if x != nil {
		return x.EkycInfo
	}
	return nil
}

func (x *Context) GetIsAtmPinRequired() common.BooleanEnum {
	if x != nil {
		return x.IsAtmPinRequired
	}
	return common.BooleanEnum(0)
}

type ActorAuthState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorAuthStatus ActorAuthStatus        `protobuf:"varint,1,opt,name=actor_auth_status,json=actorAuthStatus,proto3,enum=auth.afu.ActorAuthStatus" json:"actor_auth_status,omitempty"`
	LevelStates     map[uint32]*LevelState `protobuf:"bytes,2,rep,name=level_states,json=levelStates,proto3" json:"level_states,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ActorAuthState) Reset() {
	*x = ActorAuthState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActorAuthState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActorAuthState) ProtoMessage() {}

func (x *ActorAuthState) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActorAuthState.ProtoReflect.Descriptor instead.
func (*ActorAuthState) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{4}
}

func (x *ActorAuthState) GetActorAuthStatus() ActorAuthStatus {
	if x != nil {
		return x.ActorAuthStatus
	}
	return ActorAuthStatus_ACTOR_AUTH_STATUS_UNSPECIFIED
}

func (x *ActorAuthState) GetLevelStates() map[uint32]*LevelState {
	if x != nil {
		return x.LevelStates
	}
	return nil
}

type LevelState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LevelStatus ActorAuthStatus `protobuf:"varint,1,opt,name=level_status,json=levelStatus,proto3,enum=auth.afu.ActorAuthStatus" json:"level_status,omitempty"`
}

func (x *LevelState) Reset() {
	*x = LevelState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LevelState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LevelState) ProtoMessage() {}

func (x *LevelState) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LevelState.ProtoReflect.Descriptor instead.
func (*LevelState) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{5}
}

func (x *LevelState) GetLevelStatus() ActorAuthStatus {
	if x != nil {
		return x.LevelStatus
	}
	return ActorAuthStatus_ACTOR_AUTH_STATUS_UNSPECIFIED
}

// AuthFactorValues stores values that user wants to update to.
type AuthFactorValues struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PhoneNumber *common.PhoneNumber `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Email       string              `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	DeviceToken string              `protobuf:"bytes,3,opt,name=device_token,json=deviceToken,proto3" json:"device_token,omitempty"`
	DeviceId    string              `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
}

func (x *AuthFactorValues) Reset() {
	*x = AuthFactorValues{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthFactorValues) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthFactorValues) ProtoMessage() {}

func (x *AuthFactorValues) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthFactorValues.ProtoReflect.Descriptor instead.
func (*AuthFactorValues) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{6}
}

func (x *AuthFactorValues) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *AuthFactorValues) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *AuthFactorValues) GetDeviceToken() string {
	if x != nil {
		return x.DeviceToken
	}
	return ""
}

func (x *AuthFactorValues) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

// CredentialStatus stores Credential and its verification status
type CredentialStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Credential Credential         `protobuf:"varint,1,opt,name=credential,proto3,enum=auth.afu.Credential" json:"credential,omitempty"`
	Status     VerificationStatus `protobuf:"varint,2,opt,name=status,proto3,enum=auth.afu.VerificationStatus" json:"status,omitempty"`
	// records the level in the actor authentication layer
	// where the credential was updated
	Level uint32 `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	// The timestamp at which the credentialStatus is updated with verification status.
	// This information will be useful for debugging in case of failures,
	// and to analyze which stages have taken longer to completion etc.
	LastUpdatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=last_updated_at,json=lastUpdatedAt,proto3" json:"last_updated_at,omitempty"`
	// The timestamp at which credential verification starts
	StartedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
}

func (x *CredentialStatus) Reset() {
	*x = CredentialStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CredentialStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CredentialStatus) ProtoMessage() {}

func (x *CredentialStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CredentialStatus.ProtoReflect.Descriptor instead.
func (*CredentialStatus) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{7}
}

func (x *CredentialStatus) GetCredential() Credential {
	if x != nil {
		return x.Credential
	}
	return Credential_CREDENTIAL_UNSPECIFIED
}

func (x *CredentialStatus) GetStatus() VerificationStatus {
	if x != nil {
		return x.Status
	}
	return VerificationStatus_VERIFICATION_STATUS_UNSPECIFIED
}

func (x *CredentialStatus) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *CredentialStatus) GetLastUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdatedAt
	}
	return nil
}

func (x *CredentialStatus) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

// VendorRequestStatus stores status of requests made to vendor/partner banks
type VendorRequestStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vendor frontend.Vendor `protobuf:"varint,1,opt,name=vendor,proto3,enum=frontend.Vendor" json:"vendor,omitempty"`
	Status RequestStatus   `protobuf:"varint,2,opt,name=status,proto3,enum=auth.afu.RequestStatus" json:"status,omitempty"`
}

func (x *VendorRequestStatus) Reset() {
	*x = VendorRequestStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VendorRequestStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VendorRequestStatus) ProtoMessage() {}

func (x *VendorRequestStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VendorRequestStatus.ProtoReflect.Descriptor instead.
func (*VendorRequestStatus) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{8}
}

func (x *VendorRequestStatus) GetVendor() frontend.Vendor {
	if x != nil {
		return x.Vendor
	}
	return frontend.Vendor(0)
}

func (x *VendorRequestStatus) GetStatus() RequestStatus {
	if x != nil {
		return x.Status
	}
	return RequestStatus_REQUEST_STATUS_UNSPECIFIED
}

// UpdateVendorContext maintains the data required to update auth factor details on vendor's end.
type VendorContext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// request_id of the device re-registration request that is made while details at vendor's end
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// state of the vendor update process
	State UpdateVendorState `protobuf:"varint,2,opt,name=state,proto3,enum=auth.afu.UpdateVendorState" json:"state,omitempty"`
	// type of failure which resulted in failure of the update process
	FailureType UpdateVendorFailureType `protobuf:"varint,3,opt,name=failure_type,json=failureType,proto3,enum=auth.afu.UpdateVendorFailureType" json:"failure_type,omitempty"`
	// Federal returns "OBE0059: INVALID DEVICE TOKEN" error response
	// on repeated Device Reactivation API requests. This flag
	// will consider it as successful reactivation. We've
	// requested them for "Device Already Reactivated" response code
	// for such scenarios as long term solve.
	InvalidTokenInReactivateAsSuccess bool `protobuf:"varint,4,opt,name=invalid_token_in_reactivate_as_success,json=invalidTokenInReactivateAsSuccess,proto3" json:"invalid_token_in_reactivate_as_success,omitempty"`
	// Stores re-registration status for afu attempt.
	// This allow choosing the right methods for device activation recovery
	// when reactivation status is ambiguous
	ReRegistrationStatus RequestStatus `protobuf:"varint,5,opt,name=re_registration_status,json=reRegistrationStatus,proto3,enum=auth.afu.RequestStatus" json:"re_registration_status,omitempty"`
	// Stores vendor gateway response
	// for re-register device request
	ReRegisterDeviceVendorInitStatus *rpc.Status `protobuf:"bytes,6,opt,name=re_register_device_vendor_init_status,json=reRegisterDeviceVendorInitStatus,proto3" json:"re_register_device_vendor_init_status,omitempty"`
	// Stores afu vendor request initiated timestamp
	VendorRequestStartedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=vendor_request_started_at,json=vendorRequestStartedAt,proto3" json:"vendor_request_started_at,omitempty"`
}

func (x *VendorContext) Reset() {
	*x = VendorContext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VendorContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VendorContext) ProtoMessage() {}

func (x *VendorContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VendorContext.ProtoReflect.Descriptor instead.
func (*VendorContext) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{9}
}

func (x *VendorContext) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *VendorContext) GetState() UpdateVendorState {
	if x != nil {
		return x.State
	}
	return UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED
}

func (x *VendorContext) GetFailureType() UpdateVendorFailureType {
	if x != nil {
		return x.FailureType
	}
	return UpdateVendorFailureType_UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED
}

func (x *VendorContext) GetInvalidTokenInReactivateAsSuccess() bool {
	if x != nil {
		return x.InvalidTokenInReactivateAsSuccess
	}
	return false
}

func (x *VendorContext) GetReRegistrationStatus() RequestStatus {
	if x != nil {
		return x.ReRegistrationStatus
	}
	return RequestStatus_REQUEST_STATUS_UNSPECIFIED
}

func (x *VendorContext) GetReRegisterDeviceVendorInitStatus() *rpc.Status {
	if x != nil {
		return x.ReRegisterDeviceVendorInitStatus
	}
	return nil
}

func (x *VendorContext) GetVendorRequestStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.VendorRequestStartedAt
	}
	return nil
}

// AFUSummary gives the most-important cached details of an afu attempt
type AFUSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AfuId       string                 `protobuf:"bytes,1,opt,name=afu_id,json=afuId,proto3" json:"afu_id,omitempty"`
	AfuStatus   OverallStatus          `protobuf:"varint,2,opt,name=afu_status,json=afuStatus,proto3,enum=auth.afu.OverallStatus" json:"afu_status,omitempty"`
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	AuthFactors []AuthFactor           `protobuf:"varint,5,rep,packed,name=auth_factors,json=authFactors,proto3,enum=auth.afu.AuthFactor" json:"auth_factors,omitempty"`
}

func (x *AFUSummary) Reset() {
	*x = AFUSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AFUSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AFUSummary) ProtoMessage() {}

func (x *AFUSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AFUSummary.ProtoReflect.Descriptor instead.
func (*AFUSummary) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{10}
}

func (x *AFUSummary) GetAfuId() string {
	if x != nil {
		return x.AfuId
	}
	return ""
}

func (x *AFUSummary) GetAfuStatus() OverallStatus {
	if x != nil {
		return x.AfuStatus
	}
	return OverallStatus_OVERALL_STATUS_UNSPECIFIED
}

func (x *AFUSummary) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AFUSummary) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *AFUSummary) GetAuthFactors() []AuthFactor {
	if x != nil {
		return x.AuthFactors
	}
	return nil
}

// Contains eKYC info for the corresponding AFU context
type Context_EkycInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientRequestId string `protobuf:"bytes,1,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
}

func (x *Context_EkycInfo) Reset() {
	*x = Context_EkycInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Context_EkycInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Context_EkycInfo) ProtoMessage() {}

func (x *Context_EkycInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_auth_factor_update_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Context_EkycInfo.ProtoReflect.Descriptor instead.
func (*Context_EkycInfo) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_auth_factor_update_proto_rawDescGZIP(), []int{3, 0}
}

func (x *Context_EkycInfo) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

var File_api_auth_internal_auth_factor_update_proto protoreflect.FileDescriptor

var file_api_auth_internal_auth_factor_update_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64,
	0x2f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x23, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62,
	0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa1, 0x04, 0x0a, 0x10, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3e, 0x0a, 0x0e, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66,
	0x75, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x3e, 0x0a, 0x0e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x12, 0x3e, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x10, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x5f,
	0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x0f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x22, 0x51, 0x0a, 0x0f, 0x41, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x3e, 0x0a, 0x0f,
	0x72, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75,
	0x2e, 0x52, 0x65, 0x52, 0x65, 0x67, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x0d, 0x72,
	0x65, 0x52, 0x65, 0x67, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x22, 0xa6, 0x01, 0x0a,
	0x0c, 0x52, 0x65, 0x52, 0x65, 0x67, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0e,
	0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e,
	0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x09,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x41, 0x74, 0x22, 0xbd, 0x0e, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x37, 0x0a, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61,
	0x66, 0x75, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x0b, 0x61,
	0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x6e, 0x65,
	0x77, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x09, 0x6e, 0x65, 0x77, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x65, 0x64, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x12, 0x4b, 0x0a, 0x13, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x12, 0x63, 0x72, 0x65,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12,
	0x5a, 0x0a, 0x18, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x16, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x55, 0x0a, 0x17, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x15, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x73, 0x12, 0x57, 0x0a, 0x1c, 0x65, 0x70, 0x69, 0x66, 0x69, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x61, 0x66, 0x75, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x18, 0x65, 0x70, 0x69, 0x66, 0x69, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x47, 0x0a, 0x13, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x61, 0x66, 0x75, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x11, 0x65, 0x70, 0x69, 0x66, 0x69, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x41, 0x75,
	0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x0d,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x39, 0x0a, 0x0a,
	0x6e, 0x65, 0x77, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x09, 0x6e, 0x65,
	0x77, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x53, 0x0a, 0x19, 0x64, 0x65, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x17, 0x64, 0x65, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x43, 0x0a, 0x1e,
	0x61, 0x66, 0x75, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x61, 0x66, 0x75, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65,
	0x64, 0x12, 0x3a, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75,
	0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x09, 0x63, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x42, 0x0a,
	0x10, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61,
	0x66, 0x75, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x5a, 0x0a, 0x19, 0x69, 0x73, 0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61,
	0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x16, 0x69, 0x73, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x2b, 0x0a,
	0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x0e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x46, 0x6f, 0x72, 0x6d,
	0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x71, 0x0a, 0x1c, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x61, 0x73, 0x73, 0x65, 0x73, 0x73, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52,
	0x69, 0x73, 0x6b, 0x41, 0x73, 0x73, 0x65, 0x73, 0x73, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x19, 0x61, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52,
	0x69, 0x73, 0x6b, 0x41, 0x73, 0x73, 0x65, 0x73, 0x73, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x15, 0x0a,
	0x06, 0x73, 0x69, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x69, 0x6d, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x18, 0x65, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73,
	0x18, 0x15, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x16, 0x65, 0x78,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x65, 0x6b, 0x79, 0x63, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61,
	0x66, 0x75, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x45, 0x6b, 0x79, 0x63, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x65, 0x6b, 0x79, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4e, 0x0a,
	0x13, 0x69, 0x73, 0x5f, 0x61, 0x74, 0x6d, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x10, 0x69, 0x73, 0x41,
	0x74, 0x6d, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x1a, 0x36, 0x0a,
	0x08, 0x45, 0x6b, 0x79, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x1a, 0x5a, 0x0a, 0x1e, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x73, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x73, 0x73, 0x65, 0x73, 0x73, 0x6d, 0x65,
	0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x22, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x91, 0x01, 0x0a, 0x09, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x1a, 0x0a, 0x16, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56,
	0x45, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x56, 0x45, 0x10, 0x04, 0x22, 0xfb, 0x01, 0x0a, 0x0e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41,
	0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x45, 0x0a, 0x11, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x4c, 0x0a, 0x0c, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75,
	0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x1a, 0x54, 0x0a,
	0x10, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x4a, 0x0a, 0x0a, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61,
	0x66, 0x75, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0xac, 0x01, 0x0a, 0x10, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x21,
	0x0a, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x93,
	0x02, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61,
	0x66, 0x75, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x34, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x61, 0x66, 0x75, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x42, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x22, 0x70, 0x0a, 0x13, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x06, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xfe, 0x03, 0x0a, 0x0d, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66,
	0x75, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x44, 0x0a, 0x0c, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x21, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x51, 0x0a, 0x26, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f,
	0x61, 0x73, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x21, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e,
	0x52, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x41, 0x73, 0x53, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x12, 0x4d, 0x0a, 0x16, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x14, 0x72, 0x65,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x5c, 0x0a, 0x25, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f,
	0x69, 0x6e, 0x69, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x20,
	0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x49, 0x6e, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x55, 0x0a, 0x19, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x16, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x8a, 0x02, 0x0a, 0x0a, 0x41, 0x46, 0x55, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x66, 0x75, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x66, 0x75, 0x49, 0x64, 0x12, 0x36, 0x0a,
	0x0a, 0x61, 0x66, 0x75, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x4f, 0x76, 0x65,
	0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x61, 0x66, 0x75, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x37, 0x0a, 0x0c, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x41, 0x75, 0x74,
	0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x73, 0x2a, 0x58, 0x0a, 0x0a, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f,
	0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0a, 0x0a, 0x06, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x50,
	0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x53, 0x49, 0x4d, 0x10, 0x04, 0x2a, 0xd8,
	0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16,
	0x0a, 0x12, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x10, 0x02, 0x12,
	0x10, 0x0a, 0x0c, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10,
	0x03, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x48, 0x4f, 0x4e,
	0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x04, 0x12, 0x1b, 0x0a,
	0x17, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50,
	0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x10, 0x05, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x10, 0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x49,
	0x4d, 0x10, 0x07, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x5f, 0x53, 0x49, 0x4d, 0x10, 0x08, 0x2a, 0xfa, 0x01, 0x0a, 0x0a, 0x43, 0x72,
	0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x52, 0x45, 0x44,
	0x45, 0x4e, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x48,
	0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52,
	0x45, 0x44, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45,
	0x53, 0x53, 0x5f, 0x46, 0x4d, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x04, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x54, 0x4d, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x4f,
	0x47, 0x47, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x06, 0x12, 0x11, 0x0a, 0x0d, 0x4e, 0x4f, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x45,
	0x4e, 0x54, 0x49, 0x41, 0x4c, 0x10, 0x07, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x41, 0x44, 0x48, 0x41,
	0x41, 0x52, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x08, 0x2a, 0xa2, 0x01, 0x0a, 0x0d, 0x4f, 0x76, 0x65, 0x72, 0x61,
	0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x56, 0x45, 0x52,
	0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x56, 0x45, 0x52,
	0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52,
	0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x4f, 0x56, 0x45, 0x52,
	0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x45, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x4f, 0x56, 0x45, 0x52, 0x41, 0x4c,
	0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x03, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x56, 0x45, 0x52, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x55, 0x43, 0x4b, 0x10, 0x04, 0x2a, 0x68, 0x0a, 0x16, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x24, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x43, 0x4f,
	0x4e, 0x46, 0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x11, 0x0a, 0x0d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43,
	0x54, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x7e, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0x03, 0x2a, 0xaa, 0x02, 0x0a, 0x12, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x1f,
	0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x18, 0x0a, 0x14, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52,
	0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x45, 0x52, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x43, 0x4f,
	0x52, 0x44, 0x45, 0x44, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x4b,
	0x49, 0x50, 0x50, 0x45, 0x44, 0x10, 0x07, 0x12, 0x29, 0x0a, 0x25, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54,
	0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x08, 0x2a, 0x87, 0x07, 0x0a, 0x0d, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x46, 0x4d, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x10,
	0x01, 0x12, 0x11, 0x0a, 0x0d, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x41, 0x54, 0x4d, 0x5f, 0x50,
	0x49, 0x4e, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x54, 0x4d, 0x5f, 0x50, 0x49, 0x4e, 0x5f,
	0x4d, 0x41, 0x58, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x10, 0x03, 0x12, 0x15, 0x0a,
	0x11, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52,
	0x45, 0x44, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x4e, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x05, 0x12, 0x13, 0x0a, 0x0f, 0x4e, 0x4f, 0x5f, 0x53, 0x4d,
	0x53, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x44, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f,
	0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10,
	0x07, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x41, 0x59, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x4d, 0x49, 0x53,
	0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x08, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e,
	0x44, 0x4f, 0x52, 0x10, 0x09, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x43, 0x52, 0x45, 0x44, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x0a, 0x12, 0x17, 0x0a,
	0x13, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x4c, 0x49, 0x43, 0x54, 0x10, 0x0b, 0x12, 0x15, 0x0a, 0x11, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x4f,
	0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x49, 0x41, 0x4e, 0x54, 0x10, 0x0c, 0x12, 0x21, 0x0a,
	0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44,
	0x5f, 0x54, 0x4f, 0x5f, 0x4f, 0x4c, 0x44, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x0d,
	0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x4f, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x0e, 0x12,
	0x1f, 0x0a, 0x1b, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x4d, 0x5f, 0x4d,
	0x41, 0x4e, 0x55, 0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x0f,
	0x12, 0x17, 0x0a, 0x13, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x4d, 0x5f,
	0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x10, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x4d, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x11, 0x12, 0x26, 0x0a, 0x22, 0x4c, 0x49,
	0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x4d, 0x5f, 0x53, 0x54, 0x55, 0x43, 0x4b, 0x5f,
	0x49, 0x4e, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57,
	0x10, 0x12, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x44, 0x49,
	0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x43, 0x48, 0x5f, 0x56, 0x45, 0x4e, 0x44,
	0x4f, 0x52, 0x10, 0x13, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f,
	0x4d, 0x45, 0x52, 0x10, 0x14, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x4f, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x10, 0x15, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x16, 0x12,
	0x1a, 0x0a, 0x16, 0x41, 0x46, 0x55, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x4d, 0x4f, 0x44, 0x45,
	0x4c, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x17, 0x12, 0x22, 0x0a, 0x1e, 0x44,
	0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4f, 0x52, 0x41, 0x52, 0x49, 0x4c,
	0x59, 0x5f, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x44, 0x10, 0x18, 0x12,
	0x10, 0x0a, 0x0c, 0x41, 0x46, 0x55, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x4c, 0x49, 0x43, 0x54, 0x10,
	0x19, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x4d, 0x4f, 0x42,
	0x49, 0x4c, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x1a, 0x12, 0x2b,
	0x0a, 0x27, 0x44, 0x45, 0x56, 0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49,
	0x52, 0x59, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x5f,
	0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x1b, 0x12, 0x1b, 0x0a, 0x17, 0x55,
	0x4e, 0x45, 0x58, 0x50, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x1c, 0x12, 0x21, 0x0a, 0x1c, 0x55, 0x4e, 0x4d, 0x41,
	0x50, 0x50, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x56, 0x52, 0x45, 0x47, 0x5f, 0x49, 0x4e, 0x49, 0x54,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0xe6, 0x07, 0x12, 0x24, 0x0a, 0x1f, 0x55,
	0x4e, 0x4d, 0x41, 0x50, 0x50, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x56, 0x52, 0x45, 0x47, 0x5f, 0x45,
	0x4e, 0x51, 0x55, 0x49, 0x52, 0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0xe7,
	0x07, 0x12, 0x19, 0x0a, 0x14, 0x41, 0x46, 0x55, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f,
	0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0xe8, 0x07, 0x2a, 0xbf, 0x02, 0x0a,
	0x19, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2d, 0x0a, 0x29, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x30, 0x0a, 0x2c, 0x41, 0x55, 0x54,
	0x48, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x41,
	0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4f, 0x4e,
	0x54, 0x45, 0x58, 0x54, 0x10, 0x02, 0x12, 0x30, 0x0a, 0x2c, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x46,
	0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x45, 0x58, 0x54, 0x10, 0x03, 0x12, 0x30, 0x0a, 0x2c, 0x41, 0x55, 0x54, 0x48,
	0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52,
	0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x32, 0x0a, 0x2e, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x54, 0x54, 0x45,
	0x4d, 0x50, 0x54, 0x53, 0x5f, 0x48, 0x49, 0x53, 0x54, 0x4f, 0x52, 0x59, 0x10, 0x05, 0x2a, 0x95,
	0x01, 0x0a, 0x0f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x41, 0x55, 0x54, 0x48,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52,
	0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x43, 0x54, 0x4f,
	0x52, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x43, 0x54, 0x4f, 0x52,
	0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x55, 0x52, 0x45, 0x10, 0x03, 0x2a, 0xbf, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x1f,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01,
	0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x44, 0x10,
	0x02, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x45, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x45,
	0x44, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x45, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x45, 0x44, 0x10,
	0x04, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x06,
	0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x07, 0x12, 0x1a, 0x0a, 0x16,
	0x41, 0x4d, 0x42, 0x49, 0x47, 0x55, 0x4f, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x08, 0x2a, 0xce, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x26, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x56,
	0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x17, 0x0a, 0x13, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x45, 0x52,
	0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x18, 0x0a,
	0x14, 0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49, 0x52, 0x59, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x41, 0x43, 0x54,
	0x49, 0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x59,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x2a, 0x99, 0x01, 0x0a, 0x12, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x24, 0x0a, 0x20, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f,
	0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x4f, 0x4e, 0x54, 0x49, 0x4e,
	0x55, 0x45, 0x5f, 0x41, 0x46, 0x55, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x02, 0x12,
	0x12, 0x0a, 0x0e, 0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x41, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x41, 0x53, 0x5f, 0x53,
	0x54, 0x55, 0x43, 0x4b, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x05, 0x2a, 0x82, 0x02, 0x0a, 0x08, 0x41, 0x46, 0x55, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0e, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0xb6, 0x02, 0x12, 0x22,
	0x0a, 0x1d, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10,
	0xc0, 0x02, 0x12, 0x29, 0x0a, 0x24, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x52,
	0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x49, 0x54,
	0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0xca, 0x02, 0x12, 0x2c, 0x0a,
	0x27, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49, 0x52, 0x59, 0x5f, 0x41,
	0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0xd4, 0x02, 0x12, 0x22, 0x0a, 0x1d, 0x44,
	0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0xde, 0x02, 0x12,
	0x15, 0x0a, 0x10, 0x46, 0x49, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x10, 0xe8, 0x02, 0x12, 0x12, 0x0a, 0x0d, 0x41, 0x46, 0x55, 0x5f, 0x43, 0x4f,
	0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0xf2, 0x02, 0x2a, 0x98, 0x01, 0x0a, 0x0b, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47,
	0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44,
	0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x53,
	0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x4b, 0x49, 0x50,
	0x50, 0x45, 0x44, 0x10, 0x04, 0x2a, 0xd2, 0x01, 0x0a, 0x0a, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49,
	0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x18, 0x0a, 0x14, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4e,
	0x45, 0x57, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x45, 0x4d, 0x41,
	0x49, 0x4c, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49,
	0x45, 0x52, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x03, 0x12,
	0x1c, 0x0a, 0x18, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x43, 0x55,
	0x52, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x04, 0x12, 0x1c, 0x0a,
	0x18, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x43, 0x55, 0x52, 0x52,
	0x45, 0x4e, 0x54, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e,
	0x54, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x06, 0x42, 0x4a, 0x0a, 0x23, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66,
	0x75, 0x5a, 0x23, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75,
	0x74, 0x68, 0x2f, 0x61, 0x66, 0x75, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_internal_auth_factor_update_proto_rawDescOnce sync.Once
	file_api_auth_internal_auth_factor_update_proto_rawDescData = file_api_auth_internal_auth_factor_update_proto_rawDesc
)

func file_api_auth_internal_auth_factor_update_proto_rawDescGZIP() []byte {
	file_api_auth_internal_auth_factor_update_proto_rawDescOnce.Do(func() {
		file_api_auth_internal_auth_factor_update_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_internal_auth_factor_update_proto_rawDescData)
	})
	return file_api_auth_internal_auth_factor_update_proto_rawDescData
}

var file_api_auth_internal_auth_factor_update_proto_enumTypes = make([]protoimpl.EnumInfo, 17)
var file_api_auth_internal_auth_factor_update_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_api_auth_internal_auth_factor_update_proto_goTypes = []interface{}{
	(AuthFactor)(0),                // 0: auth.afu.AuthFactor
	(Combination)(0),               // 1: auth.afu.Combination
	(Credential)(0),                // 2: auth.afu.Credential
	(OverallStatus)(0),             // 3: auth.afu.OverallStatus
	(UserConfirmationStatus)(0),    // 4: auth.afu.UserConfirmationStatus
	(RequestStatus)(0),             // 5: auth.afu.RequestStatus
	(VerificationStatus)(0),        // 6: auth.afu.VerificationStatus
	(FailureReason)(0),             // 7: auth.afu.FailureReason
	(AuthFactorUpdateFieldMask)(0), // 8: auth.afu.AuthFactorUpdateFieldMask
	(ActorAuthStatus)(0),           // 9: auth.afu.ActorAuthStatus
	(UpdateVendorState)(0),         // 10: auth.afu.UpdateVendorState
	(UpdateVendorFailureType)(0),   // 11: auth.afu.UpdateVendorFailureType
	(UpdateVendorAction)(0),        // 12: auth.afu.UpdateVendorAction
	(AFUStage)(0),                  // 13: auth.afu.AFUStage
	(StageStatus)(0),               // 14: auth.afu.StageStatus
	(Identifier)(0),                // 15: auth.afu.Identifier
	(Context_CardState)(0),         // 16: auth.afu.Context.CardState
	(*AuthFactorUpdate)(nil),       // 17: auth.afu.AuthFactorUpdate
	(*AttemptsHistory)(nil),        // 18: auth.afu.AttemptsHistory
	(*ReRegAttempt)(nil),           // 19: auth.afu.ReRegAttempt
	(*Context)(nil),                // 20: auth.afu.Context
	(*ActorAuthState)(nil),         // 21: auth.afu.ActorAuthState
	(*LevelState)(nil),             // 22: auth.afu.LevelState
	(*AuthFactorValues)(nil),       // 23: auth.afu.AuthFactorValues
	(*CredentialStatus)(nil),       // 24: auth.afu.CredentialStatus
	(*VendorRequestStatus)(nil),    // 25: auth.afu.VendorRequestStatus
	(*VendorContext)(nil),          // 26: auth.afu.VendorContext
	(*AFUSummary)(nil),             // 27: auth.afu.AFUSummary
	(*Context_EkycInfo)(nil),       // 28: auth.afu.Context.EkycInfo
	nil,                            // 29: auth.afu.Context.AuthFactorsRiskAssessmentEntry
	nil,                            // 30: auth.afu.ActorAuthState.LevelStatesEntry
	(*timestamppb.Timestamp)(nil),  // 31: google.protobuf.Timestamp
	(*common.Device)(nil),          // 32: api.typesv2.common.Device
	(common.BooleanEnum)(0),        // 33: api.typesv2.common.BooleanEnum
	(card.CardForm)(0),             // 34: card.CardForm
	(product.ProductType)(0),       // 35: product.ProductType
	(*common.PhoneNumber)(nil),     // 36: api.typesv2.common.PhoneNumber
	(frontend.Vendor)(0),           // 37: frontend.Vendor
	(*rpc.Status)(nil),             // 38: rpc.Status
	(risk.Result)(0),               // 39: risk.Result
}
var file_api_auth_internal_auth_factor_update_proto_depIdxs = []int32{
	3,  // 0: auth.afu.AuthFactorUpdate.overall_status:type_name -> auth.afu.OverallStatus
	20, // 1: auth.afu.AuthFactorUpdate.context:type_name -> auth.afu.Context
	31, // 2: auth.afu.AuthFactorUpdate.created_at:type_name -> google.protobuf.Timestamp
	31, // 3: auth.afu.AuthFactorUpdate.deleted_at:type_name -> google.protobuf.Timestamp
	31, // 4: auth.afu.AuthFactorUpdate.updated_at:type_name -> google.protobuf.Timestamp
	26, // 5: auth.afu.AuthFactorUpdate.vendor_context:type_name -> auth.afu.VendorContext
	7,  // 6: auth.afu.AuthFactorUpdate.failure_reason:type_name -> auth.afu.FailureReason
	18, // 7: auth.afu.AuthFactorUpdate.attempts_history:type_name -> auth.afu.AttemptsHistory
	19, // 8: auth.afu.AttemptsHistory.re_reg_attempts:type_name -> auth.afu.ReRegAttempt
	7,  // 9: auth.afu.ReRegAttempt.failure_reason:type_name -> auth.afu.FailureReason
	31, // 10: auth.afu.ReRegAttempt.failed_at:type_name -> google.protobuf.Timestamp
	0,  // 11: auth.afu.Context.auth_factors:type_name -> auth.afu.AuthFactor
	23, // 12: auth.afu.Context.new_values:type_name -> auth.afu.AuthFactorValues
	24, // 13: auth.afu.Context.credential_statuses:type_name -> auth.afu.CredentialStatus
	4,  // 14: auth.afu.Context.user_confirmation_status:type_name -> auth.afu.UserConfirmationStatus
	25, // 15: auth.afu.Context.vendor_request_statuses:type_name -> auth.afu.VendorRequestStatus
	5,  // 16: auth.afu.Context.epifi_email_phone_num_update:type_name -> auth.afu.RequestStatus
	5,  // 17: auth.afu.Context.epifi_device_update:type_name -> auth.afu.RequestStatus
	23, // 18: auth.afu.Context.current_values:type_name -> auth.afu.AuthFactorValues
	32, // 19: auth.afu.Context.new_device:type_name -> api.typesv2.common.Device
	5,  // 20: auth.afu.Context.deregister_current_device:type_name -> auth.afu.RequestStatus
	16, // 21: auth.afu.Context.card_state:type_name -> auth.afu.Context.CardState
	21, // 22: auth.afu.Context.actor_auth_state:type_name -> auth.afu.ActorAuthState
	33, // 23: auth.afu.Context.is_liveness_manual_review:type_name -> api.typesv2.common.BooleanEnum
	34, // 24: auth.afu.Context.card_form:type_name -> card.CardForm
	29, // 25: auth.afu.Context.auth_factors_risk_assessment:type_name -> auth.afu.Context.AuthFactorsRiskAssessmentEntry
	35, // 26: auth.afu.Context.existing_active_products:type_name -> product.ProductType
	28, // 27: auth.afu.Context.ekyc_info:type_name -> auth.afu.Context.EkycInfo
	33, // 28: auth.afu.Context.is_atm_pin_required:type_name -> api.typesv2.common.BooleanEnum
	9,  // 29: auth.afu.ActorAuthState.actor_auth_status:type_name -> auth.afu.ActorAuthStatus
	30, // 30: auth.afu.ActorAuthState.level_states:type_name -> auth.afu.ActorAuthState.LevelStatesEntry
	9,  // 31: auth.afu.LevelState.level_status:type_name -> auth.afu.ActorAuthStatus
	36, // 32: auth.afu.AuthFactorValues.phone_number:type_name -> api.typesv2.common.PhoneNumber
	2,  // 33: auth.afu.CredentialStatus.credential:type_name -> auth.afu.Credential
	6,  // 34: auth.afu.CredentialStatus.status:type_name -> auth.afu.VerificationStatus
	31, // 35: auth.afu.CredentialStatus.last_updated_at:type_name -> google.protobuf.Timestamp
	31, // 36: auth.afu.CredentialStatus.started_at:type_name -> google.protobuf.Timestamp
	37, // 37: auth.afu.VendorRequestStatus.vendor:type_name -> frontend.Vendor
	5,  // 38: auth.afu.VendorRequestStatus.status:type_name -> auth.afu.RequestStatus
	10, // 39: auth.afu.VendorContext.state:type_name -> auth.afu.UpdateVendorState
	11, // 40: auth.afu.VendorContext.failure_type:type_name -> auth.afu.UpdateVendorFailureType
	5,  // 41: auth.afu.VendorContext.re_registration_status:type_name -> auth.afu.RequestStatus
	38, // 42: auth.afu.VendorContext.re_register_device_vendor_init_status:type_name -> rpc.Status
	31, // 43: auth.afu.VendorContext.vendor_request_started_at:type_name -> google.protobuf.Timestamp
	3,  // 44: auth.afu.AFUSummary.afu_status:type_name -> auth.afu.OverallStatus
	31, // 45: auth.afu.AFUSummary.created_at:type_name -> google.protobuf.Timestamp
	31, // 46: auth.afu.AFUSummary.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 47: auth.afu.AFUSummary.auth_factors:type_name -> auth.afu.AuthFactor
	39, // 48: auth.afu.Context.AuthFactorsRiskAssessmentEntry.value:type_name -> risk.Result
	22, // 49: auth.afu.ActorAuthState.LevelStatesEntry.value:type_name -> auth.afu.LevelState
	50, // [50:50] is the sub-list for method output_type
	50, // [50:50] is the sub-list for method input_type
	50, // [50:50] is the sub-list for extension type_name
	50, // [50:50] is the sub-list for extension extendee
	0,  // [0:50] is the sub-list for field type_name
}

func init() { file_api_auth_internal_auth_factor_update_proto_init() }
func file_api_auth_internal_auth_factor_update_proto_init() {
	if File_api_auth_internal_auth_factor_update_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_internal_auth_factor_update_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthFactorUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_auth_factor_update_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttemptsHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_auth_factor_update_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReRegAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_auth_factor_update_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Context); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_auth_factor_update_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActorAuthState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_auth_factor_update_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LevelState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_auth_factor_update_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthFactorValues); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_auth_factor_update_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CredentialStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_auth_factor_update_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VendorRequestStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_auth_factor_update_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VendorContext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_auth_factor_update_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AFUSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_auth_factor_update_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Context_EkycInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_internal_auth_factor_update_proto_rawDesc,
			NumEnums:      17,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_internal_auth_factor_update_proto_goTypes,
		DependencyIndexes: file_api_auth_internal_auth_factor_update_proto_depIdxs,
		EnumInfos:         file_api_auth_internal_auth_factor_update_proto_enumTypes,
		MessageInfos:      file_api_auth_internal_auth_factor_update_proto_msgTypes,
	}.Build()
	File_api_auth_internal_auth_factor_update_proto = out.File
	file_api_auth_internal_auth_factor_update_proto_rawDesc = nil
	file_api_auth_internal_auth_factor_update_proto_goTypes = nil
	file_api_auth_internal_auth_factor_update_proto_depIdxs = nil
}
