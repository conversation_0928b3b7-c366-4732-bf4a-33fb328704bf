package afu

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"encoding/json"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Valuer interface implementation for storing the data in string format in DB
func (o *Context) Value() (driver.Value, error) {
	if o == nil {
		return nil, nil
	}
	return protojson.Marshal(o)
}

// Scanner interface implementation for parsing data while reading from DB
func (o *Context) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		opt := protojson.UnmarshalOptions{DiscardUnknown: true}
		return backport.SafeUnmarshal(opt.Unmarshal, val, o)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}

// Valuer interface implementation for storing the data in string format in DB
func (o OverallStatus) Value() (driver.Value, error) {
	return o.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (o *OverallStatus) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := OverallStatus_value[val]
	*o = OverallStatus(valInt)
	return nil
}

func (x FailureReason) Value() (driver.Value, error) {
	return x.String(), nil
}

func (x *FailureReason) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := FailureReason_value[val]
	*x = FailureReason(valInt)
	return nil
}

func (x FailureReason) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

func (x *FailureReason) UnmarshalJSON(inp []byte) error {
	var val string
	err := json.Unmarshal(inp, &val)
	if err != nil {
		return err
	}
	*x = FailureReason(FailureReason_value[val])
	return nil
}

// Valuer interface implementation for storing the data in string format in DB
func (o *VendorContext) Value() (driver.Value, error) {
	if o == nil {
		return nil, nil
	}
	return protojson.Marshal(o)
}

// Scanner interface implementation for parsing data while reading from DB
func (o *VendorContext) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, o)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}

func (x *AuthFactorUpdate) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, x)
}

func (x *AuthFactorUpdate) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(x)
}

func (o *AttemptsHistory) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, o)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}

func (o *AttemptsHistory) Value() (driver.Value, error) {
	if o == nil {
		return nil, nil
	}
	return protojson.Marshal(o)
}
