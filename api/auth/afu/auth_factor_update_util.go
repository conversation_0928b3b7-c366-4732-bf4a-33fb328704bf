package afu

import (
	"github.com/samber/lo"
)

func (x *Context) GetCredentialStatus(credential Credential) *CredentialStatus {
	if credentialStatus, ok := lo.Find(x.GetCredentialStatuses(), func(credentialStatus *CredentialStatus) bool {
		return credentialStatus.GetCredential() == credential
	}); ok {
		return credentialStatus
	}
	return nil
}

func (c Combination) IsPhoneOrPhoneDeviceUpdate() bool {
	return c == Combination_UPDATE_DEVICE_PHONE_NUM || c == Combination_UPDATE_PHONE_NUM
}

func (c Combination) IsPhoneOnlyUpdate() bool {
	return c == Combination_UPDATE_PHONE_NUM
}

func (s VerificationStatus) IsSkipped() bool {
	return s == VerificationStatus_VERIFICATION_STATUS_SKIPPED
}
