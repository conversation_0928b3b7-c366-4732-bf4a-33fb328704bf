// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/internal/troubleshooting.proto

package afu

import (
	card "github.com/epifi/gamma/api/card"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DiagnosisCode int32

const (
	DiagnosisCode_UNSPECIFIED DiagnosisCode = 0
	DiagnosisCode_UNKNOWN     DiagnosisCode = 1
	DiagnosisCode_OK          DiagnosisCode = 2
	// LIVENESS
	DiagnosisCode_LIVENESS_IN_PROGRESS                  DiagnosisCode = 101
	DiagnosisCode_LIVENESS_MAX_RETRIES                  DiagnosisCode = 102
	DiagnosisCode_LIVENESS_STUCK_IN_MANUAL_REVIEW       DiagnosisCode = 103
	DiagnosisCode_LIVENESS_FACEMATCH_MANUALLY_FAILED    DiagnosisCode = 104
	DiagnosisCode_EKYC_IN_PROGRESS                      DiagnosisCode = 105
	DiagnosisCode_EKYC_FAILED_OTHER_REASONS             DiagnosisCode = 106
	DiagnosisCode_EKYC_AADHAAR_MOBILE_VALIDATION_FAILED DiagnosisCode = 107
	// DEVICE REREG INIT
	DiagnosisCode_INITREREG_SMS_VALIDATION_ERROR DiagnosisCode = 203
	DiagnosisCode_INITREREG_BAD_REQUEST          DiagnosisCode = 204
	// DEVICE REREG
	DiagnosisCode_REREG_PENDING_AT_VENDOR DiagnosisCode = 301
	// User entered incorrect ATM PIN
	DiagnosisCode_REREG_ATM_PIN_INCORRECT DiagnosisCode = 302
	// User has entered incorrect ATM PIN max times.
	// No more retries allowed.
	DiagnosisCode_REREG_ATM_PIN_MAX_RETRIES DiagnosisCode = 303
	// PIN Validation not allowed after Pin Reset (Should do some other card txn before this)
	DiagnosisCode_REREG_CARD_TXN_REQUIRED DiagnosisCode = 304
	// CAF status = 0 or 9 (Card Status Invalid/Issued status).
	// User needs to activate and set pin first via IVR.
	DiagnosisCode_REREG_CARD_INACTIVE DiagnosisCode = 305
	// Device Registration related error
	DiagnosisCode_REREG_NO_SMS_RECEIVED DiagnosisCode = 306
	// Device Registration related error
	DiagnosisCode_REREG_MOBILE_MISMATCH DiagnosisCode = 307
	// Device Registration related error
	DiagnosisCode_REREG_PAYLOAD_MISMATCH DiagnosisCode = 308
	// Internal error in device re-registration
	// could be due to cred block decryption
	DiagnosisCode_REREG_INTERNAL_ERROR_AT_VENDOR DiagnosisCode = 309
	// Error parsing cred block at vendor
	DiagnosisCode_REREG_INVALID_CRED_BLOCK DiagnosisCode = 310
	// Auth factors are conflicting with onboarding attempt
	// REREG_ONBOARDING_CONFLICT = 311;
	// user's kyc details not updated
	DiagnosisCode_REREG_KYC_NON_COMPLIANT DiagnosisCode = 312
	// card is not linked to old number
	DiagnosisCode_REREG_CARD_NOT_LINKED_TO_OLD_NUMBER DiagnosisCode = 313
	// card is hot listed
	DiagnosisCode_REREG_LOST_CARD DiagnosisCode = 314
	// The card is blocked from Federal's end but active on FI
	DiagnosisCode_REREG_NO_CARD_RECORD DiagnosisCode = 315
	// when account is inactive
	DiagnosisCode_REREG_ACCOUNT_INACTIVE DiagnosisCode = 316
	// user has changed the mobile number independently with bank/vendor and on Fi we have the old number
	DiagnosisCode_REREG_DEVICE_TEMPORARILY_DEACTIVATED DiagnosisCode = 317
	// DEVICE ACTIVATION  (for both: deactivation + reactivation)
	DiagnosisCode_DEVICEACTIVATION_PENDING_AT_VENDOR DiagnosisCode = 401
	DiagnosisCode_DEVICEACTIVATION_FAILED            DiagnosisCode = 402
	// FI DEVICE UPDATE
	DiagnosisCode_FIUPDATE_DEVICE_PENDING DiagnosisCode = 501
	DiagnosisCode_FIUPDATE_USER_PENDING   DiagnosisCode = 502
	// ATM PIN
	DiagnosisCode_ATM_PIN_REQUIRED DiagnosisCode = 601
	// User confirmation
	DiagnosisCode_USER_CONFIRMATION_REQUIRED DiagnosisCode = 701
)

// Enum value maps for DiagnosisCode.
var (
	DiagnosisCode_name = map[int32]string{
		0:   "UNSPECIFIED",
		1:   "UNKNOWN",
		2:   "OK",
		101: "LIVENESS_IN_PROGRESS",
		102: "LIVENESS_MAX_RETRIES",
		103: "LIVENESS_STUCK_IN_MANUAL_REVIEW",
		104: "LIVENESS_FACEMATCH_MANUALLY_FAILED",
		105: "EKYC_IN_PROGRESS",
		106: "EKYC_FAILED_OTHER_REASONS",
		107: "EKYC_AADHAAR_MOBILE_VALIDATION_FAILED",
		203: "INITREREG_SMS_VALIDATION_ERROR",
		204: "INITREREG_BAD_REQUEST",
		301: "REREG_PENDING_AT_VENDOR",
		302: "REREG_ATM_PIN_INCORRECT",
		303: "REREG_ATM_PIN_MAX_RETRIES",
		304: "REREG_CARD_TXN_REQUIRED",
		305: "REREG_CARD_INACTIVE",
		306: "REREG_NO_SMS_RECEIVED",
		307: "REREG_MOBILE_MISMATCH",
		308: "REREG_PAYLOAD_MISMATCH",
		309: "REREG_INTERNAL_ERROR_AT_VENDOR",
		310: "REREG_INVALID_CRED_BLOCK",
		312: "REREG_KYC_NON_COMPLIANT",
		313: "REREG_CARD_NOT_LINKED_TO_OLD_NUMBER",
		314: "REREG_LOST_CARD",
		315: "REREG_NO_CARD_RECORD",
		316: "REREG_ACCOUNT_INACTIVE",
		317: "REREG_DEVICE_TEMPORARILY_DEACTIVATED",
		401: "DEVICEACTIVATION_PENDING_AT_VENDOR",
		402: "DEVICEACTIVATION_FAILED",
		501: "FIUPDATE_DEVICE_PENDING",
		502: "FIUPDATE_USER_PENDING",
		601: "ATM_PIN_REQUIRED",
		701: "USER_CONFIRMATION_REQUIRED",
	}
	DiagnosisCode_value = map[string]int32{
		"UNSPECIFIED":                           0,
		"UNKNOWN":                               1,
		"OK":                                    2,
		"LIVENESS_IN_PROGRESS":                  101,
		"LIVENESS_MAX_RETRIES":                  102,
		"LIVENESS_STUCK_IN_MANUAL_REVIEW":       103,
		"LIVENESS_FACEMATCH_MANUALLY_FAILED":    104,
		"EKYC_IN_PROGRESS":                      105,
		"EKYC_FAILED_OTHER_REASONS":             106,
		"EKYC_AADHAAR_MOBILE_VALIDATION_FAILED": 107,
		"INITREREG_SMS_VALIDATION_ERROR":        203,
		"INITREREG_BAD_REQUEST":                 204,
		"REREG_PENDING_AT_VENDOR":               301,
		"REREG_ATM_PIN_INCORRECT":               302,
		"REREG_ATM_PIN_MAX_RETRIES":             303,
		"REREG_CARD_TXN_REQUIRED":               304,
		"REREG_CARD_INACTIVE":                   305,
		"REREG_NO_SMS_RECEIVED":                 306,
		"REREG_MOBILE_MISMATCH":                 307,
		"REREG_PAYLOAD_MISMATCH":                308,
		"REREG_INTERNAL_ERROR_AT_VENDOR":        309,
		"REREG_INVALID_CRED_BLOCK":              310,
		"REREG_KYC_NON_COMPLIANT":               312,
		"REREG_CARD_NOT_LINKED_TO_OLD_NUMBER":   313,
		"REREG_LOST_CARD":                       314,
		"REREG_NO_CARD_RECORD":                  315,
		"REREG_ACCOUNT_INACTIVE":                316,
		"REREG_DEVICE_TEMPORARILY_DEACTIVATED":  317,
		"DEVICEACTIVATION_PENDING_AT_VENDOR":    401,
		"DEVICEACTIVATION_FAILED":               402,
		"FIUPDATE_DEVICE_PENDING":               501,
		"FIUPDATE_USER_PENDING":                 502,
		"ATM_PIN_REQUIRED":                      601,
		"USER_CONFIRMATION_REQUIRED":            701,
	}
)

func (x DiagnosisCode) Enum() *DiagnosisCode {
	p := new(DiagnosisCode)
	*p = x
	return p
}

func (x DiagnosisCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DiagnosisCode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_troubleshooting_proto_enumTypes[0].Descriptor()
}

func (DiagnosisCode) Type() protoreflect.EnumType {
	return &file_api_auth_internal_troubleshooting_proto_enumTypes[0]
}

func (x DiagnosisCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DiagnosisCode.Descriptor instead.
func (DiagnosisCode) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_troubleshooting_proto_rawDescGZIP(), []int{0}
}

type StageInvestigatorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AfuRecord *AuthFactorUpdate `protobuf:"bytes,1,opt,name=afu_record,json=afuRecord,proto3" json:"afu_record,omitempty"`
	Stage     AFUStage          `protobuf:"varint,2,opt,name=stage,proto3,enum=auth.afu.AFUStage" json:"stage,omitempty"`
}

func (x *StageInvestigatorRequest) Reset() {
	*x = StageInvestigatorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_troubleshooting_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageInvestigatorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageInvestigatorRequest) ProtoMessage() {}

func (x *StageInvestigatorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_troubleshooting_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageInvestigatorRequest.ProtoReflect.Descriptor instead.
func (*StageInvestigatorRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_troubleshooting_proto_rawDescGZIP(), []int{0}
}

func (x *StageInvestigatorRequest) GetAfuRecord() *AuthFactorUpdate {
	if x != nil {
		return x.AfuRecord
	}
	return nil
}

func (x *StageInvestigatorRequest) GetStage() AFUStage {
	if x != nil {
		return x.Stage
	}
	return AFUStage_STAGE_UNSPECIFIED
}

type StageInvestigatorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Stage           AFUStage      `protobuf:"varint,1,opt,name=stage,proto3,enum=auth.afu.AFUStage" json:"stage,omitempty"`
	StageStatus     StageStatus   `protobuf:"varint,2,opt,name=stage_status,json=stageStatus,proto3,enum=auth.afu.StageStatus" json:"stage_status,omitempty"`
	DiagnosisCode   DiagnosisCode `protobuf:"varint,6,opt,name=diagnosis_code,json=diagnosisCode,proto3,enum=auth.afu.DiagnosisCode" json:"diagnosis_code,omitempty"`
	DiagnosisReport string        `protobuf:"bytes,7,opt,name=diagnosis_report,json=diagnosisReport,proto3" json:"diagnosis_report,omitempty"`
}

func (x *StageInvestigatorResponse) Reset() {
	*x = StageInvestigatorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_troubleshooting_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageInvestigatorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageInvestigatorResponse) ProtoMessage() {}

func (x *StageInvestigatorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_troubleshooting_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageInvestigatorResponse.ProtoReflect.Descriptor instead.
func (*StageInvestigatorResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_troubleshooting_proto_rawDescGZIP(), []int{1}
}

func (x *StageInvestigatorResponse) GetStage() AFUStage {
	if x != nil {
		return x.Stage
	}
	return AFUStage_STAGE_UNSPECIFIED
}

func (x *StageInvestigatorResponse) GetStageStatus() StageStatus {
	if x != nil {
		return x.StageStatus
	}
	return StageStatus_STAGE_STATUS_UNSPECIFIED
}

func (x *StageInvestigatorResponse) GetDiagnosisCode() DiagnosisCode {
	if x != nil {
		return x.DiagnosisCode
	}
	return DiagnosisCode_UNSPECIFIED
}

func (x *StageInvestigatorResponse) GetDiagnosisReport() string {
	if x != nil {
		return x.DiagnosisReport
	}
	return ""
}

type RemedyGeneratorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Stage           AFUStage      `protobuf:"varint,1,opt,name=stage,proto3,enum=auth.afu.AFUStage" json:"stage,omitempty"`
	StageStatus     StageStatus   `protobuf:"varint,2,opt,name=stage_status,json=stageStatus,proto3,enum=auth.afu.StageStatus" json:"stage_status,omitempty"`
	DiagnosisCode   DiagnosisCode `protobuf:"varint,3,opt,name=diagnosis_code,json=diagnosisCode,proto3,enum=auth.afu.DiagnosisCode" json:"diagnosis_code,omitempty"`
	DiagnosisReport string        `protobuf:"bytes,4,opt,name=diagnosis_report,json=diagnosisReport,proto3" json:"diagnosis_report,omitempty"`
}

func (x *RemedyGeneratorRequest) Reset() {
	*x = RemedyGeneratorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_troubleshooting_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemedyGeneratorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemedyGeneratorRequest) ProtoMessage() {}

func (x *RemedyGeneratorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_troubleshooting_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemedyGeneratorRequest.ProtoReflect.Descriptor instead.
func (*RemedyGeneratorRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_troubleshooting_proto_rawDescGZIP(), []int{2}
}

func (x *RemedyGeneratorRequest) GetStage() AFUStage {
	if x != nil {
		return x.Stage
	}
	return AFUStage_STAGE_UNSPECIFIED
}

func (x *RemedyGeneratorRequest) GetStageStatus() StageStatus {
	if x != nil {
		return x.StageStatus
	}
	return StageStatus_STAGE_STATUS_UNSPECIFIED
}

func (x *RemedyGeneratorRequest) GetDiagnosisCode() DiagnosisCode {
	if x != nil {
		return x.DiagnosisCode
	}
	return DiagnosisCode_UNSPECIFIED
}

func (x *RemedyGeneratorRequest) GetDiagnosisReport() string {
	if x != nil {
		return x.DiagnosisReport
	}
	return ""
}

type RemedyGeneratorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Remedy *Remedy `protobuf:"bytes,1,opt,name=remedy,proto3" json:"remedy,omitempty"`
}

func (x *RemedyGeneratorResponse) Reset() {
	*x = RemedyGeneratorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_troubleshooting_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemedyGeneratorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemedyGeneratorResponse) ProtoMessage() {}

func (x *RemedyGeneratorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_troubleshooting_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemedyGeneratorResponse.ProtoReflect.Descriptor instead.
func (*RemedyGeneratorResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_troubleshooting_proto_rawDescGZIP(), []int{3}
}

func (x *RemedyGeneratorResponse) GetRemedy() *Remedy {
	if x != nil {
		return x.Remedy
	}
	return nil
}

type Remedy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Advice for the CX agent for a user reaching out.
	Advice string `protobuf:"bytes,1,opt,name=advice,proto3" json:"advice,omitempty"`
	// Troubleshooting advice for employees and internal folks stuck on non-prod env.
	NonProdAdvice string `protobuf:"bytes,2,opt,name=non_prod_advice,json=nonProdAdvice,proto3" json:"non_prod_advice,omitempty"`
}

func (x *Remedy) Reset() {
	*x = Remedy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_troubleshooting_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Remedy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Remedy) ProtoMessage() {}

func (x *Remedy) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_troubleshooting_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Remedy.ProtoReflect.Descriptor instead.
func (*Remedy) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_troubleshooting_proto_rawDescGZIP(), []int{4}
}

func (x *Remedy) GetAdvice() string {
	if x != nil {
		return x.Advice
	}
	return ""
}

func (x *Remedy) GetNonProdAdvice() string {
	if x != nil {
		return x.NonProdAdvice
	}
	return ""
}

type AfuTroubleshootingDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Stage           AFUStage      `protobuf:"varint,1,opt,name=stage,proto3,enum=auth.afu.AFUStage" json:"stage,omitempty"`
	StageStatus     StageStatus   `protobuf:"varint,2,opt,name=stage_status,json=stageStatus,proto3,enum=auth.afu.StageStatus" json:"stage_status,omitempty"`
	DiagnosisCode   DiagnosisCode `protobuf:"varint,3,opt,name=diagnosis_code,json=diagnosisCode,proto3,enum=auth.afu.DiagnosisCode" json:"diagnosis_code,omitempty"`
	DiagnosisReport string        `protobuf:"bytes,4,opt,name=diagnosis_report,json=diagnosisReport,proto3" json:"diagnosis_report,omitempty"`
	Remedy          *Remedy       `protobuf:"bytes,5,opt,name=remedy,proto3" json:"remedy,omitempty"`
	CardForm        card.CardForm `protobuf:"varint,6,opt,name=card_form,json=cardForm,proto3,enum=card.CardForm" json:"card_form,omitempty"`
}

func (x *AfuTroubleshootingDetails) Reset() {
	*x = AfuTroubleshootingDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_troubleshooting_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AfuTroubleshootingDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AfuTroubleshootingDetails) ProtoMessage() {}

func (x *AfuTroubleshootingDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_troubleshooting_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AfuTroubleshootingDetails.ProtoReflect.Descriptor instead.
func (*AfuTroubleshootingDetails) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_troubleshooting_proto_rawDescGZIP(), []int{5}
}

func (x *AfuTroubleshootingDetails) GetStage() AFUStage {
	if x != nil {
		return x.Stage
	}
	return AFUStage_STAGE_UNSPECIFIED
}

func (x *AfuTroubleshootingDetails) GetStageStatus() StageStatus {
	if x != nil {
		return x.StageStatus
	}
	return StageStatus_STAGE_STATUS_UNSPECIFIED
}

func (x *AfuTroubleshootingDetails) GetDiagnosisCode() DiagnosisCode {
	if x != nil {
		return x.DiagnosisCode
	}
	return DiagnosisCode_UNSPECIFIED
}

func (x *AfuTroubleshootingDetails) GetDiagnosisReport() string {
	if x != nil {
		return x.DiagnosisReport
	}
	return ""
}

func (x *AfuTroubleshootingDetails) GetRemedy() *Remedy {
	if x != nil {
		return x.Remedy
	}
	return nil
}

func (x *AfuTroubleshootingDetails) GetCardForm() card.CardForm {
	if x != nil {
		return x.CardForm
	}
	return card.CardForm(0)
}

var File_api_auth_internal_troubleshooting_proto protoreflect.FileDescriptor

var file_api_auth_internal_troubleshooting_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x72, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x73, 0x68, 0x6f, 0x6f, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x61, 0x66, 0x75, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x13, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7f, 0x0a, 0x18, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x69, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x61, 0x66, 0x75, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e,
	0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x09, 0x61, 0x66, 0x75, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x28, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x41, 0x46, 0x55, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x22, 0xea, 0x01, 0x0a, 0x19, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x69, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x41, 0x46,
	0x55, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x38, 0x0a,
	0x0c, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0e, 0x64, 0x69, 0x61, 0x67, 0x6e,
	0x6f, 0x73, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x44, 0x69, 0x61, 0x67, 0x6e,
	0x6f, 0x73, 0x69, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0d, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f,
	0x73, 0x69, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x69, 0x61, 0x67, 0x6e,
	0x6f, 0x73, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x22, 0xe7, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x6d, 0x65, 0x64, 0x79, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x41, 0x46, 0x55, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x38, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x3e, 0x0a, 0x0e, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x61, 0x66, 0x75, 0x2e, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x0d, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x5f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x43, 0x0a, 0x17,
	0x52, 0x65, 0x6d, 0x65, 0x64, 0x79, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x65, 0x64,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61,
	0x66, 0x75, 0x2e, 0x52, 0x65, 0x6d, 0x65, 0x64, 0x79, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x65, 0x64,
	0x79, 0x22, 0x48, 0x0a, 0x06, 0x52, 0x65, 0x6d, 0x65, 0x64, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x64, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x64, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x5f,
	0x61, 0x64, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x6f,
	0x6e, 0x50, 0x72, 0x6f, 0x64, 0x41, 0x64, 0x76, 0x69, 0x63, 0x65, 0x22, 0xc1, 0x02, 0x0a, 0x19,
	0x41, 0x66, 0x75, 0x54, 0x72, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x69,
	0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x61, 0x66, 0x75, 0x2e, 0x41, 0x46, 0x55, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x12, 0x38, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x61, 0x66, 0x75, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0b, 0x73, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a,
	0x0e, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75,
	0x2e, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0d,
	0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x29, 0x0a,
	0x10, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73,
	0x69, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x28, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x65,
	0x64, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x61, 0x66, 0x75, 0x2e, 0x52, 0x65, 0x6d, 0x65, 0x64, 0x79, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x65,
	0x64, 0x79, 0x12, 0x2b, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72,
	0x64, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x2a,
	0xfe, 0x07, 0x0a, 0x0d, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x01, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x49, 0x56, 0x45, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10,
	0x65, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4d, 0x41,
	0x58, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x10, 0x66, 0x12, 0x23, 0x0a, 0x1f, 0x4c,
	0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x54, 0x55, 0x43, 0x4b, 0x5f, 0x49, 0x4e,
	0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x67,
	0x12, 0x26, 0x0a, 0x22, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x41, 0x43,
	0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x4c, 0x59, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x68, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x4b, 0x59, 0x43,
	0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x69, 0x12, 0x1d,
	0x0a, 0x19, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x4f, 0x54,
	0x48, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x53, 0x10, 0x6a, 0x12, 0x29, 0x0a,
	0x25, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x4d, 0x4f,
	0x42, 0x49, 0x4c, 0x45, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x6b, 0x12, 0x23, 0x0a, 0x1e, 0x49, 0x4e, 0x49, 0x54,
	0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x53, 0x4d, 0x53, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xcb, 0x01, 0x12, 0x1a, 0x0a,
	0x15, 0x49, 0x4e, 0x49, 0x54, 0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x42, 0x41, 0x44, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0xcc, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x52, 0x45, 0x52,
	0x45, 0x47, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45,
	0x4e, 0x44, 0x4f, 0x52, 0x10, 0xad, 0x02, 0x12, 0x1c, 0x0a, 0x17, 0x52, 0x45, 0x52, 0x45, 0x47,
	0x5f, 0x41, 0x54, 0x4d, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45,
	0x43, 0x54, 0x10, 0xae, 0x02, 0x12, 0x1e, 0x0a, 0x19, 0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x41,
	0x54, 0x4d, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49,
	0x45, 0x53, 0x10, 0xaf, 0x02, 0x12, 0x1c, 0x0a, 0x17, 0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44,
	0x10, 0xb0, 0x02, 0x12, 0x18, 0x0a, 0x13, 0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0xb1, 0x02, 0x12, 0x1a, 0x0a,
	0x15, 0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x4e, 0x4f, 0x5f, 0x53, 0x4d, 0x53, 0x5f, 0x52, 0x45,
	0x43, 0x45, 0x49, 0x56, 0x45, 0x44, 0x10, 0xb2, 0x02, 0x12, 0x1a, 0x0a, 0x15, 0x52, 0x45, 0x52,
	0x45, 0x47, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x10, 0xb3, 0x02, 0x12, 0x1b, 0x0a, 0x16, 0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x50,
	0x41, 0x59, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10,
	0xb4, 0x02, 0x12, 0x23, 0x0a, 0x1e, 0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45,
	0x4e, 0x44, 0x4f, 0x52, 0x10, 0xb5, 0x02, 0x12, 0x1d, 0x0a, 0x18, 0x52, 0x45, 0x52, 0x45, 0x47,
	0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x5f, 0x42, 0x4c,
	0x4f, 0x43, 0x4b, 0x10, 0xb6, 0x02, 0x12, 0x1c, 0x0a, 0x17, 0x52, 0x45, 0x52, 0x45, 0x47, 0x5f,
	0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x49, 0x41, 0x4e,
	0x54, 0x10, 0xb8, 0x02, 0x12, 0x28, 0x0a, 0x23, 0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x5f, 0x54, 0x4f,
	0x5f, 0x4f, 0x4c, 0x44, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0xb9, 0x02, 0x12, 0x14,
	0x0a, 0x0f, 0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x4c, 0x4f, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x10, 0xba, 0x02, 0x12, 0x19, 0x0a, 0x14, 0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x4e, 0x4f,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x10, 0xbb, 0x02, 0x12,
	0x1b, 0x0a, 0x16, 0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0xbc, 0x02, 0x12, 0x29, 0x0a, 0x24,
	0x52, 0x45, 0x52, 0x45, 0x47, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x45, 0x4d,
	0x50, 0x4f, 0x52, 0x41, 0x52, 0x49, 0x4c, 0x59, 0x5f, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56,
	0x41, 0x54, 0x45, 0x44, 0x10, 0xbd, 0x02, 0x12, 0x27, 0x0a, 0x22, 0x44, 0x45, 0x56, 0x49, 0x43,
	0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x91, 0x03,
	0x12, 0x1c, 0x0a, 0x17, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x92, 0x03, 0x12, 0x1c,
	0x0a, 0x17, 0x46, 0x49, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0xf5, 0x03, 0x12, 0x1a, 0x0a, 0x15,
	0x46, 0x49, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x50, 0x45,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0xf6, 0x03, 0x12, 0x15, 0x0a, 0x10, 0x41, 0x54, 0x4d, 0x5f,
	0x50, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0xd9, 0x04, 0x12,
	0x1f, 0x0a, 0x1a, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0xbd, 0x05,
	0x42, 0x4a, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x5a, 0x23, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x61, 0x66, 0x75, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_internal_troubleshooting_proto_rawDescOnce sync.Once
	file_api_auth_internal_troubleshooting_proto_rawDescData = file_api_auth_internal_troubleshooting_proto_rawDesc
)

func file_api_auth_internal_troubleshooting_proto_rawDescGZIP() []byte {
	file_api_auth_internal_troubleshooting_proto_rawDescOnce.Do(func() {
		file_api_auth_internal_troubleshooting_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_internal_troubleshooting_proto_rawDescData)
	})
	return file_api_auth_internal_troubleshooting_proto_rawDescData
}

var file_api_auth_internal_troubleshooting_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_internal_troubleshooting_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_auth_internal_troubleshooting_proto_goTypes = []interface{}{
	(DiagnosisCode)(0),                // 0: auth.afu.DiagnosisCode
	(*StageInvestigatorRequest)(nil),  // 1: auth.afu.StageInvestigatorRequest
	(*StageInvestigatorResponse)(nil), // 2: auth.afu.StageInvestigatorResponse
	(*RemedyGeneratorRequest)(nil),    // 3: auth.afu.RemedyGeneratorRequest
	(*RemedyGeneratorResponse)(nil),   // 4: auth.afu.RemedyGeneratorResponse
	(*Remedy)(nil),                    // 5: auth.afu.Remedy
	(*AfuTroubleshootingDetails)(nil), // 6: auth.afu.AfuTroubleshootingDetails
	(*AuthFactorUpdate)(nil),          // 7: auth.afu.AuthFactorUpdate
	(AFUStage)(0),                     // 8: auth.afu.AFUStage
	(StageStatus)(0),                  // 9: auth.afu.StageStatus
	(card.CardForm)(0),                // 10: card.CardForm
}
var file_api_auth_internal_troubleshooting_proto_depIdxs = []int32{
	7,  // 0: auth.afu.StageInvestigatorRequest.afu_record:type_name -> auth.afu.AuthFactorUpdate
	8,  // 1: auth.afu.StageInvestigatorRequest.stage:type_name -> auth.afu.AFUStage
	8,  // 2: auth.afu.StageInvestigatorResponse.stage:type_name -> auth.afu.AFUStage
	9,  // 3: auth.afu.StageInvestigatorResponse.stage_status:type_name -> auth.afu.StageStatus
	0,  // 4: auth.afu.StageInvestigatorResponse.diagnosis_code:type_name -> auth.afu.DiagnosisCode
	8,  // 5: auth.afu.RemedyGeneratorRequest.stage:type_name -> auth.afu.AFUStage
	9,  // 6: auth.afu.RemedyGeneratorRequest.stage_status:type_name -> auth.afu.StageStatus
	0,  // 7: auth.afu.RemedyGeneratorRequest.diagnosis_code:type_name -> auth.afu.DiagnosisCode
	5,  // 8: auth.afu.RemedyGeneratorResponse.remedy:type_name -> auth.afu.Remedy
	8,  // 9: auth.afu.AfuTroubleshootingDetails.stage:type_name -> auth.afu.AFUStage
	9,  // 10: auth.afu.AfuTroubleshootingDetails.stage_status:type_name -> auth.afu.StageStatus
	0,  // 11: auth.afu.AfuTroubleshootingDetails.diagnosis_code:type_name -> auth.afu.DiagnosisCode
	5,  // 12: auth.afu.AfuTroubleshootingDetails.remedy:type_name -> auth.afu.Remedy
	10, // 13: auth.afu.AfuTroubleshootingDetails.card_form:type_name -> card.CardForm
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_api_auth_internal_troubleshooting_proto_init() }
func file_api_auth_internal_troubleshooting_proto_init() {
	if File_api_auth_internal_troubleshooting_proto != nil {
		return
	}
	file_api_auth_internal_auth_factor_update_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_auth_internal_troubleshooting_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageInvestigatorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_troubleshooting_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageInvestigatorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_troubleshooting_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemedyGeneratorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_troubleshooting_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemedyGeneratorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_troubleshooting_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Remedy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_troubleshooting_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AfuTroubleshootingDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_internal_troubleshooting_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_internal_troubleshooting_proto_goTypes,
		DependencyIndexes: file_api_auth_internal_troubleshooting_proto_depIdxs,
		EnumInfos:         file_api_auth_internal_troubleshooting_proto_enumTypes,
		MessageInfos:      file_api_auth_internal_troubleshooting_proto_msgTypes,
	}.Build()
	File_api_auth_internal_troubleshooting_proto = out.File
	file_api_auth_internal_troubleshooting_proto_rawDesc = nil
	file_api_auth_internal_troubleshooting_proto_goTypes = nil
	file_api_auth_internal_troubleshooting_proto_depIdxs = nil
}
