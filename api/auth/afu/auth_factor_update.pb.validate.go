// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/internal/auth_factor_update.proto

package afu

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	card "github.com/epifi/gamma/api/card"

	common "github.com/epifi/be-common/api/typesv2/common"

	frontend "github.com/epifi/gamma/api/frontend"

	product "github.com/epifi/gamma/api/product"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = card.CardForm(0)

	_ = common.BooleanEnum(0)

	_ = frontend.Vendor(0)

	_ = product.ProductType(0)
)

// Validate checks the field values on AuthFactorUpdate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AuthFactorUpdate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthFactorUpdate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthFactorUpdateMultiError, or nil if none found.
func (m *AuthFactorUpdate) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthFactorUpdate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Id

	// no validation rules for OverallStatus

	if all {
		switch v := interface{}(m.GetContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthFactorUpdateValidationError{
					field:  "Context",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthFactorUpdateValidationError{
					field:  "Context",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthFactorUpdateValidationError{
				field:  "Context",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthFactorUpdateValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthFactorUpdateValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthFactorUpdateValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthFactorUpdateValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthFactorUpdateValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthFactorUpdateValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthFactorUpdateValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthFactorUpdateValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthFactorUpdateValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthFactorUpdateValidationError{
					field:  "VendorContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthFactorUpdateValidationError{
					field:  "VendorContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthFactorUpdateValidationError{
				field:  "VendorContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	if all {
		switch v := interface{}(m.GetAttemptsHistory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthFactorUpdateValidationError{
					field:  "AttemptsHistory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthFactorUpdateValidationError{
					field:  "AttemptsHistory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAttemptsHistory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthFactorUpdateValidationError{
				field:  "AttemptsHistory",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthFactorUpdateMultiError(errors)
	}

	return nil
}

// AuthFactorUpdateMultiError is an error wrapping multiple validation errors
// returned by AuthFactorUpdate.ValidateAll() if the designated constraints
// aren't met.
type AuthFactorUpdateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthFactorUpdateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthFactorUpdateMultiError) AllErrors() []error { return m }

// AuthFactorUpdateValidationError is the validation error returned by
// AuthFactorUpdate.Validate if the designated constraints aren't met.
type AuthFactorUpdateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthFactorUpdateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthFactorUpdateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthFactorUpdateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthFactorUpdateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthFactorUpdateValidationError) ErrorName() string { return "AuthFactorUpdateValidationError" }

// Error satisfies the builtin error interface
func (e AuthFactorUpdateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthFactorUpdate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthFactorUpdateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthFactorUpdateValidationError{}

// Validate checks the field values on AttemptsHistory with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AttemptsHistory) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttemptsHistory with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AttemptsHistoryMultiError, or nil if none found.
func (m *AttemptsHistory) ValidateAll() error {
	return m.validate(true)
}

func (m *AttemptsHistory) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetReRegAttempts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AttemptsHistoryValidationError{
						field:  fmt.Sprintf("ReRegAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AttemptsHistoryValidationError{
						field:  fmt.Sprintf("ReRegAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AttemptsHistoryValidationError{
					field:  fmt.Sprintf("ReRegAttempts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AttemptsHistoryMultiError(errors)
	}

	return nil
}

// AttemptsHistoryMultiError is an error wrapping multiple validation errors
// returned by AttemptsHistory.ValidateAll() if the designated constraints
// aren't met.
type AttemptsHistoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttemptsHistoryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttemptsHistoryMultiError) AllErrors() []error { return m }

// AttemptsHistoryValidationError is the validation error returned by
// AttemptsHistory.Validate if the designated constraints aren't met.
type AttemptsHistoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttemptsHistoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttemptsHistoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttemptsHistoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttemptsHistoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttemptsHistoryValidationError) ErrorName() string { return "AttemptsHistoryValidationError" }

// Error satisfies the builtin error interface
func (e AttemptsHistoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttemptsHistory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttemptsHistoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttemptsHistoryValidationError{}

// Validate checks the field values on ReRegAttempt with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReRegAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReRegAttempt with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReRegAttemptMultiError, or
// nil if none found.
func (m *ReRegAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *ReRegAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for FailureReason

	if all {
		switch v := interface{}(m.GetFailedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReRegAttemptValidationError{
					field:  "FailedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReRegAttemptValidationError{
					field:  "FailedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFailedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReRegAttemptValidationError{
				field:  "FailedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReRegAttemptMultiError(errors)
	}

	return nil
}

// ReRegAttemptMultiError is an error wrapping multiple validation errors
// returned by ReRegAttempt.ValidateAll() if the designated constraints aren't met.
type ReRegAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReRegAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReRegAttemptMultiError) AllErrors() []error { return m }

// ReRegAttemptValidationError is the validation error returned by
// ReRegAttempt.Validate if the designated constraints aren't met.
type ReRegAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReRegAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReRegAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReRegAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReRegAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReRegAttemptValidationError) ErrorName() string { return "ReRegAttemptValidationError" }

// Error satisfies the builtin error interface
func (e ReRegAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReRegAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReRegAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReRegAttemptValidationError{}

// Validate checks the field values on Context with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Context) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Context with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ContextMultiError, or nil if none found.
func (m *Context) ValidateAll() error {
	return m.validate(true)
}

func (m *Context) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNewValues()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContextValidationError{
					field:  "NewValues",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContextValidationError{
					field:  "NewValues",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNewValues()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContextValidationError{
				field:  "NewValues",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EncryptedPayload

	for idx, item := range m.GetCredentialStatuses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ContextValidationError{
						field:  fmt.Sprintf("CredentialStatuses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ContextValidationError{
						field:  fmt.Sprintf("CredentialStatuses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ContextValidationError{
					field:  fmt.Sprintf("CredentialStatuses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UserConfirmationStatus

	for idx, item := range m.GetVendorRequestStatuses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ContextValidationError{
						field:  fmt.Sprintf("VendorRequestStatuses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ContextValidationError{
						field:  fmt.Sprintf("VendorRequestStatuses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ContextValidationError{
					field:  fmt.Sprintf("VendorRequestStatuses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for EpifiEmailPhoneNumUpdate

	// no validation rules for EpifiDeviceUpdate

	// no validation rules for VendorCardId

	if all {
		switch v := interface{}(m.GetCurrentValues()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContextValidationError{
					field:  "CurrentValues",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContextValidationError{
					field:  "CurrentValues",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentValues()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContextValidationError{
				field:  "CurrentValues",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CredBlock

	if all {
		switch v := interface{}(m.GetNewDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContextValidationError{
					field:  "NewDevice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContextValidationError{
					field:  "NewDevice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNewDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContextValidationError{
				field:  "NewDevice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeregisterCurrentDevice

	// no validation rules for AfuCompletionEventPublished

	// no validation rules for CardState

	if all {
		switch v := interface{}(m.GetActorAuthState()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContextValidationError{
					field:  "ActorAuthState",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContextValidationError{
					field:  "ActorAuthState",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActorAuthState()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContextValidationError{
				field:  "ActorAuthState",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsLivenessManualReview

	// no validation rules for CardForm

	// no validation rules for AuthFactorsRiskAssessment

	// no validation rules for SimId

	if all {
		switch v := interface{}(m.GetEkycInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContextValidationError{
					field:  "EkycInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContextValidationError{
					field:  "EkycInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEkycInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContextValidationError{
				field:  "EkycInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsAtmPinRequired

	if len(errors) > 0 {
		return ContextMultiError(errors)
	}

	return nil
}

// ContextMultiError is an error wrapping multiple validation errors returned
// by Context.ValidateAll() if the designated constraints aren't met.
type ContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContextMultiError) AllErrors() []error { return m }

// ContextValidationError is the validation error returned by Context.Validate
// if the designated constraints aren't met.
type ContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContextValidationError) ErrorName() string { return "ContextValidationError" }

// Error satisfies the builtin error interface
func (e ContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContextValidationError{}

// Validate checks the field values on ActorAuthState with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ActorAuthState) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActorAuthState with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ActorAuthStateMultiError,
// or nil if none found.
func (m *ActorAuthState) ValidateAll() error {
	return m.validate(true)
}

func (m *ActorAuthState) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorAuthStatus

	{
		sorted_keys := make([]uint32, len(m.GetLevelStates()))
		i := 0
		for key := range m.GetLevelStates() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetLevelStates()[key]
			_ = val

			// no validation rules for LevelStates[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ActorAuthStateValidationError{
							field:  fmt.Sprintf("LevelStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ActorAuthStateValidationError{
							field:  fmt.Sprintf("LevelStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ActorAuthStateValidationError{
						field:  fmt.Sprintf("LevelStates[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return ActorAuthStateMultiError(errors)
	}

	return nil
}

// ActorAuthStateMultiError is an error wrapping multiple validation errors
// returned by ActorAuthState.ValidateAll() if the designated constraints
// aren't met.
type ActorAuthStateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActorAuthStateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActorAuthStateMultiError) AllErrors() []error { return m }

// ActorAuthStateValidationError is the validation error returned by
// ActorAuthState.Validate if the designated constraints aren't met.
type ActorAuthStateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActorAuthStateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActorAuthStateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActorAuthStateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActorAuthStateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActorAuthStateValidationError) ErrorName() string { return "ActorAuthStateValidationError" }

// Error satisfies the builtin error interface
func (e ActorAuthStateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActorAuthState.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActorAuthStateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActorAuthStateValidationError{}

// Validate checks the field values on LevelState with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LevelState) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LevelState with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LevelStateMultiError, or
// nil if none found.
func (m *LevelState) ValidateAll() error {
	return m.validate(true)
}

func (m *LevelState) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LevelStatus

	if len(errors) > 0 {
		return LevelStateMultiError(errors)
	}

	return nil
}

// LevelStateMultiError is an error wrapping multiple validation errors
// returned by LevelState.ValidateAll() if the designated constraints aren't met.
type LevelStateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LevelStateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LevelStateMultiError) AllErrors() []error { return m }

// LevelStateValidationError is the validation error returned by
// LevelState.Validate if the designated constraints aren't met.
type LevelStateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LevelStateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LevelStateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LevelStateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LevelStateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LevelStateValidationError) ErrorName() string { return "LevelStateValidationError" }

// Error satisfies the builtin error interface
func (e LevelStateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLevelState.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LevelStateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LevelStateValidationError{}

// Validate checks the field values on AuthFactorValues with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AuthFactorValues) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthFactorValues with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthFactorValuesMultiError, or nil if none found.
func (m *AuthFactorValues) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthFactorValues) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthFactorValuesValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthFactorValuesValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthFactorValuesValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for DeviceToken

	// no validation rules for DeviceId

	if len(errors) > 0 {
		return AuthFactorValuesMultiError(errors)
	}

	return nil
}

// AuthFactorValuesMultiError is an error wrapping multiple validation errors
// returned by AuthFactorValues.ValidateAll() if the designated constraints
// aren't met.
type AuthFactorValuesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthFactorValuesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthFactorValuesMultiError) AllErrors() []error { return m }

// AuthFactorValuesValidationError is the validation error returned by
// AuthFactorValues.Validate if the designated constraints aren't met.
type AuthFactorValuesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthFactorValuesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthFactorValuesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthFactorValuesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthFactorValuesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthFactorValuesValidationError) ErrorName() string { return "AuthFactorValuesValidationError" }

// Error satisfies the builtin error interface
func (e AuthFactorValuesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthFactorValues.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthFactorValuesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthFactorValuesValidationError{}

// Validate checks the field values on CredentialStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CredentialStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CredentialStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CredentialStatusMultiError, or nil if none found.
func (m *CredentialStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *CredentialStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Credential

	// no validation rules for Status

	// no validation rules for Level

	if all {
		switch v := interface{}(m.GetLastUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CredentialStatusValidationError{
					field:  "LastUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CredentialStatusValidationError{
					field:  "LastUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CredentialStatusValidationError{
				field:  "LastUpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStartedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CredentialStatusValidationError{
					field:  "StartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CredentialStatusValidationError{
					field:  "StartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CredentialStatusValidationError{
				field:  "StartedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CredentialStatusMultiError(errors)
	}

	return nil
}

// CredentialStatusMultiError is an error wrapping multiple validation errors
// returned by CredentialStatus.ValidateAll() if the designated constraints
// aren't met.
type CredentialStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CredentialStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CredentialStatusMultiError) AllErrors() []error { return m }

// CredentialStatusValidationError is the validation error returned by
// CredentialStatus.Validate if the designated constraints aren't met.
type CredentialStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CredentialStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CredentialStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CredentialStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CredentialStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CredentialStatusValidationError) ErrorName() string { return "CredentialStatusValidationError" }

// Error satisfies the builtin error interface
func (e CredentialStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCredentialStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CredentialStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CredentialStatusValidationError{}

// Validate checks the field values on VendorRequestStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VendorRequestStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VendorRequestStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VendorRequestStatusMultiError, or nil if none found.
func (m *VendorRequestStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *VendorRequestStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vendor

	// no validation rules for Status

	if len(errors) > 0 {
		return VendorRequestStatusMultiError(errors)
	}

	return nil
}

// VendorRequestStatusMultiError is an error wrapping multiple validation
// errors returned by VendorRequestStatus.ValidateAll() if the designated
// constraints aren't met.
type VendorRequestStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VendorRequestStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VendorRequestStatusMultiError) AllErrors() []error { return m }

// VendorRequestStatusValidationError is the validation error returned by
// VendorRequestStatus.Validate if the designated constraints aren't met.
type VendorRequestStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VendorRequestStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VendorRequestStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VendorRequestStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VendorRequestStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VendorRequestStatusValidationError) ErrorName() string {
	return "VendorRequestStatusValidationError"
}

// Error satisfies the builtin error interface
func (e VendorRequestStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVendorRequestStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VendorRequestStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VendorRequestStatusValidationError{}

// Validate checks the field values on VendorContext with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VendorContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VendorContext with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VendorContextMultiError, or
// nil if none found.
func (m *VendorContext) ValidateAll() error {
	return m.validate(true)
}

func (m *VendorContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for State

	// no validation rules for FailureType

	// no validation rules for InvalidTokenInReactivateAsSuccess

	// no validation rules for ReRegistrationStatus

	if all {
		switch v := interface{}(m.GetReRegisterDeviceVendorInitStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VendorContextValidationError{
					field:  "ReRegisterDeviceVendorInitStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VendorContextValidationError{
					field:  "ReRegisterDeviceVendorInitStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReRegisterDeviceVendorInitStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VendorContextValidationError{
				field:  "ReRegisterDeviceVendorInitStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorRequestStartedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VendorContextValidationError{
					field:  "VendorRequestStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VendorContextValidationError{
					field:  "VendorRequestStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorRequestStartedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VendorContextValidationError{
				field:  "VendorRequestStartedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VendorContextMultiError(errors)
	}

	return nil
}

// VendorContextMultiError is an error wrapping multiple validation errors
// returned by VendorContext.ValidateAll() if the designated constraints
// aren't met.
type VendorContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VendorContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VendorContextMultiError) AllErrors() []error { return m }

// VendorContextValidationError is the validation error returned by
// VendorContext.Validate if the designated constraints aren't met.
type VendorContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VendorContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VendorContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VendorContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VendorContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VendorContextValidationError) ErrorName() string { return "VendorContextValidationError" }

// Error satisfies the builtin error interface
func (e VendorContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVendorContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VendorContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VendorContextValidationError{}

// Validate checks the field values on AFUSummary with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AFUSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AFUSummary with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AFUSummaryMultiError, or
// nil if none found.
func (m *AFUSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *AFUSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AfuId

	// no validation rules for AfuStatus

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AFUSummaryValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AFUSummaryValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AFUSummaryValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AFUSummaryValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AFUSummaryValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AFUSummaryValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AFUSummaryMultiError(errors)
	}

	return nil
}

// AFUSummaryMultiError is an error wrapping multiple validation errors
// returned by AFUSummary.ValidateAll() if the designated constraints aren't met.
type AFUSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AFUSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AFUSummaryMultiError) AllErrors() []error { return m }

// AFUSummaryValidationError is the validation error returned by
// AFUSummary.Validate if the designated constraints aren't met.
type AFUSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AFUSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AFUSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AFUSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AFUSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AFUSummaryValidationError) ErrorName() string { return "AFUSummaryValidationError" }

// Error satisfies the builtin error interface
func (e AFUSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAFUSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AFUSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AFUSummaryValidationError{}

// Validate checks the field values on Context_EkycInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *Context_EkycInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Context_EkycInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Context_EkycInfoMultiError, or nil if none found.
func (m *Context_EkycInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *Context_EkycInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientRequestId

	if len(errors) > 0 {
		return Context_EkycInfoMultiError(errors)
	}

	return nil
}

// Context_EkycInfoMultiError is an error wrapping multiple validation errors
// returned by Context_EkycInfo.ValidateAll() if the designated constraints
// aren't met.
type Context_EkycInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Context_EkycInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Context_EkycInfoMultiError) AllErrors() []error { return m }

// Context_EkycInfoValidationError is the validation error returned by
// Context_EkycInfo.Validate if the designated constraints aren't met.
type Context_EkycInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Context_EkycInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Context_EkycInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Context_EkycInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Context_EkycInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Context_EkycInfoValidationError) ErrorName() string { return "Context_EkycInfoValidationError" }

// Error satisfies the builtin error interface
func (e Context_EkycInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContext_EkycInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Context_EkycInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Context_EkycInfoValidationError{}
