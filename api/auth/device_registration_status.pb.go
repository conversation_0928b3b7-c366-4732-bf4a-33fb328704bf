// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/device_registration_status.proto

package auth

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// DeviceRegistrationStatus represents whether device registration
// step has been completed or not. It represents the scope of power for
// an access token. When access token is first create after verifying
// Phone Number and Email, the token is given partial access.
// Once device registration is completed, the token has full access.
type DeviceRegistrationStatus int32

const (
	DeviceRegistrationStatus_DEVICE_STATUS_UNSPECIFIED DeviceRegistrationStatus = 0
	// UNREGISTERED represents pending device registration
	DeviceRegistrationStatus_UNREGISTERED DeviceRegistrationStatus = 1
	// REGISTERED represents device registration is completed
	DeviceRegistrationStatus_REGISTERED DeviceRegistrationStatus = 2
)

// Enum value maps for DeviceRegistrationStatus.
var (
	DeviceRegistrationStatus_name = map[int32]string{
		0: "DEVICE_STATUS_UNSPECIFIED",
		1: "UNREGISTERED",
		2: "REGISTERED",
	}
	DeviceRegistrationStatus_value = map[string]int32{
		"DEVICE_STATUS_UNSPECIFIED": 0,
		"UNREGISTERED":              1,
		"REGISTERED":                2,
	}
)

func (x DeviceRegistrationStatus) Enum() *DeviceRegistrationStatus {
	p := new(DeviceRegistrationStatus)
	*p = x
	return p
}

func (x DeviceRegistrationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceRegistrationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_device_registration_status_proto_enumTypes[0].Descriptor()
}

func (DeviceRegistrationStatus) Type() protoreflect.EnumType {
	return &file_api_auth_device_registration_status_proto_enumTypes[0]
}

func (x DeviceRegistrationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceRegistrationStatus.Descriptor instead.
func (DeviceRegistrationStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_device_registration_status_proto_rawDescGZIP(), []int{0}
}

type DevRegistrationFlow int32

const (
	DevRegistrationFlow_DEV_REGISTRATION_FLOW_UNSPECIFIED    DevRegistrationFlow = 0
	DevRegistrationFlow_DEV_REGISTRATION_FLOW_ONBOARDING     DevRegistrationFlow = 1
	DevRegistrationFlow_DEV_REGISTRATION_FLOW_REREGISTRATION DevRegistrationFlow = 2
)

// Enum value maps for DevRegistrationFlow.
var (
	DevRegistrationFlow_name = map[int32]string{
		0: "DEV_REGISTRATION_FLOW_UNSPECIFIED",
		1: "DEV_REGISTRATION_FLOW_ONBOARDING",
		2: "DEV_REGISTRATION_FLOW_REREGISTRATION",
	}
	DevRegistrationFlow_value = map[string]int32{
		"DEV_REGISTRATION_FLOW_UNSPECIFIED":    0,
		"DEV_REGISTRATION_FLOW_ONBOARDING":     1,
		"DEV_REGISTRATION_FLOW_REREGISTRATION": 2,
	}
)

func (x DevRegistrationFlow) Enum() *DevRegistrationFlow {
	p := new(DevRegistrationFlow)
	*p = x
	return p
}

func (x DevRegistrationFlow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DevRegistrationFlow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_device_registration_status_proto_enumTypes[1].Descriptor()
}

func (DevRegistrationFlow) Type() protoreflect.EnumType {
	return &file_api_auth_device_registration_status_proto_enumTypes[1]
}

func (x DevRegistrationFlow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DevRegistrationFlow.Descriptor instead.
func (DevRegistrationFlow) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_device_registration_status_proto_rawDescGZIP(), []int{1}
}

type SmsStatus int32

const (
	SmsStatus_SMS_STATUS_UNSPECIFIED SmsStatus = 0
	// state when server sends phone number to client
	SmsStatus_SMS_STATUS_INITIATED SmsStatus = 1
	SmsStatus_SMS_STATUS_SUCCESS   SmsStatus = 2
	SmsStatus_SMS_STATUS_FAILED    SmsStatus = 3
)

// Enum value maps for SmsStatus.
var (
	SmsStatus_name = map[int32]string{
		0: "SMS_STATUS_UNSPECIFIED",
		1: "SMS_STATUS_INITIATED",
		2: "SMS_STATUS_SUCCESS",
		3: "SMS_STATUS_FAILED",
	}
	SmsStatus_value = map[string]int32{
		"SMS_STATUS_UNSPECIFIED": 0,
		"SMS_STATUS_INITIATED":   1,
		"SMS_STATUS_SUCCESS":     2,
		"SMS_STATUS_FAILED":      3,
	}
)

func (x SmsStatus) Enum() *SmsStatus {
	p := new(SmsStatus)
	*p = x
	return p
}

func (x SmsStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SmsStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_device_registration_status_proto_enumTypes[2].Descriptor()
}

func (SmsStatus) Type() protoreflect.EnumType {
	return &file_api_auth_device_registration_status_proto_enumTypes[2]
}

func (x SmsStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SmsStatus.Descriptor instead.
func (SmsStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_device_registration_status_proto_rawDescGZIP(), []int{2}
}

var File_api_auth_device_registration_status_proto protoreflect.FileDescriptor

var file_api_auth_device_registration_status_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x61, 0x75, 0x74,
	0x68, 0x2a, 0x5b, 0x0a, 0x18, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a,
	0x19, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c,
	0x55, 0x4e, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0e,
	0x0a, 0x0a, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x8c,
	0x01, 0x0a, 0x13, 0x44, 0x65, 0x76, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x25, 0x0a, 0x21, 0x44, 0x45, 0x56, 0x5f, 0x52, 0x45,
	0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a,
	0x20, 0x44, 0x45, 0x56, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x44, 0x45, 0x56, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53,
	0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x52, 0x45, 0x52,
	0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x2a, 0x70, 0x0a,
	0x09, 0x53, 0x6d, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x4d,
	0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x4d, 0x53, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01,
	0x12, 0x16, 0x0a, 0x12, 0x53, 0x4d, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x4d, 0x53, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x42,
	0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x75, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_device_registration_status_proto_rawDescOnce sync.Once
	file_api_auth_device_registration_status_proto_rawDescData = file_api_auth_device_registration_status_proto_rawDesc
)

func file_api_auth_device_registration_status_proto_rawDescGZIP() []byte {
	file_api_auth_device_registration_status_proto_rawDescOnce.Do(func() {
		file_api_auth_device_registration_status_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_device_registration_status_proto_rawDescData)
	})
	return file_api_auth_device_registration_status_proto_rawDescData
}

var file_api_auth_device_registration_status_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_auth_device_registration_status_proto_goTypes = []interface{}{
	(DeviceRegistrationStatus)(0), // 0: auth.DeviceRegistrationStatus
	(DevRegistrationFlow)(0),      // 1: auth.DevRegistrationFlow
	(SmsStatus)(0),                // 2: auth.SmsStatus
}
var file_api_auth_device_registration_status_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_auth_device_registration_status_proto_init() }
func file_api_auth_device_registration_status_proto_init() {
	if File_api_auth_device_registration_status_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_device_registration_status_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_device_registration_status_proto_goTypes,
		DependencyIndexes: file_api_auth_device_registration_status_proto_depIdxs,
		EnumInfos:         file_api_auth_device_registration_status_proto_enumTypes,
	}.Build()
	File_api_auth_device_registration_status_proto = out.File
	file_api_auth_device_registration_status_proto_rawDesc = nil
	file_api_auth_device_registration_status_proto_goTypes = nil
	file_api_auth_device_registration_status_proto_depIdxs = nil
}
