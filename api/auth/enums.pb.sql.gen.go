// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/auth/enums.pb.go

package auth

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the TokenDeletionReason in string format in DB
func (p TokenDeletionReason) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing TokenDeletionReason while reading from DB
func (p *TokenDeletionReason) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := TokenDeletionReason_value[val]
	if !ok {
		return fmt.Errorf("unexpected TokenDeletionReason value: %s", val)
	}
	*p = TokenDeletionReason(valInt)
	return nil
}

// Marshaler interface implementation for TokenDeletionReason
func (x TokenDeletionReason) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for TokenDeletionReason
func (x *TokenDeletionReason) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = TokenDeletionReason(TokenDeletionReason_value[val])
	return nil
}
