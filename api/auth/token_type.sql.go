package auth

import (
	"database/sql/driver"
	"fmt"
)

// Helper methods to translate TokenType Enum in Code to String in DB and vice versa

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x TokenType) Value() (driver.Value, error) {
	return x.String(), nil
}

// <PERSON>an implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *TokenType) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := TokenType_value[val]
	*x = TokenType(valInt)
	return nil
}
