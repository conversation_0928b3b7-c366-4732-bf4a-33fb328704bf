// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/internal/device_registration.proto

package auth

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeviceRegisterationUpdateFieldMask int32

const (
	DeviceRegisterationUpdateFieldMask_UNSPECIFIED         DeviceRegisterationUpdateFieldMask = 0
	DeviceRegisterationUpdateFieldMask_REGISTRATION_STATUS DeviceRegisterationUpdateFieldMask = 1
	DeviceRegisterationUpdateFieldMask_DEVICE_TOKEN        DeviceRegisterationUpdateFieldMask = 2
	DeviceRegisterationUpdateFieldMask_DEVICE_REG_METADATA DeviceRegisterationUpdateFieldMask = 3
	DeviceRegisterationUpdateFieldMask_ATTEMPT_ID          DeviceRegisterationUpdateFieldMask = 4
	DeviceRegisterationUpdateFieldMask_VENDOR              DeviceRegisterationUpdateFieldMask = 5
	DeviceRegisterationUpdateFieldMask_VENDOR_DEVICE_ID    DeviceRegisterationUpdateFieldMask = 6
	DeviceRegisterationUpdateFieldMask_SIM_ID              DeviceRegisterationUpdateFieldMask = 7
)

// Enum value maps for DeviceRegisterationUpdateFieldMask.
var (
	DeviceRegisterationUpdateFieldMask_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "REGISTRATION_STATUS",
		2: "DEVICE_TOKEN",
		3: "DEVICE_REG_METADATA",
		4: "ATTEMPT_ID",
		5: "VENDOR",
		6: "VENDOR_DEVICE_ID",
		7: "SIM_ID",
	}
	DeviceRegisterationUpdateFieldMask_value = map[string]int32{
		"UNSPECIFIED":         0,
		"REGISTRATION_STATUS": 1,
		"DEVICE_TOKEN":        2,
		"DEVICE_REG_METADATA": 3,
		"ATTEMPT_ID":          4,
		"VENDOR":              5,
		"VENDOR_DEVICE_ID":    6,
		"SIM_ID":              7,
	}
)

func (x DeviceRegisterationUpdateFieldMask) Enum() *DeviceRegisterationUpdateFieldMask {
	p := new(DeviceRegisterationUpdateFieldMask)
	*p = x
	return p
}

func (x DeviceRegisterationUpdateFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceRegisterationUpdateFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_device_registration_proto_enumTypes[0].Descriptor()
}

func (DeviceRegisterationUpdateFieldMask) Type() protoreflect.EnumType {
	return &file_api_auth_internal_device_registration_proto_enumTypes[0]
}

func (x DeviceRegisterationUpdateFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceRegisterationUpdateFieldMask.Descriptor instead.
func (DeviceRegisterationUpdateFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_device_registration_proto_rawDescGZIP(), []int{0}
}

type DeviceRegistrationSource int32

const (
	DeviceRegistrationSource_DEVICE_REGISTRATION_SOURCE_UNSPECIFIED                        DeviceRegistrationSource = 0
	DeviceRegistrationSource_DEVICE_REGISTRATION_SOURCE_FEDERAL_SAVINGS_ACCOUNT_ONBOARDING DeviceRegistrationSource = 1
	DeviceRegistrationSource_DEVICE_REGISTRATION_SOURCE_TPAP                               DeviceRegistrationSource = 2
	DeviceRegistrationSource_DEVICE_REGISTRATION_SOURCE_FEDERAL_CREDIT_CARD                DeviceRegistrationSource = 3
	DeviceRegistrationSource_DEVICE_REGISTRATION_SOURCE_AFU                                DeviceRegistrationSource = 4
)

// Enum value maps for DeviceRegistrationSource.
var (
	DeviceRegistrationSource_name = map[int32]string{
		0: "DEVICE_REGISTRATION_SOURCE_UNSPECIFIED",
		1: "DEVICE_REGISTRATION_SOURCE_FEDERAL_SAVINGS_ACCOUNT_ONBOARDING",
		2: "DEVICE_REGISTRATION_SOURCE_TPAP",
		3: "DEVICE_REGISTRATION_SOURCE_FEDERAL_CREDIT_CARD",
		4: "DEVICE_REGISTRATION_SOURCE_AFU",
	}
	DeviceRegistrationSource_value = map[string]int32{
		"DEVICE_REGISTRATION_SOURCE_UNSPECIFIED":                        0,
		"DEVICE_REGISTRATION_SOURCE_FEDERAL_SAVINGS_ACCOUNT_ONBOARDING": 1,
		"DEVICE_REGISTRATION_SOURCE_TPAP":                               2,
		"DEVICE_REGISTRATION_SOURCE_FEDERAL_CREDIT_CARD":                3,
		"DEVICE_REGISTRATION_SOURCE_AFU":                                4,
	}
)

func (x DeviceRegistrationSource) Enum() *DeviceRegistrationSource {
	p := new(DeviceRegistrationSource)
	*p = x
	return p
}

func (x DeviceRegistrationSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceRegistrationSource) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_device_registration_proto_enumTypes[1].Descriptor()
}

func (DeviceRegistrationSource) Type() protoreflect.EnumType {
	return &file_api_auth_internal_device_registration_proto_enumTypes[1]
}

func (x DeviceRegistrationSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceRegistrationSource.Descriptor instead.
func (DeviceRegistrationSource) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_device_registration_proto_rawDescGZIP(), []int{1}
}

type DeviceRegistration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique id to denote a device of user
	DeviceId string `protobuf:"bytes,1,opt,name=DeviceId,proto3" json:"DeviceId,omitempty"`
	// Device details of a user device
	Device *common.Device `protobuf:"bytes,2,opt,name=Device,proto3" json:"Device,omitempty"`
	// Unique id to denote a user device for vendor apis
	DeviceToken string `protobuf:"bytes,3,opt,name=DeviceToken,proto3" json:"DeviceToken,omitempty"`
	// Unique id to represent a user
	UserProfileId string `protobuf:"bytes,4,opt,name=UserProfileId,proto3" json:"UserProfileId,omitempty"`
	// Represents the status of device registration of customer
	Status DeviceRegistrationStatus `protobuf:"varint,5,opt,name=Status,proto3,enum=auth.DeviceRegistrationStatus" json:"Status,omitempty"`
	// Actor id of the device holder
	ActorId  string                      `protobuf:"bytes,6,opt,name=ActorId,proto3" json:"ActorId,omitempty"`
	Metadata *DeviceRegistrationMetadata `protobuf:"bytes,9,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated_at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// attempt id
	AttemptId string `protobuf:"bytes,10,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
	// time at which the entry from db was soft-deleted
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// time (in unix) at which record was soft-deleted
	DeletedAtUnix int64 `protobuf:"varint,12,opt,name=deleted_at_unix,json=deletedAtUnix,proto3" json:"deleted_at_unix,omitempty"`
	// sim_id stores the sim id as returned by client from which device registration was performed to be stored at backend.
	SimId string `protobuf:"bytes,13,opt,name=sim_id,json=simId,proto3" json:"sim_id,omitempty"`
	// device Id which is registered at vendor
	VendorDeviceId string `protobuf:"bytes,14,opt,name=vendor_device_id,json=vendorDeviceId,proto3" json:"vendor_device_id,omitempty"`
	// vendor type on which device id was registered
	Vendor vendorgateway.Vendor `protobuf:"varint,15,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
}

func (x *DeviceRegistration) Reset() {
	*x = DeviceRegistration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_device_registration_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceRegistration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRegistration) ProtoMessage() {}

func (x *DeviceRegistration) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_device_registration_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRegistration.ProtoReflect.Descriptor instead.
func (*DeviceRegistration) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_device_registration_proto_rawDescGZIP(), []int{0}
}

func (x *DeviceRegistration) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *DeviceRegistration) GetDevice() *common.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *DeviceRegistration) GetDeviceToken() string {
	if x != nil {
		return x.DeviceToken
	}
	return ""
}

func (x *DeviceRegistration) GetUserProfileId() string {
	if x != nil {
		return x.UserProfileId
	}
	return ""
}

func (x *DeviceRegistration) GetStatus() DeviceRegistrationStatus {
	if x != nil {
		return x.Status
	}
	return DeviceRegistrationStatus_DEVICE_STATUS_UNSPECIFIED
}

func (x *DeviceRegistration) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DeviceRegistration) GetMetadata() *DeviceRegistrationMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *DeviceRegistration) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DeviceRegistration) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *DeviceRegistration) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *DeviceRegistration) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *DeviceRegistration) GetDeletedAtUnix() int64 {
	if x != nil {
		return x.DeletedAtUnix
	}
	return 0
}

func (x *DeviceRegistration) GetSimId() string {
	if x != nil {
		return x.SimId
	}
	return ""
}

func (x *DeviceRegistration) GetVendorDeviceId() string {
	if x != nil {
		return x.VendorDeviceId
	}
	return ""
}

func (x *DeviceRegistration) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

// DeviceRegistrationMetadata stores metadata related to DeviceRegistration entity
type DeviceRegistrationMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// request id of the API call made to the vendor.
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *DeviceRegistrationMetadata) Reset() {
	*x = DeviceRegistrationMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_device_registration_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceRegistrationMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRegistrationMetadata) ProtoMessage() {}

func (x *DeviceRegistrationMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_device_registration_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRegistrationMetadata.ProtoReflect.Descriptor instead.
func (*DeviceRegistrationMetadata) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_device_registration_proto_rawDescGZIP(), []int{1}
}

func (x *DeviceRegistrationMetadata) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

var File_api_auth_internal_device_registration_proto protoreflect.FileDescriptor

var file_api_auth_internal_device_registration_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x61,
	0x75, 0x74, 0x68, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xa4, 0x05, 0x0a, 0x12, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x55, 0x73, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12,
	0x36, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x3c, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x26, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x75, 0x6e,
	0x69, 0x78, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x55, 0x6e, 0x69, 0x78, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x69, 0x6d, 0x5f, 0x69,
	0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x69, 0x6d, 0x49, 0x64, 0x12, 0x28,
	0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52,
	0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x22, 0x3b, 0x0a, 0x1a, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x2a, 0xb7, 0x01, 0x0a, 0x22, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x0f, 0x0a, 0x0b, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13,
	0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f,
	0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x45, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x52, 0x45, 0x47, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x10, 0x03,
	0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x04,
	0x12, 0x0a, 0x0a, 0x06, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10,
	0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x44,
	0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x49, 0x4d, 0x5f, 0x49, 0x44, 0x10, 0x07, 0x2a, 0x86,
	0x02, 0x0a, 0x18, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x26, 0x44,
	0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x41, 0x0a, 0x3d, 0x44, 0x45, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x53, 0x41,
	0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x44, 0x45,
	0x56, 0x49, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x50, 0x41, 0x50, 0x10, 0x02, 0x12,
	0x32, 0x0a, 0x2e, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x46, 0x45,
	0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x52, 0x45,
	0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43,
	0x45, 0x5f, 0x41, 0x46, 0x55, 0x10, 0x04, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_auth_internal_device_registration_proto_rawDescOnce sync.Once
	file_api_auth_internal_device_registration_proto_rawDescData = file_api_auth_internal_device_registration_proto_rawDesc
)

func file_api_auth_internal_device_registration_proto_rawDescGZIP() []byte {
	file_api_auth_internal_device_registration_proto_rawDescOnce.Do(func() {
		file_api_auth_internal_device_registration_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_internal_device_registration_proto_rawDescData)
	})
	return file_api_auth_internal_device_registration_proto_rawDescData
}

var file_api_auth_internal_device_registration_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_auth_internal_device_registration_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_auth_internal_device_registration_proto_goTypes = []interface{}{
	(DeviceRegisterationUpdateFieldMask)(0), // 0: auth.DeviceRegisterationUpdateFieldMask
	(DeviceRegistrationSource)(0),           // 1: auth.DeviceRegistrationSource
	(*DeviceRegistration)(nil),              // 2: auth.DeviceRegistration
	(*DeviceRegistrationMetadata)(nil),      // 3: auth.DeviceRegistrationMetadata
	(*common.Device)(nil),                   // 4: api.typesv2.common.Device
	(DeviceRegistrationStatus)(0),           // 5: auth.DeviceRegistrationStatus
	(*timestamppb.Timestamp)(nil),           // 6: google.protobuf.Timestamp
	(vendorgateway.Vendor)(0),               // 7: vendorgateway.Vendor
}
var file_api_auth_internal_device_registration_proto_depIdxs = []int32{
	4, // 0: auth.DeviceRegistration.Device:type_name -> api.typesv2.common.Device
	5, // 1: auth.DeviceRegistration.Status:type_name -> auth.DeviceRegistrationStatus
	3, // 2: auth.DeviceRegistration.metadata:type_name -> auth.DeviceRegistrationMetadata
	6, // 3: auth.DeviceRegistration.created_at:type_name -> google.protobuf.Timestamp
	6, // 4: auth.DeviceRegistration.updated_at:type_name -> google.protobuf.Timestamp
	6, // 5: auth.DeviceRegistration.deleted_at:type_name -> google.protobuf.Timestamp
	7, // 6: auth.DeviceRegistration.vendor:type_name -> vendorgateway.Vendor
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_auth_internal_device_registration_proto_init() }
func file_api_auth_internal_device_registration_proto_init() {
	if File_api_auth_internal_device_registration_proto != nil {
		return
	}
	file_api_auth_device_registration_status_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_auth_internal_device_registration_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceRegistration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_device_registration_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceRegistrationMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_internal_device_registration_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_internal_device_registration_proto_goTypes,
		DependencyIndexes: file_api_auth_internal_device_registration_proto_depIdxs,
		EnumInfos:         file_api_auth_internal_device_registration_proto_enumTypes,
		MessageInfos:      file_api_auth_internal_device_registration_proto_msgTypes,
	}.Build()
	File_api_auth_internal_device_registration_proto = out.File
	file_api_auth_internal_device_registration_proto_rawDesc = nil
	file_api_auth_internal_device_registration_proto_goTypes = nil
	file_api_auth_internal_device_registration_proto_depIdxs = nil
}
