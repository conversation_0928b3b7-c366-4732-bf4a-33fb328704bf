// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/ios_device_attestation_data.proto

package auth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on IosDeviceAttestationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IosDeviceAttestationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IosDeviceAttestationData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IosDeviceAttestationDataMultiError, or nil if none found.
func (m *IosDeviceAttestationData) ValidateAll() error {
	return m.validate(true)
}

func (m *IosDeviceAttestationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for DeviceId

	// no validation rules for PublicKey

	// no validation rules for Receipt

	// no validation rules for Counter

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IosDeviceAttestationDataValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IosDeviceAttestationDataValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IosDeviceAttestationDataValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IosDeviceAttestationDataValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IosDeviceAttestationDataValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IosDeviceAttestationDataValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IosDeviceAttestationDataValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IosDeviceAttestationDataValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IosDeviceAttestationDataValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IosDeviceAttestationDataMultiError(errors)
	}

	return nil
}

// IosDeviceAttestationDataMultiError is an error wrapping multiple validation
// errors returned by IosDeviceAttestationData.ValidateAll() if the designated
// constraints aren't met.
type IosDeviceAttestationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IosDeviceAttestationDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IosDeviceAttestationDataMultiError) AllErrors() []error { return m }

// IosDeviceAttestationDataValidationError is the validation error returned by
// IosDeviceAttestationData.Validate if the designated constraints aren't met.
type IosDeviceAttestationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IosDeviceAttestationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IosDeviceAttestationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IosDeviceAttestationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IosDeviceAttestationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IosDeviceAttestationDataValidationError) ErrorName() string {
	return "IosDeviceAttestationDataValidationError"
}

// Error satisfies the builtin error interface
func (e IosDeviceAttestationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIosDeviceAttestationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IosDeviceAttestationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IosDeviceAttestationDataValidationError{}
