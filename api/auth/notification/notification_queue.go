package notification

import queuePb "github.com/epifi/be-common/api/queue"

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (m *AuthFactorUpdateEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	m.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (m *LivenessManualReviewEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	m.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (m *AuthTokenCreationEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	m.RequestHeader = header
}
