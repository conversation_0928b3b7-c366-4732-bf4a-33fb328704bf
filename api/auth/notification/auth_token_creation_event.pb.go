// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/notification/auth_token_creation_event.proto

package notification

import (
	queue "github.com/epifi/be-common/api/queue"
	auth "github.com/epifi/gamma/api/auth"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthTokenCreationEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header to be passed in all events
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// ActorId may be empty in some cases
	// for example, refresh token creation for a new user will not have actorId as actor is not yet created
	ActorId   string                 `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	TokenType auth.TokenType         `protobuf:"varint,3,opt,name=token_type,json=tokenType,proto3,enum=auth.TokenType" json:"token_type,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *AuthTokenCreationEvent) Reset() {
	*x = AuthTokenCreationEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_notification_auth_token_creation_event_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthTokenCreationEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthTokenCreationEvent) ProtoMessage() {}

func (x *AuthTokenCreationEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_notification_auth_token_creation_event_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthTokenCreationEvent.ProtoReflect.Descriptor instead.
func (*AuthTokenCreationEvent) Descriptor() ([]byte, []int) {
	return file_api_auth_notification_auth_token_creation_event_proto_rawDescGZIP(), []int{0}
}

func (x *AuthTokenCreationEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *AuthTokenCreationEvent) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *AuthTokenCreationEvent) GetTokenType() auth.TokenType {
	if x != nil {
		return x.TokenType
	}
	return auth.TokenType(0)
}

func (x *AuthTokenCreationEvent) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

var File_api_auth_notification_auth_token_creation_event_proto protoreflect.FileDescriptor

var file_api_auth_notification_auth_token_creation_event_proto_rawDesc = []byte{
	0x0a, 0x35, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x61, 0x75, 0x74, 0x68, 0x1a, 0x1d, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70,
	0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xe3, 0x01, 0x0a, 0x16, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x0a, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_notification_auth_token_creation_event_proto_rawDescOnce sync.Once
	file_api_auth_notification_auth_token_creation_event_proto_rawDescData = file_api_auth_notification_auth_token_creation_event_proto_rawDesc
)

func file_api_auth_notification_auth_token_creation_event_proto_rawDescGZIP() []byte {
	file_api_auth_notification_auth_token_creation_event_proto_rawDescOnce.Do(func() {
		file_api_auth_notification_auth_token_creation_event_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_notification_auth_token_creation_event_proto_rawDescData)
	})
	return file_api_auth_notification_auth_token_creation_event_proto_rawDescData
}

var file_api_auth_notification_auth_token_creation_event_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_auth_notification_auth_token_creation_event_proto_goTypes = []interface{}{
	(*AuthTokenCreationEvent)(nil),      // 0: auth.AuthTokenCreationEvent
	(*queue.ConsumerRequestHeader)(nil), // 1: queue.ConsumerRequestHeader
	(auth.TokenType)(0),                 // 2: auth.TokenType
	(*timestamppb.Timestamp)(nil),       // 3: google.protobuf.Timestamp
}
var file_api_auth_notification_auth_token_creation_event_proto_depIdxs = []int32{
	1, // 0: auth.AuthTokenCreationEvent.request_header:type_name -> queue.ConsumerRequestHeader
	2, // 1: auth.AuthTokenCreationEvent.token_type:type_name -> auth.TokenType
	3, // 2: auth.AuthTokenCreationEvent.created_at:type_name -> google.protobuf.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_auth_notification_auth_token_creation_event_proto_init() }
func file_api_auth_notification_auth_token_creation_event_proto_init() {
	if File_api_auth_notification_auth_token_creation_event_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_notification_auth_token_creation_event_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthTokenCreationEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_notification_auth_token_creation_event_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_notification_auth_token_creation_event_proto_goTypes,
		DependencyIndexes: file_api_auth_notification_auth_token_creation_event_proto_depIdxs,
		MessageInfos:      file_api_auth_notification_auth_token_creation_event_proto_msgTypes,
	}.Build()
	File_api_auth_notification_auth_token_creation_event_proto = out.File
	file_api_auth_notification_auth_token_creation_event_proto_rawDesc = nil
	file_api_auth_notification_auth_token_creation_event_proto_goTypes = nil
	file_api_auth_notification_auth_token_creation_event_proto_depIdxs = nil
}
