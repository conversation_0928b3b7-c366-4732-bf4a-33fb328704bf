// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/notification/liveness.proto

package notification

import (
	queue "github.com/epifi/be-common/api/queue"
	liveness "github.com/epifi/gamma/api/auth/liveness"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LivenessManualReviewEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header to be passed in all events
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Deprecated: use summary.
	// TODO(ankit): Remove these fields after the code goes to prod.
	//
	// Deprecated: Marked as deprecated in api/auth/notification/liveness.proto.
	AfuId string `protobuf:"bytes,2,opt,name=afu_id,json=afuId,proto3" json:"afu_id,omitempty"`
	// Deprecated: Marked as deprecated in api/auth/notification/liveness.proto.
	SummaryStatus liveness.SummaryStatus    `protobuf:"varint,3,opt,name=summary_status,json=summaryStatus,proto3,enum=auth.liveness.SummaryStatus" json:"summary_status,omitempty"`
	Summary       *liveness.LivenessSummary `protobuf:"bytes,4,opt,name=summary,proto3" json:"summary,omitempty"`
}

func (x *LivenessManualReviewEvent) Reset() {
	*x = LivenessManualReviewEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_notification_liveness_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessManualReviewEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessManualReviewEvent) ProtoMessage() {}

func (x *LivenessManualReviewEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_notification_liveness_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessManualReviewEvent.ProtoReflect.Descriptor instead.
func (*LivenessManualReviewEvent) Descriptor() ([]byte, []int) {
	return file_api_auth_notification_liveness_proto_rawDescGZIP(), []int{0}
}

func (x *LivenessManualReviewEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

// Deprecated: Marked as deprecated in api/auth/notification/liveness.proto.
func (x *LivenessManualReviewEvent) GetAfuId() string {
	if x != nil {
		return x.AfuId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/auth/notification/liveness.proto.
func (x *LivenessManualReviewEvent) GetSummaryStatus() liveness.SummaryStatus {
	if x != nil {
		return x.SummaryStatus
	}
	return liveness.SummaryStatus(0)
}

func (x *LivenessManualReviewEvent) GetSummary() *liveness.LivenessSummary {
	if x != nil {
		return x.Summary
	}
	return nil
}

var File_api_auth_notification_liveness_proto protoreflect.FileDescriptor

var file_api_auth_notification_liveness_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x61, 0x75, 0x74, 0x68, 0x1a, 0x20, 0x61, 0x70,
	0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xfe, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x61, 0x6e,
	0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x43,
	0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x06, 0x61, 0x66, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x05, 0x61, 0x66, 0x75, 0x49, 0x64, 0x12, 0x47,
	0x0a, 0x0e, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75,
	0x74, 0x68, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_notification_liveness_proto_rawDescOnce sync.Once
	file_api_auth_notification_liveness_proto_rawDescData = file_api_auth_notification_liveness_proto_rawDesc
)

func file_api_auth_notification_liveness_proto_rawDescGZIP() []byte {
	file_api_auth_notification_liveness_proto_rawDescOnce.Do(func() {
		file_api_auth_notification_liveness_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_notification_liveness_proto_rawDescData)
	})
	return file_api_auth_notification_liveness_proto_rawDescData
}

var file_api_auth_notification_liveness_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_auth_notification_liveness_proto_goTypes = []interface{}{
	(*LivenessManualReviewEvent)(nil),   // 0: auth.LivenessManualReviewEvent
	(*queue.ConsumerRequestHeader)(nil), // 1: queue.ConsumerRequestHeader
	(liveness.SummaryStatus)(0),         // 2: auth.liveness.SummaryStatus
	(*liveness.LivenessSummary)(nil),    // 3: auth.liveness.LivenessSummary
}
var file_api_auth_notification_liveness_proto_depIdxs = []int32{
	1, // 0: auth.LivenessManualReviewEvent.request_header:type_name -> queue.ConsumerRequestHeader
	2, // 1: auth.LivenessManualReviewEvent.summary_status:type_name -> auth.liveness.SummaryStatus
	3, // 2: auth.LivenessManualReviewEvent.summary:type_name -> auth.liveness.LivenessSummary
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_auth_notification_liveness_proto_init() }
func file_api_auth_notification_liveness_proto_init() {
	if File_api_auth_notification_liveness_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_notification_liveness_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessManualReviewEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_notification_liveness_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_notification_liveness_proto_goTypes,
		DependencyIndexes: file_api_auth_notification_liveness_proto_depIdxs,
		MessageInfos:      file_api_auth_notification_liveness_proto_msgTypes,
	}.Build()
	File_api_auth_notification_liveness_proto = out.File
	file_api_auth_notification_liveness_proto_rawDesc = nil
	file_api_auth_notification_liveness_proto_goTypes = nil
	file_api_auth_notification_liveness_proto_depIdxs = nil
}
