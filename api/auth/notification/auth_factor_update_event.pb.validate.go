// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/notification/auth_factor_update_event.proto

package notification

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	afu "github.com/epifi/gamma/api/auth/afu"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = afu.AuthFactor(0)
)

// Validate checks the field values on AuthFactorUpdateEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthFactorUpdateEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthFactorUpdateEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthFactorUpdateEventMultiError, or nil if none found.
func (m *AuthFactorUpdateEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthFactorUpdateEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthFactorUpdateEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthFactorUpdateEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthFactorUpdateEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetOldAuthFactors()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthFactorUpdateEventValidationError{
					field:  "OldAuthFactors",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthFactorUpdateEventValidationError{
					field:  "OldAuthFactors",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOldAuthFactors()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthFactorUpdateEventValidationError{
				field:  "OldAuthFactors",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNewAuthFactors()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthFactorUpdateEventValidationError{
					field:  "NewAuthFactors",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthFactorUpdateEventValidationError{
					field:  "NewAuthFactors",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNewAuthFactors()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthFactorUpdateEventValidationError{
				field:  "NewAuthFactors",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthFactorUpdateEventMultiError(errors)
	}

	return nil
}

// AuthFactorUpdateEventMultiError is an error wrapping multiple validation
// errors returned by AuthFactorUpdateEvent.ValidateAll() if the designated
// constraints aren't met.
type AuthFactorUpdateEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthFactorUpdateEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthFactorUpdateEventMultiError) AllErrors() []error { return m }

// AuthFactorUpdateEventValidationError is the validation error returned by
// AuthFactorUpdateEvent.Validate if the designated constraints aren't met.
type AuthFactorUpdateEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthFactorUpdateEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthFactorUpdateEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthFactorUpdateEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthFactorUpdateEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthFactorUpdateEventValidationError) ErrorName() string {
	return "AuthFactorUpdateEventValidationError"
}

// Error satisfies the builtin error interface
func (e AuthFactorUpdateEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthFactorUpdateEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthFactorUpdateEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthFactorUpdateEventValidationError{}
