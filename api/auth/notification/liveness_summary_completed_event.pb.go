// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/notification/liveness_summary_completed_event.proto

package notification

import (
	queue "github.com/epifi/be-common/api/queue"
	liveness "github.com/epifi/gamma/api/auth/liveness"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LivenessSummaryCompletedEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header to be passed in all events
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// actor whose liveness state is getting updated
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Numbers of attempts completed
	AttemptsCount          int32                           `protobuf:"varint,3,opt,name=attempts_count,json=attemptsCount,proto3" json:"attempts_count,omitempty"`
	SummaryLivenessStatus  liveness.SummaryLivenessStatus  `protobuf:"varint,4,opt,name=summary_liveness_status,json=summaryLivenessStatus,proto3,enum=auth.liveness.SummaryLivenessStatus" json:"summary_liveness_status,omitempty"`
	SummaryFacematchStatus liveness.SummaryFacematchStatus `protobuf:"varint,5,opt,name=summary_facematch_status,json=summaryFacematchStatus,proto3,enum=auth.liveness.SummaryFacematchStatus" json:"summary_facematch_status,omitempty"`
	// Liveness score
	LivenessScore float32 `protobuf:"fixed32,6,opt,name=liveness_score,json=livenessScore,proto3" json:"liveness_score,omitempty"`
	// Facematch score
	FaceMatchScore float32 `protobuf:"fixed32,7,opt,name=face_match_score,json=faceMatchScore,proto3" json:"face_match_score,omitempty"`
	// contains location related info
	Metadata *liveness.Metadata `protobuf:"bytes,8,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *LivenessSummaryCompletedEvent) Reset() {
	*x = LivenessSummaryCompletedEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_notification_liveness_summary_completed_event_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessSummaryCompletedEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessSummaryCompletedEvent) ProtoMessage() {}

func (x *LivenessSummaryCompletedEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_notification_liveness_summary_completed_event_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessSummaryCompletedEvent.ProtoReflect.Descriptor instead.
func (*LivenessSummaryCompletedEvent) Descriptor() ([]byte, []int) {
	return file_api_auth_notification_liveness_summary_completed_event_proto_rawDescGZIP(), []int{0}
}

func (x *LivenessSummaryCompletedEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *LivenessSummaryCompletedEvent) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *LivenessSummaryCompletedEvent) GetAttemptsCount() int32 {
	if x != nil {
		return x.AttemptsCount
	}
	return 0
}

func (x *LivenessSummaryCompletedEvent) GetSummaryLivenessStatus() liveness.SummaryLivenessStatus {
	if x != nil {
		return x.SummaryLivenessStatus
	}
	return liveness.SummaryLivenessStatus(0)
}

func (x *LivenessSummaryCompletedEvent) GetSummaryFacematchStatus() liveness.SummaryFacematchStatus {
	if x != nil {
		return x.SummaryFacematchStatus
	}
	return liveness.SummaryFacematchStatus(0)
}

func (x *LivenessSummaryCompletedEvent) GetLivenessScore() float32 {
	if x != nil {
		return x.LivenessScore
	}
	return 0
}

func (x *LivenessSummaryCompletedEvent) GetFaceMatchScore() float32 {
	if x != nil {
		return x.FaceMatchScore
	}
	return 0
}

func (x *LivenessSummaryCompletedEvent) GetMetadata() *liveness.Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

var File_api_auth_notification_liveness_summary_completed_event_proto protoreflect.FileDescriptor

var file_api_auth_notification_liveness_summary_completed_event_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04,
	0x61, 0x75, 0x74, 0x68, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74,
	0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xeb, 0x03, 0x0a, 0x1d, 0x4c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0d, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x5c, 0x0a, 0x17, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x15, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x5f, 0x0a, 0x18, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x16, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x61, 0x63, 0x65, 0x5f,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0e, 0x66, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x33, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_notification_liveness_summary_completed_event_proto_rawDescOnce sync.Once
	file_api_auth_notification_liveness_summary_completed_event_proto_rawDescData = file_api_auth_notification_liveness_summary_completed_event_proto_rawDesc
)

func file_api_auth_notification_liveness_summary_completed_event_proto_rawDescGZIP() []byte {
	file_api_auth_notification_liveness_summary_completed_event_proto_rawDescOnce.Do(func() {
		file_api_auth_notification_liveness_summary_completed_event_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_notification_liveness_summary_completed_event_proto_rawDescData)
	})
	return file_api_auth_notification_liveness_summary_completed_event_proto_rawDescData
}

var file_api_auth_notification_liveness_summary_completed_event_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_auth_notification_liveness_summary_completed_event_proto_goTypes = []interface{}{
	(*LivenessSummaryCompletedEvent)(nil), // 0: auth.LivenessSummaryCompletedEvent
	(*queue.ConsumerRequestHeader)(nil),   // 1: queue.ConsumerRequestHeader
	(liveness.SummaryLivenessStatus)(0),   // 2: auth.liveness.SummaryLivenessStatus
	(liveness.SummaryFacematchStatus)(0),  // 3: auth.liveness.SummaryFacematchStatus
	(*liveness.Metadata)(nil),             // 4: auth.liveness.Metadata
}
var file_api_auth_notification_liveness_summary_completed_event_proto_depIdxs = []int32{
	1, // 0: auth.LivenessSummaryCompletedEvent.request_header:type_name -> queue.ConsumerRequestHeader
	2, // 1: auth.LivenessSummaryCompletedEvent.summary_liveness_status:type_name -> auth.liveness.SummaryLivenessStatus
	3, // 2: auth.LivenessSummaryCompletedEvent.summary_facematch_status:type_name -> auth.liveness.SummaryFacematchStatus
	4, // 3: auth.LivenessSummaryCompletedEvent.metadata:type_name -> auth.liveness.Metadata
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_auth_notification_liveness_summary_completed_event_proto_init() }
func file_api_auth_notification_liveness_summary_completed_event_proto_init() {
	if File_api_auth_notification_liveness_summary_completed_event_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_notification_liveness_summary_completed_event_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessSummaryCompletedEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_notification_liveness_summary_completed_event_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_notification_liveness_summary_completed_event_proto_goTypes,
		DependencyIndexes: file_api_auth_notification_liveness_summary_completed_event_proto_depIdxs,
		MessageInfos:      file_api_auth_notification_liveness_summary_completed_event_proto_msgTypes,
	}.Build()
	File_api_auth_notification_liveness_summary_completed_event_proto = out.File
	file_api_auth_notification_liveness_summary_completed_event_proto_rawDesc = nil
	file_api_auth_notification_liveness_summary_completed_event_proto_goTypes = nil
	file_api_auth_notification_liveness_summary_completed_event_proto_depIdxs = nil
}
