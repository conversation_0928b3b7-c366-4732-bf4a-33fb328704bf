// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/notification/auth_token_creation_event.proto

package notification

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AuthTokenCreationEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthTokenCreationEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthTokenCreationEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthTokenCreationEventMultiError, or nil if none found.
func (m *AuthTokenCreationEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthTokenCreationEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthTokenCreationEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthTokenCreationEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthTokenCreationEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for TokenType

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthTokenCreationEventValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthTokenCreationEventValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthTokenCreationEventValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthTokenCreationEventMultiError(errors)
	}

	return nil
}

// AuthTokenCreationEventMultiError is an error wrapping multiple validation
// errors returned by AuthTokenCreationEvent.ValidateAll() if the designated
// constraints aren't met.
type AuthTokenCreationEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthTokenCreationEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthTokenCreationEventMultiError) AllErrors() []error { return m }

// AuthTokenCreationEventValidationError is the validation error returned by
// AuthTokenCreationEvent.Validate if the designated constraints aren't met.
type AuthTokenCreationEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthTokenCreationEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthTokenCreationEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthTokenCreationEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthTokenCreationEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthTokenCreationEventValidationError) ErrorName() string {
	return "AuthTokenCreationEventValidationError"
}

// Error satisfies the builtin error interface
func (e AuthTokenCreationEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthTokenCreationEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthTokenCreationEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthTokenCreationEventValidationError{}
