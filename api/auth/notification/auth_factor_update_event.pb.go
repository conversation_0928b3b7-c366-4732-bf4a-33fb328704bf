// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/notification/auth_factor_update_event.proto

package notification

import (
	queue "github.com/epifi/be-common/api/queue"
	afu "github.com/epifi/gamma/api/auth/afu"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AuthFactorUpdateEvent is published whenever auth factors are updated for a user
type AuthFactorUpdateEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the consumer grpc services.
	// contains important information related to message retry state.
	// passed along by queue subscriber.
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// actor whose account state is getting updated
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// auth factors that have been updated
	AuthFactors []afu.AuthFactor `protobuf:"varint,3,rep,packed,name=auth_factors,json=authFactors,proto3,enum=auth.afu.AuthFactor" json:"auth_factors,omitempty"`
	// auth factor values before update
	OldAuthFactors *afu.AuthFactorValues `protobuf:"bytes,4,opt,name=old_auth_factors,json=oldAuthFactors,proto3" json:"old_auth_factors,omitempty"`
	// auth factor values after update
	NewAuthFactors *afu.AuthFactorValues `protobuf:"bytes,5,opt,name=new_auth_factors,json=newAuthFactors,proto3" json:"new_auth_factors,omitempty"`
}

func (x *AuthFactorUpdateEvent) Reset() {
	*x = AuthFactorUpdateEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_notification_auth_factor_update_event_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthFactorUpdateEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthFactorUpdateEvent) ProtoMessage() {}

func (x *AuthFactorUpdateEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_notification_auth_factor_update_event_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthFactorUpdateEvent.ProtoReflect.Descriptor instead.
func (*AuthFactorUpdateEvent) Descriptor() ([]byte, []int) {
	return file_api_auth_notification_auth_factor_update_event_proto_rawDescGZIP(), []int{0}
}

func (x *AuthFactorUpdateEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *AuthFactorUpdateEvent) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *AuthFactorUpdateEvent) GetAuthFactors() []afu.AuthFactor {
	if x != nil {
		return x.AuthFactors
	}
	return nil
}

func (x *AuthFactorUpdateEvent) GetOldAuthFactors() *afu.AuthFactorValues {
	if x != nil {
		return x.OldAuthFactors
	}
	return nil
}

func (x *AuthFactorUpdateEvent) GetNewAuthFactors() *afu.AuthFactorValues {
	if x != nil {
		return x.NewAuthFactors
	}
	return nil
}

var File_api_auth_notification_auth_factor_update_event_proto protoreflect.FileDescriptor

var file_api_auth_notification_auth_factor_update_event_proto_rawDesc = []byte{
	0x0a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x61, 0x75, 0x74, 0x68, 0x1a, 0x2a, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbc, 0x02, 0x0a, 0x15, 0x41,
	0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x52, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x44, 0x0a,
	0x10, 0x6f, 0x6c, 0x64, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61,
	0x66, 0x75, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x52, 0x0e, 0x6f, 0x6c, 0x64, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x73, 0x12, 0x44, 0x0a, 0x10, 0x6e, 0x65, 0x77, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x0e, 0x6e, 0x65, 0x77, 0x41, 0x75,
	0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_notification_auth_factor_update_event_proto_rawDescOnce sync.Once
	file_api_auth_notification_auth_factor_update_event_proto_rawDescData = file_api_auth_notification_auth_factor_update_event_proto_rawDesc
)

func file_api_auth_notification_auth_factor_update_event_proto_rawDescGZIP() []byte {
	file_api_auth_notification_auth_factor_update_event_proto_rawDescOnce.Do(func() {
		file_api_auth_notification_auth_factor_update_event_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_notification_auth_factor_update_event_proto_rawDescData)
	})
	return file_api_auth_notification_auth_factor_update_event_proto_rawDescData
}

var file_api_auth_notification_auth_factor_update_event_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_auth_notification_auth_factor_update_event_proto_goTypes = []interface{}{
	(*AuthFactorUpdateEvent)(nil),       // 0: auth.AuthFactorUpdateEvent
	(*queue.ConsumerRequestHeader)(nil), // 1: queue.ConsumerRequestHeader
	(afu.AuthFactor)(0),                 // 2: auth.afu.AuthFactor
	(*afu.AuthFactorValues)(nil),        // 3: auth.afu.AuthFactorValues
}
var file_api_auth_notification_auth_factor_update_event_proto_depIdxs = []int32{
	1, // 0: auth.AuthFactorUpdateEvent.request_header:type_name -> queue.ConsumerRequestHeader
	2, // 1: auth.AuthFactorUpdateEvent.auth_factors:type_name -> auth.afu.AuthFactor
	3, // 2: auth.AuthFactorUpdateEvent.old_auth_factors:type_name -> auth.afu.AuthFactorValues
	3, // 3: auth.AuthFactorUpdateEvent.new_auth_factors:type_name -> auth.afu.AuthFactorValues
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_auth_notification_auth_factor_update_event_proto_init() }
func file_api_auth_notification_auth_factor_update_event_proto_init() {
	if File_api_auth_notification_auth_factor_update_event_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_notification_auth_factor_update_event_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthFactorUpdateEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_notification_auth_factor_update_event_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_notification_auth_factor_update_event_proto_goTypes,
		DependencyIndexes: file_api_auth_notification_auth_factor_update_event_proto_depIdxs,
		MessageInfos:      file_api_auth_notification_auth_factor_update_event_proto_msgTypes,
	}.Build()
	File_api_auth_notification_auth_factor_update_event_proto = out.File
	file_api_auth_notification_auth_factor_update_event_proto_rawDesc = nil
	file_api_auth_notification_auth_factor_update_event_proto_goTypes = nil
	file_api_auth_notification_auth_factor_update_event_proto_depIdxs = nil
}
