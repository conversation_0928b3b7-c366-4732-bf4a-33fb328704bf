// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/notification/liveness.proto

package notification

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	liveness "github.com/epifi/gamma/api/auth/liveness"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = liveness.SummaryStatus(0)
)

// Validate checks the field values on LivenessManualReviewEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LivenessManualReviewEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessManualReviewEvent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessManualReviewEventMultiError, or nil if none found.
func (m *LivenessManualReviewEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessManualReviewEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessManualReviewEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessManualReviewEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessManualReviewEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AfuId

	// no validation rules for SummaryStatus

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessManualReviewEventValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessManualReviewEventValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessManualReviewEventValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LivenessManualReviewEventMultiError(errors)
	}

	return nil
}

// LivenessManualReviewEventMultiError is an error wrapping multiple validation
// errors returned by LivenessManualReviewEvent.ValidateAll() if the
// designated constraints aren't met.
type LivenessManualReviewEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessManualReviewEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessManualReviewEventMultiError) AllErrors() []error { return m }

// LivenessManualReviewEventValidationError is the validation error returned by
// LivenessManualReviewEvent.Validate if the designated constraints aren't met.
type LivenessManualReviewEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessManualReviewEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessManualReviewEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessManualReviewEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessManualReviewEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessManualReviewEventValidationError) ErrorName() string {
	return "LivenessManualReviewEventValidationError"
}

// Error satisfies the builtin error interface
func (e LivenessManualReviewEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessManualReviewEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessManualReviewEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessManualReviewEventValidationError{}
