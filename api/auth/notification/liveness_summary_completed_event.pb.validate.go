// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/notification/liveness_summary_completed_event.proto

package notification

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	liveness "github.com/epifi/gamma/api/auth/liveness"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = liveness.SummaryLivenessStatus(0)
)

// Validate checks the field values on LivenessSummaryCompletedEvent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LivenessSummaryCompletedEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessSummaryCompletedEvent with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LivenessSummaryCompletedEventMultiError, or nil if none found.
func (m *LivenessSummaryCompletedEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessSummaryCompletedEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessSummaryCompletedEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessSummaryCompletedEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessSummaryCompletedEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for AttemptsCount

	// no validation rules for SummaryLivenessStatus

	// no validation rules for SummaryFacematchStatus

	// no validation rules for LivenessScore

	// no validation rules for FaceMatchScore

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessSummaryCompletedEventValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessSummaryCompletedEventValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessSummaryCompletedEventValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LivenessSummaryCompletedEventMultiError(errors)
	}

	return nil
}

// LivenessSummaryCompletedEventMultiError is an error wrapping multiple
// validation errors returned by LivenessSummaryCompletedEvent.ValidateAll()
// if the designated constraints aren't met.
type LivenessSummaryCompletedEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessSummaryCompletedEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessSummaryCompletedEventMultiError) AllErrors() []error { return m }

// LivenessSummaryCompletedEventValidationError is the validation error
// returned by LivenessSummaryCompletedEvent.Validate if the designated
// constraints aren't met.
type LivenessSummaryCompletedEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessSummaryCompletedEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessSummaryCompletedEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessSummaryCompletedEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessSummaryCompletedEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessSummaryCompletedEventValidationError) ErrorName() string {
	return "LivenessSummaryCompletedEventValidationError"
}

// Error satisfies the builtin error interface
func (e LivenessSummaryCompletedEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessSummaryCompletedEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessSummaryCompletedEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessSummaryCompletedEventValidationError{}
