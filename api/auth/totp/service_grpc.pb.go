// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/auth/totp/service.proto

package totp

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Totp_GenerateTotp_FullMethodName = "/auth.totp.Totp/GenerateTotp"
	Totp_VerifyTotp_FullMethodName   = "/auth.totp.Totp/VerifyTotp"
)

// TotpClient is the client API for Totp service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TotpClient interface {
	// GenerateTotp returns totp and it's expiry for given purpose
	GenerateTotp(ctx context.Context, in *GenerateTotpRequest, opts ...grpc.CallOption) (*GenerateTotpResponse, error)
	// VerifyTotp verifies if a given totp matches with a generated totp
	VerifyTotp(ctx context.Context, in *VerifyTotpRequest, opts ...grpc.CallOption) (*VerifyTotpResponse, error)
}

type totpClient struct {
	cc grpc.ClientConnInterface
}

func NewTotpClient(cc grpc.ClientConnInterface) TotpClient {
	return &totpClient{cc}
}

func (c *totpClient) GenerateTotp(ctx context.Context, in *GenerateTotpRequest, opts ...grpc.CallOption) (*GenerateTotpResponse, error) {
	out := new(GenerateTotpResponse)
	err := c.cc.Invoke(ctx, Totp_GenerateTotp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *totpClient) VerifyTotp(ctx context.Context, in *VerifyTotpRequest, opts ...grpc.CallOption) (*VerifyTotpResponse, error) {
	out := new(VerifyTotpResponse)
	err := c.cc.Invoke(ctx, Totp_VerifyTotp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TotpServer is the server API for Totp service.
// All implementations should embed UnimplementedTotpServer
// for forward compatibility
type TotpServer interface {
	// GenerateTotp returns totp and it's expiry for given purpose
	GenerateTotp(context.Context, *GenerateTotpRequest) (*GenerateTotpResponse, error)
	// VerifyTotp verifies if a given totp matches with a generated totp
	VerifyTotp(context.Context, *VerifyTotpRequest) (*VerifyTotpResponse, error)
}

// UnimplementedTotpServer should be embedded to have forward compatible implementations.
type UnimplementedTotpServer struct {
}

func (UnimplementedTotpServer) GenerateTotp(context.Context, *GenerateTotpRequest) (*GenerateTotpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateTotp not implemented")
}
func (UnimplementedTotpServer) VerifyTotp(context.Context, *VerifyTotpRequest) (*VerifyTotpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyTotp not implemented")
}

// UnsafeTotpServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TotpServer will
// result in compilation errors.
type UnsafeTotpServer interface {
	mustEmbedUnimplementedTotpServer()
}

func RegisterTotpServer(s grpc.ServiceRegistrar, srv TotpServer) {
	s.RegisterService(&Totp_ServiceDesc, srv)
}

func _Totp_GenerateTotp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateTotpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TotpServer).GenerateTotp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Totp_GenerateTotp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TotpServer).GenerateTotp(ctx, req.(*GenerateTotpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Totp_VerifyTotp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyTotpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TotpServer).VerifyTotp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Totp_VerifyTotp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TotpServer).VerifyTotp(ctx, req.(*VerifyTotpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Totp_ServiceDesc is the grpc.ServiceDesc for Totp service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Totp_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "auth.totp.Totp",
	HandlerType: (*TotpServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateTotp",
			Handler:    _Totp_GenerateTotp_Handler,
		},
		{
			MethodName: "VerifyTotp",
			Handler:    _Totp_VerifyTotp_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/auth/totp/service.proto",
}
