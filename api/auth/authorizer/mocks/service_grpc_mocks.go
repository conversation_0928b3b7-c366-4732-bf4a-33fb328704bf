// Code generated by MockGen. DO NOT EDIT.
// Source: api/auth/authorizer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	authorizer "github.com/epifi/gamma/api/auth/authorizer"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAuthorizerClient is a mock of AuthorizerClient interface.
type MockAuthorizerClient struct {
	ctrl     *gomock.Controller
	recorder *MockAuthorizerClientMockRecorder
}

// MockAuthorizerClientMockRecorder is the mock recorder for MockAuthorizerClient.
type MockAuthorizerClientMockRecorder struct {
	mock *MockAuthorizerClient
}

// NewMockAuthorizerClient creates a new mock instance.
func NewMockAuthorizerClient(ctrl *gomock.Controller) *MockAuthorizerClient {
	mock := &MockAuthorizerClient{ctrl: ctrl}
	mock.recorder = &MockAuthorizerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAuthorizerClient) EXPECT() *MockAuthorizerClientMockRecorder {
	return m.recorder
}

// Authorize mocks base method.
func (m *MockAuthorizerClient) Authorize(ctx context.Context, in *authorizer.AuthorizeRequest, opts ...grpc.CallOption) (*authorizer.AuthorizeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Authorize", varargs...)
	ret0, _ := ret[0].(*authorizer.AuthorizeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Authorize indicates an expected call of Authorize.
func (mr *MockAuthorizerClientMockRecorder) Authorize(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Authorize", reflect.TypeOf((*MockAuthorizerClient)(nil).Authorize), varargs...)
}

// MockAuthorizerServer is a mock of AuthorizerServer interface.
type MockAuthorizerServer struct {
	ctrl     *gomock.Controller
	recorder *MockAuthorizerServerMockRecorder
}

// MockAuthorizerServerMockRecorder is the mock recorder for MockAuthorizerServer.
type MockAuthorizerServerMockRecorder struct {
	mock *MockAuthorizerServer
}

// NewMockAuthorizerServer creates a new mock instance.
func NewMockAuthorizerServer(ctrl *gomock.Controller) *MockAuthorizerServer {
	mock := &MockAuthorizerServer{ctrl: ctrl}
	mock.recorder = &MockAuthorizerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAuthorizerServer) EXPECT() *MockAuthorizerServerMockRecorder {
	return m.recorder
}

// Authorize mocks base method.
func (m *MockAuthorizerServer) Authorize(arg0 context.Context, arg1 *authorizer.AuthorizeRequest) (*authorizer.AuthorizeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Authorize", arg0, arg1)
	ret0, _ := ret[0].(*authorizer.AuthorizeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Authorize indicates an expected call of Authorize.
func (mr *MockAuthorizerServerMockRecorder) Authorize(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Authorize", reflect.TypeOf((*MockAuthorizerServer)(nil).Authorize), arg0, arg1)
}

// MockUnsafeAuthorizerServer is a mock of UnsafeAuthorizerServer interface.
type MockUnsafeAuthorizerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAuthorizerServerMockRecorder
}

// MockUnsafeAuthorizerServerMockRecorder is the mock recorder for MockUnsafeAuthorizerServer.
type MockUnsafeAuthorizerServerMockRecorder struct {
	mock *MockUnsafeAuthorizerServer
}

// NewMockUnsafeAuthorizerServer creates a new mock instance.
func NewMockUnsafeAuthorizerServer(ctrl *gomock.Controller) *MockUnsafeAuthorizerServer {
	mock := &MockUnsafeAuthorizerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAuthorizerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAuthorizerServer) EXPECT() *MockUnsafeAuthorizerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAuthorizerServer mocks base method.
func (m *MockUnsafeAuthorizerServer) mustEmbedUnimplementedAuthorizerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAuthorizerServer")
}

// mustEmbedUnimplementedAuthorizerServer indicates an expected call of mustEmbedUnimplementedAuthorizerServer.
func (mr *MockUnsafeAuthorizerServerMockRecorder) mustEmbedUnimplementedAuthorizerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAuthorizerServer", reflect.TypeOf((*MockUnsafeAuthorizerServer)(nil).mustEmbedUnimplementedAuthorizerServer))
}
