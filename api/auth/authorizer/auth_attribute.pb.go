// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/authorizer/auth_attribute.proto

package authorizer

import (
	auth "github.com/epifi/gamma/api/auth"
	savings "github.com/epifi/gamma/api/savings"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Enum to identify type of authorization attribute
// Each type defined below is mapped to a value which is used for authorizing FE requests
type AuthAttribute int32

const (
	AuthAttribute_AUTH_ATTRIBUTE_UNSPECIFIED                        AuthAttribute = 0
	AuthAttribute_AUTH_ATTRIBUTE_FEDERAL_DEVICE_REGISTRATION_STATUS AuthAttribute = 1
	AuthAttribute_AUTH_ATTRIBUTE_FEDERAL_SAVINGS_ACCOUNT_STATUS     AuthAttribute = 2
)

// Enum value maps for AuthAttribute.
var (
	AuthAttribute_name = map[int32]string{
		0: "AUTH_ATTRIBUTE_UNSPECIFIED",
		1: "AUTH_ATTRIBUTE_FEDERAL_DEVICE_REGISTRATION_STATUS",
		2: "AUTH_ATTRIBUTE_FEDERAL_SAVINGS_ACCOUNT_STATUS",
	}
	AuthAttribute_value = map[string]int32{
		"AUTH_ATTRIBUTE_UNSPECIFIED":                        0,
		"AUTH_ATTRIBUTE_FEDERAL_DEVICE_REGISTRATION_STATUS": 1,
		"AUTH_ATTRIBUTE_FEDERAL_SAVINGS_ACCOUNT_STATUS":     2,
	}
)

func (x AuthAttribute) Enum() *AuthAttribute {
	p := new(AuthAttribute)
	*p = x
	return p
}

func (x AuthAttribute) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthAttribute) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_authorizer_auth_attribute_proto_enumTypes[0].Descriptor()
}

func (AuthAttribute) Type() protoreflect.EnumType {
	return &file_api_auth_authorizer_auth_attribute_proto_enumTypes[0]
}

func (x AuthAttribute) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthAttribute.Descriptor instead.
func (AuthAttribute) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_authorizer_auth_attribute_proto_rawDescGZIP(), []int{0}
}

// AuthAttributeValue is used to store value of auth attribute api.typesv2. Each value maps to one auth attribute type.
type AuthAttributeValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*AuthAttributeValue_FederalDeviceRegistrationStatus
	//	*AuthAttributeValue_SavingsAccountState
	Value isAuthAttributeValue_Value `protobuf_oneof:"value"`
}

func (x *AuthAttributeValue) Reset() {
	*x = AuthAttributeValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_authorizer_auth_attribute_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthAttributeValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthAttributeValue) ProtoMessage() {}

func (x *AuthAttributeValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_authorizer_auth_attribute_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthAttributeValue.ProtoReflect.Descriptor instead.
func (*AuthAttributeValue) Descriptor() ([]byte, []int) {
	return file_api_auth_authorizer_auth_attribute_proto_rawDescGZIP(), []int{0}
}

func (m *AuthAttributeValue) GetValue() isAuthAttributeValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *AuthAttributeValue) GetFederalDeviceRegistrationStatus() auth.DeviceRegistrationStatus {
	if x, ok := x.GetValue().(*AuthAttributeValue_FederalDeviceRegistrationStatus); ok {
		return x.FederalDeviceRegistrationStatus
	}
	return auth.DeviceRegistrationStatus(0)
}

func (x *AuthAttributeValue) GetSavingsAccountState() savings.State {
	if x, ok := x.GetValue().(*AuthAttributeValue_SavingsAccountState); ok {
		return x.SavingsAccountState
	}
	return savings.State(0)
}

type isAuthAttributeValue_Value interface {
	isAuthAttributeValue_Value()
}

type AuthAttributeValue_FederalDeviceRegistrationStatus struct {
	// Maps to AUTH_ATTRIBUTE_FEDERAL_DEVICE_REGISTRATION_STATUS
	FederalDeviceRegistrationStatus auth.DeviceRegistrationStatus `protobuf:"varint,1,opt,name=federal_device_registration_status,json=federalDeviceRegistrationStatus,proto3,enum=auth.DeviceRegistrationStatus,oneof"`
}

type AuthAttributeValue_SavingsAccountState struct {
	// Maps to AUTH_ATTRIBUTE_FEDERAL_SAVINGS_ACCOUNT_STATUS
	SavingsAccountState savings.State `protobuf:"varint,2,opt,name=savings_account_state,json=savingsAccountState,proto3,enum=savings.State,oneof"`
}

func (*AuthAttributeValue_FederalDeviceRegistrationStatus) isAuthAttributeValue_Value() {}

func (*AuthAttributeValue_SavingsAccountState) isAuthAttributeValue_Value() {}

type AuthAttributeKeyValuePair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enum for type of auth attribute
	AuthAttribute AuthAttribute `protobuf:"varint,1,opt,name=auth_attribute,json=authAttribute,proto3,enum=auth.authorizer.AuthAttribute" json:"auth_attribute,omitempty"`
	// value for the auth attribute type being stored
	AuthAttributeValue *AuthAttributeValue `protobuf:"bytes,2,opt,name=auth_attribute_value,json=authAttributeValue,proto3" json:"auth_attribute_value,omitempty"`
}

func (x *AuthAttributeKeyValuePair) Reset() {
	*x = AuthAttributeKeyValuePair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_authorizer_auth_attribute_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthAttributeKeyValuePair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthAttributeKeyValuePair) ProtoMessage() {}

func (x *AuthAttributeKeyValuePair) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_authorizer_auth_attribute_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthAttributeKeyValuePair.ProtoReflect.Descriptor instead.
func (*AuthAttributeKeyValuePair) Descriptor() ([]byte, []int) {
	return file_api_auth_authorizer_auth_attribute_proto_rawDescGZIP(), []int{1}
}

func (x *AuthAttributeKeyValuePair) GetAuthAttribute() AuthAttribute {
	if x != nil {
		return x.AuthAttribute
	}
	return AuthAttribute_AUTH_ATTRIBUTE_UNSPECIFIED
}

func (x *AuthAttributeKeyValuePair) GetAuthAttributeValue() *AuthAttributeValue {
	if x != nil {
		return x.AuthAttributeValue
	}
	return nil
}

var File_api_auth_authorizer_auth_attribute_proto protoreflect.FileDescriptor

var file_api_auth_authorizer_auth_attribute_proto_rawDesc = []byte{
	0x0a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x69, 0x7a, 0x65, 0x72, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x1a, 0x29, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x76, 0x69,
	0x6e, 0x67, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xd2, 0x01, 0x0a, 0x12, 0x41, 0x75, 0x74, 0x68, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x6d, 0x0a, 0x22, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x1f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x0a, 0x15, 0x73, 0x61, 0x76, 0x69, 0x6e,
	0x67, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x13, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x42, 0x07, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xb9, 0x01, 0x0a, 0x19, 0x41, 0x75, 0x74, 0x68, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x50, 0x61, 0x69, 0x72, 0x12, 0x45, 0x0a, 0x0e, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x61, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x41,
	0x75, 0x74, 0x68, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x0d, 0x61, 0x75,
	0x74, 0x68, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x55, 0x0a, 0x14, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x12,
	0x61, 0x75, 0x74, 0x68, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x2a, 0x99, 0x01, 0x0a, 0x0d, 0x41, 0x75, 0x74, 0x68, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54,
	0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x35, 0x0a, 0x31, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54,
	0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x44,
	0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x01, 0x12, 0x31, 0x0a, 0x2d, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x5f, 0x46, 0x45,
	0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x02, 0x42, 0x58,
	0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x5a, 0x2a, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x61, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_authorizer_auth_attribute_proto_rawDescOnce sync.Once
	file_api_auth_authorizer_auth_attribute_proto_rawDescData = file_api_auth_authorizer_auth_attribute_proto_rawDesc
)

func file_api_auth_authorizer_auth_attribute_proto_rawDescGZIP() []byte {
	file_api_auth_authorizer_auth_attribute_proto_rawDescOnce.Do(func() {
		file_api_auth_authorizer_auth_attribute_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_authorizer_auth_attribute_proto_rawDescData)
	})
	return file_api_auth_authorizer_auth_attribute_proto_rawDescData
}

var file_api_auth_authorizer_auth_attribute_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_authorizer_auth_attribute_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_auth_authorizer_auth_attribute_proto_goTypes = []interface{}{
	(AuthAttribute)(0),                 // 0: auth.authorizer.AuthAttribute
	(*AuthAttributeValue)(nil),         // 1: auth.authorizer.AuthAttributeValue
	(*AuthAttributeKeyValuePair)(nil),  // 2: auth.authorizer.AuthAttributeKeyValuePair
	(auth.DeviceRegistrationStatus)(0), // 3: auth.DeviceRegistrationStatus
	(savings.State)(0),                 // 4: savings.State
}
var file_api_auth_authorizer_auth_attribute_proto_depIdxs = []int32{
	3, // 0: auth.authorizer.AuthAttributeValue.federal_device_registration_status:type_name -> auth.DeviceRegistrationStatus
	4, // 1: auth.authorizer.AuthAttributeValue.savings_account_state:type_name -> savings.State
	0, // 2: auth.authorizer.AuthAttributeKeyValuePair.auth_attribute:type_name -> auth.authorizer.AuthAttribute
	1, // 3: auth.authorizer.AuthAttributeKeyValuePair.auth_attribute_value:type_name -> auth.authorizer.AuthAttributeValue
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_auth_authorizer_auth_attribute_proto_init() }
func file_api_auth_authorizer_auth_attribute_proto_init() {
	if File_api_auth_authorizer_auth_attribute_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_authorizer_auth_attribute_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthAttributeValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_authorizer_auth_attribute_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthAttributeKeyValuePair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_auth_authorizer_auth_attribute_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*AuthAttributeValue_FederalDeviceRegistrationStatus)(nil),
		(*AuthAttributeValue_SavingsAccountState)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_authorizer_auth_attribute_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_authorizer_auth_attribute_proto_goTypes,
		DependencyIndexes: file_api_auth_authorizer_auth_attribute_proto_depIdxs,
		EnumInfos:         file_api_auth_authorizer_auth_attribute_proto_enumTypes,
		MessageInfos:      file_api_auth_authorizer_auth_attribute_proto_msgTypes,
	}.Build()
	File_api_auth_authorizer_auth_attribute_proto = out.File
	file_api_auth_authorizer_auth_attribute_proto_rawDesc = nil
	file_api_auth_authorizer_auth_attribute_proto_goTypes = nil
	file_api_auth_authorizer_auth_attribute_proto_depIdxs = nil
}
