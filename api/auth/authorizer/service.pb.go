// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/authorizer/service.proto

package authorizer

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	auth "github.com/epifi/gamma/api/auth"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthorizeResponse_Status int32

const (
	AuthorizeResponse_OK            AuthorizeResponse_Status = 0
	AuthorizeResponse_TOKEN_INVALID AuthorizeResponse_Status = 100
	// Input token is expired
	AuthorizeResponse_TOKEN_EXPIRY AuthorizeResponse_Status = 101
	// Device Id mismatch
	AuthorizeResponse_DEVICE_ID_MISMATCH AuthorizeResponse_Status = 102
)

// Enum value maps for AuthorizeResponse_Status.
var (
	AuthorizeResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "TOKEN_INVALID",
		101: "TOKEN_EXPIRY",
		102: "DEVICE_ID_MISMATCH",
	}
	AuthorizeResponse_Status_value = map[string]int32{
		"OK":                 0,
		"TOKEN_INVALID":      100,
		"TOKEN_EXPIRY":       101,
		"DEVICE_ID_MISMATCH": 102,
	}
)

func (x AuthorizeResponse_Status) Enum() *AuthorizeResponse_Status {
	p := new(AuthorizeResponse_Status)
	*p = x
	return p
}

func (x AuthorizeResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthorizeResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_authorizer_service_proto_enumTypes[0].Descriptor()
}

func (AuthorizeResponse_Status) Type() protoreflect.EnumType {
	return &file_api_auth_authorizer_service_proto_enumTypes[0]
}

func (x AuthorizeResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthorizeResponse_Status.Descriptor instead.
func (AuthorizeResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_authorizer_service_proto_rawDescGZIP(), []int{1, 0}
}

type AuthorizeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Auth method options set for the RPC
	AuthOptions *rpc.AuthOptions `protobuf:"bytes,1,opt,name=auth_options,json=authOptions,proto3" json:"auth_options,omitempty"`
	// GRPC request of the FE request under authorization
	RpcRequest *anypb.Any `protobuf:"bytes,2,opt,name=rpc_request,json=rpcRequest,proto3" json:"rpc_request,omitempty"`
	// Represents token which we want to authenticate and authorize
	Token string `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	// Type of the token that is to be created
	TokenType auth.TokenType `protobuf:"varint,4,opt,name=token_type,json=tokenType,proto3,enum=auth.TokenType" json:"token_type,omitempty"`
	// Mobile Device or Browser Information used by user. Optional.
	Device *common.Device `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`
}

func (x *AuthorizeRequest) Reset() {
	*x = AuthorizeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_authorizer_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthorizeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthorizeRequest) ProtoMessage() {}

func (x *AuthorizeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_authorizer_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthorizeRequest.ProtoReflect.Descriptor instead.
func (*AuthorizeRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_authorizer_service_proto_rawDescGZIP(), []int{0}
}

func (x *AuthorizeRequest) GetAuthOptions() *rpc.AuthOptions {
	if x != nil {
		return x.AuthOptions
	}
	return nil
}

func (x *AuthorizeRequest) GetRpcRequest() *anypb.Any {
	if x != nil {
		return x.RpcRequest
	}
	return nil
}

func (x *AuthorizeRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *AuthorizeRequest) GetTokenType() auth.TokenType {
	if x != nil {
		return x.TokenType
	}
	return auth.TokenType(0)
}

func (x *AuthorizeRequest) GetDevice() *common.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type AuthorizeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Deeplink to be used if access is denied
	Deeplink     *deeplink.Deeplink `protobuf:"bytes,2,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	TokenDetails *auth.TokenDetails `protobuf:"bytes,3,opt,name=token_details,json=tokenDetails,proto3" json:"token_details,omitempty"`
}

func (x *AuthorizeResponse) Reset() {
	*x = AuthorizeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_authorizer_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthorizeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthorizeResponse) ProtoMessage() {}

func (x *AuthorizeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_authorizer_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthorizeResponse.ProtoReflect.Descriptor instead.
func (*AuthorizeResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_authorizer_service_proto_rawDescGZIP(), []int{1}
}

func (x *AuthorizeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AuthorizeResponse) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *AuthorizeResponse) GetTokenDetails() *auth.TokenDetails {
	if x != nil {
		return x.TokenDetails
	}
	return nil
}

var File_api_auth_authorizer_service_proto protoreflect.FileDescriptor

var file_api_auth_authorizer_service_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x69, 0x7a, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x69, 0x7a, 0x65, 0x72, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf8, 0x01, 0x0a, 0x10, 0x41, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x0c,
	0x61, 0x75, 0x74, 0x68, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x35, 0x0a, 0x0b, 0x72, 0x70, 0x63, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x0a, 0x72, 0x70,
	0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2e,
	0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32,
	0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x22, 0xf9, 0x01, 0x0a, 0x11, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a,
	0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x37, 0x0a, 0x0d, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22,
	0x4d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x10, 0x64, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x45, 0x58,
	0x50, 0x49, 0x52, 0x59, 0x10, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45,
	0x5f, 0x49, 0x44, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x66, 0x32, 0x60,
	0x0a, 0x0a, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x09,
	0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x21, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x41,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x5a, 0x2a,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_auth_authorizer_service_proto_rawDescOnce sync.Once
	file_api_auth_authorizer_service_proto_rawDescData = file_api_auth_authorizer_service_proto_rawDesc
)

func file_api_auth_authorizer_service_proto_rawDescGZIP() []byte {
	file_api_auth_authorizer_service_proto_rawDescOnce.Do(func() {
		file_api_auth_authorizer_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_authorizer_service_proto_rawDescData)
	})
	return file_api_auth_authorizer_service_proto_rawDescData
}

var file_api_auth_authorizer_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_authorizer_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_auth_authorizer_service_proto_goTypes = []interface{}{
	(AuthorizeResponse_Status)(0), // 0: auth.authorizer.AuthorizeResponse.Status
	(*AuthorizeRequest)(nil),      // 1: auth.authorizer.AuthorizeRequest
	(*AuthorizeResponse)(nil),     // 2: auth.authorizer.AuthorizeResponse
	(*rpc.AuthOptions)(nil),       // 3: rpc.AuthOptions
	(*anypb.Any)(nil),             // 4: google.protobuf.Any
	(auth.TokenType)(0),           // 5: auth.TokenType
	(*common.Device)(nil),         // 6: api.typesv2.common.Device
	(*rpc.Status)(nil),            // 7: rpc.Status
	(*deeplink.Deeplink)(nil),     // 8: frontend.deeplink.Deeplink
	(*auth.TokenDetails)(nil),     // 9: auth.TokenDetails
}
var file_api_auth_authorizer_service_proto_depIdxs = []int32{
	3, // 0: auth.authorizer.AuthorizeRequest.auth_options:type_name -> rpc.AuthOptions
	4, // 1: auth.authorizer.AuthorizeRequest.rpc_request:type_name -> google.protobuf.Any
	5, // 2: auth.authorizer.AuthorizeRequest.token_type:type_name -> auth.TokenType
	6, // 3: auth.authorizer.AuthorizeRequest.device:type_name -> api.typesv2.common.Device
	7, // 4: auth.authorizer.AuthorizeResponse.status:type_name -> rpc.Status
	8, // 5: auth.authorizer.AuthorizeResponse.deeplink:type_name -> frontend.deeplink.Deeplink
	9, // 6: auth.authorizer.AuthorizeResponse.token_details:type_name -> auth.TokenDetails
	1, // 7: auth.authorizer.Authorizer.Authorize:input_type -> auth.authorizer.AuthorizeRequest
	2, // 8: auth.authorizer.Authorizer.Authorize:output_type -> auth.authorizer.AuthorizeResponse
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_auth_authorizer_service_proto_init() }
func file_api_auth_authorizer_service_proto_init() {
	if File_api_auth_authorizer_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_authorizer_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthorizeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_authorizer_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthorizeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_authorizer_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_auth_authorizer_service_proto_goTypes,
		DependencyIndexes: file_api_auth_authorizer_service_proto_depIdxs,
		EnumInfos:         file_api_auth_authorizer_service_proto_enumTypes,
		MessageInfos:      file_api_auth_authorizer_service_proto_msgTypes,
	}.Build()
	File_api_auth_authorizer_service_proto = out.File
	file_api_auth_authorizer_service_proto_rawDesc = nil
	file_api_auth_authorizer_service_proto_goTypes = nil
	file_api_auth_authorizer_service_proto_depIdxs = nil
}
