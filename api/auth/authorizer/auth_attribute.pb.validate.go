// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/authorizer/auth_attribute.proto

package authorizer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	auth "github.com/epifi/gamma/api/auth"

	savings "github.com/epifi/gamma/api/savings"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = auth.DeviceRegistrationStatus(0)

	_ = savings.State(0)
)

// Validate checks the field values on AuthAttributeValue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthAttributeValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthAttributeValue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthAttributeValueMultiError, or nil if none found.
func (m *AuthAttributeValue) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthAttributeValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Value.(type) {
	case *AuthAttributeValue_FederalDeviceRegistrationStatus:
		if v == nil {
			err := AuthAttributeValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for FederalDeviceRegistrationStatus
	case *AuthAttributeValue_SavingsAccountState:
		if v == nil {
			err := AuthAttributeValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for SavingsAccountState
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AuthAttributeValueMultiError(errors)
	}

	return nil
}

// AuthAttributeValueMultiError is an error wrapping multiple validation errors
// returned by AuthAttributeValue.ValidateAll() if the designated constraints
// aren't met.
type AuthAttributeValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthAttributeValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthAttributeValueMultiError) AllErrors() []error { return m }

// AuthAttributeValueValidationError is the validation error returned by
// AuthAttributeValue.Validate if the designated constraints aren't met.
type AuthAttributeValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthAttributeValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthAttributeValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthAttributeValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthAttributeValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthAttributeValueValidationError) ErrorName() string {
	return "AuthAttributeValueValidationError"
}

// Error satisfies the builtin error interface
func (e AuthAttributeValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthAttributeValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthAttributeValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthAttributeValueValidationError{}

// Validate checks the field values on AuthAttributeKeyValuePair with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthAttributeKeyValuePair) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthAttributeKeyValuePair with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthAttributeKeyValuePairMultiError, or nil if none found.
func (m *AuthAttributeKeyValuePair) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthAttributeKeyValuePair) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AuthAttribute

	if all {
		switch v := interface{}(m.GetAuthAttributeValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthAttributeKeyValuePairValidationError{
					field:  "AuthAttributeValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthAttributeKeyValuePairValidationError{
					field:  "AuthAttributeValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthAttributeValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthAttributeKeyValuePairValidationError{
				field:  "AuthAttributeValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthAttributeKeyValuePairMultiError(errors)
	}

	return nil
}

// AuthAttributeKeyValuePairMultiError is an error wrapping multiple validation
// errors returned by AuthAttributeKeyValuePair.ValidateAll() if the
// designated constraints aren't met.
type AuthAttributeKeyValuePairMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthAttributeKeyValuePairMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthAttributeKeyValuePairMultiError) AllErrors() []error { return m }

// AuthAttributeKeyValuePairValidationError is the validation error returned by
// AuthAttributeKeyValuePair.Validate if the designated constraints aren't met.
type AuthAttributeKeyValuePairValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthAttributeKeyValuePairValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthAttributeKeyValuePairValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthAttributeKeyValuePairValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthAttributeKeyValuePairValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthAttributeKeyValuePairValidationError) ErrorName() string {
	return "AuthAttributeKeyValuePairValidationError"
}

// Error satisfies the builtin error interface
func (e AuthAttributeKeyValuePairValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthAttributeKeyValuePair.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthAttributeKeyValuePairValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthAttributeKeyValuePairValidationError{}
