package auth

import (
	"database/sql/driver"
	"fmt"
	"log"
)

// Valuer interface implementation for storing the data in string format in DB
func (p NonceStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (p *NonceStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := NonceStatus_value[val]
	if !ok {
		log.Printf("encountered unknown NonceStatus : %v", val)
	}
	*p = NonceStatus(valInt)
	return nil
}
