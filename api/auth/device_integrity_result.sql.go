package auth

import (
	"database/sql/driver"
	"fmt"
)

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x Result) Value() (driver.Value, error) {
	return x.String(), nil
}

// <PERSON>an implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *Result) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := Result_value[val]
	*x = Result(valInt)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x FailureReason) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *FailureReason) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := FailureReason_value[val]
	*x = FailureReason(valInt)
	return nil
}
