package auth

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Valuer interface implementation for storing the data in string format in DB
func (x *DeviceRegistrationAttempt_SmsInfo) Value() (driver.Value, error) {
	if x == nil {
		return nil, nil
	}
	return protojson.Marshal(x)
}

// Scanner interface implementation for parsing data while reading from DB
func (x *DeviceRegistrationAttempt_SmsInfo) Scan(input interface{}) error {
	val, ok := input.([]byte)
	if !ok {
		err := fmt.Errorf("expected []byte in device reg attempt sms info, got %T", input)
		return err
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, val, x)
}
