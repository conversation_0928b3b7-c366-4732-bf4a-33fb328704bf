//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/biometrics/consumer/consumer.proto

package biometrics

import (
	queue "github.com/epifi/be-common/api/queue"
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessBiometricEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	ActorId       string                       `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// biometric identifier received from app
	BiometricId string `protobuf:"bytes,3,opt,name=biometric_id,json=biometricId,proto3" json:"biometric_id,omitempty"`
	// app platform IOS or Android
	AppPlatform common.Platform `protobuf:"varint,4,opt,name=app_platform,json=appPlatform,proto3,enum=api.typesv2.common.Platform" json:"app_platform,omitempty"`
	// registered customer device ID
	DeviceId string `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// app version of the user
	AppVersion int64 `protobuf:"varint,6,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
}

func (x *ProcessBiometricEventRequest) Reset() {
	*x = ProcessBiometricEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_biometrics_consumer_consumer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessBiometricEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessBiometricEventRequest) ProtoMessage() {}

func (x *ProcessBiometricEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_biometrics_consumer_consumer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessBiometricEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessBiometricEventRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_biometrics_consumer_consumer_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessBiometricEventRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessBiometricEventRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ProcessBiometricEventRequest) GetBiometricId() string {
	if x != nil {
		return x.BiometricId
	}
	return ""
}

func (x *ProcessBiometricEventRequest) GetAppPlatform() common.Platform {
	if x != nil {
		return x.AppPlatform
	}
	return common.Platform(0)
}

func (x *ProcessBiometricEventRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *ProcessBiometricEventRequest) GetAppVersion() int64 {
	if x != nil {
		return x.AppVersion
	}
	return 0
}

type ProcessBiometricEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessBiometricEventResponse) Reset() {
	*x = ProcessBiometricEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_biometrics_consumer_consumer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessBiometricEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessBiometricEventResponse) ProtoMessage() {}

func (x *ProcessBiometricEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_biometrics_consumer_consumer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessBiometricEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessBiometricEventResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_biometrics_consumer_consumer_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessBiometricEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_auth_biometrics_consumer_consumer_proto protoreflect.FileDescriptor

var file_api_auth_biometrics_consumer_consumer_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x62, 0x69, 0x6f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2f, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x1a, 0x20,
	0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xa0, 0x02, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x42, 0x69, 0x6f,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x67, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x42,
	0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0x82, 0x01,
	0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x12, 0x76, 0x0a, 0x15, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x12, 0x2d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x42, 0x69, 0x6f,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x42, 0x69, 0x6f, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74,
	0x68, 0x2f, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_biometrics_consumer_consumer_proto_rawDescOnce sync.Once
	file_api_auth_biometrics_consumer_consumer_proto_rawDescData = file_api_auth_biometrics_consumer_consumer_proto_rawDesc
)

func file_api_auth_biometrics_consumer_consumer_proto_rawDescGZIP() []byte {
	file_api_auth_biometrics_consumer_consumer_proto_rawDescOnce.Do(func() {
		file_api_auth_biometrics_consumer_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_biometrics_consumer_consumer_proto_rawDescData)
	})
	return file_api_auth_biometrics_consumer_consumer_proto_rawDescData
}

var file_api_auth_biometrics_consumer_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_auth_biometrics_consumer_consumer_proto_goTypes = []interface{}{
	(*ProcessBiometricEventRequest)(nil),  // 0: auth.biometrics.ProcessBiometricEventRequest
	(*ProcessBiometricEventResponse)(nil), // 1: auth.biometrics.ProcessBiometricEventResponse
	(*queue.ConsumerRequestHeader)(nil),   // 2: queue.ConsumerRequestHeader
	(common.Platform)(0),                  // 3: api.typesv2.common.Platform
	(*queue.ConsumerResponseHeader)(nil),  // 4: queue.ConsumerResponseHeader
}
var file_api_auth_biometrics_consumer_consumer_proto_depIdxs = []int32{
	2, // 0: auth.biometrics.ProcessBiometricEventRequest.request_header:type_name -> queue.ConsumerRequestHeader
	3, // 1: auth.biometrics.ProcessBiometricEventRequest.app_platform:type_name -> api.typesv2.common.Platform
	4, // 2: auth.biometrics.ProcessBiometricEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	0, // 3: auth.biometrics.Consumer.ProcessBiometricEvent:input_type -> auth.biometrics.ProcessBiometricEventRequest
	1, // 4: auth.biometrics.Consumer.ProcessBiometricEvent:output_type -> auth.biometrics.ProcessBiometricEventResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_auth_biometrics_consumer_consumer_proto_init() }
func file_api_auth_biometrics_consumer_consumer_proto_init() {
	if File_api_auth_biometrics_consumer_consumer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_biometrics_consumer_consumer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessBiometricEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_biometrics_consumer_consumer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessBiometricEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_biometrics_consumer_consumer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_auth_biometrics_consumer_consumer_proto_goTypes,
		DependencyIndexes: file_api_auth_biometrics_consumer_consumer_proto_depIdxs,
		MessageInfos:      file_api_auth_biometrics_consumer_consumer_proto_msgTypes,
	}.Build()
	File_api_auth_biometrics_consumer_consumer_proto = out.File
	file_api_auth_biometrics_consumer_consumer_proto_rawDesc = nil
	file_api_auth_biometrics_consumer_consumer_proto_goTypes = nil
	file_api_auth_biometrics_consumer_consumer_proto_depIdxs = nil
}
