// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/auth/biometrics
package biometrics

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessBiometricEventMethod = "ProcessBiometricEvent"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &ProcessBiometricEventRequest{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessBiometricEventRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterProcessBiometricEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessBiometricEventMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessBiometricEventMethod)
}
