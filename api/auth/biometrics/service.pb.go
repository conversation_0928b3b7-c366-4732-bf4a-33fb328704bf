// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/biometrics/service.proto

package biometrics

import (
	rpc "github.com/epifi/be-common/api/rpc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetBiometricsDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetBiometricsDetailsRequest) Reset() {
	*x = GetBiometricsDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_biometrics_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBiometricsDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBiometricsDetailsRequest) ProtoMessage() {}

func (x *GetBiometricsDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_biometrics_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBiometricsDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetBiometricsDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_biometrics_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetBiometricsDetailsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetBiometricsDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          *rpc.Status     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	BiometricStatus BiometricStatus `protobuf:"varint,2,opt,name=biometric_status,json=biometricStatus,proto3,enum=auth.biometrics.BiometricStatus" json:"biometric_status,omitempty"`
	BiometricInfo   *BiometricInfo  `protobuf:"bytes,3,opt,name=biometric_info,json=biometricInfo,proto3" json:"biometric_info,omitempty"`
	// timestamp at which biometric was validated
	VerifiedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=verified_at,json=verifiedAt,proto3" json:"verified_at,omitempty"`
	// reason for updating the status
	SubStatus SubStatus `protobuf:"varint,5,opt,name=sub_status,json=subStatus,proto3,enum=auth.biometrics.SubStatus" json:"sub_status,omitempty"`
	// timestamp at which any column was last updated
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// biometric table id
	BiometricTableId string `protobuf:"bytes,7,opt,name=biometric_table_id,json=biometricTableId,proto3" json:"biometric_table_id,omitempty"`
}

func (x *GetBiometricsDetailsResponse) Reset() {
	*x = GetBiometricsDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_biometrics_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBiometricsDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBiometricsDetailsResponse) ProtoMessage() {}

func (x *GetBiometricsDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_biometrics_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBiometricsDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetBiometricsDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_biometrics_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetBiometricsDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetBiometricsDetailsResponse) GetBiometricStatus() BiometricStatus {
	if x != nil {
		return x.BiometricStatus
	}
	return BiometricStatus_BIOMETRIC_STATUS_UNSPECIFIED
}

func (x *GetBiometricsDetailsResponse) GetBiometricInfo() *BiometricInfo {
	if x != nil {
		return x.BiometricInfo
	}
	return nil
}

func (x *GetBiometricsDetailsResponse) GetVerifiedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.VerifiedAt
	}
	return nil
}

func (x *GetBiometricsDetailsResponse) GetSubStatus() SubStatus {
	if x != nil {
		return x.SubStatus
	}
	return SubStatus_SUB_STATUS_UNSPECIFIED
}

func (x *GetBiometricsDetailsResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *GetBiometricsDetailsResponse) GetBiometricTableId() string {
	if x != nil {
		return x.BiometricTableId
	}
	return ""
}

type UpdateBiometricsDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId                string          `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	UpdatedBiometricStatus BiometricStatus `protobuf:"varint,2,opt,name=updated_biometric_status,json=updatedBiometricStatus,proto3,enum=auth.biometrics.BiometricStatus" json:"updated_biometric_status,omitempty"`
	// reason for updating the status
	SubStatus SubStatus `protobuf:"varint,3,opt,name=sub_status,json=subStatus,proto3,enum=auth.biometrics.SubStatus" json:"sub_status,omitempty"`
}

func (x *UpdateBiometricsDetailsRequest) Reset() {
	*x = UpdateBiometricsDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_biometrics_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBiometricsDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBiometricsDetailsRequest) ProtoMessage() {}

func (x *UpdateBiometricsDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_biometrics_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBiometricsDetailsRequest.ProtoReflect.Descriptor instead.
func (*UpdateBiometricsDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_biometrics_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateBiometricsDetailsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UpdateBiometricsDetailsRequest) GetUpdatedBiometricStatus() BiometricStatus {
	if x != nil {
		return x.UpdatedBiometricStatus
	}
	return BiometricStatus_BIOMETRIC_STATUS_UNSPECIFIED
}

func (x *UpdateBiometricsDetailsRequest) GetSubStatus() SubStatus {
	if x != nil {
		return x.SubStatus
	}
	return SubStatus_SUB_STATUS_UNSPECIFIED
}

type UpdateBiometricsDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateBiometricsDetailsResponse) Reset() {
	*x = UpdateBiometricsDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_biometrics_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBiometricsDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBiometricsDetailsResponse) ProtoMessage() {}

func (x *UpdateBiometricsDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_biometrics_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBiometricsDetailsResponse.ProtoReflect.Descriptor instead.
func (*UpdateBiometricsDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_biometrics_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateBiometricsDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type SyncBiometricIdentifierRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId       string         `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	BiometricInfo *BiometricInfo `protobuf:"bytes,2,opt,name=biometric_info,json=biometricInfo,proto3" json:"biometric_info,omitempty"`
}

func (x *SyncBiometricIdentifierRequest) Reset() {
	*x = SyncBiometricIdentifierRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_biometrics_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncBiometricIdentifierRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncBiometricIdentifierRequest) ProtoMessage() {}

func (x *SyncBiometricIdentifierRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_biometrics_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncBiometricIdentifierRequest.ProtoReflect.Descriptor instead.
func (*SyncBiometricIdentifierRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_biometrics_service_proto_rawDescGZIP(), []int{4}
}

func (x *SyncBiometricIdentifierRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *SyncBiometricIdentifierRequest) GetBiometricInfo() *BiometricInfo {
	if x != nil {
		return x.BiometricInfo
	}
	return nil
}

type SyncBiometricIdentifierResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SyncBiometricIdentifierResponse) Reset() {
	*x = SyncBiometricIdentifierResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_biometrics_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncBiometricIdentifierResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncBiometricIdentifierResponse) ProtoMessage() {}

func (x *SyncBiometricIdentifierResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_biometrics_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncBiometricIdentifierResponse.ProtoReflect.Descriptor instead.
func (*SyncBiometricIdentifierResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_biometrics_service_proto_rawDescGZIP(), []int{5}
}

func (x *SyncBiometricIdentifierResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_api_auth_biometrics_service_proto protoreflect.FileDescriptor

var file_api_auth_biometrics_service_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x62, 0x69, 0x6f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x62,
	0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x2f, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x38, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x22, 0xb8, 0x03, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6f, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x10, 0x62, 0x69,
	0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x45, 0x0a, 0x0e, 0x62, 0x69, 0x6f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x2e, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0d, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3b,
	0x0a, 0x0b, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0a, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x73,
	0x75, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1a, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x2e, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x73, 0x75, 0x62,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x62,
	0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x64, 0x22,
	0xd2, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x5a, 0x0a,
	0x18, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x20, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x2e, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x16, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x75, 0x62,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e,
	0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x73, 0x75, 0x62, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x46, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x69,
	0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x82, 0x01, 0x0a,
	0x1e, 0x53, 0x79, 0x6e, 0x63, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0e, 0x62, 0x69,
	0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x2e, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0d, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x46, 0x0a, 0x1f, 0x53, 0x79, 0x6e, 0x63, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0x84, 0x03, 0x0a, 0x11, 0x42, 0x69,
	0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x73, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62,
	0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6f,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x69,
	0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x2f, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x30, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x7c, 0x0a, 0x17, 0x53, 0x79, 0x6e, 0x63, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x2f, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e,
	0x53, 0x79, 0x6e, 0x63, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x2e, 0x53, 0x79, 0x6e, 0x63, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5a, 0x2a,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f,
	0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_auth_biometrics_service_proto_rawDescOnce sync.Once
	file_api_auth_biometrics_service_proto_rawDescData = file_api_auth_biometrics_service_proto_rawDesc
)

func file_api_auth_biometrics_service_proto_rawDescGZIP() []byte {
	file_api_auth_biometrics_service_proto_rawDescOnce.Do(func() {
		file_api_auth_biometrics_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_biometrics_service_proto_rawDescData)
	})
	return file_api_auth_biometrics_service_proto_rawDescData
}

var file_api_auth_biometrics_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_auth_biometrics_service_proto_goTypes = []interface{}{
	(*GetBiometricsDetailsRequest)(nil),     // 0: auth.biometrics.GetBiometricsDetailsRequest
	(*GetBiometricsDetailsResponse)(nil),    // 1: auth.biometrics.GetBiometricsDetailsResponse
	(*UpdateBiometricsDetailsRequest)(nil),  // 2: auth.biometrics.UpdateBiometricsDetailsRequest
	(*UpdateBiometricsDetailsResponse)(nil), // 3: auth.biometrics.UpdateBiometricsDetailsResponse
	(*SyncBiometricIdentifierRequest)(nil),  // 4: auth.biometrics.SyncBiometricIdentifierRequest
	(*SyncBiometricIdentifierResponse)(nil), // 5: auth.biometrics.SyncBiometricIdentifierResponse
	(*rpc.Status)(nil),                      // 6: rpc.Status
	(BiometricStatus)(0),                    // 7: auth.biometrics.BiometricStatus
	(*BiometricInfo)(nil),                   // 8: auth.biometrics.BiometricInfo
	(*timestamppb.Timestamp)(nil),           // 9: google.protobuf.Timestamp
	(SubStatus)(0),                          // 10: auth.biometrics.SubStatus
}
var file_api_auth_biometrics_service_proto_depIdxs = []int32{
	6,  // 0: auth.biometrics.GetBiometricsDetailsResponse.status:type_name -> rpc.Status
	7,  // 1: auth.biometrics.GetBiometricsDetailsResponse.biometric_status:type_name -> auth.biometrics.BiometricStatus
	8,  // 2: auth.biometrics.GetBiometricsDetailsResponse.biometric_info:type_name -> auth.biometrics.BiometricInfo
	9,  // 3: auth.biometrics.GetBiometricsDetailsResponse.verified_at:type_name -> google.protobuf.Timestamp
	10, // 4: auth.biometrics.GetBiometricsDetailsResponse.sub_status:type_name -> auth.biometrics.SubStatus
	9,  // 5: auth.biometrics.GetBiometricsDetailsResponse.updated_at:type_name -> google.protobuf.Timestamp
	7,  // 6: auth.biometrics.UpdateBiometricsDetailsRequest.updated_biometric_status:type_name -> auth.biometrics.BiometricStatus
	10, // 7: auth.biometrics.UpdateBiometricsDetailsRequest.sub_status:type_name -> auth.biometrics.SubStatus
	6,  // 8: auth.biometrics.UpdateBiometricsDetailsResponse.status:type_name -> rpc.Status
	8,  // 9: auth.biometrics.SyncBiometricIdentifierRequest.biometric_info:type_name -> auth.biometrics.BiometricInfo
	6,  // 10: auth.biometrics.SyncBiometricIdentifierResponse.status:type_name -> rpc.Status
	0,  // 11: auth.biometrics.BiometricsService.GetBiometricsDetails:input_type -> auth.biometrics.GetBiometricsDetailsRequest
	2,  // 12: auth.biometrics.BiometricsService.UpdateBiometricsDetails:input_type -> auth.biometrics.UpdateBiometricsDetailsRequest
	4,  // 13: auth.biometrics.BiometricsService.SyncBiometricIdentifier:input_type -> auth.biometrics.SyncBiometricIdentifierRequest
	1,  // 14: auth.biometrics.BiometricsService.GetBiometricsDetails:output_type -> auth.biometrics.GetBiometricsDetailsResponse
	3,  // 15: auth.biometrics.BiometricsService.UpdateBiometricsDetails:output_type -> auth.biometrics.UpdateBiometricsDetailsResponse
	5,  // 16: auth.biometrics.BiometricsService.SyncBiometricIdentifier:output_type -> auth.biometrics.SyncBiometricIdentifierResponse
	14, // [14:17] is the sub-list for method output_type
	11, // [11:14] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_api_auth_biometrics_service_proto_init() }
func file_api_auth_biometrics_service_proto_init() {
	if File_api_auth_biometrics_service_proto != nil {
		return
	}
	file_api_auth_biometrics_internal_biometrics_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_auth_biometrics_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBiometricsDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_biometrics_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBiometricsDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_biometrics_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBiometricsDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_biometrics_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBiometricsDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_biometrics_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncBiometricIdentifierRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_biometrics_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncBiometricIdentifierResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_biometrics_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_auth_biometrics_service_proto_goTypes,
		DependencyIndexes: file_api_auth_biometrics_service_proto_depIdxs,
		MessageInfos:      file_api_auth_biometrics_service_proto_msgTypes,
	}.Build()
	File_api_auth_biometrics_service_proto = out.File
	file_api_auth_biometrics_service_proto_rawDesc = nil
	file_api_auth_biometrics_service_proto_goTypes = nil
	file_api_auth_biometrics_service_proto_depIdxs = nil
}
