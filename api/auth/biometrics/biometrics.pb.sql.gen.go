// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/auth/biometrics/biometrics.pb.go

package biometrics

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"google.golang.org/protobuf/encoding/protojson"
)

// Valuer interface implementation for storing the SubStatus in string format in DB
func (p SubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing SubStatus while reading from DB
func (p *SubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := SubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected SubStatus value: %s", val)
	}
	*p = SubStatus(valInt)
	return nil
}

// Marshaler interface implementation for SubStatus
func (x SubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for SubStatus
func (x *SubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = SubStatus(SubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the BiometricStatus in string format in DB
func (p BiometricStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing BiometricStatus while reading from DB
func (p *BiometricStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := BiometricStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected BiometricStatus value: %s", val)
	}
	*p = BiometricStatus(valInt)
	return nil
}

// Marshaler interface implementation for BiometricStatus
func (x BiometricStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for BiometricStatus
func (x *BiometricStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = BiometricStatus(BiometricStatus_value[val])
	return nil
}

// Scanner interface implementation for parsing BiometricInfo while reading from DB
func (a *BiometricInfo) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the BiometricInfo in string format in DB
func (a *BiometricInfo) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for BiometricInfo
func (a *BiometricInfo) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for BiometricInfo
func (a *BiometricInfo) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
