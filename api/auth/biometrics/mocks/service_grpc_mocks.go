// Code generated by MockGen. DO NOT EDIT.
// Source: api/auth/biometrics/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	biometrics "github.com/epifi/gamma/api/auth/biometrics"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockBiometricsServiceClient is a mock of BiometricsServiceClient interface.
type MockBiometricsServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockBiometricsServiceClientMockRecorder
}

// MockBiometricsServiceClientMockRecorder is the mock recorder for MockBiometricsServiceClient.
type MockBiometricsServiceClientMockRecorder struct {
	mock *MockBiometricsServiceClient
}

// NewMockBiometricsServiceClient creates a new mock instance.
func NewMockBiometricsServiceClient(ctrl *gomock.Controller) *MockBiometricsServiceClient {
	mock := &MockBiometricsServiceClient{ctrl: ctrl}
	mock.recorder = &MockBiometricsServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBiometricsServiceClient) EXPECT() *MockBiometricsServiceClientMockRecorder {
	return m.recorder
}

// GetBiometricsDetails mocks base method.
func (m *MockBiometricsServiceClient) GetBiometricsDetails(ctx context.Context, in *biometrics.GetBiometricsDetailsRequest, opts ...grpc.CallOption) (*biometrics.GetBiometricsDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBiometricsDetails", varargs...)
	ret0, _ := ret[0].(*biometrics.GetBiometricsDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBiometricsDetails indicates an expected call of GetBiometricsDetails.
func (mr *MockBiometricsServiceClientMockRecorder) GetBiometricsDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBiometricsDetails", reflect.TypeOf((*MockBiometricsServiceClient)(nil).GetBiometricsDetails), varargs...)
}

// SyncBiometricIdentifier mocks base method.
func (m *MockBiometricsServiceClient) SyncBiometricIdentifier(ctx context.Context, in *biometrics.SyncBiometricIdentifierRequest, opts ...grpc.CallOption) (*biometrics.SyncBiometricIdentifierResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SyncBiometricIdentifier", varargs...)
	ret0, _ := ret[0].(*biometrics.SyncBiometricIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncBiometricIdentifier indicates an expected call of SyncBiometricIdentifier.
func (mr *MockBiometricsServiceClientMockRecorder) SyncBiometricIdentifier(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncBiometricIdentifier", reflect.TypeOf((*MockBiometricsServiceClient)(nil).SyncBiometricIdentifier), varargs...)
}

// UpdateBiometricsDetails mocks base method.
func (m *MockBiometricsServiceClient) UpdateBiometricsDetails(ctx context.Context, in *biometrics.UpdateBiometricsDetailsRequest, opts ...grpc.CallOption) (*biometrics.UpdateBiometricsDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateBiometricsDetails", varargs...)
	ret0, _ := ret[0].(*biometrics.UpdateBiometricsDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBiometricsDetails indicates an expected call of UpdateBiometricsDetails.
func (mr *MockBiometricsServiceClientMockRecorder) UpdateBiometricsDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBiometricsDetails", reflect.TypeOf((*MockBiometricsServiceClient)(nil).UpdateBiometricsDetails), varargs...)
}

// MockBiometricsServiceServer is a mock of BiometricsServiceServer interface.
type MockBiometricsServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockBiometricsServiceServerMockRecorder
}

// MockBiometricsServiceServerMockRecorder is the mock recorder for MockBiometricsServiceServer.
type MockBiometricsServiceServerMockRecorder struct {
	mock *MockBiometricsServiceServer
}

// NewMockBiometricsServiceServer creates a new mock instance.
func NewMockBiometricsServiceServer(ctrl *gomock.Controller) *MockBiometricsServiceServer {
	mock := &MockBiometricsServiceServer{ctrl: ctrl}
	mock.recorder = &MockBiometricsServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBiometricsServiceServer) EXPECT() *MockBiometricsServiceServerMockRecorder {
	return m.recorder
}

// GetBiometricsDetails mocks base method.
func (m *MockBiometricsServiceServer) GetBiometricsDetails(arg0 context.Context, arg1 *biometrics.GetBiometricsDetailsRequest) (*biometrics.GetBiometricsDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBiometricsDetails", arg0, arg1)
	ret0, _ := ret[0].(*biometrics.GetBiometricsDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBiometricsDetails indicates an expected call of GetBiometricsDetails.
func (mr *MockBiometricsServiceServerMockRecorder) GetBiometricsDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBiometricsDetails", reflect.TypeOf((*MockBiometricsServiceServer)(nil).GetBiometricsDetails), arg0, arg1)
}

// SyncBiometricIdentifier mocks base method.
func (m *MockBiometricsServiceServer) SyncBiometricIdentifier(arg0 context.Context, arg1 *biometrics.SyncBiometricIdentifierRequest) (*biometrics.SyncBiometricIdentifierResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncBiometricIdentifier", arg0, arg1)
	ret0, _ := ret[0].(*biometrics.SyncBiometricIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncBiometricIdentifier indicates an expected call of SyncBiometricIdentifier.
func (mr *MockBiometricsServiceServerMockRecorder) SyncBiometricIdentifier(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncBiometricIdentifier", reflect.TypeOf((*MockBiometricsServiceServer)(nil).SyncBiometricIdentifier), arg0, arg1)
}

// UpdateBiometricsDetails mocks base method.
func (m *MockBiometricsServiceServer) UpdateBiometricsDetails(arg0 context.Context, arg1 *biometrics.UpdateBiometricsDetailsRequest) (*biometrics.UpdateBiometricsDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBiometricsDetails", arg0, arg1)
	ret0, _ := ret[0].(*biometrics.UpdateBiometricsDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBiometricsDetails indicates an expected call of UpdateBiometricsDetails.
func (mr *MockBiometricsServiceServerMockRecorder) UpdateBiometricsDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBiometricsDetails", reflect.TypeOf((*MockBiometricsServiceServer)(nil).UpdateBiometricsDetails), arg0, arg1)
}

// MockUnsafeBiometricsServiceServer is a mock of UnsafeBiometricsServiceServer interface.
type MockUnsafeBiometricsServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeBiometricsServiceServerMockRecorder
}

// MockUnsafeBiometricsServiceServerMockRecorder is the mock recorder for MockUnsafeBiometricsServiceServer.
type MockUnsafeBiometricsServiceServerMockRecorder struct {
	mock *MockUnsafeBiometricsServiceServer
}

// NewMockUnsafeBiometricsServiceServer creates a new mock instance.
func NewMockUnsafeBiometricsServiceServer(ctrl *gomock.Controller) *MockUnsafeBiometricsServiceServer {
	mock := &MockUnsafeBiometricsServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeBiometricsServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeBiometricsServiceServer) EXPECT() *MockUnsafeBiometricsServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedBiometricsServiceServer mocks base method.
func (m *MockUnsafeBiometricsServiceServer) mustEmbedUnimplementedBiometricsServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedBiometricsServiceServer")
}

// mustEmbedUnimplementedBiometricsServiceServer indicates an expected call of mustEmbedUnimplementedBiometricsServiceServer.
func (mr *MockUnsafeBiometricsServiceServerMockRecorder) mustEmbedUnimplementedBiometricsServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedBiometricsServiceServer", reflect.TypeOf((*MockUnsafeBiometricsServiceServer)(nil).mustEmbedUnimplementedBiometricsServiceServer))
}
