// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/auth/biometrics/service.proto

package biometrics

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	BiometricsService_GetBiometricsDetails_FullMethodName    = "/auth.biometrics.BiometricsService/GetBiometricsDetails"
	BiometricsService_UpdateBiometricsDetails_FullMethodName = "/auth.biometrics.BiometricsService/UpdateBiometricsDetails"
	BiometricsService_SyncBiometricIdentifier_FullMethodName = "/auth.biometrics.BiometricsService/SyncBiometricIdentifier"
)

// BiometricsServiceClient is the client API for BiometricsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BiometricsServiceClient interface {
	// GetBiometricsDetails will fetch biometric details for given actor id
	GetBiometricsDetails(ctx context.Context, in *GetBiometricsDetailsRequest, opts ...grpc.CallOption) (*GetBiometricsDetailsResponse, error)
	// UpdateBiometricsDetails updates biometric details for given actor id
	UpdateBiometricsDetails(ctx context.Context, in *UpdateBiometricsDetailsRequest, opts ...grpc.CallOption) (*UpdateBiometricsDetailsResponse, error)
	// SyncBiometricIdentifier: The app will send the biometric identifier generated at the client system
	// This received biometric identifier will be sent to Biometrics service for processing.
	// Biometrics service will publish the biometric app launch event to the queue
	SyncBiometricIdentifier(ctx context.Context, in *SyncBiometricIdentifierRequest, opts ...grpc.CallOption) (*SyncBiometricIdentifierResponse, error)
}

type biometricsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBiometricsServiceClient(cc grpc.ClientConnInterface) BiometricsServiceClient {
	return &biometricsServiceClient{cc}
}

func (c *biometricsServiceClient) GetBiometricsDetails(ctx context.Context, in *GetBiometricsDetailsRequest, opts ...grpc.CallOption) (*GetBiometricsDetailsResponse, error) {
	out := new(GetBiometricsDetailsResponse)
	err := c.cc.Invoke(ctx, BiometricsService_GetBiometricsDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *biometricsServiceClient) UpdateBiometricsDetails(ctx context.Context, in *UpdateBiometricsDetailsRequest, opts ...grpc.CallOption) (*UpdateBiometricsDetailsResponse, error) {
	out := new(UpdateBiometricsDetailsResponse)
	err := c.cc.Invoke(ctx, BiometricsService_UpdateBiometricsDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *biometricsServiceClient) SyncBiometricIdentifier(ctx context.Context, in *SyncBiometricIdentifierRequest, opts ...grpc.CallOption) (*SyncBiometricIdentifierResponse, error) {
	out := new(SyncBiometricIdentifierResponse)
	err := c.cc.Invoke(ctx, BiometricsService_SyncBiometricIdentifier_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BiometricsServiceServer is the server API for BiometricsService service.
// All implementations should embed UnimplementedBiometricsServiceServer
// for forward compatibility
type BiometricsServiceServer interface {
	// GetBiometricsDetails will fetch biometric details for given actor id
	GetBiometricsDetails(context.Context, *GetBiometricsDetailsRequest) (*GetBiometricsDetailsResponse, error)
	// UpdateBiometricsDetails updates biometric details for given actor id
	UpdateBiometricsDetails(context.Context, *UpdateBiometricsDetailsRequest) (*UpdateBiometricsDetailsResponse, error)
	// SyncBiometricIdentifier: The app will send the biometric identifier generated at the client system
	// This received biometric identifier will be sent to Biometrics service for processing.
	// Biometrics service will publish the biometric app launch event to the queue
	SyncBiometricIdentifier(context.Context, *SyncBiometricIdentifierRequest) (*SyncBiometricIdentifierResponse, error)
}

// UnimplementedBiometricsServiceServer should be embedded to have forward compatible implementations.
type UnimplementedBiometricsServiceServer struct {
}

func (UnimplementedBiometricsServiceServer) GetBiometricsDetails(context.Context, *GetBiometricsDetailsRequest) (*GetBiometricsDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBiometricsDetails not implemented")
}
func (UnimplementedBiometricsServiceServer) UpdateBiometricsDetails(context.Context, *UpdateBiometricsDetailsRequest) (*UpdateBiometricsDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBiometricsDetails not implemented")
}
func (UnimplementedBiometricsServiceServer) SyncBiometricIdentifier(context.Context, *SyncBiometricIdentifierRequest) (*SyncBiometricIdentifierResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncBiometricIdentifier not implemented")
}

// UnsafeBiometricsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BiometricsServiceServer will
// result in compilation errors.
type UnsafeBiometricsServiceServer interface {
	mustEmbedUnimplementedBiometricsServiceServer()
}

func RegisterBiometricsServiceServer(s grpc.ServiceRegistrar, srv BiometricsServiceServer) {
	s.RegisterService(&BiometricsService_ServiceDesc, srv)
}

func _BiometricsService_GetBiometricsDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBiometricsDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BiometricsServiceServer).GetBiometricsDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BiometricsService_GetBiometricsDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BiometricsServiceServer).GetBiometricsDetails(ctx, req.(*GetBiometricsDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BiometricsService_UpdateBiometricsDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBiometricsDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BiometricsServiceServer).UpdateBiometricsDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BiometricsService_UpdateBiometricsDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BiometricsServiceServer).UpdateBiometricsDetails(ctx, req.(*UpdateBiometricsDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BiometricsService_SyncBiometricIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncBiometricIdentifierRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BiometricsServiceServer).SyncBiometricIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BiometricsService_SyncBiometricIdentifier_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BiometricsServiceServer).SyncBiometricIdentifier(ctx, req.(*SyncBiometricIdentifierRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BiometricsService_ServiceDesc is the grpc.ServiceDesc for BiometricsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BiometricsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "auth.biometrics.BiometricsService",
	HandlerType: (*BiometricsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBiometricsDetails",
			Handler:    _BiometricsService_GetBiometricsDetails_Handler,
		},
		{
			MethodName: "UpdateBiometricsDetails",
			Handler:    _BiometricsService_UpdateBiometricsDetails_Handler,
		},
		{
			MethodName: "SyncBiometricIdentifier",
			Handler:    _BiometricsService_SyncBiometricIdentifier_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/auth/biometrics/service.proto",
}
