// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/biometrics/consumer/consumer.proto

package biometrics

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.Platform(0)
)

// Validate checks the field values on ProcessBiometricEventRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessBiometricEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessBiometricEventRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessBiometricEventRequestMultiError, or nil if none found.
func (m *ProcessBiometricEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessBiometricEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessBiometricEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessBiometricEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessBiometricEventRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for BiometricId

	// no validation rules for AppPlatform

	// no validation rules for DeviceId

	// no validation rules for AppVersion

	if len(errors) > 0 {
		return ProcessBiometricEventRequestMultiError(errors)
	}

	return nil
}

// ProcessBiometricEventRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessBiometricEventRequest.ValidateAll() if
// the designated constraints aren't met.
type ProcessBiometricEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessBiometricEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessBiometricEventRequestMultiError) AllErrors() []error { return m }

// ProcessBiometricEventRequestValidationError is the validation error returned
// by ProcessBiometricEventRequest.Validate if the designated constraints
// aren't met.
type ProcessBiometricEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessBiometricEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessBiometricEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessBiometricEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessBiometricEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessBiometricEventRequestValidationError) ErrorName() string {
	return "ProcessBiometricEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessBiometricEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessBiometricEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessBiometricEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessBiometricEventRequestValidationError{}

// Validate checks the field values on ProcessBiometricEventResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessBiometricEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessBiometricEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessBiometricEventResponseMultiError, or nil if none found.
func (m *ProcessBiometricEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessBiometricEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessBiometricEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessBiometricEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessBiometricEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessBiometricEventResponseMultiError(errors)
	}

	return nil
}

// ProcessBiometricEventResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessBiometricEventResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessBiometricEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessBiometricEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessBiometricEventResponseMultiError) AllErrors() []error { return m }

// ProcessBiometricEventResponseValidationError is the validation error
// returned by ProcessBiometricEventResponse.Validate if the designated
// constraints aren't met.
type ProcessBiometricEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessBiometricEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessBiometricEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessBiometricEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessBiometricEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessBiometricEventResponseValidationError) ErrorName() string {
	return "ProcessBiometricEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessBiometricEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessBiometricEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessBiometricEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessBiometricEventResponseValidationError{}
