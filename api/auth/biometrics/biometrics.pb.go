//go:generate gen_sql -types=BiometricInfo,BiometricStatus,SubStatus

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/biometrics/internal/biometrics.proto

package biometrics

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SubStatus int32

const (
	SubStatus_SUB_STATUS_UNSPECIFIED                             SubStatus = 0
	SubStatus_SUB_STATUS_REVOKED_BIOMETRIC_ID_CHANGED            SubStatus = 1
	SubStatus_SUB_STATUS_REVOKED_INVALID_OTP_THREASHOLD_BREACHED SubStatus = 2
)

// Enum value maps for SubStatus.
var (
	SubStatus_name = map[int32]string{
		0: "SUB_STATUS_UNSPECIFIED",
		1: "SUB_STATUS_REVOKED_BIOMETRIC_ID_CHANGED",
		2: "SUB_STATUS_REVOKED_INVALID_OTP_THREASHOLD_BREACHED",
	}
	SubStatus_value = map[string]int32{
		"SUB_STATUS_UNSPECIFIED":                             0,
		"SUB_STATUS_REVOKED_BIOMETRIC_ID_CHANGED":            1,
		"SUB_STATUS_REVOKED_INVALID_OTP_THREASHOLD_BREACHED": 2,
	}
)

func (x SubStatus) Enum() *SubStatus {
	p := new(SubStatus)
	*p = x
	return p
}

func (x SubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_biometrics_internal_biometrics_proto_enumTypes[0].Descriptor()
}

func (SubStatus) Type() protoreflect.EnumType {
	return &file_api_auth_biometrics_internal_biometrics_proto_enumTypes[0]
}

func (x SubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubStatus.Descriptor instead.
func (SubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_biometrics_internal_biometrics_proto_rawDescGZIP(), []int{0}
}

type BiometricStatus int32

const (
	BiometricStatus_BIOMETRIC_STATUS_UNSPECIFIED BiometricStatus = 0
	BiometricStatus_BIOMETRIC_STATUS_VERIFIED    BiometricStatus = 1
	BiometricStatus_BIOMETRIC_STATUS_REVOKED     BiometricStatus = 2
)

// Enum value maps for BiometricStatus.
var (
	BiometricStatus_name = map[int32]string{
		0: "BIOMETRIC_STATUS_UNSPECIFIED",
		1: "BIOMETRIC_STATUS_VERIFIED",
		2: "BIOMETRIC_STATUS_REVOKED",
	}
	BiometricStatus_value = map[string]int32{
		"BIOMETRIC_STATUS_UNSPECIFIED": 0,
		"BIOMETRIC_STATUS_VERIFIED":    1,
		"BIOMETRIC_STATUS_REVOKED":     2,
	}
)

func (x BiometricStatus) Enum() *BiometricStatus {
	p := new(BiometricStatus)
	*p = x
	return p
}

func (x BiometricStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BiometricStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_biometrics_internal_biometrics_proto_enumTypes[1].Descriptor()
}

func (BiometricStatus) Type() protoreflect.EnumType {
	return &file_api_auth_biometrics_internal_biometrics_proto_enumTypes[1]
}

func (x BiometricStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BiometricStatus.Descriptor instead.
func (BiometricStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_biometrics_internal_biometrics_proto_rawDescGZIP(), []int{1}
}

type BiometricsFieldMask int32

const (
	BiometricsFieldMask_BIOMETRICS_FIELD_MASK_UNSPECIFIED    BiometricsFieldMask = 0
	BiometricsFieldMask_BIOMETRICS_FIELD_MASK_ID             BiometricsFieldMask = 1
	BiometricsFieldMask_BIOMETRICS_FIELD_MASK_ACTOR_ID       BiometricsFieldMask = 2
	BiometricsFieldMask_BIOMETRICS_FIELD_MASK_BIOMETRIC_INFO BiometricsFieldMask = 3
	BiometricsFieldMask_BIOMETRICS_FIELD_MASK_STATUS         BiometricsFieldMask = 4
	BiometricsFieldMask_BIOMETRICS_FIELD_MASK_VERIFIED_AT    BiometricsFieldMask = 5
	BiometricsFieldMask_BIOMETRICS_FIELD_MASK_CREATED_AT     BiometricsFieldMask = 6
	BiometricsFieldMask_BIOMETRICS_FIELD_MASK_UPDATED_AT     BiometricsFieldMask = 7
	BiometricsFieldMask_BIOMETRICS_FIELD_MASK_DELETED_AT     BiometricsFieldMask = 8
	BiometricsFieldMask_BIOMETRICS_FIELD_MASK_SUB_STATUS     BiometricsFieldMask = 9
)

// Enum value maps for BiometricsFieldMask.
var (
	BiometricsFieldMask_name = map[int32]string{
		0: "BIOMETRICS_FIELD_MASK_UNSPECIFIED",
		1: "BIOMETRICS_FIELD_MASK_ID",
		2: "BIOMETRICS_FIELD_MASK_ACTOR_ID",
		3: "BIOMETRICS_FIELD_MASK_BIOMETRIC_INFO",
		4: "BIOMETRICS_FIELD_MASK_STATUS",
		5: "BIOMETRICS_FIELD_MASK_VERIFIED_AT",
		6: "BIOMETRICS_FIELD_MASK_CREATED_AT",
		7: "BIOMETRICS_FIELD_MASK_UPDATED_AT",
		8: "BIOMETRICS_FIELD_MASK_DELETED_AT",
		9: "BIOMETRICS_FIELD_MASK_SUB_STATUS",
	}
	BiometricsFieldMask_value = map[string]int32{
		"BIOMETRICS_FIELD_MASK_UNSPECIFIED":    0,
		"BIOMETRICS_FIELD_MASK_ID":             1,
		"BIOMETRICS_FIELD_MASK_ACTOR_ID":       2,
		"BIOMETRICS_FIELD_MASK_BIOMETRIC_INFO": 3,
		"BIOMETRICS_FIELD_MASK_STATUS":         4,
		"BIOMETRICS_FIELD_MASK_VERIFIED_AT":    5,
		"BIOMETRICS_FIELD_MASK_CREATED_AT":     6,
		"BIOMETRICS_FIELD_MASK_UPDATED_AT":     7,
		"BIOMETRICS_FIELD_MASK_DELETED_AT":     8,
		"BIOMETRICS_FIELD_MASK_SUB_STATUS":     9,
	}
)

func (x BiometricsFieldMask) Enum() *BiometricsFieldMask {
	p := new(BiometricsFieldMask)
	*p = x
	return p
}

func (x BiometricsFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BiometricsFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_biometrics_internal_biometrics_proto_enumTypes[2].Descriptor()
}

func (BiometricsFieldMask) Type() protoreflect.EnumType {
	return &file_api_auth_biometrics_internal_biometrics_proto_enumTypes[2]
}

func (x BiometricsFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BiometricsFieldMask.Descriptor instead.
func (BiometricsFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_biometrics_internal_biometrics_proto_rawDescGZIP(), []int{2}
}

type Biometrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// actor id of the user
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// data regarding biometrics of the user
	BiometricInfo *BiometricInfo `protobuf:"bytes,3,opt,name=biometric_info,json=biometricInfo,proto3" json:"biometric_info,omitempty"`
	// status of the biometric; represents the validity of the biometrics set on the device
	Status BiometricStatus `protobuf:"varint,4,opt,name=status,proto3,enum=auth.biometrics.BiometricStatus" json:"status,omitempty"`
	// timestamp at which biometric was validated
	VerifiedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=verified_at,json=verifiedAt,proto3" json:"verified_at,omitempty"`
	CreatedAt  *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt  *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt  *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// reason for updating the status
	SubStatus SubStatus `protobuf:"varint,9,opt,name=sub_status,json=subStatus,proto3,enum=auth.biometrics.SubStatus" json:"sub_status,omitempty"`
}

func (x *Biometrics) Reset() {
	*x = Biometrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_biometrics_internal_biometrics_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Biometrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Biometrics) ProtoMessage() {}

func (x *Biometrics) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_biometrics_internal_biometrics_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Biometrics.ProtoReflect.Descriptor instead.
func (*Biometrics) Descriptor() ([]byte, []int) {
	return file_api_auth_biometrics_internal_biometrics_proto_rawDescGZIP(), []int{0}
}

func (x *Biometrics) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Biometrics) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *Biometrics) GetBiometricInfo() *BiometricInfo {
	if x != nil {
		return x.BiometricInfo
	}
	return nil
}

func (x *Biometrics) GetStatus() BiometricStatus {
	if x != nil {
		return x.Status
	}
	return BiometricStatus_BIOMETRIC_STATUS_UNSPECIFIED
}

func (x *Biometrics) GetVerifiedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.VerifiedAt
	}
	return nil
}

func (x *Biometrics) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Biometrics) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Biometrics) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *Biometrics) GetSubStatus() SubStatus {
	if x != nil {
		return x.SubStatus
	}
	return SubStatus_SUB_STATUS_UNSPECIFIED
}

type BiometricInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// biometric identifier received from the app
	// this id will be passed at every app launch by the client
	// in case of any change in biometric id, we will mark the biometric status
	// as revoked and will be updated to Verified post successful auth operation
	BiometricId string `protobuf:"bytes,1,opt,name=biometric_id,json=biometricId,proto3" json:"biometric_id,omitempty"`
	// app platform IOS or Android
	AppPlatform common.Platform `protobuf:"varint,2,opt,name=app_platform,json=appPlatform,proto3,enum=api.typesv2.common.Platform" json:"app_platform,omitempty"`
	// registered customer device ID
	DeviceId string `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// version of the app from which biometric details are collected
	AppVersion int64 `protobuf:"varint,4,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
}

func (x *BiometricInfo) Reset() {
	*x = BiometricInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_biometrics_internal_biometrics_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BiometricInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BiometricInfo) ProtoMessage() {}

func (x *BiometricInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_biometrics_internal_biometrics_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BiometricInfo.ProtoReflect.Descriptor instead.
func (*BiometricInfo) Descriptor() ([]byte, []int) {
	return file_api_auth_biometrics_internal_biometrics_proto_rawDescGZIP(), []int{1}
}

func (x *BiometricInfo) GetBiometricId() string {
	if x != nil {
		return x.BiometricId
	}
	return ""
}

func (x *BiometricInfo) GetAppPlatform() common.Platform {
	if x != nil {
		return x.AppPlatform
	}
	return common.Platform(0)
}

func (x *BiometricInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *BiometricInfo) GetAppVersion() int64 {
	if x != nil {
		return x.AppVersion
	}
	return 0
}

var File_api_auth_biometrics_internal_biometrics_proto protoreflect.FileDescriptor

var file_api_auth_biometrics_internal_biometrics_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x62, 0x69, 0x6f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x62,
	0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xe1, 0x03, 0x0a, 0x0a, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0e,
	0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a,
	0x0b, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x73,
	0x75, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1a, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x2e, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x73, 0x75, 0x62,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xb1, 0x01, 0x0a, 0x0d, 0x42, 0x69, 0x6f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x69, 0x6f, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0c, 0x61,
	0x70, 0x70, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52,
	0x0b, 0x61, 0x70, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x1b, 0x0a, 0x09,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2a, 0x8c, 0x01, 0x0a, 0x09, 0x53,
	0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x2b, 0x0a, 0x27, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x44, 0x5f, 0x42, 0x49, 0x4f, 0x4d, 0x45,
	0x54, 0x52, 0x49, 0x43, 0x5f, 0x49, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x36, 0x0a, 0x32, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x4f, 0x54, 0x50, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x41, 0x53, 0x48, 0x4f, 0x4c, 0x44, 0x5f, 0x42,
	0x52, 0x45, 0x41, 0x43, 0x48, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x70, 0x0a, 0x0f, 0x42, 0x69, 0x6f,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x1c,
	0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d,
	0x0a, 0x19, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1c, 0x0a,
	0x18, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x89, 0x03, 0x0a, 0x13,
	0x42, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d,
	0x61, 0x73, 0x6b, 0x12, 0x25, 0x0a, 0x21, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43,
	0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x42, 0x49,
	0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x42, 0x49, 0x4f, 0x4d,
	0x45, 0x54, 0x52, 0x49, 0x43, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x28, 0x0a, 0x24,
	0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54,
	0x52, 0x49, 0x43, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x04, 0x12, 0x25, 0x0a, 0x21, 0x42, 0x49, 0x4f, 0x4d,
	0x45, 0x54, 0x52, 0x49, 0x43, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x05, 0x12,
	0x24, 0x0a, 0x20, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x53, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44,
	0x5f, 0x41, 0x54, 0x10, 0x06, 0x12, 0x24, 0x0a, 0x20, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52,
	0x49, 0x43, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x07, 0x12, 0x24, 0x0a, 0x20, 0x42,
	0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10,
	0x08, 0x12, 0x24, 0x0a, 0x20, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x53, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x09, 0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x62, 0x69, 0x6f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x62, 0x69, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_biometrics_internal_biometrics_proto_rawDescOnce sync.Once
	file_api_auth_biometrics_internal_biometrics_proto_rawDescData = file_api_auth_biometrics_internal_biometrics_proto_rawDesc
)

func file_api_auth_biometrics_internal_biometrics_proto_rawDescGZIP() []byte {
	file_api_auth_biometrics_internal_biometrics_proto_rawDescOnce.Do(func() {
		file_api_auth_biometrics_internal_biometrics_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_biometrics_internal_biometrics_proto_rawDescData)
	})
	return file_api_auth_biometrics_internal_biometrics_proto_rawDescData
}

var file_api_auth_biometrics_internal_biometrics_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_auth_biometrics_internal_biometrics_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_auth_biometrics_internal_biometrics_proto_goTypes = []interface{}{
	(SubStatus)(0),                // 0: auth.biometrics.SubStatus
	(BiometricStatus)(0),          // 1: auth.biometrics.BiometricStatus
	(BiometricsFieldMask)(0),      // 2: auth.biometrics.BiometricsFieldMask
	(*Biometrics)(nil),            // 3: auth.biometrics.Biometrics
	(*BiometricInfo)(nil),         // 4: auth.biometrics.BiometricInfo
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
	(common.Platform)(0),          // 6: api.typesv2.common.Platform
}
var file_api_auth_biometrics_internal_biometrics_proto_depIdxs = []int32{
	4, // 0: auth.biometrics.Biometrics.biometric_info:type_name -> auth.biometrics.BiometricInfo
	1, // 1: auth.biometrics.Biometrics.status:type_name -> auth.biometrics.BiometricStatus
	5, // 2: auth.biometrics.Biometrics.verified_at:type_name -> google.protobuf.Timestamp
	5, // 3: auth.biometrics.Biometrics.created_at:type_name -> google.protobuf.Timestamp
	5, // 4: auth.biometrics.Biometrics.updated_at:type_name -> google.protobuf.Timestamp
	5, // 5: auth.biometrics.Biometrics.deleted_at:type_name -> google.protobuf.Timestamp
	0, // 6: auth.biometrics.Biometrics.sub_status:type_name -> auth.biometrics.SubStatus
	6, // 7: auth.biometrics.BiometricInfo.app_platform:type_name -> api.typesv2.common.Platform
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_auth_biometrics_internal_biometrics_proto_init() }
func file_api_auth_biometrics_internal_biometrics_proto_init() {
	if File_api_auth_biometrics_internal_biometrics_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_biometrics_internal_biometrics_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Biometrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_biometrics_internal_biometrics_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BiometricInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_biometrics_internal_biometrics_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_biometrics_internal_biometrics_proto_goTypes,
		DependencyIndexes: file_api_auth_biometrics_internal_biometrics_proto_depIdxs,
		EnumInfos:         file_api_auth_biometrics_internal_biometrics_proto_enumTypes,
		MessageInfos:      file_api_auth_biometrics_internal_biometrics_proto_msgTypes,
	}.Build()
	File_api_auth_biometrics_internal_biometrics_proto = out.File
	file_api_auth_biometrics_internal_biometrics_proto_rawDesc = nil
	file_api_auth_biometrics_internal_biometrics_proto_goTypes = nil
	file_api_auth_biometrics_internal_biometrics_proto_depIdxs = nil
}
