// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/biometrics/internal/biometrics.proto

package biometrics

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.Platform(0)
)

// Validate checks the field values on Biometrics with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Biometrics) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Biometrics with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BiometricsMultiError, or
// nil if none found.
func (m *Biometrics) ValidateAll() error {
	return m.validate(true)
}

func (m *Biometrics) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetBiometricInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BiometricsValidationError{
					field:  "BiometricInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BiometricsValidationError{
					field:  "BiometricInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBiometricInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BiometricsValidationError{
				field:  "BiometricInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetVerifiedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BiometricsValidationError{
					field:  "VerifiedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BiometricsValidationError{
					field:  "VerifiedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVerifiedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BiometricsValidationError{
				field:  "VerifiedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BiometricsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BiometricsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BiometricsValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BiometricsValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BiometricsValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BiometricsValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BiometricsValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BiometricsValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BiometricsValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SubStatus

	if len(errors) > 0 {
		return BiometricsMultiError(errors)
	}

	return nil
}

// BiometricsMultiError is an error wrapping multiple validation errors
// returned by Biometrics.ValidateAll() if the designated constraints aren't met.
type BiometricsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BiometricsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BiometricsMultiError) AllErrors() []error { return m }

// BiometricsValidationError is the validation error returned by
// Biometrics.Validate if the designated constraints aren't met.
type BiometricsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BiometricsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BiometricsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BiometricsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BiometricsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BiometricsValidationError) ErrorName() string { return "BiometricsValidationError" }

// Error satisfies the builtin error interface
func (e BiometricsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBiometrics.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BiometricsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BiometricsValidationError{}

// Validate checks the field values on BiometricInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BiometricInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BiometricInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BiometricInfoMultiError, or
// nil if none found.
func (m *BiometricInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *BiometricInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BiometricId

	// no validation rules for AppPlatform

	// no validation rules for DeviceId

	// no validation rules for AppVersion

	if len(errors) > 0 {
		return BiometricInfoMultiError(errors)
	}

	return nil
}

// BiometricInfoMultiError is an error wrapping multiple validation errors
// returned by BiometricInfo.ValidateAll() if the designated constraints
// aren't met.
type BiometricInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BiometricInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BiometricInfoMultiError) AllErrors() []error { return m }

// BiometricInfoValidationError is the validation error returned by
// BiometricInfo.Validate if the designated constraints aren't met.
type BiometricInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BiometricInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BiometricInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BiometricInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BiometricInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BiometricInfoValidationError) ErrorName() string { return "BiometricInfoValidationError" }

// Error satisfies the builtin error interface
func (e BiometricInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBiometricInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BiometricInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BiometricInfoValidationError{}
