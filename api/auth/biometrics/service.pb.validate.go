// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/biometrics/service.proto

package biometrics

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetBiometricsDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBiometricsDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBiometricsDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBiometricsDetailsRequestMultiError, or nil if none found.
func (m *GetBiometricsDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBiometricsDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetBiometricsDetailsRequestMultiError(errors)
	}

	return nil
}

// GetBiometricsDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by GetBiometricsDetailsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetBiometricsDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBiometricsDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBiometricsDetailsRequestMultiError) AllErrors() []error { return m }

// GetBiometricsDetailsRequestValidationError is the validation error returned
// by GetBiometricsDetailsRequest.Validate if the designated constraints
// aren't met.
type GetBiometricsDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBiometricsDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBiometricsDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBiometricsDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBiometricsDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBiometricsDetailsRequestValidationError) ErrorName() string {
	return "GetBiometricsDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBiometricsDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBiometricsDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBiometricsDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBiometricsDetailsRequestValidationError{}

// Validate checks the field values on GetBiometricsDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBiometricsDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBiometricsDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBiometricsDetailsResponseMultiError, or nil if none found.
func (m *GetBiometricsDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBiometricsDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBiometricsDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBiometricsDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBiometricsDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BiometricStatus

	if all {
		switch v := interface{}(m.GetBiometricInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBiometricsDetailsResponseValidationError{
					field:  "BiometricInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBiometricsDetailsResponseValidationError{
					field:  "BiometricInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBiometricInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBiometricsDetailsResponseValidationError{
				field:  "BiometricInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVerifiedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBiometricsDetailsResponseValidationError{
					field:  "VerifiedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBiometricsDetailsResponseValidationError{
					field:  "VerifiedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVerifiedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBiometricsDetailsResponseValidationError{
				field:  "VerifiedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SubStatus

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBiometricsDetailsResponseValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBiometricsDetailsResponseValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBiometricsDetailsResponseValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BiometricTableId

	if len(errors) > 0 {
		return GetBiometricsDetailsResponseMultiError(errors)
	}

	return nil
}

// GetBiometricsDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by GetBiometricsDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetBiometricsDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBiometricsDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBiometricsDetailsResponseMultiError) AllErrors() []error { return m }

// GetBiometricsDetailsResponseValidationError is the validation error returned
// by GetBiometricsDetailsResponse.Validate if the designated constraints
// aren't met.
type GetBiometricsDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBiometricsDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBiometricsDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBiometricsDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBiometricsDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBiometricsDetailsResponseValidationError) ErrorName() string {
	return "GetBiometricsDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBiometricsDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBiometricsDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBiometricsDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBiometricsDetailsResponseValidationError{}

// Validate checks the field values on UpdateBiometricsDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateBiometricsDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBiometricsDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateBiometricsDetailsRequestMultiError, or nil if none found.
func (m *UpdateBiometricsDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBiometricsDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for UpdatedBiometricStatus

	// no validation rules for SubStatus

	if len(errors) > 0 {
		return UpdateBiometricsDetailsRequestMultiError(errors)
	}

	return nil
}

// UpdateBiometricsDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateBiometricsDetailsRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateBiometricsDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBiometricsDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBiometricsDetailsRequestMultiError) AllErrors() []error { return m }

// UpdateBiometricsDetailsRequestValidationError is the validation error
// returned by UpdateBiometricsDetailsRequest.Validate if the designated
// constraints aren't met.
type UpdateBiometricsDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBiometricsDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBiometricsDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBiometricsDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBiometricsDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBiometricsDetailsRequestValidationError) ErrorName() string {
	return "UpdateBiometricsDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBiometricsDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBiometricsDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBiometricsDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBiometricsDetailsRequestValidationError{}

// Validate checks the field values on UpdateBiometricsDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateBiometricsDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBiometricsDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateBiometricsDetailsResponseMultiError, or nil if none found.
func (m *UpdateBiometricsDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBiometricsDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateBiometricsDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateBiometricsDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateBiometricsDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateBiometricsDetailsResponseMultiError(errors)
	}

	return nil
}

// UpdateBiometricsDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateBiometricsDetailsResponse.ValidateAll()
// if the designated constraints aren't met.
type UpdateBiometricsDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBiometricsDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBiometricsDetailsResponseMultiError) AllErrors() []error { return m }

// UpdateBiometricsDetailsResponseValidationError is the validation error
// returned by UpdateBiometricsDetailsResponse.Validate if the designated
// constraints aren't met.
type UpdateBiometricsDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBiometricsDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBiometricsDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBiometricsDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBiometricsDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBiometricsDetailsResponseValidationError) ErrorName() string {
	return "UpdateBiometricsDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBiometricsDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBiometricsDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBiometricsDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBiometricsDetailsResponseValidationError{}

// Validate checks the field values on SyncBiometricIdentifierRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncBiometricIdentifierRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncBiometricIdentifierRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SyncBiometricIdentifierRequestMultiError, or nil if none found.
func (m *SyncBiometricIdentifierRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncBiometricIdentifierRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetBiometricInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SyncBiometricIdentifierRequestValidationError{
					field:  "BiometricInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SyncBiometricIdentifierRequestValidationError{
					field:  "BiometricInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBiometricInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SyncBiometricIdentifierRequestValidationError{
				field:  "BiometricInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SyncBiometricIdentifierRequestMultiError(errors)
	}

	return nil
}

// SyncBiometricIdentifierRequestMultiError is an error wrapping multiple
// validation errors returned by SyncBiometricIdentifierRequest.ValidateAll()
// if the designated constraints aren't met.
type SyncBiometricIdentifierRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncBiometricIdentifierRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncBiometricIdentifierRequestMultiError) AllErrors() []error { return m }

// SyncBiometricIdentifierRequestValidationError is the validation error
// returned by SyncBiometricIdentifierRequest.Validate if the designated
// constraints aren't met.
type SyncBiometricIdentifierRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncBiometricIdentifierRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncBiometricIdentifierRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncBiometricIdentifierRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncBiometricIdentifierRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncBiometricIdentifierRequestValidationError) ErrorName() string {
	return "SyncBiometricIdentifierRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncBiometricIdentifierRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncBiometricIdentifierRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncBiometricIdentifierRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncBiometricIdentifierRequestValidationError{}

// Validate checks the field values on SyncBiometricIdentifierResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncBiometricIdentifierResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncBiometricIdentifierResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SyncBiometricIdentifierResponseMultiError, or nil if none found.
func (m *SyncBiometricIdentifierResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncBiometricIdentifierResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SyncBiometricIdentifierResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SyncBiometricIdentifierResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SyncBiometricIdentifierResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SyncBiometricIdentifierResponseMultiError(errors)
	}

	return nil
}

// SyncBiometricIdentifierResponseMultiError is an error wrapping multiple
// validation errors returned by SyncBiometricIdentifierResponse.ValidateAll()
// if the designated constraints aren't met.
type SyncBiometricIdentifierResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncBiometricIdentifierResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncBiometricIdentifierResponseMultiError) AllErrors() []error { return m }

// SyncBiometricIdentifierResponseValidationError is the validation error
// returned by SyncBiometricIdentifierResponse.Validate if the designated
// constraints aren't met.
type SyncBiometricIdentifierResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncBiometricIdentifierResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncBiometricIdentifierResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncBiometricIdentifierResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncBiometricIdentifierResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncBiometricIdentifierResponseValidationError) ErrorName() string {
	return "SyncBiometricIdentifierResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SyncBiometricIdentifierResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncBiometricIdentifierResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncBiometricIdentifierResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncBiometricIdentifierResponseValidationError{}
