// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/internal/token.proto

package auth

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// An enum to represent the type of authentication tokens
type TokenType int32

const (
	TokenType_TOKEN_TYPE_UNSPECIFIED TokenType = 0
	// A short lived authentication token
	TokenType_ACCESS_TOKEN TokenType = 1
	// Refresh tokens mitigate the risk of a long-lived access_token
	// The idea of refresh tokens is that if an access token is compromised,
	// because it is short-lived,
	// the attacker has a limited window in which to abuse it.
	TokenType_REFRESH_TOKEN TokenType = 2
	// Access token for waitlist users
	TokenType_WAITLIST_ACCESS_TOKEN TokenType = 3
	// Access token to call insights API from web-view
	TokenType_APP_INSIGHTS_ACCESS_TOKEN TokenType = 4
	// Access token to be used for user authentication with chatbot vendor
	TokenType_CHATBOT_ACCESS_TOKEN TokenType = 5
	// Access token for web onboarding users; generated with the phone number
	TokenType_WEB_LITE_ACCESS_TOKEN TokenType = 6
	// HANDSHAKE_TOKEN is generated when we complete verification of handshake code.
	TokenType_BKYC_HANDSHAKE_TOKEN TokenType = 7
	// Refresh token for Genie (Biometric KYC app); generated with Email OTP
	TokenType_GENIE_REFRESH_TOKEN TokenType = 8
	// Access token for Genie (Biometric KYC app); generated with MPIN and refresh token
	TokenType_GENIE_ACCESS_TOKEN TokenType = 9
	// Refresh token for webform used for balance transfer of MIN KYC closed accounts; generated with Phone/Email OTP
	TokenType_WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_REFRESH_TOKEN TokenType = 10
	// Access token for webform used for balance transfer of MIN KYC closed accounts; generated after PAN verification
	TokenType_WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_ACCESS_TOKEN TokenType = 11
	// Access token for webform used for risk outcall.
	TokenType_RISK_OUTCALL_WEBFORM_ACCESS_TOKEN TokenType = 12
	// Access token for web login - used for auth in web flows
	// This token is generated after OTP verification
	TokenType_WEB_ACCESS_TOKEN TokenType = 13
	// Access token to be used for user authentication with networth mcp
	TokenType_NETWORTH_MCP_ACCESS_TOKEN TokenType = 14
	// Access token for Nugget Chatbot; generated when passing data for SDK init
	TokenType_NUGGET_CHATBOT_ACCESS_TOKEN TokenType = 15
)

// Enum value maps for TokenType.
var (
	TokenType_name = map[int32]string{
		0:  "TOKEN_TYPE_UNSPECIFIED",
		1:  "ACCESS_TOKEN",
		2:  "REFRESH_TOKEN",
		3:  "WAITLIST_ACCESS_TOKEN",
		4:  "APP_INSIGHTS_ACCESS_TOKEN",
		5:  "CHATBOT_ACCESS_TOKEN",
		6:  "WEB_LITE_ACCESS_TOKEN",
		7:  "BKYC_HANDSHAKE_TOKEN",
		8:  "GENIE_REFRESH_TOKEN",
		9:  "GENIE_ACCESS_TOKEN",
		10: "WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_REFRESH_TOKEN",
		11: "WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_ACCESS_TOKEN",
		12: "RISK_OUTCALL_WEBFORM_ACCESS_TOKEN",
		13: "WEB_ACCESS_TOKEN",
		14: "NETWORTH_MCP_ACCESS_TOKEN",
		15: "NUGGET_CHATBOT_ACCESS_TOKEN",
	}
	TokenType_value = map[string]int32{
		"TOKEN_TYPE_UNSPECIFIED":    0,
		"ACCESS_TOKEN":              1,
		"REFRESH_TOKEN":             2,
		"WAITLIST_ACCESS_TOKEN":     3,
		"APP_INSIGHTS_ACCESS_TOKEN": 4,
		"CHATBOT_ACCESS_TOKEN":      5,
		"WEB_LITE_ACCESS_TOKEN":     6,
		"BKYC_HANDSHAKE_TOKEN":      7,
		"GENIE_REFRESH_TOKEN":       8,
		"GENIE_ACCESS_TOKEN":        9,
		"WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_REFRESH_TOKEN": 10,
		"WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_ACCESS_TOKEN":  11,
		"RISK_OUTCALL_WEBFORM_ACCESS_TOKEN":                     12,
		"WEB_ACCESS_TOKEN":                                      13,
		"NETWORTH_MCP_ACCESS_TOKEN":                             14,
		"NUGGET_CHATBOT_ACCESS_TOKEN":                           15,
	}
)

func (x TokenType) Enum() *TokenType {
	p := new(TokenType)
	*p = x
	return p
}

func (x TokenType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TokenType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_internal_token_proto_enumTypes[0].Descriptor()
}

func (TokenType) Type() protoreflect.EnumType {
	return &file_api_auth_internal_token_proto_enumTypes[0]
}

func (x TokenType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TokenType.Descriptor instead.
func (TokenType) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_internal_token_proto_rawDescGZIP(), []int{0}
}

type TokenDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                       string                   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	PhoneNumber              *common.PhoneNumber      `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	TokenType                TokenType                `protobuf:"varint,3,opt,name=tokenType,proto3,enum=auth.TokenType" json:"tokenType,omitempty"`
	ActorId                  string                   `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AfuId                    string                   `protobuf:"bytes,5,opt,name=afu_id,json=afuId,proto3" json:"afu_id,omitempty"`
	Device                   *common.Device           `protobuf:"bytes,6,opt,name=device,proto3" json:"device,omitempty"`
	DeviceRegistrationStatus DeviceRegistrationStatus `protobuf:"varint,7,opt,name=device_registration_status,json=deviceRegistrationStatus,proto3,enum=auth.DeviceRegistrationStatus" json:"device_registration_status,omitempty"`
	LastActivity             *timestamppb.Timestamp   `protobuf:"bytes,8,opt,name=last_activity,json=lastActivity,proto3" json:"last_activity,omitempty"`
	CreatedAt                *timestamppb.Timestamp   `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	DeletedAt                *timestamppb.Timestamp   `protobuf:"bytes,10,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	Token                    string                   `protobuf:"bytes,11,opt,name=token,proto3" json:"token,omitempty"`
	TokenDeletionReason      TokenDeletionReason      `protobuf:"varint,12,opt,name=token_deletion_reason,json=tokenDeletionReason,proto3,enum=auth.TokenDeletionReason" json:"token_deletion_reason,omitempty"`
	Email                    string                   `protobuf:"bytes,13,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *TokenDetails) Reset() {
	*x = TokenDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_token_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenDetails) ProtoMessage() {}

func (x *TokenDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_token_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenDetails.ProtoReflect.Descriptor instead.
func (*TokenDetails) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_token_proto_rawDescGZIP(), []int{0}
}

func (x *TokenDetails) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TokenDetails) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *TokenDetails) GetTokenType() TokenType {
	if x != nil {
		return x.TokenType
	}
	return TokenType_TOKEN_TYPE_UNSPECIFIED
}

func (x *TokenDetails) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *TokenDetails) GetAfuId() string {
	if x != nil {
		return x.AfuId
	}
	return ""
}

func (x *TokenDetails) GetDevice() *common.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *TokenDetails) GetDeviceRegistrationStatus() DeviceRegistrationStatus {
	if x != nil {
		return x.DeviceRegistrationStatus
	}
	return DeviceRegistrationStatus_DEVICE_STATUS_UNSPECIFIED
}

func (x *TokenDetails) GetLastActivity() *timestamppb.Timestamp {
	if x != nil {
		return x.LastActivity
	}
	return nil
}

func (x *TokenDetails) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TokenDetails) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *TokenDetails) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *TokenDetails) GetTokenDeletionReason() TokenDeletionReason {
	if x != nil {
		return x.TokenDeletionReason
	}
	return TokenDeletionReason_TOKEN_DELETION_REASON_UNSPECIFIED
}

func (x *TokenDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

var File_api_auth_internal_token_proto protoreflect.FileDescriptor

var file_api_auth_internal_token_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x04, 0x61, 0x75, 0x74, 0x68, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x87, 0x05, 0x0a, 0x0c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x61, 0x66, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x66, 0x75, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5c, 0x0a, 0x1a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x18, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x4d, 0x0a, 0x15, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x13,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2a, 0xf8, 0x03, 0x0a, 0x09, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x4f, 0x4b, 0x45, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x4f,
	0x4b, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48,
	0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x57, 0x41, 0x49, 0x54,
	0x4c, 0x49, 0x53, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x4f, 0x4b, 0x45,
	0x4e, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x50, 0x50, 0x5f, 0x49, 0x4e, 0x53, 0x49, 0x47,
	0x48, 0x54, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e,
	0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x41, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x05, 0x12, 0x19, 0x0a, 0x15,
	0x57, 0x45, 0x42, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f,
	0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x42, 0x4b, 0x59, 0x43, 0x5f,
	0x48, 0x41, 0x4e, 0x44, 0x53, 0x48, 0x41, 0x4b, 0x45, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10,
	0x07, 0x12, 0x17, 0x0a, 0x13, 0x47, 0x45, 0x4e, 0x49, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x45,
	0x53, 0x48, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x08, 0x12, 0x16, 0x0a, 0x12, 0x47, 0x45,
	0x4e, 0x49, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e,
	0x10, 0x09, 0x12, 0x39, 0x0a, 0x35, 0x57, 0x45, 0x42, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44,
	0x5f, 0x41, 0x43, 0x43, 0x5f, 0x4d, 0x49, 0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x42, 0x41, 0x4c,
	0x41, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x52, 0x45,
	0x46, 0x52, 0x45, 0x53, 0x48, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x0a, 0x12, 0x38, 0x0a,
	0x34, 0x57, 0x45, 0x42, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x5f,
	0x4d, 0x49, 0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f,
	0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x0b, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x49, 0x53, 0x4b, 0x5f,
	0x4f, 0x55, 0x54, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x57, 0x45, 0x42, 0x46, 0x4f, 0x52, 0x4d, 0x5f,
	0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x0c, 0x12, 0x14,
	0x0a, 0x10, 0x57, 0x45, 0x42, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x4f, 0x4b,
	0x45, 0x4e, 0x10, 0x0d, 0x12, 0x1d, 0x0a, 0x19, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48,
	0x5f, 0x4d, 0x43, 0x50, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x4f, 0x4b, 0x45,
	0x4e, 0x10, 0x0e, 0x12, 0x1f, 0x0a, 0x1b, 0x4e, 0x55, 0x47, 0x47, 0x45, 0x54, 0x5f, 0x43, 0x48,
	0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x4f, 0x4b,
	0x45, 0x4e, 0x10, 0x0f, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_internal_token_proto_rawDescOnce sync.Once
	file_api_auth_internal_token_proto_rawDescData = file_api_auth_internal_token_proto_rawDesc
)

func file_api_auth_internal_token_proto_rawDescGZIP() []byte {
	file_api_auth_internal_token_proto_rawDescOnce.Do(func() {
		file_api_auth_internal_token_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_internal_token_proto_rawDescData)
	})
	return file_api_auth_internal_token_proto_rawDescData
}

var file_api_auth_internal_token_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_internal_token_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_auth_internal_token_proto_goTypes = []interface{}{
	(TokenType)(0),                // 0: auth.TokenType
	(*TokenDetails)(nil),          // 1: auth.TokenDetails
	(*common.PhoneNumber)(nil),    // 2: api.typesv2.common.PhoneNumber
	(*common.Device)(nil),         // 3: api.typesv2.common.Device
	(DeviceRegistrationStatus)(0), // 4: auth.DeviceRegistrationStatus
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
	(TokenDeletionReason)(0),      // 6: auth.TokenDeletionReason
}
var file_api_auth_internal_token_proto_depIdxs = []int32{
	2, // 0: auth.TokenDetails.phone_number:type_name -> api.typesv2.common.PhoneNumber
	0, // 1: auth.TokenDetails.tokenType:type_name -> auth.TokenType
	3, // 2: auth.TokenDetails.device:type_name -> api.typesv2.common.Device
	4, // 3: auth.TokenDetails.device_registration_status:type_name -> auth.DeviceRegistrationStatus
	5, // 4: auth.TokenDetails.last_activity:type_name -> google.protobuf.Timestamp
	5, // 5: auth.TokenDetails.created_at:type_name -> google.protobuf.Timestamp
	5, // 6: auth.TokenDetails.deleted_at:type_name -> google.protobuf.Timestamp
	6, // 7: auth.TokenDetails.token_deletion_reason:type_name -> auth.TokenDeletionReason
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_auth_internal_token_proto_init() }
func file_api_auth_internal_token_proto_init() {
	if File_api_auth_internal_token_proto != nil {
		return
	}
	file_api_auth_device_registration_status_proto_init()
	file_api_auth_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_auth_internal_token_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_internal_token_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_internal_token_proto_goTypes,
		DependencyIndexes: file_api_auth_internal_token_proto_depIdxs,
		EnumInfos:         file_api_auth_internal_token_proto_enumTypes,
		MessageInfos:      file_api_auth_internal_token_proto_msgTypes,
	}.Build()
	File_api_auth_internal_token_proto = out.File
	file_api_auth_internal_token_proto_rawDesc = nil
	file_api_auth_internal_token_proto_goTypes = nil
	file_api_auth_internal_token_proto_depIdxs = nil
}
