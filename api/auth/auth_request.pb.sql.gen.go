// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /Users/<USER>/go/src/github.com/epifi/gamma/api/auth/auth_request.pb.go

package auth

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the AuthRequestStatus in string format in DB
func (p AuthRequestStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing AuthRequestStatus while reading from DB
func (p *AuthRequestStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := AuthRequestStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected AuthRequestStatus value: %s", val)
	}
	*p = AuthRequestStatus(valInt)
	return nil
}

// Marshaler interface implementation for AuthRequestStatus
func (x AuthRequestStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for AuthRequestStatus
func (x *AuthRequestStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = AuthRequestStatus(AuthRequestStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the FlowName in string format in DB
func (p FlowName) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing FlowName while reading from DB
func (p *FlowName) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := FlowName_value[val]
	if !ok {
		return fmt.Errorf("unexpected FlowName value: %s", val)
	}
	*p = FlowName(valInt)
	return nil
}

// Marshaler interface implementation for FlowName
func (x FlowName) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for FlowName
func (x *FlowName) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = FlowName(FlowName_value[val])
	return nil
}

// Valuer interface implementation for storing the Provenance in string format in DB
func (p Provenance) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Provenance while reading from DB
func (p *Provenance) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Provenance_value[val]
	if !ok {
		return fmt.Errorf("unexpected Provenance value: %s", val)
	}
	*p = Provenance(valInt)
	return nil
}

// Marshaler interface implementation for Provenance
func (x Provenance) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Provenance
func (x *Provenance) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Provenance(Provenance_value[val])
	return nil
}
