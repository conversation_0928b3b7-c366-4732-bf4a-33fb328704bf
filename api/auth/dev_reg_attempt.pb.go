// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/internal/dev_reg_attempt.proto

package auth

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeviceRegistrationAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttemptId string `protobuf:"bytes,1,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
	// Unique id to denote a device of user
	DeviceId string `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// Actor id of the device holder
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Recipient phone number
	PhoneNumber string `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// string request id
	RequestId string `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Represents the status of device registration of customer
	Status DeviceRegistrationStatus `protobuf:"varint,6,opt,name=Status,proto3,enum=auth.DeviceRegistrationStatus" json:"Status,omitempty"`
	// acknowledgement info for the registration sms
	SmsInfo *DeviceRegistrationAttempt_SmsInfo `protobuf:"bytes,9,opt,name=sms_info,json=smsInfo,proto3" json:"sms_info,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated_at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *DeviceRegistrationAttempt) Reset() {
	*x = DeviceRegistrationAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_dev_reg_attempt_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceRegistrationAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRegistrationAttempt) ProtoMessage() {}

func (x *DeviceRegistrationAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_dev_reg_attempt_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRegistrationAttempt.ProtoReflect.Descriptor instead.
func (*DeviceRegistrationAttempt) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_dev_reg_attempt_proto_rawDescGZIP(), []int{0}
}

func (x *DeviceRegistrationAttempt) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *DeviceRegistrationAttempt) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *DeviceRegistrationAttempt) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DeviceRegistrationAttempt) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *DeviceRegistrationAttempt) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DeviceRegistrationAttempt) GetStatus() DeviceRegistrationStatus {
	if x != nil {
		return x.Status
	}
	return DeviceRegistrationStatus_DEVICE_STATUS_UNSPECIFIED
}

func (x *DeviceRegistrationAttempt) GetSmsInfo() *DeviceRegistrationAttempt_SmsInfo {
	if x != nil {
		return x.SmsInfo
	}
	return nil
}

func (x *DeviceRegistrationAttempt) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DeviceRegistrationAttempt) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type DeviceRegistrationAttempt_SmsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sms acknowledged at
	SmsAckAt *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=sms_ack_at,json=smsAckAt,proto3" json:"sms_ack_at,omitempty"`
	// sms acknowledgement notified at
	SmsAckNotifiedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=sms_ack_notified_at,json=smsAckNotifiedAt,proto3" json:"sms_ack_notified_at,omitempty"`
}

func (x *DeviceRegistrationAttempt_SmsInfo) Reset() {
	*x = DeviceRegistrationAttempt_SmsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_internal_dev_reg_attempt_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceRegistrationAttempt_SmsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRegistrationAttempt_SmsInfo) ProtoMessage() {}

func (x *DeviceRegistrationAttempt_SmsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_internal_dev_reg_attempt_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRegistrationAttempt_SmsInfo.ProtoReflect.Descriptor instead.
func (*DeviceRegistrationAttempt_SmsInfo) Descriptor() ([]byte, []int) {
	return file_api_auth_internal_dev_reg_attempt_proto_rawDescGZIP(), []int{0, 0}
}

func (x *DeviceRegistrationAttempt_SmsInfo) GetSmsAckAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SmsAckAt
	}
	return nil
}

func (x *DeviceRegistrationAttempt_SmsInfo) GetSmsAckNotifiedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SmsAckNotifiedAt
	}
	return nil
}

var File_api_auth_internal_dev_reg_attempt_proto protoreflect.FileDescriptor

var file_api_auth_internal_dev_reg_attempt_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x64, 0x65, 0x76, 0x5f, 0x72, 0x65, 0x67, 0x5f, 0x61, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x61, 0x75, 0x74, 0x68, 0x1a,
	0x29, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb7, 0x04, 0x0a, 0x19,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x36, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x42, 0x0a, 0x08, 0x73, 0x6d,
	0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x2e, 0x53, 0x6d,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x73, 0x6d, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x1a, 0x8e, 0x01, 0x0a, 0x07, 0x53, 0x6d, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x38, 0x0a, 0x0a, 0x73, 0x6d, 0x73, 0x5f, 0x61, 0x63, 0x6b, 0x5f, 0x61, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x08, 0x73, 0x6d, 0x73, 0x41, 0x63, 0x6b, 0x41, 0x74, 0x12, 0x49, 0x0a, 0x13, 0x73, 0x6d,
	0x73, 0x5f, 0x61, 0x63, 0x6b, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x10, 0x73, 0x6d, 0x73, 0x41, 0x63, 0x6b, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x41, 0x74, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_auth_internal_dev_reg_attempt_proto_rawDescOnce sync.Once
	file_api_auth_internal_dev_reg_attempt_proto_rawDescData = file_api_auth_internal_dev_reg_attempt_proto_rawDesc
)

func file_api_auth_internal_dev_reg_attempt_proto_rawDescGZIP() []byte {
	file_api_auth_internal_dev_reg_attempt_proto_rawDescOnce.Do(func() {
		file_api_auth_internal_dev_reg_attempt_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_internal_dev_reg_attempt_proto_rawDescData)
	})
	return file_api_auth_internal_dev_reg_attempt_proto_rawDescData
}

var file_api_auth_internal_dev_reg_attempt_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_auth_internal_dev_reg_attempt_proto_goTypes = []interface{}{
	(*DeviceRegistrationAttempt)(nil),         // 0: auth.DeviceRegistrationAttempt
	(*DeviceRegistrationAttempt_SmsInfo)(nil), // 1: auth.DeviceRegistrationAttempt.SmsInfo
	(DeviceRegistrationStatus)(0),             // 2: auth.DeviceRegistrationStatus
	(*timestamppb.Timestamp)(nil),             // 3: google.protobuf.Timestamp
}
var file_api_auth_internal_dev_reg_attempt_proto_depIdxs = []int32{
	2, // 0: auth.DeviceRegistrationAttempt.Status:type_name -> auth.DeviceRegistrationStatus
	1, // 1: auth.DeviceRegistrationAttempt.sms_info:type_name -> auth.DeviceRegistrationAttempt.SmsInfo
	3, // 2: auth.DeviceRegistrationAttempt.created_at:type_name -> google.protobuf.Timestamp
	3, // 3: auth.DeviceRegistrationAttempt.updated_at:type_name -> google.protobuf.Timestamp
	3, // 4: auth.DeviceRegistrationAttempt.SmsInfo.sms_ack_at:type_name -> google.protobuf.Timestamp
	3, // 5: auth.DeviceRegistrationAttempt.SmsInfo.sms_ack_notified_at:type_name -> google.protobuf.Timestamp
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_auth_internal_dev_reg_attempt_proto_init() }
func file_api_auth_internal_dev_reg_attempt_proto_init() {
	if File_api_auth_internal_dev_reg_attempt_proto != nil {
		return
	}
	file_api_auth_device_registration_status_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_auth_internal_dev_reg_attempt_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceRegistrationAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_internal_dev_reg_attempt_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceRegistrationAttempt_SmsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_internal_dev_reg_attempt_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_internal_dev_reg_attempt_proto_goTypes,
		DependencyIndexes: file_api_auth_internal_dev_reg_attempt_proto_depIdxs,
		MessageInfos:      file_api_auth_internal_dev_reg_attempt_proto_msgTypes,
	}.Build()
	File_api_auth_internal_dev_reg_attempt_proto = out.File
	file_api_auth_internal_dev_reg_attempt_proto_rawDesc = nil
	file_api_auth_internal_dev_reg_attempt_proto_goTypes = nil
	file_api_auth_internal_dev_reg_attempt_proto_depIdxs = nil
}
