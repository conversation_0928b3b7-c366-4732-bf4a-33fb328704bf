// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/service.proto

package auth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	afu "github.com/epifi/gamma/api/auth/afu"

	card "github.com/epifi/gamma/api/card"

	common "github.com/epifi/be-common/api/typesv2/common"

	comms "github.com/epifi/gamma/api/comms"

	consent "github.com/epifi/gamma/api/consent"

	typesv2 "github.com/epifi/gamma/api/typesv2"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = afu.AuthFactor(0)

	_ = card.CardForm(0)

	_ = common.BooleanEnum(0)

	_ = comms.Medium(0)

	_ = consent.ConsentType(0)

	_ = typesv2.DeviceIntegrityTokenType(0)

	_ = vendorgateway.Vendor(0)
)

// define the regex for a UUID once up-front
var _service_uuidPattern = regexp.MustCompile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")

// Validate checks the field values on GetTokenDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTokenDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTokenDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTokenDetailsRequestMultiError, or nil if none found.
func (m *GetTokenDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTokenDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Limit

	// no validation rules for IncludeDeleted

	switch v := m.IdentifierOneof.(type) {
	case *GetTokenDetailsRequest_PhoneNumber:
		if v == nil {
			err := GetTokenDetailsRequestValidationError{
				field:  "IdentifierOneof",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPhoneNumber()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTokenDetailsRequestValidationError{
						field:  "PhoneNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTokenDetailsRequestValidationError{
						field:  "PhoneNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTokenDetailsRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetTokenDetailsRequest_ActorId:
		if v == nil {
			err := GetTokenDetailsRequestValidationError{
				field:  "IdentifierOneof",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetTokenDetailsRequestMultiError(errors)
	}

	return nil
}

// GetTokenDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetTokenDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTokenDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTokenDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTokenDetailsRequestMultiError) AllErrors() []error { return m }

// GetTokenDetailsRequestValidationError is the validation error returned by
// GetTokenDetailsRequest.Validate if the designated constraints aren't met.
type GetTokenDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTokenDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTokenDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTokenDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTokenDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTokenDetailsRequestValidationError) ErrorName() string {
	return "GetTokenDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTokenDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTokenDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTokenDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTokenDetailsRequestValidationError{}

// Validate checks the field values on GetTokenDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTokenDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTokenDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTokenDetailsResponseMultiError, or nil if none found.
func (m *GetTokenDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTokenDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTokenDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTokenDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTokenDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTokenDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTokenDetailsResponseValidationError{
						field:  fmt.Sprintf("TokenDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTokenDetailsResponseValidationError{
						field:  fmt.Sprintf("TokenDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTokenDetailsResponseValidationError{
					field:  fmt.Sprintf("TokenDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTokenDetailsResponseMultiError(errors)
	}

	return nil
}

// GetTokenDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetTokenDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTokenDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTokenDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTokenDetailsResponseMultiError) AllErrors() []error { return m }

// GetTokenDetailsResponseValidationError is the validation error returned by
// GetTokenDetailsResponse.Validate if the designated constraints aren't met.
type GetTokenDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTokenDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTokenDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTokenDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTokenDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTokenDetailsResponseValidationError) ErrorName() string {
	return "GetTokenDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTokenDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTokenDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTokenDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTokenDetailsResponseValidationError{}

// Validate checks the field values on UserProfile with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserProfile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserProfile with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserProfileMultiError, or
// nil if none found.
func (m *UserProfile) ValidateAll() error {
	return m.validate(true)
}

func (m *UserProfile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserProfileValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserProfileValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserProfileValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for Locale

	if len(errors) > 0 {
		return UserProfileMultiError(errors)
	}

	return nil
}

// UserProfileMultiError is an error wrapping multiple validation errors
// returned by UserProfile.ValidateAll() if the designated constraints aren't met.
type UserProfileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserProfileMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserProfileMultiError) AllErrors() []error { return m }

// UserProfileValidationError is the validation error returned by
// UserProfile.Validate if the designated constraints aren't met.
type UserProfileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserProfileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserProfileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserProfileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserProfileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserProfileValidationError) ErrorName() string { return "UserProfileValidationError" }

// Error satisfies the builtin error interface
func (e UserProfileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserProfile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserProfileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserProfileValidationError{}

// Validate checks the field values on GenerateOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOtpRequestMultiError, or nil if none found.
func (m *GenerateOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetDevice() == nil {
		err := GenerateOtpRequestValidationError{
			field:  "Device",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if !_GenerateOtpRequest_Token_Pattern.MatchString(m.GetToken()) {
		err := GenerateOtpRequestValidationError{
			field:  "Token",
			reason: "value does not match regex pattern \"(^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[8|9|aA|bB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$|^$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for GenerateOtpFlow

	// no validation rules for Email

	switch v := m.OtpOptions.(type) {
	case *GenerateOtpRequest_MfWithdrawalData:
		if v == nil {
			err := GenerateOtpRequestValidationError{
				field:  "OtpOptions",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMfWithdrawalData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateOtpRequestValidationError{
						field:  "MfWithdrawalData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateOtpRequestValidationError{
						field:  "MfWithdrawalData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMfWithdrawalData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateOtpRequestValidationError{
					field:  "MfWithdrawalData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GenerateOtpRequest_WealthAccountNomineeDeclarationData:
		if v == nil {
			err := GenerateOtpRequestValidationError{
				field:  "OtpOptions",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWealthAccountNomineeDeclarationData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateOtpRequestValidationError{
						field:  "WealthAccountNomineeDeclarationData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateOtpRequestValidationError{
						field:  "WealthAccountNomineeDeclarationData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWealthAccountNomineeDeclarationData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateOtpRequestValidationError{
					field:  "WealthAccountNomineeDeclarationData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GenerateOtpRequest_MfOneTimeBuyData:
		if v == nil {
			err := GenerateOtpRequestValidationError{
				field:  "OtpOptions",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMfOneTimeBuyData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateOtpRequestValidationError{
						field:  "MfOneTimeBuyData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateOtpRequestValidationError{
						field:  "MfOneTimeBuyData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMfOneTimeBuyData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateOtpRequestValidationError{
					field:  "MfOneTimeBuyData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GenerateOtpRequest_MfRegisterSipData:
		if v == nil {
			err := GenerateOtpRequestValidationError{
				field:  "OtpOptions",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMfRegisterSipData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateOtpRequestValidationError{
						field:  "MfRegisterSipData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateOtpRequestValidationError{
						field:  "MfRegisterSipData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMfRegisterSipData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateOtpRequestValidationError{
					field:  "MfRegisterSipData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GenerateOtpRequestMultiError(errors)
	}

	return nil
}

// GenerateOtpRequestMultiError is an error wrapping multiple validation errors
// returned by GenerateOtpRequest.ValidateAll() if the designated constraints
// aren't met.
type GenerateOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOtpRequestMultiError) AllErrors() []error { return m }

// GenerateOtpRequestValidationError is the validation error returned by
// GenerateOtpRequest.Validate if the designated constraints aren't met.
type GenerateOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOtpRequestValidationError) ErrorName() string {
	return "GenerateOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOtpRequestValidationError{}

var _GenerateOtpRequest_Token_Pattern = regexp.MustCompile("(^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[8|9|aA|bB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$|^$)")

// Validate checks the field values on MutualFundWithdrawalData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MutualFundWithdrawalData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MutualFundWithdrawalData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MutualFundWithdrawalDataMultiError, or nil if none found.
func (m *MutualFundWithdrawalData) ValidateAll() error {
	return m.validate(true)
}

func (m *MutualFundWithdrawalData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MutualFundWithdrawalDataValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MutualFundWithdrawalDataValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MutualFundWithdrawalDataValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MutualFundName

	if len(errors) > 0 {
		return MutualFundWithdrawalDataMultiError(errors)
	}

	return nil
}

// MutualFundWithdrawalDataMultiError is an error wrapping multiple validation
// errors returned by MutualFundWithdrawalData.ValidateAll() if the designated
// constraints aren't met.
type MutualFundWithdrawalDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MutualFundWithdrawalDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MutualFundWithdrawalDataMultiError) AllErrors() []error { return m }

// MutualFundWithdrawalDataValidationError is the validation error returned by
// MutualFundWithdrawalData.Validate if the designated constraints aren't met.
type MutualFundWithdrawalDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MutualFundWithdrawalDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MutualFundWithdrawalDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MutualFundWithdrawalDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MutualFundWithdrawalDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MutualFundWithdrawalDataValidationError) ErrorName() string {
	return "MutualFundWithdrawalDataValidationError"
}

// Error satisfies the builtin error interface
func (e MutualFundWithdrawalDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMutualFundWithdrawalData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MutualFundWithdrawalDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MutualFundWithdrawalDataValidationError{}

// Validate checks the field values on WealthAccountNomineeDeclarationData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *WealthAccountNomineeDeclarationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WealthAccountNomineeDeclarationData
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// WealthAccountNomineeDeclarationDataMultiError, or nil if none found.
func (m *WealthAccountNomineeDeclarationData) ValidateAll() error {
	return m.validate(true)
}

func (m *WealthAccountNomineeDeclarationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _WealthAccountNomineeDeclarationData_OptIn_NotInLookup[m.GetOptIn()]; ok {
		err := WealthAccountNomineeDeclarationDataValidationError{
			field:  "OptIn",
			reason: "value must not be in list [BOOLEAN_ENUM_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return WealthAccountNomineeDeclarationDataMultiError(errors)
	}

	return nil
}

// WealthAccountNomineeDeclarationDataMultiError is an error wrapping multiple
// validation errors returned by
// WealthAccountNomineeDeclarationData.ValidateAll() if the designated
// constraints aren't met.
type WealthAccountNomineeDeclarationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WealthAccountNomineeDeclarationDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WealthAccountNomineeDeclarationDataMultiError) AllErrors() []error { return m }

// WealthAccountNomineeDeclarationDataValidationError is the validation error
// returned by WealthAccountNomineeDeclarationData.Validate if the designated
// constraints aren't met.
type WealthAccountNomineeDeclarationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WealthAccountNomineeDeclarationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WealthAccountNomineeDeclarationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WealthAccountNomineeDeclarationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WealthAccountNomineeDeclarationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WealthAccountNomineeDeclarationDataValidationError) ErrorName() string {
	return "WealthAccountNomineeDeclarationDataValidationError"
}

// Error satisfies the builtin error interface
func (e WealthAccountNomineeDeclarationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWealthAccountNomineeDeclarationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WealthAccountNomineeDeclarationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WealthAccountNomineeDeclarationDataValidationError{}

var _WealthAccountNomineeDeclarationData_OptIn_NotInLookup = map[common.BooleanEnum]struct{}{
	0: {},
}

// Validate checks the field values on MutualFundOneTimeBuyData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MutualFundOneTimeBuyData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MutualFundOneTimeBuyData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MutualFundOneTimeBuyDataMultiError, or nil if none found.
func (m *MutualFundOneTimeBuyData) ValidateAll() error {
	return m.validate(true)
}

func (m *MutualFundOneTimeBuyData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MutualFundOneTimeBuyDataValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MutualFundOneTimeBuyDataValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MutualFundOneTimeBuyDataValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MutualFundName

	if len(errors) > 0 {
		return MutualFundOneTimeBuyDataMultiError(errors)
	}

	return nil
}

// MutualFundOneTimeBuyDataMultiError is an error wrapping multiple validation
// errors returned by MutualFundOneTimeBuyData.ValidateAll() if the designated
// constraints aren't met.
type MutualFundOneTimeBuyDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MutualFundOneTimeBuyDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MutualFundOneTimeBuyDataMultiError) AllErrors() []error { return m }

// MutualFundOneTimeBuyDataValidationError is the validation error returned by
// MutualFundOneTimeBuyData.Validate if the designated constraints aren't met.
type MutualFundOneTimeBuyDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MutualFundOneTimeBuyDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MutualFundOneTimeBuyDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MutualFundOneTimeBuyDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MutualFundOneTimeBuyDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MutualFundOneTimeBuyDataValidationError) ErrorName() string {
	return "MutualFundOneTimeBuyDataValidationError"
}

// Error satisfies the builtin error interface
func (e MutualFundOneTimeBuyDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMutualFundOneTimeBuyData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MutualFundOneTimeBuyDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MutualFundOneTimeBuyDataValidationError{}

// Validate checks the field values on MutualFundRegisterSIPData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MutualFundRegisterSIPData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MutualFundRegisterSIPData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MutualFundRegisterSIPDataMultiError, or nil if none found.
func (m *MutualFundRegisterSIPData) ValidateAll() error {
	return m.validate(true)
}

func (m *MutualFundRegisterSIPData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MutualFundRegisterSIPDataValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MutualFundRegisterSIPDataValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MutualFundRegisterSIPDataValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MutualFundName

	// no validation rules for Frequency

	if len(errors) > 0 {
		return MutualFundRegisterSIPDataMultiError(errors)
	}

	return nil
}

// MutualFundRegisterSIPDataMultiError is an error wrapping multiple validation
// errors returned by MutualFundRegisterSIPData.ValidateAll() if the
// designated constraints aren't met.
type MutualFundRegisterSIPDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MutualFundRegisterSIPDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MutualFundRegisterSIPDataMultiError) AllErrors() []error { return m }

// MutualFundRegisterSIPDataValidationError is the validation error returned by
// MutualFundRegisterSIPData.Validate if the designated constraints aren't met.
type MutualFundRegisterSIPDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MutualFundRegisterSIPDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MutualFundRegisterSIPDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MutualFundRegisterSIPDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MutualFundRegisterSIPDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MutualFundRegisterSIPDataValidationError) ErrorName() string {
	return "MutualFundRegisterSIPDataValidationError"
}

// Error satisfies the builtin error interface
func (e MutualFundRegisterSIPDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMutualFundRegisterSIPData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MutualFundRegisterSIPDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MutualFundRegisterSIPDataValidationError{}

// Validate checks the field values on GenerateOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOtpResponseMultiError, or nil if none found.
func (m *GenerateOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	// no validation rules for RetryTimerSeconds

	// no validation rules for Otp

	if len(errors) > 0 {
		return GenerateOtpResponseMultiError(errors)
	}

	return nil
}

// GenerateOtpResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateOtpResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOtpResponseMultiError) AllErrors() []error { return m }

// GenerateOtpResponseValidationError is the validation error returned by
// GenerateOtpResponse.Validate if the designated constraints aren't met.
type GenerateOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOtpResponseValidationError) ErrorName() string {
	return "GenerateOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOtpResponseValidationError{}

// Validate checks the field values on VerifyOtpRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyOtpRequestMultiError, or nil if none found.
func (m *VerifyOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetDevice() == nil {
		err := VerifyOtpRequestValidationError{
			field:  "Device",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if err := m._validateUuid(m.GetToken()); err != nil {
		err = VerifyOtpRequestValidationError{
			field:  "Token",
			reason: "value must be a valid UUID",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOtp()) != 6 {
		err := VerifyOtpRequestValidationError{
			field:  "Otp",
			reason: "value length must be 6 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)

	}

	if !_VerifyOtpRequest_Otp_Pattern.MatchString(m.GetOtp()) {
		err := VerifyOtpRequestValidationError{
			field:  "Otp",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SkipUserRevokeStateCheck

	// no validation rules for Email

	// no validation rules for SkipTokenVerification

	// no validation rules for SkipDeviceMatch

	if len(errors) > 0 {
		return VerifyOtpRequestMultiError(errors)
	}

	return nil
}

func (m *VerifyOtpRequest) _validateUuid(uuid string) error {
	if matched := _service_uuidPattern.MatchString(uuid); !matched {
		return errors.New("invalid uuid format")
	}

	return nil
}

// VerifyOtpRequestMultiError is an error wrapping multiple validation errors
// returned by VerifyOtpRequest.ValidateAll() if the designated constraints
// aren't met.
type VerifyOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyOtpRequestMultiError) AllErrors() []error { return m }

// VerifyOtpRequestValidationError is the validation error returned by
// VerifyOtpRequest.Validate if the designated constraints aren't met.
type VerifyOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyOtpRequestValidationError) ErrorName() string { return "VerifyOtpRequestValidationError" }

// Error satisfies the builtin error interface
func (e VerifyOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyOtpRequestValidationError{}

var _VerifyOtpRequest_Otp_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on VerifyOtpResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyOtpResponseMultiError, or nil if none found.
func (m *VerifyOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyOtpResponseMultiError(errors)
	}

	return nil
}

// VerifyOtpResponseMultiError is an error wrapping multiple validation errors
// returned by VerifyOtpResponse.ValidateAll() if the designated constraints
// aren't met.
type VerifyOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyOtpResponseMultiError) AllErrors() []error { return m }

// VerifyOtpResponseValidationError is the validation error returned by
// VerifyOtpResponse.Validate if the designated constraints aren't met.
type VerifyOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyOtpResponseValidationError) ErrorName() string {
	return "VerifyOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyOtpResponseValidationError{}

// Validate checks the field values on CreateTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTokenRequestMultiError, or nil if none found.
func (m *CreateTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TokenType

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTokenRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTokenRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTokenRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTokenRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTokenRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTokenRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for AuthFactorUpdateId

	if all {
		switch v := interface{}(m.GetAccessLevelFlags()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTokenRequestValidationError{
					field:  "AccessLevelFlags",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTokenRequestValidationError{
					field:  "AccessLevelFlags",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccessLevelFlags()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTokenRequestValidationError{
				field:  "AccessLevelFlags",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for IsNonResidentUser

	if len(errors) > 0 {
		return CreateTokenRequestMultiError(errors)
	}

	return nil
}

// CreateTokenRequestMultiError is an error wrapping multiple validation errors
// returned by CreateTokenRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTokenRequestMultiError) AllErrors() []error { return m }

// CreateTokenRequestValidationError is the validation error returned by
// CreateTokenRequest.Validate if the designated constraints aren't met.
type CreateTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTokenRequestValidationError) ErrorName() string {
	return "CreateTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTokenRequestValidationError{}

// Validate checks the field values on AccessLevelFlags with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccessLevelFlags) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccessLevelFlags with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccessLevelFlagsMultiError, or nil if none found.
func (m *AccessLevelFlags) ValidateAll() error {
	return m.validate(true)
}

func (m *AccessLevelFlags) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsAccessRevoked

	// no validation rules for IsAccountCreated

	if len(errors) > 0 {
		return AccessLevelFlagsMultiError(errors)
	}

	return nil
}

// AccessLevelFlagsMultiError is an error wrapping multiple validation errors
// returned by AccessLevelFlags.ValidateAll() if the designated constraints
// aren't met.
type AccessLevelFlagsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccessLevelFlagsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccessLevelFlagsMultiError) AllErrors() []error { return m }

// AccessLevelFlagsValidationError is the validation error returned by
// AccessLevelFlags.Validate if the designated constraints aren't met.
type AccessLevelFlagsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccessLevelFlagsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccessLevelFlagsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccessLevelFlagsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccessLevelFlagsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccessLevelFlagsValidationError) ErrorName() string { return "AccessLevelFlagsValidationError" }

// Error satisfies the builtin error interface
func (e AccessLevelFlagsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccessLevelFlags.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccessLevelFlagsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccessLevelFlagsValidationError{}

// Validate checks the field values on CreateTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTokenResponseMultiError, or nil if none found.
func (m *CreateTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	// no validation rules for DeviceRegStatus

	if all {
		switch v := interface{}(m.GetTokenMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTokenResponseValidationError{
					field:  "TokenMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTokenResponseValidationError{
					field:  "TokenMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTokenMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTokenResponseValidationError{
				field:  "TokenMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateTokenResponseMultiError(errors)
	}

	return nil
}

// CreateTokenResponseMultiError is an error wrapping multiple validation
// errors returned by CreateTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTokenResponseMultiError) AllErrors() []error { return m }

// CreateTokenResponseValidationError is the validation error returned by
// CreateTokenResponse.Validate if the designated constraints aren't met.
type CreateTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTokenResponseValidationError) ErrorName() string {
	return "CreateTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTokenResponseValidationError{}

// Validate checks the field values on TokenMetadata with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TokenMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TokenMetadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TokenMetadataMultiError, or
// nil if none found.
func (m *TokenMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *TokenMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeviceRegStatus

	// no validation rules for IsDeviceIntegrityLinkedToAccessToken

	if len(errors) > 0 {
		return TokenMetadataMultiError(errors)
	}

	return nil
}

// TokenMetadataMultiError is an error wrapping multiple validation errors
// returned by TokenMetadata.ValidateAll() if the designated constraints
// aren't met.
type TokenMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TokenMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TokenMetadataMultiError) AllErrors() []error { return m }

// TokenMetadataValidationError is the validation error returned by
// TokenMetadata.Validate if the designated constraints aren't met.
type TokenMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TokenMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TokenMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TokenMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TokenMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TokenMetadataValidationError) ErrorName() string { return "TokenMetadataValidationError" }

// Error satisfies the builtin error interface
func (e TokenMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTokenMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TokenMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TokenMetadataValidationError{}

// Validate checks the field values on ValidateTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateTokenRequestMultiError, or nil if none found.
func (m *ValidateTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	// no validation rules for TokenType

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateTokenRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateTokenRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateTokenRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateTokenRequestMultiError(errors)
	}

	return nil
}

// ValidateTokenRequestMultiError is an error wrapping multiple validation
// errors returned by ValidateTokenRequest.ValidateAll() if the designated
// constraints aren't met.
type ValidateTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateTokenRequestMultiError) AllErrors() []error { return m }

// ValidateTokenRequestValidationError is the validation error returned by
// ValidateTokenRequest.Validate if the designated constraints aren't met.
type ValidateTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateTokenRequestValidationError) ErrorName() string {
	return "ValidateTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateTokenRequestValidationError{}

// Validate checks the field values on ValidateTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateTokenResponseMultiError, or nil if none found.
func (m *ValidateTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateTokenResponseValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateTokenResponseValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateTokenResponseValidationError{
				field:  "Actor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateTokenResponseValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateTokenResponseValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateTokenResponseValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeviceRegStatus

	// no validation rules for AuthFactorUpdateId

	// no validation rules for DeviceIntegrityId

	// no validation rules for DeviceIntegrityResult

	// no validation rules for ActorId

	// no validation rules for IsSavingsAccountCreated

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetTokenDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateTokenResponseValidationError{
					field:  "TokenDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateTokenResponseValidationError{
					field:  "TokenDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTokenDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateTokenResponseValidationError{
				field:  "TokenDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsNonResidentUser

	if len(errors) > 0 {
		return ValidateTokenResponseMultiError(errors)
	}

	return nil
}

// ValidateTokenResponseMultiError is an error wrapping multiple validation
// errors returned by ValidateTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type ValidateTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateTokenResponseMultiError) AllErrors() []error { return m }

// ValidateTokenResponseValidationError is the validation error returned by
// ValidateTokenResponse.Validate if the designated constraints aren't met.
type ValidateTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateTokenResponseValidationError) ErrorName() string {
	return "ValidateTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateTokenResponseValidationError{}

// Validate checks the field values on SignOutRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SignOutRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SignOutRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SignOutRequestMultiError,
// or nil if none found.
func (m *SignOutRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SignOutRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetActor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SignOutRequestValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SignOutRequestValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SignOutRequestValidationError{
				field:  "Actor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SignOutRequestMultiError(errors)
	}

	return nil
}

// SignOutRequestMultiError is an error wrapping multiple validation errors
// returned by SignOutRequest.ValidateAll() if the designated constraints
// aren't met.
type SignOutRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SignOutRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SignOutRequestMultiError) AllErrors() []error { return m }

// SignOutRequestValidationError is the validation error returned by
// SignOutRequest.Validate if the designated constraints aren't met.
type SignOutRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SignOutRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SignOutRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SignOutRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SignOutRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SignOutRequestValidationError) ErrorName() string { return "SignOutRequestValidationError" }

// Error satisfies the builtin error interface
func (e SignOutRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSignOutRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SignOutRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SignOutRequestValidationError{}

// Validate checks the field values on SignOutResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SignOutResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SignOutResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SignOutResponseMultiError, or nil if none found.
func (m *SignOutResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SignOutResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SignOutResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SignOutResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SignOutResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SignOutResponseMultiError(errors)
	}

	return nil
}

// SignOutResponseMultiError is an error wrapping multiple validation errors
// returned by SignOutResponse.ValidateAll() if the designated constraints
// aren't met.
type SignOutResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SignOutResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SignOutResponseMultiError) AllErrors() []error { return m }

// SignOutResponseValidationError is the validation error returned by
// SignOutResponse.Validate if the designated constraints aren't met.
type SignOutResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SignOutResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SignOutResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SignOutResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SignOutResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SignOutResponseValidationError) ErrorName() string { return "SignOutResponseValidationError" }

// Error satisfies the builtin error interface
func (e SignOutResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSignOutResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SignOutResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SignOutResponseValidationError{}

// Validate checks the field values on ValidateOAuthTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateOAuthTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateOAuthTokenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateOAuthTokenRequestMultiError, or nil if none found.
func (m *ValidateOAuthTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateOAuthTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OauthProvider

	// no validation rules for OauthToken

	// no validation rules for PhoneNumber

	// no validation rules for DeviceId

	if len(errors) > 0 {
		return ValidateOAuthTokenRequestMultiError(errors)
	}

	return nil
}

// ValidateOAuthTokenRequestMultiError is an error wrapping multiple validation
// errors returned by ValidateOAuthTokenRequest.ValidateAll() if the
// designated constraints aren't met.
type ValidateOAuthTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateOAuthTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateOAuthTokenRequestMultiError) AllErrors() []error { return m }

// ValidateOAuthTokenRequestValidationError is the validation error returned by
// ValidateOAuthTokenRequest.Validate if the designated constraints aren't met.
type ValidateOAuthTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateOAuthTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateOAuthTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateOAuthTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateOAuthTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateOAuthTokenRequestValidationError) ErrorName() string {
	return "ValidateOAuthTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateOAuthTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateOAuthTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateOAuthTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateOAuthTokenRequestValidationError{}

// Validate checks the field values on ValidateOAuthTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateOAuthTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateOAuthTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateOAuthTokenResponseMultiError, or nil if none found.
func (m *ValidateOAuthTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateOAuthTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateOAuthTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateOAuthTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateOAuthTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserProfile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateOAuthTokenResponseValidationError{
					field:  "UserProfile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateOAuthTokenResponseValidationError{
					field:  "UserProfile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserProfile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateOAuthTokenResponseValidationError{
				field:  "UserProfile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateOAuthTokenResponseMultiError(errors)
	}

	return nil
}

// ValidateOAuthTokenResponseMultiError is an error wrapping multiple
// validation errors returned by ValidateOAuthTokenResponse.ValidateAll() if
// the designated constraints aren't met.
type ValidateOAuthTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateOAuthTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateOAuthTokenResponseMultiError) AllErrors() []error { return m }

// ValidateOAuthTokenResponseValidationError is the validation error returned
// by ValidateOAuthTokenResponse.Validate if the designated constraints aren't met.
type ValidateOAuthTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateOAuthTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateOAuthTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateOAuthTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateOAuthTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateOAuthTokenResponseValidationError) ErrorName() string {
	return "ValidateOAuthTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateOAuthTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateOAuthTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateOAuthTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateOAuthTokenResponseValidationError{}

// Validate checks the field values on RegisterDeviceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterDeviceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterDeviceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterDeviceRequestMultiError, or nil if none found.
func (m *RegisterDeviceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterDeviceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterDeviceRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterDeviceRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterDeviceRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterDeviceRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterDeviceRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterDeviceRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for Payload

	// no validation rules for ActorId

	// no validation rules for AccessToken

	if all {
		switch v := interface{}(m.GetRecipientPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterDeviceRequestValidationError{
					field:  "RecipientPhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterDeviceRequestValidationError{
					field:  "RecipientPhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecipientPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterDeviceRequestValidationError{
				field:  "RecipientPhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SimId

	if len(errors) > 0 {
		return RegisterDeviceRequestMultiError(errors)
	}

	return nil
}

// RegisterDeviceRequestMultiError is an error wrapping multiple validation
// errors returned by RegisterDeviceRequest.ValidateAll() if the designated
// constraints aren't met.
type RegisterDeviceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterDeviceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterDeviceRequestMultiError) AllErrors() []error { return m }

// RegisterDeviceRequestValidationError is the validation error returned by
// RegisterDeviceRequest.Validate if the designated constraints aren't met.
type RegisterDeviceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterDeviceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterDeviceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterDeviceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterDeviceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterDeviceRequestValidationError) ErrorName() string {
	return "RegisterDeviceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterDeviceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterDeviceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterDeviceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterDeviceRequestValidationError{}

// Validate checks the field values on RegisterDeviceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterDeviceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterDeviceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterDeviceResponseMultiError, or nil if none found.
func (m *RegisterDeviceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterDeviceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterDeviceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterDeviceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterDeviceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeviceToken

	if len(errors) > 0 {
		return RegisterDeviceResponseMultiError(errors)
	}

	return nil
}

// RegisterDeviceResponseMultiError is an error wrapping multiple validation
// errors returned by RegisterDeviceResponse.ValidateAll() if the designated
// constraints aren't met.
type RegisterDeviceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterDeviceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterDeviceResponseMultiError) AllErrors() []error { return m }

// RegisterDeviceResponseValidationError is the validation error returned by
// RegisterDeviceResponse.Validate if the designated constraints aren't met.
type RegisterDeviceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterDeviceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterDeviceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterDeviceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterDeviceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterDeviceResponseValidationError) ErrorName() string {
	return "RegisterDeviceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterDeviceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterDeviceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterDeviceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterDeviceResponseValidationError{}

// Validate checks the field values on GetDeviceBindingSMSRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeviceBindingSMSRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceBindingSMSRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeviceBindingSMSRequestMultiError, or nil if none found.
func (m *GetDeviceBindingSMSRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceBindingSMSRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeviceId

	// no validation rules for IsAuthFactorUpdate

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetDeviceBindingSMSRequestMultiError(errors)
	}

	return nil
}

// GetDeviceBindingSMSRequestMultiError is an error wrapping multiple
// validation errors returned by GetDeviceBindingSMSRequest.ValidateAll() if
// the designated constraints aren't met.
type GetDeviceBindingSMSRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceBindingSMSRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceBindingSMSRequestMultiError) AllErrors() []error { return m }

// GetDeviceBindingSMSRequestValidationError is the validation error returned
// by GetDeviceBindingSMSRequest.Validate if the designated constraints aren't met.
type GetDeviceBindingSMSRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceBindingSMSRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceBindingSMSRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceBindingSMSRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceBindingSMSRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceBindingSMSRequestValidationError) ErrorName() string {
	return "GetDeviceBindingSMSRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeviceBindingSMSRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceBindingSMSRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceBindingSMSRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceBindingSMSRequestValidationError{}

// Validate checks the field values on GetDeviceBindingSMSResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeviceBindingSMSResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceBindingSMSResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeviceBindingSMSResponseMultiError, or nil if none found.
func (m *GetDeviceBindingSMSResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceBindingSMSResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Payload

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceBindingSMSResponseValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceBindingSMSResponseValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceBindingSMSResponseValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceBindingSMSResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceBindingSMSResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceBindingSMSResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDeviceBindingSMSResponseMultiError(errors)
	}

	return nil
}

// GetDeviceBindingSMSResponseMultiError is an error wrapping multiple
// validation errors returned by GetDeviceBindingSMSResponse.ValidateAll() if
// the designated constraints aren't met.
type GetDeviceBindingSMSResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceBindingSMSResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceBindingSMSResponseMultiError) AllErrors() []error { return m }

// GetDeviceBindingSMSResponseValidationError is the validation error returned
// by GetDeviceBindingSMSResponse.Validate if the designated constraints
// aren't met.
type GetDeviceBindingSMSResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceBindingSMSResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceBindingSMSResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceBindingSMSResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceBindingSMSResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceBindingSMSResponseValidationError) ErrorName() string {
	return "GetDeviceBindingSMSResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeviceBindingSMSResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceBindingSMSResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceBindingSMSResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceBindingSMSResponseValidationError{}

// Validate checks the field values on GetDeviceAuthRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeviceAuthRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceAuthRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeviceAuthRequestMultiError, or nil if none found.
func (m *GetDeviceAuthRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceAuthRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceAuthRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceAuthRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceAuthRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	if len(errors) > 0 {
		return GetDeviceAuthRequestMultiError(errors)
	}

	return nil
}

// GetDeviceAuthRequestMultiError is an error wrapping multiple validation
// errors returned by GetDeviceAuthRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDeviceAuthRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceAuthRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceAuthRequestMultiError) AllErrors() []error { return m }

// GetDeviceAuthRequestValidationError is the validation error returned by
// GetDeviceAuthRequest.Validate if the designated constraints aren't met.
type GetDeviceAuthRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceAuthRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceAuthRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceAuthRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceAuthRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceAuthRequestValidationError) ErrorName() string {
	return "GetDeviceAuthRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeviceAuthRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceAuthRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceAuthRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceAuthRequestValidationError{}

// Validate checks the field values on GetDeviceAuthResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeviceAuthResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceAuthResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeviceAuthResponseMultiError, or nil if none found.
func (m *GetDeviceAuthResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceAuthResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceAuthResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceAuthResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceAuthResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for UserProfileId

	// no validation rules for DeviceToken

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceAuthResponseValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceAuthResponseValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceAuthResponseValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeviceRegStatus

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceAuthResponseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceAuthResponseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceAuthResponseValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceAuthResponseValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceAuthResponseValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceAuthResponseValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SimId

	// no validation rules for VendorDeviceId

	if len(errors) > 0 {
		return GetDeviceAuthResponseMultiError(errors)
	}

	return nil
}

// GetDeviceAuthResponseMultiError is an error wrapping multiple validation
// errors returned by GetDeviceAuthResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDeviceAuthResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceAuthResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceAuthResponseMultiError) AllErrors() []error { return m }

// GetDeviceAuthResponseValidationError is the validation error returned by
// GetDeviceAuthResponse.Validate if the designated constraints aren't met.
type GetDeviceAuthResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceAuthResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceAuthResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceAuthResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceAuthResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceAuthResponseValidationError) ErrorName() string {
	return "GetDeviceAuthResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeviceAuthResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceAuthResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceAuthResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceAuthResponseValidationError{}

// Validate checks the field values on RegisterAuthFactorUpdateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterAuthFactorUpdateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterAuthFactorUpdateRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RegisterAuthFactorUpdateRequestMultiError, or nil if none found.
func (m *RegisterAuthFactorUpdateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterAuthFactorUpdateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := RegisterAuthFactorUpdateRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDevice() == nil {
		err := RegisterAuthFactorUpdateRequestValidationError{
			field:  "Device",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterAuthFactorUpdateRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterAuthFactorUpdateRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterAuthFactorUpdateRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterAuthFactorUpdateRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterAuthFactorUpdateRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterAuthFactorUpdateRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for CurrentEmail

	if all {
		switch v := interface{}(m.GetCurrentPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterAuthFactorUpdateRequestValidationError{
					field:  "CurrentPhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterAuthFactorUpdateRequestValidationError{
					field:  "CurrentPhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterAuthFactorUpdateRequestValidationError{
				field:  "CurrentPhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterAuthFactorUpdateRequestValidationError{
					field:  "CurrentDevice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterAuthFactorUpdateRequestValidationError{
					field:  "CurrentDevice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterAuthFactorUpdateRequestValidationError{
				field:  "CurrentDevice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsCardActive

	if len(errors) > 0 {
		return RegisterAuthFactorUpdateRequestMultiError(errors)
	}

	return nil
}

// RegisterAuthFactorUpdateRequestMultiError is an error wrapping multiple
// validation errors returned by RegisterAuthFactorUpdateRequest.ValidateAll()
// if the designated constraints aren't met.
type RegisterAuthFactorUpdateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterAuthFactorUpdateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterAuthFactorUpdateRequestMultiError) AllErrors() []error { return m }

// RegisterAuthFactorUpdateRequestValidationError is the validation error
// returned by RegisterAuthFactorUpdateRequest.Validate if the designated
// constraints aren't met.
type RegisterAuthFactorUpdateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterAuthFactorUpdateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterAuthFactorUpdateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterAuthFactorUpdateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterAuthFactorUpdateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterAuthFactorUpdateRequestValidationError) ErrorName() string {
	return "RegisterAuthFactorUpdateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterAuthFactorUpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterAuthFactorUpdateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterAuthFactorUpdateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterAuthFactorUpdateRequestValidationError{}

// Validate checks the field values on RegisterAuthFactorUpdateResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RegisterAuthFactorUpdateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterAuthFactorUpdateResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RegisterAuthFactorUpdateResponseMultiError, or nil if none found.
func (m *RegisterAuthFactorUpdateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterAuthFactorUpdateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterAuthFactorUpdateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterAuthFactorUpdateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterAuthFactorUpdateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Id

	// no validation rules for NextAction

	if all {
		switch v := interface{}(m.GetNextActionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterAuthFactorUpdateResponseValidationError{
					field:  "NextActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterAuthFactorUpdateResponseValidationError{
					field:  "NextActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterAuthFactorUpdateResponseValidationError{
				field:  "NextActionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsAtmPinSetRequired

	if len(errors) > 0 {
		return RegisterAuthFactorUpdateResponseMultiError(errors)
	}

	return nil
}

// RegisterAuthFactorUpdateResponseMultiError is an error wrapping multiple
// validation errors returned by
// RegisterAuthFactorUpdateResponse.ValidateAll() if the designated
// constraints aren't met.
type RegisterAuthFactorUpdateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterAuthFactorUpdateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterAuthFactorUpdateResponseMultiError) AllErrors() []error { return m }

// RegisterAuthFactorUpdateResponseValidationError is the validation error
// returned by RegisterAuthFactorUpdateResponse.Validate if the designated
// constraints aren't met.
type RegisterAuthFactorUpdateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterAuthFactorUpdateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterAuthFactorUpdateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterAuthFactorUpdateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterAuthFactorUpdateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterAuthFactorUpdateResponseValidationError) ErrorName() string {
	return "RegisterAuthFactorUpdateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterAuthFactorUpdateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterAuthFactorUpdateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterAuthFactorUpdateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterAuthFactorUpdateResponseValidationError{}

// Validate checks the field values on GetAuthFactorUpdateStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAuthFactorUpdateStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthFactorUpdateStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAuthFactorUpdateStatusRequestMultiError, or nil if none found.
func (m *GetAuthFactorUpdateStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthFactorUpdateStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AuthFactorUpdateId

	if len(errors) > 0 {
		return GetAuthFactorUpdateStatusRequestMultiError(errors)
	}

	return nil
}

// GetAuthFactorUpdateStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAuthFactorUpdateStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAuthFactorUpdateStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthFactorUpdateStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthFactorUpdateStatusRequestMultiError) AllErrors() []error { return m }

// GetAuthFactorUpdateStatusRequestValidationError is the validation error
// returned by GetAuthFactorUpdateStatusRequest.Validate if the designated
// constraints aren't met.
type GetAuthFactorUpdateStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthFactorUpdateStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthFactorUpdateStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthFactorUpdateStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthFactorUpdateStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthFactorUpdateStatusRequestValidationError) ErrorName() string {
	return "GetAuthFactorUpdateStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthFactorUpdateStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthFactorUpdateStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthFactorUpdateStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthFactorUpdateStatusRequestValidationError{}

// Validate checks the field values on GetAuthFactorUpdateStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAuthFactorUpdateStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthFactorUpdateStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAuthFactorUpdateStatusResponseMultiError, or nil if none found.
func (m *GetAuthFactorUpdateStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthFactorUpdateStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAuthFactorUpdateStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAuthFactorUpdateStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAuthFactorUpdateStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OverallStatus

	// no validation rules for NextAction

	// no validation rules for FailureReason

	if all {
		switch v := interface{}(m.GetReRegistrationDeviceVendorInitStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAuthFactorUpdateStatusResponseValidationError{
					field:  "ReRegistrationDeviceVendorInitStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAuthFactorUpdateStatusResponseValidationError{
					field:  "ReRegistrationDeviceVendorInitStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReRegistrationDeviceVendorInitStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAuthFactorUpdateStatusResponseValidationError{
				field:  "ReRegistrationDeviceVendorInitStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsDeviceRegistrationRetryable

	// no validation rules for UpdateVendorState

	if all {
		switch v := interface{}(m.GetNextActionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAuthFactorUpdateStatusResponseValidationError{
					field:  "NextActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAuthFactorUpdateStatusResponseValidationError{
					field:  "NextActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAuthFactorUpdateStatusResponseValidationError{
				field:  "NextActionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAuthFactorUpdateStatusResponseMultiError(errors)
	}

	return nil
}

// GetAuthFactorUpdateStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetAuthFactorUpdateStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAuthFactorUpdateStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthFactorUpdateStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthFactorUpdateStatusResponseMultiError) AllErrors() []error { return m }

// GetAuthFactorUpdateStatusResponseValidationError is the validation error
// returned by GetAuthFactorUpdateStatusResponse.Validate if the designated
// constraints aren't met.
type GetAuthFactorUpdateStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthFactorUpdateStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthFactorUpdateStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthFactorUpdateStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthFactorUpdateStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthFactorUpdateStatusResponseValidationError) ErrorName() string {
	return "GetAuthFactorUpdateStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthFactorUpdateStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthFactorUpdateStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthFactorUpdateStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthFactorUpdateStatusResponseValidationError{}

// Validate checks the field values on UpdateAFUCredentialStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateAFUCredentialStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAFUCredentialStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateAFUCredentialStatusRequestMultiError, or nil if none found.
func (m *UpdateAFUCredentialStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAFUCredentialStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AuthFactorUpdateId

	if all {
		switch v := interface{}(m.GetCredentialStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAFUCredentialStatusRequestValidationError{
					field:  "CredentialStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAFUCredentialStatusRequestValidationError{
					field:  "CredentialStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCredentialStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAFUCredentialStatusRequestValidationError{
				field:  "CredentialStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateAFUCredentialStatusRequestMultiError(errors)
	}

	return nil
}

// UpdateAFUCredentialStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateAFUCredentialStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateAFUCredentialStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAFUCredentialStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAFUCredentialStatusRequestMultiError) AllErrors() []error { return m }

// UpdateAFUCredentialStatusRequestValidationError is the validation error
// returned by UpdateAFUCredentialStatusRequest.Validate if the designated
// constraints aren't met.
type UpdateAFUCredentialStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAFUCredentialStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAFUCredentialStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAFUCredentialStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAFUCredentialStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAFUCredentialStatusRequestValidationError) ErrorName() string {
	return "UpdateAFUCredentialStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAFUCredentialStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAFUCredentialStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAFUCredentialStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAFUCredentialStatusRequestValidationError{}

// Validate checks the field values on UpdateAFUCredentialStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateAFUCredentialStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAFUCredentialStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateAFUCredentialStatusResponseMultiError, or nil if none found.
func (m *UpdateAFUCredentialStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAFUCredentialStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAFUCredentialStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAFUCredentialStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAFUCredentialStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OverallStatus

	// no validation rules for NextAction

	if all {
		switch v := interface{}(m.GetNextActionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAFUCredentialStatusResponseValidationError{
					field:  "NextActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAFUCredentialStatusResponseValidationError{
					field:  "NextActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAFUCredentialStatusResponseValidationError{
				field:  "NextActionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateAFUCredentialStatusResponseMultiError(errors)
	}

	return nil
}

// UpdateAFUCredentialStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateAFUCredentialStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateAFUCredentialStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAFUCredentialStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAFUCredentialStatusResponseMultiError) AllErrors() []error { return m }

// UpdateAFUCredentialStatusResponseValidationError is the validation error
// returned by UpdateAFUCredentialStatusResponse.Validate if the designated
// constraints aren't met.
type UpdateAFUCredentialStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAFUCredentialStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAFUCredentialStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAFUCredentialStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAFUCredentialStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAFUCredentialStatusResponseValidationError) ErrorName() string {
	return "UpdateAFUCredentialStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAFUCredentialStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAFUCredentialStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAFUCredentialStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAFUCredentialStatusResponseValidationError{}

// Validate checks the field values on ConfirmAuthFactorUpdateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConfirmAuthFactorUpdateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConfirmAuthFactorUpdateRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ConfirmAuthFactorUpdateRequestMultiError, or nil if none found.
func (m *ConfirmAuthFactorUpdateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmAuthFactorUpdateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AuthFactorUpdateId

	// no validation rules for CredBlock

	// no validation rules for CredBlockRequestId

	// no validation rules for VendorCardId

	// no validation rules for CardForm

	if len(errors) > 0 {
		return ConfirmAuthFactorUpdateRequestMultiError(errors)
	}

	return nil
}

// ConfirmAuthFactorUpdateRequestMultiError is an error wrapping multiple
// validation errors returned by ConfirmAuthFactorUpdateRequest.ValidateAll()
// if the designated constraints aren't met.
type ConfirmAuthFactorUpdateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmAuthFactorUpdateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmAuthFactorUpdateRequestMultiError) AllErrors() []error { return m }

// ConfirmAuthFactorUpdateRequestValidationError is the validation error
// returned by ConfirmAuthFactorUpdateRequest.Validate if the designated
// constraints aren't met.
type ConfirmAuthFactorUpdateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmAuthFactorUpdateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfirmAuthFactorUpdateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConfirmAuthFactorUpdateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfirmAuthFactorUpdateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmAuthFactorUpdateRequestValidationError) ErrorName() string {
	return "ConfirmAuthFactorUpdateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmAuthFactorUpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmAuthFactorUpdateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmAuthFactorUpdateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmAuthFactorUpdateRequestValidationError{}

// Validate checks the field values on ConfirmAuthFactorUpdateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConfirmAuthFactorUpdateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConfirmAuthFactorUpdateResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ConfirmAuthFactorUpdateResponseMultiError, or nil if none found.
func (m *ConfirmAuthFactorUpdateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmAuthFactorUpdateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmAuthFactorUpdateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmAuthFactorUpdateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmAuthFactorUpdateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NextAction

	if all {
		switch v := interface{}(m.GetNextActionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmAuthFactorUpdateResponseValidationError{
					field:  "NextActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmAuthFactorUpdateResponseValidationError{
					field:  "NextActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmAuthFactorUpdateResponseValidationError{
				field:  "NextActionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConfirmAuthFactorUpdateResponseMultiError(errors)
	}

	return nil
}

// ConfirmAuthFactorUpdateResponseMultiError is an error wrapping multiple
// validation errors returned by ConfirmAuthFactorUpdateResponse.ValidateAll()
// if the designated constraints aren't met.
type ConfirmAuthFactorUpdateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmAuthFactorUpdateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmAuthFactorUpdateResponseMultiError) AllErrors() []error { return m }

// ConfirmAuthFactorUpdateResponseValidationError is the validation error
// returned by ConfirmAuthFactorUpdateResponse.Validate if the designated
// constraints aren't met.
type ConfirmAuthFactorUpdateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmAuthFactorUpdateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfirmAuthFactorUpdateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConfirmAuthFactorUpdateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfirmAuthFactorUpdateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmAuthFactorUpdateResponseValidationError) ErrorName() string {
	return "ConfirmAuthFactorUpdateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmAuthFactorUpdateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmAuthFactorUpdateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmAuthFactorUpdateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmAuthFactorUpdateResponseValidationError{}

// Validate checks the field values on GetAuthFactorUpdateRecordRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAuthFactorUpdateRecordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthFactorUpdateRecordRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAuthFactorUpdateRecordRequestMultiError, or nil if none found.
func (m *GetAuthFactorUpdateRecordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthFactorUpdateRecordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AuthFactorUpdateId

	if len(errors) > 0 {
		return GetAuthFactorUpdateRecordRequestMultiError(errors)
	}

	return nil
}

// GetAuthFactorUpdateRecordRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAuthFactorUpdateRecordRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAuthFactorUpdateRecordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthFactorUpdateRecordRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthFactorUpdateRecordRequestMultiError) AllErrors() []error { return m }

// GetAuthFactorUpdateRecordRequestValidationError is the validation error
// returned by GetAuthFactorUpdateRecordRequest.Validate if the designated
// constraints aren't met.
type GetAuthFactorUpdateRecordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthFactorUpdateRecordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthFactorUpdateRecordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthFactorUpdateRecordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthFactorUpdateRecordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthFactorUpdateRecordRequestValidationError) ErrorName() string {
	return "GetAuthFactorUpdateRecordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthFactorUpdateRecordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthFactorUpdateRecordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthFactorUpdateRecordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthFactorUpdateRecordRequestValidationError{}

// Validate checks the field values on GetAuthFactorUpdateRecordResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAuthFactorUpdateRecordResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthFactorUpdateRecordResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAuthFactorUpdateRecordResponseMultiError, or nil if none found.
func (m *GetAuthFactorUpdateRecordResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthFactorUpdateRecordResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAuthFactorUpdateRecordResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAuthFactorUpdateRecordResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAuthFactorUpdateRecordResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecord()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAuthFactorUpdateRecordResponseValidationError{
					field:  "Record",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAuthFactorUpdateRecordResponseValidationError{
					field:  "Record",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecord()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAuthFactorUpdateRecordResponseValidationError{
				field:  "Record",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAuthFactorUpdateRecordResponseMultiError(errors)
	}

	return nil
}

// GetAuthFactorUpdateRecordResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetAuthFactorUpdateRecordResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAuthFactorUpdateRecordResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthFactorUpdateRecordResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthFactorUpdateRecordResponseMultiError) AllErrors() []error { return m }

// GetAuthFactorUpdateRecordResponseValidationError is the validation error
// returned by GetAuthFactorUpdateRecordResponse.Validate if the designated
// constraints aren't met.
type GetAuthFactorUpdateRecordResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthFactorUpdateRecordResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthFactorUpdateRecordResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthFactorUpdateRecordResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthFactorUpdateRecordResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthFactorUpdateRecordResponseValidationError) ErrorName() string {
	return "GetAuthFactorUpdateRecordResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthFactorUpdateRecordResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthFactorUpdateRecordResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthFactorUpdateRecordResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthFactorUpdateRecordResponseValidationError{}

// Validate checks the field values on AuthFactorsProfileUpdateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthFactorsProfileUpdateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthFactorsProfileUpdateRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AuthFactorsProfileUpdateRequestMultiError, or nil if none found.
func (m *AuthFactorsProfileUpdateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthFactorsProfileUpdateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AuthFactorUpdateId

	// no validation rules for RequestStatus

	if len(errors) > 0 {
		return AuthFactorsProfileUpdateRequestMultiError(errors)
	}

	return nil
}

// AuthFactorsProfileUpdateRequestMultiError is an error wrapping multiple
// validation errors returned by AuthFactorsProfileUpdateRequest.ValidateAll()
// if the designated constraints aren't met.
type AuthFactorsProfileUpdateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthFactorsProfileUpdateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthFactorsProfileUpdateRequestMultiError) AllErrors() []error { return m }

// AuthFactorsProfileUpdateRequestValidationError is the validation error
// returned by AuthFactorsProfileUpdateRequest.Validate if the designated
// constraints aren't met.
type AuthFactorsProfileUpdateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthFactorsProfileUpdateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthFactorsProfileUpdateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthFactorsProfileUpdateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthFactorsProfileUpdateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthFactorsProfileUpdateRequestValidationError) ErrorName() string {
	return "AuthFactorsProfileUpdateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AuthFactorsProfileUpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthFactorsProfileUpdateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthFactorsProfileUpdateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthFactorsProfileUpdateRequestValidationError{}

// Validate checks the field values on AuthFactorsProfileUpdateResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *AuthFactorsProfileUpdateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthFactorsProfileUpdateResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AuthFactorsProfileUpdateResponseMultiError, or nil if none found.
func (m *AuthFactorsProfileUpdateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthFactorsProfileUpdateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthFactorsProfileUpdateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthFactorsProfileUpdateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthFactorsProfileUpdateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthFactorsProfileUpdateResponseMultiError(errors)
	}

	return nil
}

// AuthFactorsProfileUpdateResponseMultiError is an error wrapping multiple
// validation errors returned by
// AuthFactorsProfileUpdateResponse.ValidateAll() if the designated
// constraints aren't met.
type AuthFactorsProfileUpdateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthFactorsProfileUpdateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthFactorsProfileUpdateResponseMultiError) AllErrors() []error { return m }

// AuthFactorsProfileUpdateResponseValidationError is the validation error
// returned by AuthFactorsProfileUpdateResponse.Validate if the designated
// constraints aren't met.
type AuthFactorsProfileUpdateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthFactorsProfileUpdateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthFactorsProfileUpdateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthFactorsProfileUpdateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthFactorsProfileUpdateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthFactorsProfileUpdateResponseValidationError) ErrorName() string {
	return "AuthFactorsProfileUpdateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AuthFactorsProfileUpdateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthFactorsProfileUpdateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthFactorsProfileUpdateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthFactorsProfileUpdateResponseValidationError{}

// Validate checks the field values on ReRegisterDeviceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReRegisterDeviceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReRegisterDeviceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReRegisterDeviceRequestMultiError, or nil if none found.
func (m *ReRegisterDeviceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReRegisterDeviceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReRegisterDeviceRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReRegisterDeviceRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReRegisterDeviceRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReRegisterDeviceRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReRegisterDeviceRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReRegisterDeviceRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for Payload

	// no validation rules for ActorId

	// no validation rules for AuthFactorUpdateId

	// no validation rules for AccessToken

	// no validation rules for CredBlock

	// no validation rules for CredBlockRequestId

	// no validation rules for SimId

	if len(errors) > 0 {
		return ReRegisterDeviceRequestMultiError(errors)
	}

	return nil
}

// ReRegisterDeviceRequestMultiError is an error wrapping multiple validation
// errors returned by ReRegisterDeviceRequest.ValidateAll() if the designated
// constraints aren't met.
type ReRegisterDeviceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReRegisterDeviceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReRegisterDeviceRequestMultiError) AllErrors() []error { return m }

// ReRegisterDeviceRequestValidationError is the validation error returned by
// ReRegisterDeviceRequest.Validate if the designated constraints aren't met.
type ReRegisterDeviceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReRegisterDeviceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReRegisterDeviceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReRegisterDeviceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReRegisterDeviceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReRegisterDeviceRequestValidationError) ErrorName() string {
	return "ReRegisterDeviceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReRegisterDeviceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReRegisterDeviceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReRegisterDeviceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReRegisterDeviceRequestValidationError{}

// Validate checks the field values on ReRegisterDeviceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReRegisterDeviceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReRegisterDeviceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReRegisterDeviceResponseMultiError, or nil if none found.
func (m *ReRegisterDeviceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReRegisterDeviceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReRegisterDeviceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReRegisterDeviceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReRegisterDeviceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NextAction

	if all {
		switch v := interface{}(m.GetNextActionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReRegisterDeviceResponseValidationError{
					field:  "NextActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReRegisterDeviceResponseValidationError{
					field:  "NextActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReRegisterDeviceResponseValidationError{
				field:  "NextActionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReRegisterDeviceResponseMultiError(errors)
	}

	return nil
}

// ReRegisterDeviceResponseMultiError is an error wrapping multiple validation
// errors returned by ReRegisterDeviceResponse.ValidateAll() if the designated
// constraints aren't met.
type ReRegisterDeviceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReRegisterDeviceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReRegisterDeviceResponseMultiError) AllErrors() []error { return m }

// ReRegisterDeviceResponseValidationError is the validation error returned by
// ReRegisterDeviceResponse.Validate if the designated constraints aren't met.
type ReRegisterDeviceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReRegisterDeviceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReRegisterDeviceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReRegisterDeviceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReRegisterDeviceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReRegisterDeviceResponseValidationError) ErrorName() string {
	return "ReRegisterDeviceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReRegisterDeviceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReRegisterDeviceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReRegisterDeviceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReRegisterDeviceResponseValidationError{}

// Validate checks the field values on VerifyDeviceIntegrityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyDeviceIntegrityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyDeviceIntegrityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyDeviceIntegrityRequestMultiError, or nil if none found.
func (m *VerifyDeviceIntegrityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyDeviceIntegrityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyDeviceIntegrityRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyDeviceIntegrityRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyDeviceIntegrityRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Attestation

	// no validation rules for VerificationRequired

	// no validation rules for Nonce

	// no validation rules for ActorId

	// no validation rules for AccessToken

	// no validation rules for ConsentType

	// no validation rules for TokenType

	if len(errors) > 0 {
		return VerifyDeviceIntegrityRequestMultiError(errors)
	}

	return nil
}

// VerifyDeviceIntegrityRequestMultiError is an error wrapping multiple
// validation errors returned by VerifyDeviceIntegrityRequest.ValidateAll() if
// the designated constraints aren't met.
type VerifyDeviceIntegrityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyDeviceIntegrityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyDeviceIntegrityRequestMultiError) AllErrors() []error { return m }

// VerifyDeviceIntegrityRequestValidationError is the validation error returned
// by VerifyDeviceIntegrityRequest.Validate if the designated constraints
// aren't met.
type VerifyDeviceIntegrityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyDeviceIntegrityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyDeviceIntegrityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyDeviceIntegrityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyDeviceIntegrityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyDeviceIntegrityRequestValidationError) ErrorName() string {
	return "VerifyDeviceIntegrityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyDeviceIntegrityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyDeviceIntegrityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyDeviceIntegrityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyDeviceIntegrityRequestValidationError{}

// Validate checks the field values on VerifyDeviceIntegrityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyDeviceIntegrityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyDeviceIntegrityResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// VerifyDeviceIntegrityResponseMultiError, or nil if none found.
func (m *VerifyDeviceIntegrityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyDeviceIntegrityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyDeviceIntegrityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyDeviceIntegrityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyDeviceIntegrityResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsVerified

	// no validation rules for DeviceIntegrityId

	if len(errors) > 0 {
		return VerifyDeviceIntegrityResponseMultiError(errors)
	}

	return nil
}

// VerifyDeviceIntegrityResponseMultiError is an error wrapping multiple
// validation errors returned by VerifyDeviceIntegrityResponse.ValidateAll()
// if the designated constraints aren't met.
type VerifyDeviceIntegrityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyDeviceIntegrityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyDeviceIntegrityResponseMultiError) AllErrors() []error { return m }

// VerifyDeviceIntegrityResponseValidationError is the validation error
// returned by VerifyDeviceIntegrityResponse.Validate if the designated
// constraints aren't met.
type VerifyDeviceIntegrityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyDeviceIntegrityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyDeviceIntegrityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyDeviceIntegrityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyDeviceIntegrityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyDeviceIntegrityResponseValidationError) ErrorName() string {
	return "VerifyDeviceIntegrityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyDeviceIntegrityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyDeviceIntegrityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyDeviceIntegrityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyDeviceIntegrityResponseValidationError{}

// Validate checks the field values on GetDeviceIntegrityNonceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeviceIntegrityNonceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceIntegrityNonceRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetDeviceIntegrityNonceRequestMultiError, or nil if none found.
func (m *GetDeviceIntegrityNonceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceIntegrityNonceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceIntegrityNonceRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceIntegrityNonceRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceIntegrityNonceRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDeviceIntegrityNonceRequestMultiError(errors)
	}

	return nil
}

// GetDeviceIntegrityNonceRequestMultiError is an error wrapping multiple
// validation errors returned by GetDeviceIntegrityNonceRequest.ValidateAll()
// if the designated constraints aren't met.
type GetDeviceIntegrityNonceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceIntegrityNonceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceIntegrityNonceRequestMultiError) AllErrors() []error { return m }

// GetDeviceIntegrityNonceRequestValidationError is the validation error
// returned by GetDeviceIntegrityNonceRequest.Validate if the designated
// constraints aren't met.
type GetDeviceIntegrityNonceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceIntegrityNonceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceIntegrityNonceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceIntegrityNonceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceIntegrityNonceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceIntegrityNonceRequestValidationError) ErrorName() string {
	return "GetDeviceIntegrityNonceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeviceIntegrityNonceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceIntegrityNonceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceIntegrityNonceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceIntegrityNonceRequestValidationError{}

// Validate checks the field values on GetDeviceIntegrityNonceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeviceIntegrityNonceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceIntegrityNonceResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetDeviceIntegrityNonceResponseMultiError, or nil if none found.
func (m *GetDeviceIntegrityNonceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceIntegrityNonceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceIntegrityNonceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceIntegrityNonceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceIntegrityNonceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Nonce

	if len(errors) > 0 {
		return GetDeviceIntegrityNonceResponseMultiError(errors)
	}

	return nil
}

// GetDeviceIntegrityNonceResponseMultiError is an error wrapping multiple
// validation errors returned by GetDeviceIntegrityNonceResponse.ValidateAll()
// if the designated constraints aren't met.
type GetDeviceIntegrityNonceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceIntegrityNonceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceIntegrityNonceResponseMultiError) AllErrors() []error { return m }

// GetDeviceIntegrityNonceResponseValidationError is the validation error
// returned by GetDeviceIntegrityNonceResponse.Validate if the designated
// constraints aren't met.
type GetDeviceIntegrityNonceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceIntegrityNonceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceIntegrityNonceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceIntegrityNonceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceIntegrityNonceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceIntegrityNonceResponseValidationError) ErrorName() string {
	return "GetDeviceIntegrityNonceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeviceIntegrityNonceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceIntegrityNonceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceIntegrityNonceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceIntegrityNonceResponseValidationError{}

// Validate checks the field values on GetFirstDeviceRegAfterTimeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFirstDeviceRegAfterTimeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFirstDeviceRegAfterTimeRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFirstDeviceRegAfterTimeRequestMultiError, or nil if none found.
func (m *GetFirstDeviceRegAfterTimeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFirstDeviceRegAfterTimeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFirstDeviceRegAfterTimeRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFirstDeviceRegAfterTimeRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFirstDeviceRegAfterTimeRequestValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFirstDeviceRegAfterTimeRequestMultiError(errors)
	}

	return nil
}

// GetFirstDeviceRegAfterTimeRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetFirstDeviceRegAfterTimeRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFirstDeviceRegAfterTimeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFirstDeviceRegAfterTimeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFirstDeviceRegAfterTimeRequestMultiError) AllErrors() []error { return m }

// GetFirstDeviceRegAfterTimeRequestValidationError is the validation error
// returned by GetFirstDeviceRegAfterTimeRequest.Validate if the designated
// constraints aren't met.
type GetFirstDeviceRegAfterTimeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFirstDeviceRegAfterTimeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFirstDeviceRegAfterTimeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFirstDeviceRegAfterTimeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFirstDeviceRegAfterTimeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFirstDeviceRegAfterTimeRequestValidationError) ErrorName() string {
	return "GetFirstDeviceRegAfterTimeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFirstDeviceRegAfterTimeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFirstDeviceRegAfterTimeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFirstDeviceRegAfterTimeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFirstDeviceRegAfterTimeRequestValidationError{}

// Validate checks the field values on GetFirstDeviceRegAfterTimeResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFirstDeviceRegAfterTimeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFirstDeviceRegAfterTimeResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFirstDeviceRegAfterTimeResponseMultiError, or nil if none found.
func (m *GetFirstDeviceRegAfterTimeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFirstDeviceRegAfterTimeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFirstDeviceRegAfterTimeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFirstDeviceRegAfterTimeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFirstDeviceRegAfterTimeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeviceRegistration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFirstDeviceRegAfterTimeResponseValidationError{
					field:  "DeviceRegistration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFirstDeviceRegAfterTimeResponseValidationError{
					field:  "DeviceRegistration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceRegistration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFirstDeviceRegAfterTimeResponseValidationError{
				field:  "DeviceRegistration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFirstDeviceRegAfterTimeResponseMultiError(errors)
	}

	return nil
}

// GetFirstDeviceRegAfterTimeResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetFirstDeviceRegAfterTimeResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFirstDeviceRegAfterTimeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFirstDeviceRegAfterTimeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFirstDeviceRegAfterTimeResponseMultiError) AllErrors() []error { return m }

// GetFirstDeviceRegAfterTimeResponseValidationError is the validation error
// returned by GetFirstDeviceRegAfterTimeResponse.Validate if the designated
// constraints aren't met.
type GetFirstDeviceRegAfterTimeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFirstDeviceRegAfterTimeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFirstDeviceRegAfterTimeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFirstDeviceRegAfterTimeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFirstDeviceRegAfterTimeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFirstDeviceRegAfterTimeResponseValidationError) ErrorName() string {
	return "GetFirstDeviceRegAfterTimeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFirstDeviceRegAfterTimeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFirstDeviceRegAfterTimeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFirstDeviceRegAfterTimeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFirstDeviceRegAfterTimeResponseValidationError{}

// Validate checks the field values on GenerateVendorOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateVendorOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateVendorOtpRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateVendorOtpRequestMultiError, or nil if none found.
func (m *GenerateVendorOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateVendorOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RequestType

	// no validation rules for CardAuthAttemptId

	if len(errors) > 0 {
		return GenerateVendorOtpRequestMultiError(errors)
	}

	return nil
}

// GenerateVendorOtpRequestMultiError is an error wrapping multiple validation
// errors returned by GenerateVendorOtpRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateVendorOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateVendorOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateVendorOtpRequestMultiError) AllErrors() []error { return m }

// GenerateVendorOtpRequestValidationError is the validation error returned by
// GenerateVendorOtpRequest.Validate if the designated constraints aren't met.
type GenerateVendorOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateVendorOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateVendorOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateVendorOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateVendorOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateVendorOtpRequestValidationError) ErrorName() string {
	return "GenerateVendorOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateVendorOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateVendorOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateVendorOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateVendorOtpRequestValidationError{}

// Validate checks the field values on GenerateVendorOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateVendorOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateVendorOtpResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateVendorOtpResponseMultiError, or nil if none found.
func (m *GenerateVendorOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateVendorOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateVendorOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateVendorOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateVendorOtpResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateVendorOtpResponseMultiError(errors)
	}

	return nil
}

// GenerateVendorOtpResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateVendorOtpResponse.ValidateAll() if the
// designated constraints aren't met.
type GenerateVendorOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateVendorOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateVendorOtpResponseMultiError) AllErrors() []error { return m }

// GenerateVendorOtpResponseValidationError is the validation error returned by
// GenerateVendorOtpResponse.Validate if the designated constraints aren't met.
type GenerateVendorOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateVendorOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateVendorOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateVendorOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateVendorOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateVendorOtpResponseValidationError) ErrorName() string {
	return "GenerateVendorOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateVendorOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateVendorOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateVendorOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateVendorOtpResponseValidationError{}

// Validate checks the field values on ValidateAuthFactorUpdateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateAuthFactorUpdateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateAuthFactorUpdateRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ValidateAuthFactorUpdateRequestMultiError, or nil if none found.
func (m *ValidateAuthFactorUpdateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateAuthFactorUpdateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Email

	// no validation rules for DeviceId

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateAuthFactorUpdateRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateAuthFactorUpdateRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateAuthFactorUpdateRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateAuthFactorUpdateRequestMultiError(errors)
	}

	return nil
}

// ValidateAuthFactorUpdateRequestMultiError is an error wrapping multiple
// validation errors returned by ValidateAuthFactorUpdateRequest.ValidateAll()
// if the designated constraints aren't met.
type ValidateAuthFactorUpdateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateAuthFactorUpdateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateAuthFactorUpdateRequestMultiError) AllErrors() []error { return m }

// ValidateAuthFactorUpdateRequestValidationError is the validation error
// returned by ValidateAuthFactorUpdateRequest.Validate if the designated
// constraints aren't met.
type ValidateAuthFactorUpdateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateAuthFactorUpdateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateAuthFactorUpdateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateAuthFactorUpdateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateAuthFactorUpdateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateAuthFactorUpdateRequestValidationError) ErrorName() string {
	return "ValidateAuthFactorUpdateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateAuthFactorUpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateAuthFactorUpdateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateAuthFactorUpdateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateAuthFactorUpdateRequestValidationError{}

// Validate checks the field values on ValidateAuthFactorUpdateResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ValidateAuthFactorUpdateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateAuthFactorUpdateResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ValidateAuthFactorUpdateResponseMultiError, or nil if none found.
func (m *ValidateAuthFactorUpdateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateAuthFactorUpdateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateAuthFactorUpdateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateAuthFactorUpdateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateAuthFactorUpdateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Id

	// no validation rules for NextAction

	if all {
		switch v := interface{}(m.GetNextActionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateAuthFactorUpdateResponseValidationError{
					field:  "NextActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateAuthFactorUpdateResponseValidationError{
					field:  "NextActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateAuthFactorUpdateResponseValidationError{
				field:  "NextActionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateAuthFactorUpdateResponseMultiError(errors)
	}

	return nil
}

// ValidateAuthFactorUpdateResponseMultiError is an error wrapping multiple
// validation errors returned by
// ValidateAuthFactorUpdateResponse.ValidateAll() if the designated
// constraints aren't met.
type ValidateAuthFactorUpdateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateAuthFactorUpdateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateAuthFactorUpdateResponseMultiError) AllErrors() []error { return m }

// ValidateAuthFactorUpdateResponseValidationError is the validation error
// returned by ValidateAuthFactorUpdateResponse.Validate if the designated
// constraints aren't met.
type ValidateAuthFactorUpdateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateAuthFactorUpdateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateAuthFactorUpdateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateAuthFactorUpdateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateAuthFactorUpdateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateAuthFactorUpdateResponseValidationError) ErrorName() string {
	return "ValidateAuthFactorUpdateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateAuthFactorUpdateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateAuthFactorUpdateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateAuthFactorUpdateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateAuthFactorUpdateResponseValidationError{}

// Validate checks the field values on RecoverAFUProcessForActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecoverAFUProcessForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecoverAFUProcessForActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RecoverAFUProcessForActorRequestMultiError, or nil if none found.
func (m *RecoverAFUProcessForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecoverAFUProcessForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for InvalidTokenInReactivateAsSuccess

	switch v := m.Identifier.(type) {
	case *RecoverAFUProcessForActorRequest_ActorIdV2:
		if v == nil {
			err := RecoverAFUProcessForActorRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorIdV2
	case *RecoverAFUProcessForActorRequest_DeviceReregRequestId:
		if v == nil {
			err := RecoverAFUProcessForActorRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for DeviceReregRequestId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RecoverAFUProcessForActorRequestMultiError(errors)
	}

	return nil
}

// RecoverAFUProcessForActorRequestMultiError is an error wrapping multiple
// validation errors returned by
// RecoverAFUProcessForActorRequest.ValidateAll() if the designated
// constraints aren't met.
type RecoverAFUProcessForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecoverAFUProcessForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecoverAFUProcessForActorRequestMultiError) AllErrors() []error { return m }

// RecoverAFUProcessForActorRequestValidationError is the validation error
// returned by RecoverAFUProcessForActorRequest.Validate if the designated
// constraints aren't met.
type RecoverAFUProcessForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecoverAFUProcessForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecoverAFUProcessForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecoverAFUProcessForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecoverAFUProcessForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecoverAFUProcessForActorRequestValidationError) ErrorName() string {
	return "RecoverAFUProcessForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecoverAFUProcessForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecoverAFUProcessForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecoverAFUProcessForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecoverAFUProcessForActorRequestValidationError{}

// Validate checks the field values on RecoverAFUProcessForActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecoverAFUProcessForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecoverAFUProcessForActorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecoverAFUProcessForActorResponseMultiError, or nil if none found.
func (m *RecoverAFUProcessForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecoverAFUProcessForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecoverAFUProcessForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecoverAFUProcessForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecoverAFUProcessForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecoverAFUProcessForActorResponseMultiError(errors)
	}

	return nil
}

// RecoverAFUProcessForActorResponseMultiError is an error wrapping multiple
// validation errors returned by
// RecoverAFUProcessForActorResponse.ValidateAll() if the designated
// constraints aren't met.
type RecoverAFUProcessForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecoverAFUProcessForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecoverAFUProcessForActorResponseMultiError) AllErrors() []error { return m }

// RecoverAFUProcessForActorResponseValidationError is the validation error
// returned by RecoverAFUProcessForActorResponse.Validate if the designated
// constraints aren't met.
type RecoverAFUProcessForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecoverAFUProcessForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecoverAFUProcessForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecoverAFUProcessForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecoverAFUProcessForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecoverAFUProcessForActorResponseValidationError) ErrorName() string {
	return "RecoverAFUProcessForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecoverAFUProcessForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecoverAFUProcessForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecoverAFUProcessForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecoverAFUProcessForActorResponseValidationError{}

// Validate checks the field values on UpdateTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTokenRequestMultiError, or nil if none found.
func (m *UpdateTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for TokenUpdationReason

	switch v := m.Identifier.(type) {
	case *UpdateTokenRequest_PhoneNumber:
		if v == nil {
			err := UpdateTokenRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPhoneNumber()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateTokenRequestValidationError{
						field:  "PhoneNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateTokenRequestValidationError{
						field:  "PhoneNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateTokenRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UpdateTokenRequest_TokenId:
		if v == nil {
			err := UpdateTokenRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for TokenId
	case *UpdateTokenRequest_ActorId:
		if v == nil {
			err := UpdateTokenRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return UpdateTokenRequestMultiError(errors)
	}

	return nil
}

// UpdateTokenRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateTokenRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTokenRequestMultiError) AllErrors() []error { return m }

// UpdateTokenRequestValidationError is the validation error returned by
// UpdateTokenRequest.Validate if the designated constraints aren't met.
type UpdateTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTokenRequestValidationError) ErrorName() string {
	return "UpdateTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTokenRequestValidationError{}

// Validate checks the field values on UpdateTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTokenResponseMultiError, or nil if none found.
func (m *UpdateTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateTokenResponseMultiError(errors)
	}

	return nil
}

// UpdateTokenResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTokenResponseMultiError) AllErrors() []error { return m }

// UpdateTokenResponseValidationError is the validation error returned by
// UpdateTokenResponse.Validate if the designated constraints aren't met.
type UpdateTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTokenResponseValidationError) ErrorName() string {
	return "UpdateTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTokenResponseValidationError{}

// Validate checks the field values on GetDeviceDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeviceDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeviceDetailsRequestMultiError, or nil if none found.
func (m *GetDeviceDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetDeviceDetailsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetDeviceDetailsRequestMultiError(errors)
	}

	return nil
}

// GetDeviceDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetDeviceDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDeviceDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceDetailsRequestMultiError) AllErrors() []error { return m }

// GetDeviceDetailsRequestValidationError is the validation error returned by
// GetDeviceDetailsRequest.Validate if the designated constraints aren't met.
type GetDeviceDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceDetailsRequestValidationError) ErrorName() string {
	return "GetDeviceDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeviceDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceDetailsRequestValidationError{}

// Validate checks the field values on GetDeviceDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeviceDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeviceDetailsResponseMultiError, or nil if none found.
func (m *GetDeviceDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceDetailsResponseValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceDetailsResponseValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceDetailsResponseValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeviceRegistrationStatus

	// no validation rules for ResponseVersion

	if len(errors) > 0 {
		return GetDeviceDetailsResponseMultiError(errors)
	}

	return nil
}

// GetDeviceDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetDeviceDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDeviceDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceDetailsResponseMultiError) AllErrors() []error { return m }

// GetDeviceDetailsResponseValidationError is the validation error returned by
// GetDeviceDetailsResponse.Validate if the designated constraints aren't met.
type GetDeviceDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceDetailsResponseValidationError) ErrorName() string {
	return "GetDeviceDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeviceDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceDetailsResponseValidationError{}

// Validate checks the field values on GetAuthFactorUpdatesForActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAuthFactorUpdatesForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthFactorUpdatesForActorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAuthFactorUpdatesForActorRequestMultiError, or nil if none found.
func (m *GetAuthFactorUpdatesForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthFactorUpdatesForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetAuthFactorUpdatesForActorRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Count

	// no validation rules for OverallStatus

	if len(errors) > 0 {
		return GetAuthFactorUpdatesForActorRequestMultiError(errors)
	}

	return nil
}

// GetAuthFactorUpdatesForActorRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAuthFactorUpdatesForActorRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAuthFactorUpdatesForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthFactorUpdatesForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthFactorUpdatesForActorRequestMultiError) AllErrors() []error { return m }

// GetAuthFactorUpdatesForActorRequestValidationError is the validation error
// returned by GetAuthFactorUpdatesForActorRequest.Validate if the designated
// constraints aren't met.
type GetAuthFactorUpdatesForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthFactorUpdatesForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthFactorUpdatesForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthFactorUpdatesForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthFactorUpdatesForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthFactorUpdatesForActorRequestValidationError) ErrorName() string {
	return "GetAuthFactorUpdatesForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthFactorUpdatesForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthFactorUpdatesForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthFactorUpdatesForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthFactorUpdatesForActorRequestValidationError{}

// Validate checks the field values on GetAuthFactorUpdatesForActorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetAuthFactorUpdatesForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthFactorUpdatesForActorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAuthFactorUpdatesForActorResponseMultiError, or nil if none found.
func (m *GetAuthFactorUpdatesForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthFactorUpdatesForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAuthFactorUpdatesForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAuthFactorUpdatesForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAuthFactorUpdatesForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAuthFactorUpdates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAuthFactorUpdatesForActorResponseValidationError{
						field:  fmt.Sprintf("AuthFactorUpdates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAuthFactorUpdatesForActorResponseValidationError{
						field:  fmt.Sprintf("AuthFactorUpdates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAuthFactorUpdatesForActorResponseValidationError{
					field:  fmt.Sprintf("AuthFactorUpdates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAuthFactorUpdatesForActorResponseMultiError(errors)
	}

	return nil
}

// GetAuthFactorUpdatesForActorResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetAuthFactorUpdatesForActorResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAuthFactorUpdatesForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthFactorUpdatesForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthFactorUpdatesForActorResponseMultiError) AllErrors() []error { return m }

// GetAuthFactorUpdatesForActorResponseValidationError is the validation error
// returned by GetAuthFactorUpdatesForActorResponse.Validate if the designated
// constraints aren't met.
type GetAuthFactorUpdatesForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthFactorUpdatesForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthFactorUpdatesForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthFactorUpdatesForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthFactorUpdatesForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthFactorUpdatesForActorResponseValidationError) ErrorName() string {
	return "GetAuthFactorUpdatesForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthFactorUpdatesForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthFactorUpdatesForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthFactorUpdatesForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthFactorUpdatesForActorResponseValidationError{}

// Validate checks the field values on GetOtpsByPhoneNumberLimitRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetOtpsByPhoneNumberLimitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOtpsByPhoneNumberLimitRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetOtpsByPhoneNumberLimitRequestMultiError, or nil if none found.
func (m *GetOtpsByPhoneNumberLimitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOtpsByPhoneNumberLimitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOtpsByPhoneNumberLimitRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOtpsByPhoneNumberLimitRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOtpsByPhoneNumberLimitRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetLimit() > 100 {
		err := GetOtpsByPhoneNumberLimitRequestValidationError{
			field:  "Limit",
			reason: "value must be less than or equal to 100",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetOtpsByPhoneNumberLimitRequestMultiError(errors)
	}

	return nil
}

// GetOtpsByPhoneNumberLimitRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetOtpsByPhoneNumberLimitRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOtpsByPhoneNumberLimitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOtpsByPhoneNumberLimitRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOtpsByPhoneNumberLimitRequestMultiError) AllErrors() []error { return m }

// GetOtpsByPhoneNumberLimitRequestValidationError is the validation error
// returned by GetOtpsByPhoneNumberLimitRequest.Validate if the designated
// constraints aren't met.
type GetOtpsByPhoneNumberLimitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOtpsByPhoneNumberLimitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOtpsByPhoneNumberLimitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOtpsByPhoneNumberLimitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOtpsByPhoneNumberLimitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOtpsByPhoneNumberLimitRequestValidationError) ErrorName() string {
	return "GetOtpsByPhoneNumberLimitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOtpsByPhoneNumberLimitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOtpsByPhoneNumberLimitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOtpsByPhoneNumberLimitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOtpsByPhoneNumberLimitRequestValidationError{}

// Validate checks the field values on GetOtpsByPhoneNumberLimitResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetOtpsByPhoneNumberLimitResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOtpsByPhoneNumberLimitResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetOtpsByPhoneNumberLimitResponseMultiError, or nil if none found.
func (m *GetOtpsByPhoneNumberLimitResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOtpsByPhoneNumberLimitResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOtpsByPhoneNumberLimitResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOtpsByPhoneNumberLimitResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOtpsByPhoneNumberLimitResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOtps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOtpsByPhoneNumberLimitResponseValidationError{
						field:  fmt.Sprintf("Otps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOtpsByPhoneNumberLimitResponseValidationError{
						field:  fmt.Sprintf("Otps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOtpsByPhoneNumberLimitResponseValidationError{
					field:  fmt.Sprintf("Otps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetOtpsByPhoneNumberLimitResponseMultiError(errors)
	}

	return nil
}

// GetOtpsByPhoneNumberLimitResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetOtpsByPhoneNumberLimitResponse.ValidateAll() if the designated
// constraints aren't met.
type GetOtpsByPhoneNumberLimitResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOtpsByPhoneNumberLimitResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOtpsByPhoneNumberLimitResponseMultiError) AllErrors() []error { return m }

// GetOtpsByPhoneNumberLimitResponseValidationError is the validation error
// returned by GetOtpsByPhoneNumberLimitResponse.Validate if the designated
// constraints aren't met.
type GetOtpsByPhoneNumberLimitResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOtpsByPhoneNumberLimitResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOtpsByPhoneNumberLimitResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOtpsByPhoneNumberLimitResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOtpsByPhoneNumberLimitResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOtpsByPhoneNumberLimitResponseValidationError) ErrorName() string {
	return "GetOtpsByPhoneNumberLimitResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOtpsByPhoneNumberLimitResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOtpsByPhoneNumberLimitResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOtpsByPhoneNumberLimitResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOtpsByPhoneNumberLimitResponseValidationError{}

// Validate checks the field values on RecordIosDeviceAttestationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecordIosDeviceAttestationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordIosDeviceAttestationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecordIosDeviceAttestationRequestMultiError, or nil if none found.
func (m *RecordIosDeviceAttestationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordIosDeviceAttestationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordIosDeviceAttestationRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordIosDeviceAttestationRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordIosDeviceAttestationRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Attestation

	// no validation rules for KeyIdentifier

	// no validation rules for Nonce

	if len(errors) > 0 {
		return RecordIosDeviceAttestationRequestMultiError(errors)
	}

	return nil
}

// RecordIosDeviceAttestationRequestMultiError is an error wrapping multiple
// validation errors returned by
// RecordIosDeviceAttestationRequest.ValidateAll() if the designated
// constraints aren't met.
type RecordIosDeviceAttestationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordIosDeviceAttestationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordIosDeviceAttestationRequestMultiError) AllErrors() []error { return m }

// RecordIosDeviceAttestationRequestValidationError is the validation error
// returned by RecordIosDeviceAttestationRequest.Validate if the designated
// constraints aren't met.
type RecordIosDeviceAttestationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordIosDeviceAttestationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordIosDeviceAttestationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordIosDeviceAttestationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordIosDeviceAttestationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordIosDeviceAttestationRequestValidationError) ErrorName() string {
	return "RecordIosDeviceAttestationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecordIosDeviceAttestationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordIosDeviceAttestationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordIosDeviceAttestationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordIosDeviceAttestationRequestValidationError{}

// Validate checks the field values on RecordIosDeviceAttestationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecordIosDeviceAttestationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordIosDeviceAttestationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecordIosDeviceAttestationResponseMultiError, or nil if none found.
func (m *RecordIosDeviceAttestationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordIosDeviceAttestationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordIosDeviceAttestationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordIosDeviceAttestationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordIosDeviceAttestationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordIosDeviceAttestationResponseMultiError(errors)
	}

	return nil
}

// RecordIosDeviceAttestationResponseMultiError is an error wrapping multiple
// validation errors returned by
// RecordIosDeviceAttestationResponse.ValidateAll() if the designated
// constraints aren't met.
type RecordIosDeviceAttestationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordIosDeviceAttestationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordIosDeviceAttestationResponseMultiError) AllErrors() []error { return m }

// RecordIosDeviceAttestationResponseValidationError is the validation error
// returned by RecordIosDeviceAttestationResponse.Validate if the designated
// constraints aren't met.
type RecordIosDeviceAttestationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordIosDeviceAttestationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordIosDeviceAttestationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordIosDeviceAttestationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordIosDeviceAttestationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordIosDeviceAttestationResponseValidationError) ErrorName() string {
	return "RecordIosDeviceAttestationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecordIosDeviceAttestationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordIosDeviceAttestationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordIosDeviceAttestationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordIosDeviceAttestationResponseValidationError{}

// Validate checks the field values on ReactivateDeviceForActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReactivateDeviceForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReactivateDeviceForActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ReactivateDeviceForActorRequestMultiError, or nil if none found.
func (m *ReactivateDeviceForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReactivateDeviceForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return ReactivateDeviceForActorRequestMultiError(errors)
	}

	return nil
}

// ReactivateDeviceForActorRequestMultiError is an error wrapping multiple
// validation errors returned by ReactivateDeviceForActorRequest.ValidateAll()
// if the designated constraints aren't met.
type ReactivateDeviceForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReactivateDeviceForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReactivateDeviceForActorRequestMultiError) AllErrors() []error { return m }

// ReactivateDeviceForActorRequestValidationError is the validation error
// returned by ReactivateDeviceForActorRequest.Validate if the designated
// constraints aren't met.
type ReactivateDeviceForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReactivateDeviceForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReactivateDeviceForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReactivateDeviceForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReactivateDeviceForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReactivateDeviceForActorRequestValidationError) ErrorName() string {
	return "ReactivateDeviceForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReactivateDeviceForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReactivateDeviceForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReactivateDeviceForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReactivateDeviceForActorRequestValidationError{}

// Validate checks the field values on ReactivateDeviceForActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ReactivateDeviceForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReactivateDeviceForActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ReactivateDeviceForActorResponseMultiError, or nil if none found.
func (m *ReactivateDeviceForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReactivateDeviceForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReactivateDeviceForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReactivateDeviceForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReactivateDeviceForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReactivateDeviceForActorResponseMultiError(errors)
	}

	return nil
}

// ReactivateDeviceForActorResponseMultiError is an error wrapping multiple
// validation errors returned by
// ReactivateDeviceForActorResponse.ValidateAll() if the designated
// constraints aren't met.
type ReactivateDeviceForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReactivateDeviceForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReactivateDeviceForActorResponseMultiError) AllErrors() []error { return m }

// ReactivateDeviceForActorResponseValidationError is the validation error
// returned by ReactivateDeviceForActorResponse.Validate if the designated
// constraints aren't met.
type ReactivateDeviceForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReactivateDeviceForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReactivateDeviceForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReactivateDeviceForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReactivateDeviceForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReactivateDeviceForActorResponseValidationError) ErrorName() string {
	return "ReactivateDeviceForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReactivateDeviceForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReactivateDeviceForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReactivateDeviceForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReactivateDeviceForActorResponseValidationError{}

// Validate checks the field values on StoreAuthAttemptRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoreAuthAttemptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoreAuthAttemptRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoreAuthAttemptRequestMultiError, or nil if none found.
func (m *StoreAuthAttemptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StoreAuthAttemptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if m.GetPhoneNumber() == nil {
		err := StoreAuthAttemptRequestValidationError{
			field:  "PhoneNumber",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoreAuthAttemptRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoreAuthAttemptRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoreAuthAttemptRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StatusCode

	// no validation rules for StatusMessage

	// no validation rules for ErrorCode

	if utf8.RuneCountInString(m.GetErrorMessage()) < 1 {
		err := StoreAuthAttemptRequestValidationError{
			field:  "ErrorMessage",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoreAuthAttemptRequestValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoreAuthAttemptRequestValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoreAuthAttemptRequestValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetErrorView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoreAuthAttemptRequestValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoreAuthAttemptRequestValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrorView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoreAuthAttemptRequestValidationError{
				field:  "ErrorView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StoreAuthAttemptRequestMultiError(errors)
	}

	return nil
}

// StoreAuthAttemptRequestMultiError is an error wrapping multiple validation
// errors returned by StoreAuthAttemptRequest.ValidateAll() if the designated
// constraints aren't met.
type StoreAuthAttemptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoreAuthAttemptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoreAuthAttemptRequestMultiError) AllErrors() []error { return m }

// StoreAuthAttemptRequestValidationError is the validation error returned by
// StoreAuthAttemptRequest.Validate if the designated constraints aren't met.
type StoreAuthAttemptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoreAuthAttemptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoreAuthAttemptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoreAuthAttemptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoreAuthAttemptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoreAuthAttemptRequestValidationError) ErrorName() string {
	return "StoreAuthAttemptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StoreAuthAttemptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoreAuthAttemptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoreAuthAttemptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoreAuthAttemptRequestValidationError{}

// Validate checks the field values on StoreAuthAttemptResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoreAuthAttemptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoreAuthAttemptResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoreAuthAttemptResponseMultiError, or nil if none found.
func (m *StoreAuthAttemptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StoreAuthAttemptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoreAuthAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoreAuthAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoreAuthAttemptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StoreAuthAttemptResponseMultiError(errors)
	}

	return nil
}

// StoreAuthAttemptResponseMultiError is an error wrapping multiple validation
// errors returned by StoreAuthAttemptResponse.ValidateAll() if the designated
// constraints aren't met.
type StoreAuthAttemptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoreAuthAttemptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoreAuthAttemptResponseMultiError) AllErrors() []error { return m }

// StoreAuthAttemptResponseValidationError is the validation error returned by
// StoreAuthAttemptResponse.Validate if the designated constraints aren't met.
type StoreAuthAttemptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoreAuthAttemptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoreAuthAttemptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoreAuthAttemptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoreAuthAttemptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoreAuthAttemptResponseValidationError) ErrorName() string {
	return "StoreAuthAttemptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StoreAuthAttemptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoreAuthAttemptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoreAuthAttemptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoreAuthAttemptResponseValidationError{}

// Validate checks the field values on BlockExistingAuthFactorUpdatesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *BlockExistingAuthFactorUpdatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlockExistingAuthFactorUpdatesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BlockExistingAuthFactorUpdatesRequestMultiError, or nil if none found.
func (m *BlockExistingAuthFactorUpdatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BlockExistingAuthFactorUpdatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Device

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BlockExistingAuthFactorUpdatesRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BlockExistingAuthFactorUpdatesRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BlockExistingAuthFactorUpdatesRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BlockExistingAuthFactorUpdatesRequestMultiError(errors)
	}

	return nil
}

// BlockExistingAuthFactorUpdatesRequestMultiError is an error wrapping
// multiple validation errors returned by
// BlockExistingAuthFactorUpdatesRequest.ValidateAll() if the designated
// constraints aren't met.
type BlockExistingAuthFactorUpdatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlockExistingAuthFactorUpdatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlockExistingAuthFactorUpdatesRequestMultiError) AllErrors() []error { return m }

// BlockExistingAuthFactorUpdatesRequestValidationError is the validation error
// returned by BlockExistingAuthFactorUpdatesRequest.Validate if the
// designated constraints aren't met.
type BlockExistingAuthFactorUpdatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlockExistingAuthFactorUpdatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlockExistingAuthFactorUpdatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlockExistingAuthFactorUpdatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlockExistingAuthFactorUpdatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlockExistingAuthFactorUpdatesRequestValidationError) ErrorName() string {
	return "BlockExistingAuthFactorUpdatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BlockExistingAuthFactorUpdatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlockExistingAuthFactorUpdatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlockExistingAuthFactorUpdatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlockExistingAuthFactorUpdatesRequestValidationError{}

// Validate checks the field values on BlockExistingAuthFactorUpdatesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *BlockExistingAuthFactorUpdatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// BlockExistingAuthFactorUpdatesResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// BlockExistingAuthFactorUpdatesResponseMultiError, or nil if none found.
func (m *BlockExistingAuthFactorUpdatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BlockExistingAuthFactorUpdatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BlockExistingAuthFactorUpdatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BlockExistingAuthFactorUpdatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BlockExistingAuthFactorUpdatesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BlockExistingAuthFactorUpdatesResponseMultiError(errors)
	}

	return nil
}

// BlockExistingAuthFactorUpdatesResponseMultiError is an error wrapping
// multiple validation errors returned by
// BlockExistingAuthFactorUpdatesResponse.ValidateAll() if the designated
// constraints aren't met.
type BlockExistingAuthFactorUpdatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlockExistingAuthFactorUpdatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlockExistingAuthFactorUpdatesResponseMultiError) AllErrors() []error { return m }

// BlockExistingAuthFactorUpdatesResponseValidationError is the validation
// error returned by BlockExistingAuthFactorUpdatesResponse.Validate if the
// designated constraints aren't met.
type BlockExistingAuthFactorUpdatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlockExistingAuthFactorUpdatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlockExistingAuthFactorUpdatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlockExistingAuthFactorUpdatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlockExistingAuthFactorUpdatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlockExistingAuthFactorUpdatesResponseValidationError) ErrorName() string {
	return "BlockExistingAuthFactorUpdatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BlockExistingAuthFactorUpdatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlockExistingAuthFactorUpdatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlockExistingAuthFactorUpdatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlockExistingAuthFactorUpdatesResponseValidationError{}

// Validate checks the field values on GetTroubleshootingDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTroubleshootingDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTroubleshootingDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTroubleshootingDetailsRequestMultiError, or nil if none found.
func (m *GetTroubleshootingDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTroubleshootingDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetTroubleshootingDetailsRequestMultiError(errors)
	}

	return nil
}

// GetTroubleshootingDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetTroubleshootingDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTroubleshootingDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTroubleshootingDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTroubleshootingDetailsRequestMultiError) AllErrors() []error { return m }

// GetTroubleshootingDetailsRequestValidationError is the validation error
// returned by GetTroubleshootingDetailsRequest.Validate if the designated
// constraints aren't met.
type GetTroubleshootingDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTroubleshootingDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTroubleshootingDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTroubleshootingDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTroubleshootingDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTroubleshootingDetailsRequestValidationError) ErrorName() string {
	return "GetTroubleshootingDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTroubleshootingDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTroubleshootingDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTroubleshootingDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTroubleshootingDetailsRequestValidationError{}

// Validate checks the field values on GetTroubleshootingDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTroubleshootingDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTroubleshootingDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetTroubleshootingDetailsResponseMultiError, or nil if none found.
func (m *GetTroubleshootingDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTroubleshootingDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTroubleshootingDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTroubleshootingDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTroubleshootingDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTroubleshootingDetailsResponseValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTroubleshootingDetailsResponseValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTroubleshootingDetailsResponseValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTroubleshootingDetailsResponseMultiError(errors)
	}

	return nil
}

// GetTroubleshootingDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetTroubleshootingDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTroubleshootingDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTroubleshootingDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTroubleshootingDetailsResponseMultiError) AllErrors() []error { return m }

// GetTroubleshootingDetailsResponseValidationError is the validation error
// returned by GetTroubleshootingDetailsResponse.Validate if the designated
// constraints aren't met.
type GetTroubleshootingDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTroubleshootingDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTroubleshootingDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTroubleshootingDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTroubleshootingDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTroubleshootingDetailsResponseValidationError) ErrorName() string {
	return "GetTroubleshootingDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTroubleshootingDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTroubleshootingDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTroubleshootingDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTroubleshootingDetailsResponseValidationError{}

// Validate checks the field values on GetAFUSummariesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAFUSummariesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAFUSummariesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAFUSummariesRequestMultiError, or nil if none found.
func (m *GetAFUSummariesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAFUSummariesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAFUSummariesRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAFUSummariesRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAFUSummariesRequestValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAFUSummariesRequestMultiError(errors)
	}

	return nil
}

// GetAFUSummariesRequestMultiError is an error wrapping multiple validation
// errors returned by GetAFUSummariesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAFUSummariesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAFUSummariesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAFUSummariesRequestMultiError) AllErrors() []error { return m }

// GetAFUSummariesRequestValidationError is the validation error returned by
// GetAFUSummariesRequest.Validate if the designated constraints aren't met.
type GetAFUSummariesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAFUSummariesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAFUSummariesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAFUSummariesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAFUSummariesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAFUSummariesRequestValidationError) ErrorName() string {
	return "GetAFUSummariesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAFUSummariesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAFUSummariesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAFUSummariesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAFUSummariesRequestValidationError{}

// Validate checks the field values on GetAFUSummariesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAFUSummariesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAFUSummariesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAFUSummariesResponseMultiError, or nil if none found.
func (m *GetAFUSummariesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAFUSummariesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAFUSummariesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAFUSummariesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAFUSummariesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAfuSummaries() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAFUSummariesResponseValidationError{
						field:  fmt.Sprintf("AfuSummaries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAFUSummariesResponseValidationError{
						field:  fmt.Sprintf("AfuSummaries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAFUSummariesResponseValidationError{
					field:  fmt.Sprintf("AfuSummaries[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAFUSummariesResponseMultiError(errors)
	}

	return nil
}

// GetAFUSummariesResponseMultiError is an error wrapping multiple validation
// errors returned by GetAFUSummariesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAFUSummariesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAFUSummariesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAFUSummariesResponseMultiError) AllErrors() []error { return m }

// GetAFUSummariesResponseValidationError is the validation error returned by
// GetAFUSummariesResponse.Validate if the designated constraints aren't met.
type GetAFUSummariesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAFUSummariesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAFUSummariesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAFUSummariesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAFUSummariesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAFUSummariesResponseValidationError) ErrorName() string {
	return "GetAFUSummariesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAFUSummariesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAFUSummariesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAFUSummariesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAFUSummariesResponseValidationError{}

// Validate checks the field values on RevokeOAuthTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RevokeOAuthTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RevokeOAuthTokenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RevokeOAuthTokenRequestMultiError, or nil if none found.
func (m *RevokeOAuthTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RevokeOAuthTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OauthToken

	// no validation rules for OauthProvider

	// no validation rules for ActorId

	if len(errors) > 0 {
		return RevokeOAuthTokenRequestMultiError(errors)
	}

	return nil
}

// RevokeOAuthTokenRequestMultiError is an error wrapping multiple validation
// errors returned by RevokeOAuthTokenRequest.ValidateAll() if the designated
// constraints aren't met.
type RevokeOAuthTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RevokeOAuthTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RevokeOAuthTokenRequestMultiError) AllErrors() []error { return m }

// RevokeOAuthTokenRequestValidationError is the validation error returned by
// RevokeOAuthTokenRequest.Validate if the designated constraints aren't met.
type RevokeOAuthTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RevokeOAuthTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RevokeOAuthTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RevokeOAuthTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RevokeOAuthTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RevokeOAuthTokenRequestValidationError) ErrorName() string {
	return "RevokeOAuthTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RevokeOAuthTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRevokeOAuthTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RevokeOAuthTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RevokeOAuthTokenRequestValidationError{}

// Validate checks the field values on RevokeOAuthTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RevokeOAuthTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RevokeOAuthTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RevokeOAuthTokenResponseMultiError, or nil if none found.
func (m *RevokeOAuthTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RevokeOAuthTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RevokeOAuthTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RevokeOAuthTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RevokeOAuthTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RevokeOAuthTokenResponseMultiError(errors)
	}

	return nil
}

// RevokeOAuthTokenResponseMultiError is an error wrapping multiple validation
// errors returned by RevokeOAuthTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type RevokeOAuthTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RevokeOAuthTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RevokeOAuthTokenResponseMultiError) AllErrors() []error { return m }

// RevokeOAuthTokenResponseValidationError is the validation error returned by
// RevokeOAuthTokenResponse.Validate if the designated constraints aren't met.
type RevokeOAuthTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RevokeOAuthTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RevokeOAuthTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RevokeOAuthTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RevokeOAuthTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RevokeOAuthTokenResponseValidationError) ErrorName() string {
	return "RevokeOAuthTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RevokeOAuthTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRevokeOAuthTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RevokeOAuthTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RevokeOAuthTokenResponseValidationError{}

// Validate checks the field values on GetDeviceIntegrityStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeviceIntegrityStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceIntegrityStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetDeviceIntegrityStatusRequestMultiError, or nil if none found.
func (m *GetDeviceIntegrityStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceIntegrityStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeviceIntegrityToken

	// no validation rules for DeviceId

	if len(errors) > 0 {
		return GetDeviceIntegrityStatusRequestMultiError(errors)
	}

	return nil
}

// GetDeviceIntegrityStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetDeviceIntegrityStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type GetDeviceIntegrityStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceIntegrityStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceIntegrityStatusRequestMultiError) AllErrors() []error { return m }

// GetDeviceIntegrityStatusRequestValidationError is the validation error
// returned by GetDeviceIntegrityStatusRequest.Validate if the designated
// constraints aren't met.
type GetDeviceIntegrityStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceIntegrityStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceIntegrityStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceIntegrityStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceIntegrityStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceIntegrityStatusRequestValidationError) ErrorName() string {
	return "GetDeviceIntegrityStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeviceIntegrityStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceIntegrityStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceIntegrityStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceIntegrityStatusRequestValidationError{}

// Validate checks the field values on GetDeviceIntegrityStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetDeviceIntegrityStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceIntegrityStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetDeviceIntegrityStatusResponseMultiError, or nil if none found.
func (m *GetDeviceIntegrityStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceIntegrityStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceIntegrityStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceIntegrityStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceIntegrityStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeviceIntegrityStatus

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceIntegrityStatusResponseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceIntegrityStatusResponseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceIntegrityStatusResponseValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDeviceIntegrityStatusResponseMultiError(errors)
	}

	return nil
}

// GetDeviceIntegrityStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetDeviceIntegrityStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDeviceIntegrityStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceIntegrityStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceIntegrityStatusResponseMultiError) AllErrors() []error { return m }

// GetDeviceIntegrityStatusResponseValidationError is the validation error
// returned by GetDeviceIntegrityStatusResponse.Validate if the designated
// constraints aren't met.
type GetDeviceIntegrityStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceIntegrityStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceIntegrityStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceIntegrityStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceIntegrityStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceIntegrityStatusResponseValidationError) ErrorName() string {
	return "GetDeviceIntegrityStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeviceIntegrityStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceIntegrityStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceIntegrityStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceIntegrityStatusResponseValidationError{}

// Validate checks the field values on DeactivateDeviceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeactivateDeviceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeactivateDeviceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeactivateDeviceRequestMultiError, or nil if none found.
func (m *DeactivateDeviceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeactivateDeviceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for DeactivationType

	if len(errors) > 0 {
		return DeactivateDeviceRequestMultiError(errors)
	}

	return nil
}

// DeactivateDeviceRequestMultiError is an error wrapping multiple validation
// errors returned by DeactivateDeviceRequest.ValidateAll() if the designated
// constraints aren't met.
type DeactivateDeviceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeactivateDeviceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeactivateDeviceRequestMultiError) AllErrors() []error { return m }

// DeactivateDeviceRequestValidationError is the validation error returned by
// DeactivateDeviceRequest.Validate if the designated constraints aren't met.
type DeactivateDeviceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeactivateDeviceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeactivateDeviceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeactivateDeviceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeactivateDeviceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeactivateDeviceRequestValidationError) ErrorName() string {
	return "DeactivateDeviceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeactivateDeviceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeactivateDeviceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeactivateDeviceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeactivateDeviceRequestValidationError{}

// Validate checks the field values on DeactivateDeviceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeactivateDeviceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeactivateDeviceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeactivateDeviceResponseMultiError, or nil if none found.
func (m *DeactivateDeviceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeactivateDeviceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivateDeviceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivateDeviceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivateDeviceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeactivateDeviceResponseMultiError(errors)
	}

	return nil
}

// DeactivateDeviceResponseMultiError is an error wrapping multiple validation
// errors returned by DeactivateDeviceResponse.ValidateAll() if the designated
// constraints aren't met.
type DeactivateDeviceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeactivateDeviceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeactivateDeviceResponseMultiError) AllErrors() []error { return m }

// DeactivateDeviceResponseValidationError is the validation error returned by
// DeactivateDeviceResponse.Validate if the designated constraints aren't met.
type DeactivateDeviceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeactivateDeviceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeactivateDeviceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeactivateDeviceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeactivateDeviceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeactivateDeviceResponseValidationError) ErrorName() string {
	return "DeactivateDeviceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeactivateDeviceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeactivateDeviceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeactivateDeviceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeactivateDeviceResponseValidationError{}

// Validate checks the field values on ProcessAFURiskVerdictRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessAFURiskVerdictRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessAFURiskVerdictRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessAFURiskVerdictRequestMultiError, or nil if none found.
func (m *ProcessAFURiskVerdictRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessAFURiskVerdictRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AfuId

	// no validation rules for ActorId

	// no validation rules for CaseId

	// no validation rules for Verdict

	if len(errors) > 0 {
		return ProcessAFURiskVerdictRequestMultiError(errors)
	}

	return nil
}

// ProcessAFURiskVerdictRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessAFURiskVerdictRequest.ValidateAll() if
// the designated constraints aren't met.
type ProcessAFURiskVerdictRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessAFURiskVerdictRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessAFURiskVerdictRequestMultiError) AllErrors() []error { return m }

// ProcessAFURiskVerdictRequestValidationError is the validation error returned
// by ProcessAFURiskVerdictRequest.Validate if the designated constraints
// aren't met.
type ProcessAFURiskVerdictRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessAFURiskVerdictRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessAFURiskVerdictRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessAFURiskVerdictRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessAFURiskVerdictRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessAFURiskVerdictRequestValidationError) ErrorName() string {
	return "ProcessAFURiskVerdictRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessAFURiskVerdictRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessAFURiskVerdictRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessAFURiskVerdictRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessAFURiskVerdictRequestValidationError{}

// Validate checks the field values on ProcessAFURiskVerdictResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessAFURiskVerdictResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessAFURiskVerdictResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessAFURiskVerdictResponseMultiError, or nil if none found.
func (m *ProcessAFURiskVerdictResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessAFURiskVerdictResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessAFURiskVerdictResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessAFURiskVerdictResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessAFURiskVerdictResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessAFURiskVerdictResponseMultiError(errors)
	}

	return nil
}

// ProcessAFURiskVerdictResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessAFURiskVerdictResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessAFURiskVerdictResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessAFURiskVerdictResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessAFURiskVerdictResponseMultiError) AllErrors() []error { return m }

// ProcessAFURiskVerdictResponseValidationError is the validation error
// returned by ProcessAFURiskVerdictResponse.Validate if the designated
// constraints aren't met.
type ProcessAFURiskVerdictResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessAFURiskVerdictResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessAFURiskVerdictResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessAFURiskVerdictResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessAFURiskVerdictResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessAFURiskVerdictResponseValidationError) ErrorName() string {
	return "ProcessAFURiskVerdictResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessAFURiskVerdictResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessAFURiskVerdictResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessAFURiskVerdictResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessAFURiskVerdictResponseValidationError{}

// Validate checks the field values on VerifyPasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyPasswordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyPasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyPasswordRequestMultiError, or nil if none found.
func (m *VerifyPasswordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyPasswordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EncryptedPassword

	// no validation rules for PasswordFlow

	switch v := m.UserIdentifier.(type) {
	case *VerifyPasswordRequest_ActorId:
		if v == nil {
			err := VerifyPasswordRequestValidationError{
				field:  "UserIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return VerifyPasswordRequestMultiError(errors)
	}

	return nil
}

// VerifyPasswordRequestMultiError is an error wrapping multiple validation
// errors returned by VerifyPasswordRequest.ValidateAll() if the designated
// constraints aren't met.
type VerifyPasswordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyPasswordRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyPasswordRequestMultiError) AllErrors() []error { return m }

// VerifyPasswordRequestValidationError is the validation error returned by
// VerifyPasswordRequest.Validate if the designated constraints aren't met.
type VerifyPasswordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyPasswordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyPasswordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyPasswordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyPasswordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyPasswordRequestValidationError) ErrorName() string {
	return "VerifyPasswordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyPasswordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyPasswordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyPasswordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyPasswordRequestValidationError{}

// Validate checks the field values on VerifyPasswordResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyPasswordResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyPasswordResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyPasswordResponseMultiError, or nil if none found.
func (m *VerifyPasswordResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyPasswordResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyPasswordResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyPasswordResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyPasswordResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyPasswordResponseMultiError(errors)
	}

	return nil
}

// VerifyPasswordResponseMultiError is an error wrapping multiple validation
// errors returned by VerifyPasswordResponse.ValidateAll() if the designated
// constraints aren't met.
type VerifyPasswordResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyPasswordResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyPasswordResponseMultiError) AllErrors() []error { return m }

// VerifyPasswordResponseValidationError is the validation error returned by
// VerifyPasswordResponse.Validate if the designated constraints aren't met.
type VerifyPasswordResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyPasswordResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyPasswordResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyPasswordResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyPasswordResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyPasswordResponseValidationError) ErrorName() string {
	return "VerifyPasswordResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyPasswordResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyPasswordResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyPasswordResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyPasswordResponseValidationError{}

// Validate checks the field values on ResetPasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetPasswordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetPasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetPasswordRequestMultiError, or nil if none found.
func (m *ResetPasswordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetPasswordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EncryptedOldPassword

	// no validation rules for EncryptedNewPassword

	// no validation rules for PasswordFlow

	// no validation rules for ForceReset

	switch v := m.UserIdentifier.(type) {
	case *ResetPasswordRequest_ActorId:
		if v == nil {
			err := ResetPasswordRequestValidationError{
				field:  "UserIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ResetPasswordRequestMultiError(errors)
	}

	return nil
}

// ResetPasswordRequestMultiError is an error wrapping multiple validation
// errors returned by ResetPasswordRequest.ValidateAll() if the designated
// constraints aren't met.
type ResetPasswordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetPasswordRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetPasswordRequestMultiError) AllErrors() []error { return m }

// ResetPasswordRequestValidationError is the validation error returned by
// ResetPasswordRequest.Validate if the designated constraints aren't met.
type ResetPasswordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetPasswordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetPasswordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetPasswordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetPasswordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetPasswordRequestValidationError) ErrorName() string {
	return "ResetPasswordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResetPasswordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetPasswordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetPasswordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetPasswordRequestValidationError{}

// Validate checks the field values on ResetPasswordResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetPasswordResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetPasswordResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetPasswordResponseMultiError, or nil if none found.
func (m *ResetPasswordResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetPasswordResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResetPasswordResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResetPasswordResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResetPasswordResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResetPasswordResponseMultiError(errors)
	}

	return nil
}

// ResetPasswordResponseMultiError is an error wrapping multiple validation
// errors returned by ResetPasswordResponse.ValidateAll() if the designated
// constraints aren't met.
type ResetPasswordResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetPasswordResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetPasswordResponseMultiError) AllErrors() []error { return m }

// ResetPasswordResponseValidationError is the validation error returned by
// ResetPasswordResponse.Validate if the designated constraints aren't met.
type ResetPasswordResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetPasswordResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetPasswordResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetPasswordResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetPasswordResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetPasswordResponseValidationError) ErrorName() string {
	return "ResetPasswordResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ResetPasswordResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetPasswordResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetPasswordResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetPasswordResponseValidationError{}

// Validate checks the field values on SetDefaultPasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetDefaultPasswordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetDefaultPasswordRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetDefaultPasswordRequestMultiError, or nil if none found.
func (m *SetDefaultPasswordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetDefaultPasswordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PasswordFlow

	switch v := m.UserIdentifier.(type) {
	case *SetDefaultPasswordRequest_ActorId:
		if v == nil {
			err := SetDefaultPasswordRequestValidationError{
				field:  "UserIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SetDefaultPasswordRequestMultiError(errors)
	}

	return nil
}

// SetDefaultPasswordRequestMultiError is an error wrapping multiple validation
// errors returned by SetDefaultPasswordRequest.ValidateAll() if the
// designated constraints aren't met.
type SetDefaultPasswordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetDefaultPasswordRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetDefaultPasswordRequestMultiError) AllErrors() []error { return m }

// SetDefaultPasswordRequestValidationError is the validation error returned by
// SetDefaultPasswordRequest.Validate if the designated constraints aren't met.
type SetDefaultPasswordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetDefaultPasswordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetDefaultPasswordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetDefaultPasswordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetDefaultPasswordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetDefaultPasswordRequestValidationError) ErrorName() string {
	return "SetDefaultPasswordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetDefaultPasswordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetDefaultPasswordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetDefaultPasswordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetDefaultPasswordRequestValidationError{}

// Validate checks the field values on SetDefaultPasswordResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetDefaultPasswordResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetDefaultPasswordResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetDefaultPasswordResponseMultiError, or nil if none found.
func (m *SetDefaultPasswordResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SetDefaultPasswordResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetDefaultPasswordResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetDefaultPasswordResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetDefaultPasswordResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Password

	if len(errors) > 0 {
		return SetDefaultPasswordResponseMultiError(errors)
	}

	return nil
}

// SetDefaultPasswordResponseMultiError is an error wrapping multiple
// validation errors returned by SetDefaultPasswordResponse.ValidateAll() if
// the designated constraints aren't met.
type SetDefaultPasswordResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetDefaultPasswordResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetDefaultPasswordResponseMultiError) AllErrors() []error { return m }

// SetDefaultPasswordResponseValidationError is the validation error returned
// by SetDefaultPasswordResponse.Validate if the designated constraints aren't met.
type SetDefaultPasswordResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetDefaultPasswordResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetDefaultPasswordResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetDefaultPasswordResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetDefaultPasswordResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetDefaultPasswordResponseValidationError) ErrorName() string {
	return "SetDefaultPasswordResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SetDefaultPasswordResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetDefaultPasswordResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetDefaultPasswordResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetDefaultPasswordResponseValidationError{}

// Validate checks the field values on CreateHandshakeTokenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateHandshakeTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateHandshakeTokenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateHandshakeTokenRequestMultiError, or nil if none found.
func (m *CreateHandshakeTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateHandshakeTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TokenType

	switch v := m.TokenMetadata.(type) {
	case *CreateHandshakeTokenRequest_BkycTokenMetadata:
		if v == nil {
			err := CreateHandshakeTokenRequestValidationError{
				field:  "TokenMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBkycTokenMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateHandshakeTokenRequestValidationError{
						field:  "BkycTokenMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateHandshakeTokenRequestValidationError{
						field:  "BkycTokenMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBkycTokenMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateHandshakeTokenRequestValidationError{
					field:  "BkycTokenMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CreateHandshakeTokenRequestMultiError(errors)
	}

	return nil
}

// CreateHandshakeTokenRequestMultiError is an error wrapping multiple
// validation errors returned by CreateHandshakeTokenRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateHandshakeTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateHandshakeTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateHandshakeTokenRequestMultiError) AllErrors() []error { return m }

// CreateHandshakeTokenRequestValidationError is the validation error returned
// by CreateHandshakeTokenRequest.Validate if the designated constraints
// aren't met.
type CreateHandshakeTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateHandshakeTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateHandshakeTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateHandshakeTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateHandshakeTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateHandshakeTokenRequestValidationError) ErrorName() string {
	return "CreateHandshakeTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateHandshakeTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateHandshakeTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateHandshakeTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateHandshakeTokenRequestValidationError{}

// Validate checks the field values on BKYCTokenMetadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BKYCTokenMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BKYCTokenMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BKYCTokenMetadataMultiError, or nil if none found.
func (m *BKYCTokenMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *BKYCTokenMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequesterActorId

	// no validation rules for ProviderActorId

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BKYCTokenMetadataValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BKYCTokenMetadataValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BKYCTokenMetadataValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BKYCTokenMetadataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BKYCTokenMetadataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BKYCTokenMetadataValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	if len(errors) > 0 {
		return BKYCTokenMetadataMultiError(errors)
	}

	return nil
}

// BKYCTokenMetadataMultiError is an error wrapping multiple validation errors
// returned by BKYCTokenMetadata.ValidateAll() if the designated constraints
// aren't met.
type BKYCTokenMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BKYCTokenMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BKYCTokenMetadataMultiError) AllErrors() []error { return m }

// BKYCTokenMetadataValidationError is the validation error returned by
// BKYCTokenMetadata.Validate if the designated constraints aren't met.
type BKYCTokenMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BKYCTokenMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BKYCTokenMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BKYCTokenMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BKYCTokenMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BKYCTokenMetadataValidationError) ErrorName() string {
	return "BKYCTokenMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e BKYCTokenMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBKYCTokenMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BKYCTokenMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BKYCTokenMetadataValidationError{}

// Validate checks the field values on CreateHandshakeTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateHandshakeTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateHandshakeTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateHandshakeTokenResponseMultiError, or nil if none found.
func (m *CreateHandshakeTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateHandshakeTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateHandshakeTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateHandshakeTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateHandshakeTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	if len(errors) > 0 {
		return CreateHandshakeTokenResponseMultiError(errors)
	}

	return nil
}

// CreateHandshakeTokenResponseMultiError is an error wrapping multiple
// validation errors returned by CreateHandshakeTokenResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateHandshakeTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateHandshakeTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateHandshakeTokenResponseMultiError) AllErrors() []error { return m }

// CreateHandshakeTokenResponseValidationError is the validation error returned
// by CreateHandshakeTokenResponse.Validate if the designated constraints
// aren't met.
type CreateHandshakeTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateHandshakeTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateHandshakeTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateHandshakeTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateHandshakeTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateHandshakeTokenResponseValidationError) ErrorName() string {
	return "CreateHandshakeTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateHandshakeTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateHandshakeTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateHandshakeTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateHandshakeTokenResponseValidationError{}

// Validate checks the field values on VerifyHandshakeTokenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyHandshakeTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyHandshakeTokenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyHandshakeTokenRequestMultiError, or nil if none found.
func (m *VerifyHandshakeTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyHandshakeTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TokenType

	// no validation rules for Token

	if len(errors) > 0 {
		return VerifyHandshakeTokenRequestMultiError(errors)
	}

	return nil
}

// VerifyHandshakeTokenRequestMultiError is an error wrapping multiple
// validation errors returned by VerifyHandshakeTokenRequest.ValidateAll() if
// the designated constraints aren't met.
type VerifyHandshakeTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyHandshakeTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyHandshakeTokenRequestMultiError) AllErrors() []error { return m }

// VerifyHandshakeTokenRequestValidationError is the validation error returned
// by VerifyHandshakeTokenRequest.Validate if the designated constraints
// aren't met.
type VerifyHandshakeTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyHandshakeTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyHandshakeTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyHandshakeTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyHandshakeTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyHandshakeTokenRequestValidationError) ErrorName() string {
	return "VerifyHandshakeTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyHandshakeTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyHandshakeTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyHandshakeTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyHandshakeTokenRequestValidationError{}

// Validate checks the field values on VerifyHandshakeTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyHandshakeTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyHandshakeTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyHandshakeTokenResponseMultiError, or nil if none found.
func (m *VerifyHandshakeTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyHandshakeTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyHandshakeTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyHandshakeTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyHandshakeTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequesterActorId

	// no validation rules for ProviderActorId

	if len(errors) > 0 {
		return VerifyHandshakeTokenResponseMultiError(errors)
	}

	return nil
}

// VerifyHandshakeTokenResponseMultiError is an error wrapping multiple
// validation errors returned by VerifyHandshakeTokenResponse.ValidateAll() if
// the designated constraints aren't met.
type VerifyHandshakeTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyHandshakeTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyHandshakeTokenResponseMultiError) AllErrors() []error { return m }

// VerifyHandshakeTokenResponseValidationError is the validation error returned
// by VerifyHandshakeTokenResponse.Validate if the designated constraints
// aren't met.
type VerifyHandshakeTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyHandshakeTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyHandshakeTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyHandshakeTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyHandshakeTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyHandshakeTokenResponseValidationError) ErrorName() string {
	return "VerifyHandshakeTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyHandshakeTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyHandshakeTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyHandshakeTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyHandshakeTokenResponseValidationError{}

// Validate checks the field values on TriggerInitHandshakeCommsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TriggerInitHandshakeCommsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerInitHandshakeCommsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerInitHandshakeCommsRequestMultiError, or nil if none found.
func (m *TriggerInitHandshakeCommsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerInitHandshakeCommsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for HandshakeFlow

	if len(errors) > 0 {
		return TriggerInitHandshakeCommsRequestMultiError(errors)
	}

	return nil
}

// TriggerInitHandshakeCommsRequestMultiError is an error wrapping multiple
// validation errors returned by
// TriggerInitHandshakeCommsRequest.ValidateAll() if the designated
// constraints aren't met.
type TriggerInitHandshakeCommsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerInitHandshakeCommsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerInitHandshakeCommsRequestMultiError) AllErrors() []error { return m }

// TriggerInitHandshakeCommsRequestValidationError is the validation error
// returned by TriggerInitHandshakeCommsRequest.Validate if the designated
// constraints aren't met.
type TriggerInitHandshakeCommsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerInitHandshakeCommsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerInitHandshakeCommsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerInitHandshakeCommsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerInitHandshakeCommsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerInitHandshakeCommsRequestValidationError) ErrorName() string {
	return "TriggerInitHandshakeCommsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerInitHandshakeCommsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerInitHandshakeCommsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerInitHandshakeCommsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerInitHandshakeCommsRequestValidationError{}

// Validate checks the field values on TriggerInitHandshakeCommsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TriggerInitHandshakeCommsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerInitHandshakeCommsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// TriggerInitHandshakeCommsResponseMultiError, or nil if none found.
func (m *TriggerInitHandshakeCommsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerInitHandshakeCommsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TriggerInitHandshakeCommsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TriggerInitHandshakeCommsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TriggerInitHandshakeCommsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TriggerInitHandshakeCommsResponseMultiError(errors)
	}

	return nil
}

// TriggerInitHandshakeCommsResponseMultiError is an error wrapping multiple
// validation errors returned by
// TriggerInitHandshakeCommsResponse.ValidateAll() if the designated
// constraints aren't met.
type TriggerInitHandshakeCommsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerInitHandshakeCommsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerInitHandshakeCommsResponseMultiError) AllErrors() []error { return m }

// TriggerInitHandshakeCommsResponseValidationError is the validation error
// returned by TriggerInitHandshakeCommsResponse.Validate if the designated
// constraints aren't met.
type TriggerInitHandshakeCommsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerInitHandshakeCommsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerInitHandshakeCommsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerInitHandshakeCommsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerInitHandshakeCommsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerInitHandshakeCommsResponseValidationError) ErrorName() string {
	return "TriggerInitHandshakeCommsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerInitHandshakeCommsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerInitHandshakeCommsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerInitHandshakeCommsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerInitHandshakeCommsResponseValidationError{}

// Validate checks the field values on RecordReRegisteredDeviceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecordReRegisteredDeviceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordReRegisteredDeviceRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RecordReRegisteredDeviceRequestMultiError, or nil if none found.
func (m *RecordReRegisteredDeviceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordReRegisteredDeviceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordReRegisteredDeviceRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordReRegisteredDeviceRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordReRegisteredDeviceRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VendorDeviceId

	// no validation rules for Vendor

	// no validation rules for SimId

	// no validation rules for VendorRequestId

	if len(errors) > 0 {
		return RecordReRegisteredDeviceRequestMultiError(errors)
	}

	return nil
}

// RecordReRegisteredDeviceRequestMultiError is an error wrapping multiple
// validation errors returned by RecordReRegisteredDeviceRequest.ValidateAll()
// if the designated constraints aren't met.
type RecordReRegisteredDeviceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordReRegisteredDeviceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordReRegisteredDeviceRequestMultiError) AllErrors() []error { return m }

// RecordReRegisteredDeviceRequestValidationError is the validation error
// returned by RecordReRegisteredDeviceRequest.Validate if the designated
// constraints aren't met.
type RecordReRegisteredDeviceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordReRegisteredDeviceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordReRegisteredDeviceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordReRegisteredDeviceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordReRegisteredDeviceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordReRegisteredDeviceRequestValidationError) ErrorName() string {
	return "RecordReRegisteredDeviceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecordReRegisteredDeviceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordReRegisteredDeviceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordReRegisteredDeviceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordReRegisteredDeviceRequestValidationError{}

// Validate checks the field values on RecordReRegisteredDeviceResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecordReRegisteredDeviceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordReRegisteredDeviceResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RecordReRegisteredDeviceResponseMultiError, or nil if none found.
func (m *RecordReRegisteredDeviceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordReRegisteredDeviceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordReRegisteredDeviceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordReRegisteredDeviceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordReRegisteredDeviceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordReRegisteredDeviceResponseMultiError(errors)
	}

	return nil
}

// RecordReRegisteredDeviceResponseMultiError is an error wrapping multiple
// validation errors returned by
// RecordReRegisteredDeviceResponse.ValidateAll() if the designated
// constraints aren't met.
type RecordReRegisteredDeviceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordReRegisteredDeviceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordReRegisteredDeviceResponseMultiError) AllErrors() []error { return m }

// RecordReRegisteredDeviceResponseValidationError is the validation error
// returned by RecordReRegisteredDeviceResponse.Validate if the designated
// constraints aren't met.
type RecordReRegisteredDeviceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordReRegisteredDeviceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordReRegisteredDeviceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordReRegisteredDeviceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordReRegisteredDeviceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordReRegisteredDeviceResponseValidationError) ErrorName() string {
	return "RecordReRegisteredDeviceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecordReRegisteredDeviceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordReRegisteredDeviceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordReRegisteredDeviceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordReRegisteredDeviceResponseValidationError{}

// Validate checks the field values on IsDeviceRegistrationAllowedRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *IsDeviceRegistrationAllowedRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsDeviceRegistrationAllowedRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// IsDeviceRegistrationAllowedRequestMultiError, or nil if none found.
func (m *IsDeviceRegistrationAllowedRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IsDeviceRegistrationAllowedRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for DeviceId

	if len(errors) > 0 {
		return IsDeviceRegistrationAllowedRequestMultiError(errors)
	}

	return nil
}

// IsDeviceRegistrationAllowedRequestMultiError is an error wrapping multiple
// validation errors returned by
// IsDeviceRegistrationAllowedRequest.ValidateAll() if the designated
// constraints aren't met.
type IsDeviceRegistrationAllowedRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsDeviceRegistrationAllowedRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsDeviceRegistrationAllowedRequestMultiError) AllErrors() []error { return m }

// IsDeviceRegistrationAllowedRequestValidationError is the validation error
// returned by IsDeviceRegistrationAllowedRequest.Validate if the designated
// constraints aren't met.
type IsDeviceRegistrationAllowedRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsDeviceRegistrationAllowedRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsDeviceRegistrationAllowedRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsDeviceRegistrationAllowedRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsDeviceRegistrationAllowedRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsDeviceRegistrationAllowedRequestValidationError) ErrorName() string {
	return "IsDeviceRegistrationAllowedRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IsDeviceRegistrationAllowedRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsDeviceRegistrationAllowedRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsDeviceRegistrationAllowedRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsDeviceRegistrationAllowedRequestValidationError{}

// Validate checks the field values on IsDeviceRegistrationAllowedResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *IsDeviceRegistrationAllowedResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsDeviceRegistrationAllowedResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// IsDeviceRegistrationAllowedResponseMultiError, or nil if none found.
func (m *IsDeviceRegistrationAllowedResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IsDeviceRegistrationAllowedResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsDeviceRegistrationAllowedResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsDeviceRegistrationAllowedResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsDeviceRegistrationAllowedResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsAllowed

	// no validation rules for Reason

	if len(errors) > 0 {
		return IsDeviceRegistrationAllowedResponseMultiError(errors)
	}

	return nil
}

// IsDeviceRegistrationAllowedResponseMultiError is an error wrapping multiple
// validation errors returned by
// IsDeviceRegistrationAllowedResponse.ValidateAll() if the designated
// constraints aren't met.
type IsDeviceRegistrationAllowedResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsDeviceRegistrationAllowedResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsDeviceRegistrationAllowedResponseMultiError) AllErrors() []error { return m }

// IsDeviceRegistrationAllowedResponseValidationError is the validation error
// returned by IsDeviceRegistrationAllowedResponse.Validate if the designated
// constraints aren't met.
type IsDeviceRegistrationAllowedResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsDeviceRegistrationAllowedResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsDeviceRegistrationAllowedResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsDeviceRegistrationAllowedResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsDeviceRegistrationAllowedResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsDeviceRegistrationAllowedResponseValidationError) ErrorName() string {
	return "IsDeviceRegistrationAllowedResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IsDeviceRegistrationAllowedResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsDeviceRegistrationAllowedResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsDeviceRegistrationAllowedResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsDeviceRegistrationAllowedResponseValidationError{}

// Validate checks the field values on GetDeviceRegSMSAckInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeviceRegSMSAckInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceRegSMSAckInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetDeviceRegSMSAckInfoRequestMultiError, or nil if none found.
func (m *GetDeviceRegSMSAckInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceRegSMSAckInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for DeviceId

	if len(errors) > 0 {
		return GetDeviceRegSMSAckInfoRequestMultiError(errors)
	}

	return nil
}

// GetDeviceRegSMSAckInfoRequestMultiError is an error wrapping multiple
// validation errors returned by GetDeviceRegSMSAckInfoRequest.ValidateAll()
// if the designated constraints aren't met.
type GetDeviceRegSMSAckInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceRegSMSAckInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceRegSMSAckInfoRequestMultiError) AllErrors() []error { return m }

// GetDeviceRegSMSAckInfoRequestValidationError is the validation error
// returned by GetDeviceRegSMSAckInfoRequest.Validate if the designated
// constraints aren't met.
type GetDeviceRegSMSAckInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceRegSMSAckInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceRegSMSAckInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceRegSMSAckInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceRegSMSAckInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceRegSMSAckInfoRequestValidationError) ErrorName() string {
	return "GetDeviceRegSMSAckInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeviceRegSMSAckInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceRegSMSAckInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceRegSMSAckInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceRegSMSAckInfoRequestValidationError{}

// Validate checks the field values on GetDeviceRegSMSAckInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeviceRegSMSAckInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceRegSMSAckInfoResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetDeviceRegSMSAckInfoResponseMultiError, or nil if none found.
func (m *GetDeviceRegSMSAckInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceRegSMSAckInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceRegSMSAckInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceRegSMSAckInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceRegSMSAckInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsAcknowledged

	if all {
		switch v := interface{}(m.GetAttemptedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceRegSMSAckInfoResponseValidationError{
					field:  "AttemptedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceRegSMSAckInfoResponseValidationError{
					field:  "AttemptedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAttemptedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceRegSMSAckInfoResponseValidationError{
				field:  "AttemptedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAckAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceRegSMSAckInfoResponseValidationError{
					field:  "AckAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceRegSMSAckInfoResponseValidationError{
					field:  "AckAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAckAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceRegSMSAckInfoResponseValidationError{
				field:  "AckAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDeviceRegSMSAckInfoResponseMultiError(errors)
	}

	return nil
}

// GetDeviceRegSMSAckInfoResponseMultiError is an error wrapping multiple
// validation errors returned by GetDeviceRegSMSAckInfoResponse.ValidateAll()
// if the designated constraints aren't met.
type GetDeviceRegSMSAckInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceRegSMSAckInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceRegSMSAckInfoResponseMultiError) AllErrors() []error { return m }

// GetDeviceRegSMSAckInfoResponseValidationError is the validation error
// returned by GetDeviceRegSMSAckInfoResponse.Validate if the designated
// constraints aren't met.
type GetDeviceRegSMSAckInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceRegSMSAckInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceRegSMSAckInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceRegSMSAckInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceRegSMSAckInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceRegSMSAckInfoResponseValidationError) ErrorName() string {
	return "GetDeviceRegSMSAckInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeviceRegSMSAckInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceRegSMSAckInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceRegSMSAckInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceRegSMSAckInfoResponseValidationError{}

// Validate checks the field values on BackFillSimInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BackFillSimInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BackFillSimInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BackFillSimInfoRequestMultiError, or nil if none found.
func (m *BackFillSimInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BackFillSimInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for DeviceId

	// no validation rules for SimId

	if len(errors) > 0 {
		return BackFillSimInfoRequestMultiError(errors)
	}

	return nil
}

// BackFillSimInfoRequestMultiError is an error wrapping multiple validation
// errors returned by BackFillSimInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type BackFillSimInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BackFillSimInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BackFillSimInfoRequestMultiError) AllErrors() []error { return m }

// BackFillSimInfoRequestValidationError is the validation error returned by
// BackFillSimInfoRequest.Validate if the designated constraints aren't met.
type BackFillSimInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BackFillSimInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BackFillSimInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BackFillSimInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BackFillSimInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BackFillSimInfoRequestValidationError) ErrorName() string {
	return "BackFillSimInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BackFillSimInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBackFillSimInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BackFillSimInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BackFillSimInfoRequestValidationError{}

// Validate checks the field values on BackFillSimInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BackFillSimInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BackFillSimInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BackFillSimInfoResponseMultiError, or nil if none found.
func (m *BackFillSimInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BackFillSimInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BackFillSimInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BackFillSimInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BackFillSimInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BackFillSimInfoResponseMultiError(errors)
	}

	return nil
}

// BackFillSimInfoResponseMultiError is an error wrapping multiple validation
// errors returned by BackFillSimInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type BackFillSimInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BackFillSimInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BackFillSimInfoResponseMultiError) AllErrors() []error { return m }

// BackFillSimInfoResponseValidationError is the validation error returned by
// BackFillSimInfoResponse.Validate if the designated constraints aren't met.
type BackFillSimInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BackFillSimInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BackFillSimInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BackFillSimInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BackFillSimInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BackFillSimInfoResponseValidationError) ErrorName() string {
	return "BackFillSimInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BackFillSimInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBackFillSimInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BackFillSimInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BackFillSimInfoResponseValidationError{}

// Validate checks the field values on GetAuthFactorCooldownInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAuthFactorCooldownInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthFactorCooldownInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAuthFactorCooldownInfoRequestMultiError, or nil if none found.
func (m *GetAuthFactorCooldownInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthFactorCooldownInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetAuthFactorCooldownInfoRequestMultiError(errors)
	}

	return nil
}

// GetAuthFactorCooldownInfoRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAuthFactorCooldownInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAuthFactorCooldownInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthFactorCooldownInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthFactorCooldownInfoRequestMultiError) AllErrors() []error { return m }

// GetAuthFactorCooldownInfoRequestValidationError is the validation error
// returned by GetAuthFactorCooldownInfoRequest.Validate if the designated
// constraints aren't met.
type GetAuthFactorCooldownInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthFactorCooldownInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthFactorCooldownInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthFactorCooldownInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthFactorCooldownInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthFactorCooldownInfoRequestValidationError) ErrorName() string {
	return "GetAuthFactorCooldownInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthFactorCooldownInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthFactorCooldownInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthFactorCooldownInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthFactorCooldownInfoRequestValidationError{}

// Validate checks the field values on GetAuthFactorCooldownInfoResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAuthFactorCooldownInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthFactorCooldownInfoResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAuthFactorCooldownInfoResponseMultiError, or nil if none found.
func (m *GetAuthFactorCooldownInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthFactorCooldownInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAuthFactorCooldownInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAuthFactorCooldownInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAuthFactorCooldownInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCooldownInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAuthFactorCooldownInfoResponseValidationError{
						field:  fmt.Sprintf("CooldownInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAuthFactorCooldownInfoResponseValidationError{
						field:  fmt.Sprintf("CooldownInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAuthFactorCooldownInfoResponseValidationError{
					field:  fmt.Sprintf("CooldownInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAuthFactorCooldownInfoResponseMultiError(errors)
	}

	return nil
}

// GetAuthFactorCooldownInfoResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetAuthFactorCooldownInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAuthFactorCooldownInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthFactorCooldownInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthFactorCooldownInfoResponseMultiError) AllErrors() []error { return m }

// GetAuthFactorCooldownInfoResponseValidationError is the validation error
// returned by GetAuthFactorCooldownInfoResponse.Validate if the designated
// constraints aren't met.
type GetAuthFactorCooldownInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthFactorCooldownInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthFactorCooldownInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthFactorCooldownInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthFactorCooldownInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthFactorCooldownInfoResponseValidationError) ErrorName() string {
	return "GetAuthFactorCooldownInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthFactorCooldownInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthFactorCooldownInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthFactorCooldownInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthFactorCooldownInfoResponseValidationError{}

// Validate checks the field values on AuthFactorCooldownInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthFactorCooldownInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthFactorCooldownInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthFactorCooldownInfoMultiError, or nil if none found.
func (m *AuthFactorCooldownInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthFactorCooldownInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AuthFactor

	// no validation rules for DaysRemaining

	// no validation rules for IsInCooldown

	if len(errors) > 0 {
		return AuthFactorCooldownInfoMultiError(errors)
	}

	return nil
}

// AuthFactorCooldownInfoMultiError is an error wrapping multiple validation
// errors returned by AuthFactorCooldownInfo.ValidateAll() if the designated
// constraints aren't met.
type AuthFactorCooldownInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthFactorCooldownInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthFactorCooldownInfoMultiError) AllErrors() []error { return m }

// AuthFactorCooldownInfoValidationError is the validation error returned by
// AuthFactorCooldownInfo.Validate if the designated constraints aren't met.
type AuthFactorCooldownInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthFactorCooldownInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthFactorCooldownInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthFactorCooldownInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthFactorCooldownInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthFactorCooldownInfoValidationError) ErrorName() string {
	return "AuthFactorCooldownInfoValidationError"
}

// Error satisfies the builtin error interface
func (e AuthFactorCooldownInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthFactorCooldownInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthFactorCooldownInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthFactorCooldownInfoValidationError{}
