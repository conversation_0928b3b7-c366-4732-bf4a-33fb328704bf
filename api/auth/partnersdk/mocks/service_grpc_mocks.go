// Code generated by MockGen. DO NOT EDIT.
// Source: api/auth/partnersdk/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	partnersdk "github.com/epifi/gamma/api/auth/partnersdk"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockPartnerSDKClient is a mock of PartnerSDKClient interface.
type MockPartnerSDKClient struct {
	ctrl     *gomock.Controller
	recorder *MockPartnerSDKClientMockRecorder
}

// MockPartnerSDKClientMockRecorder is the mock recorder for MockPartnerSDKClient.
type MockPartnerSDKClientMockRecorder struct {
	mock *MockPartnerSDKClient
}

// NewMockPartnerSDKClient creates a new mock instance.
func NewMockPartnerSDKClient(ctrl *gomock.Controller) *MockPartnerSDKClient {
	mock := &MockPartnerSDKClient{ctrl: ctrl}
	mock.recorder = &MockPartnerSDKClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPartnerSDKClient) EXPECT() *MockPartnerSDKClientMockRecorder {
	return m.recorder
}

// GetSessionParams mocks base method.
func (m *MockPartnerSDKClient) GetSessionParams(ctx context.Context, in *partnersdk.GetSessionParamsRequest, opts ...grpc.CallOption) (*partnersdk.GetSessionParamsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSessionParams", varargs...)
	ret0, _ := ret[0].(*partnersdk.GetSessionParamsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSessionParams indicates an expected call of GetSessionParams.
func (mr *MockPartnerSDKClientMockRecorder) GetSessionParams(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSessionParams", reflect.TypeOf((*MockPartnerSDKClient)(nil).GetSessionParams), varargs...)
}

// MockPartnerSDKServer is a mock of PartnerSDKServer interface.
type MockPartnerSDKServer struct {
	ctrl     *gomock.Controller
	recorder *MockPartnerSDKServerMockRecorder
}

// MockPartnerSDKServerMockRecorder is the mock recorder for MockPartnerSDKServer.
type MockPartnerSDKServerMockRecorder struct {
	mock *MockPartnerSDKServer
}

// NewMockPartnerSDKServer creates a new mock instance.
func NewMockPartnerSDKServer(ctrl *gomock.Controller) *MockPartnerSDKServer {
	mock := &MockPartnerSDKServer{ctrl: ctrl}
	mock.recorder = &MockPartnerSDKServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPartnerSDKServer) EXPECT() *MockPartnerSDKServerMockRecorder {
	return m.recorder
}

// GetSessionParams mocks base method.
func (m *MockPartnerSDKServer) GetSessionParams(arg0 context.Context, arg1 *partnersdk.GetSessionParamsRequest) (*partnersdk.GetSessionParamsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSessionParams", arg0, arg1)
	ret0, _ := ret[0].(*partnersdk.GetSessionParamsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSessionParams indicates an expected call of GetSessionParams.
func (mr *MockPartnerSDKServerMockRecorder) GetSessionParams(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSessionParams", reflect.TypeOf((*MockPartnerSDKServer)(nil).GetSessionParams), arg0, arg1)
}

// MockUnsafePartnerSDKServer is a mock of UnsafePartnerSDKServer interface.
type MockUnsafePartnerSDKServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafePartnerSDKServerMockRecorder
}

// MockUnsafePartnerSDKServerMockRecorder is the mock recorder for MockUnsafePartnerSDKServer.
type MockUnsafePartnerSDKServerMockRecorder struct {
	mock *MockUnsafePartnerSDKServer
}

// NewMockUnsafePartnerSDKServer creates a new mock instance.
func NewMockUnsafePartnerSDKServer(ctrl *gomock.Controller) *MockUnsafePartnerSDKServer {
	mock := &MockUnsafePartnerSDKServer{ctrl: ctrl}
	mock.recorder = &MockUnsafePartnerSDKServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafePartnerSDKServer) EXPECT() *MockUnsafePartnerSDKServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedPartnerSDKServer mocks base method.
func (m *MockUnsafePartnerSDKServer) mustEmbedUnimplementedPartnerSDKServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedPartnerSDKServer")
}

// mustEmbedUnimplementedPartnerSDKServer indicates an expected call of mustEmbedUnimplementedPartnerSDKServer.
func (mr *MockUnsafePartnerSDKServerMockRecorder) mustEmbedUnimplementedPartnerSDKServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedPartnerSDKServer", reflect.TypeOf((*MockUnsafePartnerSDKServer)(nil).mustEmbedUnimplementedPartnerSDKServer))
}
