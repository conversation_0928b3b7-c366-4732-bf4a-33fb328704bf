// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/partnersdk/credblock.proto

package partnersdk

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CredBlock with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CredBlock) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CredBlock with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CredBlockMultiError, or nil
// if none found.
func (m *CredBlock) ValidateAll() error {
	return m.validate(true)
}

func (m *CredBlock) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CredentialType

	// no validation rules for EncryptedCredential

	// no validation rules for EcdhPk

	if len(errors) > 0 {
		return CredBlockMultiError(errors)
	}

	return nil
}

// CredBlockMultiError is an error wrapping multiple validation errors returned
// by CredBlock.ValidateAll() if the designated constraints aren't met.
type CredBlockMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CredBlockMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CredBlockMultiError) AllErrors() []error { return m }

// CredBlockValidationError is the validation error returned by
// CredBlock.Validate if the designated constraints aren't met.
type CredBlockValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CredBlockValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CredBlockValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CredBlockValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CredBlockValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CredBlockValidationError) ErrorName() string { return "CredBlockValidationError" }

// Error satisfies the builtin error interface
func (e CredBlockValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCredBlock.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CredBlockValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CredBlockValidationError{}

// Validate checks the field values on PartnerSdkCred with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PartnerSdkCred) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PartnerSdkCred with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PartnerSdkCredMultiError,
// or nil if none found.
func (m *PartnerSdkCred) ValidateAll() error {
	return m.validate(true)
}

func (m *PartnerSdkCred) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeviceId

	// no validation rules for PartnerSdkCredBlock

	if len(errors) > 0 {
		return PartnerSdkCredMultiError(errors)
	}

	return nil
}

// PartnerSdkCredMultiError is an error wrapping multiple validation errors
// returned by PartnerSdkCred.ValidateAll() if the designated constraints
// aren't met.
type PartnerSdkCredMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PartnerSdkCredMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PartnerSdkCredMultiError) AllErrors() []error { return m }

// PartnerSdkCredValidationError is the validation error returned by
// PartnerSdkCred.Validate if the designated constraints aren't met.
type PartnerSdkCredValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PartnerSdkCredValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PartnerSdkCredValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PartnerSdkCredValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PartnerSdkCredValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PartnerSdkCredValidationError) ErrorName() string { return "PartnerSdkCredValidationError" }

// Error satisfies the builtin error interface
func (e PartnerSdkCredValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPartnerSdkCred.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PartnerSdkCredValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PartnerSdkCredValidationError{}
