// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/partnersdk/credblock.proto

package partnersdk

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// An enum to represent the type of PIN
type CredentialType int32

const (
	CredentialType_CREDENTIAL_TYPE_UNSPECIFIED CredentialType = 0
	CredentialType_NEW_CARD_PIN                CredentialType = 1
	CredentialType_CARD_PIN                    CredentialType = 2
	CredentialType_SECURE_PIN                  CredentialType = 3
	CredentialType_OTP                         CredentialType = 5
)

// Enum value maps for CredentialType.
var (
	CredentialType_name = map[int32]string{
		0: "CREDENTIAL_TYPE_UNSPECIFIED",
		1: "NEW_CARD_PIN",
		2: "CARD_PIN",
		3: "SECURE_PIN",
		5: "OTP",
	}
	CredentialType_value = map[string]int32{
		"CREDENTIAL_TYPE_UNSPECIFIED": 0,
		"NEW_CARD_PIN":                1,
		"CARD_PIN":                    2,
		"SECURE_PIN":                  3,
		"OTP":                         5,
	}
)

func (x CredentialType) Enum() *CredentialType {
	p := new(CredentialType)
	*p = x
	return p
}

func (x CredentialType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CredentialType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_partnersdk_credblock_proto_enumTypes[0].Descriptor()
}

func (CredentialType) Type() protoreflect.EnumType {
	return &file_api_auth_partnersdk_credblock_proto_enumTypes[0]
}

func (x CredentialType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CredentialType.Descriptor instead.
func (CredentialType) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_partnersdk_credblock_proto_rawDescGZIP(), []int{0}
}

type CredBlock struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CredentialType      CredentialType `protobuf:"varint,1,opt,name=credential_type,json=CredentialType,proto3,enum=auth.partnersdk.CredentialType" json:"credential_type,omitempty"`
	EncryptedCredential string         `protobuf:"bytes,2,opt,name=encrypted_credential,json=EncryptedCredential,proto3" json:"encrypted_credential,omitempty"`
	// Ephemeral public key that is used in
	// Elliptic curve's Diffie hellman(ECDH) key agreement protocol
	// to generate the keys to encrypt the payload i.e., keys
	EcdhPk string `protobuf:"bytes,3,opt,name=ecdh_pk,json=Ecdh_PK,proto3" json:"ecdh_pk,omitempty"`
}

func (x *CredBlock) Reset() {
	*x = CredBlock{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_partnersdk_credblock_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CredBlock) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CredBlock) ProtoMessage() {}

func (x *CredBlock) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_partnersdk_credblock_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CredBlock.ProtoReflect.Descriptor instead.
func (*CredBlock) Descriptor() ([]byte, []int) {
	return file_api_auth_partnersdk_credblock_proto_rawDescGZIP(), []int{0}
}

func (x *CredBlock) GetCredentialType() CredentialType {
	if x != nil {
		return x.CredentialType
	}
	return CredentialType_CREDENTIAL_TYPE_UNSPECIFIED
}

func (x *CredBlock) GetEncryptedCredential() string {
	if x != nil {
		return x.EncryptedCredential
	}
	return ""
}

func (x *CredBlock) GetEcdhPk() string {
	if x != nil {
		return x.EcdhPk
	}
	return ""
}

// Fields required to send partner sdk related cred block and data
type PartnerSdkCred struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// device id for which partner sdk cred block is generated
	DeviceId string `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// Financial transactions such as Intra Bank, NEFT, IMPS RTGS require `Secure PIN`
	// as second factor authentication. Secure PIN is the same as UPI PIN
	//
	// However, the encrypted PIN is generated through Partner bank's SDK and is a
	// Base64 encoded JSON string that can be passed as it is
	//
	// Partner bank evaluates the risk of transaction based on multiple attributes e.g:
	//   - Amount of the transaction
	//   - Time when the transaction is initiated
	//   - Location from where the transaction is initiated
	//
	// Based on the evaluated risk, `cred_block` may be required to complete the transaction
	// In absence of `cred_block` being set, appropriate error may be returned by the partner bank,
	// if `cred_block` is required
	PartnerSdkCredBlock string `protobuf:"bytes,2,opt,name=partner_sdk_cred_block,json=partnerSdkCredBlock,proto3" json:"partner_sdk_cred_block,omitempty"`
}

func (x *PartnerSdkCred) Reset() {
	*x = PartnerSdkCred{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_partnersdk_credblock_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartnerSdkCred) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartnerSdkCred) ProtoMessage() {}

func (x *PartnerSdkCred) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_partnersdk_credblock_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartnerSdkCred.ProtoReflect.Descriptor instead.
func (*PartnerSdkCred) Descriptor() ([]byte, []int) {
	return file_api_auth_partnersdk_credblock_proto_rawDescGZIP(), []int{1}
}

func (x *PartnerSdkCred) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *PartnerSdkCred) GetPartnerSdkCredBlock() string {
	if x != nil {
		return x.PartnerSdkCredBlock
	}
	return ""
}

var File_api_auth_partnersdk_credblock_proto protoreflect.FileDescriptor

var file_api_auth_partnersdk_credblock_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x73, 0x64, 0x6b, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x73, 0x64, 0x6b, 0x22, 0xa2, 0x01, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x64, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x48, 0x0a, 0x0f, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x73, 0x64, 0x6b, 0x2e,
	0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e,
	0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31,
	0x0a, 0x14, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x45, 0x6e,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61,
	0x6c, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x63, 0x64, 0x68, 0x5f, 0x70, 0x6b, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x45, 0x63, 0x64, 0x68, 0x5f, 0x50, 0x4b, 0x22, 0x62, 0x0a, 0x0e, 0x50,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x53, 0x64, 0x6b, 0x43, 0x72, 0x65, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x16, 0x70, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x73, 0x64, 0x6b, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x53, 0x64, 0x6b, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2a,
	0x6a, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x52, 0x45, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x41, 0x4c, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50,
	0x49, 0x4e, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x49, 0x4e,
	0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x49, 0x4e,
	0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x4f, 0x54, 0x50, 0x10, 0x05, 0x42, 0x58, 0x0a, 0x2a, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x70,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x73, 0x64, 0x6b, 0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x73, 0x64, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_partnersdk_credblock_proto_rawDescOnce sync.Once
	file_api_auth_partnersdk_credblock_proto_rawDescData = file_api_auth_partnersdk_credblock_proto_rawDesc
)

func file_api_auth_partnersdk_credblock_proto_rawDescGZIP() []byte {
	file_api_auth_partnersdk_credblock_proto_rawDescOnce.Do(func() {
		file_api_auth_partnersdk_credblock_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_partnersdk_credblock_proto_rawDescData)
	})
	return file_api_auth_partnersdk_credblock_proto_rawDescData
}

var file_api_auth_partnersdk_credblock_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_partnersdk_credblock_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_auth_partnersdk_credblock_proto_goTypes = []interface{}{
	(CredentialType)(0),    // 0: auth.partnersdk.CredentialType
	(*CredBlock)(nil),      // 1: auth.partnersdk.CredBlock
	(*PartnerSdkCred)(nil), // 2: auth.partnersdk.PartnerSdkCred
}
var file_api_auth_partnersdk_credblock_proto_depIdxs = []int32{
	0, // 0: auth.partnersdk.CredBlock.credential_type:type_name -> auth.partnersdk.CredentialType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_auth_partnersdk_credblock_proto_init() }
func file_api_auth_partnersdk_credblock_proto_init() {
	if File_api_auth_partnersdk_credblock_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_partnersdk_credblock_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CredBlock); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_partnersdk_credblock_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartnerSdkCred); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_partnersdk_credblock_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_partnersdk_credblock_proto_goTypes,
		DependencyIndexes: file_api_auth_partnersdk_credblock_proto_depIdxs,
		EnumInfos:         file_api_auth_partnersdk_credblock_proto_enumTypes,
		MessageInfos:      file_api_auth_partnersdk_credblock_proto_msgTypes,
	}.Build()
	File_api_auth_partnersdk_credblock_proto = out.File
	file_api_auth_partnersdk_credblock_proto_rawDesc = nil
	file_api_auth_partnersdk_credblock_proto_goTypes = nil
	file_api_auth_partnersdk_credblock_proto_depIdxs = nil
}
