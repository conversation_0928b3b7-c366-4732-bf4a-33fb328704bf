// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/partnersdk/service.proto

package partnersdk

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.Platform(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on GetSessionParamsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSessionParamsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSessionParamsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSessionParamsRequestMultiError, or nil if none found.
func (m *GetSessionParamsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSessionParamsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for DeviceId

	// no validation rules for Refresh

	if all {
		switch v := interface{}(m.GetSessionParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSessionParamsRequestValidationError{
					field:  "SessionParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSessionParamsRequestValidationError{
					field:  "SessionParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSessionParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSessionParamsRequestValidationError{
				field:  "SessionParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _GetSessionParamsRequest_Vendor_NotInLookup[m.GetVendor()]; ok {
		err := GetSessionParamsRequestValidationError{
			field:  "Vendor",
			reason: "value must not be in list [VENDOR_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _GetSessionParamsRequest_Platform_NotInLookup[m.GetPlatform()]; ok {
		err := GetSessionParamsRequestValidationError{
			field:  "Platform",
			reason: "value must not be in list [PLATFORM_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _GetSessionParamsRequest_AppPlatform_NotInLookup[m.GetAppPlatform()]; ok {
		err := GetSessionParamsRequestValidationError{
			field:  "AppPlatform",
			reason: "value must not be in list [PLATFORM_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetSessionParamsRequestMultiError(errors)
	}

	return nil
}

// GetSessionParamsRequestMultiError is an error wrapping multiple validation
// errors returned by GetSessionParamsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSessionParamsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSessionParamsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSessionParamsRequestMultiError) AllErrors() []error { return m }

// GetSessionParamsRequestValidationError is the validation error returned by
// GetSessionParamsRequest.Validate if the designated constraints aren't met.
type GetSessionParamsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSessionParamsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSessionParamsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSessionParamsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSessionParamsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSessionParamsRequestValidationError) ErrorName() string {
	return "GetSessionParamsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSessionParamsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSessionParamsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSessionParamsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSessionParamsRequestValidationError{}

var _GetSessionParamsRequest_Vendor_NotInLookup = map[vendorgateway.Vendor]struct{}{
	0: {},
}

var _GetSessionParamsRequest_Platform_NotInLookup = map[Platform]struct{}{
	0: {},
}

var _GetSessionParamsRequest_AppPlatform_NotInLookup = map[common.Platform]struct{}{
	0: {},
}

// Validate checks the field values on GetSessionParamsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSessionParamsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSessionParamsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSessionParamsResponseMultiError, or nil if none found.
func (m *GetSessionParamsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSessionParamsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSessionParamsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSessionParamsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSessionParamsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AppHmacKey

	// no validation rules for EncryptedEcdhPk

	// no validation rules for Signature

	// no validation rules for SignatureKeyId

	if all {
		switch v := interface{}(m.GetBankConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSessionParamsResponseValidationError{
					field:  "BankConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSessionParamsResponseValidationError{
					field:  "BankConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSessionParamsResponseValidationError{
				field:  "BankConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSessionParamsResponseMultiError(errors)
	}

	return nil
}

// GetSessionParamsResponseMultiError is an error wrapping multiple validation
// errors returned by GetSessionParamsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSessionParamsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSessionParamsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSessionParamsResponseMultiError) AllErrors() []error { return m }

// GetSessionParamsResponseValidationError is the validation error returned by
// GetSessionParamsResponse.Validate if the designated constraints aren't met.
type GetSessionParamsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSessionParamsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSessionParamsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSessionParamsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSessionParamsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSessionParamsResponseValidationError) ErrorName() string {
	return "GetSessionParamsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSessionParamsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSessionParamsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSessionParamsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSessionParamsResponseValidationError{}

// Validate checks the field values on BankConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BankConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BankConfigMultiError, or
// nil if none found.
func (m *BankConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *BankConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BankLogoUrl

	// no validation rules for BankName

	// no validation rules for CardPinLength

	// no validation rules for SecurePinLength

	// no validation rules for VendorOtpLength

	if len(errors) > 0 {
		return BankConfigMultiError(errors)
	}

	return nil
}

// BankConfigMultiError is an error wrapping multiple validation errors
// returned by BankConfig.ValidateAll() if the designated constraints aren't met.
type BankConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankConfigMultiError) AllErrors() []error { return m }

// BankConfigValidationError is the validation error returned by
// BankConfig.Validate if the designated constraints aren't met.
type BankConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankConfigValidationError) ErrorName() string { return "BankConfigValidationError" }

// Error satisfies the builtin error interface
func (e BankConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankConfigValidationError{}

// Validate checks the field values on GetSessionParamsRequest_SessionParams
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetSessionParamsRequest_SessionParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSessionParamsRequest_SessionParams
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSessionParamsRequest_SessionParamsMultiError, or nil if none found.
func (m *GetSessionParamsRequest_SessionParams) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSessionParamsRequest_SessionParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KeyId

	// no validation rules for EncryptedKeys

	// no validation rules for Hmac

	// no validation rules for EcdhPk

	if len(errors) > 0 {
		return GetSessionParamsRequest_SessionParamsMultiError(errors)
	}

	return nil
}

// GetSessionParamsRequest_SessionParamsMultiError is an error wrapping
// multiple validation errors returned by
// GetSessionParamsRequest_SessionParams.ValidateAll() if the designated
// constraints aren't met.
type GetSessionParamsRequest_SessionParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSessionParamsRequest_SessionParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSessionParamsRequest_SessionParamsMultiError) AllErrors() []error { return m }

// GetSessionParamsRequest_SessionParamsValidationError is the validation error
// returned by GetSessionParamsRequest_SessionParams.Validate if the
// designated constraints aren't met.
type GetSessionParamsRequest_SessionParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSessionParamsRequest_SessionParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSessionParamsRequest_SessionParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSessionParamsRequest_SessionParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSessionParamsRequest_SessionParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSessionParamsRequest_SessionParamsValidationError) ErrorName() string {
	return "GetSessionParamsRequest_SessionParamsValidationError"
}

// Error satisfies the builtin error interface
func (e GetSessionParamsRequest_SessionParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSessionParamsRequest_SessionParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSessionParamsRequest_SessionParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSessionParamsRequest_SessionParamsValidationError{}
