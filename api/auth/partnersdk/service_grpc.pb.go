// RPCs related to Partner SDK's frontend service

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/auth/partnersdk/service.proto

package partnersdk

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PartnerSDK_GetSessionParams_FullMethodName = "/auth.partnersdk.PartnerSDK/GetSessionParams"
)

// PartnerSDKClient is the client API for PartnerSDK service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PartnerSDKClient interface {
	// GetSessionParams registers the client details at partner bank
	// and responds with session parameters. Client can make use of
	// these parameters to securely interact with SDK and partner bank
	GetSessionParams(ctx context.Context, in *GetSessionParamsRequest, opts ...grpc.CallOption) (*GetSessionParamsResponse, error)
}

type partnerSDKClient struct {
	cc grpc.ClientConnInterface
}

func NewPartnerSDKClient(cc grpc.ClientConnInterface) PartnerSDKClient {
	return &partnerSDKClient{cc}
}

func (c *partnerSDKClient) GetSessionParams(ctx context.Context, in *GetSessionParamsRequest, opts ...grpc.CallOption) (*GetSessionParamsResponse, error) {
	out := new(GetSessionParamsResponse)
	err := c.cc.Invoke(ctx, PartnerSDK_GetSessionParams_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PartnerSDKServer is the server API for PartnerSDK service.
// All implementations should embed UnimplementedPartnerSDKServer
// for forward compatibility
type PartnerSDKServer interface {
	// GetSessionParams registers the client details at partner bank
	// and responds with session parameters. Client can make use of
	// these parameters to securely interact with SDK and partner bank
	GetSessionParams(context.Context, *GetSessionParamsRequest) (*GetSessionParamsResponse, error)
}

// UnimplementedPartnerSDKServer should be embedded to have forward compatible implementations.
type UnimplementedPartnerSDKServer struct {
}

func (UnimplementedPartnerSDKServer) GetSessionParams(context.Context, *GetSessionParamsRequest) (*GetSessionParamsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSessionParams not implemented")
}

// UnsafePartnerSDKServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PartnerSDKServer will
// result in compilation errors.
type UnsafePartnerSDKServer interface {
	mustEmbedUnimplementedPartnerSDKServer()
}

func RegisterPartnerSDKServer(s grpc.ServiceRegistrar, srv PartnerSDKServer) {
	s.RegisterService(&PartnerSDK_ServiceDesc, srv)
}

func _PartnerSDK_GetSessionParams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSessionParamsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PartnerSDKServer).GetSessionParams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PartnerSDK_GetSessionParams_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PartnerSDKServer).GetSessionParams(ctx, req.(*GetSessionParamsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PartnerSDK_ServiceDesc is the grpc.ServiceDesc for PartnerSDK service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PartnerSDK_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "auth.partnersdk.PartnerSDK",
	HandlerType: (*PartnerSDKServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSessionParams",
			Handler:    _PartnerSDK_GetSessionParams_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/auth/partnersdk/service.proto",
}
