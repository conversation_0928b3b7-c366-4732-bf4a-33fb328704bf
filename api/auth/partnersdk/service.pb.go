// RPCs related to Partner SDK's frontend service

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/partnersdk/service.proto

package partnersdk

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetSessionParamsResponse_Status int32

const (
	// Request has been processed successfully
	GetSessionParamsResponse_OK GetSessionParamsResponse_Status = 0
	// Invalid argument passed in the request
	GetSessionParamsResponse_INVALID_ARGUMENT GetSessionParamsResponse_Status = 3
	// Bank account not found with the given parameters
	GetSessionParamsResponse_RECORD_NOT_FOUND GetSessionParamsResponse_Status = 5
	// Internal server error
	GetSessionParamsResponse_INTERNAL GetSessionParamsResponse_Status = 13
	// Public key of the partner bank is compromised - use backup key
	// Upgrade SDK if backup key is also compromised
	GetSessionParamsResponse_COMPROMISED_PARTNER_PUB_KEY GetSessionParamsResponse_Status = 100
	// Public key used in the request is invalid
	// Use the right key
	// e.g., If backup key is used when the primary key is active
	// (or) if key ID is invalid
	GetSessionParamsResponse_INVALID_PARTNER_PUB_KEY GetSessionParamsResponse_Status = 101
	// HMac verification failed
	GetSessionParamsResponse_INVALID_HMAC GetSessionParamsResponse_Status = 102
	// Previous session not found in case of Refresh
	GetSessionParamsResponse_INVALID_REFRESH_ATTEMPT GetSessionParamsResponse_Status = 103
)

// Enum value maps for GetSessionParamsResponse_Status.
var (
	GetSessionParamsResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		5:   "RECORD_NOT_FOUND",
		13:  "INTERNAL",
		100: "COMPROMISED_PARTNER_PUB_KEY",
		101: "INVALID_PARTNER_PUB_KEY",
		102: "INVALID_HMAC",
		103: "INVALID_REFRESH_ATTEMPT",
	}
	GetSessionParamsResponse_Status_value = map[string]int32{
		"OK":                          0,
		"INVALID_ARGUMENT":            3,
		"RECORD_NOT_FOUND":            5,
		"INTERNAL":                    13,
		"COMPROMISED_PARTNER_PUB_KEY": 100,
		"INVALID_PARTNER_PUB_KEY":     101,
		"INVALID_HMAC":                102,
		"INVALID_REFRESH_ATTEMPT":     103,
	}
)

func (x GetSessionParamsResponse_Status) Enum() *GetSessionParamsResponse_Status {
	p := new(GetSessionParamsResponse_Status)
	*p = x
	return p
}

func (x GetSessionParamsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetSessionParamsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_partnersdk_service_proto_enumTypes[0].Descriptor()
}

func (GetSessionParamsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_auth_partnersdk_service_proto_enumTypes[0]
}

func (x GetSessionParamsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetSessionParamsResponse_Status.Descriptor instead.
func (GetSessionParamsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_partnersdk_service_proto_rawDescGZIP(), []int{1, 0}
}

type GetSessionParamsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifier of the actor that initiated the request
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Device ID from where the request has originated
	DeviceId string `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// Determines if the client is refreshing the session params
	// (or) if this is fresh session registration
	//
	// A session parameter is active for 30 days from the time
	// it is created and client is expected to refresh the
	// parameters post this duration
	//
	// Client can make use of this parameter to refresh the parameters
	Refresh       bool                                   `protobuf:"varint,3,opt,name=refresh,proto3" json:"refresh,omitempty"`
	SessionParams *GetSessionParamsRequest_SessionParams `protobuf:"bytes,4,opt,name=session_params,json=sessionParams,proto3" json:"session_params,omitempty"`
	// Partner bank with whom the client is to be registered with
	Vendor vendorgateway.Vendor `protobuf:"varint,5,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	// app platform - Android, iOS etc.
	// deprecated in favour of api.typesv2.common.Platform, use app_platform instead
	//
	// Deprecated: Marked as deprecated in api/auth/partnersdk/service.proto.
	Platform Platform `protobuf:"varint,7,opt,name=platform,proto3,enum=auth.partnersdk.Platform" json:"platform,omitempty"`
	// app platform - Android, iOS etc.
	AppPlatform common.Platform `protobuf:"varint,8,opt,name=app_platform,json=appPlatform,proto3,enum=api.typesv2.common.Platform" json:"app_platform,omitempty"`
}

func (x *GetSessionParamsRequest) Reset() {
	*x = GetSessionParamsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_partnersdk_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionParamsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionParamsRequest) ProtoMessage() {}

func (x *GetSessionParamsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_partnersdk_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionParamsRequest.ProtoReflect.Descriptor instead.
func (*GetSessionParamsRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_partnersdk_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetSessionParamsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetSessionParamsRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *GetSessionParamsRequest) GetRefresh() bool {
	if x != nil {
		return x.Refresh
	}
	return false
}

func (x *GetSessionParamsRequest) GetSessionParams() *GetSessionParamsRequest_SessionParams {
	if x != nil {
		return x.SessionParams
	}
	return nil
}

func (x *GetSessionParamsRequest) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

// Deprecated: Marked as deprecated in api/auth/partnersdk/service.proto.
func (x *GetSessionParamsRequest) GetPlatform() Platform {
	if x != nil {
		return x.Platform
	}
	return Platform_PLATFORM_UNSPECIFIED
}

func (x *GetSessionParamsRequest) GetAppPlatform() common.Platform {
	if x != nil {
		return x.AppPlatform
	}
	return common.Platform(0)
}

type GetSessionParamsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// HMAC key that is to be used by the app
	// to generate HMAC for all the request payloads
	// between app and SDK
	// Base 64 encoded
	AppHmacKey string `protobuf:"bytes,2,opt,name=app_hmac_key,json=appHmacKey,proto3" json:"app_hmac_key,omitempty"`
	// ECDH's Public key to be registered with the SDK
	// Encrypted value is to be passed to SDK as it is
	// Base 64 encoded
	EncryptedEcdhPk string `protobuf:"bytes,3,opt,name=encrypted_ecdh_pk,json=encryptedEcdhPk,proto3" json:"encrypted_ecdh_pk,omitempty"`
	// Signature of the payload
	// Signature is to be passed to SDK as it is
	// Base 64 encoded
	Signature string `protobuf:"bytes,4,opt,name=signature,proto3" json:"signature,omitempty"`
	// There are multiple keys using which a partner bank
	// can sign a payload. Key ID identified the key
	// using which a payload has been signed
	// Signature's Key ID is to be passed to SDK as it is
	// Base 64 encoded
	SignatureKeyId string `protobuf:"bytes,5,opt,name=signature_key_id,json=signatureKeyId,proto3" json:"signature_key_id,omitempty"`
	// Bank config to render the pin page for sdk.
	BankConfig *BankConfig `protobuf:"bytes,6,opt,name=bank_config,json=bankConfig,proto3" json:"bank_config,omitempty"`
}

func (x *GetSessionParamsResponse) Reset() {
	*x = GetSessionParamsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_partnersdk_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionParamsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionParamsResponse) ProtoMessage() {}

func (x *GetSessionParamsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_partnersdk_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionParamsResponse.ProtoReflect.Descriptor instead.
func (*GetSessionParamsResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_partnersdk_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetSessionParamsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSessionParamsResponse) GetAppHmacKey() string {
	if x != nil {
		return x.AppHmacKey
	}
	return ""
}

func (x *GetSessionParamsResponse) GetEncryptedEcdhPk() string {
	if x != nil {
		return x.EncryptedEcdhPk
	}
	return ""
}

func (x *GetSessionParamsResponse) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *GetSessionParamsResponse) GetSignatureKeyId() string {
	if x != nil {
		return x.SignatureKeyId
	}
	return ""
}

func (x *GetSessionParamsResponse) GetBankConfig() *BankConfig {
	if x != nil {
		return x.BankConfig
	}
	return nil
}

type BankConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Bank logo url.
	// It will be used to show logo of bank at pin page.
	BankLogoUrl string `protobuf:"bytes,1,opt,name=bank_logo_url,json=bankLogoUrl,proto3" json:"bank_logo_url,omitempty"`
	// Bank name. It will be used to show bank name.
	BankName string `protobuf:"bytes,2,opt,name=bank_name,json=bankName,proto3" json:"bank_name,omitempty"`
	// Card pin length supported by bank.
	// Based on the value number of input field will be rendered to capture card PIN.
	CardPinLength uint32 `protobuf:"varint,3,opt,name=card_pin_length,json=cardPinLength,proto3" json:"card_pin_length,omitempty"`
	// UPI pin length supported by bank.
	// Based on the value number of input field will be rendered to capture UPI PIN.
	SecurePinLength uint32 `protobuf:"varint,4,opt,name=secure_pin_length,json=securePinLength,proto3" json:"secure_pin_length,omitempty"`
	// Otp length supported by bank for banking APIs.
	// Based on the value number of input field will be rendered to capture otp by cred block.
	VendorOtpLength uint32 `protobuf:"varint,5,opt,name=vendor_otp_length,json=vendorOtpLength,proto3" json:"vendor_otp_length,omitempty"`
}

func (x *BankConfig) Reset() {
	*x = BankConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_partnersdk_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankConfig) ProtoMessage() {}

func (x *BankConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_partnersdk_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankConfig.ProtoReflect.Descriptor instead.
func (*BankConfig) Descriptor() ([]byte, []int) {
	return file_api_auth_partnersdk_service_proto_rawDescGZIP(), []int{2}
}

func (x *BankConfig) GetBankLogoUrl() string {
	if x != nil {
		return x.BankLogoUrl
	}
	return ""
}

func (x *BankConfig) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

func (x *BankConfig) GetCardPinLength() uint32 {
	if x != nil {
		return x.CardPinLength
	}
	return 0
}

func (x *BankConfig) GetSecurePinLength() uint32 {
	if x != nil {
		return x.SecurePinLength
	}
	return 0
}

func (x *BankConfig) GetVendorOtpLength() uint32 {
	if x != nil {
		return x.VendorOtpLength
	}
	return 0
}

// List of session parameters that helps the partner bank
// to authenticate the request
type GetSessionParamsRequest_SessionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier of the partner bank's key
	// that is used in the process of generating
	// the session attributes
	KeyId string `protobuf:"bytes,1,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	// Initial session keys to register the client app
	// with the partner bank
	// Base 64 encoded
	EncryptedKeys string `protobuf:"bytes,2,opt,name=encrypted_keys,json=encryptedKeys,proto3" json:"encrypted_keys,omitempty"`
	// HMAC of keys
	// If HMAC verification fails, the request will be rejected
	// Base 64 encoded
	Hmac string `protobuf:"bytes,3,opt,name=hmac,proto3" json:"hmac,omitempty"`
	// Ephemeral public key that is used in
	// Elliptic curve's Diffie hellman(ECDH) key agreement protocol
	// to generate the keys to encrypt the payload i.e., keys
	// Base 64 encoded
	EcdhPk string `protobuf:"bytes,4,opt,name=ecdh_pk,json=ecdhPk,proto3" json:"ecdh_pk,omitempty"`
}

func (x *GetSessionParamsRequest_SessionParams) Reset() {
	*x = GetSessionParamsRequest_SessionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_partnersdk_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionParamsRequest_SessionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionParamsRequest_SessionParams) ProtoMessage() {}

func (x *GetSessionParamsRequest_SessionParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_partnersdk_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionParamsRequest_SessionParams.ProtoReflect.Descriptor instead.
func (*GetSessionParamsRequest_SessionParams) Descriptor() ([]byte, []int) {
	return file_api_auth_partnersdk_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GetSessionParamsRequest_SessionParams) GetKeyId() string {
	if x != nil {
		return x.KeyId
	}
	return ""
}

func (x *GetSessionParamsRequest_SessionParams) GetEncryptedKeys() string {
	if x != nil {
		return x.EncryptedKeys
	}
	return ""
}

func (x *GetSessionParamsRequest_SessionParams) GetHmac() string {
	if x != nil {
		return x.Hmac
	}
	return ""
}

func (x *GetSessionParamsRequest_SessionParams) GetEcdhPk() string {
	if x != nil {
		return x.EcdhPk
	}
	return ""
}

var File_api_auth_partnersdk_service_proto protoreflect.FileDescriptor

var file_api_auth_partnersdk_service_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x73, 0x64, 0x6b, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x73, 0x64, 0x6b, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x70,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x73, 0x64, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8d, 0x04, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x12, 0x5d, 0x0a, 0x0e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x73, 0x64, 0x6b,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x37, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x41,
	0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x19, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x73,
	0x64, 0x6b, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x20, 0x00, 0x18, 0x01, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x12, 0x49, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52,
	0x0b, 0x61, 0x70, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x1a, 0x7a, 0x0a, 0x0d,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x15, 0x0a,
	0x06, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6b,
	0x65, 0x79, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65,
	0x64, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x6e,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x68,
	0x6d, 0x61, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6d, 0x61, 0x63, 0x12,
	0x17, 0x0a, 0x07, 0x65, 0x63, 0x64, 0x68, 0x5f, 0x70, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x65, 0x63, 0x64, 0x68, 0x50, 0x6b, 0x22, 0xcd, 0x03, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x70,
	0x70, 0x5f, 0x68, 0x6d, 0x61, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x70, 0x70, 0x48, 0x6d, 0x61, 0x63, 0x4b, 0x65, 0x79, 0x12, 0x2a, 0x0a, 0x11,
	0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x63, 0x64, 0x68, 0x5f, 0x70,
	0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x65, 0x64, 0x45, 0x63, 0x64, 0x68, 0x50, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4b, 0x65, 0x79, 0x49, 0x64,
	0x12, 0x3c, 0x0a, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x70, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x73, 0x64, 0x6b, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0a, 0x62, 0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xb7,
	0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47,
	0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52,
	0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x1f, 0x0a, 0x1b, 0x43,
	0x4f, 0x4d, 0x50, 0x52, 0x4f, 0x4d, 0x49, 0x53, 0x45, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x4e,
	0x45, 0x52, 0x5f, 0x50, 0x55, 0x42, 0x5f, 0x4b, 0x45, 0x59, 0x10, 0x64, 0x12, 0x1b, 0x0a, 0x17,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x4e, 0x45, 0x52, 0x5f,
	0x50, 0x55, 0x42, 0x5f, 0x4b, 0x45, 0x59, 0x10, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x48, 0x4d, 0x41, 0x43, 0x10, 0x66, 0x12, 0x1b, 0x0a, 0x17, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48, 0x5f, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x10, 0x67, 0x22, 0xcd, 0x01, 0x0a, 0x0a, 0x42, 0x61, 0x6e,
	0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x62, 0x61, 0x6e, 0x6b, 0x4c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x62,
	0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x50, 0x69, 0x6e, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x6c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x65, 0x50, 0x69, 0x6e, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x2a, 0x0a, 0x11,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6f, 0x74, 0x70, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74,
	0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f,
	0x74, 0x70, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x32, 0x75, 0x0a, 0x0a, 0x50, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x53, 0x44, 0x4b, 0x12, 0x67, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x73, 0x64, 0x6b, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x73, 0x64, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x73, 0x64, 0x6b, 0x5a, 0x2a, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x70,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x73, 0x64, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_auth_partnersdk_service_proto_rawDescOnce sync.Once
	file_api_auth_partnersdk_service_proto_rawDescData = file_api_auth_partnersdk_service_proto_rawDesc
)

func file_api_auth_partnersdk_service_proto_rawDescGZIP() []byte {
	file_api_auth_partnersdk_service_proto_rawDescOnce.Do(func() {
		file_api_auth_partnersdk_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_partnersdk_service_proto_rawDescData)
	})
	return file_api_auth_partnersdk_service_proto_rawDescData
}

var file_api_auth_partnersdk_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_partnersdk_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_auth_partnersdk_service_proto_goTypes = []interface{}{
	(GetSessionParamsResponse_Status)(0),          // 0: auth.partnersdk.GetSessionParamsResponse.Status
	(*GetSessionParamsRequest)(nil),               // 1: auth.partnersdk.GetSessionParamsRequest
	(*GetSessionParamsResponse)(nil),              // 2: auth.partnersdk.GetSessionParamsResponse
	(*BankConfig)(nil),                            // 3: auth.partnersdk.BankConfig
	(*GetSessionParamsRequest_SessionParams)(nil), // 4: auth.partnersdk.GetSessionParamsRequest.SessionParams
	(vendorgateway.Vendor)(0),                     // 5: vendorgateway.Vendor
	(Platform)(0),                                 // 6: auth.partnersdk.Platform
	(common.Platform)(0),                          // 7: api.typesv2.common.Platform
	(*rpc.Status)(nil),                            // 8: rpc.Status
}
var file_api_auth_partnersdk_service_proto_depIdxs = []int32{
	4, // 0: auth.partnersdk.GetSessionParamsRequest.session_params:type_name -> auth.partnersdk.GetSessionParamsRequest.SessionParams
	5, // 1: auth.partnersdk.GetSessionParamsRequest.vendor:type_name -> vendorgateway.Vendor
	6, // 2: auth.partnersdk.GetSessionParamsRequest.platform:type_name -> auth.partnersdk.Platform
	7, // 3: auth.partnersdk.GetSessionParamsRequest.app_platform:type_name -> api.typesv2.common.Platform
	8, // 4: auth.partnersdk.GetSessionParamsResponse.status:type_name -> rpc.Status
	3, // 5: auth.partnersdk.GetSessionParamsResponse.bank_config:type_name -> auth.partnersdk.BankConfig
	1, // 6: auth.partnersdk.PartnerSDK.GetSessionParams:input_type -> auth.partnersdk.GetSessionParamsRequest
	2, // 7: auth.partnersdk.PartnerSDK.GetSessionParams:output_type -> auth.partnersdk.GetSessionParamsResponse
	7, // [7:8] is the sub-list for method output_type
	6, // [6:7] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_auth_partnersdk_service_proto_init() }
func file_api_auth_partnersdk_service_proto_init() {
	if File_api_auth_partnersdk_service_proto != nil {
		return
	}
	file_api_auth_partnersdk_platform_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_auth_partnersdk_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionParamsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_partnersdk_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionParamsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_partnersdk_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_partnersdk_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionParamsRequest_SessionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_partnersdk_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_auth_partnersdk_service_proto_goTypes,
		DependencyIndexes: file_api_auth_partnersdk_service_proto_depIdxs,
		EnumInfos:         file_api_auth_partnersdk_service_proto_enumTypes,
		MessageInfos:      file_api_auth_partnersdk_service_proto_msgTypes,
	}.Build()
	File_api_auth_partnersdk_service_proto = out.File
	file_api_auth_partnersdk_service_proto_rawDesc = nil
	file_api_auth_partnersdk_service_proto_goTypes = nil
	file_api_auth_partnersdk_service_proto_depIdxs = nil
}
