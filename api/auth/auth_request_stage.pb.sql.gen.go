// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /Users/<USER>/go/src/github.com/epifi/gamma/api/auth/auth_request_stage.pb.go

package auth

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the AuthRequestStageStatus in string format in DB
func (p AuthRequestStageStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing AuthRequestStageStatus while reading from DB
func (p *AuthRequestStageStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := AuthRequestStageStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected AuthRequestStageStatus value: %s", val)
	}
	*p = AuthRequestStageStatus(valInt)
	return nil
}

// Marshaler interface implementation for AuthRequestStageStatus
func (x AuthRequestStageStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for AuthRequestStageStatus
func (x *AuthRequestStageStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = AuthRequestStageStatus(AuthRequestStageStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the AuthStage in string format in DB
func (p AuthStage) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing AuthStage while reading from DB
func (p *AuthStage) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := AuthStage_value[val]
	if !ok {
		return fmt.Errorf("unexpected AuthStage value: %s", val)
	}
	*p = AuthStage(valInt)
	return nil
}

// Marshaler interface implementation for AuthStage
func (x AuthStage) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for AuthStage
func (x *AuthStage) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = AuthStage(AuthStage_value[val])
	return nil
}
