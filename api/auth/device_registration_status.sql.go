package auth

import (
	"database/sql/driver"
	"fmt"
)

// Helper methods to translate TokenType Enum in Code to String in DB and vice versa

// Value implements driver.Valuer interface It stores data as string in DB.
func (s DeviceRegistrationStatus) Value() (driver.Value, error) {
	return s.String(), nil
}

// <PERSON><PERSON> implements sql.Scanner interface.
// It parses the data from DB and converts to required type.
func (s *DeviceRegistrationStatus) Scan(src interface{}) error {
	dbVal, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", dbVal)
	}

	if valInt, ok := DeviceRegistrationStatus_value[dbVal]; ok {
		*s = DeviceRegistrationStatus(valInt)
		return nil
	}
	return fmt.Errorf("invalid token type from db: %v", dbVal)
}
