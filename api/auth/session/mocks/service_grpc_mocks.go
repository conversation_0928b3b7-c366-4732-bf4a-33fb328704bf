// Code generated by MockGen. DO NOT EDIT.
// Source: api/auth/session/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	session "github.com/epifi/gamma/api/auth/session"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockSessionManagerClient is a mock of SessionManagerClient interface.
type MockSessionManagerClient struct {
	ctrl     *gomock.Controller
	recorder *MockSessionManagerClientMockRecorder
}

// MockSessionManagerClientMockRecorder is the mock recorder for MockSessionManagerClient.
type MockSessionManagerClientMockRecorder struct {
	mock *MockSessionManagerClient
}

// NewMockSessionManagerClient creates a new mock instance.
func NewMockSessionManagerClient(ctrl *gomock.Controller) *MockSessionManagerClient {
	mock := &MockSessionManagerClient{ctrl: ctrl}
	mock.recorder = &MockSessionManagerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSessionManagerClient) EXPECT() *MockSessionManagerClientMockRecorder {
	return m.recorder
}

// CreateSession mocks base method.
func (m *MockSessionManagerClient) CreateSession(ctx context.Context, in *session.CreateSessionRequest, opts ...grpc.CallOption) (*session.CreateSessionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateSession", varargs...)
	ret0, _ := ret[0].(*session.CreateSessionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSession indicates an expected call of CreateSession.
func (mr *MockSessionManagerClientMockRecorder) CreateSession(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSession", reflect.TypeOf((*MockSessionManagerClient)(nil).CreateSession), varargs...)
}

// GetSessionLoginUrl mocks base method.
func (m *MockSessionManagerClient) GetSessionLoginUrl(ctx context.Context, in *session.GetSessionLoginUrlRequest, opts ...grpc.CallOption) (*session.GetSessionLoginUrlResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSessionLoginUrl", varargs...)
	ret0, _ := ret[0].(*session.GetSessionLoginUrlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSessionLoginUrl indicates an expected call of GetSessionLoginUrl.
func (mr *MockSessionManagerClientMockRecorder) GetSessionLoginUrl(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSessionLoginUrl", reflect.TypeOf((*MockSessionManagerClient)(nil).GetSessionLoginUrl), varargs...)
}

// ValidateSession mocks base method.
func (m *MockSessionManagerClient) ValidateSession(ctx context.Context, in *session.ValidateSessionRequest, opts ...grpc.CallOption) (*session.ValidateSessionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValidateSession", varargs...)
	ret0, _ := ret[0].(*session.ValidateSessionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateSession indicates an expected call of ValidateSession.
func (mr *MockSessionManagerClientMockRecorder) ValidateSession(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateSession", reflect.TypeOf((*MockSessionManagerClient)(nil).ValidateSession), varargs...)
}

// MockSessionManagerServer is a mock of SessionManagerServer interface.
type MockSessionManagerServer struct {
	ctrl     *gomock.Controller
	recorder *MockSessionManagerServerMockRecorder
}

// MockSessionManagerServerMockRecorder is the mock recorder for MockSessionManagerServer.
type MockSessionManagerServerMockRecorder struct {
	mock *MockSessionManagerServer
}

// NewMockSessionManagerServer creates a new mock instance.
func NewMockSessionManagerServer(ctrl *gomock.Controller) *MockSessionManagerServer {
	mock := &MockSessionManagerServer{ctrl: ctrl}
	mock.recorder = &MockSessionManagerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSessionManagerServer) EXPECT() *MockSessionManagerServerMockRecorder {
	return m.recorder
}

// CreateSession mocks base method.
func (m *MockSessionManagerServer) CreateSession(arg0 context.Context, arg1 *session.CreateSessionRequest) (*session.CreateSessionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSession", arg0, arg1)
	ret0, _ := ret[0].(*session.CreateSessionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSession indicates an expected call of CreateSession.
func (mr *MockSessionManagerServerMockRecorder) CreateSession(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSession", reflect.TypeOf((*MockSessionManagerServer)(nil).CreateSession), arg0, arg1)
}

// GetSessionLoginUrl mocks base method.
func (m *MockSessionManagerServer) GetSessionLoginUrl(arg0 context.Context, arg1 *session.GetSessionLoginUrlRequest) (*session.GetSessionLoginUrlResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSessionLoginUrl", arg0, arg1)
	ret0, _ := ret[0].(*session.GetSessionLoginUrlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSessionLoginUrl indicates an expected call of GetSessionLoginUrl.
func (mr *MockSessionManagerServerMockRecorder) GetSessionLoginUrl(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSessionLoginUrl", reflect.TypeOf((*MockSessionManagerServer)(nil).GetSessionLoginUrl), arg0, arg1)
}

// ValidateSession mocks base method.
func (m *MockSessionManagerServer) ValidateSession(arg0 context.Context, arg1 *session.ValidateSessionRequest) (*session.ValidateSessionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateSession", arg0, arg1)
	ret0, _ := ret[0].(*session.ValidateSessionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateSession indicates an expected call of ValidateSession.
func (mr *MockSessionManagerServerMockRecorder) ValidateSession(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateSession", reflect.TypeOf((*MockSessionManagerServer)(nil).ValidateSession), arg0, arg1)
}

// MockUnsafeSessionManagerServer is a mock of UnsafeSessionManagerServer interface.
type MockUnsafeSessionManagerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeSessionManagerServerMockRecorder
}

// MockUnsafeSessionManagerServerMockRecorder is the mock recorder for MockUnsafeSessionManagerServer.
type MockUnsafeSessionManagerServerMockRecorder struct {
	mock *MockUnsafeSessionManagerServer
}

// NewMockUnsafeSessionManagerServer creates a new mock instance.
func NewMockUnsafeSessionManagerServer(ctrl *gomock.Controller) *MockUnsafeSessionManagerServer {
	mock := &MockUnsafeSessionManagerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeSessionManagerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeSessionManagerServer) EXPECT() *MockUnsafeSessionManagerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedSessionManagerServer mocks base method.
func (m *MockUnsafeSessionManagerServer) mustEmbedUnimplementedSessionManagerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedSessionManagerServer")
}

// mustEmbedUnimplementedSessionManagerServer indicates an expected call of mustEmbedUnimplementedSessionManagerServer.
func (mr *MockUnsafeSessionManagerServerMockRecorder) mustEmbedUnimplementedSessionManagerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedSessionManagerServer", reflect.TypeOf((*MockUnsafeSessionManagerServer)(nil).mustEmbedUnimplementedSessionManagerServer))
}
