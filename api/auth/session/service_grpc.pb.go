// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/auth/session/service.proto

package session

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SessionManager_GetSessionLoginUrl_FullMethodName = "/auth.session.SessionManager/GetSessionLoginUrl"
	SessionManager_CreateSession_FullMethodName      = "/auth.session.SessionManager/CreateSession"
	SessionManager_ValidateSession_FullMethodName    = "/auth.session.SessionManager/ValidateSession"
)

// SessionManagerClient is the client API for SessionManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SessionManagerClient interface {
	// GetSessionLoginUrl generates a signed URL for session-based login.
	GetSessionLoginUrl(ctx context.Context, in *GetSessionLoginUrlRequest, opts ...grpc.CallOption) (*GetSessionLoginUrlResponse, error)
	// CreateSession creates a new session, generates a token, and stores sessionId:auth_token in redis
	CreateSession(ctx context.Context, in *CreateSessionRequest, opts ...grpc.CallOption) (*CreateSessionResponse, error)
	// ValidateSession is used to validate session if valid and active
	// gets auth_token from redis stored while CreateSession and validates auth_token
	ValidateSession(ctx context.Context, in *ValidateSessionRequest, opts ...grpc.CallOption) (*ValidateSessionResponse, error)
}

type sessionManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewSessionManagerClient(cc grpc.ClientConnInterface) SessionManagerClient {
	return &sessionManagerClient{cc}
}

func (c *sessionManagerClient) GetSessionLoginUrl(ctx context.Context, in *GetSessionLoginUrlRequest, opts ...grpc.CallOption) (*GetSessionLoginUrlResponse, error) {
	out := new(GetSessionLoginUrlResponse)
	err := c.cc.Invoke(ctx, SessionManager_GetSessionLoginUrl_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionManagerClient) CreateSession(ctx context.Context, in *CreateSessionRequest, opts ...grpc.CallOption) (*CreateSessionResponse, error) {
	out := new(CreateSessionResponse)
	err := c.cc.Invoke(ctx, SessionManager_CreateSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionManagerClient) ValidateSession(ctx context.Context, in *ValidateSessionRequest, opts ...grpc.CallOption) (*ValidateSessionResponse, error) {
	out := new(ValidateSessionResponse)
	err := c.cc.Invoke(ctx, SessionManager_ValidateSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SessionManagerServer is the server API for SessionManager service.
// All implementations should embed UnimplementedSessionManagerServer
// for forward compatibility
type SessionManagerServer interface {
	// GetSessionLoginUrl generates a signed URL for session-based login.
	GetSessionLoginUrl(context.Context, *GetSessionLoginUrlRequest) (*GetSessionLoginUrlResponse, error)
	// CreateSession creates a new session, generates a token, and stores sessionId:auth_token in redis
	CreateSession(context.Context, *CreateSessionRequest) (*CreateSessionResponse, error)
	// ValidateSession is used to validate session if valid and active
	// gets auth_token from redis stored while CreateSession and validates auth_token
	ValidateSession(context.Context, *ValidateSessionRequest) (*ValidateSessionResponse, error)
}

// UnimplementedSessionManagerServer should be embedded to have forward compatible implementations.
type UnimplementedSessionManagerServer struct {
}

func (UnimplementedSessionManagerServer) GetSessionLoginUrl(context.Context, *GetSessionLoginUrlRequest) (*GetSessionLoginUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSessionLoginUrl not implemented")
}
func (UnimplementedSessionManagerServer) CreateSession(context.Context, *CreateSessionRequest) (*CreateSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSession not implemented")
}
func (UnimplementedSessionManagerServer) ValidateSession(context.Context, *ValidateSessionRequest) (*ValidateSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateSession not implemented")
}

// UnsafeSessionManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SessionManagerServer will
// result in compilation errors.
type UnsafeSessionManagerServer interface {
	mustEmbedUnimplementedSessionManagerServer()
}

func RegisterSessionManagerServer(s grpc.ServiceRegistrar, srv SessionManagerServer) {
	s.RegisterService(&SessionManager_ServiceDesc, srv)
}

func _SessionManager_GetSessionLoginUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSessionLoginUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionManagerServer).GetSessionLoginUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SessionManager_GetSessionLoginUrl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionManagerServer).GetSessionLoginUrl(ctx, req.(*GetSessionLoginUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionManager_CreateSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionManagerServer).CreateSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SessionManager_CreateSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionManagerServer).CreateSession(ctx, req.(*CreateSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionManager_ValidateSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionManagerServer).ValidateSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SessionManager_ValidateSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionManagerServer).ValidateSession(ctx, req.(*ValidateSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SessionManager_ServiceDesc is the grpc.ServiceDesc for SessionManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SessionManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "auth.session.SessionManager",
	HandlerType: (*SessionManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSessionLoginUrl",
			Handler:    _SessionManager_GetSessionLoginUrl_Handler,
		},
		{
			MethodName: "CreateSession",
			Handler:    _SessionManager_CreateSession_Handler,
		},
		{
			MethodName: "ValidateSession",
			Handler:    _SessionManager_ValidateSession_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/auth/session/service.proto",
}
