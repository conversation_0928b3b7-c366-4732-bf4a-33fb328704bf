// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/session/session.proto

package session

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SessionDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SessionDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SessionDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SessionDetailsMultiError,
// or nil if none found.
func (m *SessionDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *SessionDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SessionId

	// no validation rules for ActorId

	// no validation rules for AccessToken

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SessionDetailsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SessionDetailsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SessionDetailsValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpiresAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SessionDetailsValidationError{
					field:  "ExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SessionDetailsValidationError{
					field:  "ExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiresAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SessionDetailsValidationError{
				field:  "ExpiresAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SessionDetailsMultiError(errors)
	}

	return nil
}

// SessionDetailsMultiError is an error wrapping multiple validation errors
// returned by SessionDetails.ValidateAll() if the designated constraints
// aren't met.
type SessionDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SessionDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SessionDetailsMultiError) AllErrors() []error { return m }

// SessionDetailsValidationError is the validation error returned by
// SessionDetails.Validate if the designated constraints aren't met.
type SessionDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SessionDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SessionDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SessionDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SessionDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SessionDetailsValidationError) ErrorName() string { return "SessionDetailsValidationError" }

// Error satisfies the builtin error interface
func (e SessionDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSessionDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SessionDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SessionDetailsValidationError{}
