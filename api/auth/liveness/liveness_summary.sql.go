package liveness

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/logger"
)

// UnmarshalJSON implements the Unmarshaler interface
func (x *LivenessSummary) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, x)
}

// MarshalJSON implements the Marshaler interface
func (x *LivenessSummary) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(x)
}

// Value Valuer interface implementation for storing the data in string format in DB
func (x SummaryStatus) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan Scanner interface implementation for parsing data while reading from DB
func (x *SummaryStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := SummaryStatus_value[val]
	if !ok {
		logger.ErrorNoCtx(fmt.Sprintf("encountered unknown summary status : %v", val))
	}
	*x = SummaryStatus(valInt)
	return nil
}

// Value Valuer interface implementation for storing the data in string format in DB
func (x SummaryLivenessStatus) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan Scanner interface implementation for parsing data while reading from DB
func (x *SummaryLivenessStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := SummaryLivenessStatus_value[val]
	if !ok {
		logger.ErrorNoCtx(fmt.Sprintf("encountered unknown summary liveness status : %v", val))
	}
	*x = SummaryLivenessStatus(valInt)
	return nil
}

// Value Valuer interface implementation for storing the data in string format in DB
func (x SummaryFacematchStatus) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan Scanner interface implementation for parsing data while reading from DB
func (x *SummaryFacematchStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := SummaryFacematchStatus_value[val]
	if !ok {
		logger.ErrorNoCtx(fmt.Sprintf("encountered unknown summary facematch status : %v", val))
	}
	*x = SummaryFacematchStatus(valInt)
	return nil
}

// UnmarshalJSON implements the Unmarshaler interface
func (x *FacematchInfo) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, x)
}

// MarshalJSON implements the Marshaler interface
func (x *FacematchInfo) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(x)
}

// Value : Valuer interface implementation for storing the data in string format in DB
func (x *FacematchInfo) Value() (driver.Value, error) {
	if x == nil {
		return nil, nil
	}
	return protojson.Marshal(x)
}

// Scan : Scanner interface implementation for parsing data while reading from DB
func (x *FacematchInfo) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, x)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}

// UnmarshalJSON implements the Unmarshaler interface
func (x *LivenessSummary_ClientRequestParams) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, x)
}

// MarshalJSON implements the Marshaler interface
func (x *LivenessSummary_ClientRequestParams) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(x)
}

// Value : Valuer interface implementation for storing the data in string format in DB
func (x *LivenessSummary_ClientRequestParams) Value() (driver.Value, error) {
	if x == nil {
		return nil, nil
	}
	return protojson.Marshal(x)
}

// Scan : Scanner interface implementation for parsing data while reading from DB
func (x *LivenessSummary_ClientRequestParams) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, x)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}

// Value Valuer interface implementation for storing the data in string format in DB
func (x StrictnessLogic) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan Scanner interface implementation for parsing data while reading from DB
func (x *StrictnessLogic) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := StrictnessLogic_value[val]
	if !ok {
		logger.ErrorNoCtx(fmt.Sprintf("encountered unknown strictness logic : %v", val))
	}
	*x = StrictnessLogic(valInt)
	return nil
}

func (x SummaryLivenessStatus) IsSummaryLivenessFailed() bool {
	return x == SummaryLivenessStatus_SUMMARY_LIVENESS_RETRY ||
		x == SummaryLivenessStatus_SUMMARY_LIVENESS_MANUALLY_FAILED
}

func (x SummaryFacematchStatus) IsSummaryFMFailed() bool {
	return x == SummaryFacematchStatus_SUMMARY_FACEMATCH_RETRY ||
		x == SummaryFacematchStatus_SUMMARY_FACEMATCH_MANUALLY_FAILED
}

func (x *LivenessSummary) IsSummaryLivenessFailed() bool {
	return x.GetSummaryLivenessStatus() == SummaryLivenessStatus_SUMMARY_LIVENESS_RETRY ||
		x.GetSummaryLivenessStatus() == SummaryLivenessStatus_SUMMARY_LIVENESS_MANUALLY_FAILED
}

func (x *LivenessSummary) IsSummaryLivenessPassed() bool {
	return x.GetSummaryLivenessStatus() == SummaryLivenessStatus_SUMMARY_LIVENESS_MANUALLY_PASSED ||
		x.GetSummaryLivenessStatus() == SummaryLivenessStatus_SUMMARY_LIVENESS_COMPLETED
}

func (x *LivenessSummary) IsSummaryFMFailed() bool {
	return x.GetSummaryFacematchStatus() == SummaryFacematchStatus_SUMMARY_FACEMATCH_RETRY ||
		x.GetSummaryFacematchStatus() == SummaryFacematchStatus_SUMMARY_FACEMATCH_MANUALLY_FAILED
}

func (x *LivenessSummary) IsSummaryFMPassed() bool {
	return x.GetSummaryFacematchStatus() == SummaryFacematchStatus_SUMMARY_FACEMATCH_COMPLETED ||
		x.GetSummaryFacematchStatus() == SummaryFacematchStatus_SUMMARY_FACEMATCH_MANUALLY_PASSED
}
