package liveness

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"github.com/epifi/be-common/pkg/logger"

	"google.golang.org/protobuf/encoding/protojson"
)

// UnmarshalJSON implements the Unmarshaler interface
func (x *FaceMatchAnnotation) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, x)
}

// MarshalJSON implements the Marshaler interface
func (x *FaceMatchAnnotation) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(x)
}

// Value : Valuer interface implementation for storing the data in string format in DB
func (x *FaceMatchAnnotation) Value() (driver.Value, error) {
	if x == nil {
		return nil, nil
	}
	return protojson.Marshal(x)
}

// Scan : Scanner interface implementation for parsing data while reading from DB
func (x *FaceMatchAnnotation) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, x)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}

// UnmarshalJSON implements the Unmarshaler interface
func (x *FaceMatchAttempt) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, x)
}

// MarshalJSON implements the Marshaler interface
func (x *FaceMatchAttempt) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(x)
}

// Value Valuer interface implementation for storing the data in string format in DB
func (x FaceMatchStatus) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan Scanner interface implementation for parsing data while reading from DB
func (x *FaceMatchStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}
	valInt, ok := FaceMatchStatus_value[val]
	if !ok {
		logger.ErrorNoCtx(fmt.Sprintf("encountered unknown facematch status : %v", val))
	}
	*x = FaceMatchStatus(valInt)
	return nil
}
