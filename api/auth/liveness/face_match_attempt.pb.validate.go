// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/liveness/internal/face_match_attempt.proto

package liveness

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on FaceMatchAttempt with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FaceMatchAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FaceMatchAttempt with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FaceMatchAttemptMultiError, or nil if none found.
func (m *FaceMatchAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *FaceMatchAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AttemptId

	// no validation rules for RequestId

	// no validation rules for VendorRequestId

	// no validation rules for Vendor

	// no validation rules for VideoLocation

	// no validation rules for ImageLocation

	// no validation rules for Status

	// no validation rules for FaceMatchScore

	// no validation rules for SummaryId

	if all {
		switch v := interface{}(m.GetAnnotation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FaceMatchAttemptValidationError{
					field:  "Annotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FaceMatchAttemptValidationError{
					field:  "Annotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnotation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FaceMatchAttemptValidationError{
				field:  "Annotation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Threshold

	// no validation rules for StrictnessLogic

	// no validation rules for InhouseStatus

	// no validation rules for LivenessRequestId

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FaceMatchAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FaceMatchAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FaceMatchAttemptValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FaceMatchAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FaceMatchAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FaceMatchAttemptValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FaceMatchAttemptMultiError(errors)
	}

	return nil
}

// FaceMatchAttemptMultiError is an error wrapping multiple validation errors
// returned by FaceMatchAttempt.ValidateAll() if the designated constraints
// aren't met.
type FaceMatchAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FaceMatchAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FaceMatchAttemptMultiError) AllErrors() []error { return m }

// FaceMatchAttemptValidationError is the validation error returned by
// FaceMatchAttempt.Validate if the designated constraints aren't met.
type FaceMatchAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FaceMatchAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FaceMatchAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FaceMatchAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FaceMatchAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FaceMatchAttemptValidationError) ErrorName() string { return "FaceMatchAttemptValidationError" }

// Error satisfies the builtin error interface
func (e FaceMatchAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFaceMatchAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FaceMatchAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FaceMatchAttemptValidationError{}
