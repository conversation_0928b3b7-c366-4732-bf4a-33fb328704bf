// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/liveness/internal/face_match_annotation.proto

package liveness

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FaceMatchVerdict int32

const (
	FaceMatchVerdict_FACE_MATCH_VERDICT_UNSPECIFIED FaceMatchVerdict = 0
	FaceMatchVerdict_FACE_MATCH_VERDICT_PASS        FaceMatchVerdict = 1
	FaceMatchVerdict_FACE_MATCH_VERDICT_FAIL        FaceMatchVerdict = 2
)

// Enum value maps for FaceMatchVerdict.
var (
	FaceMatchVerdict_name = map[int32]string{
		0: "FACE_MATCH_VERDICT_UNSPECIFIED",
		1: "FACE_MATCH_VERDICT_PASS",
		2: "FACE_MATCH_VERDICT_FAIL",
	}
	FaceMatchVerdict_value = map[string]int32{
		"FACE_MATCH_VERDICT_UNSPECIFIED": 0,
		"FACE_MATCH_VERDICT_PASS":        1,
		"FACE_MATCH_VERDICT_FAIL":        2,
	}
)

func (x FaceMatchVerdict) Enum() *FaceMatchVerdict {
	p := new(FaceMatchVerdict)
	*p = x
	return p
}

func (x FaceMatchVerdict) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FaceMatchVerdict) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_internal_face_match_annotation_proto_enumTypes[0].Descriptor()
}

func (FaceMatchVerdict) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_internal_face_match_annotation_proto_enumTypes[0]
}

func (x FaceMatchVerdict) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FaceMatchVerdict.Descriptor instead.
func (FaceMatchVerdict) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_face_match_annotation_proto_rawDescGZIP(), []int{0}
}

type FaceMatchIssue int32

const (
	FaceMatchIssue_FACE_MATCH_ISSUE_UNSPECIFIED                  FaceMatchIssue = 0
	FaceMatchIssue_FACE_MATCH_ISSUE_LIVENESS_IMAGE_NOT_CLEAR     FaceMatchIssue = 1
	FaceMatchIssue_FACE_MATCH_ISSUE_PHOTO_MISMATCH               FaceMatchIssue = 2
	FaceMatchIssue_FACE_MATCH_ISSUE_MULTIPLE_FACES               FaceMatchIssue = 3
	FaceMatchIssue_FACE_MATCH_ISSUE_TECHNICAL_ISSUE              FaceMatchIssue = 4
	FaceMatchIssue_FACE_MATCH_ISSUE_KYC_IMAGE_NOT_CLEAR          FaceMatchIssue = 5
	FaceMatchIssue_FACE_MATCH_ISSUE_KYC_IMAGE_NOT_AVAILABLE      FaceMatchIssue = 6
	FaceMatchIssue_FACE_MATCH_ISSUE_LIVENESS_IMAGE_NOT_AVAILABLE FaceMatchIssue = 7
	FaceMatchIssue_FACE_MATCH_ISSUE_GENDER_MISMATCH              FaceMatchIssue = 8
	FaceMatchIssue_FACE_MATCH_ISSUE_OTHER                        FaceMatchIssue = 9
	FaceMatchIssue_FACE_MATCH_ISSUE_LOW_LIGHT                    FaceMatchIssue = 10
	FaceMatchIssue_FACE_MATCH_ISSUE_OVEREXPOSURE                 FaceMatchIssue = 11
	FaceMatchIssue_FACE_MATCH_ISSUE_BLURRED_PHOTO                FaceMatchIssue = 12
	FaceMatchIssue_FACE_MATCH_ISSUE_FACE_COVERED                 FaceMatchIssue = 13
	FaceMatchIssue_FACE_MATCH_ISSUE_CAMERA_PLACEMENT_ISSUE       FaceMatchIssue = 14
	FaceMatchIssue_FACE_MATCH_ISSUE_FACE_TOO_CLOSE               FaceMatchIssue = 15
	FaceMatchIssue_FACE_MATCH_ISSUE_FACE_TOO_FAR                 FaceMatchIssue = 16
	FaceMatchIssue_FACE_MATCH_ISSUE_LIVENESS_SPOOF_ALERT         FaceMatchIssue = 17
)

// Enum value maps for FaceMatchIssue.
var (
	FaceMatchIssue_name = map[int32]string{
		0:  "FACE_MATCH_ISSUE_UNSPECIFIED",
		1:  "FACE_MATCH_ISSUE_LIVENESS_IMAGE_NOT_CLEAR",
		2:  "FACE_MATCH_ISSUE_PHOTO_MISMATCH",
		3:  "FACE_MATCH_ISSUE_MULTIPLE_FACES",
		4:  "FACE_MATCH_ISSUE_TECHNICAL_ISSUE",
		5:  "FACE_MATCH_ISSUE_KYC_IMAGE_NOT_CLEAR",
		6:  "FACE_MATCH_ISSUE_KYC_IMAGE_NOT_AVAILABLE",
		7:  "FACE_MATCH_ISSUE_LIVENESS_IMAGE_NOT_AVAILABLE",
		8:  "FACE_MATCH_ISSUE_GENDER_MISMATCH",
		9:  "FACE_MATCH_ISSUE_OTHER",
		10: "FACE_MATCH_ISSUE_LOW_LIGHT",
		11: "FACE_MATCH_ISSUE_OVEREXPOSURE",
		12: "FACE_MATCH_ISSUE_BLURRED_PHOTO",
		13: "FACE_MATCH_ISSUE_FACE_COVERED",
		14: "FACE_MATCH_ISSUE_CAMERA_PLACEMENT_ISSUE",
		15: "FACE_MATCH_ISSUE_FACE_TOO_CLOSE",
		16: "FACE_MATCH_ISSUE_FACE_TOO_FAR",
		17: "FACE_MATCH_ISSUE_LIVENESS_SPOOF_ALERT",
	}
	FaceMatchIssue_value = map[string]int32{
		"FACE_MATCH_ISSUE_UNSPECIFIED":                  0,
		"FACE_MATCH_ISSUE_LIVENESS_IMAGE_NOT_CLEAR":     1,
		"FACE_MATCH_ISSUE_PHOTO_MISMATCH":               2,
		"FACE_MATCH_ISSUE_MULTIPLE_FACES":               3,
		"FACE_MATCH_ISSUE_TECHNICAL_ISSUE":              4,
		"FACE_MATCH_ISSUE_KYC_IMAGE_NOT_CLEAR":          5,
		"FACE_MATCH_ISSUE_KYC_IMAGE_NOT_AVAILABLE":      6,
		"FACE_MATCH_ISSUE_LIVENESS_IMAGE_NOT_AVAILABLE": 7,
		"FACE_MATCH_ISSUE_GENDER_MISMATCH":              8,
		"FACE_MATCH_ISSUE_OTHER":                        9,
		"FACE_MATCH_ISSUE_LOW_LIGHT":                    10,
		"FACE_MATCH_ISSUE_OVEREXPOSURE":                 11,
		"FACE_MATCH_ISSUE_BLURRED_PHOTO":                12,
		"FACE_MATCH_ISSUE_FACE_COVERED":                 13,
		"FACE_MATCH_ISSUE_CAMERA_PLACEMENT_ISSUE":       14,
		"FACE_MATCH_ISSUE_FACE_TOO_CLOSE":               15,
		"FACE_MATCH_ISSUE_FACE_TOO_FAR":                 16,
		"FACE_MATCH_ISSUE_LIVENESS_SPOOF_ALERT":         17,
	}
)

func (x FaceMatchIssue) Enum() *FaceMatchIssue {
	p := new(FaceMatchIssue)
	*p = x
	return p
}

func (x FaceMatchIssue) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FaceMatchIssue) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_internal_face_match_annotation_proto_enumTypes[1].Descriptor()
}

func (FaceMatchIssue) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_internal_face_match_annotation_proto_enumTypes[1]
}

func (x FaceMatchIssue) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FaceMatchIssue.Descriptor instead.
func (FaceMatchIssue) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_face_match_annotation_proto_rawDescGZIP(), []int{1}
}

// type of error made by analyst
type ReReviewErrorType int32

const (
	ReReviewErrorType_RE_REVIEW_ERROR_TYPE_UNSPECIFIED ReReviewErrorType = 0
	ReReviewErrorType_RE_REVIEW_ERROR_TYPE_NORMAL      ReReviewErrorType = 1
	ReReviewErrorType_RE_REVIEW_ERROR_TYPE_CRITICAL    ReReviewErrorType = 2
)

// Enum value maps for ReReviewErrorType.
var (
	ReReviewErrorType_name = map[int32]string{
		0: "RE_REVIEW_ERROR_TYPE_UNSPECIFIED",
		1: "RE_REVIEW_ERROR_TYPE_NORMAL",
		2: "RE_REVIEW_ERROR_TYPE_CRITICAL",
	}
	ReReviewErrorType_value = map[string]int32{
		"RE_REVIEW_ERROR_TYPE_UNSPECIFIED": 0,
		"RE_REVIEW_ERROR_TYPE_NORMAL":      1,
		"RE_REVIEW_ERROR_TYPE_CRITICAL":    2,
	}
)

func (x ReReviewErrorType) Enum() *ReReviewErrorType {
	p := new(ReReviewErrorType)
	*p = x
	return p
}

func (x ReReviewErrorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReReviewErrorType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_internal_face_match_annotation_proto_enumTypes[2].Descriptor()
}

func (ReReviewErrorType) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_internal_face_match_annotation_proto_enumTypes[2]
}

func (x ReReviewErrorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReReviewErrorType.Descriptor instead.
func (ReReviewErrorType) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_face_match_annotation_proto_rawDescGZIP(), []int{2}
}

type ReReviewErrorSubcategory int32

const (
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_UNSPECIFIED                   ReReviewErrorSubcategory = 0
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_OTHER_LIVENESS_ATTEMPT_ISSUE  ReReviewErrorSubcategory = 1
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_QUEUED_VIDEO_ISSUE            ReReviewErrorSubcategory = 2
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_WRONG_VERDICT_AFTER_OUTCALL   ReReviewErrorSubcategory = 3
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_OUTCALL_NOT_MADE              ReReviewErrorSubcategory = 4
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_CUSTOMER_PROFILE_REVIEW_ISSUE ReReviewErrorSubcategory = 5
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_FACEMATCH_WRONG_VERDICT       ReReviewErrorSubcategory = 6
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_MISSED_VIDEO_ISSUES           ReReviewErrorSubcategory = 7
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_MISSED_POTENTIAL_FRAUD        ReReviewErrorSubcategory = 8
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_MISSED_REMARKS                ReReviewErrorSubcategory = 9
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_WRONG_VIDEO_ISSUE             ReReviewErrorSubcategory = 10
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_WRONG_POTENTIAL_FRAUD         ReReviewErrorSubcategory = 11
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_WRONG_LIVE_PERSON             ReReviewErrorSubcategory = 12
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_INVALID_COMBO                 ReReviewErrorSubcategory = 13
	ReReviewErrorSubcategory_RE_REVIEW_ERROR_SUBCATEGORY_WRONG_REMARKS                 ReReviewErrorSubcategory = 14
)

// Enum value maps for ReReviewErrorSubcategory.
var (
	ReReviewErrorSubcategory_name = map[int32]string{
		0:  "RE_REVIEW_ERROR_SUBCATEGORY_UNSPECIFIED",
		1:  "RE_REVIEW_ERROR_SUBCATEGORY_OTHER_LIVENESS_ATTEMPT_ISSUE",
		2:  "RE_REVIEW_ERROR_SUBCATEGORY_QUEUED_VIDEO_ISSUE",
		3:  "RE_REVIEW_ERROR_SUBCATEGORY_WRONG_VERDICT_AFTER_OUTCALL",
		4:  "RE_REVIEW_ERROR_SUBCATEGORY_OUTCALL_NOT_MADE",
		5:  "RE_REVIEW_ERROR_SUBCATEGORY_CUSTOMER_PROFILE_REVIEW_ISSUE",
		6:  "RE_REVIEW_ERROR_SUBCATEGORY_FACEMATCH_WRONG_VERDICT",
		7:  "RE_REVIEW_ERROR_SUBCATEGORY_MISSED_VIDEO_ISSUES",
		8:  "RE_REVIEW_ERROR_SUBCATEGORY_MISSED_POTENTIAL_FRAUD",
		9:  "RE_REVIEW_ERROR_SUBCATEGORY_MISSED_REMARKS",
		10: "RE_REVIEW_ERROR_SUBCATEGORY_WRONG_VIDEO_ISSUE",
		11: "RE_REVIEW_ERROR_SUBCATEGORY_WRONG_POTENTIAL_FRAUD",
		12: "RE_REVIEW_ERROR_SUBCATEGORY_WRONG_LIVE_PERSON",
		13: "RE_REVIEW_ERROR_SUBCATEGORY_INVALID_COMBO",
		14: "RE_REVIEW_ERROR_SUBCATEGORY_WRONG_REMARKS",
	}
	ReReviewErrorSubcategory_value = map[string]int32{
		"RE_REVIEW_ERROR_SUBCATEGORY_UNSPECIFIED":                   0,
		"RE_REVIEW_ERROR_SUBCATEGORY_OTHER_LIVENESS_ATTEMPT_ISSUE":  1,
		"RE_REVIEW_ERROR_SUBCATEGORY_QUEUED_VIDEO_ISSUE":            2,
		"RE_REVIEW_ERROR_SUBCATEGORY_WRONG_VERDICT_AFTER_OUTCALL":   3,
		"RE_REVIEW_ERROR_SUBCATEGORY_OUTCALL_NOT_MADE":              4,
		"RE_REVIEW_ERROR_SUBCATEGORY_CUSTOMER_PROFILE_REVIEW_ISSUE": 5,
		"RE_REVIEW_ERROR_SUBCATEGORY_FACEMATCH_WRONG_VERDICT":       6,
		"RE_REVIEW_ERROR_SUBCATEGORY_MISSED_VIDEO_ISSUES":           7,
		"RE_REVIEW_ERROR_SUBCATEGORY_MISSED_POTENTIAL_FRAUD":        8,
		"RE_REVIEW_ERROR_SUBCATEGORY_MISSED_REMARKS":                9,
		"RE_REVIEW_ERROR_SUBCATEGORY_WRONG_VIDEO_ISSUE":             10,
		"RE_REVIEW_ERROR_SUBCATEGORY_WRONG_POTENTIAL_FRAUD":         11,
		"RE_REVIEW_ERROR_SUBCATEGORY_WRONG_LIVE_PERSON":             12,
		"RE_REVIEW_ERROR_SUBCATEGORY_INVALID_COMBO":                 13,
		"RE_REVIEW_ERROR_SUBCATEGORY_WRONG_REMARKS":                 14,
	}
)

func (x ReReviewErrorSubcategory) Enum() *ReReviewErrorSubcategory {
	p := new(ReReviewErrorSubcategory)
	*p = x
	return p
}

func (x ReReviewErrorSubcategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReReviewErrorSubcategory) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_internal_face_match_annotation_proto_enumTypes[3].Descriptor()
}

func (ReReviewErrorSubcategory) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_internal_face_match_annotation_proto_enumTypes[3]
}

func (x ReReviewErrorSubcategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReReviewErrorSubcategory.Descriptor instead.
func (ReReviewErrorSubcategory) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_face_match_annotation_proto_rawDescGZIP(), []int{3}
}

// FaceMatchAnnotation is used to collect extra information in FaceMatchAttempt
type FaceMatchAnnotation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// facematch issue
	FacematchIssues []FaceMatchIssue `protobuf:"varint,1,rep,packed,name=facematch_issues,json=facematchIssues,proto3,enum=auth.liveness.FaceMatchIssue" json:"facematch_issues,omitempty"`
	// annotation are reviewed by
	ReviewedBy string `protobuf:"bytes,2,opt,name=reviewed_by,json=reviewedBy,proto3" json:"reviewed_by,omitempty"`
	// verdict whether pass or fail
	ReviewVerdict FaceMatchVerdict `protobuf:"varint,3,opt,name=review_verdict,json=reviewVerdict,proto3,enum=auth.liveness.FaceMatchVerdict" json:"review_verdict,omitempty"`
	// comments by reviewer
	Remarks string `protobuf:"bytes,4,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// timestamp when annotations were recorded
	ReviewedOn *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=reviewed_on,json=reviewedOn,proto3" json:"reviewed_on,omitempty"`
	// re review details
	ReReviewVerdict FaceMatchVerdict `protobuf:"varint,6,opt,name=re_review_verdict,json=reReviewVerdict,proto3,enum=auth.liveness.FaceMatchVerdict" json:"re_review_verdict,omitempty"`
	// annotation is re-reviewed by
	ReReviewedBy string `protobuf:"bytes,7,opt,name=re_reviewed_by,json=reReviewedBy,proto3" json:"re_reviewed_by,omitempty"`
	// timestamp when annotations were re recorded
	ReReviewedOn *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=re_reviewed_on,json=reReviewedOn,proto3" json:"re_reviewed_on,omitempty"`
	// comments by re-reviewer
	ReReviewRemarks string `protobuf:"bytes,9,opt,name=re_review_remarks,json=reReviewRemarks,proto3" json:"re_review_remarks,omitempty"`
	// type of error made by analyst in L1
	//
	// Deprecated: Marked as deprecated in api/auth/liveness/internal/face_match_annotation.proto.
	ReReviewErrorType          ReReviewErrorType          `protobuf:"varint,10,opt,name=re_review_error_type,json=reReviewErrorType,proto3,enum=auth.liveness.ReReviewErrorType" json:"re_review_error_type,omitempty"`
	ReReviewErrorTypes         []ReReviewErrorType        `protobuf:"varint,11,rep,packed,name=re_review_error_types,json=reReviewErrorTypes,proto3,enum=auth.liveness.ReReviewErrorType" json:"re_review_error_types,omitempty"`
	ReReviewErrorSubcategories []ReReviewErrorSubcategory `protobuf:"varint,12,rep,packed,name=re_review_error_subcategories,json=reReviewErrorSubcategories,proto3,enum=auth.liveness.ReReviewErrorSubcategory" json:"re_review_error_subcategories,omitempty"`
	// case Id of risk case
	CaseId string `protobuf:"bytes,13,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
}

func (x *FaceMatchAnnotation) Reset() {
	*x = FaceMatchAnnotation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_internal_face_match_annotation_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceMatchAnnotation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceMatchAnnotation) ProtoMessage() {}

func (x *FaceMatchAnnotation) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_internal_face_match_annotation_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceMatchAnnotation.ProtoReflect.Descriptor instead.
func (*FaceMatchAnnotation) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_face_match_annotation_proto_rawDescGZIP(), []int{0}
}

func (x *FaceMatchAnnotation) GetFacematchIssues() []FaceMatchIssue {
	if x != nil {
		return x.FacematchIssues
	}
	return nil
}

func (x *FaceMatchAnnotation) GetReviewedBy() string {
	if x != nil {
		return x.ReviewedBy
	}
	return ""
}

func (x *FaceMatchAnnotation) GetReviewVerdict() FaceMatchVerdict {
	if x != nil {
		return x.ReviewVerdict
	}
	return FaceMatchVerdict_FACE_MATCH_VERDICT_UNSPECIFIED
}

func (x *FaceMatchAnnotation) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *FaceMatchAnnotation) GetReviewedOn() *timestamppb.Timestamp {
	if x != nil {
		return x.ReviewedOn
	}
	return nil
}

func (x *FaceMatchAnnotation) GetReReviewVerdict() FaceMatchVerdict {
	if x != nil {
		return x.ReReviewVerdict
	}
	return FaceMatchVerdict_FACE_MATCH_VERDICT_UNSPECIFIED
}

func (x *FaceMatchAnnotation) GetReReviewedBy() string {
	if x != nil {
		return x.ReReviewedBy
	}
	return ""
}

func (x *FaceMatchAnnotation) GetReReviewedOn() *timestamppb.Timestamp {
	if x != nil {
		return x.ReReviewedOn
	}
	return nil
}

func (x *FaceMatchAnnotation) GetReReviewRemarks() string {
	if x != nil {
		return x.ReReviewRemarks
	}
	return ""
}

// Deprecated: Marked as deprecated in api/auth/liveness/internal/face_match_annotation.proto.
func (x *FaceMatchAnnotation) GetReReviewErrorType() ReReviewErrorType {
	if x != nil {
		return x.ReReviewErrorType
	}
	return ReReviewErrorType_RE_REVIEW_ERROR_TYPE_UNSPECIFIED
}

func (x *FaceMatchAnnotation) GetReReviewErrorTypes() []ReReviewErrorType {
	if x != nil {
		return x.ReReviewErrorTypes
	}
	return nil
}

func (x *FaceMatchAnnotation) GetReReviewErrorSubcategories() []ReReviewErrorSubcategory {
	if x != nil {
		return x.ReReviewErrorSubcategories
	}
	return nil
}

func (x *FaceMatchAnnotation) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

var File_api_auth_liveness_internal_face_match_annotation_proto protoreflect.FileDescriptor

var file_api_auth_liveness_internal_face_match_annotation_proto_rawDesc = []byte{
	0x0a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x66, 0x61, 0x63,
	0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb1, 0x06, 0x0a, 0x13, 0x46, 0x61, 0x63,
	0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x48, 0x0a, 0x10, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x73,
	0x73, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x49, 0x73, 0x73, 0x75, 0x65, 0x52, 0x0f, 0x66, 0x61, 0x63, 0x65, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x49, 0x73, 0x73, 0x75, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x42, 0x79, 0x12, 0x46, 0x0a, 0x0e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x56, 0x65, 0x72,
	0x64, 0x69, 0x63, 0x74, 0x52, 0x0d, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x56, 0x65, 0x72, 0x64,
	0x69, 0x63, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x3b, 0x0a,
	0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x4f, 0x6e, 0x12, 0x4b, 0x0a, 0x11, 0x72, 0x65,
	0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x56,
	0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x0f, 0x72, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65, 0x5f, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x72, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x42, 0x79, 0x12, 0x40, 0x0a,
	0x0e, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0c, 0x72, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x4f, 0x6e, 0x12,
	0x2a, 0x0a, 0x11, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x55, 0x0a, 0x14, 0x72,
	0x65, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x52, 0x65, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x11, 0x72, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x53, 0x0a, 0x15, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x2e, 0x52, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x12, 0x72, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x6a, 0x0a, 0x1d, 0x72, 0x65, 0x5f, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x52,
	0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x75, 0x62, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x1a, 0x72, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x75, 0x62, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x2a, 0x70, 0x0a, 0x10,
	0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74,
	0x12, 0x22, 0x0a, 0x1e, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x56,
	0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x5f, 0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x10,
	0x01, 0x12, 0x1b, 0x0a, 0x17, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x2a, 0xc8,
	0x05, 0x0a, 0x0e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x73, 0x73, 0x75,
	0x65, 0x12, 0x20, 0x0a, 0x1c, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x2d, 0x0a, 0x29, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4c, 0x45, 0x41, 0x52,
	0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48,
	0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x50, 0x48, 0x4f, 0x54, 0x4f, 0x5f, 0x4d, 0x49, 0x53,
	0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x41, 0x43, 0x45, 0x5f,
	0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x4d, 0x55, 0x4c, 0x54,
	0x49, 0x50, 0x4c, 0x45, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x53, 0x10, 0x03, 0x12, 0x24, 0x0a, 0x20,
	0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45,
	0x5f, 0x54, 0x45, 0x43, 0x48, 0x4e, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45,
	0x10, 0x04, 0x12, 0x28, 0x0a, 0x24, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48,
	0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4c, 0x45, 0x41, 0x52, 0x10, 0x05, 0x12, 0x2c, 0x0a, 0x28,
	0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45,
	0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41,
	0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x06, 0x12, 0x31, 0x0a, 0x2d, 0x46, 0x41,
	0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x4c,
	0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x07, 0x12, 0x24, 0x0a,
	0x20, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53, 0x53, 0x55,
	0x45, 0x5f, 0x47, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x10, 0x08, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x09, 0x12,
	0x1e, 0x0a, 0x1a, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53,
	0x53, 0x55, 0x45, 0x5f, 0x4c, 0x4f, 0x57, 0x5f, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x10, 0x0a, 0x12,
	0x21, 0x0a, 0x1d, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53,
	0x53, 0x55, 0x45, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x45, 0x58, 0x50, 0x4f, 0x53, 0x55, 0x52, 0x45,
	0x10, 0x0b, 0x12, 0x22, 0x0a, 0x1e, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48,
	0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x42, 0x4c, 0x55, 0x52, 0x52, 0x45, 0x44, 0x5f, 0x50,
	0x48, 0x4f, 0x54, 0x4f, 0x10, 0x0c, 0x12, 0x21, 0x0a, 0x1d, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d,
	0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f,
	0x43, 0x4f, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x0d, 0x12, 0x2b, 0x0a, 0x27, 0x46, 0x41, 0x43,
	0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x43, 0x41,
	0x4d, 0x45, 0x52, 0x41, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49,
	0x53, 0x53, 0x55, 0x45, 0x10, 0x0e, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d,
	0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f,
	0x54, 0x4f, 0x4f, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x10, 0x0f, 0x12, 0x21, 0x0a, 0x1d, 0x46,
	0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f,
	0x46, 0x41, 0x43, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x46, 0x41, 0x52, 0x10, 0x10, 0x12, 0x29,
	0x0a, 0x25, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x53, 0x53,
	0x55, 0x45, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x50, 0x4f, 0x4f,
	0x46, 0x5f, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x10, 0x11, 0x2a, 0x7d, 0x0a, 0x11, 0x52, 0x65, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24,
	0x0a, 0x20, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x52,
	0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52,
	0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x02, 0x2a, 0xb8, 0x06, 0x0a, 0x18, 0x52, 0x65, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x75, 0x62, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x2b, 0x0a, 0x27, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x3c, 0x0a, 0x38, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10, 0x01,
	0x12, 0x32, 0x0a, 0x2e, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x51, 0x55, 0x45, 0x55, 0x45, 0x44, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53,
	0x55, 0x45, 0x10, 0x02, 0x12, 0x3b, 0x0a, 0x37, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x56, 0x45, 0x52, 0x44, 0x49, 0x43,
	0x54, 0x5f, 0x41, 0x46, 0x54, 0x45, 0x52, 0x5f, 0x4f, 0x55, 0x54, 0x43, 0x41, 0x4c, 0x4c, 0x10,
	0x03, 0x12, 0x30, 0x0a, 0x2c, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x5f, 0x4f, 0x55, 0x54, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4d, 0x41, 0x44,
	0x45, 0x10, 0x04, 0x12, 0x3d, 0x0a, 0x39, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45,
	0x10, 0x05, 0x12, 0x37, 0x0a, 0x33, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x57, 0x52, 0x4f, 0x4e,
	0x47, 0x5f, 0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x10, 0x06, 0x12, 0x33, 0x0a, 0x2f, 0x52,
	0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53,
	0x55, 0x42, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x45,
	0x44, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x53, 0x10, 0x07,
	0x12, 0x36, 0x0a, 0x32, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x4d, 0x49, 0x53, 0x53, 0x45, 0x44, 0x5f, 0x50, 0x4f, 0x54, 0x45, 0x4e, 0x54, 0x49, 0x41, 0x4c,
	0x5f, 0x46, 0x52, 0x41, 0x55, 0x44, 0x10, 0x08, 0x12, 0x2e, 0x0a, 0x2a, 0x52, 0x45, 0x5f, 0x52,
	0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x45, 0x44, 0x5f, 0x52,
	0x45, 0x4d, 0x41, 0x52, 0x4b, 0x53, 0x10, 0x09, 0x12, 0x31, 0x0a, 0x2d, 0x52, 0x45, 0x5f, 0x52,
	0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x56, 0x49,
	0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10, 0x0a, 0x12, 0x35, 0x0a, 0x31, 0x52,
	0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53,
	0x55, 0x42, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x57, 0x52, 0x4f, 0x4e, 0x47,
	0x5f, 0x50, 0x4f, 0x54, 0x45, 0x4e, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x46, 0x52, 0x41, 0x55, 0x44,
	0x10, 0x0b, 0x12, 0x31, 0x0a, 0x2d, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x50, 0x45, 0x52,
	0x53, 0x4f, 0x4e, 0x10, 0x0c, 0x12, 0x2d, 0x0a, 0x29, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43, 0x4f, 0x4d,
	0x42, 0x4f, 0x10, 0x0d, 0x12, 0x2d, 0x0a, 0x29, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x4d, 0x41, 0x52, 0x4b,
	0x53, 0x10, 0x0e, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5a,
	0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68,
	0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_auth_liveness_internal_face_match_annotation_proto_rawDescOnce sync.Once
	file_api_auth_liveness_internal_face_match_annotation_proto_rawDescData = file_api_auth_liveness_internal_face_match_annotation_proto_rawDesc
)

func file_api_auth_liveness_internal_face_match_annotation_proto_rawDescGZIP() []byte {
	file_api_auth_liveness_internal_face_match_annotation_proto_rawDescOnce.Do(func() {
		file_api_auth_liveness_internal_face_match_annotation_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_liveness_internal_face_match_annotation_proto_rawDescData)
	})
	return file_api_auth_liveness_internal_face_match_annotation_proto_rawDescData
}

var file_api_auth_liveness_internal_face_match_annotation_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_auth_liveness_internal_face_match_annotation_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_auth_liveness_internal_face_match_annotation_proto_goTypes = []interface{}{
	(FaceMatchVerdict)(0),         // 0: auth.liveness.FaceMatchVerdict
	(FaceMatchIssue)(0),           // 1: auth.liveness.FaceMatchIssue
	(ReReviewErrorType)(0),        // 2: auth.liveness.ReReviewErrorType
	(ReReviewErrorSubcategory)(0), // 3: auth.liveness.ReReviewErrorSubcategory
	(*FaceMatchAnnotation)(nil),   // 4: auth.liveness.FaceMatchAnnotation
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
}
var file_api_auth_liveness_internal_face_match_annotation_proto_depIdxs = []int32{
	1, // 0: auth.liveness.FaceMatchAnnotation.facematch_issues:type_name -> auth.liveness.FaceMatchIssue
	0, // 1: auth.liveness.FaceMatchAnnotation.review_verdict:type_name -> auth.liveness.FaceMatchVerdict
	5, // 2: auth.liveness.FaceMatchAnnotation.reviewed_on:type_name -> google.protobuf.Timestamp
	0, // 3: auth.liveness.FaceMatchAnnotation.re_review_verdict:type_name -> auth.liveness.FaceMatchVerdict
	5, // 4: auth.liveness.FaceMatchAnnotation.re_reviewed_on:type_name -> google.protobuf.Timestamp
	2, // 5: auth.liveness.FaceMatchAnnotation.re_review_error_type:type_name -> auth.liveness.ReReviewErrorType
	2, // 6: auth.liveness.FaceMatchAnnotation.re_review_error_types:type_name -> auth.liveness.ReReviewErrorType
	3, // 7: auth.liveness.FaceMatchAnnotation.re_review_error_subcategories:type_name -> auth.liveness.ReReviewErrorSubcategory
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_auth_liveness_internal_face_match_annotation_proto_init() }
func file_api_auth_liveness_internal_face_match_annotation_proto_init() {
	if File_api_auth_liveness_internal_face_match_annotation_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_liveness_internal_face_match_annotation_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceMatchAnnotation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_liveness_internal_face_match_annotation_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_liveness_internal_face_match_annotation_proto_goTypes,
		DependencyIndexes: file_api_auth_liveness_internal_face_match_annotation_proto_depIdxs,
		EnumInfos:         file_api_auth_liveness_internal_face_match_annotation_proto_enumTypes,
		MessageInfos:      file_api_auth_liveness_internal_face_match_annotation_proto_msgTypes,
	}.Build()
	File_api_auth_liveness_internal_face_match_annotation_proto = out.File
	file_api_auth_liveness_internal_face_match_annotation_proto_rawDesc = nil
	file_api_auth_liveness_internal_face_match_annotation_proto_goTypes = nil
	file_api_auth_liveness_internal_face_match_annotation_proto_depIdxs = nil
}
