// protolint:disable MAX_LINE_LENGTH

//
//Protos relating to the Liveness that are internal to the domain such as data models

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/liveness/internal/liveness_attempt.proto

package liveness

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	inhouse "github.com/epifi/gamma/api/vendors/inhouse"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// LivenessAttemptFieldMask is used to mask columns to update in DB Update call
type LivenessAttemptFieldMask int32

const (
	LivenessAttemptFieldMask_LIVENESS_ATTEMPT_FIELD_NONE LivenessAttemptFieldMask = 0
	LivenessAttemptFieldMask_VENDOR                      LivenessAttemptFieldMask = 1
	LivenessAttemptFieldMask_VENDOR_REQUEST_ID           LivenessAttemptFieldMask = 2
	LivenessAttemptFieldMask_VIDEO_LOCATION              LivenessAttemptFieldMask = 4
	LivenessAttemptFieldMask_STATUS                      LivenessAttemptFieldMask = 5
	LivenessAttemptFieldMask_IMAGE_FRAME                 LivenessAttemptFieldMask = 6
	LivenessAttemptFieldMask_OTP_SCORE                   LivenessAttemptFieldMask = 7
	LivenessAttemptFieldMask_LIVENESS_SCORE              LivenessAttemptFieldMask = 8
	LivenessAttemptFieldMask_DETECTED_OTP                LivenessAttemptFieldMask = 9
	LivenessAttemptFieldMask_GOOGLE_DETECTED_OTP         LivenessAttemptFieldMask = 10
	LivenessAttemptFieldMask_BLINK_CONFIDENCE            LivenessAttemptFieldMask = 11
	LivenessAttemptFieldMask_BLINK_HEURISTICS_CONFIDENCE LivenessAttemptFieldMask = 12
	LivenessAttemptFieldMask_BLINK_LIVENESS              LivenessAttemptFieldMask = 13
	LivenessAttemptFieldMask_VENDOR_RESPONSE             LivenessAttemptFieldMask = 14
	LivenessAttemptFieldMask_LIVENESS_FLOW               LivenessAttemptFieldMask = 15
	LivenessAttemptFieldMask_BLINK_LIVENESS_STATUS       LivenessAttemptFieldMask = 16
	LivenessAttemptFieldMask_METADATA                    LivenessAttemptFieldMask = 17
	LivenessAttemptFieldMask_INHOUSE_LIVENESS            LivenessAttemptFieldMask = 18
	LivenessAttemptFieldMask_ANNOTATION                  LivenessAttemptFieldMask = 19
	LivenessAttemptFieldMask_SUMMARY_ID                  LivenessAttemptFieldMask = 20
	LivenessAttemptFieldMask_VENDOR_STATUS               LivenessAttemptFieldMask = 21
	LivenessAttemptFieldMask_STRICTNESS_LOGIC            LivenessAttemptFieldMask = 22
)

// Enum value maps for LivenessAttemptFieldMask.
var (
	LivenessAttemptFieldMask_name = map[int32]string{
		0:  "LIVENESS_ATTEMPT_FIELD_NONE",
		1:  "VENDOR",
		2:  "VENDOR_REQUEST_ID",
		4:  "VIDEO_LOCATION",
		5:  "STATUS",
		6:  "IMAGE_FRAME",
		7:  "OTP_SCORE",
		8:  "LIVENESS_SCORE",
		9:  "DETECTED_OTP",
		10: "GOOGLE_DETECTED_OTP",
		11: "BLINK_CONFIDENCE",
		12: "BLINK_HEURISTICS_CONFIDENCE",
		13: "BLINK_LIVENESS",
		14: "VENDOR_RESPONSE",
		15: "LIVENESS_FLOW",
		16: "BLINK_LIVENESS_STATUS",
		17: "METADATA",
		18: "INHOUSE_LIVENESS",
		19: "ANNOTATION",
		20: "SUMMARY_ID",
		21: "VENDOR_STATUS",
		22: "STRICTNESS_LOGIC",
	}
	LivenessAttemptFieldMask_value = map[string]int32{
		"LIVENESS_ATTEMPT_FIELD_NONE": 0,
		"VENDOR":                      1,
		"VENDOR_REQUEST_ID":           2,
		"VIDEO_LOCATION":              4,
		"STATUS":                      5,
		"IMAGE_FRAME":                 6,
		"OTP_SCORE":                   7,
		"LIVENESS_SCORE":              8,
		"DETECTED_OTP":                9,
		"GOOGLE_DETECTED_OTP":         10,
		"BLINK_CONFIDENCE":            11,
		"BLINK_HEURISTICS_CONFIDENCE": 12,
		"BLINK_LIVENESS":              13,
		"VENDOR_RESPONSE":             14,
		"LIVENESS_FLOW":               15,
		"BLINK_LIVENESS_STATUS":       16,
		"METADATA":                    17,
		"INHOUSE_LIVENESS":            18,
		"ANNOTATION":                  19,
		"SUMMARY_ID":                  20,
		"VENDOR_STATUS":               21,
		"STRICTNESS_LOGIC":            22,
	}
)

func (x LivenessAttemptFieldMask) Enum() *LivenessAttemptFieldMask {
	p := new(LivenessAttemptFieldMask)
	*p = x
	return p
}

func (x LivenessAttemptFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LivenessAttemptFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_internal_liveness_attempt_proto_enumTypes[0].Descriptor()
}

func (LivenessAttemptFieldMask) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_internal_liveness_attempt_proto_enumTypes[0]
}

func (x LivenessAttemptFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LivenessAttemptFieldMask.Descriptor instead.
func (LivenessAttemptFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_attempt_proto_rawDescGZIP(), []int{0}
}

type Certify int32

const (
	Certify_CERTIFY_UNSPECIFIED Certify = 0
	Certify_CERTIFY_TRUE        Certify = 1
	Certify_CERTIFY_FALSE       Certify = 2
	Certify_CERTIFY_UNSURE      Certify = 3
)

// Enum value maps for Certify.
var (
	Certify_name = map[int32]string{
		0: "CERTIFY_UNSPECIFIED",
		1: "CERTIFY_TRUE",
		2: "CERTIFY_FALSE",
		3: "CERTIFY_UNSURE",
	}
	Certify_value = map[string]int32{
		"CERTIFY_UNSPECIFIED": 0,
		"CERTIFY_TRUE":        1,
		"CERTIFY_FALSE":       2,
		"CERTIFY_UNSURE":      3,
	}
)

func (x Certify) Enum() *Certify {
	p := new(Certify)
	*p = x
	return p
}

func (x Certify) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Certify) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_internal_liveness_attempt_proto_enumTypes[1].Descriptor()
}

func (Certify) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_internal_liveness_attempt_proto_enumTypes[1]
}

func (x Certify) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Certify.Descriptor instead.
func (Certify) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_attempt_proto_rawDescGZIP(), []int{1}
}

type Verdict int32

const (
	Verdict_VERDICT_UNSPECIFIED Verdict = 0
	Verdict_VERDICT_PASS        Verdict = 1
	Verdict_VERDICT_FAIL        Verdict = 2
)

// Enum value maps for Verdict.
var (
	Verdict_name = map[int32]string{
		0: "VERDICT_UNSPECIFIED",
		1: "VERDICT_PASS",
		2: "VERDICT_FAIL",
	}
	Verdict_value = map[string]int32{
		"VERDICT_UNSPECIFIED": 0,
		"VERDICT_PASS":        1,
		"VERDICT_FAIL":        2,
	}
)

func (x Verdict) Enum() *Verdict {
	p := new(Verdict)
	*p = x
	return p
}

func (x Verdict) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Verdict) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_internal_liveness_attempt_proto_enumTypes[2].Descriptor()
}

func (Verdict) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_internal_liveness_attempt_proto_enumTypes[2]
}

func (x Verdict) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Verdict.Descriptor instead.
func (Verdict) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_attempt_proto_rawDescGZIP(), []int{2}
}

type VideoIssue int32

const (
	VideoIssue_VIDEO_ISSUE_UNSPECIFIED                       VideoIssue = 0
	VideoIssue_VIDEO_ISSUE_LOW_LIGHTING                      VideoIssue = 1
	VideoIssue_VIDEO_ISSUE_OVEREXPOSURE                      VideoIssue = 2
	VideoIssue_VIDEO_ISSUE_UNUSUAL_ANGLE                     VideoIssue = 3
	VideoIssue_VIDEO_ISSUE_LOW_QUALITY_VIDEO                 VideoIssue = 4
	VideoIssue_VIDEO_ISSUE_OBFUSCATED_FACE                   VideoIssue = 5
	VideoIssue_VIDEO_ISSUE_TECHNICAL_ISSUE                   VideoIssue = 6
	VideoIssue_VIDEO_ISSUE_CUSTOMER_USING_PHOTO_FOR_LIVENESS VideoIssue = 7
	VideoIssue_VIDEO_ISSUE_CUSTOMER_USING_VIDEO_FOR_LIVENESS VideoIssue = 8
	VideoIssue_VIDEO_ISSUE_CUSTOMER_USING_APP_FOR_LIVENESS   VideoIssue = 9
	VideoIssue_VIDEO_ISSUE_VIDEO_OPTIMISATION_ISSUE          VideoIssue = 10
	VideoIssue_VIDEO_ISSUE_OTP_RELATED                       VideoIssue = 11
	VideoIssue_VIDEO_ISSUE_GENDER_MISMATCH_AUDIO             VideoIssue = 12
	VideoIssue_VIDEO_ISSUE_MULTIPLE_FACES                    VideoIssue = 13
	VideoIssue_VIDEO_ISSUE_BLURRED_VIDEO                     VideoIssue = 14
	VideoIssue_VIDEO_ISSUE_SPOOFING_ATTEMPT                  VideoIssue = 15
	VideoIssue_VIDEO_ISSUE_USER_MISLEAD                      VideoIssue = 16
	VideoIssue_VIDEO_ISSUE_UNDER_AGE_CUSTOMER                VideoIssue = 17
	VideoIssue_VIDEO_ISSUE_GENDER_MISMATCH_VIDEO             VideoIssue = 18
	VideoIssue_VIDEO_ISSUE_FACE_COVER_ISSUE                  VideoIssue = 19
	VideoIssue_VIDEO_ISSUE_FACE_TOO_CLOSE                    VideoIssue = 20
	VideoIssue_VIDEO_ISSUE_FACE_TOO_FAR                      VideoIssue = 21
	VideoIssue_VIDEO_ISSUE_CAMERA_PLACEMENT_ISSUE            VideoIssue = 22
	VideoIssue_VIDEO_ISSUE_OTHER                             VideoIssue = 23
	VideoIssue_VIDEO_ISSUE_MULTIPLE_AUDIO                    VideoIssue = 24
)

// Enum value maps for VideoIssue.
var (
	VideoIssue_name = map[int32]string{
		0:  "VIDEO_ISSUE_UNSPECIFIED",
		1:  "VIDEO_ISSUE_LOW_LIGHTING",
		2:  "VIDEO_ISSUE_OVEREXPOSURE",
		3:  "VIDEO_ISSUE_UNUSUAL_ANGLE",
		4:  "VIDEO_ISSUE_LOW_QUALITY_VIDEO",
		5:  "VIDEO_ISSUE_OBFUSCATED_FACE",
		6:  "VIDEO_ISSUE_TECHNICAL_ISSUE",
		7:  "VIDEO_ISSUE_CUSTOMER_USING_PHOTO_FOR_LIVENESS",
		8:  "VIDEO_ISSUE_CUSTOMER_USING_VIDEO_FOR_LIVENESS",
		9:  "VIDEO_ISSUE_CUSTOMER_USING_APP_FOR_LIVENESS",
		10: "VIDEO_ISSUE_VIDEO_OPTIMISATION_ISSUE",
		11: "VIDEO_ISSUE_OTP_RELATED",
		12: "VIDEO_ISSUE_GENDER_MISMATCH_AUDIO",
		13: "VIDEO_ISSUE_MULTIPLE_FACES",
		14: "VIDEO_ISSUE_BLURRED_VIDEO",
		15: "VIDEO_ISSUE_SPOOFING_ATTEMPT",
		16: "VIDEO_ISSUE_USER_MISLEAD",
		17: "VIDEO_ISSUE_UNDER_AGE_CUSTOMER",
		18: "VIDEO_ISSUE_GENDER_MISMATCH_VIDEO",
		19: "VIDEO_ISSUE_FACE_COVER_ISSUE",
		20: "VIDEO_ISSUE_FACE_TOO_CLOSE",
		21: "VIDEO_ISSUE_FACE_TOO_FAR",
		22: "VIDEO_ISSUE_CAMERA_PLACEMENT_ISSUE",
		23: "VIDEO_ISSUE_OTHER",
		24: "VIDEO_ISSUE_MULTIPLE_AUDIO",
	}
	VideoIssue_value = map[string]int32{
		"VIDEO_ISSUE_UNSPECIFIED":                       0,
		"VIDEO_ISSUE_LOW_LIGHTING":                      1,
		"VIDEO_ISSUE_OVEREXPOSURE":                      2,
		"VIDEO_ISSUE_UNUSUAL_ANGLE":                     3,
		"VIDEO_ISSUE_LOW_QUALITY_VIDEO":                 4,
		"VIDEO_ISSUE_OBFUSCATED_FACE":                   5,
		"VIDEO_ISSUE_TECHNICAL_ISSUE":                   6,
		"VIDEO_ISSUE_CUSTOMER_USING_PHOTO_FOR_LIVENESS": 7,
		"VIDEO_ISSUE_CUSTOMER_USING_VIDEO_FOR_LIVENESS": 8,
		"VIDEO_ISSUE_CUSTOMER_USING_APP_FOR_LIVENESS":   9,
		"VIDEO_ISSUE_VIDEO_OPTIMISATION_ISSUE":          10,
		"VIDEO_ISSUE_OTP_RELATED":                       11,
		"VIDEO_ISSUE_GENDER_MISMATCH_AUDIO":             12,
		"VIDEO_ISSUE_MULTIPLE_FACES":                    13,
		"VIDEO_ISSUE_BLURRED_VIDEO":                     14,
		"VIDEO_ISSUE_SPOOFING_ATTEMPT":                  15,
		"VIDEO_ISSUE_USER_MISLEAD":                      16,
		"VIDEO_ISSUE_UNDER_AGE_CUSTOMER":                17,
		"VIDEO_ISSUE_GENDER_MISMATCH_VIDEO":             18,
		"VIDEO_ISSUE_FACE_COVER_ISSUE":                  19,
		"VIDEO_ISSUE_FACE_TOO_CLOSE":                    20,
		"VIDEO_ISSUE_FACE_TOO_FAR":                      21,
		"VIDEO_ISSUE_CAMERA_PLACEMENT_ISSUE":            22,
		"VIDEO_ISSUE_OTHER":                             23,
		"VIDEO_ISSUE_MULTIPLE_AUDIO":                    24,
	}
)

func (x VideoIssue) Enum() *VideoIssue {
	p := new(VideoIssue)
	*p = x
	return p
}

func (x VideoIssue) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VideoIssue) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_internal_liveness_attempt_proto_enumTypes[3].Descriptor()
}

func (VideoIssue) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_internal_liveness_attempt_proto_enumTypes[3]
}

func (x VideoIssue) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VideoIssue.Descriptor instead.
func (VideoIssue) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_attempt_proto_rawDescGZIP(), []int{3}
}

// Inhouse Liveness API sends errors for various situations
type InhouseError int32

const (
	InhouseError_INHOUSE_ERROR_UNSPECIFIED             InhouseError = 0
	InhouseError_INHOUSE_ERROR_NO_SOURCE               InhouseError = 1
	InhouseError_INHOUSE_ERROR_LESS_FRAMES             InhouseError = 2
	InhouseError_INHOUSE_ERROR_PROCESSING_FAILURE      InhouseError = 3
	InhouseError_INHOUSE_ERROR_DOWNLOAD_FAILURE        InhouseError = 4
	InhouseError_INHOUSE_ERROR_MULTIPLE_FACES_DETECTED InhouseError = 5
	InhouseError_INHOUSE_ERROR_NO_FACE_FRAMES          InhouseError = 6
	InhouseError_INHOUSE_ERROR_FACE_OBFUSCATION        InhouseError = 7
)

// Enum value maps for InhouseError.
var (
	InhouseError_name = map[int32]string{
		0: "INHOUSE_ERROR_UNSPECIFIED",
		1: "INHOUSE_ERROR_NO_SOURCE",
		2: "INHOUSE_ERROR_LESS_FRAMES",
		3: "INHOUSE_ERROR_PROCESSING_FAILURE",
		4: "INHOUSE_ERROR_DOWNLOAD_FAILURE",
		5: "INHOUSE_ERROR_MULTIPLE_FACES_DETECTED",
		6: "INHOUSE_ERROR_NO_FACE_FRAMES",
		7: "INHOUSE_ERROR_FACE_OBFUSCATION",
	}
	InhouseError_value = map[string]int32{
		"INHOUSE_ERROR_UNSPECIFIED":             0,
		"INHOUSE_ERROR_NO_SOURCE":               1,
		"INHOUSE_ERROR_LESS_FRAMES":             2,
		"INHOUSE_ERROR_PROCESSING_FAILURE":      3,
		"INHOUSE_ERROR_DOWNLOAD_FAILURE":        4,
		"INHOUSE_ERROR_MULTIPLE_FACES_DETECTED": 5,
		"INHOUSE_ERROR_NO_FACE_FRAMES":          6,
		"INHOUSE_ERROR_FACE_OBFUSCATION":        7,
	}
)

func (x InhouseError) Enum() *InhouseError {
	p := new(InhouseError)
	*p = x
	return p
}

func (x InhouseError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InhouseError) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_internal_liveness_attempt_proto_enumTypes[4].Descriptor()
}

func (InhouseError) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_internal_liveness_attempt_proto_enumTypes[4]
}

func (x InhouseError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InhouseError.Descriptor instead.
func (InhouseError) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_attempt_proto_rawDescGZIP(), []int{4}
}

// Inhouse Liveness API sends statuses of faces detected in each frame
type FaceFlag int32

const (
	FaceFlag_FACE_FLAG_UNSPECIFIED         FaceFlag = 0
	FaceFlag_FACE_FLAG_NO_FACE             FaceFlag = 1
	FaceFlag_FACE_FLAG_MANY_FACES          FaceFlag = 2
	FaceFlag_FACE_FLAG_FACE_LOW_CONFIDENCE FaceFlag = 3
	FaceFlag_FACE_FLAG_FACE_TOO_CLOSE      FaceFlag = 4
	FaceFlag_FACE_FLAG_FACE_TOO_FAR        FaceFlag = 5
	FaceFlag_FACE_FLAG_FACE_SUCCESS        FaceFlag = 6
)

// Enum value maps for FaceFlag.
var (
	FaceFlag_name = map[int32]string{
		0: "FACE_FLAG_UNSPECIFIED",
		1: "FACE_FLAG_NO_FACE",
		2: "FACE_FLAG_MANY_FACES",
		3: "FACE_FLAG_FACE_LOW_CONFIDENCE",
		4: "FACE_FLAG_FACE_TOO_CLOSE",
		5: "FACE_FLAG_FACE_TOO_FAR",
		6: "FACE_FLAG_FACE_SUCCESS",
	}
	FaceFlag_value = map[string]int32{
		"FACE_FLAG_UNSPECIFIED":         0,
		"FACE_FLAG_NO_FACE":             1,
		"FACE_FLAG_MANY_FACES":          2,
		"FACE_FLAG_FACE_LOW_CONFIDENCE": 3,
		"FACE_FLAG_FACE_TOO_CLOSE":      4,
		"FACE_FLAG_FACE_TOO_FAR":        5,
		"FACE_FLAG_FACE_SUCCESS":        6,
	}
)

func (x FaceFlag) Enum() *FaceFlag {
	p := new(FaceFlag)
	*p = x
	return p
}

func (x FaceFlag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FaceFlag) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_internal_liveness_attempt_proto_enumTypes[5].Descriptor()
}

func (FaceFlag) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_internal_liveness_attempt_proto_enumTypes[5]
}

func (x FaceFlag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FaceFlag.Descriptor instead.
func (FaceFlag) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_attempt_proto_rawDescGZIP(), []int{5}
}

// LivenessAttempt stores the information related to the liveness attempt.
type LivenessAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId   string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AttemptId string `protobuf:"bytes,2,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
	// RequestID is the ID with which the caller calls liveness to identify and track the status of the request.
	RequestId string `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Request ID to track vendor request
	VendorRequestId string `protobuf:"bytes,4,opt,name=vendor_request_id,json=vendorRequestId,proto3" json:"vendor_request_id,omitempty"`
	// Vendor
	Vendor     vendorgateway.Vendor `protobuf:"varint,8,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	ImageFrame *common.Image        `protobuf:"bytes,10,opt,name=image_frame,json=imageFrame,proto3" json:"image_frame,omitempty"`
	// location of video which was used to do the liveness check. Example for S3: s3://demo-bucket/try/p3_2.mp4
	VideoLocation string `protobuf:"bytes,5,opt,name=video_location,json=videoLocation,proto3" json:"video_location,omitempty"`
	// Status of the liveness attempt
	Status LivenessStatus `protobuf:"varint,6,opt,name=status,proto3,enum=auth.liveness.LivenessStatus" json:"status,omitempty"`
	// OTP of the request
	Otp string `protobuf:"bytes,7,opt,name=otp,proto3" json:"otp,omitempty"`
	// OTP detected by the vendor.
	DetectedOtp string `protobuf:"bytes,13,opt,name=detected_otp,json=detectedOtp,proto3" json:"detected_otp,omitempty"`
	// OTP detected by the google speech to text.
	GoogleDetectedOtp string `protobuf:"bytes,14,opt,name=google_detected_otp,json=googleDetectedOtp,proto3" json:"google_detected_otp,omitempty"`
	// OTP is matching
	OtpScore float32 `protobuf:"fixed32,11,opt,name=otp_score,json=otpScore,proto3" json:"otp_score,omitempty"`
	// Liveness score
	LivenessScore float32 `protobuf:"fixed32,12,opt,name=liveness_score,json=livenessScore,proto3" json:"liveness_score,omitempty"`
	// inhouse blink based confidence.
	// deprecated in favour of InHouseLiveness
	//
	// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_attempt.proto.
	BlinkConfidence float32 `protobuf:"fixed32,15,opt,name=blink_confidence,json=blinkConfidence,proto3" json:"blink_confidence,omitempty"`
	// Confidence in the accuracy of the model.
	// deprecated in favour of InHouseLiveness
	//
	// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_attempt.proto.
	BlinkHeuristicsConfidence float32 `protobuf:"fixed32,16,opt,name=blink_heuristics_confidence,json=blinkHeuristicsConfidence,proto3" json:"blink_heuristics_confidence,omitempty"`
	// deprecated in favour of InHouseLiveness
	//
	// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_attempt.proto.
	BlinkLiveness  bool                   `protobuf:"varint,17,opt,name=blink_liveness,json=blinkLiveness,proto3" json:"blink_liveness,omitempty"`
	VendorResponse string                 `protobuf:"bytes,18,opt,name=vendor_response,json=vendorResponse,proto3" json:"vendor_response,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	LivenessFlow   LivenessFlow           `protobuf:"varint,20,opt,name=liveness_flow,json=livenessFlow,proto3,enum=auth.liveness.LivenessFlow" json:"liveness_flow,omitempty"`
	// Status of blink liveness request
	//
	// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_attempt.proto.
	BlinkLivenessStatus BlinkLivenessStatus    `protobuf:"varint,21,opt,name=blink_liveness_status,json=blinkLivenessStatus,proto3,enum=auth.liveness.BlinkLivenessStatus" json:"blink_liveness_status,omitempty"`
	UpdatedAt           *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Stores metadata about liveness attempts
	Metadata *Metadata `protobuf:"bytes,23,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// InHouseLiveness stores all info related to in-house liveness processing
	InHouseLiveness *InHouseLiveness `protobuf:"bytes,24,opt,name=in_house_liveness,json=inHouseLiveness,proto3" json:"in_house_liveness,omitempty"`
	// Annotation stores notes and observations related to liveness attempt
	Annotation *Annotation `protobuf:"bytes,25,opt,name=annotation,proto3" json:"annotation,omitempty"`
	// Id of the liveness summary this attempt corresponds to
	SummaryId string `protobuf:"bytes,26,opt,name=summary_id,json=summaryId,proto3" json:"summary_id,omitempty"`
	// store strictness logic applicable for user
	//
	// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_attempt.proto.
	StrictnessLogic StrictnessLogic `protobuf:"varint,27,opt,name=strictness_logic,json=strictnessLogic,proto3,enum=auth.liveness.StrictnessLogic" json:"strictness_logic,omitempty"`
	// store vendor status response
	VendorStatus LivenessStatus `protobuf:"varint,28,opt,name=vendor_status,json=vendorStatus,proto3,enum=auth.liveness.LivenessStatus" json:"vendor_status,omitempty"`
}

func (x *LivenessAttempt) Reset() {
	*x = LivenessAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessAttempt) ProtoMessage() {}

func (x *LivenessAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessAttempt.ProtoReflect.Descriptor instead.
func (*LivenessAttempt) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_attempt_proto_rawDescGZIP(), []int{0}
}

func (x *LivenessAttempt) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *LivenessAttempt) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *LivenessAttempt) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *LivenessAttempt) GetVendorRequestId() string {
	if x != nil {
		return x.VendorRequestId
	}
	return ""
}

func (x *LivenessAttempt) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *LivenessAttempt) GetImageFrame() *common.Image {
	if x != nil {
		return x.ImageFrame
	}
	return nil
}

func (x *LivenessAttempt) GetVideoLocation() string {
	if x != nil {
		return x.VideoLocation
	}
	return ""
}

func (x *LivenessAttempt) GetStatus() LivenessStatus {
	if x != nil {
		return x.Status
	}
	return LivenessStatus_LIVENESS_STATUS_UNSPECIFIED
}

func (x *LivenessAttempt) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *LivenessAttempt) GetDetectedOtp() string {
	if x != nil {
		return x.DetectedOtp
	}
	return ""
}

func (x *LivenessAttempt) GetGoogleDetectedOtp() string {
	if x != nil {
		return x.GoogleDetectedOtp
	}
	return ""
}

func (x *LivenessAttempt) GetOtpScore() float32 {
	if x != nil {
		return x.OtpScore
	}
	return 0
}

func (x *LivenessAttempt) GetLivenessScore() float32 {
	if x != nil {
		return x.LivenessScore
	}
	return 0
}

// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_attempt.proto.
func (x *LivenessAttempt) GetBlinkConfidence() float32 {
	if x != nil {
		return x.BlinkConfidence
	}
	return 0
}

// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_attempt.proto.
func (x *LivenessAttempt) GetBlinkHeuristicsConfidence() float32 {
	if x != nil {
		return x.BlinkHeuristicsConfidence
	}
	return 0
}

// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_attempt.proto.
func (x *LivenessAttempt) GetBlinkLiveness() bool {
	if x != nil {
		return x.BlinkLiveness
	}
	return false
}

func (x *LivenessAttempt) GetVendorResponse() string {
	if x != nil {
		return x.VendorResponse
	}
	return ""
}

func (x *LivenessAttempt) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LivenessAttempt) GetLivenessFlow() LivenessFlow {
	if x != nil {
		return x.LivenessFlow
	}
	return LivenessFlow_LIVENESS_FLOW_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_attempt.proto.
func (x *LivenessAttempt) GetBlinkLivenessStatus() BlinkLivenessStatus {
	if x != nil {
		return x.BlinkLivenessStatus
	}
	return BlinkLivenessStatus_BLINK_LIVENESS_UNSPECIFIED
}

func (x *LivenessAttempt) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *LivenessAttempt) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *LivenessAttempt) GetInHouseLiveness() *InHouseLiveness {
	if x != nil {
		return x.InHouseLiveness
	}
	return nil
}

func (x *LivenessAttempt) GetAnnotation() *Annotation {
	if x != nil {
		return x.Annotation
	}
	return nil
}

func (x *LivenessAttempt) GetSummaryId() string {
	if x != nil {
		return x.SummaryId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_attempt.proto.
func (x *LivenessAttempt) GetStrictnessLogic() StrictnessLogic {
	if x != nil {
		return x.StrictnessLogic
	}
	return StrictnessLogic_STRICTNESS_LOGIC_UNSPECIFIED
}

func (x *LivenessAttempt) GetVendorStatus() LivenessStatus {
	if x != nil {
		return x.VendorStatus
	}
	return LivenessStatus_LIVENESS_STATUS_UNSPECIFIED
}

type InHouseLiveness struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status            InHouseLivenessStatus          `protobuf:"varint,1,opt,name=status,proto3,enum=auth.liveness.InHouseLivenessStatus" json:"status,omitempty"`
	RawResponse       *inhouse.CheckLivenessResponse `protobuf:"bytes,2,opt,name=raw_response,json=rawResponse,proto3" json:"raw_response,omitempty"`
	RawVendorResponse string                         `protobuf:"bytes,3,opt,name=raw_vendor_response,json=rawVendorResponse,proto3" json:"raw_vendor_response,omitempty"`
	InhouseErrors     []InhouseError                 `protobuf:"varint,4,rep,packed,name=inhouse_errors,json=inhouseErrors,proto3,enum=auth.liveness.InhouseError" json:"inhouse_errors,omitempty"`
	FaceFlags         []FaceFlag                     `protobuf:"varint,5,rep,packed,name=face_flags,json=faceFlags,proto3,enum=auth.liveness.FaceFlag" json:"face_flags,omitempty"`
}

func (x *InHouseLiveness) Reset() {
	*x = InHouseLiveness{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InHouseLiveness) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InHouseLiveness) ProtoMessage() {}

func (x *InHouseLiveness) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InHouseLiveness.ProtoReflect.Descriptor instead.
func (*InHouseLiveness) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_attempt_proto_rawDescGZIP(), []int{1}
}

func (x *InHouseLiveness) GetStatus() InHouseLivenessStatus {
	if x != nil {
		return x.Status
	}
	return InHouseLivenessStatus_IN_HOUSE_UNSPECIFIED
}

func (x *InHouseLiveness) GetRawResponse() *inhouse.CheckLivenessResponse {
	if x != nil {
		return x.RawResponse
	}
	return nil
}

func (x *InHouseLiveness) GetRawVendorResponse() string {
	if x != nil {
		return x.RawVendorResponse
	}
	return ""
}

func (x *InHouseLiveness) GetInhouseErrors() []InhouseError {
	if x != nil {
		return x.InhouseErrors
	}
	return nil
}

func (x *InHouseLiveness) GetFaceFlags() []FaceFlag {
	if x != nil {
		return x.FaceFlags
	}
	return nil
}

type Metadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvalidVideoRetryCount int32 `protobuf:"varint,1,opt,name=invalid_video_retry_count,json=invalidVideoRetryCount,proto3" json:"invalid_video_retry_count,omitempty"`
	// user location store user's location token during liveness
	UserLocationToken string `protobuf:"bytes,2,opt,name=user_location_token,json=userLocationToken,proto3" json:"user_location_token,omitempty"`
	// user ip address stores user ip address during liveness
	UserIpAddress string `protobuf:"bytes,3,opt,name=user_ip_address,json=userIpAddress,proto3" json:"user_ip_address,omitempty"`
	// message for storing device sensor data
	DeviceSensorData *DeviceSensorData `protobuf:"bytes,4,opt,name=device_sensor_data,json=deviceSensorData,proto3" json:"device_sensor_data,omitempty"`
	// timestamp at which we updated the liveness attempt status to streaming started
	StreamingStartedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=streaming_started_at,json=streamingStartedAt,proto3" json:"streaming_started_at,omitempty"`
	VpnInfo            *typesv2.VPNInfo       `protobuf:"bytes,6,opt,name=vpn_info,json=vpnInfo,proto3" json:"vpn_info,omitempty"`
}

func (x *Metadata) Reset() {
	*x = Metadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metadata) ProtoMessage() {}

func (x *Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metadata.ProtoReflect.Descriptor instead.
func (*Metadata) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_attempt_proto_rawDescGZIP(), []int{2}
}

func (x *Metadata) GetInvalidVideoRetryCount() int32 {
	if x != nil {
		return x.InvalidVideoRetryCount
	}
	return 0
}

func (x *Metadata) GetUserLocationToken() string {
	if x != nil {
		return x.UserLocationToken
	}
	return ""
}

func (x *Metadata) GetUserIpAddress() string {
	if x != nil {
		return x.UserIpAddress
	}
	return ""
}

func (x *Metadata) GetDeviceSensorData() *DeviceSensorData {
	if x != nil {
		return x.DeviceSensorData
	}
	return nil
}

func (x *Metadata) GetStreamingStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StreamingStartedAt
	}
	return nil
}

func (x *Metadata) GetVpnInfo() *typesv2.VPNInfo {
	if x != nil {
		return x.VpnInfo
	}
	return nil
}

type DeviceSensorData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccelerometerData []*typesv2.ThreeDimensionalCoordinate `protobuf:"bytes,1,rep,name=accelerometer_data,json=accelerometerData,proto3" json:"accelerometer_data,omitempty"`
	GyroscopeData     []*typesv2.ThreeDimensionalCoordinate `protobuf:"bytes,2,rep,name=gyroscope_data,json=gyroscopeData,proto3" json:"gyroscope_data,omitempty"`
	Proximity         []float32                             `protobuf:"fixed32,3,rep,packed,name=proximity,proto3" json:"proximity,omitempty"`
}

func (x *DeviceSensorData) Reset() {
	*x = DeviceSensorData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceSensorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceSensorData) ProtoMessage() {}

func (x *DeviceSensorData) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceSensorData.ProtoReflect.Descriptor instead.
func (*DeviceSensorData) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_attempt_proto_rawDescGZIP(), []int{3}
}

func (x *DeviceSensorData) GetAccelerometerData() []*typesv2.ThreeDimensionalCoordinate {
	if x != nil {
		return x.AccelerometerData
	}
	return nil
}

func (x *DeviceSensorData) GetGyroscopeData() []*typesv2.ThreeDimensionalCoordinate {
	if x != nil {
		return x.GyroscopeData
	}
	return nil
}

func (x *DeviceSensorData) GetProximity() []float32 {
	if x != nil {
		return x.Proximity
	}
	return nil
}

type Annotation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// flag if person is live
	LivePerson Certify `protobuf:"varint,1,opt,name=live_person,json=livePerson,proto3,enum=auth.liveness.Certify" json:"live_person,omitempty"`
	// flag if agreement with Karza verdict
	ConcurWithKarza Certify `protobuf:"varint,2,opt,name=concur_with_karza,json=concurWithKarza,proto3,enum=auth.liveness.Certify" json:"concur_with_karza,omitempty"`
	// mark issues with user's recorded video
	VideoIssues []VideoIssue `protobuf:"varint,3,rep,packed,name=video_issues,json=videoIssues,proto3,enum=auth.liveness.VideoIssue" json:"video_issues,omitempty"`
	// record remarks in disagreement with Karza
	Remarks string `protobuf:"bytes,4,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// mark attempt as potential fraud
	PotentialFraud Certify `protobuf:"varint,5,opt,name=potential_fraud,json=potentialFraud,proto3,enum=auth.liveness.Certify" json:"potential_fraud,omitempty"`
	// certify if user's face visible in liveness check attempt
	UserFaceShown Certify `protobuf:"varint,6,opt,name=user_face_shown,json=userFaceShown,proto3,enum=auth.liveness.Certify" json:"user_face_shown,omitempty"`
	// final review for liveness validation
	ReviewVerdict Verdict `protobuf:"varint,7,opt,name=review_verdict,json=reviewVerdict,proto3,enum=auth.liveness.Verdict" json:"review_verdict,omitempty"`
	// annotations are reviewed by
	ReviewedBy string `protobuf:"bytes,8,opt,name=reviewed_by,json=reviewedBy,proto3" json:"reviewed_by,omitempty"`
	// timestamp when annotations were recorded
	ReviewedOn *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=reviewed_on,json=reviewedOn,proto3" json:"reviewed_on,omitempty"`
	// flag if further review is required
	ReviewRequired bool `protobuf:"varint,10,opt,name=review_required,json=reviewRequired,proto3" json:"review_required,omitempty"`
	// flag for retryliveness action
	RetryGiven bool `protobuf:"varint,11,opt,name=retry_given,json=retryGiven,proto3" json:"retry_given,omitempty"`
	// facematch issues
	FaceMatchIssues []FaceMatchIssue `protobuf:"varint,12,rep,packed,name=face_match_issues,json=faceMatchIssues,proto3,enum=auth.liveness.FaceMatchIssue" json:"face_match_issues,omitempty"`
	// re review details
	ReReviewVerdict Verdict `protobuf:"varint,13,opt,name=re_review_verdict,json=reReviewVerdict,proto3,enum=auth.liveness.Verdict" json:"re_review_verdict,omitempty"`
	// annotation is re-reviewed by
	ReReviewedBy string `protobuf:"bytes,14,opt,name=re_reviewed_by,json=reReviewedBy,proto3" json:"re_reviewed_by,omitempty"`
	// timestamp when annotations were re recorded
	ReReviewedOn *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=re_reviewed_on,json=reReviewedOn,proto3" json:"re_reviewed_on,omitempty"`
	// comments by re-reviewer
	ReReviewRemarks string `protobuf:"bytes,16,opt,name=re_review_remarks,json=reReviewRemarks,proto3" json:"re_review_remarks,omitempty"`
	// type of error made by analyst in L1
	//
	// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_attempt.proto.
	ReReviewErrorType          ReReviewErrorType          `protobuf:"varint,17,opt,name=re_review_error_type,json=reReviewErrorType,proto3,enum=auth.liveness.ReReviewErrorType" json:"re_review_error_type,omitempty"`
	ReReviewErrorTypes         []ReReviewErrorType        `protobuf:"varint,18,rep,packed,name=re_review_error_types,json=reReviewErrorTypes,proto3,enum=auth.liveness.ReReviewErrorType" json:"re_review_error_types,omitempty"`
	ReReviewErrorSubcategories []ReReviewErrorSubcategory `protobuf:"varint,19,rep,packed,name=re_review_error_subcategories,json=reReviewErrorSubcategories,proto3,enum=auth.liveness.ReReviewErrorSubcategory" json:"re_review_error_subcategories,omitempty"`
	// case Id of risk case
	CaseId string `protobuf:"bytes,20,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
}

func (x *Annotation) Reset() {
	*x = Annotation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Annotation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Annotation) ProtoMessage() {}

func (x *Annotation) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Annotation.ProtoReflect.Descriptor instead.
func (*Annotation) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_attempt_proto_rawDescGZIP(), []int{4}
}

func (x *Annotation) GetLivePerson() Certify {
	if x != nil {
		return x.LivePerson
	}
	return Certify_CERTIFY_UNSPECIFIED
}

func (x *Annotation) GetConcurWithKarza() Certify {
	if x != nil {
		return x.ConcurWithKarza
	}
	return Certify_CERTIFY_UNSPECIFIED
}

func (x *Annotation) GetVideoIssues() []VideoIssue {
	if x != nil {
		return x.VideoIssues
	}
	return nil
}

func (x *Annotation) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *Annotation) GetPotentialFraud() Certify {
	if x != nil {
		return x.PotentialFraud
	}
	return Certify_CERTIFY_UNSPECIFIED
}

func (x *Annotation) GetUserFaceShown() Certify {
	if x != nil {
		return x.UserFaceShown
	}
	return Certify_CERTIFY_UNSPECIFIED
}

func (x *Annotation) GetReviewVerdict() Verdict {
	if x != nil {
		return x.ReviewVerdict
	}
	return Verdict_VERDICT_UNSPECIFIED
}

func (x *Annotation) GetReviewedBy() string {
	if x != nil {
		return x.ReviewedBy
	}
	return ""
}

func (x *Annotation) GetReviewedOn() *timestamppb.Timestamp {
	if x != nil {
		return x.ReviewedOn
	}
	return nil
}

func (x *Annotation) GetReviewRequired() bool {
	if x != nil {
		return x.ReviewRequired
	}
	return false
}

func (x *Annotation) GetRetryGiven() bool {
	if x != nil {
		return x.RetryGiven
	}
	return false
}

func (x *Annotation) GetFaceMatchIssues() []FaceMatchIssue {
	if x != nil {
		return x.FaceMatchIssues
	}
	return nil
}

func (x *Annotation) GetReReviewVerdict() Verdict {
	if x != nil {
		return x.ReReviewVerdict
	}
	return Verdict_VERDICT_UNSPECIFIED
}

func (x *Annotation) GetReReviewedBy() string {
	if x != nil {
		return x.ReReviewedBy
	}
	return ""
}

func (x *Annotation) GetReReviewedOn() *timestamppb.Timestamp {
	if x != nil {
		return x.ReReviewedOn
	}
	return nil
}

func (x *Annotation) GetReReviewRemarks() string {
	if x != nil {
		return x.ReReviewRemarks
	}
	return ""
}

// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_attempt.proto.
func (x *Annotation) GetReReviewErrorType() ReReviewErrorType {
	if x != nil {
		return x.ReReviewErrorType
	}
	return ReReviewErrorType_RE_REVIEW_ERROR_TYPE_UNSPECIFIED
}

func (x *Annotation) GetReReviewErrorTypes() []ReReviewErrorType {
	if x != nil {
		return x.ReReviewErrorTypes
	}
	return nil
}

func (x *Annotation) GetReReviewErrorSubcategories() []ReReviewErrorSubcategory {
	if x != nil {
		return x.ReReviewErrorSubcategories
	}
	return nil
}

func (x *Annotation) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

var File_api_auth_liveness_internal_liveness_attempt_proto protoreflect.FileDescriptor

var file_api_auth_liveness_internal_liveness_attempt_proto_rawDesc = []byte{
	0x0a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x66,
	0x61, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd1, 0x0a, 0x0a, 0x0f, 0x4c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x12, 0x3a, 0x0a, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x66, 0x72, 0x61, 0x6d,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03,
	0x6f, 0x74, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x21,
	0x0a, 0x0c, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x74, 0x70, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x74,
	0x70, 0x12, 0x2e, 0x0a, 0x13, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x74, 0x70, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x74,
	0x70, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x74, 0x70, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x6f, 0x74, 0x70, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2d, 0x0a, 0x10, 0x62, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x02, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x0f, 0x62, 0x6c, 0x69, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x12, 0x42, 0x0a, 0x1b, 0x62, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x68, 0x65,
	0x75, 0x72, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x02, 0x42, 0x02, 0x18, 0x01, 0x52, 0x19, 0x62,
	0x6c, 0x69, 0x6e, 0x6b, 0x48, 0x65, 0x75, 0x72, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x29, 0x0a, 0x0e, 0x62, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x62, 0x6c, 0x69, 0x6e, 0x6b, 0x4c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x40, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x0c, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x5a, 0x0a, 0x15, 0x62, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x42, 0x6c, 0x69, 0x6e, 0x6b, 0x4c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x13, 0x62, 0x6c, 0x69, 0x6e, 0x6b, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x33, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x17, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x4a, 0x0a, 0x11, 0x69, 0x6e, 0x5f, 0x68, 0x6f, 0x75, 0x73,
	0x65, 0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x2e, 0x49, 0x6e, 0x48, 0x6f, 0x75, 0x73, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x52, 0x0f, 0x69, 0x6e, 0x48, 0x6f, 0x75, 0x73, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x12, 0x39, 0x0a, 0x0a, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x10, 0x73,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6e, 0x65, 0x73, 0x73,
	0x4c, 0x6f, 0x67, 0x69, 0x63, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f, 0x73, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x12, 0x42, 0x0a, 0x0d, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1c, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xc6,
	0x02, 0x0a, 0x0f, 0x49, 0x6e, 0x48, 0x6f, 0x75, 0x73, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x12, 0x3c, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x2e, 0x49, 0x6e, 0x48, 0x6f, 0x75, 0x73, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x49, 0x0a, 0x0c, 0x72, 0x61, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b,
	0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72,
	0x61, 0x77, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x61, 0x77, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x69,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2e, 0x49, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x0d, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12,
	0x36, 0x0a, 0x0a, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x52, 0x09, 0x66, 0x61,
	0x63, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x22, 0xeb, 0x02, 0x0a, 0x08, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x19, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x65, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x2e, 0x0a, 0x13, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x75, 0x73,
	0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x26, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x49, 0x70,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x4d, 0x0a, 0x12, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x10, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4c, 0x0a, 0x14, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x12, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x2f, 0x0a, 0x08, 0x76, 0x70, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x56, 0x50, 0x4e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x76, 0x70,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xd8, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x56, 0x0a, 0x12, 0x61, 0x63,
	0x63, 0x65, 0x6c, 0x65, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x52,
	0x11, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x4e, 0x0a, 0x0e, 0x67, 0x79, 0x72, 0x6f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x69,
	0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e,
	0x61, 0x74, 0x65, 0x52, 0x0d, 0x67, 0x79, 0x72, 0x6f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x78, 0x69, 0x6d, 0x69, 0x74, 0x79, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x02, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x78, 0x69, 0x6d, 0x69, 0x74, 0x79,
	0x22, 0x9d, 0x09, 0x0a, 0x0a, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x37, 0x0a, 0x0b, 0x6c, 0x69, 0x76, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x2e, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x79, 0x52, 0x0a, 0x6c, 0x69,
	0x76, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x63,
	0x75, 0x72, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x6b, 0x61, 0x72, 0x7a, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2e, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x79, 0x52, 0x0f, 0x63, 0x6f, 0x6e,
	0x63, 0x75, 0x72, 0x57, 0x69, 0x74, 0x68, 0x4b, 0x61, 0x72, 0x7a, 0x61, 0x12, 0x3c, 0x0a, 0x0c,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x73, 0x73, 0x75, 0x65, 0x52, 0x0b, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x49, 0x73, 0x73, 0x75, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x73, 0x12, 0x3f, 0x0a, 0x0f, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61,
	0x6c, 0x5f, 0x66, 0x72, 0x61, 0x75, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x43, 0x65,
	0x72, 0x74, 0x69, 0x66, 0x79, 0x52, 0x0e, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c,
	0x46, 0x72, 0x61, 0x75, 0x64, 0x12, 0x3e, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x66, 0x61,
	0x63, 0x65, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x43,
	0x65, 0x72, 0x74, 0x69, 0x66, 0x79, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x46, 0x61, 0x63, 0x65,
	0x53, 0x68, 0x6f, 0x77, 0x6e, 0x12, 0x3d, 0x0a, 0x0e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x56, 0x65,
	0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x0d, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x56, 0x65, 0x72,
	0x64, 0x69, 0x63, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x65, 0x64, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65,
	0x64, 0x5f, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64,
	0x4f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x5f, 0x67, 0x69, 0x76, 0x65, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x72, 0x65, 0x74, 0x72, 0x79, 0x47, 0x69, 0x76, 0x65, 0x6e, 0x12, 0x49, 0x0a, 0x11,
	0x66, 0x61, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65,
	0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x49, 0x73, 0x73, 0x75, 0x65, 0x52, 0x0f, 0x66, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x49, 0x73, 0x73, 0x75, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x11, 0x72, 0x65, 0x5f, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x5f, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x2e, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x0f, 0x72, 0x65, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x72,
	0x65, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x40, 0x0a, 0x0e, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64,
	0x5f, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x72, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65,
	0x64, 0x4f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x72, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12,
	0x55, 0x0a, 0x14, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x52, 0x65,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x11, 0x72, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x15, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x12, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x52, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x72, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x6a, 0x0a, 0x1d, 0x72,
	0x65, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x73,
	0x75, 0x62, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x13, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x27, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x2e, 0x52, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x53, 0x75, 0x62, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x1a, 0x72, 0x65, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x75, 0x62, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x2a, 0xd8, 0x03, 0x0a, 0x18, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x1f, 0x0a,
	0x1b, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50,
	0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0a,
	0x0a, 0x06, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x45,
	0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10,
	0x02, 0x12, 0x12, 0x0a, 0x0e, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10,
	0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x52, 0x41, 0x4d, 0x45,
	0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x4f, 0x54, 0x50, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x10,
	0x07, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43,
	0x4f, 0x52, 0x45, 0x10, 0x08, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x09, 0x12, 0x17, 0x0a, 0x13, 0x47, 0x4f, 0x4f, 0x47, 0x4c,
	0x45, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x0a,
	0x12, 0x14, 0x0a, 0x10, 0x42, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x44,
	0x45, 0x4e, 0x43, 0x45, 0x10, 0x0b, 0x12, 0x1f, 0x0a, 0x1b, 0x42, 0x4c, 0x49, 0x4e, 0x4b, 0x5f,
	0x48, 0x45, 0x55, 0x52, 0x49, 0x53, 0x54, 0x49, 0x43, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49,
	0x44, 0x45, 0x4e, 0x43, 0x45, 0x10, 0x0c, 0x12, 0x12, 0x0a, 0x0e, 0x42, 0x4c, 0x49, 0x4e, 0x4b,
	0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x0d, 0x12, 0x13, 0x0a, 0x0f, 0x56,
	0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x10, 0x0e,
	0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x10, 0x0f, 0x12, 0x19, 0x0a, 0x15, 0x42, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x10, 0x12, 0x0c,
	0x0a, 0x08, 0x4d, 0x45, 0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x10, 0x11, 0x12, 0x14, 0x0a, 0x10,
	0x49, 0x4e, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53,
	0x10, 0x12, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x13, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x49, 0x44,
	0x10, 0x14, 0x12, 0x11, 0x0a, 0x0d, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x10, 0x15, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x43, 0x10, 0x16, 0x2a, 0x5b, 0x0a, 0x07, 0x43,
	0x65, 0x72, 0x74, 0x69, 0x66, 0x79, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x45, 0x52, 0x54, 0x49, 0x46,
	0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x10, 0x0a, 0x0c, 0x43, 0x45, 0x52, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x54, 0x52, 0x55, 0x45, 0x10,
	0x01, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x45, 0x52, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x46, 0x41, 0x4c,
	0x53, 0x45, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x45, 0x52, 0x54, 0x49, 0x46, 0x59, 0x5f,
	0x55, 0x4e, 0x53, 0x55, 0x52, 0x45, 0x10, 0x03, 0x2a, 0x46, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x64,
	0x69, 0x63, 0x74, 0x12, 0x17, 0x0a, 0x13, 0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c,
	0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x10, 0x01, 0x12, 0x10,
	0x0a, 0x0c, 0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x02,
	0x2a, 0xf7, 0x06, 0x0a, 0x0a, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x73, 0x73, 0x75, 0x65, 0x12,
	0x1b, 0x0a, 0x17, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18,
	0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x4c, 0x4f, 0x57, 0x5f,
	0x4c, 0x49, 0x47, 0x48, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x56, 0x49,
	0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x45, 0x58,
	0x50, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x49, 0x44, 0x45,
	0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x55, 0x4e, 0x55, 0x53, 0x55, 0x41, 0x4c, 0x5f,
	0x41, 0x4e, 0x47, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x56, 0x49, 0x44, 0x45, 0x4f,
	0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x4c, 0x4f, 0x57, 0x5f, 0x51, 0x55, 0x41, 0x4c, 0x49,
	0x54, 0x59, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x04, 0x12, 0x1f, 0x0a, 0x1b, 0x56, 0x49,
	0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x4f, 0x42, 0x46, 0x55, 0x53, 0x43,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x10, 0x05, 0x12, 0x1f, 0x0a, 0x1b, 0x56,
	0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x54, 0x45, 0x43, 0x48, 0x4e,
	0x49, 0x43, 0x41, 0x4c, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10, 0x06, 0x12, 0x31, 0x0a, 0x2d,
	0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54,
	0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x55, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x48, 0x4f, 0x54, 0x4f,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x07, 0x12,
	0x31, 0x0a, 0x2d, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x55, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x56, 0x49,
	0x44, 0x45, 0x4f, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53,
	0x10, 0x08, 0x12, 0x2f, 0x0a, 0x2b, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55,
	0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x55, 0x53, 0x49, 0x4e, 0x47,
	0x5f, 0x41, 0x50, 0x50, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53,
	0x53, 0x10, 0x09, 0x12, 0x28, 0x0a, 0x24, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53,
	0x55, 0x45, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4d, 0x49, 0x53,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10, 0x0a, 0x12, 0x1b, 0x0a,
	0x17, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x4f, 0x54, 0x50,
	0x5f, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x45, 0x44, 0x10, 0x0b, 0x12, 0x25, 0x0a, 0x21, 0x56, 0x49,
	0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x47, 0x45, 0x4e, 0x44, 0x45, 0x52,
	0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x4f, 0x10,
	0x0c, 0x12, 0x1e, 0x0a, 0x1a, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45,
	0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c, 0x45, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x53, 0x10,
	0x0d, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45,
	0x5f, 0x42, 0x4c, 0x55, 0x52, 0x52, 0x45, 0x44, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x0e,
	0x12, 0x20, 0x0a, 0x1c, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f,
	0x53, 0x50, 0x4f, 0x4f, 0x46, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54,
	0x10, 0x0f, 0x12, 0x1c, 0x0a, 0x18, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55,
	0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4d, 0x49, 0x53, 0x4c, 0x45, 0x41, 0x44, 0x10, 0x10,
	0x12, 0x22, 0x0a, 0x1e, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f,
	0x55, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x45, 0x52, 0x10, 0x11, 0x12, 0x25, 0x0a, 0x21, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53,
	0x53, 0x55, 0x45, 0x5f, 0x47, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41,
	0x54, 0x43, 0x48, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x12, 0x12, 0x20, 0x0a, 0x1c, 0x56,
	0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f,
	0x43, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10, 0x13, 0x12, 0x1e, 0x0a,
	0x1a, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x46, 0x41, 0x43,
	0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x10, 0x14, 0x12, 0x1c, 0x0a,
	0x18, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x46, 0x41, 0x43,
	0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x46, 0x41, 0x52, 0x10, 0x15, 0x12, 0x26, 0x0a, 0x22, 0x56,
	0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x43, 0x41, 0x4d, 0x45, 0x52,
	0x41, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x53, 0x53, 0x55,
	0x45, 0x10, 0x16, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53,
	0x55, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x17, 0x12, 0x1e, 0x0a, 0x1a, 0x56, 0x49,
	0x44, 0x45, 0x4f, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50,
	0x4c, 0x45, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x4f, 0x10, 0x18, 0x2a, 0xa4, 0x02, 0x0a, 0x0c, 0x49,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x19, 0x49,
	0x4e, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e,
	0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x5f, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x48, 0x4f, 0x55,
	0x53, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x52,
	0x41, 0x4d, 0x45, 0x53, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x49, 0x4e, 0x48, 0x4f, 0x55, 0x53,
	0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49,
	0x4e, 0x47, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e,
	0x49, 0x4e, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x44, 0x4f,
	0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x04,
	0x12, 0x29, 0x0a, 0x25, 0x49, 0x4e, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c, 0x45, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x53,
	0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x49,
	0x4e, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x5f,
	0x46, 0x41, 0x43, 0x45, 0x5f, 0x46, 0x52, 0x41, 0x4d, 0x45, 0x53, 0x10, 0x06, 0x12, 0x22, 0x0a,
	0x1e, 0x49, 0x4e, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x46,
	0x41, 0x43, 0x45, 0x5f, 0x4f, 0x42, 0x46, 0x55, 0x53, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x07, 0x2a, 0xcf, 0x01, 0x0a, 0x08, 0x46, 0x61, 0x63, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x19,
	0x0a, 0x15, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x41, 0x43,
	0x45, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x4e, 0x4f, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x10, 0x01,
	0x12, 0x18, 0x0a, 0x14, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x4d, 0x41,
	0x4e, 0x59, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x53, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x46, 0x41,
	0x43, 0x45, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4c, 0x4f, 0x57,
	0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x10, 0x03, 0x12, 0x1c, 0x0a,
	0x18, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f,
	0x54, 0x4f, 0x4f, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x46,
	0x41, 0x43, 0x45, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x54, 0x4f,
	0x4f, 0x5f, 0x46, 0x41, 0x52, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x41, 0x43, 0x45, 0x5f,
	0x46, 0x4c, 0x41, 0x47, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x10, 0x06, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5a,
	0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68,
	0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_auth_liveness_internal_liveness_attempt_proto_rawDescOnce sync.Once
	file_api_auth_liveness_internal_liveness_attempt_proto_rawDescData = file_api_auth_liveness_internal_liveness_attempt_proto_rawDesc
)

func file_api_auth_liveness_internal_liveness_attempt_proto_rawDescGZIP() []byte {
	file_api_auth_liveness_internal_liveness_attempt_proto_rawDescOnce.Do(func() {
		file_api_auth_liveness_internal_liveness_attempt_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_liveness_internal_liveness_attempt_proto_rawDescData)
	})
	return file_api_auth_liveness_internal_liveness_attempt_proto_rawDescData
}

var file_api_auth_liveness_internal_liveness_attempt_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_auth_liveness_internal_liveness_attempt_proto_goTypes = []interface{}{
	(LivenessAttemptFieldMask)(0),              // 0: auth.liveness.LivenessAttemptFieldMask
	(Certify)(0),                               // 1: auth.liveness.Certify
	(Verdict)(0),                               // 2: auth.liveness.Verdict
	(VideoIssue)(0),                            // 3: auth.liveness.VideoIssue
	(InhouseError)(0),                          // 4: auth.liveness.InhouseError
	(FaceFlag)(0),                              // 5: auth.liveness.FaceFlag
	(*LivenessAttempt)(nil),                    // 6: auth.liveness.LivenessAttempt
	(*InHouseLiveness)(nil),                    // 7: auth.liveness.InHouseLiveness
	(*Metadata)(nil),                           // 8: auth.liveness.Metadata
	(*DeviceSensorData)(nil),                   // 9: auth.liveness.DeviceSensorData
	(*Annotation)(nil),                         // 10: auth.liveness.Annotation
	(vendorgateway.Vendor)(0),                  // 11: vendorgateway.Vendor
	(*common.Image)(nil),                       // 12: api.typesv2.common.Image
	(LivenessStatus)(0),                        // 13: auth.liveness.LivenessStatus
	(*timestamppb.Timestamp)(nil),              // 14: google.protobuf.Timestamp
	(LivenessFlow)(0),                          // 15: auth.liveness.LivenessFlow
	(BlinkLivenessStatus)(0),                   // 16: auth.liveness.BlinkLivenessStatus
	(StrictnessLogic)(0),                       // 17: auth.liveness.StrictnessLogic
	(InHouseLivenessStatus)(0),                 // 18: auth.liveness.InHouseLivenessStatus
	(*inhouse.CheckLivenessResponse)(nil),      // 19: vendors.inhouse.CheckLivenessResponse
	(*typesv2.VPNInfo)(nil),                    // 20: api.typesv2.VPNInfo
	(*typesv2.ThreeDimensionalCoordinate)(nil), // 21: api.typesv2.ThreeDimensionalCoordinate
	(FaceMatchIssue)(0),                        // 22: auth.liveness.FaceMatchIssue
	(ReReviewErrorType)(0),                     // 23: auth.liveness.ReReviewErrorType
	(ReReviewErrorSubcategory)(0),              // 24: auth.liveness.ReReviewErrorSubcategory
}
var file_api_auth_liveness_internal_liveness_attempt_proto_depIdxs = []int32{
	11, // 0: auth.liveness.LivenessAttempt.vendor:type_name -> vendorgateway.Vendor
	12, // 1: auth.liveness.LivenessAttempt.image_frame:type_name -> api.typesv2.common.Image
	13, // 2: auth.liveness.LivenessAttempt.status:type_name -> auth.liveness.LivenessStatus
	14, // 3: auth.liveness.LivenessAttempt.created_at:type_name -> google.protobuf.Timestamp
	15, // 4: auth.liveness.LivenessAttempt.liveness_flow:type_name -> auth.liveness.LivenessFlow
	16, // 5: auth.liveness.LivenessAttempt.blink_liveness_status:type_name -> auth.liveness.BlinkLivenessStatus
	14, // 6: auth.liveness.LivenessAttempt.updated_at:type_name -> google.protobuf.Timestamp
	8,  // 7: auth.liveness.LivenessAttempt.metadata:type_name -> auth.liveness.Metadata
	7,  // 8: auth.liveness.LivenessAttempt.in_house_liveness:type_name -> auth.liveness.InHouseLiveness
	10, // 9: auth.liveness.LivenessAttempt.annotation:type_name -> auth.liveness.Annotation
	17, // 10: auth.liveness.LivenessAttempt.strictness_logic:type_name -> auth.liveness.StrictnessLogic
	13, // 11: auth.liveness.LivenessAttempt.vendor_status:type_name -> auth.liveness.LivenessStatus
	18, // 12: auth.liveness.InHouseLiveness.status:type_name -> auth.liveness.InHouseLivenessStatus
	19, // 13: auth.liveness.InHouseLiveness.raw_response:type_name -> vendors.inhouse.CheckLivenessResponse
	4,  // 14: auth.liveness.InHouseLiveness.inhouse_errors:type_name -> auth.liveness.InhouseError
	5,  // 15: auth.liveness.InHouseLiveness.face_flags:type_name -> auth.liveness.FaceFlag
	9,  // 16: auth.liveness.Metadata.device_sensor_data:type_name -> auth.liveness.DeviceSensorData
	14, // 17: auth.liveness.Metadata.streaming_started_at:type_name -> google.protobuf.Timestamp
	20, // 18: auth.liveness.Metadata.vpn_info:type_name -> api.typesv2.VPNInfo
	21, // 19: auth.liveness.DeviceSensorData.accelerometer_data:type_name -> api.typesv2.ThreeDimensionalCoordinate
	21, // 20: auth.liveness.DeviceSensorData.gyroscope_data:type_name -> api.typesv2.ThreeDimensionalCoordinate
	1,  // 21: auth.liveness.Annotation.live_person:type_name -> auth.liveness.Certify
	1,  // 22: auth.liveness.Annotation.concur_with_karza:type_name -> auth.liveness.Certify
	3,  // 23: auth.liveness.Annotation.video_issues:type_name -> auth.liveness.VideoIssue
	1,  // 24: auth.liveness.Annotation.potential_fraud:type_name -> auth.liveness.Certify
	1,  // 25: auth.liveness.Annotation.user_face_shown:type_name -> auth.liveness.Certify
	2,  // 26: auth.liveness.Annotation.review_verdict:type_name -> auth.liveness.Verdict
	14, // 27: auth.liveness.Annotation.reviewed_on:type_name -> google.protobuf.Timestamp
	22, // 28: auth.liveness.Annotation.face_match_issues:type_name -> auth.liveness.FaceMatchIssue
	2,  // 29: auth.liveness.Annotation.re_review_verdict:type_name -> auth.liveness.Verdict
	14, // 30: auth.liveness.Annotation.re_reviewed_on:type_name -> google.protobuf.Timestamp
	23, // 31: auth.liveness.Annotation.re_review_error_type:type_name -> auth.liveness.ReReviewErrorType
	23, // 32: auth.liveness.Annotation.re_review_error_types:type_name -> auth.liveness.ReReviewErrorType
	24, // 33: auth.liveness.Annotation.re_review_error_subcategories:type_name -> auth.liveness.ReReviewErrorSubcategory
	34, // [34:34] is the sub-list for method output_type
	34, // [34:34] is the sub-list for method input_type
	34, // [34:34] is the sub-list for extension type_name
	34, // [34:34] is the sub-list for extension extendee
	0,  // [0:34] is the sub-list for field type_name
}

func init() { file_api_auth_liveness_internal_liveness_attempt_proto_init() }
func file_api_auth_liveness_internal_liveness_attempt_proto_init() {
	if File_api_auth_liveness_internal_liveness_attempt_proto != nil {
		return
	}
	file_api_auth_liveness_internal_face_match_annotation_proto_init()
	file_api_auth_liveness_types_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InHouseLiveness); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Metadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceSensorData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Annotation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_liveness_internal_liveness_attempt_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_liveness_internal_liveness_attempt_proto_goTypes,
		DependencyIndexes: file_api_auth_liveness_internal_liveness_attempt_proto_depIdxs,
		EnumInfos:         file_api_auth_liveness_internal_liveness_attempt_proto_enumTypes,
		MessageInfos:      file_api_auth_liveness_internal_liveness_attempt_proto_msgTypes,
	}.Build()
	File_api_auth_liveness_internal_liveness_attempt_proto = out.File
	file_api_auth_liveness_internal_liveness_attempt_proto_rawDesc = nil
	file_api_auth_liveness_internal_liveness_attempt_proto_goTypes = nil
	file_api_auth_liveness_internal_liveness_attempt_proto_depIdxs = nil
}
