// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/liveness/internal/liveness_summary.proto

package liveness

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LivenessSummaryFieldMask int32

const (
	LivenessSummaryFieldMask_LIVENESS_SUMMARY_FIELD_NONE    LivenessSummaryFieldMask = 0
	LivenessSummaryFieldMask_ATTEMPT_COUNT                  LivenessSummaryFieldMask = 1
	LivenessSummaryFieldMask_SUMMARY_STATUS                 LivenessSummaryFieldMask = 2
	LivenessSummaryFieldMask_LIVENESS_ATTEMPT_ID            LivenessSummaryFieldMask = 3
	LivenessSummaryFieldMask_FACEMATCH_ATTEMPT_ID           LivenessSummaryFieldMask = 4
	LivenessSummaryFieldMask_FACEMATCH_IMAGE                LivenessSummaryFieldMask = 5
	LivenessSummaryFieldMask_SUMMARY_LIVENESS_STATUS        LivenessSummaryFieldMask = 6
	LivenessSummaryFieldMask_SUMMARY_FACEMATCH_STATUS       LivenessSummaryFieldMask = 7
	LivenessSummaryFieldMask_STRICTNESS_LOGIC_FIELD_MASK    LivenessSummaryFieldMask = 8
	LivenessSummaryFieldMask_FORCE_MANUAL_REVIEW_FIELD_MASK LivenessSummaryFieldMask = 9
	LivenessSummaryFieldMask_MAX_ATTEMPTS                   LivenessSummaryFieldMask = 10
	LivenessSummaryFieldMask_EXPIRE_AT                      LivenessSummaryFieldMask = 11
)

// Enum value maps for LivenessSummaryFieldMask.
var (
	LivenessSummaryFieldMask_name = map[int32]string{
		0:  "LIVENESS_SUMMARY_FIELD_NONE",
		1:  "ATTEMPT_COUNT",
		2:  "SUMMARY_STATUS",
		3:  "LIVENESS_ATTEMPT_ID",
		4:  "FACEMATCH_ATTEMPT_ID",
		5:  "FACEMATCH_IMAGE",
		6:  "SUMMARY_LIVENESS_STATUS",
		7:  "SUMMARY_FACEMATCH_STATUS",
		8:  "STRICTNESS_LOGIC_FIELD_MASK",
		9:  "FORCE_MANUAL_REVIEW_FIELD_MASK",
		10: "MAX_ATTEMPTS",
		11: "EXPIRE_AT",
	}
	LivenessSummaryFieldMask_value = map[string]int32{
		"LIVENESS_SUMMARY_FIELD_NONE":    0,
		"ATTEMPT_COUNT":                  1,
		"SUMMARY_STATUS":                 2,
		"LIVENESS_ATTEMPT_ID":            3,
		"FACEMATCH_ATTEMPT_ID":           4,
		"FACEMATCH_IMAGE":                5,
		"SUMMARY_LIVENESS_STATUS":        6,
		"SUMMARY_FACEMATCH_STATUS":       7,
		"STRICTNESS_LOGIC_FIELD_MASK":    8,
		"FORCE_MANUAL_REVIEW_FIELD_MASK": 9,
		"MAX_ATTEMPTS":                   10,
		"EXPIRE_AT":                      11,
	}
)

func (x LivenessSummaryFieldMask) Enum() *LivenessSummaryFieldMask {
	p := new(LivenessSummaryFieldMask)
	*p = x
	return p
}

func (x LivenessSummaryFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LivenessSummaryFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_internal_liveness_summary_proto_enumTypes[0].Descriptor()
}

func (LivenessSummaryFieldMask) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_internal_liveness_summary_proto_enumTypes[0]
}

func (x LivenessSummaryFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LivenessSummaryFieldMask.Descriptor instead.
func (LivenessSummaryFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_summary_proto_rawDescGZIP(), []int{0}
}

type LivenessSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId            string       `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RequestId          string       `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	LivenessFlow       LivenessFlow `protobuf:"varint,4,opt,name=liveness_flow,json=livenessFlow,proto3,enum=auth.liveness.LivenessFlow" json:"liveness_flow,omitempty"`
	LivenessAttemptId  string       `protobuf:"bytes,5,opt,name=liveness_attempt_id,json=livenessAttemptId,proto3" json:"liveness_attempt_id,omitempty"`
	FacematchAttemptId string       `protobuf:"bytes,6,opt,name=facematch_attempt_id,json=facematchAttemptId,proto3" json:"facematch_attempt_id,omitempty"`
	// The image to be used for facematch
	FacematchInfo *FacematchInfo `protobuf:"bytes,7,opt,name=facematch_info,json=facematchInfo,proto3" json:"facematch_info,omitempty"`
	// Maximum number of retries to be attempted
	MaxAttempts int32 `protobuf:"varint,8,opt,name=max_attempts,json=maxAttempts,proto3" json:"max_attempts,omitempty"`
	// Numbers of attempts completed
	AttemptsCount          int32                  `protobuf:"varint,9,opt,name=attempts_count,json=attemptsCount,proto3" json:"attempts_count,omitempty"`
	Status                 SummaryStatus          `protobuf:"varint,10,opt,name=status,proto3,enum=auth.liveness.SummaryStatus" json:"status,omitempty"`
	CreatedAt              *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt              *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	SummaryLivenessStatus  SummaryLivenessStatus  `protobuf:"varint,13,opt,name=summary_liveness_status,json=summaryLivenessStatus,proto3,enum=auth.liveness.SummaryLivenessStatus" json:"summary_liveness_status,omitempty"`
	SummaryFacematchStatus SummaryFacematchStatus `protobuf:"varint,14,opt,name=summary_facematch_status,json=summaryFacematchStatus,proto3,enum=auth.liveness.SummaryFacematchStatus" json:"summary_facematch_status,omitempty"`
	// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_summary.proto.
	StrictnessLogic StrictnessLogic `protobuf:"varint,15,opt,name=strictness_logic,json=strictnessLogic,proto3,enum=auth.liveness.StrictnessLogic" json:"strictness_logic,omitempty"`
	// Flag to force manual review of liveness for a user
	ForceManualReview   common.BooleanEnum                   `protobuf:"varint,16,opt,name=force_manual_review,json=forceManualReview,proto3,enum=api.typesv2.common.BooleanEnum" json:"force_manual_review,omitempty"`
	ExpireAt            *timestamppb.Timestamp               `protobuf:"bytes,17,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	ClientRequestParams *LivenessSummary_ClientRequestParams `protobuf:"bytes,18,opt,name=client_request_params,json=clientRequestParams,proto3" json:"client_request_params,omitempty"`
}

func (x *LivenessSummary) Reset() {
	*x = LivenessSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessSummary) ProtoMessage() {}

func (x *LivenessSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessSummary.ProtoReflect.Descriptor instead.
func (*LivenessSummary) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_summary_proto_rawDescGZIP(), []int{0}
}

func (x *LivenessSummary) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LivenessSummary) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *LivenessSummary) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *LivenessSummary) GetLivenessFlow() LivenessFlow {
	if x != nil {
		return x.LivenessFlow
	}
	return LivenessFlow_LIVENESS_FLOW_UNSPECIFIED
}

func (x *LivenessSummary) GetLivenessAttemptId() string {
	if x != nil {
		return x.LivenessAttemptId
	}
	return ""
}

func (x *LivenessSummary) GetFacematchAttemptId() string {
	if x != nil {
		return x.FacematchAttemptId
	}
	return ""
}

func (x *LivenessSummary) GetFacematchInfo() *FacematchInfo {
	if x != nil {
		return x.FacematchInfo
	}
	return nil
}

func (x *LivenessSummary) GetMaxAttempts() int32 {
	if x != nil {
		return x.MaxAttempts
	}
	return 0
}

func (x *LivenessSummary) GetAttemptsCount() int32 {
	if x != nil {
		return x.AttemptsCount
	}
	return 0
}

func (x *LivenessSummary) GetStatus() SummaryStatus {
	if x != nil {
		return x.Status
	}
	return SummaryStatus_SUMMARY_UNSPECIFIED
}

func (x *LivenessSummary) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LivenessSummary) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *LivenessSummary) GetSummaryLivenessStatus() SummaryLivenessStatus {
	if x != nil {
		return x.SummaryLivenessStatus
	}
	return SummaryLivenessStatus_SUMMARY_LIVENESS_UNSPECIFIED
}

func (x *LivenessSummary) GetSummaryFacematchStatus() SummaryFacematchStatus {
	if x != nil {
		return x.SummaryFacematchStatus
	}
	return SummaryFacematchStatus_SUMMARY_FACEMATCH_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_summary.proto.
func (x *LivenessSummary) GetStrictnessLogic() StrictnessLogic {
	if x != nil {
		return x.StrictnessLogic
	}
	return StrictnessLogic_STRICTNESS_LOGIC_UNSPECIFIED
}

func (x *LivenessSummary) GetForceManualReview() common.BooleanEnum {
	if x != nil {
		return x.ForceManualReview
	}
	return common.BooleanEnum(0)
}

func (x *LivenessSummary) GetExpireAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireAt
	}
	return nil
}

func (x *LivenessSummary) GetClientRequestParams() *LivenessSummary_ClientRequestParams {
	if x != nil {
		return x.ClientRequestParams
	}
	return nil
}

// custom intro screen options for the CHECK_LIVENESS screen, caller services can send this in case they need to show different content on the intro screen
// https://github.com/epiFi/protos/blob/master/api/frontend/deeplink/deeplink.proto#L5290
type CustomIntroScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image     *common.VisualElement    `protobuf:"bytes,8,opt,name=image,proto3" json:"image,omitempty"`
	Title     *common.Text             `protobuf:"bytes,9,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle  *common.Text             `protobuf:"bytes,10,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	ListItems []*common.Text           `protobuf:"bytes,11,rep,name=list_items,json=listItems,proto3" json:"list_items,omitempty"`
	BgColor   *widget.BackgroundColour `protobuf:"bytes,12,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *CustomIntroScreenOptions) Reset() {
	*x = CustomIntroScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomIntroScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomIntroScreenOptions) ProtoMessage() {}

func (x *CustomIntroScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomIntroScreenOptions.ProtoReflect.Descriptor instead.
func (*CustomIntroScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_summary_proto_rawDescGZIP(), []int{1}
}

func (x *CustomIntroScreenOptions) GetImage() *common.VisualElement {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *CustomIntroScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *CustomIntroScreenOptions) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *CustomIntroScreenOptions) GetListItems() []*common.Text {
	if x != nil {
		return x.ListItems
	}
	return nil
}

func (x *CustomIntroScreenOptions) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

type NextActionHooks struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// next_action_post_video_upload to be returned post liveness video upload
	NextActionPostVideoUpload *deeplink.Deeplink `protobuf:"bytes,1,opt,name=next_action_post_video_upload,json=nextActionPostVideoUpload,proto3" json:"next_action_post_video_upload,omitempty"`
	NextActionPostSuccess     *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action_post_success,json=nextActionPostSuccess,proto3" json:"next_action_post_success,omitempty"`
	// Deeplink to be sent when all the retries are exhausted for the user
	NextActionPostFailure *deeplink.Deeplink `protobuf:"bytes,3,opt,name=next_action_post_failure,json=nextActionPostFailure,proto3" json:"next_action_post_failure,omitempty"`
	// Deeplink to be sent when the summary has expired for a user
	NextActionPostExpiry *deeplink.Deeplink `protobuf:"bytes,4,opt,name=next_action_post_expiry,json=nextActionPostExpiry,proto3" json:"next_action_post_expiry,omitempty"`
	// This default next action will be returned in case caller services are not sending any other next action hooks
	// This will be a mandatory parameter while creating liveness summary
	DefaultNextAction *deeplink.Deeplink `protobuf:"bytes,5,opt,name=default_next_action,json=defaultNextAction,proto3" json:"default_next_action,omitempty"`
}

func (x *NextActionHooks) Reset() {
	*x = NextActionHooks{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NextActionHooks) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NextActionHooks) ProtoMessage() {}

func (x *NextActionHooks) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NextActionHooks.ProtoReflect.Descriptor instead.
func (*NextActionHooks) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_summary_proto_rawDescGZIP(), []int{2}
}

func (x *NextActionHooks) GetNextActionPostVideoUpload() *deeplink.Deeplink {
	if x != nil {
		return x.NextActionPostVideoUpload
	}
	return nil
}

func (x *NextActionHooks) GetNextActionPostSuccess() *deeplink.Deeplink {
	if x != nil {
		return x.NextActionPostSuccess
	}
	return nil
}

func (x *NextActionHooks) GetNextActionPostFailure() *deeplink.Deeplink {
	if x != nil {
		return x.NextActionPostFailure
	}
	return nil
}

func (x *NextActionHooks) GetNextActionPostExpiry() *deeplink.Deeplink {
	if x != nil {
		return x.NextActionPostExpiry
	}
	return nil
}

func (x *NextActionHooks) GetDefaultNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.DefaultNextAction
	}
	return nil
}

type FacematchInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FacematchPhotos []*common.Image `protobuf:"bytes,1,rep,name=facematch_photos,json=facematchPhotos,proto3" json:"facematch_photos,omitempty"`
}

func (x *FacematchInfo) Reset() {
	*x = FacematchInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FacematchInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FacematchInfo) ProtoMessage() {}

func (x *FacematchInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FacematchInfo.ProtoReflect.Descriptor instead.
func (*FacematchInfo) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_summary_proto_rawDescGZIP(), []int{3}
}

func (x *FacematchInfo) GetFacematchPhotos() []*common.Image {
	if x != nil {
		return x.FacematchPhotos
	}
	return nil
}

type LivenessSummary_ClientRequestParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// next_action_post_video_upload to be returned post liveness video upload
	// Deprecated in favour of NextActionHooks -> next_action_post_video_upload.
	// TODO(ankit): Remove once all clients are migrated
	//
	// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_summary.proto.
	NextActionPostVideoUpload *deeplink.Deeplink        `protobuf:"bytes,1,opt,name=next_action_post_video_upload,json=nextActionPostVideoUpload,proto3" json:"next_action_post_video_upload,omitempty"`
	NextActionHooks           *NextActionHooks          `protobuf:"bytes,2,opt,name=next_action_hooks,json=nextActionHooks,proto3" json:"next_action_hooks,omitempty"`
	CustomIntroScreenOptions  *CustomIntroScreenOptions `protobuf:"bytes,3,opt,name=custom_intro_screen_options,json=customIntroScreenOptions,proto3" json:"custom_intro_screen_options,omitempty"`
}

func (x *LivenessSummary_ClientRequestParams) Reset() {
	*x = LivenessSummary_ClientRequestParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessSummary_ClientRequestParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessSummary_ClientRequestParams) ProtoMessage() {}

func (x *LivenessSummary_ClientRequestParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessSummary_ClientRequestParams.ProtoReflect.Descriptor instead.
func (*LivenessSummary_ClientRequestParams) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_summary_proto_rawDescGZIP(), []int{0, 0}
}

// Deprecated: Marked as deprecated in api/auth/liveness/internal/liveness_summary.proto.
func (x *LivenessSummary_ClientRequestParams) GetNextActionPostVideoUpload() *deeplink.Deeplink {
	if x != nil {
		return x.NextActionPostVideoUpload
	}
	return nil
}

func (x *LivenessSummary_ClientRequestParams) GetNextActionHooks() *NextActionHooks {
	if x != nil {
		return x.NextActionHooks
	}
	return nil
}

func (x *LivenessSummary_ClientRequestParams) GetCustomIntroScreenOptions() *CustomIntroScreenOptions {
	if x != nil {
		return x.CustomIntroScreenOptions
	}
	return nil
}

var File_api_auth_liveness_internal_liveness_summary_proto protoreflect.FileDescriptor

var file_api_auth_liveness_internal_liveness_summary_proto_rawDesc = []byte{
	0x0a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c,
	0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65,
	0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68,
	0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe9, 0x0a, 0x0a, 0x0f, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x40, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x66,
	0x6c, 0x6f, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x0c, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x46, 0x6c, 0x6f, 0x77, 0x12, 0x2e, 0x0a, 0x13, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0e, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x46,
	0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x66, 0x61,
	0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x6d,
	0x61, 0x78, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x5c, 0x0a, 0x17, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x15, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x5f, 0x0a, 0x18, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x16, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x4d, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6c,
	0x6f, 0x67, 0x69, 0x63, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f,
	0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x12,
	0x4f, 0x0a, 0x13, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x11, 0x66,
	0x6f, 0x72, 0x63, 0x65, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x12, 0x37, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x41, 0x74, 0x12, 0x66, 0x0a, 0x15, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x13, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0xac, 0x02, 0x0a, 0x13, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x61, 0x0a, 0x1d, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x19, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x73,
	0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x4a, 0x0a, 0x11,
	0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x6f, 0x6f, 0x6b,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x6f, 0x6f, 0x6b, 0x73, 0x52, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x6f, 0x6f, 0x6b, 0x73, 0x12, 0x66, 0x0a, 0x1b, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x18, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6e,
	0x74, 0x72, 0x6f, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0xbd, 0x02, 0x0a, 0x18, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6e, 0x74, 0x72, 0x6f,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x37, 0x0a,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x37, 0x0a, 0x0a,
	0x6c, 0x69, 0x73, 0x74, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x09, 0x6c, 0x69, 0x73, 0x74,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x22, 0xbd, 0x03, 0x0a, 0x0f, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x6f, 0x6f, 0x6b, 0x73, 0x12, 0x5d, 0x0a, 0x1d, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x19, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x73, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x12, 0x54, 0x0a, 0x18, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x52, 0x15, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f,
	0x73, 0x74, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x54, 0x0a, 0x18, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x15, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x73, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12,
	0x52, 0x0a, 0x17, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70,
	0x6f, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x14, 0x6e,
	0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x73, 0x74, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x79, 0x12, 0x4b, 0x0a, 0x13, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6e,
	0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x11, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x55, 0x0a, 0x0d, 0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x44, 0x0a, 0x10, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x70,
	0x68, 0x6f, 0x74, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x0f, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x73, 0x2a, 0xcb, 0x02, 0x0a, 0x18, 0x4c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4d, 0x61, 0x73, 0x6b, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4e,
	0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54,
	0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x55, 0x4d, 0x4d,
	0x41, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13,
	0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54,
	0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x41, 0x43, 0x45, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12,
	0x13, 0x0a, 0x0f, 0x46, 0x41, 0x43, 0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x4d, 0x41,
	0x47, 0x45, 0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f,
	0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10,
	0x06, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x46, 0x41, 0x43,
	0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x07, 0x12,
	0x1f, 0x0a, 0x1b, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4c, 0x4f,
	0x47, 0x49, 0x43, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x10, 0x08,
	0x12, 0x22, 0x0a, 0x1e, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c,
	0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x10, 0x09, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x41, 0x58, 0x5f, 0x41, 0x54, 0x54, 0x45,
	0x4d, 0x50, 0x54, 0x53, 0x10, 0x0a, 0x12, 0x0d, 0x0a, 0x09, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45,
	0x5f, 0x41, 0x54, 0x10, 0x0b, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75,
	0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_auth_liveness_internal_liveness_summary_proto_rawDescOnce sync.Once
	file_api_auth_liveness_internal_liveness_summary_proto_rawDescData = file_api_auth_liveness_internal_liveness_summary_proto_rawDesc
)

func file_api_auth_liveness_internal_liveness_summary_proto_rawDescGZIP() []byte {
	file_api_auth_liveness_internal_liveness_summary_proto_rawDescOnce.Do(func() {
		file_api_auth_liveness_internal_liveness_summary_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_liveness_internal_liveness_summary_proto_rawDescData)
	})
	return file_api_auth_liveness_internal_liveness_summary_proto_rawDescData
}

var file_api_auth_liveness_internal_liveness_summary_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_liveness_internal_liveness_summary_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_auth_liveness_internal_liveness_summary_proto_goTypes = []interface{}{
	(LivenessSummaryFieldMask)(0),               // 0: auth.liveness.LivenessSummaryFieldMask
	(*LivenessSummary)(nil),                     // 1: auth.liveness.LivenessSummary
	(*CustomIntroScreenOptions)(nil),            // 2: auth.liveness.CustomIntroScreenOptions
	(*NextActionHooks)(nil),                     // 3: auth.liveness.NextActionHooks
	(*FacematchInfo)(nil),                       // 4: auth.liveness.FacematchInfo
	(*LivenessSummary_ClientRequestParams)(nil), // 5: auth.liveness.LivenessSummary.ClientRequestParams
	(LivenessFlow)(0),                           // 6: auth.liveness.LivenessFlow
	(SummaryStatus)(0),                          // 7: auth.liveness.SummaryStatus
	(*timestamppb.Timestamp)(nil),               // 8: google.protobuf.Timestamp
	(SummaryLivenessStatus)(0),                  // 9: auth.liveness.SummaryLivenessStatus
	(SummaryFacematchStatus)(0),                 // 10: auth.liveness.SummaryFacematchStatus
	(StrictnessLogic)(0),                        // 11: auth.liveness.StrictnessLogic
	(common.BooleanEnum)(0),                     // 12: api.typesv2.common.BooleanEnum
	(*common.VisualElement)(nil),                // 13: api.typesv2.common.VisualElement
	(*common.Text)(nil),                         // 14: api.typesv2.common.Text
	(*widget.BackgroundColour)(nil),             // 15: api.typesv2.common.ui.widget.BackgroundColour
	(*deeplink.Deeplink)(nil),                   // 16: frontend.deeplink.Deeplink
	(*common.Image)(nil),                        // 17: api.typesv2.common.Image
}
var file_api_auth_liveness_internal_liveness_summary_proto_depIdxs = []int32{
	6,  // 0: auth.liveness.LivenessSummary.liveness_flow:type_name -> auth.liveness.LivenessFlow
	4,  // 1: auth.liveness.LivenessSummary.facematch_info:type_name -> auth.liveness.FacematchInfo
	7,  // 2: auth.liveness.LivenessSummary.status:type_name -> auth.liveness.SummaryStatus
	8,  // 3: auth.liveness.LivenessSummary.created_at:type_name -> google.protobuf.Timestamp
	8,  // 4: auth.liveness.LivenessSummary.updated_at:type_name -> google.protobuf.Timestamp
	9,  // 5: auth.liveness.LivenessSummary.summary_liveness_status:type_name -> auth.liveness.SummaryLivenessStatus
	10, // 6: auth.liveness.LivenessSummary.summary_facematch_status:type_name -> auth.liveness.SummaryFacematchStatus
	11, // 7: auth.liveness.LivenessSummary.strictness_logic:type_name -> auth.liveness.StrictnessLogic
	12, // 8: auth.liveness.LivenessSummary.force_manual_review:type_name -> api.typesv2.common.BooleanEnum
	8,  // 9: auth.liveness.LivenessSummary.expire_at:type_name -> google.protobuf.Timestamp
	5,  // 10: auth.liveness.LivenessSummary.client_request_params:type_name -> auth.liveness.LivenessSummary.ClientRequestParams
	13, // 11: auth.liveness.CustomIntroScreenOptions.image:type_name -> api.typesv2.common.VisualElement
	14, // 12: auth.liveness.CustomIntroScreenOptions.title:type_name -> api.typesv2.common.Text
	14, // 13: auth.liveness.CustomIntroScreenOptions.subtitle:type_name -> api.typesv2.common.Text
	14, // 14: auth.liveness.CustomIntroScreenOptions.list_items:type_name -> api.typesv2.common.Text
	15, // 15: auth.liveness.CustomIntroScreenOptions.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	16, // 16: auth.liveness.NextActionHooks.next_action_post_video_upload:type_name -> frontend.deeplink.Deeplink
	16, // 17: auth.liveness.NextActionHooks.next_action_post_success:type_name -> frontend.deeplink.Deeplink
	16, // 18: auth.liveness.NextActionHooks.next_action_post_failure:type_name -> frontend.deeplink.Deeplink
	16, // 19: auth.liveness.NextActionHooks.next_action_post_expiry:type_name -> frontend.deeplink.Deeplink
	16, // 20: auth.liveness.NextActionHooks.default_next_action:type_name -> frontend.deeplink.Deeplink
	17, // 21: auth.liveness.FacematchInfo.facematch_photos:type_name -> api.typesv2.common.Image
	16, // 22: auth.liveness.LivenessSummary.ClientRequestParams.next_action_post_video_upload:type_name -> frontend.deeplink.Deeplink
	3,  // 23: auth.liveness.LivenessSummary.ClientRequestParams.next_action_hooks:type_name -> auth.liveness.NextActionHooks
	2,  // 24: auth.liveness.LivenessSummary.ClientRequestParams.custom_intro_screen_options:type_name -> auth.liveness.CustomIntroScreenOptions
	25, // [25:25] is the sub-list for method output_type
	25, // [25:25] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_api_auth_liveness_internal_liveness_summary_proto_init() }
func file_api_auth_liveness_internal_liveness_summary_proto_init() {
	if File_api_auth_liveness_internal_liveness_summary_proto != nil {
		return
	}
	file_api_auth_liveness_types_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomIntroScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NextActionHooks); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FacematchInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_internal_liveness_summary_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessSummary_ClientRequestParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_liveness_internal_liveness_summary_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_liveness_internal_liveness_summary_proto_goTypes,
		DependencyIndexes: file_api_auth_liveness_internal_liveness_summary_proto_depIdxs,
		EnumInfos:         file_api_auth_liveness_internal_liveness_summary_proto_enumTypes,
		MessageInfos:      file_api_auth_liveness_internal_liveness_summary_proto_msgTypes,
	}.Build()
	File_api_auth_liveness_internal_liveness_summary_proto = out.File
	file_api_auth_liveness_internal_liveness_summary_proto_rawDesc = nil
	file_api_auth_liveness_internal_liveness_summary_proto_goTypes = nil
	file_api_auth_liveness_internal_liveness_summary_proto_depIdxs = nil
}
