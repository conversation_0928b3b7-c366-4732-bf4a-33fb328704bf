package liveness

import (
	"database/sql/driver"
	"fmt"

	"github.com/epifi/be-common/pkg/logger"
)

// Valuer interface implementation for storing the data in string format in DB
func (p LivenessFlow) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (p *LivenessFlow) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LivenessFlow_value[val]
	if !ok {
		logger.ErrorNoCtx(fmt.Sprintf("encountered unknown liveness flow : %v", val))
	}
	*p = LivenessFlow(valInt)
	return nil
}
