// protolint:disable MAX_LINE_LENGTH

//
//Defines a service for liveness and face match-related data and operations.
//This service provides the following functionality:
// Verify Face Match
// Verify Liveness
//It has both sync and async form of requests.
//Sync waits for the full process to complete and return the final result.
//For Async user has to poll and get the status to check the result.
//There are no retries in this service and user of this service should handle the retry according
//to their use case.

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/auth/liveness/service.proto

package liveness

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Liveness_GetLivenessStatus_FullMethodName            = "/auth.liveness.Liveness/GetLivenessStatus"
	Liveness_FaceMatch_FullMethodName                    = "/auth.liveness.Liveness/FaceMatch"
	Liveness_GetFaceMatchStatus_FullMethodName           = "/auth.liveness.Liveness/GetFaceMatchStatus"
	Liveness_CheckLivenessWithVideoStream_FullMethodName = "/auth.liveness.Liveness/CheckLivenessWithVideoStream"
	Liveness_GenerateOTP_FullMethodName                  = "/auth.liveness.Liveness/GenerateOTP"
	Liveness_GetS3Image_FullMethodName                   = "/auth.liveness.Liveness/GetS3Image"
	Liveness_GetLivenessAttempts_FullMethodName          = "/auth.liveness.Liveness/GetLivenessAttempts"
	Liveness_AnnotateLivenessAttempt_FullMethodName      = "/auth.liveness.Liveness/AnnotateLivenessAttempt"
	Liveness_CreateLivenessSummary_FullMethodName        = "/auth.liveness.Liveness/CreateLivenessSummary"
	Liveness_GetLivenessSummary_FullMethodName           = "/auth.liveness.Liveness/GetLivenessSummary"
	Liveness_GetLivenessSummaryStatus_FullMethodName     = "/auth.liveness.Liveness/GetLivenessSummaryStatus"
	Liveness_CreateLivenessAttempt_FullMethodName        = "/auth.liveness.Liveness/CreateLivenessAttempt"
	Liveness_AnnotateFacematchAttempt_FullMethodName     = "/auth.liveness.Liveness/AnnotateFacematchAttempt"
	Liveness_RedoFaceMatch_FullMethodName                = "/auth.liveness.Liveness/RedoFaceMatch"
	Liveness_GetFaceMatchAttempts_FullMethodName         = "/auth.liveness.Liveness/GetFaceMatchAttempts"
	Liveness_RetryLiveness_FullMethodName                = "/auth.liveness.Liveness/RetryLiveness"
	Liveness_GetLivenessAttempt_FullMethodName           = "/auth.liveness.Liveness/GetLivenessAttempt"
)

// LivenessClient is the client API for Liveness service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LivenessClient interface {
	// GetLivenessStatus Gets the liveness status of a user.
	GetLivenessStatus(ctx context.Context, in *GetLivenessStatusRequest, opts ...grpc.CallOption) (*GetLivenessStatusResponse, error)
	// FaceMatch compares the reference image of the user with the shared video.
	FaceMatch(ctx context.Context, in *FaceMatchRequest, opts ...grpc.CallOption) (*FaceMatchResponse, error)
	// GetFaceMatchStatus checks the face match status of a user.
	GetFaceMatchStatus(ctx context.Context, in *GetFaceMatchStatusRequest, opts ...grpc.CallOption) (*GetFaceMatchStatusResponse, error)
	// LivenessCheckWithStreamVideoRequest streams the video of the user and starts liveness and face match.
	CheckLivenessWithVideoStream(ctx context.Context, opts ...grpc.CallOption) (Liveness_CheckLivenessWithVideoStreamClient, error)
	// GenerateOTP generates OTP for a request ID and registers the request ID to start processing the video streaming.
	GenerateOTP(ctx context.Context, in *GenerateOTPRequest, opts ...grpc.CallOption) (*GenerateOTPResponse, error)
	// GetS3Image gets the S3 image stored in liveness bucket based on location key.
	GetS3Image(ctx context.Context, in *GetS3ImageRequest, opts ...grpc.CallOption) (*GetS3ImageResponse, error)
	// GetLivenessAttempts gets all the liveness attempts for an actorId
	GetLivenessAttempts(ctx context.Context, in *GetLivenessAttemptsRequest, opts ...grpc.CallOption) (*GetLivenessAttemptsResponse, error)
	// Annotate notes and observations for liveness request ID
	AnnotateLivenessAttempt(ctx context.Context, in *AnnotateLivenessAttemptRequest, opts ...grpc.CallOption) (*AnnotateLivenessAttemptResponse, error)
	// CreateLivenessSummary creates a liveness summary for the
	CreateLivenessSummary(ctx context.Context, in *CreateLivenessSummaryRequest, opts ...grpc.CallOption) (*CreateLivenessSummaryResponse, error)
	// GetLivenessSummary gets the liveness summary associated with an actor and a liveness flow
	GetLivenessSummary(ctx context.Context, in *GetLivenessSummaryRequest, opts ...grpc.CallOption) (*GetLivenessSummaryResponse, error)
	// GetLivenessSummaryStatus gets the latest summary status after updating both the liveness and facematch statuses
	GetLivenessSummaryStatus(ctx context.Context, in *GetLivenessSummaryStatusRequest, opts ...grpc.CallOption) (*GetLivenessSummaryStatusResponse, error)
	// CreateLivenessAttempt creates or sends the previous liveness attempt associated with the summary
	CreateLivenessAttempt(ctx context.Context, in *CreateLivenessAttemptRequest, opts ...grpc.CallOption) (*CreateLivenessAttemptResponse, error)
	// AnnotateFacematchAttempt will be used to annotate notes and observations for facematch attempts
	// But it is currently being used to manually approve summary facematch status
	AnnotateFacematchAttempt(ctx context.Context, in *AnnotateFacematchAttemptRequest, opts ...grpc.CallOption) (*AnnotateFacematchAttemptResponse, error)
	// RedoFacematch should be called when the caller wants to do face match with a new set of photos.
	// Old face match results will be discarded
	RedoFaceMatch(ctx context.Context, in *RedoFaceMatchRequest, opts ...grpc.CallOption) (*RedoFaceMatchResponse, error)
	// GetFacematchAttempts return all the attempts for a actorid
	GetFaceMatchAttempts(ctx context.Context, in *GetFaceMatchAttemptsRequest, opts ...grpc.CallOption) (*GetFaceMatchAttemptsResponse, error)
	// RetryLiveness rpc is to give retries to user to do liveness again, this will only be used for liveness attempts
	// which were created using liveness summary
	RetryLiveness(ctx context.Context, in *RetryLivenessRequest, opts ...grpc.CallOption) (*RetryLivenessResponse, error)
	// GetLivenessAttemptByReqId fetches liveness attempt by request id from the db
	// CAUTION: This rpc might return stale data, if you need latest synced status use GetLivenessStatus
	GetLivenessAttempt(ctx context.Context, in *GetLivenessAttemptRequest, opts ...grpc.CallOption) (*GetLivenessAttemptResponse, error)
}

type livenessClient struct {
	cc grpc.ClientConnInterface
}

func NewLivenessClient(cc grpc.ClientConnInterface) LivenessClient {
	return &livenessClient{cc}
}

func (c *livenessClient) GetLivenessStatus(ctx context.Context, in *GetLivenessStatusRequest, opts ...grpc.CallOption) (*GetLivenessStatusResponse, error) {
	out := new(GetLivenessStatusResponse)
	err := c.cc.Invoke(ctx, Liveness_GetLivenessStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) FaceMatch(ctx context.Context, in *FaceMatchRequest, opts ...grpc.CallOption) (*FaceMatchResponse, error) {
	out := new(FaceMatchResponse)
	err := c.cc.Invoke(ctx, Liveness_FaceMatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) GetFaceMatchStatus(ctx context.Context, in *GetFaceMatchStatusRequest, opts ...grpc.CallOption) (*GetFaceMatchStatusResponse, error) {
	out := new(GetFaceMatchStatusResponse)
	err := c.cc.Invoke(ctx, Liveness_GetFaceMatchStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) CheckLivenessWithVideoStream(ctx context.Context, opts ...grpc.CallOption) (Liveness_CheckLivenessWithVideoStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &Liveness_ServiceDesc.Streams[0], Liveness_CheckLivenessWithVideoStream_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &livenessCheckLivenessWithVideoStreamClient{stream}
	return x, nil
}

type Liveness_CheckLivenessWithVideoStreamClient interface {
	Send(*CheckLivenessWithVideoStreamRequest) error
	CloseAndRecv() (*CheckLivenessWithVideoStreamResponse, error)
	grpc.ClientStream
}

type livenessCheckLivenessWithVideoStreamClient struct {
	grpc.ClientStream
}

func (x *livenessCheckLivenessWithVideoStreamClient) Send(m *CheckLivenessWithVideoStreamRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *livenessCheckLivenessWithVideoStreamClient) CloseAndRecv() (*CheckLivenessWithVideoStreamResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(CheckLivenessWithVideoStreamResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *livenessClient) GenerateOTP(ctx context.Context, in *GenerateOTPRequest, opts ...grpc.CallOption) (*GenerateOTPResponse, error) {
	out := new(GenerateOTPResponse)
	err := c.cc.Invoke(ctx, Liveness_GenerateOTP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) GetS3Image(ctx context.Context, in *GetS3ImageRequest, opts ...grpc.CallOption) (*GetS3ImageResponse, error) {
	out := new(GetS3ImageResponse)
	err := c.cc.Invoke(ctx, Liveness_GetS3Image_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) GetLivenessAttempts(ctx context.Context, in *GetLivenessAttemptsRequest, opts ...grpc.CallOption) (*GetLivenessAttemptsResponse, error) {
	out := new(GetLivenessAttemptsResponse)
	err := c.cc.Invoke(ctx, Liveness_GetLivenessAttempts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) AnnotateLivenessAttempt(ctx context.Context, in *AnnotateLivenessAttemptRequest, opts ...grpc.CallOption) (*AnnotateLivenessAttemptResponse, error) {
	out := new(AnnotateLivenessAttemptResponse)
	err := c.cc.Invoke(ctx, Liveness_AnnotateLivenessAttempt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) CreateLivenessSummary(ctx context.Context, in *CreateLivenessSummaryRequest, opts ...grpc.CallOption) (*CreateLivenessSummaryResponse, error) {
	out := new(CreateLivenessSummaryResponse)
	err := c.cc.Invoke(ctx, Liveness_CreateLivenessSummary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) GetLivenessSummary(ctx context.Context, in *GetLivenessSummaryRequest, opts ...grpc.CallOption) (*GetLivenessSummaryResponse, error) {
	out := new(GetLivenessSummaryResponse)
	err := c.cc.Invoke(ctx, Liveness_GetLivenessSummary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) GetLivenessSummaryStatus(ctx context.Context, in *GetLivenessSummaryStatusRequest, opts ...grpc.CallOption) (*GetLivenessSummaryStatusResponse, error) {
	out := new(GetLivenessSummaryStatusResponse)
	err := c.cc.Invoke(ctx, Liveness_GetLivenessSummaryStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) CreateLivenessAttempt(ctx context.Context, in *CreateLivenessAttemptRequest, opts ...grpc.CallOption) (*CreateLivenessAttemptResponse, error) {
	out := new(CreateLivenessAttemptResponse)
	err := c.cc.Invoke(ctx, Liveness_CreateLivenessAttempt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) AnnotateFacematchAttempt(ctx context.Context, in *AnnotateFacematchAttemptRequest, opts ...grpc.CallOption) (*AnnotateFacematchAttemptResponse, error) {
	out := new(AnnotateFacematchAttemptResponse)
	err := c.cc.Invoke(ctx, Liveness_AnnotateFacematchAttempt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) RedoFaceMatch(ctx context.Context, in *RedoFaceMatchRequest, opts ...grpc.CallOption) (*RedoFaceMatchResponse, error) {
	out := new(RedoFaceMatchResponse)
	err := c.cc.Invoke(ctx, Liveness_RedoFaceMatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) GetFaceMatchAttempts(ctx context.Context, in *GetFaceMatchAttemptsRequest, opts ...grpc.CallOption) (*GetFaceMatchAttemptsResponse, error) {
	out := new(GetFaceMatchAttemptsResponse)
	err := c.cc.Invoke(ctx, Liveness_GetFaceMatchAttempts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) RetryLiveness(ctx context.Context, in *RetryLivenessRequest, opts ...grpc.CallOption) (*RetryLivenessResponse, error) {
	out := new(RetryLivenessResponse)
	err := c.cc.Invoke(ctx, Liveness_RetryLiveness_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *livenessClient) GetLivenessAttempt(ctx context.Context, in *GetLivenessAttemptRequest, opts ...grpc.CallOption) (*GetLivenessAttemptResponse, error) {
	out := new(GetLivenessAttemptResponse)
	err := c.cc.Invoke(ctx, Liveness_GetLivenessAttempt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LivenessServer is the server API for Liveness service.
// All implementations should embed UnimplementedLivenessServer
// for forward compatibility
type LivenessServer interface {
	// GetLivenessStatus Gets the liveness status of a user.
	GetLivenessStatus(context.Context, *GetLivenessStatusRequest) (*GetLivenessStatusResponse, error)
	// FaceMatch compares the reference image of the user with the shared video.
	FaceMatch(context.Context, *FaceMatchRequest) (*FaceMatchResponse, error)
	// GetFaceMatchStatus checks the face match status of a user.
	GetFaceMatchStatus(context.Context, *GetFaceMatchStatusRequest) (*GetFaceMatchStatusResponse, error)
	// LivenessCheckWithStreamVideoRequest streams the video of the user and starts liveness and face match.
	CheckLivenessWithVideoStream(Liveness_CheckLivenessWithVideoStreamServer) error
	// GenerateOTP generates OTP for a request ID and registers the request ID to start processing the video streaming.
	GenerateOTP(context.Context, *GenerateOTPRequest) (*GenerateOTPResponse, error)
	// GetS3Image gets the S3 image stored in liveness bucket based on location key.
	GetS3Image(context.Context, *GetS3ImageRequest) (*GetS3ImageResponse, error)
	// GetLivenessAttempts gets all the liveness attempts for an actorId
	GetLivenessAttempts(context.Context, *GetLivenessAttemptsRequest) (*GetLivenessAttemptsResponse, error)
	// Annotate notes and observations for liveness request ID
	AnnotateLivenessAttempt(context.Context, *AnnotateLivenessAttemptRequest) (*AnnotateLivenessAttemptResponse, error)
	// CreateLivenessSummary creates a liveness summary for the
	CreateLivenessSummary(context.Context, *CreateLivenessSummaryRequest) (*CreateLivenessSummaryResponse, error)
	// GetLivenessSummary gets the liveness summary associated with an actor and a liveness flow
	GetLivenessSummary(context.Context, *GetLivenessSummaryRequest) (*GetLivenessSummaryResponse, error)
	// GetLivenessSummaryStatus gets the latest summary status after updating both the liveness and facematch statuses
	GetLivenessSummaryStatus(context.Context, *GetLivenessSummaryStatusRequest) (*GetLivenessSummaryStatusResponse, error)
	// CreateLivenessAttempt creates or sends the previous liveness attempt associated with the summary
	CreateLivenessAttempt(context.Context, *CreateLivenessAttemptRequest) (*CreateLivenessAttemptResponse, error)
	// AnnotateFacematchAttempt will be used to annotate notes and observations for facematch attempts
	// But it is currently being used to manually approve summary facematch status
	AnnotateFacematchAttempt(context.Context, *AnnotateFacematchAttemptRequest) (*AnnotateFacematchAttemptResponse, error)
	// RedoFacematch should be called when the caller wants to do face match with a new set of photos.
	// Old face match results will be discarded
	RedoFaceMatch(context.Context, *RedoFaceMatchRequest) (*RedoFaceMatchResponse, error)
	// GetFacematchAttempts return all the attempts for a actorid
	GetFaceMatchAttempts(context.Context, *GetFaceMatchAttemptsRequest) (*GetFaceMatchAttemptsResponse, error)
	// RetryLiveness rpc is to give retries to user to do liveness again, this will only be used for liveness attempts
	// which were created using liveness summary
	RetryLiveness(context.Context, *RetryLivenessRequest) (*RetryLivenessResponse, error)
	// GetLivenessAttemptByReqId fetches liveness attempt by request id from the db
	// CAUTION: This rpc might return stale data, if you need latest synced status use GetLivenessStatus
	GetLivenessAttempt(context.Context, *GetLivenessAttemptRequest) (*GetLivenessAttemptResponse, error)
}

// UnimplementedLivenessServer should be embedded to have forward compatible implementations.
type UnimplementedLivenessServer struct {
}

func (UnimplementedLivenessServer) GetLivenessStatus(context.Context, *GetLivenessStatusRequest) (*GetLivenessStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLivenessStatus not implemented")
}
func (UnimplementedLivenessServer) FaceMatch(context.Context, *FaceMatchRequest) (*FaceMatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaceMatch not implemented")
}
func (UnimplementedLivenessServer) GetFaceMatchStatus(context.Context, *GetFaceMatchStatusRequest) (*GetFaceMatchStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFaceMatchStatus not implemented")
}
func (UnimplementedLivenessServer) CheckLivenessWithVideoStream(Liveness_CheckLivenessWithVideoStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method CheckLivenessWithVideoStream not implemented")
}
func (UnimplementedLivenessServer) GenerateOTP(context.Context, *GenerateOTPRequest) (*GenerateOTPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateOTP not implemented")
}
func (UnimplementedLivenessServer) GetS3Image(context.Context, *GetS3ImageRequest) (*GetS3ImageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetS3Image not implemented")
}
func (UnimplementedLivenessServer) GetLivenessAttempts(context.Context, *GetLivenessAttemptsRequest) (*GetLivenessAttemptsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLivenessAttempts not implemented")
}
func (UnimplementedLivenessServer) AnnotateLivenessAttempt(context.Context, *AnnotateLivenessAttemptRequest) (*AnnotateLivenessAttemptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AnnotateLivenessAttempt not implemented")
}
func (UnimplementedLivenessServer) CreateLivenessSummary(context.Context, *CreateLivenessSummaryRequest) (*CreateLivenessSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLivenessSummary not implemented")
}
func (UnimplementedLivenessServer) GetLivenessSummary(context.Context, *GetLivenessSummaryRequest) (*GetLivenessSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLivenessSummary not implemented")
}
func (UnimplementedLivenessServer) GetLivenessSummaryStatus(context.Context, *GetLivenessSummaryStatusRequest) (*GetLivenessSummaryStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLivenessSummaryStatus not implemented")
}
func (UnimplementedLivenessServer) CreateLivenessAttempt(context.Context, *CreateLivenessAttemptRequest) (*CreateLivenessAttemptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLivenessAttempt not implemented")
}
func (UnimplementedLivenessServer) AnnotateFacematchAttempt(context.Context, *AnnotateFacematchAttemptRequest) (*AnnotateFacematchAttemptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AnnotateFacematchAttempt not implemented")
}
func (UnimplementedLivenessServer) RedoFaceMatch(context.Context, *RedoFaceMatchRequest) (*RedoFaceMatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RedoFaceMatch not implemented")
}
func (UnimplementedLivenessServer) GetFaceMatchAttempts(context.Context, *GetFaceMatchAttemptsRequest) (*GetFaceMatchAttemptsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFaceMatchAttempts not implemented")
}
func (UnimplementedLivenessServer) RetryLiveness(context.Context, *RetryLivenessRequest) (*RetryLivenessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetryLiveness not implemented")
}
func (UnimplementedLivenessServer) GetLivenessAttempt(context.Context, *GetLivenessAttemptRequest) (*GetLivenessAttemptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLivenessAttempt not implemented")
}

// UnsafeLivenessServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LivenessServer will
// result in compilation errors.
type UnsafeLivenessServer interface {
	mustEmbedUnimplementedLivenessServer()
}

func RegisterLivenessServer(s grpc.ServiceRegistrar, srv LivenessServer) {
	s.RegisterService(&Liveness_ServiceDesc, srv)
}

func _Liveness_GetLivenessStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLivenessStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).GetLivenessStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_GetLivenessStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).GetLivenessStatus(ctx, req.(*GetLivenessStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_FaceMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaceMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).FaceMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_FaceMatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).FaceMatch(ctx, req.(*FaceMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_GetFaceMatchStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFaceMatchStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).GetFaceMatchStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_GetFaceMatchStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).GetFaceMatchStatus(ctx, req.(*GetFaceMatchStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_CheckLivenessWithVideoStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(LivenessServer).CheckLivenessWithVideoStream(&livenessCheckLivenessWithVideoStreamServer{stream})
}

type Liveness_CheckLivenessWithVideoStreamServer interface {
	SendAndClose(*CheckLivenessWithVideoStreamResponse) error
	Recv() (*CheckLivenessWithVideoStreamRequest, error)
	grpc.ServerStream
}

type livenessCheckLivenessWithVideoStreamServer struct {
	grpc.ServerStream
}

func (x *livenessCheckLivenessWithVideoStreamServer) SendAndClose(m *CheckLivenessWithVideoStreamResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *livenessCheckLivenessWithVideoStreamServer) Recv() (*CheckLivenessWithVideoStreamRequest, error) {
	m := new(CheckLivenessWithVideoStreamRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Liveness_GenerateOTP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateOTPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).GenerateOTP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_GenerateOTP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).GenerateOTP(ctx, req.(*GenerateOTPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_GetS3Image_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetS3ImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).GetS3Image(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_GetS3Image_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).GetS3Image(ctx, req.(*GetS3ImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_GetLivenessAttempts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLivenessAttemptsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).GetLivenessAttempts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_GetLivenessAttempts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).GetLivenessAttempts(ctx, req.(*GetLivenessAttemptsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_AnnotateLivenessAttempt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnnotateLivenessAttemptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).AnnotateLivenessAttempt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_AnnotateLivenessAttempt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).AnnotateLivenessAttempt(ctx, req.(*AnnotateLivenessAttemptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_CreateLivenessSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLivenessSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).CreateLivenessSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_CreateLivenessSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).CreateLivenessSummary(ctx, req.(*CreateLivenessSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_GetLivenessSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLivenessSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).GetLivenessSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_GetLivenessSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).GetLivenessSummary(ctx, req.(*GetLivenessSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_GetLivenessSummaryStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLivenessSummaryStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).GetLivenessSummaryStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_GetLivenessSummaryStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).GetLivenessSummaryStatus(ctx, req.(*GetLivenessSummaryStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_CreateLivenessAttempt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLivenessAttemptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).CreateLivenessAttempt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_CreateLivenessAttempt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).CreateLivenessAttempt(ctx, req.(*CreateLivenessAttemptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_AnnotateFacematchAttempt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnnotateFacematchAttemptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).AnnotateFacematchAttempt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_AnnotateFacematchAttempt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).AnnotateFacematchAttempt(ctx, req.(*AnnotateFacematchAttemptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_RedoFaceMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RedoFaceMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).RedoFaceMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_RedoFaceMatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).RedoFaceMatch(ctx, req.(*RedoFaceMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_GetFaceMatchAttempts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFaceMatchAttemptsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).GetFaceMatchAttempts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_GetFaceMatchAttempts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).GetFaceMatchAttempts(ctx, req.(*GetFaceMatchAttemptsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_RetryLiveness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetryLivenessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).RetryLiveness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_RetryLiveness_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).RetryLiveness(ctx, req.(*RetryLivenessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Liveness_GetLivenessAttempt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLivenessAttemptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LivenessServer).GetLivenessAttempt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Liveness_GetLivenessAttempt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LivenessServer).GetLivenessAttempt(ctx, req.(*GetLivenessAttemptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Liveness_ServiceDesc is the grpc.ServiceDesc for Liveness service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Liveness_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "auth.liveness.Liveness",
	HandlerType: (*LivenessServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLivenessStatus",
			Handler:    _Liveness_GetLivenessStatus_Handler,
		},
		{
			MethodName: "FaceMatch",
			Handler:    _Liveness_FaceMatch_Handler,
		},
		{
			MethodName: "GetFaceMatchStatus",
			Handler:    _Liveness_GetFaceMatchStatus_Handler,
		},
		{
			MethodName: "GenerateOTP",
			Handler:    _Liveness_GenerateOTP_Handler,
		},
		{
			MethodName: "GetS3Image",
			Handler:    _Liveness_GetS3Image_Handler,
		},
		{
			MethodName: "GetLivenessAttempts",
			Handler:    _Liveness_GetLivenessAttempts_Handler,
		},
		{
			MethodName: "AnnotateLivenessAttempt",
			Handler:    _Liveness_AnnotateLivenessAttempt_Handler,
		},
		{
			MethodName: "CreateLivenessSummary",
			Handler:    _Liveness_CreateLivenessSummary_Handler,
		},
		{
			MethodName: "GetLivenessSummary",
			Handler:    _Liveness_GetLivenessSummary_Handler,
		},
		{
			MethodName: "GetLivenessSummaryStatus",
			Handler:    _Liveness_GetLivenessSummaryStatus_Handler,
		},
		{
			MethodName: "CreateLivenessAttempt",
			Handler:    _Liveness_CreateLivenessAttempt_Handler,
		},
		{
			MethodName: "AnnotateFacematchAttempt",
			Handler:    _Liveness_AnnotateFacematchAttempt_Handler,
		},
		{
			MethodName: "RedoFaceMatch",
			Handler:    _Liveness_RedoFaceMatch_Handler,
		},
		{
			MethodName: "GetFaceMatchAttempts",
			Handler:    _Liveness_GetFaceMatchAttempts_Handler,
		},
		{
			MethodName: "RetryLiveness",
			Handler:    _Liveness_RetryLiveness_Handler,
		},
		{
			MethodName: "GetLivenessAttempt",
			Handler:    _Liveness_GetLivenessAttempt_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "CheckLivenessWithVideoStream",
			Handler:       _Liveness_CheckLivenessWithVideoStream_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "api/auth/liveness/service.proto",
}
