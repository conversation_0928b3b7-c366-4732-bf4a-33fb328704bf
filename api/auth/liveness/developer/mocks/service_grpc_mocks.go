// Code generated by MockGen. DO NOT EDIT.
// Source: api/auth/liveness/developer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockDevLivenessClient is a mock of DevLivenessClient interface.
type MockDevLivenessClient struct {
	ctrl     *gomock.Controller
	recorder *MockDevLivenessClientMockRecorder
}

// MockDevLivenessClientMockRecorder is the mock recorder for MockDevLivenessClient.
type MockDevLivenessClientMockRecorder struct {
	mock *MockDevLivenessClient
}

// NewMockDevLivenessClient creates a new mock instance.
func NewMockDevLivenessClient(ctrl *gomock.Controller) *MockDevLivenessClient {
	mock := &MockDevLivenessClient{ctrl: ctrl}
	mock.recorder = &MockDevLivenessClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevLivenessClient) EXPECT() *MockDevLivenessClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDevLivenessClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDevLivenessClientMockRecorder) GetData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDevLivenessClient)(nil).GetData), varargs...)
}

// GetEntityList mocks base method.
func (m *MockDevLivenessClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntityList", varargs...)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDevLivenessClientMockRecorder) GetEntityList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDevLivenessClient)(nil).GetEntityList), varargs...)
}

// GetParameterList mocks base method.
func (m *MockDevLivenessClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParameterList", varargs...)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDevLivenessClientMockRecorder) GetParameterList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDevLivenessClient)(nil).GetParameterList), varargs...)
}

// MockDevLivenessServer is a mock of DevLivenessServer interface.
type MockDevLivenessServer struct {
	ctrl     *gomock.Controller
	recorder *MockDevLivenessServerMockRecorder
}

// MockDevLivenessServerMockRecorder is the mock recorder for MockDevLivenessServer.
type MockDevLivenessServerMockRecorder struct {
	mock *MockDevLivenessServer
}

// NewMockDevLivenessServer creates a new mock instance.
func NewMockDevLivenessServer(ctrl *gomock.Controller) *MockDevLivenessServer {
	mock := &MockDevLivenessServer{ctrl: ctrl}
	mock.recorder = &MockDevLivenessServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevLivenessServer) EXPECT() *MockDevLivenessServerMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDevLivenessServer) GetData(arg0 context.Context, arg1 *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDevLivenessServerMockRecorder) GetData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDevLivenessServer)(nil).GetData), arg0, arg1)
}

// GetEntityList mocks base method.
func (m *MockDevLivenessServer) GetEntityList(arg0 context.Context, arg1 *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDevLivenessServerMockRecorder) GetEntityList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDevLivenessServer)(nil).GetEntityList), arg0, arg1)
}

// GetParameterList mocks base method.
func (m *MockDevLivenessServer) GetParameterList(arg0 context.Context, arg1 *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameterList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDevLivenessServerMockRecorder) GetParameterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDevLivenessServer)(nil).GetParameterList), arg0, arg1)
}

// MockUnsafeDevLivenessServer is a mock of UnsafeDevLivenessServer interface.
type MockUnsafeDevLivenessServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeDevLivenessServerMockRecorder
}

// MockUnsafeDevLivenessServerMockRecorder is the mock recorder for MockUnsafeDevLivenessServer.
type MockUnsafeDevLivenessServerMockRecorder struct {
	mock *MockUnsafeDevLivenessServer
}

// NewMockUnsafeDevLivenessServer creates a new mock instance.
func NewMockUnsafeDevLivenessServer(ctrl *gomock.Controller) *MockUnsafeDevLivenessServer {
	mock := &MockUnsafeDevLivenessServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeDevLivenessServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeDevLivenessServer) EXPECT() *MockUnsafeDevLivenessServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedDevLivenessServer mocks base method.
func (m *MockUnsafeDevLivenessServer) mustEmbedUnimplementedDevLivenessServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedDevLivenessServer")
}

// mustEmbedUnimplementedDevLivenessServer indicates an expected call of mustEmbedUnimplementedDevLivenessServer.
func (mr *MockUnsafeDevLivenessServerMockRecorder) mustEmbedUnimplementedDevLivenessServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedDevLivenessServer", reflect.TypeOf((*MockUnsafeDevLivenessServer)(nil).mustEmbedUnimplementedDevLivenessServer))
}
