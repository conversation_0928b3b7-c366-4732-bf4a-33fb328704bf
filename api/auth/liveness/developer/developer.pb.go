// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/liveness/developer/developer.proto

package developer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LivenessEntity int32

const (
	LivenessEntity_LIVENESS_ENTITY_UNSPECIFIED LivenessEntity = 0
	LivenessEntity_LIVENESS_SUMMARY            LivenessEntity = 1
	LivenessEntity_LIVENESS_INHOUSE_SUMMARY    LivenessEntity = 2
)

// Enum value maps for LivenessEntity.
var (
	LivenessEntity_name = map[int32]string{
		0: "LIVENESS_ENTITY_UNSPECIFIED",
		1: "LIVENESS_SUMMARY",
		2: "LIVENESS_INHOUSE_SUMMARY",
	}
	LivenessEntity_value = map[string]int32{
		"LIVENESS_ENTITY_UNSPECIFIED": 0,
		"LIVENESS_SUMMARY":            1,
		"LIVENESS_INHOUSE_SUMMARY":    2,
	}
)

func (x LivenessEntity) Enum() *LivenessEntity {
	p := new(LivenessEntity)
	*p = x
	return p
}

func (x LivenessEntity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LivenessEntity) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_developer_developer_proto_enumTypes[0].Descriptor()
}

func (LivenessEntity) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_developer_developer_proto_enumTypes[0]
}

func (x LivenessEntity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LivenessEntity.Descriptor instead.
func (LivenessEntity) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_developer_developer_proto_rawDescGZIP(), []int{0}
}

var File_api_auth_liveness_developer_developer_proto protoreflect.FileDescriptor

var file_api_auth_liveness_developer_developer_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2f, 0x64, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x64, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2a, 0x65, 0x0a, 0x0e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x49, 0x56, 0x45,
	0x4e, 0x45, 0x53, 0x53, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x10, 0x01, 0x12,
	0x1c, 0x0a, 0x18, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x49, 0x4e, 0x48, 0x4f,
	0x55, 0x53, 0x45, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x10, 0x02, 0x42, 0x68, 0x0a,
	0x32, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x72, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x64, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_liveness_developer_developer_proto_rawDescOnce sync.Once
	file_api_auth_liveness_developer_developer_proto_rawDescData = file_api_auth_liveness_developer_developer_proto_rawDesc
)

func file_api_auth_liveness_developer_developer_proto_rawDescGZIP() []byte {
	file_api_auth_liveness_developer_developer_proto_rawDescOnce.Do(func() {
		file_api_auth_liveness_developer_developer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_liveness_developer_developer_proto_rawDescData)
	})
	return file_api_auth_liveness_developer_developer_proto_rawDescData
}

var file_api_auth_liveness_developer_developer_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_liveness_developer_developer_proto_goTypes = []interface{}{
	(LivenessEntity)(0), // 0: auth.liveness.developer.LivenessEntity
}
var file_api_auth_liveness_developer_developer_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_auth_liveness_developer_developer_proto_init() }
func file_api_auth_liveness_developer_developer_proto_init() {
	if File_api_auth_liveness_developer_developer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_liveness_developer_developer_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_liveness_developer_developer_proto_goTypes,
		DependencyIndexes: file_api_auth_liveness_developer_developer_proto_depIdxs,
		EnumInfos:         file_api_auth_liveness_developer_developer_proto_enumTypes,
	}.Build()
	File_api_auth_liveness_developer_developer_proto = out.File
	file_api_auth_liveness_developer_developer_proto_rawDesc = nil
	file_api_auth_liveness_developer_developer_proto_goTypes = nil
	file_api_auth_liveness_developer_developer_proto_depIdxs = nil
}
