// protolint:disable MAX_LINE_LENGTH

//
//Protos relating to the Liveness that are internal to the domain such as data models

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/liveness/internal/face_match_attempt.proto

package liveness

import (
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// FaceMatchAttemptFieldMask is used to mask columns to update in DB Update call
type FaceMatchAttemptFieldMask int32

const (
	FaceMatchAttemptFieldMask_FM_ATTEMPT_FIELD_MASK_FM_ATTEMPT_FIELD_NONE FaceMatchAttemptFieldMask = 0
	FaceMatchAttemptFieldMask_FM_ATTEMPT_FIELD_MASK_VENDOR                FaceMatchAttemptFieldMask = 1
	FaceMatchAttemptFieldMask_FM_ATTEMPT_FIELD_MASK_VENDOR_REQUEST_ID     FaceMatchAttemptFieldMask = 2
	FaceMatchAttemptFieldMask_FM_ATTEMPT_FIELD_MASK_VIDEO_LOCATION        FaceMatchAttemptFieldMask = 4
	FaceMatchAttemptFieldMask_FM_ATTEMPT_FIELD_MASK_IMAGE_LOCATION        FaceMatchAttemptFieldMask = 5
	FaceMatchAttemptFieldMask_FM_ATTEMPT_FIELD_MASK_STATUS                FaceMatchAttemptFieldMask = 6
	FaceMatchAttemptFieldMask_FM_ATTEMPT_FIELD_MASK_FACE_MATCH_SCORE      FaceMatchAttemptFieldMask = 7
	FaceMatchAttemptFieldMask_FM_ATTEMPT_FIELD_MASK_SUMMARY_ID            FaceMatchAttemptFieldMask = 8
	FaceMatchAttemptFieldMask_FM_ATTEMPT_FIELD_MASK_ANNOTATION            FaceMatchAttemptFieldMask = 9
	FaceMatchAttemptFieldMask_FM_ATTEMPT_FIELD_MASK_THRESHOLD             FaceMatchAttemptFieldMask = 10
	FaceMatchAttemptFieldMask_FM_ATTEMPT_FIELD_MASK_INHOUSE_STATUS        FaceMatchAttemptFieldMask = 11
)

// Enum value maps for FaceMatchAttemptFieldMask.
var (
	FaceMatchAttemptFieldMask_name = map[int32]string{
		0:  "FM_ATTEMPT_FIELD_MASK_FM_ATTEMPT_FIELD_NONE",
		1:  "FM_ATTEMPT_FIELD_MASK_VENDOR",
		2:  "FM_ATTEMPT_FIELD_MASK_VENDOR_REQUEST_ID",
		4:  "FM_ATTEMPT_FIELD_MASK_VIDEO_LOCATION",
		5:  "FM_ATTEMPT_FIELD_MASK_IMAGE_LOCATION",
		6:  "FM_ATTEMPT_FIELD_MASK_STATUS",
		7:  "FM_ATTEMPT_FIELD_MASK_FACE_MATCH_SCORE",
		8:  "FM_ATTEMPT_FIELD_MASK_SUMMARY_ID",
		9:  "FM_ATTEMPT_FIELD_MASK_ANNOTATION",
		10: "FM_ATTEMPT_FIELD_MASK_THRESHOLD",
		11: "FM_ATTEMPT_FIELD_MASK_INHOUSE_STATUS",
	}
	FaceMatchAttemptFieldMask_value = map[string]int32{
		"FM_ATTEMPT_FIELD_MASK_FM_ATTEMPT_FIELD_NONE": 0,
		"FM_ATTEMPT_FIELD_MASK_VENDOR":                1,
		"FM_ATTEMPT_FIELD_MASK_VENDOR_REQUEST_ID":     2,
		"FM_ATTEMPT_FIELD_MASK_VIDEO_LOCATION":        4,
		"FM_ATTEMPT_FIELD_MASK_IMAGE_LOCATION":        5,
		"FM_ATTEMPT_FIELD_MASK_STATUS":                6,
		"FM_ATTEMPT_FIELD_MASK_FACE_MATCH_SCORE":      7,
		"FM_ATTEMPT_FIELD_MASK_SUMMARY_ID":            8,
		"FM_ATTEMPT_FIELD_MASK_ANNOTATION":            9,
		"FM_ATTEMPT_FIELD_MASK_THRESHOLD":             10,
		"FM_ATTEMPT_FIELD_MASK_INHOUSE_STATUS":        11,
	}
)

func (x FaceMatchAttemptFieldMask) Enum() *FaceMatchAttemptFieldMask {
	p := new(FaceMatchAttemptFieldMask)
	*p = x
	return p
}

func (x FaceMatchAttemptFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FaceMatchAttemptFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_internal_face_match_attempt_proto_enumTypes[0].Descriptor()
}

func (FaceMatchAttemptFieldMask) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_internal_face_match_attempt_proto_enumTypes[0]
}

func (x FaceMatchAttemptFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FaceMatchAttemptFieldMask.Descriptor instead.
func (FaceMatchAttemptFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_face_match_attempt_proto_rawDescGZIP(), []int{0}
}

// FaceMatchAttempt stores the information related to the
// face match attempt.
type FaceMatchAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// unique identifier auto generated by DB.
	AttemptId string `protobuf:"bytes,2,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
	// RequestID is the ID with which the caller calls FM to identify and track the status of the request
	RequestId string `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Request ID to track vendor request
	VendorRequestId string `protobuf:"bytes,4,opt,name=vendor_request_id,json=vendorRequestId,proto3" json:"vendor_request_id,omitempty"`
	// Vendor
	Vendor vendorgateway.Vendor `protobuf:"varint,8,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	// location of video which was used to do the FM. Example for S3: s3://demo-bucket/try/p3_2.mp4.
	VideoLocation string `protobuf:"bytes,5,opt,name=video_location,json=videoLocation,proto3" json:"video_location,omitempty"`
	ImageLocation string `protobuf:"bytes,6,opt,name=image_location,json=imageLocation,proto3" json:"image_location,omitempty"`
	// Status of the FM attempt
	Status         FaceMatchStatus `protobuf:"varint,7,opt,name=status,proto3,enum=auth.liveness.FaceMatchStatus" json:"status,omitempty"`
	FaceMatchScore float32         `protobuf:"fixed32,10,opt,name=face_match_score,json=faceMatchScore,proto3" json:"face_match_score,omitempty"`
	// Id of the liveness summary this attempt corresponds to
	SummaryId string `protobuf:"bytes,11,opt,name=summary_id,json=summaryId,proto3" json:"summary_id,omitempty"`
	// annotation to update extra information
	Annotation *FaceMatchAnnotation `protobuf:"bytes,12,opt,name=annotation,proto3" json:"annotation,omitempty"`
	// Threshold used to pass the facematch attempt
	Threshold float32 `protobuf:"fixed32,13,opt,name=threshold,proto3" json:"threshold,omitempty"`
	// store strictness logic applicable for user
	//
	// Deprecated: Marked as deprecated in api/auth/liveness/internal/face_match_attempt.proto.
	StrictnessLogic StrictnessLogic `protobuf:"varint,14,opt,name=strictness_logic,json=strictnessLogic,proto3,enum=auth.liveness.StrictnessLogic" json:"strictness_logic,omitempty"`
	// store inhouse status
	InhouseStatus FaceMatchStatus `protobuf:"varint,15,opt,name=inhouse_status,json=inhouseStatus,proto3,enum=auth.liveness.FaceMatchStatus" json:"inhouse_status,omitempty"`
	// Request id of the liveness attempt this facematch was done for
	LivenessRequestId string                 `protobuf:"bytes,16,opt,name=liveness_request_id,json=livenessRequestId,proto3" json:"liveness_request_id,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *FaceMatchAttempt) Reset() {
	*x = FaceMatchAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_internal_face_match_attempt_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceMatchAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceMatchAttempt) ProtoMessage() {}

func (x *FaceMatchAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_internal_face_match_attempt_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceMatchAttempt.ProtoReflect.Descriptor instead.
func (*FaceMatchAttempt) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_face_match_attempt_proto_rawDescGZIP(), []int{0}
}

func (x *FaceMatchAttempt) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *FaceMatchAttempt) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *FaceMatchAttempt) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *FaceMatchAttempt) GetVendorRequestId() string {
	if x != nil {
		return x.VendorRequestId
	}
	return ""
}

func (x *FaceMatchAttempt) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *FaceMatchAttempt) GetVideoLocation() string {
	if x != nil {
		return x.VideoLocation
	}
	return ""
}

func (x *FaceMatchAttempt) GetImageLocation() string {
	if x != nil {
		return x.ImageLocation
	}
	return ""
}

func (x *FaceMatchAttempt) GetStatus() FaceMatchStatus {
	if x != nil {
		return x.Status
	}
	return FaceMatchStatus_FACE_MATCH_STATUS_UNSPECIFIED
}

func (x *FaceMatchAttempt) GetFaceMatchScore() float32 {
	if x != nil {
		return x.FaceMatchScore
	}
	return 0
}

func (x *FaceMatchAttempt) GetSummaryId() string {
	if x != nil {
		return x.SummaryId
	}
	return ""
}

func (x *FaceMatchAttempt) GetAnnotation() *FaceMatchAnnotation {
	if x != nil {
		return x.Annotation
	}
	return nil
}

func (x *FaceMatchAttempt) GetThreshold() float32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

// Deprecated: Marked as deprecated in api/auth/liveness/internal/face_match_attempt.proto.
func (x *FaceMatchAttempt) GetStrictnessLogic() StrictnessLogic {
	if x != nil {
		return x.StrictnessLogic
	}
	return StrictnessLogic_STRICTNESS_LOGIC_UNSPECIFIED
}

func (x *FaceMatchAttempt) GetInhouseStatus() FaceMatchStatus {
	if x != nil {
		return x.InhouseStatus
	}
	return FaceMatchStatus_FACE_MATCH_STATUS_UNSPECIFIED
}

func (x *FaceMatchAttempt) GetLivenessRequestId() string {
	if x != nil {
		return x.LivenessRequestId
	}
	return ""
}

func (x *FaceMatchAttempt) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *FaceMatchAttempt) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_api_auth_liveness_internal_face_match_attempt_proto protoreflect.FileDescriptor

var file_api_auth_liveness_internal_face_match_attempt_proto_rawDesc = []byte{
	0x0a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x66, 0x61, 0x63,
	0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2f, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb3, 0x06, 0x0a,
	0x10, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x66,
	0x61, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x66, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0a, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x74, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x4d, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x63,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x0f, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6e, 0x65, 0x73, 0x73,
	0x4c, 0x6f, 0x67, 0x69, 0x63, 0x12, 0x45, 0x0a, 0x0e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x46, 0x61,
	0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x69,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x13,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x2a, 0xd8, 0x03, 0x0a, 0x19, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b,
	0x12, 0x2f, 0x0a, 0x2b, 0x46, 0x4d, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x4d, 0x5f, 0x41, 0x54, 0x54,
	0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10,
	0x00, 0x12, 0x20, 0x0a, 0x1c, 0x46, 0x4d, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f,
	0x52, 0x10, 0x01, 0x12, 0x2b, 0x0a, 0x27, 0x46, 0x4d, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50,
	0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e,
	0x44, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x02,
	0x12, 0x28, 0x0a, 0x24, 0x46, 0x4d, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f,
	0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x28, 0x0a, 0x24, 0x46, 0x4d,
	0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x46, 0x4d, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d,
	0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0x06, 0x12, 0x2a, 0x0a, 0x26, 0x46, 0x4d, 0x5f, 0x41, 0x54, 0x54,
	0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45,
	0x10, 0x07, 0x12, 0x24, 0x0a, 0x20, 0x46, 0x4d, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x4d, 0x4d,
	0x41, 0x52, 0x59, 0x5f, 0x49, 0x44, 0x10, 0x08, 0x12, 0x24, 0x0a, 0x20, 0x46, 0x4d, 0x5f, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x23,
	0x0a, 0x1f, 0x46, 0x4d, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x53, 0x48, 0x4f, 0x4c,
	0x44, 0x10, 0x0a, 0x12, 0x28, 0x0a, 0x24, 0x46, 0x4d, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50,
	0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x4e, 0x48,
	0x4f, 0x55, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x0b, 0x42, 0x54, 0x0a,
	0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_liveness_internal_face_match_attempt_proto_rawDescOnce sync.Once
	file_api_auth_liveness_internal_face_match_attempt_proto_rawDescData = file_api_auth_liveness_internal_face_match_attempt_proto_rawDesc
)

func file_api_auth_liveness_internal_face_match_attempt_proto_rawDescGZIP() []byte {
	file_api_auth_liveness_internal_face_match_attempt_proto_rawDescOnce.Do(func() {
		file_api_auth_liveness_internal_face_match_attempt_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_liveness_internal_face_match_attempt_proto_rawDescData)
	})
	return file_api_auth_liveness_internal_face_match_attempt_proto_rawDescData
}

var file_api_auth_liveness_internal_face_match_attempt_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_liveness_internal_face_match_attempt_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_auth_liveness_internal_face_match_attempt_proto_goTypes = []interface{}{
	(FaceMatchAttemptFieldMask)(0), // 0: auth.liveness.FaceMatchAttemptFieldMask
	(*FaceMatchAttempt)(nil),       // 1: auth.liveness.FaceMatchAttempt
	(vendorgateway.Vendor)(0),      // 2: vendorgateway.Vendor
	(FaceMatchStatus)(0),           // 3: auth.liveness.FaceMatchStatus
	(*FaceMatchAnnotation)(nil),    // 4: auth.liveness.FaceMatchAnnotation
	(StrictnessLogic)(0),           // 5: auth.liveness.StrictnessLogic
	(*timestamppb.Timestamp)(nil),  // 6: google.protobuf.Timestamp
}
var file_api_auth_liveness_internal_face_match_attempt_proto_depIdxs = []int32{
	2, // 0: auth.liveness.FaceMatchAttempt.vendor:type_name -> vendorgateway.Vendor
	3, // 1: auth.liveness.FaceMatchAttempt.status:type_name -> auth.liveness.FaceMatchStatus
	4, // 2: auth.liveness.FaceMatchAttempt.annotation:type_name -> auth.liveness.FaceMatchAnnotation
	5, // 3: auth.liveness.FaceMatchAttempt.strictness_logic:type_name -> auth.liveness.StrictnessLogic
	3, // 4: auth.liveness.FaceMatchAttempt.inhouse_status:type_name -> auth.liveness.FaceMatchStatus
	6, // 5: auth.liveness.FaceMatchAttempt.created_at:type_name -> google.protobuf.Timestamp
	6, // 6: auth.liveness.FaceMatchAttempt.updated_at:type_name -> google.protobuf.Timestamp
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_auth_liveness_internal_face_match_attempt_proto_init() }
func file_api_auth_liveness_internal_face_match_attempt_proto_init() {
	if File_api_auth_liveness_internal_face_match_attempt_proto != nil {
		return
	}
	file_api_auth_liveness_internal_face_match_annotation_proto_init()
	file_api_auth_liveness_types_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_auth_liveness_internal_face_match_attempt_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceMatchAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_liveness_internal_face_match_attempt_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_liveness_internal_face_match_attempt_proto_goTypes,
		DependencyIndexes: file_api_auth_liveness_internal_face_match_attempt_proto_depIdxs,
		EnumInfos:         file_api_auth_liveness_internal_face_match_attempt_proto_enumTypes,
		MessageInfos:      file_api_auth_liveness_internal_face_match_attempt_proto_msgTypes,
	}.Build()
	File_api_auth_liveness_internal_face_match_attempt_proto = out.File
	file_api_auth_liveness_internal_face_match_attempt_proto_rawDesc = nil
	file_api_auth_liveness_internal_face_match_attempt_proto_goTypes = nil
	file_api_auth_liveness_internal_face_match_attempt_proto_depIdxs = nil
}
