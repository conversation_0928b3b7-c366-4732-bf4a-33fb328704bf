// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/liveness/internal/liveness_fm_annotations.proto

package liveness

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AnnotationType int32

const (
	AnnotationType_ANNOTATION_TYPE_UNSPECIFIED AnnotationType = 0
	AnnotationType_ANNOTATION_TYPE_LIVENESS    AnnotationType = 1
	AnnotationType_ANNOTATION_TYPE_FACE_MATCH  AnnotationType = 2
)

// Enum value maps for AnnotationType.
var (
	AnnotationType_name = map[int32]string{
		0: "ANNOTATION_TYPE_UNSPECIFIED",
		1: "ANNOTATION_TYPE_LIVENESS",
		2: "ANNOTATION_TYPE_FACE_MATCH",
	}
	AnnotationType_value = map[string]int32{
		"ANNOTATION_TYPE_UNSPECIFIED": 0,
		"ANNOTATION_TYPE_LIVENESS":    1,
		"ANNOTATION_TYPE_FACE_MATCH":  2,
	}
)

func (x AnnotationType) Enum() *AnnotationType {
	p := new(AnnotationType)
	*p = x
	return p
}

func (x AnnotationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AnnotationType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_internal_liveness_fm_annotations_proto_enumTypes[0].Descriptor()
}

func (AnnotationType) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_internal_liveness_fm_annotations_proto_enumTypes[0]
}

func (x AnnotationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AnnotationType.Descriptor instead.
func (AnnotationType) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_fm_annotations_proto_rawDescGZIP(), []int{0}
}

type LivenessFMAnnotation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// ID of the attempt which is being annotated. Eg. request id of liveness
	ReqId string `protobuf:"bytes,3,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	// type of attempt which is being annotated. Eg. liveness, facematch
	AnnotationType AnnotationType `protobuf:"varint,4,opt,name=annotation_type,json=annotationType,proto3,enum=auth.liveness.AnnotationType" json:"annotation_type,omitempty"`
	// annotation provided by ops team
	Annotation *LivenessFMAnnotationPayload `protobuf:"bytes,5,opt,name=annotation,proto3" json:"annotation,omitempty"`
	// timestamp when annotations were recorded
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// timestamp when annotations were updated
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *LivenessFMAnnotation) Reset() {
	*x = LivenessFMAnnotation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_internal_liveness_fm_annotations_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessFMAnnotation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessFMAnnotation) ProtoMessage() {}

func (x *LivenessFMAnnotation) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_internal_liveness_fm_annotations_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessFMAnnotation.ProtoReflect.Descriptor instead.
func (*LivenessFMAnnotation) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_fm_annotations_proto_rawDescGZIP(), []int{0}
}

func (x *LivenessFMAnnotation) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LivenessFMAnnotation) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *LivenessFMAnnotation) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *LivenessFMAnnotation) GetAnnotationType() AnnotationType {
	if x != nil {
		return x.AnnotationType
	}
	return AnnotationType_ANNOTATION_TYPE_UNSPECIFIED
}

func (x *LivenessFMAnnotation) GetAnnotation() *LivenessFMAnnotationPayload {
	if x != nil {
		return x.Annotation
	}
	return nil
}

func (x *LivenessFMAnnotation) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LivenessFMAnnotation) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type LivenessFMAnnotationPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Payload:
	//
	//	*LivenessFMAnnotationPayload_LivenessAnnotation
	//	*LivenessFMAnnotationPayload_FaceMatchAnnotation
	Payload isLivenessFMAnnotationPayload_Payload `protobuf_oneof:"Payload"`
}

func (x *LivenessFMAnnotationPayload) Reset() {
	*x = LivenessFMAnnotationPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_internal_liveness_fm_annotations_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessFMAnnotationPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessFMAnnotationPayload) ProtoMessage() {}

func (x *LivenessFMAnnotationPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_internal_liveness_fm_annotations_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessFMAnnotationPayload.ProtoReflect.Descriptor instead.
func (*LivenessFMAnnotationPayload) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_internal_liveness_fm_annotations_proto_rawDescGZIP(), []int{1}
}

func (m *LivenessFMAnnotationPayload) GetPayload() isLivenessFMAnnotationPayload_Payload {
	if m != nil {
		return m.Payload
	}
	return nil
}

func (x *LivenessFMAnnotationPayload) GetLivenessAnnotation() *Annotation {
	if x, ok := x.GetPayload().(*LivenessFMAnnotationPayload_LivenessAnnotation); ok {
		return x.LivenessAnnotation
	}
	return nil
}

func (x *LivenessFMAnnotationPayload) GetFaceMatchAnnotation() *FaceMatchAnnotation {
	if x, ok := x.GetPayload().(*LivenessFMAnnotationPayload_FaceMatchAnnotation); ok {
		return x.FaceMatchAnnotation
	}
	return nil
}

type isLivenessFMAnnotationPayload_Payload interface {
	isLivenessFMAnnotationPayload_Payload()
}

type LivenessFMAnnotationPayload_LivenessAnnotation struct {
	LivenessAnnotation *Annotation `protobuf:"bytes,1,opt,name=liveness_annotation,json=livenessAnnotation,proto3,oneof"`
}

type LivenessFMAnnotationPayload_FaceMatchAnnotation struct {
	FaceMatchAnnotation *FaceMatchAnnotation `protobuf:"bytes,2,opt,name=face_match_annotation,json=faceMatchAnnotation,proto3,oneof"`
}

func (*LivenessFMAnnotationPayload_LivenessAnnotation) isLivenessFMAnnotationPayload_Payload() {}

func (*LivenessFMAnnotationPayload_FaceMatchAnnotation) isLivenessFMAnnotationPayload_Payload() {}

var File_api_auth_liveness_internal_liveness_fm_annotations_proto protoreflect.FileDescriptor

var file_api_auth_liveness_internal_liveness_fm_annotations_proto_rawDesc = []byte{
	0x0a, 0x38, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x6d, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe2, 0x02, 0x0a, 0x14, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x46, 0x4d, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64,
	0x12, 0x46, 0x0a, 0x0f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x0a, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x4d, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x0a, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xd0, 0x01, 0x0a, 0x1b, 0x4c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x4d, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x4c, 0x0a, 0x13, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x00, 0x52, 0x12, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x58, 0x0a, 0x15, 0x66, 0x61, 0x63, 0x65,
	0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x13, 0x66,
	0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2a, 0x6f, 0x0a,
	0x0e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1f, 0x0a, 0x1b, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1c, 0x0a, 0x18, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x1e,
	0x0a, 0x1a, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x02, 0x42, 0x54,
	0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_liveness_internal_liveness_fm_annotations_proto_rawDescOnce sync.Once
	file_api_auth_liveness_internal_liveness_fm_annotations_proto_rawDescData = file_api_auth_liveness_internal_liveness_fm_annotations_proto_rawDesc
)

func file_api_auth_liveness_internal_liveness_fm_annotations_proto_rawDescGZIP() []byte {
	file_api_auth_liveness_internal_liveness_fm_annotations_proto_rawDescOnce.Do(func() {
		file_api_auth_liveness_internal_liveness_fm_annotations_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_liveness_internal_liveness_fm_annotations_proto_rawDescData)
	})
	return file_api_auth_liveness_internal_liveness_fm_annotations_proto_rawDescData
}

var file_api_auth_liveness_internal_liveness_fm_annotations_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_liveness_internal_liveness_fm_annotations_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_auth_liveness_internal_liveness_fm_annotations_proto_goTypes = []interface{}{
	(AnnotationType)(0),                 // 0: auth.liveness.AnnotationType
	(*LivenessFMAnnotation)(nil),        // 1: auth.liveness.LivenessFMAnnotation
	(*LivenessFMAnnotationPayload)(nil), // 2: auth.liveness.LivenessFMAnnotationPayload
	(*timestamppb.Timestamp)(nil),       // 3: google.protobuf.Timestamp
	(*Annotation)(nil),                  // 4: auth.liveness.Annotation
	(*FaceMatchAnnotation)(nil),         // 5: auth.liveness.FaceMatchAnnotation
}
var file_api_auth_liveness_internal_liveness_fm_annotations_proto_depIdxs = []int32{
	0, // 0: auth.liveness.LivenessFMAnnotation.annotation_type:type_name -> auth.liveness.AnnotationType
	2, // 1: auth.liveness.LivenessFMAnnotation.annotation:type_name -> auth.liveness.LivenessFMAnnotationPayload
	3, // 2: auth.liveness.LivenessFMAnnotation.created_at:type_name -> google.protobuf.Timestamp
	3, // 3: auth.liveness.LivenessFMAnnotation.updated_at:type_name -> google.protobuf.Timestamp
	4, // 4: auth.liveness.LivenessFMAnnotationPayload.liveness_annotation:type_name -> auth.liveness.Annotation
	5, // 5: auth.liveness.LivenessFMAnnotationPayload.face_match_annotation:type_name -> auth.liveness.FaceMatchAnnotation
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_auth_liveness_internal_liveness_fm_annotations_proto_init() }
func file_api_auth_liveness_internal_liveness_fm_annotations_proto_init() {
	if File_api_auth_liveness_internal_liveness_fm_annotations_proto != nil {
		return
	}
	file_api_auth_liveness_internal_face_match_annotation_proto_init()
	file_api_auth_liveness_internal_liveness_attempt_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_auth_liveness_internal_liveness_fm_annotations_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessFMAnnotation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_internal_liveness_fm_annotations_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessFMAnnotationPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_auth_liveness_internal_liveness_fm_annotations_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*LivenessFMAnnotationPayload_LivenessAnnotation)(nil),
		(*LivenessFMAnnotationPayload_FaceMatchAnnotation)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_liveness_internal_liveness_fm_annotations_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_liveness_internal_liveness_fm_annotations_proto_goTypes,
		DependencyIndexes: file_api_auth_liveness_internal_liveness_fm_annotations_proto_depIdxs,
		EnumInfos:         file_api_auth_liveness_internal_liveness_fm_annotations_proto_enumTypes,
		MessageInfos:      file_api_auth_liveness_internal_liveness_fm_annotations_proto_msgTypes,
	}.Build()
	File_api_auth_liveness_internal_liveness_fm_annotations_proto = out.File
	file_api_auth_liveness_internal_liveness_fm_annotations_proto_rawDesc = nil
	file_api_auth_liveness_internal_liveness_fm_annotations_proto_goTypes = nil
	file_api_auth_liveness_internal_liveness_fm_annotations_proto_depIdxs = nil
}
