// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/liveness/internal/liveness_fm_annotations.proto

package liveness

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on LivenessFMAnnotation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LivenessFMAnnotation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessFMAnnotation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessFMAnnotationMultiError, or nil if none found.
func (m *LivenessFMAnnotation) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessFMAnnotation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for ReqId

	// no validation rules for AnnotationType

	if all {
		switch v := interface{}(m.GetAnnotation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessFMAnnotationValidationError{
					field:  "Annotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessFMAnnotationValidationError{
					field:  "Annotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnotation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessFMAnnotationValidationError{
				field:  "Annotation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessFMAnnotationValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessFMAnnotationValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessFMAnnotationValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessFMAnnotationValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessFMAnnotationValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessFMAnnotationValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LivenessFMAnnotationMultiError(errors)
	}

	return nil
}

// LivenessFMAnnotationMultiError is an error wrapping multiple validation
// errors returned by LivenessFMAnnotation.ValidateAll() if the designated
// constraints aren't met.
type LivenessFMAnnotationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessFMAnnotationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessFMAnnotationMultiError) AllErrors() []error { return m }

// LivenessFMAnnotationValidationError is the validation error returned by
// LivenessFMAnnotation.Validate if the designated constraints aren't met.
type LivenessFMAnnotationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessFMAnnotationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessFMAnnotationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessFMAnnotationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessFMAnnotationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessFMAnnotationValidationError) ErrorName() string {
	return "LivenessFMAnnotationValidationError"
}

// Error satisfies the builtin error interface
func (e LivenessFMAnnotationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessFMAnnotation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessFMAnnotationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessFMAnnotationValidationError{}

// Validate checks the field values on LivenessFMAnnotationPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LivenessFMAnnotationPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessFMAnnotationPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessFMAnnotationPayloadMultiError, or nil if none found.
func (m *LivenessFMAnnotationPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessFMAnnotationPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Payload.(type) {
	case *LivenessFMAnnotationPayload_LivenessAnnotation:
		if v == nil {
			err := LivenessFMAnnotationPayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLivenessAnnotation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LivenessFMAnnotationPayloadValidationError{
						field:  "LivenessAnnotation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LivenessFMAnnotationPayloadValidationError{
						field:  "LivenessAnnotation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLivenessAnnotation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LivenessFMAnnotationPayloadValidationError{
					field:  "LivenessAnnotation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LivenessFMAnnotationPayload_FaceMatchAnnotation:
		if v == nil {
			err := LivenessFMAnnotationPayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFaceMatchAnnotation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LivenessFMAnnotationPayloadValidationError{
						field:  "FaceMatchAnnotation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LivenessFMAnnotationPayloadValidationError{
						field:  "FaceMatchAnnotation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFaceMatchAnnotation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LivenessFMAnnotationPayloadValidationError{
					field:  "FaceMatchAnnotation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return LivenessFMAnnotationPayloadMultiError(errors)
	}

	return nil
}

// LivenessFMAnnotationPayloadMultiError is an error wrapping multiple
// validation errors returned by LivenessFMAnnotationPayload.ValidateAll() if
// the designated constraints aren't met.
type LivenessFMAnnotationPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessFMAnnotationPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessFMAnnotationPayloadMultiError) AllErrors() []error { return m }

// LivenessFMAnnotationPayloadValidationError is the validation error returned
// by LivenessFMAnnotationPayload.Validate if the designated constraints
// aren't met.
type LivenessFMAnnotationPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessFMAnnotationPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessFMAnnotationPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessFMAnnotationPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessFMAnnotationPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessFMAnnotationPayloadValidationError) ErrorName() string {
	return "LivenessFMAnnotationPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e LivenessFMAnnotationPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessFMAnnotationPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessFMAnnotationPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessFMAnnotationPayloadValidationError{}
