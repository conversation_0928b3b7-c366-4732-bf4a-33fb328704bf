// Code generated by MockGen. DO NOT EDIT.
// Source: api/auth/liveness/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	liveness "github.com/epifi/gamma/api/auth/liveness"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

// MockLivenessClient is a mock of LivenessClient interface.
type MockLivenessClient struct {
	ctrl     *gomock.Controller
	recorder *MockLivenessClientMockRecorder
}

// MockLivenessClientMockRecorder is the mock recorder for MockLivenessClient.
type MockLivenessClientMockRecorder struct {
	mock *MockLivenessClient
}

// NewMockLivenessClient creates a new mock instance.
func NewMockLivenessClient(ctrl *gomock.Controller) *MockLivenessClient {
	mock := &MockLivenessClient{ctrl: ctrl}
	mock.recorder = &MockLivenessClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLivenessClient) EXPECT() *MockLivenessClientMockRecorder {
	return m.recorder
}

// AnnotateFacematchAttempt mocks base method.
func (m *MockLivenessClient) AnnotateFacematchAttempt(ctx context.Context, in *liveness.AnnotateFacematchAttemptRequest, opts ...grpc.CallOption) (*liveness.AnnotateFacematchAttemptResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AnnotateFacematchAttempt", varargs...)
	ret0, _ := ret[0].(*liveness.AnnotateFacematchAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnnotateFacematchAttempt indicates an expected call of AnnotateFacematchAttempt.
func (mr *MockLivenessClientMockRecorder) AnnotateFacematchAttempt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnnotateFacematchAttempt", reflect.TypeOf((*MockLivenessClient)(nil).AnnotateFacematchAttempt), varargs...)
}

// AnnotateLivenessAttempt mocks base method.
func (m *MockLivenessClient) AnnotateLivenessAttempt(ctx context.Context, in *liveness.AnnotateLivenessAttemptRequest, opts ...grpc.CallOption) (*liveness.AnnotateLivenessAttemptResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AnnotateLivenessAttempt", varargs...)
	ret0, _ := ret[0].(*liveness.AnnotateLivenessAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnnotateLivenessAttempt indicates an expected call of AnnotateLivenessAttempt.
func (mr *MockLivenessClientMockRecorder) AnnotateLivenessAttempt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnnotateLivenessAttempt", reflect.TypeOf((*MockLivenessClient)(nil).AnnotateLivenessAttempt), varargs...)
}

// CheckLivenessWithVideoStream mocks base method.
func (m *MockLivenessClient) CheckLivenessWithVideoStream(ctx context.Context, opts ...grpc.CallOption) (liveness.Liveness_CheckLivenessWithVideoStreamClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckLivenessWithVideoStream", varargs...)
	ret0, _ := ret[0].(liveness.Liveness_CheckLivenessWithVideoStreamClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckLivenessWithVideoStream indicates an expected call of CheckLivenessWithVideoStream.
func (mr *MockLivenessClientMockRecorder) CheckLivenessWithVideoStream(ctx interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckLivenessWithVideoStream", reflect.TypeOf((*MockLivenessClient)(nil).CheckLivenessWithVideoStream), varargs...)
}

// CreateLivenessAttempt mocks base method.
func (m *MockLivenessClient) CreateLivenessAttempt(ctx context.Context, in *liveness.CreateLivenessAttemptRequest, opts ...grpc.CallOption) (*liveness.CreateLivenessAttemptResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateLivenessAttempt", varargs...)
	ret0, _ := ret[0].(*liveness.CreateLivenessAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLivenessAttempt indicates an expected call of CreateLivenessAttempt.
func (mr *MockLivenessClientMockRecorder) CreateLivenessAttempt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLivenessAttempt", reflect.TypeOf((*MockLivenessClient)(nil).CreateLivenessAttempt), varargs...)
}

// CreateLivenessSummary mocks base method.
func (m *MockLivenessClient) CreateLivenessSummary(ctx context.Context, in *liveness.CreateLivenessSummaryRequest, opts ...grpc.CallOption) (*liveness.CreateLivenessSummaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateLivenessSummary", varargs...)
	ret0, _ := ret[0].(*liveness.CreateLivenessSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLivenessSummary indicates an expected call of CreateLivenessSummary.
func (mr *MockLivenessClientMockRecorder) CreateLivenessSummary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLivenessSummary", reflect.TypeOf((*MockLivenessClient)(nil).CreateLivenessSummary), varargs...)
}

// FaceMatch mocks base method.
func (m *MockLivenessClient) FaceMatch(ctx context.Context, in *liveness.FaceMatchRequest, opts ...grpc.CallOption) (*liveness.FaceMatchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FaceMatch", varargs...)
	ret0, _ := ret[0].(*liveness.FaceMatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FaceMatch indicates an expected call of FaceMatch.
func (mr *MockLivenessClientMockRecorder) FaceMatch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FaceMatch", reflect.TypeOf((*MockLivenessClient)(nil).FaceMatch), varargs...)
}

// GenerateOTP mocks base method.
func (m *MockLivenessClient) GenerateOTP(ctx context.Context, in *liveness.GenerateOTPRequest, opts ...grpc.CallOption) (*liveness.GenerateOTPResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateOTP", varargs...)
	ret0, _ := ret[0].(*liveness.GenerateOTPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateOTP indicates an expected call of GenerateOTP.
func (mr *MockLivenessClientMockRecorder) GenerateOTP(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateOTP", reflect.TypeOf((*MockLivenessClient)(nil).GenerateOTP), varargs...)
}

// GetFaceMatchAttempts mocks base method.
func (m *MockLivenessClient) GetFaceMatchAttempts(ctx context.Context, in *liveness.GetFaceMatchAttemptsRequest, opts ...grpc.CallOption) (*liveness.GetFaceMatchAttemptsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFaceMatchAttempts", varargs...)
	ret0, _ := ret[0].(*liveness.GetFaceMatchAttemptsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFaceMatchAttempts indicates an expected call of GetFaceMatchAttempts.
func (mr *MockLivenessClientMockRecorder) GetFaceMatchAttempts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFaceMatchAttempts", reflect.TypeOf((*MockLivenessClient)(nil).GetFaceMatchAttempts), varargs...)
}

// GetFaceMatchStatus mocks base method.
func (m *MockLivenessClient) GetFaceMatchStatus(ctx context.Context, in *liveness.GetFaceMatchStatusRequest, opts ...grpc.CallOption) (*liveness.GetFaceMatchStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFaceMatchStatus", varargs...)
	ret0, _ := ret[0].(*liveness.GetFaceMatchStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFaceMatchStatus indicates an expected call of GetFaceMatchStatus.
func (mr *MockLivenessClientMockRecorder) GetFaceMatchStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFaceMatchStatus", reflect.TypeOf((*MockLivenessClient)(nil).GetFaceMatchStatus), varargs...)
}

// GetLivenessAttempt mocks base method.
func (m *MockLivenessClient) GetLivenessAttempt(ctx context.Context, in *liveness.GetLivenessAttemptRequest, opts ...grpc.CallOption) (*liveness.GetLivenessAttemptResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLivenessAttempt", varargs...)
	ret0, _ := ret[0].(*liveness.GetLivenessAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivenessAttempt indicates an expected call of GetLivenessAttempt.
func (mr *MockLivenessClientMockRecorder) GetLivenessAttempt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivenessAttempt", reflect.TypeOf((*MockLivenessClient)(nil).GetLivenessAttempt), varargs...)
}

// GetLivenessAttempts mocks base method.
func (m *MockLivenessClient) GetLivenessAttempts(ctx context.Context, in *liveness.GetLivenessAttemptsRequest, opts ...grpc.CallOption) (*liveness.GetLivenessAttemptsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLivenessAttempts", varargs...)
	ret0, _ := ret[0].(*liveness.GetLivenessAttemptsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivenessAttempts indicates an expected call of GetLivenessAttempts.
func (mr *MockLivenessClientMockRecorder) GetLivenessAttempts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivenessAttempts", reflect.TypeOf((*MockLivenessClient)(nil).GetLivenessAttempts), varargs...)
}

// GetLivenessStatus mocks base method.
func (m *MockLivenessClient) GetLivenessStatus(ctx context.Context, in *liveness.GetLivenessStatusRequest, opts ...grpc.CallOption) (*liveness.GetLivenessStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLivenessStatus", varargs...)
	ret0, _ := ret[0].(*liveness.GetLivenessStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivenessStatus indicates an expected call of GetLivenessStatus.
func (mr *MockLivenessClientMockRecorder) GetLivenessStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivenessStatus", reflect.TypeOf((*MockLivenessClient)(nil).GetLivenessStatus), varargs...)
}

// GetLivenessSummary mocks base method.
func (m *MockLivenessClient) GetLivenessSummary(ctx context.Context, in *liveness.GetLivenessSummaryRequest, opts ...grpc.CallOption) (*liveness.GetLivenessSummaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLivenessSummary", varargs...)
	ret0, _ := ret[0].(*liveness.GetLivenessSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivenessSummary indicates an expected call of GetLivenessSummary.
func (mr *MockLivenessClientMockRecorder) GetLivenessSummary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivenessSummary", reflect.TypeOf((*MockLivenessClient)(nil).GetLivenessSummary), varargs...)
}

// GetLivenessSummaryStatus mocks base method.
func (m *MockLivenessClient) GetLivenessSummaryStatus(ctx context.Context, in *liveness.GetLivenessSummaryStatusRequest, opts ...grpc.CallOption) (*liveness.GetLivenessSummaryStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLivenessSummaryStatus", varargs...)
	ret0, _ := ret[0].(*liveness.GetLivenessSummaryStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivenessSummaryStatus indicates an expected call of GetLivenessSummaryStatus.
func (mr *MockLivenessClientMockRecorder) GetLivenessSummaryStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivenessSummaryStatus", reflect.TypeOf((*MockLivenessClient)(nil).GetLivenessSummaryStatus), varargs...)
}

// GetS3Image mocks base method.
func (m *MockLivenessClient) GetS3Image(ctx context.Context, in *liveness.GetS3ImageRequest, opts ...grpc.CallOption) (*liveness.GetS3ImageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetS3Image", varargs...)
	ret0, _ := ret[0].(*liveness.GetS3ImageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetS3Image indicates an expected call of GetS3Image.
func (mr *MockLivenessClientMockRecorder) GetS3Image(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetS3Image", reflect.TypeOf((*MockLivenessClient)(nil).GetS3Image), varargs...)
}

// RedoFaceMatch mocks base method.
func (m *MockLivenessClient) RedoFaceMatch(ctx context.Context, in *liveness.RedoFaceMatchRequest, opts ...grpc.CallOption) (*liveness.RedoFaceMatchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RedoFaceMatch", varargs...)
	ret0, _ := ret[0].(*liveness.RedoFaceMatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RedoFaceMatch indicates an expected call of RedoFaceMatch.
func (mr *MockLivenessClientMockRecorder) RedoFaceMatch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedoFaceMatch", reflect.TypeOf((*MockLivenessClient)(nil).RedoFaceMatch), varargs...)
}

// RetryLiveness mocks base method.
func (m *MockLivenessClient) RetryLiveness(ctx context.Context, in *liveness.RetryLivenessRequest, opts ...grpc.CallOption) (*liveness.RetryLivenessResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RetryLiveness", varargs...)
	ret0, _ := ret[0].(*liveness.RetryLivenessResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetryLiveness indicates an expected call of RetryLiveness.
func (mr *MockLivenessClientMockRecorder) RetryLiveness(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetryLiveness", reflect.TypeOf((*MockLivenessClient)(nil).RetryLiveness), varargs...)
}

// MockLiveness_CheckLivenessWithVideoStreamClient is a mock of Liveness_CheckLivenessWithVideoStreamClient interface.
type MockLiveness_CheckLivenessWithVideoStreamClient struct {
	ctrl     *gomock.Controller
	recorder *MockLiveness_CheckLivenessWithVideoStreamClientMockRecorder
}

// MockLiveness_CheckLivenessWithVideoStreamClientMockRecorder is the mock recorder for MockLiveness_CheckLivenessWithVideoStreamClient.
type MockLiveness_CheckLivenessWithVideoStreamClientMockRecorder struct {
	mock *MockLiveness_CheckLivenessWithVideoStreamClient
}

// NewMockLiveness_CheckLivenessWithVideoStreamClient creates a new mock instance.
func NewMockLiveness_CheckLivenessWithVideoStreamClient(ctrl *gomock.Controller) *MockLiveness_CheckLivenessWithVideoStreamClient {
	mock := &MockLiveness_CheckLivenessWithVideoStreamClient{ctrl: ctrl}
	mock.recorder = &MockLiveness_CheckLivenessWithVideoStreamClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLiveness_CheckLivenessWithVideoStreamClient) EXPECT() *MockLiveness_CheckLivenessWithVideoStreamClientMockRecorder {
	return m.recorder
}

// CloseAndRecv mocks base method.
func (m *MockLiveness_CheckLivenessWithVideoStreamClient) CloseAndRecv() (*liveness.CheckLivenessWithVideoStreamResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseAndRecv")
	ret0, _ := ret[0].(*liveness.CheckLivenessWithVideoStreamResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CloseAndRecv indicates an expected call of CloseAndRecv.
func (mr *MockLiveness_CheckLivenessWithVideoStreamClientMockRecorder) CloseAndRecv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseAndRecv", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamClient)(nil).CloseAndRecv))
}

// CloseSend mocks base method.
func (m *MockLiveness_CheckLivenessWithVideoStreamClient) CloseSend() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseSend")
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseSend indicates an expected call of CloseSend.
func (mr *MockLiveness_CheckLivenessWithVideoStreamClientMockRecorder) CloseSend() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseSend", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamClient)(nil).CloseSend))
}

// Context mocks base method.
func (m *MockLiveness_CheckLivenessWithVideoStreamClient) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockLiveness_CheckLivenessWithVideoStreamClientMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamClient)(nil).Context))
}

// Header mocks base method.
func (m *MockLiveness_CheckLivenessWithVideoStreamClient) Header() (metadata.MD, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Header")
	ret0, _ := ret[0].(metadata.MD)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Header indicates an expected call of Header.
func (mr *MockLiveness_CheckLivenessWithVideoStreamClientMockRecorder) Header() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Header", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamClient)(nil).Header))
}

// RecvMsg mocks base method.
func (m_2 *MockLiveness_CheckLivenessWithVideoStreamClient) RecvMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "RecvMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockLiveness_CheckLivenessWithVideoStreamClientMockRecorder) RecvMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamClient)(nil).RecvMsg), m)
}

// Send mocks base method.
func (m *MockLiveness_CheckLivenessWithVideoStreamClient) Send(arg0 *liveness.CheckLivenessWithVideoStreamRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Send indicates an expected call of Send.
func (mr *MockLiveness_CheckLivenessWithVideoStreamClientMockRecorder) Send(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamClient)(nil).Send), arg0)
}

// SendMsg mocks base method.
func (m_2 *MockLiveness_CheckLivenessWithVideoStreamClient) SendMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "SendMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockLiveness_CheckLivenessWithVideoStreamClientMockRecorder) SendMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamClient)(nil).SendMsg), m)
}

// Trailer mocks base method.
func (m *MockLiveness_CheckLivenessWithVideoStreamClient) Trailer() metadata.MD {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Trailer")
	ret0, _ := ret[0].(metadata.MD)
	return ret0
}

// Trailer indicates an expected call of Trailer.
func (mr *MockLiveness_CheckLivenessWithVideoStreamClientMockRecorder) Trailer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Trailer", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamClient)(nil).Trailer))
}

// MockLivenessServer is a mock of LivenessServer interface.
type MockLivenessServer struct {
	ctrl     *gomock.Controller
	recorder *MockLivenessServerMockRecorder
}

// MockLivenessServerMockRecorder is the mock recorder for MockLivenessServer.
type MockLivenessServerMockRecorder struct {
	mock *MockLivenessServer
}

// NewMockLivenessServer creates a new mock instance.
func NewMockLivenessServer(ctrl *gomock.Controller) *MockLivenessServer {
	mock := &MockLivenessServer{ctrl: ctrl}
	mock.recorder = &MockLivenessServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLivenessServer) EXPECT() *MockLivenessServerMockRecorder {
	return m.recorder
}

// AnnotateFacematchAttempt mocks base method.
func (m *MockLivenessServer) AnnotateFacematchAttempt(arg0 context.Context, arg1 *liveness.AnnotateFacematchAttemptRequest) (*liveness.AnnotateFacematchAttemptResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnnotateFacematchAttempt", arg0, arg1)
	ret0, _ := ret[0].(*liveness.AnnotateFacematchAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnnotateFacematchAttempt indicates an expected call of AnnotateFacematchAttempt.
func (mr *MockLivenessServerMockRecorder) AnnotateFacematchAttempt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnnotateFacematchAttempt", reflect.TypeOf((*MockLivenessServer)(nil).AnnotateFacematchAttempt), arg0, arg1)
}

// AnnotateLivenessAttempt mocks base method.
func (m *MockLivenessServer) AnnotateLivenessAttempt(arg0 context.Context, arg1 *liveness.AnnotateLivenessAttemptRequest) (*liveness.AnnotateLivenessAttemptResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnnotateLivenessAttempt", arg0, arg1)
	ret0, _ := ret[0].(*liveness.AnnotateLivenessAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnnotateLivenessAttempt indicates an expected call of AnnotateLivenessAttempt.
func (mr *MockLivenessServerMockRecorder) AnnotateLivenessAttempt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnnotateLivenessAttempt", reflect.TypeOf((*MockLivenessServer)(nil).AnnotateLivenessAttempt), arg0, arg1)
}

// CheckLivenessWithVideoStream mocks base method.
func (m *MockLivenessServer) CheckLivenessWithVideoStream(arg0 liveness.Liveness_CheckLivenessWithVideoStreamServer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckLivenessWithVideoStream", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckLivenessWithVideoStream indicates an expected call of CheckLivenessWithVideoStream.
func (mr *MockLivenessServerMockRecorder) CheckLivenessWithVideoStream(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckLivenessWithVideoStream", reflect.TypeOf((*MockLivenessServer)(nil).CheckLivenessWithVideoStream), arg0)
}

// CreateLivenessAttempt mocks base method.
func (m *MockLivenessServer) CreateLivenessAttempt(arg0 context.Context, arg1 *liveness.CreateLivenessAttemptRequest) (*liveness.CreateLivenessAttemptResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLivenessAttempt", arg0, arg1)
	ret0, _ := ret[0].(*liveness.CreateLivenessAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLivenessAttempt indicates an expected call of CreateLivenessAttempt.
func (mr *MockLivenessServerMockRecorder) CreateLivenessAttempt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLivenessAttempt", reflect.TypeOf((*MockLivenessServer)(nil).CreateLivenessAttempt), arg0, arg1)
}

// CreateLivenessSummary mocks base method.
func (m *MockLivenessServer) CreateLivenessSummary(arg0 context.Context, arg1 *liveness.CreateLivenessSummaryRequest) (*liveness.CreateLivenessSummaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLivenessSummary", arg0, arg1)
	ret0, _ := ret[0].(*liveness.CreateLivenessSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLivenessSummary indicates an expected call of CreateLivenessSummary.
func (mr *MockLivenessServerMockRecorder) CreateLivenessSummary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLivenessSummary", reflect.TypeOf((*MockLivenessServer)(nil).CreateLivenessSummary), arg0, arg1)
}

// FaceMatch mocks base method.
func (m *MockLivenessServer) FaceMatch(arg0 context.Context, arg1 *liveness.FaceMatchRequest) (*liveness.FaceMatchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FaceMatch", arg0, arg1)
	ret0, _ := ret[0].(*liveness.FaceMatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FaceMatch indicates an expected call of FaceMatch.
func (mr *MockLivenessServerMockRecorder) FaceMatch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FaceMatch", reflect.TypeOf((*MockLivenessServer)(nil).FaceMatch), arg0, arg1)
}

// GenerateOTP mocks base method.
func (m *MockLivenessServer) GenerateOTP(arg0 context.Context, arg1 *liveness.GenerateOTPRequest) (*liveness.GenerateOTPResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateOTP", arg0, arg1)
	ret0, _ := ret[0].(*liveness.GenerateOTPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateOTP indicates an expected call of GenerateOTP.
func (mr *MockLivenessServerMockRecorder) GenerateOTP(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateOTP", reflect.TypeOf((*MockLivenessServer)(nil).GenerateOTP), arg0, arg1)
}

// GetFaceMatchAttempts mocks base method.
func (m *MockLivenessServer) GetFaceMatchAttempts(arg0 context.Context, arg1 *liveness.GetFaceMatchAttemptsRequest) (*liveness.GetFaceMatchAttemptsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFaceMatchAttempts", arg0, arg1)
	ret0, _ := ret[0].(*liveness.GetFaceMatchAttemptsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFaceMatchAttempts indicates an expected call of GetFaceMatchAttempts.
func (mr *MockLivenessServerMockRecorder) GetFaceMatchAttempts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFaceMatchAttempts", reflect.TypeOf((*MockLivenessServer)(nil).GetFaceMatchAttempts), arg0, arg1)
}

// GetFaceMatchStatus mocks base method.
func (m *MockLivenessServer) GetFaceMatchStatus(arg0 context.Context, arg1 *liveness.GetFaceMatchStatusRequest) (*liveness.GetFaceMatchStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFaceMatchStatus", arg0, arg1)
	ret0, _ := ret[0].(*liveness.GetFaceMatchStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFaceMatchStatus indicates an expected call of GetFaceMatchStatus.
func (mr *MockLivenessServerMockRecorder) GetFaceMatchStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFaceMatchStatus", reflect.TypeOf((*MockLivenessServer)(nil).GetFaceMatchStatus), arg0, arg1)
}

// GetLivenessAttempt mocks base method.
func (m *MockLivenessServer) GetLivenessAttempt(arg0 context.Context, arg1 *liveness.GetLivenessAttemptRequest) (*liveness.GetLivenessAttemptResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLivenessAttempt", arg0, arg1)
	ret0, _ := ret[0].(*liveness.GetLivenessAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivenessAttempt indicates an expected call of GetLivenessAttempt.
func (mr *MockLivenessServerMockRecorder) GetLivenessAttempt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivenessAttempt", reflect.TypeOf((*MockLivenessServer)(nil).GetLivenessAttempt), arg0, arg1)
}

// GetLivenessAttempts mocks base method.
func (m *MockLivenessServer) GetLivenessAttempts(arg0 context.Context, arg1 *liveness.GetLivenessAttemptsRequest) (*liveness.GetLivenessAttemptsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLivenessAttempts", arg0, arg1)
	ret0, _ := ret[0].(*liveness.GetLivenessAttemptsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivenessAttempts indicates an expected call of GetLivenessAttempts.
func (mr *MockLivenessServerMockRecorder) GetLivenessAttempts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivenessAttempts", reflect.TypeOf((*MockLivenessServer)(nil).GetLivenessAttempts), arg0, arg1)
}

// GetLivenessStatus mocks base method.
func (m *MockLivenessServer) GetLivenessStatus(arg0 context.Context, arg1 *liveness.GetLivenessStatusRequest) (*liveness.GetLivenessStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLivenessStatus", arg0, arg1)
	ret0, _ := ret[0].(*liveness.GetLivenessStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivenessStatus indicates an expected call of GetLivenessStatus.
func (mr *MockLivenessServerMockRecorder) GetLivenessStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivenessStatus", reflect.TypeOf((*MockLivenessServer)(nil).GetLivenessStatus), arg0, arg1)
}

// GetLivenessSummary mocks base method.
func (m *MockLivenessServer) GetLivenessSummary(arg0 context.Context, arg1 *liveness.GetLivenessSummaryRequest) (*liveness.GetLivenessSummaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLivenessSummary", arg0, arg1)
	ret0, _ := ret[0].(*liveness.GetLivenessSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivenessSummary indicates an expected call of GetLivenessSummary.
func (mr *MockLivenessServerMockRecorder) GetLivenessSummary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivenessSummary", reflect.TypeOf((*MockLivenessServer)(nil).GetLivenessSummary), arg0, arg1)
}

// GetLivenessSummaryStatus mocks base method.
func (m *MockLivenessServer) GetLivenessSummaryStatus(arg0 context.Context, arg1 *liveness.GetLivenessSummaryStatusRequest) (*liveness.GetLivenessSummaryStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLivenessSummaryStatus", arg0, arg1)
	ret0, _ := ret[0].(*liveness.GetLivenessSummaryStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivenessSummaryStatus indicates an expected call of GetLivenessSummaryStatus.
func (mr *MockLivenessServerMockRecorder) GetLivenessSummaryStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivenessSummaryStatus", reflect.TypeOf((*MockLivenessServer)(nil).GetLivenessSummaryStatus), arg0, arg1)
}

// GetS3Image mocks base method.
func (m *MockLivenessServer) GetS3Image(arg0 context.Context, arg1 *liveness.GetS3ImageRequest) (*liveness.GetS3ImageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetS3Image", arg0, arg1)
	ret0, _ := ret[0].(*liveness.GetS3ImageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetS3Image indicates an expected call of GetS3Image.
func (mr *MockLivenessServerMockRecorder) GetS3Image(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetS3Image", reflect.TypeOf((*MockLivenessServer)(nil).GetS3Image), arg0, arg1)
}

// RedoFaceMatch mocks base method.
func (m *MockLivenessServer) RedoFaceMatch(arg0 context.Context, arg1 *liveness.RedoFaceMatchRequest) (*liveness.RedoFaceMatchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RedoFaceMatch", arg0, arg1)
	ret0, _ := ret[0].(*liveness.RedoFaceMatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RedoFaceMatch indicates an expected call of RedoFaceMatch.
func (mr *MockLivenessServerMockRecorder) RedoFaceMatch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedoFaceMatch", reflect.TypeOf((*MockLivenessServer)(nil).RedoFaceMatch), arg0, arg1)
}

// RetryLiveness mocks base method.
func (m *MockLivenessServer) RetryLiveness(arg0 context.Context, arg1 *liveness.RetryLivenessRequest) (*liveness.RetryLivenessResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetryLiveness", arg0, arg1)
	ret0, _ := ret[0].(*liveness.RetryLivenessResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetryLiveness indicates an expected call of RetryLiveness.
func (mr *MockLivenessServerMockRecorder) RetryLiveness(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetryLiveness", reflect.TypeOf((*MockLivenessServer)(nil).RetryLiveness), arg0, arg1)
}

// MockUnsafeLivenessServer is a mock of UnsafeLivenessServer interface.
type MockUnsafeLivenessServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeLivenessServerMockRecorder
}

// MockUnsafeLivenessServerMockRecorder is the mock recorder for MockUnsafeLivenessServer.
type MockUnsafeLivenessServerMockRecorder struct {
	mock *MockUnsafeLivenessServer
}

// NewMockUnsafeLivenessServer creates a new mock instance.
func NewMockUnsafeLivenessServer(ctrl *gomock.Controller) *MockUnsafeLivenessServer {
	mock := &MockUnsafeLivenessServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeLivenessServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeLivenessServer) EXPECT() *MockUnsafeLivenessServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedLivenessServer mocks base method.
func (m *MockUnsafeLivenessServer) mustEmbedUnimplementedLivenessServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedLivenessServer")
}

// mustEmbedUnimplementedLivenessServer indicates an expected call of mustEmbedUnimplementedLivenessServer.
func (mr *MockUnsafeLivenessServerMockRecorder) mustEmbedUnimplementedLivenessServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedLivenessServer", reflect.TypeOf((*MockUnsafeLivenessServer)(nil).mustEmbedUnimplementedLivenessServer))
}

// MockLiveness_CheckLivenessWithVideoStreamServer is a mock of Liveness_CheckLivenessWithVideoStreamServer interface.
type MockLiveness_CheckLivenessWithVideoStreamServer struct {
	ctrl     *gomock.Controller
	recorder *MockLiveness_CheckLivenessWithVideoStreamServerMockRecorder
}

// MockLiveness_CheckLivenessWithVideoStreamServerMockRecorder is the mock recorder for MockLiveness_CheckLivenessWithVideoStreamServer.
type MockLiveness_CheckLivenessWithVideoStreamServerMockRecorder struct {
	mock *MockLiveness_CheckLivenessWithVideoStreamServer
}

// NewMockLiveness_CheckLivenessWithVideoStreamServer creates a new mock instance.
func NewMockLiveness_CheckLivenessWithVideoStreamServer(ctrl *gomock.Controller) *MockLiveness_CheckLivenessWithVideoStreamServer {
	mock := &MockLiveness_CheckLivenessWithVideoStreamServer{ctrl: ctrl}
	mock.recorder = &MockLiveness_CheckLivenessWithVideoStreamServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLiveness_CheckLivenessWithVideoStreamServer) EXPECT() *MockLiveness_CheckLivenessWithVideoStreamServerMockRecorder {
	return m.recorder
}

// Context mocks base method.
func (m *MockLiveness_CheckLivenessWithVideoStreamServer) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockLiveness_CheckLivenessWithVideoStreamServerMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamServer)(nil).Context))
}

// Recv mocks base method.
func (m *MockLiveness_CheckLivenessWithVideoStreamServer) Recv() (*liveness.CheckLivenessWithVideoStreamRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Recv")
	ret0, _ := ret[0].(*liveness.CheckLivenessWithVideoStreamRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Recv indicates an expected call of Recv.
func (mr *MockLiveness_CheckLivenessWithVideoStreamServerMockRecorder) Recv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Recv", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamServer)(nil).Recv))
}

// RecvMsg mocks base method.
func (m_2 *MockLiveness_CheckLivenessWithVideoStreamServer) RecvMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "RecvMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockLiveness_CheckLivenessWithVideoStreamServerMockRecorder) RecvMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamServer)(nil).RecvMsg), m)
}

// SendAndClose mocks base method.
func (m *MockLiveness_CheckLivenessWithVideoStreamServer) SendAndClose(arg0 *liveness.CheckLivenessWithVideoStreamResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendAndClose", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendAndClose indicates an expected call of SendAndClose.
func (mr *MockLiveness_CheckLivenessWithVideoStreamServerMockRecorder) SendAndClose(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAndClose", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamServer)(nil).SendAndClose), arg0)
}

// SendHeader mocks base method.
func (m *MockLiveness_CheckLivenessWithVideoStreamServer) SendHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendHeader indicates an expected call of SendHeader.
func (mr *MockLiveness_CheckLivenessWithVideoStreamServerMockRecorder) SendHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendHeader", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamServer)(nil).SendHeader), arg0)
}

// SendMsg mocks base method.
func (m_2 *MockLiveness_CheckLivenessWithVideoStreamServer) SendMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "SendMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockLiveness_CheckLivenessWithVideoStreamServerMockRecorder) SendMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamServer)(nil).SendMsg), m)
}

// SetHeader mocks base method.
func (m *MockLiveness_CheckLivenessWithVideoStreamServer) SetHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetHeader indicates an expected call of SetHeader.
func (mr *MockLiveness_CheckLivenessWithVideoStreamServerMockRecorder) SetHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHeader", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamServer)(nil).SetHeader), arg0)
}

// SetTrailer mocks base method.
func (m *MockLiveness_CheckLivenessWithVideoStreamServer) SetTrailer(arg0 metadata.MD) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetTrailer", arg0)
}

// SetTrailer indicates an expected call of SetTrailer.
func (mr *MockLiveness_CheckLivenessWithVideoStreamServerMockRecorder) SetTrailer(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTrailer", reflect.TypeOf((*MockLiveness_CheckLivenessWithVideoStreamServer)(nil).SetTrailer), arg0)
}
