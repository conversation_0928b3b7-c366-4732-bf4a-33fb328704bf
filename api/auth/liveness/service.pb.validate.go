// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/liveness/service.proto

package liveness

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)
)

// Validate checks the field values on Frames with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Frames) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Frames with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in FramesMultiError, or nil if none found.
func (m *Frames) ValidateAll() error {
	return m.validate(true)
}

func (m *Frames) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return FramesMultiError(errors)
	}

	return nil
}

// FramesMultiError is an error wrapping multiple validation errors returned by
// Frames.ValidateAll() if the designated constraints aren't met.
type FramesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FramesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FramesMultiError) AllErrors() []error { return m }

// FramesValidationError is the validation error returned by Frames.Validate if
// the designated constraints aren't met.
type FramesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FramesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FramesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FramesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FramesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FramesValidationError) ErrorName() string { return "FramesValidationError" }

// Error satisfies the builtin error interface
func (e FramesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFrames.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FramesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FramesValidationError{}

// Validate checks the field values on GetLivenessStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLivenessStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLivenessStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLivenessStatusRequestMultiError, or nil if none found.
func (m *GetLivenessStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLivenessStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AttemptId

	if len(errors) > 0 {
		return GetLivenessStatusRequestMultiError(errors)
	}

	return nil
}

// GetLivenessStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetLivenessStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLivenessStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLivenessStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLivenessStatusRequestMultiError) AllErrors() []error { return m }

// GetLivenessStatusRequestValidationError is the validation error returned by
// GetLivenessStatusRequest.Validate if the designated constraints aren't met.
type GetLivenessStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLivenessStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLivenessStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLivenessStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLivenessStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLivenessStatusRequestValidationError) ErrorName() string {
	return "GetLivenessStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLivenessStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLivenessStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLivenessStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLivenessStatusRequestValidationError{}

// Validate checks the field values on GetLivenessStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLivenessStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLivenessStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLivenessStatusResponseMultiError, or nil if none found.
func (m *GetLivenessStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLivenessStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LivenessStatus

	// no validation rules for VideoLocation

	if all {
		switch v := interface{}(m.GetFrame()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessStatusResponseValidationError{
					field:  "Frame",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessStatusResponseValidationError{
					field:  "Frame",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFrame()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessStatusResponseValidationError{
				field:  "Frame",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Otp

	// no validation rules for LivenessScore

	// no validation rules for OtpScore

	// no validation rules for LivenessScoreThreshold

	// no validation rules for OtpScoreThreshold

	if len(errors) > 0 {
		return GetLivenessStatusResponseMultiError(errors)
	}

	return nil
}

// GetLivenessStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetLivenessStatusResponse.ValidateAll() if the
// designated constraints aren't met.
type GetLivenessStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLivenessStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLivenessStatusResponseMultiError) AllErrors() []error { return m }

// GetLivenessStatusResponseValidationError is the validation error returned by
// GetLivenessStatusResponse.Validate if the designated constraints aren't met.
type GetLivenessStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLivenessStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLivenessStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLivenessStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLivenessStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLivenessStatusResponseValidationError) ErrorName() string {
	return "GetLivenessStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLivenessStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLivenessStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLivenessStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLivenessStatusResponseValidationError{}

// Validate checks the field values on FaceMatchRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FaceMatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FaceMatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FaceMatchRequestMultiError, or nil if none found.
func (m *FaceMatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FaceMatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AttemptId

	// no validation rules for FaceMatchLevel

	// no validation rules for Sync

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FaceMatchRequestValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FaceMatchRequestValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FaceMatchRequestValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImageFrame()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FaceMatchRequestValidationError{
					field:  "ImageFrame",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FaceMatchRequestValidationError{
					field:  "ImageFrame",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImageFrame()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FaceMatchRequestValidationError{
				field:  "ImageFrame",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VideoLocation

	// no validation rules for SummaryId

	// no validation rules for Threshold

	// no validation rules for StrictnessLogic

	// no validation rules for LivenessRequestId

	// no validation rules for IsSourceVideo

	if len(errors) > 0 {
		return FaceMatchRequestMultiError(errors)
	}

	return nil
}

// FaceMatchRequestMultiError is an error wrapping multiple validation errors
// returned by FaceMatchRequest.ValidateAll() if the designated constraints
// aren't met.
type FaceMatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FaceMatchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FaceMatchRequestMultiError) AllErrors() []error { return m }

// FaceMatchRequestValidationError is the validation error returned by
// FaceMatchRequest.Validate if the designated constraints aren't met.
type FaceMatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FaceMatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FaceMatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FaceMatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FaceMatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FaceMatchRequestValidationError) ErrorName() string { return "FaceMatchRequestValidationError" }

// Error satisfies the builtin error interface
func (e FaceMatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFaceMatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FaceMatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FaceMatchRequestValidationError{}

// Validate checks the field values on FaceMatchResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FaceMatchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FaceMatchResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FaceMatchResponseMultiError, or nil if none found.
func (m *FaceMatchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FaceMatchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FaceMatchResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FaceMatchResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FaceMatchResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FaceMatchStatus

	// no validation rules for AttemptId

	if len(errors) > 0 {
		return FaceMatchResponseMultiError(errors)
	}

	return nil
}

// FaceMatchResponseMultiError is an error wrapping multiple validation errors
// returned by FaceMatchResponse.ValidateAll() if the designated constraints
// aren't met.
type FaceMatchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FaceMatchResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FaceMatchResponseMultiError) AllErrors() []error { return m }

// FaceMatchResponseValidationError is the validation error returned by
// FaceMatchResponse.Validate if the designated constraints aren't met.
type FaceMatchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FaceMatchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FaceMatchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FaceMatchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FaceMatchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FaceMatchResponseValidationError) ErrorName() string {
	return "FaceMatchResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FaceMatchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFaceMatchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FaceMatchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FaceMatchResponseValidationError{}

// Validate checks the field values on GetFaceMatchStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFaceMatchStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFaceMatchStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFaceMatchStatusRequestMultiError, or nil if none found.
func (m *GetFaceMatchStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFaceMatchStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AttemptId

	if len(errors) > 0 {
		return GetFaceMatchStatusRequestMultiError(errors)
	}

	return nil
}

// GetFaceMatchStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetFaceMatchStatusRequest.ValidateAll() if the
// designated constraints aren't met.
type GetFaceMatchStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFaceMatchStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFaceMatchStatusRequestMultiError) AllErrors() []error { return m }

// GetFaceMatchStatusRequestValidationError is the validation error returned by
// GetFaceMatchStatusRequest.Validate if the designated constraints aren't met.
type GetFaceMatchStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFaceMatchStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFaceMatchStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFaceMatchStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFaceMatchStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFaceMatchStatusRequestValidationError) ErrorName() string {
	return "GetFaceMatchStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFaceMatchStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFaceMatchStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFaceMatchStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFaceMatchStatusRequestValidationError{}

// Validate checks the field values on GetFaceMatchStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFaceMatchStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFaceMatchStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFaceMatchStatusResponseMultiError, or nil if none found.
func (m *GetFaceMatchStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFaceMatchStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFaceMatchStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFaceMatchStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFaceMatchStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FaceMatchStatus

	// no validation rules for FaceMatchScore

	// no validation rules for FmScoreThreshold

	if len(errors) > 0 {
		return GetFaceMatchStatusResponseMultiError(errors)
	}

	return nil
}

// GetFaceMatchStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetFaceMatchStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type GetFaceMatchStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFaceMatchStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFaceMatchStatusResponseMultiError) AllErrors() []error { return m }

// GetFaceMatchStatusResponseValidationError is the validation error returned
// by GetFaceMatchStatusResponse.Validate if the designated constraints aren't met.
type GetFaceMatchStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFaceMatchStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFaceMatchStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFaceMatchStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFaceMatchStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFaceMatchStatusResponseValidationError) ErrorName() string {
	return "GetFaceMatchStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFaceMatchStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFaceMatchStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFaceMatchStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFaceMatchStatusResponseValidationError{}

// Validate checks the field values on LivenessOptions with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LivenessOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessOptionsMultiError, or nil if none found.
func (m *LivenessOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AttemptId

	// no validation rules for LivenessLevel

	// no validation rules for LivenessFlow

	if all {
		switch v := interface{}(m.GetVpnInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessOptionsValidationError{
					field:  "VpnInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessOptionsValidationError{
					field:  "VpnInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVpnInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessOptionsValidationError{
				field:  "VpnInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LivenessOptionsMultiError(errors)
	}

	return nil
}

// LivenessOptionsMultiError is an error wrapping multiple validation errors
// returned by LivenessOptions.ValidateAll() if the designated constraints
// aren't met.
type LivenessOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessOptionsMultiError) AllErrors() []error { return m }

// LivenessOptionsValidationError is the validation error returned by
// LivenessOptions.Validate if the designated constraints aren't met.
type LivenessOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessOptionsValidationError) ErrorName() string { return "LivenessOptionsValidationError" }

// Error satisfies the builtin error interface
func (e LivenessOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessOptionsValidationError{}

// Validate checks the field values on CheckLivenessWithVideoStreamRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CheckLivenessWithVideoStreamRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckLivenessWithVideoStreamRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CheckLivenessWithVideoStreamRequestMultiError, or nil if none found.
func (m *CheckLivenessWithVideoStreamRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckLivenessWithVideoStreamRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.MetadataVideoChunks.(type) {
	case *CheckLivenessWithVideoStreamRequest_Options:
		if v == nil {
			err := CheckLivenessWithVideoStreamRequestValidationError{
				field:  "MetadataVideoChunks",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOptions()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CheckLivenessWithVideoStreamRequestValidationError{
						field:  "Options",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CheckLivenessWithVideoStreamRequestValidationError{
						field:  "Options",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOptions()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CheckLivenessWithVideoStreamRequestValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CheckLivenessWithVideoStreamRequest_VideoChunk:
		if v == nil {
			err := CheckLivenessWithVideoStreamRequestValidationError{
				field:  "MetadataVideoChunks",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for VideoChunk
	case *CheckLivenessWithVideoStreamRequest_ImageFrame:
		if v == nil {
			err := CheckLivenessWithVideoStreamRequestValidationError{
				field:  "MetadataVideoChunks",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ImageFrame
	case *CheckLivenessWithVideoStreamRequest_PassiveImageFrames:
		if v == nil {
			err := CheckLivenessWithVideoStreamRequestValidationError{
				field:  "MetadataVideoChunks",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPassiveImageFrames()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CheckLivenessWithVideoStreamRequestValidationError{
						field:  "PassiveImageFrames",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CheckLivenessWithVideoStreamRequestValidationError{
						field:  "PassiveImageFrames",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPassiveImageFrames()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CheckLivenessWithVideoStreamRequestValidationError{
					field:  "PassiveImageFrames",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CheckLivenessWithVideoStreamRequestMultiError(errors)
	}

	return nil
}

// CheckLivenessWithVideoStreamRequestMultiError is an error wrapping multiple
// validation errors returned by
// CheckLivenessWithVideoStreamRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckLivenessWithVideoStreamRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckLivenessWithVideoStreamRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckLivenessWithVideoStreamRequestMultiError) AllErrors() []error { return m }

// CheckLivenessWithVideoStreamRequestValidationError is the validation error
// returned by CheckLivenessWithVideoStreamRequest.Validate if the designated
// constraints aren't met.
type CheckLivenessWithVideoStreamRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckLivenessWithVideoStreamRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckLivenessWithVideoStreamRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckLivenessWithVideoStreamRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckLivenessWithVideoStreamRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckLivenessWithVideoStreamRequestValidationError) ErrorName() string {
	return "CheckLivenessWithVideoStreamRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckLivenessWithVideoStreamRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckLivenessWithVideoStreamRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckLivenessWithVideoStreamRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckLivenessWithVideoStreamRequestValidationError{}

// Validate checks the field values on CheckLivenessWithVideoStreamResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CheckLivenessWithVideoStreamResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckLivenessWithVideoStreamResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CheckLivenessWithVideoStreamResponseMultiError, or nil if none found.
func (m *CheckLivenessWithVideoStreamResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckLivenessWithVideoStreamResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckLivenessWithVideoStreamResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckLivenessWithVideoStreamResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckLivenessWithVideoStreamResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LivenessStatus

	if len(errors) > 0 {
		return CheckLivenessWithVideoStreamResponseMultiError(errors)
	}

	return nil
}

// CheckLivenessWithVideoStreamResponseMultiError is an error wrapping multiple
// validation errors returned by
// CheckLivenessWithVideoStreamResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckLivenessWithVideoStreamResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckLivenessWithVideoStreamResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckLivenessWithVideoStreamResponseMultiError) AllErrors() []error { return m }

// CheckLivenessWithVideoStreamResponseValidationError is the validation error
// returned by CheckLivenessWithVideoStreamResponse.Validate if the designated
// constraints aren't met.
type CheckLivenessWithVideoStreamResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckLivenessWithVideoStreamResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckLivenessWithVideoStreamResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckLivenessWithVideoStreamResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckLivenessWithVideoStreamResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckLivenessWithVideoStreamResponseValidationError) ErrorName() string {
	return "CheckLivenessWithVideoStreamResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckLivenessWithVideoStreamResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckLivenessWithVideoStreamResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckLivenessWithVideoStreamResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckLivenessWithVideoStreamResponseValidationError{}

// Validate checks the field values on GenerateOTPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOTPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOTPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOTPRequestMultiError, or nil if none found.
func (m *GenerateOTPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOTPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLivenessOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOTPRequestValidationError{
					field:  "LivenessOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOTPRequestValidationError{
					field:  "LivenessOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLivenessOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOTPRequestValidationError{
				field:  "LivenessOptions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StrictnessLogic

	if len(errors) > 0 {
		return GenerateOTPRequestMultiError(errors)
	}

	return nil
}

// GenerateOTPRequestMultiError is an error wrapping multiple validation errors
// returned by GenerateOTPRequest.ValidateAll() if the designated constraints
// aren't met.
type GenerateOTPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOTPRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOTPRequestMultiError) AllErrors() []error { return m }

// GenerateOTPRequestValidationError is the validation error returned by
// GenerateOTPRequest.Validate if the designated constraints aren't met.
type GenerateOTPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOTPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOTPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOTPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOTPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOTPRequestValidationError) ErrorName() string {
	return "GenerateOTPRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOTPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOTPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOTPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOTPRequestValidationError{}

// Validate checks the field values on GenerateOTPResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOTPResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOTPResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOTPResponseMultiError, or nil if none found.
func (m *GenerateOTPResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOTPResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOTPResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOTPResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOTPResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Otp

	if len(errors) > 0 {
		return GenerateOTPResponseMultiError(errors)
	}

	return nil
}

// GenerateOTPResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateOTPResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateOTPResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOTPResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOTPResponseMultiError) AllErrors() []error { return m }

// GenerateOTPResponseValidationError is the validation error returned by
// GenerateOTPResponse.Validate if the designated constraints aren't met.
type GenerateOTPResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOTPResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOTPResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOTPResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOTPResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOTPResponseValidationError) ErrorName() string {
	return "GenerateOTPResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOTPResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOTPResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOTPResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOTPResponseValidationError{}

// Validate checks the field values on GetS3ImageRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetS3ImageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetS3ImageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetS3ImageRequestMultiError, or nil if none found.
func (m *GetS3ImageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetS3ImageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LocationKey

	if len(errors) > 0 {
		return GetS3ImageRequestMultiError(errors)
	}

	return nil
}

// GetS3ImageRequestMultiError is an error wrapping multiple validation errors
// returned by GetS3ImageRequest.ValidateAll() if the designated constraints
// aren't met.
type GetS3ImageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetS3ImageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetS3ImageRequestMultiError) AllErrors() []error { return m }

// GetS3ImageRequestValidationError is the validation error returned by
// GetS3ImageRequest.Validate if the designated constraints aren't met.
type GetS3ImageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetS3ImageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetS3ImageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetS3ImageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetS3ImageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetS3ImageRequestValidationError) ErrorName() string {
	return "GetS3ImageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetS3ImageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetS3ImageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetS3ImageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetS3ImageRequestValidationError{}

// Validate checks the field values on GetS3ImageResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetS3ImageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetS3ImageResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetS3ImageResponseMultiError, or nil if none found.
func (m *GetS3ImageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetS3ImageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetS3ImageResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetS3ImageResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetS3ImageResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Image

	if len(errors) > 0 {
		return GetS3ImageResponseMultiError(errors)
	}

	return nil
}

// GetS3ImageResponseMultiError is an error wrapping multiple validation errors
// returned by GetS3ImageResponse.ValidateAll() if the designated constraints
// aren't met.
type GetS3ImageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetS3ImageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetS3ImageResponseMultiError) AllErrors() []error { return m }

// GetS3ImageResponseValidationError is the validation error returned by
// GetS3ImageResponse.Validate if the designated constraints aren't met.
type GetS3ImageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetS3ImageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetS3ImageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetS3ImageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetS3ImageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetS3ImageResponseValidationError) ErrorName() string {
	return "GetS3ImageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetS3ImageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetS3ImageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetS3ImageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetS3ImageResponseValidationError{}

// Validate checks the field values on GetLivenessAttemptsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLivenessAttemptsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLivenessAttemptsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLivenessAttemptsRequestMultiError, or nil if none found.
func (m *GetLivenessAttemptsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLivenessAttemptsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Limit

	// no validation rules for LivenessFlow

	if len(errors) > 0 {
		return GetLivenessAttemptsRequestMultiError(errors)
	}

	return nil
}

// GetLivenessAttemptsRequestMultiError is an error wrapping multiple
// validation errors returned by GetLivenessAttemptsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetLivenessAttemptsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLivenessAttemptsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLivenessAttemptsRequestMultiError) AllErrors() []error { return m }

// GetLivenessAttemptsRequestValidationError is the validation error returned
// by GetLivenessAttemptsRequest.Validate if the designated constraints aren't met.
type GetLivenessAttemptsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLivenessAttemptsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLivenessAttemptsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLivenessAttemptsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLivenessAttemptsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLivenessAttemptsRequestValidationError) ErrorName() string {
	return "GetLivenessAttemptsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLivenessAttemptsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLivenessAttemptsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLivenessAttemptsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLivenessAttemptsRequestValidationError{}

// Validate checks the field values on GetLivenessAttemptsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLivenessAttemptsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLivenessAttemptsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLivenessAttemptsResponseMultiError, or nil if none found.
func (m *GetLivenessAttemptsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLivenessAttemptsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessAttemptsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessAttemptsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessAttemptsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLivenessAttempts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLivenessAttemptsResponseValidationError{
						field:  fmt.Sprintf("LivenessAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLivenessAttemptsResponseValidationError{
						field:  fmt.Sprintf("LivenessAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLivenessAttemptsResponseValidationError{
					field:  fmt.Sprintf("LivenessAttempts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetLivenessAttemptsResponseMultiError(errors)
	}

	return nil
}

// GetLivenessAttemptsResponseMultiError is an error wrapping multiple
// validation errors returned by GetLivenessAttemptsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetLivenessAttemptsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLivenessAttemptsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLivenessAttemptsResponseMultiError) AllErrors() []error { return m }

// GetLivenessAttemptsResponseValidationError is the validation error returned
// by GetLivenessAttemptsResponse.Validate if the designated constraints
// aren't met.
type GetLivenessAttemptsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLivenessAttemptsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLivenessAttemptsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLivenessAttemptsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLivenessAttemptsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLivenessAttemptsResponseValidationError) ErrorName() string {
	return "GetLivenessAttemptsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLivenessAttemptsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLivenessAttemptsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLivenessAttemptsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLivenessAttemptsResponseValidationError{}

// Validate checks the field values on AnnotateLivenessAttemptRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AnnotateLivenessAttemptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnotateLivenessAttemptRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AnnotateLivenessAttemptRequestMultiError, or nil if none found.
func (m *AnnotateLivenessAttemptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnotateLivenessAttemptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetAnnotation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnotateLivenessAttemptRequestValidationError{
					field:  "Annotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnotateLivenessAttemptRequestValidationError{
					field:  "Annotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnotation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnotateLivenessAttemptRequestValidationError{
				field:  "Annotation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AnnotateLivenessAttemptRequestMultiError(errors)
	}

	return nil
}

// AnnotateLivenessAttemptRequestMultiError is an error wrapping multiple
// validation errors returned by AnnotateLivenessAttemptRequest.ValidateAll()
// if the designated constraints aren't met.
type AnnotateLivenessAttemptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnotateLivenessAttemptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnotateLivenessAttemptRequestMultiError) AllErrors() []error { return m }

// AnnotateLivenessAttemptRequestValidationError is the validation error
// returned by AnnotateLivenessAttemptRequest.Validate if the designated
// constraints aren't met.
type AnnotateLivenessAttemptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnotateLivenessAttemptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnotateLivenessAttemptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnotateLivenessAttemptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnotateLivenessAttemptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnotateLivenessAttemptRequestValidationError) ErrorName() string {
	return "AnnotateLivenessAttemptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AnnotateLivenessAttemptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnotateLivenessAttemptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnotateLivenessAttemptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnotateLivenessAttemptRequestValidationError{}

// Validate checks the field values on AnnotateLivenessAttemptResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AnnotateLivenessAttemptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnotateLivenessAttemptResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AnnotateLivenessAttemptResponseMultiError, or nil if none found.
func (m *AnnotateLivenessAttemptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnotateLivenessAttemptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnotateLivenessAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnotateLivenessAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnotateLivenessAttemptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SummaryId

	if len(errors) > 0 {
		return AnnotateLivenessAttemptResponseMultiError(errors)
	}

	return nil
}

// AnnotateLivenessAttemptResponseMultiError is an error wrapping multiple
// validation errors returned by AnnotateLivenessAttemptResponse.ValidateAll()
// if the designated constraints aren't met.
type AnnotateLivenessAttemptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnotateLivenessAttemptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnotateLivenessAttemptResponseMultiError) AllErrors() []error { return m }

// AnnotateLivenessAttemptResponseValidationError is the validation error
// returned by AnnotateLivenessAttemptResponse.Validate if the designated
// constraints aren't met.
type AnnotateLivenessAttemptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnotateLivenessAttemptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnotateLivenessAttemptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnotateLivenessAttemptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnotateLivenessAttemptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnotateLivenessAttemptResponseValidationError) ErrorName() string {
	return "AnnotateLivenessAttemptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AnnotateLivenessAttemptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnotateLivenessAttemptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnotateLivenessAttemptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnotateLivenessAttemptResponseValidationError{}

// Validate checks the field values on CreateLivenessSummaryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLivenessSummaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLivenessSummaryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLivenessSummaryRequestMultiError, or nil if none found.
func (m *CreateLivenessSummaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLivenessSummaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RequestId

	// no validation rules for LivenessFlow

	// no validation rules for MaxRetries

	for idx, item := range m.GetRefFacematchPhotos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateLivenessSummaryRequestValidationError{
						field:  fmt.Sprintf("RefFacematchPhotos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateLivenessSummaryRequestValidationError{
						field:  fmt.Sprintf("RefFacematchPhotos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateLivenessSummaryRequestValidationError{
					field:  fmt.Sprintf("RefFacematchPhotos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for StrictnessLogic

	// no validation rules for ForceManualReview

	if all {
		switch v := interface{}(m.GetExpiryDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLivenessSummaryRequestValidationError{
					field:  "ExpiryDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLivenessSummaryRequestValidationError{
					field:  "ExpiryDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLivenessSummaryRequestValidationError{
				field:  "ExpiryDuration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextActionPostVideoUpload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLivenessSummaryRequestValidationError{
					field:  "NextActionPostVideoUpload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLivenessSummaryRequestValidationError{
					field:  "NextActionPostVideoUpload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionPostVideoUpload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLivenessSummaryRequestValidationError{
				field:  "NextActionPostVideoUpload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextActionHooks()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLivenessSummaryRequestValidationError{
					field:  "NextActionHooks",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLivenessSummaryRequestValidationError{
					field:  "NextActionHooks",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionHooks()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLivenessSummaryRequestValidationError{
				field:  "NextActionHooks",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCustomIntroScreenOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLivenessSummaryRequestValidationError{
					field:  "CustomIntroScreenOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLivenessSummaryRequestValidationError{
					field:  "CustomIntroScreenOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomIntroScreenOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLivenessSummaryRequestValidationError{
				field:  "CustomIntroScreenOptions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateLivenessSummaryRequestMultiError(errors)
	}

	return nil
}

// CreateLivenessSummaryRequestMultiError is an error wrapping multiple
// validation errors returned by CreateLivenessSummaryRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateLivenessSummaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLivenessSummaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLivenessSummaryRequestMultiError) AllErrors() []error { return m }

// CreateLivenessSummaryRequestValidationError is the validation error returned
// by CreateLivenessSummaryRequest.Validate if the designated constraints
// aren't met.
type CreateLivenessSummaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLivenessSummaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLivenessSummaryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLivenessSummaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLivenessSummaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLivenessSummaryRequestValidationError) ErrorName() string {
	return "CreateLivenessSummaryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLivenessSummaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLivenessSummaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLivenessSummaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLivenessSummaryRequestValidationError{}

// Validate checks the field values on CreateLivenessSummaryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLivenessSummaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLivenessSummaryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateLivenessSummaryResponseMultiError, or nil if none found.
func (m *CreateLivenessSummaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLivenessSummaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLivenessSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLivenessSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLivenessSummaryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLivenessSummaryResponseValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLivenessSummaryResponseValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLivenessSummaryResponseValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateLivenessSummaryResponseMultiError(errors)
	}

	return nil
}

// CreateLivenessSummaryResponseMultiError is an error wrapping multiple
// validation errors returned by CreateLivenessSummaryResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateLivenessSummaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLivenessSummaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLivenessSummaryResponseMultiError) AllErrors() []error { return m }

// CreateLivenessSummaryResponseValidationError is the validation error
// returned by CreateLivenessSummaryResponse.Validate if the designated
// constraints aren't met.
type CreateLivenessSummaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLivenessSummaryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLivenessSummaryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLivenessSummaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLivenessSummaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLivenessSummaryResponseValidationError) ErrorName() string {
	return "CreateLivenessSummaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLivenessSummaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLivenessSummaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLivenessSummaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLivenessSummaryResponseValidationError{}

// Validate checks the field values on GetLivenessSummaryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLivenessSummaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLivenessSummaryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLivenessSummaryRequestMultiError, or nil if none found.
func (m *GetLivenessSummaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLivenessSummaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RequestId

	// no validation rules for LivenessFlow

	if len(errors) > 0 {
		return GetLivenessSummaryRequestMultiError(errors)
	}

	return nil
}

// GetLivenessSummaryRequestMultiError is an error wrapping multiple validation
// errors returned by GetLivenessSummaryRequest.ValidateAll() if the
// designated constraints aren't met.
type GetLivenessSummaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLivenessSummaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLivenessSummaryRequestMultiError) AllErrors() []error { return m }

// GetLivenessSummaryRequestValidationError is the validation error returned by
// GetLivenessSummaryRequest.Validate if the designated constraints aren't met.
type GetLivenessSummaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLivenessSummaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLivenessSummaryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLivenessSummaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLivenessSummaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLivenessSummaryRequestValidationError) ErrorName() string {
	return "GetLivenessSummaryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLivenessSummaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLivenessSummaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLivenessSummaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLivenessSummaryRequestValidationError{}

// Validate checks the field values on GetLivenessSummaryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLivenessSummaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLivenessSummaryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLivenessSummaryResponseMultiError, or nil if none found.
func (m *GetLivenessSummaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLivenessSummaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessSummaryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessSummaryResponseValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessSummaryResponseValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessSummaryResponseValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLivenessSummaryResponseMultiError(errors)
	}

	return nil
}

// GetLivenessSummaryResponseMultiError is an error wrapping multiple
// validation errors returned by GetLivenessSummaryResponse.ValidateAll() if
// the designated constraints aren't met.
type GetLivenessSummaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLivenessSummaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLivenessSummaryResponseMultiError) AllErrors() []error { return m }

// GetLivenessSummaryResponseValidationError is the validation error returned
// by GetLivenessSummaryResponse.Validate if the designated constraints aren't met.
type GetLivenessSummaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLivenessSummaryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLivenessSummaryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLivenessSummaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLivenessSummaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLivenessSummaryResponseValidationError) ErrorName() string {
	return "GetLivenessSummaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLivenessSummaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLivenessSummaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLivenessSummaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLivenessSummaryResponseValidationError{}

// Validate checks the field values on GetLivenessSummaryStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLivenessSummaryStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLivenessSummaryStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLivenessSummaryStatusRequestMultiError, or nil if none found.
func (m *GetLivenessSummaryStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLivenessSummaryStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RequestId

	// no validation rules for LivenessFlow

	if len(errors) > 0 {
		return GetLivenessSummaryStatusRequestMultiError(errors)
	}

	return nil
}

// GetLivenessSummaryStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetLivenessSummaryStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type GetLivenessSummaryStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLivenessSummaryStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLivenessSummaryStatusRequestMultiError) AllErrors() []error { return m }

// GetLivenessSummaryStatusRequestValidationError is the validation error
// returned by GetLivenessSummaryStatusRequest.Validate if the designated
// constraints aren't met.
type GetLivenessSummaryStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLivenessSummaryStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLivenessSummaryStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLivenessSummaryStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLivenessSummaryStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLivenessSummaryStatusRequestValidationError) ErrorName() string {
	return "GetLivenessSummaryStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLivenessSummaryStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLivenessSummaryStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLivenessSummaryStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLivenessSummaryStatusRequestValidationError{}

// Validate checks the field values on GetLivenessSummaryStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLivenessSummaryStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLivenessSummaryStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLivenessSummaryStatusResponseMultiError, or nil if none found.
func (m *GetLivenessSummaryStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLivenessSummaryStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessSummaryStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessSummaryStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessSummaryStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SummaryStatus

	// no validation rules for MaxAttempts

	// no validation rules for AttemptCount

	// no validation rules for SummaryLivenessStatus

	// no validation rules for SummaryFacematchStatus

	// no validation rules for LivenessAttemptStatus

	if all {
		switch v := interface{}(m.GetNextActionPostVideoUpload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessSummaryStatusResponseValidationError{
					field:  "NextActionPostVideoUpload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessSummaryStatusResponseValidationError{
					field:  "NextActionPostVideoUpload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionPostVideoUpload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessSummaryStatusResponseValidationError{
				field:  "NextActionPostVideoUpload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessSummaryStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessSummaryStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessSummaryStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLivenessSummaryStatusResponseMultiError(errors)
	}

	return nil
}

// GetLivenessSummaryStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetLivenessSummaryStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLivenessSummaryStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLivenessSummaryStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLivenessSummaryStatusResponseMultiError) AllErrors() []error { return m }

// GetLivenessSummaryStatusResponseValidationError is the validation error
// returned by GetLivenessSummaryStatusResponse.Validate if the designated
// constraints aren't met.
type GetLivenessSummaryStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLivenessSummaryStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLivenessSummaryStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLivenessSummaryStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLivenessSummaryStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLivenessSummaryStatusResponseValidationError) ErrorName() string {
	return "GetLivenessSummaryStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLivenessSummaryStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLivenessSummaryStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLivenessSummaryStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLivenessSummaryStatusResponseValidationError{}

// Validate checks the field values on CreateLivenessAttemptRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLivenessAttemptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLivenessAttemptRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLivenessAttemptRequestMultiError, or nil if none found.
func (m *CreateLivenessAttemptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLivenessAttemptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for SummaryRequestId

	// no validation rules for LivenessFlow

	if len(errors) > 0 {
		return CreateLivenessAttemptRequestMultiError(errors)
	}

	return nil
}

// CreateLivenessAttemptRequestMultiError is an error wrapping multiple
// validation errors returned by CreateLivenessAttemptRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateLivenessAttemptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLivenessAttemptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLivenessAttemptRequestMultiError) AllErrors() []error { return m }

// CreateLivenessAttemptRequestValidationError is the validation error returned
// by CreateLivenessAttemptRequest.Validate if the designated constraints
// aren't met.
type CreateLivenessAttemptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLivenessAttemptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLivenessAttemptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLivenessAttemptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLivenessAttemptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLivenessAttemptRequestValidationError) ErrorName() string {
	return "CreateLivenessAttemptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLivenessAttemptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLivenessAttemptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLivenessAttemptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLivenessAttemptRequestValidationError{}

// Validate checks the field values on CreateLivenessAttemptResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLivenessAttemptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLivenessAttemptResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateLivenessAttemptResponseMultiError, or nil if none found.
func (m *CreateLivenessAttemptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLivenessAttemptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLivenessAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLivenessAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLivenessAttemptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLivenessAttempt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLivenessAttemptResponseValidationError{
					field:  "LivenessAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLivenessAttemptResponseValidationError{
					field:  "LivenessAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLivenessAttempt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLivenessAttemptResponseValidationError{
				field:  "LivenessAttempt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OldLivenessStatus

	// no validation rules for OldAttemptStatus

	if len(errors) > 0 {
		return CreateLivenessAttemptResponseMultiError(errors)
	}

	return nil
}

// CreateLivenessAttemptResponseMultiError is an error wrapping multiple
// validation errors returned by CreateLivenessAttemptResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateLivenessAttemptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLivenessAttemptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLivenessAttemptResponseMultiError) AllErrors() []error { return m }

// CreateLivenessAttemptResponseValidationError is the validation error
// returned by CreateLivenessAttemptResponse.Validate if the designated
// constraints aren't met.
type CreateLivenessAttemptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLivenessAttemptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLivenessAttemptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLivenessAttemptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLivenessAttemptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLivenessAttemptResponseValidationError) ErrorName() string {
	return "CreateLivenessAttemptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLivenessAttemptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLivenessAttemptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLivenessAttemptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLivenessAttemptResponseValidationError{}

// Validate checks the field values on AnnotateFacematchAttemptRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AnnotateFacematchAttemptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnotateFacematchAttemptRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AnnotateFacematchAttemptRequestMultiError, or nil if none found.
func (m *AnnotateFacematchAttemptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnotateFacematchAttemptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RequestId

	// no validation rules for ReviewVerdict

	if all {
		switch v := interface{}(m.GetAnnotation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnotateFacematchAttemptRequestValidationError{
					field:  "Annotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnotateFacematchAttemptRequestValidationError{
					field:  "Annotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnotation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnotateFacematchAttemptRequestValidationError{
				field:  "Annotation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AnnotateFacematchAttemptRequestMultiError(errors)
	}

	return nil
}

// AnnotateFacematchAttemptRequestMultiError is an error wrapping multiple
// validation errors returned by AnnotateFacematchAttemptRequest.ValidateAll()
// if the designated constraints aren't met.
type AnnotateFacematchAttemptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnotateFacematchAttemptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnotateFacematchAttemptRequestMultiError) AllErrors() []error { return m }

// AnnotateFacematchAttemptRequestValidationError is the validation error
// returned by AnnotateFacematchAttemptRequest.Validate if the designated
// constraints aren't met.
type AnnotateFacematchAttemptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnotateFacematchAttemptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnotateFacematchAttemptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnotateFacematchAttemptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnotateFacematchAttemptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnotateFacematchAttemptRequestValidationError) ErrorName() string {
	return "AnnotateFacematchAttemptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AnnotateFacematchAttemptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnotateFacematchAttemptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnotateFacematchAttemptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnotateFacematchAttemptRequestValidationError{}

// Validate checks the field values on AnnotateFacematchAttemptResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *AnnotateFacematchAttemptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnotateFacematchAttemptResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AnnotateFacematchAttemptResponseMultiError, or nil if none found.
func (m *AnnotateFacematchAttemptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnotateFacematchAttemptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnotateFacematchAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnotateFacematchAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnotateFacematchAttemptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SummaryId

	if len(errors) > 0 {
		return AnnotateFacematchAttemptResponseMultiError(errors)
	}

	return nil
}

// AnnotateFacematchAttemptResponseMultiError is an error wrapping multiple
// validation errors returned by
// AnnotateFacematchAttemptResponse.ValidateAll() if the designated
// constraints aren't met.
type AnnotateFacematchAttemptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnotateFacematchAttemptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnotateFacematchAttemptResponseMultiError) AllErrors() []error { return m }

// AnnotateFacematchAttemptResponseValidationError is the validation error
// returned by AnnotateFacematchAttemptResponse.Validate if the designated
// constraints aren't met.
type AnnotateFacematchAttemptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnotateFacematchAttemptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnotateFacematchAttemptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnotateFacematchAttemptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnotateFacematchAttemptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnotateFacematchAttemptResponseValidationError) ErrorName() string {
	return "AnnotateFacematchAttemptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AnnotateFacematchAttemptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnotateFacematchAttemptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnotateFacematchAttemptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnotateFacematchAttemptResponseValidationError{}

// Validate checks the field values on RedoFaceMatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RedoFaceMatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RedoFaceMatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RedoFaceMatchRequestMultiError, or nil if none found.
func (m *RedoFaceMatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RedoFaceMatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for SummaryRequestId

	// no validation rules for LivenessFlow

	for idx, item := range m.GetReferencePhotos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RedoFaceMatchRequestValidationError{
						field:  fmt.Sprintf("ReferencePhotos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RedoFaceMatchRequestValidationError{
						field:  fmt.Sprintf("ReferencePhotos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RedoFaceMatchRequestValidationError{
					field:  fmt.Sprintf("ReferencePhotos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RedoFaceMatchRequestMultiError(errors)
	}

	return nil
}

// RedoFaceMatchRequestMultiError is an error wrapping multiple validation
// errors returned by RedoFaceMatchRequest.ValidateAll() if the designated
// constraints aren't met.
type RedoFaceMatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RedoFaceMatchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RedoFaceMatchRequestMultiError) AllErrors() []error { return m }

// RedoFaceMatchRequestValidationError is the validation error returned by
// RedoFaceMatchRequest.Validate if the designated constraints aren't met.
type RedoFaceMatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RedoFaceMatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RedoFaceMatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RedoFaceMatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RedoFaceMatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RedoFaceMatchRequestValidationError) ErrorName() string {
	return "RedoFaceMatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RedoFaceMatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRedoFaceMatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RedoFaceMatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RedoFaceMatchRequestValidationError{}

// Validate checks the field values on RedoFaceMatchResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RedoFaceMatchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RedoFaceMatchResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RedoFaceMatchResponseMultiError, or nil if none found.
func (m *RedoFaceMatchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RedoFaceMatchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RedoFaceMatchResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RedoFaceMatchResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RedoFaceMatchResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RedoFaceMatchResponseMultiError(errors)
	}

	return nil
}

// RedoFaceMatchResponseMultiError is an error wrapping multiple validation
// errors returned by RedoFaceMatchResponse.ValidateAll() if the designated
// constraints aren't met.
type RedoFaceMatchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RedoFaceMatchResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RedoFaceMatchResponseMultiError) AllErrors() []error { return m }

// RedoFaceMatchResponseValidationError is the validation error returned by
// RedoFaceMatchResponse.Validate if the designated constraints aren't met.
type RedoFaceMatchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RedoFaceMatchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RedoFaceMatchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RedoFaceMatchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RedoFaceMatchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RedoFaceMatchResponseValidationError) ErrorName() string {
	return "RedoFaceMatchResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RedoFaceMatchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRedoFaceMatchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RedoFaceMatchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RedoFaceMatchResponseValidationError{}

// Validate checks the field values on GetFaceMatchAttemptsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFaceMatchAttemptsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFaceMatchAttemptsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFaceMatchAttemptsRequestMultiError, or nil if none found.
func (m *GetFaceMatchAttemptsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFaceMatchAttemptsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Limit

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetCriteria()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFaceMatchAttemptsRequestValidationError{
					field:  "Criteria",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFaceMatchAttemptsRequestValidationError{
					field:  "Criteria",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCriteria()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFaceMatchAttemptsRequestValidationError{
				field:  "Criteria",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFaceMatchAttemptsRequestMultiError(errors)
	}

	return nil
}

// GetFaceMatchAttemptsRequestMultiError is an error wrapping multiple
// validation errors returned by GetFaceMatchAttemptsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetFaceMatchAttemptsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFaceMatchAttemptsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFaceMatchAttemptsRequestMultiError) AllErrors() []error { return m }

// GetFaceMatchAttemptsRequestValidationError is the validation error returned
// by GetFaceMatchAttemptsRequest.Validate if the designated constraints
// aren't met.
type GetFaceMatchAttemptsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFaceMatchAttemptsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFaceMatchAttemptsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFaceMatchAttemptsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFaceMatchAttemptsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFaceMatchAttemptsRequestValidationError) ErrorName() string {
	return "GetFaceMatchAttemptsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFaceMatchAttemptsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFaceMatchAttemptsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFaceMatchAttemptsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFaceMatchAttemptsRequestValidationError{}

// Validate checks the field values on FacematchRequestCriteria with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FacematchRequestCriteria) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FacematchRequestCriteria with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FacematchRequestCriteriaMultiError, or nil if none found.
func (m *FacematchRequestCriteria) ValidateAll() error {
	return m.validate(true)
}

func (m *FacematchRequestCriteria) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Criteria.(type) {
	case *FacematchRequestCriteria_ActorId:
		if v == nil {
			err := FacematchRequestCriteriaValidationError{
				field:  "Criteria",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	case *FacematchRequestCriteria_LivenessRequestId:
		if v == nil {
			err := FacematchRequestCriteriaValidationError{
				field:  "Criteria",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for LivenessRequestId
	case *FacematchRequestCriteria_FacematchRequestId:
		if v == nil {
			err := FacematchRequestCriteriaValidationError{
				field:  "Criteria",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for FacematchRequestId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return FacematchRequestCriteriaMultiError(errors)
	}

	return nil
}

// FacematchRequestCriteriaMultiError is an error wrapping multiple validation
// errors returned by FacematchRequestCriteria.ValidateAll() if the designated
// constraints aren't met.
type FacematchRequestCriteriaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FacematchRequestCriteriaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FacematchRequestCriteriaMultiError) AllErrors() []error { return m }

// FacematchRequestCriteriaValidationError is the validation error returned by
// FacematchRequestCriteria.Validate if the designated constraints aren't met.
type FacematchRequestCriteriaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FacematchRequestCriteriaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FacematchRequestCriteriaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FacematchRequestCriteriaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FacematchRequestCriteriaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FacematchRequestCriteriaValidationError) ErrorName() string {
	return "FacematchRequestCriteriaValidationError"
}

// Error satisfies the builtin error interface
func (e FacematchRequestCriteriaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFacematchRequestCriteria.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FacematchRequestCriteriaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FacematchRequestCriteriaValidationError{}

// Validate checks the field values on GetFaceMatchAttemptsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFaceMatchAttemptsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFaceMatchAttemptsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFaceMatchAttemptsResponseMultiError, or nil if none found.
func (m *GetFaceMatchAttemptsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFaceMatchAttemptsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFaceMatchAttemptsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFaceMatchAttemptsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFaceMatchAttemptsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFaceMatchAttempts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFaceMatchAttemptsResponseValidationError{
						field:  fmt.Sprintf("FaceMatchAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFaceMatchAttemptsResponseValidationError{
						field:  fmt.Sprintf("FaceMatchAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFaceMatchAttemptsResponseValidationError{
					field:  fmt.Sprintf("FaceMatchAttempts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFaceMatchAttemptsResponseMultiError(errors)
	}

	return nil
}

// GetFaceMatchAttemptsResponseMultiError is an error wrapping multiple
// validation errors returned by GetFaceMatchAttemptsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetFaceMatchAttemptsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFaceMatchAttemptsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFaceMatchAttemptsResponseMultiError) AllErrors() []error { return m }

// GetFaceMatchAttemptsResponseValidationError is the validation error returned
// by GetFaceMatchAttemptsResponse.Validate if the designated constraints
// aren't met.
type GetFaceMatchAttemptsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFaceMatchAttemptsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFaceMatchAttemptsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFaceMatchAttemptsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFaceMatchAttemptsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFaceMatchAttemptsResponseValidationError) ErrorName() string {
	return "GetFaceMatchAttemptsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFaceMatchAttemptsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFaceMatchAttemptsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFaceMatchAttemptsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFaceMatchAttemptsResponseValidationError{}

// Validate checks the field values on RetryLivenessRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RetryLivenessRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RetryLivenessRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RetryLivenessRequestMultiError, or nil if none found.
func (m *RetryLivenessRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RetryLivenessRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for SummaryRequestId

	// no validation rules for LivenessFlow

	// no validation rules for SummaryId

	if len(errors) > 0 {
		return RetryLivenessRequestMultiError(errors)
	}

	return nil
}

// RetryLivenessRequestMultiError is an error wrapping multiple validation
// errors returned by RetryLivenessRequest.ValidateAll() if the designated
// constraints aren't met.
type RetryLivenessRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RetryLivenessRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RetryLivenessRequestMultiError) AllErrors() []error { return m }

// RetryLivenessRequestValidationError is the validation error returned by
// RetryLivenessRequest.Validate if the designated constraints aren't met.
type RetryLivenessRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RetryLivenessRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RetryLivenessRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RetryLivenessRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RetryLivenessRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RetryLivenessRequestValidationError) ErrorName() string {
	return "RetryLivenessRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RetryLivenessRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRetryLivenessRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RetryLivenessRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RetryLivenessRequestValidationError{}

// Validate checks the field values on RetryLivenessResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RetryLivenessResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RetryLivenessResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RetryLivenessResponseMultiError, or nil if none found.
func (m *RetryLivenessResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RetryLivenessResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RetryLivenessResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RetryLivenessResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RetryLivenessResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RetryLivenessResponseMultiError(errors)
	}

	return nil
}

// RetryLivenessResponseMultiError is an error wrapping multiple validation
// errors returned by RetryLivenessResponse.ValidateAll() if the designated
// constraints aren't met.
type RetryLivenessResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RetryLivenessResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RetryLivenessResponseMultiError) AllErrors() []error { return m }

// RetryLivenessResponseValidationError is the validation error returned by
// RetryLivenessResponse.Validate if the designated constraints aren't met.
type RetryLivenessResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RetryLivenessResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RetryLivenessResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RetryLivenessResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RetryLivenessResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RetryLivenessResponseValidationError) ErrorName() string {
	return "RetryLivenessResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RetryLivenessResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRetryLivenessResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RetryLivenessResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RetryLivenessResponseValidationError{}

// Validate checks the field values on GetLivenessAttemptRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLivenessAttemptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLivenessAttemptRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLivenessAttemptRequestMultiError, or nil if none found.
func (m *GetLivenessAttemptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLivenessAttemptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LivenessReqId

	if len(errors) > 0 {
		return GetLivenessAttemptRequestMultiError(errors)
	}

	return nil
}

// GetLivenessAttemptRequestMultiError is an error wrapping multiple validation
// errors returned by GetLivenessAttemptRequest.ValidateAll() if the
// designated constraints aren't met.
type GetLivenessAttemptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLivenessAttemptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLivenessAttemptRequestMultiError) AllErrors() []error { return m }

// GetLivenessAttemptRequestValidationError is the validation error returned by
// GetLivenessAttemptRequest.Validate if the designated constraints aren't met.
type GetLivenessAttemptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLivenessAttemptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLivenessAttemptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLivenessAttemptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLivenessAttemptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLivenessAttemptRequestValidationError) ErrorName() string {
	return "GetLivenessAttemptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLivenessAttemptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLivenessAttemptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLivenessAttemptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLivenessAttemptRequestValidationError{}

// Validate checks the field values on GetLivenessAttemptResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLivenessAttemptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLivenessAttemptResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLivenessAttemptResponseMultiError, or nil if none found.
func (m *GetLivenessAttemptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLivenessAttemptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessAttemptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLivenessAttempt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLivenessAttemptResponseValidationError{
					field:  "LivenessAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLivenessAttemptResponseValidationError{
					field:  "LivenessAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLivenessAttempt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLivenessAttemptResponseValidationError{
				field:  "LivenessAttempt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLivenessAttemptResponseMultiError(errors)
	}

	return nil
}

// GetLivenessAttemptResponseMultiError is an error wrapping multiple
// validation errors returned by GetLivenessAttemptResponse.ValidateAll() if
// the designated constraints aren't met.
type GetLivenessAttemptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLivenessAttemptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLivenessAttemptResponseMultiError) AllErrors() []error { return m }

// GetLivenessAttemptResponseValidationError is the validation error returned
// by GetLivenessAttemptResponse.Validate if the designated constraints aren't met.
type GetLivenessAttemptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLivenessAttemptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLivenessAttemptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLivenessAttemptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLivenessAttemptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLivenessAttemptResponseValidationError) ErrorName() string {
	return "GetLivenessAttemptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLivenessAttemptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLivenessAttemptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLivenessAttemptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLivenessAttemptResponseValidationError{}
