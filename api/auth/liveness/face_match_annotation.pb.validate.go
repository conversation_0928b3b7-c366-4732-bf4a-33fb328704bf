// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/liveness/internal/face_match_annotation.proto

package liveness

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on FaceMatchAnnotation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FaceMatchAnnotation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FaceMatchAnnotation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FaceMatchAnnotationMultiError, or nil if none found.
func (m *FaceMatchAnnotation) ValidateAll() error {
	return m.validate(true)
}

func (m *FaceMatchAnnotation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReviewedBy

	// no validation rules for ReviewVerdict

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetReviewedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FaceMatchAnnotationValidationError{
					field:  "ReviewedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FaceMatchAnnotationValidationError{
					field:  "ReviewedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReviewedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FaceMatchAnnotationValidationError{
				field:  "ReviewedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReReviewVerdict

	// no validation rules for ReReviewedBy

	if all {
		switch v := interface{}(m.GetReReviewedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FaceMatchAnnotationValidationError{
					field:  "ReReviewedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FaceMatchAnnotationValidationError{
					field:  "ReReviewedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReReviewedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FaceMatchAnnotationValidationError{
				field:  "ReReviewedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReReviewRemarks

	// no validation rules for ReReviewErrorType

	// no validation rules for CaseId

	if len(errors) > 0 {
		return FaceMatchAnnotationMultiError(errors)
	}

	return nil
}

// FaceMatchAnnotationMultiError is an error wrapping multiple validation
// errors returned by FaceMatchAnnotation.ValidateAll() if the designated
// constraints aren't met.
type FaceMatchAnnotationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FaceMatchAnnotationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FaceMatchAnnotationMultiError) AllErrors() []error { return m }

// FaceMatchAnnotationValidationError is the validation error returned by
// FaceMatchAnnotation.Validate if the designated constraints aren't met.
type FaceMatchAnnotationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FaceMatchAnnotationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FaceMatchAnnotationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FaceMatchAnnotationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FaceMatchAnnotationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FaceMatchAnnotationValidationError) ErrorName() string {
	return "FaceMatchAnnotationValidationError"
}

// Error satisfies the builtin error interface
func (e FaceMatchAnnotationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFaceMatchAnnotation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FaceMatchAnnotationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FaceMatchAnnotationValidationError{}
