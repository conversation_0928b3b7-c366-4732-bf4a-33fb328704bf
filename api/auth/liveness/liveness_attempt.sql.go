package liveness

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/logger"
)

// UnmarshalJSON implements the Unmarshaler interface
func (x *LivenessAttempt) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, x)
}

// MarshalJSON implements the Marshaler interface
func (x *LivenessAttempt) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(x)
}

// Value Valuer interface implementation for storing the data in string format in DB
func (x BlinkLivenessStatus) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan Scanner interface implementation for parsing data while reading from DB
func (x *BlinkLivenessStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := BlinkLivenessStatus_value[val]
	if !ok {
		logger.ErrorNoCtx(fmt.Sprintf("encountered unknown blink liveness status : %v", val))
	}
	*x = BlinkLivenessStatus(valInt)
	return nil
}

// UnmarshalJSON implements the Unmarshaler interface
func (x *Metadata) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, x)
}

// MarshalJSON implements the Marshaler interface
func (x *Metadata) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(x)
}

// Value : Valuer interface implementation for storing the data in string format in DB
func (x *Metadata) Value() (driver.Value, error) {
	if x == nil {
		return nil, nil
	}
	return protojson.Marshal(x)
}

// Scan : Scanner interface implementation for parsing data while reading from DB
func (x *Metadata) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, x)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}

// UnmarshalJSON implements the Unmarshaler interface
func (x *Annotation) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, x)
}

// MarshalJSON implements the Marshaler interface
func (x *Annotation) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(x)
}

// Value : Valuer interface implementation for storing the data in string format in DB
func (x *Annotation) Value() (driver.Value, error) {
	if x == nil {
		return nil, nil
	}
	return protojson.Marshal(x)
}

// Scan : Scanner interface implementation for parsing data while reading from DB
func (x *Annotation) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, x)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}

// UnmarshalJSON implements the Unmarshaler interface
func (x *InHouseLiveness) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, x)
}

// MarshalJSON implements the Marshaler interface
func (x *InHouseLiveness) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(x)
}

// Value : Valuer interface implementation for storing the data in string format in DB
func (x *InHouseLiveness) Value() (driver.Value, error) {
	if x == nil {
		return nil, nil
	}
	return protojson.Marshal(x)
}

// Scan : Scanner interface implementation for parsing data while reading from DB
func (x *InHouseLiveness) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, x)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}
