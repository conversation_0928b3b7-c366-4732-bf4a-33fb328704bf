// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/liveness/types.proto

package liveness

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// LivenessLevel specify the level of liveness to be used to treat as liveness as passed.
type LivenessLevel int32

const (
	LivenessLevel_LIVENESS_LEVEL_UNSPECIFIED LivenessLevel = 0
	// LIVENESS_LENIENT represents a lenient check which passes even if reviewing is recommended by the vendor.
	LivenessLevel_LIVENESS_LENIENT LivenessLevel = 1
	// LIVENESS_HARD represents a hard check which passes only if reviewing isn't recommended by the vendor.
	LivenessLevel_LIVENESS_HARD LivenessLevel = 2
)

// Enum value maps for LivenessLevel.
var (
	LivenessLevel_name = map[int32]string{
		0: "LIVENESS_LEVEL_UNSPECIFIED",
		1: "LIVENESS_LENIENT",
		2: "LIVENESS_HARD",
	}
	LivenessLevel_value = map[string]int32{
		"LIVENESS_LEVEL_UNSPECIFIED": 0,
		"LIVENESS_LENIENT":           1,
		"LIVENESS_HARD":              2,
	}
)

func (x LivenessLevel) Enum() *LivenessLevel {
	p := new(LivenessLevel)
	*p = x
	return p
}

func (x LivenessLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LivenessLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_types_proto_enumTypes[0].Descriptor()
}

func (LivenessLevel) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_types_proto_enumTypes[0]
}

func (x LivenessLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LivenessLevel.Descriptor instead.
func (LivenessLevel) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_types_proto_rawDescGZIP(), []int{0}
}

// FaceMatchLevel specify the level of FaceMatch to be used to treat as liveness as passed.
type FaceMatchLevel int32

const (
	FaceMatchLevel_FACE_MATCH_LEVEL_UNSPECIFIED FaceMatchLevel = 0
	// FACE_MATCH_LENIENT represents a lenient check which passes even if reviewing is recommended by the vendor.
	FaceMatchLevel_FACE_MATCH_LENIENT FaceMatchLevel = 1
	// FACE_MATCH_HARD represents a hard check which passes only if reviewing isn't recommended by the vendor.
	FaceMatchLevel_FACE_MATCH_HARD FaceMatchLevel = 2
)

// Enum value maps for FaceMatchLevel.
var (
	FaceMatchLevel_name = map[int32]string{
		0: "FACE_MATCH_LEVEL_UNSPECIFIED",
		1: "FACE_MATCH_LENIENT",
		2: "FACE_MATCH_HARD",
	}
	FaceMatchLevel_value = map[string]int32{
		"FACE_MATCH_LEVEL_UNSPECIFIED": 0,
		"FACE_MATCH_LENIENT":           1,
		"FACE_MATCH_HARD":              2,
	}
)

func (x FaceMatchLevel) Enum() *FaceMatchLevel {
	p := new(FaceMatchLevel)
	*p = x
	return p
}

func (x FaceMatchLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FaceMatchLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_types_proto_enumTypes[1].Descriptor()
}

func (FaceMatchLevel) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_types_proto_enumTypes[1]
}

func (x FaceMatchLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FaceMatchLevel.Descriptor instead.
func (FaceMatchLevel) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_types_proto_rawDescGZIP(), []int{1}
}

// FaceMatchStatus represents the current status of Face Match.
type FaceMatchStatus int32

const (
	FaceMatchStatus_FACE_MATCH_STATUS_UNSPECIFIED FaceMatchStatus = 0
	// FACE_MATCH_PENDING means that the face match is in the process.
	FaceMatchStatus_FACE_MATCH_PENDING FaceMatchStatus = 1
	// FACE_MATCH_PASSED means that the face match has passed.
	FaceMatchStatus_FACE_MATCH_PASSED FaceMatchStatus = 2
	// FACE_MATCH_FAILED_RETRY means that the face match has failed with retryable error.
	FaceMatchStatus_FACE_MATCH_FAILED_RETRY FaceMatchStatus = 3
	// FACE_MATCH_FAILED means that the face match has failed.
	FaceMatchStatus_FACE_MATCH_FAILED FaceMatchStatus = 4
	// State when facematch is manually passed after verifying the photos
	FaceMatchStatus_FACE_MATCH_MANUALLY_PASSED FaceMatchStatus = 5
	// State when facematch is manually failed after verifying the photos
	FaceMatchStatus_FACE_MATCH_MANUALLY_FAILED FaceMatchStatus = 6
)

// Enum value maps for FaceMatchStatus.
var (
	FaceMatchStatus_name = map[int32]string{
		0: "FACE_MATCH_STATUS_UNSPECIFIED",
		1: "FACE_MATCH_PENDING",
		2: "FACE_MATCH_PASSED",
		3: "FACE_MATCH_FAILED_RETRY",
		4: "FACE_MATCH_FAILED",
		5: "FACE_MATCH_MANUALLY_PASSED",
		6: "FACE_MATCH_MANUALLY_FAILED",
	}
	FaceMatchStatus_value = map[string]int32{
		"FACE_MATCH_STATUS_UNSPECIFIED": 0,
		"FACE_MATCH_PENDING":            1,
		"FACE_MATCH_PASSED":             2,
		"FACE_MATCH_FAILED_RETRY":       3,
		"FACE_MATCH_FAILED":             4,
		"FACE_MATCH_MANUALLY_PASSED":    5,
		"FACE_MATCH_MANUALLY_FAILED":    6,
	}
)

func (x FaceMatchStatus) Enum() *FaceMatchStatus {
	p := new(FaceMatchStatus)
	*p = x
	return p
}

func (x FaceMatchStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FaceMatchStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_types_proto_enumTypes[2].Descriptor()
}

func (FaceMatchStatus) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_types_proto_enumTypes[2]
}

func (x FaceMatchStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FaceMatchStatus.Descriptor instead.
func (FaceMatchStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_types_proto_rawDescGZIP(), []int{2}
}

// LivenessStatus represents the current status of Liveness.
type LivenessStatus int32

const (
	LivenessStatus_LIVENESS_STATUS_UNSPECIFIED LivenessStatus = 0
	// OTP received for the request.
	LivenessStatus_LIVENESS_OTP_RECEIVED LivenessStatus = 1
	// LIVENESS_STREAMING_STARTED means that the streaming from client has started
	LivenessStatus_LIVENESS_STREAMING_STARTED LivenessStatus = 2
	// LIVENESS_VIDEO_RECEIVED means that the streaming of video has completed.
	LivenessStatus_LIVENESS_VIDEO_RECEIVED LivenessStatus = 3
	// LIVENESS_PENDING means that the liveness is in the process.
	LivenessStatus_LIVENESS_PENDING LivenessStatus = 4
	// LIVENESS_PASSED means that the liveness has passed.
	LivenessStatus_LIVENESS_PASSED LivenessStatus = 5
	// LIVENESS_FAILED_RETRY means that the liveness has failed with retryable error.
	LivenessStatus_LIVENESS_FAILED_RETRY LivenessStatus = 6
	// LIVENESS_FACE_NOT_DETECTED means that the liveness has failed with manual retry due to face not
	// being in the liveness video.
	// The client needs to check next onboarding action to get new set of params to retry.
	// This state occurs when user has failed OTP check or passive liveness but not both.
	LivenessStatus_LIVENESS_FACE_NOT_DETECTED LivenessStatus = 10
	// LIVENESS_MULTIPLE_FACES_DETECTED means that the liveness has failed with manual retry due to multiple faces
	// being detected in the liveness video.
	// The client needs to check next onboarding action to get new set of params to retry.
	// This state occurs when user has failed OTP check or passive liveness but not both.
	LivenessStatus_LIVENESS_MULTIPLE_FACES_DETECTED LivenessStatus = 11
	// LIVENESS_MANUAL_RETRY means that the liveness has failed with manual retry.
	// The client needs to check next onboarding action to get new set of params to retry.
	// This state occurs when user has failed OTP check or passive liveness but not both.
	LivenessStatus_LIVENESS_MANUAL_RETRY LivenessStatus = 8
	// LIVENESS_INVALID_VIDEO means that the liveness has failed with manual retry due to an invliad video from client.
	// The client needs to check next onboarding action to get new set of params to retry.
	// This state occurs when user has failed OTP check or passive liveness but not both.
	LivenessStatus_LIVENESS_INVALID_VIDEO LivenessStatus = 9
	// LIVENESS_FAILED means that the liveness has failed.
	LivenessStatus_LIVENESS_FAILED LivenessStatus = 7
	// Face is poorly detected in the video
	LivenessStatus_LIVENESS_FACE_POORLY_DETECTED LivenessStatus = 12
	// Face is too far in the video
	LivenessStatus_LIVENESS_FACE_TOO_FAR LivenessStatus = 13
	// Face is too close in the video
	LivenessStatus_LIVENESS_FACE_TOO_CLOSE LivenessStatus = 14
	// Face is too dark in the video
	LivenessStatus_LIVENESS_FACE_TOO_DARK LivenessStatus = 15
	// Face is too bright in the video
	LivenessStatus_LIVENESS_FACE_TOO_BRIGHT LivenessStatus = 16
	// No face was detected in the video
	LivenessStatus_LIVENESS_NO_FACE_DETECTED LivenessStatus = 17
	// State when liveness is manually passed after verifying the video
	LivenessStatus_LIVENESS_MANUALLY_PASSED LivenessStatus = 18
	// State when liveness is manually failed after verifying the video
	LivenessStatus_LIVENESS_MANUALLY_FAILED LivenessStatus = 19
	// State when max retries of status check queue have been exhausted
	LivenessStatus_LIVENESS_QUEUE_MAX_RETRIES LivenessStatus = 20
)

// Enum value maps for LivenessStatus.
var (
	LivenessStatus_name = map[int32]string{
		0:  "LIVENESS_STATUS_UNSPECIFIED",
		1:  "LIVENESS_OTP_RECEIVED",
		2:  "LIVENESS_STREAMING_STARTED",
		3:  "LIVENESS_VIDEO_RECEIVED",
		4:  "LIVENESS_PENDING",
		5:  "LIVENESS_PASSED",
		6:  "LIVENESS_FAILED_RETRY",
		10: "LIVENESS_FACE_NOT_DETECTED",
		11: "LIVENESS_MULTIPLE_FACES_DETECTED",
		8:  "LIVENESS_MANUAL_RETRY",
		9:  "LIVENESS_INVALID_VIDEO",
		7:  "LIVENESS_FAILED",
		12: "LIVENESS_FACE_POORLY_DETECTED",
		13: "LIVENESS_FACE_TOO_FAR",
		14: "LIVENESS_FACE_TOO_CLOSE",
		15: "LIVENESS_FACE_TOO_DARK",
		16: "LIVENESS_FACE_TOO_BRIGHT",
		17: "LIVENESS_NO_FACE_DETECTED",
		18: "LIVENESS_MANUALLY_PASSED",
		19: "LIVENESS_MANUALLY_FAILED",
		20: "LIVENESS_QUEUE_MAX_RETRIES",
	}
	LivenessStatus_value = map[string]int32{
		"LIVENESS_STATUS_UNSPECIFIED":      0,
		"LIVENESS_OTP_RECEIVED":            1,
		"LIVENESS_STREAMING_STARTED":       2,
		"LIVENESS_VIDEO_RECEIVED":          3,
		"LIVENESS_PENDING":                 4,
		"LIVENESS_PASSED":                  5,
		"LIVENESS_FAILED_RETRY":            6,
		"LIVENESS_FACE_NOT_DETECTED":       10,
		"LIVENESS_MULTIPLE_FACES_DETECTED": 11,
		"LIVENESS_MANUAL_RETRY":            8,
		"LIVENESS_INVALID_VIDEO":           9,
		"LIVENESS_FAILED":                  7,
		"LIVENESS_FACE_POORLY_DETECTED":    12,
		"LIVENESS_FACE_TOO_FAR":            13,
		"LIVENESS_FACE_TOO_CLOSE":          14,
		"LIVENESS_FACE_TOO_DARK":           15,
		"LIVENESS_FACE_TOO_BRIGHT":         16,
		"LIVENESS_NO_FACE_DETECTED":        17,
		"LIVENESS_MANUALLY_PASSED":         18,
		"LIVENESS_MANUALLY_FAILED":         19,
		"LIVENESS_QUEUE_MAX_RETRIES":       20,
	}
)

func (x LivenessStatus) Enum() *LivenessStatus {
	p := new(LivenessStatus)
	*p = x
	return p
}

func (x LivenessStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LivenessStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_types_proto_enumTypes[3].Descriptor()
}

func (LivenessStatus) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_types_proto_enumTypes[3]
}

func (x LivenessStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LivenessStatus.Descriptor instead.
func (LivenessStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_types_proto_rawDescGZIP(), []int{3}
}

type LivenessFlow int32

const (
	LivenessFlow_LIVENESS_FLOW_UNSPECIFIED LivenessFlow = 0
	LivenessFlow_ONBOARDING                LivenessFlow = 1
	LivenessFlow_SET_PIN                   LivenessFlow = 2
	LivenessFlow_RESET_PIN                 LivenessFlow = 3
	LivenessFlow_AUTH_FACTOR_UPDATE        LivenessFlow = 4
	LivenessFlow_WEALTH_ONBOARDING         LivenessFlow = 5
	LivenessFlow_PRE_APPROVED_LOANS        LivenessFlow = 6
	LivenessFlow_USER_ACTIONS              LivenessFlow = 7
	LivenessFlow_CREDIT_CARD               LivenessFlow = 8
	LivenessFlow_DEVICE_BIOMETRIC          LivenessFlow = 9
	LivenessFlow_BENEFICIARY_ACTIVATION    LivenessFlow = 10
	LivenessFlow_NON_RESIDENT_ONBOARDING   LivenessFlow = 11
)

// Enum value maps for LivenessFlow.
var (
	LivenessFlow_name = map[int32]string{
		0:  "LIVENESS_FLOW_UNSPECIFIED",
		1:  "ONBOARDING",
		2:  "SET_PIN",
		3:  "RESET_PIN",
		4:  "AUTH_FACTOR_UPDATE",
		5:  "WEALTH_ONBOARDING",
		6:  "PRE_APPROVED_LOANS",
		7:  "USER_ACTIONS",
		8:  "CREDIT_CARD",
		9:  "DEVICE_BIOMETRIC",
		10: "BENEFICIARY_ACTIVATION",
		11: "NON_RESIDENT_ONBOARDING",
	}
	LivenessFlow_value = map[string]int32{
		"LIVENESS_FLOW_UNSPECIFIED": 0,
		"ONBOARDING":                1,
		"SET_PIN":                   2,
		"RESET_PIN":                 3,
		"AUTH_FACTOR_UPDATE":        4,
		"WEALTH_ONBOARDING":         5,
		"PRE_APPROVED_LOANS":        6,
		"USER_ACTIONS":              7,
		"CREDIT_CARD":               8,
		"DEVICE_BIOMETRIC":          9,
		"BENEFICIARY_ACTIVATION":    10,
		"NON_RESIDENT_ONBOARDING":   11,
	}
)

func (x LivenessFlow) Enum() *LivenessFlow {
	p := new(LivenessFlow)
	*p = x
	return p
}

func (x LivenessFlow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LivenessFlow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_types_proto_enumTypes[4].Descriptor()
}

func (LivenessFlow) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_types_proto_enumTypes[4]
}

func (x LivenessFlow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LivenessFlow.Descriptor instead.
func (LivenessFlow) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_types_proto_rawDescGZIP(), []int{4}
}

type BlinkLivenessStatus int32

const (
	BlinkLivenessStatus_BLINK_LIVENESS_UNSPECIFIED BlinkLivenessStatus = 0
	BlinkLivenessStatus_BLINK_LIVENESS_SUCCESS     BlinkLivenessStatus = 1
	BlinkLivenessStatus_BLINK_LIVENESS_FAILED      BlinkLivenessStatus = 2
)

// Enum value maps for BlinkLivenessStatus.
var (
	BlinkLivenessStatus_name = map[int32]string{
		0: "BLINK_LIVENESS_UNSPECIFIED",
		1: "BLINK_LIVENESS_SUCCESS",
		2: "BLINK_LIVENESS_FAILED",
	}
	BlinkLivenessStatus_value = map[string]int32{
		"BLINK_LIVENESS_UNSPECIFIED": 0,
		"BLINK_LIVENESS_SUCCESS":     1,
		"BLINK_LIVENESS_FAILED":      2,
	}
)

func (x BlinkLivenessStatus) Enum() *BlinkLivenessStatus {
	p := new(BlinkLivenessStatus)
	*p = x
	return p
}

func (x BlinkLivenessStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BlinkLivenessStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_types_proto_enumTypes[5].Descriptor()
}

func (BlinkLivenessStatus) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_types_proto_enumTypes[5]
}

func (x BlinkLivenessStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BlinkLivenessStatus.Descriptor instead.
func (BlinkLivenessStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_types_proto_rawDescGZIP(), []int{5}
}

type InHouseLivenessStatus int32

const (
	InHouseLivenessStatus_IN_HOUSE_UNSPECIFIED InHouseLivenessStatus = 0
	// liveness verified.
	InHouseLivenessStatus_IN_HOUSE_SUCCESS InHouseLivenessStatus = 1
	// liveness not verified.
	InHouseLivenessStatus_IN_HOUSE_FAILED InHouseLivenessStatus = 2
	// unexpected error in the API
	InHouseLivenessStatus_IN_HOUSE_ERRORED InHouseLivenessStatus = 3
)

// Enum value maps for InHouseLivenessStatus.
var (
	InHouseLivenessStatus_name = map[int32]string{
		0: "IN_HOUSE_UNSPECIFIED",
		1: "IN_HOUSE_SUCCESS",
		2: "IN_HOUSE_FAILED",
		3: "IN_HOUSE_ERRORED",
	}
	InHouseLivenessStatus_value = map[string]int32{
		"IN_HOUSE_UNSPECIFIED": 0,
		"IN_HOUSE_SUCCESS":     1,
		"IN_HOUSE_FAILED":      2,
		"IN_HOUSE_ERRORED":     3,
	}
)

func (x InHouseLivenessStatus) Enum() *InHouseLivenessStatus {
	p := new(InHouseLivenessStatus)
	*p = x
	return p
}

func (x InHouseLivenessStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InHouseLivenessStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_types_proto_enumTypes[6].Descriptor()
}

func (InHouseLivenessStatus) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_types_proto_enumTypes[6]
}

func (x InHouseLivenessStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InHouseLivenessStatus.Descriptor instead.
func (InHouseLivenessStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_types_proto_rawDescGZIP(), []int{6}
}

type SummaryStatus int32

const (
	SummaryStatus_SUMMARY_UNSPECIFIED       SummaryStatus = 0
	SummaryStatus_SUMMARY_IN_PROGRESS       SummaryStatus = 1
	SummaryStatus_SUMMARY_PASSED            SummaryStatus = 2
	SummaryStatus_SUMMARY_RETRIES_EXHAUSTED SummaryStatus = 3
	SummaryStatus_SUMMARY_EXPIRED           SummaryStatus = 4
)

// Enum value maps for SummaryStatus.
var (
	SummaryStatus_name = map[int32]string{
		0: "SUMMARY_UNSPECIFIED",
		1: "SUMMARY_IN_PROGRESS",
		2: "SUMMARY_PASSED",
		3: "SUMMARY_RETRIES_EXHAUSTED",
		4: "SUMMARY_EXPIRED",
	}
	SummaryStatus_value = map[string]int32{
		"SUMMARY_UNSPECIFIED":       0,
		"SUMMARY_IN_PROGRESS":       1,
		"SUMMARY_PASSED":            2,
		"SUMMARY_RETRIES_EXHAUSTED": 3,
		"SUMMARY_EXPIRED":           4,
	}
)

func (x SummaryStatus) Enum() *SummaryStatus {
	p := new(SummaryStatus)
	*p = x
	return p
}

func (x SummaryStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SummaryStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_types_proto_enumTypes[7].Descriptor()
}

func (SummaryStatus) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_types_proto_enumTypes[7]
}

func (x SummaryStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SummaryStatus.Descriptor instead.
func (SummaryStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_types_proto_rawDescGZIP(), []int{7}
}

type SummaryLivenessStatus int32

const (
	SummaryLivenessStatus_SUMMARY_LIVENESS_UNSPECIFIED     SummaryLivenessStatus = 0
	SummaryLivenessStatus_SUMMARY_LIVENESS_IN_PROGRESS     SummaryLivenessStatus = 1
	SummaryLivenessStatus_SUMMARY_LIVENESS_COMPLETED       SummaryLivenessStatus = 2
	SummaryLivenessStatus_SUMMARY_LIVENESS_RETRY           SummaryLivenessStatus = 3
	SummaryLivenessStatus_SUMMARY_LIVENESS_MANUALLY_PASSED SummaryLivenessStatus = 4
	SummaryLivenessStatus_SUMMARY_LIVENESS_MANUALLY_FAILED SummaryLivenessStatus = 5
)

// Enum value maps for SummaryLivenessStatus.
var (
	SummaryLivenessStatus_name = map[int32]string{
		0: "SUMMARY_LIVENESS_UNSPECIFIED",
		1: "SUMMARY_LIVENESS_IN_PROGRESS",
		2: "SUMMARY_LIVENESS_COMPLETED",
		3: "SUMMARY_LIVENESS_RETRY",
		4: "SUMMARY_LIVENESS_MANUALLY_PASSED",
		5: "SUMMARY_LIVENESS_MANUALLY_FAILED",
	}
	SummaryLivenessStatus_value = map[string]int32{
		"SUMMARY_LIVENESS_UNSPECIFIED":     0,
		"SUMMARY_LIVENESS_IN_PROGRESS":     1,
		"SUMMARY_LIVENESS_COMPLETED":       2,
		"SUMMARY_LIVENESS_RETRY":           3,
		"SUMMARY_LIVENESS_MANUALLY_PASSED": 4,
		"SUMMARY_LIVENESS_MANUALLY_FAILED": 5,
	}
)

func (x SummaryLivenessStatus) Enum() *SummaryLivenessStatus {
	p := new(SummaryLivenessStatus)
	*p = x
	return p
}

func (x SummaryLivenessStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SummaryLivenessStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_types_proto_enumTypes[8].Descriptor()
}

func (SummaryLivenessStatus) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_types_proto_enumTypes[8]
}

func (x SummaryLivenessStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SummaryLivenessStatus.Descriptor instead.
func (SummaryLivenessStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_types_proto_rawDescGZIP(), []int{8}
}

type SummaryFacematchStatus int32

const (
	SummaryFacematchStatus_SUMMARY_FACEMATCH_UNSPECIFIED     SummaryFacematchStatus = 0
	SummaryFacematchStatus_SUMMARY_FACEMATCH_IN_PROGRESS     SummaryFacematchStatus = 1
	SummaryFacematchStatus_SUMMARY_FACEMATCH_COMPLETED       SummaryFacematchStatus = 2
	SummaryFacematchStatus_SUMMARY_FACEMATCH_RETRY           SummaryFacematchStatus = 3
	SummaryFacematchStatus_SUMMARY_FACEMATCH_MANUALLY_PASSED SummaryFacematchStatus = 4
	SummaryFacematchStatus_SUMMARY_FACEMATCH_MANUALLY_FAILED SummaryFacematchStatus = 5
)

// Enum value maps for SummaryFacematchStatus.
var (
	SummaryFacematchStatus_name = map[int32]string{
		0: "SUMMARY_FACEMATCH_UNSPECIFIED",
		1: "SUMMARY_FACEMATCH_IN_PROGRESS",
		2: "SUMMARY_FACEMATCH_COMPLETED",
		3: "SUMMARY_FACEMATCH_RETRY",
		4: "SUMMARY_FACEMATCH_MANUALLY_PASSED",
		5: "SUMMARY_FACEMATCH_MANUALLY_FAILED",
	}
	SummaryFacematchStatus_value = map[string]int32{
		"SUMMARY_FACEMATCH_UNSPECIFIED":     0,
		"SUMMARY_FACEMATCH_IN_PROGRESS":     1,
		"SUMMARY_FACEMATCH_COMPLETED":       2,
		"SUMMARY_FACEMATCH_RETRY":           3,
		"SUMMARY_FACEMATCH_MANUALLY_PASSED": 4,
		"SUMMARY_FACEMATCH_MANUALLY_FAILED": 5,
	}
)

func (x SummaryFacematchStatus) Enum() *SummaryFacematchStatus {
	p := new(SummaryFacematchStatus)
	*p = x
	return p
}

func (x SummaryFacematchStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SummaryFacematchStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_types_proto_enumTypes[9].Descriptor()
}

func (SummaryFacematchStatus) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_types_proto_enumTypes[9]
}

func (x SummaryFacematchStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SummaryFacematchStatus.Descriptor instead.
func (SummaryFacematchStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_types_proto_rawDescGZIP(), []int{9}
}

// Every case contains set of conditions each user that is eligible for case should pass these set of condition for liveness pass.
type StrictnessLogic int32

const (
	StrictnessLogic_STRICTNESS_LOGIC_UNSPECIFIED StrictnessLogic = 0
	StrictnessLogic_STRICTNESS_LOGIC_DEFAULT     StrictnessLogic = 1
	// karza liveness scrore > 90 and inhouse should pass and facematch score > 80
	StrictnessLogic_STRICTNESS_LOGIC_A StrictnessLogic = 2
)

// Enum value maps for StrictnessLogic.
var (
	StrictnessLogic_name = map[int32]string{
		0: "STRICTNESS_LOGIC_UNSPECIFIED",
		1: "STRICTNESS_LOGIC_DEFAULT",
		2: "STRICTNESS_LOGIC_A",
	}
	StrictnessLogic_value = map[string]int32{
		"STRICTNESS_LOGIC_UNSPECIFIED": 0,
		"STRICTNESS_LOGIC_DEFAULT":     1,
		"STRICTNESS_LOGIC_A":           2,
	}
)

func (x StrictnessLogic) Enum() *StrictnessLogic {
	p := new(StrictnessLogic)
	*p = x
	return p
}

func (x StrictnessLogic) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StrictnessLogic) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_types_proto_enumTypes[10].Descriptor()
}

func (StrictnessLogic) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_types_proto_enumTypes[10]
}

func (x StrictnessLogic) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StrictnessLogic.Descriptor instead.
func (StrictnessLogic) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_types_proto_rawDescGZIP(), []int{10}
}

// This stores a blanket status of liveness attempts
// This translates from individual attempt status to an overall status
type LivenessBlanketStatus int32

const (
	LivenessBlanketStatus_LIVENESS_BLANKET_STATUS_UNSPECIFIED    LivenessBlanketStatus = 0
	LivenessBlanketStatus_LIVENESS_BLANKET_STATUS_VIDEO_RECEIVED LivenessBlanketStatus = 1
	LivenessBlanketStatus_LIVENESS_BLANKET_STATUS_SUCCEEDED      LivenessBlanketStatus = 2
	LivenessBlanketStatus_LIVENESS_BLANKET_STATUS_FAILED         LivenessBlanketStatus = 3
)

// Enum value maps for LivenessBlanketStatus.
var (
	LivenessBlanketStatus_name = map[int32]string{
		0: "LIVENESS_BLANKET_STATUS_UNSPECIFIED",
		1: "LIVENESS_BLANKET_STATUS_VIDEO_RECEIVED",
		2: "LIVENESS_BLANKET_STATUS_SUCCEEDED",
		3: "LIVENESS_BLANKET_STATUS_FAILED",
	}
	LivenessBlanketStatus_value = map[string]int32{
		"LIVENESS_BLANKET_STATUS_UNSPECIFIED":    0,
		"LIVENESS_BLANKET_STATUS_VIDEO_RECEIVED": 1,
		"LIVENESS_BLANKET_STATUS_SUCCEEDED":      2,
		"LIVENESS_BLANKET_STATUS_FAILED":         3,
	}
)

func (x LivenessBlanketStatus) Enum() *LivenessBlanketStatus {
	p := new(LivenessBlanketStatus)
	*p = x
	return p
}

func (x LivenessBlanketStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LivenessBlanketStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_types_proto_enumTypes[11].Descriptor()
}

func (LivenessBlanketStatus) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_types_proto_enumTypes[11]
}

func (x LivenessBlanketStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LivenessBlanketStatus.Descriptor instead.
func (LivenessBlanketStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_types_proto_rawDescGZIP(), []int{11}
}

// Liveness bucket is the separation of buckets of each liveness attempt based on various logics as shared by the DS team
type LivenessBucket int32

const (
	LivenessBucket_LIVENESS_BUCKET_UNSPECIFIED   LivenessBucket = 0
	LivenessBucket_LIVENESS_BUCKET_REJECT        LivenessBucket = 1
	LivenessBucket_LIVENESS_BUCKET_PASS          LivenessBucket = 2
	LivenessBucket_LIVENESS_BUCKET_STRONG_REJECT LivenessBucket = 3
	LivenessBucket_LIVENESS_BUCKET_STRONG_PASS   LivenessBucket = 4
)

// Enum value maps for LivenessBucket.
var (
	LivenessBucket_name = map[int32]string{
		0: "LIVENESS_BUCKET_UNSPECIFIED",
		1: "LIVENESS_BUCKET_REJECT",
		2: "LIVENESS_BUCKET_PASS",
		3: "LIVENESS_BUCKET_STRONG_REJECT",
		4: "LIVENESS_BUCKET_STRONG_PASS",
	}
	LivenessBucket_value = map[string]int32{
		"LIVENESS_BUCKET_UNSPECIFIED":   0,
		"LIVENESS_BUCKET_REJECT":        1,
		"LIVENESS_BUCKET_PASS":          2,
		"LIVENESS_BUCKET_STRONG_REJECT": 3,
		"LIVENESS_BUCKET_STRONG_PASS":   4,
	}
)

func (x LivenessBucket) Enum() *LivenessBucket {
	p := new(LivenessBucket)
	*p = x
	return p
}

func (x LivenessBucket) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LivenessBucket) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_liveness_types_proto_enumTypes[12].Descriptor()
}

func (LivenessBucket) Type() protoreflect.EnumType {
	return &file_api_auth_liveness_types_proto_enumTypes[12]
}

func (x LivenessBucket) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LivenessBucket.Descriptor instead.
func (LivenessBucket) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_liveness_types_proto_rawDescGZIP(), []int{12}
}

var File_api_auth_liveness_types_proto protoreflect.FileDescriptor

var file_api_auth_liveness_types_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0d, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2a, 0x58,
	0x0a, 0x0d, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x1e, 0x0a, 0x1a, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4c, 0x45, 0x56, 0x45,
	0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x14, 0x0a, 0x10, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4c, 0x45, 0x4e, 0x49,
	0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53,
	0x53, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x10, 0x02, 0x2a, 0x5f, 0x0a, 0x0e, 0x46, 0x61, 0x63, 0x65,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x20, 0x0a, 0x1c, 0x46, 0x41,
	0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12,
	0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x4c, 0x45, 0x4e, 0x49, 0x45,
	0x4e, 0x54, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x10, 0x02, 0x2a, 0xd7, 0x01, 0x0a, 0x0f, 0x46, 0x61,
	0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a,
	0x1d, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x16, 0x0a, 0x12, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x50,
	0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x41, 0x43, 0x45,
	0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x45, 0x44, 0x10, 0x02, 0x12,
	0x1b, 0x0a, 0x17, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11,
	0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x45,
	0x44, 0x10, 0x05, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x06, 0x2a, 0xf1, 0x04, 0x0a, 0x0e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45,
	0x53, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x49, 0x56, 0x45, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53,
	0x54, 0x52, 0x45, 0x41, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x45, 0x44,
	0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x56,
	0x49, 0x44, 0x45, 0x4f, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x44, 0x10, 0x03, 0x12,
	0x14, 0x0a, 0x10, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53,
	0x53, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x45, 0x44, 0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x49,
	0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x52, 0x45,
	0x54, 0x52, 0x59, 0x10, 0x06, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53,
	0x53, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43,
	0x54, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x24, 0x0a, 0x20, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53,
	0x53, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c, 0x45, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x53,
	0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x0b, 0x12, 0x19, 0x0a, 0x15, 0x4c,
	0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52,
	0x45, 0x54, 0x52, 0x59, 0x10, 0x08, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45,
	0x53, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f,
	0x10, 0x09, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x07, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x49, 0x56, 0x45, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x50, 0x4f, 0x4f, 0x52, 0x4c, 0x59, 0x5f,
	0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x0c, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x49,
	0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x5f,
	0x46, 0x41, 0x52, 0x10, 0x0d, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53,
	0x53, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45,
	0x10, 0x0e, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46,
	0x41, 0x43, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x44, 0x41, 0x52, 0x4b, 0x10, 0x0f, 0x12, 0x1c,
	0x0a, 0x18, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f,
	0x54, 0x4f, 0x4f, 0x5f, 0x42, 0x52, 0x49, 0x47, 0x48, 0x54, 0x10, 0x10, 0x12, 0x1d, 0x0a, 0x19,
	0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4e, 0x4f, 0x5f, 0x46, 0x41, 0x43, 0x45,
	0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x11, 0x12, 0x1c, 0x0a, 0x18, 0x4c,
	0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x4c, 0x59,
	0x5f, 0x50, 0x41, 0x53, 0x53, 0x45, 0x44, 0x10, 0x12, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x13, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x49, 0x56, 0x45, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x51, 0x55, 0x45, 0x55, 0x45, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x45,
	0x54, 0x52, 0x49, 0x45, 0x53, 0x10, 0x14, 0x2a, 0x92, 0x02, 0x0a, 0x0c, 0x4c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x49, 0x56, 0x45,
	0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x4e, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x45, 0x54, 0x5f, 0x50,
	0x49, 0x4e, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x45, 0x53, 0x45, 0x54, 0x5f, 0x50, 0x49,
	0x4e, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x46, 0x41, 0x43, 0x54,
	0x4f, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x57,
	0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x05, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56,
	0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x10, 0x06, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b,
	0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x08, 0x12, 0x14, 0x0a,
	0x10, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49,
	0x43, 0x10, 0x09, 0x12, 0x1a, 0x0a, 0x16, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x43, 0x49, 0x41,
	0x52, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0a, 0x12,
	0x1b, 0x0a, 0x17, 0x4e, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x0b, 0x2a, 0x6c, 0x0a, 0x13,
	0x42, 0x6c, 0x69, 0x6e, 0x6b, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x42, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x42, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12,
	0x19, 0x0a, 0x15, 0x42, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53,
	0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x72, 0x0a, 0x15, 0x49, 0x6e,
	0x48, 0x6f, 0x75, 0x73, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e, 0x5f, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x49, 0x4e, 0x5f, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x4e, 0x5f, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x5f, 0x48,
	0x4f, 0x55, 0x53, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x89,
	0x01, 0x0a, 0x0d, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x17, 0x0a, 0x13, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x55, 0x4d,
	0x4d, 0x41, 0x52, 0x59, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53,
	0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x41,
	0x53, 0x53, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52,
	0x59, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x48, 0x41, 0x55, 0x53,
	0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59,
	0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x04, 0x2a, 0xe3, 0x01, 0x0a, 0x15, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x1c, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f,
	0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52,
	0x59, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52,
	0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x55, 0x4d, 0x4d,
	0x41, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x55, 0x4d, 0x4d,
	0x41, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x54,
	0x52, 0x59, 0x10, 0x03, 0x12, 0x24, 0x0a, 0x20, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f,
	0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x4c,
	0x59, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x45, 0x44, 0x10, 0x04, 0x12, 0x24, 0x0a, 0x20, 0x53, 0x55,
	0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4d,
	0x41, 0x4e, 0x55, 0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05,
	0x2a, 0xea, 0x01, 0x0a, 0x16, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x61, 0x63, 0x65,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x1d, 0x53,
	0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x4d, 0x41, 0x54, 0x43, 0x48,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21,
	0x0a, 0x1d, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x4d, 0x41,
	0x54, 0x43, 0x48, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10,
	0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x46, 0x41, 0x43,
	0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44,
	0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x46, 0x41,
	0x43, 0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x10, 0x03, 0x12,
	0x25, 0x0a, 0x21, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x4d,
	0x41, 0x54, 0x43, 0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x50, 0x41,
	0x53, 0x53, 0x45, 0x44, 0x10, 0x04, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52,
	0x59, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55,
	0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x2a, 0x69, 0x0a,
	0x0f, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x63,
	0x12, 0x20, 0x0a, 0x1c, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4c,
	0x4f, 0x47, 0x49, 0x43, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x43, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x01,
	0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4c,
	0x4f, 0x47, 0x49, 0x43, 0x5f, 0x41, 0x10, 0x02, 0x2a, 0xb7, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x42, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x42,
	0x4c, 0x41, 0x4e, 0x4b, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a, 0x26, 0x4c,
	0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x42, 0x4c, 0x41, 0x4e, 0x4b, 0x45, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x52, 0x45, 0x43,
	0x45, 0x49, 0x56, 0x45, 0x44, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x4c, 0x49, 0x56, 0x45, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x42, 0x4c, 0x41, 0x4e, 0x4b, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x02, 0x12, 0x22,
	0x0a, 0x1e, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x42, 0x4c, 0x41, 0x4e, 0x4b,
	0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x03, 0x2a, 0xab, 0x01, 0x0a, 0x0e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x42,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53,
	0x53, 0x5f, 0x42, 0x55, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45,
	0x53, 0x53, 0x5f, 0x42, 0x55, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54,
	0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x42,
	0x55, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d,
	0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x42, 0x55, 0x43, 0x4b, 0x45, 0x54, 0x5f,
	0x53, 0x54, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x10, 0x03, 0x12,
	0x1f, 0x0a, 0x1b, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x42, 0x55, 0x43, 0x4b,
	0x45, 0x54, 0x5f, 0x53, 0x54, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x10, 0x04,
	0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5a, 0x28, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_liveness_types_proto_rawDescOnce sync.Once
	file_api_auth_liveness_types_proto_rawDescData = file_api_auth_liveness_types_proto_rawDesc
)

func file_api_auth_liveness_types_proto_rawDescGZIP() []byte {
	file_api_auth_liveness_types_proto_rawDescOnce.Do(func() {
		file_api_auth_liveness_types_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_liveness_types_proto_rawDescData)
	})
	return file_api_auth_liveness_types_proto_rawDescData
}

var file_api_auth_liveness_types_proto_enumTypes = make([]protoimpl.EnumInfo, 13)
var file_api_auth_liveness_types_proto_goTypes = []interface{}{
	(LivenessLevel)(0),          // 0: auth.liveness.LivenessLevel
	(FaceMatchLevel)(0),         // 1: auth.liveness.FaceMatchLevel
	(FaceMatchStatus)(0),        // 2: auth.liveness.FaceMatchStatus
	(LivenessStatus)(0),         // 3: auth.liveness.LivenessStatus
	(LivenessFlow)(0),           // 4: auth.liveness.LivenessFlow
	(BlinkLivenessStatus)(0),    // 5: auth.liveness.BlinkLivenessStatus
	(InHouseLivenessStatus)(0),  // 6: auth.liveness.InHouseLivenessStatus
	(SummaryStatus)(0),          // 7: auth.liveness.SummaryStatus
	(SummaryLivenessStatus)(0),  // 8: auth.liveness.SummaryLivenessStatus
	(SummaryFacematchStatus)(0), // 9: auth.liveness.SummaryFacematchStatus
	(StrictnessLogic)(0),        // 10: auth.liveness.StrictnessLogic
	(LivenessBlanketStatus)(0),  // 11: auth.liveness.LivenessBlanketStatus
	(LivenessBucket)(0),         // 12: auth.liveness.LivenessBucket
}
var file_api_auth_liveness_types_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_auth_liveness_types_proto_init() }
func file_api_auth_liveness_types_proto_init() {
	if File_api_auth_liveness_types_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_liveness_types_proto_rawDesc,
			NumEnums:      13,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_liveness_types_proto_goTypes,
		DependencyIndexes: file_api_auth_liveness_types_proto_depIdxs,
		EnumInfos:         file_api_auth_liveness_types_proto_enumTypes,
	}.Build()
	File_api_auth_liveness_types_proto = out.File
	file_api_auth_liveness_types_proto_rawDesc = nil
	file_api_auth_liveness_types_proto_goTypes = nil
	file_api_auth_liveness_types_proto_depIdxs = nil
}
