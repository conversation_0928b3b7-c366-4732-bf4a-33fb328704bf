package liveness

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/logger"
)

func (x AnnotationType) Value() (driver.Value, error) {
	return x.String(), nil
}

func (x *AnnotationType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := AnnotationType_value[val]
	if !ok {
		logger.ErrorNoCtx(fmt.Sprintf("encountered unknown  Annotation Type : %v", val))
	}
	*x = AnnotationType(valInt)
	return nil
}

func (x *LivenessFMAnnotationPayload) Scan(input interface{}) error {
	val, ok := input.([]byte)
	if !ok {
		err := fmt.Errorf("expected []byte, got %T", input)
		return err
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, val, x)
}

// Valuer interface implementation for storing the data in JSONB format in DB
func (x *LivenessFMAnnotationPayload) Value() (driver.Value, error) {
	return protojson.Marshal(x)
}
