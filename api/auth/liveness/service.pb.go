// protolint:disable MAX_LINE_LENGTH

//
//Defines a service for liveness and face match-related data and operations.
//This service provides the following functionality:
// Verify Face Match
// Verify Liveness
//It has both sync and async form of requests.
//Sync waits for the full process to complete and return the final result.
//For Async user has to poll and get the status to check the result.
//There are no retries in this service and user of this service should handle the retry according
//to their use case.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/liveness/service.proto

package liveness

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Frames struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Frames []int64 `protobuf:"varint,1,rep,packed,name=frames,proto3" json:"frames,omitempty"`
}

func (x *Frames) Reset() {
	*x = Frames{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Frames) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Frames) ProtoMessage() {}

func (x *Frames) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Frames.ProtoReflect.Descriptor instead.
func (*Frames) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{0}
}

func (x *Frames) GetFrames() []int64 {
	if x != nil {
		return x.Frames
	}
	return nil
}

type GetLivenessStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Attempt ID to get the status of the particular liveness attempt.
	AttemptId string `protobuf:"bytes,2,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
}

func (x *GetLivenessStatusRequest) Reset() {
	*x = GetLivenessStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLivenessStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLivenessStatusRequest) ProtoMessage() {}

func (x *GetLivenessStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLivenessStatusRequest.ProtoReflect.Descriptor instead.
func (*GetLivenessStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetLivenessStatusRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetLivenessStatusRequest) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

type GetLivenessStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status         *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LivenessStatus LivenessStatus `protobuf:"varint,2,opt,name=liveness_status,json=livenessStatus,proto3,enum=auth.liveness.LivenessStatus" json:"liveness_status,omitempty"`
	VideoLocation  string         `protobuf:"bytes,3,opt,name=video_location,json=videoLocation,proto3" json:"video_location,omitempty"`
	Frame          *common.Image  `protobuf:"bytes,4,opt,name=frame,proto3" json:"frame,omitempty"`
	Otp            string         `protobuf:"bytes,5,opt,name=otp,proto3" json:"otp,omitempty"`
	LivenessScore  float32        `protobuf:"fixed32,6,opt,name=liveness_score,json=livenessScore,proto3" json:"liveness_score,omitempty"`
	OtpScore       float32        `protobuf:"fixed32,7,opt,name=otp_score,json=otpScore,proto3" json:"otp_score,omitempty"`
	// Threshold for liveness to pass.
	LivenessScoreThreshold float32 `protobuf:"fixed32,8,opt,name=liveness_score_threshold,json=livenessScoreThreshold,proto3" json:"liveness_score_threshold,omitempty"`
	// Threshold for otp match to pass.
	OtpScoreThreshold float32 `protobuf:"fixed32,9,opt,name=otp_score_threshold,json=otpScoreThreshold,proto3" json:"otp_score_threshold,omitempty"`
}

func (x *GetLivenessStatusResponse) Reset() {
	*x = GetLivenessStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLivenessStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLivenessStatusResponse) ProtoMessage() {}

func (x *GetLivenessStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLivenessStatusResponse.ProtoReflect.Descriptor instead.
func (*GetLivenessStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetLivenessStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLivenessStatusResponse) GetLivenessStatus() LivenessStatus {
	if x != nil {
		return x.LivenessStatus
	}
	return LivenessStatus_LIVENESS_STATUS_UNSPECIFIED
}

func (x *GetLivenessStatusResponse) GetVideoLocation() string {
	if x != nil {
		return x.VideoLocation
	}
	return ""
}

func (x *GetLivenessStatusResponse) GetFrame() *common.Image {
	if x != nil {
		return x.Frame
	}
	return nil
}

func (x *GetLivenessStatusResponse) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *GetLivenessStatusResponse) GetLivenessScore() float32 {
	if x != nil {
		return x.LivenessScore
	}
	return 0
}

func (x *GetLivenessStatusResponse) GetOtpScore() float32 {
	if x != nil {
		return x.OtpScore
	}
	return 0
}

func (x *GetLivenessStatusResponse) GetLivenessScoreThreshold() float32 {
	if x != nil {
		return x.LivenessScoreThreshold
	}
	return 0
}

func (x *GetLivenessStatusResponse) GetOtpScoreThreshold() float32 {
	if x != nil {
		return x.OtpScoreThreshold
	}
	return 0
}

type FaceMatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Attempt ID that can be used to get the status of this attempt of face match.
	AttemptId      string         `protobuf:"bytes,2,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
	FaceMatchLevel FaceMatchLevel `protobuf:"varint,3,opt,name=face_match_level,json=faceMatchLevel,proto3,enum=auth.liveness.FaceMatchLevel" json:"face_match_level,omitempty"`
	// sync=True implies the face match has to be completed before responding.
	// This is always true. Async responses not supported right now.
	Sync bool `protobuf:"varint,4,opt,name=sync,proto3" json:"sync,omitempty"`
	// reference image to compare for the face match comparision.
	Image      *common.Image `protobuf:"bytes,5,opt,name=image,proto3" json:"image,omitempty"`
	ImageFrame *common.Image `protobuf:"bytes,8,opt,name=image_frame,json=imageFrame,proto3" json:"image_frame,omitempty"`
	// video_location represents video location of the video to run the face match on.
	// Currently, only Amazon S3 URLs are acceptable for video locations.
	VideoLocation string `protobuf:"bytes,6,opt,name=video_location,json=videoLocation,proto3" json:"video_location,omitempty"`
	// summary id of the related liveness summary
	SummaryId string `protobuf:"bytes,7,opt,name=summary_id,json=summaryId,proto3" json:"summary_id,omitempty"`
	// Threshold with which the score needs to be compared to pass the attempt
	// If no threshold is given then it is compared against a default threshold
	Threshold float32 `protobuf:"fixed32,9,opt,name=threshold,proto3" json:"threshold,omitempty"`
	// Strict liveness logic contains condition for liveness facematch check
	//
	// Deprecated: Marked as deprecated in api/auth/liveness/service.proto.
	StrictnessLogic StrictnessLogic `protobuf:"varint,10,opt,name=strictness_logic,json=strictnessLogic,proto3,enum=auth.liveness.StrictnessLogic" json:"strictness_logic,omitempty"`
	// Request id of the liveness attempt for which facematch needs to be done
	LivenessRequestId string `protobuf:"bytes,11,opt,name=liveness_request_id,json=livenessRequestId,proto3" json:"liveness_request_id,omitempty"`
	IsSourceVideo     bool   `protobuf:"varint,12,opt,name=is_source_video,json=isSourceVideo,proto3" json:"is_source_video,omitempty"`
}

func (x *FaceMatchRequest) Reset() {
	*x = FaceMatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceMatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceMatchRequest) ProtoMessage() {}

func (x *FaceMatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceMatchRequest.ProtoReflect.Descriptor instead.
func (*FaceMatchRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{3}
}

func (x *FaceMatchRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *FaceMatchRequest) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *FaceMatchRequest) GetFaceMatchLevel() FaceMatchLevel {
	if x != nil {
		return x.FaceMatchLevel
	}
	return FaceMatchLevel_FACE_MATCH_LEVEL_UNSPECIFIED
}

func (x *FaceMatchRequest) GetSync() bool {
	if x != nil {
		return x.Sync
	}
	return false
}

func (x *FaceMatchRequest) GetImage() *common.Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *FaceMatchRequest) GetImageFrame() *common.Image {
	if x != nil {
		return x.ImageFrame
	}
	return nil
}

func (x *FaceMatchRequest) GetVideoLocation() string {
	if x != nil {
		return x.VideoLocation
	}
	return ""
}

func (x *FaceMatchRequest) GetSummaryId() string {
	if x != nil {
		return x.SummaryId
	}
	return ""
}

func (x *FaceMatchRequest) GetThreshold() float32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

// Deprecated: Marked as deprecated in api/auth/liveness/service.proto.
func (x *FaceMatchRequest) GetStrictnessLogic() StrictnessLogic {
	if x != nil {
		return x.StrictnessLogic
	}
	return StrictnessLogic_STRICTNESS_LOGIC_UNSPECIFIED
}

func (x *FaceMatchRequest) GetLivenessRequestId() string {
	if x != nil {
		return x.LivenessRequestId
	}
	return ""
}

func (x *FaceMatchRequest) GetIsSourceVideo() bool {
	if x != nil {
		return x.IsSourceVideo
	}
	return false
}

type FaceMatchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request.
	Status          *rpc.Status     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	FaceMatchStatus FaceMatchStatus `protobuf:"varint,2,opt,name=face_match_status,json=faceMatchStatus,proto3,enum=auth.liveness.FaceMatchStatus" json:"face_match_status,omitempty"`
	// Attempt ID that can be used to get the status of this attempt of liveness.
	AttemptId string `protobuf:"bytes,3,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
}

func (x *FaceMatchResponse) Reset() {
	*x = FaceMatchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceMatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceMatchResponse) ProtoMessage() {}

func (x *FaceMatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceMatchResponse.ProtoReflect.Descriptor instead.
func (*FaceMatchResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{4}
}

func (x *FaceMatchResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FaceMatchResponse) GetFaceMatchStatus() FaceMatchStatus {
	if x != nil {
		return x.FaceMatchStatus
	}
	return FaceMatchStatus_FACE_MATCH_STATUS_UNSPECIFIED
}

func (x *FaceMatchResponse) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

type GetFaceMatchStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Attempt ID to get the status of the particular face match attempt.
	AttemptId string `protobuf:"bytes,2,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
}

func (x *GetFaceMatchStatusRequest) Reset() {
	*x = GetFaceMatchStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFaceMatchStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFaceMatchStatusRequest) ProtoMessage() {}

func (x *GetFaceMatchStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFaceMatchStatusRequest.ProtoReflect.Descriptor instead.
func (*GetFaceMatchStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetFaceMatchStatusRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetFaceMatchStatusRequest) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

type GetFaceMatchStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status          *rpc.Status     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	FaceMatchStatus FaceMatchStatus `protobuf:"varint,2,opt,name=face_match_status,json=faceMatchStatus,proto3,enum=auth.liveness.FaceMatchStatus" json:"face_match_status,omitempty"`
	// if status is not in terminal state, then this value is 0.
	FaceMatchScore float32 `protobuf:"fixed32,3,opt,name=face_match_score,json=faceMatchScore,proto3" json:"face_match_score,omitempty"`
	// Threshold for face match to pass.
	FmScoreThreshold float32 `protobuf:"fixed32,4,opt,name=fm_score_threshold,json=fmScoreThreshold,proto3" json:"fm_score_threshold,omitempty"`
}

func (x *GetFaceMatchStatusResponse) Reset() {
	*x = GetFaceMatchStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFaceMatchStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFaceMatchStatusResponse) ProtoMessage() {}

func (x *GetFaceMatchStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFaceMatchStatusResponse.ProtoReflect.Descriptor instead.
func (*GetFaceMatchStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetFaceMatchStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetFaceMatchStatusResponse) GetFaceMatchStatus() FaceMatchStatus {
	if x != nil {
		return x.FaceMatchStatus
	}
	return FaceMatchStatus_FACE_MATCH_STATUS_UNSPECIFIED
}

func (x *GetFaceMatchStatusResponse) GetFaceMatchScore() float32 {
	if x != nil {
		return x.FaceMatchScore
	}
	return 0
}

func (x *GetFaceMatchStatusResponse) GetFmScoreThreshold() float32 {
	if x != nil {
		return x.FmScoreThreshold
	}
	return 0
}

type LivenessOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Attempt ID that can be used to get the status of this attempt of liveness.
	AttemptId     string           `protobuf:"bytes,2,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
	LivenessLevel LivenessLevel    `protobuf:"varint,3,opt,name=liveness_level,json=livenessLevel,proto3,enum=auth.liveness.LivenessLevel" json:"liveness_level,omitempty"`
	LivenessFlow  LivenessFlow     `protobuf:"varint,4,opt,name=liveness_flow,json=livenessFlow,proto3,enum=auth.liveness.LivenessFlow" json:"liveness_flow,omitempty"`
	VpnInfo       *typesv2.VPNInfo `protobuf:"bytes,5,opt,name=vpn_info,json=vpnInfo,proto3" json:"vpn_info,omitempty"`
}

func (x *LivenessOptions) Reset() {
	*x = LivenessOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessOptions) ProtoMessage() {}

func (x *LivenessOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessOptions.ProtoReflect.Descriptor instead.
func (*LivenessOptions) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{7}
}

func (x *LivenessOptions) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *LivenessOptions) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *LivenessOptions) GetLivenessLevel() LivenessLevel {
	if x != nil {
		return x.LivenessLevel
	}
	return LivenessLevel_LIVENESS_LEVEL_UNSPECIFIED
}

func (x *LivenessOptions) GetLivenessFlow() LivenessFlow {
	if x != nil {
		return x.LivenessFlow
	}
	return LivenessFlow_LIVENESS_FLOW_UNSPECIFIED
}

func (x *LivenessOptions) GetVpnInfo() *typesv2.VPNInfo {
	if x != nil {
		return x.VpnInfo
	}
	return nil
}

type CheckLivenessWithVideoStreamRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// First chunk in the stream needs to be the options chunk and the later ones part of videos.
	//
	// Types that are assignable to MetadataVideoChunks:
	//
	//	*CheckLivenessWithVideoStreamRequest_Options
	//	*CheckLivenessWithVideoStreamRequest_VideoChunk
	//	*CheckLivenessWithVideoStreamRequest_ImageFrame
	//	*CheckLivenessWithVideoStreamRequest_PassiveImageFrames
	MetadataVideoChunks isCheckLivenessWithVideoStreamRequest_MetadataVideoChunks `protobuf_oneof:"metadata_video_chunks"`
}

func (x *CheckLivenessWithVideoStreamRequest) Reset() {
	*x = CheckLivenessWithVideoStreamRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckLivenessWithVideoStreamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckLivenessWithVideoStreamRequest) ProtoMessage() {}

func (x *CheckLivenessWithVideoStreamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckLivenessWithVideoStreamRequest.ProtoReflect.Descriptor instead.
func (*CheckLivenessWithVideoStreamRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{8}
}

func (m *CheckLivenessWithVideoStreamRequest) GetMetadataVideoChunks() isCheckLivenessWithVideoStreamRequest_MetadataVideoChunks {
	if m != nil {
		return m.MetadataVideoChunks
	}
	return nil
}

func (x *CheckLivenessWithVideoStreamRequest) GetOptions() *LivenessOptions {
	if x, ok := x.GetMetadataVideoChunks().(*CheckLivenessWithVideoStreamRequest_Options); ok {
		return x.Options
	}
	return nil
}

func (x *CheckLivenessWithVideoStreamRequest) GetVideoChunk() []byte {
	if x, ok := x.GetMetadataVideoChunks().(*CheckLivenessWithVideoStreamRequest_VideoChunk); ok {
		return x.VideoChunk
	}
	return nil
}

func (x *CheckLivenessWithVideoStreamRequest) GetImageFrame() int64 {
	if x, ok := x.GetMetadataVideoChunks().(*CheckLivenessWithVideoStreamRequest_ImageFrame); ok {
		return x.ImageFrame
	}
	return 0
}

func (x *CheckLivenessWithVideoStreamRequest) GetPassiveImageFrames() *Frames {
	if x, ok := x.GetMetadataVideoChunks().(*CheckLivenessWithVideoStreamRequest_PassiveImageFrames); ok {
		return x.PassiveImageFrames
	}
	return nil
}

type isCheckLivenessWithVideoStreamRequest_MetadataVideoChunks interface {
	isCheckLivenessWithVideoStreamRequest_MetadataVideoChunks()
}

type CheckLivenessWithVideoStreamRequest_Options struct {
	Options *LivenessOptions `protobuf:"bytes,1,opt,name=options,proto3,oneof"`
}

type CheckLivenessWithVideoStreamRequest_VideoChunk struct {
	// expected format: .mp4
	VideoChunk []byte `protobuf:"bytes,2,opt,name=video_chunk,json=videoChunk,proto3,oneof"`
}

type CheckLivenessWithVideoStreamRequest_ImageFrame struct {
	// image frame
	ImageFrame int64 `protobuf:"varint,4,opt,name=image_frame,json=imageFrame,proto3,oneof"`
}

type CheckLivenessWithVideoStreamRequest_PassiveImageFrames struct {
	PassiveImageFrames *Frames `protobuf:"bytes,5,opt,name=passive_image_frames,json=passiveImageFrames,proto3,oneof"`
}

func (*CheckLivenessWithVideoStreamRequest_Options) isCheckLivenessWithVideoStreamRequest_MetadataVideoChunks() {
}

func (*CheckLivenessWithVideoStreamRequest_VideoChunk) isCheckLivenessWithVideoStreamRequest_MetadataVideoChunks() {
}

func (*CheckLivenessWithVideoStreamRequest_ImageFrame) isCheckLivenessWithVideoStreamRequest_MetadataVideoChunks() {
}

func (*CheckLivenessWithVideoStreamRequest_PassiveImageFrames) isCheckLivenessWithVideoStreamRequest_MetadataVideoChunks() {
}

type CheckLivenessWithVideoStreamResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status         *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LivenessStatus LivenessStatus `protobuf:"varint,2,opt,name=liveness_status,json=livenessStatus,proto3,enum=auth.liveness.LivenessStatus" json:"liveness_status,omitempty"`
}

func (x *CheckLivenessWithVideoStreamResponse) Reset() {
	*x = CheckLivenessWithVideoStreamResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckLivenessWithVideoStreamResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckLivenessWithVideoStreamResponse) ProtoMessage() {}

func (x *CheckLivenessWithVideoStreamResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckLivenessWithVideoStreamResponse.ProtoReflect.Descriptor instead.
func (*CheckLivenessWithVideoStreamResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{9}
}

func (x *CheckLivenessWithVideoStreamResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckLivenessWithVideoStreamResponse) GetLivenessStatus() LivenessStatus {
	if x != nil {
		return x.LivenessStatus
	}
	return LivenessStatus_LIVENESS_STATUS_UNSPECIFIED
}

type GenerateOTPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LivenessOptions *LivenessOptions `protobuf:"bytes,1,opt,name=liveness_options,json=livenessOptions,proto3" json:"liveness_options,omitempty"`
	// Strict liveness logic contains logic level for liveness facematch check
	//
	// Deprecated: Marked as deprecated in api/auth/liveness/service.proto.
	StrictnessLogic StrictnessLogic `protobuf:"varint,2,opt,name=strictness_logic,json=strictnessLogic,proto3,enum=auth.liveness.StrictnessLogic" json:"strictness_logic,omitempty"`
}

func (x *GenerateOTPRequest) Reset() {
	*x = GenerateOTPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateOTPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateOTPRequest) ProtoMessage() {}

func (x *GenerateOTPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateOTPRequest.ProtoReflect.Descriptor instead.
func (*GenerateOTPRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{10}
}

func (x *GenerateOTPRequest) GetLivenessOptions() *LivenessOptions {
	if x != nil {
		return x.LivenessOptions
	}
	return nil
}

// Deprecated: Marked as deprecated in api/auth/liveness/service.proto.
func (x *GenerateOTPRequest) GetStrictnessLogic() StrictnessLogic {
	if x != nil {
		return x.StrictnessLogic
	}
	return StrictnessLogic_STRICTNESS_LOGIC_UNSPECIFIED
}

type GenerateOTPResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Otp    string      `protobuf:"bytes,2,opt,name=otp,proto3" json:"otp,omitempty"`
}

func (x *GenerateOTPResponse) Reset() {
	*x = GenerateOTPResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateOTPResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateOTPResponse) ProtoMessage() {}

func (x *GenerateOTPResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateOTPResponse.ProtoReflect.Descriptor instead.
func (*GenerateOTPResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{11}
}

func (x *GenerateOTPResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GenerateOTPResponse) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

type GetS3ImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LocationKey string `protobuf:"bytes,1,opt,name=location_key,json=locationKey,proto3" json:"location_key,omitempty"`
}

func (x *GetS3ImageRequest) Reset() {
	*x = GetS3ImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetS3ImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetS3ImageRequest) ProtoMessage() {}

func (x *GetS3ImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetS3ImageRequest.ProtoReflect.Descriptor instead.
func (*GetS3ImageRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetS3ImageRequest) GetLocationKey() string {
	if x != nil {
		return x.LocationKey
	}
	return ""
}

type GetS3ImageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Image  []byte      `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty"`
}

func (x *GetS3ImageResponse) Reset() {
	*x = GetS3ImageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetS3ImageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetS3ImageResponse) ProtoMessage() {}

func (x *GetS3ImageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetS3ImageResponse.ProtoReflect.Descriptor instead.
func (*GetS3ImageResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetS3ImageResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetS3ImageResponse) GetImage() []byte {
	if x != nil {
		return x.Image
	}
	return nil
}

type GetLivenessAttemptsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// number of liveness attempts to fetch
	Limit int32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// Optional Parameter
	LivenessFlow LivenessFlow `protobuf:"varint,3,opt,name=liveness_flow,json=livenessFlow,proto3,enum=auth.liveness.LivenessFlow" json:"liveness_flow,omitempty"`
}

func (x *GetLivenessAttemptsRequest) Reset() {
	*x = GetLivenessAttemptsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLivenessAttemptsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLivenessAttemptsRequest) ProtoMessage() {}

func (x *GetLivenessAttemptsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLivenessAttemptsRequest.ProtoReflect.Descriptor instead.
func (*GetLivenessAttemptsRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetLivenessAttemptsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetLivenessAttemptsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetLivenessAttemptsRequest) GetLivenessFlow() LivenessFlow {
	if x != nil {
		return x.LivenessFlow
	}
	return LivenessFlow_LIVENESS_FLOW_UNSPECIFIED
}

type GetLivenessAttemptsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LivenessAttempts []*LivenessAttempt `protobuf:"bytes,2,rep,name=liveness_attempts,json=livenessAttempts,proto3" json:"liveness_attempts,omitempty"`
}

func (x *GetLivenessAttemptsResponse) Reset() {
	*x = GetLivenessAttemptsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLivenessAttemptsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLivenessAttemptsResponse) ProtoMessage() {}

func (x *GetLivenessAttemptsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLivenessAttemptsResponse.ProtoReflect.Descriptor instead.
func (*GetLivenessAttemptsResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetLivenessAttemptsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLivenessAttemptsResponse) GetLivenessAttempts() []*LivenessAttempt {
	if x != nil {
		return x.LivenessAttempts
	}
	return nil
}

type AnnotateLivenessAttemptRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// request ID for which annotations are to be marked
	RequestId  string      `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Annotation *Annotation `protobuf:"bytes,3,opt,name=annotation,proto3" json:"annotation,omitempty"`
}

func (x *AnnotateLivenessAttemptRequest) Reset() {
	*x = AnnotateLivenessAttemptRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnotateLivenessAttemptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnotateLivenessAttemptRequest) ProtoMessage() {}

func (x *AnnotateLivenessAttemptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnotateLivenessAttemptRequest.ProtoReflect.Descriptor instead.
func (*AnnotateLivenessAttemptRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{16}
}

func (x *AnnotateLivenessAttemptRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *AnnotateLivenessAttemptRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AnnotateLivenessAttemptRequest) GetAnnotation() *Annotation {
	if x != nil {
		return x.Annotation
	}
	return nil
}

type AnnotateLivenessAttemptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status of the request
	Status    *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SummaryId string      `protobuf:"bytes,2,opt,name=summary_id,json=summaryId,proto3" json:"summary_id,omitempty"`
}

func (x *AnnotateLivenessAttemptResponse) Reset() {
	*x = AnnotateLivenessAttemptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnotateLivenessAttemptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnotateLivenessAttemptResponse) ProtoMessage() {}

func (x *AnnotateLivenessAttemptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnotateLivenessAttemptResponse.ProtoReflect.Descriptor instead.
func (*AnnotateLivenessAttemptResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{17}
}

func (x *AnnotateLivenessAttemptResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AnnotateLivenessAttemptResponse) GetSummaryId() string {
	if x != nil {
		return x.SummaryId
	}
	return ""
}

type CreateLivenessSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId            string          `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RequestId          string          `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	LivenessFlow       LivenessFlow    `protobuf:"varint,3,opt,name=liveness_flow,json=livenessFlow,proto3,enum=auth.liveness.LivenessFlow" json:"liveness_flow,omitempty"`
	MaxRetries         int32           `protobuf:"varint,4,opt,name=max_retries,json=maxRetries,proto3" json:"max_retries,omitempty"`
	RefFacematchPhotos []*common.Image `protobuf:"bytes,5,rep,name=ref_facematch_photos,json=refFacematchPhotos,proto3" json:"ref_facematch_photos,omitempty"`
	// Deprecated: Marked as deprecated in api/auth/liveness/service.proto.
	StrictnessLogic StrictnessLogic `protobuf:"varint,6,opt,name=strictness_logic,json=strictnessLogic,proto3,enum=auth.liveness.StrictnessLogic" json:"strictness_logic,omitempty"`
	// Boolean flag to send if we want to enforce manual review of liveness
	ForceManualReview common.BooleanEnum   `protobuf:"varint,7,opt,name=force_manual_review,json=forceManualReview,proto3,enum=api.typesv2.common.BooleanEnum" json:"force_manual_review,omitempty"`
	ExpiryDuration    *durationpb.Duration `protobuf:"bytes,8,opt,name=expiry_duration,json=expiryDuration,proto3" json:"expiry_duration,omitempty"`
	// next_action_post_video_upload to be shown post liveness video upload
	NextActionPostVideoUpload *deeplink.Deeplink        `protobuf:"bytes,9,opt,name=next_action_post_video_upload,json=nextActionPostVideoUpload,proto3" json:"next_action_post_video_upload,omitempty"`
	NextActionHooks           *NextActionHooks          `protobuf:"bytes,10,opt,name=next_action_hooks,json=nextActionHooks,proto3" json:"next_action_hooks,omitempty"`
	CustomIntroScreenOptions  *CustomIntroScreenOptions `protobuf:"bytes,11,opt,name=custom_intro_screen_options,json=customIntroScreenOptions,proto3" json:"custom_intro_screen_options,omitempty"`
}

func (x *CreateLivenessSummaryRequest) Reset() {
	*x = CreateLivenessSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLivenessSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLivenessSummaryRequest) ProtoMessage() {}

func (x *CreateLivenessSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLivenessSummaryRequest.ProtoReflect.Descriptor instead.
func (*CreateLivenessSummaryRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{18}
}

func (x *CreateLivenessSummaryRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CreateLivenessSummaryRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CreateLivenessSummaryRequest) GetLivenessFlow() LivenessFlow {
	if x != nil {
		return x.LivenessFlow
	}
	return LivenessFlow_LIVENESS_FLOW_UNSPECIFIED
}

func (x *CreateLivenessSummaryRequest) GetMaxRetries() int32 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *CreateLivenessSummaryRequest) GetRefFacematchPhotos() []*common.Image {
	if x != nil {
		return x.RefFacematchPhotos
	}
	return nil
}

// Deprecated: Marked as deprecated in api/auth/liveness/service.proto.
func (x *CreateLivenessSummaryRequest) GetStrictnessLogic() StrictnessLogic {
	if x != nil {
		return x.StrictnessLogic
	}
	return StrictnessLogic_STRICTNESS_LOGIC_UNSPECIFIED
}

func (x *CreateLivenessSummaryRequest) GetForceManualReview() common.BooleanEnum {
	if x != nil {
		return x.ForceManualReview
	}
	return common.BooleanEnum(0)
}

func (x *CreateLivenessSummaryRequest) GetExpiryDuration() *durationpb.Duration {
	if x != nil {
		return x.ExpiryDuration
	}
	return nil
}

func (x *CreateLivenessSummaryRequest) GetNextActionPostVideoUpload() *deeplink.Deeplink {
	if x != nil {
		return x.NextActionPostVideoUpload
	}
	return nil
}

func (x *CreateLivenessSummaryRequest) GetNextActionHooks() *NextActionHooks {
	if x != nil {
		return x.NextActionHooks
	}
	return nil
}

func (x *CreateLivenessSummaryRequest) GetCustomIntroScreenOptions() *CustomIntroScreenOptions {
	if x != nil {
		return x.CustomIntroScreenOptions
	}
	return nil
}

type CreateLivenessSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Summary *LivenessSummary `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
}

func (x *CreateLivenessSummaryResponse) Reset() {
	*x = CreateLivenessSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLivenessSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLivenessSummaryResponse) ProtoMessage() {}

func (x *CreateLivenessSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLivenessSummaryResponse.ProtoReflect.Descriptor instead.
func (*CreateLivenessSummaryResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{19}
}

func (x *CreateLivenessSummaryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateLivenessSummaryResponse) GetSummary() *LivenessSummary {
	if x != nil {
		return x.Summary
	}
	return nil
}

type GetLivenessSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId      string       `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RequestId    string       `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	LivenessFlow LivenessFlow `protobuf:"varint,3,opt,name=liveness_flow,json=livenessFlow,proto3,enum=auth.liveness.LivenessFlow" json:"liveness_flow,omitempty"`
}

func (x *GetLivenessSummaryRequest) Reset() {
	*x = GetLivenessSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLivenessSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLivenessSummaryRequest) ProtoMessage() {}

func (x *GetLivenessSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLivenessSummaryRequest.ProtoReflect.Descriptor instead.
func (*GetLivenessSummaryRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetLivenessSummaryRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetLivenessSummaryRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetLivenessSummaryRequest) GetLivenessFlow() LivenessFlow {
	if x != nil {
		return x.LivenessFlow
	}
	return LivenessFlow_LIVENESS_FLOW_UNSPECIFIED
}

type GetLivenessSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Summary *LivenessSummary `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
}

func (x *GetLivenessSummaryResponse) Reset() {
	*x = GetLivenessSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLivenessSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLivenessSummaryResponse) ProtoMessage() {}

func (x *GetLivenessSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLivenessSummaryResponse.ProtoReflect.Descriptor instead.
func (*GetLivenessSummaryResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetLivenessSummaryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLivenessSummaryResponse) GetSummary() *LivenessSummary {
	if x != nil {
		return x.Summary
	}
	return nil
}

type GetLivenessSummaryStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId      string       `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RequestId    string       `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	LivenessFlow LivenessFlow `protobuf:"varint,3,opt,name=liveness_flow,json=livenessFlow,proto3,enum=auth.liveness.LivenessFlow" json:"liveness_flow,omitempty"`
}

func (x *GetLivenessSummaryStatusRequest) Reset() {
	*x = GetLivenessSummaryStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLivenessSummaryStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLivenessSummaryStatusRequest) ProtoMessage() {}

func (x *GetLivenessSummaryStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLivenessSummaryStatusRequest.ProtoReflect.Descriptor instead.
func (*GetLivenessSummaryStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{22}
}

func (x *GetLivenessSummaryStatusRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetLivenessSummaryStatusRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetLivenessSummaryStatusRequest) GetLivenessFlow() LivenessFlow {
	if x != nil {
		return x.LivenessFlow
	}
	return LivenessFlow_LIVENESS_FLOW_UNSPECIFIED
}

type GetLivenessSummaryStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                 *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SummaryStatus          SummaryStatus          `protobuf:"varint,2,opt,name=summary_status,json=summaryStatus,proto3,enum=auth.liveness.SummaryStatus" json:"summary_status,omitempty"`
	MaxAttempts            int32                  `protobuf:"varint,3,opt,name=max_attempts,json=maxAttempts,proto3" json:"max_attempts,omitempty"`
	AttemptCount           int32                  `protobuf:"varint,4,opt,name=attempt_count,json=attemptCount,proto3" json:"attempt_count,omitempty"`
	SummaryLivenessStatus  SummaryLivenessStatus  `protobuf:"varint,5,opt,name=summary_liveness_status,json=summaryLivenessStatus,proto3,enum=auth.liveness.SummaryLivenessStatus" json:"summary_liveness_status,omitempty"`
	SummaryFacematchStatus SummaryFacematchStatus `protobuf:"varint,6,opt,name=summary_facematch_status,json=summaryFacematchStatus,proto3,enum=auth.liveness.SummaryFacematchStatus" json:"summary_facematch_status,omitempty"`
	LivenessAttemptStatus  LivenessBlanketStatus  `protobuf:"varint,7,opt,name=liveness_attempt_status,json=livenessAttemptStatus,proto3,enum=auth.liveness.LivenessBlanketStatus" json:"liveness_attempt_status,omitempty"`
	// next_action_post_video_upload is sent when liveness summary status is in progress. There are two cases when liveness summary is in progress, liveness video uploaded or not
	// next_action_post_video_upload is populated with CHECK_LIVENESS deeplink if video is not yet uploaded
	// next_action_post_video_upload is populated with the one received in CreateLivenessSummary
	// Deprecated: Please use next_action field from the response
	//
	// Deprecated: Marked as deprecated in api/auth/liveness/service.proto.
	NextActionPostVideoUpload *deeplink.Deeplink `protobuf:"bytes,8,opt,name=next_action_post_video_upload,json=nextActionPostVideoUpload,proto3" json:"next_action_post_video_upload,omitempty"`
	// Liveness summary will send the next action for caller services to consume
	NextAction *deeplink.Deeplink `protobuf:"bytes,9,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *GetLivenessSummaryStatusResponse) Reset() {
	*x = GetLivenessSummaryStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLivenessSummaryStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLivenessSummaryStatusResponse) ProtoMessage() {}

func (x *GetLivenessSummaryStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLivenessSummaryStatusResponse.ProtoReflect.Descriptor instead.
func (*GetLivenessSummaryStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetLivenessSummaryStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLivenessSummaryStatusResponse) GetSummaryStatus() SummaryStatus {
	if x != nil {
		return x.SummaryStatus
	}
	return SummaryStatus_SUMMARY_UNSPECIFIED
}

func (x *GetLivenessSummaryStatusResponse) GetMaxAttempts() int32 {
	if x != nil {
		return x.MaxAttempts
	}
	return 0
}

func (x *GetLivenessSummaryStatusResponse) GetAttemptCount() int32 {
	if x != nil {
		return x.AttemptCount
	}
	return 0
}

func (x *GetLivenessSummaryStatusResponse) GetSummaryLivenessStatus() SummaryLivenessStatus {
	if x != nil {
		return x.SummaryLivenessStatus
	}
	return SummaryLivenessStatus_SUMMARY_LIVENESS_UNSPECIFIED
}

func (x *GetLivenessSummaryStatusResponse) GetSummaryFacematchStatus() SummaryFacematchStatus {
	if x != nil {
		return x.SummaryFacematchStatus
	}
	return SummaryFacematchStatus_SUMMARY_FACEMATCH_UNSPECIFIED
}

func (x *GetLivenessSummaryStatusResponse) GetLivenessAttemptStatus() LivenessBlanketStatus {
	if x != nil {
		return x.LivenessAttemptStatus
	}
	return LivenessBlanketStatus_LIVENESS_BLANKET_STATUS_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/auth/liveness/service.proto.
func (x *GetLivenessSummaryStatusResponse) GetNextActionPostVideoUpload() *deeplink.Deeplink {
	if x != nil {
		return x.NextActionPostVideoUpload
	}
	return nil
}

func (x *GetLivenessSummaryStatusResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type CreateLivenessAttemptRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId          string       `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	SummaryRequestId string       `protobuf:"bytes,2,opt,name=summary_request_id,json=summaryRequestId,proto3" json:"summary_request_id,omitempty"`
	LivenessFlow     LivenessFlow `protobuf:"varint,3,opt,name=liveness_flow,json=livenessFlow,proto3,enum=auth.liveness.LivenessFlow" json:"liveness_flow,omitempty"`
}

func (x *CreateLivenessAttemptRequest) Reset() {
	*x = CreateLivenessAttemptRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLivenessAttemptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLivenessAttemptRequest) ProtoMessage() {}

func (x *CreateLivenessAttemptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLivenessAttemptRequest.ProtoReflect.Descriptor instead.
func (*CreateLivenessAttemptRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{24}
}

func (x *CreateLivenessAttemptRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CreateLivenessAttemptRequest) GetSummaryRequestId() string {
	if x != nil {
		return x.SummaryRequestId
	}
	return ""
}

func (x *CreateLivenessAttemptRequest) GetLivenessFlow() LivenessFlow {
	if x != nil {
		return x.LivenessFlow
	}
	return LivenessFlow_LIVENESS_FLOW_UNSPECIFIED
}

type CreateLivenessAttemptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status            *rpc.Status           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LivenessAttempt   *LivenessAttempt      `protobuf:"bytes,2,opt,name=liveness_attempt,json=livenessAttempt,proto3" json:"liveness_attempt,omitempty"`
	OldLivenessStatus LivenessBlanketStatus `protobuf:"varint,3,opt,name=old_liveness_status,json=oldLivenessStatus,proto3,enum=auth.liveness.LivenessBlanketStatus" json:"old_liveness_status,omitempty"`
	OldAttemptStatus  LivenessStatus        `protobuf:"varint,4,opt,name=old_attempt_status,json=oldAttemptStatus,proto3,enum=auth.liveness.LivenessStatus" json:"old_attempt_status,omitempty"`
}

func (x *CreateLivenessAttemptResponse) Reset() {
	*x = CreateLivenessAttemptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLivenessAttemptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLivenessAttemptResponse) ProtoMessage() {}

func (x *CreateLivenessAttemptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLivenessAttemptResponse.ProtoReflect.Descriptor instead.
func (*CreateLivenessAttemptResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{25}
}

func (x *CreateLivenessAttemptResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateLivenessAttemptResponse) GetLivenessAttempt() *LivenessAttempt {
	if x != nil {
		return x.LivenessAttempt
	}
	return nil
}

func (x *CreateLivenessAttemptResponse) GetOldLivenessStatus() LivenessBlanketStatus {
	if x != nil {
		return x.OldLivenessStatus
	}
	return LivenessBlanketStatus_LIVENESS_BLANKET_STATUS_UNSPECIFIED
}

func (x *CreateLivenessAttemptResponse) GetOldAttemptStatus() LivenessStatus {
	if x != nil {
		return x.OldAttemptStatus
	}
	return LivenessStatus_LIVENESS_STATUS_UNSPECIFIED
}

type AnnotateFacematchAttemptRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// request id of the facematch attempt
	RequestId     string               `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	ReviewVerdict Verdict              `protobuf:"varint,4,opt,name=review_verdict,json=reviewVerdict,proto3,enum=auth.liveness.Verdict" json:"review_verdict,omitempty"`
	Annotation    *FaceMatchAnnotation `protobuf:"bytes,5,opt,name=annotation,proto3" json:"annotation,omitempty"`
}

func (x *AnnotateFacematchAttemptRequest) Reset() {
	*x = AnnotateFacematchAttemptRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnotateFacematchAttemptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnotateFacematchAttemptRequest) ProtoMessage() {}

func (x *AnnotateFacematchAttemptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnotateFacematchAttemptRequest.ProtoReflect.Descriptor instead.
func (*AnnotateFacematchAttemptRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{26}
}

func (x *AnnotateFacematchAttemptRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *AnnotateFacematchAttemptRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AnnotateFacematchAttemptRequest) GetReviewVerdict() Verdict {
	if x != nil {
		return x.ReviewVerdict
	}
	return Verdict_VERDICT_UNSPECIFIED
}

func (x *AnnotateFacematchAttemptRequest) GetAnnotation() *FaceMatchAnnotation {
	if x != nil {
		return x.Annotation
	}
	return nil
}

type AnnotateFacematchAttemptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status    *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SummaryId string      `protobuf:"bytes,2,opt,name=summary_id,json=summaryId,proto3" json:"summary_id,omitempty"`
}

func (x *AnnotateFacematchAttemptResponse) Reset() {
	*x = AnnotateFacematchAttemptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnotateFacematchAttemptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnotateFacematchAttemptResponse) ProtoMessage() {}

func (x *AnnotateFacematchAttemptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnotateFacematchAttemptResponse.ProtoReflect.Descriptor instead.
func (*AnnotateFacematchAttemptResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{27}
}

func (x *AnnotateFacematchAttemptResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AnnotateFacematchAttemptResponse) GetSummaryId() string {
	if x != nil {
		return x.SummaryId
	}
	return ""
}

type RedoFaceMatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId          string          `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	SummaryRequestId string          `protobuf:"bytes,2,opt,name=summary_request_id,json=summaryRequestId,proto3" json:"summary_request_id,omitempty"`
	LivenessFlow     LivenessFlow    `protobuf:"varint,3,opt,name=liveness_flow,json=livenessFlow,proto3,enum=auth.liveness.LivenessFlow" json:"liveness_flow,omitempty"`
	ReferencePhotos  []*common.Image `protobuf:"bytes,4,rep,name=reference_photos,json=referencePhotos,proto3" json:"reference_photos,omitempty"`
}

func (x *RedoFaceMatchRequest) Reset() {
	*x = RedoFaceMatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedoFaceMatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedoFaceMatchRequest) ProtoMessage() {}

func (x *RedoFaceMatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedoFaceMatchRequest.ProtoReflect.Descriptor instead.
func (*RedoFaceMatchRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{28}
}

func (x *RedoFaceMatchRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *RedoFaceMatchRequest) GetSummaryRequestId() string {
	if x != nil {
		return x.SummaryRequestId
	}
	return ""
}

func (x *RedoFaceMatchRequest) GetLivenessFlow() LivenessFlow {
	if x != nil {
		return x.LivenessFlow
	}
	return LivenessFlow_LIVENESS_FLOW_UNSPECIFIED
}

func (x *RedoFaceMatchRequest) GetReferencePhotos() []*common.Image {
	if x != nil {
		return x.ReferencePhotos
	}
	return nil
}

type RedoFaceMatchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *RedoFaceMatchResponse) Reset() {
	*x = RedoFaceMatchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedoFaceMatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedoFaceMatchResponse) ProtoMessage() {}

func (x *RedoFaceMatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedoFaceMatchResponse.ProtoReflect.Descriptor instead.
func (*RedoFaceMatchResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{29}
}

func (x *RedoFaceMatchResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetFaceMatchAttemptsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// deprecating in favour of criteria
	//
	// Deprecated: Marked as deprecated in api/auth/liveness/service.proto.
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// number of liveness attempts to fetch
	// will be applicable only for actor id criteria
	Limit int32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// deprecating in favour of criteria
	//
	// Deprecated: Marked as deprecated in api/auth/liveness/service.proto.
	RequestId string `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// one-of criteria that can be used to fetch facematch attempts
	Criteria *FacematchRequestCriteria `protobuf:"bytes,4,opt,name=criteria,proto3" json:"criteria,omitempty"`
}

func (x *GetFaceMatchAttemptsRequest) Reset() {
	*x = GetFaceMatchAttemptsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFaceMatchAttemptsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFaceMatchAttemptsRequest) ProtoMessage() {}

func (x *GetFaceMatchAttemptsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFaceMatchAttemptsRequest.ProtoReflect.Descriptor instead.
func (*GetFaceMatchAttemptsRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{30}
}

// Deprecated: Marked as deprecated in api/auth/liveness/service.proto.
func (x *GetFaceMatchAttemptsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetFaceMatchAttemptsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

// Deprecated: Marked as deprecated in api/auth/liveness/service.proto.
func (x *GetFaceMatchAttemptsRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetFaceMatchAttemptsRequest) GetCriteria() *FacematchRequestCriteria {
	if x != nil {
		return x.Criteria
	}
	return nil
}

type FacematchRequestCriteria struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Criteria:
	//
	//	*FacematchRequestCriteria_ActorId
	//	*FacematchRequestCriteria_LivenessRequestId
	//	*FacematchRequestCriteria_FacematchRequestId
	Criteria isFacematchRequestCriteria_Criteria `protobuf_oneof:"criteria"`
}

func (x *FacematchRequestCriteria) Reset() {
	*x = FacematchRequestCriteria{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FacematchRequestCriteria) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FacematchRequestCriteria) ProtoMessage() {}

func (x *FacematchRequestCriteria) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FacematchRequestCriteria.ProtoReflect.Descriptor instead.
func (*FacematchRequestCriteria) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{31}
}

func (m *FacematchRequestCriteria) GetCriteria() isFacematchRequestCriteria_Criteria {
	if m != nil {
		return m.Criteria
	}
	return nil
}

func (x *FacematchRequestCriteria) GetActorId() string {
	if x, ok := x.GetCriteria().(*FacematchRequestCriteria_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *FacematchRequestCriteria) GetLivenessRequestId() string {
	if x, ok := x.GetCriteria().(*FacematchRequestCriteria_LivenessRequestId); ok {
		return x.LivenessRequestId
	}
	return ""
}

func (x *FacematchRequestCriteria) GetFacematchRequestId() string {
	if x, ok := x.GetCriteria().(*FacematchRequestCriteria_FacematchRequestId); ok {
		return x.FacematchRequestId
	}
	return ""
}

type isFacematchRequestCriteria_Criteria interface {
	isFacematchRequestCriteria_Criteria()
}

type FacematchRequestCriteria_ActorId struct {
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3,oneof"`
}

type FacematchRequestCriteria_LivenessRequestId struct {
	// get facematch for corresponding liveness request id
	LivenessRequestId string `protobuf:"bytes,2,opt,name=liveness_request_id,json=livenessRequestId,proto3,oneof"`
}

type FacematchRequestCriteria_FacematchRequestId struct {
	// get facematch for corresponding facematch request id
	FacematchRequestId string `protobuf:"bytes,3,opt,name=facematch_request_id,json=facematchRequestId,proto3,oneof"`
}

func (*FacematchRequestCriteria_ActorId) isFacematchRequestCriteria_Criteria() {}

func (*FacematchRequestCriteria_LivenessRequestId) isFacematchRequestCriteria_Criteria() {}

func (*FacematchRequestCriteria_FacematchRequestId) isFacematchRequestCriteria_Criteria() {}

type GetFaceMatchAttemptsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status            *rpc.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	FaceMatchAttempts []*FaceMatchAttempt `protobuf:"bytes,2,rep,name=face_match_attempts,json=faceMatchAttempts,proto3" json:"face_match_attempts,omitempty"`
}

func (x *GetFaceMatchAttemptsResponse) Reset() {
	*x = GetFaceMatchAttemptsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFaceMatchAttemptsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFaceMatchAttemptsResponse) ProtoMessage() {}

func (x *GetFaceMatchAttemptsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFaceMatchAttemptsResponse.ProtoReflect.Descriptor instead.
func (*GetFaceMatchAttemptsResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{32}
}

func (x *GetFaceMatchAttemptsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetFaceMatchAttemptsResponse) GetFaceMatchAttempts() []*FaceMatchAttempt {
	if x != nil {
		return x.FaceMatchAttempts
	}
	return nil
}

type RetryLivenessRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId          string       `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	SummaryRequestId string       `protobuf:"bytes,2,opt,name=summary_request_id,json=summaryRequestId,proto3" json:"summary_request_id,omitempty"`
	LivenessFlow     LivenessFlow `protobuf:"varint,3,opt,name=liveness_flow,json=livenessFlow,proto3,enum=auth.liveness.LivenessFlow" json:"liveness_flow,omitempty"`
	// if summary_id is being sent then all other params will be ignored
	SummaryId string `protobuf:"bytes,4,opt,name=summary_id,json=summaryId,proto3" json:"summary_id,omitempty"`
}

func (x *RetryLivenessRequest) Reset() {
	*x = RetryLivenessRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryLivenessRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryLivenessRequest) ProtoMessage() {}

func (x *RetryLivenessRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryLivenessRequest.ProtoReflect.Descriptor instead.
func (*RetryLivenessRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{33}
}

func (x *RetryLivenessRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *RetryLivenessRequest) GetSummaryRequestId() string {
	if x != nil {
		return x.SummaryRequestId
	}
	return ""
}

func (x *RetryLivenessRequest) GetLivenessFlow() LivenessFlow {
	if x != nil {
		return x.LivenessFlow
	}
	return LivenessFlow_LIVENESS_FLOW_UNSPECIFIED
}

func (x *RetryLivenessRequest) GetSummaryId() string {
	if x != nil {
		return x.SummaryId
	}
	return ""
}

type RetryLivenessResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *RetryLivenessResponse) Reset() {
	*x = RetryLivenessResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryLivenessResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryLivenessResponse) ProtoMessage() {}

func (x *RetryLivenessResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryLivenessResponse.ProtoReflect.Descriptor instead.
func (*RetryLivenessResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{34}
}

func (x *RetryLivenessResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetLivenessAttemptRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LivenessReqId string `protobuf:"bytes,1,opt,name=liveness_req_id,json=livenessReqId,proto3" json:"liveness_req_id,omitempty"`
}

func (x *GetLivenessAttemptRequest) Reset() {
	*x = GetLivenessAttemptRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLivenessAttemptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLivenessAttemptRequest) ProtoMessage() {}

func (x *GetLivenessAttemptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLivenessAttemptRequest.ProtoReflect.Descriptor instead.
func (*GetLivenessAttemptRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetLivenessAttemptRequest) GetLivenessReqId() string {
	if x != nil {
		return x.LivenessReqId
	}
	return ""
}

type GetLivenessAttemptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          *rpc.Status      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LivenessAttempt *LivenessAttempt `protobuf:"bytes,2,opt,name=liveness_attempt,json=livenessAttempt,proto3" json:"liveness_attempt,omitempty"`
}

func (x *GetLivenessAttemptResponse) Reset() {
	*x = GetLivenessAttemptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_liveness_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLivenessAttemptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLivenessAttemptResponse) ProtoMessage() {}

func (x *GetLivenessAttemptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_liveness_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLivenessAttemptResponse.ProtoReflect.Descriptor instead.
func (*GetLivenessAttemptResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_liveness_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetLivenessAttemptResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLivenessAttemptResponse) GetLivenessAttempt() *LivenessAttempt {
	if x != nil {
		return x.LivenessAttempt
	}
	return nil
}

var File_api_auth_liveness_service_proto protoreflect.FileDescriptor

var file_api_auth_liveness_service_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x66, 0x61, 0x63,
	0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75,
	0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f,
	0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x20, 0x0a, 0x06,
	0x46, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x54,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x49, 0x64, 0x22, 0xa0, 0x03, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x0a, 0x0f, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x0e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x25, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x52, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x74, 0x70, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x08, 0x6f, 0x74, 0x70, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x38, 0x0a,
	0x18, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f,
	0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x16, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x6f, 0x74, 0x70, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x6f, 0x74, 0x70, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x22, 0xa1, 0x04, 0x0a, 0x10, 0x46, 0x61, 0x63, 0x65,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x10, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52,
	0x0e, 0x66, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x79, 0x6e, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x73,
	0x79, 0x6e, 0x63, 0x12, 0x2f, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x05, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x66, 0x72,
	0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x52, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x46, 0x72, 0x61, 0x6d, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68,
	0x6f, 0x6c, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x12, 0x4d, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0f, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x6f,
	0x67, 0x69, 0x63, 0x12, 0x2e, 0x0a, 0x13, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x22, 0xa3, 0x01, 0x0a, 0x11,
	0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a, 0x11, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0f, 0x66, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49,
	0x64, 0x22, 0x55, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x64, 0x22, 0xe5, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74,
	0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a, 0x11,
	0x66, 0x61, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x66, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x61, 0x63, 0x65,
	0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0e, 0x66, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x6d, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10,
	0x66, 0x6d, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x22, 0x83, 0x02, 0x0a, 0x0f, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x64, 0x12, 0x43,
	0x0a, 0x0e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x52, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x12, 0x40, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x66, 0x6c, 0x6f, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x0c, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x2f, 0x0a, 0x08, 0x76, 0x70, 0x6e, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x56, 0x50, 0x4e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x76,
	0x70, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x8b, 0x02, 0x0a, 0x23, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x57, 0x69, 0x74, 0x68, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a,
	0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e,
	0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x48,
	0x00, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x21, 0x0a, 0x0b, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x48,
	0x00, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x12, 0x21, 0x0a,
	0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x46, 0x72, 0x61, 0x6d, 0x65,
	0x12, 0x49, 0x0a, 0x14, 0x70, 0x61, 0x73, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x46,
	0x72, 0x61, 0x6d, 0x65, 0x73, 0x48, 0x00, 0x52, 0x12, 0x70, 0x61, 0x73, 0x73, 0x69, 0x76, 0x65,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x73, 0x22, 0x93, 0x01, 0x0a, 0x24, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x57, 0x69, 0x74, 0x68, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x53,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x46, 0x0a, 0x0f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xae, 0x01, 0x0a, 0x12, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x49, 0x0a, 0x10, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0f, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4d, 0x0a, 0x10,
	0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x63,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6e, 0x65, 0x73,
	0x73, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f, 0x73, 0x74, 0x72, 0x69,
	0x63, 0x74, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x22, 0x4c, 0x0a, 0x13, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x22, 0x36, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x53, 0x33, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4b, 0x65,
	0x79, 0x22, 0x4f, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x33, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x22, 0x8f, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x12, 0x40, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x66,
	0x6c, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x0c, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x46, 0x6c, 0x6f, 0x77, 0x22, 0x8f, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x11, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x52, 0x10, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x22, 0x95, 0x01, 0x0a, 0x1e, 0x41, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0a, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x65,
	0x0a, 0x1f, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x49, 0x64, 0x22, 0xff, 0x05, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x40, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x6c, 0x6f,
	0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x46, 0x6c, 0x6f, 0x77, 0x52, 0x0c, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x6c,
	0x6f, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x14, 0x72, 0x65, 0x66, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x5f, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x12, 0x72, 0x65,
	0x66, 0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x73,
	0x12, 0x4d, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6c,
	0x6f, 0x67, 0x69, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f,
	0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x12,
	0x4f, 0x0a, 0x13, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x11, 0x66,
	0x6f, 0x72, 0x63, 0x65, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x12, 0x42, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5d, 0x0a, 0x1d, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x19, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x73, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x12, 0x4a, 0x0a, 0x11, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4e,
	0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x6f, 0x6f, 0x6b, 0x73, 0x52, 0x0f,
	0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x6f, 0x6f, 0x6b, 0x73, 0x12,
	0x66, 0x0a, 0x1b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6e, 0x74, 0x72, 0x6f,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x18, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x7e, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a,
	0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x07,
	0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x22, 0x97, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x40, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x6c, 0x6f, 0x77,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x46,
	0x6c, 0x6f, 0x77, 0x52, 0x0c, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x6f,
	0x77, 0x22, 0x7b, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x22, 0x9d,
	0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0d,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x6f, 0x77,
	0x52, 0x0c, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x6f, 0x77, 0x22, 0x92,
	0x05, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x0a, 0x0e, 0x73, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d,
	0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73,
	0x12, 0x23, 0x0a, 0x0d, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x5c, 0x0a, 0x17, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x4c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x15, 0x73, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x5f, 0x0a, 0x18, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x66,
	0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x61, 0x63,
	0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x16, 0x73, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x5c, 0x0a, 0x17, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x42, 0x6c,
	0x61, 0x6e, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x15, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x61, 0x0a, 0x1d, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x42, 0x02, 0x18, 0x01, 0x52, 0x19, 0x6e, 0x65, 0x78, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x73, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0xa9, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x2c, 0x0a, 0x12, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x40, 0x0a,
	0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x6f,
	0x77, 0x52, 0x0c, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x6f, 0x77, 0x22,
	0xb2, 0x02, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x10, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x52, 0x0f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x12, 0x54, 0x0a, 0x13, 0x6f, 0x6c, 0x64, 0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x42, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x6f, 0x6c, 0x64, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x12, 0x6f, 0x6c, 0x64, 0x5f, 0x61,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x10, 0x6f, 0x6c, 0x64, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0xde, 0x01, 0x0a, 0x1f, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x65, 0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x76, 0x65, 0x72,
	0x64, 0x69, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x56, 0x65, 0x72, 0x64, 0x69,
	0x63, 0x74, 0x52, 0x0d, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63,
	0x74, 0x12, 0x42, 0x0a, 0x0a, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x66, 0x0a, 0x20, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x65, 0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x64, 0x22, 0xe7, 0x01,
	0x0a, 0x14, 0x52, 0x65, 0x64, 0x6f, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x40, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x6c, 0x6f, 0x77,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x46,
	0x6c, 0x6f, 0x77, 0x52, 0x0c, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x6f,
	0x77, 0x12, 0x44, 0x0a, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x70,
	0x68, 0x6f, 0x74, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x73, 0x22, 0x3c, 0x0a, 0x15, 0x52, 0x65, 0x64, 0x6f, 0x46,
	0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xba, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x46, 0x61, 0x63,
	0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x21, 0x0a, 0x0a, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x43, 0x0a,
	0x08, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e,
	0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x52, 0x08, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x22, 0xa9, 0x01, 0x0a, 0x18, 0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x12,
	0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x13,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x11, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x32,
	0x0a, 0x14, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x12,
	0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x22, 0x94,
	0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x4f, 0x0a, 0x13, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x52, 0x11, 0x66, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x73, 0x22, 0xc0, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x0c, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x15, 0x52, 0x65, 0x74, 0x72,
	0x79, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x43, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x49, 0x64, 0x22, 0x8c, 0x01, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x49, 0x0a, 0x10, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x0f, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x32, 0xab, 0x0e, 0x0a, 0x08, 0x4c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x66, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4e, 0x0a, 0x09, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x1f, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x46, 0x61, 0x63,
	0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x46, 0x61,
	0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x69, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x29, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x1c, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x57, 0x69, 0x74, 0x68,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x32, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x57, 0x69, 0x74, 0x68, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x33, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x57, 0x69, 0x74,
	0x68, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x28, 0x01, 0x12, 0x54, 0x0a, 0x0b, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x4f, 0x54, 0x50, 0x12, 0x21, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x54,
	0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x0a,
	0x47, 0x65, 0x74, 0x53, 0x33, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x20, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x33,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x33, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x6c, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x29, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a,
	0x17, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x2d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x12, 0x2b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x12, 0x28, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x2e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x2b, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x18, 0x41, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x65, 0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x12, 0x2e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x46, 0x61, 0x63, 0x65,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x46, 0x61, 0x63, 0x65,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x0d, 0x52, 0x65, 0x64, 0x6f, 0x46, 0x61, 0x63, 0x65,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x23, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x52, 0x65, 0x64, 0x6f, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x52, 0x65, 0x64, 0x6f, 0x46,
	0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x6f, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x2a, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x61, 0x63, 0x65,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x5a, 0x0a, 0x0d, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x12, 0x23, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x12, 0x28, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_liveness_service_proto_rawDescOnce sync.Once
	file_api_auth_liveness_service_proto_rawDescData = file_api_auth_liveness_service_proto_rawDesc
)

func file_api_auth_liveness_service_proto_rawDescGZIP() []byte {
	file_api_auth_liveness_service_proto_rawDescOnce.Do(func() {
		file_api_auth_liveness_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_liveness_service_proto_rawDescData)
	})
	return file_api_auth_liveness_service_proto_rawDescData
}

var file_api_auth_liveness_service_proto_msgTypes = make([]protoimpl.MessageInfo, 37)
var file_api_auth_liveness_service_proto_goTypes = []interface{}{
	(*Frames)(nil),                               // 0: auth.liveness.Frames
	(*GetLivenessStatusRequest)(nil),             // 1: auth.liveness.GetLivenessStatusRequest
	(*GetLivenessStatusResponse)(nil),            // 2: auth.liveness.GetLivenessStatusResponse
	(*FaceMatchRequest)(nil),                     // 3: auth.liveness.FaceMatchRequest
	(*FaceMatchResponse)(nil),                    // 4: auth.liveness.FaceMatchResponse
	(*GetFaceMatchStatusRequest)(nil),            // 5: auth.liveness.GetFaceMatchStatusRequest
	(*GetFaceMatchStatusResponse)(nil),           // 6: auth.liveness.GetFaceMatchStatusResponse
	(*LivenessOptions)(nil),                      // 7: auth.liveness.LivenessOptions
	(*CheckLivenessWithVideoStreamRequest)(nil),  // 8: auth.liveness.CheckLivenessWithVideoStreamRequest
	(*CheckLivenessWithVideoStreamResponse)(nil), // 9: auth.liveness.CheckLivenessWithVideoStreamResponse
	(*GenerateOTPRequest)(nil),                   // 10: auth.liveness.GenerateOTPRequest
	(*GenerateOTPResponse)(nil),                  // 11: auth.liveness.GenerateOTPResponse
	(*GetS3ImageRequest)(nil),                    // 12: auth.liveness.GetS3ImageRequest
	(*GetS3ImageResponse)(nil),                   // 13: auth.liveness.GetS3ImageResponse
	(*GetLivenessAttemptsRequest)(nil),           // 14: auth.liveness.GetLivenessAttemptsRequest
	(*GetLivenessAttemptsResponse)(nil),          // 15: auth.liveness.GetLivenessAttemptsResponse
	(*AnnotateLivenessAttemptRequest)(nil),       // 16: auth.liveness.AnnotateLivenessAttemptRequest
	(*AnnotateLivenessAttemptResponse)(nil),      // 17: auth.liveness.AnnotateLivenessAttemptResponse
	(*CreateLivenessSummaryRequest)(nil),         // 18: auth.liveness.CreateLivenessSummaryRequest
	(*CreateLivenessSummaryResponse)(nil),        // 19: auth.liveness.CreateLivenessSummaryResponse
	(*GetLivenessSummaryRequest)(nil),            // 20: auth.liveness.GetLivenessSummaryRequest
	(*GetLivenessSummaryResponse)(nil),           // 21: auth.liveness.GetLivenessSummaryResponse
	(*GetLivenessSummaryStatusRequest)(nil),      // 22: auth.liveness.GetLivenessSummaryStatusRequest
	(*GetLivenessSummaryStatusResponse)(nil),     // 23: auth.liveness.GetLivenessSummaryStatusResponse
	(*CreateLivenessAttemptRequest)(nil),         // 24: auth.liveness.CreateLivenessAttemptRequest
	(*CreateLivenessAttemptResponse)(nil),        // 25: auth.liveness.CreateLivenessAttemptResponse
	(*AnnotateFacematchAttemptRequest)(nil),      // 26: auth.liveness.AnnotateFacematchAttemptRequest
	(*AnnotateFacematchAttemptResponse)(nil),     // 27: auth.liveness.AnnotateFacematchAttemptResponse
	(*RedoFaceMatchRequest)(nil),                 // 28: auth.liveness.RedoFaceMatchRequest
	(*RedoFaceMatchResponse)(nil),                // 29: auth.liveness.RedoFaceMatchResponse
	(*GetFaceMatchAttemptsRequest)(nil),          // 30: auth.liveness.GetFaceMatchAttemptsRequest
	(*FacematchRequestCriteria)(nil),             // 31: auth.liveness.FacematchRequestCriteria
	(*GetFaceMatchAttemptsResponse)(nil),         // 32: auth.liveness.GetFaceMatchAttemptsResponse
	(*RetryLivenessRequest)(nil),                 // 33: auth.liveness.RetryLivenessRequest
	(*RetryLivenessResponse)(nil),                // 34: auth.liveness.RetryLivenessResponse
	(*GetLivenessAttemptRequest)(nil),            // 35: auth.liveness.GetLivenessAttemptRequest
	(*GetLivenessAttemptResponse)(nil),           // 36: auth.liveness.GetLivenessAttemptResponse
	(*rpc.Status)(nil),                           // 37: rpc.Status
	(LivenessStatus)(0),                          // 38: auth.liveness.LivenessStatus
	(*common.Image)(nil),                         // 39: api.typesv2.common.Image
	(FaceMatchLevel)(0),                          // 40: auth.liveness.FaceMatchLevel
	(StrictnessLogic)(0),                         // 41: auth.liveness.StrictnessLogic
	(FaceMatchStatus)(0),                         // 42: auth.liveness.FaceMatchStatus
	(LivenessLevel)(0),                           // 43: auth.liveness.LivenessLevel
	(LivenessFlow)(0),                            // 44: auth.liveness.LivenessFlow
	(*typesv2.VPNInfo)(nil),                      // 45: api.typesv2.VPNInfo
	(*LivenessAttempt)(nil),                      // 46: auth.liveness.LivenessAttempt
	(*Annotation)(nil),                           // 47: auth.liveness.Annotation
	(common.BooleanEnum)(0),                      // 48: api.typesv2.common.BooleanEnum
	(*durationpb.Duration)(nil),                  // 49: google.protobuf.Duration
	(*deeplink.Deeplink)(nil),                    // 50: frontend.deeplink.Deeplink
	(*NextActionHooks)(nil),                      // 51: auth.liveness.NextActionHooks
	(*CustomIntroScreenOptions)(nil),             // 52: auth.liveness.CustomIntroScreenOptions
	(*LivenessSummary)(nil),                      // 53: auth.liveness.LivenessSummary
	(SummaryStatus)(0),                           // 54: auth.liveness.SummaryStatus
	(SummaryLivenessStatus)(0),                   // 55: auth.liveness.SummaryLivenessStatus
	(SummaryFacematchStatus)(0),                  // 56: auth.liveness.SummaryFacematchStatus
	(LivenessBlanketStatus)(0),                   // 57: auth.liveness.LivenessBlanketStatus
	(Verdict)(0),                                 // 58: auth.liveness.Verdict
	(*FaceMatchAnnotation)(nil),                  // 59: auth.liveness.FaceMatchAnnotation
	(*FaceMatchAttempt)(nil),                     // 60: auth.liveness.FaceMatchAttempt
}
var file_api_auth_liveness_service_proto_depIdxs = []int32{
	37, // 0: auth.liveness.GetLivenessStatusResponse.status:type_name -> rpc.Status
	38, // 1: auth.liveness.GetLivenessStatusResponse.liveness_status:type_name -> auth.liveness.LivenessStatus
	39, // 2: auth.liveness.GetLivenessStatusResponse.frame:type_name -> api.typesv2.common.Image
	40, // 3: auth.liveness.FaceMatchRequest.face_match_level:type_name -> auth.liveness.FaceMatchLevel
	39, // 4: auth.liveness.FaceMatchRequest.image:type_name -> api.typesv2.common.Image
	39, // 5: auth.liveness.FaceMatchRequest.image_frame:type_name -> api.typesv2.common.Image
	41, // 6: auth.liveness.FaceMatchRequest.strictness_logic:type_name -> auth.liveness.StrictnessLogic
	37, // 7: auth.liveness.FaceMatchResponse.status:type_name -> rpc.Status
	42, // 8: auth.liveness.FaceMatchResponse.face_match_status:type_name -> auth.liveness.FaceMatchStatus
	37, // 9: auth.liveness.GetFaceMatchStatusResponse.status:type_name -> rpc.Status
	42, // 10: auth.liveness.GetFaceMatchStatusResponse.face_match_status:type_name -> auth.liveness.FaceMatchStatus
	43, // 11: auth.liveness.LivenessOptions.liveness_level:type_name -> auth.liveness.LivenessLevel
	44, // 12: auth.liveness.LivenessOptions.liveness_flow:type_name -> auth.liveness.LivenessFlow
	45, // 13: auth.liveness.LivenessOptions.vpn_info:type_name -> api.typesv2.VPNInfo
	7,  // 14: auth.liveness.CheckLivenessWithVideoStreamRequest.options:type_name -> auth.liveness.LivenessOptions
	0,  // 15: auth.liveness.CheckLivenessWithVideoStreamRequest.passive_image_frames:type_name -> auth.liveness.Frames
	37, // 16: auth.liveness.CheckLivenessWithVideoStreamResponse.status:type_name -> rpc.Status
	38, // 17: auth.liveness.CheckLivenessWithVideoStreamResponse.liveness_status:type_name -> auth.liveness.LivenessStatus
	7,  // 18: auth.liveness.GenerateOTPRequest.liveness_options:type_name -> auth.liveness.LivenessOptions
	41, // 19: auth.liveness.GenerateOTPRequest.strictness_logic:type_name -> auth.liveness.StrictnessLogic
	37, // 20: auth.liveness.GenerateOTPResponse.status:type_name -> rpc.Status
	37, // 21: auth.liveness.GetS3ImageResponse.status:type_name -> rpc.Status
	44, // 22: auth.liveness.GetLivenessAttemptsRequest.liveness_flow:type_name -> auth.liveness.LivenessFlow
	37, // 23: auth.liveness.GetLivenessAttemptsResponse.status:type_name -> rpc.Status
	46, // 24: auth.liveness.GetLivenessAttemptsResponse.liveness_attempts:type_name -> auth.liveness.LivenessAttempt
	47, // 25: auth.liveness.AnnotateLivenessAttemptRequest.annotation:type_name -> auth.liveness.Annotation
	37, // 26: auth.liveness.AnnotateLivenessAttemptResponse.status:type_name -> rpc.Status
	44, // 27: auth.liveness.CreateLivenessSummaryRequest.liveness_flow:type_name -> auth.liveness.LivenessFlow
	39, // 28: auth.liveness.CreateLivenessSummaryRequest.ref_facematch_photos:type_name -> api.typesv2.common.Image
	41, // 29: auth.liveness.CreateLivenessSummaryRequest.strictness_logic:type_name -> auth.liveness.StrictnessLogic
	48, // 30: auth.liveness.CreateLivenessSummaryRequest.force_manual_review:type_name -> api.typesv2.common.BooleanEnum
	49, // 31: auth.liveness.CreateLivenessSummaryRequest.expiry_duration:type_name -> google.protobuf.Duration
	50, // 32: auth.liveness.CreateLivenessSummaryRequest.next_action_post_video_upload:type_name -> frontend.deeplink.Deeplink
	51, // 33: auth.liveness.CreateLivenessSummaryRequest.next_action_hooks:type_name -> auth.liveness.NextActionHooks
	52, // 34: auth.liveness.CreateLivenessSummaryRequest.custom_intro_screen_options:type_name -> auth.liveness.CustomIntroScreenOptions
	37, // 35: auth.liveness.CreateLivenessSummaryResponse.status:type_name -> rpc.Status
	53, // 36: auth.liveness.CreateLivenessSummaryResponse.summary:type_name -> auth.liveness.LivenessSummary
	44, // 37: auth.liveness.GetLivenessSummaryRequest.liveness_flow:type_name -> auth.liveness.LivenessFlow
	37, // 38: auth.liveness.GetLivenessSummaryResponse.status:type_name -> rpc.Status
	53, // 39: auth.liveness.GetLivenessSummaryResponse.summary:type_name -> auth.liveness.LivenessSummary
	44, // 40: auth.liveness.GetLivenessSummaryStatusRequest.liveness_flow:type_name -> auth.liveness.LivenessFlow
	37, // 41: auth.liveness.GetLivenessSummaryStatusResponse.status:type_name -> rpc.Status
	54, // 42: auth.liveness.GetLivenessSummaryStatusResponse.summary_status:type_name -> auth.liveness.SummaryStatus
	55, // 43: auth.liveness.GetLivenessSummaryStatusResponse.summary_liveness_status:type_name -> auth.liveness.SummaryLivenessStatus
	56, // 44: auth.liveness.GetLivenessSummaryStatusResponse.summary_facematch_status:type_name -> auth.liveness.SummaryFacematchStatus
	57, // 45: auth.liveness.GetLivenessSummaryStatusResponse.liveness_attempt_status:type_name -> auth.liveness.LivenessBlanketStatus
	50, // 46: auth.liveness.GetLivenessSummaryStatusResponse.next_action_post_video_upload:type_name -> frontend.deeplink.Deeplink
	50, // 47: auth.liveness.GetLivenessSummaryStatusResponse.next_action:type_name -> frontend.deeplink.Deeplink
	44, // 48: auth.liveness.CreateLivenessAttemptRequest.liveness_flow:type_name -> auth.liveness.LivenessFlow
	37, // 49: auth.liveness.CreateLivenessAttemptResponse.status:type_name -> rpc.Status
	46, // 50: auth.liveness.CreateLivenessAttemptResponse.liveness_attempt:type_name -> auth.liveness.LivenessAttempt
	57, // 51: auth.liveness.CreateLivenessAttemptResponse.old_liveness_status:type_name -> auth.liveness.LivenessBlanketStatus
	38, // 52: auth.liveness.CreateLivenessAttemptResponse.old_attempt_status:type_name -> auth.liveness.LivenessStatus
	58, // 53: auth.liveness.AnnotateFacematchAttemptRequest.review_verdict:type_name -> auth.liveness.Verdict
	59, // 54: auth.liveness.AnnotateFacematchAttemptRequest.annotation:type_name -> auth.liveness.FaceMatchAnnotation
	37, // 55: auth.liveness.AnnotateFacematchAttemptResponse.status:type_name -> rpc.Status
	44, // 56: auth.liveness.RedoFaceMatchRequest.liveness_flow:type_name -> auth.liveness.LivenessFlow
	39, // 57: auth.liveness.RedoFaceMatchRequest.reference_photos:type_name -> api.typesv2.common.Image
	37, // 58: auth.liveness.RedoFaceMatchResponse.status:type_name -> rpc.Status
	31, // 59: auth.liveness.GetFaceMatchAttemptsRequest.criteria:type_name -> auth.liveness.FacematchRequestCriteria
	37, // 60: auth.liveness.GetFaceMatchAttemptsResponse.status:type_name -> rpc.Status
	60, // 61: auth.liveness.GetFaceMatchAttemptsResponse.face_match_attempts:type_name -> auth.liveness.FaceMatchAttempt
	44, // 62: auth.liveness.RetryLivenessRequest.liveness_flow:type_name -> auth.liveness.LivenessFlow
	37, // 63: auth.liveness.RetryLivenessResponse.status:type_name -> rpc.Status
	37, // 64: auth.liveness.GetLivenessAttemptResponse.status:type_name -> rpc.Status
	46, // 65: auth.liveness.GetLivenessAttemptResponse.liveness_attempt:type_name -> auth.liveness.LivenessAttempt
	1,  // 66: auth.liveness.Liveness.GetLivenessStatus:input_type -> auth.liveness.GetLivenessStatusRequest
	3,  // 67: auth.liveness.Liveness.FaceMatch:input_type -> auth.liveness.FaceMatchRequest
	5,  // 68: auth.liveness.Liveness.GetFaceMatchStatus:input_type -> auth.liveness.GetFaceMatchStatusRequest
	8,  // 69: auth.liveness.Liveness.CheckLivenessWithVideoStream:input_type -> auth.liveness.CheckLivenessWithVideoStreamRequest
	10, // 70: auth.liveness.Liveness.GenerateOTP:input_type -> auth.liveness.GenerateOTPRequest
	12, // 71: auth.liveness.Liveness.GetS3Image:input_type -> auth.liveness.GetS3ImageRequest
	14, // 72: auth.liveness.Liveness.GetLivenessAttempts:input_type -> auth.liveness.GetLivenessAttemptsRequest
	16, // 73: auth.liveness.Liveness.AnnotateLivenessAttempt:input_type -> auth.liveness.AnnotateLivenessAttemptRequest
	18, // 74: auth.liveness.Liveness.CreateLivenessSummary:input_type -> auth.liveness.CreateLivenessSummaryRequest
	20, // 75: auth.liveness.Liveness.GetLivenessSummary:input_type -> auth.liveness.GetLivenessSummaryRequest
	22, // 76: auth.liveness.Liveness.GetLivenessSummaryStatus:input_type -> auth.liveness.GetLivenessSummaryStatusRequest
	24, // 77: auth.liveness.Liveness.CreateLivenessAttempt:input_type -> auth.liveness.CreateLivenessAttemptRequest
	26, // 78: auth.liveness.Liveness.AnnotateFacematchAttempt:input_type -> auth.liveness.AnnotateFacematchAttemptRequest
	28, // 79: auth.liveness.Liveness.RedoFaceMatch:input_type -> auth.liveness.RedoFaceMatchRequest
	30, // 80: auth.liveness.Liveness.GetFaceMatchAttempts:input_type -> auth.liveness.GetFaceMatchAttemptsRequest
	33, // 81: auth.liveness.Liveness.RetryLiveness:input_type -> auth.liveness.RetryLivenessRequest
	35, // 82: auth.liveness.Liveness.GetLivenessAttempt:input_type -> auth.liveness.GetLivenessAttemptRequest
	2,  // 83: auth.liveness.Liveness.GetLivenessStatus:output_type -> auth.liveness.GetLivenessStatusResponse
	4,  // 84: auth.liveness.Liveness.FaceMatch:output_type -> auth.liveness.FaceMatchResponse
	6,  // 85: auth.liveness.Liveness.GetFaceMatchStatus:output_type -> auth.liveness.GetFaceMatchStatusResponse
	9,  // 86: auth.liveness.Liveness.CheckLivenessWithVideoStream:output_type -> auth.liveness.CheckLivenessWithVideoStreamResponse
	11, // 87: auth.liveness.Liveness.GenerateOTP:output_type -> auth.liveness.GenerateOTPResponse
	13, // 88: auth.liveness.Liveness.GetS3Image:output_type -> auth.liveness.GetS3ImageResponse
	15, // 89: auth.liveness.Liveness.GetLivenessAttempts:output_type -> auth.liveness.GetLivenessAttemptsResponse
	17, // 90: auth.liveness.Liveness.AnnotateLivenessAttempt:output_type -> auth.liveness.AnnotateLivenessAttemptResponse
	19, // 91: auth.liveness.Liveness.CreateLivenessSummary:output_type -> auth.liveness.CreateLivenessSummaryResponse
	21, // 92: auth.liveness.Liveness.GetLivenessSummary:output_type -> auth.liveness.GetLivenessSummaryResponse
	23, // 93: auth.liveness.Liveness.GetLivenessSummaryStatus:output_type -> auth.liveness.GetLivenessSummaryStatusResponse
	25, // 94: auth.liveness.Liveness.CreateLivenessAttempt:output_type -> auth.liveness.CreateLivenessAttemptResponse
	27, // 95: auth.liveness.Liveness.AnnotateFacematchAttempt:output_type -> auth.liveness.AnnotateFacematchAttemptResponse
	29, // 96: auth.liveness.Liveness.RedoFaceMatch:output_type -> auth.liveness.RedoFaceMatchResponse
	32, // 97: auth.liveness.Liveness.GetFaceMatchAttempts:output_type -> auth.liveness.GetFaceMatchAttemptsResponse
	34, // 98: auth.liveness.Liveness.RetryLiveness:output_type -> auth.liveness.RetryLivenessResponse
	36, // 99: auth.liveness.Liveness.GetLivenessAttempt:output_type -> auth.liveness.GetLivenessAttemptResponse
	83, // [83:100] is the sub-list for method output_type
	66, // [66:83] is the sub-list for method input_type
	66, // [66:66] is the sub-list for extension type_name
	66, // [66:66] is the sub-list for extension extendee
	0,  // [0:66] is the sub-list for field type_name
}

func init() { file_api_auth_liveness_service_proto_init() }
func file_api_auth_liveness_service_proto_init() {
	if File_api_auth_liveness_service_proto != nil {
		return
	}
	file_api_auth_liveness_internal_face_match_annotation_proto_init()
	file_api_auth_liveness_internal_face_match_attempt_proto_init()
	file_api_auth_liveness_internal_liveness_attempt_proto_init()
	file_api_auth_liveness_internal_liveness_summary_proto_init()
	file_api_auth_liveness_types_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_auth_liveness_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Frames); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLivenessStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLivenessStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceMatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceMatchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFaceMatchStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFaceMatchStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckLivenessWithVideoStreamRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckLivenessWithVideoStreamResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateOTPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateOTPResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetS3ImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetS3ImageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLivenessAttemptsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLivenessAttemptsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnotateLivenessAttemptRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnotateLivenessAttemptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLivenessSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLivenessSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLivenessSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLivenessSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLivenessSummaryStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLivenessSummaryStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLivenessAttemptRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLivenessAttemptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnotateFacematchAttemptRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnotateFacematchAttemptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedoFaceMatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedoFaceMatchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFaceMatchAttemptsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FacematchRequestCriteria); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFaceMatchAttemptsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryLivenessRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryLivenessResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLivenessAttemptRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_liveness_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLivenessAttemptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_auth_liveness_service_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*CheckLivenessWithVideoStreamRequest_Options)(nil),
		(*CheckLivenessWithVideoStreamRequest_VideoChunk)(nil),
		(*CheckLivenessWithVideoStreamRequest_ImageFrame)(nil),
		(*CheckLivenessWithVideoStreamRequest_PassiveImageFrames)(nil),
	}
	file_api_auth_liveness_service_proto_msgTypes[31].OneofWrappers = []interface{}{
		(*FacematchRequestCriteria_ActorId)(nil),
		(*FacematchRequestCriteria_LivenessRequestId)(nil),
		(*FacematchRequestCriteria_FacematchRequestId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_liveness_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   37,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_auth_liveness_service_proto_goTypes,
		DependencyIndexes: file_api_auth_liveness_service_proto_depIdxs,
		MessageInfos:      file_api_auth_liveness_service_proto_msgTypes,
	}.Build()
	File_api_auth_liveness_service_proto = out.File
	file_api_auth_liveness_service_proto_rawDesc = nil
	file_api_auth_liveness_service_proto_goTypes = nil
	file_api_auth_liveness_service_proto_depIdxs = nil
}
