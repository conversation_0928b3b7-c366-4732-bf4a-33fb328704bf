// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/liveness/internal/liveness_summary.proto

package liveness

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)
)

// Validate checks the field values on LivenessSummary with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LivenessSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessSummary with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessSummaryMultiError, or nil if none found.
func (m *LivenessSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for RequestId

	// no validation rules for LivenessFlow

	// no validation rules for LivenessAttemptId

	// no validation rules for FacematchAttemptId

	if all {
		switch v := interface{}(m.GetFacematchInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessSummaryValidationError{
					field:  "FacematchInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessSummaryValidationError{
					field:  "FacematchInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFacematchInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessSummaryValidationError{
				field:  "FacematchInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaxAttempts

	// no validation rules for AttemptsCount

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessSummaryValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessSummaryValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessSummaryValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessSummaryValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessSummaryValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessSummaryValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SummaryLivenessStatus

	// no validation rules for SummaryFacematchStatus

	// no validation rules for StrictnessLogic

	// no validation rules for ForceManualReview

	if all {
		switch v := interface{}(m.GetExpireAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessSummaryValidationError{
					field:  "ExpireAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessSummaryValidationError{
					field:  "ExpireAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpireAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessSummaryValidationError{
				field:  "ExpireAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClientRequestParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessSummaryValidationError{
					field:  "ClientRequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessSummaryValidationError{
					field:  "ClientRequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientRequestParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessSummaryValidationError{
				field:  "ClientRequestParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LivenessSummaryMultiError(errors)
	}

	return nil
}

// LivenessSummaryMultiError is an error wrapping multiple validation errors
// returned by LivenessSummary.ValidateAll() if the designated constraints
// aren't met.
type LivenessSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessSummaryMultiError) AllErrors() []error { return m }

// LivenessSummaryValidationError is the validation error returned by
// LivenessSummary.Validate if the designated constraints aren't met.
type LivenessSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessSummaryValidationError) ErrorName() string { return "LivenessSummaryValidationError" }

// Error satisfies the builtin error interface
func (e LivenessSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessSummaryValidationError{}

// Validate checks the field values on CustomIntroScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomIntroScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomIntroScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomIntroScreenOptionsMultiError, or nil if none found.
func (m *CustomIntroScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomIntroScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomIntroScreenOptionsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomIntroScreenOptionsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomIntroScreenOptionsValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomIntroScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomIntroScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomIntroScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomIntroScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomIntroScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomIntroScreenOptionsValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetListItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomIntroScreenOptionsValidationError{
						field:  fmt.Sprintf("ListItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomIntroScreenOptionsValidationError{
						field:  fmt.Sprintf("ListItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomIntroScreenOptionsValidationError{
					field:  fmt.Sprintf("ListItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomIntroScreenOptionsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomIntroScreenOptionsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomIntroScreenOptionsValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CustomIntroScreenOptionsMultiError(errors)
	}

	return nil
}

// CustomIntroScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by CustomIntroScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type CustomIntroScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomIntroScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomIntroScreenOptionsMultiError) AllErrors() []error { return m }

// CustomIntroScreenOptionsValidationError is the validation error returned by
// CustomIntroScreenOptions.Validate if the designated constraints aren't met.
type CustomIntroScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomIntroScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomIntroScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomIntroScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomIntroScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomIntroScreenOptionsValidationError) ErrorName() string {
	return "CustomIntroScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CustomIntroScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomIntroScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomIntroScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomIntroScreenOptionsValidationError{}

// Validate checks the field values on NextActionHooks with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NextActionHooks) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NextActionHooks with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NextActionHooksMultiError, or nil if none found.
func (m *NextActionHooks) ValidateAll() error {
	return m.validate(true)
}

func (m *NextActionHooks) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNextActionPostVideoUpload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NextActionHooksValidationError{
					field:  "NextActionPostVideoUpload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NextActionHooksValidationError{
					field:  "NextActionPostVideoUpload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionPostVideoUpload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NextActionHooksValidationError{
				field:  "NextActionPostVideoUpload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextActionPostSuccess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NextActionHooksValidationError{
					field:  "NextActionPostSuccess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NextActionHooksValidationError{
					field:  "NextActionPostSuccess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionPostSuccess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NextActionHooksValidationError{
				field:  "NextActionPostSuccess",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextActionPostFailure()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NextActionHooksValidationError{
					field:  "NextActionPostFailure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NextActionHooksValidationError{
					field:  "NextActionPostFailure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionPostFailure()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NextActionHooksValidationError{
				field:  "NextActionPostFailure",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextActionPostExpiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NextActionHooksValidationError{
					field:  "NextActionPostExpiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NextActionHooksValidationError{
					field:  "NextActionPostExpiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionPostExpiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NextActionHooksValidationError{
				field:  "NextActionPostExpiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDefaultNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NextActionHooksValidationError{
					field:  "DefaultNextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NextActionHooksValidationError{
					field:  "DefaultNextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefaultNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NextActionHooksValidationError{
				field:  "DefaultNextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NextActionHooksMultiError(errors)
	}

	return nil
}

// NextActionHooksMultiError is an error wrapping multiple validation errors
// returned by NextActionHooks.ValidateAll() if the designated constraints
// aren't met.
type NextActionHooksMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NextActionHooksMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NextActionHooksMultiError) AllErrors() []error { return m }

// NextActionHooksValidationError is the validation error returned by
// NextActionHooks.Validate if the designated constraints aren't met.
type NextActionHooksValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NextActionHooksValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NextActionHooksValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NextActionHooksValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NextActionHooksValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NextActionHooksValidationError) ErrorName() string { return "NextActionHooksValidationError" }

// Error satisfies the builtin error interface
func (e NextActionHooksValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNextActionHooks.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NextActionHooksValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NextActionHooksValidationError{}

// Validate checks the field values on FacematchInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FacematchInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FacematchInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FacematchInfoMultiError, or
// nil if none found.
func (m *FacematchInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FacematchInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFacematchPhotos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FacematchInfoValidationError{
						field:  fmt.Sprintf("FacematchPhotos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FacematchInfoValidationError{
						field:  fmt.Sprintf("FacematchPhotos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FacematchInfoValidationError{
					field:  fmt.Sprintf("FacematchPhotos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FacematchInfoMultiError(errors)
	}

	return nil
}

// FacematchInfoMultiError is an error wrapping multiple validation errors
// returned by FacematchInfo.ValidateAll() if the designated constraints
// aren't met.
type FacematchInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FacematchInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FacematchInfoMultiError) AllErrors() []error { return m }

// FacematchInfoValidationError is the validation error returned by
// FacematchInfo.Validate if the designated constraints aren't met.
type FacematchInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FacematchInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FacematchInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FacematchInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FacematchInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FacematchInfoValidationError) ErrorName() string { return "FacematchInfoValidationError" }

// Error satisfies the builtin error interface
func (e FacematchInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFacematchInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FacematchInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FacematchInfoValidationError{}

// Validate checks the field values on LivenessSummary_ClientRequestParams with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *LivenessSummary_ClientRequestParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessSummary_ClientRequestParams
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LivenessSummary_ClientRequestParamsMultiError, or nil if none found.
func (m *LivenessSummary_ClientRequestParams) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessSummary_ClientRequestParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNextActionPostVideoUpload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessSummary_ClientRequestParamsValidationError{
					field:  "NextActionPostVideoUpload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessSummary_ClientRequestParamsValidationError{
					field:  "NextActionPostVideoUpload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionPostVideoUpload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessSummary_ClientRequestParamsValidationError{
				field:  "NextActionPostVideoUpload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextActionHooks()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessSummary_ClientRequestParamsValidationError{
					field:  "NextActionHooks",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessSummary_ClientRequestParamsValidationError{
					field:  "NextActionHooks",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionHooks()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessSummary_ClientRequestParamsValidationError{
				field:  "NextActionHooks",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCustomIntroScreenOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessSummary_ClientRequestParamsValidationError{
					field:  "CustomIntroScreenOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessSummary_ClientRequestParamsValidationError{
					field:  "CustomIntroScreenOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomIntroScreenOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessSummary_ClientRequestParamsValidationError{
				field:  "CustomIntroScreenOptions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LivenessSummary_ClientRequestParamsMultiError(errors)
	}

	return nil
}

// LivenessSummary_ClientRequestParamsMultiError is an error wrapping multiple
// validation errors returned by
// LivenessSummary_ClientRequestParams.ValidateAll() if the designated
// constraints aren't met.
type LivenessSummary_ClientRequestParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessSummary_ClientRequestParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessSummary_ClientRequestParamsMultiError) AllErrors() []error { return m }

// LivenessSummary_ClientRequestParamsValidationError is the validation error
// returned by LivenessSummary_ClientRequestParams.Validate if the designated
// constraints aren't met.
type LivenessSummary_ClientRequestParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessSummary_ClientRequestParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessSummary_ClientRequestParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessSummary_ClientRequestParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessSummary_ClientRequestParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessSummary_ClientRequestParamsValidationError) ErrorName() string {
	return "LivenessSummary_ClientRequestParamsValidationError"
}

// Error satisfies the builtin error interface
func (e LivenessSummary_ClientRequestParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessSummary_ClientRequestParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessSummary_ClientRequestParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessSummary_ClientRequestParamsValidationError{}
