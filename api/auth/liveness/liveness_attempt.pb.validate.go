// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/liveness/internal/liveness_attempt.proto

package liveness

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on LivenessAttempt with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LivenessAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessAttempt with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessAttemptMultiError, or nil if none found.
func (m *LivenessAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AttemptId

	// no validation rules for RequestId

	// no validation rules for VendorRequestId

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetImageFrame()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessAttemptValidationError{
					field:  "ImageFrame",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessAttemptValidationError{
					field:  "ImageFrame",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImageFrame()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessAttemptValidationError{
				field:  "ImageFrame",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VideoLocation

	// no validation rules for Status

	// no validation rules for Otp

	// no validation rules for DetectedOtp

	// no validation rules for GoogleDetectedOtp

	// no validation rules for OtpScore

	// no validation rules for LivenessScore

	// no validation rules for BlinkConfidence

	// no validation rules for BlinkHeuristicsConfidence

	// no validation rules for BlinkLiveness

	// no validation rules for VendorResponse

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessAttemptValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LivenessFlow

	// no validation rules for BlinkLivenessStatus

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessAttemptValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessAttemptValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessAttemptValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessAttemptValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInHouseLiveness()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessAttemptValidationError{
					field:  "InHouseLiveness",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessAttemptValidationError{
					field:  "InHouseLiveness",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInHouseLiveness()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessAttemptValidationError{
				field:  "InHouseLiveness",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAnnotation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LivenessAttemptValidationError{
					field:  "Annotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LivenessAttemptValidationError{
					field:  "Annotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnotation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LivenessAttemptValidationError{
				field:  "Annotation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SummaryId

	// no validation rules for StrictnessLogic

	// no validation rules for VendorStatus

	if len(errors) > 0 {
		return LivenessAttemptMultiError(errors)
	}

	return nil
}

// LivenessAttemptMultiError is an error wrapping multiple validation errors
// returned by LivenessAttempt.ValidateAll() if the designated constraints
// aren't met.
type LivenessAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessAttemptMultiError) AllErrors() []error { return m }

// LivenessAttemptValidationError is the validation error returned by
// LivenessAttempt.Validate if the designated constraints aren't met.
type LivenessAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessAttemptValidationError) ErrorName() string { return "LivenessAttemptValidationError" }

// Error satisfies the builtin error interface
func (e LivenessAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessAttemptValidationError{}

// Validate checks the field values on InHouseLiveness with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InHouseLiveness) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InHouseLiveness with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InHouseLivenessMultiError, or nil if none found.
func (m *InHouseLiveness) ValidateAll() error {
	return m.validate(true)
}

func (m *InHouseLiveness) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetRawResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InHouseLivenessValidationError{
					field:  "RawResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InHouseLivenessValidationError{
					field:  "RawResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRawResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InHouseLivenessValidationError{
				field:  "RawResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawVendorResponse

	if len(errors) > 0 {
		return InHouseLivenessMultiError(errors)
	}

	return nil
}

// InHouseLivenessMultiError is an error wrapping multiple validation errors
// returned by InHouseLiveness.ValidateAll() if the designated constraints
// aren't met.
type InHouseLivenessMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InHouseLivenessMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InHouseLivenessMultiError) AllErrors() []error { return m }

// InHouseLivenessValidationError is the validation error returned by
// InHouseLiveness.Validate if the designated constraints aren't met.
type InHouseLivenessValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InHouseLivenessValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InHouseLivenessValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InHouseLivenessValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InHouseLivenessValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InHouseLivenessValidationError) ErrorName() string { return "InHouseLivenessValidationError" }

// Error satisfies the builtin error interface
func (e InHouseLivenessValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInHouseLiveness.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InHouseLivenessValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InHouseLivenessValidationError{}

// Validate checks the field values on Metadata with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Metadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Metadata with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MetadataMultiError, or nil
// if none found.
func (m *Metadata) ValidateAll() error {
	return m.validate(true)
}

func (m *Metadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InvalidVideoRetryCount

	// no validation rules for UserLocationToken

	// no validation rules for UserIpAddress

	if all {
		switch v := interface{}(m.GetDeviceSensorData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MetadataValidationError{
					field:  "DeviceSensorData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MetadataValidationError{
					field:  "DeviceSensorData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceSensorData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MetadataValidationError{
				field:  "DeviceSensorData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStreamingStartedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MetadataValidationError{
					field:  "StreamingStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MetadataValidationError{
					field:  "StreamingStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStreamingStartedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MetadataValidationError{
				field:  "StreamingStartedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVpnInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MetadataValidationError{
					field:  "VpnInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MetadataValidationError{
					field:  "VpnInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVpnInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MetadataValidationError{
				field:  "VpnInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MetadataMultiError(errors)
	}

	return nil
}

// MetadataMultiError is an error wrapping multiple validation errors returned
// by Metadata.ValidateAll() if the designated constraints aren't met.
type MetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MetadataMultiError) AllErrors() []error { return m }

// MetadataValidationError is the validation error returned by
// Metadata.Validate if the designated constraints aren't met.
type MetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MetadataValidationError) ErrorName() string { return "MetadataValidationError" }

// Error satisfies the builtin error interface
func (e MetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MetadataValidationError{}

// Validate checks the field values on DeviceSensorData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeviceSensorData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceSensorData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeviceSensorDataMultiError, or nil if none found.
func (m *DeviceSensorData) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceSensorData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAccelerometerData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeviceSensorDataValidationError{
						field:  fmt.Sprintf("AccelerometerData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeviceSensorDataValidationError{
						field:  fmt.Sprintf("AccelerometerData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeviceSensorDataValidationError{
					field:  fmt.Sprintf("AccelerometerData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetGyroscopeData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeviceSensorDataValidationError{
						field:  fmt.Sprintf("GyroscopeData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeviceSensorDataValidationError{
						field:  fmt.Sprintf("GyroscopeData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeviceSensorDataValidationError{
					field:  fmt.Sprintf("GyroscopeData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DeviceSensorDataMultiError(errors)
	}

	return nil
}

// DeviceSensorDataMultiError is an error wrapping multiple validation errors
// returned by DeviceSensorData.ValidateAll() if the designated constraints
// aren't met.
type DeviceSensorDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceSensorDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceSensorDataMultiError) AllErrors() []error { return m }

// DeviceSensorDataValidationError is the validation error returned by
// DeviceSensorData.Validate if the designated constraints aren't met.
type DeviceSensorDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceSensorDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceSensorDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceSensorDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceSensorDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceSensorDataValidationError) ErrorName() string { return "DeviceSensorDataValidationError" }

// Error satisfies the builtin error interface
func (e DeviceSensorDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceSensorData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceSensorDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceSensorDataValidationError{}

// Validate checks the field values on Annotation with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Annotation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Annotation with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AnnotationMultiError, or
// nil if none found.
func (m *Annotation) ValidateAll() error {
	return m.validate(true)
}

func (m *Annotation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LivePerson

	// no validation rules for ConcurWithKarza

	// no validation rules for Remarks

	// no validation rules for PotentialFraud

	// no validation rules for UserFaceShown

	// no validation rules for ReviewVerdict

	// no validation rules for ReviewedBy

	if all {
		switch v := interface{}(m.GetReviewedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnotationValidationError{
					field:  "ReviewedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnotationValidationError{
					field:  "ReviewedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReviewedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnotationValidationError{
				field:  "ReviewedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReviewRequired

	// no validation rules for RetryGiven

	// no validation rules for ReReviewVerdict

	// no validation rules for ReReviewedBy

	if all {
		switch v := interface{}(m.GetReReviewedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnotationValidationError{
					field:  "ReReviewedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnotationValidationError{
					field:  "ReReviewedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReReviewedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnotationValidationError{
				field:  "ReReviewedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReReviewRemarks

	// no validation rules for ReReviewErrorType

	// no validation rules for CaseId

	if len(errors) > 0 {
		return AnnotationMultiError(errors)
	}

	return nil
}

// AnnotationMultiError is an error wrapping multiple validation errors
// returned by Annotation.ValidateAll() if the designated constraints aren't met.
type AnnotationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnotationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnotationMultiError) AllErrors() []error { return m }

// AnnotationValidationError is the validation error returned by
// Annotation.Validate if the designated constraints aren't met.
type AnnotationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnotationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnotationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnotationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnotationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnotationValidationError) ErrorName() string { return "AnnotationValidationError" }

// Error satisfies the builtin error interface
func (e AnnotationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnotation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnotationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnotationValidationError{}
