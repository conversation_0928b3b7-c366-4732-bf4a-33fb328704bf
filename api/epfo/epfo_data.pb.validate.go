// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/epfo/epfo_data.proto

package epfo

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on EpfoData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EpfoData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EpfoData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EpfoDataMultiError, or nil
// if none found.
func (m *EpfoData) ValidateAll() error {
	return m.validate(true)
}

func (m *EpfoData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for Client

	// no validation rules for Vendor

	// no validation rules for RawEpfoData

	if all {
		switch v := interface{}(m.GetFetchedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EpfoDataValidationError{
					field:  "FetchedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EpfoDataValidationError{
					field:  "FetchedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFetchedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EpfoDataValidationError{
				field:  "FetchedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EpfoDataValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EpfoDataValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EpfoDataValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EpfoDataValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EpfoDataValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EpfoDataValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EpfoDataValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EpfoDataValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EpfoDataValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EpfoDataMultiError(errors)
	}

	return nil
}

// EpfoDataMultiError is an error wrapping multiple validation errors returned
// by EpfoData.ValidateAll() if the designated constraints aren't met.
type EpfoDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EpfoDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EpfoDataMultiError) AllErrors() []error { return m }

// EpfoDataValidationError is the validation error returned by
// EpfoData.Validate if the designated constraints aren't met.
type EpfoDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EpfoDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EpfoDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EpfoDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EpfoDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EpfoDataValidationError) ErrorName() string { return "EpfoDataValidationError" }

// Error satisfies the builtin error interface
func (e EpfoDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEpfoData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EpfoDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EpfoDataValidationError{}

// Validate checks the field values on EpfoRawResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EpfoRawResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EpfoRawResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EpfoRawResponseMultiError, or nil if none found.
func (m *EpfoRawResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EpfoRawResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vendor

	// no validation rules for RawEpfoData

	if len(errors) > 0 {
		return EpfoRawResponseMultiError(errors)
	}

	return nil
}

// EpfoRawResponseMultiError is an error wrapping multiple validation errors
// returned by EpfoRawResponse.ValidateAll() if the designated constraints
// aren't met.
type EpfoRawResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EpfoRawResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EpfoRawResponseMultiError) AllErrors() []error { return m }

// EpfoRawResponseValidationError is the validation error returned by
// EpfoRawResponse.Validate if the designated constraints aren't met.
type EpfoRawResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EpfoRawResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EpfoRawResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EpfoRawResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EpfoRawResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EpfoRawResponseValidationError) ErrorName() string { return "EpfoRawResponseValidationError" }

// Error satisfies the builtin error interface
func (e EpfoRawResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEpfoRawResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EpfoRawResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EpfoRawResponseValidationError{}
