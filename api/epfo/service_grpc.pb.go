//go:generate gen_sql -types=Client,Vendor

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/epfo/service.proto

package epfo

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Epfo_GetEpfoData_FullMethodName = "/epfo.Epfo/GetEpfoData"
)

// EpfoClient is the client API for Epfo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EpfoClient interface {
	// This rpc should be used to get employment verification data for a user.
	// It fetches data from the vendor if not already present in our system
	// If vendor data fetching is in progress, rpc will throw transient error.
	GetEpfoData(ctx context.Context, in *GetEpfoDataRequest, opts ...grpc.CallOption) (*GetEpfoDataResponse, error)
}

type epfoClient struct {
	cc grpc.ClientConnInterface
}

func NewEpfoClient(cc grpc.ClientConnInterface) EpfoClient {
	return &epfoClient{cc}
}

func (c *epfoClient) GetEpfoData(ctx context.Context, in *GetEpfoDataRequest, opts ...grpc.CallOption) (*GetEpfoDataResponse, error) {
	out := new(GetEpfoDataResponse)
	err := c.cc.Invoke(ctx, Epfo_GetEpfoData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EpfoServer is the server API for Epfo service.
// All implementations should embed UnimplementedEpfoServer
// for forward compatibility
type EpfoServer interface {
	// This rpc should be used to get employment verification data for a user.
	// It fetches data from the vendor if not already present in our system
	// If vendor data fetching is in progress, rpc will throw transient error.
	GetEpfoData(context.Context, *GetEpfoDataRequest) (*GetEpfoDataResponse, error)
}

// UnimplementedEpfoServer should be embedded to have forward compatible implementations.
type UnimplementedEpfoServer struct {
}

func (UnimplementedEpfoServer) GetEpfoData(context.Context, *GetEpfoDataRequest) (*GetEpfoDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEpfoData not implemented")
}

// UnsafeEpfoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EpfoServer will
// result in compilation errors.
type UnsafeEpfoServer interface {
	mustEmbedUnimplementedEpfoServer()
}

func RegisterEpfoServer(s grpc.ServiceRegistrar, srv EpfoServer) {
	s.RegisterService(&Epfo_ServiceDesc, srv)
}

func _Epfo_GetEpfoData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEpfoDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpfoServer).GetEpfoData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epfo_GetEpfoData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpfoServer).GetEpfoData(ctx, req.(*GetEpfoDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Epfo_ServiceDesc is the grpc.ServiceDesc for Epfo service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Epfo_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "epfo.Epfo",
	HandlerType: (*EpfoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEpfoData",
			Handler:    _Epfo_GetEpfoData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/epfo/service.proto",
}
