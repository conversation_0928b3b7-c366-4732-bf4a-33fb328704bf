// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/epfo/epfo_data.pb.go

package epfo

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing EpfoData while reading from DB
func (a *EpfoData) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the EpfoData in string format in DB
func (a *EpfoData) Value() (driver.Value, error) {
	return protojson.Marshal(a)
}

// Marshaler interface implementation for EpfoData
func (a *EpfoData) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for EpfoData
func (a *EpfoData) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
