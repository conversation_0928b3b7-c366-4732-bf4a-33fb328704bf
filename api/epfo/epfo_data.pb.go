//go:generate gen_sql -types=EpfoData, EpfoDataFieldMask

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/epfo/epfo_data.proto

package epfo

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EpfoDataFieldMask int32

const (
	EpfoDataFieldMask_EPFO_DATA_FIELD_MASK_UNSPECIFIED   EpfoDataFieldMask = 0
	EpfoDataFieldMask_EPFO_DATA_FIELD_MASK_CLIENT        EpfoDataFieldMask = 1
	EpfoDataFieldMask_EPFO_DATA_FIELD_MASK_RAW_EPFO_DATA EpfoDataFieldMask = 2
	EpfoDataFieldMask_EPFO_DATA_FIELD_MASK_FETCHED_AT    EpfoDataFieldMask = 3
	EpfoDataFieldMask_EPFO_DATA_FIELD_MASK_VENDOR        EpfoDataFieldMask = 4
)

// Enum value maps for EpfoDataFieldMask.
var (
	EpfoDataFieldMask_name = map[int32]string{
		0: "EPFO_DATA_FIELD_MASK_UNSPECIFIED",
		1: "EPFO_DATA_FIELD_MASK_CLIENT",
		2: "EPFO_DATA_FIELD_MASK_RAW_EPFO_DATA",
		3: "EPFO_DATA_FIELD_MASK_FETCHED_AT",
		4: "EPFO_DATA_FIELD_MASK_VENDOR",
	}
	EpfoDataFieldMask_value = map[string]int32{
		"EPFO_DATA_FIELD_MASK_UNSPECIFIED":   0,
		"EPFO_DATA_FIELD_MASK_CLIENT":        1,
		"EPFO_DATA_FIELD_MASK_RAW_EPFO_DATA": 2,
		"EPFO_DATA_FIELD_MASK_FETCHED_AT":    3,
		"EPFO_DATA_FIELD_MASK_VENDOR":        4,
	}
)

func (x EpfoDataFieldMask) Enum() *EpfoDataFieldMask {
	p := new(EpfoDataFieldMask)
	*p = x
	return p
}

func (x EpfoDataFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EpfoDataFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_epfo_epfo_data_proto_enumTypes[0].Descriptor()
}

func (EpfoDataFieldMask) Type() protoreflect.EnumType {
	return &file_api_epfo_epfo_data_proto_enumTypes[0]
}

func (x EpfoDataFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EpfoDataFieldMask.Descriptor instead.
func (EpfoDataFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_epfo_epfo_data_proto_rawDescGZIP(), []int{0}
}

type EpfoData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId     string                 `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Client      Client                 `protobuf:"varint,3,opt,name=client,proto3,enum=epfo.Client" json:"client,omitempty"`
	Vendor      Vendor                 `protobuf:"varint,4,opt,name=vendor,proto3,enum=epfo.Vendor" json:"vendor,omitempty"`
	RawEpfoData string                 `protobuf:"bytes,5,opt,name=raw_epfo_data,json=rawEpfoData,proto3" json:"raw_epfo_data,omitempty"`
	FetchedAt   *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=fetched_at,json=fetchedAt,proto3" json:"fetched_at,omitempty"`
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt   *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt   *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *EpfoData) Reset() {
	*x = EpfoData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_epfo_epfo_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EpfoData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EpfoData) ProtoMessage() {}

func (x *EpfoData) ProtoReflect() protoreflect.Message {
	mi := &file_api_epfo_epfo_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EpfoData.ProtoReflect.Descriptor instead.
func (*EpfoData) Descriptor() ([]byte, []int) {
	return file_api_epfo_epfo_data_proto_rawDescGZIP(), []int{0}
}

func (x *EpfoData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EpfoData) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *EpfoData) GetClient() Client {
	if x != nil {
		return x.Client
	}
	return Client_CLIENT_UNSPECIFIED
}

func (x *EpfoData) GetVendor() Vendor {
	if x != nil {
		return x.Vendor
	}
	return Vendor_VENDOR_UNSPECIFIED
}

func (x *EpfoData) GetRawEpfoData() string {
	if x != nil {
		return x.RawEpfoData
	}
	return ""
}

func (x *EpfoData) GetFetchedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FetchedAt
	}
	return nil
}

func (x *EpfoData) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *EpfoData) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *EpfoData) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

type EpfoRawResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vendor      string `protobuf:"bytes,1,opt,name=vendor,proto3" json:"vendor,omitempty"`
	RawEpfoData string `protobuf:"bytes,2,opt,name=raw_epfo_data,json=rawEpfoData,proto3" json:"raw_epfo_data,omitempty"`
}

func (x *EpfoRawResponse) Reset() {
	*x = EpfoRawResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_epfo_epfo_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EpfoRawResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EpfoRawResponse) ProtoMessage() {}

func (x *EpfoRawResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_epfo_epfo_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EpfoRawResponse.ProtoReflect.Descriptor instead.
func (*EpfoRawResponse) Descriptor() ([]byte, []int) {
	return file_api_epfo_epfo_data_proto_rawDescGZIP(), []int{1}
}

func (x *EpfoRawResponse) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *EpfoRawResponse) GetRawEpfoData() string {
	if x != nil {
		return x.RawEpfoData
	}
	return ""
}

var File_api_epfo_epfo_data_proto protoreflect.FileDescriptor

var file_api_epfo_epfo_data_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x70, 0x66, 0x6f, 0x2f, 0x65, 0x70, 0x66, 0x6f, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x65, 0x70, 0x66, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x70, 0x66, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91, 0x03, 0x0a, 0x08, 0x45, 0x70,
	0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x24, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x0c, 0x2e, 0x65, 0x70, 0x66, 0x6f, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52,
	0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x65, 0x70, 0x66, 0x6f, 0x2e, 0x56,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x22, 0x0a,
	0x0d, 0x72, 0x61, 0x77, 0x5f, 0x65, 0x70, 0x66, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x61, 0x77, 0x45, 0x70, 0x66, 0x6f, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x39, 0x0a, 0x0a, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x4d, 0x0a,
	0x0f, 0x65, 0x70, 0x66, 0x6f, 0x52, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x22, 0x0a, 0x0d, 0x72, 0x61, 0x77, 0x5f,
	0x65, 0x70, 0x66, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x72, 0x61, 0x77, 0x45, 0x70, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x2a, 0xc8, 0x01, 0x0a,
	0x11, 0x45, 0x70, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x12, 0x24, 0x0a, 0x20, 0x45, 0x50, 0x46, 0x4f, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x50, 0x46, 0x4f,
	0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x45, 0x50, 0x46,
	0x4f, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x52, 0x41, 0x57, 0x5f, 0x45, 0x50, 0x46, 0x4f, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10,
	0x02, 0x12, 0x23, 0x0a, 0x1f, 0x45, 0x50, 0x46, 0x4f, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x50, 0x46, 0x4f, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56,
	0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x04, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x70, 0x66, 0x6f, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x70, 0x66, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_epfo_epfo_data_proto_rawDescOnce sync.Once
	file_api_epfo_epfo_data_proto_rawDescData = file_api_epfo_epfo_data_proto_rawDesc
)

func file_api_epfo_epfo_data_proto_rawDescGZIP() []byte {
	file_api_epfo_epfo_data_proto_rawDescOnce.Do(func() {
		file_api_epfo_epfo_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_epfo_epfo_data_proto_rawDescData)
	})
	return file_api_epfo_epfo_data_proto_rawDescData
}

var file_api_epfo_epfo_data_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_epfo_epfo_data_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_epfo_epfo_data_proto_goTypes = []interface{}{
	(EpfoDataFieldMask)(0),        // 0: epfo.EpfoDataFieldMask
	(*EpfoData)(nil),              // 1: epfo.EpfoData
	(*EpfoRawResponse)(nil),       // 2: epfo.epfoRawResponse
	(Client)(0),                   // 3: epfo.Client
	(Vendor)(0),                   // 4: epfo.Vendor
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
}
var file_api_epfo_epfo_data_proto_depIdxs = []int32{
	3, // 0: epfo.EpfoData.client:type_name -> epfo.Client
	4, // 1: epfo.EpfoData.vendor:type_name -> epfo.Vendor
	5, // 2: epfo.EpfoData.fetched_at:type_name -> google.protobuf.Timestamp
	5, // 3: epfo.EpfoData.created_at:type_name -> google.protobuf.Timestamp
	5, // 4: epfo.EpfoData.updated_at:type_name -> google.protobuf.Timestamp
	5, // 5: epfo.EpfoData.deleted_at:type_name -> google.protobuf.Timestamp
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_epfo_epfo_data_proto_init() }
func file_api_epfo_epfo_data_proto_init() {
	if File_api_epfo_epfo_data_proto != nil {
		return
	}
	file_api_epfo_service_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_epfo_epfo_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EpfoData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_epfo_epfo_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EpfoRawResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_epfo_epfo_data_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_epfo_epfo_data_proto_goTypes,
		DependencyIndexes: file_api_epfo_epfo_data_proto_depIdxs,
		EnumInfos:         file_api_epfo_epfo_data_proto_enumTypes,
		MessageInfos:      file_api_epfo_epfo_data_proto_msgTypes,
	}.Build()
	File_api_epfo_epfo_data_proto = out.File
	file_api_epfo_epfo_data_proto_rawDesc = nil
	file_api_epfo_epfo_data_proto_goTypes = nil
	file_api_epfo_epfo_data_proto_depIdxs = nil
}
