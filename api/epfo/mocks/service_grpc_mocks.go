// Code generated by MockGen. DO NOT EDIT.
// Source: api/epfo/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	epfo "github.com/epifi/gamma/api/epfo"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockEpfoClient is a mock of EpfoClient interface.
type MockEpfoClient struct {
	ctrl     *gomock.Controller
	recorder *MockEpfoClientMockRecorder
}

// MockEpfoClientMockRecorder is the mock recorder for MockEpfoClient.
type MockEpfoClientMockRecorder struct {
	mock *MockEpfoClient
}

// NewMockEpfoClient creates a new mock instance.
func NewMockEpfoClient(ctrl *gomock.Controller) *MockEpfoClient {
	mock := &MockEpfoClient{ctrl: ctrl}
	mock.recorder = &MockEpfoClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEpfoClient) EXPECT() *MockEpfoClientMockRecorder {
	return m.recorder
}

// GetEpfoData mocks base method.
func (m *MockEpfoClient) GetEpfoData(ctx context.Context, in *epfo.GetEpfoDataRequest, opts ...grpc.CallOption) (*epfo.GetEpfoDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEpfoData", varargs...)
	ret0, _ := ret[0].(*epfo.GetEpfoDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEpfoData indicates an expected call of GetEpfoData.
func (mr *MockEpfoClientMockRecorder) GetEpfoData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEpfoData", reflect.TypeOf((*MockEpfoClient)(nil).GetEpfoData), varargs...)
}

// MockEpfoServer is a mock of EpfoServer interface.
type MockEpfoServer struct {
	ctrl     *gomock.Controller
	recorder *MockEpfoServerMockRecorder
}

// MockEpfoServerMockRecorder is the mock recorder for MockEpfoServer.
type MockEpfoServerMockRecorder struct {
	mock *MockEpfoServer
}

// NewMockEpfoServer creates a new mock instance.
func NewMockEpfoServer(ctrl *gomock.Controller) *MockEpfoServer {
	mock := &MockEpfoServer{ctrl: ctrl}
	mock.recorder = &MockEpfoServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEpfoServer) EXPECT() *MockEpfoServerMockRecorder {
	return m.recorder
}

// GetEpfoData mocks base method.
func (m *MockEpfoServer) GetEpfoData(arg0 context.Context, arg1 *epfo.GetEpfoDataRequest) (*epfo.GetEpfoDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEpfoData", arg0, arg1)
	ret0, _ := ret[0].(*epfo.GetEpfoDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEpfoData indicates an expected call of GetEpfoData.
func (mr *MockEpfoServerMockRecorder) GetEpfoData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEpfoData", reflect.TypeOf((*MockEpfoServer)(nil).GetEpfoData), arg0, arg1)
}

// MockUnsafeEpfoServer is a mock of UnsafeEpfoServer interface.
type MockUnsafeEpfoServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeEpfoServerMockRecorder
}

// MockUnsafeEpfoServerMockRecorder is the mock recorder for MockUnsafeEpfoServer.
type MockUnsafeEpfoServerMockRecorder struct {
	mock *MockUnsafeEpfoServer
}

// NewMockUnsafeEpfoServer creates a new mock instance.
func NewMockUnsafeEpfoServer(ctrl *gomock.Controller) *MockUnsafeEpfoServer {
	mock := &MockUnsafeEpfoServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeEpfoServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeEpfoServer) EXPECT() *MockUnsafeEpfoServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedEpfoServer mocks base method.
func (m *MockUnsafeEpfoServer) mustEmbedUnimplementedEpfoServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedEpfoServer")
}

// mustEmbedUnimplementedEpfoServer indicates an expected call of mustEmbedUnimplementedEpfoServer.
func (mr *MockUnsafeEpfoServerMockRecorder) mustEmbedUnimplementedEpfoServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedEpfoServer", reflect.TypeOf((*MockUnsafeEpfoServer)(nil).mustEmbedUnimplementedEpfoServer))
}
