// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/epfo/service.proto

package epfo

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetEpfoDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEpfoDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEpfoDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEpfoDataRequestMultiError, or nil if none found.
func (m *GetEpfoDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEpfoDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Vendor

	// no validation rules for Client

	if len(errors) > 0 {
		return GetEpfoDataRequestMultiError(errors)
	}

	return nil
}

// GetEpfoDataRequestMultiError is an error wrapping multiple validation errors
// returned by GetEpfoDataRequest.ValidateAll() if the designated constraints
// aren't met.
type GetEpfoDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEpfoDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEpfoDataRequestMultiError) AllErrors() []error { return m }

// GetEpfoDataRequestValidationError is the validation error returned by
// GetEpfoDataRequest.Validate if the designated constraints aren't met.
type GetEpfoDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEpfoDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEpfoDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEpfoDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEpfoDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEpfoDataRequestValidationError) ErrorName() string {
	return "GetEpfoDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEpfoDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEpfoDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEpfoDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEpfoDataRequestValidationError{}

// Validate checks the field values on GetEpfoDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEpfoDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEpfoDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEpfoDataResponseMultiError, or nil if none found.
func (m *GetEpfoDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEpfoDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEpfoDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEpfoDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEpfoDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Client

	// no validation rules for RawEpfoData

	if all {
		switch v := interface{}(m.GetFetchedTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEpfoDataResponseValidationError{
					field:  "FetchedTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEpfoDataResponseValidationError{
					field:  "FetchedTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFetchedTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEpfoDataResponseValidationError{
				field:  "FetchedTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	if len(errors) > 0 {
		return GetEpfoDataResponseMultiError(errors)
	}

	return nil
}

// GetEpfoDataResponseMultiError is an error wrapping multiple validation
// errors returned by GetEpfoDataResponse.ValidateAll() if the designated
// constraints aren't met.
type GetEpfoDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEpfoDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEpfoDataResponseMultiError) AllErrors() []error { return m }

// GetEpfoDataResponseValidationError is the validation error returned by
// GetEpfoDataResponse.Validate if the designated constraints aren't met.
type GetEpfoDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEpfoDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEpfoDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEpfoDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEpfoDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEpfoDataResponseValidationError) ErrorName() string {
	return "GetEpfoDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEpfoDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEpfoDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEpfoDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEpfoDataResponseValidationError{}
