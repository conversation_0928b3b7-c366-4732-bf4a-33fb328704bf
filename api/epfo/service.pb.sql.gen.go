// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/epfo/service.pb.go

package epfo

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the Client in string format in DB
func (p Client) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Client while reading from DB
func (p *Client) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Client_value[val]
	if !ok {
		return fmt.Errorf("unexpected Client value: %s", val)
	}
	*p = Client(valInt)
	return nil
}

// Marshaler interface implementation for Client
func (x Client) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Client
func (x *Client) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Client(Client_value[val])
	return nil
}

// Valuer interface implementation for storing the Vendor in string format in DB
func (p Vendor) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Vendor while reading from DB
func (p *Vendor) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Vendor_value[val]
	if !ok {
		return fmt.Errorf("unexpected Vendor value: %s", val)
	}
	*p = Vendor(valInt)
	return nil
}

// Marshaler interface implementation for Vendor
func (x Vendor) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Vendor
func (x *Vendor) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Vendor(Vendor_value[val])
	return nil
}
