//go:generate gen_sql -types=Client,Vendor

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/epfo/service.proto

package epfo

import (
	rpc "github.com/epifi/be-common/api/rpc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Client int32

const (
	Client_CLIENT_UNSPECIFIED Client = 0
	Client_CLIENT_LOANS       Client = 1
)

// Enum value maps for Client.
var (
	Client_name = map[int32]string{
		0: "CLIENT_UNSPECIFIED",
		1: "CLIENT_LOANS",
	}
	Client_value = map[string]int32{
		"CLIENT_UNSPECIFIED": 0,
		"CLIENT_LOANS":       1,
	}
)

func (x Client) Enum() *Client {
	p := new(Client)
	*p = x
	return p
}

func (x Client) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Client) Descriptor() protoreflect.EnumDescriptor {
	return file_api_epfo_service_proto_enumTypes[0].Descriptor()
}

func (Client) Type() protoreflect.EnumType {
	return &file_api_epfo_service_proto_enumTypes[0]
}

func (x Client) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Client.Descriptor instead.
func (Client) EnumDescriptor() ([]byte, []int) {
	return file_api_epfo_service_proto_rawDescGZIP(), []int{0}
}

type Vendor int32

const (
	Vendor_VENDOR_UNSPECIFIED Vendor = 0
	Vendor_VENDOR_KARZA       Vendor = 1
	Vendor_VENDOR_DIGITAP     Vendor = 2
)

// Enum value maps for Vendor.
var (
	Vendor_name = map[int32]string{
		0: "VENDOR_UNSPECIFIED",
		1: "VENDOR_KARZA",
		2: "VENDOR_DIGITAP",
	}
	Vendor_value = map[string]int32{
		"VENDOR_UNSPECIFIED": 0,
		"VENDOR_KARZA":       1,
		"VENDOR_DIGITAP":     2,
	}
)

func (x Vendor) Enum() *Vendor {
	p := new(Vendor)
	*p = x
	return p
}

func (x Vendor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Vendor) Descriptor() protoreflect.EnumDescriptor {
	return file_api_epfo_service_proto_enumTypes[1].Descriptor()
}

func (Vendor) Type() protoreflect.EnumType {
	return &file_api_epfo_service_proto_enumTypes[1]
}

func (x Vendor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Vendor.Descriptor instead.
func (Vendor) EnumDescriptor() ([]byte, []int) {
	return file_api_epfo_service_proto_rawDescGZIP(), []int{1}
}

type GetEpfoDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor ID for whom to fetch employment verification data
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// EPFO data can be fetched from multiple vendors. The report will only be fetched from that vendor if this field is populated.
	// If UNSPECIFIED, this will return the latest report fetched for the actor irrespective of the vendor.
	Vendor Vendor `protobuf:"varint,2,opt,name=vendor,proto3,enum=epfo.Vendor" json:"vendor,omitempty"`
	// requesting client who wants to access this data
	Client Client `protobuf:"varint,3,opt,name=client,proto3,enum=epfo.Client" json:"client,omitempty"`
}

func (x *GetEpfoDataRequest) Reset() {
	*x = GetEpfoDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_epfo_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEpfoDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEpfoDataRequest) ProtoMessage() {}

func (x *GetEpfoDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_epfo_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEpfoDataRequest.ProtoReflect.Descriptor instead.
func (*GetEpfoDataRequest) Descriptor() ([]byte, []int) {
	return file_api_epfo_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetEpfoDataRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetEpfoDataRequest) GetVendor() Vendor {
	if x != nil {
		return x.Vendor
	}
	return Vendor_VENDOR_UNSPECIFIED
}

func (x *GetEpfoDataRequest) GetClient() Client {
	if x != nil {
		return x.Client
	}
	return Client_CLIENT_UNSPECIFIED
}

type GetEpfoDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// client for whom the data was fetched
	Client      Client `protobuf:"varint,2,opt,name=client,proto3,enum=epfo.Client" json:"client,omitempty"`
	RawEpfoData string `protobuf:"bytes,3,opt,name=raw_epfo_data,json=rawEpfoData,proto3" json:"raw_epfo_data,omitempty"`
	// timestamp for when the data was fetched from vendor
	FetchedTimestamp *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=fetched_timestamp,json=fetchedTimestamp,proto3" json:"fetched_timestamp,omitempty"`
	Vendor           Vendor                 `protobuf:"varint,5,opt,name=vendor,proto3,enum=epfo.Vendor" json:"vendor,omitempty"`
}

func (x *GetEpfoDataResponse) Reset() {
	*x = GetEpfoDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_epfo_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEpfoDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEpfoDataResponse) ProtoMessage() {}

func (x *GetEpfoDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_epfo_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEpfoDataResponse.ProtoReflect.Descriptor instead.
func (*GetEpfoDataResponse) Descriptor() ([]byte, []int) {
	return file_api_epfo_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetEpfoDataResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetEpfoDataResponse) GetClient() Client {
	if x != nil {
		return x.Client
	}
	return Client_CLIENT_UNSPECIFIED
}

func (x *GetEpfoDataResponse) GetRawEpfoData() string {
	if x != nil {
		return x.RawEpfoData
	}
	return ""
}

func (x *GetEpfoDataResponse) GetFetchedTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.FetchedTimestamp
	}
	return nil
}

func (x *GetEpfoDataResponse) GetVendor() Vendor {
	if x != nil {
		return x.Vendor
	}
	return Vendor_VENDOR_UNSPECIFIED
}

var File_api_epfo_service_proto protoreflect.FileDescriptor

var file_api_epfo_service_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x70, 0x66, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x65, 0x70, 0x66, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x45, 0x70, 0x66, 0x6f,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x65, 0x70, 0x66, 0x6f, 0x2e, 0x56, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x24, 0x0a, 0x06,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x65,
	0x70, 0x66, 0x6f, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x22, 0xf3, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x45, 0x70, 0x66, 0x6f, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x24, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0c, 0x2e, 0x65, 0x70, 0x66, 0x6f, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x72, 0x61, 0x77, 0x5f, 0x65, 0x70, 0x66,
	0x6f, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x61,
	0x77, 0x45, 0x70, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x12, 0x47, 0x0a, 0x11, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x10, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x24, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x65, 0x70, 0x66, 0x6f, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2a, 0x32, 0x0a, 0x06, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x4c,
	0x49, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x10, 0x01, 0x2a, 0x46, 0x0a, 0x06,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x12, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10,
	0x0a, 0x0c, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4b, 0x41, 0x52, 0x5a, 0x41, 0x10, 0x01,
	0x12, 0x12, 0x0a, 0x0e, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x44, 0x49, 0x47, 0x49, 0x54,
	0x41, 0x50, 0x10, 0x02, 0x32, 0x4c, 0x0a, 0x04, 0x45, 0x70, 0x66, 0x6f, 0x12, 0x44, 0x0a, 0x0b,
	0x47, 0x65, 0x74, 0x45, 0x70, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x2e, 0x65, 0x70,
	0x66, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x70, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x65, 0x70, 0x66, 0x6f, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x70, 0x66, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x70, 0x66, 0x6f, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x65, 0x70, 0x66, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_epfo_service_proto_rawDescOnce sync.Once
	file_api_epfo_service_proto_rawDescData = file_api_epfo_service_proto_rawDesc
)

func file_api_epfo_service_proto_rawDescGZIP() []byte {
	file_api_epfo_service_proto_rawDescOnce.Do(func() {
		file_api_epfo_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_epfo_service_proto_rawDescData)
	})
	return file_api_epfo_service_proto_rawDescData
}

var file_api_epfo_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_epfo_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_epfo_service_proto_goTypes = []interface{}{
	(Client)(0),                   // 0: epfo.Client
	(Vendor)(0),                   // 1: epfo.Vendor
	(*GetEpfoDataRequest)(nil),    // 2: epfo.GetEpfoDataRequest
	(*GetEpfoDataResponse)(nil),   // 3: epfo.GetEpfoDataResponse
	(*rpc.Status)(nil),            // 4: rpc.Status
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
}
var file_api_epfo_service_proto_depIdxs = []int32{
	1, // 0: epfo.GetEpfoDataRequest.vendor:type_name -> epfo.Vendor
	0, // 1: epfo.GetEpfoDataRequest.client:type_name -> epfo.Client
	4, // 2: epfo.GetEpfoDataResponse.status:type_name -> rpc.Status
	0, // 3: epfo.GetEpfoDataResponse.client:type_name -> epfo.Client
	5, // 4: epfo.GetEpfoDataResponse.fetched_timestamp:type_name -> google.protobuf.Timestamp
	1, // 5: epfo.GetEpfoDataResponse.vendor:type_name -> epfo.Vendor
	2, // 6: epfo.Epfo.GetEpfoData:input_type -> epfo.GetEpfoDataRequest
	3, // 7: epfo.Epfo.GetEpfoData:output_type -> epfo.GetEpfoDataResponse
	7, // [7:8] is the sub-list for method output_type
	6, // [6:7] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_epfo_service_proto_init() }
func file_api_epfo_service_proto_init() {
	if File_api_epfo_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_epfo_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEpfoDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_epfo_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEpfoDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_epfo_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_epfo_service_proto_goTypes,
		DependencyIndexes: file_api_epfo_service_proto_depIdxs,
		EnumInfos:         file_api_epfo_service_proto_enumTypes,
		MessageInfos:      file_api_epfo_service_proto_msgTypes,
	}.Build()
	File_api_epfo_service_proto = out.File
	file_api_epfo_service_proto_rawDesc = nil
	file_api_epfo_service_proto_goTypes = nil
	file_api_epfo_service_proto_depIdxs = nil
}
