// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/pay/payment_protocol.proto

package pay

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PaymentProtocol defines list of protocols at a bank through which a transaction can be sent
//
// Deprecated: Marked as deprecated in api/frontend/pay/payment_protocol.proto.
type PaymentProtocol int32

const (
	PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED PaymentProtocol = 0
	// Intra bank Fund Transfer denotes a transfer to a beneficiary having account in the same bank
	// This protocol provides a real time fund transfer solution to transfer funds between two accounts maintained with the
	// same partner bank
	//
	// Restrictions like minimum and maximum amount can be applicable and are applicable by the partner bank on a client
	PaymentProtocol_INTRA_BANK PaymentProtocol = 1
	// National Electronic Funds Transfer (NEFT) is an electronic funds transfer system maintained by the RBI.
	// It enables Bank customers to transfer funds from any bank branch to any individual having an account with another
	// participating bank branch.
	//
	// The fund transfer is not real time but takes place in batches which are settled on half-hourly time slots.
	// This means that if the transaction is done after the settlement, it is required to wait until the next settlement time.
	// There will be 48 half-hourly batches every day. The settlement of first batch will commence after 00:30 hours and the
	// last batch will end at 00:00 hours.
	// The system will be available on all days of the year, including holidays.
	//
	// NEFT does not impose any minimum or maximum limits on the amount to be transferred although partner bank can.
	PaymentProtocol_NEFT PaymentProtocol = 2
	// The Immediate Payment Service (IMPS) provides a real time fund transfer solution,
	// offering an instantaneous, 24x7, interbank electronic fund transfer service that is provided by the NPCI.
	//
	// In order to use IMPS for fund transfer, both Remitter as well as Beneficiary need to register their mobile number with the
	// respective Bank account and get MMID (Mobile Money Identifier). In order to cater to a scenario where the Beneficiary’s
	// mobile number is not registered with any Bank Account, IMPS funds transfer has been made possible using Beneficiary account
	// number and IFSC code.
	//
	// Maximum limit that can be transferred is INR 2 lakhs, although this can vary from Bank to Bank.
	PaymentProtocol_IMPS PaymentProtocol = 3
	// Real Time Gross Settlement (RTGS) is a fund transfer system maintained by the RBI, which enables fund transfer
	// from one Bank to another, in real-time and gross basis. Real time settlement means transactions are settled as soon as
	// they are processed. The transactions are settled on a one-to-one basis, which means the system will not club or bundle
	// a transaction with another. This is one of the fastest possible ways to transfer money, between Banks in India.
	// RTGS is a better option for high value transactions, that need immediate clearing.
	//
	// The RTGS service window for customer's transactions is available to banks from 9.00 hours to 16.30 hours on week days and
	// from 9.00 hours to 14:00 hours on Saturdays for settlement at the RBI end. However, the timings that the banks follow may
	// vary depending on the customer timings of the bank branches.
	//
	// If the RTGS request is received after the cut-off time, system will try the NEFT protocol.
	// Note that the request cannot be cancelled.
	//
	// Minimum amount to be remitted is INR 2 lakhs and maximum limit varies from Bank to Bank.
	PaymentProtocol_RTGS PaymentProtocol = 4
	// Unified Payments Interface (UPI) is an instant real-time 24x7 payment system developed by National Payments Corporation of India
	// facilitating inter-bank transactions. The interface is regulated by the Reserve Bank of India and works by instantly
	// transferring funds between two bank accounts on a mobile platform.
	//
	// Money can be sent or requested with the following methods:
	//
	// - Virtual Payment Address (VPA) or UPI ID: Send or request money from/to bank account mapped using VPA.
	// - Mobile number: Send or request money from/to the bank account mapped using mobile number.
	// - Account number & IFSC: Send money to the bank account.
	// - Aadhaar: Send money to the bank account mapped using Aadhaar number.
	// - QR code: Send money by QR code which has enclosed VPA, Account number and IFSC or Mobile number.
	//
	// The transaction limit per UPI transaction and overall transaction limit per day is INR 1 lakh
	// UPI transaction frequency limit has been set to 10 per day for Peer to Peer transfers (P2P) and no limit for P2M
	// These limits vary from Bank to Bank.
	PaymentProtocol_UPI PaymentProtocol = 5
	// all the card related transactions - POS/ATM/ E-Comm are denoted by cards
	// card can be of any type - debit/credit
	PaymentProtocol_CARD PaymentProtocol = 6
	// RBI is considering replacement of the existing system of settlement of payment on the basis of physical cheques by a new procedure called “ Cheque Truncation System” (CTS).
	// It is an online image-based cheque clearing system where cheque images and Magnetic Ink Character Recognition (MICR) data are captured at the collecting  bank branch and transmitted electronically eliminating the actual cheque movement.
	PaymentProtocol_CTS PaymentProtocol = 7
)

// Enum value maps for PaymentProtocol.
var (
	PaymentProtocol_name = map[int32]string{
		0: "PAYMENT_PROTOCOL_UNSPECIFIED",
		1: "INTRA_BANK",
		2: "NEFT",
		3: "IMPS",
		4: "RTGS",
		5: "UPI",
		6: "CARD",
		7: "CTS",
	}
	PaymentProtocol_value = map[string]int32{
		"PAYMENT_PROTOCOL_UNSPECIFIED": 0,
		"INTRA_BANK":                   1,
		"NEFT":                         2,
		"IMPS":                         3,
		"RTGS":                         4,
		"UPI":                          5,
		"CARD":                         6,
		"CTS":                          7,
	}
)

func (x PaymentProtocol) Enum() *PaymentProtocol {
	p := new(PaymentProtocol)
	*p = x
	return p
}

func (x PaymentProtocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentProtocol) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_pay_payment_protocol_proto_enumTypes[0].Descriptor()
}

func (PaymentProtocol) Type() protoreflect.EnumType {
	return &file_api_frontend_pay_payment_protocol_proto_enumTypes[0]
}

func (x PaymentProtocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentProtocol.Descriptor instead.
func (PaymentProtocol) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_pay_payment_protocol_proto_rawDescGZIP(), []int{0}
}

var File_api_frontend_pay_payment_protocol_proto protoreflect.FileDescriptor

var file_api_frontend_pay_payment_protocol_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70,
	0x61, 0x79, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x70, 0x61, 0x79, 0x2a, 0x81, 0x01, 0x0a, 0x0f, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x20, 0x0a, 0x1c, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a,
	0x0a, 0x49, 0x4e, 0x54, 0x52, 0x41, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x01, 0x12, 0x08, 0x0a,
	0x04, 0x4e, 0x45, 0x46, 0x54, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x4d, 0x50, 0x53, 0x10,
	0x03, 0x12, 0x08, 0x0a, 0x04, 0x52, 0x54, 0x47, 0x53, 0x10, 0x04, 0x12, 0x07, 0x0a, 0x03, 0x55,
	0x50, 0x49, 0x10, 0x05, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x41, 0x52, 0x44, 0x10, 0x06, 0x12, 0x07,
	0x0a, 0x03, 0x43, 0x54, 0x53, 0x10, 0x07, 0x1a, 0x02, 0x18, 0x01, 0x42, 0x69, 0x0a, 0x27, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x70, 0x61, 0x79, 0x42, 0x15, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x5a, 0x27, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2f, 0x70, 0x61, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_pay_payment_protocol_proto_rawDescOnce sync.Once
	file_api_frontend_pay_payment_protocol_proto_rawDescData = file_api_frontend_pay_payment_protocol_proto_rawDesc
)

func file_api_frontend_pay_payment_protocol_proto_rawDescGZIP() []byte {
	file_api_frontend_pay_payment_protocol_proto_rawDescOnce.Do(func() {
		file_api_frontend_pay_payment_protocol_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_pay_payment_protocol_proto_rawDescData)
	})
	return file_api_frontend_pay_payment_protocol_proto_rawDescData
}

var file_api_frontend_pay_payment_protocol_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_frontend_pay_payment_protocol_proto_goTypes = []interface{}{
	(PaymentProtocol)(0), // 0: frontend.pay.PaymentProtocol
}
var file_api_frontend_pay_payment_protocol_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_frontend_pay_payment_protocol_proto_init() }
func file_api_frontend_pay_payment_protocol_proto_init() {
	if File_api_frontend_pay_payment_protocol_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_pay_payment_protocol_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_pay_payment_protocol_proto_goTypes,
		DependencyIndexes: file_api_frontend_pay_payment_protocol_proto_depIdxs,
		EnumInfos:         file_api_frontend_pay_payment_protocol_proto_enumTypes,
	}.Build()
	File_api_frontend_pay_payment_protocol_proto = out.File
	file_api_frontend_pay_payment_protocol_proto_rawDesc = nil
	file_api_frontend_pay_payment_protocol_proto_goTypes = nil
	file_api_frontend_pay_payment_protocol_proto_depIdxs = nil
}
