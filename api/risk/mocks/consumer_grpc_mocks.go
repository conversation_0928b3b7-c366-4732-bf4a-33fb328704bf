// Code generated by MockGen. DO NOT EDIT.
// Source: api/risk/consumer_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	risk "github.com/epifi/gamma/api/risk"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockRiskConsumerClient is a mock of RiskConsumerClient interface.
type MockRiskConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockRiskConsumerClientMockRecorder
}

// MockRiskConsumerClientMockRecorder is the mock recorder for MockRiskConsumerClient.
type MockRiskConsumerClientMockRecorder struct {
	mock *MockRiskConsumerClient
}

// NewMockRiskConsumerClient creates a new mock instance.
func NewMockRiskConsumerClient(ctrl *gomock.Controller) *MockRiskConsumerClient {
	mock := &MockRiskConsumerClient{ctrl: ctrl}
	mock.recorder = &MockRiskConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskConsumerClient) EXPECT() *MockRiskConsumerClientMockRecorder {
	return m.recorder
}

// ProcessDisputeUpload mocks base method.
func (m *MockRiskConsumerClient) ProcessDisputeUpload(ctx context.Context, in *risk.DisputeUploadEvent, opts ...grpc.CallOption) (*risk.ProcessDisputeUploadResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessDisputeUpload", varargs...)
	ret0, _ := ret[0].(*risk.ProcessDisputeUploadResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessDisputeUpload indicates an expected call of ProcessDisputeUpload.
func (mr *MockRiskConsumerClientMockRecorder) ProcessDisputeUpload(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessDisputeUpload", reflect.TypeOf((*MockRiskConsumerClient)(nil).ProcessDisputeUpload), varargs...)
}

// ProcessRedListUpdate mocks base method.
func (m *MockRiskConsumerClient) ProcessRedListUpdate(ctx context.Context, in *risk.ProcessRedListUpdateRequest, opts ...grpc.CallOption) (*risk.ProcessRedListUpdateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessRedListUpdate", varargs...)
	ret0, _ := ret[0].(*risk.ProcessRedListUpdateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessRedListUpdate indicates an expected call of ProcessRedListUpdate.
func (mr *MockRiskConsumerClientMockRecorder) ProcessRedListUpdate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessRedListUpdate", reflect.TypeOf((*MockRiskConsumerClient)(nil).ProcessRedListUpdate), varargs...)
}

// MockRiskConsumerServer is a mock of RiskConsumerServer interface.
type MockRiskConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockRiskConsumerServerMockRecorder
}

// MockRiskConsumerServerMockRecorder is the mock recorder for MockRiskConsumerServer.
type MockRiskConsumerServerMockRecorder struct {
	mock *MockRiskConsumerServer
}

// NewMockRiskConsumerServer creates a new mock instance.
func NewMockRiskConsumerServer(ctrl *gomock.Controller) *MockRiskConsumerServer {
	mock := &MockRiskConsumerServer{ctrl: ctrl}
	mock.recorder = &MockRiskConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskConsumerServer) EXPECT() *MockRiskConsumerServerMockRecorder {
	return m.recorder
}

// ProcessDisputeUpload mocks base method.
func (m *MockRiskConsumerServer) ProcessDisputeUpload(arg0 context.Context, arg1 *risk.DisputeUploadEvent) (*risk.ProcessDisputeUploadResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessDisputeUpload", arg0, arg1)
	ret0, _ := ret[0].(*risk.ProcessDisputeUploadResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessDisputeUpload indicates an expected call of ProcessDisputeUpload.
func (mr *MockRiskConsumerServerMockRecorder) ProcessDisputeUpload(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessDisputeUpload", reflect.TypeOf((*MockRiskConsumerServer)(nil).ProcessDisputeUpload), arg0, arg1)
}

// ProcessRedListUpdate mocks base method.
func (m *MockRiskConsumerServer) ProcessRedListUpdate(arg0 context.Context, arg1 *risk.ProcessRedListUpdateRequest) (*risk.ProcessRedListUpdateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessRedListUpdate", arg0, arg1)
	ret0, _ := ret[0].(*risk.ProcessRedListUpdateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessRedListUpdate indicates an expected call of ProcessRedListUpdate.
func (mr *MockRiskConsumerServerMockRecorder) ProcessRedListUpdate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessRedListUpdate", reflect.TypeOf((*MockRiskConsumerServer)(nil).ProcessRedListUpdate), arg0, arg1)
}

// MockUnsafeRiskConsumerServer is a mock of UnsafeRiskConsumerServer interface.
type MockUnsafeRiskConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeRiskConsumerServerMockRecorder
}

// MockUnsafeRiskConsumerServerMockRecorder is the mock recorder for MockUnsafeRiskConsumerServer.
type MockUnsafeRiskConsumerServerMockRecorder struct {
	mock *MockUnsafeRiskConsumerServer
}

// NewMockUnsafeRiskConsumerServer creates a new mock instance.
func NewMockUnsafeRiskConsumerServer(ctrl *gomock.Controller) *MockUnsafeRiskConsumerServer {
	mock := &MockUnsafeRiskConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeRiskConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeRiskConsumerServer) EXPECT() *MockUnsafeRiskConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedRiskConsumerServer mocks base method.
func (m *MockUnsafeRiskConsumerServer) mustEmbedUnimplementedRiskConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedRiskConsumerServer")
}

// mustEmbedUnimplementedRiskConsumerServer indicates an expected call of mustEmbedUnimplementedRiskConsumerServer.
func (mr *MockUnsafeRiskConsumerServerMockRecorder) mustEmbedUnimplementedRiskConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedRiskConsumerServer", reflect.TypeOf((*MockUnsafeRiskConsumerServer)(nil).mustEmbedUnimplementedRiskConsumerServer))
}
