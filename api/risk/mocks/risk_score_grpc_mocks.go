// Code generated by MockGen. DO NOT EDIT.
// Source: api/risk/risk_score_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	risk "github.com/epifi/gamma/api/risk"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockTxnRiskScoreServiceClient is a mock of TxnRiskScoreServiceClient interface.
type MockTxnRiskScoreServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockTxnRiskScoreServiceClientMockRecorder
}

// MockTxnRiskScoreServiceClientMockRecorder is the mock recorder for MockTxnRiskScoreServiceClient.
type MockTxnRiskScoreServiceClientMockRecorder struct {
	mock *MockTxnRiskScoreServiceClient
}

// NewMockTxnRiskScoreServiceClient creates a new mock instance.
func NewMockTxnRiskScoreServiceClient(ctrl *gomock.Controller) *MockTxnRiskScoreServiceClient {
	mock := &MockTxnRiskScoreServiceClient{ctrl: ctrl}
	mock.recorder = &MockTxnRiskScoreServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTxnRiskScoreServiceClient) EXPECT() *MockTxnRiskScoreServiceClientMockRecorder {
	return m.recorder
}

// CreateRiskScore mocks base method.
func (m *MockTxnRiskScoreServiceClient) CreateRiskScore(ctx context.Context, in *risk.CreateRiskScoreRequest, opts ...grpc.CallOption) (*risk.CreateRiskScoreResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRiskScore", varargs...)
	ret0, _ := ret[0].(*risk.CreateRiskScoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRiskScore indicates an expected call of CreateRiskScore.
func (mr *MockTxnRiskScoreServiceClientMockRecorder) CreateRiskScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRiskScore", reflect.TypeOf((*MockTxnRiskScoreServiceClient)(nil).CreateRiskScore), varargs...)
}

// MockTxnRiskScoreServiceServer is a mock of TxnRiskScoreServiceServer interface.
type MockTxnRiskScoreServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockTxnRiskScoreServiceServerMockRecorder
}

// MockTxnRiskScoreServiceServerMockRecorder is the mock recorder for MockTxnRiskScoreServiceServer.
type MockTxnRiskScoreServiceServerMockRecorder struct {
	mock *MockTxnRiskScoreServiceServer
}

// NewMockTxnRiskScoreServiceServer creates a new mock instance.
func NewMockTxnRiskScoreServiceServer(ctrl *gomock.Controller) *MockTxnRiskScoreServiceServer {
	mock := &MockTxnRiskScoreServiceServer{ctrl: ctrl}
	mock.recorder = &MockTxnRiskScoreServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTxnRiskScoreServiceServer) EXPECT() *MockTxnRiskScoreServiceServerMockRecorder {
	return m.recorder
}

// CreateRiskScore mocks base method.
func (m *MockTxnRiskScoreServiceServer) CreateRiskScore(arg0 context.Context, arg1 *risk.CreateRiskScoreRequest) (*risk.CreateRiskScoreResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRiskScore", arg0, arg1)
	ret0, _ := ret[0].(*risk.CreateRiskScoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRiskScore indicates an expected call of CreateRiskScore.
func (mr *MockTxnRiskScoreServiceServerMockRecorder) CreateRiskScore(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRiskScore", reflect.TypeOf((*MockTxnRiskScoreServiceServer)(nil).CreateRiskScore), arg0, arg1)
}

// MockUnsafeTxnRiskScoreServiceServer is a mock of UnsafeTxnRiskScoreServiceServer interface.
type MockUnsafeTxnRiskScoreServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTxnRiskScoreServiceServerMockRecorder
}

// MockUnsafeTxnRiskScoreServiceServerMockRecorder is the mock recorder for MockUnsafeTxnRiskScoreServiceServer.
type MockUnsafeTxnRiskScoreServiceServerMockRecorder struct {
	mock *MockUnsafeTxnRiskScoreServiceServer
}

// NewMockUnsafeTxnRiskScoreServiceServer creates a new mock instance.
func NewMockUnsafeTxnRiskScoreServiceServer(ctrl *gomock.Controller) *MockUnsafeTxnRiskScoreServiceServer {
	mock := &MockUnsafeTxnRiskScoreServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTxnRiskScoreServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTxnRiskScoreServiceServer) EXPECT() *MockUnsafeTxnRiskScoreServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTxnRiskScoreServiceServer mocks base method.
func (m *MockUnsafeTxnRiskScoreServiceServer) mustEmbedUnimplementedTxnRiskScoreServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTxnRiskScoreServiceServer")
}

// mustEmbedUnimplementedTxnRiskScoreServiceServer indicates an expected call of mustEmbedUnimplementedTxnRiskScoreServiceServer.
func (mr *MockUnsafeTxnRiskScoreServiceServerMockRecorder) mustEmbedUnimplementedTxnRiskScoreServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTxnRiskScoreServiceServer", reflect.TypeOf((*MockUnsafeTxnRiskScoreServiceServer)(nil).mustEmbedUnimplementedTxnRiskScoreServiceServer))
}
