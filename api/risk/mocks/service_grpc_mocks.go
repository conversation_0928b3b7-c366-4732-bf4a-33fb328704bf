// Code generated by MockGen. DO NOT EDIT.
// Source: api/risk/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	dynamic_elements "github.com/epifi/gamma/api/dynamic_elements"
	risk "github.com/epifi/gamma/api/risk"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockRiskClient is a mock of RiskClient interface.
type MockRiskClient struct {
	ctrl     *gomock.Controller
	recorder *MockRiskClientMockRecorder
}

// MockRiskClientMockRecorder is the mock recorder for MockRiskClient.
type MockRiskClientMockRecorder struct {
	mock *MockRiskClient
}

// NewMockRiskClient creates a new mock instance.
func NewMockRiskClient(ctrl *gomock.Controller) *MockRiskClient {
	mock := &MockRiskClient{ctrl: ctrl}
	mock.recorder = &MockRiskClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskClient) EXPECT() *MockRiskClientMockRecorder {
	return m.recorder
}

// AddLEAComplaintSource mocks base method.
func (m *MockRiskClient) AddLEAComplaintSource(ctx context.Context, in *risk.AddLEAComplaintSourceRequest, opts ...grpc.CallOption) (*risk.AddLEAComplaintSourceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddLEAComplaintSource", varargs...)
	ret0, _ := ret[0].(*risk.AddLEAComplaintSourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddLEAComplaintSource indicates an expected call of AddLEAComplaintSource.
func (mr *MockRiskClientMockRecorder) AddLEAComplaintSource(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLEAComplaintSource", reflect.TypeOf((*MockRiskClient)(nil).AddLEAComplaintSource), varargs...)
}

// BulkAddSavingsAccountBankAction mocks base method.
func (m *MockRiskClient) BulkAddSavingsAccountBankAction(ctx context.Context, in *risk.BulkAddSavingsAccountBankActionRequest, opts ...grpc.CallOption) (*risk.BulkAddSavingsAccountBankActionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BulkAddSavingsAccountBankAction", varargs...)
	ret0, _ := ret[0].(*risk.BulkAddSavingsAccountBankActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkAddSavingsAccountBankAction indicates an expected call of BulkAddSavingsAccountBankAction.
func (mr *MockRiskClientMockRecorder) BulkAddSavingsAccountBankAction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkAddSavingsAccountBankAction", reflect.TypeOf((*MockRiskClient)(nil).BulkAddSavingsAccountBankAction), varargs...)
}

// BulkInsertBureauIdRiskDetails mocks base method.
func (m *MockRiskClient) BulkInsertBureauIdRiskDetails(ctx context.Context, in *risk.BulkInsertBureauIdRiskDetailsRequest, opts ...grpc.CallOption) (*risk.BulkInsertBureauIdRiskDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BulkInsertBureauIdRiskDetails", varargs...)
	ret0, _ := ret[0].(*risk.BulkInsertBureauIdRiskDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkInsertBureauIdRiskDetails indicates an expected call of BulkInsertBureauIdRiskDetails.
func (mr *MockRiskClientMockRecorder) BulkInsertBureauIdRiskDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkInsertBureauIdRiskDetails", reflect.TypeOf((*MockRiskClient)(nil).BulkInsertBureauIdRiskDetails), varargs...)
}

// BulkOverrideBankActionState mocks base method.
func (m *MockRiskClient) BulkOverrideBankActionState(ctx context.Context, in *risk.BulkOverrideBankActionStateRequest, opts ...grpc.CallOption) (*risk.BulkOverrideBankActionStateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BulkOverrideBankActionState", varargs...)
	ret0, _ := ret[0].(*risk.BulkOverrideBankActionStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkOverrideBankActionState indicates an expected call of BulkOverrideBankActionState.
func (mr *MockRiskClientMockRecorder) BulkOverrideBankActionState(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkOverrideBankActionState", reflect.TypeOf((*MockRiskClient)(nil).BulkOverrideBankActionState), varargs...)
}

// CreateLEAComplaint mocks base method.
func (m *MockRiskClient) CreateLEAComplaint(ctx context.Context, in *risk.CreateLEAComplaintRequest, opts ...grpc.CallOption) (*risk.CreateLEAComplaintResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateLEAComplaint", varargs...)
	ret0, _ := ret[0].(*risk.CreateLEAComplaintResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLEAComplaint indicates an expected call of CreateLEAComplaint.
func (mr *MockRiskClientMockRecorder) CreateLEAComplaint(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLEAComplaint", reflect.TypeOf((*MockRiskClient)(nil).CreateLEAComplaint), varargs...)
}

// DynamicElementCallback mocks base method.
func (m *MockRiskClient) DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DynamicElementCallback", varargs...)
	ret0, _ := ret[0].(*dynamic_elements.DynamicElementCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DynamicElementCallback indicates an expected call of DynamicElementCallback.
func (mr *MockRiskClientMockRecorder) DynamicElementCallback(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DynamicElementCallback", reflect.TypeOf((*MockRiskClient)(nil).DynamicElementCallback), varargs...)
}

// FetchDynamicElements mocks base method.
func (m *MockRiskClient) FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchDynamicElements", varargs...)
	ret0, _ := ret[0].(*dynamic_elements.FetchDynamicElementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchDynamicElements indicates an expected call of FetchDynamicElements.
func (mr *MockRiskClientMockRecorder) FetchDynamicElements(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchDynamicElements", reflect.TypeOf((*MockRiskClient)(nil).FetchDynamicElements), varargs...)
}

// FetchLEAComplaints mocks base method.
func (m *MockRiskClient) FetchLEAComplaints(ctx context.Context, in *risk.FetchLEAComplaintsRequest, opts ...grpc.CallOption) (*risk.FetchLEAComplaintsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchLEAComplaints", varargs...)
	ret0, _ := ret[0].(*risk.FetchLEAComplaintsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchLEAComplaints indicates an expected call of FetchLEAComplaints.
func (mr *MockRiskClientMockRecorder) FetchLEAComplaints(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchLEAComplaints", reflect.TypeOf((*MockRiskClient)(nil).FetchLEAComplaints), varargs...)
}

// GetBureauIdRiskDetails mocks base method.
func (m *MockRiskClient) GetBureauIdRiskDetails(ctx context.Context, in *risk.GetBureauIdRiskDetailsRequest, opts ...grpc.CallOption) (*risk.GetBureauIdRiskDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBureauIdRiskDetails", varargs...)
	ret0, _ := ret[0].(*risk.GetBureauIdRiskDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBureauIdRiskDetails indicates an expected call of GetBureauIdRiskDetails.
func (mr *MockRiskClientMockRecorder) GetBureauIdRiskDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBureauIdRiskDetails", reflect.TypeOf((*MockRiskClient)(nil).GetBureauIdRiskDetails), varargs...)
}

// GetRiskData mocks base method.
func (m *MockRiskClient) GetRiskData(ctx context.Context, in *risk.GetRiskDataRequest, opts ...grpc.CallOption) (*risk.GetRiskDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRiskData", varargs...)
	ret0, _ := ret[0].(*risk.GetRiskDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRiskData indicates an expected call of GetRiskData.
func (mr *MockRiskClientMockRecorder) GetRiskData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRiskData", reflect.TypeOf((*MockRiskClient)(nil).GetRiskData), varargs...)
}

// GetScreenerAttemptStatus mocks base method.
func (m *MockRiskClient) GetScreenerAttemptStatus(ctx context.Context, in *risk.GetScreenerAttemptStatusRequest, opts ...grpc.CallOption) (*risk.GetScreenerAttemptStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScreenerAttemptStatus", varargs...)
	ret0, _ := ret[0].(*risk.GetScreenerAttemptStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScreenerAttemptStatus indicates an expected call of GetScreenerAttemptStatus.
func (mr *MockRiskClientMockRecorder) GetScreenerAttemptStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScreenerAttemptStatus", reflect.TypeOf((*MockRiskClient)(nil).GetScreenerAttemptStatus), varargs...)
}

// GetScreenerCheckDetails mocks base method.
func (m *MockRiskClient) GetScreenerCheckDetails(ctx context.Context, in *risk.GetScreenerCheckDetailsRequest, opts ...grpc.CallOption) (*risk.GetScreenerCheckDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScreenerCheckDetails", varargs...)
	ret0, _ := ret[0].(*risk.GetScreenerCheckDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScreenerCheckDetails indicates an expected call of GetScreenerCheckDetails.
func (mr *MockRiskClientMockRecorder) GetScreenerCheckDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScreenerCheckDetails", reflect.TypeOf((*MockRiskClient)(nil).GetScreenerCheckDetails), varargs...)
}

// GetScreenerCheckResults mocks base method.
func (m *MockRiskClient) GetScreenerCheckResults(ctx context.Context, in *risk.GetScreenerCheckResultsRequest, opts ...grpc.CallOption) (*risk.GetScreenerCheckResultsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScreenerCheckResults", varargs...)
	ret0, _ := ret[0].(*risk.GetScreenerCheckResultsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScreenerCheckResults indicates an expected call of GetScreenerCheckResults.
func (mr *MockRiskClientMockRecorder) GetScreenerCheckResults(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScreenerCheckResults", reflect.TypeOf((*MockRiskClient)(nil).GetScreenerCheckResults), varargs...)
}

// GetTransactionTags mocks base method.
func (m *MockRiskClient) GetTransactionTags(ctx context.Context, in *risk.GetTransactionTagsRequest, opts ...grpc.CallOption) (*risk.GetTransactionTagsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionTags", varargs...)
	ret0, _ := ret[0].(*risk.GetTransactionTagsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionTags indicates an expected call of GetTransactionTags.
func (mr *MockRiskClientMockRecorder) GetTransactionTags(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionTags", reflect.TypeOf((*MockRiskClient)(nil).GetTransactionTags), varargs...)
}

// PassRiskScreenerAttempt mocks base method.
func (m *MockRiskClient) PassRiskScreenerAttempt(ctx context.Context, in *risk.PassRiskScreenerAttemptRequest, opts ...grpc.CallOption) (*risk.PassRiskScreenerAttemptResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PassRiskScreenerAttempt", varargs...)
	ret0, _ := ret[0].(*risk.PassRiskScreenerAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PassRiskScreenerAttempt indicates an expected call of PassRiskScreenerAttempt.
func (mr *MockRiskClientMockRecorder) PassRiskScreenerAttempt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PassRiskScreenerAttempt", reflect.TypeOf((*MockRiskClient)(nil).PassRiskScreenerAttempt), varargs...)
}

// ProcessLEAComplaintNarration mocks base method.
func (m *MockRiskClient) ProcessLEAComplaintNarration(ctx context.Context, in *risk.ProcessLEAComplaintNarrationRequest, opts ...grpc.CallOption) (*risk.ProcessLEAComplaintNarrationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessLEAComplaintNarration", varargs...)
	ret0, _ := ret[0].(*risk.ProcessLEAComplaintNarrationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessLEAComplaintNarration indicates an expected call of ProcessLEAComplaintNarration.
func (mr *MockRiskClientMockRecorder) ProcessLEAComplaintNarration(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessLEAComplaintNarration", reflect.TypeOf((*MockRiskClient)(nil).ProcessLEAComplaintNarration), varargs...)
}

// ScreenActor mocks base method.
func (m *MockRiskClient) ScreenActor(ctx context.Context, in *risk.ScreenActorRequest, opts ...grpc.CallOption) (*risk.ScreenActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ScreenActor", varargs...)
	ret0, _ := ret[0].(*risk.ScreenActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScreenActor indicates an expected call of ScreenActor.
func (mr *MockRiskClientMockRecorder) ScreenActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScreenActor", reflect.TypeOf((*MockRiskClient)(nil).ScreenActor), varargs...)
}

// ScreenUser mocks base method.
func (m *MockRiskClient) ScreenUser(ctx context.Context, in *risk.ScreenUserRequest, opts ...grpc.CallOption) (*risk.ScreenUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ScreenUser", varargs...)
	ret0, _ := ret[0].(*risk.ScreenUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScreenUser indicates an expected call of ScreenUser.
func (mr *MockRiskClientMockRecorder) ScreenUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScreenUser", reflect.TypeOf((*MockRiskClient)(nil).ScreenUser), varargs...)
}

// UpdateLEAComplaint mocks base method.
func (m *MockRiskClient) UpdateLEAComplaint(ctx context.Context, in *risk.UpdateLEAComplaintRequest, opts ...grpc.CallOption) (*risk.UpdateLEAComplaintResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateLEAComplaint", varargs...)
	ret0, _ := ret[0].(*risk.UpdateLEAComplaintResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLEAComplaint indicates an expected call of UpdateLEAComplaint.
func (mr *MockRiskClientMockRecorder) UpdateLEAComplaint(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLEAComplaint", reflect.TypeOf((*MockRiskClient)(nil).UpdateLEAComplaint), varargs...)
}

// UpdateScreenerAttempt mocks base method.
func (m *MockRiskClient) UpdateScreenerAttempt(ctx context.Context, in *risk.UpdateScreenerAttemptRequest, opts ...grpc.CallOption) (*risk.UpdateScreenerAttemptResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateScreenerAttempt", varargs...)
	ret0, _ := ret[0].(*risk.UpdateScreenerAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateScreenerAttempt indicates an expected call of UpdateScreenerAttempt.
func (mr *MockRiskClientMockRecorder) UpdateScreenerAttempt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateScreenerAttempt", reflect.TypeOf((*MockRiskClient)(nil).UpdateScreenerAttempt), varargs...)
}

// UpsertDispute mocks base method.
func (m *MockRiskClient) UpsertDispute(ctx context.Context, in *risk.UpsertDisputeRequest, opts ...grpc.CallOption) (*risk.UpsertDisputeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertDispute", varargs...)
	ret0, _ := ret[0].(*risk.UpsertDisputeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertDispute indicates an expected call of UpsertDispute.
func (mr *MockRiskClientMockRecorder) UpsertDispute(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertDispute", reflect.TypeOf((*MockRiskClient)(nil).UpsertDispute), varargs...)
}

// UpsertRiskData mocks base method.
func (m *MockRiskClient) UpsertRiskData(ctx context.Context, in *risk.UpsertRiskDataRequest, opts ...grpc.CallOption) (*risk.UpsertRiskDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertRiskData", varargs...)
	ret0, _ := ret[0].(*risk.UpsertRiskDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertRiskData indicates an expected call of UpsertRiskData.
func (mr *MockRiskClientMockRecorder) UpsertRiskData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertRiskData", reflect.TypeOf((*MockRiskClient)(nil).UpsertRiskData), varargs...)
}

// MockRiskServer is a mock of RiskServer interface.
type MockRiskServer struct {
	ctrl     *gomock.Controller
	recorder *MockRiskServerMockRecorder
}

// MockRiskServerMockRecorder is the mock recorder for MockRiskServer.
type MockRiskServerMockRecorder struct {
	mock *MockRiskServer
}

// NewMockRiskServer creates a new mock instance.
func NewMockRiskServer(ctrl *gomock.Controller) *MockRiskServer {
	mock := &MockRiskServer{ctrl: ctrl}
	mock.recorder = &MockRiskServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskServer) EXPECT() *MockRiskServerMockRecorder {
	return m.recorder
}

// AddLEAComplaintSource mocks base method.
func (m *MockRiskServer) AddLEAComplaintSource(arg0 context.Context, arg1 *risk.AddLEAComplaintSourceRequest) (*risk.AddLEAComplaintSourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddLEAComplaintSource", arg0, arg1)
	ret0, _ := ret[0].(*risk.AddLEAComplaintSourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddLEAComplaintSource indicates an expected call of AddLEAComplaintSource.
func (mr *MockRiskServerMockRecorder) AddLEAComplaintSource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLEAComplaintSource", reflect.TypeOf((*MockRiskServer)(nil).AddLEAComplaintSource), arg0, arg1)
}

// BulkAddSavingsAccountBankAction mocks base method.
func (m *MockRiskServer) BulkAddSavingsAccountBankAction(arg0 context.Context, arg1 *risk.BulkAddSavingsAccountBankActionRequest) (*risk.BulkAddSavingsAccountBankActionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkAddSavingsAccountBankAction", arg0, arg1)
	ret0, _ := ret[0].(*risk.BulkAddSavingsAccountBankActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkAddSavingsAccountBankAction indicates an expected call of BulkAddSavingsAccountBankAction.
func (mr *MockRiskServerMockRecorder) BulkAddSavingsAccountBankAction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkAddSavingsAccountBankAction", reflect.TypeOf((*MockRiskServer)(nil).BulkAddSavingsAccountBankAction), arg0, arg1)
}

// BulkInsertBureauIdRiskDetails mocks base method.
func (m *MockRiskServer) BulkInsertBureauIdRiskDetails(arg0 context.Context, arg1 *risk.BulkInsertBureauIdRiskDetailsRequest) (*risk.BulkInsertBureauIdRiskDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkInsertBureauIdRiskDetails", arg0, arg1)
	ret0, _ := ret[0].(*risk.BulkInsertBureauIdRiskDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkInsertBureauIdRiskDetails indicates an expected call of BulkInsertBureauIdRiskDetails.
func (mr *MockRiskServerMockRecorder) BulkInsertBureauIdRiskDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkInsertBureauIdRiskDetails", reflect.TypeOf((*MockRiskServer)(nil).BulkInsertBureauIdRiskDetails), arg0, arg1)
}

// BulkOverrideBankActionState mocks base method.
func (m *MockRiskServer) BulkOverrideBankActionState(arg0 context.Context, arg1 *risk.BulkOverrideBankActionStateRequest) (*risk.BulkOverrideBankActionStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkOverrideBankActionState", arg0, arg1)
	ret0, _ := ret[0].(*risk.BulkOverrideBankActionStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkOverrideBankActionState indicates an expected call of BulkOverrideBankActionState.
func (mr *MockRiskServerMockRecorder) BulkOverrideBankActionState(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkOverrideBankActionState", reflect.TypeOf((*MockRiskServer)(nil).BulkOverrideBankActionState), arg0, arg1)
}

// CreateLEAComplaint mocks base method.
func (m *MockRiskServer) CreateLEAComplaint(arg0 context.Context, arg1 *risk.CreateLEAComplaintRequest) (*risk.CreateLEAComplaintResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLEAComplaint", arg0, arg1)
	ret0, _ := ret[0].(*risk.CreateLEAComplaintResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLEAComplaint indicates an expected call of CreateLEAComplaint.
func (mr *MockRiskServerMockRecorder) CreateLEAComplaint(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLEAComplaint", reflect.TypeOf((*MockRiskServer)(nil).CreateLEAComplaint), arg0, arg1)
}

// DynamicElementCallback mocks base method.
func (m *MockRiskServer) DynamicElementCallback(arg0 context.Context, arg1 *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DynamicElementCallback", arg0, arg1)
	ret0, _ := ret[0].(*dynamic_elements.DynamicElementCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DynamicElementCallback indicates an expected call of DynamicElementCallback.
func (mr *MockRiskServerMockRecorder) DynamicElementCallback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DynamicElementCallback", reflect.TypeOf((*MockRiskServer)(nil).DynamicElementCallback), arg0, arg1)
}

// FetchDynamicElements mocks base method.
func (m *MockRiskServer) FetchDynamicElements(arg0 context.Context, arg1 *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchDynamicElements", arg0, arg1)
	ret0, _ := ret[0].(*dynamic_elements.FetchDynamicElementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchDynamicElements indicates an expected call of FetchDynamicElements.
func (mr *MockRiskServerMockRecorder) FetchDynamicElements(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchDynamicElements", reflect.TypeOf((*MockRiskServer)(nil).FetchDynamicElements), arg0, arg1)
}

// FetchLEAComplaints mocks base method.
func (m *MockRiskServer) FetchLEAComplaints(arg0 context.Context, arg1 *risk.FetchLEAComplaintsRequest) (*risk.FetchLEAComplaintsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchLEAComplaints", arg0, arg1)
	ret0, _ := ret[0].(*risk.FetchLEAComplaintsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchLEAComplaints indicates an expected call of FetchLEAComplaints.
func (mr *MockRiskServerMockRecorder) FetchLEAComplaints(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchLEAComplaints", reflect.TypeOf((*MockRiskServer)(nil).FetchLEAComplaints), arg0, arg1)
}

// GetBureauIdRiskDetails mocks base method.
func (m *MockRiskServer) GetBureauIdRiskDetails(arg0 context.Context, arg1 *risk.GetBureauIdRiskDetailsRequest) (*risk.GetBureauIdRiskDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBureauIdRiskDetails", arg0, arg1)
	ret0, _ := ret[0].(*risk.GetBureauIdRiskDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBureauIdRiskDetails indicates an expected call of GetBureauIdRiskDetails.
func (mr *MockRiskServerMockRecorder) GetBureauIdRiskDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBureauIdRiskDetails", reflect.TypeOf((*MockRiskServer)(nil).GetBureauIdRiskDetails), arg0, arg1)
}

// GetRiskData mocks base method.
func (m *MockRiskServer) GetRiskData(arg0 context.Context, arg1 *risk.GetRiskDataRequest) (*risk.GetRiskDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRiskData", arg0, arg1)
	ret0, _ := ret[0].(*risk.GetRiskDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRiskData indicates an expected call of GetRiskData.
func (mr *MockRiskServerMockRecorder) GetRiskData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRiskData", reflect.TypeOf((*MockRiskServer)(nil).GetRiskData), arg0, arg1)
}

// GetScreenerAttemptStatus mocks base method.
func (m *MockRiskServer) GetScreenerAttemptStatus(arg0 context.Context, arg1 *risk.GetScreenerAttemptStatusRequest) (*risk.GetScreenerAttemptStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScreenerAttemptStatus", arg0, arg1)
	ret0, _ := ret[0].(*risk.GetScreenerAttemptStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScreenerAttemptStatus indicates an expected call of GetScreenerAttemptStatus.
func (mr *MockRiskServerMockRecorder) GetScreenerAttemptStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScreenerAttemptStatus", reflect.TypeOf((*MockRiskServer)(nil).GetScreenerAttemptStatus), arg0, arg1)
}

// GetScreenerCheckDetails mocks base method.
func (m *MockRiskServer) GetScreenerCheckDetails(arg0 context.Context, arg1 *risk.GetScreenerCheckDetailsRequest) (*risk.GetScreenerCheckDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScreenerCheckDetails", arg0, arg1)
	ret0, _ := ret[0].(*risk.GetScreenerCheckDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScreenerCheckDetails indicates an expected call of GetScreenerCheckDetails.
func (mr *MockRiskServerMockRecorder) GetScreenerCheckDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScreenerCheckDetails", reflect.TypeOf((*MockRiskServer)(nil).GetScreenerCheckDetails), arg0, arg1)
}

// GetScreenerCheckResults mocks base method.
func (m *MockRiskServer) GetScreenerCheckResults(arg0 context.Context, arg1 *risk.GetScreenerCheckResultsRequest) (*risk.GetScreenerCheckResultsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScreenerCheckResults", arg0, arg1)
	ret0, _ := ret[0].(*risk.GetScreenerCheckResultsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScreenerCheckResults indicates an expected call of GetScreenerCheckResults.
func (mr *MockRiskServerMockRecorder) GetScreenerCheckResults(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScreenerCheckResults", reflect.TypeOf((*MockRiskServer)(nil).GetScreenerCheckResults), arg0, arg1)
}

// GetTransactionTags mocks base method.
func (m *MockRiskServer) GetTransactionTags(arg0 context.Context, arg1 *risk.GetTransactionTagsRequest) (*risk.GetTransactionTagsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionTags", arg0, arg1)
	ret0, _ := ret[0].(*risk.GetTransactionTagsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionTags indicates an expected call of GetTransactionTags.
func (mr *MockRiskServerMockRecorder) GetTransactionTags(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionTags", reflect.TypeOf((*MockRiskServer)(nil).GetTransactionTags), arg0, arg1)
}

// PassRiskScreenerAttempt mocks base method.
func (m *MockRiskServer) PassRiskScreenerAttempt(arg0 context.Context, arg1 *risk.PassRiskScreenerAttemptRequest) (*risk.PassRiskScreenerAttemptResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PassRiskScreenerAttempt", arg0, arg1)
	ret0, _ := ret[0].(*risk.PassRiskScreenerAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PassRiskScreenerAttempt indicates an expected call of PassRiskScreenerAttempt.
func (mr *MockRiskServerMockRecorder) PassRiskScreenerAttempt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PassRiskScreenerAttempt", reflect.TypeOf((*MockRiskServer)(nil).PassRiskScreenerAttempt), arg0, arg1)
}

// ProcessLEAComplaintNarration mocks base method.
func (m *MockRiskServer) ProcessLEAComplaintNarration(arg0 context.Context, arg1 *risk.ProcessLEAComplaintNarrationRequest) (*risk.ProcessLEAComplaintNarrationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessLEAComplaintNarration", arg0, arg1)
	ret0, _ := ret[0].(*risk.ProcessLEAComplaintNarrationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessLEAComplaintNarration indicates an expected call of ProcessLEAComplaintNarration.
func (mr *MockRiskServerMockRecorder) ProcessLEAComplaintNarration(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessLEAComplaintNarration", reflect.TypeOf((*MockRiskServer)(nil).ProcessLEAComplaintNarration), arg0, arg1)
}

// ScreenActor mocks base method.
func (m *MockRiskServer) ScreenActor(arg0 context.Context, arg1 *risk.ScreenActorRequest) (*risk.ScreenActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScreenActor", arg0, arg1)
	ret0, _ := ret[0].(*risk.ScreenActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScreenActor indicates an expected call of ScreenActor.
func (mr *MockRiskServerMockRecorder) ScreenActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScreenActor", reflect.TypeOf((*MockRiskServer)(nil).ScreenActor), arg0, arg1)
}

// ScreenUser mocks base method.
func (m *MockRiskServer) ScreenUser(arg0 context.Context, arg1 *risk.ScreenUserRequest) (*risk.ScreenUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScreenUser", arg0, arg1)
	ret0, _ := ret[0].(*risk.ScreenUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScreenUser indicates an expected call of ScreenUser.
func (mr *MockRiskServerMockRecorder) ScreenUser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScreenUser", reflect.TypeOf((*MockRiskServer)(nil).ScreenUser), arg0, arg1)
}

// UpdateLEAComplaint mocks base method.
func (m *MockRiskServer) UpdateLEAComplaint(arg0 context.Context, arg1 *risk.UpdateLEAComplaintRequest) (*risk.UpdateLEAComplaintResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLEAComplaint", arg0, arg1)
	ret0, _ := ret[0].(*risk.UpdateLEAComplaintResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLEAComplaint indicates an expected call of UpdateLEAComplaint.
func (mr *MockRiskServerMockRecorder) UpdateLEAComplaint(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLEAComplaint", reflect.TypeOf((*MockRiskServer)(nil).UpdateLEAComplaint), arg0, arg1)
}

// UpdateScreenerAttempt mocks base method.
func (m *MockRiskServer) UpdateScreenerAttempt(arg0 context.Context, arg1 *risk.UpdateScreenerAttemptRequest) (*risk.UpdateScreenerAttemptResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateScreenerAttempt", arg0, arg1)
	ret0, _ := ret[0].(*risk.UpdateScreenerAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateScreenerAttempt indicates an expected call of UpdateScreenerAttempt.
func (mr *MockRiskServerMockRecorder) UpdateScreenerAttempt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateScreenerAttempt", reflect.TypeOf((*MockRiskServer)(nil).UpdateScreenerAttempt), arg0, arg1)
}

// UpsertDispute mocks base method.
func (m *MockRiskServer) UpsertDispute(arg0 context.Context, arg1 *risk.UpsertDisputeRequest) (*risk.UpsertDisputeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertDispute", arg0, arg1)
	ret0, _ := ret[0].(*risk.UpsertDisputeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertDispute indicates an expected call of UpsertDispute.
func (mr *MockRiskServerMockRecorder) UpsertDispute(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertDispute", reflect.TypeOf((*MockRiskServer)(nil).UpsertDispute), arg0, arg1)
}

// UpsertRiskData mocks base method.
func (m *MockRiskServer) UpsertRiskData(arg0 context.Context, arg1 *risk.UpsertRiskDataRequest) (*risk.UpsertRiskDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertRiskData", arg0, arg1)
	ret0, _ := ret[0].(*risk.UpsertRiskDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertRiskData indicates an expected call of UpsertRiskData.
func (mr *MockRiskServerMockRecorder) UpsertRiskData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertRiskData", reflect.TypeOf((*MockRiskServer)(nil).UpsertRiskData), arg0, arg1)
}

// MockUnsafeRiskServer is a mock of UnsafeRiskServer interface.
type MockUnsafeRiskServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeRiskServerMockRecorder
}

// MockUnsafeRiskServerMockRecorder is the mock recorder for MockUnsafeRiskServer.
type MockUnsafeRiskServerMockRecorder struct {
	mock *MockUnsafeRiskServer
}

// NewMockUnsafeRiskServer creates a new mock instance.
func NewMockUnsafeRiskServer(ctrl *gomock.Controller) *MockUnsafeRiskServer {
	mock := &MockUnsafeRiskServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeRiskServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeRiskServer) EXPECT() *MockUnsafeRiskServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedRiskServer mocks base method.
func (m *MockUnsafeRiskServer) mustEmbedUnimplementedRiskServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedRiskServer")
}

// mustEmbedUnimplementedRiskServer indicates an expected call of mustEmbedUnimplementedRiskServer.
func (mr *MockUnsafeRiskServerMockRecorder) mustEmbedUnimplementedRiskServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedRiskServer", reflect.TypeOf((*MockUnsafeRiskServer)(nil).mustEmbedUnimplementedRiskServer))
}
