// Code generated by MockGen. DO NOT EDIT.
// Source: api/risk/case_management/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	case_management "github.com/epifi/gamma/api/risk/case_management"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCaseManagementClient is a mock of CaseManagementClient interface.
type MockCaseManagementClient struct {
	ctrl     *gomock.Controller
	recorder *MockCaseManagementClientMockRecorder
}

// MockCaseManagementClientMockRecorder is the mock recorder for MockCaseManagementClient.
type MockCaseManagementClientMockRecorder struct {
	mock *MockCaseManagementClient
}

// NewMockCaseManagementClient creates a new mock instance.
func NewMockCaseManagementClient(ctrl *gomock.Controller) *MockCaseManagementClient {
	mock := &MockCaseManagementClient{ctrl: ctrl}
	mock.recorder = &MockCaseManagementClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCaseManagementClient) EXPECT() *MockCaseManagementClientMockRecorder {
	return m.recorder
}

// CreateAlerts mocks base method.
func (m *MockCaseManagementClient) CreateAlerts(ctx context.Context, in *case_management.CreateAlertsRequest, opts ...grpc.CallOption) (*case_management.CreateAlertsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAlerts", varargs...)
	ret0, _ := ret[0].(*case_management.CreateAlertsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAlerts indicates an expected call of CreateAlerts.
func (mr *MockCaseManagementClientMockRecorder) CreateAlerts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAlerts", reflect.TypeOf((*MockCaseManagementClient)(nil).CreateAlerts), varargs...)
}

// CreateAllowedAnnotation mocks base method.
func (m *MockCaseManagementClient) CreateAllowedAnnotation(ctx context.Context, in *case_management.CreateAllowedAnnotationRequest, opts ...grpc.CallOption) (*case_management.CreateAllowedAnnotationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAllowedAnnotation", varargs...)
	ret0, _ := ret[0].(*case_management.CreateAllowedAnnotationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAllowedAnnotation indicates an expected call of CreateAllowedAnnotation.
func (mr *MockCaseManagementClientMockRecorder) CreateAllowedAnnotation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAllowedAnnotation", reflect.TypeOf((*MockCaseManagementClient)(nil).CreateAllowedAnnotation), varargs...)
}

// CreateAnnotation mocks base method.
func (m *MockCaseManagementClient) CreateAnnotation(ctx context.Context, in *case_management.CreateAnnotationRequest, opts ...grpc.CallOption) (*case_management.CreateAnnotationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAnnotation", varargs...)
	ret0, _ := ret[0].(*case_management.CreateAnnotationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAnnotation indicates an expected call of CreateAnnotation.
func (mr *MockCaseManagementClientMockRecorder) CreateAnnotation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAnnotation", reflect.TypeOf((*MockCaseManagementClient)(nil).CreateAnnotation), varargs...)
}

// CreateAnnotations mocks base method.
func (m *MockCaseManagementClient) CreateAnnotations(ctx context.Context, in *case_management.CreateAnnotationsRequest, opts ...grpc.CallOption) (*case_management.CreateAnnotationsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAnnotations", varargs...)
	ret0, _ := ret[0].(*case_management.CreateAnnotationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAnnotations indicates an expected call of CreateAnnotations.
func (mr *MockCaseManagementClientMockRecorder) CreateAnnotations(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAnnotations", reflect.TypeOf((*MockCaseManagementClient)(nil).CreateAnnotations), varargs...)
}

// CreateComment mocks base method.
func (m *MockCaseManagementClient) CreateComment(ctx context.Context, in *case_management.CreateCommentRequest, opts ...grpc.CallOption) (*case_management.CreateCommentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateComment", varargs...)
	ret0, _ := ret[0].(*case_management.CreateCommentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateComment indicates an expected call of CreateComment.
func (mr *MockCaseManagementClientMockRecorder) CreateComment(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateComment", reflect.TypeOf((*MockCaseManagementClient)(nil).CreateComment), varargs...)
}

// CreateRule mocks base method.
func (m *MockCaseManagementClient) CreateRule(ctx context.Context, in *case_management.CreateRuleRequest, opts ...grpc.CallOption) (*case_management.CreateRuleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRule", varargs...)
	ret0, _ := ret[0].(*case_management.CreateRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRule indicates an expected call of CreateRule.
func (mr *MockCaseManagementClientMockRecorder) CreateRule(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRule", reflect.TypeOf((*MockCaseManagementClient)(nil).CreateRule), varargs...)
}

// CreateRuleReviewTypeMapping mocks base method.
func (m *MockCaseManagementClient) CreateRuleReviewTypeMapping(ctx context.Context, in *case_management.CreateRuleReviewTypeMappingRequest, opts ...grpc.CallOption) (*case_management.CreateRuleReviewTypeMappingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRuleReviewTypeMapping", varargs...)
	ret0, _ := ret[0].(*case_management.CreateRuleReviewTypeMappingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRuleReviewTypeMapping indicates an expected call of CreateRuleReviewTypeMapping.
func (mr *MockCaseManagementClientMockRecorder) CreateRuleReviewTypeMapping(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRuleReviewTypeMapping", reflect.TypeOf((*MockCaseManagementClient)(nil).CreateRuleReviewTypeMapping), varargs...)
}

// CreateSuggestedActionForRule mocks base method.
func (m *MockCaseManagementClient) CreateSuggestedActionForRule(ctx context.Context, in *case_management.CreateSuggestedActionForRuleRequest, opts ...grpc.CallOption) (*case_management.CreateSuggestedActionForRuleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateSuggestedActionForRule", varargs...)
	ret0, _ := ret[0].(*case_management.CreateSuggestedActionForRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSuggestedActionForRule indicates an expected call of CreateSuggestedActionForRule.
func (mr *MockCaseManagementClientMockRecorder) CreateSuggestedActionForRule(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSuggestedActionForRule", reflect.TypeOf((*MockCaseManagementClient)(nil).CreateSuggestedActionForRule), varargs...)
}

// CreateTransactionBlock mocks base method.
func (m *MockCaseManagementClient) CreateTransactionBlock(ctx context.Context, in *case_management.CreateTransactionBlockRequest, opts ...grpc.CallOption) (*case_management.CreateTransactionBlockResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTransactionBlock", varargs...)
	ret0, _ := ret[0].(*case_management.CreateTransactionBlockResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTransactionBlock indicates an expected call of CreateTransactionBlock.
func (mr *MockCaseManagementClientMockRecorder) CreateTransactionBlock(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTransactionBlock", reflect.TypeOf((*MockCaseManagementClient)(nil).CreateTransactionBlock), varargs...)
}

// GetAlerts mocks base method.
func (m *MockCaseManagementClient) GetAlerts(ctx context.Context, in *case_management.GetAlertsRequest, opts ...grpc.CallOption) (*case_management.GetAlertsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAlerts", varargs...)
	ret0, _ := ret[0].(*case_management.GetAlertsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAlerts indicates an expected call of GetAlerts.
func (mr *MockCaseManagementClientMockRecorder) GetAlerts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAlerts", reflect.TypeOf((*MockCaseManagementClient)(nil).GetAlerts), varargs...)
}

// GetAllTags mocks base method.
func (m *MockCaseManagementClient) GetAllTags(ctx context.Context, in *case_management.GetAllTagsRequest, opts ...grpc.CallOption) (*case_management.GetAllTagsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllTags", varargs...)
	ret0, _ := ret[0].(*case_management.GetAllTagsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllTags indicates an expected call of GetAllTags.
func (mr *MockCaseManagementClientMockRecorder) GetAllTags(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTags", reflect.TypeOf((*MockCaseManagementClient)(nil).GetAllTags), varargs...)
}

// GetAllowedAnnotations mocks base method.
func (m *MockCaseManagementClient) GetAllowedAnnotations(ctx context.Context, in *case_management.GetAllowedAnnotationsRequest, opts ...grpc.CallOption) (*case_management.GetAllowedAnnotationsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllowedAnnotations", varargs...)
	ret0, _ := ret[0].(*case_management.GetAllowedAnnotationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllowedAnnotations indicates an expected call of GetAllowedAnnotations.
func (mr *MockCaseManagementClientMockRecorder) GetAllowedAnnotations(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllowedAnnotations", reflect.TypeOf((*MockCaseManagementClient)(nil).GetAllowedAnnotations), varargs...)
}

// GetFiUserRelationship mocks base method.
func (m *MockCaseManagementClient) GetFiUserRelationship(ctx context.Context, in *case_management.GetFiUserRelationshipRequest, opts ...grpc.CallOption) (*case_management.GetFiUserRelationshipResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFiUserRelationship", varargs...)
	ret0, _ := ret[0].(*case_management.GetFiUserRelationshipResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFiUserRelationship indicates an expected call of GetFiUserRelationship.
func (mr *MockCaseManagementClientMockRecorder) GetFiUserRelationship(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFiUserRelationship", reflect.TypeOf((*MockCaseManagementClient)(nil).GetFiUserRelationship), varargs...)
}

// GetForm mocks base method.
func (m *MockCaseManagementClient) GetForm(ctx context.Context, in *case_management.GetFormRequest, opts ...grpc.CallOption) (*case_management.GetFormResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetForm", varargs...)
	ret0, _ := ret[0].(*case_management.GetFormResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForm indicates an expected call of GetForm.
func (mr *MockCaseManagementClientMockRecorder) GetForm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForm", reflect.TypeOf((*MockCaseManagementClient)(nil).GetForm), varargs...)
}

// GetFormsForActor mocks base method.
func (m *MockCaseManagementClient) GetFormsForActor(ctx context.Context, in *case_management.GetFormsForActorRequest, opts ...grpc.CallOption) (*case_management.GetFormsForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFormsForActor", varargs...)
	ret0, _ := ret[0].(*case_management.GetFormsForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFormsForActor indicates an expected call of GetFormsForActor.
func (mr *MockCaseManagementClientMockRecorder) GetFormsForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFormsForActor", reflect.TypeOf((*MockCaseManagementClient)(nil).GetFormsForActor), varargs...)
}

// GetLinkedAlerts mocks base method.
func (m *MockCaseManagementClient) GetLinkedAlerts(ctx context.Context, in *case_management.GetLinkedAlertsRequest, opts ...grpc.CallOption) (*case_management.GetLinkedAlertsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLinkedAlerts", varargs...)
	ret0, _ := ret[0].(*case_management.GetLinkedAlertsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLinkedAlerts indicates an expected call of GetLinkedAlerts.
func (mr *MockCaseManagementClientMockRecorder) GetLinkedAlerts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLinkedAlerts", reflect.TypeOf((*MockCaseManagementClient)(nil).GetLinkedAlerts), varargs...)
}

// GetPrioritizedCase mocks base method.
func (m *MockCaseManagementClient) GetPrioritizedCase(ctx context.Context, in *case_management.GetPrioritizedCaseRequest, opts ...grpc.CallOption) (*case_management.GetPrioritizedCaseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPrioritizedCase", varargs...)
	ret0, _ := ret[0].(*case_management.GetPrioritizedCaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrioritizedCase indicates an expected call of GetPrioritizedCase.
func (mr *MockCaseManagementClientMockRecorder) GetPrioritizedCase(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrioritizedCase", reflect.TypeOf((*MockCaseManagementClient)(nil).GetPrioritizedCase), varargs...)
}

// GetReviewDetails mocks base method.
func (m *MockCaseManagementClient) GetReviewDetails(ctx context.Context, in *case_management.GetReviewDetailsRequest, opts ...grpc.CallOption) (*case_management.GetReviewDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetReviewDetails", varargs...)
	ret0, _ := ret[0].(*case_management.GetReviewDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReviewDetails indicates an expected call of GetReviewDetails.
func (mr *MockCaseManagementClientMockRecorder) GetReviewDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReviewDetails", reflect.TypeOf((*MockCaseManagementClient)(nil).GetReviewDetails), varargs...)
}

// GetRiskTransactionAggregatedMetrics mocks base method.
func (m *MockCaseManagementClient) GetRiskTransactionAggregatedMetrics(ctx context.Context, in *case_management.GetRiskTransactionAggregatedMetricsRequest, opts ...grpc.CallOption) (*case_management.GetRiskTransactionAggregatedMetricsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRiskTransactionAggregatedMetrics", varargs...)
	ret0, _ := ret[0].(*case_management.GetRiskTransactionAggregatedMetricsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRiskTransactionAggregatedMetrics indicates an expected call of GetRiskTransactionAggregatedMetrics.
func (mr *MockCaseManagementClientMockRecorder) GetRiskTransactionAggregatedMetrics(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRiskTransactionAggregatedMetrics", reflect.TypeOf((*MockCaseManagementClient)(nil).GetRiskTransactionAggregatedMetrics), varargs...)
}

// GetTransactionBlocks mocks base method.
func (m *MockCaseManagementClient) GetTransactionBlocks(ctx context.Context, in *case_management.GetTransactionBlocksRequest, opts ...grpc.CallOption) (*case_management.GetTransactionBlocksResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionBlocks", varargs...)
	ret0, _ := ret[0].(*case_management.GetTransactionBlocksResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionBlocks indicates an expected call of GetTransactionBlocks.
func (mr *MockCaseManagementClientMockRecorder) GetTransactionBlocks(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionBlocks", reflect.TypeOf((*MockCaseManagementClient)(nil).GetTransactionBlocks), varargs...)
}

// GetTransactionDetailsForReview mocks base method.
func (m *MockCaseManagementClient) GetTransactionDetailsForReview(ctx context.Context, in *case_management.GetTransactionDetailsForReviewRequest, opts ...grpc.CallOption) (*case_management.GetTransactionDetailsForReviewResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionDetailsForReview", varargs...)
	ret0, _ := ret[0].(*case_management.GetTransactionDetailsForReviewResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionDetailsForReview indicates an expected call of GetTransactionDetailsForReview.
func (mr *MockCaseManagementClientMockRecorder) GetTransactionDetailsForReview(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionDetailsForReview", reflect.TypeOf((*MockCaseManagementClient)(nil).GetTransactionDetailsForReview), varargs...)
}

// ListAllowedAnnotations mocks base method.
func (m *MockCaseManagementClient) ListAllowedAnnotations(ctx context.Context, in *case_management.ListAllowedAnnotationsRequest, opts ...grpc.CallOption) (*case_management.ListAllowedAnnotationsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListAllowedAnnotations", varargs...)
	ret0, _ := ret[0].(*case_management.ListAllowedAnnotationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAllowedAnnotations indicates an expected call of ListAllowedAnnotations.
func (mr *MockCaseManagementClientMockRecorder) ListAllowedAnnotations(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllowedAnnotations", reflect.TypeOf((*MockCaseManagementClient)(nil).ListAllowedAnnotations), varargs...)
}

// ListAnnotations mocks base method.
func (m *MockCaseManagementClient) ListAnnotations(ctx context.Context, in *case_management.ListAnnotationsRequest, opts ...grpc.CallOption) (*case_management.ListAnnotationsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListAnnotations", varargs...)
	ret0, _ := ret[0].(*case_management.ListAnnotationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAnnotations indicates an expected call of ListAnnotations.
func (mr *MockCaseManagementClientMockRecorder) ListAnnotations(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAnnotations", reflect.TypeOf((*MockCaseManagementClient)(nil).ListAnnotations), varargs...)
}

// ListCases mocks base method.
func (m *MockCaseManagementClient) ListCases(ctx context.Context, in *case_management.ListCasesRequest, opts ...grpc.CallOption) (*case_management.ListCasesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListCases", varargs...)
	ret0, _ := ret[0].(*case_management.ListCasesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCases indicates an expected call of ListCases.
func (mr *MockCaseManagementClientMockRecorder) ListCases(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCases", reflect.TypeOf((*MockCaseManagementClient)(nil).ListCases), varargs...)
}

// ListComments mocks base method.
func (m *MockCaseManagementClient) ListComments(ctx context.Context, in *case_management.ListCommentsRequest, opts ...grpc.CallOption) (*case_management.ListCommentsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListComments", varargs...)
	ret0, _ := ret[0].(*case_management.ListCommentsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListComments indicates an expected call of ListComments.
func (mr *MockCaseManagementClientMockRecorder) ListComments(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListComments", reflect.TypeOf((*MockCaseManagementClient)(nil).ListComments), varargs...)
}

// ListForms mocks base method.
func (m *MockCaseManagementClient) ListForms(ctx context.Context, in *case_management.ListFormsRequest, opts ...grpc.CallOption) (*case_management.ListFormsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListForms", varargs...)
	ret0, _ := ret[0].(*case_management.ListFormsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListForms indicates an expected call of ListForms.
func (mr *MockCaseManagementClientMockRecorder) ListForms(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListForms", reflect.TypeOf((*MockCaseManagementClient)(nil).ListForms), varargs...)
}

// ListRules mocks base method.
func (m *MockCaseManagementClient) ListRules(ctx context.Context, in *case_management.ListRulesRequest, opts ...grpc.CallOption) (*case_management.ListRulesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListRules", varargs...)
	ret0, _ := ret[0].(*case_management.ListRulesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRules indicates an expected call of ListRules.
func (mr *MockCaseManagementClientMockRecorder) ListRules(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRules", reflect.TypeOf((*MockCaseManagementClient)(nil).ListRules), varargs...)
}

// ListSortedCases mocks base method.
func (m *MockCaseManagementClient) ListSortedCases(ctx context.Context, in *case_management.ListSortedCasesRequest, opts ...grpc.CallOption) (*case_management.ListSortedCasesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListSortedCases", varargs...)
	ret0, _ := ret[0].(*case_management.ListSortedCasesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSortedCases indicates an expected call of ListSortedCases.
func (mr *MockCaseManagementClientMockRecorder) ListSortedCases(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSortedCases", reflect.TypeOf((*MockCaseManagementClient)(nil).ListSortedCases), varargs...)
}

// PerformAction mocks base method.
func (m *MockCaseManagementClient) PerformAction(ctx context.Context, in *case_management.PerformActionRequest, opts ...grpc.CallOption) (*case_management.PerformActionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PerformAction", varargs...)
	ret0, _ := ret[0].(*case_management.PerformActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PerformAction indicates an expected call of PerformAction.
func (mr *MockCaseManagementClientMockRecorder) PerformAction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PerformAction", reflect.TypeOf((*MockCaseManagementClient)(nil).PerformAction), varargs...)
}

// SubmitForm mocks base method.
func (m *MockCaseManagementClient) SubmitForm(ctx context.Context, in *case_management.SubmitFormRequest, opts ...grpc.CallOption) (*case_management.SubmitFormResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitForm", varargs...)
	ret0, _ := ret[0].(*case_management.SubmitFormResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitForm indicates an expected call of SubmitForm.
func (mr *MockCaseManagementClientMockRecorder) SubmitForm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitForm", reflect.TypeOf((*MockCaseManagementClient)(nil).SubmitForm), varargs...)
}

// UpdateCase mocks base method.
func (m *MockCaseManagementClient) UpdateCase(ctx context.Context, in *case_management.UpdateCaseRequest, opts ...grpc.CallOption) (*case_management.UpdateCaseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCase", varargs...)
	ret0, _ := ret[0].(*case_management.UpdateCaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCase indicates an expected call of UpdateCase.
func (mr *MockCaseManagementClientMockRecorder) UpdateCase(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCase", reflect.TypeOf((*MockCaseManagementClient)(nil).UpdateCase), varargs...)
}

// UpdateRule mocks base method.
func (m *MockCaseManagementClient) UpdateRule(ctx context.Context, in *case_management.UpdateRuleRequest, opts ...grpc.CallOption) (*case_management.UpdateRuleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRule", varargs...)
	ret0, _ := ret[0].(*case_management.UpdateRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRule indicates an expected call of UpdateRule.
func (mr *MockCaseManagementClientMockRecorder) UpdateRule(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRule", reflect.TypeOf((*MockCaseManagementClient)(nil).UpdateRule), varargs...)
}

// MockCaseManagementServer is a mock of CaseManagementServer interface.
type MockCaseManagementServer struct {
	ctrl     *gomock.Controller
	recorder *MockCaseManagementServerMockRecorder
}

// MockCaseManagementServerMockRecorder is the mock recorder for MockCaseManagementServer.
type MockCaseManagementServerMockRecorder struct {
	mock *MockCaseManagementServer
}

// NewMockCaseManagementServer creates a new mock instance.
func NewMockCaseManagementServer(ctrl *gomock.Controller) *MockCaseManagementServer {
	mock := &MockCaseManagementServer{ctrl: ctrl}
	mock.recorder = &MockCaseManagementServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCaseManagementServer) EXPECT() *MockCaseManagementServerMockRecorder {
	return m.recorder
}

// CreateAlerts mocks base method.
func (m *MockCaseManagementServer) CreateAlerts(arg0 context.Context, arg1 *case_management.CreateAlertsRequest) (*case_management.CreateAlertsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAlerts", arg0, arg1)
	ret0, _ := ret[0].(*case_management.CreateAlertsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAlerts indicates an expected call of CreateAlerts.
func (mr *MockCaseManagementServerMockRecorder) CreateAlerts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAlerts", reflect.TypeOf((*MockCaseManagementServer)(nil).CreateAlerts), arg0, arg1)
}

// CreateAllowedAnnotation mocks base method.
func (m *MockCaseManagementServer) CreateAllowedAnnotation(arg0 context.Context, arg1 *case_management.CreateAllowedAnnotationRequest) (*case_management.CreateAllowedAnnotationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAllowedAnnotation", arg0, arg1)
	ret0, _ := ret[0].(*case_management.CreateAllowedAnnotationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAllowedAnnotation indicates an expected call of CreateAllowedAnnotation.
func (mr *MockCaseManagementServerMockRecorder) CreateAllowedAnnotation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAllowedAnnotation", reflect.TypeOf((*MockCaseManagementServer)(nil).CreateAllowedAnnotation), arg0, arg1)
}

// CreateAnnotation mocks base method.
func (m *MockCaseManagementServer) CreateAnnotation(arg0 context.Context, arg1 *case_management.CreateAnnotationRequest) (*case_management.CreateAnnotationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAnnotation", arg0, arg1)
	ret0, _ := ret[0].(*case_management.CreateAnnotationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAnnotation indicates an expected call of CreateAnnotation.
func (mr *MockCaseManagementServerMockRecorder) CreateAnnotation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAnnotation", reflect.TypeOf((*MockCaseManagementServer)(nil).CreateAnnotation), arg0, arg1)
}

// CreateAnnotations mocks base method.
func (m *MockCaseManagementServer) CreateAnnotations(arg0 context.Context, arg1 *case_management.CreateAnnotationsRequest) (*case_management.CreateAnnotationsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAnnotations", arg0, arg1)
	ret0, _ := ret[0].(*case_management.CreateAnnotationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAnnotations indicates an expected call of CreateAnnotations.
func (mr *MockCaseManagementServerMockRecorder) CreateAnnotations(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAnnotations", reflect.TypeOf((*MockCaseManagementServer)(nil).CreateAnnotations), arg0, arg1)
}

// CreateComment mocks base method.
func (m *MockCaseManagementServer) CreateComment(arg0 context.Context, arg1 *case_management.CreateCommentRequest) (*case_management.CreateCommentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateComment", arg0, arg1)
	ret0, _ := ret[0].(*case_management.CreateCommentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateComment indicates an expected call of CreateComment.
func (mr *MockCaseManagementServerMockRecorder) CreateComment(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateComment", reflect.TypeOf((*MockCaseManagementServer)(nil).CreateComment), arg0, arg1)
}

// CreateRule mocks base method.
func (m *MockCaseManagementServer) CreateRule(arg0 context.Context, arg1 *case_management.CreateRuleRequest) (*case_management.CreateRuleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRule", arg0, arg1)
	ret0, _ := ret[0].(*case_management.CreateRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRule indicates an expected call of CreateRule.
func (mr *MockCaseManagementServerMockRecorder) CreateRule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRule", reflect.TypeOf((*MockCaseManagementServer)(nil).CreateRule), arg0, arg1)
}

// CreateRuleReviewTypeMapping mocks base method.
func (m *MockCaseManagementServer) CreateRuleReviewTypeMapping(arg0 context.Context, arg1 *case_management.CreateRuleReviewTypeMappingRequest) (*case_management.CreateRuleReviewTypeMappingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRuleReviewTypeMapping", arg0, arg1)
	ret0, _ := ret[0].(*case_management.CreateRuleReviewTypeMappingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRuleReviewTypeMapping indicates an expected call of CreateRuleReviewTypeMapping.
func (mr *MockCaseManagementServerMockRecorder) CreateRuleReviewTypeMapping(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRuleReviewTypeMapping", reflect.TypeOf((*MockCaseManagementServer)(nil).CreateRuleReviewTypeMapping), arg0, arg1)
}

// CreateSuggestedActionForRule mocks base method.
func (m *MockCaseManagementServer) CreateSuggestedActionForRule(arg0 context.Context, arg1 *case_management.CreateSuggestedActionForRuleRequest) (*case_management.CreateSuggestedActionForRuleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSuggestedActionForRule", arg0, arg1)
	ret0, _ := ret[0].(*case_management.CreateSuggestedActionForRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSuggestedActionForRule indicates an expected call of CreateSuggestedActionForRule.
func (mr *MockCaseManagementServerMockRecorder) CreateSuggestedActionForRule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSuggestedActionForRule", reflect.TypeOf((*MockCaseManagementServer)(nil).CreateSuggestedActionForRule), arg0, arg1)
}

// CreateTransactionBlock mocks base method.
func (m *MockCaseManagementServer) CreateTransactionBlock(arg0 context.Context, arg1 *case_management.CreateTransactionBlockRequest) (*case_management.CreateTransactionBlockResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTransactionBlock", arg0, arg1)
	ret0, _ := ret[0].(*case_management.CreateTransactionBlockResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTransactionBlock indicates an expected call of CreateTransactionBlock.
func (mr *MockCaseManagementServerMockRecorder) CreateTransactionBlock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTransactionBlock", reflect.TypeOf((*MockCaseManagementServer)(nil).CreateTransactionBlock), arg0, arg1)
}

// GetAlerts mocks base method.
func (m *MockCaseManagementServer) GetAlerts(arg0 context.Context, arg1 *case_management.GetAlertsRequest) (*case_management.GetAlertsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAlerts", arg0, arg1)
	ret0, _ := ret[0].(*case_management.GetAlertsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAlerts indicates an expected call of GetAlerts.
func (mr *MockCaseManagementServerMockRecorder) GetAlerts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAlerts", reflect.TypeOf((*MockCaseManagementServer)(nil).GetAlerts), arg0, arg1)
}

// GetAllTags mocks base method.
func (m *MockCaseManagementServer) GetAllTags(arg0 context.Context, arg1 *case_management.GetAllTagsRequest) (*case_management.GetAllTagsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTags", arg0, arg1)
	ret0, _ := ret[0].(*case_management.GetAllTagsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllTags indicates an expected call of GetAllTags.
func (mr *MockCaseManagementServerMockRecorder) GetAllTags(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTags", reflect.TypeOf((*MockCaseManagementServer)(nil).GetAllTags), arg0, arg1)
}

// GetAllowedAnnotations mocks base method.
func (m *MockCaseManagementServer) GetAllowedAnnotations(arg0 context.Context, arg1 *case_management.GetAllowedAnnotationsRequest) (*case_management.GetAllowedAnnotationsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllowedAnnotations", arg0, arg1)
	ret0, _ := ret[0].(*case_management.GetAllowedAnnotationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllowedAnnotations indicates an expected call of GetAllowedAnnotations.
func (mr *MockCaseManagementServerMockRecorder) GetAllowedAnnotations(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllowedAnnotations", reflect.TypeOf((*MockCaseManagementServer)(nil).GetAllowedAnnotations), arg0, arg1)
}

// GetFiUserRelationship mocks base method.
func (m *MockCaseManagementServer) GetFiUserRelationship(arg0 context.Context, arg1 *case_management.GetFiUserRelationshipRequest) (*case_management.GetFiUserRelationshipResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFiUserRelationship", arg0, arg1)
	ret0, _ := ret[0].(*case_management.GetFiUserRelationshipResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFiUserRelationship indicates an expected call of GetFiUserRelationship.
func (mr *MockCaseManagementServerMockRecorder) GetFiUserRelationship(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFiUserRelationship", reflect.TypeOf((*MockCaseManagementServer)(nil).GetFiUserRelationship), arg0, arg1)
}

// GetForm mocks base method.
func (m *MockCaseManagementServer) GetForm(arg0 context.Context, arg1 *case_management.GetFormRequest) (*case_management.GetFormResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetForm", arg0, arg1)
	ret0, _ := ret[0].(*case_management.GetFormResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForm indicates an expected call of GetForm.
func (mr *MockCaseManagementServerMockRecorder) GetForm(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForm", reflect.TypeOf((*MockCaseManagementServer)(nil).GetForm), arg0, arg1)
}

// GetFormsForActor mocks base method.
func (m *MockCaseManagementServer) GetFormsForActor(arg0 context.Context, arg1 *case_management.GetFormsForActorRequest) (*case_management.GetFormsForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFormsForActor", arg0, arg1)
	ret0, _ := ret[0].(*case_management.GetFormsForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFormsForActor indicates an expected call of GetFormsForActor.
func (mr *MockCaseManagementServerMockRecorder) GetFormsForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFormsForActor", reflect.TypeOf((*MockCaseManagementServer)(nil).GetFormsForActor), arg0, arg1)
}

// GetLinkedAlerts mocks base method.
func (m *MockCaseManagementServer) GetLinkedAlerts(arg0 context.Context, arg1 *case_management.GetLinkedAlertsRequest) (*case_management.GetLinkedAlertsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLinkedAlerts", arg0, arg1)
	ret0, _ := ret[0].(*case_management.GetLinkedAlertsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLinkedAlerts indicates an expected call of GetLinkedAlerts.
func (mr *MockCaseManagementServerMockRecorder) GetLinkedAlerts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLinkedAlerts", reflect.TypeOf((*MockCaseManagementServer)(nil).GetLinkedAlerts), arg0, arg1)
}

// GetPrioritizedCase mocks base method.
func (m *MockCaseManagementServer) GetPrioritizedCase(arg0 context.Context, arg1 *case_management.GetPrioritizedCaseRequest) (*case_management.GetPrioritizedCaseResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrioritizedCase", arg0, arg1)
	ret0, _ := ret[0].(*case_management.GetPrioritizedCaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrioritizedCase indicates an expected call of GetPrioritizedCase.
func (mr *MockCaseManagementServerMockRecorder) GetPrioritizedCase(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrioritizedCase", reflect.TypeOf((*MockCaseManagementServer)(nil).GetPrioritizedCase), arg0, arg1)
}

// GetReviewDetails mocks base method.
func (m *MockCaseManagementServer) GetReviewDetails(arg0 context.Context, arg1 *case_management.GetReviewDetailsRequest) (*case_management.GetReviewDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReviewDetails", arg0, arg1)
	ret0, _ := ret[0].(*case_management.GetReviewDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReviewDetails indicates an expected call of GetReviewDetails.
func (mr *MockCaseManagementServerMockRecorder) GetReviewDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReviewDetails", reflect.TypeOf((*MockCaseManagementServer)(nil).GetReviewDetails), arg0, arg1)
}

// GetRiskTransactionAggregatedMetrics mocks base method.
func (m *MockCaseManagementServer) GetRiskTransactionAggregatedMetrics(arg0 context.Context, arg1 *case_management.GetRiskTransactionAggregatedMetricsRequest) (*case_management.GetRiskTransactionAggregatedMetricsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRiskTransactionAggregatedMetrics", arg0, arg1)
	ret0, _ := ret[0].(*case_management.GetRiskTransactionAggregatedMetricsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRiskTransactionAggregatedMetrics indicates an expected call of GetRiskTransactionAggregatedMetrics.
func (mr *MockCaseManagementServerMockRecorder) GetRiskTransactionAggregatedMetrics(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRiskTransactionAggregatedMetrics", reflect.TypeOf((*MockCaseManagementServer)(nil).GetRiskTransactionAggregatedMetrics), arg0, arg1)
}

// GetTransactionBlocks mocks base method.
func (m *MockCaseManagementServer) GetTransactionBlocks(arg0 context.Context, arg1 *case_management.GetTransactionBlocksRequest) (*case_management.GetTransactionBlocksResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionBlocks", arg0, arg1)
	ret0, _ := ret[0].(*case_management.GetTransactionBlocksResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionBlocks indicates an expected call of GetTransactionBlocks.
func (mr *MockCaseManagementServerMockRecorder) GetTransactionBlocks(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionBlocks", reflect.TypeOf((*MockCaseManagementServer)(nil).GetTransactionBlocks), arg0, arg1)
}

// GetTransactionDetailsForReview mocks base method.
func (m *MockCaseManagementServer) GetTransactionDetailsForReview(arg0 context.Context, arg1 *case_management.GetTransactionDetailsForReviewRequest) (*case_management.GetTransactionDetailsForReviewResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionDetailsForReview", arg0, arg1)
	ret0, _ := ret[0].(*case_management.GetTransactionDetailsForReviewResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionDetailsForReview indicates an expected call of GetTransactionDetailsForReview.
func (mr *MockCaseManagementServerMockRecorder) GetTransactionDetailsForReview(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionDetailsForReview", reflect.TypeOf((*MockCaseManagementServer)(nil).GetTransactionDetailsForReview), arg0, arg1)
}

// ListAllowedAnnotations mocks base method.
func (m *MockCaseManagementServer) ListAllowedAnnotations(arg0 context.Context, arg1 *case_management.ListAllowedAnnotationsRequest) (*case_management.ListAllowedAnnotationsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAllowedAnnotations", arg0, arg1)
	ret0, _ := ret[0].(*case_management.ListAllowedAnnotationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAllowedAnnotations indicates an expected call of ListAllowedAnnotations.
func (mr *MockCaseManagementServerMockRecorder) ListAllowedAnnotations(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllowedAnnotations", reflect.TypeOf((*MockCaseManagementServer)(nil).ListAllowedAnnotations), arg0, arg1)
}

// ListAnnotations mocks base method.
func (m *MockCaseManagementServer) ListAnnotations(arg0 context.Context, arg1 *case_management.ListAnnotationsRequest) (*case_management.ListAnnotationsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAnnotations", arg0, arg1)
	ret0, _ := ret[0].(*case_management.ListAnnotationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAnnotations indicates an expected call of ListAnnotations.
func (mr *MockCaseManagementServerMockRecorder) ListAnnotations(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAnnotations", reflect.TypeOf((*MockCaseManagementServer)(nil).ListAnnotations), arg0, arg1)
}

// ListCases mocks base method.
func (m *MockCaseManagementServer) ListCases(arg0 context.Context, arg1 *case_management.ListCasesRequest) (*case_management.ListCasesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCases", arg0, arg1)
	ret0, _ := ret[0].(*case_management.ListCasesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCases indicates an expected call of ListCases.
func (mr *MockCaseManagementServerMockRecorder) ListCases(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCases", reflect.TypeOf((*MockCaseManagementServer)(nil).ListCases), arg0, arg1)
}

// ListComments mocks base method.
func (m *MockCaseManagementServer) ListComments(arg0 context.Context, arg1 *case_management.ListCommentsRequest) (*case_management.ListCommentsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListComments", arg0, arg1)
	ret0, _ := ret[0].(*case_management.ListCommentsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListComments indicates an expected call of ListComments.
func (mr *MockCaseManagementServerMockRecorder) ListComments(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListComments", reflect.TypeOf((*MockCaseManagementServer)(nil).ListComments), arg0, arg1)
}

// ListForms mocks base method.
func (m *MockCaseManagementServer) ListForms(arg0 context.Context, arg1 *case_management.ListFormsRequest) (*case_management.ListFormsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListForms", arg0, arg1)
	ret0, _ := ret[0].(*case_management.ListFormsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListForms indicates an expected call of ListForms.
func (mr *MockCaseManagementServerMockRecorder) ListForms(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListForms", reflect.TypeOf((*MockCaseManagementServer)(nil).ListForms), arg0, arg1)
}

// ListRules mocks base method.
func (m *MockCaseManagementServer) ListRules(arg0 context.Context, arg1 *case_management.ListRulesRequest) (*case_management.ListRulesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRules", arg0, arg1)
	ret0, _ := ret[0].(*case_management.ListRulesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRules indicates an expected call of ListRules.
func (mr *MockCaseManagementServerMockRecorder) ListRules(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRules", reflect.TypeOf((*MockCaseManagementServer)(nil).ListRules), arg0, arg1)
}

// ListSortedCases mocks base method.
func (m *MockCaseManagementServer) ListSortedCases(arg0 context.Context, arg1 *case_management.ListSortedCasesRequest) (*case_management.ListSortedCasesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSortedCases", arg0, arg1)
	ret0, _ := ret[0].(*case_management.ListSortedCasesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSortedCases indicates an expected call of ListSortedCases.
func (mr *MockCaseManagementServerMockRecorder) ListSortedCases(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSortedCases", reflect.TypeOf((*MockCaseManagementServer)(nil).ListSortedCases), arg0, arg1)
}

// PerformAction mocks base method.
func (m *MockCaseManagementServer) PerformAction(arg0 context.Context, arg1 *case_management.PerformActionRequest) (*case_management.PerformActionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PerformAction", arg0, arg1)
	ret0, _ := ret[0].(*case_management.PerformActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PerformAction indicates an expected call of PerformAction.
func (mr *MockCaseManagementServerMockRecorder) PerformAction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PerformAction", reflect.TypeOf((*MockCaseManagementServer)(nil).PerformAction), arg0, arg1)
}

// SubmitForm mocks base method.
func (m *MockCaseManagementServer) SubmitForm(arg0 context.Context, arg1 *case_management.SubmitFormRequest) (*case_management.SubmitFormResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitForm", arg0, arg1)
	ret0, _ := ret[0].(*case_management.SubmitFormResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitForm indicates an expected call of SubmitForm.
func (mr *MockCaseManagementServerMockRecorder) SubmitForm(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitForm", reflect.TypeOf((*MockCaseManagementServer)(nil).SubmitForm), arg0, arg1)
}

// UpdateCase mocks base method.
func (m *MockCaseManagementServer) UpdateCase(arg0 context.Context, arg1 *case_management.UpdateCaseRequest) (*case_management.UpdateCaseResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCase", arg0, arg1)
	ret0, _ := ret[0].(*case_management.UpdateCaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCase indicates an expected call of UpdateCase.
func (mr *MockCaseManagementServerMockRecorder) UpdateCase(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCase", reflect.TypeOf((*MockCaseManagementServer)(nil).UpdateCase), arg0, arg1)
}

// UpdateRule mocks base method.
func (m *MockCaseManagementServer) UpdateRule(arg0 context.Context, arg1 *case_management.UpdateRuleRequest) (*case_management.UpdateRuleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRule", arg0, arg1)
	ret0, _ := ret[0].(*case_management.UpdateRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRule indicates an expected call of UpdateRule.
func (mr *MockCaseManagementServerMockRecorder) UpdateRule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRule", reflect.TypeOf((*MockCaseManagementServer)(nil).UpdateRule), arg0, arg1)
}

// MockUnsafeCaseManagementServer is a mock of UnsafeCaseManagementServer interface.
type MockUnsafeCaseManagementServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCaseManagementServerMockRecorder
}

// MockUnsafeCaseManagementServerMockRecorder is the mock recorder for MockUnsafeCaseManagementServer.
type MockUnsafeCaseManagementServerMockRecorder struct {
	mock *MockUnsafeCaseManagementServer
}

// NewMockUnsafeCaseManagementServer creates a new mock instance.
func NewMockUnsafeCaseManagementServer(ctrl *gomock.Controller) *MockUnsafeCaseManagementServer {
	mock := &MockUnsafeCaseManagementServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCaseManagementServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCaseManagementServer) EXPECT() *MockUnsafeCaseManagementServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCaseManagementServer mocks base method.
func (m *MockUnsafeCaseManagementServer) mustEmbedUnimplementedCaseManagementServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCaseManagementServer")
}

// mustEmbedUnimplementedCaseManagementServer indicates an expected call of mustEmbedUnimplementedCaseManagementServer.
func (mr *MockUnsafeCaseManagementServerMockRecorder) mustEmbedUnimplementedCaseManagementServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCaseManagementServer", reflect.TypeOf((*MockUnsafeCaseManagementServer)(nil).mustEmbedUnimplementedCaseManagementServer))
}
