// Code generated by MockGen. DO NOT EDIT.
// Source: api/risk/case_management/consumer_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	event "github.com/epifi/gamma/api/cx/call_routing/event"
	case_management "github.com/epifi/gamma/api/risk/case_management"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockRiskCaseManagementConsumerClient is a mock of RiskCaseManagementConsumerClient interface.
type MockRiskCaseManagementConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockRiskCaseManagementConsumerClientMockRecorder
}

// MockRiskCaseManagementConsumerClientMockRecorder is the mock recorder for MockRiskCaseManagementConsumerClient.
type MockRiskCaseManagementConsumerClientMockRecorder struct {
	mock *MockRiskCaseManagementConsumerClient
}

// NewMockRiskCaseManagementConsumerClient creates a new mock instance.
func NewMockRiskCaseManagementConsumerClient(ctrl *gomock.Controller) *MockRiskCaseManagementConsumerClient {
	mock := &MockRiskCaseManagementConsumerClient{ctrl: ctrl}
	mock.recorder = &MockRiskCaseManagementConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskCaseManagementConsumerClient) EXPECT() *MockRiskCaseManagementConsumerClientMockRecorder {
	return m.recorder
}

// AddAlerts mocks base method.
func (m *MockRiskCaseManagementConsumerClient) AddAlerts(ctx context.Context, in *case_management.FrmIngestAlertsEvent, opts ...grpc.CallOption) (*case_management.AddAlertsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddAlerts", varargs...)
	ret0, _ := ret[0].(*case_management.AddAlertsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAlerts indicates an expected call of AddAlerts.
func (mr *MockRiskCaseManagementConsumerClientMockRecorder) AddAlerts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAlerts", reflect.TypeOf((*MockRiskCaseManagementConsumerClient)(nil).AddAlerts), varargs...)
}

// AddCases mocks base method.
func (m *MockRiskCaseManagementConsumerClient) AddCases(ctx context.Context, in *case_management.RiskCasesIngestEvent, opts ...grpc.CallOption) (*case_management.AddCasesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddCases", varargs...)
	ret0, _ := ret[0].(*case_management.AddCasesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddCases indicates an expected call of AddCases.
func (mr *MockRiskCaseManagementConsumerClientMockRecorder) AddCases(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCases", reflect.TypeOf((*MockRiskCaseManagementConsumerClient)(nil).AddCases), varargs...)
}

// ProcessBatchRuleEngineEvent mocks base method.
func (m *MockRiskCaseManagementConsumerClient) ProcessBatchRuleEngineEvent(ctx context.Context, in *case_management.BatchRuleEngineEvent, opts ...grpc.CallOption) (*case_management.ProcessBatchRuleEngineEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessBatchRuleEngineEvent", varargs...)
	ret0, _ := ret[0].(*case_management.ProcessBatchRuleEngineEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessBatchRuleEngineEvent indicates an expected call of ProcessBatchRuleEngineEvent.
func (mr *MockRiskCaseManagementConsumerClientMockRecorder) ProcessBatchRuleEngineEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessBatchRuleEngineEvent", reflect.TypeOf((*MockRiskCaseManagementConsumerClient)(nil).ProcessBatchRuleEngineEvent), varargs...)
}

// ProcessCXTicketUpdateEvent mocks base method.
func (m *MockRiskCaseManagementConsumerClient) ProcessCXTicketUpdateEvent(ctx context.Context, in *case_management.CXTicketUpdateEvent, opts ...grpc.CallOption) (*case_management.ProcessCXTicketUpdateEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCXTicketUpdateEvent", varargs...)
	ret0, _ := ret[0].(*case_management.ProcessCXTicketUpdateEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCXTicketUpdateEvent indicates an expected call of ProcessCXTicketUpdateEvent.
func (mr *MockRiskCaseManagementConsumerClientMockRecorder) ProcessCXTicketUpdateEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCXTicketUpdateEvent", reflect.TypeOf((*MockRiskCaseManagementConsumerClient)(nil).ProcessCXTicketUpdateEvent), varargs...)
}

// ProcessCallRoutingEvent mocks base method.
func (m *MockRiskCaseManagementConsumerClient) ProcessCallRoutingEvent(ctx context.Context, in *event.CallRoutingEvent, opts ...grpc.CallOption) (*case_management.ProcessCallRoutingEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCallRoutingEvent", varargs...)
	ret0, _ := ret[0].(*case_management.ProcessCallRoutingEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCallRoutingEvent indicates an expected call of ProcessCallRoutingEvent.
func (mr *MockRiskCaseManagementConsumerClientMockRecorder) ProcessCallRoutingEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCallRoutingEvent", reflect.TypeOf((*MockRiskCaseManagementConsumerClient)(nil).ProcessCallRoutingEvent), varargs...)
}

// ProcessFormSubmission mocks base method.
func (m *MockRiskCaseManagementConsumerClient) ProcessFormSubmission(ctx context.Context, in *case_management.FormSubmissionEvent, opts ...grpc.CallOption) (*case_management.ProcessFormSubmissionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessFormSubmission", varargs...)
	ret0, _ := ret[0].(*case_management.ProcessFormSubmissionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessFormSubmission indicates an expected call of ProcessFormSubmission.
func (mr *MockRiskCaseManagementConsumerClientMockRecorder) ProcessFormSubmission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessFormSubmission", reflect.TypeOf((*MockRiskCaseManagementConsumerClient)(nil).ProcessFormSubmission), varargs...)
}

// ProcessRiskAlertEvent mocks base method.
func (m *MockRiskCaseManagementConsumerClient) ProcessRiskAlertEvent(ctx context.Context, in *case_management.RiskSignalIngestEvent, opts ...grpc.CallOption) (*case_management.RiskSignalIngestResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessRiskAlertEvent", varargs...)
	ret0, _ := ret[0].(*case_management.RiskSignalIngestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessRiskAlertEvent indicates an expected call of ProcessRiskAlertEvent.
func (mr *MockRiskCaseManagementConsumerClientMockRecorder) ProcessRiskAlertEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessRiskAlertEvent", reflect.TypeOf((*MockRiskCaseManagementConsumerClient)(nil).ProcessRiskAlertEvent), varargs...)
}

// ProcessRiskSignalEvent mocks base method.
func (m *MockRiskCaseManagementConsumerClient) ProcessRiskSignalEvent(ctx context.Context, in *case_management.RiskSignalIngestEvent, opts ...grpc.CallOption) (*case_management.RiskSignalIngestResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessRiskSignalEvent", varargs...)
	ret0, _ := ret[0].(*case_management.RiskSignalIngestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessRiskSignalEvent indicates an expected call of ProcessRiskSignalEvent.
func (mr *MockRiskCaseManagementConsumerClientMockRecorder) ProcessRiskSignalEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessRiskSignalEvent", reflect.TypeOf((*MockRiskCaseManagementConsumerClient)(nil).ProcessRiskSignalEvent), varargs...)
}

// MockRiskCaseManagementConsumerServer is a mock of RiskCaseManagementConsumerServer interface.
type MockRiskCaseManagementConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockRiskCaseManagementConsumerServerMockRecorder
}

// MockRiskCaseManagementConsumerServerMockRecorder is the mock recorder for MockRiskCaseManagementConsumerServer.
type MockRiskCaseManagementConsumerServerMockRecorder struct {
	mock *MockRiskCaseManagementConsumerServer
}

// NewMockRiskCaseManagementConsumerServer creates a new mock instance.
func NewMockRiskCaseManagementConsumerServer(ctrl *gomock.Controller) *MockRiskCaseManagementConsumerServer {
	mock := &MockRiskCaseManagementConsumerServer{ctrl: ctrl}
	mock.recorder = &MockRiskCaseManagementConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskCaseManagementConsumerServer) EXPECT() *MockRiskCaseManagementConsumerServerMockRecorder {
	return m.recorder
}

// AddAlerts mocks base method.
func (m *MockRiskCaseManagementConsumerServer) AddAlerts(arg0 context.Context, arg1 *case_management.FrmIngestAlertsEvent) (*case_management.AddAlertsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAlerts", arg0, arg1)
	ret0, _ := ret[0].(*case_management.AddAlertsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAlerts indicates an expected call of AddAlerts.
func (mr *MockRiskCaseManagementConsumerServerMockRecorder) AddAlerts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAlerts", reflect.TypeOf((*MockRiskCaseManagementConsumerServer)(nil).AddAlerts), arg0, arg1)
}

// AddCases mocks base method.
func (m *MockRiskCaseManagementConsumerServer) AddCases(arg0 context.Context, arg1 *case_management.RiskCasesIngestEvent) (*case_management.AddCasesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCases", arg0, arg1)
	ret0, _ := ret[0].(*case_management.AddCasesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddCases indicates an expected call of AddCases.
func (mr *MockRiskCaseManagementConsumerServerMockRecorder) AddCases(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCases", reflect.TypeOf((*MockRiskCaseManagementConsumerServer)(nil).AddCases), arg0, arg1)
}

// ProcessBatchRuleEngineEvent mocks base method.
func (m *MockRiskCaseManagementConsumerServer) ProcessBatchRuleEngineEvent(arg0 context.Context, arg1 *case_management.BatchRuleEngineEvent) (*case_management.ProcessBatchRuleEngineEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessBatchRuleEngineEvent", arg0, arg1)
	ret0, _ := ret[0].(*case_management.ProcessBatchRuleEngineEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessBatchRuleEngineEvent indicates an expected call of ProcessBatchRuleEngineEvent.
func (mr *MockRiskCaseManagementConsumerServerMockRecorder) ProcessBatchRuleEngineEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessBatchRuleEngineEvent", reflect.TypeOf((*MockRiskCaseManagementConsumerServer)(nil).ProcessBatchRuleEngineEvent), arg0, arg1)
}

// ProcessCXTicketUpdateEvent mocks base method.
func (m *MockRiskCaseManagementConsumerServer) ProcessCXTicketUpdateEvent(arg0 context.Context, arg1 *case_management.CXTicketUpdateEvent) (*case_management.ProcessCXTicketUpdateEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCXTicketUpdateEvent", arg0, arg1)
	ret0, _ := ret[0].(*case_management.ProcessCXTicketUpdateEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCXTicketUpdateEvent indicates an expected call of ProcessCXTicketUpdateEvent.
func (mr *MockRiskCaseManagementConsumerServerMockRecorder) ProcessCXTicketUpdateEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCXTicketUpdateEvent", reflect.TypeOf((*MockRiskCaseManagementConsumerServer)(nil).ProcessCXTicketUpdateEvent), arg0, arg1)
}

// ProcessCallRoutingEvent mocks base method.
func (m *MockRiskCaseManagementConsumerServer) ProcessCallRoutingEvent(arg0 context.Context, arg1 *event.CallRoutingEvent) (*case_management.ProcessCallRoutingEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCallRoutingEvent", arg0, arg1)
	ret0, _ := ret[0].(*case_management.ProcessCallRoutingEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCallRoutingEvent indicates an expected call of ProcessCallRoutingEvent.
func (mr *MockRiskCaseManagementConsumerServerMockRecorder) ProcessCallRoutingEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCallRoutingEvent", reflect.TypeOf((*MockRiskCaseManagementConsumerServer)(nil).ProcessCallRoutingEvent), arg0, arg1)
}

// ProcessFormSubmission mocks base method.
func (m *MockRiskCaseManagementConsumerServer) ProcessFormSubmission(arg0 context.Context, arg1 *case_management.FormSubmissionEvent) (*case_management.ProcessFormSubmissionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessFormSubmission", arg0, arg1)
	ret0, _ := ret[0].(*case_management.ProcessFormSubmissionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessFormSubmission indicates an expected call of ProcessFormSubmission.
func (mr *MockRiskCaseManagementConsumerServerMockRecorder) ProcessFormSubmission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessFormSubmission", reflect.TypeOf((*MockRiskCaseManagementConsumerServer)(nil).ProcessFormSubmission), arg0, arg1)
}

// ProcessRiskAlertEvent mocks base method.
func (m *MockRiskCaseManagementConsumerServer) ProcessRiskAlertEvent(arg0 context.Context, arg1 *case_management.RiskSignalIngestEvent) (*case_management.RiskSignalIngestResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessRiskAlertEvent", arg0, arg1)
	ret0, _ := ret[0].(*case_management.RiskSignalIngestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessRiskAlertEvent indicates an expected call of ProcessRiskAlertEvent.
func (mr *MockRiskCaseManagementConsumerServerMockRecorder) ProcessRiskAlertEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessRiskAlertEvent", reflect.TypeOf((*MockRiskCaseManagementConsumerServer)(nil).ProcessRiskAlertEvent), arg0, arg1)
}

// ProcessRiskSignalEvent mocks base method.
func (m *MockRiskCaseManagementConsumerServer) ProcessRiskSignalEvent(arg0 context.Context, arg1 *case_management.RiskSignalIngestEvent) (*case_management.RiskSignalIngestResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessRiskSignalEvent", arg0, arg1)
	ret0, _ := ret[0].(*case_management.RiskSignalIngestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessRiskSignalEvent indicates an expected call of ProcessRiskSignalEvent.
func (mr *MockRiskCaseManagementConsumerServerMockRecorder) ProcessRiskSignalEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessRiskSignalEvent", reflect.TypeOf((*MockRiskCaseManagementConsumerServer)(nil).ProcessRiskSignalEvent), arg0, arg1)
}

// MockUnsafeRiskCaseManagementConsumerServer is a mock of UnsafeRiskCaseManagementConsumerServer interface.
type MockUnsafeRiskCaseManagementConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeRiskCaseManagementConsumerServerMockRecorder
}

// MockUnsafeRiskCaseManagementConsumerServerMockRecorder is the mock recorder for MockUnsafeRiskCaseManagementConsumerServer.
type MockUnsafeRiskCaseManagementConsumerServerMockRecorder struct {
	mock *MockUnsafeRiskCaseManagementConsumerServer
}

// NewMockUnsafeRiskCaseManagementConsumerServer creates a new mock instance.
func NewMockUnsafeRiskCaseManagementConsumerServer(ctrl *gomock.Controller) *MockUnsafeRiskCaseManagementConsumerServer {
	mock := &MockUnsafeRiskCaseManagementConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeRiskCaseManagementConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeRiskCaseManagementConsumerServer) EXPECT() *MockUnsafeRiskCaseManagementConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedRiskCaseManagementConsumerServer mocks base method.
func (m *MockUnsafeRiskCaseManagementConsumerServer) mustEmbedUnimplementedRiskCaseManagementConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedRiskCaseManagementConsumerServer")
}

// mustEmbedUnimplementedRiskCaseManagementConsumerServer indicates an expected call of mustEmbedUnimplementedRiskCaseManagementConsumerServer.
func (mr *MockUnsafeRiskCaseManagementConsumerServerMockRecorder) mustEmbedUnimplementedRiskCaseManagementConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedRiskCaseManagementConsumerServer", reflect.TypeOf((*MockUnsafeRiskCaseManagementConsumerServer)(nil).mustEmbedUnimplementedRiskCaseManagementConsumerServer))
}
