//go:generate gen_sql -types=QuestionOptions,MultiChoiceOptions,StoredConditionalQuestions,TextOptions,FileOptions

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/form/question.proto

package form

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Question stores templates for questions that can be sent to users in questionnaire.
// On question update, a new record will be created with the same code and the version will be increased by 1.
// i.e., question can be uniquely identified by code and version.
type Question struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Limited length code to identify a question.
	// Remains constant throughout a question's lifecycle.
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	// Versioning on incremental question updates.
	Version int32 `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	// Question text
	Text string `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	// Placeholder for question response.
	Placeholder string `protobuf:"bytes,5,opt,name=placeholder,proto3" json:"placeholder,omitempty"`
	// Optional help text to be shown with question.
	Tip string `protobuf:"bytes,6,opt,name=tip,proto3" json:"tip,omitempty"`
	// Optional description for internal use.
	Description string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	// Specifies type of question for type of response accepted by question.
	// A question type might be multi choice, text or file upload.
	Type QuestionType `protobuf:"varint,8,opt,name=type,proto3,enum=risk.case_management.form.QuestionType" json:"type,omitempty"`
	// Optional params varying by question type.
	// It can store validations on response or additional params required for a question type.
	Options   *QuestionOptions       `protobuf:"bytes,9,opt,name=options,proto3" json:"options,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *Question) Reset() {
	*x = Question{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Question) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Question) ProtoMessage() {}

func (x *Question) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Question.ProtoReflect.Descriptor instead.
func (*Question) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{0}
}

func (x *Question) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Question) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Question) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *Question) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *Question) GetPlaceholder() string {
	if x != nil {
		return x.Placeholder
	}
	return ""
}

func (x *Question) GetTip() string {
	if x != nil {
		return x.Tip
	}
	return ""
}

func (x *Question) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Question) GetType() QuestionType {
	if x != nil {
		return x.Type
	}
	return QuestionType_QUESTION_TYPE_UNSPECIFIED
}

func (x *Question) GetOptions() *QuestionOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *Question) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Question) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Question) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

// QuestionEntityMapping is a mapping table between question code and entities.
// Ordering between questions is only defined on entity and entity id level.
type QuestionEntityMapping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Mapped question code
	QuestionCode string `protobuf:"bytes,2,opt,name=question_code,json=questionCode,proto3" json:"question_code,omitempty"`
	// Entity type of the mapping required
	EntityType EntityType `protobuf:"varint,3,opt,name=entity_type,json=entityType,proto3,enum=risk.case_management.form.EntityType" json:"entity_type,omitempty"`
	// Entity id of the mapping
	EntityId string `protobuf:"bytes,5,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	// Entity level score to order questions.
	EntityOrderScore int32 `protobuf:"varint,6,opt,name=entity_order_score,json=entityOrderScore,proto3" json:"entity_order_score,omitempty"`
	// Indicates whether a question is mandatory wrt an entity.
	IsMandatory bool                   `protobuf:"varint,7,opt,name=is_mandatory,json=isMandatory,proto3" json:"is_mandatory,omitempty"`
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt   *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt   *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *QuestionEntityMapping) Reset() {
	*x = QuestionEntityMapping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionEntityMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionEntityMapping) ProtoMessage() {}

func (x *QuestionEntityMapping) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionEntityMapping.ProtoReflect.Descriptor instead.
func (*QuestionEntityMapping) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{1}
}

func (x *QuestionEntityMapping) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *QuestionEntityMapping) GetQuestionCode() string {
	if x != nil {
		return x.QuestionCode
	}
	return ""
}

func (x *QuestionEntityMapping) GetEntityType() EntityType {
	if x != nil {
		return x.EntityType
	}
	return EntityType_ENTITY_TYPE_UNSPECIFIED
}

func (x *QuestionEntityMapping) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

func (x *QuestionEntityMapping) GetEntityOrderScore() int32 {
	if x != nil {
		return x.EntityOrderScore
	}
	return 0
}

func (x *QuestionEntityMapping) GetIsMandatory() bool {
	if x != nil {
		return x.IsMandatory
	}
	return false
}

func (x *QuestionEntityMapping) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *QuestionEntityMapping) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *QuestionEntityMapping) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

type QuestionOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Options:
	//
	//	*QuestionOptions_MultiChoice
	//	*QuestionOptions_FileOptions
	//	*QuestionOptions_TextOptions
	Options isQuestionOptions_Options `protobuf_oneof:"Options"`
}

func (x *QuestionOptions) Reset() {
	*x = QuestionOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionOptions) ProtoMessage() {}

func (x *QuestionOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionOptions.ProtoReflect.Descriptor instead.
func (*QuestionOptions) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{2}
}

func (m *QuestionOptions) GetOptions() isQuestionOptions_Options {
	if m != nil {
		return m.Options
	}
	return nil
}

func (x *QuestionOptions) GetMultiChoice() *MultiChoiceOptions {
	if x, ok := x.GetOptions().(*QuestionOptions_MultiChoice); ok {
		return x.MultiChoice
	}
	return nil
}

func (x *QuestionOptions) GetFileOptions() *FileOptions {
	if x, ok := x.GetOptions().(*QuestionOptions_FileOptions); ok {
		return x.FileOptions
	}
	return nil
}

func (x *QuestionOptions) GetTextOptions() *TextOptions {
	if x, ok := x.GetOptions().(*QuestionOptions_TextOptions); ok {
		return x.TextOptions
	}
	return nil
}

type isQuestionOptions_Options interface {
	isQuestionOptions_Options()
}

type QuestionOptions_MultiChoice struct {
	MultiChoice *MultiChoiceOptions `protobuf:"bytes,1,opt,name=multi_choice,json=multiChoice,proto3,oneof"`
}

type QuestionOptions_FileOptions struct {
	FileOptions *FileOptions `protobuf:"bytes,2,opt,name=file_options,json=fileOptions,proto3,oneof"`
}

type QuestionOptions_TextOptions struct {
	TextOptions *TextOptions `protobuf:"bytes,3,opt,name=text_options,json=textOptions,proto3,oneof"`
}

func (*QuestionOptions_MultiChoice) isQuestionOptions_Options() {}

func (*QuestionOptions_FileOptions) isQuestionOptions_Options() {}

func (*QuestionOptions_TextOptions) isQuestionOptions_Options() {}

type MultiChoiceOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// choices in multi choice question type mapped against conditional questions.
	ChoiceConditionalQuestionsMap map[string]*StoredConditionalQuestions `protobuf:"bytes,1,rep,name=choice_conditional_questions_map,json=choiceConditionalQuestionsMap,proto3" json:"choice_conditional_questions_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	IsMultiSelect                 bool                                   `protobuf:"varint,2,opt,name=is_multi_select,json=isMultiSelect,proto3" json:"is_multi_select,omitempty"`
	// It is intended for deterministic ordering of choices.
	Choices []string `protobuf:"bytes,3,rep,name=choices,proto3" json:"choices,omitempty"`
}

func (x *MultiChoiceOptions) Reset() {
	*x = MultiChoiceOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiChoiceOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiChoiceOptions) ProtoMessage() {}

func (x *MultiChoiceOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiChoiceOptions.ProtoReflect.Descriptor instead.
func (*MultiChoiceOptions) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{3}
}

func (x *MultiChoiceOptions) GetChoiceConditionalQuestionsMap() map[string]*StoredConditionalQuestions {
	if x != nil {
		return x.ChoiceConditionalQuestionsMap
	}
	return nil
}

func (x *MultiChoiceOptions) GetIsMultiSelect() bool {
	if x != nil {
		return x.IsMultiSelect
	}
	return false
}

func (x *MultiChoiceOptions) GetChoices() []string {
	if x != nil {
		return x.Choices
	}
	return nil
}

type FileOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AllowedContentTypes []FileContentType `protobuf:"varint,1,rep,packed,name=allowed_content_types,json=allowedContentTypes,proto3,enum=risk.case_management.form.FileContentType" json:"allowed_content_types,omitempty"`
}

func (x *FileOptions) Reset() {
	*x = FileOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileOptions) ProtoMessage() {}

func (x *FileOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileOptions.ProtoReflect.Descriptor instead.
func (*FileOptions) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{4}
}

func (x *FileOptions) GetAllowedContentTypes() []FileContentType {
	if x != nil {
		return x.AllowedContentTypes
	}
	return nil
}

type TextOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Max characters limit for text response.
	MaxCharsLimit uint32 `protobuf:"varint,1,opt,name=max_chars_limit,json=maxCharsLimit,proto3" json:"max_chars_limit,omitempty"`
}

func (x *TextOptions) Reset() {
	*x = TextOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextOptions) ProtoMessage() {}

func (x *TextOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextOptions.ProtoReflect.Descriptor instead.
func (*TextOptions) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{5}
}

func (x *TextOptions) GetMaxCharsLimit() uint32 {
	if x != nil {
		return x.MaxCharsLimit
	}
	return 0
}

// ExtendedQuestion is enriched with additional info such as conditional questions.
type ExtendedQuestion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Question             *Question                                         `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	IsMandatory          bool                                              `protobuf:"varint,2,opt,name=is_mandatory,json=isMandatory,proto3" json:"is_mandatory,omitempty"`
	ConditionalQuestions map[string]*ExtendedQuestion_ConditionalQuestions `protobuf:"bytes,3,rep,name=conditional_questions,json=conditionalQuestions,proto3" json:"conditional_questions,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ExtendedQuestion) Reset() {
	*x = ExtendedQuestion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtendedQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtendedQuestion) ProtoMessage() {}

func (x *ExtendedQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtendedQuestion.ProtoReflect.Descriptor instead.
func (*ExtendedQuestion) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{6}
}

func (x *ExtendedQuestion) GetQuestion() *Question {
	if x != nil {
		return x.Question
	}
	return nil
}

func (x *ExtendedQuestion) GetIsMandatory() bool {
	if x != nil {
		return x.IsMandatory
	}
	return false
}

func (x *ExtendedQuestion) GetConditionalQuestions() map[string]*ExtendedQuestion_ConditionalQuestions {
	if x != nil {
		return x.ConditionalQuestions
	}
	return nil
}

// StoredConditionalQuestions is used to store conditional questions against choices of multi choice questions.
type StoredConditionalQuestions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Questions []*StoredConditionalQuestions_ConditionalQuestion `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
}

func (x *StoredConditionalQuestions) Reset() {
	*x = StoredConditionalQuestions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoredConditionalQuestions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoredConditionalQuestions) ProtoMessage() {}

func (x *StoredConditionalQuestions) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoredConditionalQuestions.ProtoReflect.Descriptor instead.
func (*StoredConditionalQuestions) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{7}
}

func (x *StoredConditionalQuestions) GetQuestions() []*StoredConditionalQuestions_ConditionalQuestion {
	if x != nil {
		return x.Questions
	}
	return nil
}

type QuestionIdentifiers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifiers:
	//
	//	*QuestionIdentifiers_EntityIdentifiers
	//	*QuestionIdentifiers_QuestionIdentifiers
	Identifiers isQuestionIdentifiers_Identifiers `protobuf_oneof:"identifiers"`
}

func (x *QuestionIdentifiers) Reset() {
	*x = QuestionIdentifiers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionIdentifiers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionIdentifiers) ProtoMessage() {}

func (x *QuestionIdentifiers) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionIdentifiers.ProtoReflect.Descriptor instead.
func (*QuestionIdentifiers) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{8}
}

func (m *QuestionIdentifiers) GetIdentifiers() isQuestionIdentifiers_Identifiers {
	if m != nil {
		return m.Identifiers
	}
	return nil
}

func (x *QuestionIdentifiers) GetEntityIdentifiers() *EntityIdentifiers {
	if x, ok := x.GetIdentifiers().(*QuestionIdentifiers_EntityIdentifiers); ok {
		return x.EntityIdentifiers
	}
	return nil
}

func (x *QuestionIdentifiers) GetQuestionIdentifiers() *QuestionIdentifiers {
	if x, ok := x.GetIdentifiers().(*QuestionIdentifiers_QuestionIdentifiers); ok {
		return x.QuestionIdentifiers
	}
	return nil
}

type isQuestionIdentifiers_Identifiers interface {
	isQuestionIdentifiers_Identifiers()
}

type QuestionIdentifiers_EntityIdentifiers struct {
	EntityIdentifiers *EntityIdentifiers `protobuf:"bytes,1,opt,name=entity_identifiers,json=entityIdentifiers,proto3,oneof"`
}

type QuestionIdentifiers_QuestionIdentifiers struct {
	QuestionIdentifiers *QuestionIdentifiers `protobuf:"bytes,2,opt,name=question_identifiers,json=questionIdentifiers,proto3,oneof"`
}

func (*QuestionIdentifiers_EntityIdentifiers) isQuestionIdentifiers_Identifiers() {}

func (*QuestionIdentifiers_QuestionIdentifiers) isQuestionIdentifiers_Identifiers() {}

type EntityIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type EntityType `protobuf:"varint,1,opt,name=type,proto3,enum=risk.case_management.form.EntityType" json:"type,omitempty"`
	Id   string     `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *EntityIdentifier) Reset() {
	*x = EntityIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntityIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityIdentifier) ProtoMessage() {}

func (x *EntityIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityIdentifier.ProtoReflect.Descriptor instead.
func (*EntityIdentifier) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{9}
}

func (x *EntityIdentifier) GetType() EntityType {
	if x != nil {
		return x.Type
	}
	return EntityType_ENTITY_TYPE_UNSPECIFIED
}

func (x *EntityIdentifier) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type EntityIdentifiers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identifiers []*EntityIdentifier `protobuf:"bytes,1,rep,name=identifiers,proto3" json:"identifiers,omitempty"`
}

func (x *EntityIdentifiers) Reset() {
	*x = EntityIdentifiers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntityIdentifiers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityIdentifiers) ProtoMessage() {}

func (x *EntityIdentifiers) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityIdentifiers.ProtoReflect.Descriptor instead.
func (*EntityIdentifiers) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{10}
}

func (x *EntityIdentifiers) GetIdentifiers() []*EntityIdentifier {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

type QuestionCodeIdentifiers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identifiers []*QuestionCodeIdentifier `protobuf:"bytes,1,rep,name=identifiers,proto3" json:"identifiers,omitempty"`
}

func (x *QuestionCodeIdentifiers) Reset() {
	*x = QuestionCodeIdentifiers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionCodeIdentifiers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionCodeIdentifiers) ProtoMessage() {}

func (x *QuestionCodeIdentifiers) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionCodeIdentifiers.ProtoReflect.Descriptor instead.
func (*QuestionCodeIdentifiers) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{11}
}

func (x *QuestionCodeIdentifiers) GetIdentifiers() []*QuestionCodeIdentifier {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

type QuestionCodeIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	IsMandatory bool   `protobuf:"varint,2,opt,name=is_mandatory,json=isMandatory,proto3" json:"is_mandatory,omitempty"`
}

func (x *QuestionCodeIdentifier) Reset() {
	*x = QuestionCodeIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionCodeIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionCodeIdentifier) ProtoMessage() {}

func (x *QuestionCodeIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionCodeIdentifier.ProtoReflect.Descriptor instead.
func (*QuestionCodeIdentifier) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{12}
}

func (x *QuestionCodeIdentifier) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *QuestionCodeIdentifier) GetIsMandatory() bool {
	if x != nil {
		return x.IsMandatory
	}
	return false
}

type ExtendedQuestion_ConditionalQuestions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Questions []*ExtendedQuestion `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
}

func (x *ExtendedQuestion_ConditionalQuestions) Reset() {
	*x = ExtendedQuestion_ConditionalQuestions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtendedQuestion_ConditionalQuestions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtendedQuestion_ConditionalQuestions) ProtoMessage() {}

func (x *ExtendedQuestion_ConditionalQuestions) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtendedQuestion_ConditionalQuestions.ProtoReflect.Descriptor instead.
func (*ExtendedQuestion_ConditionalQuestions) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{6, 1}
}

func (x *ExtendedQuestion_ConditionalQuestions) GetQuestions() []*ExtendedQuestion {
	if x != nil {
		return x.Questions
	}
	return nil
}

type StoredConditionalQuestions_ConditionalQuestion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestionCode string `protobuf:"bytes,1,opt,name=question_code,json=questionCode,proto3" json:"question_code,omitempty"`
	IsMandatory  bool   `protobuf:"varint,2,opt,name=is_mandatory,json=isMandatory,proto3" json:"is_mandatory,omitempty"`
}

func (x *StoredConditionalQuestions_ConditionalQuestion) Reset() {
	*x = StoredConditionalQuestions_ConditionalQuestion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_question_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoredConditionalQuestions_ConditionalQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoredConditionalQuestions_ConditionalQuestion) ProtoMessage() {}

func (x *StoredConditionalQuestions_ConditionalQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_question_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoredConditionalQuestions_ConditionalQuestion.ProtoReflect.Descriptor instead.
func (*StoredConditionalQuestions_ConditionalQuestion) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_question_proto_rawDescGZIP(), []int{7, 0}
}

func (x *StoredConditionalQuestions_ConditionalQuestion) GetQuestionCode() string {
	if x != nil {
		return x.QuestionCode
	}
	return ""
}

func (x *StoredConditionalQuestions_ConditionalQuestion) GetIsMandatory() bool {
	if x != nil {
		return x.IsMandatory
	}
	return false
}

var File_api_risk_case_management_form_question_proto protoreflect.FileDescriptor

var file_api_risk_case_management_form_question_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x82,
	0x04, 0x0a, 0x08, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12,
	0x20, 0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x74, 0x69, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x22, 0xda, 0x03, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a,
	0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x50, 0x0a, 0x0b, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a,
	0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x12, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x10, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x12,
	0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x22, 0x8a, 0x02, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x52, 0x0a, 0x0c, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x5f, 0x63, 0x68,
	0x6f, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x68, 0x6f, 0x69,
	0x63, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x48, 0x00, 0x52, 0x0b, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x4b, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x48, 0x00, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4b, 0x0a, 0x0c, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x48, 0x00, 0x52, 0x0b, 0x74, 0x65, 0x78, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xfc, 0x02,
	0x0a, 0x12, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x99, 0x01, 0x0a, 0x20, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x50, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x4d, 0x75, 0x6c, 0x74,
	0x69, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x43,
	0x68, 0x6f, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x1d, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70,
	0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x5f, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x4d, 0x75, 0x6c,
	0x74, 0x69, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x6f, 0x69,
	0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x6f, 0x69, 0x63,
	0x65, 0x73, 0x1a, 0x87, 0x01, 0x0a, 0x22, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4b, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x64, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x6d, 0x0a, 0x0b,
	0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x5e, 0x0a, 0x15, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x35, 0x0a, 0x0b, 0x54,
	0x65, 0x78, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x61,
	0x78, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x43, 0x68, 0x61, 0x72, 0x73, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x22, 0xe1, 0x03, 0x0a, 0x10, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x69, 0x73, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x7a, 0x0a, 0x15, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x14, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x89, 0x01, 0x0a, 0x19, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x56, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x61, 0x0a, 0x14, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x49, 0x0a, 0x09, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x64, 0x65, 0x64, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xf7, 0x01, 0x0a, 0x1a, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x64, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x71, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x09, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x66, 0x0a, 0x13, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2c, 0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79,
	0x22, 0xe8, 0x01, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x12, 0x5d, 0x0a, 0x12, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d,
	0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x73, 0x48, 0x00, 0x52, 0x11, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x12, 0x63, 0x0a, 0x14, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72,
	0x6d, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x13, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x42, 0x0d, 0x0a, 0x0b,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x22, 0x5d, 0x0a, 0x10, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12,
	0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x62, 0x0a, 0x11, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x12,
	0x4d, 0x0a, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d,
	0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x52, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x22, 0x6e,
	0x0a, 0x17, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x12, 0x53, 0x0a, 0x0b, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x52, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x22, 0x4f,
	0x0a, 0x16, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x42,
	0x6c, 0x0a, 0x34, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_form_question_proto_rawDescOnce sync.Once
	file_api_risk_case_management_form_question_proto_rawDescData = file_api_risk_case_management_form_question_proto_rawDesc
)

func file_api_risk_case_management_form_question_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_form_question_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_form_question_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_form_question_proto_rawDescData)
	})
	return file_api_risk_case_management_form_question_proto_rawDescData
}

var file_api_risk_case_management_form_question_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_api_risk_case_management_form_question_proto_goTypes = []interface{}{
	(*Question)(nil),                              // 0: risk.case_management.form.Question
	(*QuestionEntityMapping)(nil),                 // 1: risk.case_management.form.QuestionEntityMapping
	(*QuestionOptions)(nil),                       // 2: risk.case_management.form.QuestionOptions
	(*MultiChoiceOptions)(nil),                    // 3: risk.case_management.form.MultiChoiceOptions
	(*FileOptions)(nil),                           // 4: risk.case_management.form.FileOptions
	(*TextOptions)(nil),                           // 5: risk.case_management.form.TextOptions
	(*ExtendedQuestion)(nil),                      // 6: risk.case_management.form.ExtendedQuestion
	(*StoredConditionalQuestions)(nil),            // 7: risk.case_management.form.StoredConditionalQuestions
	(*QuestionIdentifiers)(nil),                   // 8: risk.case_management.form.QuestionIdentifiers
	(*EntityIdentifier)(nil),                      // 9: risk.case_management.form.EntityIdentifier
	(*EntityIdentifiers)(nil),                     // 10: risk.case_management.form.EntityIdentifiers
	(*QuestionCodeIdentifiers)(nil),               // 11: risk.case_management.form.QuestionCodeIdentifiers
	(*QuestionCodeIdentifier)(nil),                // 12: risk.case_management.form.QuestionCodeIdentifier
	nil,                                           // 13: risk.case_management.form.MultiChoiceOptions.ChoiceConditionalQuestionsMapEntry
	nil,                                           // 14: risk.case_management.form.ExtendedQuestion.ConditionalQuestionsEntry
	(*ExtendedQuestion_ConditionalQuestions)(nil), // 15: risk.case_management.form.ExtendedQuestion.ConditionalQuestions
	(*StoredConditionalQuestions_ConditionalQuestion)(nil), // 16: risk.case_management.form.StoredConditionalQuestions.ConditionalQuestion
	(QuestionType)(0),             // 17: risk.case_management.form.QuestionType
	(*timestamppb.Timestamp)(nil), // 18: google.protobuf.Timestamp
	(EntityType)(0),               // 19: risk.case_management.form.EntityType
	(FileContentType)(0),          // 20: risk.case_management.form.FileContentType
}
var file_api_risk_case_management_form_question_proto_depIdxs = []int32{
	17, // 0: risk.case_management.form.Question.type:type_name -> risk.case_management.form.QuestionType
	2,  // 1: risk.case_management.form.Question.options:type_name -> risk.case_management.form.QuestionOptions
	18, // 2: risk.case_management.form.Question.created_at:type_name -> google.protobuf.Timestamp
	18, // 3: risk.case_management.form.Question.updated_at:type_name -> google.protobuf.Timestamp
	18, // 4: risk.case_management.form.Question.deleted_at:type_name -> google.protobuf.Timestamp
	19, // 5: risk.case_management.form.QuestionEntityMapping.entity_type:type_name -> risk.case_management.form.EntityType
	18, // 6: risk.case_management.form.QuestionEntityMapping.created_at:type_name -> google.protobuf.Timestamp
	18, // 7: risk.case_management.form.QuestionEntityMapping.updated_at:type_name -> google.protobuf.Timestamp
	18, // 8: risk.case_management.form.QuestionEntityMapping.deleted_at:type_name -> google.protobuf.Timestamp
	3,  // 9: risk.case_management.form.QuestionOptions.multi_choice:type_name -> risk.case_management.form.MultiChoiceOptions
	4,  // 10: risk.case_management.form.QuestionOptions.file_options:type_name -> risk.case_management.form.FileOptions
	5,  // 11: risk.case_management.form.QuestionOptions.text_options:type_name -> risk.case_management.form.TextOptions
	13, // 12: risk.case_management.form.MultiChoiceOptions.choice_conditional_questions_map:type_name -> risk.case_management.form.MultiChoiceOptions.ChoiceConditionalQuestionsMapEntry
	20, // 13: risk.case_management.form.FileOptions.allowed_content_types:type_name -> risk.case_management.form.FileContentType
	0,  // 14: risk.case_management.form.ExtendedQuestion.question:type_name -> risk.case_management.form.Question
	14, // 15: risk.case_management.form.ExtendedQuestion.conditional_questions:type_name -> risk.case_management.form.ExtendedQuestion.ConditionalQuestionsEntry
	16, // 16: risk.case_management.form.StoredConditionalQuestions.questions:type_name -> risk.case_management.form.StoredConditionalQuestions.ConditionalQuestion
	10, // 17: risk.case_management.form.QuestionIdentifiers.entity_identifiers:type_name -> risk.case_management.form.EntityIdentifiers
	8,  // 18: risk.case_management.form.QuestionIdentifiers.question_identifiers:type_name -> risk.case_management.form.QuestionIdentifiers
	19, // 19: risk.case_management.form.EntityIdentifier.type:type_name -> risk.case_management.form.EntityType
	9,  // 20: risk.case_management.form.EntityIdentifiers.identifiers:type_name -> risk.case_management.form.EntityIdentifier
	12, // 21: risk.case_management.form.QuestionCodeIdentifiers.identifiers:type_name -> risk.case_management.form.QuestionCodeIdentifier
	7,  // 22: risk.case_management.form.MultiChoiceOptions.ChoiceConditionalQuestionsMapEntry.value:type_name -> risk.case_management.form.StoredConditionalQuestions
	15, // 23: risk.case_management.form.ExtendedQuestion.ConditionalQuestionsEntry.value:type_name -> risk.case_management.form.ExtendedQuestion.ConditionalQuestions
	6,  // 24: risk.case_management.form.ExtendedQuestion.ConditionalQuestions.questions:type_name -> risk.case_management.form.ExtendedQuestion
	25, // [25:25] is the sub-list for method output_type
	25, // [25:25] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_form_question_proto_init() }
func file_api_risk_case_management_form_question_proto_init() {
	if File_api_risk_case_management_form_question_proto != nil {
		return
	}
	file_api_risk_case_management_form_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_case_management_form_question_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Question); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_question_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuestionEntityMapping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_question_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuestionOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_question_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultiChoiceOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_question_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_question_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_question_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtendedQuestion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_question_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoredConditionalQuestions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_question_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuestionIdentifiers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_question_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EntityIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_question_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EntityIdentifiers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_question_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuestionCodeIdentifiers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_question_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuestionCodeIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_question_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtendedQuestion_ConditionalQuestions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_question_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoredConditionalQuestions_ConditionalQuestion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_case_management_form_question_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*QuestionOptions_MultiChoice)(nil),
		(*QuestionOptions_FileOptions)(nil),
		(*QuestionOptions_TextOptions)(nil),
	}
	file_api_risk_case_management_form_question_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*QuestionIdentifiers_EntityIdentifiers)(nil),
		(*QuestionIdentifiers_QuestionIdentifiers)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_form_question_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_case_management_form_question_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_form_question_proto_depIdxs,
		MessageInfos:      file_api_risk_case_management_form_question_proto_msgTypes,
	}.Build()
	File_api_risk_case_management_form_question_proto = out.File
	file_api_risk_case_management_form_question_proto_rawDesc = nil
	file_api_risk_case_management_form_question_proto_goTypes = nil
	file_api_risk_case_management_form_question_proto_depIdxs = nil
}
