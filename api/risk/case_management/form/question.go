package form

type QuestionEntityMappings []*QuestionEntityMapping
type FormQuestionMappings []*FormQuestionMapping
type ExtendedQuestions []*ExtendedQuestion

func (s *StoredConditionalQuestions) ToCodeIdentifiers() *QuestionCodeIdentifiers {
	var res = &QuestionCodeIdentifiers{}
	for _, question := range s.GetQuestions() {
		res.Identifiers = append(res.GetIdentifiers(), &QuestionCodeIdentifier{
			Code:        question.GetQuestionCode(),
			IsMandatory: question.GetIsMandatory(),
		})
	}
	return res
}

func (q QuestionEntityMappings) ToCodeIdentifiers() *QuestionCodeIdentifiers {
	var res = &QuestionCodeIdentifiers{}
	for _, question := range q {
		res.Identifiers = append(res.GetIdentifiers(), &QuestionCodeIdentifier{
			Code:        question.GetQuestionCode(),
			IsMandatory: question.GetIsMandatory(),
		})
	}
	return res
}

func (q FormQuestionMappings) ToCodeIdentifiers() *QuestionCodeIdentifiers {
	var res = &QuestionCodeIdentifiers{}
	for _, question := range q {
		res.Identifiers = append(res.GetIdentifiers(), &QuestionCodeIdentifier{
			Code:        question.GetQuestionCode(),
			IsMandatory: question.GetIsMandatory(),
		})
	}
	return res
}

func (e ExtendedQuestions) Flatten() []*ExtendedQuestion {
	var res = e
	for _, q := range e {
		for _, conditionalQuestion := range q.GetConditionalQuestions() {
			res = append(res, ExtendedQuestions(conditionalQuestion.GetQuestions()).Flatten()...)
		}
	}
	return res
}
