// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/case_management/form/question.proto

package form

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Question with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Question) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Question with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QuestionMultiError, or nil
// if none found.
func (m *Question) ValidateAll() error {
	return m.validate(true)
}

func (m *Question) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if utf8.RuneCountInString(m.GetCode()) < 1 {
		err := QuestionValidationError{
			field:  "Code",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Version

	if utf8.RuneCountInString(m.GetText()) < 1 {
		err := QuestionValidationError{
			field:  "Text",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Placeholder

	// no validation rules for Tip

	// no validation rules for Description

	if _, ok := _Question_Type_NotInLookup[m.GetType()]; ok {
		err := QuestionValidationError{
			field:  "Type",
			reason: "value must not be in list [QUESTION_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QuestionValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QuestionValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionValidationError{
				field:  "Options",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QuestionValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QuestionValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QuestionValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QuestionValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QuestionValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QuestionValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return QuestionMultiError(errors)
	}

	return nil
}

// QuestionMultiError is an error wrapping multiple validation errors returned
// by Question.ValidateAll() if the designated constraints aren't met.
type QuestionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionMultiError) AllErrors() []error { return m }

// QuestionValidationError is the validation error returned by
// Question.Validate if the designated constraints aren't met.
type QuestionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionValidationError) ErrorName() string { return "QuestionValidationError" }

// Error satisfies the builtin error interface
func (e QuestionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionValidationError{}

var _Question_Type_NotInLookup = map[QuestionType]struct{}{
	0: {},
}

// Validate checks the field values on QuestionEntityMapping with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionEntityMapping) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionEntityMapping with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionEntityMappingMultiError, or nil if none found.
func (m *QuestionEntityMapping) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionEntityMapping) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if utf8.RuneCountInString(m.GetQuestionCode()) < 1 {
		err := QuestionEntityMappingValidationError{
			field:  "QuestionCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _QuestionEntityMapping_EntityType_NotInLookup[m.GetEntityType()]; ok {
		err := QuestionEntityMappingValidationError{
			field:  "EntityType",
			reason: "value must not be in list [ENTITY_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetEntityId()) < 1 {
		err := QuestionEntityMappingValidationError{
			field:  "EntityId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetEntityOrderScore(); val < 0 || val > 100 {
		err := QuestionEntityMappingValidationError{
			field:  "EntityOrderScore",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IsMandatory

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QuestionEntityMappingValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QuestionEntityMappingValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionEntityMappingValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QuestionEntityMappingValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QuestionEntityMappingValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionEntityMappingValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QuestionEntityMappingValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QuestionEntityMappingValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionEntityMappingValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return QuestionEntityMappingMultiError(errors)
	}

	return nil
}

// QuestionEntityMappingMultiError is an error wrapping multiple validation
// errors returned by QuestionEntityMapping.ValidateAll() if the designated
// constraints aren't met.
type QuestionEntityMappingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionEntityMappingMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionEntityMappingMultiError) AllErrors() []error { return m }

// QuestionEntityMappingValidationError is the validation error returned by
// QuestionEntityMapping.Validate if the designated constraints aren't met.
type QuestionEntityMappingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionEntityMappingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionEntityMappingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionEntityMappingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionEntityMappingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionEntityMappingValidationError) ErrorName() string {
	return "QuestionEntityMappingValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionEntityMappingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionEntityMapping.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionEntityMappingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionEntityMappingValidationError{}

var _QuestionEntityMapping_EntityType_NotInLookup = map[EntityType]struct{}{
	0: {},
}

// Validate checks the field values on QuestionOptions with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *QuestionOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionOptionsMultiError, or nil if none found.
func (m *QuestionOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Options.(type) {
	case *QuestionOptions_MultiChoice:
		if v == nil {
			err := QuestionOptionsValidationError{
				field:  "Options",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMultiChoice()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QuestionOptionsValidationError{
						field:  "MultiChoice",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QuestionOptionsValidationError{
						field:  "MultiChoice",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMultiChoice()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QuestionOptionsValidationError{
					field:  "MultiChoice",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *QuestionOptions_FileOptions:
		if v == nil {
			err := QuestionOptionsValidationError{
				field:  "Options",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileOptions()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QuestionOptionsValidationError{
						field:  "FileOptions",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QuestionOptionsValidationError{
						field:  "FileOptions",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileOptions()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QuestionOptionsValidationError{
					field:  "FileOptions",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *QuestionOptions_TextOptions:
		if v == nil {
			err := QuestionOptionsValidationError{
				field:  "Options",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTextOptions()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QuestionOptionsValidationError{
						field:  "TextOptions",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QuestionOptionsValidationError{
						field:  "TextOptions",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTextOptions()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QuestionOptionsValidationError{
					field:  "TextOptions",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return QuestionOptionsMultiError(errors)
	}

	return nil
}

// QuestionOptionsMultiError is an error wrapping multiple validation errors
// returned by QuestionOptions.ValidateAll() if the designated constraints
// aren't met.
type QuestionOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionOptionsMultiError) AllErrors() []error { return m }

// QuestionOptionsValidationError is the validation error returned by
// QuestionOptions.Validate if the designated constraints aren't met.
type QuestionOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionOptionsValidationError) ErrorName() string { return "QuestionOptionsValidationError" }

// Error satisfies the builtin error interface
func (e QuestionOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionOptionsValidationError{}

// Validate checks the field values on MultiChoiceOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MultiChoiceOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MultiChoiceOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MultiChoiceOptionsMultiError, or nil if none found.
func (m *MultiChoiceOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *MultiChoiceOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetChoiceConditionalQuestionsMap()))
		i := 0
		for key := range m.GetChoiceConditionalQuestionsMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetChoiceConditionalQuestionsMap()[key]
			_ = val

			// no validation rules for ChoiceConditionalQuestionsMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, MultiChoiceOptionsValidationError{
							field:  fmt.Sprintf("ChoiceConditionalQuestionsMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, MultiChoiceOptionsValidationError{
							field:  fmt.Sprintf("ChoiceConditionalQuestionsMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return MultiChoiceOptionsValidationError{
						field:  fmt.Sprintf("ChoiceConditionalQuestionsMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for IsMultiSelect

	if len(errors) > 0 {
		return MultiChoiceOptionsMultiError(errors)
	}

	return nil
}

// MultiChoiceOptionsMultiError is an error wrapping multiple validation errors
// returned by MultiChoiceOptions.ValidateAll() if the designated constraints
// aren't met.
type MultiChoiceOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MultiChoiceOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MultiChoiceOptionsMultiError) AllErrors() []error { return m }

// MultiChoiceOptionsValidationError is the validation error returned by
// MultiChoiceOptions.Validate if the designated constraints aren't met.
type MultiChoiceOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MultiChoiceOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MultiChoiceOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MultiChoiceOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MultiChoiceOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MultiChoiceOptionsValidationError) ErrorName() string {
	return "MultiChoiceOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e MultiChoiceOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMultiChoiceOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MultiChoiceOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MultiChoiceOptionsValidationError{}

// Validate checks the field values on FileOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileOptionsMultiError, or
// nil if none found.
func (m *FileOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *FileOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return FileOptionsMultiError(errors)
	}

	return nil
}

// FileOptionsMultiError is an error wrapping multiple validation errors
// returned by FileOptions.ValidateAll() if the designated constraints aren't met.
type FileOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileOptionsMultiError) AllErrors() []error { return m }

// FileOptionsValidationError is the validation error returned by
// FileOptions.Validate if the designated constraints aren't met.
type FileOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileOptionsValidationError) ErrorName() string { return "FileOptionsValidationError" }

// Error satisfies the builtin error interface
func (e FileOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileOptionsValidationError{}

// Validate checks the field values on TextOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TextOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TextOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TextOptionsMultiError, or
// nil if none found.
func (m *TextOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *TextOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MaxCharsLimit

	if len(errors) > 0 {
		return TextOptionsMultiError(errors)
	}

	return nil
}

// TextOptionsMultiError is an error wrapping multiple validation errors
// returned by TextOptions.ValidateAll() if the designated constraints aren't met.
type TextOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TextOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TextOptionsMultiError) AllErrors() []error { return m }

// TextOptionsValidationError is the validation error returned by
// TextOptions.Validate if the designated constraints aren't met.
type TextOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TextOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TextOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TextOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TextOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TextOptionsValidationError) ErrorName() string { return "TextOptionsValidationError" }

// Error satisfies the builtin error interface
func (e TextOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTextOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TextOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TextOptionsValidationError{}

// Validate checks the field values on ExtendedQuestion with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExtendedQuestion) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExtendedQuestion with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExtendedQuestionMultiError, or nil if none found.
func (m *ExtendedQuestion) ValidateAll() error {
	return m.validate(true)
}

func (m *ExtendedQuestion) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetQuestion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExtendedQuestionValidationError{
					field:  "Question",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExtendedQuestionValidationError{
					field:  "Question",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuestion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExtendedQuestionValidationError{
				field:  "Question",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsMandatory

	{
		sorted_keys := make([]string, len(m.GetConditionalQuestions()))
		i := 0
		for key := range m.GetConditionalQuestions() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetConditionalQuestions()[key]
			_ = val

			// no validation rules for ConditionalQuestions[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ExtendedQuestionValidationError{
							field:  fmt.Sprintf("ConditionalQuestions[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ExtendedQuestionValidationError{
							field:  fmt.Sprintf("ConditionalQuestions[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ExtendedQuestionValidationError{
						field:  fmt.Sprintf("ConditionalQuestions[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return ExtendedQuestionMultiError(errors)
	}

	return nil
}

// ExtendedQuestionMultiError is an error wrapping multiple validation errors
// returned by ExtendedQuestion.ValidateAll() if the designated constraints
// aren't met.
type ExtendedQuestionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExtendedQuestionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExtendedQuestionMultiError) AllErrors() []error { return m }

// ExtendedQuestionValidationError is the validation error returned by
// ExtendedQuestion.Validate if the designated constraints aren't met.
type ExtendedQuestionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExtendedQuestionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExtendedQuestionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExtendedQuestionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExtendedQuestionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExtendedQuestionValidationError) ErrorName() string { return "ExtendedQuestionValidationError" }

// Error satisfies the builtin error interface
func (e ExtendedQuestionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExtendedQuestion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExtendedQuestionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExtendedQuestionValidationError{}

// Validate checks the field values on StoredConditionalQuestions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoredConditionalQuestions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoredConditionalQuestions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoredConditionalQuestionsMultiError, or nil if none found.
func (m *StoredConditionalQuestions) ValidateAll() error {
	return m.validate(true)
}

func (m *StoredConditionalQuestions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetQuestions()) < 1 {
		err := StoredConditionalQuestionsValidationError{
			field:  "Questions",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoredConditionalQuestionsValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoredConditionalQuestionsValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoredConditionalQuestionsValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoredConditionalQuestionsMultiError(errors)
	}

	return nil
}

// StoredConditionalQuestionsMultiError is an error wrapping multiple
// validation errors returned by StoredConditionalQuestions.ValidateAll() if
// the designated constraints aren't met.
type StoredConditionalQuestionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoredConditionalQuestionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoredConditionalQuestionsMultiError) AllErrors() []error { return m }

// StoredConditionalQuestionsValidationError is the validation error returned
// by StoredConditionalQuestions.Validate if the designated constraints aren't met.
type StoredConditionalQuestionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoredConditionalQuestionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoredConditionalQuestionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoredConditionalQuestionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoredConditionalQuestionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoredConditionalQuestionsValidationError) ErrorName() string {
	return "StoredConditionalQuestionsValidationError"
}

// Error satisfies the builtin error interface
func (e StoredConditionalQuestionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoredConditionalQuestions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoredConditionalQuestionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoredConditionalQuestionsValidationError{}

// Validate checks the field values on QuestionIdentifiers with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionIdentifiers) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionIdentifiers with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionIdentifiersMultiError, or nil if none found.
func (m *QuestionIdentifiers) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionIdentifiers) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifiers.(type) {
	case *QuestionIdentifiers_EntityIdentifiers:
		if v == nil {
			err := QuestionIdentifiersValidationError{
				field:  "Identifiers",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEntityIdentifiers()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QuestionIdentifiersValidationError{
						field:  "EntityIdentifiers",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QuestionIdentifiersValidationError{
						field:  "EntityIdentifiers",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEntityIdentifiers()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QuestionIdentifiersValidationError{
					field:  "EntityIdentifiers",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *QuestionIdentifiers_QuestionIdentifiers:
		if v == nil {
			err := QuestionIdentifiersValidationError{
				field:  "Identifiers",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetQuestionIdentifiers()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QuestionIdentifiersValidationError{
						field:  "QuestionIdentifiers",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QuestionIdentifiersValidationError{
						field:  "QuestionIdentifiers",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetQuestionIdentifiers()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QuestionIdentifiersValidationError{
					field:  "QuestionIdentifiers",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return QuestionIdentifiersMultiError(errors)
	}

	return nil
}

// QuestionIdentifiersMultiError is an error wrapping multiple validation
// errors returned by QuestionIdentifiers.ValidateAll() if the designated
// constraints aren't met.
type QuestionIdentifiersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionIdentifiersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionIdentifiersMultiError) AllErrors() []error { return m }

// QuestionIdentifiersValidationError is the validation error returned by
// QuestionIdentifiers.Validate if the designated constraints aren't met.
type QuestionIdentifiersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionIdentifiersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionIdentifiersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionIdentifiersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionIdentifiersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionIdentifiersValidationError) ErrorName() string {
	return "QuestionIdentifiersValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionIdentifiersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionIdentifiers.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionIdentifiersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionIdentifiersValidationError{}

// Validate checks the field values on EntityIdentifier with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EntityIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityIdentifierMultiError, or nil if none found.
func (m *EntityIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Id

	if len(errors) > 0 {
		return EntityIdentifierMultiError(errors)
	}

	return nil
}

// EntityIdentifierMultiError is an error wrapping multiple validation errors
// returned by EntityIdentifier.ValidateAll() if the designated constraints
// aren't met.
type EntityIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityIdentifierMultiError) AllErrors() []error { return m }

// EntityIdentifierValidationError is the validation error returned by
// EntityIdentifier.Validate if the designated constraints aren't met.
type EntityIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityIdentifierValidationError) ErrorName() string { return "EntityIdentifierValidationError" }

// Error satisfies the builtin error interface
func (e EntityIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityIdentifierValidationError{}

// Validate checks the field values on EntityIdentifiers with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EntityIdentifiers) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityIdentifiers with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityIdentifiersMultiError, or nil if none found.
func (m *EntityIdentifiers) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityIdentifiers) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetIdentifiers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EntityIdentifiersValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EntityIdentifiersValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EntityIdentifiersValidationError{
					field:  fmt.Sprintf("Identifiers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EntityIdentifiersMultiError(errors)
	}

	return nil
}

// EntityIdentifiersMultiError is an error wrapping multiple validation errors
// returned by EntityIdentifiers.ValidateAll() if the designated constraints
// aren't met.
type EntityIdentifiersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityIdentifiersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityIdentifiersMultiError) AllErrors() []error { return m }

// EntityIdentifiersValidationError is the validation error returned by
// EntityIdentifiers.Validate if the designated constraints aren't met.
type EntityIdentifiersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityIdentifiersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityIdentifiersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityIdentifiersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityIdentifiersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityIdentifiersValidationError) ErrorName() string {
	return "EntityIdentifiersValidationError"
}

// Error satisfies the builtin error interface
func (e EntityIdentifiersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityIdentifiers.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityIdentifiersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityIdentifiersValidationError{}

// Validate checks the field values on QuestionCodeIdentifiers with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionCodeIdentifiers) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionCodeIdentifiers with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionCodeIdentifiersMultiError, or nil if none found.
func (m *QuestionCodeIdentifiers) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionCodeIdentifiers) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetIdentifiers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QuestionCodeIdentifiersValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QuestionCodeIdentifiersValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QuestionCodeIdentifiersValidationError{
					field:  fmt.Sprintf("Identifiers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return QuestionCodeIdentifiersMultiError(errors)
	}

	return nil
}

// QuestionCodeIdentifiersMultiError is an error wrapping multiple validation
// errors returned by QuestionCodeIdentifiers.ValidateAll() if the designated
// constraints aren't met.
type QuestionCodeIdentifiersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionCodeIdentifiersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionCodeIdentifiersMultiError) AllErrors() []error { return m }

// QuestionCodeIdentifiersValidationError is the validation error returned by
// QuestionCodeIdentifiers.Validate if the designated constraints aren't met.
type QuestionCodeIdentifiersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionCodeIdentifiersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionCodeIdentifiersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionCodeIdentifiersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionCodeIdentifiersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionCodeIdentifiersValidationError) ErrorName() string {
	return "QuestionCodeIdentifiersValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionCodeIdentifiersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionCodeIdentifiers.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionCodeIdentifiersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionCodeIdentifiersValidationError{}

// Validate checks the field values on QuestionCodeIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionCodeIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionCodeIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionCodeIdentifierMultiError, or nil if none found.
func (m *QuestionCodeIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionCodeIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for IsMandatory

	if len(errors) > 0 {
		return QuestionCodeIdentifierMultiError(errors)
	}

	return nil
}

// QuestionCodeIdentifierMultiError is an error wrapping multiple validation
// errors returned by QuestionCodeIdentifier.ValidateAll() if the designated
// constraints aren't met.
type QuestionCodeIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionCodeIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionCodeIdentifierMultiError) AllErrors() []error { return m }

// QuestionCodeIdentifierValidationError is the validation error returned by
// QuestionCodeIdentifier.Validate if the designated constraints aren't met.
type QuestionCodeIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionCodeIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionCodeIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionCodeIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionCodeIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionCodeIdentifierValidationError) ErrorName() string {
	return "QuestionCodeIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionCodeIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionCodeIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionCodeIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionCodeIdentifierValidationError{}

// Validate checks the field values on ExtendedQuestion_ConditionalQuestions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ExtendedQuestion_ConditionalQuestions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExtendedQuestion_ConditionalQuestions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ExtendedQuestion_ConditionalQuestionsMultiError, or nil if none found.
func (m *ExtendedQuestion_ConditionalQuestions) ValidateAll() error {
	return m.validate(true)
}

func (m *ExtendedQuestion_ConditionalQuestions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExtendedQuestion_ConditionalQuestionsValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExtendedQuestion_ConditionalQuestionsValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExtendedQuestion_ConditionalQuestionsValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ExtendedQuestion_ConditionalQuestionsMultiError(errors)
	}

	return nil
}

// ExtendedQuestion_ConditionalQuestionsMultiError is an error wrapping
// multiple validation errors returned by
// ExtendedQuestion_ConditionalQuestions.ValidateAll() if the designated
// constraints aren't met.
type ExtendedQuestion_ConditionalQuestionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExtendedQuestion_ConditionalQuestionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExtendedQuestion_ConditionalQuestionsMultiError) AllErrors() []error { return m }

// ExtendedQuestion_ConditionalQuestionsValidationError is the validation error
// returned by ExtendedQuestion_ConditionalQuestions.Validate if the
// designated constraints aren't met.
type ExtendedQuestion_ConditionalQuestionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExtendedQuestion_ConditionalQuestionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExtendedQuestion_ConditionalQuestionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExtendedQuestion_ConditionalQuestionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExtendedQuestion_ConditionalQuestionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExtendedQuestion_ConditionalQuestionsValidationError) ErrorName() string {
	return "ExtendedQuestion_ConditionalQuestionsValidationError"
}

// Error satisfies the builtin error interface
func (e ExtendedQuestion_ConditionalQuestionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExtendedQuestion_ConditionalQuestions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExtendedQuestion_ConditionalQuestionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExtendedQuestion_ConditionalQuestionsValidationError{}

// Validate checks the field values on
// StoredConditionalQuestions_ConditionalQuestion with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StoredConditionalQuestions_ConditionalQuestion) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoredConditionalQuestions_ConditionalQuestion with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// StoredConditionalQuestions_ConditionalQuestionMultiError, or nil if none found.
func (m *StoredConditionalQuestions_ConditionalQuestion) ValidateAll() error {
	return m.validate(true)
}

func (m *StoredConditionalQuestions_ConditionalQuestion) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetQuestionCode()) < 1 {
		err := StoredConditionalQuestions_ConditionalQuestionValidationError{
			field:  "QuestionCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IsMandatory

	if len(errors) > 0 {
		return StoredConditionalQuestions_ConditionalQuestionMultiError(errors)
	}

	return nil
}

// StoredConditionalQuestions_ConditionalQuestionMultiError is an error
// wrapping multiple validation errors returned by
// StoredConditionalQuestions_ConditionalQuestion.ValidateAll() if the
// designated constraints aren't met.
type StoredConditionalQuestions_ConditionalQuestionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoredConditionalQuestions_ConditionalQuestionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoredConditionalQuestions_ConditionalQuestionMultiError) AllErrors() []error { return m }

// StoredConditionalQuestions_ConditionalQuestionValidationError is the
// validation error returned by
// StoredConditionalQuestions_ConditionalQuestion.Validate if the designated
// constraints aren't met.
type StoredConditionalQuestions_ConditionalQuestionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoredConditionalQuestions_ConditionalQuestionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoredConditionalQuestions_ConditionalQuestionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e StoredConditionalQuestions_ConditionalQuestionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoredConditionalQuestions_ConditionalQuestionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoredConditionalQuestions_ConditionalQuestionValidationError) ErrorName() string {
	return "StoredConditionalQuestions_ConditionalQuestionValidationError"
}

// Error satisfies the builtin error interface
func (e StoredConditionalQuestions_ConditionalQuestionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoredConditionalQuestions_ConditionalQuestion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoredConditionalQuestions_ConditionalQuestionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoredConditionalQuestions_ConditionalQuestionValidationError{}
