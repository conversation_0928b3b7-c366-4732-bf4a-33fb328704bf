// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/form/form.proto

package form

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	notification "github.com/epifi/gamma/api/celestial/activity/notification"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Form is an entity to store any form sent to the user for a risk use case.
type Form struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Optional: Risk Case against which form was triggered.
	CaseId  string `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Current status of the form.
	Status Status `protobuf:"varint,4,opt,name=status,proto3,enum=risk.case_management.form.Status" json:"status,omitempty"`
	// Request id populated at form trigger/origin point.
	ClientReqId string `protobuf:"bytes,5,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// Inception point from where form generation is triggered.
	// e.g., Manual Review if an agent triggers a questionnaire to be sent to user during review.
	Origin FormOrigin `protobuf:"varint,6,opt,name=origin,proto3,enum=risk.case_management.form.FormOrigin" json:"origin,omitempty"`
	// Form orchestration workflow request id.
	WorkflowReqId string `protobuf:"bytes,7,opt,name=workflow_req_id,json=workflowReqId,proto3" json:"workflow_req_id,omitempty"`
	// Analyst email
	AddedBy   string                 `protobuf:"bytes,8,opt,name=added_by,json=addedBy,proto3" json:"added_by,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// Timestamp at which form will expire/expired.
	// Whether a form expired without submission should be checked from status.
	ExpireAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
}

func (x *Form) Reset() {
	*x = Form{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_form_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Form) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Form) ProtoMessage() {}

func (x *Form) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_form_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Form.ProtoReflect.Descriptor instead.
func (*Form) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_form_proto_rawDescGZIP(), []int{0}
}

func (x *Form) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Form) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *Form) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *Form) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_STATUS_UNSPECIFIED
}

func (x *Form) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *Form) GetOrigin() FormOrigin {
	if x != nil {
		return x.Origin
	}
	return FormOrigin_FORM_ORIGIN_UNSPECIFIED
}

func (x *Form) GetWorkflowReqId() string {
	if x != nil {
		return x.WorkflowReqId
	}
	return ""
}

func (x *Form) GetAddedBy() string {
	if x != nil {
		return x.AddedBy
	}
	return ""
}

func (x *Form) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Form) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Form) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *Form) GetExpireAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireAt
	}
	return nil
}

// FormQuestionMapping stores all questions against a form that will be part of the questionnaire in form.
// It is a mapping of question code to form that reflects the latest version of question on form open.
type FormQuestionMapping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FormId       string                 `protobuf:"bytes,2,opt,name=form_id,json=formId,proto3" json:"form_id,omitempty"`
	QuestionCode string                 `protobuf:"bytes,3,opt,name=question_code,json=questionCode,proto3" json:"question_code,omitempty"`
	IsMandatory  bool                   `protobuf:"varint,4,opt,name=is_mandatory,json=isMandatory,proto3" json:"is_mandatory,omitempty"`
	CreatedAt    *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt    *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt    *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *FormQuestionMapping) Reset() {
	*x = FormQuestionMapping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_form_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormQuestionMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormQuestionMapping) ProtoMessage() {}

func (x *FormQuestionMapping) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_form_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormQuestionMapping.ProtoReflect.Descriptor instead.
func (*FormQuestionMapping) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_form_proto_rawDescGZIP(), []int{1}
}

func (x *FormQuestionMapping) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *FormQuestionMapping) GetFormId() string {
	if x != nil {
		return x.FormId
	}
	return ""
}

func (x *FormQuestionMapping) GetQuestionCode() string {
	if x != nil {
		return x.QuestionCode
	}
	return ""
}

func (x *FormQuestionMapping) GetIsMandatory() bool {
	if x != nil {
		return x.IsMandatory
	}
	return false
}

func (x *FormQuestionMapping) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *FormQuestionMapping) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *FormQuestionMapping) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

type FormFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*FormFilters_ActorId
	//	*FormFilters_CaseId
	Identifier isFormFilters_Identifier `protobuf_oneof:"identifier"`
}

func (x *FormFilters) Reset() {
	*x = FormFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_form_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormFilters) ProtoMessage() {}

func (x *FormFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_form_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormFilters.ProtoReflect.Descriptor instead.
func (*FormFilters) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_form_proto_rawDescGZIP(), []int{2}
}

func (m *FormFilters) GetIdentifier() isFormFilters_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *FormFilters) GetActorId() string {
	if x, ok := x.GetIdentifier().(*FormFilters_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *FormFilters) GetCaseId() string {
	if x, ok := x.GetIdentifier().(*FormFilters_CaseId); ok {
		return x.CaseId
	}
	return ""
}

type isFormFilters_Identifier interface {
	isFormFilters_Identifier()
}

type FormFilters_ActorId struct {
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3,oneof"`
}

type FormFilters_CaseId struct {
	CaseId string `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3,oneof"`
}

func (*FormFilters_ActorId) isFormFilters_Identifier() {}

func (*FormFilters_CaseId) isFormFilters_Identifier() {}

type ExtendedForm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Form *Form `protobuf:"bytes,1,opt,name=form,proto3" json:"form,omitempty"`
	// List of questions linked to form.
	// It contains the first level of question when no answer is selected and all conditional questions as well.
	Questions []*ExtendedQuestion `protobuf:"bytes,2,rep,name=questions,proto3" json:"questions,omitempty"`
	// Contains questions for responses submitted by user.
	// Note: It will be empty if the form is uncompleted.
	Responses []*QuestionWithResponse `protobuf:"bytes,3,rep,name=responses,proto3" json:"responses,omitempty"`
}

func (x *ExtendedForm) Reset() {
	*x = ExtendedForm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_form_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtendedForm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtendedForm) ProtoMessage() {}

func (x *ExtendedForm) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_form_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtendedForm.ProtoReflect.Descriptor instead.
func (*ExtendedForm) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_form_proto_rawDescGZIP(), []int{3}
}

func (x *ExtendedForm) GetForm() *Form {
	if x != nil {
		return x.Form
	}
	return nil
}

func (x *ExtendedForm) GetQuestions() []*ExtendedQuestion {
	if x != nil {
		return x.Questions
	}
	return nil
}

func (x *ExtendedForm) GetResponses() []*QuestionWithResponse {
	if x != nil {
		return x.Responses
	}
	return nil
}

// Input params for form orchestration workflow.
type FormOrchestrationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Questionnaire identifiers
	QuestionIdentifiers  *QuestionIdentifiers       `protobuf:"bytes,1,opt,name=question_identifiers,json=questionIdentifiers,proto3" json:"question_identifiers,omitempty"`
	Form                 *Form                      `protobuf:"bytes,2,opt,name=form,proto3" json:"form,omitempty"`
	Notification         *notification.Notification `protobuf:"bytes,3,opt,name=notification,proto3" json:"notification,omitempty"`
	ReminderNotification *notification.Notification `protobuf:"bytes,4,opt,name=reminder_notification,json=reminderNotification,proto3" json:"reminder_notification,omitempty"`
}

func (x *FormOrchestrationParams) Reset() {
	*x = FormOrchestrationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_form_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormOrchestrationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormOrchestrationParams) ProtoMessage() {}

func (x *FormOrchestrationParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_form_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormOrchestrationParams.ProtoReflect.Descriptor instead.
func (*FormOrchestrationParams) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_form_proto_rawDescGZIP(), []int{4}
}

func (x *FormOrchestrationParams) GetQuestionIdentifiers() *QuestionIdentifiers {
	if x != nil {
		return x.QuestionIdentifiers
	}
	return nil
}

func (x *FormOrchestrationParams) GetForm() *Form {
	if x != nil {
		return x.Form
	}
	return nil
}

func (x *FormOrchestrationParams) GetNotification() *notification.Notification {
	if x != nil {
		return x.Notification
	}
	return nil
}

func (x *FormOrchestrationParams) GetReminderNotification() *notification.Notification {
	if x != nil {
		return x.ReminderNotification
	}
	return nil
}

var File_api_risk_case_management_form_form_proto protoreflect.FileDescriptor

var file_api_risk_case_management_form_form_proto_rawDesc = []byte{
	0x0a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69,
	0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b,
	0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcd,
	0x04, 0x0a, 0x04, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20,
	0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x0d, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x06, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x06, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12,
	0x2f, 0x0a, 0x0f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x64, 0x64,
	0x65, 0x64, 0x42, 0x79, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x37, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f,
	0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x41, 0x74, 0x22, 0xc9,
	0x02, 0x0a, 0x13, 0x46, 0x6f, 0x72, 0x6d, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73,
	0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x53, 0x0a, 0x0b, 0x46, 0x6f,
	0x72, 0x6d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49,
	0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22,
	0xdd, 0x01, 0x0a, 0x0c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x6d,
	0x12, 0x33, 0x0a, 0x04, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x52,
	0x04, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x49, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x4d, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x22,
	0xfc, 0x02, 0x0a, 0x17, 0x46, 0x6f, 0x72, 0x6d, 0x4f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x6b, 0x0a, 0x14, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x13, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x12, 0x3d, 0x0a, 0x04, 0x66, 0x6f, 0x72, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x04, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x51, 0x0a, 0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x62, 0x0a, 0x15, 0x72, 0x65,
	0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64,
	0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x6c,
	0x0a, 0x34, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_form_form_proto_rawDescOnce sync.Once
	file_api_risk_case_management_form_form_proto_rawDescData = file_api_risk_case_management_form_form_proto_rawDesc
)

func file_api_risk_case_management_form_form_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_form_form_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_form_form_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_form_form_proto_rawDescData)
	})
	return file_api_risk_case_management_form_form_proto_rawDescData
}

var file_api_risk_case_management_form_form_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_risk_case_management_form_form_proto_goTypes = []interface{}{
	(*Form)(nil),                      // 0: risk.case_management.form.Form
	(*FormQuestionMapping)(nil),       // 1: risk.case_management.form.FormQuestionMapping
	(*FormFilters)(nil),               // 2: risk.case_management.form.FormFilters
	(*ExtendedForm)(nil),              // 3: risk.case_management.form.ExtendedForm
	(*FormOrchestrationParams)(nil),   // 4: risk.case_management.form.FormOrchestrationParams
	(Status)(0),                       // 5: risk.case_management.form.Status
	(FormOrigin)(0),                   // 6: risk.case_management.form.FormOrigin
	(*timestamppb.Timestamp)(nil),     // 7: google.protobuf.Timestamp
	(*ExtendedQuestion)(nil),          // 8: risk.case_management.form.ExtendedQuestion
	(*QuestionWithResponse)(nil),      // 9: risk.case_management.form.QuestionWithResponse
	(*QuestionIdentifiers)(nil),       // 10: risk.case_management.form.QuestionIdentifiers
	(*notification.Notification)(nil), // 11: celestial.activity.notification.Notification
}
var file_api_risk_case_management_form_form_proto_depIdxs = []int32{
	5,  // 0: risk.case_management.form.Form.status:type_name -> risk.case_management.form.Status
	6,  // 1: risk.case_management.form.Form.origin:type_name -> risk.case_management.form.FormOrigin
	7,  // 2: risk.case_management.form.Form.created_at:type_name -> google.protobuf.Timestamp
	7,  // 3: risk.case_management.form.Form.updated_at:type_name -> google.protobuf.Timestamp
	7,  // 4: risk.case_management.form.Form.deleted_at:type_name -> google.protobuf.Timestamp
	7,  // 5: risk.case_management.form.Form.expire_at:type_name -> google.protobuf.Timestamp
	7,  // 6: risk.case_management.form.FormQuestionMapping.created_at:type_name -> google.protobuf.Timestamp
	7,  // 7: risk.case_management.form.FormQuestionMapping.updated_at:type_name -> google.protobuf.Timestamp
	7,  // 8: risk.case_management.form.FormQuestionMapping.deleted_at:type_name -> google.protobuf.Timestamp
	0,  // 9: risk.case_management.form.ExtendedForm.form:type_name -> risk.case_management.form.Form
	8,  // 10: risk.case_management.form.ExtendedForm.questions:type_name -> risk.case_management.form.ExtendedQuestion
	9,  // 11: risk.case_management.form.ExtendedForm.responses:type_name -> risk.case_management.form.QuestionWithResponse
	10, // 12: risk.case_management.form.FormOrchestrationParams.question_identifiers:type_name -> risk.case_management.form.QuestionIdentifiers
	0,  // 13: risk.case_management.form.FormOrchestrationParams.form:type_name -> risk.case_management.form.Form
	11, // 14: risk.case_management.form.FormOrchestrationParams.notification:type_name -> celestial.activity.notification.Notification
	11, // 15: risk.case_management.form.FormOrchestrationParams.reminder_notification:type_name -> celestial.activity.notification.Notification
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_form_form_proto_init() }
func file_api_risk_case_management_form_form_proto_init() {
	if File_api_risk_case_management_form_form_proto != nil {
		return
	}
	file_api_risk_case_management_form_enums_proto_init()
	file_api_risk_case_management_form_question_proto_init()
	file_api_risk_case_management_form_response_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_case_management_form_form_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Form); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_form_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormQuestionMapping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_form_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_form_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtendedForm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_form_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormOrchestrationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_case_management_form_form_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*FormFilters_ActorId)(nil),
		(*FormFilters_CaseId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_form_form_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_case_management_form_form_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_form_form_proto_depIdxs,
		MessageInfos:      file_api_risk_case_management_form_form_proto_msgTypes,
	}.Build()
	File_api_risk_case_management_form_form_proto = out.File
	file_api_risk_case_management_form_form_proto_rawDesc = nil
	file_api_risk_case_management_form_form_proto_goTypes = nil
	file_api_risk_case_management_form_form_proto_depIdxs = nil
}
