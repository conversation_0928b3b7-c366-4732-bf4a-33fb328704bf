//go:generate gen_sql -types=Status,FormOrigin,EntityType,QuestionType,FileContentType

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/form/enums.proto

package form

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Current status of the form sent/to be sent to the user.
type Status int32

const (
	Status_STATUS_UNSPECIFIED Status = 0
	// Form is recorded in db.
	Status_STATUS_CREATED Status = 1
	// Form is sent to the user.
	Status_STATUS_SENT Status = 2
	// Response is submitted by user and submission is in process at backend.
	Status_STATUS_PROCESSING_SUBMISSION Status = 4
	// Response to all mandatory questions is submitted by user.
	Status_STATUS_SUBMITTED Status = 5
	// The Form has timed out without a user response and user can no longer respond.
	Status_STATUS_EXPIRED Status = 6
	// The form has been cancelled due to some reason.
	Status_STATUS_CANCELLED Status = 7
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "STATUS_CREATED",
		2: "STATUS_SENT",
		4: "STATUS_PROCESSING_SUBMISSION",
		5: "STATUS_SUBMITTED",
		6: "STATUS_EXPIRED",
		7: "STATUS_CANCELLED",
	}
	Status_value = map[string]int32{
		"STATUS_UNSPECIFIED":           0,
		"STATUS_CREATED":               1,
		"STATUS_SENT":                  2,
		"STATUS_PROCESSING_SUBMISSION": 4,
		"STATUS_SUBMITTED":             5,
		"STATUS_EXPIRED":               6,
		"STATUS_CANCELLED":             7,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_form_enums_proto_enumTypes[0].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_form_enums_proto_enumTypes[0]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_enums_proto_rawDescGZIP(), []int{0}
}

// Form generation origin point.
type FormOrigin int32

const (
	FormOrigin_FORM_ORIGIN_UNSPECIFIED FormOrigin = 0
	// If triggered by agent during manual review for outcall.
	FormOrigin_FORM_ORIGIN_MANUAL_REVIEW_OUTCALL FormOrigin = 1
	// If triggered by freeze/ unfreeze flows affecting operational status
	FormOrigin_FORM_ORIGIN_ACCOUNT_OPERATIONAL_STATUS_UPDATE FormOrigin = 2
)

// Enum value maps for FormOrigin.
var (
	FormOrigin_name = map[int32]string{
		0: "FORM_ORIGIN_UNSPECIFIED",
		1: "FORM_ORIGIN_MANUAL_REVIEW_OUTCALL",
		2: "FORM_ORIGIN_ACCOUNT_OPERATIONAL_STATUS_UPDATE",
	}
	FormOrigin_value = map[string]int32{
		"FORM_ORIGIN_UNSPECIFIED":                       0,
		"FORM_ORIGIN_MANUAL_REVIEW_OUTCALL":             1,
		"FORM_ORIGIN_ACCOUNT_OPERATIONAL_STATUS_UPDATE": 2,
	}
)

func (x FormOrigin) Enum() *FormOrigin {
	p := new(FormOrigin)
	*p = x
	return p
}

func (x FormOrigin) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FormOrigin) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_form_enums_proto_enumTypes[1].Descriptor()
}

func (FormOrigin) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_form_enums_proto_enumTypes[1]
}

func (x FormOrigin) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FormOrigin.Descriptor instead.
func (FormOrigin) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_enums_proto_rawDescGZIP(), []int{1}
}

type EntityType int32

const (
	EntityType_ENTITY_TYPE_UNSPECIFIED            EntityType = 0
	EntityType_ENTITY_TYPE_QUESTIONNAIRE_TEMPLATE EntityType = 1
)

// Enum value maps for EntityType.
var (
	EntityType_name = map[int32]string{
		0: "ENTITY_TYPE_UNSPECIFIED",
		1: "ENTITY_TYPE_QUESTIONNAIRE_TEMPLATE",
	}
	EntityType_value = map[string]int32{
		"ENTITY_TYPE_UNSPECIFIED":            0,
		"ENTITY_TYPE_QUESTIONNAIRE_TEMPLATE": 1,
	}
)

func (x EntityType) Enum() *EntityType {
	p := new(EntityType)
	*p = x
	return p
}

func (x EntityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EntityType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_form_enums_proto_enumTypes[2].Descriptor()
}

func (EntityType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_form_enums_proto_enumTypes[2]
}

func (x EntityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EntityType.Descriptor instead.
func (EntityType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_enums_proto_rawDescGZIP(), []int{2}
}

type QuestionType int32

const (
	QuestionType_QUESTION_TYPE_UNSPECIFIED  QuestionType = 0
	QuestionType_QUESTION_TYPE_TEXT         QuestionType = 1
	QuestionType_QUESTION_TYPE_FILE         QuestionType = 2
	QuestionType_QUESTION_TYPE_MULTI_CHOICE QuestionType = 3
)

// Enum value maps for QuestionType.
var (
	QuestionType_name = map[int32]string{
		0: "QUESTION_TYPE_UNSPECIFIED",
		1: "QUESTION_TYPE_TEXT",
		2: "QUESTION_TYPE_FILE",
		3: "QUESTION_TYPE_MULTI_CHOICE",
	}
	QuestionType_value = map[string]int32{
		"QUESTION_TYPE_UNSPECIFIED":  0,
		"QUESTION_TYPE_TEXT":         1,
		"QUESTION_TYPE_FILE":         2,
		"QUESTION_TYPE_MULTI_CHOICE": 3,
	}
)

func (x QuestionType) Enum() *QuestionType {
	p := new(QuestionType)
	*p = x
	return p
}

func (x QuestionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_form_enums_proto_enumTypes[3].Descriptor()
}

func (QuestionType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_form_enums_proto_enumTypes[3]
}

func (x QuestionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestionType.Descriptor instead.
func (QuestionType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_enums_proto_rawDescGZIP(), []int{3}
}

type FileContentType int32

const (
	FileContentType_FILE_CONTENT_TYPE_UNSPECIFIED FileContentType = 0
	FileContentType_FILE_CONTENT_TYPE_JPEG        FileContentType = 1
	FileContentType_FILE_CONTENT_TYPE_PDF         FileContentType = 2
	FileContentType_FILE_CONTENT_TYPE_PNG         FileContentType = 3
)

// Enum value maps for FileContentType.
var (
	FileContentType_name = map[int32]string{
		0: "FILE_CONTENT_TYPE_UNSPECIFIED",
		1: "FILE_CONTENT_TYPE_JPEG",
		2: "FILE_CONTENT_TYPE_PDF",
		3: "FILE_CONTENT_TYPE_PNG",
	}
	FileContentType_value = map[string]int32{
		"FILE_CONTENT_TYPE_UNSPECIFIED": 0,
		"FILE_CONTENT_TYPE_JPEG":        1,
		"FILE_CONTENT_TYPE_PDF":         2,
		"FILE_CONTENT_TYPE_PNG":         3,
	}
)

func (x FileContentType) Enum() *FileContentType {
	p := new(FileContentType)
	*p = x
	return p
}

func (x FileContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_form_enums_proto_enumTypes[4].Descriptor()
}

func (FileContentType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_form_enums_proto_enumTypes[4]
}

func (x FileContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileContentType.Descriptor instead.
func (FileContentType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_enums_proto_rawDescGZIP(), []int{4}
}

type QuestionResponseFieldMask int32

const (
	QuestionResponseFieldMask_QUESTION_RESPONSE_FIELD_MASK_UNSPECIFIED QuestionResponseFieldMask = 0
	QuestionResponseFieldMask_QUESTION_RESPONSE_FIELD_MASK_ALL         QuestionResponseFieldMask = 1
	QuestionResponseFieldMask_QUESTION_RESPONSE_FIELD_MASK_ID          QuestionResponseFieldMask = 2
	QuestionResponseFieldMask_QUESTION_RESPONSE_FIELD_MASK_FORM_ID     QuestionResponseFieldMask = 3
	QuestionResponseFieldMask_QUESTION_RESPONSE_FIELD_MASK_QUESTION_ID QuestionResponseFieldMask = 4
	QuestionResponseFieldMask_QUESTION_RESPONSE_FIELD_MASK_RESPONSE    QuestionResponseFieldMask = 5
	QuestionResponseFieldMask_QUESTION_RESPONSE_FIELD_MASK_CREATED_AT  QuestionResponseFieldMask = 6
	QuestionResponseFieldMask_QUESTION_RESPONSE_FIELD_MASK_UPDATED_AT  QuestionResponseFieldMask = 7
	QuestionResponseFieldMask_QUESTION_RESPONSE_FIELD_MASK_DELETED_AT  QuestionResponseFieldMask = 8
)

// Enum value maps for QuestionResponseFieldMask.
var (
	QuestionResponseFieldMask_name = map[int32]string{
		0: "QUESTION_RESPONSE_FIELD_MASK_UNSPECIFIED",
		1: "QUESTION_RESPONSE_FIELD_MASK_ALL",
		2: "QUESTION_RESPONSE_FIELD_MASK_ID",
		3: "QUESTION_RESPONSE_FIELD_MASK_FORM_ID",
		4: "QUESTION_RESPONSE_FIELD_MASK_QUESTION_ID",
		5: "QUESTION_RESPONSE_FIELD_MASK_RESPONSE",
		6: "QUESTION_RESPONSE_FIELD_MASK_CREATED_AT",
		7: "QUESTION_RESPONSE_FIELD_MASK_UPDATED_AT",
		8: "QUESTION_RESPONSE_FIELD_MASK_DELETED_AT",
	}
	QuestionResponseFieldMask_value = map[string]int32{
		"QUESTION_RESPONSE_FIELD_MASK_UNSPECIFIED": 0,
		"QUESTION_RESPONSE_FIELD_MASK_ALL":         1,
		"QUESTION_RESPONSE_FIELD_MASK_ID":          2,
		"QUESTION_RESPONSE_FIELD_MASK_FORM_ID":     3,
		"QUESTION_RESPONSE_FIELD_MASK_QUESTION_ID": 4,
		"QUESTION_RESPONSE_FIELD_MASK_RESPONSE":    5,
		"QUESTION_RESPONSE_FIELD_MASK_CREATED_AT":  6,
		"QUESTION_RESPONSE_FIELD_MASK_UPDATED_AT":  7,
		"QUESTION_RESPONSE_FIELD_MASK_DELETED_AT":  8,
	}
)

func (x QuestionResponseFieldMask) Enum() *QuestionResponseFieldMask {
	p := new(QuestionResponseFieldMask)
	*p = x
	return p
}

func (x QuestionResponseFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestionResponseFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_form_enums_proto_enumTypes[5].Descriptor()
}

func (QuestionResponseFieldMask) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_form_enums_proto_enumTypes[5]
}

func (x QuestionResponseFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestionResponseFieldMask.Descriptor instead.
func (QuestionResponseFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_enums_proto_rawDescGZIP(), []int{5}
}

type FormFieldMask int32

const (
	FormFieldMask_FORM_FIELD_MASK_UNSPECIFIED     FormFieldMask = 0
	FormFieldMask_FORM_FIELD_MASK_ALL             FormFieldMask = 1
	FormFieldMask_FORM_FIELD_MASK_ID              FormFieldMask = 2
	FormFieldMask_FORM_FIELD_MASK_CASE_ID         FormFieldMask = 3
	FormFieldMask_FORM_FIELD_MASK_ACTOR_ID        FormFieldMask = 4
	FormFieldMask_FORM_FIELD_MASK_STATUS          FormFieldMask = 5
	FormFieldMask_FORM_FIELD_MASK_ADDED_BY        FormFieldMask = 6
	FormFieldMask_FORM_FIELD_MASK_CLIENT_REQ_ID   FormFieldMask = 7
	FormFieldMask_FORM_FIELD_MASK_ORIGIN          FormFieldMask = 8
	FormFieldMask_FORM_FIELD_MASK_WORKFLOW_REQ_ID FormFieldMask = 9
	FormFieldMask_FORM_FIELD_MASK_CREATED_AT      FormFieldMask = 10
	FormFieldMask_FORM_FIELD_MASK_UPDATED_AT      FormFieldMask = 11
	FormFieldMask_FORM_FIELD_MASK_DELETED_AT      FormFieldMask = 12
	FormFieldMask_FORM_FIELD_MASK_EXPIRE_AT       FormFieldMask = 13
)

// Enum value maps for FormFieldMask.
var (
	FormFieldMask_name = map[int32]string{
		0:  "FORM_FIELD_MASK_UNSPECIFIED",
		1:  "FORM_FIELD_MASK_ALL",
		2:  "FORM_FIELD_MASK_ID",
		3:  "FORM_FIELD_MASK_CASE_ID",
		4:  "FORM_FIELD_MASK_ACTOR_ID",
		5:  "FORM_FIELD_MASK_STATUS",
		6:  "FORM_FIELD_MASK_ADDED_BY",
		7:  "FORM_FIELD_MASK_CLIENT_REQ_ID",
		8:  "FORM_FIELD_MASK_ORIGIN",
		9:  "FORM_FIELD_MASK_WORKFLOW_REQ_ID",
		10: "FORM_FIELD_MASK_CREATED_AT",
		11: "FORM_FIELD_MASK_UPDATED_AT",
		12: "FORM_FIELD_MASK_DELETED_AT",
		13: "FORM_FIELD_MASK_EXPIRE_AT",
	}
	FormFieldMask_value = map[string]int32{
		"FORM_FIELD_MASK_UNSPECIFIED":     0,
		"FORM_FIELD_MASK_ALL":             1,
		"FORM_FIELD_MASK_ID":              2,
		"FORM_FIELD_MASK_CASE_ID":         3,
		"FORM_FIELD_MASK_ACTOR_ID":        4,
		"FORM_FIELD_MASK_STATUS":          5,
		"FORM_FIELD_MASK_ADDED_BY":        6,
		"FORM_FIELD_MASK_CLIENT_REQ_ID":   7,
		"FORM_FIELD_MASK_ORIGIN":          8,
		"FORM_FIELD_MASK_WORKFLOW_REQ_ID": 9,
		"FORM_FIELD_MASK_CREATED_AT":      10,
		"FORM_FIELD_MASK_UPDATED_AT":      11,
		"FORM_FIELD_MASK_DELETED_AT":      12,
		"FORM_FIELD_MASK_EXPIRE_AT":       13,
	}
)

func (x FormFieldMask) Enum() *FormFieldMask {
	p := new(FormFieldMask)
	*p = x
	return p
}

func (x FormFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FormFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_form_enums_proto_enumTypes[6].Descriptor()
}

func (FormFieldMask) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_form_enums_proto_enumTypes[6]
}

func (x FormFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FormFieldMask.Descriptor instead.
func (FormFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_enums_proto_rawDescGZIP(), []int{6}
}

type QuestionFieldMask int32

const (
	QuestionFieldMask_QUESTION_FIELD_MASK_UNSPECIFIED QuestionFieldMask = 0
	QuestionFieldMask_QUESTION_FIELD_MASK_ALL         QuestionFieldMask = 1
	QuestionFieldMask_QUESTION_FIELD_MASK_ID          QuestionFieldMask = 2
	QuestionFieldMask_QUESTION_FIELD_MASK_CODE        QuestionFieldMask = 3
	QuestionFieldMask_QUESTION_FIELD_MASK_TEXT        QuestionFieldMask = 4
	QuestionFieldMask_QUESTION_FIELD_MASK_TIP         QuestionFieldMask = 5
	QuestionFieldMask_QUESTION_FIELD_MASK_DESCRIPTION QuestionFieldMask = 6
	QuestionFieldMask_QUESTION_FIELD_MASK_TYPE        QuestionFieldMask = 7
	QuestionFieldMask_QUESTION_FIELD_MASK_OPTIONS     QuestionFieldMask = 8
	QuestionFieldMask_QUESTION_FIELD_MASK_VERSION     QuestionFieldMask = 9
	QuestionFieldMask_QUESTION_FIELD_MASK_CREATED_AT  QuestionFieldMask = 10
	QuestionFieldMask_QUESTION_FIELD_MASK_UPDATED_AT  QuestionFieldMask = 11
	QuestionFieldMask_QUESTION_FIELD_MASK_DELETED_AT  QuestionFieldMask = 12
	QuestionFieldMask_QUESTION_FIELD_MASK_PLACEHOLDER QuestionFieldMask = 13
)

// Enum value maps for QuestionFieldMask.
var (
	QuestionFieldMask_name = map[int32]string{
		0:  "QUESTION_FIELD_MASK_UNSPECIFIED",
		1:  "QUESTION_FIELD_MASK_ALL",
		2:  "QUESTION_FIELD_MASK_ID",
		3:  "QUESTION_FIELD_MASK_CODE",
		4:  "QUESTION_FIELD_MASK_TEXT",
		5:  "QUESTION_FIELD_MASK_TIP",
		6:  "QUESTION_FIELD_MASK_DESCRIPTION",
		7:  "QUESTION_FIELD_MASK_TYPE",
		8:  "QUESTION_FIELD_MASK_OPTIONS",
		9:  "QUESTION_FIELD_MASK_VERSION",
		10: "QUESTION_FIELD_MASK_CREATED_AT",
		11: "QUESTION_FIELD_MASK_UPDATED_AT",
		12: "QUESTION_FIELD_MASK_DELETED_AT",
		13: "QUESTION_FIELD_MASK_PLACEHOLDER",
	}
	QuestionFieldMask_value = map[string]int32{
		"QUESTION_FIELD_MASK_UNSPECIFIED": 0,
		"QUESTION_FIELD_MASK_ALL":         1,
		"QUESTION_FIELD_MASK_ID":          2,
		"QUESTION_FIELD_MASK_CODE":        3,
		"QUESTION_FIELD_MASK_TEXT":        4,
		"QUESTION_FIELD_MASK_TIP":         5,
		"QUESTION_FIELD_MASK_DESCRIPTION": 6,
		"QUESTION_FIELD_MASK_TYPE":        7,
		"QUESTION_FIELD_MASK_OPTIONS":     8,
		"QUESTION_FIELD_MASK_VERSION":     9,
		"QUESTION_FIELD_MASK_CREATED_AT":  10,
		"QUESTION_FIELD_MASK_UPDATED_AT":  11,
		"QUESTION_FIELD_MASK_DELETED_AT":  12,
		"QUESTION_FIELD_MASK_PLACEHOLDER": 13,
	}
)

func (x QuestionFieldMask) Enum() *QuestionFieldMask {
	p := new(QuestionFieldMask)
	*p = x
	return p
}

func (x QuestionFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestionFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_form_enums_proto_enumTypes[7].Descriptor()
}

func (QuestionFieldMask) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_form_enums_proto_enumTypes[7]
}

func (x QuestionFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestionFieldMask.Descriptor instead.
func (QuestionFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_enums_proto_rawDescGZIP(), []int{7}
}

type FormQuestionMappingFieldMask int32

const (
	FormQuestionMappingFieldMask_FORM_QUESTION_MAPPING_FIELD_MASK_UNSPECIFIED   FormQuestionMappingFieldMask = 0
	FormQuestionMappingFieldMask_FORM_QUESTION_MAPPING_FIELD_MASK_ALL           FormQuestionMappingFieldMask = 1
	FormQuestionMappingFieldMask_FORM_QUESTION_MAPPING_FIELD_MASK_ID            FormQuestionMappingFieldMask = 2
	FormQuestionMappingFieldMask_FORM_QUESTION_MAPPING_FIELD_MASK_FORM_ID       FormQuestionMappingFieldMask = 3
	FormQuestionMappingFieldMask_FORM_QUESTION_MAPPING_FIELD_MASK_QUESTION_CODE FormQuestionMappingFieldMask = 4
	FormQuestionMappingFieldMask_FORM_QUESTION_MAPPING_FIELD_MASK_IS_MANDATORY  FormQuestionMappingFieldMask = 5
	FormQuestionMappingFieldMask_FORM_QUESTION_MAPPING_FIELD_MASK_CREATED_AT    FormQuestionMappingFieldMask = 6
	FormQuestionMappingFieldMask_FORM_QUESTION_MAPPING_FIELD_MASK_UPDATED_AT    FormQuestionMappingFieldMask = 7
	FormQuestionMappingFieldMask_FORM_QUESTION_MAPPING_FIELD_MASK_DELETED_AT    FormQuestionMappingFieldMask = 8
)

// Enum value maps for FormQuestionMappingFieldMask.
var (
	FormQuestionMappingFieldMask_name = map[int32]string{
		0: "FORM_QUESTION_MAPPING_FIELD_MASK_UNSPECIFIED",
		1: "FORM_QUESTION_MAPPING_FIELD_MASK_ALL",
		2: "FORM_QUESTION_MAPPING_FIELD_MASK_ID",
		3: "FORM_QUESTION_MAPPING_FIELD_MASK_FORM_ID",
		4: "FORM_QUESTION_MAPPING_FIELD_MASK_QUESTION_CODE",
		5: "FORM_QUESTION_MAPPING_FIELD_MASK_IS_MANDATORY",
		6: "FORM_QUESTION_MAPPING_FIELD_MASK_CREATED_AT",
		7: "FORM_QUESTION_MAPPING_FIELD_MASK_UPDATED_AT",
		8: "FORM_QUESTION_MAPPING_FIELD_MASK_DELETED_AT",
	}
	FormQuestionMappingFieldMask_value = map[string]int32{
		"FORM_QUESTION_MAPPING_FIELD_MASK_UNSPECIFIED":   0,
		"FORM_QUESTION_MAPPING_FIELD_MASK_ALL":           1,
		"FORM_QUESTION_MAPPING_FIELD_MASK_ID":            2,
		"FORM_QUESTION_MAPPING_FIELD_MASK_FORM_ID":       3,
		"FORM_QUESTION_MAPPING_FIELD_MASK_QUESTION_CODE": 4,
		"FORM_QUESTION_MAPPING_FIELD_MASK_IS_MANDATORY":  5,
		"FORM_QUESTION_MAPPING_FIELD_MASK_CREATED_AT":    6,
		"FORM_QUESTION_MAPPING_FIELD_MASK_UPDATED_AT":    7,
		"FORM_QUESTION_MAPPING_FIELD_MASK_DELETED_AT":    8,
	}
)

func (x FormQuestionMappingFieldMask) Enum() *FormQuestionMappingFieldMask {
	p := new(FormQuestionMappingFieldMask)
	*p = x
	return p
}

func (x FormQuestionMappingFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FormQuestionMappingFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_form_enums_proto_enumTypes[8].Descriptor()
}

func (FormQuestionMappingFieldMask) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_form_enums_proto_enumTypes[8]
}

func (x FormQuestionMappingFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FormQuestionMappingFieldMask.Descriptor instead.
func (FormQuestionMappingFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_enums_proto_rawDescGZIP(), []int{8}
}

type ExtendedFormFieldMask int32

const (
	ExtendedFormFieldMask_EXTENDED_FORM_FIELD_MASK_UNSPECIFIED ExtendedFormFieldMask = 0
	// Included form, questions and responses(if submitted).
	ExtendedFormFieldMask_EXTENDED_FORM_FIELD_MASK_ALL  ExtendedFormFieldMask = 1
	ExtendedFormFieldMask_EXTENDED_FORM_FIELD_MASK_FORM ExtendedFormFieldMask = 2
	// If only questions are needed.
	ExtendedFormFieldMask_EXTENDED_FORM_FIELD_MASK_QUESTIONS ExtendedFormFieldMask = 3
	// If only responses are needed.
	ExtendedFormFieldMask_EXTENDED_FORM_FIELD_MASK_RESPONSES ExtendedFormFieldMask = 4
)

// Enum value maps for ExtendedFormFieldMask.
var (
	ExtendedFormFieldMask_name = map[int32]string{
		0: "EXTENDED_FORM_FIELD_MASK_UNSPECIFIED",
		1: "EXTENDED_FORM_FIELD_MASK_ALL",
		2: "EXTENDED_FORM_FIELD_MASK_FORM",
		3: "EXTENDED_FORM_FIELD_MASK_QUESTIONS",
		4: "EXTENDED_FORM_FIELD_MASK_RESPONSES",
	}
	ExtendedFormFieldMask_value = map[string]int32{
		"EXTENDED_FORM_FIELD_MASK_UNSPECIFIED": 0,
		"EXTENDED_FORM_FIELD_MASK_ALL":         1,
		"EXTENDED_FORM_FIELD_MASK_FORM":        2,
		"EXTENDED_FORM_FIELD_MASK_QUESTIONS":   3,
		"EXTENDED_FORM_FIELD_MASK_RESPONSES":   4,
	}
)

func (x ExtendedFormFieldMask) Enum() *ExtendedFormFieldMask {
	p := new(ExtendedFormFieldMask)
	*p = x
	return p
}

func (x ExtendedFormFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExtendedFormFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_form_enums_proto_enumTypes[9].Descriptor()
}

func (ExtendedFormFieldMask) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_form_enums_proto_enumTypes[9]
}

func (x ExtendedFormFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExtendedFormFieldMask.Descriptor instead.
func (ExtendedFormFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_enums_proto_rawDescGZIP(), []int{9}
}

// Prebuild questionnaire templates.
type QuestionnaireTemplate int32

const (
	QuestionnaireTemplate_QUESTIONNAIRE_TEMPLATE_UNSPECIFIED                    QuestionnaireTemplate = 0
	QuestionnaireTemplate_QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_AMOUNT             QuestionnaireTemplate = 1
	QuestionnaireTemplate_QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_COUNT              QuestionnaireTemplate = 2
	QuestionnaireTemplate_QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_COUNT_AND_AMOUNT   QuestionnaireTemplate = 3
	QuestionnaireTemplate_QUESTIONNAIRE_TEMPLATE_STOP_BUSINESS_ACTIVITY_ACK     QuestionnaireTemplate = 4
	QuestionnaireTemplate_QUESTIONNAIRE_TEMPLATE_INCOME_DISCREPANCY             QuestionnaireTemplate = 5
	QuestionnaireTemplate_QUESTIONNAIRE_TEMPLATE_CFR_BLOCKED_USER_REVIEW        QuestionnaireTemplate = 6
	QuestionnaireTemplate_QUESTIONNAIRE_TEMPLATE_CFR_BLOCKED_TRANSACTION_REVIEW QuestionnaireTemplate = 7
)

// Enum value maps for QuestionnaireTemplate.
var (
	QuestionnaireTemplate_name = map[int32]string{
		0: "QUESTIONNAIRE_TEMPLATE_UNSPECIFIED",
		1: "QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_AMOUNT",
		2: "QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_COUNT",
		3: "QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_COUNT_AND_AMOUNT",
		4: "QUESTIONNAIRE_TEMPLATE_STOP_BUSINESS_ACTIVITY_ACK",
		5: "QUESTIONNAIRE_TEMPLATE_INCOME_DISCREPANCY",
		6: "QUESTIONNAIRE_TEMPLATE_CFR_BLOCKED_USER_REVIEW",
		7: "QUESTIONNAIRE_TEMPLATE_CFR_BLOCKED_TRANSACTION_REVIEW",
	}
	QuestionnaireTemplate_value = map[string]int32{
		"QUESTIONNAIRE_TEMPLATE_UNSPECIFIED":                    0,
		"QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_AMOUNT":             1,
		"QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_COUNT":              2,
		"QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_COUNT_AND_AMOUNT":   3,
		"QUESTIONNAIRE_TEMPLATE_STOP_BUSINESS_ACTIVITY_ACK":     4,
		"QUESTIONNAIRE_TEMPLATE_INCOME_DISCREPANCY":             5,
		"QUESTIONNAIRE_TEMPLATE_CFR_BLOCKED_USER_REVIEW":        6,
		"QUESTIONNAIRE_TEMPLATE_CFR_BLOCKED_TRANSACTION_REVIEW": 7,
	}
)

func (x QuestionnaireTemplate) Enum() *QuestionnaireTemplate {
	p := new(QuestionnaireTemplate)
	*p = x
	return p
}

func (x QuestionnaireTemplate) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestionnaireTemplate) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_form_enums_proto_enumTypes[10].Descriptor()
}

func (QuestionnaireTemplate) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_form_enums_proto_enumTypes[10]
}

func (x QuestionnaireTemplate) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestionnaireTemplate.Descriptor instead.
func (QuestionnaireTemplate) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_enums_proto_rawDescGZIP(), []int{10}
}

var File_api_risk_case_management_form_enums_proto protoreflect.FileDescriptor

var file_api_risk_case_management_form_enums_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2a, 0xa7, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0f, 0x0a,
	0x0b, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x20,
	0x0a, 0x1c, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53,
	0x49, 0x4e, 0x47, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x04,
	0x12, 0x14, 0x0a, 0x10, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49,
	0x54, 0x54, 0x45, 0x44, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x06, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x07,
	0x2a, 0x83, 0x01, 0x0a, 0x0a, 0x46, 0x6f, 0x72, 0x6d, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12,
	0x1b, 0x0a, 0x17, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x4f, 0x52, 0x49, 0x47, 0x49, 0x4e, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21,
	0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x4f, 0x52, 0x49, 0x47, 0x49, 0x4e, 0x5f, 0x4d, 0x41, 0x4e, 0x55,
	0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x4f, 0x55, 0x54, 0x43, 0x41, 0x4c,
	0x4c, 0x10, 0x01, 0x12, 0x31, 0x0a, 0x2d, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x4f, 0x52, 0x49, 0x47,
	0x49, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x10, 0x02, 0x2a, 0x51, 0x0a, 0x0a, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x26, 0x0a, 0x22, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45, 0x5f, 0x54,
	0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x10, 0x01, 0x2a, 0x7d, 0x0a, 0x0c, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x01,
	0x12, 0x16, 0x0a, 0x12, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f,
	0x43, 0x48, 0x4f, 0x49, 0x43, 0x45, 0x10, 0x03, 0x2a, 0x86, 0x01, 0x0a, 0x0f, 0x46, 0x69, 0x6c,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x1a, 0x0a, 0x16, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x50, 0x45, 0x47, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x50, 0x44, 0x46, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4e, 0x47, 0x10,
	0x03, 0x2a, 0x9e, 0x03, 0x0a, 0x19, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12,
	0x2c, 0x0a, 0x28, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50,
	0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a,
	0x20, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e,
	0x53, 0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x4c,
	0x4c, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x28, 0x0a, 0x24, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x49, 0x44,
	0x10, 0x03, 0x12, 0x2c, 0x0a, 0x28, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x04,
	0x12, 0x29, 0x0a, 0x25, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x10, 0x05, 0x12, 0x2b, 0x0a, 0x27, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x06, 0x12, 0x2b, 0x0a, 0x27, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44,
	0x5f, 0x41, 0x54, 0x10, 0x07, 0x12, 0x2b, 0x0a, 0x27, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54,
	0x10, 0x08, 0x2a, 0xb9, 0x03, 0x0a, 0x0d, 0x46, 0x6f, 0x72, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4d, 0x61, 0x73, 0x6b, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x01, 0x12, 0x16,
	0x0a, 0x12, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x49,
	0x44, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10,
	0x04, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x05, 0x12, 0x1c, 0x0a,
	0x18, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x41, 0x44, 0x44, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x10, 0x06, 0x12, 0x21, 0x0a, 0x1d, 0x46,
	0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43,
	0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x5f, 0x49, 0x44, 0x10, 0x07, 0x12, 0x1a,
	0x0a, 0x16, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x4f, 0x52, 0x49, 0x47, 0x49, 0x4e, 0x10, 0x08, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x4f,
	0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x57, 0x4f,
	0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x52, 0x45, 0x51, 0x5f, 0x49, 0x44, 0x10, 0x09, 0x12,
	0x1e, 0x0a, 0x1a, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0a, 0x12,
	0x1e, 0x0a, 0x1a, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0b, 0x12,
	0x1e, 0x0a, 0x1a, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0c, 0x12,
	0x1d, 0x0a, 0x19, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x5f, 0x41, 0x54, 0x10, 0x0d, 0x2a, 0xe0,
	0x03, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4d, 0x61, 0x73, 0x6b, 0x12, 0x23, 0x0a, 0x1f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44,
	0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x03,
	0x12, 0x1c, 0x0a, 0x18, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x04, 0x12, 0x1b,
	0x0a, 0x17, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x49, 0x50, 0x10, 0x05, 0x12, 0x23, 0x0a, 0x1f, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06,
	0x12, 0x1c, 0x0a, 0x18, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x07, 0x12, 0x1f,
	0x0a, 0x1b, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x08, 0x12,
	0x1f, 0x0a, 0x1b, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x09,
	0x12, 0x22, 0x0a, 0x1e, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f,
	0x41, 0x54, 0x10, 0x0a, 0x12, 0x22, 0x0a, 0x1e, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0b, 0x12, 0x22, 0x0a, 0x1e, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0c, 0x12, 0x23, 0x0a, 0x1f,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x48, 0x4f, 0x4c, 0x44, 0x45, 0x52, 0x10,
	0x0d, 0x2a, 0xcb, 0x03, 0x0a, 0x1c, 0x46, 0x6f, 0x72, 0x6d, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x12, 0x30, 0x0a, 0x2c, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x28, 0x0a, 0x24, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x01, 0x12, 0x27,
	0x0a, 0x23, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x2c, 0x0a, 0x28, 0x46, 0x4f, 0x52, 0x4d, 0x5f,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x4f, 0x52, 0x4d,
	0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x32, 0x0a, 0x2e, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x04, 0x12, 0x31, 0x0a, 0x2d, 0x46, 0x4f, 0x52,
	0x4d, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49,
	0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x53,
	0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x4f, 0x52, 0x59, 0x10, 0x05, 0x12, 0x2f, 0x0a, 0x2b,
	0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x41,
	0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x06, 0x12, 0x2f, 0x0a,
	0x2b, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d,
	0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x07, 0x12, 0x2f,
	0x0a, 0x2b, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x08, 0x2a,
	0xd6, 0x01, 0x0a, 0x15, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x6d,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x28, 0x0a, 0x24, 0x45, 0x58, 0x54,
	0x45, 0x4e, 0x44, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x44, 0x45, 0x44, 0x5f,
	0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x41, 0x4c, 0x4c, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x44, 0x45,
	0x44, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x10, 0x02, 0x12, 0x26, 0x0a, 0x22, 0x45, 0x58, 0x54, 0x45,
	0x4e, 0x44, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x03,
	0x12, 0x26, 0x0a, 0x22, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x44, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52,
	0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x53, 0x10, 0x04, 0x2a, 0xaa, 0x03, 0x0a, 0x15, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x6e, 0x61, 0x69, 0x72, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x4e, 0x41,
	0x49, 0x52, 0x45, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2d, 0x0a, 0x29, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45, 0x5f, 0x54, 0x45, 0x4d, 0x50,
	0x4c, 0x41, 0x54, 0x45, 0x5f, 0x48, 0x49, 0x47, 0x48, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x2c, 0x0a, 0x28, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c,
	0x41, 0x54, 0x45, 0x5f, 0x48, 0x49, 0x47, 0x48, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x37, 0x0a, 0x33, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54,
	0x45, 0x5f, 0x48, 0x49, 0x47, 0x48, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x03,
	0x12, 0x35, 0x0a, 0x31, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52,
	0x45, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x4f, 0x50, 0x5f,
	0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54,
	0x59, 0x5f, 0x41, 0x43, 0x4b, 0x10, 0x04, 0x12, 0x2d, 0x0a, 0x29, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54,
	0x45, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x52, 0x45, 0x50,
	0x41, 0x4e, 0x43, 0x59, 0x10, 0x05, 0x12, 0x32, 0x0a, 0x2e, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49,
	0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45,
	0x5f, 0x43, 0x46, 0x52, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x06, 0x12, 0x39, 0x0a, 0x35, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45, 0x5f, 0x54, 0x45, 0x4d, 0x50,
	0x4c, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x46, 0x52, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56,
	0x49, 0x45, 0x57, 0x10, 0x07, 0x42, 0x6c, 0x0a, 0x34, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x5a, 0x34, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66,
	0x6f, 0x72, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_form_enums_proto_rawDescOnce sync.Once
	file_api_risk_case_management_form_enums_proto_rawDescData = file_api_risk_case_management_form_enums_proto_rawDesc
)

func file_api_risk_case_management_form_enums_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_form_enums_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_form_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_form_enums_proto_rawDescData)
	})
	return file_api_risk_case_management_form_enums_proto_rawDescData
}

var file_api_risk_case_management_form_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_api_risk_case_management_form_enums_proto_goTypes = []interface{}{
	(Status)(0),                       // 0: risk.case_management.form.Status
	(FormOrigin)(0),                   // 1: risk.case_management.form.FormOrigin
	(EntityType)(0),                   // 2: risk.case_management.form.EntityType
	(QuestionType)(0),                 // 3: risk.case_management.form.QuestionType
	(FileContentType)(0),              // 4: risk.case_management.form.FileContentType
	(QuestionResponseFieldMask)(0),    // 5: risk.case_management.form.QuestionResponseFieldMask
	(FormFieldMask)(0),                // 6: risk.case_management.form.FormFieldMask
	(QuestionFieldMask)(0),            // 7: risk.case_management.form.QuestionFieldMask
	(FormQuestionMappingFieldMask)(0), // 8: risk.case_management.form.FormQuestionMappingFieldMask
	(ExtendedFormFieldMask)(0),        // 9: risk.case_management.form.ExtendedFormFieldMask
	(QuestionnaireTemplate)(0),        // 10: risk.case_management.form.QuestionnaireTemplate
}
var file_api_risk_case_management_form_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_form_enums_proto_init() }
func file_api_risk_case_management_form_enums_proto_init() {
	if File_api_risk_case_management_form_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_form_enums_proto_rawDesc,
			NumEnums:      11,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_case_management_form_enums_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_form_enums_proto_depIdxs,
		EnumInfos:         file_api_risk_case_management_form_enums_proto_enumTypes,
	}.Build()
	File_api_risk_case_management_form_enums_proto = out.File
	file_api_risk_case_management_form_enums_proto_rawDesc = nil
	file_api_risk_case_management_form_enums_proto_goTypes = nil
	file_api_risk_case_management_form_enums_proto_depIdxs = nil
}
