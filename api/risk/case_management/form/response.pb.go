//go:generate gen_sql -types=Response,File

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/form/response.proto

package form

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// QuestionResponse stores user response to a question.
// Response is recorded against the question version for which response was submitted by user.
// Optional questions with no response will also be recorded with empty response.
type QuestionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FormId string `protobuf:"bytes,2,opt,name=form_id,json=formId,proto3" json:"form_id,omitempty"`
	// Question might not be the latest version
	// since the question version might change while submission is in progress.
	QuestionId string    `protobuf:"bytes,3,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	Response   *Response `protobuf:"bytes,4,opt,name=response,proto3" json:"response,omitempty"`
	// Timestamp at which question response was submitted by user.
	// It will be equivalent to form submission time if all questions are submitted
	// together with form.
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *QuestionResponse) Reset() {
	*x = QuestionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_response_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionResponse) ProtoMessage() {}

func (x *QuestionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_response_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionResponse.ProtoReflect.Descriptor instead.
func (*QuestionResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_response_proto_rawDescGZIP(), []int{0}
}

func (x *QuestionResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *QuestionResponse) GetFormId() string {
	if x != nil {
		return x.FormId
	}
	return ""
}

func (x *QuestionResponse) GetQuestionId() string {
	if x != nil {
		return x.QuestionId
	}
	return ""
}

func (x *QuestionResponse) GetResponse() *Response {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *QuestionResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *QuestionResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *QuestionResponse) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actual response value depending on the field type
	//
	// Types that are assignable to Response:
	//
	//	*Response_Value
	//	*Response_File
	Response isResponse_Response `protobuf_oneof:"response"`
}

func (x *Response) Reset() {
	*x = Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_response_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_response_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_response_proto_rawDescGZIP(), []int{1}
}

func (m *Response) GetResponse() isResponse_Response {
	if m != nil {
		return m.Response
	}
	return nil
}

func (x *Response) GetValue() *structpb.Value {
	if x, ok := x.GetResponse().(*Response_Value); ok {
		return x.Value
	}
	return nil
}

func (x *Response) GetFile() *File {
	if x, ok := x.GetResponse().(*Response_File); ok {
		return x.File
	}
	return nil
}

type isResponse_Response interface {
	isResponse_Response()
}

type Response_Value struct {
	Value *structpb.Value `protobuf:"bytes,1,opt,name=value,proto3,oneof"`
}

type Response_File struct {
	File *File `protobuf:"bytes,4,opt,name=file,proto3,oneof"`
}

func (*Response_Value) isResponse_Response() {}

func (*Response_File) isResponse_Response() {}

type File struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// S3 path of file
	S3Path string `protobuf:"bytes,1,opt,name=s3_path,json=s3Path,proto3" json:"s3_path,omitempty"`
}

func (x *File) Reset() {
	*x = File{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_response_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *File) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*File) ProtoMessage() {}

func (x *File) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_response_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use File.ProtoReflect.Descriptor instead.
func (*File) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_response_proto_rawDescGZIP(), []int{2}
}

func (x *File) GetS3Path() string {
	if x != nil {
		return x.S3Path
	}
	return ""
}

type QuestionWithResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Question    *Question         `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	Response    *QuestionResponse `protobuf:"bytes,2,opt,name=response,proto3" json:"response,omitempty"`
	IsMandatory bool              `protobuf:"varint,3,opt,name=is_mandatory,json=isMandatory,proto3" json:"is_mandatory,omitempty"`
}

func (x *QuestionWithResponse) Reset() {
	*x = QuestionWithResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_form_response_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionWithResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionWithResponse) ProtoMessage() {}

func (x *QuestionWithResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_form_response_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionWithResponse.ProtoReflect.Descriptor instead.
func (*QuestionWithResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_form_response_proto_rawDescGZIP(), []int{3}
}

func (x *QuestionWithResponse) GetQuestion() *Question {
	if x != nil {
		return x.Question
	}
	return nil
}

func (x *QuestionWithResponse) GetResponse() *QuestionResponse {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *QuestionWithResponse) GetIsMandatory() bool {
	if x != nil {
		return x.IsMandatory
	}
	return false
}

var File_api_risk_case_management_form_response_proto protoreflect.FileDescriptor

var file_api_risk_case_management_form_response_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xe0, 0x02, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06,
	0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x3f, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x22, 0x7d, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x35,
	0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x48, 0x00, 0x52,
	0x04, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x1f, 0x0a, 0x04, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x33, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x33, 0x50, 0x61,
	0x74, 0x68, 0x22, 0xc3, 0x01, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x57,
	0x69, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x08, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x08,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x42, 0x6c, 0x0a, 0x34, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d,
	0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73,
	0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_form_response_proto_rawDescOnce sync.Once
	file_api_risk_case_management_form_response_proto_rawDescData = file_api_risk_case_management_form_response_proto_rawDesc
)

func file_api_risk_case_management_form_response_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_form_response_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_form_response_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_form_response_proto_rawDescData)
	})
	return file_api_risk_case_management_form_response_proto_rawDescData
}

var file_api_risk_case_management_form_response_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_risk_case_management_form_response_proto_goTypes = []interface{}{
	(*QuestionResponse)(nil),      // 0: risk.case_management.form.QuestionResponse
	(*Response)(nil),              // 1: risk.case_management.form.Response
	(*File)(nil),                  // 2: risk.case_management.form.File
	(*QuestionWithResponse)(nil),  // 3: risk.case_management.form.QuestionWithResponse
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
	(*structpb.Value)(nil),        // 5: google.protobuf.Value
	(*Question)(nil),              // 6: risk.case_management.form.Question
}
var file_api_risk_case_management_form_response_proto_depIdxs = []int32{
	1, // 0: risk.case_management.form.QuestionResponse.response:type_name -> risk.case_management.form.Response
	4, // 1: risk.case_management.form.QuestionResponse.created_at:type_name -> google.protobuf.Timestamp
	4, // 2: risk.case_management.form.QuestionResponse.updated_at:type_name -> google.protobuf.Timestamp
	4, // 3: risk.case_management.form.QuestionResponse.deleted_at:type_name -> google.protobuf.Timestamp
	5, // 4: risk.case_management.form.Response.value:type_name -> google.protobuf.Value
	2, // 5: risk.case_management.form.Response.file:type_name -> risk.case_management.form.File
	6, // 6: risk.case_management.form.QuestionWithResponse.question:type_name -> risk.case_management.form.Question
	0, // 7: risk.case_management.form.QuestionWithResponse.response:type_name -> risk.case_management.form.QuestionResponse
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_form_response_proto_init() }
func file_api_risk_case_management_form_response_proto_init() {
	if File_api_risk_case_management_form_response_proto != nil {
		return
	}
	file_api_risk_case_management_form_question_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_case_management_form_response_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuestionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_response_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_response_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*File); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_form_response_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuestionWithResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_case_management_form_response_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Response_Value)(nil),
		(*Response_File)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_form_response_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_case_management_form_response_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_form_response_proto_depIdxs,
		MessageInfos:      file_api_risk_case_management_form_response_proto_msgTypes,
	}.Build()
	File_api_risk_case_management_form_response_proto = out.File
	file_api_risk_case_management_form_response_proto_rawDesc = nil
	file_api_risk_case_management_form_response_proto_goTypes = nil
	file_api_risk_case_management_form_response_proto_depIdxs = nil
}
