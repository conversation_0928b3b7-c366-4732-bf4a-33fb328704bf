// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/case_management/form/form.proto

package form

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Form with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Form) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Form with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in FormMultiError, or nil if none found.
func (m *Form) ValidateAll() error {
	return m.validate(true)
}

func (m *Form) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CaseId

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := FormValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _Form_Status_NotInLookup[m.GetStatus()]; ok {
		err := FormValidationError{
			field:  "Status",
			reason: "value must not be in list [STATUS_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetClientReqId()) < 1 {
		err := FormValidationError{
			field:  "ClientReqId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _Form_Origin_NotInLookup[m.GetOrigin()]; ok {
		err := FormValidationError{
			field:  "Origin",
			reason: "value must not be in list [FORM_ORIGIN_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetWorkflowReqId()) < 1 {
		err := FormValidationError{
			field:  "WorkflowReqId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAddedBy()) < 1 {
		err := FormValidationError{
			field:  "AddedBy",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpireAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormValidationError{
					field:  "ExpireAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormValidationError{
					field:  "ExpireAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpireAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormValidationError{
				field:  "ExpireAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FormMultiError(errors)
	}

	return nil
}

// FormMultiError is an error wrapping multiple validation errors returned by
// Form.ValidateAll() if the designated constraints aren't met.
type FormMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FormMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FormMultiError) AllErrors() []error { return m }

// FormValidationError is the validation error returned by Form.Validate if the
// designated constraints aren't met.
type FormValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FormValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FormValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FormValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FormValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FormValidationError) ErrorName() string { return "FormValidationError" }

// Error satisfies the builtin error interface
func (e FormValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForm.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FormValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FormValidationError{}

var _Form_Status_NotInLookup = map[Status]struct{}{
	0: {},
}

var _Form_Origin_NotInLookup = map[FormOrigin]struct{}{
	0: {},
}

// Validate checks the field values on FormQuestionMapping with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FormQuestionMapping) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FormQuestionMapping with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FormQuestionMappingMultiError, or nil if none found.
func (m *FormQuestionMapping) ValidateAll() error {
	return m.validate(true)
}

func (m *FormQuestionMapping) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if utf8.RuneCountInString(m.GetFormId()) < 1 {
		err := FormQuestionMappingValidationError{
			field:  "FormId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetQuestionCode()) < 1 {
		err := FormQuestionMappingValidationError{
			field:  "QuestionCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IsMandatory

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormQuestionMappingValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormQuestionMappingValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormQuestionMappingValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormQuestionMappingValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormQuestionMappingValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormQuestionMappingValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormQuestionMappingValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormQuestionMappingValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormQuestionMappingValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FormQuestionMappingMultiError(errors)
	}

	return nil
}

// FormQuestionMappingMultiError is an error wrapping multiple validation
// errors returned by FormQuestionMapping.ValidateAll() if the designated
// constraints aren't met.
type FormQuestionMappingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FormQuestionMappingMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FormQuestionMappingMultiError) AllErrors() []error { return m }

// FormQuestionMappingValidationError is the validation error returned by
// FormQuestionMapping.Validate if the designated constraints aren't met.
type FormQuestionMappingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FormQuestionMappingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FormQuestionMappingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FormQuestionMappingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FormQuestionMappingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FormQuestionMappingValidationError) ErrorName() string {
	return "FormQuestionMappingValidationError"
}

// Error satisfies the builtin error interface
func (e FormQuestionMappingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFormQuestionMapping.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FormQuestionMappingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FormQuestionMappingValidationError{}

// Validate checks the field values on FormFilters with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FormFilters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FormFilters with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FormFiltersMultiError, or
// nil if none found.
func (m *FormFilters) ValidateAll() error {
	return m.validate(true)
}

func (m *FormFilters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *FormFilters_ActorId:
		if v == nil {
			err := FormFiltersValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	case *FormFilters_CaseId:
		if v == nil {
			err := FormFiltersValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for CaseId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return FormFiltersMultiError(errors)
	}

	return nil
}

// FormFiltersMultiError is an error wrapping multiple validation errors
// returned by FormFilters.ValidateAll() if the designated constraints aren't met.
type FormFiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FormFiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FormFiltersMultiError) AllErrors() []error { return m }

// FormFiltersValidationError is the validation error returned by
// FormFilters.Validate if the designated constraints aren't met.
type FormFiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FormFiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FormFiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FormFiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FormFiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FormFiltersValidationError) ErrorName() string { return "FormFiltersValidationError" }

// Error satisfies the builtin error interface
func (e FormFiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFormFilters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FormFiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FormFiltersValidationError{}

// Validate checks the field values on ExtendedForm with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExtendedForm) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExtendedForm with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExtendedFormMultiError, or
// nil if none found.
func (m *ExtendedForm) ValidateAll() error {
	return m.validate(true)
}

func (m *ExtendedForm) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetForm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExtendedFormValidationError{
					field:  "Form",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExtendedFormValidationError{
					field:  "Form",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExtendedFormValidationError{
				field:  "Form",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExtendedFormValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExtendedFormValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExtendedFormValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetResponses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExtendedFormValidationError{
						field:  fmt.Sprintf("Responses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExtendedFormValidationError{
						field:  fmt.Sprintf("Responses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExtendedFormValidationError{
					field:  fmt.Sprintf("Responses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ExtendedFormMultiError(errors)
	}

	return nil
}

// ExtendedFormMultiError is an error wrapping multiple validation errors
// returned by ExtendedForm.ValidateAll() if the designated constraints aren't met.
type ExtendedFormMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExtendedFormMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExtendedFormMultiError) AllErrors() []error { return m }

// ExtendedFormValidationError is the validation error returned by
// ExtendedForm.Validate if the designated constraints aren't met.
type ExtendedFormValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExtendedFormValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExtendedFormValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExtendedFormValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExtendedFormValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExtendedFormValidationError) ErrorName() string { return "ExtendedFormValidationError" }

// Error satisfies the builtin error interface
func (e ExtendedFormValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExtendedForm.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExtendedFormValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExtendedFormValidationError{}

// Validate checks the field values on FormOrchestrationParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FormOrchestrationParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FormOrchestrationParams with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FormOrchestrationParamsMultiError, or nil if none found.
func (m *FormOrchestrationParams) ValidateAll() error {
	return m.validate(true)
}

func (m *FormOrchestrationParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetQuestionIdentifiers() == nil {
		err := FormOrchestrationParamsValidationError{
			field:  "QuestionIdentifiers",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetQuestionIdentifiers()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormOrchestrationParamsValidationError{
					field:  "QuestionIdentifiers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormOrchestrationParamsValidationError{
					field:  "QuestionIdentifiers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuestionIdentifiers()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormOrchestrationParamsValidationError{
				field:  "QuestionIdentifiers",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetForm() == nil {
		err := FormOrchestrationParamsValidationError{
			field:  "Form",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetForm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormOrchestrationParamsValidationError{
					field:  "Form",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormOrchestrationParamsValidationError{
					field:  "Form",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormOrchestrationParamsValidationError{
				field:  "Form",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNotification()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormOrchestrationParamsValidationError{
					field:  "Notification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormOrchestrationParamsValidationError{
					field:  "Notification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotification()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormOrchestrationParamsValidationError{
				field:  "Notification",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReminderNotification()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormOrchestrationParamsValidationError{
					field:  "ReminderNotification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormOrchestrationParamsValidationError{
					field:  "ReminderNotification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReminderNotification()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormOrchestrationParamsValidationError{
				field:  "ReminderNotification",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FormOrchestrationParamsMultiError(errors)
	}

	return nil
}

// FormOrchestrationParamsMultiError is an error wrapping multiple validation
// errors returned by FormOrchestrationParams.ValidateAll() if the designated
// constraints aren't met.
type FormOrchestrationParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FormOrchestrationParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FormOrchestrationParamsMultiError) AllErrors() []error { return m }

// FormOrchestrationParamsValidationError is the validation error returned by
// FormOrchestrationParams.Validate if the designated constraints aren't met.
type FormOrchestrationParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FormOrchestrationParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FormOrchestrationParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FormOrchestrationParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FormOrchestrationParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FormOrchestrationParamsValidationError) ErrorName() string {
	return "FormOrchestrationParamsValidationError"
}

// Error satisfies the builtin error interface
func (e FormOrchestrationParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFormOrchestrationParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FormOrchestrationParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FormOrchestrationParamsValidationError{}
