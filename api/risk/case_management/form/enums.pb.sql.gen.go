// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/risk/case_management/form/enums.pb.go

package form

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the Status in string format in DB
func (p Status) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Status while reading from DB
func (p *Status) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Status_value[val]
	if !ok {
		return fmt.Errorf("unexpected Status value: %s", val)
	}
	*p = Status(valInt)
	return nil
}

// Marshaler interface implementation for Status
func (x Status) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Status
func (x *Status) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Status(Status_value[val])
	return nil
}

// Valuer interface implementation for storing the FormOrigin in string format in DB
func (p FormOrigin) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing FormOrigin while reading from DB
func (p *FormOrigin) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := FormOrigin_value[val]
	if !ok {
		return fmt.Errorf("unexpected FormOrigin value: %s", val)
	}
	*p = FormOrigin(valInt)
	return nil
}

// Marshaler interface implementation for FormOrigin
func (x FormOrigin) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for FormOrigin
func (x *FormOrigin) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = FormOrigin(FormOrigin_value[val])
	return nil
}

// Valuer interface implementation for storing the EntityType in string format in DB
func (p EntityType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing EntityType while reading from DB
func (p *EntityType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := EntityType_value[val]
	if !ok {
		return fmt.Errorf("unexpected EntityType value: %s", val)
	}
	*p = EntityType(valInt)
	return nil
}

// Marshaler interface implementation for EntityType
func (x EntityType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for EntityType
func (x *EntityType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = EntityType(EntityType_value[val])
	return nil
}

// Valuer interface implementation for storing the QuestionType in string format in DB
func (p QuestionType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing QuestionType while reading from DB
func (p *QuestionType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := QuestionType_value[val]
	if !ok {
		return fmt.Errorf("unexpected QuestionType value: %s", val)
	}
	*p = QuestionType(valInt)
	return nil
}

// Marshaler interface implementation for QuestionType
func (x QuestionType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for QuestionType
func (x *QuestionType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = QuestionType(QuestionType_value[val])
	return nil
}

// Valuer interface implementation for storing the FileContentType in string format in DB
func (p FileContentType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing FileContentType while reading from DB
func (p *FileContentType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := FileContentType_value[val]
	if !ok {
		return fmt.Errorf("unexpected FileContentType value: %s", val)
	}
	*p = FileContentType(valInt)
	return nil
}

// Marshaler interface implementation for FileContentType
func (x FileContentType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for FileContentType
func (x *FileContentType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = FileContentType(FileContentType_value[val])
	return nil
}
