package review

import (
	"context"
	"reflect"
	"testing"

	vgCrmPb "github.com/epifi/gamma/api/vendorgateway/crm"
	riskPb "github.com/epifi/gamma/api/vendorgateway/crm/risk"
	"github.com/epifi/be-common/pkg/logger"
)

func TestGetCMAnalystFromCrmAgent(t *testing.T) {
	logger.Init("test") // Don't want debug logs
	type args struct {
		vgCrmAnalyst *vgCrmPb.Agent
	}
	tests := []struct {
		name    string
		args    args
		want    *Analyst
		wantErr bool
	}{
		{
			name:    "Fails for empty input agent",
			args:    args{},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Success - fails to parse a agent group",
			args: args{
				vgCrmAnalyst: &vgCrmPb.Agent{
					AdditionalDetails: &vgCrmPb.Agent_RiskAgentAdditionalDetails{
						RiskAgentAdditionalDetails: &riskPb.RiskAgentAdditionalDetails{
							Groups: []riskPb.AgentGroup{
								riskPb.AgentGroup_AGENT_GROUP_UNSPECIFIED,
								riskPb.AgentGroup_AGENT_GROUP_USER_REVIEW,
							},
						},
					},
				},
			},
			want:    &Analyst{Groups: []AnalystGroup{AnalystGroup_ANALYST_GROUP_USER_REVIEW}},
			wantErr: false,
		},
		{
			name: "Success",
			args: args{
				vgCrmAnalyst: &vgCrmPb.Agent{
					AdditionalDetails: &vgCrmPb.Agent_RiskAgentAdditionalDetails{
						RiskAgentAdditionalDetails: &riskPb.RiskAgentAdditionalDetails{
							Groups: []riskPb.AgentGroup{
								riskPb.AgentGroup_AGENT_GROUP_USER_REVIEW,
								riskPb.AgentGroup_AGENT_GROUP_TRANSACTION_REVIEW,
							},
						},
					},
				},
			},
			want: &Analyst{Groups: []AnalystGroup{
				AnalystGroup_ANALYST_GROUP_USER_REVIEW,
				AnalystGroup_ANALYST_GROUP_TRANSACTION_REVIEW,
			}},
			wantErr: false,
		},
		{
			name: "Success with escalation",
			args: args{
				vgCrmAnalyst: &vgCrmPb.Agent{
					AdditionalDetails: &vgCrmPb.Agent_RiskAgentAdditionalDetails{
						RiskAgentAdditionalDetails: &riskPb.RiskAgentAdditionalDetails{
							Groups: []riskPb.AgentGroup{
								riskPb.AgentGroup_AGENT_GROUP_USER_REVIEW,
								riskPb.AgentGroup_AGENT_GROUP_TRANSACTION_REVIEW,
								riskPb.AgentGroup_AGENT_GROUP_ESCALATION,
							},
						},
					},
				},
			},
			want: &Analyst{Groups: []AnalystGroup{
				AnalystGroup_ANALYST_GROUP_USER_REVIEW,
				AnalystGroup_ANALYST_GROUP_TRANSACTION_REVIEW,
				AnalystGroup_ANALYST_GROUP_ESCALATION,
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetCMAnalystFromCrmAgent(context.Background(), tt.args.vgCrmAnalyst)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCMAnalystFromCrmAgent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCMAnalystFromCrmAgent() got = %v, want %v", got, tt.want)
			}
		})
	}
}
