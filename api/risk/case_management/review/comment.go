package review

import (
	"errors"

	"github.com/epifi/be-common/pkg/epifierrors"
)

var ErrCouldNotSetCommentType = errors.New("could not set comment type")

func (c *Comment) SetCommentType(commentType string) error {
	if len(commentType) == 0 {
		return epifierrors.ErrInvalidArgument
	}
	switch c.GetEntityType() {
	case ReviewEntityType_REVIEW_ENTITY_TYPE_USER:
		c.CommentType = &CommentType{
			CommentType: &CommentType_UserCommentType{
				UserCommentType: UserCommentType(UserCommentType_value[commentType]),
			}}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_TRANSACTION:
		c.CommentType = &CommentType{
			CommentType: &CommentType_TxnCommentType{
				TxnCommentType: TxnCommentType(TxnCommentType_value[commentType]),
			}}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_LIVENESS:
		c.CommentType = &CommentType{
			CommentType: &CommentType_LivenessCommentType{
				LivenessCommentType: LivenessCommentType(LivenessCommentType_value[commentType]),
			}}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_CASE:
		c.CommentType = &CommentType{
			CommentType: &CommentType_CaseCommentType{
				CaseCommentType: CaseCommentType(CaseCommentType_value[commentType]),
			}}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_FACEMATCH:
		c.CommentType = &CommentType{
			CommentType: &CommentType_FacematchCommentType{
				FacematchCommentType: FacematchCommentType(FacematchCommentType_value[commentType]),
			}}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_AFU:
		c.CommentType = &CommentType{
			CommentType: &CommentType_AfuCommentType{
				AfuCommentType: AfuCommentType(AfuCommentType_value[commentType]),
			}}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_LEA_COMPLAINT:
		c.CommentType = &CommentType{
			CommentType: &CommentType_LeaComplaintCommentType{
				LeaComplaintCommentType: LEAComplaintCommentType(LEAComplaintCommentType_value[commentType]),
			}}
	default:
		return ErrCouldNotSetCommentType
	}
	return nil
}
