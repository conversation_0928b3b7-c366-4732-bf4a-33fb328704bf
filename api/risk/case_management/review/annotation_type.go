package review

import (
	"errors"
)

var CouldNotSetAnnotationType = errors.New("could not set comment type")

func (a *AllowedAnnotation) SetAnnotationType(annotationType string) error {
	if len(annotationType) == 0 {
		return nil
	}
	switch a.GetEntityType() {
	case ReviewEntityType_REVIEW_ENTITY_TYPE_USER:
		a.AnnotationType = &AnnotationType{
			AnnotationType: &AnnotationType_UserAnnotationType{
				UserAnnotationType: UserAnnotationType(UserAnnotationType_value[annotationType]),
			}}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_TRANSACTION:
		a.AnnotationType = &AnnotationType{
			AnnotationType: &AnnotationType_TxnAnnotationType{
				TxnAnnotationType: TxnAnnotationType(TxnAnnotationType_value[annotationType]),
			}}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_LIVENESS:
		a.AnnotationType = &AnnotationType{
			AnnotationType: &AnnotationType_LivenessAnnotationType{
				LivenessAnnotationType: LivenessAnnotationType(LivenessAnnotationType_value[annotationType]),
			}}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_CASE:
		a.AnnotationType = &AnnotationType{
			AnnotationType: &AnnotationType_CaseAnnotationType{
				CaseAnnotationType: CaseAnnotationType(CaseAnnotationType_value[annotationType]),
			}}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_RULE:
		a.AnnotationType = &AnnotationType{
			AnnotationType: &AnnotationType_RuleAnnotationType{
				RuleAnnotationType: RuleAnnotationType(RuleAnnotationType_value[annotationType]),
			}}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_FACEMATCH:
		a.AnnotationType = &AnnotationType{
			AnnotationType: &AnnotationType_FaceMatchAnnotationType{
				FaceMatchAnnotationType: FaceMatchAnnotationType(FaceMatchAnnotationType_value[annotationType]),
			}}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_AFU:
		a.AnnotationType = &AnnotationType{
			AnnotationType: &AnnotationType_AfuAnnotationType{
				AfuAnnotationType: AfuAnnotationType(AfuAnnotationType_value[annotationType]),
			}}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_LEA_COMPLAINT:
		a.AnnotationType = &AnnotationType{
			AnnotationType: &AnnotationType_LeaComplaintAnnotationType{
				LeaComplaintAnnotationType: LEAComplaintAnnotationType(LEAComplaintAnnotationType_value[annotationType]),
			}}
	default:
		return CouldNotSetAnnotationType
	}
	return nil
}
