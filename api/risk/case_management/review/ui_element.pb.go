// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/review/ui_element.proto

package review

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UIElement defines a review element for a element type.
// e.g., For user review page, element type will be page and page name can be name.
type UIElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type UIElementType `protobuf:"varint,1,opt,name=type,proto3,enum=risk.case_management.review.UIElementType" json:"type,omitempty"`
	Name string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *UIElement) Reset() {
	*x = UIElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_ui_element_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UIElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIElement) ProtoMessage() {}

func (x *UIElement) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_ui_element_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIElement.ProtoReflect.Descriptor instead.
func (*UIElement) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_ui_element_proto_rawDescGZIP(), []int{0}
}

func (x *UIElement) GetType() UIElementType {
	if x != nil {
		return x.Type
	}
	return UIElementType_UI_ELEMENT_TYPE_UNSPECIFIED
}

func (x *UIElement) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type UIElementAnnotationTypeMapping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Element *UIElement `protobuf:"bytes,2,opt,name=element,proto3" json:"element,omitempty"`
	// Id of mapped annotation type
	AnnotationTypeId string `protobuf:"bytes,3,opt,name=annotation_type_id,json=annotationTypeId,proto3" json:"annotation_type_id,omitempty"`
	// Whether annotation type is mandatory for ui element.
	IsMandatory bool `protobuf:"varint,4,opt,name=is_mandatory,json=isMandatory,proto3" json:"is_mandatory,omitempty"`
	// Defines whether multiple annotation values can be selected for annotation type.
	IsMultiSelect bool                   `protobuf:"varint,5,opt,name=is_multi_select,json=isMultiSelect,proto3" json:"is_multi_select,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *UIElementAnnotationTypeMapping) Reset() {
	*x = UIElementAnnotationTypeMapping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_ui_element_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UIElementAnnotationTypeMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIElementAnnotationTypeMapping) ProtoMessage() {}

func (x *UIElementAnnotationTypeMapping) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_ui_element_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIElementAnnotationTypeMapping.ProtoReflect.Descriptor instead.
func (*UIElementAnnotationTypeMapping) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_ui_element_proto_rawDescGZIP(), []int{1}
}

func (x *UIElementAnnotationTypeMapping) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UIElementAnnotationTypeMapping) GetElement() *UIElement {
	if x != nil {
		return x.Element
	}
	return nil
}

func (x *UIElementAnnotationTypeMapping) GetAnnotationTypeId() string {
	if x != nil {
		return x.AnnotationTypeId
	}
	return ""
}

func (x *UIElementAnnotationTypeMapping) GetIsMandatory() bool {
	if x != nil {
		return x.IsMandatory
	}
	return false
}

func (x *UIElementAnnotationTypeMapping) GetIsMultiSelect() bool {
	if x != nil {
		return x.IsMultiSelect
	}
	return false
}

func (x *UIElementAnnotationTypeMapping) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *UIElementAnnotationTypeMapping) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Contains all annotation types with values for an ui element.
type UIElementAllowedAnnotations struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of annotation types with values.
	AnnotationTypesDetails []*UIElementAllowedAnnotations_AnnotationTypeDetails `protobuf:"bytes,1,rep,name=annotation_types_details,json=annotationTypesDetails,proto3" json:"annotation_types_details,omitempty"`
}

func (x *UIElementAllowedAnnotations) Reset() {
	*x = UIElementAllowedAnnotations{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_ui_element_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UIElementAllowedAnnotations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIElementAllowedAnnotations) ProtoMessage() {}

func (x *UIElementAllowedAnnotations) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_ui_element_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIElementAllowedAnnotations.ProtoReflect.Descriptor instead.
func (*UIElementAllowedAnnotations) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_ui_element_proto_rawDescGZIP(), []int{2}
}

func (x *UIElementAllowedAnnotations) GetAnnotationTypesDetails() []*UIElementAllowedAnnotations_AnnotationTypeDetails {
	if x != nil {
		return x.AnnotationTypesDetails
	}
	return nil
}

type AllowedAnnotationFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Filter:
	//
	//	*AllowedAnnotationFilters_UiElement
	Filter isAllowedAnnotationFilters_Filter `protobuf_oneof:"filter"`
}

func (x *AllowedAnnotationFilters) Reset() {
	*x = AllowedAnnotationFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_ui_element_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllowedAnnotationFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllowedAnnotationFilters) ProtoMessage() {}

func (x *AllowedAnnotationFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_ui_element_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllowedAnnotationFilters.ProtoReflect.Descriptor instead.
func (*AllowedAnnotationFilters) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_ui_element_proto_rawDescGZIP(), []int{3}
}

func (m *AllowedAnnotationFilters) GetFilter() isAllowedAnnotationFilters_Filter {
	if m != nil {
		return m.Filter
	}
	return nil
}

func (x *AllowedAnnotationFilters) GetUiElement() *UIElement {
	if x, ok := x.GetFilter().(*AllowedAnnotationFilters_UiElement); ok {
		return x.UiElement
	}
	return nil
}

type isAllowedAnnotationFilters_Filter interface {
	isAllowedAnnotationFilters_Filter()
}

type AllowedAnnotationFilters_UiElement struct {
	UiElement *UIElement `protobuf:"bytes,1,opt,name=ui_element,json=uiElement,proto3,oneof"` // TODO: add more filters such as annotation type, id etc.
}

func (*AllowedAnnotationFilters_UiElement) isAllowedAnnotationFilters_Filter() {}

type UIElementAllowedAnnotations_AnnotationTypeDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mapping *UIElementAnnotationTypeMapping `protobuf:"bytes,1,opt,name=mapping,proto3" json:"mapping,omitempty"`
	// Annotation type with list of values mapped against an ui element.
	TypeWithValues *AnnotationTypeWithValues `protobuf:"bytes,2,opt,name=type_with_values,json=typeWithValues,proto3" json:"type_with_values,omitempty"`
}

func (x *UIElementAllowedAnnotations_AnnotationTypeDetails) Reset() {
	*x = UIElementAllowedAnnotations_AnnotationTypeDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_ui_element_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UIElementAllowedAnnotations_AnnotationTypeDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIElementAllowedAnnotations_AnnotationTypeDetails) ProtoMessage() {}

func (x *UIElementAllowedAnnotations_AnnotationTypeDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_ui_element_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIElementAllowedAnnotations_AnnotationTypeDetails.ProtoReflect.Descriptor instead.
func (*UIElementAllowedAnnotations_AnnotationTypeDetails) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_ui_element_proto_rawDescGZIP(), []int{2, 0}
}

func (x *UIElementAllowedAnnotations_AnnotationTypeDetails) GetMapping() *UIElementAnnotationTypeMapping {
	if x != nil {
		return x.Mapping
	}
	return nil
}

func (x *UIElementAllowedAnnotations_AnnotationTypeDetails) GetTypeWithValues() *AnnotationTypeWithValues {
	if x != nil {
		return x.TypeWithValues
	}
	return nil
}

var File_api_risk_case_management_review_ui_element_proto protoreflect.FileDescriptor

var file_api_risk_case_management_review_ui_element_proto_rawDesc = []byte{
	0x0a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x2f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1b, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x1a,
	0x38, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x2f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x5f, 0x0a, 0x09, 0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3e, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0xf4, 0x02, 0x0a, 0x1e, 0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x61, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x4a, 0x0a, 0x07, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x35, 0x0a, 0x12, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x10, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x6d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69,
	0x73, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73,
	0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xfa, 0x02, 0x0a, 0x1b, 0x55, 0x49, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x88, 0x01, 0x0a, 0x18, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x16, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x1a, 0xcf, 0x01, 0x0a, 0x15, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x55, 0x0a,
	0x07, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x55, 0x49, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x6d, 0x61, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x12, 0x5f, 0x0a, 0x10, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x77, 0x69, 0x74,
	0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x57, 0x69, 0x74, 0x68, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x0e, 0x74, 0x79, 0x70, 0x65, 0x57, 0x69, 0x74, 0x68, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x6d, 0x0a, 0x18, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64,
	0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x47, 0x0a, 0x0a, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52,
	0x09, 0x75, 0x69, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x08, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x42, 0x70, 0x0a, 0x36, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5a, 0x36,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_review_ui_element_proto_rawDescOnce sync.Once
	file_api_risk_case_management_review_ui_element_proto_rawDescData = file_api_risk_case_management_review_ui_element_proto_rawDesc
)

func file_api_risk_case_management_review_ui_element_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_review_ui_element_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_review_ui_element_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_review_ui_element_proto_rawDescData)
	})
	return file_api_risk_case_management_review_ui_element_proto_rawDescData
}

var file_api_risk_case_management_review_ui_element_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_risk_case_management_review_ui_element_proto_goTypes = []interface{}{
	(*UIElement)(nil),                                         // 0: risk.case_management.review.UIElement
	(*UIElementAnnotationTypeMapping)(nil),                    // 1: risk.case_management.review.UIElementAnnotationTypeMapping
	(*UIElementAllowedAnnotations)(nil),                       // 2: risk.case_management.review.UIElementAllowedAnnotations
	(*AllowedAnnotationFilters)(nil),                          // 3: risk.case_management.review.AllowedAnnotationFilters
	(*UIElementAllowedAnnotations_AnnotationTypeDetails)(nil), // 4: risk.case_management.review.UIElementAllowedAnnotations.AnnotationTypeDetails
	(UIElementType)(0),                                        // 5: risk.case_management.review.UIElementType
	(*timestamppb.Timestamp)(nil),                             // 6: google.protobuf.Timestamp
	(*AnnotationTypeWithValues)(nil),                          // 7: risk.case_management.review.AnnotationTypeWithValues
}
var file_api_risk_case_management_review_ui_element_proto_depIdxs = []int32{
	5, // 0: risk.case_management.review.UIElement.type:type_name -> risk.case_management.review.UIElementType
	0, // 1: risk.case_management.review.UIElementAnnotationTypeMapping.element:type_name -> risk.case_management.review.UIElement
	6, // 2: risk.case_management.review.UIElementAnnotationTypeMapping.created_at:type_name -> google.protobuf.Timestamp
	6, // 3: risk.case_management.review.UIElementAnnotationTypeMapping.updated_at:type_name -> google.protobuf.Timestamp
	4, // 4: risk.case_management.review.UIElementAllowedAnnotations.annotation_types_details:type_name -> risk.case_management.review.UIElementAllowedAnnotations.AnnotationTypeDetails
	0, // 5: risk.case_management.review.AllowedAnnotationFilters.ui_element:type_name -> risk.case_management.review.UIElement
	1, // 6: risk.case_management.review.UIElementAllowedAnnotations.AnnotationTypeDetails.mapping:type_name -> risk.case_management.review.UIElementAnnotationTypeMapping
	7, // 7: risk.case_management.review.UIElementAllowedAnnotations.AnnotationTypeDetails.type_with_values:type_name -> risk.case_management.review.AnnotationTypeWithValues
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_review_ui_element_proto_init() }
func file_api_risk_case_management_review_ui_element_proto_init() {
	if File_api_risk_case_management_review_ui_element_proto != nil {
		return
	}
	file_api_risk_case_management_review_allowed_annotation_proto_init()
	file_api_risk_case_management_review_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_case_management_review_ui_element_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UIElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_ui_element_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UIElementAnnotationTypeMapping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_ui_element_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UIElementAllowedAnnotations); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_ui_element_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllowedAnnotationFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_ui_element_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UIElementAllowedAnnotations_AnnotationTypeDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_case_management_review_ui_element_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*AllowedAnnotationFilters_UiElement)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_review_ui_element_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_case_management_review_ui_element_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_review_ui_element_proto_depIdxs,
		MessageInfos:      file_api_risk_case_management_review_ui_element_proto_msgTypes,
	}.Build()
	File_api_risk_case_management_review_ui_element_proto = out.File
	file_api_risk_case_management_review_ui_element_proto_rawDesc = nil
	file_api_risk_case_management_review_ui_element_proto_goTypes = nil
	file_api_risk_case_management_review_ui_element_proto_depIdxs = nil
}
