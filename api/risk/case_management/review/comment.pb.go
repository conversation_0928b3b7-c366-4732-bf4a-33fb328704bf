// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/review/comment.proto

package review

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UserCommentType defines the allowed user comment type
type UserCommentType int32

const (
	UserCommentType_USER_COMMENT_TYPE_UNSPECIFIED UserCommentType = 0
	UserCommentType_USER_COMMENT_TYPE_ALL         UserCommentType = 1
)

// Enum value maps for UserCommentType.
var (
	UserCommentType_name = map[int32]string{
		0: "USER_COMMENT_TYPE_UNSPECIFIED",
		1: "USER_COMMENT_TYPE_ALL",
	}
	UserCommentType_value = map[string]int32{
		"USER_COMMENT_TYPE_UNSPECIFIED": 0,
		"USER_COMMENT_TYPE_ALL":         1,
	}
)

func (x UserCommentType) Enum() *UserCommentType {
	p := new(UserCommentType)
	*p = x
	return p
}

func (x UserCommentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserCommentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_review_comment_proto_enumTypes[0].Descriptor()
}

func (UserCommentType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_review_comment_proto_enumTypes[0]
}

func (x UserCommentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserCommentType.Descriptor instead.
func (UserCommentType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_comment_proto_rawDescGZIP(), []int{0}
}

// TxnCommentType defines the allowed transaction comment type
type TxnCommentType int32

const (
	TxnCommentType_TXN_COMMENT_TYPE_UNSPECIFIED TxnCommentType = 0
	TxnCommentType_TXN_COMMENT_TYPE_ALL         TxnCommentType = 1
)

// Enum value maps for TxnCommentType.
var (
	TxnCommentType_name = map[int32]string{
		0: "TXN_COMMENT_TYPE_UNSPECIFIED",
		1: "TXN_COMMENT_TYPE_ALL",
	}
	TxnCommentType_value = map[string]int32{
		"TXN_COMMENT_TYPE_UNSPECIFIED": 0,
		"TXN_COMMENT_TYPE_ALL":         1,
	}
)

func (x TxnCommentType) Enum() *TxnCommentType {
	p := new(TxnCommentType)
	*p = x
	return p
}

func (x TxnCommentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TxnCommentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_review_comment_proto_enumTypes[1].Descriptor()
}

func (TxnCommentType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_review_comment_proto_enumTypes[1]
}

func (x TxnCommentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TxnCommentType.Descriptor instead.
func (TxnCommentType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_comment_proto_rawDescGZIP(), []int{1}
}

// LivenessCommentType defines the allowed Liveness comment type
type LivenessCommentType int32

const (
	LivenessCommentType_LIVENESS_COMMENT_TYPE_UNSPECIFIED LivenessCommentType = 0
	LivenessCommentType_LIVENESS_COMMENT_TYPE_ALL         LivenessCommentType = 1
)

// Enum value maps for LivenessCommentType.
var (
	LivenessCommentType_name = map[int32]string{
		0: "LIVENESS_COMMENT_TYPE_UNSPECIFIED",
		1: "LIVENESS_COMMENT_TYPE_ALL",
	}
	LivenessCommentType_value = map[string]int32{
		"LIVENESS_COMMENT_TYPE_UNSPECIFIED": 0,
		"LIVENESS_COMMENT_TYPE_ALL":         1,
	}
)

func (x LivenessCommentType) Enum() *LivenessCommentType {
	p := new(LivenessCommentType)
	*p = x
	return p
}

func (x LivenessCommentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LivenessCommentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_review_comment_proto_enumTypes[2].Descriptor()
}

func (LivenessCommentType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_review_comment_proto_enumTypes[2]
}

func (x LivenessCommentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LivenessCommentType.Descriptor instead.
func (LivenessCommentType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_comment_proto_rawDescGZIP(), []int{2}
}

// CaseCommentType defines the allowed Liveness comment type
type CaseCommentType int32

const (
	CaseCommentType_CASE_COMMENT_TYPE_UNSPECIFIED CaseCommentType = 0
	// Deprecated: Marked as deprecated in api/risk/case_management/review/comment.proto.
	CaseCommentType_CASE_COMMENT_TYPE_ALL CaseCommentType = 1
	// Comment type to be used by default for any adhoc comments on the case
	// can be used in cases where comment is not related to a particular use case
	CaseCommentType_CASE_COMMENT_TYPE_REMARKS CaseCommentType = 2
	// Investigation notes to be saved against the case to back up the verdict given OR
	// to sharing context for further reviews
	CaseCommentType_CASE_COMMENT_TYPE_INVESTIGATION_NOTES CaseCommentType = 3
	// If more information is required from the user and case needs to be moved to outbound call flow,
	// questions to be asked can be added with this comment type
	CaseCommentType_CASE_COMMENT_TYPE_USER_INFO_QUESTIONS CaseCommentType = 4
	// Comment type to be used for capturing user response for the questions in the outbound call flow
	CaseCommentType_CASE_COMMENT_TYPE_USER_RESPONSE CaseCommentType = 5
	// Comment type to be used for snooze case comments by analysts
	CaseCommentType_CASE_COMMENT_TYPE_SNOOZE CaseCommentType = 6
	// comment type to be used when there are multiple actions against the case are possible
	// and out of which system picks the best actions to be picked.
	// Not keeping the system in the name as it can be manually triggered as well.
	CaseCommentType_CASE_COMMENT_TYPE_ACTION_DEDUPE CaseCommentType = 7
	// comment type to be used when system has decided to exclude the actor from certain auto action
	CaseCommentType_CASE_COMMENT_TYPE_EXCLUSION CaseCommentType = 8
	// comment type to be used when any communication is sent to user in context of a case.
	CaseCommentType_CASE_COMMENT_TYPE_COMMS CaseCommentType = 9
)

// Enum value maps for CaseCommentType.
var (
	CaseCommentType_name = map[int32]string{
		0: "CASE_COMMENT_TYPE_UNSPECIFIED",
		1: "CASE_COMMENT_TYPE_ALL",
		2: "CASE_COMMENT_TYPE_REMARKS",
		3: "CASE_COMMENT_TYPE_INVESTIGATION_NOTES",
		4: "CASE_COMMENT_TYPE_USER_INFO_QUESTIONS",
		5: "CASE_COMMENT_TYPE_USER_RESPONSE",
		6: "CASE_COMMENT_TYPE_SNOOZE",
		7: "CASE_COMMENT_TYPE_ACTION_DEDUPE",
		8: "CASE_COMMENT_TYPE_EXCLUSION",
		9: "CASE_COMMENT_TYPE_COMMS",
	}
	CaseCommentType_value = map[string]int32{
		"CASE_COMMENT_TYPE_UNSPECIFIED":         0,
		"CASE_COMMENT_TYPE_ALL":                 1,
		"CASE_COMMENT_TYPE_REMARKS":             2,
		"CASE_COMMENT_TYPE_INVESTIGATION_NOTES": 3,
		"CASE_COMMENT_TYPE_USER_INFO_QUESTIONS": 4,
		"CASE_COMMENT_TYPE_USER_RESPONSE":       5,
		"CASE_COMMENT_TYPE_SNOOZE":              6,
		"CASE_COMMENT_TYPE_ACTION_DEDUPE":       7,
		"CASE_COMMENT_TYPE_EXCLUSION":           8,
		"CASE_COMMENT_TYPE_COMMS":               9,
	}
)

func (x CaseCommentType) Enum() *CaseCommentType {
	p := new(CaseCommentType)
	*p = x
	return p
}

func (x CaseCommentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CaseCommentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_review_comment_proto_enumTypes[3].Descriptor()
}

func (CaseCommentType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_review_comment_proto_enumTypes[3]
}

func (x CaseCommentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CaseCommentType.Descriptor instead.
func (CaseCommentType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_comment_proto_rawDescGZIP(), []int{3}
}

type FacematchCommentType int32

const (
	FacematchCommentType_FACEMATCH_COMMENT_TYPE_UNSPECIFIED FacematchCommentType = 0
	FacematchCommentType_FACEMATCH_COMMENT_TYPE_REMARKS     FacematchCommentType = 1
)

// Enum value maps for FacematchCommentType.
var (
	FacematchCommentType_name = map[int32]string{
		0: "FACEMATCH_COMMENT_TYPE_UNSPECIFIED",
		1: "FACEMATCH_COMMENT_TYPE_REMARKS",
	}
	FacematchCommentType_value = map[string]int32{
		"FACEMATCH_COMMENT_TYPE_UNSPECIFIED": 0,
		"FACEMATCH_COMMENT_TYPE_REMARKS":     1,
	}
)

func (x FacematchCommentType) Enum() *FacematchCommentType {
	p := new(FacematchCommentType)
	*p = x
	return p
}

func (x FacematchCommentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FacematchCommentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_review_comment_proto_enumTypes[4].Descriptor()
}

func (FacematchCommentType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_review_comment_proto_enumTypes[4]
}

func (x FacematchCommentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FacematchCommentType.Descriptor instead.
func (FacematchCommentType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_comment_proto_rawDescGZIP(), []int{4}
}

// AfuCommentType defines comment type against afu entity
type AfuCommentType int32

const (
	AfuCommentType_AFU_COMMENT_TYPE_UNSPECIFIED AfuCommentType = 0
)

// Enum value maps for AfuCommentType.
var (
	AfuCommentType_name = map[int32]string{
		0: "AFU_COMMENT_TYPE_UNSPECIFIED",
	}
	AfuCommentType_value = map[string]int32{
		"AFU_COMMENT_TYPE_UNSPECIFIED": 0,
	}
)

func (x AfuCommentType) Enum() *AfuCommentType {
	p := new(AfuCommentType)
	*p = x
	return p
}

func (x AfuCommentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AfuCommentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_review_comment_proto_enumTypes[5].Descriptor()
}

func (AfuCommentType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_review_comment_proto_enumTypes[5]
}

func (x AfuCommentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AfuCommentType.Descriptor instead.
func (AfuCommentType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_comment_proto_rawDescGZIP(), []int{5}
}

// LEACommentType defines comment type against lea complaint entity
type LEAComplaintCommentType int32

const (
	LEAComplaintCommentType_LEA_COMPLAINT_COMMENT_TYPE_UNSPECIFIED LEAComplaintCommentType = 0
)

// Enum value maps for LEAComplaintCommentType.
var (
	LEAComplaintCommentType_name = map[int32]string{
		0: "LEA_COMPLAINT_COMMENT_TYPE_UNSPECIFIED",
	}
	LEAComplaintCommentType_value = map[string]int32{
		"LEA_COMPLAINT_COMMENT_TYPE_UNSPECIFIED": 0,
	}
)

func (x LEAComplaintCommentType) Enum() *LEAComplaintCommentType {
	p := new(LEAComplaintCommentType)
	*p = x
	return p
}

func (x LEAComplaintCommentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LEAComplaintCommentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_review_comment_proto_enumTypes[6].Descriptor()
}

func (LEAComplaintCommentType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_review_comment_proto_enumTypes[6]
}

func (x LEAComplaintCommentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LEAComplaintCommentType.Descriptor instead.
func (LEAComplaintCommentType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_comment_proto_rawDescGZIP(), []int{6}
}

// AlertCommentType defines comment type against alert entity
type AlertCommentType int32

const (
	AlertCommentType_ALERT_COMMENT_TYPE_UNSPECIFIED            AlertCommentType = 0
	AlertCommentType_ALERT_COMMENT_TYPE_ALERT_HANDLING_REMARKS AlertCommentType = 1
)

// Enum value maps for AlertCommentType.
var (
	AlertCommentType_name = map[int32]string{
		0: "ALERT_COMMENT_TYPE_UNSPECIFIED",
		1: "ALERT_COMMENT_TYPE_ALERT_HANDLING_REMARKS",
	}
	AlertCommentType_value = map[string]int32{
		"ALERT_COMMENT_TYPE_UNSPECIFIED":            0,
		"ALERT_COMMENT_TYPE_ALERT_HANDLING_REMARKS": 1,
	}
)

func (x AlertCommentType) Enum() *AlertCommentType {
	p := new(AlertCommentType)
	*p = x
	return p
}

func (x AlertCommentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AlertCommentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_review_comment_proto_enumTypes[7].Descriptor()
}

func (AlertCommentType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_review_comment_proto_enumTypes[7]
}

func (x AlertCommentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AlertCommentType.Descriptor instead.
func (AlertCommentType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_comment_proto_rawDescGZIP(), []int{7}
}

// comment is the unstructured response comes from the analyst against the entity
// this will be against the entity and comment type
type Comment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary key for the comment
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// against which entity the input should get created
	EntityType ReviewEntityType `protobuf:"varint,2,opt,name=entity_type,json=entityType,proto3,enum=risk.case_management.review.ReviewEntityType" json:"entity_type,omitempty"`
	// entity id against which the comment is created
	EntityId string `protobuf:"bytes,3,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	// comment type defined for the library, it can be field, all or some specific type like assignment tags etc.
	// This is tightly coupled with the entity type and need to add into the oneof with the addition of new entity type
	CommentType *CommentType `protobuf:"bytes,4,opt,name=comment_type,json=commentType,proto3" json:"comment_type,omitempty"`
	// comment input by analyst
	Comment string `protobuf:"bytes,7,opt,name=comment,proto3" json:"comment,omitempty"`
	// comment added by for audit
	AddedByEmail string `protobuf:"bytes,8,opt,name=added_by_email,json=addedByEmail,proto3" json:"added_by_email,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// deleted at
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// case id against which the comment has been added
	CaseId string `protobuf:"bytes,12,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
}

func (x *Comment) Reset() {
	*x = Comment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_comment_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Comment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Comment) ProtoMessage() {}

func (x *Comment) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_comment_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Comment.ProtoReflect.Descriptor instead.
func (*Comment) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_comment_proto_rawDescGZIP(), []int{0}
}

func (x *Comment) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Comment) GetEntityType() ReviewEntityType {
	if x != nil {
		return x.EntityType
	}
	return ReviewEntityType_REVIEW_ENTITY_TYPE_UNSPECIFIED
}

func (x *Comment) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

func (x *Comment) GetCommentType() *CommentType {
	if x != nil {
		return x.CommentType
	}
	return nil
}

func (x *Comment) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *Comment) GetAddedByEmail() string {
	if x != nil {
		return x.AddedByEmail
	}
	return ""
}

func (x *Comment) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Comment) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Comment) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *Comment) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

// comment query is the abstraction over the possible queries we would want to fetch the comments from system
type CommentQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EntityType  ReviewEntityType `protobuf:"varint,1,opt,name=entity_type,json=entityType,proto3,enum=risk.case_management.review.ReviewEntityType" json:"entity_type,omitempty"`
	EntityId    string           `protobuf:"bytes,2,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	CommentType *CommentType     `protobuf:"bytes,3,opt,name=comment_type,json=commentType,proto3" json:"comment_type,omitempty"`
}

func (x *CommentQuery) Reset() {
	*x = CommentQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_comment_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommentQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommentQuery) ProtoMessage() {}

func (x *CommentQuery) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_comment_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommentQuery.ProtoReflect.Descriptor instead.
func (*CommentQuery) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_comment_proto_rawDescGZIP(), []int{1}
}

func (x *CommentQuery) GetEntityType() ReviewEntityType {
	if x != nil {
		return x.EntityType
	}
	return ReviewEntityType_REVIEW_ENTITY_TYPE_UNSPECIFIED
}

func (x *CommentQuery) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

func (x *CommentQuery) GetCommentType() *CommentType {
	if x != nil {
		return x.CommentType
	}
	return nil
}

// All non-empty filters will be applied with AND condition.
// At least one filter should be present.
type CommentFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// CAUTION: only a single query is supported.
	Queries []*CommentQuery `protobuf:"bytes,1,rep,name=queries,proto3" json:"queries,omitempty"`
	// It Can be used to fetch all comments against review entities linked to the actor and comment added in context to a case.
	// e.g., liveness comments on actor's liveness video, case comments, actor's afu attempt comments or
	// transaction comments.
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *CommentFilters) Reset() {
	*x = CommentFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_comment_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommentFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommentFilters) ProtoMessage() {}

func (x *CommentFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_comment_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommentFilters.ProtoReflect.Descriptor instead.
func (*CommentFilters) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_comment_proto_rawDescGZIP(), []int{2}
}

func (x *CommentFilters) GetQueries() []*CommentQuery {
	if x != nil {
		return x.Queries
	}
	return nil
}

func (x *CommentFilters) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

// possible comment type for the comments
type CommentType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to CommentType:
	//
	//	*CommentType_UserCommentType
	//	*CommentType_TxnCommentType
	//	*CommentType_LivenessCommentType
	//	*CommentType_CaseCommentType
	//	*CommentType_FacematchCommentType
	//	*CommentType_AfuCommentType
	//	*CommentType_LeaComplaintCommentType
	CommentType isCommentType_CommentType `protobuf_oneof:"comment_type"`
}

func (x *CommentType) Reset() {
	*x = CommentType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_comment_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommentType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommentType) ProtoMessage() {}

func (x *CommentType) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_comment_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommentType.ProtoReflect.Descriptor instead.
func (*CommentType) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_comment_proto_rawDescGZIP(), []int{3}
}

func (m *CommentType) GetCommentType() isCommentType_CommentType {
	if m != nil {
		return m.CommentType
	}
	return nil
}

func (x *CommentType) GetUserCommentType() UserCommentType {
	if x, ok := x.GetCommentType().(*CommentType_UserCommentType); ok {
		return x.UserCommentType
	}
	return UserCommentType_USER_COMMENT_TYPE_UNSPECIFIED
}

func (x *CommentType) GetTxnCommentType() TxnCommentType {
	if x, ok := x.GetCommentType().(*CommentType_TxnCommentType); ok {
		return x.TxnCommentType
	}
	return TxnCommentType_TXN_COMMENT_TYPE_UNSPECIFIED
}

func (x *CommentType) GetLivenessCommentType() LivenessCommentType {
	if x, ok := x.GetCommentType().(*CommentType_LivenessCommentType); ok {
		return x.LivenessCommentType
	}
	return LivenessCommentType_LIVENESS_COMMENT_TYPE_UNSPECIFIED
}

func (x *CommentType) GetCaseCommentType() CaseCommentType {
	if x, ok := x.GetCommentType().(*CommentType_CaseCommentType); ok {
		return x.CaseCommentType
	}
	return CaseCommentType_CASE_COMMENT_TYPE_UNSPECIFIED
}

func (x *CommentType) GetFacematchCommentType() FacematchCommentType {
	if x, ok := x.GetCommentType().(*CommentType_FacematchCommentType); ok {
		return x.FacematchCommentType
	}
	return FacematchCommentType_FACEMATCH_COMMENT_TYPE_UNSPECIFIED
}

func (x *CommentType) GetAfuCommentType() AfuCommentType {
	if x, ok := x.GetCommentType().(*CommentType_AfuCommentType); ok {
		return x.AfuCommentType
	}
	return AfuCommentType_AFU_COMMENT_TYPE_UNSPECIFIED
}

func (x *CommentType) GetLeaComplaintCommentType() LEAComplaintCommentType {
	if x, ok := x.GetCommentType().(*CommentType_LeaComplaintCommentType); ok {
		return x.LeaComplaintCommentType
	}
	return LEAComplaintCommentType_LEA_COMPLAINT_COMMENT_TYPE_UNSPECIFIED
}

type isCommentType_CommentType interface {
	isCommentType_CommentType()
}

type CommentType_UserCommentType struct {
	UserCommentType UserCommentType `protobuf:"varint,3,opt,name=user_comment_type,json=userCommentType,proto3,enum=risk.case_management.review.UserCommentType,oneof"`
}

type CommentType_TxnCommentType struct {
	TxnCommentType TxnCommentType `protobuf:"varint,4,opt,name=txn_comment_type,json=txnCommentType,proto3,enum=risk.case_management.review.TxnCommentType,oneof"`
}

type CommentType_LivenessCommentType struct {
	LivenessCommentType LivenessCommentType `protobuf:"varint,5,opt,name=liveness_comment_type,json=livenessCommentType,proto3,enum=risk.case_management.review.LivenessCommentType,oneof"`
}

type CommentType_CaseCommentType struct {
	CaseCommentType CaseCommentType `protobuf:"varint,6,opt,name=case_comment_type,json=caseCommentType,proto3,enum=risk.case_management.review.CaseCommentType,oneof"`
}

type CommentType_FacematchCommentType struct {
	FacematchCommentType FacematchCommentType `protobuf:"varint,7,opt,name=facematch_comment_type,json=facematchCommentType,proto3,enum=risk.case_management.review.FacematchCommentType,oneof"`
}

type CommentType_AfuCommentType struct {
	AfuCommentType AfuCommentType `protobuf:"varint,8,opt,name=afu_comment_type,json=afuCommentType,proto3,enum=risk.case_management.review.AfuCommentType,oneof"`
}

type CommentType_LeaComplaintCommentType struct {
	LeaComplaintCommentType LEAComplaintCommentType `protobuf:"varint,9,opt,name=lea_complaint_comment_type,json=leaComplaintCommentType,proto3,enum=risk.case_management.review.LEAComplaintCommentType,oneof"`
}

func (*CommentType_UserCommentType) isCommentType_CommentType() {}

func (*CommentType_TxnCommentType) isCommentType_CommentType() {}

func (*CommentType_LivenessCommentType) isCommentType_CommentType() {}

func (*CommentType_CaseCommentType) isCommentType_CommentType() {}

func (*CommentType_FacematchCommentType) isCommentType_CommentType() {}

func (*CommentType_AfuCommentType) isCommentType_CommentType() {}

func (*CommentType_LeaComplaintCommentType) isCommentType_CommentType() {}

var File_api_risk_case_management_review_comment_proto protoreflect.FileDescriptor

var file_api_risk_case_management_review_comment_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1b, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x1a, 0x2b, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x82, 0x04, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x58, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12,
	0x4b, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x07,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x04, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x2d, 0x0a, 0x0e, 0x61, 0x64, 0x64, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x0c, 0x61, 0x64, 0x64, 0x65, 0x64, 0x42, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x22, 0xdb, 0x01, 0x0a, 0x0c, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x58, 0x0a, 0x0b, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0c, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x70, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x43, 0x0a, 0x07, 0x71, 0x75, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x07, 0x71, 0x75, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xcf, 0x05, 0x0a, 0x0b, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5a, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x48, 0x00, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x57, 0x0a, 0x10, 0x74, 0x78, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x54, 0x78, 0x6e,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x74,
	0x78, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x66, 0x0a,
	0x15, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00,
	0x52, 0x13, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5a, 0x0a, 0x11, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43,
	0x61, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00,
	0x52, 0x0f, 0x63, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x69, 0x0a, 0x16, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x31, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e,
	0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x14, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x57, 0x0a, 0x10,
	0x61, 0x66, 0x75, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x66, 0x75, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x61, 0x66, 0x75, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x73, 0x0a, 0x1a, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x48,
	0x00, 0x52, 0x17, 0x6c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2a, 0x4f, 0x0a, 0x0f, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a,
	0x1d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x19, 0x0a, 0x15, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x01, 0x2a, 0x4c, 0x0a, 0x0e, 0x54,
	0x78, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a,
	0x1c, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x18, 0x0a, 0x14, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x01, 0x2a, 0x5b, 0x0a, 0x13, 0x4c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x25, 0x0a, 0x21, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x4f, 0x4d,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x49, 0x56, 0x45, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x01, 0x2a, 0xee, 0x02, 0x0a, 0x0f, 0x43, 0x61, 0x73, 0x65, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41,
	0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a,
	0x15, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x01, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x1d, 0x0a, 0x19,
	0x43, 0x41, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x52, 0x45, 0x4d, 0x41, 0x52, 0x4b, 0x53, 0x10, 0x02, 0x12, 0x29, 0x0a, 0x25, 0x43,
	0x41, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x49, 0x47, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e,
	0x4f, 0x54, 0x45, 0x53, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x43,
	0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10,
	0x04, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x53, 0x50,
	0x4f, 0x4e, 0x53, 0x45, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x43,
	0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x4e, 0x4f, 0x4f,
	0x5a, 0x45, 0x10, 0x06, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4d,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x10, 0x07, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41, 0x53,
	0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45,
	0x58, 0x43, 0x4c, 0x55, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x08, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x41,
	0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x10, 0x09, 0x2a, 0x62, 0x0a, 0x14, 0x46, 0x61, 0x63, 0x65, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x26, 0x0a, 0x22, 0x46, 0x41, 0x43, 0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x43, 0x4f, 0x4d,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x46, 0x41, 0x43, 0x45, 0x4d,
	0x41, 0x54, 0x43, 0x48, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x52, 0x45, 0x4d, 0x41, 0x52, 0x4b, 0x53, 0x10, 0x01, 0x2a, 0x32, 0x0a, 0x0e, 0x41,
	0x66, 0x75, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a,
	0x1c, 0x41, 0x46, 0x55, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x2a,
	0x45, 0x0a, 0x17, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x45,
	0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x2a, 0x65, 0x0a, 0x10, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x4c,
	0x45, 0x52, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2d,
	0x0a, 0x29, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c,
	0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x4d, 0x41, 0x52, 0x4b, 0x53, 0x10, 0x01, 0x42, 0x70, 0x0a,
	0x36, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_review_comment_proto_rawDescOnce sync.Once
	file_api_risk_case_management_review_comment_proto_rawDescData = file_api_risk_case_management_review_comment_proto_rawDesc
)

func file_api_risk_case_management_review_comment_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_review_comment_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_review_comment_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_review_comment_proto_rawDescData)
	})
	return file_api_risk_case_management_review_comment_proto_rawDescData
}

var file_api_risk_case_management_review_comment_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_api_risk_case_management_review_comment_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_risk_case_management_review_comment_proto_goTypes = []interface{}{
	(UserCommentType)(0),          // 0: risk.case_management.review.UserCommentType
	(TxnCommentType)(0),           // 1: risk.case_management.review.TxnCommentType
	(LivenessCommentType)(0),      // 2: risk.case_management.review.LivenessCommentType
	(CaseCommentType)(0),          // 3: risk.case_management.review.CaseCommentType
	(FacematchCommentType)(0),     // 4: risk.case_management.review.FacematchCommentType
	(AfuCommentType)(0),           // 5: risk.case_management.review.AfuCommentType
	(LEAComplaintCommentType)(0),  // 6: risk.case_management.review.LEAComplaintCommentType
	(AlertCommentType)(0),         // 7: risk.case_management.review.AlertCommentType
	(*Comment)(nil),               // 8: risk.case_management.review.Comment
	(*CommentQuery)(nil),          // 9: risk.case_management.review.CommentQuery
	(*CommentFilters)(nil),        // 10: risk.case_management.review.CommentFilters
	(*CommentType)(nil),           // 11: risk.case_management.review.CommentType
	(ReviewEntityType)(0),         // 12: risk.case_management.review.ReviewEntityType
	(*timestamppb.Timestamp)(nil), // 13: google.protobuf.Timestamp
}
var file_api_risk_case_management_review_comment_proto_depIdxs = []int32{
	12, // 0: risk.case_management.review.Comment.entity_type:type_name -> risk.case_management.review.ReviewEntityType
	11, // 1: risk.case_management.review.Comment.comment_type:type_name -> risk.case_management.review.CommentType
	13, // 2: risk.case_management.review.Comment.created_at:type_name -> google.protobuf.Timestamp
	13, // 3: risk.case_management.review.Comment.updated_at:type_name -> google.protobuf.Timestamp
	13, // 4: risk.case_management.review.Comment.deleted_at:type_name -> google.protobuf.Timestamp
	12, // 5: risk.case_management.review.CommentQuery.entity_type:type_name -> risk.case_management.review.ReviewEntityType
	11, // 6: risk.case_management.review.CommentQuery.comment_type:type_name -> risk.case_management.review.CommentType
	9,  // 7: risk.case_management.review.CommentFilters.queries:type_name -> risk.case_management.review.CommentQuery
	0,  // 8: risk.case_management.review.CommentType.user_comment_type:type_name -> risk.case_management.review.UserCommentType
	1,  // 9: risk.case_management.review.CommentType.txn_comment_type:type_name -> risk.case_management.review.TxnCommentType
	2,  // 10: risk.case_management.review.CommentType.liveness_comment_type:type_name -> risk.case_management.review.LivenessCommentType
	3,  // 11: risk.case_management.review.CommentType.case_comment_type:type_name -> risk.case_management.review.CaseCommentType
	4,  // 12: risk.case_management.review.CommentType.facematch_comment_type:type_name -> risk.case_management.review.FacematchCommentType
	5,  // 13: risk.case_management.review.CommentType.afu_comment_type:type_name -> risk.case_management.review.AfuCommentType
	6,  // 14: risk.case_management.review.CommentType.lea_complaint_comment_type:type_name -> risk.case_management.review.LEAComplaintCommentType
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_review_comment_proto_init() }
func file_api_risk_case_management_review_comment_proto_init() {
	if File_api_risk_case_management_review_comment_proto != nil {
		return
	}
	file_api_risk_case_management_review_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_case_management_review_comment_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Comment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_comment_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommentQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_comment_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommentFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_comment_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommentType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_case_management_review_comment_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*CommentType_UserCommentType)(nil),
		(*CommentType_TxnCommentType)(nil),
		(*CommentType_LivenessCommentType)(nil),
		(*CommentType_CaseCommentType)(nil),
		(*CommentType_FacematchCommentType)(nil),
		(*CommentType_AfuCommentType)(nil),
		(*CommentType_LeaComplaintCommentType)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_review_comment_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_case_management_review_comment_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_review_comment_proto_depIdxs,
		EnumInfos:         file_api_risk_case_management_review_comment_proto_enumTypes,
		MessageInfos:      file_api_risk_case_management_review_comment_proto_msgTypes,
	}.Build()
	File_api_risk_case_management_review_comment_proto = out.File
	file_api_risk_case_management_review_comment_proto_rawDesc = nil
	file_api_risk_case_management_review_comment_proto_goTypes = nil
	file_api_risk_case_management_review_comment_proto_depIdxs = nil
}
