package review

import "errors"

var CouldNotConvertAnnotationType = errors.New("could not convert to the annotation type")

func (a *AnnotationType) ToString(entityType ReviewEntityType) (string, error) {
	switch entityType {
	case ReviewEntityType_REVIEW_ENTITY_TYPE_USER:
		if a.GetUserAnnotationType() != UserAnnotationType_USER_ANNOTATION_TYPE_UNSPECIFIED {
			return a.GetUserAnnotationType().String(), nil
		}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_TRANSACTION:
		if a.GetTxnAnnotationType() != TxnAnnotationType_TXN_ANNOTATION_TYPE_UNSPECIFIED {
			return a.GetTxnAnnotationType().String(), nil
		}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_LIVENESS:
		if a.GetLivenessAnnotationType() != LivenessAnnotationType_LIVENESS_ANNOTATION_TYPE_UNSPECIFIED {
			return a.GetLivenessAnnotationType().String(), nil
		}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_CASE:
		if a.GetCaseAnnotationType() != CaseAnnotationType_CASE_ANNOTATION_TYPE_UNSPECIFIED {
			return a.GetCaseAnnotationType().String(), nil
		}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_RULE:
		if a.GetRuleAnnotationType() != RuleAnnotationType_RULE_ANNOTATION_TYPE_UNSPECIFIED {
			return a.GetRuleAnnotationType().String(), nil
		}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_FACEMATCH:
		if a.GetFaceMatchAnnotationType() != FaceMatchAnnotationType_FACE_MATCH_ANNOTATION_TYPE_UNSPECIFIED {
			return a.GetFaceMatchAnnotationType().String(), nil
		}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_AFU:
		if a.GetAfuAnnotationType() != AfuAnnotationType_AFU_ANNOTATION_TYPE_UNSPECIFIED {
			return a.GetAfuAnnotationType().String(), nil
		}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_LEA_COMPLAINT:
		if a.GetLeaComplaintAnnotationType() != LEAComplaintAnnotationType_LEA_COMPLAINT_ANNOTATION_TYPE_UNSPECIFIED {
			return a.GetLeaComplaintAnnotationType().String(), nil
		}
	}
	return "", nil
}
