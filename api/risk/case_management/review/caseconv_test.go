package review

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"reflect"
	"testing"

	"google.golang.org/protobuf/runtime/protoimpl"

	vgCrmPb "github.com/epifi/gamma/api/vendorgateway/crm"
	vgRiskPb "github.com/epifi/gamma/api/vendorgateway/crm/risk"
)

func TestSortableCaseFilters_ToCRMFilters(t *testing.T) {
	type fields struct {
		state         protoimpl.MessageState
		sizeCache     protoimpl.SizeCache
		unknownFields protoimpl.UnknownFields
		ActorId       string
	}
	type args struct {
		crmUserId string
		sortBy    *CaseSortBy
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *vgCrmPb.GetSortedTicketsRequest_RiskFilters
		wantErr bool
	}{
		{
			name:    "empty filter option",
			wantErr: false,
			want: &vgCrmPb.GetSortedTicketsRequest_RiskFilters{RiskFilters: &vgRiskPb.SortableRiskFilters{
				Order:             commontypes.SortOrder_SORT_ORDER_DESCENDING,
				SortByTicketField: vgRiskPb.RiskTicketFieldMask_RISK_TICKET_FIELD_MASK_CREATED_AT,
			}},
		},
		{
			name:    "empty crm user id for actor id filter",
			wantErr: true,
			fields:  fields{ActorId: "actorId"},
			want:    nil,
		},
		{
			name:    "actor id filter",
			wantErr: false,
			fields:  fields{ActorId: "actorId"},
			args:    args{crmUserId: "crmUserId"},
			want: &vgCrmPb.GetSortedTicketsRequest_RiskFilters{RiskFilters: &vgRiskPb.SortableRiskFilters{
				FilterOptions:        []vgRiskPb.FilterOption{vgRiskPb.FilterOption_FILTER_OPTION_UNIQUE_EXTERNAL_ID},
				UniqueExternalUserId: "crmUserId",
				Order:                commontypes.SortOrder_SORT_ORDER_DESCENDING,
				SortByTicketField:    vgRiskPb.RiskTicketFieldMask_RISK_TICKET_FIELD_MASK_CREATED_AT,
			},
			},
		},
		{
			name:    "Sort by created at",
			wantErr: false,
			fields:  fields{ActorId: "actorId"},
			args: args{crmUserId: "crmUserId", sortBy: &CaseSortBy{
				FieldMask: CaseFieldMask_CASE_FIELD_MASK_CREATED_AT,
				SortOrder: commontypes.SortOrder_SORT_ORDER_DESCENDING,
			}},
			want: &vgCrmPb.GetSortedTicketsRequest_RiskFilters{RiskFilters: &vgRiskPb.SortableRiskFilters{
				FilterOptions: []vgRiskPb.FilterOption{
					vgRiskPb.FilterOption_FILTER_OPTION_UNIQUE_EXTERNAL_ID,
				},
				UniqueExternalUserId: "crmUserId",
				SortByTicketField:    vgRiskPb.RiskTicketFieldMask_RISK_TICKET_FIELD_MASK_CREATED_AT,
				Order:                commontypes.SortOrder_SORT_ORDER_DESCENDING,
			},
			},
		},
		{
			name:    "Sort by updated at",
			wantErr: false,
			fields:  fields{ActorId: "actorId"},
			args: args{crmUserId: "crmUserId", sortBy: &CaseSortBy{
				FieldMask: CaseFieldMask_CASE_FIELD_MASK_UPDATED_AT,
				SortOrder: commontypes.SortOrder_SORT_ORDER_DESCENDING,
			}},
			want: &vgCrmPb.GetSortedTicketsRequest_RiskFilters{RiskFilters: &vgRiskPb.SortableRiskFilters{
				FilterOptions: []vgRiskPb.FilterOption{
					vgRiskPb.FilterOption_FILTER_OPTION_UNIQUE_EXTERNAL_ID,
				},
				UniqueExternalUserId: "crmUserId",
				SortByTicketField:    vgRiskPb.RiskTicketFieldMask_RISK_TICKET_FIELD_MASK_UPDATED_AT,
				Order:                commontypes.SortOrder_SORT_ORDER_DESCENDING,
			},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &SortableCaseFilters{
				ActorId: tt.fields.ActorId,
			}
			got, err := c.ToCRMFilters(tt.args.crmUserId, tt.args.sortBy)
			if (err != nil) != tt.wantErr {
				t.Errorf("ToCRMFilters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ToCRMFilters() got = %v, want %v", got, tt.want)
			}
		})
	}
}
