// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/risk/case_management/review/enums.pb.go

package review

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the ActionStatus in string format in DB
func (p ActionStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ActionStatus while reading from DB
func (p *ActionStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ActionStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected ActionStatus value: %s", val)
	}
	*p = ActionStatus(valInt)
	return nil
}

// Marshaler interface implementation for ActionStatus
func (x ActionStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ActionStatus
func (x *ActionStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ActionStatus(ActionStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the ActionProcessingType in string format in DB
func (p ActionProcessingType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ActionProcessingType while reading from DB
func (p *ActionProcessingType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ActionProcessingType_value[val]
	if !ok {
		return fmt.Errorf("unexpected ActionProcessingType value: %s", val)
	}
	*p = ActionProcessingType(valInt)
	return nil
}

// Marshaler interface implementation for ActionProcessingType
func (x ActionProcessingType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ActionProcessingType
func (x *ActionProcessingType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ActionProcessingType(ActionProcessingType_value[val])
	return nil
}

// Valuer interface implementation for storing the UIElementType in string format in DB
func (p UIElementType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing UIElementType while reading from DB
func (p *UIElementType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := UIElementType_value[val]
	if !ok {
		return fmt.Errorf("unexpected UIElementType value: %s", val)
	}
	*p = UIElementType(valInt)
	return nil
}

// Marshaler interface implementation for UIElementType
func (x UIElementType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for UIElementType
func (x *UIElementType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = UIElementType(UIElementType_value[val])
	return nil
}
