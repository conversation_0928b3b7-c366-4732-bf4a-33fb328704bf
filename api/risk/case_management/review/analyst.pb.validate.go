// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/case_management/review/analyst.proto

package review

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Analyst with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Analyst) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Analyst with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AnalystMultiError, or nil if none found.
func (m *Analyst) ValidateAll() error {
	return m.validate(true)
}

func (m *Analyst) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for EmailId

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalystValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalystValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalystValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalystValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalystValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalystValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalystValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalystValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalystValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalystValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalystValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalystValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastActiveAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalystValidationError{
					field:  "LastActiveAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalystValidationError{
					field:  "LastActiveAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastActiveAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalystValidationError{
				field:  "LastActiveAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AnalystMultiError(errors)
	}

	return nil
}

// AnalystMultiError is an error wrapping multiple validation errors returned
// by Analyst.ValidateAll() if the designated constraints aren't met.
type AnalystMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnalystMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnalystMultiError) AllErrors() []error { return m }

// AnalystValidationError is the validation error returned by Analyst.Validate
// if the designated constraints aren't met.
type AnalystValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnalystValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnalystValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnalystValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnalystValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnalystValidationError) ErrorName() string { return "AnalystValidationError" }

// Error satisfies the builtin error interface
func (e AnalystValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnalyst.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnalystValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnalystValidationError{}

// Validate checks the field values on AnalystIdentifier with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AnalystIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnalystIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AnalystIdentifierMultiError, or nil if none found.
func (m *AnalystIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *AnalystIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *AnalystIdentifier_AnalystEmail:
		if v == nil {
			err := AnalystIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for AnalystEmail
	case *AnalystIdentifier_Id:
		if v == nil {
			err := AnalystIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Id
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AnalystIdentifierMultiError(errors)
	}

	return nil
}

// AnalystIdentifierMultiError is an error wrapping multiple validation errors
// returned by AnalystIdentifier.ValidateAll() if the designated constraints
// aren't met.
type AnalystIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnalystIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnalystIdentifierMultiError) AllErrors() []error { return m }

// AnalystIdentifierValidationError is the validation error returned by
// AnalystIdentifier.Validate if the designated constraints aren't met.
type AnalystIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnalystIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnalystIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnalystIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnalystIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnalystIdentifierValidationError) ErrorName() string {
	return "AnalystIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e AnalystIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnalystIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnalystIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnalystIdentifierValidationError{}
