// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/review/action.proto

package review

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	form "github.com/epifi/gamma/api/risk/case_management/form"
	enums "github.com/epifi/gamma/api/risk/enums"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// When a case is registered, analyst is expected to take actions on the top of a risk case
// Actions can vary from adding annotation, adding user attributes to a negative database,
// applying verdict on a case which may result in state transition for a case.
// Action represents all such actions along with details for a case and is intended to
// act as an audit trail for a case as well.
type Action struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary row id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// case for which the action was taken
	CaseId string `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	// type of review for which action was taken
	// storing this explicitly since review type can change during the lifecycle of a case
	ReviewType ReviewType `protobuf:"varint,3,opt,name=review_type,json=reviewType,proto3,enum=risk.case_management.review.ReviewType" json:"review_type,omitempty"`
	// Different actions can be performed against a case like pass_account, freeze_account, request_more_info etc
	// This field represents the type of action that was taken on the case
	ActionType ActionType `protobuf:"varint,4,opt,name=action_type,json=actionType,proto3,enum=risk.case_management.review.ActionType" json:"action_type,omitempty"`
	// For each action type we will have some action specific parameters
	// Ex: For action_type FreezeAccount, we can have different freeze levels like credit_freeze, debit_freeze or total_freeze etc.
	// This action specific info will be captured as part of parameters
	Parameters *ActionParameters `protobuf:"bytes,5,opt,name=parameters,proto3" json:"parameters,omitempty"`
	// This is to identify from where the action was taken,
	// it can be an analyst taking action from sherlock via review flow or bulk action by ops admin or automated system action via backend etc.
	Source ActionSource `protobuf:"varint,6,opt,name=source,proto3,enum=risk.case_management.review.ActionSource" json:"source,omitempty"`
	// email of analyst taking the action
	// this will not be present for auto actions taken by system like auto-freeze based on alert score threshold
	AnalystEmail string `protobuf:"bytes,7,opt,name=analyst_email,json=analystEmail,proto3" json:"analyst_email,omitempty"`
	// Time at which action was initiated from the source system
	// Capturing initiated at field explicitly since we will be processing these actions async in a workflow and there can be delays
	// so the actual initiated_at and created_at(auto populated column in db) can be different
	InitiatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=initiated_at,json=initiatedAt,proto3" json:"initiated_at,omitempty"`
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt   *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Actor associated with case for which action is taken.
	ActorId string `protobuf:"bytes,11,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Current status of the action. Status update is the last step in action processing.
	Status ActionStatus `protobuf:"varint,12,opt,name=status,proto3,enum=risk.case_management.review.ActionStatus" json:"status,omitempty"`
	// ActionProcessingType indicates how an action on a case was processed.
	ProcessingType ActionProcessingType `protobuf:"varint,13,opt,name=processing_type,json=processingType,proto3,enum=risk.case_management.review.ActionProcessingType" json:"processing_type,omitempty"`
}

func (x *Action) Reset() {
	*x = Action{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_action_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Action) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Action) ProtoMessage() {}

func (x *Action) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_action_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Action.ProtoReflect.Descriptor instead.
func (*Action) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_action_proto_rawDescGZIP(), []int{0}
}

func (x *Action) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Action) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *Action) GetReviewType() ReviewType {
	if x != nil {
		return x.ReviewType
	}
	return ReviewType_REVIEW_TYPE_UNSPECIFIED
}

func (x *Action) GetActionType() ActionType {
	if x != nil {
		return x.ActionType
	}
	return ActionType_ACTION_TYPE_UNSPECIFIED
}

func (x *Action) GetParameters() *ActionParameters {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *Action) GetSource() ActionSource {
	if x != nil {
		return x.Source
	}
	return ActionSource_ACTION_SOURCE_UNSPECIFIED
}

func (x *Action) GetAnalystEmail() string {
	if x != nil {
		return x.AnalystEmail
	}
	return ""
}

func (x *Action) GetInitiatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.InitiatedAt
	}
	return nil
}

func (x *Action) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Action) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Action) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *Action) GetStatus() ActionStatus {
	if x != nil {
		return x.Status
	}
	return ActionStatus_ACTION_STATUS_UNSPECIFIED
}

func (x *Action) GetProcessingType() ActionProcessingType {
	if x != nil {
		return x.ProcessingType
	}
	return ActionProcessingType_ACTION_PROCESSING_TYPE_UNSPECIFIED
}

type ActionParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Parameter:
	//	*ActionParameters_AccountFreezeParameters
	//	*ActionParameters_SnoozeParameters
	//	*ActionParameters_RequestUserInfoParameters
	//	*ActionParameters_RejectEscalationParameters
	//	*ActionParameters_AccountLienParameters
	Parameter isActionParameters_Parameter `protobuf_oneof:"parameter"`
}

func (x *ActionParameters) Reset() {
	*x = ActionParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_action_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActionParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionParameters) ProtoMessage() {}

func (x *ActionParameters) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_action_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionParameters.ProtoReflect.Descriptor instead.
func (*ActionParameters) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_action_proto_rawDescGZIP(), []int{1}
}

func (m *ActionParameters) GetParameter() isActionParameters_Parameter {
	if m != nil {
		return m.Parameter
	}
	return nil
}

func (x *ActionParameters) GetAccountFreezeParameters() *AccountFreezeParameters {
	if x, ok := x.GetParameter().(*ActionParameters_AccountFreezeParameters); ok {
		return x.AccountFreezeParameters
	}
	return nil
}

func (x *ActionParameters) GetSnoozeParameters() *SnoozeParameters {
	if x, ok := x.GetParameter().(*ActionParameters_SnoozeParameters); ok {
		return x.SnoozeParameters
	}
	return nil
}

func (x *ActionParameters) GetRequestUserInfoParameters() *RequestUserInfoParameters {
	if x, ok := x.GetParameter().(*ActionParameters_RequestUserInfoParameters); ok {
		return x.RequestUserInfoParameters
	}
	return nil
}

func (x *ActionParameters) GetRejectEscalationParameters() *RejectEscalationParameters {
	if x, ok := x.GetParameter().(*ActionParameters_RejectEscalationParameters); ok {
		return x.RejectEscalationParameters
	}
	return nil
}

func (x *ActionParameters) GetAccountLienParameters() *AccountLienParameters {
	if x, ok := x.GetParameter().(*ActionParameters_AccountLienParameters); ok {
		return x.AccountLienParameters
	}
	return nil
}

type isActionParameters_Parameter interface {
	isActionParameters_Parameter()
}

type ActionParameters_AccountFreezeParameters struct {
	// parameters for ACTION_TYPE_FREEZE_ACCOUNT
	AccountFreezeParameters *AccountFreezeParameters `protobuf:"bytes,1,opt,name=account_freeze_parameters,json=accountFreezeParameters,proto3,oneof"`
}

type ActionParameters_SnoozeParameters struct {
	// parameters for ACTION_TYPE_SNOOZE
	SnoozeParameters *SnoozeParameters `protobuf:"bytes,2,opt,name=snooze_parameters,json=snoozeParameters,proto3,oneof"`
}

type ActionParameters_RequestUserInfoParameters struct {
	// parameters for ACTION_TYPE_REQUEST_USER_INFO
	RequestUserInfoParameters *RequestUserInfoParameters `protobuf:"bytes,3,opt,name=request_user_info_parameters,json=requestUserInfoParameters,proto3,oneof"`
}

type ActionParameters_RejectEscalationParameters struct {
	// parameters for ACTION_TYPE_REJECT_ESCALATION
	RejectEscalationParameters *RejectEscalationParameters `protobuf:"bytes,4,opt,name=reject_escalation_parameters,json=rejectEscalationParameters,proto3,oneof"`
}

type ActionParameters_AccountLienParameters struct {
	// parameters for ACTION_TYPE_ACCOUNT_LIEN
	AccountLienParameters *AccountLienParameters `protobuf:"bytes,5,opt,name=account_lien_parameters,json=accountLienParameters,proto3,oneof"`
}

func (*ActionParameters_AccountFreezeParameters) isActionParameters_Parameter() {}

func (*ActionParameters_SnoozeParameters) isActionParameters_Parameter() {}

func (*ActionParameters_RequestUserInfoParameters) isActionParameters_Parameter() {}

func (*ActionParameters_RejectEscalationParameters) isActionParameters_Parameter() {}

func (*ActionParameters_AccountLienParameters) isActionParameters_Parameter() {}

type AccountFreezeParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// level of freeze(credit, debit or total etc) we need to do for user account
	FreezeLevel FreezeLevel `protobuf:"varint,1,opt,name=freeze_level,json=freezeLevel,proto3,enum=risk.case_management.review.FreezeLevel" json:"freeze_level,omitempty"`
	// reason to change account state
	RequestReason *RequestReason `protobuf:"bytes,2,opt,name=request_reason,json=requestReason,proto3" json:"request_reason,omitempty"`
	// comms template to be triggered
	CommsTemplate []enums.CommsTemplate `protobuf:"varint,3,rep,packed,name=comms_template,json=commsTemplate,proto3,enum=enums.CommsTemplate" json:"comms_template,omitempty"`
}

func (x *AccountFreezeParameters) Reset() {
	*x = AccountFreezeParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_action_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountFreezeParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountFreezeParameters) ProtoMessage() {}

func (x *AccountFreezeParameters) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_action_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountFreezeParameters.ProtoReflect.Descriptor instead.
func (*AccountFreezeParameters) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_action_proto_rawDescGZIP(), []int{2}
}

func (x *AccountFreezeParameters) GetFreezeLevel() FreezeLevel {
	if x != nil {
		return x.FreezeLevel
	}
	return FreezeLevel_FREEZE_LEVEL_UNSPECIFIED
}

func (x *AccountFreezeParameters) GetRequestReason() *RequestReason {
	if x != nil {
		return x.RequestReason
	}
	return nil
}

func (x *AccountFreezeParameters) GetCommsTemplate() []enums.CommsTemplate {
	if x != nil {
		return x.CommsTemplate
	}
	return nil
}

type AccountLienParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reason to change account state
	RequestReason *RequestReason `protobuf:"bytes,1,opt,name=request_reason,json=requestReason,proto3" json:"request_reason,omitempty"`
	// the amount to be marked as lien
	LienAmount *money.Money `protobuf:"bytes,2,opt,name=lien_amount,json=lienAmount,proto3" json:"lien_amount,omitempty"`
	// time duration in hours for which lien is to be applied
	LienDurationInHours int32 `protobuf:"varint,3,opt,name=lien_duration_in_hours,json=lienDurationInHours,proto3" json:"lien_duration_in_hours,omitempty"`
	// comms template to be triggered
	CommsTemplate []enums.CommsTemplate `protobuf:"varint,4,rep,packed,name=comms_template,json=commsTemplate,proto3,enum=enums.CommsTemplate" json:"comms_template,omitempty"`
	AccountNumber string                `protobuf:"bytes,5,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
}

func (x *AccountLienParameters) Reset() {
	*x = AccountLienParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_action_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountLienParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountLienParameters) ProtoMessage() {}

func (x *AccountLienParameters) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_action_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountLienParameters.ProtoReflect.Descriptor instead.
func (*AccountLienParameters) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_action_proto_rawDescGZIP(), []int{3}
}

func (x *AccountLienParameters) GetRequestReason() *RequestReason {
	if x != nil {
		return x.RequestReason
	}
	return nil
}

func (x *AccountLienParameters) GetLienAmount() *money.Money {
	if x != nil {
		return x.LienAmount
	}
	return nil
}

func (x *AccountLienParameters) GetLienDurationInHours() int32 {
	if x != nil {
		return x.LienDurationInHours
	}
	return 0
}

func (x *AccountLienParameters) GetCommsTemplate() []enums.CommsTemplate {
	if x != nil {
		return x.CommsTemplate
	}
	return nil
}

func (x *AccountLienParameters) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

type SnoozeParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SnoozeTill *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=snooze_till,json=snoozeTill,proto3" json:"snooze_till,omitempty"`
	// Types that are assignable to Options:
	//	*SnoozeParameters_OutcallOptions_
	Options isSnoozeParameters_Options `protobuf_oneof:"options"`
}

func (x *SnoozeParameters) Reset() {
	*x = SnoozeParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_action_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnoozeParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnoozeParameters) ProtoMessage() {}

func (x *SnoozeParameters) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_action_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnoozeParameters.ProtoReflect.Descriptor instead.
func (*SnoozeParameters) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_action_proto_rawDescGZIP(), []int{4}
}

func (x *SnoozeParameters) GetSnoozeTill() *timestamppb.Timestamp {
	if x != nil {
		return x.SnoozeTill
	}
	return nil
}

func (m *SnoozeParameters) GetOptions() isSnoozeParameters_Options {
	if m != nil {
		return m.Options
	}
	return nil
}

func (x *SnoozeParameters) GetOutcallOptions() *SnoozeParameters_OutcallOptions {
	if x, ok := x.GetOptions().(*SnoozeParameters_OutcallOptions_); ok {
		return x.OutcallOptions
	}
	return nil
}

type isSnoozeParameters_Options interface {
	isSnoozeParameters_Options()
}

type SnoozeParameters_OutcallOptions_ struct {
	OutcallOptions *SnoozeParameters_OutcallOptions `protobuf:"bytes,2,opt,name=outcall_options,json=outcallOptions,proto3,oneof"`
}

func (*SnoozeParameters_OutcallOptions_) isSnoozeParameters_Options() {}

type RequestUserInfoParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mode OutcallMode `protobuf:"varint,1,opt,name=mode,proto3,enum=risk.case_management.review.OutcallMode" json:"mode,omitempty"`
	// Types that are assignable to Options:
	//	*RequestUserInfoParameters_FormModeOptions
	Options isRequestUserInfoParameters_Options `protobuf_oneof:"options"`
}

func (x *RequestUserInfoParameters) Reset() {
	*x = RequestUserInfoParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_action_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestUserInfoParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestUserInfoParameters) ProtoMessage() {}

func (x *RequestUserInfoParameters) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_action_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestUserInfoParameters.ProtoReflect.Descriptor instead.
func (*RequestUserInfoParameters) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_action_proto_rawDescGZIP(), []int{5}
}

func (x *RequestUserInfoParameters) GetMode() OutcallMode {
	if x != nil {
		return x.Mode
	}
	return OutcallMode_OUTCALL_MODE_UNSPECIFIED
}

func (m *RequestUserInfoParameters) GetOptions() isRequestUserInfoParameters_Options {
	if m != nil {
		return m.Options
	}
	return nil
}

func (x *RequestUserInfoParameters) GetFormModeOptions() *FormOutcallModeOptions {
	if x, ok := x.GetOptions().(*RequestUserInfoParameters_FormModeOptions); ok {
		return x.FormModeOptions
	}
	return nil
}

type isRequestUserInfoParameters_Options interface {
	isRequestUserInfoParameters_Options()
}

type RequestUserInfoParameters_FormModeOptions struct {
	FormModeOptions *FormOutcallModeOptions `protobuf:"bytes,2,opt,name=form_mode_options,json=formModeOptions,proto3,oneof"`
}

func (*RequestUserInfoParameters_FormModeOptions) isRequestUserInfoParameters_Options() {}

type FormOutcallModeOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostOutcallDefaultAction *PostOutcallDefaultAction `protobuf:"bytes,1,opt,name=post_outcall_default_action,json=postOutcallDefaultAction,proto3" json:"post_outcall_default_action,omitempty"`
	Identifiers              *form.QuestionIdentifiers `protobuf:"bytes,2,opt,name=identifiers,proto3" json:"identifiers,omitempty"`
	ExpiresAt                *timestamppb.Timestamp    `protobuf:"bytes,3,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
}

func (x *FormOutcallModeOptions) Reset() {
	*x = FormOutcallModeOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_action_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormOutcallModeOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormOutcallModeOptions) ProtoMessage() {}

func (x *FormOutcallModeOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_action_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormOutcallModeOptions.ProtoReflect.Descriptor instead.
func (*FormOutcallModeOptions) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_action_proto_rawDescGZIP(), []int{6}
}

func (x *FormOutcallModeOptions) GetPostOutcallDefaultAction() *PostOutcallDefaultAction {
	if x != nil {
		return x.PostOutcallDefaultAction
	}
	return nil
}

func (x *FormOutcallModeOptions) GetIdentifiers() *form.QuestionIdentifiers {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

func (x *FormOutcallModeOptions) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

type PostOutcallDefaultAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// default action to be performed on the case if outcall is unsuccessful
	ActionType ActionType `protobuf:"varint,1,opt,name=action_type,json=actionType,proto3,enum=risk.case_management.review.ActionType" json:"action_type,omitempty"`
	// additional action parameters if required
	ActionParameters *ActionParameters `protobuf:"bytes,2,opt,name=action_parameters,json=actionParameters,proto3" json:"action_parameters,omitempty"`
}

func (x *PostOutcallDefaultAction) Reset() {
	*x = PostOutcallDefaultAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_action_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostOutcallDefaultAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostOutcallDefaultAction) ProtoMessage() {}

func (x *PostOutcallDefaultAction) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_action_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostOutcallDefaultAction.ProtoReflect.Descriptor instead.
func (*PostOutcallDefaultAction) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_action_proto_rawDescGZIP(), []int{7}
}

func (x *PostOutcallDefaultAction) GetActionType() ActionType {
	if x != nil {
		return x.ActionType
	}
	return ActionType_ACTION_TYPE_UNSPECIFIED
}

func (x *PostOutcallDefaultAction) GetActionParameters() *ActionParameters {
	if x != nil {
		return x.ActionParameters
	}
	return nil
}

// Reason for which request was entered onto our system
type RequestReason struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Specifies reason for which action is being taken
	Reason enums.RequestReason `protobuf:"varint,1,opt,name=reason,proto3,enum=enums.RequestReason" json:"reason,omitempty"`
	// remarks should be non empty if reason is other
	Remarks string `protobuf:"bytes,2,opt,name=remarks,proto3" json:"remarks,omitempty"`
}

func (x *RequestReason) Reset() {
	*x = RequestReason{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_action_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestReason) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestReason) ProtoMessage() {}

func (x *RequestReason) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_action_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestReason.ProtoReflect.Descriptor instead.
func (*RequestReason) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_action_proto_rawDescGZIP(), []int{8}
}

func (x *RequestReason) GetReason() enums.RequestReason {
	if x != nil {
		return x.Reason
	}
	return enums.RequestReason(0)
}

func (x *RequestReason) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

type ActionProcessingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action         *Action              `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	ProcessingType ActionProcessingType `protobuf:"varint,2,opt,name=processing_type,json=processingType,proto3,enum=risk.case_management.review.ActionProcessingType" json:"processing_type,omitempty"`
}

func (x *ActionProcessingParams) Reset() {
	*x = ActionProcessingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_action_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActionProcessingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionProcessingParams) ProtoMessage() {}

func (x *ActionProcessingParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_action_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionProcessingParams.ProtoReflect.Descriptor instead.
func (*ActionProcessingParams) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_action_proto_rawDescGZIP(), []int{9}
}

func (x *ActionProcessingParams) GetAction() *Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *ActionProcessingParams) GetProcessingType() ActionProcessingType {
	if x != nil {
		return x.ProcessingType
	}
	return ActionProcessingType_ACTION_PROCESSING_TYPE_UNSPECIFIED
}

type RejectEscalationParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Rejection reason can be used to take decision on future escalations.
	Reason string `protobuf:"bytes,1,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *RejectEscalationParameters) Reset() {
	*x = RejectEscalationParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_action_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RejectEscalationParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectEscalationParameters) ProtoMessage() {}

func (x *RejectEscalationParameters) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_action_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectEscalationParameters.ProtoReflect.Descriptor instead.
func (*RejectEscalationParameters) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_action_proto_rawDescGZIP(), []int{10}
}

func (x *RejectEscalationParameters) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type SnoozeParameters_OutcallOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If case is snoozed by outcall agent for failure to reach out to user,
	// a communication can be triggered reminding them of current and further outcall attempts.
	SendOutcallReminderComms bool `protobuf:"varint,2,opt,name=send_outcall_reminder_comms,json=sendOutcallReminderComms,proto3" json:"send_outcall_reminder_comms,omitempty"`
}

func (x *SnoozeParameters_OutcallOptions) Reset() {
	*x = SnoozeParameters_OutcallOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_action_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnoozeParameters_OutcallOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnoozeParameters_OutcallOptions) ProtoMessage() {}

func (x *SnoozeParameters_OutcallOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_action_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnoozeParameters_OutcallOptions.ProtoReflect.Descriptor instead.
func (*SnoozeParameters_OutcallOptions) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_action_proto_rawDescGZIP(), []int{4, 0}
}

func (x *SnoozeParameters_OutcallOptions) GetSendOutcallReminderComms() bool {
	if x != nil {
		return x.SendOutcallReminderComms
	}
	return false
}

var File_api_risk_case_management_review_action_proto protoreflect.FileDescriptor

var file_api_risk_case_management_review_action_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x1a, 0x2c, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x92, 0x06, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x20, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65,
	0x49, 0x64, 0x12, 0x52, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x52, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x0a, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x4b, 0x0a, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x3d, 0x0a, 0x0c, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5a,
	0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x22, 0xd7, 0x04, 0x0a, 0x10, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12,
	0x72, 0x0a, 0x19, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x7a,
	0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x34, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x17, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x12, 0x5c, 0x0a, 0x11, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x5f, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x53, 0x6e, 0x6f,
	0x6f, 0x7a, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52,
	0x10, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x79, 0x0a, 0x1c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48,
	0x00, 0x52, 0x19, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x7b, 0x0a, 0x1c,
	0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x65, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x2e, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x1a, 0x72,
	0x65, 0x6a, 0x65, 0x63, 0x74, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x6c, 0x0a, 0x17, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4c, 0x69, 0x65, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00,
	0x52, 0x15, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x65, 0x6e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x42, 0x0b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x22, 0xf6, 0x01, 0x0a, 0x17, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x4b, 0x0a, 0x0c, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x52, 0x0b, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x51, 0x0a,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x3b, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x0d,
	0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0xb8, 0x02,
	0x0a, 0x15, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x65, 0x6e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x51, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x0b, 0x6c, 0x69,
	0x65, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0a, 0x6c, 0x69, 0x65, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x33, 0x0a, 0x16, 0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x6e, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x13, 0x6c, 0x69, 0x65, 0x6e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x48,
	0x6f, 0x75, 0x72, 0x73, 0x12, 0x3b, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x94, 0x02, 0x0a, 0x10, 0x53, 0x6e, 0x6f,
	0x6f, 0x7a, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3b, 0x0a,
	0x0b, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x54, 0x69, 0x6c, 0x6c, 0x12, 0x67, 0x0a, 0x0f, 0x6f, 0x75,
	0x74, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x2e, 0x53, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x2e, 0x4f, 0x75, 0x74, 0x63, 0x61, 0x6c, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x48, 0x00, 0x52, 0x0e, 0x6f, 0x75, 0x74, 0x63, 0x61, 0x6c, 0x6c, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x1a, 0x4f, 0x0a, 0x0e, 0x4f, 0x75, 0x74, 0x63, 0x61, 0x6c, 0x6c, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3d, 0x0a, 0x1b, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6f, 0x75,
	0x74, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x6d, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x18, 0x73, 0x65, 0x6e, 0x64,
	0x4f, 0x75, 0x74, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x43,
	0x6f, 0x6d, 0x6d, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0xc7, 0x01, 0x0a, 0x19, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3c, 0x0a,
	0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x4f, 0x75, 0x74, 0x63, 0x61, 0x6c,
	0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x61, 0x0a, 0x11, 0x66,
	0x6f, 0x72, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x4f, 0x75, 0x74, 0x63, 0x61, 0x6c, 0x6c,
	0x4d, 0x6f, 0x64, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x48, 0x00, 0x52, 0x0f, 0x66,
	0x6f, 0x72, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x09,
	0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x9b, 0x02, 0x0a, 0x16, 0x46, 0x6f,
	0x72, 0x6d, 0x4f, 0x75, 0x74, 0x63, 0x61, 0x6c, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x74, 0x0a, 0x1b, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x6f, 0x75, 0x74,
	0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x63,
	0x61, 0x6c, 0x6c, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x18, 0x70, 0x6f, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x63, 0x61, 0x6c, 0x6c, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x0b, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x52,
	0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x12, 0x39, 0x0a, 0x0a,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x73, 0x41, 0x74, 0x22, 0xce, 0x01, 0x0a, 0x18, 0x50, 0x6f, 0x73, 0x74,
	0x4f, 0x75, 0x74, 0x63, 0x61, 0x6c, 0x6c, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x56, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x82, 0x01, 0x06, 0x18, 0x01, 0x18, 0x02, 0x18, 0x05,
	0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5a, 0x0a, 0x11,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x10, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x22, 0x57, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x73, 0x22, 0xc5, 0x01, 0x0a, 0x16, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x45, 0x0a, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x64, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x22, 0x34, 0x0a, 0x1a, 0x52, 0x65, 0x6a,
	0x65, 0x63, 0x74, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42,
	0x70, 0x0a, 0x36, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_review_action_proto_rawDescOnce sync.Once
	file_api_risk_case_management_review_action_proto_rawDescData = file_api_risk_case_management_review_action_proto_rawDesc
)

func file_api_risk_case_management_review_action_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_review_action_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_review_action_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_review_action_proto_rawDescData)
	})
	return file_api_risk_case_management_review_action_proto_rawDescData
}

var file_api_risk_case_management_review_action_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_api_risk_case_management_review_action_proto_goTypes = []interface{}{
	(*Action)(nil),                          // 0: risk.case_management.review.Action
	(*ActionParameters)(nil),                // 1: risk.case_management.review.ActionParameters
	(*AccountFreezeParameters)(nil),         // 2: risk.case_management.review.AccountFreezeParameters
	(*AccountLienParameters)(nil),           // 3: risk.case_management.review.AccountLienParameters
	(*SnoozeParameters)(nil),                // 4: risk.case_management.review.SnoozeParameters
	(*RequestUserInfoParameters)(nil),       // 5: risk.case_management.review.RequestUserInfoParameters
	(*FormOutcallModeOptions)(nil),          // 6: risk.case_management.review.FormOutcallModeOptions
	(*PostOutcallDefaultAction)(nil),        // 7: risk.case_management.review.PostOutcallDefaultAction
	(*RequestReason)(nil),                   // 8: risk.case_management.review.RequestReason
	(*ActionProcessingParams)(nil),          // 9: risk.case_management.review.ActionProcessingParams
	(*RejectEscalationParameters)(nil),      // 10: risk.case_management.review.RejectEscalationParameters
	(*SnoozeParameters_OutcallOptions)(nil), // 11: risk.case_management.review.SnoozeParameters.OutcallOptions
	(ReviewType)(0),                         // 12: risk.case_management.review.ReviewType
	(ActionType)(0),                         // 13: risk.case_management.review.ActionType
	(ActionSource)(0),                       // 14: risk.case_management.review.ActionSource
	(*timestamppb.Timestamp)(nil),           // 15: google.protobuf.Timestamp
	(ActionStatus)(0),                       // 16: risk.case_management.review.ActionStatus
	(ActionProcessingType)(0),               // 17: risk.case_management.review.ActionProcessingType
	(FreezeLevel)(0),                        // 18: risk.case_management.review.FreezeLevel
	(enums.CommsTemplate)(0),                // 19: enums.CommsTemplate
	(*money.Money)(nil),                     // 20: google.type.Money
	(OutcallMode)(0),                        // 21: risk.case_management.review.OutcallMode
	(*form.QuestionIdentifiers)(nil),        // 22: risk.case_management.form.QuestionIdentifiers
	(enums.RequestReason)(0),                // 23: enums.RequestReason
}
var file_api_risk_case_management_review_action_proto_depIdxs = []int32{
	12, // 0: risk.case_management.review.Action.review_type:type_name -> risk.case_management.review.ReviewType
	13, // 1: risk.case_management.review.Action.action_type:type_name -> risk.case_management.review.ActionType
	1,  // 2: risk.case_management.review.Action.parameters:type_name -> risk.case_management.review.ActionParameters
	14, // 3: risk.case_management.review.Action.source:type_name -> risk.case_management.review.ActionSource
	15, // 4: risk.case_management.review.Action.initiated_at:type_name -> google.protobuf.Timestamp
	15, // 5: risk.case_management.review.Action.created_at:type_name -> google.protobuf.Timestamp
	15, // 6: risk.case_management.review.Action.updated_at:type_name -> google.protobuf.Timestamp
	16, // 7: risk.case_management.review.Action.status:type_name -> risk.case_management.review.ActionStatus
	17, // 8: risk.case_management.review.Action.processing_type:type_name -> risk.case_management.review.ActionProcessingType
	2,  // 9: risk.case_management.review.ActionParameters.account_freeze_parameters:type_name -> risk.case_management.review.AccountFreezeParameters
	4,  // 10: risk.case_management.review.ActionParameters.snooze_parameters:type_name -> risk.case_management.review.SnoozeParameters
	5,  // 11: risk.case_management.review.ActionParameters.request_user_info_parameters:type_name -> risk.case_management.review.RequestUserInfoParameters
	10, // 12: risk.case_management.review.ActionParameters.reject_escalation_parameters:type_name -> risk.case_management.review.RejectEscalationParameters
	3,  // 13: risk.case_management.review.ActionParameters.account_lien_parameters:type_name -> risk.case_management.review.AccountLienParameters
	18, // 14: risk.case_management.review.AccountFreezeParameters.freeze_level:type_name -> risk.case_management.review.FreezeLevel
	8,  // 15: risk.case_management.review.AccountFreezeParameters.request_reason:type_name -> risk.case_management.review.RequestReason
	19, // 16: risk.case_management.review.AccountFreezeParameters.comms_template:type_name -> enums.CommsTemplate
	8,  // 17: risk.case_management.review.AccountLienParameters.request_reason:type_name -> risk.case_management.review.RequestReason
	20, // 18: risk.case_management.review.AccountLienParameters.lien_amount:type_name -> google.type.Money
	19, // 19: risk.case_management.review.AccountLienParameters.comms_template:type_name -> enums.CommsTemplate
	15, // 20: risk.case_management.review.SnoozeParameters.snooze_till:type_name -> google.protobuf.Timestamp
	11, // 21: risk.case_management.review.SnoozeParameters.outcall_options:type_name -> risk.case_management.review.SnoozeParameters.OutcallOptions
	21, // 22: risk.case_management.review.RequestUserInfoParameters.mode:type_name -> risk.case_management.review.OutcallMode
	6,  // 23: risk.case_management.review.RequestUserInfoParameters.form_mode_options:type_name -> risk.case_management.review.FormOutcallModeOptions
	7,  // 24: risk.case_management.review.FormOutcallModeOptions.post_outcall_default_action:type_name -> risk.case_management.review.PostOutcallDefaultAction
	22, // 25: risk.case_management.review.FormOutcallModeOptions.identifiers:type_name -> risk.case_management.form.QuestionIdentifiers
	15, // 26: risk.case_management.review.FormOutcallModeOptions.expires_at:type_name -> google.protobuf.Timestamp
	13, // 27: risk.case_management.review.PostOutcallDefaultAction.action_type:type_name -> risk.case_management.review.ActionType
	1,  // 28: risk.case_management.review.PostOutcallDefaultAction.action_parameters:type_name -> risk.case_management.review.ActionParameters
	23, // 29: risk.case_management.review.RequestReason.reason:type_name -> enums.RequestReason
	0,  // 30: risk.case_management.review.ActionProcessingParams.action:type_name -> risk.case_management.review.Action
	17, // 31: risk.case_management.review.ActionProcessingParams.processing_type:type_name -> risk.case_management.review.ActionProcessingType
	32, // [32:32] is the sub-list for method output_type
	32, // [32:32] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_review_action_proto_init() }
func file_api_risk_case_management_review_action_proto_init() {
	if File_api_risk_case_management_review_action_proto != nil {
		return
	}
	file_api_risk_case_management_review_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_case_management_review_action_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Action); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_action_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActionParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_action_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountFreezeParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_action_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountLienParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_action_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnoozeParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_action_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestUserInfoParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_action_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormOutcallModeOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_action_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostOutcallDefaultAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_action_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestReason); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_action_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActionProcessingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_action_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RejectEscalationParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_action_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnoozeParameters_OutcallOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_case_management_review_action_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*ActionParameters_AccountFreezeParameters)(nil),
		(*ActionParameters_SnoozeParameters)(nil),
		(*ActionParameters_RequestUserInfoParameters)(nil),
		(*ActionParameters_RejectEscalationParameters)(nil),
		(*ActionParameters_AccountLienParameters)(nil),
	}
	file_api_risk_case_management_review_action_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*SnoozeParameters_OutcallOptions_)(nil),
	}
	file_api_risk_case_management_review_action_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*RequestUserInfoParameters_FormModeOptions)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_review_action_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_case_management_review_action_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_review_action_proto_depIdxs,
		MessageInfos:      file_api_risk_case_management_review_action_proto_msgTypes,
	}.Build()
	File_api_risk_case_management_review_action_proto = out.File
	file_api_risk_case_management_review_action_proto_rawDesc = nil
	file_api_risk_case_management_review_action_proto_goTypes = nil
	file_api_risk_case_management_review_action_proto_depIdxs = nil
}
