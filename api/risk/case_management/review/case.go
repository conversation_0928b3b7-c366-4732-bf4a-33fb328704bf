package review

func (c *Case) ShouldAllowNewActions() bool {
	switch c.GetStatus() {
	case Status_STATUS_REVIEW_ACTION_IN_PROGRESS:
		return false
	default:
		return true
	}
}

// InReview method will return true if a review has been started OR Completed for the given case
// will return false if review has not yet begun for the case i.e. case is in pre-review stages
func (c *Case) InReview() bool {
	switch c.GetStatus() {
	// if case is still in created or assigned stage review has not been started for the case
	case Status_STATUS_ASSIGNED, Status_STATUS_CREATED:
		return false
	default:
		return true
	}
}

// CanAppendAlert helps determine the cases in which alerts can be appended
func (c *Case) CanAppendAlert() bool {
	// case can be appended for lea review type even though it is closed
	if c.GetReviewType() == ReviewType_REVIEW_TYPE_LEA_COMPLAINT_REVIEW {
		return true
	}
	switch c.GetStatus() {
	// if case is still in created or assigned stage review has not been started for the case
	// exception to that will be out-call/ pending user info stage where another review will take place
	// post out call
	case Status_STATUS_ASSIGNED, Status_STATUS_CREATED, Status_STATUS_PENDING_USER_INFO,
		Status_STATUS_MARKED_FOR_AUTO_ACTION, Status_STATUS_PENDING_ON_USER:
		return true
	default:
		return false
	}
}
