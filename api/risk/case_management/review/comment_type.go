package review

import "errors"

var CouldNotConvertCommentType = errors.New("could not convert to the comment type")

func (ct *CommentType) ToString(entityType ReviewEntityType) (string, error) {
	switch entityType {
	case ReviewEntityType_REVIEW_ENTITY_TYPE_USER:
		if ct.GetUserCommentType() != UserCommentType_USER_COMMENT_TYPE_UNSPECIFIED {
			return ct.GetUserCommentType().String(), nil
		}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_TRANSACTION:
		if ct.GetTxnCommentType() != TxnCommentType_TXN_COMMENT_TYPE_UNSPECIFIED {
			return ct.GetTxnCommentType().String(), nil
		}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_LIVENESS:
		if ct.GetLivenessCommentType() != LivenessCommentType_LIVENESS_COMMENT_TYPE_UNSPECIFIED {
			return ct.GetLivenessCommentType().String(), nil
		}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_CASE:
		if ct.GetCaseCommentType() != CaseCommentType_CASE_COMMENT_TYPE_UNSPECIFIED {
			return ct.GetCaseCommentType().String(), nil
		}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_FACEMATCH:
		if ct.GetFacematchCommentType() != FacematchCommentType_FACEMATCH_COMMENT_TYPE_UNSPECIFIED {
			return ct.GetFacematchCommentType().String(), nil
		}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_AFU:
		if ct.GetAfuCommentType() != AfuCommentType_AFU_COMMENT_TYPE_UNSPECIFIED {
			return ct.GetAfuCommentType().String(), nil
		}
	case ReviewEntityType_REVIEW_ENTITY_TYPE_LEA_COMPLAINT:
		if ct.GetLeaComplaintCommentType() != LEAComplaintCommentType_LEA_COMPLAINT_COMMENT_TYPE_UNSPECIFIED {
			return ct.GetLeaComplaintCommentType().String(), nil
		}
	}
	return "", CouldNotConvertCommentType
}
