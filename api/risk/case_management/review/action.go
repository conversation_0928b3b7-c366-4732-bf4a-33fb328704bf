//nolint:all
package review

import "github.com/samber/lo"

// Blocking action is an action after which no other action is allowed on a case
func (a ActionType) IsBlockingAction() bool {
	switch a {
	// snooze is not a blocking action
	case ActionType_ACTION_TYPE_SNOOZE:
		return false
	// fail afu is not a blocking action since freeze can be applied after this.
	case ActionType_ACTION_TYPE_FAIL_AFU:
		return false
	default:
		return true
	}
}

// IsTerminalAction Returns true when terminal action against a case
func (a ActionType) IsTerminalAction() bool {
	return lo.Contains(GetTerminalActions(), a)
}

func GetTerminalActions() []ActionType {
	return []ActionType{
		ActionType_ACTION_TYPE_ADD_LIVENESS_RETRIES,
		ActionType_ACTION_TYPE_FAIL_USER_ONBOARDING,
		ActionType_ACTION_TYPE_PASS_USER_ONBOARDING,
		ActionType_ACTION_TYPE_FAIL_AFU,
		ActionType_ACTION_TYPE_PASS_AFU,
		ActionType_ACTION_TYPE_PASS_ACCOUNT,
		ActionType_ACTION_TYPE_UNFREEZE_ACCOUNT,
		ActionType_ACTION_TYPE_FREEZE_ACCOUNT,
		ActionType_ACTION_TYPE_INVESTIGATE_LEA_ACTOR,
		ActionType_ACTION_TYPE_REJECT_ESCALATION,
	}
}
