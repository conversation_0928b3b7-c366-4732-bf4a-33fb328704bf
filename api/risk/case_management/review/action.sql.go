package review

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// <PERSON>an implements sql.Scanner interface
// It parses the data from DB and converts to required type
// Value implements driver.Valuer interface
// It stores data as string in DB
func (d *ActionParameters) Scan(input interface{}) error {
	val, ok := input.([]byte)
	if !ok {
		return fmt.Errorf("expected []byte, got %T", input)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, val, d)
}

func (d *ActionParameters) Value() (driver.Value, error) {
	if d == nil {
		return nil, nil
	}
	return protojson.Marshal(d)
}

func (d *ActionParameters) UnmarshalJSON(marshalledData []byte) error {
	return protojson.Unmarshal(marshalledData, d)
}

func (d *ActionParameters) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(d)
}
