package review

import (
	"fmt"

	"github.com/pkg/errors"
)

var (
	// PriorityOrderMap Map will contain the actual priority order against a given Priority enum
	// 1 being the highest priority in the order
	priorityOrderMap = map[Priority]int{
		Priority_PRIORITY_CRITICAL:    1,
		Priority_PRIORITY_HIGH:        2,
		Priority_PRIORITY_MEDIUM:      3,
		Priority_PRIORITY_LOW:         4,
		Priority_PRIORITY_UNSPECIFIED: 10,
	}

	ErrPriorityOrderMappingNotFound = errors.New("priority order is missing for given enum value")
)

func (p Priority) IsGreaterThan(otherPriority Priority) (bool, error) {
	currentPriorityOrder, ok := priorityOrderMap[p]
	if !ok {
		return false, errors.Wrap(ErrPriorityOrderMappingNotFound, fmt.Sprintf("error while getting priority order for: %s", p.String()))
	}

	otherPriorityOrder, ok := priorityOrderMap[otherPriority]
	if !ok {
		return false, errors.Wrap(ErrPriorityOrderMappingNotFound, fmt.Sprintf("error while getting priority order for: %s", p.String()))
	}
	// If current priority order number is lower, Current priority is higher than the other priority
	return currentPriorityOrder < otherPriorityOrder, nil
}
