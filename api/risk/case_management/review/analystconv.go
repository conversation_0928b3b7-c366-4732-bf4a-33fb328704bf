package review

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"strconv"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	vgCrmPb "github.com/epifi/gamma/api/vendorgateway/crm"
	vgRiskPb "github.com/epifi/gamma/api/vendorgateway/crm/risk"
)

var (
	ErrNilAnalystObject = errors.New("analyst object is nil")
	analystGroupMap     = map[AnalystGroup]vgRiskPb.AgentGroup{
		AnalystGroup_ANALYST_GROUP_TRANSACTION_REVIEW: vgRiskPb.AgentGroup_AGENT_GROUP_TRANSACTION_REVIEW,
		AnalystGroup_ANALYST_GROUP_USER_OUTBOUND_CALL: vgRiskPb.AgentGroup_AGENT_GROUP_USER_OUTBOUND_CALL,
		AnalystGroup_ANALYST_GROUP_USER_REVIEW:        vgRiskPb.AgentGroup_AGENT_GROUP_USER_REVIEW,
		AnalystGroup_ANALYST_GROUP_L1:                 vgRiskPb.AgentGroup_AGENT_GROUP_L1,
		AnalystGroup_ANALYST_GROUP_L2:                 vgRiskPb.AgentGroup_AGENT_GROUP_L2,
		AnalystGroup_ANALYST_GROUP_QA_REVIEW:          vgRiskPb.AgentGroup_AGENT_GROUP_QA,
		AnalystGroup_ANALYST_GROUP_ESCALATION:         vgRiskPb.AgentGroup_AGENT_GROUP_ESCALATION,
		AnalystGroup_ANALYST_GROUP_MULTI_REVIEW:       vgRiskPb.AgentGroup_AGENT_GROUP_MULTI_REVIEW,
	}
	analystGroupReverseMap = lo.Invert(analystGroupMap)
)

// GetCMAnalystFromCrmAgent will convert crm agent object to case management analyst.
func GetCMAnalystFromCrmAgent(ctx context.Context, vgCrmAnalyst *vgCrmPb.Agent) (*Analyst, error) {
	if vgCrmAnalyst == nil {
		return nil, ErrNilAnalystObject
	}
	var (
		analystGroups []AnalystGroup
		name          *commontypes.Name
		analystId     string
	)
	for _, group := range vgCrmAnalyst.GetRiskAgentAdditionalDetails().GetGroups() {
		analystGroup, ok := analystGroupReverseMap[group]
		if !ok {
			logger.Error(ctx, "failed to map crm agent group to analyst group", zap.String(logger.ANALYST_GROUP, group.String()))
			continue
		}
		analystGroups = append(analystGroups, analystGroup)
	}

	if len(vgCrmAnalyst.GetName()) != 0 {
		name = (&commontypes.Name{}).Parse(vgCrmAnalyst.GetName())
	}

	if vgCrmAnalyst.Id != 0 {
		analystId = strconv.FormatUint(vgCrmAnalyst.GetId(), 10)
	}

	return &Analyst{
		EmailId:      vgCrmAnalyst.GetEmail(),
		PhoneNumber:  vgCrmAnalyst.GetPhone(),
		Name:         name,
		Id:           analystId,
		Groups:       analystGroups,
		CreatedAt:    vgCrmAnalyst.GetCreatedAt(),
		UpdatedAt:    vgCrmAnalyst.GetUpdatedAt(),
		LastActiveAt: vgCrmAnalyst.GetLastActiveAt(),
	}, nil
}
