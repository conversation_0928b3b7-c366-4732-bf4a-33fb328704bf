package review

import (
	"database/sql/driver"
	"fmt"
)

// Valuer interface implementation for storing the data  in string format in DB
func (a ActionType) Value() (driver.Value, error) {
	return a.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (a *ActionType) Scan(input interface{}) error {
	if a == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := ActionType_value[val]
	*a = ActionType(valInt)
	return nil
}

// Valuer interface implementation for storing the data  in string format in DB
func (a ActionSource) Value() (driver.Value, error) {
	return a.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (a *ActionSource) Scan(input interface{}) error {
	if a == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := ActionSource_value[val]
	*a = ActionSource(valInt)
	return nil
}

// Valuer interface implementation for storing the data  in string format in DB
func (a ReviewType) Value() (driver.Value, error) {
	return a.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (a *ReviewType) Scan(input interface{}) error {
	if a == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := ReviewType_value[val]
	*a = ReviewType(valInt)
	return nil
}

// Valuer interface implementation for storing the data  in string format in DB
func (a ReviewEntityType) Value() (driver.Value, error) {
	return a.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (a *ReviewEntityType) Scan(input interface{}) error {
	if a == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := ReviewEntityType_value[val]
	*a = ReviewEntityType(valInt)
	return nil
}
