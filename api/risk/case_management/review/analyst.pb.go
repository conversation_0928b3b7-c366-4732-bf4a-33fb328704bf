// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/review/analyst.proto

package review

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Analyst object to represent persons associated with reviewing cases.
// It enables assigning cases to specific analysts.
type Analyst struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// official registered email id of analyst with epifi
	EmailId string `protobuf:"bytes,2,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	// phone number of analyst
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Name        *common.Name        `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// Groups that analyst is part of
	Groups []AnalystGroup `protobuf:"varint,5,rep,packed,name=groups,proto3,enum=risk.case_management.review.AnalystGroup" json:"groups,omitempty"`
	// Analyst creation timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Analyst updated timestamp
	UpdatedAt    *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	LastActiveAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=last_active_at,json=lastActiveAt,proto3" json:"last_active_at,omitempty"`
}

func (x *Analyst) Reset() {
	*x = Analyst{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_analyst_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Analyst) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Analyst) ProtoMessage() {}

func (x *Analyst) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_analyst_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Analyst.ProtoReflect.Descriptor instead.
func (*Analyst) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_analyst_proto_rawDescGZIP(), []int{0}
}

func (x *Analyst) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Analyst) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *Analyst) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *Analyst) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *Analyst) GetGroups() []AnalystGroup {
	if x != nil {
		return x.Groups
	}
	return nil
}

func (x *Analyst) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Analyst) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Analyst) GetLastActiveAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastActiveAt
	}
	return nil
}

// AnalystIdentifier defines the identifier which can be used to fetch Analyst object.
type AnalystIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*AnalystIdentifier_AnalystEmail
	//	*AnalystIdentifier_Id
	Identifier isAnalystIdentifier_Identifier `protobuf_oneof:"identifier"`
}

func (x *AnalystIdentifier) Reset() {
	*x = AnalystIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_analyst_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalystIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalystIdentifier) ProtoMessage() {}

func (x *AnalystIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_analyst_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalystIdentifier.ProtoReflect.Descriptor instead.
func (*AnalystIdentifier) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_analyst_proto_rawDescGZIP(), []int{1}
}

func (m *AnalystIdentifier) GetIdentifier() isAnalystIdentifier_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *AnalystIdentifier) GetAnalystEmail() string {
	if x, ok := x.GetIdentifier().(*AnalystIdentifier_AnalystEmail); ok {
		return x.AnalystEmail
	}
	return ""
}

func (x *AnalystIdentifier) GetId() string {
	if x, ok := x.GetIdentifier().(*AnalystIdentifier_Id); ok {
		return x.Id
	}
	return ""
}

type isAnalystIdentifier_Identifier interface {
	isAnalystIdentifier_Identifier()
}

type AnalystIdentifier_AnalystEmail struct {
	AnalystEmail string `protobuf:"bytes,1,opt,name=analyst_email,json=analystEmail,proto3,oneof"`
}

type AnalystIdentifier_Id struct {
	Id string `protobuf:"bytes,2,opt,name=id,proto3,oneof"`
}

func (*AnalystIdentifier_AnalystEmail) isAnalystIdentifier_Identifier() {}

func (*AnalystIdentifier_Id) isAnalystIdentifier_Identifier() {}

var File_api_risk_case_management_review_analyst_proto protoreflect.FileDescriptor

var file_api_risk_case_management_review_analyst_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1b, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x1a, 0x2b, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xa1, 0x03, 0x0a, 0x07, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x06, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x40, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x41, 0x74, 0x22, 0x5a, 0x0a, 0x11, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0d, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x10, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x42, 0x70, 0x0a, 0x36, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_review_analyst_proto_rawDescOnce sync.Once
	file_api_risk_case_management_review_analyst_proto_rawDescData = file_api_risk_case_management_review_analyst_proto_rawDesc
)

func file_api_risk_case_management_review_analyst_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_review_analyst_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_review_analyst_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_review_analyst_proto_rawDescData)
	})
	return file_api_risk_case_management_review_analyst_proto_rawDescData
}

var file_api_risk_case_management_review_analyst_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_risk_case_management_review_analyst_proto_goTypes = []interface{}{
	(*Analyst)(nil),               // 0: risk.case_management.review.Analyst
	(*AnalystIdentifier)(nil),     // 1: risk.case_management.review.AnalystIdentifier
	(*common.PhoneNumber)(nil),    // 2: api.typesv2.common.PhoneNumber
	(*common.Name)(nil),           // 3: api.typesv2.common.Name
	(AnalystGroup)(0),             // 4: risk.case_management.review.AnalystGroup
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
}
var file_api_risk_case_management_review_analyst_proto_depIdxs = []int32{
	2, // 0: risk.case_management.review.Analyst.phone_number:type_name -> api.typesv2.common.PhoneNumber
	3, // 1: risk.case_management.review.Analyst.name:type_name -> api.typesv2.common.Name
	4, // 2: risk.case_management.review.Analyst.groups:type_name -> risk.case_management.review.AnalystGroup
	5, // 3: risk.case_management.review.Analyst.created_at:type_name -> google.protobuf.Timestamp
	5, // 4: risk.case_management.review.Analyst.updated_at:type_name -> google.protobuf.Timestamp
	5, // 5: risk.case_management.review.Analyst.last_active_at:type_name -> google.protobuf.Timestamp
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_review_analyst_proto_init() }
func file_api_risk_case_management_review_analyst_proto_init() {
	if File_api_risk_case_management_review_analyst_proto != nil {
		return
	}
	file_api_risk_case_management_review_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_case_management_review_analyst_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Analyst); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_analyst_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnalystIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_case_management_review_analyst_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*AnalystIdentifier_AnalystEmail)(nil),
		(*AnalystIdentifier_Id)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_review_analyst_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_case_management_review_analyst_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_review_analyst_proto_depIdxs,
		MessageInfos:      file_api_risk_case_management_review_analyst_proto_msgTypes,
	}.Build()
	File_api_risk_case_management_review_analyst_proto = out.File
	file_api_risk_case_management_review_analyst_proto_rawDesc = nil
	file_api_risk_case_management_review_analyst_proto_goTypes = nil
	file_api_risk_case_management_review_analyst_proto_depIdxs = nil
}
