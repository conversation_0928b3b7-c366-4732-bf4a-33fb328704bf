package enums

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"

	"github.com/lib/pq"
)

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x Provenance) Value() (driver.Value, error) {
	return x.String(), nil
}

// <PERSON>an implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *Provenance) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valEm := Provenance_value[val]
	*x = Provenance(valEm)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x EntityType) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *EntityType) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.E<PERSON><PERSON>("type conversion to string failed, src: %T", val)
	}

	valEm := EntityType_value[val]
	*x = EntityType(valEm)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x AccountType) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *AccountType) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valEm := AccountType_value[val]
	*x = AccountType(valEm)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x Verdict) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *Verdict) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valEm := Verdict_value[val]
	*x = Verdict(valEm)
	return nil
}

type AlertHandlingReasons []AlertHandlingReason

func (a AlertHandlingReasons) Value() (driver.Value, error) {
	var groups []string
	for _, g := range a {
		groups = append(groups, g.String())
	}
	strArr := pq.StringArray(groups)
	return strArr.Value()
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (a *AlertHandlingReasons) Scan(src interface{}) error {
	strArr := pq.StringArray([]string{})
	if err := strArr.Scan(src); err != nil {
		return fmt.Errorf("failed to scan []string using pq.StringArray, str: %v", src)
	}

	var groups []AlertHandlingReason
	for _, v := range strArr {
		groups = append(groups, AlertHandlingReason(AlertHandlingReason_value[v]))
	}

	*a = groups
	return nil
}

func (a *AlertHandlingReasons) UnmarshalJSON(marshalledData []byte) error {
	var val []string
	err := json.Unmarshal(marshalledData, &val)
	if err != nil {
		return err
	}
	var values []AlertHandlingReason
	for _, v := range val {
		values = append(values, AlertHandlingReason(AlertHandlingReason_value[v]))
	}
	*a = values
	return nil
}

// MarshalJSON implements the Marshaler interface
func (a AlertHandlingReasons) MarshalJSON() ([]byte, error) {
	var groups []string
	for _, g := range a {
		groups = append(groups, g.String())
	}
	return json.Marshal(groups)
}
