//go:generate gen_sql -types=AlertHandlingReason,AlertHandlingType

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/enums/enums.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Each review or investigation of a case needs to result in one of the given verdict values
type Verdict int32

const (
	Verdict_VERDICT_UNSPECIFIED Verdict = 0
	// Entity is deemed to be non-fraudulent and will chose to have similar access
	Verdict_VERDICT_PASS Verdict = 1
	// Entity is deemed to be fraudulent and will have restricted access
	Verdict_VERDICT_FAIL Verdict = 2
)

// Enum value maps for Verdict.
var (
	Verdict_name = map[int32]string{
		0: "VERDICT_UNSPECIFIED",
		1: "VERDICT_PASS",
		2: "VERDICT_FAIL",
	}
	Verdict_value = map[string]int32{
		"VERDICT_UNSPECIFIED": 0,
		"VERDICT_PASS":        1,
		"VERDICT_FAIL":        2,
	}
)

func (x Verdict) Enum() *Verdict {
	p := new(Verdict)
	*p = x
	return p
}

func (x Verdict) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Verdict) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_enums_enums_proto_enumTypes[0].Descriptor()
}

func (Verdict) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_enums_enums_proto_enumTypes[0]
}

func (x Verdict) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Verdict.Descriptor instead.
func (Verdict) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_enums_enums_proto_rawDescGZIP(), []int{0}
}

// In context of case management we can store and access different information at different levels
// Ex: We can have information like alerts, annotation, actions etc which we might want to fetch against different levels like
// case, entity, account or actor level etc
// Adding a generic information level enum to be used in different rpcs which requires this filter
type InformationLevel int32

const (
	InformationLevel_INFORMATION_LEVEL_UNSPECIFIED InformationLevel = 0
	// to be used for fetching information only against case directly
	InformationLevel_INFORMATION_LEVEL_CASE InformationLevel = 1
	// to be used for fetching information against flagged entity
	InformationLevel_INFORMATION_LEVEL_ENTITY InformationLevel = 2
	// to be used for fetching information against flagged account
	InformationLevel_INFORMATION_LEVEL_ACCOUNT InformationLevel = 3
	// to be used for fetching information against flagged actor
	InformationLevel_INFORMATION_LEVEL_ACTOR InformationLevel = 4
)

// Enum value maps for InformationLevel.
var (
	InformationLevel_name = map[int32]string{
		0: "INFORMATION_LEVEL_UNSPECIFIED",
		1: "INFORMATION_LEVEL_CASE",
		2: "INFORMATION_LEVEL_ENTITY",
		3: "INFORMATION_LEVEL_ACCOUNT",
		4: "INFORMATION_LEVEL_ACTOR",
	}
	InformationLevel_value = map[string]int32{
		"INFORMATION_LEVEL_UNSPECIFIED": 0,
		"INFORMATION_LEVEL_CASE":        1,
		"INFORMATION_LEVEL_ENTITY":      2,
		"INFORMATION_LEVEL_ACCOUNT":     3,
		"INFORMATION_LEVEL_ACTOR":       4,
	}
)

func (x InformationLevel) Enum() *InformationLevel {
	p := new(InformationLevel)
	*p = x
	return p
}

func (x InformationLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InformationLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_enums_enums_proto_enumTypes[1].Descriptor()
}

func (InformationLevel) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_enums_enums_proto_enumTypes[1]
}

func (x InformationLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InformationLevel.Descriptor instead.
func (InformationLevel) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_enums_enums_proto_rawDescGZIP(), []int{1}
}

// EntityType will indicate against which entity a alert is raised
// we can have different rules assessing different properties of user in the system
// Ex: we can have rule which are just assessing each transaction happening in the system vs a rule(model) which is assessing liveness video of a user
// based on what is being assessed an alert will be raised against a particular entity type
// Whatever has 1:n mapping with the user/account can be a separate entity. Others are just properties and the user entity for example can be tagged here.
type EntityType int32

const (
	EntityType_ENTITY_TYPE_UNSPECIFIED       EntityType = 0
	EntityType_ENTITY_TYPE_USER              EntityType = 1
	EntityType_ENTITY_TYPE_TRANSACTION       EntityType = 2
	EntityType_ENTITY_TYPE_LIVENESS          EntityType = 3
	EntityType_ENTITY_TYPE_AFU               EntityType = 4
	EntityType_ENTITY_TYPE_SCREENER          EntityType = 5
	EntityType_ENTITY_TYPE_ESCALATION        EntityType = 6
	EntityType_ENTITY_TYPE_TRANSACTION_BLOCK EntityType = 7
)

// Enum value maps for EntityType.
var (
	EntityType_name = map[int32]string{
		0: "ENTITY_TYPE_UNSPECIFIED",
		1: "ENTITY_TYPE_USER",
		2: "ENTITY_TYPE_TRANSACTION",
		3: "ENTITY_TYPE_LIVENESS",
		4: "ENTITY_TYPE_AFU",
		5: "ENTITY_TYPE_SCREENER",
		6: "ENTITY_TYPE_ESCALATION",
		7: "ENTITY_TYPE_TRANSACTION_BLOCK",
	}
	EntityType_value = map[string]int32{
		"ENTITY_TYPE_UNSPECIFIED":       0,
		"ENTITY_TYPE_USER":              1,
		"ENTITY_TYPE_TRANSACTION":       2,
		"ENTITY_TYPE_LIVENESS":          3,
		"ENTITY_TYPE_AFU":               4,
		"ENTITY_TYPE_SCREENER":          5,
		"ENTITY_TYPE_ESCALATION":        6,
		"ENTITY_TYPE_TRANSACTION_BLOCK": 7,
	}
)

func (x EntityType) Enum() *EntityType {
	p := new(EntityType)
	*p = x
	return p
}

func (x EntityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EntityType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_enums_enums_proto_enumTypes[2].Descriptor()
}

func (EntityType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_enums_enums_proto_enumTypes[2]
}

func (x EntityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EntityType.Descriptor instead.
func (EntityType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_enums_enums_proto_rawDescGZIP(), []int{2}
}

type AccountType int32

const (
	AccountType_ACCOUNT_TYPE_UNSPECIFIED AccountType = 0
	AccountType_ACCOUNT_TYPE_SAVING_BANK AccountType = 1
)

// Enum value maps for AccountType.
var (
	AccountType_name = map[int32]string{
		0: "ACCOUNT_TYPE_UNSPECIFIED",
		1: "ACCOUNT_TYPE_SAVING_BANK",
	}
	AccountType_value = map[string]int32{
		"ACCOUNT_TYPE_UNSPECIFIED": 0,
		"ACCOUNT_TYPE_SAVING_BANK": 1,
	}
)

func (x AccountType) Enum() *AccountType {
	p := new(AccountType)
	*p = x
	return p
}

func (x AccountType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_enums_enums_proto_enumTypes[3].Descriptor()
}

func (AccountType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_enums_enums_proto_enumTypes[3]
}

func (x AccountType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountType.Descriptor instead.
func (AccountType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_enums_enums_proto_rawDescGZIP(), []int{3}
}

// In what system the rule is running and alert was generated from
type Provenance int32

const (
	Provenance_PROVENANCE_UNSPECIFIED Provenance = 0
	// dronapay is a rule engine we are using for real time transaction monitoring
	Provenance_PROVENANCE_DRONAPAY Provenance = 1
	// DS model running on live onboarding/reonboarding events as well as back runs against set of users
	Provenance_PROVENANCE_DS_RISK_MODEL Provenance = 3
	// manual and automated query runs on snowflake
	Provenance_PROVENANCE_SNOWFLAKE_RULES Provenance = 4
	// When a crime is committed, victims approach the law enforcement agencies(Cyber police) who then forward the details to the banking partner
	// e.g., Federal bank. Banking partners will then forward either partial details or full data to us.
	Provenance_PROVENANCE_LEA Provenance = 5
	// Internal risk screener e.g. onboarding internal risk checks.
	Provenance_PROVENANCE_RISK_SCREENER Provenance = 6
	// Escalation including CX incoming
	Provenance_PROVENANCE_ESCALATION Provenance = 7
	// Data Analytics rules running in big query
	Provenance_PROVENANCE_DATA_ANALYTICS Provenance = 8
)

// Enum value maps for Provenance.
var (
	Provenance_name = map[int32]string{
		0: "PROVENANCE_UNSPECIFIED",
		1: "PROVENANCE_DRONAPAY",
		3: "PROVENANCE_DS_RISK_MODEL",
		4: "PROVENANCE_SNOWFLAKE_RULES",
		5: "PROVENANCE_LEA",
		6: "PROVENANCE_RISK_SCREENER",
		7: "PROVENANCE_ESCALATION",
		8: "PROVENANCE_DATA_ANALYTICS",
	}
	Provenance_value = map[string]int32{
		"PROVENANCE_UNSPECIFIED":     0,
		"PROVENANCE_DRONAPAY":        1,
		"PROVENANCE_DS_RISK_MODEL":   3,
		"PROVENANCE_SNOWFLAKE_RULES": 4,
		"PROVENANCE_LEA":             5,
		"PROVENANCE_RISK_SCREENER":   6,
		"PROVENANCE_ESCALATION":      7,
		"PROVENANCE_DATA_ANALYTICS":  8,
	}
)

func (x Provenance) Enum() *Provenance {
	p := new(Provenance)
	*p = x
	return p
}

func (x Provenance) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Provenance) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_enums_enums_proto_enumTypes[4].Descriptor()
}

func (Provenance) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_enums_enums_proto_enumTypes[4]
}

func (x Provenance) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Provenance.Descriptor instead.
func (Provenance) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_enums_enums_proto_rawDescGZIP(), []int{4}
}

// enum to indicate what type of action needs to be performed for a given alert
// action level can be factor of different attributes like system/rule which generated the alert, rule status or score etc
type AlertActionLevel int32

const (
	AlertActionLevel_ALERT_ACTION_LEVEL_UNSPECIFIED AlertActionLevel = 0
	// No action is needed for the alert
	AlertActionLevel_ALERT_ACTION_LEVEL_NONE AlertActionLevel = 1
	// A case needs to be created and manual review is required
	AlertActionLevel_ALERT_ACTION_LEVEL_MANUAL_REVIEW AlertActionLevel = 2
	// A case needs to be created and user can be autoblocked
	//
	// Deprecated: Marked as deprecated in api/risk/case_management/enums/enums.proto.
	AlertActionLevel_ALERT_ACTION_LEVEL_AUTO_BLOCK AlertActionLevel = 3
	// A case needs to be created and an auto-action needs to be performed on the case
	// action to be peformed can depend on multiple factors like rule, score stc and will be derived separately
	AlertActionLevel_ALERT_ACTION_LEVEL_AUTO_ACTION AlertActionLevel = 4
)

// Enum value maps for AlertActionLevel.
var (
	AlertActionLevel_name = map[int32]string{
		0: "ALERT_ACTION_LEVEL_UNSPECIFIED",
		1: "ALERT_ACTION_LEVEL_NONE",
		2: "ALERT_ACTION_LEVEL_MANUAL_REVIEW",
		3: "ALERT_ACTION_LEVEL_AUTO_BLOCK",
		4: "ALERT_ACTION_LEVEL_AUTO_ACTION",
	}
	AlertActionLevel_value = map[string]int32{
		"ALERT_ACTION_LEVEL_UNSPECIFIED":   0,
		"ALERT_ACTION_LEVEL_NONE":          1,
		"ALERT_ACTION_LEVEL_MANUAL_REVIEW": 2,
		"ALERT_ACTION_LEVEL_AUTO_BLOCK":    3,
		"ALERT_ACTION_LEVEL_AUTO_ACTION":   4,
	}
)

func (x AlertActionLevel) Enum() *AlertActionLevel {
	p := new(AlertActionLevel)
	*p = x
	return p
}

func (x AlertActionLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AlertActionLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_enums_enums_proto_enumTypes[5].Descriptor()
}

func (AlertActionLevel) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_enums_enums_proto_enumTypes[5]
}

func (x AlertActionLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AlertActionLevel.Descriptor instead.
func (AlertActionLevel) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_enums_enums_proto_rawDescGZIP(), []int{5}
}

// enum to indicate what review type should be taken against a case
// case creation could be rejected, passed on certain params depending on this enum
type CaseReviewLevel int32

const (
	CaseReviewLevel_CASE_ACTION_LEVEL_UNSPECIFIED CaseReviewLevel = 0
	// should create a case with won't review status
	CaseReviewLevel_CASE_ACTION_LEVEL_CREATE_WONT_REVIEW_CASE CaseReviewLevel = 1
	// should ignore case
	CaseReviewLevel_CASE_ACTION_LEVEL_IGNORE_CASE CaseReviewLevel = 2
)

// Enum value maps for CaseReviewLevel.
var (
	CaseReviewLevel_name = map[int32]string{
		0: "CASE_ACTION_LEVEL_UNSPECIFIED",
		1: "CASE_ACTION_LEVEL_CREATE_WONT_REVIEW_CASE",
		2: "CASE_ACTION_LEVEL_IGNORE_CASE",
	}
	CaseReviewLevel_value = map[string]int32{
		"CASE_ACTION_LEVEL_UNSPECIFIED":             0,
		"CASE_ACTION_LEVEL_CREATE_WONT_REVIEW_CASE": 1,
		"CASE_ACTION_LEVEL_IGNORE_CASE":             2,
	}
)

func (x CaseReviewLevel) Enum() *CaseReviewLevel {
	p := new(CaseReviewLevel)
	*p = x
	return p
}

func (x CaseReviewLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CaseReviewLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_enums_enums_proto_enumTypes[6].Descriptor()
}

func (CaseReviewLevel) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_enums_enums_proto_enumTypes[6]
}

func (x CaseReviewLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CaseReviewLevel.Descriptor instead.
func (CaseReviewLevel) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_enums_enums_proto_rawDescGZIP(), []int{6}
}

// common enum to be used in cases where data freshness needs to be decided by clients for certain data
type DataFreshness int32

const (
	DataFreshness_DATA_FRESHNESS_UNSPECIFIED DataFreshness = 0
	// makes a fresh vendor call
	DataFreshness_DATA_FRESHNESS_REAL_TIME DataFreshness = 1
	// data is fetched from db if it not older than x mins, otherwise makes a vendor call
	// worst case staleness could be upto 10 mins
	DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME DataFreshness = 3
	// data is fetched from db if it is not older than x days, otherwise makes a vendor call
	// worst case staleness could be upto a week
	DataFreshness_DATA_FRESHNESS_RECENT DataFreshness = 2
	// data will be fetched from db only, return record not exist if data unavailable
	DataFreshness_DATA_FRESHNESS_LAST_KNOWN DataFreshness = 4
)

// Enum value maps for DataFreshness.
var (
	DataFreshness_name = map[int32]string{
		0: "DATA_FRESHNESS_UNSPECIFIED",
		1: "DATA_FRESHNESS_REAL_TIME",
		3: "DATA_FRESHNESS_NEAR_REAL_TIME",
		2: "DATA_FRESHNESS_RECENT",
		4: "DATA_FRESHNESS_LAST_KNOWN",
	}
	DataFreshness_value = map[string]int32{
		"DATA_FRESHNESS_UNSPECIFIED":    0,
		"DATA_FRESHNESS_REAL_TIME":      1,
		"DATA_FRESHNESS_NEAR_REAL_TIME": 3,
		"DATA_FRESHNESS_RECENT":         2,
		"DATA_FRESHNESS_LAST_KNOWN":     4,
	}
)

func (x DataFreshness) Enum() *DataFreshness {
	p := new(DataFreshness)
	*p = x
	return p
}

func (x DataFreshness) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataFreshness) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_enums_enums_proto_enumTypes[7].Descriptor()
}

func (DataFreshness) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_enums_enums_proto_enumTypes[7]
}

func (x DataFreshness) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataFreshness.Descriptor instead.
func (DataFreshness) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_enums_enums_proto_rawDescGZIP(), []int{7}
}

// AlertHandlingReason specifies how alert can be handled/processed after creation.
type AlertHandlingType int32

const (
	// No action is needed for the alert
	AlertHandlingType_ALERT_HANDLING_TYPE_UNSPECIFIED AlertHandlingType = 0
	// No handling is required for alert and alert processing will terminate after alert creation.
	AlertHandlingType_ALERT_HANDLING_TYPE_NONE AlertHandlingType = 1
	// A case needs to be created and manual review is required
	AlertHandlingType_ALERT_HANDLING_TYPE_SEND_FOR_REVIEW AlertHandlingType = 2
	// A case needs to be created and an auto-action needs to be performed on the case
	// action to be peformed can depend on multiple factors like rule, score stc and will be derived separately
	AlertHandlingType_ALERT_HANDLING_TYPE_AUTO_ACTION AlertHandlingType = 3
	// Alert needs be appended to existing case since manual review is not required.
	// Review can be skipped for reasons such as account frozen or freeze action is in progress.
	AlertHandlingType_ALERT_HANDLING_TYPE_SKIP_REVIEW AlertHandlingType = 4
)

// Enum value maps for AlertHandlingType.
var (
	AlertHandlingType_name = map[int32]string{
		0: "ALERT_HANDLING_TYPE_UNSPECIFIED",
		1: "ALERT_HANDLING_TYPE_NONE",
		2: "ALERT_HANDLING_TYPE_SEND_FOR_REVIEW",
		3: "ALERT_HANDLING_TYPE_AUTO_ACTION",
		4: "ALERT_HANDLING_TYPE_SKIP_REVIEW",
	}
	AlertHandlingType_value = map[string]int32{
		"ALERT_HANDLING_TYPE_UNSPECIFIED":     0,
		"ALERT_HANDLING_TYPE_NONE":            1,
		"ALERT_HANDLING_TYPE_SEND_FOR_REVIEW": 2,
		"ALERT_HANDLING_TYPE_AUTO_ACTION":     3,
		"ALERT_HANDLING_TYPE_SKIP_REVIEW":     4,
	}
)

func (x AlertHandlingType) Enum() *AlertHandlingType {
	p := new(AlertHandlingType)
	*p = x
	return p
}

func (x AlertHandlingType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AlertHandlingType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_enums_enums_proto_enumTypes[8].Descriptor()
}

func (AlertHandlingType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_enums_enums_proto_enumTypes[8]
}

func (x AlertHandlingType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AlertHandlingType.Descriptor instead.
func (AlertHandlingType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_enums_enums_proto_rawDescGZIP(), []int{8}
}

// AlertHandlingReason specifies reasons for alert handling type.
type AlertHandlingReason int32

const (
	AlertHandlingReason_ALERT_HANDLING_REASON_UNSPECIFIED AlertHandlingReason = 0
	// Rule is marked inactive.
	// If a rule is inactive, no alerts will be generated for it.
	//
	// Deprecated: Marked as deprecated in api/risk/case_management/enums/enums.proto.
	AlertHandlingReason_ALERT_HANDLING_REASON_RULE_INACTIVE AlertHandlingReason = 1
	// If rule confidence is below a predefined threshold.
	AlertHandlingReason_ALERT_HANDLING_REASON_LOW_CONFIDENCE_RULE AlertHandlingReason = 2
	// If alert needs to be sent for manual review
	AlertHandlingReason_ALERT_HANDLING_REASON_ELIGIBLE_FOR_REVIEW AlertHandlingReason = 3
	// A rule can be in cool off for a duration after initial trigger.
	AlertHandlingReason_ALERT_HANDLING_REASON_RULE_IN_COOL_OFF AlertHandlingReason = 4
	// If user account is already frozen
	AlertHandlingReason_ALERT_HANDLING_REASON_ACCOUNT_FROZEN AlertHandlingReason = 5
	// If a freeze action is taken and account freeze is pending.
	AlertHandlingReason_ALERT_HANDLING_REASON_FREEZE_ACTION_IN_PROGRESS AlertHandlingReason = 6
	// If auto action is enabled for the rule.
	AlertHandlingReason_ALERT_HANDLING_REASON_AUTO_ACTION_ENABLED AlertHandlingReason = 7
	// if user present in whitelist
	AlertHandlingReason_ALERT_HANDLING_REASON_USER_IS_IN_WHITELIST AlertHandlingReason = 8
	// if rule state marked as shadow
	AlertHandlingReason_ALERT_HANDLING_REASON_RULE_IN_SHADOW AlertHandlingReason = 9
	// if actor id is external
	AlertHandlingReason_ALERT_HANDLING_REASON_EXTERNAL_ACTOR AlertHandlingReason = 10
)

// Enum value maps for AlertHandlingReason.
var (
	AlertHandlingReason_name = map[int32]string{
		0:  "ALERT_HANDLING_REASON_UNSPECIFIED",
		1:  "ALERT_HANDLING_REASON_RULE_INACTIVE",
		2:  "ALERT_HANDLING_REASON_LOW_CONFIDENCE_RULE",
		3:  "ALERT_HANDLING_REASON_ELIGIBLE_FOR_REVIEW",
		4:  "ALERT_HANDLING_REASON_RULE_IN_COOL_OFF",
		5:  "ALERT_HANDLING_REASON_ACCOUNT_FROZEN",
		6:  "ALERT_HANDLING_REASON_FREEZE_ACTION_IN_PROGRESS",
		7:  "ALERT_HANDLING_REASON_AUTO_ACTION_ENABLED",
		8:  "ALERT_HANDLING_REASON_USER_IS_IN_WHITELIST",
		9:  "ALERT_HANDLING_REASON_RULE_IN_SHADOW",
		10: "ALERT_HANDLING_REASON_EXTERNAL_ACTOR",
	}
	AlertHandlingReason_value = map[string]int32{
		"ALERT_HANDLING_REASON_UNSPECIFIED":               0,
		"ALERT_HANDLING_REASON_RULE_INACTIVE":             1,
		"ALERT_HANDLING_REASON_LOW_CONFIDENCE_RULE":       2,
		"ALERT_HANDLING_REASON_ELIGIBLE_FOR_REVIEW":       3,
		"ALERT_HANDLING_REASON_RULE_IN_COOL_OFF":          4,
		"ALERT_HANDLING_REASON_ACCOUNT_FROZEN":            5,
		"ALERT_HANDLING_REASON_FREEZE_ACTION_IN_PROGRESS": 6,
		"ALERT_HANDLING_REASON_AUTO_ACTION_ENABLED":       7,
		"ALERT_HANDLING_REASON_USER_IS_IN_WHITELIST":      8,
		"ALERT_HANDLING_REASON_RULE_IN_SHADOW":            9,
		"ALERT_HANDLING_REASON_EXTERNAL_ACTOR":            10,
	}
)

func (x AlertHandlingReason) Enum() *AlertHandlingReason {
	p := new(AlertHandlingReason)
	*p = x
	return p
}

func (x AlertHandlingReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AlertHandlingReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_enums_enums_proto_enumTypes[9].Descriptor()
}

func (AlertHandlingReason) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_enums_enums_proto_enumTypes[9]
}

func (x AlertHandlingReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AlertHandlingReason.Descriptor instead.
func (AlertHandlingReason) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_enums_enums_proto_rawDescGZIP(), []int{9}
}

var File_api_risk_case_management_enums_enums_proto protoreflect.FileDescriptor

var file_api_risk_case_management_enums_enums_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a, 0x46, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x64,
	0x69, 0x63, 0x74, 0x12, 0x17, 0x0a, 0x13, 0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c,
	0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x10, 0x01, 0x12, 0x10,
	0x0a, 0x0c, 0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x02,
	0x2a, 0xab, 0x01, 0x0a, 0x10, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x4e, 0x46, 0x4f, 0x52, 0x4d, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x4e, 0x46, 0x4f,
	0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x43, 0x41,
	0x53, 0x45, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59,
	0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10,
	0x03, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x04, 0x2a, 0xe4,
	0x01, 0x0a, 0x0a, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a,
	0x17, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x4e,
	0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x01,
	0x12, 0x1b, 0x0a, 0x17, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x18, 0x0a,
	0x14, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x4e, 0x54, 0x49, 0x54,
	0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x46, 0x55, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14,
	0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x45, 0x52, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x53, 0x43, 0x41, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x06, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4c,
	0x4f, 0x43, 0x4b, 0x10, 0x07, 0x2a, 0x49, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x01,
	0x2a, 0xeb, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x50,
	0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x44, 0x52, 0x4f, 0x4e, 0x41, 0x50,
	0x41, 0x59, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x44, 0x53, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c,
	0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x53, 0x4e, 0x4f, 0x57, 0x46, 0x4c, 0x41, 0x4b, 0x45, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x53,
	0x10, 0x04, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x4c, 0x45, 0x41, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e,
	0x41, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x45, 0x52, 0x10, 0x06, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x45, 0x53, 0x43, 0x41, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x07, 0x12,
	0x1d, 0x0a, 0x19, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x54, 0x49, 0x43, 0x53, 0x10, 0x08, 0x2a, 0xc4,
	0x01, 0x0a, 0x10, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x4c, 0x45, 0x52, 0x54,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x4e, 0x4f,
	0x4e, 0x45, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41,
	0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x1d, 0x41, 0x4c,
	0x45, 0x52, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c,
	0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x03, 0x1a, 0x02, 0x08,
	0x01, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x04, 0x2a, 0x86, 0x01, 0x0a, 0x0f, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x53,
	0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2d, 0x0a, 0x29,
	0x43, 0x41, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45,
	0x4c, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x57, 0x4f, 0x4e, 0x54, 0x5f, 0x52, 0x45,
	0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x43,
	0x41, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c,
	0x5f, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x02, 0x2a, 0xaa,
	0x01, 0x0a, 0x0d, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x65, 0x73, 0x68, 0x6e, 0x65, 0x73, 0x73,
	0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x4e, 0x45,
	0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1c, 0x0a, 0x18, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x4e, 0x45,
	0x53, 0x53, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x21,
	0x0a, 0x1d, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x4e, 0x45, 0x41, 0x52, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10,
	0x03, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4c,
	0x41, 0x53, 0x54, 0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x04, 0x2a, 0xc9, 0x01, 0x0a, 0x11,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c,
	0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f,
	0x48, 0x41, 0x4e, 0x44, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f,
	0x4e, 0x45, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x48, 0x41,
	0x4e, 0x44, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4e, 0x44,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x02, 0x12, 0x23, 0x0a,
	0x1f, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x49, 0x4e, 0x47, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x4e, 0x44,
	0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x4b, 0x49, 0x50, 0x5f, 0x52,
	0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x04, 0x2a, 0x85, 0x04, 0x0a, 0x13, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x25, 0x0a, 0x21, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x49, 0x4e,
	0x47, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2b, 0x0a, 0x23, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f,
	0x48, 0x41, 0x4e, 0x44, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x52, 0x55, 0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x1a,
	0x02, 0x08, 0x01, 0x12, 0x2d, 0x0a, 0x29, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x4e,
	0x44, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x57,
	0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x55, 0x4c, 0x45,
	0x10, 0x02, 0x12, 0x2d, 0x0a, 0x29, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x4e, 0x44,
	0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x45, 0x4c, 0x49, 0x47,
	0x49, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10,
	0x03, 0x12, 0x2a, 0x0a, 0x26, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c,
	0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x5f,
	0x49, 0x4e, 0x5f, 0x43, 0x4f, 0x4f, 0x4c, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x04, 0x12, 0x28, 0x0a,
	0x24, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x49, 0x4e, 0x47, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46,
	0x52, 0x4f, 0x5a, 0x45, 0x4e, 0x10, 0x05, 0x12, 0x33, 0x0a, 0x2f, 0x41, 0x4c, 0x45, 0x52, 0x54,
	0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49,
	0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x06, 0x12, 0x2d, 0x0a, 0x29,
	0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x07, 0x12, 0x2e, 0x0a, 0x2a, 0x41,
	0x4c, 0x45, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45,
	0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x53, 0x5f, 0x49, 0x4e, 0x5f,
	0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x08, 0x12, 0x28, 0x0a, 0x24, 0x41,
	0x4c, 0x45, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45,
	0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x53, 0x48, 0x41,
	0x44, 0x4f, 0x57, 0x10, 0x09, 0x12, 0x28, 0x0a, 0x24, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x48,
	0x41, 0x4e, 0x44, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x45,
	0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x0a, 0x42,
	0x6e, 0x0a, 0x35, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_enums_enums_proto_rawDescOnce sync.Once
	file_api_risk_case_management_enums_enums_proto_rawDescData = file_api_risk_case_management_enums_enums_proto_rawDesc
)

func file_api_risk_case_management_enums_enums_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_enums_enums_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_enums_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_enums_enums_proto_rawDescData)
	})
	return file_api_risk_case_management_enums_enums_proto_rawDescData
}

var file_api_risk_case_management_enums_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 10)
var file_api_risk_case_management_enums_enums_proto_goTypes = []interface{}{
	(Verdict)(0),             // 0: risk.case_management.enums.Verdict
	(InformationLevel)(0),    // 1: risk.case_management.enums.InformationLevel
	(EntityType)(0),          // 2: risk.case_management.enums.EntityType
	(AccountType)(0),         // 3: risk.case_management.enums.AccountType
	(Provenance)(0),          // 4: risk.case_management.enums.Provenance
	(AlertActionLevel)(0),    // 5: risk.case_management.enums.AlertActionLevel
	(CaseReviewLevel)(0),     // 6: risk.case_management.enums.CaseReviewLevel
	(DataFreshness)(0),       // 7: risk.case_management.enums.DataFreshness
	(AlertHandlingType)(0),   // 8: risk.case_management.enums.AlertHandlingType
	(AlertHandlingReason)(0), // 9: risk.case_management.enums.AlertHandlingReason
}
var file_api_risk_case_management_enums_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_enums_enums_proto_init() }
func file_api_risk_case_management_enums_enums_proto_init() {
	if File_api_risk_case_management_enums_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_enums_enums_proto_rawDesc,
			NumEnums:      10,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_case_management_enums_enums_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_enums_enums_proto_depIdxs,
		EnumInfos:         file_api_risk_case_management_enums_enums_proto_enumTypes,
	}.Build()
	File_api_risk_case_management_enums_enums_proto = out.File
	file_api_risk_case_management_enums_enums_proto_rawDesc = nil
	file_api_risk_case_management_enums_enums_proto_goTypes = nil
	file_api_risk_case_management_enums_enums_proto_depIdxs = nil
}
