package enums

import (
	"github.com/samber/lo"

	vgRiskPb "github.com/epifi/gamma/api/vendorgateway/crm/risk"
)

var (
	VerdictMap = map[Verdict]vgRiskPb.Verdict{
		Verdict_VERDICT_UNSPECIFIED: vgRiskPb.Verdict_VERDICT_UNSPECIFIED,
		Verdict_VERDICT_PASS:        vgRiskPb.Verdict_VERDICT_PASS,
		Verdict_VERDICT_FAIL:        vgRiskPb.Verdict_VERDICT_FAIL,
	}
	VerdictReverseMap = lo.Invert(VerdictMap)
)

func (v Verdict) ToCRMProto() vgRiskPb.Verdict {
	return VerdictMap[v]
}

func (v Verdict) IsTerminal() bool {
	return v == Verdict_VERDICT_PASS || v == Verdict_VERDICT_FAIL
}
