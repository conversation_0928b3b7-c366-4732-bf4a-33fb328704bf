// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/risk/case_management/enums/enums.pb.go

package enums

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the AlertHandlingType in string format in DB
func (p AlertHandlingType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing AlertHandlingType while reading from DB
func (p *AlertHandlingType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := AlertHandlingType_value[val]
	if !ok {
		return fmt.Errorf("unexpected AlertHandlingType value: %s", val)
	}
	*p = AlertHandlingType(valInt)
	return nil
}

// Marshaler interface implementation for AlertHandlingType
func (x AlertHandlingType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for AlertHandlingType
func (x *AlertHandlingType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = AlertHandlingType(AlertHandlingType_value[val])
	return nil
}

// Valuer interface implementation for storing the AlertHandlingReason in string format in DB
func (p AlertHandlingReason) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing AlertHandlingReason while reading from DB
func (p *AlertHandlingReason) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := AlertHandlingReason_value[val]
	if !ok {
		return fmt.Errorf("unexpected AlertHandlingReason value: %s", val)
	}
	*p = AlertHandlingReason(valInt)
	return nil
}

// Marshaler interface implementation for AlertHandlingReason
func (x AlertHandlingReason) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for AlertHandlingReason
func (x *AlertHandlingReason) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = AlertHandlingReason(AlertHandlingReason_value[val])
	return nil
}
