// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/risk/profile
package profile

import (
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessAccountOperationStatusUpdateEventMethod = "ProcessAccountOperationStatusUpdateEvent"
)

// RegisterProcessAccountOperationStatusUpdateEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessAccountOperationStatusUpdateEventMethodToSubscriber(subscriber queue.Subscriber, srv RiskProfileConsumerServer) {
	subscriber.RegisterService(&RiskProfileConsumer_ServiceDesc, srv, ProcessAccountOperationStatusUpdateEventMethod)
}
