// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/profile/service.proto

package profile

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	accounts "github.com/epifi/gamma/api/accounts"

	enums "github.com/epifi/gamma/api/risk/enums"

	form "github.com/epifi/gamma/api/risk/case_management/form"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = accounts.Type(0)

	_ = enums.AccountFreezeStatus(0)

	_ = form.Status(0)
)

// Validate checks the field values on GetUserProfileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserProfileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserProfileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserProfileRequestMultiError, or nil if none found.
func (m *GetUserProfileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserProfileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetUserProfileRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetUserProfileRequestMultiError(errors)
	}

	return nil
}

// GetUserProfileRequestMultiError is an error wrapping multiple validation
// errors returned by GetUserProfileRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUserProfileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserProfileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserProfileRequestMultiError) AllErrors() []error { return m }

// GetUserProfileRequestValidationError is the validation error returned by
// GetUserProfileRequest.Validate if the designated constraints aren't met.
type GetUserProfileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserProfileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserProfileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserProfileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserProfileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserProfileRequestValidationError) ErrorName() string {
	return "GetUserProfileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserProfileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserProfileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserProfileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserProfileRequestValidationError{}

// Validate checks the field values on GetUserProfileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserProfileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserProfileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserProfileResponseMultiError, or nil if none found.
func (m *GetUserProfileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserProfileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserProfileResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserProfileResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserProfileResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountsInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUserProfileResponseValidationError{
						field:  fmt.Sprintf("AccountsInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUserProfileResponseValidationError{
						field:  fmt.Sprintf("AccountsInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserProfileResponseValidationError{
					field:  fmt.Sprintf("AccountsInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetUserProfileResponseMultiError(errors)
	}

	return nil
}

// GetUserProfileResponseMultiError is an error wrapping multiple validation
// errors returned by GetUserProfileResponse.ValidateAll() if the designated
// constraints aren't met.
type GetUserProfileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserProfileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserProfileResponseMultiError) AllErrors() []error { return m }

// GetUserProfileResponseValidationError is the validation error returned by
// GetUserProfileResponse.Validate if the designated constraints aren't met.
type GetUserProfileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserProfileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserProfileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserProfileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserProfileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserProfileResponseValidationError) ErrorName() string {
	return "GetUserProfileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserProfileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserProfileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserProfileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserProfileResponseValidationError{}

// Validate checks the field values on AccountInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AccountInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AccountInfoMultiError, or
// nil if none found.
func (m *AccountInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	// no validation rules for PresentFreezeStatus

	// no validation rules for ImpendingFreezeStatus

	// no validation rules for IsUnderReview

	if all {
		switch v := interface{}(m.GetLastFlaggedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountInfoValidationError{
					field:  "LastFlaggedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountInfoValidationError{
					field:  "LastFlaggedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastFlaggedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountInfoValidationError{
				field:  "LastFlaggedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsAccountClosed

	if len(errors) > 0 {
		return AccountInfoMultiError(errors)
	}

	return nil
}

// AccountInfoMultiError is an error wrapping multiple validation errors
// returned by AccountInfo.ValidateAll() if the designated constraints aren't met.
type AccountInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountInfoMultiError) AllErrors() []error { return m }

// AccountInfoValidationError is the validation error returned by
// AccountInfo.Validate if the designated constraints aren't met.
type AccountInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountInfoValidationError) ErrorName() string { return "AccountInfoValidationError" }

// Error satisfies the builtin error interface
func (e AccountInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountInfoValidationError{}

// Validate checks the field values on GetDetailedUserProfileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDetailedUserProfileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDetailedUserProfileRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetDetailedUserProfileRequestMultiError, or nil if none found.
func (m *GetDetailedUserProfileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDetailedUserProfileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetDetailedUserProfileRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for MaxLimit

	if len(errors) > 0 {
		return GetDetailedUserProfileRequestMultiError(errors)
	}

	return nil
}

// GetDetailedUserProfileRequestMultiError is an error wrapping multiple
// validation errors returned by GetDetailedUserProfileRequest.ValidateAll()
// if the designated constraints aren't met.
type GetDetailedUserProfileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDetailedUserProfileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDetailedUserProfileRequestMultiError) AllErrors() []error { return m }

// GetDetailedUserProfileRequestValidationError is the validation error
// returned by GetDetailedUserProfileRequest.Validate if the designated
// constraints aren't met.
type GetDetailedUserProfileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDetailedUserProfileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDetailedUserProfileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDetailedUserProfileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDetailedUserProfileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDetailedUserProfileRequestValidationError) ErrorName() string {
	return "GetDetailedUserProfileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDetailedUserProfileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDetailedUserProfileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDetailedUserProfileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDetailedUserProfileRequestValidationError{}

// Validate checks the field values on GetDetailedUserProfileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDetailedUserProfileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDetailedUserProfileResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetDetailedUserProfileResponseMultiError, or nil if none found.
func (m *GetDetailedUserProfileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDetailedUserProfileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDetailedUserProfileResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDetailedUserProfileResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDetailedUserProfileResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetAccountsInfo()))
		i := 0
		for key := range m.GetAccountsInfo() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetAccountsInfo()[key]
			_ = val

			// no validation rules for AccountsInfo[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetDetailedUserProfileResponseValidationError{
							field:  fmt.Sprintf("AccountsInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetDetailedUserProfileResponseValidationError{
							field:  fmt.Sprintf("AccountsInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetDetailedUserProfileResponseValidationError{
						field:  fmt.Sprintf("AccountsInfo[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetDetailedUserProfileResponseMultiError(errors)
	}

	return nil
}

// GetDetailedUserProfileResponseMultiError is an error wrapping multiple
// validation errors returned by GetDetailedUserProfileResponse.ValidateAll()
// if the designated constraints aren't met.
type GetDetailedUserProfileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDetailedUserProfileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDetailedUserProfileResponseMultiError) AllErrors() []error { return m }

// GetDetailedUserProfileResponseValidationError is the validation error
// returned by GetDetailedUserProfileResponse.Validate if the designated
// constraints aren't met.
type GetDetailedUserProfileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDetailedUserProfileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDetailedUserProfileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDetailedUserProfileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDetailedUserProfileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDetailedUserProfileResponseValidationError) ErrorName() string {
	return "GetDetailedUserProfileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDetailedUserProfileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDetailedUserProfileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDetailedUserProfileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDetailedUserProfileResponseValidationError{}

// Validate checks the field values on AccountStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AccountStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AccountStatusMultiError, or
// nil if none found.
func (m *AccountStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PresentStatus

	// no validation rules for IsAccountClosed

	if len(errors) > 0 {
		return AccountStatusMultiError(errors)
	}

	return nil
}

// AccountStatusMultiError is an error wrapping multiple validation errors
// returned by AccountStatus.ValidateAll() if the designated constraints
// aren't met.
type AccountStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountStatusMultiError) AllErrors() []error { return m }

// AccountStatusValidationError is the validation error returned by
// AccountStatus.Validate if the designated constraints aren't met.
type AccountStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountStatusValidationError) ErrorName() string { return "AccountStatusValidationError" }

// Error satisfies the builtin error interface
func (e AccountStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountStatusValidationError{}

// Validate checks the field values on AccountAction with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AccountAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountAction with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AccountActionMultiError, or
// nil if none found.
func (m *AccountAction) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Reason

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetBankActionDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountActionValidationError{
					field:  "BankActionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountActionValidationError{
					field:  "BankActionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankActionDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountActionValidationError{
				field:  "BankActionDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	// no validation rules for IsCompleted

	if len(errors) > 0 {
		return AccountActionMultiError(errors)
	}

	return nil
}

// AccountActionMultiError is an error wrapping multiple validation errors
// returned by AccountAction.ValidateAll() if the designated constraints
// aren't met.
type AccountActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountActionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountActionMultiError) AllErrors() []error { return m }

// AccountActionValidationError is the validation error returned by
// AccountAction.Validate if the designated constraints aren't met.
type AccountActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountActionValidationError) ErrorName() string { return "AccountActionValidationError" }

// Error satisfies the builtin error interface
func (e AccountActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountActionValidationError{}

// Validate checks the field values on LEAComplaintInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LEAComplaintInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LEAComplaintInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LEAComplaintInfoMultiError, or nil if none found.
func (m *LEAComplaintInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *LEAComplaintInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LeaFreezeStatus

	if all {
		switch v := interface{}(m.GetLeaActionDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LEAComplaintInfoValidationError{
					field:  "LeaActionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LEAComplaintInfoValidationError{
					field:  "LeaActionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeaActionDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LEAComplaintInfoValidationError{
				field:  "LeaActionDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SourceDetails

	if len(errors) > 0 {
		return LEAComplaintInfoMultiError(errors)
	}

	return nil
}

// LEAComplaintInfoMultiError is an error wrapping multiple validation errors
// returned by LEAComplaintInfo.ValidateAll() if the designated constraints
// aren't met.
type LEAComplaintInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LEAComplaintInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LEAComplaintInfoMultiError) AllErrors() []error { return m }

// LEAComplaintInfoValidationError is the validation error returned by
// LEAComplaintInfo.Validate if the designated constraints aren't met.
type LEAComplaintInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LEAComplaintInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LEAComplaintInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LEAComplaintInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LEAComplaintInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LEAComplaintInfoValidationError) ErrorName() string { return "LEAComplaintInfoValidationError" }

// Error satisfies the builtin error interface
func (e LEAComplaintInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLEAComplaintInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LEAComplaintInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LEAComplaintInfoValidationError{}

// Validate checks the field values on FormInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FormInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FormInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FormInfoMultiError, or nil
// if none found.
func (m *FormInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FormInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for FormLink

	// no validation rules for CaseId

	// no validation rules for Origin

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormInfoValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpireAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormInfoValidationError{
					field:  "ExpireAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormInfoValidationError{
					field:  "ExpireAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpireAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormInfoValidationError{
				field:  "ExpireAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FormInfoMultiError(errors)
	}

	return nil
}

// FormInfoMultiError is an error wrapping multiple validation errors returned
// by FormInfo.ValidateAll() if the designated constraints aren't met.
type FormInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FormInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FormInfoMultiError) AllErrors() []error { return m }

// FormInfoValidationError is the validation error returned by
// FormInfo.Validate if the designated constraints aren't met.
type FormInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FormInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FormInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FormInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FormInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FormInfoValidationError) ErrorName() string { return "FormInfoValidationError" }

// Error satisfies the builtin error interface
func (e FormInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFormInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FormInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FormInfoValidationError{}

// Validate checks the field values on DetailedAccountInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetailedAccountInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetailedAccountInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DetailedAccountInfoMultiError, or nil if none found.
func (m *DetailedAccountInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DetailedAccountInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	// no validation rules for AccountType

	if all {
		switch v := interface{}(m.GetAccountStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetailedAccountInfoValidationError{
					field:  "AccountStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetailedAccountInfoValidationError{
					field:  "AccountStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetailedAccountInfoValidationError{
				field:  "AccountStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPastActions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DetailedAccountInfoValidationError{
						field:  fmt.Sprintf("PastActions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DetailedAccountInfoValidationError{
						field:  fmt.Sprintf("PastActions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DetailedAccountInfoValidationError{
					field:  fmt.Sprintf("PastActions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetLeaStatus() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DetailedAccountInfoValidationError{
						field:  fmt.Sprintf("LeaStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DetailedAccountInfoValidationError{
						field:  fmt.Sprintf("LeaStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DetailedAccountInfoValidationError{
					field:  fmt.Sprintf("LeaStatus[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsUnderReview

	for idx, item := range m.GetForms() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DetailedAccountInfoValidationError{
						field:  fmt.Sprintf("Forms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DetailedAccountInfoValidationError{
						field:  fmt.Sprintf("Forms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DetailedAccountInfoValidationError{
					field:  fmt.Sprintf("Forms[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AccountOperationalStatus

	for idx, item := range m.GetUnifiedLeaComplaints() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DetailedAccountInfoValidationError{
						field:  fmt.Sprintf("UnifiedLeaComplaints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DetailedAccountInfoValidationError{
						field:  fmt.Sprintf("UnifiedLeaComplaints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DetailedAccountInfoValidationError{
					field:  fmt.Sprintf("UnifiedLeaComplaints[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProcessedFreezeReason

	if len(errors) > 0 {
		return DetailedAccountInfoMultiError(errors)
	}

	return nil
}

// DetailedAccountInfoMultiError is an error wrapping multiple validation
// errors returned by DetailedAccountInfo.ValidateAll() if the designated
// constraints aren't met.
type DetailedAccountInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetailedAccountInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetailedAccountInfoMultiError) AllErrors() []error { return m }

// DetailedAccountInfoValidationError is the validation error returned by
// DetailedAccountInfo.Validate if the designated constraints aren't met.
type DetailedAccountInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetailedAccountInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetailedAccountInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetailedAccountInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetailedAccountInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetailedAccountInfoValidationError) ErrorName() string {
	return "DetailedAccountInfoValidationError"
}

// Error satisfies the builtin error interface
func (e DetailedAccountInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetailedAccountInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetailedAccountInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetailedAccountInfoValidationError{}

// Validate checks the field values on GetRiskyConnectionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRiskyConnectionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRiskyConnectionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRiskyConnectionsRequestMultiError, or nil if none found.
func (m *GetRiskyConnectionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRiskyConnectionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetRiskyConnectionsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetConnectionsFieldMask()) < 1 {
		err := GetRiskyConnectionsRequestValidationError{
			field:  "ConnectionsFieldMask",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetRiskyConnectionsRequestMultiError(errors)
	}

	return nil
}

// GetRiskyConnectionsRequestMultiError is an error wrapping multiple
// validation errors returned by GetRiskyConnectionsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetRiskyConnectionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRiskyConnectionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRiskyConnectionsRequestMultiError) AllErrors() []error { return m }

// GetRiskyConnectionsRequestValidationError is the validation error returned
// by GetRiskyConnectionsRequest.Validate if the designated constraints aren't met.
type GetRiskyConnectionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRiskyConnectionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRiskyConnectionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRiskyConnectionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRiskyConnectionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRiskyConnectionsRequestValidationError) ErrorName() string {
	return "GetRiskyConnectionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRiskyConnectionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRiskyConnectionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRiskyConnectionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRiskyConnectionsRequestValidationError{}

// Validate checks the field values on GetRiskyConnectionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRiskyConnectionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRiskyConnectionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRiskyConnectionsResponseMultiError, or nil if none found.
func (m *GetRiskyConnectionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRiskyConnectionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRiskyConnectionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRiskyConnectionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRiskyConnectionsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInwardContactAssociations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRiskyConnectionsResponseValidationError{
						field:  fmt.Sprintf("InwardContactAssociations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRiskyConnectionsResponseValidationError{
						field:  fmt.Sprintf("InwardContactAssociations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRiskyConnectionsResponseValidationError{
					field:  fmt.Sprintf("InwardContactAssociations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetOutwardContactAssociations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRiskyConnectionsResponseValidationError{
						field:  fmt.Sprintf("OutwardContactAssociations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRiskyConnectionsResponseValidationError{
						field:  fmt.Sprintf("OutwardContactAssociations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRiskyConnectionsResponseValidationError{
					field:  fmt.Sprintf("OutwardContactAssociations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetReferrerInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRiskyConnectionsResponseValidationError{
					field:  "ReferrerInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRiskyConnectionsResponseValidationError{
					field:  "ReferrerInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReferrerInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRiskyConnectionsResponseValidationError{
				field:  "ReferrerInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRefereesInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRiskyConnectionsResponseValidationError{
						field:  fmt.Sprintf("RefereesInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRiskyConnectionsResponseValidationError{
						field:  fmt.Sprintf("RefereesInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRiskyConnectionsResponseValidationError{
					field:  fmt.Sprintf("RefereesInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRiskyConnectionsResponseMultiError(errors)
	}

	return nil
}

// GetRiskyConnectionsResponseMultiError is an error wrapping multiple
// validation errors returned by GetRiskyConnectionsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetRiskyConnectionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRiskyConnectionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRiskyConnectionsResponseMultiError) AllErrors() []error { return m }

// GetRiskyConnectionsResponseValidationError is the validation error returned
// by GetRiskyConnectionsResponse.Validate if the designated constraints
// aren't met.
type GetRiskyConnectionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRiskyConnectionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRiskyConnectionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRiskyConnectionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRiskyConnectionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRiskyConnectionsResponseValidationError) ErrorName() string {
	return "GetRiskyConnectionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRiskyConnectionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRiskyConnectionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRiskyConnectionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRiskyConnectionsResponseValidationError{}

// Validate checks the field values on AccountActionDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AccountActionDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountActionDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountActionDetailsMultiError, or nil if none found.
func (m *AccountActionDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountActionDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsNegativeActionPresent

	// no validation rules for Action

	// no validation rules for Reason

	if len(errors) > 0 {
		return AccountActionDetailsMultiError(errors)
	}

	return nil
}

// AccountActionDetailsMultiError is an error wrapping multiple validation
// errors returned by AccountActionDetails.ValidateAll() if the designated
// constraints aren't met.
type AccountActionDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountActionDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountActionDetailsMultiError) AllErrors() []error { return m }

// AccountActionDetailsValidationError is the validation error returned by
// AccountActionDetails.Validate if the designated constraints aren't met.
type AccountActionDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountActionDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountActionDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountActionDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountActionDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountActionDetailsValidationError) ErrorName() string {
	return "AccountActionDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AccountActionDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountActionDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountActionDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountActionDetailsValidationError{}

// Validate checks the field values on LEADetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LEADetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LEADetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LEADetailsMultiError, or
// nil if none found.
func (m *LEADetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LEADetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsLeaPresent

	if all {
		switch v := interface{}(m.GetLeaReportedTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LEADetailsValidationError{
					field:  "LeaReportedTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LEADetailsValidationError{
					field:  "LeaReportedTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeaReportedTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LEADetailsValidationError{
				field:  "LeaReportedTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LEADetailsMultiError(errors)
	}

	return nil
}

// LEADetailsMultiError is an error wrapping multiple validation errors
// returned by LEADetails.ValidateAll() if the designated constraints aren't met.
type LEADetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LEADetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LEADetailsMultiError) AllErrors() []error { return m }

// LEADetailsValidationError is the validation error returned by
// LEADetails.Validate if the designated constraints aren't met.
type LEADetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LEADetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LEADetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LEADetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LEADetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LEADetailsValidationError) ErrorName() string { return "LEADetailsValidationError" }

// Error satisfies the builtin error interface
func (e LEADetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLEADetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LEADetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LEADetailsValidationError{}

// Validate checks the field values on
// GetRiskyConnectionsResponse_ContactAssociation with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetRiskyConnectionsResponse_ContactAssociation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRiskyConnectionsResponse_ContactAssociation with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetRiskyConnectionsResponse_ContactAssociationMultiError, or nil if none found.
func (m *GetRiskyConnectionsResponse_ContactAssociation) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRiskyConnectionsResponse_ContactAssociation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetAccountActionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRiskyConnectionsResponse_ContactAssociationValidationError{
					field:  "AccountActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRiskyConnectionsResponse_ContactAssociationValidationError{
					field:  "AccountActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountActionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRiskyConnectionsResponse_ContactAssociationValidationError{
				field:  "AccountActionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLeaDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRiskyConnectionsResponse_ContactAssociationValidationError{
					field:  "LeaDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRiskyConnectionsResponse_ContactAssociationValidationError{
					field:  "LeaDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeaDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRiskyConnectionsResponse_ContactAssociationValidationError{
				field:  "LeaDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRiskyConnectionsResponse_ContactAssociationMultiError(errors)
	}

	return nil
}

// GetRiskyConnectionsResponse_ContactAssociationMultiError is an error
// wrapping multiple validation errors returned by
// GetRiskyConnectionsResponse_ContactAssociation.ValidateAll() if the
// designated constraints aren't met.
type GetRiskyConnectionsResponse_ContactAssociationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRiskyConnectionsResponse_ContactAssociationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRiskyConnectionsResponse_ContactAssociationMultiError) AllErrors() []error { return m }

// GetRiskyConnectionsResponse_ContactAssociationValidationError is the
// validation error returned by
// GetRiskyConnectionsResponse_ContactAssociation.Validate if the designated
// constraints aren't met.
type GetRiskyConnectionsResponse_ContactAssociationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRiskyConnectionsResponse_ContactAssociationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRiskyConnectionsResponse_ContactAssociationValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRiskyConnectionsResponse_ContactAssociationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRiskyConnectionsResponse_ContactAssociationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRiskyConnectionsResponse_ContactAssociationValidationError) ErrorName() string {
	return "GetRiskyConnectionsResponse_ContactAssociationValidationError"
}

// Error satisfies the builtin error interface
func (e GetRiskyConnectionsResponse_ContactAssociationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRiskyConnectionsResponse_ContactAssociation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRiskyConnectionsResponse_ContactAssociationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRiskyConnectionsResponse_ContactAssociationValidationError{}

// Validate checks the field values on GetRiskyConnectionsResponse_ReferralInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRiskyConnectionsResponse_ReferralInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRiskyConnectionsResponse_ReferralInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetRiskyConnectionsResponse_ReferralInfoMultiError, or nil if none found.
func (m *GetRiskyConnectionsResponse_ReferralInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRiskyConnectionsResponse_ReferralInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetAccountActionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRiskyConnectionsResponse_ReferralInfoValidationError{
					field:  "AccountActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRiskyConnectionsResponse_ReferralInfoValidationError{
					field:  "AccountActionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountActionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRiskyConnectionsResponse_ReferralInfoValidationError{
				field:  "AccountActionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLeaDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRiskyConnectionsResponse_ReferralInfoValidationError{
					field:  "LeaDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRiskyConnectionsResponse_ReferralInfoValidationError{
					field:  "LeaDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeaDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRiskyConnectionsResponse_ReferralInfoValidationError{
				field:  "LeaDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRiskyConnectionsResponse_ReferralInfoMultiError(errors)
	}

	return nil
}

// GetRiskyConnectionsResponse_ReferralInfoMultiError is an error wrapping
// multiple validation errors returned by
// GetRiskyConnectionsResponse_ReferralInfo.ValidateAll() if the designated
// constraints aren't met.
type GetRiskyConnectionsResponse_ReferralInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRiskyConnectionsResponse_ReferralInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRiskyConnectionsResponse_ReferralInfoMultiError) AllErrors() []error { return m }

// GetRiskyConnectionsResponse_ReferralInfoValidationError is the validation
// error returned by GetRiskyConnectionsResponse_ReferralInfo.Validate if the
// designated constraints aren't met.
type GetRiskyConnectionsResponse_ReferralInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRiskyConnectionsResponse_ReferralInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRiskyConnectionsResponse_ReferralInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRiskyConnectionsResponse_ReferralInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRiskyConnectionsResponse_ReferralInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRiskyConnectionsResponse_ReferralInfoValidationError) ErrorName() string {
	return "GetRiskyConnectionsResponse_ReferralInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetRiskyConnectionsResponse_ReferralInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRiskyConnectionsResponse_ReferralInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRiskyConnectionsResponse_ReferralInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRiskyConnectionsResponse_ReferralInfoValidationError{}
