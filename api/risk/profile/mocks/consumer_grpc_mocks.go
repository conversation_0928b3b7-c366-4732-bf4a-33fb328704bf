// Code generated by MockGen. DO NOT EDIT.
// Source: api/risk/profile/consumer_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	operstatus "github.com/epifi/gamma/api/accounts/operstatus"
	profile "github.com/epifi/gamma/api/risk/profile"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockRiskProfileConsumerClient is a mock of RiskProfileConsumerClient interface.
type MockRiskProfileConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockRiskProfileConsumerClientMockRecorder
}

// MockRiskProfileConsumerClientMockRecorder is the mock recorder for MockRiskProfileConsumerClient.
type MockRiskProfileConsumerClientMockRecorder struct {
	mock *MockRiskProfileConsumerClient
}

// NewMockRiskProfileConsumerClient creates a new mock instance.
func NewMockRiskProfileConsumerClient(ctrl *gomock.Controller) *MockRiskProfileConsumerClient {
	mock := &MockRiskProfileConsumerClient{ctrl: ctrl}
	mock.recorder = &MockRiskProfileConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskProfileConsumerClient) EXPECT() *MockRiskProfileConsumerClientMockRecorder {
	return m.recorder
}

// ProcessAccountOperationStatusUpdateEvent mocks base method.
func (m *MockRiskProfileConsumerClient) ProcessAccountOperationStatusUpdateEvent(ctx context.Context, in *operstatus.OperationalStatusUpdateEvent, opts ...grpc.CallOption) (*profile.ProcessAccountOperationStatusUpdateEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessAccountOperationStatusUpdateEvent", varargs...)
	ret0, _ := ret[0].(*profile.ProcessAccountOperationStatusUpdateEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAccountOperationStatusUpdateEvent indicates an expected call of ProcessAccountOperationStatusUpdateEvent.
func (mr *MockRiskProfileConsumerClientMockRecorder) ProcessAccountOperationStatusUpdateEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAccountOperationStatusUpdateEvent", reflect.TypeOf((*MockRiskProfileConsumerClient)(nil).ProcessAccountOperationStatusUpdateEvent), varargs...)
}

// MockRiskProfileConsumerServer is a mock of RiskProfileConsumerServer interface.
type MockRiskProfileConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockRiskProfileConsumerServerMockRecorder
}

// MockRiskProfileConsumerServerMockRecorder is the mock recorder for MockRiskProfileConsumerServer.
type MockRiskProfileConsumerServerMockRecorder struct {
	mock *MockRiskProfileConsumerServer
}

// NewMockRiskProfileConsumerServer creates a new mock instance.
func NewMockRiskProfileConsumerServer(ctrl *gomock.Controller) *MockRiskProfileConsumerServer {
	mock := &MockRiskProfileConsumerServer{ctrl: ctrl}
	mock.recorder = &MockRiskProfileConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskProfileConsumerServer) EXPECT() *MockRiskProfileConsumerServerMockRecorder {
	return m.recorder
}

// ProcessAccountOperationStatusUpdateEvent mocks base method.
func (m *MockRiskProfileConsumerServer) ProcessAccountOperationStatusUpdateEvent(arg0 context.Context, arg1 *operstatus.OperationalStatusUpdateEvent) (*profile.ProcessAccountOperationStatusUpdateEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessAccountOperationStatusUpdateEvent", arg0, arg1)
	ret0, _ := ret[0].(*profile.ProcessAccountOperationStatusUpdateEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAccountOperationStatusUpdateEvent indicates an expected call of ProcessAccountOperationStatusUpdateEvent.
func (mr *MockRiskProfileConsumerServerMockRecorder) ProcessAccountOperationStatusUpdateEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAccountOperationStatusUpdateEvent", reflect.TypeOf((*MockRiskProfileConsumerServer)(nil).ProcessAccountOperationStatusUpdateEvent), arg0, arg1)
}

// MockUnsafeRiskProfileConsumerServer is a mock of UnsafeRiskProfileConsumerServer interface.
type MockUnsafeRiskProfileConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeRiskProfileConsumerServerMockRecorder
}

// MockUnsafeRiskProfileConsumerServerMockRecorder is the mock recorder for MockUnsafeRiskProfileConsumerServer.
type MockUnsafeRiskProfileConsumerServerMockRecorder struct {
	mock *MockUnsafeRiskProfileConsumerServer
}

// NewMockUnsafeRiskProfileConsumerServer creates a new mock instance.
func NewMockUnsafeRiskProfileConsumerServer(ctrl *gomock.Controller) *MockUnsafeRiskProfileConsumerServer {
	mock := &MockUnsafeRiskProfileConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeRiskProfileConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeRiskProfileConsumerServer) EXPECT() *MockUnsafeRiskProfileConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedRiskProfileConsumerServer mocks base method.
func (m *MockUnsafeRiskProfileConsumerServer) mustEmbedUnimplementedRiskProfileConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedRiskProfileConsumerServer")
}

// mustEmbedUnimplementedRiskProfileConsumerServer indicates an expected call of mustEmbedUnimplementedRiskProfileConsumerServer.
func (mr *MockUnsafeRiskProfileConsumerServerMockRecorder) mustEmbedUnimplementedRiskProfileConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedRiskProfileConsumerServer", reflect.TypeOf((*MockUnsafeRiskProfileConsumerServer)(nil).mustEmbedUnimplementedRiskProfileConsumerServer))
}
