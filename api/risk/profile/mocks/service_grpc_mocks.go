// Code generated by MockGen. DO NOT EDIT.
// Source: api/risk/profile/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	sherlock_banners "github.com/epifi/gamma/api/cx/sherlock_banners"
	profile "github.com/epifi/gamma/api/risk/profile"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockProfileClient is a mock of ProfileClient interface.
type MockProfileClient struct {
	ctrl     *gomock.Controller
	recorder *MockProfileClientMockRecorder
}

// MockProfileClientMockRecorder is the mock recorder for MockProfileClient.
type MockProfileClientMockRecorder struct {
	mock *MockProfileClient
}

// NewMockProfileClient creates a new mock instance.
func NewMockProfileClient(ctrl *gomock.Controller) *MockProfileClient {
	mock := &MockProfileClient{ctrl: ctrl}
	mock.recorder = &MockProfileClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProfileClient) EXPECT() *MockProfileClientMockRecorder {
	return m.recorder
}

// FetchSherlockBanners mocks base method.
func (m *MockProfileClient) FetchSherlockBanners(ctx context.Context, in *sherlock_banners.FetchSherlockBannersRequest, opts ...grpc.CallOption) (*sherlock_banners.FetchSherlockBannersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchSherlockBanners", varargs...)
	ret0, _ := ret[0].(*sherlock_banners.FetchSherlockBannersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchSherlockBanners indicates an expected call of FetchSherlockBanners.
func (mr *MockProfileClientMockRecorder) FetchSherlockBanners(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchSherlockBanners", reflect.TypeOf((*MockProfileClient)(nil).FetchSherlockBanners), varargs...)
}

// GetDetailedUserProfile mocks base method.
func (m *MockProfileClient) GetDetailedUserProfile(ctx context.Context, in *profile.GetDetailedUserProfileRequest, opts ...grpc.CallOption) (*profile.GetDetailedUserProfileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDetailedUserProfile", varargs...)
	ret0, _ := ret[0].(*profile.GetDetailedUserProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDetailedUserProfile indicates an expected call of GetDetailedUserProfile.
func (mr *MockProfileClientMockRecorder) GetDetailedUserProfile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDetailedUserProfile", reflect.TypeOf((*MockProfileClient)(nil).GetDetailedUserProfile), varargs...)
}

// GetRiskyConnections mocks base method.
func (m *MockProfileClient) GetRiskyConnections(ctx context.Context, in *profile.GetRiskyConnectionsRequest, opts ...grpc.CallOption) (*profile.GetRiskyConnectionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRiskyConnections", varargs...)
	ret0, _ := ret[0].(*profile.GetRiskyConnectionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRiskyConnections indicates an expected call of GetRiskyConnections.
func (mr *MockProfileClientMockRecorder) GetRiskyConnections(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRiskyConnections", reflect.TypeOf((*MockProfileClient)(nil).GetRiskyConnections), varargs...)
}

// GetUserProfile mocks base method.
func (m *MockProfileClient) GetUserProfile(ctx context.Context, in *profile.GetUserProfileRequest, opts ...grpc.CallOption) (*profile.GetUserProfileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserProfile", varargs...)
	ret0, _ := ret[0].(*profile.GetUserProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProfile indicates an expected call of GetUserProfile.
func (mr *MockProfileClientMockRecorder) GetUserProfile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfile", reflect.TypeOf((*MockProfileClient)(nil).GetUserProfile), varargs...)
}

// MockProfileServer is a mock of ProfileServer interface.
type MockProfileServer struct {
	ctrl     *gomock.Controller
	recorder *MockProfileServerMockRecorder
}

// MockProfileServerMockRecorder is the mock recorder for MockProfileServer.
type MockProfileServerMockRecorder struct {
	mock *MockProfileServer
}

// NewMockProfileServer creates a new mock instance.
func NewMockProfileServer(ctrl *gomock.Controller) *MockProfileServer {
	mock := &MockProfileServer{ctrl: ctrl}
	mock.recorder = &MockProfileServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProfileServer) EXPECT() *MockProfileServerMockRecorder {
	return m.recorder
}

// FetchSherlockBanners mocks base method.
func (m *MockProfileServer) FetchSherlockBanners(arg0 context.Context, arg1 *sherlock_banners.FetchSherlockBannersRequest) (*sherlock_banners.FetchSherlockBannersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchSherlockBanners", arg0, arg1)
	ret0, _ := ret[0].(*sherlock_banners.FetchSherlockBannersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchSherlockBanners indicates an expected call of FetchSherlockBanners.
func (mr *MockProfileServerMockRecorder) FetchSherlockBanners(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchSherlockBanners", reflect.TypeOf((*MockProfileServer)(nil).FetchSherlockBanners), arg0, arg1)
}

// GetDetailedUserProfile mocks base method.
func (m *MockProfileServer) GetDetailedUserProfile(arg0 context.Context, arg1 *profile.GetDetailedUserProfileRequest) (*profile.GetDetailedUserProfileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDetailedUserProfile", arg0, arg1)
	ret0, _ := ret[0].(*profile.GetDetailedUserProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDetailedUserProfile indicates an expected call of GetDetailedUserProfile.
func (mr *MockProfileServerMockRecorder) GetDetailedUserProfile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDetailedUserProfile", reflect.TypeOf((*MockProfileServer)(nil).GetDetailedUserProfile), arg0, arg1)
}

// GetRiskyConnections mocks base method.
func (m *MockProfileServer) GetRiskyConnections(arg0 context.Context, arg1 *profile.GetRiskyConnectionsRequest) (*profile.GetRiskyConnectionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRiskyConnections", arg0, arg1)
	ret0, _ := ret[0].(*profile.GetRiskyConnectionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRiskyConnections indicates an expected call of GetRiskyConnections.
func (mr *MockProfileServerMockRecorder) GetRiskyConnections(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRiskyConnections", reflect.TypeOf((*MockProfileServer)(nil).GetRiskyConnections), arg0, arg1)
}

// GetUserProfile mocks base method.
func (m *MockProfileServer) GetUserProfile(arg0 context.Context, arg1 *profile.GetUserProfileRequest) (*profile.GetUserProfileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfile", arg0, arg1)
	ret0, _ := ret[0].(*profile.GetUserProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProfile indicates an expected call of GetUserProfile.
func (mr *MockProfileServerMockRecorder) GetUserProfile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfile", reflect.TypeOf((*MockProfileServer)(nil).GetUserProfile), arg0, arg1)
}

// MockUnsafeProfileServer is a mock of UnsafeProfileServer interface.
type MockUnsafeProfileServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeProfileServerMockRecorder
}

// MockUnsafeProfileServerMockRecorder is the mock recorder for MockUnsafeProfileServer.
type MockUnsafeProfileServerMockRecorder struct {
	mock *MockUnsafeProfileServer
}

// NewMockUnsafeProfileServer creates a new mock instance.
func NewMockUnsafeProfileServer(ctrl *gomock.Controller) *MockUnsafeProfileServer {
	mock := &MockUnsafeProfileServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeProfileServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeProfileServer) EXPECT() *MockUnsafeProfileServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedProfileServer mocks base method.
func (m *MockUnsafeProfileServer) mustEmbedUnimplementedProfileServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedProfileServer")
}

// mustEmbedUnimplementedProfileServer indicates an expected call of mustEmbedUnimplementedProfileServer.
func (mr *MockUnsafeProfileServerMockRecorder) mustEmbedUnimplementedProfileServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedProfileServer", reflect.TypeOf((*MockUnsafeProfileServer)(nil).mustEmbedUnimplementedProfileServer))
}
