//go:generate gen_queue_pb

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/risk/profile/consumer.proto

package profile

import (
	context "context"
	operstatus "github.com/epifi/gamma/api/accounts/operstatus"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RiskProfileConsumer_ProcessAccountOperationStatusUpdateEvent_FullMethodName = "/risk.case_management.RiskProfileConsumer/ProcessAccountOperationStatusUpdateEvent"
)

// RiskProfileConsumerClient is the client API for RiskProfileConsumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RiskProfileConsumerClient interface {
	// ProcessAccountStatusUpdateEvent will consume account status update event from the CG service
	// and manage the emails sent to the user on status change
	// Topic of the queue: account-operational-status-update-topic
	ProcessAccountOperationStatusUpdateEvent(ctx context.Context, in *operstatus.OperationalStatusUpdateEvent, opts ...grpc.CallOption) (*ProcessAccountOperationStatusUpdateEventResponse, error)
}

type riskProfileConsumerClient struct {
	cc grpc.ClientConnInterface
}

func NewRiskProfileConsumerClient(cc grpc.ClientConnInterface) RiskProfileConsumerClient {
	return &riskProfileConsumerClient{cc}
}

func (c *riskProfileConsumerClient) ProcessAccountOperationStatusUpdateEvent(ctx context.Context, in *operstatus.OperationalStatusUpdateEvent, opts ...grpc.CallOption) (*ProcessAccountOperationStatusUpdateEventResponse, error) {
	out := new(ProcessAccountOperationStatusUpdateEventResponse)
	err := c.cc.Invoke(ctx, RiskProfileConsumer_ProcessAccountOperationStatusUpdateEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RiskProfileConsumerServer is the server API for RiskProfileConsumer service.
// All implementations should embed UnimplementedRiskProfileConsumerServer
// for forward compatibility
type RiskProfileConsumerServer interface {
	// ProcessAccountStatusUpdateEvent will consume account status update event from the CG service
	// and manage the emails sent to the user on status change
	// Topic of the queue: account-operational-status-update-topic
	ProcessAccountOperationStatusUpdateEvent(context.Context, *operstatus.OperationalStatusUpdateEvent) (*ProcessAccountOperationStatusUpdateEventResponse, error)
}

// UnimplementedRiskProfileConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedRiskProfileConsumerServer struct {
}

func (UnimplementedRiskProfileConsumerServer) ProcessAccountOperationStatusUpdateEvent(context.Context, *operstatus.OperationalStatusUpdateEvent) (*ProcessAccountOperationStatusUpdateEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessAccountOperationStatusUpdateEvent not implemented")
}

// UnsafeRiskProfileConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RiskProfileConsumerServer will
// result in compilation errors.
type UnsafeRiskProfileConsumerServer interface {
	mustEmbedUnimplementedRiskProfileConsumerServer()
}

func RegisterRiskProfileConsumerServer(s grpc.ServiceRegistrar, srv RiskProfileConsumerServer) {
	s.RegisterService(&RiskProfileConsumer_ServiceDesc, srv)
}

func _RiskProfileConsumer_ProcessAccountOperationStatusUpdateEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(operstatus.OperationalStatusUpdateEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskProfileConsumerServer).ProcessAccountOperationStatusUpdateEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskProfileConsumer_ProcessAccountOperationStatusUpdateEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskProfileConsumerServer).ProcessAccountOperationStatusUpdateEvent(ctx, req.(*operstatus.OperationalStatusUpdateEvent))
	}
	return interceptor(ctx, in, info, handler)
}

// RiskProfileConsumer_ServiceDesc is the grpc.ServiceDesc for RiskProfileConsumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RiskProfileConsumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "risk.case_management.RiskProfileConsumer",
	HandlerType: (*RiskProfileConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessAccountOperationStatusUpdateEvent",
			Handler:    _RiskProfileConsumer_ProcessAccountOperationStatusUpdateEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/risk/profile/consumer.proto",
}
