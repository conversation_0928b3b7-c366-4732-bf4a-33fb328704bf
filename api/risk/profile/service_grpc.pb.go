// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/risk/profile/service.proto

package profile

import (
	context "context"
	sherlock_banners "github.com/epifi/gamma/api/cx/sherlock_banners"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Profile_GetUserProfile_FullMethodName         = "/risk.profile.Profile/GetUserProfile"
	Profile_GetDetailedUserProfile_FullMethodName = "/risk.profile.Profile/GetDetailedUserProfile"
	Profile_FetchSherlockBanners_FullMethodName   = "/risk.profile.Profile/FetchSherlockBanners"
	Profile_GetRiskyConnections_FullMethodName    = "/risk.profile.Profile/GetRiskyConnections"
)

// ProfileClient is the client API for Profile service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ProfileClient interface {
	// GetUserProfile rpc is used to get user information such as
	// account freeze status, user under investigation, freeze in progress etc.
	// When user does not have an account, rpc will return status success with empty accounts_info
	GetUserProfile(ctx context.Context, in *GetUserProfileRequest, opts ...grpc.CallOption) (*GetUserProfileResponse, error)
	// GetDetailedUserProfile rpc intents to provide a deep risk profile details
	// Aiming this to be an internal rpc for uses cases like in sherlock banner etc.
	// RPC accepts actor id and returns array of different account details
	GetDetailedUserProfile(ctx context.Context, in *GetDetailedUserProfileRequest, opts ...grpc.CallOption) (*GetDetailedUserProfileResponse, error)
	// rpc invoked by sherlock banner service to fetch list of sherlock banners for agent view
	// No explicit error is returned
	FetchSherlockBanners(ctx context.Context, in *sherlock_banners.FetchSherlockBannersRequest, opts ...grpc.CallOption) (*sherlock_banners.FetchSherlockBannersResponse, error)
	// GetRiskyConnections returns risky connections of the user
	// Here, connection can be contact, referee or referrer
	// Connection type can be passed as array of field masks and is a mandatory field
	// Aiming this rpc for low QPS uses cases like in risk-ops user view.
	GetRiskyConnections(ctx context.Context, in *GetRiskyConnectionsRequest, opts ...grpc.CallOption) (*GetRiskyConnectionsResponse, error)
}

type profileClient struct {
	cc grpc.ClientConnInterface
}

func NewProfileClient(cc grpc.ClientConnInterface) ProfileClient {
	return &profileClient{cc}
}

func (c *profileClient) GetUserProfile(ctx context.Context, in *GetUserProfileRequest, opts ...grpc.CallOption) (*GetUserProfileResponse, error) {
	out := new(GetUserProfileResponse)
	err := c.cc.Invoke(ctx, Profile_GetUserProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *profileClient) GetDetailedUserProfile(ctx context.Context, in *GetDetailedUserProfileRequest, opts ...grpc.CallOption) (*GetDetailedUserProfileResponse, error) {
	out := new(GetDetailedUserProfileResponse)
	err := c.cc.Invoke(ctx, Profile_GetDetailedUserProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *profileClient) FetchSherlockBanners(ctx context.Context, in *sherlock_banners.FetchSherlockBannersRequest, opts ...grpc.CallOption) (*sherlock_banners.FetchSherlockBannersResponse, error) {
	out := new(sherlock_banners.FetchSherlockBannersResponse)
	err := c.cc.Invoke(ctx, Profile_FetchSherlockBanners_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *profileClient) GetRiskyConnections(ctx context.Context, in *GetRiskyConnectionsRequest, opts ...grpc.CallOption) (*GetRiskyConnectionsResponse, error) {
	out := new(GetRiskyConnectionsResponse)
	err := c.cc.Invoke(ctx, Profile_GetRiskyConnections_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProfileServer is the server API for Profile service.
// All implementations should embed UnimplementedProfileServer
// for forward compatibility
type ProfileServer interface {
	// GetUserProfile rpc is used to get user information such as
	// account freeze status, user under investigation, freeze in progress etc.
	// When user does not have an account, rpc will return status success with empty accounts_info
	GetUserProfile(context.Context, *GetUserProfileRequest) (*GetUserProfileResponse, error)
	// GetDetailedUserProfile rpc intents to provide a deep risk profile details
	// Aiming this to be an internal rpc for uses cases like in sherlock banner etc.
	// RPC accepts actor id and returns array of different account details
	GetDetailedUserProfile(context.Context, *GetDetailedUserProfileRequest) (*GetDetailedUserProfileResponse, error)
	// rpc invoked by sherlock banner service to fetch list of sherlock banners for agent view
	// No explicit error is returned
	FetchSherlockBanners(context.Context, *sherlock_banners.FetchSherlockBannersRequest) (*sherlock_banners.FetchSherlockBannersResponse, error)
	// GetRiskyConnections returns risky connections of the user
	// Here, connection can be contact, referee or referrer
	// Connection type can be passed as array of field masks and is a mandatory field
	// Aiming this rpc for low QPS uses cases like in risk-ops user view.
	GetRiskyConnections(context.Context, *GetRiskyConnectionsRequest) (*GetRiskyConnectionsResponse, error)
}

// UnimplementedProfileServer should be embedded to have forward compatible implementations.
type UnimplementedProfileServer struct {
}

func (UnimplementedProfileServer) GetUserProfile(context.Context, *GetUserProfileRequest) (*GetUserProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserProfile not implemented")
}
func (UnimplementedProfileServer) GetDetailedUserProfile(context.Context, *GetDetailedUserProfileRequest) (*GetDetailedUserProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDetailedUserProfile not implemented")
}
func (UnimplementedProfileServer) FetchSherlockBanners(context.Context, *sherlock_banners.FetchSherlockBannersRequest) (*sherlock_banners.FetchSherlockBannersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchSherlockBanners not implemented")
}
func (UnimplementedProfileServer) GetRiskyConnections(context.Context, *GetRiskyConnectionsRequest) (*GetRiskyConnectionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRiskyConnections not implemented")
}

// UnsafeProfileServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ProfileServer will
// result in compilation errors.
type UnsafeProfileServer interface {
	mustEmbedUnimplementedProfileServer()
}

func RegisterProfileServer(s grpc.ServiceRegistrar, srv ProfileServer) {
	s.RegisterService(&Profile_ServiceDesc, srv)
}

func _Profile_GetUserProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProfileServer).GetUserProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Profile_GetUserProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProfileServer).GetUserProfile(ctx, req.(*GetUserProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Profile_GetDetailedUserProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDetailedUserProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProfileServer).GetDetailedUserProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Profile_GetDetailedUserProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProfileServer).GetDetailedUserProfile(ctx, req.(*GetDetailedUserProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Profile_FetchSherlockBanners_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(sherlock_banners.FetchSherlockBannersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProfileServer).FetchSherlockBanners(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Profile_FetchSherlockBanners_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProfileServer).FetchSherlockBanners(ctx, req.(*sherlock_banners.FetchSherlockBannersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Profile_GetRiskyConnections_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRiskyConnectionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProfileServer).GetRiskyConnections(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Profile_GetRiskyConnections_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProfileServer).GetRiskyConnections(ctx, req.(*GetRiskyConnectionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Profile_ServiceDesc is the grpc.ServiceDesc for Profile service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Profile_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "risk.profile.Profile",
	HandlerType: (*ProfileServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserProfile",
			Handler:    _Profile_GetUserProfile_Handler,
		},
		{
			MethodName: "GetDetailedUserProfile",
			Handler:    _Profile_GetDetailedUserProfile_Handler,
		},
		{
			MethodName: "FetchSherlockBanners",
			Handler:    _Profile_FetchSherlockBanners_Handler,
		},
		{
			MethodName: "GetRiskyConnections",
			Handler:    _Profile_GetRiskyConnections_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/risk/profile/service.proto",
}
