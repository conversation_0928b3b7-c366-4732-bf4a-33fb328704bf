// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/profile/consumer.proto

package profile

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on
// ProcessAccountOperationStatusUpdateEventResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessAccountOperationStatusUpdateEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessAccountOperationStatusUpdateEventResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ProcessAccountOperationStatusUpdateEventResponseMultiError, or nil if none found.
func (m *ProcessAccountOperationStatusUpdateEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessAccountOperationStatusUpdateEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessAccountOperationStatusUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessAccountOperationStatusUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessAccountOperationStatusUpdateEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessAccountOperationStatusUpdateEventResponseMultiError(errors)
	}

	return nil
}

// ProcessAccountOperationStatusUpdateEventResponseMultiError is an error
// wrapping multiple validation errors returned by
// ProcessAccountOperationStatusUpdateEventResponse.ValidateAll() if the
// designated constraints aren't met.
type ProcessAccountOperationStatusUpdateEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessAccountOperationStatusUpdateEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessAccountOperationStatusUpdateEventResponseMultiError) AllErrors() []error { return m }

// ProcessAccountOperationStatusUpdateEventResponseValidationError is the
// validation error returned by
// ProcessAccountOperationStatusUpdateEventResponse.Validate if the designated
// constraints aren't met.
type ProcessAccountOperationStatusUpdateEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessAccountOperationStatusUpdateEventResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ProcessAccountOperationStatusUpdateEventResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ProcessAccountOperationStatusUpdateEventResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ProcessAccountOperationStatusUpdateEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessAccountOperationStatusUpdateEventResponseValidationError) ErrorName() string {
	return "ProcessAccountOperationStatusUpdateEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessAccountOperationStatusUpdateEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessAccountOperationStatusUpdateEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessAccountOperationStatusUpdateEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessAccountOperationStatusUpdateEventResponseValidationError{}
