// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/profile/service.proto

package profile

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	accounts "github.com/epifi/gamma/api/accounts"
	sherlock_banners "github.com/epifi/gamma/api/cx/sherlock_banners"
	form "github.com/epifi/gamma/api/risk/case_management/form"
	enums "github.com/epifi/gamma/api/risk/enums"
	lea "github.com/epifi/gamma/api/risk/lea"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetUserProfileResponse_Status int32

const (
	GetUserProfileResponse_OK GetUserProfileResponse_Status = 0
	// Internal resposne due to some internal error in the rpc
	GetUserProfileResponse_INTERNAL GetUserProfileResponse_Status = 13
	// Record not found for the given actor id
	GetUserProfileResponse_RECORD_NOT_FOUND GetUserProfileResponse_Status = 5
)

// Enum value maps for GetUserProfileResponse_Status.
var (
	GetUserProfileResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		5:  "RECORD_NOT_FOUND",
	}
	GetUserProfileResponse_Status_value = map[string]int32{
		"OK":               0,
		"INTERNAL":         13,
		"RECORD_NOT_FOUND": 5,
	}
)

func (x GetUserProfileResponse_Status) Enum() *GetUserProfileResponse_Status {
	p := new(GetUserProfileResponse_Status)
	*p = x
	return p
}

func (x GetUserProfileResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetUserProfileResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_profile_service_proto_enumTypes[0].Descriptor()
}

func (GetUserProfileResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_profile_service_proto_enumTypes[0]
}

func (x GetUserProfileResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetUserProfileResponse_Status.Descriptor instead.
func (GetUserProfileResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{1, 0}
}

type GetDetailedUserProfileResponse_Status int32

const (
	GetDetailedUserProfileResponse_OK GetDetailedUserProfileResponse_Status = 0
	// Invalid argument passed in the request
	GetDetailedUserProfileResponse_INVALID_ARGUMENT GetDetailedUserProfileResponse_Status = 3
	// Internal response due to some internal error in the rpc
	GetDetailedUserProfileResponse_INTERNAL GetDetailedUserProfileResponse_Status = 13
)

// Enum value maps for GetDetailedUserProfileResponse_Status.
var (
	GetDetailedUserProfileResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	GetDetailedUserProfileResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x GetDetailedUserProfileResponse_Status) Enum() *GetDetailedUserProfileResponse_Status {
	p := new(GetDetailedUserProfileResponse_Status)
	*p = x
	return p
}

func (x GetDetailedUserProfileResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetDetailedUserProfileResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_profile_service_proto_enumTypes[1].Descriptor()
}

func (GetDetailedUserProfileResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_profile_service_proto_enumTypes[1]
}

func (x GetDetailedUserProfileResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetDetailedUserProfileResponse_Status.Descriptor instead.
func (GetDetailedUserProfileResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{4, 0}
}

type GetRiskyConnectionsRequest_ConnectionsFieldMask int32

const (
	GetRiskyConnectionsRequest_CONNECTIONS_MASK_UNDEFINED             GetRiskyConnectionsRequest_ConnectionsFieldMask = 0
	GetRiskyConnectionsRequest_CONNECTIONS_MASK_CONTACT_ASSOCIATIONS  GetRiskyConnectionsRequest_ConnectionsFieldMask = 1
	GetRiskyConnectionsRequest_CONNECTIONS_MASK_REFERRAL_ASSOCIATIONS GetRiskyConnectionsRequest_ConnectionsFieldMask = 2
)

// Enum value maps for GetRiskyConnectionsRequest_ConnectionsFieldMask.
var (
	GetRiskyConnectionsRequest_ConnectionsFieldMask_name = map[int32]string{
		0: "CONNECTIONS_MASK_UNDEFINED",
		1: "CONNECTIONS_MASK_CONTACT_ASSOCIATIONS",
		2: "CONNECTIONS_MASK_REFERRAL_ASSOCIATIONS",
	}
	GetRiskyConnectionsRequest_ConnectionsFieldMask_value = map[string]int32{
		"CONNECTIONS_MASK_UNDEFINED":             0,
		"CONNECTIONS_MASK_CONTACT_ASSOCIATIONS":  1,
		"CONNECTIONS_MASK_REFERRAL_ASSOCIATIONS": 2,
	}
)

func (x GetRiskyConnectionsRequest_ConnectionsFieldMask) Enum() *GetRiskyConnectionsRequest_ConnectionsFieldMask {
	p := new(GetRiskyConnectionsRequest_ConnectionsFieldMask)
	*p = x
	return p
}

func (x GetRiskyConnectionsRequest_ConnectionsFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetRiskyConnectionsRequest_ConnectionsFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_profile_service_proto_enumTypes[2].Descriptor()
}

func (GetRiskyConnectionsRequest_ConnectionsFieldMask) Type() protoreflect.EnumType {
	return &file_api_risk_profile_service_proto_enumTypes[2]
}

func (x GetRiskyConnectionsRequest_ConnectionsFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetRiskyConnectionsRequest_ConnectionsFieldMask.Descriptor instead.
func (GetRiskyConnectionsRequest_ConnectionsFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{10, 0}
}

type GetRiskyConnectionsResponse_Status int32

const (
	GetRiskyConnectionsResponse_OK       GetRiskyConnectionsResponse_Status = 0
	GetRiskyConnectionsResponse_INTERNAL GetRiskyConnectionsResponse_Status = 13
)

// Enum value maps for GetRiskyConnectionsResponse_Status.
var (
	GetRiskyConnectionsResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetRiskyConnectionsResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetRiskyConnectionsResponse_Status) Enum() *GetRiskyConnectionsResponse_Status {
	p := new(GetRiskyConnectionsResponse_Status)
	*p = x
	return p
}

func (x GetRiskyConnectionsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetRiskyConnectionsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_profile_service_proto_enumTypes[3].Descriptor()
}

func (GetRiskyConnectionsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_profile_service_proto_enumTypes[3]
}

func (x GetRiskyConnectionsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetRiskyConnectionsResponse_Status.Descriptor instead.
func (GetRiskyConnectionsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{11, 0}
}

type GetUserProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetUserProfileRequest) Reset() {
	*x = GetUserProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserProfileRequest) ProtoMessage() {}

func (x *GetUserProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserProfileRequest.ProtoReflect.Descriptor instead.
func (*GetUserProfileRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetUserProfileRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetUserProfileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	AccountsInfo []*AccountInfo `protobuf:"bytes,2,rep,name=accounts_info,json=accountsInfo,proto3" json:"accounts_info,omitempty"`
}

func (x *GetUserProfileResponse) Reset() {
	*x = GetUserProfileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserProfileResponse) ProtoMessage() {}

func (x *GetUserProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserProfileResponse.ProtoReflect.Descriptor instead.
func (*GetUserProfileResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserProfileResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUserProfileResponse) GetAccountsInfo() []*AccountInfo {
	if x != nil {
		return x.AccountsInfo
	}
	return nil
}

type AccountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// Account freeze status at present
	//   - currently it applies only for savings account
	PresentFreezeStatus enums.AccountFreezeStatus `protobuf:"varint,2,opt,name=present_freeze_status,json=presentFreezeStatus,proto3,enum=enums.AccountFreezeStatus" json:"present_freeze_status,omitempty"`
	// Proposed account freeze status that is
	// currently pending either at the bank or in an intermediate state.
	ImpendingFreezeStatus enums.AccountFreezeStatus `protobuf:"varint,3,opt,name=impending_freeze_status,json=impendingFreezeStatus,proto3,enum=enums.AccountFreezeStatus" json:"impending_freeze_status,omitempty"`
	// Whether account is under review for some suspicious activity
	IsUnderReview bool `protobuf:"varint,4,opt,name=is_under_review,json=isUnderReview,proto3" json:"is_under_review,omitempty"`
	// time at which account was flagged last
	// for some suspicious activity and sent for review
	LastFlaggedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=last_flagged_at,json=lastFlaggedAt,proto3" json:"last_flagged_at,omitempty"`
	// freeze reasons for additional freeze info
	FreezeReasons   []string `protobuf:"bytes,6,rep,name=freeze_reasons,json=freezeReasons,proto3" json:"freeze_reasons,omitempty"`
	IsAccountClosed bool     `protobuf:"varint,7,opt,name=is_account_closed,json=isAccountClosed,proto3" json:"is_account_closed,omitempty"`
}

func (x *AccountInfo) Reset() {
	*x = AccountInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountInfo) ProtoMessage() {}

func (x *AccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountInfo.ProtoReflect.Descriptor instead.
func (*AccountInfo) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{2}
}

func (x *AccountInfo) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *AccountInfo) GetPresentFreezeStatus() enums.AccountFreezeStatus {
	if x != nil {
		return x.PresentFreezeStatus
	}
	return enums.AccountFreezeStatus(0)
}

func (x *AccountInfo) GetImpendingFreezeStatus() enums.AccountFreezeStatus {
	if x != nil {
		return x.ImpendingFreezeStatus
	}
	return enums.AccountFreezeStatus(0)
}

func (x *AccountInfo) GetIsUnderReview() bool {
	if x != nil {
		return x.IsUnderReview
	}
	return false
}

func (x *AccountInfo) GetLastFlaggedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastFlaggedAt
	}
	return nil
}

func (x *AccountInfo) GetFreezeReasons() []string {
	if x != nil {
		return x.FreezeReasons
	}
	return nil
}

func (x *AccountInfo) GetIsAccountClosed() bool {
	if x != nil {
		return x.IsAccountClosed
	}
	return false
}

type GetDetailedUserProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// global limit for fetching entries
	// for all array elements, below max limit will be applied
	MaxLimit int32 `protobuf:"varint,2,opt,name=max_limit,json=maxLimit,proto3" json:"max_limit,omitempty"`
}

func (x *GetDetailedUserProfileRequest) Reset() {
	*x = GetDetailedUserProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDetailedUserProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDetailedUserProfileRequest) ProtoMessage() {}

func (x *GetDetailedUserProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDetailedUserProfileRequest.ProtoReflect.Descriptor instead.
func (*GetDetailedUserProfileRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetDetailedUserProfileRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetDetailedUserProfileRequest) GetMaxLimit() int32 {
	if x != nil {
		return x.MaxLimit
	}
	return 0
}

type GetDetailedUserProfileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// map of different account id to detailed account info against an actor
	AccountsInfo map[string]*DetailedAccountInfo `protobuf:"bytes,2,rep,name=accounts_info,json=accountsInfo,proto3" json:"accounts_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetDetailedUserProfileResponse) Reset() {
	*x = GetDetailedUserProfileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDetailedUserProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDetailedUserProfileResponse) ProtoMessage() {}

func (x *GetDetailedUserProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDetailedUserProfileResponse.ProtoReflect.Descriptor instead.
func (*GetDetailedUserProfileResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetDetailedUserProfileResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetDetailedUserProfileResponse) GetAccountsInfo() map[string]*DetailedAccountInfo {
	if x != nil {
		return x.AccountsInfo
	}
	return nil
}

// current account status with reason if any
type AccountStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PresentStatus enums.AccountFreezeStatus `protobuf:"varint,1,opt,name=present_status,json=presentStatus,proto3,enum=enums.AccountFreezeStatus" json:"present_status,omitempty"`
	// free flow freeze reason strings
	FreezeReason    []string `protobuf:"bytes,2,rep,name=freeze_reason,json=freezeReason,proto3" json:"freeze_reason,omitempty"`
	IsAccountClosed bool     `protobuf:"varint,3,opt,name=is_account_closed,json=isAccountClosed,proto3" json:"is_account_closed,omitempty"`
}

func (x *AccountStatus) Reset() {
	*x = AccountStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountStatus) ProtoMessage() {}

func (x *AccountStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountStatus.ProtoReflect.Descriptor instead.
func (*AccountStatus) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{5}
}

func (x *AccountStatus) GetPresentStatus() enums.AccountFreezeStatus {
	if x != nil {
		return x.PresentStatus
	}
	return enums.AccountFreezeStatus(0)
}

func (x *AccountStatus) GetFreezeReason() []string {
	if x != nil {
		return x.FreezeReason
	}
	return nil
}

func (x *AccountStatus) GetIsAccountClosed() bool {
	if x != nil {
		return x.IsAccountClosed
	}
	return false
}

// account state transitions eg: credit, debit, total-freeze
type AccountAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// specifies reason for which action is being taken
	Reason enums.RequestReason `protobuf:"varint,1,opt,name=reason,proto3,enum=enums.RequestReason" json:"reason,omitempty"`
	// free flow action remarks
	Remarks        string                 `protobuf:"bytes,2,opt,name=remarks,proto3" json:"remarks,omitempty"`
	BankActionDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=bank_action_date,json=bankActionDate,proto3" json:"bank_action_date,omitempty"`
	// indented action
	Action      enums.Action `protobuf:"varint,4,opt,name=action,proto3,enum=enums.Action" json:"action,omitempty"`
	IsCompleted bool         `protobuf:"varint,5,opt,name=is_completed,json=isCompleted,proto3" json:"is_completed,omitempty"`
}

func (x *AccountAction) Reset() {
	*x = AccountAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountAction) ProtoMessage() {}

func (x *AccountAction) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountAction.ProtoReflect.Descriptor instead.
func (*AccountAction) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{6}
}

func (x *AccountAction) GetReason() enums.RequestReason {
	if x != nil {
		return x.Reason
	}
	return enums.RequestReason(0)
}

func (x *AccountAction) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *AccountAction) GetBankActionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.BankActionDate
	}
	return nil
}

func (x *AccountAction) GetAction() enums.Action {
	if x != nil {
		return x.Action
	}
	return enums.Action(0)
}

func (x *AccountAction) GetIsCompleted() bool {
	if x != nil {
		return x.IsCompleted
	}
	return false
}

// LEA complaint info against the account, with date, remarks and source info
type LEAComplaintInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// freeze status enforced by LEA complaint, can have unfreeze here as well if latest action was unfreeze (for NOC cases)
	LeaFreezeStatus enums.AccountFreezeStatus `protobuf:"varint,1,opt,name=lea_freeze_status,json=leaFreezeStatus,proto3,enum=enums.AccountFreezeStatus" json:"lea_freeze_status,omitempty"`
	// best effort LEA action date
	LeaActionDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=lea_action_date,json=leaActionDate,proto3" json:"lea_action_date,omitempty"`
	// free flow human readable remarks
	Remarks []string `protobuf:"bytes,3,rep,name=remarks,proto3" json:"remarks,omitempty"`
	// free flow human readable string
	SourceDetails string `protobuf:"bytes,4,opt,name=source_details,json=sourceDetails,proto3" json:"source_details,omitempty"`
}

func (x *LEAComplaintInfo) Reset() {
	*x = LEAComplaintInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LEAComplaintInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LEAComplaintInfo) ProtoMessage() {}

func (x *LEAComplaintInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LEAComplaintInfo.ProtoReflect.Descriptor instead.
func (*LEAComplaintInfo) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{7}
}

func (x *LEAComplaintInfo) GetLeaFreezeStatus() enums.AccountFreezeStatus {
	if x != nil {
		return x.LeaFreezeStatus
	}
	return enums.AccountFreezeStatus(0)
}

func (x *LEAComplaintInfo) GetLeaActionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LeaActionDate
	}
	return nil
}

func (x *LEAComplaintInfo) GetRemarks() []string {
	if x != nil {
		return x.Remarks
	}
	return nil
}

func (x *LEAComplaintInfo) GetSourceDetails() string {
	if x != nil {
		return x.SourceDetails
	}
	return ""
}

// Form details for actor
type FormInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Current status of the form.
	Status form.Status `protobuf:"varint,1,opt,name=status,proto3,enum=risk.case_management.form.Status" json:"status,omitempty"`
	// will be present only when form can be filled
	FormLink string `protobuf:"bytes,2,opt,name=form_link,json=formLink,proto3" json:"form_link,omitempty"`
	// Optional: Risk Case against which form was triggered.
	CaseId string `protobuf:"bytes,3,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	// Inception point from where form generation is triggered.
	Origin    form.FormOrigin        `protobuf:"varint,4,opt,name=origin,proto3,enum=risk.case_management.form.FormOrigin" json:"origin,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	ExpireAt  *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
}

func (x *FormInfo) Reset() {
	*x = FormInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormInfo) ProtoMessage() {}

func (x *FormInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormInfo.ProtoReflect.Descriptor instead.
func (*FormInfo) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{8}
}

func (x *FormInfo) GetStatus() form.Status {
	if x != nil {
		return x.Status
	}
	return form.Status(0)
}

func (x *FormInfo) GetFormLink() string {
	if x != nil {
		return x.FormLink
	}
	return ""
}

func (x *FormInfo) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *FormInfo) GetOrigin() form.FormOrigin {
	if x != nil {
		return x.Origin
	}
	return form.FormOrigin(0)
}

func (x *FormInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *FormInfo) GetExpireAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireAt
	}
	return nil
}

type DetailedAccountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// type of account to which statement belongs, can be deposit or savings
	AccountType accounts.Type `protobuf:"varint,2,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
	// account's current freeze status. this is different from account operational status given below. freeze status
	// explains the freeze state that we want to achieve in our risk system. account operational status explains
	// the actual status in the bank's system
	AccountStatus *AccountStatus `protobuf:"bytes,3,opt,name=account_status,json=accountStatus,proto3" json:"account_status,omitempty"`
	// previous account actions
	PastActions []*AccountAction `protobuf:"bytes,4,rep,name=past_actions,json=pastActions,proto3" json:"past_actions,omitempty"`
	// Deprecated in favour of unified_lea_complaints since we have deprecated LEA Complaint Manager which populates
	// this field
	// use unified_lea_complaints instead
	//
	// Deprecated: Marked as deprecated in api/risk/profile/service.proto.
	LeaStatus []*LEAComplaintInfo `protobuf:"bytes,5,rep,name=lea_status,json=leaStatus,proto3" json:"lea_status,omitempty"`
	// is account is under review for some suspicious activity
	IsUnderReview bool `protobuf:"varint,6,opt,name=is_under_review,json=isUnderReview,proto3" json:"is_under_review,omitempty"`
	// list of recent forms, sorted by created_at desc
	Forms []*FormInfo `protobuf:"bytes,7,rep,name=forms,proto3" json:"forms,omitempty"`
	// current operational status at bank's side. different from AccountStatus message above which only contains freeze status
	AccountOperationalStatus enums.AccountOperationalStatus `protobuf:"varint,8,opt,name=account_operational_status,json=accountOperationalStatus,proto3,enum=enums.AccountOperationalStatus" json:"account_operational_status,omitempty"`
	// contains details of LEA complaint we received from bank
	UnifiedLeaComplaints []*lea.UnifiedLeaComplaint `protobuf:"bytes,9,rep,name=unified_lea_complaints,json=unifiedLeaComplaints,proto3" json:"unified_lea_complaints,omitempty"`
	// based on freeze code, lea report existence, layer etc., calculate the freeze reason. Details on logic here: https://docs.google.com/spreadsheets/d/1S0FXw2yxGSM7Y6fBLjSaTX2Q6S1C5O16/edit?gid=**********#gid=**********
	// this field is calculated at an account level across freeze codes and lea reports attached to the account
	ProcessedFreezeReason enums.ProcessedFreezeReason `protobuf:"varint,10,opt,name=processed_freeze_reason,json=processedFreezeReason,proto3,enum=enums.ProcessedFreezeReason" json:"processed_freeze_reason,omitempty"`
}

func (x *DetailedAccountInfo) Reset() {
	*x = DetailedAccountInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailedAccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailedAccountInfo) ProtoMessage() {}

func (x *DetailedAccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailedAccountInfo.ProtoReflect.Descriptor instead.
func (*DetailedAccountInfo) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{9}
}

func (x *DetailedAccountInfo) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *DetailedAccountInfo) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

func (x *DetailedAccountInfo) GetAccountStatus() *AccountStatus {
	if x != nil {
		return x.AccountStatus
	}
	return nil
}

func (x *DetailedAccountInfo) GetPastActions() []*AccountAction {
	if x != nil {
		return x.PastActions
	}
	return nil
}

// Deprecated: Marked as deprecated in api/risk/profile/service.proto.
func (x *DetailedAccountInfo) GetLeaStatus() []*LEAComplaintInfo {
	if x != nil {
		return x.LeaStatus
	}
	return nil
}

func (x *DetailedAccountInfo) GetIsUnderReview() bool {
	if x != nil {
		return x.IsUnderReview
	}
	return false
}

func (x *DetailedAccountInfo) GetForms() []*FormInfo {
	if x != nil {
		return x.Forms
	}
	return nil
}

func (x *DetailedAccountInfo) GetAccountOperationalStatus() enums.AccountOperationalStatus {
	if x != nil {
		return x.AccountOperationalStatus
	}
	return enums.AccountOperationalStatus(0)
}

func (x *DetailedAccountInfo) GetUnifiedLeaComplaints() []*lea.UnifiedLeaComplaint {
	if x != nil {
		return x.UnifiedLeaComplaints
	}
	return nil
}

func (x *DetailedAccountInfo) GetProcessedFreezeReason() enums.ProcessedFreezeReason {
	if x != nil {
		return x.ProcessedFreezeReason
	}
	return enums.ProcessedFreezeReason(0)
}

type GetRiskyConnectionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId              string                                            `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ConnectionsFieldMask []GetRiskyConnectionsRequest_ConnectionsFieldMask `protobuf:"varint,2,rep,packed,name=connections_field_mask,json=connectionsFieldMask,proto3,enum=risk.profile.GetRiskyConnectionsRequest_ConnectionsFieldMask" json:"connections_field_mask,omitempty"`
}

func (x *GetRiskyConnectionsRequest) Reset() {
	*x = GetRiskyConnectionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRiskyConnectionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRiskyConnectionsRequest) ProtoMessage() {}

func (x *GetRiskyConnectionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRiskyConnectionsRequest.ProtoReflect.Descriptor instead.
func (*GetRiskyConnectionsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetRiskyConnectionsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRiskyConnectionsRequest) GetConnectionsFieldMask() []GetRiskyConnectionsRequest_ConnectionsFieldMask {
	if x != nil {
		return x.ConnectionsFieldMask
	}
	return nil
}

type GetRiskyConnectionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Risky users who have given actor as contact.
	InwardContactAssociations []*GetRiskyConnectionsResponse_ContactAssociation `protobuf:"bytes,2,rep,name=inward_contact_associations,json=inwardContactAssociations,proto3" json:"inward_contact_associations,omitempty"`
	// Risky contacts of given actor.
	OutwardContactAssociations []*GetRiskyConnectionsResponse_ContactAssociation `protobuf:"bytes,3,rep,name=outward_contact_associations,json=outwardContactAssociations,proto3" json:"outward_contact_associations,omitempty"`
	// Referrer details for input actor, will be nil if no referrer
	// Contains complete list of referrer actor id with appropriate flags for risky behaviour
	ReferrerInfo *GetRiskyConnectionsResponse_ReferralInfo `protobuf:"bytes,4,opt,name=referrer_info,json=referrerInfo,proto3" json:"referrer_info,omitempty"`
	// Referrals by input actor
	// Contains exhaustive list of referrer actor ids with appropriate flags for risky behaviour
	RefereesInfo []*GetRiskyConnectionsResponse_ReferralInfo `protobuf:"bytes,5,rep,name=referees_info,json=refereesInfo,proto3" json:"referees_info,omitempty"`
}

func (x *GetRiskyConnectionsResponse) Reset() {
	*x = GetRiskyConnectionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRiskyConnectionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRiskyConnectionsResponse) ProtoMessage() {}

func (x *GetRiskyConnectionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRiskyConnectionsResponse.ProtoReflect.Descriptor instead.
func (*GetRiskyConnectionsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetRiskyConnectionsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRiskyConnectionsResponse) GetInwardContactAssociations() []*GetRiskyConnectionsResponse_ContactAssociation {
	if x != nil {
		return x.InwardContactAssociations
	}
	return nil
}

func (x *GetRiskyConnectionsResponse) GetOutwardContactAssociations() []*GetRiskyConnectionsResponse_ContactAssociation {
	if x != nil {
		return x.OutwardContactAssociations
	}
	return nil
}

func (x *GetRiskyConnectionsResponse) GetReferrerInfo() *GetRiskyConnectionsResponse_ReferralInfo {
	if x != nil {
		return x.ReferrerInfo
	}
	return nil
}

func (x *GetRiskyConnectionsResponse) GetRefereesInfo() []*GetRiskyConnectionsResponse_ReferralInfo {
	if x != nil {
		return x.RefereesInfo
	}
	return nil
}

type AccountActionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsNegativeActionPresent bool                `protobuf:"varint,1,opt,name=is_negative_action_present,json=isNegativeActionPresent,proto3" json:"is_negative_action_present,omitempty"`
	Action                  enums.Action        `protobuf:"varint,2,opt,name=action,proto3,enum=enums.Action" json:"action,omitempty"`
	Reason                  enums.RequestReason `protobuf:"varint,3,opt,name=reason,proto3,enum=enums.RequestReason" json:"reason,omitempty"`
}

func (x *AccountActionDetails) Reset() {
	*x = AccountActionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountActionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountActionDetails) ProtoMessage() {}

func (x *AccountActionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountActionDetails.ProtoReflect.Descriptor instead.
func (*AccountActionDetails) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{12}
}

func (x *AccountActionDetails) GetIsNegativeActionPresent() bool {
	if x != nil {
		return x.IsNegativeActionPresent
	}
	return false
}

func (x *AccountActionDetails) GetAction() enums.Action {
	if x != nil {
		return x.Action
	}
	return enums.Action(0)
}

func (x *AccountActionDetails) GetReason() enums.RequestReason {
	if x != nil {
		return x.Reason
	}
	return enums.RequestReason(0)
}

type LEADetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsLeaPresent         bool                   `protobuf:"varint,1,opt,name=is_lea_present,json=isLeaPresent,proto3" json:"is_lea_present,omitempty"`
	LeaReportedTimestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=lea_reported_timestamp,json=leaReportedTimestamp,proto3" json:"lea_reported_timestamp,omitempty"`
}

func (x *LEADetails) Reset() {
	*x = LEADetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LEADetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LEADetails) ProtoMessage() {}

func (x *LEADetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LEADetails.ProtoReflect.Descriptor instead.
func (*LEADetails) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{13}
}

func (x *LEADetails) GetIsLeaPresent() bool {
	if x != nil {
		return x.IsLeaPresent
	}
	return false
}

func (x *LEADetails) GetLeaReportedTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.LeaReportedTimestamp
	}
	return nil
}

// Contact links for actor
type GetRiskyConnectionsResponse_ContactAssociation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor id of referee or referrer, depends on how it's used.
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Any negative action on account present
	AccountActionDetails *AccountActionDetails `protobuf:"bytes,2,opt,name=account_action_details,json=accountActionDetails,proto3" json:"account_action_details,omitempty"`
	// Any LEA complaint against account
	LeaDetails *LEADetails `protobuf:"bytes,3,opt,name=lea_details,json=leaDetails,proto3" json:"lea_details,omitempty"`
}

func (x *GetRiskyConnectionsResponse_ContactAssociation) Reset() {
	*x = GetRiskyConnectionsResponse_ContactAssociation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRiskyConnectionsResponse_ContactAssociation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRiskyConnectionsResponse_ContactAssociation) ProtoMessage() {}

func (x *GetRiskyConnectionsResponse_ContactAssociation) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRiskyConnectionsResponse_ContactAssociation.ProtoReflect.Descriptor instead.
func (*GetRiskyConnectionsResponse_ContactAssociation) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{11, 0}
}

func (x *GetRiskyConnectionsResponse_ContactAssociation) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRiskyConnectionsResponse_ContactAssociation) GetAccountActionDetails() *AccountActionDetails {
	if x != nil {
		return x.AccountActionDetails
	}
	return nil
}

func (x *GetRiskyConnectionsResponse_ContactAssociation) GetLeaDetails() *LEADetails {
	if x != nil {
		return x.LeaDetails
	}
	return nil
}

type GetRiskyConnectionsResponse_ReferralInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor id of referee or referrer, depends on how it's used.
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Any negative action on account present
	AccountActionDetails *AccountActionDetails `protobuf:"bytes,2,opt,name=account_action_details,json=accountActionDetails,proto3" json:"account_action_details,omitempty"`
	// Any LEA complaint against account
	LeaDetails *LEADetails `protobuf:"bytes,3,opt,name=lea_details,json=leaDetails,proto3" json:"lea_details,omitempty"`
}

func (x *GetRiskyConnectionsResponse_ReferralInfo) Reset() {
	*x = GetRiskyConnectionsResponse_ReferralInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_profile_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRiskyConnectionsResponse_ReferralInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRiskyConnectionsResponse_ReferralInfo) ProtoMessage() {}

func (x *GetRiskyConnectionsResponse_ReferralInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_profile_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRiskyConnectionsResponse_ReferralInfo.ProtoReflect.Descriptor instead.
func (*GetRiskyConnectionsResponse_ReferralInfo) Descriptor() ([]byte, []int) {
	return file_api_risk_profile_service_proto_rawDescGZIP(), []int{11, 1}
}

func (x *GetRiskyConnectionsResponse_ReferralInfo) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRiskyConnectionsResponse_ReferralInfo) GetAccountActionDetails() *AccountActionDetails {
	if x != nil {
		return x.AccountActionDetails
	}
	return nil
}

func (x *GetRiskyConnectionsResponse_ReferralInfo) GetLeaDetails() *LEADetails {
	if x != nil {
		return x.LeaDetails
	}
	return nil
}

var File_api_risk_profile_service_proto protoreflect.FileDescriptor

var file_api_risk_profile_service_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0c, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x1a, 0x1f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x27, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69,
	0x73, 0x6b, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x28, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x65, 0x61, 0x2f, 0x75, 0x6e,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3b, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xb3, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x22, 0x8f, 0x03, 0x0a,
	0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x15, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x13, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x46,
	0x72, 0x65, 0x65, 0x7a, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x52, 0x0a, 0x17, 0x69,
	0x6d, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x72, 0x65, 0x65,
	0x7a, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x15, 0x69, 0x6d, 0x70, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x75, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x55, 0x6e, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x42, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x66, 0x6c, 0x61, 0x67, 0x67, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6c, 0x61,
	0x73, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x67, 0x65, 0x64, 0x41, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x66,
	0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69,
	0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x22, 0x60,
	0x0a, 0x1d, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x22, 0xc4, 0x02, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x63, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x62, 0x0a,
	0x11, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x37, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41,
	0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xa3, 0x01, 0x0a, 0x0d, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a, 0x0e, 0x70, 0x72, 0x65,
	0x73, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1a, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x22, 0xe7, 0x01,
	0x0a, 0x0d, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2c, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x44, 0x0a, 0x10, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x62,
	0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x22, 0xdf, 0x01, 0x0a, 0x10, 0x4c, 0x45, 0x41, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x46, 0x0a, 0x11,
	0x6c, 0x65, 0x61, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0f, 0x6c, 0x65, 0x61, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x42, 0x0a, 0x0f, 0x6c, 0x65, 0x61, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6c, 0x65, 0x61, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xae, 0x02, 0x0a, 0x08, 0x46, 0x6f,
	0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x6f, 0x72, 0x6d, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x06, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66,
	0x6f, 0x72, 0x6d, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x52, 0x06,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x37, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x41, 0x74, 0x22, 0x8a, 0x05, 0x0a, 0x13, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x31, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0c, 0x70, 0x61, 0x73, 0x74,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x70, 0x61, 0x73,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x6c, 0x65, 0x61, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x4c, 0x45, 0x41, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x09, 0x6c, 0x65, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x69,
	0x73, 0x5f, 0x75, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x55, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x12, 0x2c, 0x0a, 0x05, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x66, 0x6f, 0x72, 0x6d,
	0x73, 0x12, 0x5d, 0x0a, 0x1a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x18, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x4f, 0x0a, 0x16, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x6c, 0x65, 0x61, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c,
	0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x14, 0x75, 0x6e, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74,
	0x73, 0x12, 0x54, 0x0a, 0x17, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x66,
	0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x65, 0x64, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x52, 0x15, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x46, 0x72, 0x65, 0x65, 0x7a,
	0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xcf, 0x02, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x52,
	0x69, 0x73, 0x6b, 0x79, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x7d, 0x0a, 0x16, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x6d, 0x61, 0x73, 0x6b, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73,
	0x6b, 0x79, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x08, 0x01, 0x52, 0x14, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x22, 0x8d, 0x01, 0x0a, 0x14, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x53, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x53, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x41,
	0x53, 0x53, 0x4f, 0x43, 0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x01, 0x12, 0x2a, 0x0a,
	0x26, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x4f, 0x43,
	0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x02, 0x22, 0xa2, 0x07, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x52, 0x69, 0x73, 0x6b, 0x79, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x7c,
	0x0a, 0x1b, 0x69, 0x6e, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x79, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x19, 0x69, 0x6e, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x7e, 0x0a, 0x1c,
	0x6f, 0x75, 0x74, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f,
	0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x79, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x1a, 0x6f, 0x75, 0x74, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x5b, 0x0a, 0x0d,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x79, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5b, 0x0a, 0x0d, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x79, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0xc4, 0x01, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x58, 0x0a, 0x16, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x39, 0x0a, 0x0b, 0x6c, 0x65, 0x61, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x4c, 0x45, 0x41, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x0a, 0x6c, 0x65, 0x61, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0xbe, 0x01,
	0x0a, 0x0c, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x58, 0x0a, 0x16, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x39, 0x0a, 0x0b, 0x6c, 0x65, 0x61, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x4c, 0x45, 0x41, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x0a, 0x6c, 0x65, 0x61, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x1e,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xa8,
	0x01, 0x0a, 0x14, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3b, 0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x6e, 0x65,
	0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72,
	0x65, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x69, 0x73, 0x4e,
	0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x65,
	0x73, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x84, 0x01, 0x0a, 0x0a, 0x4c, 0x45,
	0x41, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x6c,
	0x65, 0x61, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0c, 0x69, 0x73, 0x4c, 0x65, 0x61, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x50,
	0x0a, 0x16, 0x6c, 0x65, 0x61, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x6c, 0x65, 0x61, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x32, 0xc4, 0x03, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x5b, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x23,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x16, 0x47, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x12, 0x2b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b,
	0x0a, 0x14, 0x46, 0x65, 0x74, 0x63, 0x68, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x42,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x30, 0x2e, 0x63, 0x78, 0x2e, 0x73, 0x68, 0x65, 0x72,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x2e, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x63, 0x78, 0x2e, 0x73, 0x68,
	0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x2e, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x79, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x28, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x79, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x69, 0x73, 0x6b, 0x79, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x52, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_risk_profile_service_proto_rawDescOnce sync.Once
	file_api_risk_profile_service_proto_rawDescData = file_api_risk_profile_service_proto_rawDesc
)

func file_api_risk_profile_service_proto_rawDescGZIP() []byte {
	file_api_risk_profile_service_proto_rawDescOnce.Do(func() {
		file_api_risk_profile_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_profile_service_proto_rawDescData)
	})
	return file_api_risk_profile_service_proto_rawDescData
}

var file_api_risk_profile_service_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_risk_profile_service_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_api_risk_profile_service_proto_goTypes = []interface{}{
	(GetUserProfileResponse_Status)(0),                     // 0: risk.profile.GetUserProfileResponse.Status
	(GetDetailedUserProfileResponse_Status)(0),             // 1: risk.profile.GetDetailedUserProfileResponse.Status
	(GetRiskyConnectionsRequest_ConnectionsFieldMask)(0),   // 2: risk.profile.GetRiskyConnectionsRequest.ConnectionsFieldMask
	(GetRiskyConnectionsResponse_Status)(0),                // 3: risk.profile.GetRiskyConnectionsResponse.Status
	(*GetUserProfileRequest)(nil),                          // 4: risk.profile.GetUserProfileRequest
	(*GetUserProfileResponse)(nil),                         // 5: risk.profile.GetUserProfileResponse
	(*AccountInfo)(nil),                                    // 6: risk.profile.AccountInfo
	(*GetDetailedUserProfileRequest)(nil),                  // 7: risk.profile.GetDetailedUserProfileRequest
	(*GetDetailedUserProfileResponse)(nil),                 // 8: risk.profile.GetDetailedUserProfileResponse
	(*AccountStatus)(nil),                                  // 9: risk.profile.AccountStatus
	(*AccountAction)(nil),                                  // 10: risk.profile.AccountAction
	(*LEAComplaintInfo)(nil),                               // 11: risk.profile.LEAComplaintInfo
	(*FormInfo)(nil),                                       // 12: risk.profile.FormInfo
	(*DetailedAccountInfo)(nil),                            // 13: risk.profile.DetailedAccountInfo
	(*GetRiskyConnectionsRequest)(nil),                     // 14: risk.profile.GetRiskyConnectionsRequest
	(*GetRiskyConnectionsResponse)(nil),                    // 15: risk.profile.GetRiskyConnectionsResponse
	(*AccountActionDetails)(nil),                           // 16: risk.profile.AccountActionDetails
	(*LEADetails)(nil),                                     // 17: risk.profile.LEADetails
	nil,                                                    // 18: risk.profile.GetDetailedUserProfileResponse.AccountsInfoEntry
	(*GetRiskyConnectionsResponse_ContactAssociation)(nil), // 19: risk.profile.GetRiskyConnectionsResponse.ContactAssociation
	(*GetRiskyConnectionsResponse_ReferralInfo)(nil),       // 20: risk.profile.GetRiskyConnectionsResponse.ReferralInfo
	(*rpc.Status)(nil),                                     // 21: rpc.Status
	(enums.AccountFreezeStatus)(0),                         // 22: enums.AccountFreezeStatus
	(*timestamppb.Timestamp)(nil),                          // 23: google.protobuf.Timestamp
	(enums.RequestReason)(0),                               // 24: enums.RequestReason
	(enums.Action)(0),                                      // 25: enums.Action
	(form.Status)(0),                                       // 26: risk.case_management.form.Status
	(form.FormOrigin)(0),                                   // 27: risk.case_management.form.FormOrigin
	(accounts.Type)(0),                                     // 28: accounts.Type
	(enums.AccountOperationalStatus)(0),                    // 29: enums.AccountOperationalStatus
	(*lea.UnifiedLeaComplaint)(nil),                        // 30: risk.UnifiedLeaComplaint
	(enums.ProcessedFreezeReason)(0),                       // 31: enums.ProcessedFreezeReason
	(*sherlock_banners.FetchSherlockBannersRequest)(nil),   // 32: cx.sherlock_banners.FetchSherlockBannersRequest
	(*sherlock_banners.FetchSherlockBannersResponse)(nil),  // 33: cx.sherlock_banners.FetchSherlockBannersResponse
}
var file_api_risk_profile_service_proto_depIdxs = []int32{
	21, // 0: risk.profile.GetUserProfileResponse.status:type_name -> rpc.Status
	6,  // 1: risk.profile.GetUserProfileResponse.accounts_info:type_name -> risk.profile.AccountInfo
	22, // 2: risk.profile.AccountInfo.present_freeze_status:type_name -> enums.AccountFreezeStatus
	22, // 3: risk.profile.AccountInfo.impending_freeze_status:type_name -> enums.AccountFreezeStatus
	23, // 4: risk.profile.AccountInfo.last_flagged_at:type_name -> google.protobuf.Timestamp
	21, // 5: risk.profile.GetDetailedUserProfileResponse.status:type_name -> rpc.Status
	18, // 6: risk.profile.GetDetailedUserProfileResponse.accounts_info:type_name -> risk.profile.GetDetailedUserProfileResponse.AccountsInfoEntry
	22, // 7: risk.profile.AccountStatus.present_status:type_name -> enums.AccountFreezeStatus
	24, // 8: risk.profile.AccountAction.reason:type_name -> enums.RequestReason
	23, // 9: risk.profile.AccountAction.bank_action_date:type_name -> google.protobuf.Timestamp
	25, // 10: risk.profile.AccountAction.action:type_name -> enums.Action
	22, // 11: risk.profile.LEAComplaintInfo.lea_freeze_status:type_name -> enums.AccountFreezeStatus
	23, // 12: risk.profile.LEAComplaintInfo.lea_action_date:type_name -> google.protobuf.Timestamp
	26, // 13: risk.profile.FormInfo.status:type_name -> risk.case_management.form.Status
	27, // 14: risk.profile.FormInfo.origin:type_name -> risk.case_management.form.FormOrigin
	23, // 15: risk.profile.FormInfo.created_at:type_name -> google.protobuf.Timestamp
	23, // 16: risk.profile.FormInfo.expire_at:type_name -> google.protobuf.Timestamp
	28, // 17: risk.profile.DetailedAccountInfo.account_type:type_name -> accounts.Type
	9,  // 18: risk.profile.DetailedAccountInfo.account_status:type_name -> risk.profile.AccountStatus
	10, // 19: risk.profile.DetailedAccountInfo.past_actions:type_name -> risk.profile.AccountAction
	11, // 20: risk.profile.DetailedAccountInfo.lea_status:type_name -> risk.profile.LEAComplaintInfo
	12, // 21: risk.profile.DetailedAccountInfo.forms:type_name -> risk.profile.FormInfo
	29, // 22: risk.profile.DetailedAccountInfo.account_operational_status:type_name -> enums.AccountOperationalStatus
	30, // 23: risk.profile.DetailedAccountInfo.unified_lea_complaints:type_name -> risk.UnifiedLeaComplaint
	31, // 24: risk.profile.DetailedAccountInfo.processed_freeze_reason:type_name -> enums.ProcessedFreezeReason
	2,  // 25: risk.profile.GetRiskyConnectionsRequest.connections_field_mask:type_name -> risk.profile.GetRiskyConnectionsRequest.ConnectionsFieldMask
	21, // 26: risk.profile.GetRiskyConnectionsResponse.status:type_name -> rpc.Status
	19, // 27: risk.profile.GetRiskyConnectionsResponse.inward_contact_associations:type_name -> risk.profile.GetRiskyConnectionsResponse.ContactAssociation
	19, // 28: risk.profile.GetRiskyConnectionsResponse.outward_contact_associations:type_name -> risk.profile.GetRiskyConnectionsResponse.ContactAssociation
	20, // 29: risk.profile.GetRiskyConnectionsResponse.referrer_info:type_name -> risk.profile.GetRiskyConnectionsResponse.ReferralInfo
	20, // 30: risk.profile.GetRiskyConnectionsResponse.referees_info:type_name -> risk.profile.GetRiskyConnectionsResponse.ReferralInfo
	25, // 31: risk.profile.AccountActionDetails.action:type_name -> enums.Action
	24, // 32: risk.profile.AccountActionDetails.reason:type_name -> enums.RequestReason
	23, // 33: risk.profile.LEADetails.lea_reported_timestamp:type_name -> google.protobuf.Timestamp
	13, // 34: risk.profile.GetDetailedUserProfileResponse.AccountsInfoEntry.value:type_name -> risk.profile.DetailedAccountInfo
	16, // 35: risk.profile.GetRiskyConnectionsResponse.ContactAssociation.account_action_details:type_name -> risk.profile.AccountActionDetails
	17, // 36: risk.profile.GetRiskyConnectionsResponse.ContactAssociation.lea_details:type_name -> risk.profile.LEADetails
	16, // 37: risk.profile.GetRiskyConnectionsResponse.ReferralInfo.account_action_details:type_name -> risk.profile.AccountActionDetails
	17, // 38: risk.profile.GetRiskyConnectionsResponse.ReferralInfo.lea_details:type_name -> risk.profile.LEADetails
	4,  // 39: risk.profile.Profile.GetUserProfile:input_type -> risk.profile.GetUserProfileRequest
	7,  // 40: risk.profile.Profile.GetDetailedUserProfile:input_type -> risk.profile.GetDetailedUserProfileRequest
	32, // 41: risk.profile.Profile.FetchSherlockBanners:input_type -> cx.sherlock_banners.FetchSherlockBannersRequest
	14, // 42: risk.profile.Profile.GetRiskyConnections:input_type -> risk.profile.GetRiskyConnectionsRequest
	5,  // 43: risk.profile.Profile.GetUserProfile:output_type -> risk.profile.GetUserProfileResponse
	8,  // 44: risk.profile.Profile.GetDetailedUserProfile:output_type -> risk.profile.GetDetailedUserProfileResponse
	33, // 45: risk.profile.Profile.FetchSherlockBanners:output_type -> cx.sherlock_banners.FetchSherlockBannersResponse
	15, // 46: risk.profile.Profile.GetRiskyConnections:output_type -> risk.profile.GetRiskyConnectionsResponse
	43, // [43:47] is the sub-list for method output_type
	39, // [39:43] is the sub-list for method input_type
	39, // [39:39] is the sub-list for extension type_name
	39, // [39:39] is the sub-list for extension extendee
	0,  // [0:39] is the sub-list for field type_name
}

func init() { file_api_risk_profile_service_proto_init() }
func file_api_risk_profile_service_proto_init() {
	if File_api_risk_profile_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_profile_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserProfileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDetailedUserProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDetailedUserProfileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LEAComplaintInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailedAccountInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRiskyConnectionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRiskyConnectionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountActionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LEADetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRiskyConnectionsResponse_ContactAssociation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_profile_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRiskyConnectionsResponse_ReferralInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_profile_service_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_risk_profile_service_proto_goTypes,
		DependencyIndexes: file_api_risk_profile_service_proto_depIdxs,
		EnumInfos:         file_api_risk_profile_service_proto_enumTypes,
		MessageInfos:      file_api_risk_profile_service_proto_msgTypes,
	}.Build()
	File_api_risk_profile_service_proto = out.File
	file_api_risk_profile_service_proto_rawDesc = nil
	file_api_risk_profile_service_proto_goTypes = nil
	file_api_risk_profile_service_proto_depIdxs = nil
}
