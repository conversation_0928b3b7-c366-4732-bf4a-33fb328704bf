package profile

import (
	accountsPb "github.com/epifi/gamma/api/accounts"
	enumsPb "github.com/epifi/gamma/api/risk/enums"

	"github.com/samber/lo"
)

var (
	// Freeze code 57: Federal suggested block for profile discrepancy/suspicious transactions.
	federalSuggestedBlockFreezeCode = "57"
	vkycFreezeCodes                 = []string{"54", "60", "61", "62"}
)

func (a *GetDetailedUserProfileResponse) IsCreditFrozen() bool {
	savingsInfo := a.GetSavingsInfo()
	if savingsInfo == nil {
		return false
	}

	// return credit frozen true only when account status matches too
	return savingsInfo.isCreditFrozenInPastActionAndCurrentStatus()
}

// ShouldPlayCallRecording returns true if we should play call recording for risk frozen accounts
// Also handling edge cases like returning false for VKYC freezes
func (a *GetDetailedUserProfileResponse) ShouldPlayCallRecording() bool {
	savingsInfo := a.GetSavingsInfo()

	if savingsInfo == nil {
		return false
	}

	// ignore the case for VKYC freeze
	if IsVKYCFreeze(savingsInfo.GetAccountStatus().GetFreezeReason()) {
		return false
	}

	if IsFederalSuggestedFreeze(savingsInfo.GetAccountStatus().GetPresentStatus(), savingsInfo.GetAccountStatus().GetFreezeReason()) {
		return true
	}

	// return true only when account status matches with credit freeze
	return savingsInfo.isCreditFrozenInPastActionAndCurrentStatus()

}

func (a *GetDetailedUserProfileResponse) GetSavingsInfo() *DetailedAccountInfo {
	// considering only savings account object
	for _, accInfo := range a.GetAccountsInfo() {
		if accInfo.GetAccountType() == accountsPb.Type_SAVINGS {
			return accInfo
		}
	}

	return nil
}

func (d *DetailedAccountInfo) isCreditFrozenInPastActionAndCurrentStatus() bool {
	if pastActionLen := len(d.PastActions); pastActionLen != 0 {
		if d.PastActions[0].IsCompleted && d.PastActions[0].GetAction() == enumsPb.Action_ACTION_CREDIT_FREEZE &&
			d.GetAccountStatus().GetPresentStatus() == enumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE {
			return true
		}
	}
	return false
}

func IsFederalSuggestedFreeze(presentStatus enumsPb.AccountFreezeStatus, freezeReasons []string) bool {
	if presentStatus == enumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE &&
		lo.Contains(freezeReasons, federalSuggestedBlockFreezeCode) {
		return true
	}
	return false
}

func IsVKYCFreeze(freezeReasons []string) bool {
	for _, reason := range freezeReasons {
		if lo.Contains(vkycFreezeCodes, reason) {
			return true
		}
	}
	return false
}

func IsUserBlockedByRiskForCX(resp *GetDetailedUserProfileResponse) (bool, enumsPb.AccountFreezeStatus) {
	for _, accountDetails := range resp.GetAccountsInfo() {
		if accountDetails.GetAccountType() == accountsPb.Type_SAVINGS {
			if accountDetails.GetAccountStatus().GetPresentStatus() != enumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_UNFROZEN &&
				accountDetails.GetAccountStatus().GetPresentStatus() != enumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_UNSPECIFIED {
				return true, accountDetails.GetAccountStatus().GetPresentStatus()
			}
		}
	}
	return false, enumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_UNSPECIFIED
}
