package profile

import (
	"testing"

	enumsPb "github.com/epifi/gamma/api/risk/enums"
)

var (
	cfAccountInfoWithCfPastActions = map[string]*DetailedAccountInfo{
		"acc1": {
			AccountId:   "acc1",
			AccountType: 1,
			AccountStatus: &AccountStatus{
				PresentStatus: enumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE,
			},
			PastActions: []*AccountAction{
				{
					Action:      enumsPb.Action_ACTION_CREDIT_FREEZE,
					IsCompleted: true,
				},
			},
			LeaStatus:     nil,
			IsUnderReview: false,
		},
	}

	ufAccountInfoUnfCurrentStatus = map[string]*DetailedAccountInfo{
		"acc1": {
			AccountId:   "acc1",
			AccountType: 1,
			AccountStatus: &AccountStatus{
				PresentStatus: enumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_UNFROZEN,
			},
			PastActions: []*AccountAction{
				{
					Action:      enumsPb.Action_ACTION_CREDIT_FREEZE,
					IsCompleted: true,
				},
			},
			LeaStatus:     nil,
			IsUnderReview: false,
		},
	}

	cfAccountInfoUfPastActions = map[string]*DetailedAccountInfo{
		"acc1": {
			AccountId:   "acc1",
			AccountType: 1,
			AccountStatus: &AccountStatus{
				PresentStatus: enumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE,
			},
			PastActions: []*AccountAction{
				{
					Action:      enumsPb.Action_ACTION_UNFREEZE,
					IsCompleted: true,
				},
			},
			LeaStatus:     nil,
			IsUnderReview: false,
		},
	}

	accountInfoWithLea = map[string]*DetailedAccountInfo{
		"acc1": {
			AccountId:   "acc1",
			AccountType: 1,
			LeaStatus: []*LEAComplaintInfo{
				{
					LeaFreezeStatus: 1,
				},
			},
			IsUnderReview: false,
		},
	}

	accountInfoWithVKYCFreeze = map[string]*DetailedAccountInfo{
		"acc1": {
			AccountId:   "acc1",
			AccountType: 1,
			AccountStatus: &AccountStatus{
				FreezeReason: []string{"60"},
			},
			PastActions: []*AccountAction{
				{
					Action:      enumsPb.Action_ACTION_CREDIT_FREEZE,
					IsCompleted: true,
				},
			},
			IsUnderReview: false,
		},
	}
	accountInfoWithNoVKYCFreeze = map[string]*DetailedAccountInfo{
		"acc1": {
			AccountId:   "acc1",
			AccountType: 1,
			AccountStatus: &AccountStatus{
				FreezeReason:  []string{"11"},
				PresentStatus: enumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE,
			},
			PastActions: []*AccountAction{
				{
					Action:      enumsPb.Action_ACTION_CREDIT_FREEZE,
					IsCompleted: true,
				},
			},
			IsUnderReview: false,
		},
	}
)

func TestGetDetailedUserProfileResponse_ShouldPlayCallRecording(t *testing.T) {

	type fields struct {
		AccountsInfo map[string]*DetailedAccountInfo
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "failed with no account status found",
			want: false,
		},
		{
			name: "success checking for VKYC freeze",
			fields: fields{
				AccountsInfo: accountInfoWithVKYCFreeze,
			},
			want: false,
		},
		{
			name: "success checking for no VKYC freeze",
			fields: fields{
				AccountsInfo: accountInfoWithNoVKYCFreeze,
			},
			want: true,
		},
		{
			name: "success account unfrozen but rba credit frozen",
			fields: fields{
				AccountsInfo: ufAccountInfoUnfCurrentStatus,
			},
			want: false,
		},
		{
			name: "success account CF but rba unfrozen",
			fields: fields{
				AccountsInfo: cfAccountInfoUfPastActions,
			},
			want: false,
		},
		{
			name: "success parsing past actions",
			fields: fields{
				AccountsInfo: cfAccountInfoWithCfPastActions,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &GetDetailedUserProfileResponse{
				AccountsInfo: tt.fields.AccountsInfo,
			}
			if got := a.ShouldPlayCallRecording(); got != tt.want {
				t.Errorf("ShouldPlayCallRecording() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetDetailedUserProfileResponse_IsCreditFrozen(t *testing.T) {
	type fields struct {
		AccountsInfo map[string]*DetailedAccountInfo
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "failed with no account status found",
			want: false,
		},
		{
			name: "false with lea present",
			fields: fields{
				AccountsInfo: accountInfoWithLea,
			},
			want: false,
		},
		{
			name: "success account unfrozen but rba credit frozen",
			fields: fields{
				AccountsInfo: ufAccountInfoUnfCurrentStatus,
			},
			want: false,
		},
		{
			name: "success account CF but rba unfrozen",
			fields: fields{
				AccountsInfo: cfAccountInfoUfPastActions,
			},
			want: false,
		},
		{
			name: "true due to credit freeze past action",
			fields: fields{
				AccountsInfo: cfAccountInfoWithCfPastActions,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &GetDetailedUserProfileResponse{
				AccountsInfo: tt.fields.AccountsInfo,
			}
			if got := a.IsCreditFrozen(); got != tt.want {
				t.Errorf("IsCreditFrozen() = %v, want %v", got, tt.want)
			}
		})
	}
}
