//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/consumer.proto

package risk

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	queue "github.com/epifi/be-common/api/queue"
	s3 "github.com/epifi/gamma/api/aws/s3"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessRedListUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// single record represents a single RedListUpdate file event
	Records []*s3.Record `protobuf:"bytes,2,rep,name=records,json=Records,proto3" json:"records,omitempty"`
}

func (x *ProcessRedListUpdateRequest) Reset() {
	*x = ProcessRedListUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_consumer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessRedListUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessRedListUpdateRequest) ProtoMessage() {}

func (x *ProcessRedListUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_consumer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessRedListUpdateRequest.ProtoReflect.Descriptor instead.
func (*ProcessRedListUpdateRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_consumer_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessRedListUpdateRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessRedListUpdateRequest) GetRecords() []*s3.Record {
	if x != nil {
		return x.Records
	}
	return nil
}

type ProcessRedListUpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessRedListUpdateResponse) Reset() {
	*x = ProcessRedListUpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_consumer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessRedListUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessRedListUpdateResponse) ProtoMessage() {}

func (x *ProcessRedListUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_consumer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessRedListUpdateResponse.ProtoReflect.Descriptor instead.
func (*ProcessRedListUpdateResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_consumer_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessRedListUpdateResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type DisputeUploadEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Some fields such as transaction id will be empty and will need to be pe populated.
	Dispute *Dispute `protobuf:"bytes,2,opt,name=dispute,proto3" json:"dispute,omitempty"`
	// Additional Raw dispute details received from federal.
	RawDispute *RawDispute `protobuf:"bytes,3,opt,name=raw_dispute,json=rawDispute,proto3" json:"raw_dispute,omitempty"`
}

func (x *DisputeUploadEvent) Reset() {
	*x = DisputeUploadEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_consumer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisputeUploadEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisputeUploadEvent) ProtoMessage() {}

func (x *DisputeUploadEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_consumer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisputeUploadEvent.ProtoReflect.Descriptor instead.
func (*DisputeUploadEvent) Descriptor() ([]byte, []int) {
	return file_api_risk_consumer_proto_rawDescGZIP(), []int{2}
}

func (x *DisputeUploadEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *DisputeUploadEvent) GetDispute() *Dispute {
	if x != nil {
		return x.Dispute
	}
	return nil
}

func (x *DisputeUploadEvent) GetRawDispute() *RawDispute {
	if x != nil {
		return x.RawDispute
	}
	return nil
}

type ProcessDisputeUploadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessDisputeUploadResponse) Reset() {
	*x = ProcessDisputeUploadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_consumer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessDisputeUploadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessDisputeUploadResponse) ProtoMessage() {}

func (x *ProcessDisputeUploadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_consumer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessDisputeUploadResponse.ProtoReflect.Descriptor instead.
func (*ProcessDisputeUploadResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_consumer_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessDisputeUploadResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type RawDispute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountNo string `protobuf:"bytes,1,opt,name=account_no,json=accountNo,proto3" json:"account_no,omitempty"`
}

func (x *RawDispute) Reset() {
	*x = RawDispute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_consumer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RawDispute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawDispute) ProtoMessage() {}

func (x *RawDispute) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_consumer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawDispute.ProtoReflect.Descriptor instead.
func (*RawDispute) Descriptor() ([]byte, []int) {
	return file_api_risk_consumer_proto_rawDescGZIP(), []int{4}
}

func (x *RawDispute) GetAccountNo() string {
	if x != nil {
		return x.AccountNo
	}
	return ""
}

var File_api_risk_consumer_proto protoreflect.FileDescriptor

var file_api_risk_consumer_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x72, 0x69, 0x73, 0x6b, 0x1a,
	0x13, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x77, 0x73, 0x2f, 0x73, 0x33, 0x2f, 0x73, 0x33, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x8c, 0x01, 0x0a, 0x1b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x73, 0x33, 0x2e,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22,
	0x66, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xc9, 0x01, 0x0a, 0x12, 0x44, 0x69, 0x73, 0x70,
	0x75, 0x74, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x43,
	0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x70,
	0x75, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x64,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x69,
	0x73, 0x70, 0x75, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x52, 0x61, 0x77, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x72, 0x61, 0x77, 0x44, 0x69, 0x73, 0x70,
	0x75, 0x74, 0x65, 0x22, 0x66, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x44, 0x69,
	0x73, 0x70, 0x75, 0x74, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x34, 0x0a, 0x0a, 0x52,
	0x61, 0x77, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x6f, 0x32, 0xc3, 0x01, 0x0a, 0x0c, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x12, 0x5d, 0x0a, 0x14, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x21, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x54, 0x0a, 0x14, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x44, 0x69, 0x73, 0x70,
	0x75, 0x74, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x18, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x1a, 0x22, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_risk_consumer_proto_rawDescOnce sync.Once
	file_api_risk_consumer_proto_rawDescData = file_api_risk_consumer_proto_rawDesc
)

func file_api_risk_consumer_proto_rawDescGZIP() []byte {
	file_api_risk_consumer_proto_rawDescOnce.Do(func() {
		file_api_risk_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_consumer_proto_rawDescData)
	})
	return file_api_risk_consumer_proto_rawDescData
}

var file_api_risk_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_risk_consumer_proto_goTypes = []interface{}{
	(*ProcessRedListUpdateRequest)(nil),  // 0: risk.ProcessRedListUpdateRequest
	(*ProcessRedListUpdateResponse)(nil), // 1: risk.ProcessRedListUpdateResponse
	(*DisputeUploadEvent)(nil),           // 2: risk.DisputeUploadEvent
	(*ProcessDisputeUploadResponse)(nil), // 3: risk.ProcessDisputeUploadResponse
	(*RawDispute)(nil),                   // 4: risk.RawDispute
	(*queue.ConsumerRequestHeader)(nil),  // 5: queue.ConsumerRequestHeader
	(*s3.Record)(nil),                    // 6: aws.s3.Record
	(*queue.ConsumerResponseHeader)(nil), // 7: queue.ConsumerResponseHeader
	(*Dispute)(nil),                      // 8: risk.Dispute
}
var file_api_risk_consumer_proto_depIdxs = []int32{
	5, // 0: risk.ProcessRedListUpdateRequest.request_header:type_name -> queue.ConsumerRequestHeader
	6, // 1: risk.ProcessRedListUpdateRequest.records:type_name -> aws.s3.Record
	7, // 2: risk.ProcessRedListUpdateResponse.response_header:type_name -> queue.ConsumerResponseHeader
	5, // 3: risk.DisputeUploadEvent.request_header:type_name -> queue.ConsumerRequestHeader
	8, // 4: risk.DisputeUploadEvent.dispute:type_name -> risk.Dispute
	4, // 5: risk.DisputeUploadEvent.raw_dispute:type_name -> risk.RawDispute
	7, // 6: risk.ProcessDisputeUploadResponse.response_header:type_name -> queue.ConsumerResponseHeader
	0, // 7: risk.RiskConsumer.ProcessRedListUpdate:input_type -> risk.ProcessRedListUpdateRequest
	2, // 8: risk.RiskConsumer.ProcessDisputeUpload:input_type -> risk.DisputeUploadEvent
	1, // 9: risk.RiskConsumer.ProcessRedListUpdate:output_type -> risk.ProcessRedListUpdateResponse
	3, // 10: risk.RiskConsumer.ProcessDisputeUpload:output_type -> risk.ProcessDisputeUploadResponse
	9, // [9:11] is the sub-list for method output_type
	7, // [7:9] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_risk_consumer_proto_init() }
func file_api_risk_consumer_proto_init() {
	if File_api_risk_consumer_proto != nil {
		return
	}
	file_api_risk_internal_dispute_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_consumer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessRedListUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_consumer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessRedListUpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_consumer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisputeUploadEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_consumer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessDisputeUploadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_consumer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RawDispute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_consumer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_risk_consumer_proto_goTypes,
		DependencyIndexes: file_api_risk_consumer_proto_depIdxs,
		MessageInfos:      file_api_risk_consumer_proto_msgTypes,
	}.Build()
	File_api_risk_consumer_proto = out.File
	file_api_risk_consumer_proto_rawDesc = nil
	file_api_risk_consumer_proto_goTypes = nil
	file_api_risk_consumer_proto_depIdxs = nil
}
