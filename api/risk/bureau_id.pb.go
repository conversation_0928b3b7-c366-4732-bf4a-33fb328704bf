//go:generate gen_sql -types=PhoneNetworkAttributes,PhoneAttributes,PhoneNameAttributes,EmailAttributes,EmailNameAttributes,PhoneSocialAttributes,EmailSocialAttributes,RiskAttributes

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/bureau_id.proto

package risk

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AttributeStatus represents the status of particular service i.e. PhoneNetworkAttributes, PhoneAttributes, etc.
type AttributeStatus int32

const (
	AttributeStatus_ATTRIBUTE_STATUS_OK     AttributeStatus = 0
	AttributeStatus_ATTRIBUTE_STATUS_FAILED AttributeStatus = 1
	// NOT_FOUND indicates attribute not found
	AttributeStatus_ATTRIBUTE_STATUS_NOT_FOUND AttributeStatus = 2
	// NOT_EXECUTED indicates that the attribute service was not executed by the vendor.
	// In such cases, retries can be performed using the same request_id
	AttributeStatus_ATTRIBUTE_STATUS_NOT_EXECUTED AttributeStatus = 3
)

// Enum value maps for AttributeStatus.
var (
	AttributeStatus_name = map[int32]string{
		0: "ATTRIBUTE_STATUS_OK",
		1: "ATTRIBUTE_STATUS_FAILED",
		2: "ATTRIBUTE_STATUS_NOT_FOUND",
		3: "ATTRIBUTE_STATUS_NOT_EXECUTED",
	}
	AttributeStatus_value = map[string]int32{
		"ATTRIBUTE_STATUS_OK":           0,
		"ATTRIBUTE_STATUS_FAILED":       1,
		"ATTRIBUTE_STATUS_NOT_FOUND":    2,
		"ATTRIBUTE_STATUS_NOT_EXECUTED": 3,
	}
)

func (x AttributeStatus) Enum() *AttributeStatus {
	p := new(AttributeStatus)
	*p = x
	return p
}

func (x AttributeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AttributeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_bureau_id_proto_enumTypes[0].Descriptor()
}

func (AttributeStatus) Type() protoreflect.EnumType {
	return &file_api_risk_bureau_id_proto_enumTypes[0]
}

func (x AttributeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AttributeStatus.Descriptor instead.
func (AttributeStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_bureau_id_proto_rawDescGZIP(), []int{0}
}

type BureauIdRiskDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary key
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// request_id is the unique identifier generated by the vendor and included in the response
	// request_id allows retrieval of data generated at various time intervals
	// without incurring additional costs. This supports efficient polling by referencing
	// the same request_id.
	RequestId string              `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	ActorId   string              `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Name      string              `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Email     string              `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	Phone     *common.PhoneNumber `protobuf:"bytes,6,opt,name=phone,proto3" json:"phone,omitempty"`
	Services  *Services           `protobuf:"bytes,7,opt,name=services,proto3" json:"services,omitempty"`
	// entire raw json response of bureau.id vendor api call
	RawResponse string                 `protobuf:"bytes,8,opt,name=raw_response,json=rawResponse,proto3" json:"raw_response,omitempty"`
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt   *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *BureauIdRiskDetail) Reset() {
	*x = BureauIdRiskDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_bureau_id_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BureauIdRiskDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BureauIdRiskDetail) ProtoMessage() {}

func (x *BureauIdRiskDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_bureau_id_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BureauIdRiskDetail.ProtoReflect.Descriptor instead.
func (*BureauIdRiskDetail) Descriptor() ([]byte, []int) {
	return file_api_risk_bureau_id_proto_rawDescGZIP(), []int{0}
}

func (x *BureauIdRiskDetail) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BureauIdRiskDetail) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *BureauIdRiskDetail) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *BureauIdRiskDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BureauIdRiskDetail) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BureauIdRiskDetail) GetPhone() *common.PhoneNumber {
	if x != nil {
		return x.Phone
	}
	return nil
}

func (x *BureauIdRiskDetail) GetServices() *Services {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *BureauIdRiskDetail) GetRawResponse() string {
	if x != nil {
		return x.RawResponse
	}
	return ""
}

func (x *BureauIdRiskDetail) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BureauIdRiskDetail) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Services represents a collection of attributes and information, provided by the bureau.id
// these services gives all the information associated with user's name, phone number and email id.
type Services struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PhoneNetworkAttributes *PhoneNetworkAttributes `protobuf:"bytes,1,opt,name=phone_network_attributes,json=phoneNetworkAttributes,proto3" json:"phone_network_attributes,omitempty"`
	PhoneAttributes        *PhoneAttributes        `protobuf:"bytes,2,opt,name=phone_attributes,json=phoneAttributes,proto3" json:"phone_attributes,omitempty"`
	PhoneNameAttributes    *PhoneNameAttributes    `protobuf:"bytes,3,opt,name=phone_name_attributes,json=phoneNameAttributes,proto3" json:"phone_name_attributes,omitempty"`
	EmailAttributes        *EmailAttributes        `protobuf:"bytes,4,opt,name=email_attributes,json=emailAttributes,proto3" json:"email_attributes,omitempty"`
	EmailNameAttributes    *EmailNameAttributes    `protobuf:"bytes,5,opt,name=email_name_attributes,json=emailNameAttributes,proto3" json:"email_name_attributes,omitempty"`
	EmailSocialAttributes  *EmailSocialAttributes  `protobuf:"bytes,6,opt,name=email_social_attributes,json=emailSocialAttributes,proto3" json:"email_social_attributes,omitempty"`
	PhoneSocialAttributes  *PhoneSocialAttributes  `protobuf:"bytes,7,opt,name=phone_social_attributes,json=phoneSocialAttributes,proto3" json:"phone_social_attributes,omitempty"`
	RiskAttributes         *RiskAttributes         `protobuf:"bytes,8,opt,name=risk_attributes,json=riskAttributes,proto3" json:"risk_attributes,omitempty"`
}

func (x *Services) Reset() {
	*x = Services{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_bureau_id_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Services) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Services) ProtoMessage() {}

func (x *Services) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_bureau_id_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Services.ProtoReflect.Descriptor instead.
func (*Services) Descriptor() ([]byte, []int) {
	return file_api_risk_bureau_id_proto_rawDescGZIP(), []int{1}
}

func (x *Services) GetPhoneNetworkAttributes() *PhoneNetworkAttributes {
	if x != nil {
		return x.PhoneNetworkAttributes
	}
	return nil
}

func (x *Services) GetPhoneAttributes() *PhoneAttributes {
	if x != nil {
		return x.PhoneAttributes
	}
	return nil
}

func (x *Services) GetPhoneNameAttributes() *PhoneNameAttributes {
	if x != nil {
		return x.PhoneNameAttributes
	}
	return nil
}

func (x *Services) GetEmailAttributes() *EmailAttributes {
	if x != nil {
		return x.EmailAttributes
	}
	return nil
}

func (x *Services) GetEmailNameAttributes() *EmailNameAttributes {
	if x != nil {
		return x.EmailNameAttributes
	}
	return nil
}

func (x *Services) GetEmailSocialAttributes() *EmailSocialAttributes {
	if x != nil {
		return x.EmailSocialAttributes
	}
	return nil
}

func (x *Services) GetPhoneSocialAttributes() *PhoneSocialAttributes {
	if x != nil {
		return x.PhoneSocialAttributes
	}
	return nil
}

func (x *Services) GetRiskAttributes() *RiskAttributes {
	if x != nil {
		return x.RiskAttributes
	}
	return nil
}

// EmailAttributes covers attributes related to the email address.
type EmailAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributesStatus         AttributeStatus `protobuf:"varint,1,opt,name=attributes_status,json=attributesStatus,proto3,enum=risk.AttributeStatus" json:"attributes_status,omitempty"`
	EmailExists              bool            `protobuf:"varint,2,opt,name=email_exists,json=emailExists,proto3" json:"email_exists,omitempty"`
	DigitalAge               int32           `protobuf:"varint,3,opt,name=digital_age,json=digitalAge,proto3" json:"digital_age,omitempty"`
	DomainExists             bool            `protobuf:"varint,4,opt,name=domain_exists,json=domainExists,proto3" json:"domain_exists,omitempty"`
	DomainRiskLevel          string          `protobuf:"bytes,5,opt,name=domain_risk_level,json=domainRiskLevel,proto3" json:"domain_risk_level,omitempty"`
	DomainCategory           string          `protobuf:"bytes,6,opt,name=domain_category,json=domainCategory,proto3" json:"domain_category,omitempty"`
	DomainCorporate          string          `protobuf:"bytes,7,opt,name=domain_corporate,json=domainCorporate,proto3" json:"domain_corporate,omitempty"`
	UniqueHits               int32           `protobuf:"varint,8,opt,name=unique_hits,json=uniqueHits,proto3" json:"unique_hits,omitempty"`
	EmailFinalRecommendation string          `protobuf:"bytes,9,opt,name=email_final_recommendation,json=emailFinalRecommendation,proto3" json:"email_final_recommendation,omitempty"`
	LastVerificationDate     string          `protobuf:"bytes,10,opt,name=last_verification_date,json=lastVerificationDate,proto3" json:"last_verification_date,omitempty"`
	FirstVerificationDate    string          `protobuf:"bytes,11,opt,name=first_verification_date,json=firstVerificationDate,proto3" json:"first_verification_date,omitempty"`
}

func (x *EmailAttributes) Reset() {
	*x = EmailAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_bureau_id_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailAttributes) ProtoMessage() {}

func (x *EmailAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_bureau_id_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailAttributes.ProtoReflect.Descriptor instead.
func (*EmailAttributes) Descriptor() ([]byte, []int) {
	return file_api_risk_bureau_id_proto_rawDescGZIP(), []int{2}
}

func (x *EmailAttributes) GetAttributesStatus() AttributeStatus {
	if x != nil {
		return x.AttributesStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *EmailAttributes) GetEmailExists() bool {
	if x != nil {
		return x.EmailExists
	}
	return false
}

func (x *EmailAttributes) GetDigitalAge() int32 {
	if x != nil {
		return x.DigitalAge
	}
	return 0
}

func (x *EmailAttributes) GetDomainExists() bool {
	if x != nil {
		return x.DomainExists
	}
	return false
}

func (x *EmailAttributes) GetDomainRiskLevel() string {
	if x != nil {
		return x.DomainRiskLevel
	}
	return ""
}

func (x *EmailAttributes) GetDomainCategory() string {
	if x != nil {
		return x.DomainCategory
	}
	return ""
}

func (x *EmailAttributes) GetDomainCorporate() string {
	if x != nil {
		return x.DomainCorporate
	}
	return ""
}

func (x *EmailAttributes) GetUniqueHits() int32 {
	if x != nil {
		return x.UniqueHits
	}
	return 0
}

func (x *EmailAttributes) GetEmailFinalRecommendation() string {
	if x != nil {
		return x.EmailFinalRecommendation
	}
	return ""
}

func (x *EmailAttributes) GetLastVerificationDate() string {
	if x != nil {
		return x.LastVerificationDate
	}
	return ""
}

func (x *EmailAttributes) GetFirstVerificationDate() string {
	if x != nil {
		return x.FirstVerificationDate
	}
	return ""
}

// EmailNameAttributes includes attributes that verifies the relation between the email address and users profile.
type EmailNameAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributesStatus       AttributeStatus `protobuf:"varint,1,opt,name=attributes_status,json=attributesStatus,proto3,enum=risk.AttributeStatus" json:"attributes_status,omitempty"`
	Address                string          `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	DigitalAge             int32           `protobuf:"varint,3,opt,name=digital_age,json=digitalAge,proto3" json:"digital_age,omitempty"`
	PhoneNumber            int64           `protobuf:"varint,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	FirstNameMatch         bool            `protobuf:"varint,5,opt,name=first_name_match,json=firstNameMatch,proto3" json:"first_name_match,omitempty"`
	LastNameMatch          bool            `protobuf:"varint,6,opt,name=last_name_match,json=lastNameMatch,proto3" json:"last_name_match,omitempty"`
	BusinessNameDetected   bool            `protobuf:"varint,7,opt,name=business_name_detected,json=businessNameDetected,proto3" json:"business_name_detected,omitempty"`
	EmailFootprintStrength string          `protobuf:"bytes,8,opt,name=email_footprint_strength,json=emailFootprintStrength,proto3" json:"email_footprint_strength,omitempty"`
	EmailNameDigitalAge    int32           `protobuf:"varint,9,opt,name=email_name_digital_age,json=emailNameDigitalAge,proto3" json:"email_name_digital_age,omitempty"`
	EmailSpclChars         int32           `protobuf:"varint,10,opt,name=email_spcl_chars,json=emailSpclChars,proto3" json:"email_spcl_chars,omitempty"`
	// the maximum similarity score between names extracted from an email address and names provided in the request.
	NdrScore float64 `protobuf:"fixed64,11,opt,name=ndr_score,json=ndrScore,proto3" json:"ndr_score,omitempty"`
	// the ratio of number of names that were unmatched to the total number of names found in the request.
	UnrScore float64 `protobuf:"fixed64,12,opt,name=unr_score,json=unrScore,proto3" json:"unr_score,omitempty"`
	// represents at which extent the name matches with email
	NameMatchScore        float64 `protobuf:"fixed64,13,opt,name=name_match_score,json=nameMatchScore,proto3" json:"name_match_score,omitempty"`
	NameEmailMatch        int32   `protobuf:"varint,14,opt,name=name_email_match,json=nameEmailMatch,proto3" json:"name_email_match,omitempty"`
	MultiplePhoneAttached bool    `protobuf:"varint,15,opt,name=multiple_phone_attached,json=multiplePhoneAttached,proto3" json:"multiple_phone_attached,omitempty"`
}

func (x *EmailNameAttributes) Reset() {
	*x = EmailNameAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_bureau_id_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailNameAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailNameAttributes) ProtoMessage() {}

func (x *EmailNameAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_bureau_id_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailNameAttributes.ProtoReflect.Descriptor instead.
func (*EmailNameAttributes) Descriptor() ([]byte, []int) {
	return file_api_risk_bureau_id_proto_rawDescGZIP(), []int{3}
}

func (x *EmailNameAttributes) GetAttributesStatus() AttributeStatus {
	if x != nil {
		return x.AttributesStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *EmailNameAttributes) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *EmailNameAttributes) GetDigitalAge() int32 {
	if x != nil {
		return x.DigitalAge
	}
	return 0
}

func (x *EmailNameAttributes) GetPhoneNumber() int64 {
	if x != nil {
		return x.PhoneNumber
	}
	return 0
}

func (x *EmailNameAttributes) GetFirstNameMatch() bool {
	if x != nil {
		return x.FirstNameMatch
	}
	return false
}

func (x *EmailNameAttributes) GetLastNameMatch() bool {
	if x != nil {
		return x.LastNameMatch
	}
	return false
}

func (x *EmailNameAttributes) GetBusinessNameDetected() bool {
	if x != nil {
		return x.BusinessNameDetected
	}
	return false
}

func (x *EmailNameAttributes) GetEmailFootprintStrength() string {
	if x != nil {
		return x.EmailFootprintStrength
	}
	return ""
}

func (x *EmailNameAttributes) GetEmailNameDigitalAge() int32 {
	if x != nil {
		return x.EmailNameDigitalAge
	}
	return 0
}

func (x *EmailNameAttributes) GetEmailSpclChars() int32 {
	if x != nil {
		return x.EmailSpclChars
	}
	return 0
}

func (x *EmailNameAttributes) GetNdrScore() float64 {
	if x != nil {
		return x.NdrScore
	}
	return 0
}

func (x *EmailNameAttributes) GetUnrScore() float64 {
	if x != nil {
		return x.UnrScore
	}
	return 0
}

func (x *EmailNameAttributes) GetNameMatchScore() float64 {
	if x != nil {
		return x.NameMatchScore
	}
	return 0
}

func (x *EmailNameAttributes) GetNameEmailMatch() int32 {
	if x != nil {
		return x.NameEmailMatch
	}
	return 0
}

func (x *EmailNameAttributes) GetMultiplePhoneAttached() bool {
	if x != nil {
		return x.MultiplePhoneAttached
	}
	return false
}

// PhoneAttributes contains various attributes related to the phone number.
type PhoneAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributesStatus AttributeStatus `protobuf:"varint,1,opt,name=attributes_status,json=attributesStatus,proto3,enum=risk.AttributeStatus" json:"attributes_status,omitempty"`
	Name             string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Source           string          `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	Vpa              string          `protobuf:"bytes,4,opt,name=vpa,proto3" json:"vpa,omitempty"`
}

func (x *PhoneAttributes) Reset() {
	*x = PhoneAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_bureau_id_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhoneAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneAttributes) ProtoMessage() {}

func (x *PhoneAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_bureau_id_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneAttributes.ProtoReflect.Descriptor instead.
func (*PhoneAttributes) Descriptor() ([]byte, []int) {
	return file_api_risk_bureau_id_proto_rawDescGZIP(), []int{4}
}

func (x *PhoneAttributes) GetAttributesStatus() AttributeStatus {
	if x != nil {
		return x.AttributesStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *PhoneAttributes) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PhoneAttributes) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *PhoneAttributes) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

// PhoneNameAttributes provides attributes related to the phone number and name.
type PhoneNameAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributesStatus         AttributeStatus `protobuf:"varint,1,opt,name=attributes_status,json=attributesStatus,proto3,enum=risk.AttributeStatus" json:"attributes_status,omitempty"`
	Address                  string          `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	DigitalAge               int32           `protobuf:"varint,3,opt,name=digital_age,json=digitalAge,proto3" json:"digital_age,omitempty"`
	FirstNameMatch           bool            `protobuf:"varint,4,opt,name=first_name_match,json=firstNameMatch,proto3" json:"first_name_match,omitempty"`
	LastNameMatch            bool            `protobuf:"varint,5,opt,name=last_name_match,json=lastNameMatch,proto3" json:"last_name_match,omitempty"`
	BusinessNameDetected     bool            `protobuf:"varint,6,opt,name=business_name_detected,json=businessNameDetected,proto3" json:"business_name_detected,omitempty"`
	NdrScore                 float64         `protobuf:"fixed64,7,opt,name=ndr_score,json=ndrScore,proto3" json:"ndr_score,omitempty"`
	UnrScore                 float64         `protobuf:"fixed64,8,opt,name=unr_score,json=unrScore,proto3" json:"unr_score,omitempty"`
	NameMatchScore           float64         `protobuf:"fixed64,9,opt,name=name_match_score,json=nameMatchScore,proto3" json:"name_match_score,omitempty"`
	FootprintStrengthOverall string          `protobuf:"bytes,10,opt,name=footprint_strength_overall,json=footprintStrengthOverall,proto3" json:"footprint_strength_overall,omitempty"`
	PhoneNameDigitalAge      int32           `protobuf:"varint,11,opt,name=phone_name_digital_age,json=phoneNameDigitalAge,proto3" json:"phone_name_digital_age,omitempty"`
}

func (x *PhoneNameAttributes) Reset() {
	*x = PhoneNameAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_bureau_id_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhoneNameAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneNameAttributes) ProtoMessage() {}

func (x *PhoneNameAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_bureau_id_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneNameAttributes.ProtoReflect.Descriptor instead.
func (*PhoneNameAttributes) Descriptor() ([]byte, []int) {
	return file_api_risk_bureau_id_proto_rawDescGZIP(), []int{5}
}

func (x *PhoneNameAttributes) GetAttributesStatus() AttributeStatus {
	if x != nil {
		return x.AttributesStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *PhoneNameAttributes) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *PhoneNameAttributes) GetDigitalAge() int32 {
	if x != nil {
		return x.DigitalAge
	}
	return 0
}

func (x *PhoneNameAttributes) GetFirstNameMatch() bool {
	if x != nil {
		return x.FirstNameMatch
	}
	return false
}

func (x *PhoneNameAttributes) GetLastNameMatch() bool {
	if x != nil {
		return x.LastNameMatch
	}
	return false
}

func (x *PhoneNameAttributes) GetBusinessNameDetected() bool {
	if x != nil {
		return x.BusinessNameDetected
	}
	return false
}

func (x *PhoneNameAttributes) GetNdrScore() float64 {
	if x != nil {
		return x.NdrScore
	}
	return 0
}

func (x *PhoneNameAttributes) GetUnrScore() float64 {
	if x != nil {
		return x.UnrScore
	}
	return 0
}

func (x *PhoneNameAttributes) GetNameMatchScore() float64 {
	if x != nil {
		return x.NameMatchScore
	}
	return 0
}

func (x *PhoneNameAttributes) GetFootprintStrengthOverall() string {
	if x != nil {
		return x.FootprintStrengthOverall
	}
	return ""
}

func (x *PhoneNameAttributes) GetPhoneNameDigitalAge() int32 {
	if x != nil {
		return x.PhoneNameDigitalAge
	}
	return 0
}

// PhoneNetworkAttributes includes information about the phone network.
// This covers details such as current network status, roaming status, and network history.
type PhoneNetworkAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributesStatus                 AttributeStatus `protobuf:"varint,1,opt,name=attributes_status,json=attributesStatus,proto3,enum=risk.AttributeStatus" json:"attributes_status,omitempty"`
	Imsi                             string          `protobuf:"bytes,2,opt,name=imsi,proto3" json:"imsi,omitempty"`
	CurrentNetworkName               string          `protobuf:"bytes,3,opt,name=current_network_name,json=currentNetworkName,proto3" json:"current_network_name,omitempty"`
	CurrentNetworkRegion             string          `protobuf:"bytes,4,opt,name=current_network_region,json=currentNetworkRegion,proto3" json:"current_network_region,omitempty"`
	CurrentNetworkCountryCodeIso2    string          `protobuf:"bytes,5,opt,name=current_network_country_code_iso2,json=currentNetworkCountryCodeIso2,proto3" json:"current_network_country_code_iso2,omitempty"`
	IsPhoneReachable                 bool            `protobuf:"varint,6,opt,name=is_phone_reachable,json=isPhoneReachable,proto3" json:"is_phone_reachable,omitempty"`
	IsValidPhoneNumber               bool            `protobuf:"varint,7,opt,name=is_valid_phone_number,json=isValidPhoneNumber,proto3" json:"is_valid_phone_number,omitempty"`
	NumberBillingType                string          `protobuf:"bytes,8,opt,name=number_billing_type,json=numberBillingType,proto3" json:"number_billing_type,omitempty"`
	NumberHasPortingHistory          bool            `protobuf:"varint,9,opt,name=number_has_porting_history,json=numberHasPortingHistory,proto3" json:"number_has_porting_history,omitempty"`
	PortedFromNetworkName            string          `protobuf:"bytes,10,opt,name=ported_from_network_name,json=portedFromNetworkName,proto3" json:"ported_from_network_name,omitempty"`
	PortedFromNetworkRegion          string          `protobuf:"bytes,11,opt,name=ported_from_network_region,json=portedFromNetworkRegion,proto3" json:"ported_from_network_region,omitempty"`
	PortedFromNetworkCountryCodeIso2 string          `protobuf:"bytes,12,opt,name=ported_from_network_country_code_iso2,json=portedFromNetworkCountryCodeIso2,proto3" json:"ported_from_network_country_code_iso2,omitempty"`
	Roaming                          bool            `protobuf:"varint,13,opt,name=roaming,proto3" json:"roaming,omitempty"`
	RoamingNetworkName               string          `protobuf:"bytes,14,opt,name=roaming_network_name,json=roamingNetworkName,proto3" json:"roaming_network_name,omitempty"`
	RoamingNetworkRegion             string          `protobuf:"bytes,15,opt,name=roaming_network_region,json=roamingNetworkRegion,proto3" json:"roaming_network_region,omitempty"`
	RoamingNetworkCountryCodeIso2    string          `protobuf:"bytes,16,opt,name=roaming_network_country_code_iso2,json=roamingNetworkCountryCodeIso2,proto3" json:"roaming_network_country_code_iso2,omitempty"`
}

func (x *PhoneNetworkAttributes) Reset() {
	*x = PhoneNetworkAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_bureau_id_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhoneNetworkAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneNetworkAttributes) ProtoMessage() {}

func (x *PhoneNetworkAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_bureau_id_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneNetworkAttributes.ProtoReflect.Descriptor instead.
func (*PhoneNetworkAttributes) Descriptor() ([]byte, []int) {
	return file_api_risk_bureau_id_proto_rawDescGZIP(), []int{6}
}

func (x *PhoneNetworkAttributes) GetAttributesStatus() AttributeStatus {
	if x != nil {
		return x.AttributesStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *PhoneNetworkAttributes) GetImsi() string {
	if x != nil {
		return x.Imsi
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetCurrentNetworkName() string {
	if x != nil {
		return x.CurrentNetworkName
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetCurrentNetworkRegion() string {
	if x != nil {
		return x.CurrentNetworkRegion
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetCurrentNetworkCountryCodeIso2() string {
	if x != nil {
		return x.CurrentNetworkCountryCodeIso2
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetIsPhoneReachable() bool {
	if x != nil {
		return x.IsPhoneReachable
	}
	return false
}

func (x *PhoneNetworkAttributes) GetIsValidPhoneNumber() bool {
	if x != nil {
		return x.IsValidPhoneNumber
	}
	return false
}

func (x *PhoneNetworkAttributes) GetNumberBillingType() string {
	if x != nil {
		return x.NumberBillingType
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetNumberHasPortingHistory() bool {
	if x != nil {
		return x.NumberHasPortingHistory
	}
	return false
}

func (x *PhoneNetworkAttributes) GetPortedFromNetworkName() string {
	if x != nil {
		return x.PortedFromNetworkName
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetPortedFromNetworkRegion() string {
	if x != nil {
		return x.PortedFromNetworkRegion
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetPortedFromNetworkCountryCodeIso2() string {
	if x != nil {
		return x.PortedFromNetworkCountryCodeIso2
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetRoaming() bool {
	if x != nil {
		return x.Roaming
	}
	return false
}

func (x *PhoneNetworkAttributes) GetRoamingNetworkName() string {
	if x != nil {
		return x.RoamingNetworkName
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetRoamingNetworkRegion() string {
	if x != nil {
		return x.RoamingNetworkRegion
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetRoamingNetworkCountryCodeIso2() string {
	if x != nil {
		return x.RoamingNetworkCountryCodeIso2
	}
	return ""
}

// PhoneSocialAttributes represents social attributes associated with the phone number.
type PhoneSocialAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributesStatus AttributeStatus `protobuf:"varint,1,opt,name=attributes_status,json=attributesStatus,proto3,enum=risk.AttributeStatus" json:"attributes_status,omitempty"`
	Amazon           string          `protobuf:"bytes,2,opt,name=amazon,proto3" json:"amazon,omitempty"`
	Flipkart         string          `protobuf:"bytes,5,opt,name=flipkart,proto3" json:"flipkart,omitempty"`
	Housing          string          `protobuf:"bytes,6,opt,name=housing,proto3" json:"housing,omitempty"`
	Indiamart        string          `protobuf:"bytes,7,opt,name=indiamart,proto3" json:"indiamart,omitempty"`
	Instagram        string          `protobuf:"bytes,8,opt,name=instagram,proto3" json:"instagram,omitempty"`
	IsWABusiness     string          `protobuf:"bytes,9,opt,name=isWABusiness,proto3" json:"isWABusiness,omitempty"`
	Jeevansaathi     string          `protobuf:"bytes,10,opt,name=jeevansaathi,proto3" json:"jeevansaathi,omitempty"`
	Jiomart          string          `protobuf:"bytes,11,opt,name=jiomart,proto3" json:"jiomart,omitempty"`
	Microsoft        string          `protobuf:"bytes,12,opt,name=microsoft,proto3" json:"microsoft,omitempty"`
	Mobile           string          `protobuf:"bytes,13,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Paytm            string          `protobuf:"bytes,14,opt,name=paytm,proto3" json:"paytm,omitempty"`
	Shaadi           string          `protobuf:"bytes,15,opt,name=shaadi,proto3" json:"shaadi,omitempty"`
	Skype            string          `protobuf:"bytes,16,opt,name=skype,proto3" json:"skype,omitempty"`
	Swiggy           string          `protobuf:"bytes,19,opt,name=swiggy,proto3" json:"swiggy,omitempty"`
	Toi              string          `protobuf:"bytes,20,opt,name=toi,proto3" json:"toi,omitempty"`
	Whatsapp         string          `protobuf:"bytes,21,opt,name=whatsapp,proto3" json:"whatsapp,omitempty"`
	Yatra            string          `protobuf:"bytes,22,opt,name=yatra,proto3" json:"yatra,omitempty"`
	Zoho             string          `protobuf:"bytes,23,opt,name=zoho,proto3" json:"zoho,omitempty"`
}

func (x *PhoneSocialAttributes) Reset() {
	*x = PhoneSocialAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_bureau_id_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhoneSocialAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneSocialAttributes) ProtoMessage() {}

func (x *PhoneSocialAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_bureau_id_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneSocialAttributes.ProtoReflect.Descriptor instead.
func (*PhoneSocialAttributes) Descriptor() ([]byte, []int) {
	return file_api_risk_bureau_id_proto_rawDescGZIP(), []int{7}
}

func (x *PhoneSocialAttributes) GetAttributesStatus() AttributeStatus {
	if x != nil {
		return x.AttributesStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *PhoneSocialAttributes) GetAmazon() string {
	if x != nil {
		return x.Amazon
	}
	return ""
}

func (x *PhoneSocialAttributes) GetFlipkart() string {
	if x != nil {
		return x.Flipkart
	}
	return ""
}

func (x *PhoneSocialAttributes) GetHousing() string {
	if x != nil {
		return x.Housing
	}
	return ""
}

func (x *PhoneSocialAttributes) GetIndiamart() string {
	if x != nil {
		return x.Indiamart
	}
	return ""
}

func (x *PhoneSocialAttributes) GetInstagram() string {
	if x != nil {
		return x.Instagram
	}
	return ""
}

func (x *PhoneSocialAttributes) GetIsWABusiness() string {
	if x != nil {
		return x.IsWABusiness
	}
	return ""
}

func (x *PhoneSocialAttributes) GetJeevansaathi() string {
	if x != nil {
		return x.Jeevansaathi
	}
	return ""
}

func (x *PhoneSocialAttributes) GetJiomart() string {
	if x != nil {
		return x.Jiomart
	}
	return ""
}

func (x *PhoneSocialAttributes) GetMicrosoft() string {
	if x != nil {
		return x.Microsoft
	}
	return ""
}

func (x *PhoneSocialAttributes) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *PhoneSocialAttributes) GetPaytm() string {
	if x != nil {
		return x.Paytm
	}
	return ""
}

func (x *PhoneSocialAttributes) GetShaadi() string {
	if x != nil {
		return x.Shaadi
	}
	return ""
}

func (x *PhoneSocialAttributes) GetSkype() string {
	if x != nil {
		return x.Skype
	}
	return ""
}

func (x *PhoneSocialAttributes) GetSwiggy() string {
	if x != nil {
		return x.Swiggy
	}
	return ""
}

func (x *PhoneSocialAttributes) GetToi() string {
	if x != nil {
		return x.Toi
	}
	return ""
}

func (x *PhoneSocialAttributes) GetWhatsapp() string {
	if x != nil {
		return x.Whatsapp
	}
	return ""
}

func (x *PhoneSocialAttributes) GetYatra() string {
	if x != nil {
		return x.Yatra
	}
	return ""
}

func (x *PhoneSocialAttributes) GetZoho() string {
	if x != nil {
		return x.Zoho
	}
	return ""
}

// EmailSocialAttributes contains social attributes associated with the email address.
type EmailSocialAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributesStatus AttributeStatus `protobuf:"varint,1,opt,name=attributes_status,json=attributesStatus,proto3,enum=risk.AttributeStatus" json:"attributes_status,omitempty"`
	Amazon           string          `protobuf:"bytes,2,opt,name=amazon,proto3" json:"amazon,omitempty"`
	Booking          string          `protobuf:"bytes,3,opt,name=booking,proto3" json:"booking,omitempty"`
	Email            string          `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Flickr           string          `protobuf:"bytes,5,opt,name=flickr,proto3" json:"flickr,omitempty"`
	Flipkart         string          `protobuf:"bytes,6,opt,name=flipkart,proto3" json:"flipkart,omitempty"`
	Housing          string          `protobuf:"bytes,7,opt,name=housing,proto3" json:"housing,omitempty"`
	Instagram        string          `protobuf:"bytes,8,opt,name=instagram,proto3" json:"instagram,omitempty"`
	Jeevansaathi     string          `protobuf:"bytes,9,opt,name=jeevansaathi,proto3" json:"jeevansaathi,omitempty"`
	Microsoft        string          `protobuf:"bytes,10,opt,name=microsoft,proto3" json:"microsoft,omitempty"`
	Paytm            string          `protobuf:"bytes,11,opt,name=paytm,proto3" json:"paytm,omitempty"`
	Pinterest        string          `protobuf:"bytes,12,opt,name=pinterest,proto3" json:"pinterest,omitempty"`
	Quora            string          `protobuf:"bytes,13,opt,name=quora,proto3" json:"quora,omitempty"`
	Shaadi           string          `protobuf:"bytes,14,opt,name=shaadi,proto3" json:"shaadi,omitempty"`
	Skype            string          `protobuf:"bytes,15,opt,name=skype,proto3" json:"skype,omitempty"`
	Spotify          string          `protobuf:"bytes,16,opt,name=spotify,proto3" json:"spotify,omitempty"`
	Toi              string          `protobuf:"bytes,17,opt,name=toi,proto3" json:"toi,omitempty"`
	Wordpress        string          `protobuf:"bytes,18,opt,name=wordpress,proto3" json:"wordpress,omitempty"`
	Yatra            string          `protobuf:"bytes,19,opt,name=yatra,proto3" json:"yatra,omitempty"`
	Zoho             string          `protobuf:"bytes,20,opt,name=zoho,proto3" json:"zoho,omitempty"`
}

func (x *EmailSocialAttributes) Reset() {
	*x = EmailSocialAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_bureau_id_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailSocialAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailSocialAttributes) ProtoMessage() {}

func (x *EmailSocialAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_bureau_id_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailSocialAttributes.ProtoReflect.Descriptor instead.
func (*EmailSocialAttributes) Descriptor() ([]byte, []int) {
	return file_api_risk_bureau_id_proto_rawDescGZIP(), []int{8}
}

func (x *EmailSocialAttributes) GetAttributesStatus() AttributeStatus {
	if x != nil {
		return x.AttributesStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *EmailSocialAttributes) GetAmazon() string {
	if x != nil {
		return x.Amazon
	}
	return ""
}

func (x *EmailSocialAttributes) GetBooking() string {
	if x != nil {
		return x.Booking
	}
	return ""
}

func (x *EmailSocialAttributes) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *EmailSocialAttributes) GetFlickr() string {
	if x != nil {
		return x.Flickr
	}
	return ""
}

func (x *EmailSocialAttributes) GetFlipkart() string {
	if x != nil {
		return x.Flipkart
	}
	return ""
}

func (x *EmailSocialAttributes) GetHousing() string {
	if x != nil {
		return x.Housing
	}
	return ""
}

func (x *EmailSocialAttributes) GetInstagram() string {
	if x != nil {
		return x.Instagram
	}
	return ""
}

func (x *EmailSocialAttributes) GetJeevansaathi() string {
	if x != nil {
		return x.Jeevansaathi
	}
	return ""
}

func (x *EmailSocialAttributes) GetMicrosoft() string {
	if x != nil {
		return x.Microsoft
	}
	return ""
}

func (x *EmailSocialAttributes) GetPaytm() string {
	if x != nil {
		return x.Paytm
	}
	return ""
}

func (x *EmailSocialAttributes) GetPinterest() string {
	if x != nil {
		return x.Pinterest
	}
	return ""
}

func (x *EmailSocialAttributes) GetQuora() string {
	if x != nil {
		return x.Quora
	}
	return ""
}

func (x *EmailSocialAttributes) GetShaadi() string {
	if x != nil {
		return x.Shaadi
	}
	return ""
}

func (x *EmailSocialAttributes) GetSkype() string {
	if x != nil {
		return x.Skype
	}
	return ""
}

func (x *EmailSocialAttributes) GetSpotify() string {
	if x != nil {
		return x.Spotify
	}
	return ""
}

func (x *EmailSocialAttributes) GetToi() string {
	if x != nil {
		return x.Toi
	}
	return ""
}

func (x *EmailSocialAttributes) GetWordpress() string {
	if x != nil {
		return x.Wordpress
	}
	return ""
}

func (x *EmailSocialAttributes) GetYatra() string {
	if x != nil {
		return x.Yatra
	}
	return ""
}

func (x *EmailSocialAttributes) GetZoho() string {
	if x != nil {
		return x.Zoho
	}
	return ""
}

// RiskAttributes evaluates the overall risk associated with the user.
// This determines the riskiness of the user based on phone and email activities.
type RiskAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributesStatus         AttributeStatus `protobuf:"varint,1,opt,name=attributes_status,json=attributesStatus,proto3,enum=risk.AttributeStatus" json:"attributes_status,omitempty"`
	Address1                 string          `protobuf:"bytes,2,opt,name=address1,proto3" json:"address1,omitempty"`
	Address2                 string          `protobuf:"bytes,3,opt,name=address2,proto3" json:"address2,omitempty"`
	Landmark                 string          `protobuf:"bytes,4,opt,name=landmark,proto3" json:"landmark,omitempty"`
	AreaName                 string          `protobuf:"bytes,5,opt,name=area_name,json=areaName,proto3" json:"area_name,omitempty"`
	AreaPincode              string          `protobuf:"bytes,6,opt,name=area_pincode,json=areaPincode,proto3" json:"area_pincode,omitempty"`
	SocietyName              string          `protobuf:"bytes,7,opt,name=society_name,json=societyName,proto3" json:"society_name,omitempty"`
	StreetName               string          `protobuf:"bytes,8,opt,name=street_name,json=streetName,proto3" json:"street_name,omitempty"`
	CityName                 string          `protobuf:"bytes,9,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	StateName                string          `protobuf:"bytes,10,opt,name=state_name,json=stateName,proto3" json:"state_name,omitempty"`
	AddressInsights          string          `protobuf:"bytes,11,opt,name=address_insights,json=addressInsights,proto3" json:"address_insights,omitempty"`
	AddressRisk              string          `protobuf:"bytes,12,opt,name=address_risk,json=addressRisk,proto3" json:"address_risk,omitempty"`
	AddressCompletenessScore float32         `protobuf:"fixed32,13,opt,name=address_completeness_score,json=addressCompletenessScore,proto3" json:"address_completeness_score,omitempty"`
	// common_email_count represents number of vendor api calls with same email id in last 28 days
	CommonEmailCount       int32    `protobuf:"varint,14,opt,name=common_email_count,json=commonEmailCount,proto3" json:"common_email_count,omitempty"`
	EmailFraud             bool     `protobuf:"varint,15,opt,name=email_fraud,json=emailFraud,proto3" json:"email_fraud,omitempty"`
	EmailFraudCount        int32    `protobuf:"varint,16,opt,name=email_fraud_count,json=emailFraudCount,proto3" json:"email_fraud_count,omitempty"`
	EmailFraudNetwork      bool     `protobuf:"varint,17,opt,name=email_fraud_network,json=emailFraudNetwork,proto3" json:"email_fraud_network,omitempty"`
	EmailIdentityTrust     string   `protobuf:"bytes,18,opt,name=email_identity_trust,json=emailIdentityTrust,proto3" json:"email_identity_trust,omitempty"`
	EmailSocialMediaCount  int32    `protobuf:"varint,19,opt,name=email_social_media_count,json=emailSocialMediaCount,proto3" json:"email_social_media_count,omitempty"`
	IdentityConfidence     string   `protobuf:"bytes,20,opt,name=identity_confidence,json=identityConfidence,proto3" json:"identity_confidence,omitempty"`
	TelecomRisk            string   `protobuf:"bytes,21,opt,name=telecom_risk,json=telecomRisk,proto3" json:"telecom_risk,omitempty"`
	IsPhoneReachable       bool     `protobuf:"varint,22,opt,name=is_phone_reachable,json=isPhoneReachable,proto3" json:"is_phone_reachable,omitempty"`
	PhoneFraud             bool     `protobuf:"varint,23,opt,name=phone_fraud,json=phoneFraud,proto3" json:"phone_fraud,omitempty"`
	PhoneFraudCount        int32    `protobuf:"varint,24,opt,name=phone_fraud_count,json=phoneFraudCount,proto3" json:"phone_fraud_count,omitempty"`
	PhoneFraudNetwork      bool     `protobuf:"varint,25,opt,name=phone_fraud_network,json=phoneFraudNetwork,proto3" json:"phone_fraud_network,omitempty"`
	PhoneIdentityTrust     string   `protobuf:"bytes,26,opt,name=phone_identity_trust,json=phoneIdentityTrust,proto3" json:"phone_identity_trust,omitempty"`
	PhoneSocialMediaCount  int32    `protobuf:"varint,27,opt,name=phone_social_media_count,json=phoneSocialMediaCount,proto3" json:"phone_social_media_count,omitempty"`
	NegativeInsights       []string `protobuf:"bytes,28,rep,name=negative_insights,json=negativeInsights,proto3" json:"negative_insights,omitempty"`
	PositiveInsights       []string `protobuf:"bytes,29,rep,name=positive_insights,json=positiveInsights,proto3" json:"positive_insights,omitempty"`
	UpiPhoneNameMatch      int32    `protobuf:"varint,30,opt,name=upi_phone_name_match,json=upiPhoneNameMatch,proto3" json:"upi_phone_name_match,omitempty"`
	UpiPhoneNameMatchScore float64  `protobuf:"fixed64,31,opt,name=upi_phone_name_match_score,json=upiPhoneNameMatchScore,proto3" json:"upi_phone_name_match_score,omitempty"`
	AlternateRiskScore     float64  `protobuf:"fixed64,32,opt,name=alternate_risk_score,json=alternateRiskScore,proto3" json:"alternate_risk_score,omitempty"`
}

func (x *RiskAttributes) Reset() {
	*x = RiskAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_bureau_id_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskAttributes) ProtoMessage() {}

func (x *RiskAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_bureau_id_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskAttributes.ProtoReflect.Descriptor instead.
func (*RiskAttributes) Descriptor() ([]byte, []int) {
	return file_api_risk_bureau_id_proto_rawDescGZIP(), []int{9}
}

func (x *RiskAttributes) GetAttributesStatus() AttributeStatus {
	if x != nil {
		return x.AttributesStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *RiskAttributes) GetAddress1() string {
	if x != nil {
		return x.Address1
	}
	return ""
}

func (x *RiskAttributes) GetAddress2() string {
	if x != nil {
		return x.Address2
	}
	return ""
}

func (x *RiskAttributes) GetLandmark() string {
	if x != nil {
		return x.Landmark
	}
	return ""
}

func (x *RiskAttributes) GetAreaName() string {
	if x != nil {
		return x.AreaName
	}
	return ""
}

func (x *RiskAttributes) GetAreaPincode() string {
	if x != nil {
		return x.AreaPincode
	}
	return ""
}

func (x *RiskAttributes) GetSocietyName() string {
	if x != nil {
		return x.SocietyName
	}
	return ""
}

func (x *RiskAttributes) GetStreetName() string {
	if x != nil {
		return x.StreetName
	}
	return ""
}

func (x *RiskAttributes) GetCityName() string {
	if x != nil {
		return x.CityName
	}
	return ""
}

func (x *RiskAttributes) GetStateName() string {
	if x != nil {
		return x.StateName
	}
	return ""
}

func (x *RiskAttributes) GetAddressInsights() string {
	if x != nil {
		return x.AddressInsights
	}
	return ""
}

func (x *RiskAttributes) GetAddressRisk() string {
	if x != nil {
		return x.AddressRisk
	}
	return ""
}

func (x *RiskAttributes) GetAddressCompletenessScore() float32 {
	if x != nil {
		return x.AddressCompletenessScore
	}
	return 0
}

func (x *RiskAttributes) GetCommonEmailCount() int32 {
	if x != nil {
		return x.CommonEmailCount
	}
	return 0
}

func (x *RiskAttributes) GetEmailFraud() bool {
	if x != nil {
		return x.EmailFraud
	}
	return false
}

func (x *RiskAttributes) GetEmailFraudCount() int32 {
	if x != nil {
		return x.EmailFraudCount
	}
	return 0
}

func (x *RiskAttributes) GetEmailFraudNetwork() bool {
	if x != nil {
		return x.EmailFraudNetwork
	}
	return false
}

func (x *RiskAttributes) GetEmailIdentityTrust() string {
	if x != nil {
		return x.EmailIdentityTrust
	}
	return ""
}

func (x *RiskAttributes) GetEmailSocialMediaCount() int32 {
	if x != nil {
		return x.EmailSocialMediaCount
	}
	return 0
}

func (x *RiskAttributes) GetIdentityConfidence() string {
	if x != nil {
		return x.IdentityConfidence
	}
	return ""
}

func (x *RiskAttributes) GetTelecomRisk() string {
	if x != nil {
		return x.TelecomRisk
	}
	return ""
}

func (x *RiskAttributes) GetIsPhoneReachable() bool {
	if x != nil {
		return x.IsPhoneReachable
	}
	return false
}

func (x *RiskAttributes) GetPhoneFraud() bool {
	if x != nil {
		return x.PhoneFraud
	}
	return false
}

func (x *RiskAttributes) GetPhoneFraudCount() int32 {
	if x != nil {
		return x.PhoneFraudCount
	}
	return 0
}

func (x *RiskAttributes) GetPhoneFraudNetwork() bool {
	if x != nil {
		return x.PhoneFraudNetwork
	}
	return false
}

func (x *RiskAttributes) GetPhoneIdentityTrust() string {
	if x != nil {
		return x.PhoneIdentityTrust
	}
	return ""
}

func (x *RiskAttributes) GetPhoneSocialMediaCount() int32 {
	if x != nil {
		return x.PhoneSocialMediaCount
	}
	return 0
}

func (x *RiskAttributes) GetNegativeInsights() []string {
	if x != nil {
		return x.NegativeInsights
	}
	return nil
}

func (x *RiskAttributes) GetPositiveInsights() []string {
	if x != nil {
		return x.PositiveInsights
	}
	return nil
}

func (x *RiskAttributes) GetUpiPhoneNameMatch() int32 {
	if x != nil {
		return x.UpiPhoneNameMatch
	}
	return 0
}

func (x *RiskAttributes) GetUpiPhoneNameMatchScore() float64 {
	if x != nil {
		return x.UpiPhoneNameMatchScore
	}
	return 0
}

func (x *RiskAttributes) GetAlternateRiskScore() float64 {
	if x != nil {
		return x.AlternateRiskScore
	}
	return 0
}

var File_api_risk_bureau_id_proto protoreflect.FileDescriptor

var file_api_risk_bureau_id_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x62, 0x75, 0x72, 0x65, 0x61,
	0x75, 0x5f, 0x69, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x72, 0x69, 0x73, 0x6b,
	0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x84, 0x03, 0x0a, 0x12, 0x42, 0x75, 0x72,
	0x65, 0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x35, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x2a, 0x0a, 0x08, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x08, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x61, 0x77, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x61,
	0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0xed, 0x04, 0x0a, 0x08, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x56, 0x0a, 0x18,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x16, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x10, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x0f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x4d, 0x0a, 0x15, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x52, 0x13, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x10, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x61,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x0f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x4d, 0x0a, 0x15, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x52, 0x13, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x17, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x73, 0x52, 0x15, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x6f, 0x63, 0x69, 0x61,
	0x6c, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x17, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x61, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x15, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x12, 0x3d, 0x0a, 0x0f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52,
	0x0e, 0x72, 0x69, 0x73, 0x6b, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x22,
	0x8b, 0x04, 0x0a, 0x0f, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x11, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69,
	0x67, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x64, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73,
	0x12, 0x2a, 0x0a, 0x11, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x27, 0x0a, 0x0f,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f,
	0x63, 0x6f, 0x72, 0x70, 0x6f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x72, 0x70, 0x6f, 0x72, 0x61, 0x74, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x68, 0x69, 0x74, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x48, 0x69, 0x74,
	0x73, 0x12, 0x3c, 0x0a, 0x1a, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6c,
	0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x46, 0x69, 0x6e, 0x61,
	0x6c, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x34, 0x0a, 0x16, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x6c, 0x61, 0x73, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x66, 0x69, 0x72, 0x73, 0x74, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x22, 0x9e, 0x05,
	0x0a, 0x13, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x11, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x69, 0x67, 0x69, 0x74, 0x61,
	0x6c, 0x41, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0e, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x34, 0x0a, 0x16, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12,
	0x38, 0x0a, 0x18, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x66, 0x6f, 0x6f, 0x74, 0x70, 0x72, 0x69,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x16, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x46, 0x6f, 0x6f, 0x74, 0x70, 0x72, 0x69, 0x6e,
	0x74, 0x53, 0x74, 0x72, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x33, 0x0a, 0x16, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x5f,
	0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x67, 0x65, 0x12, 0x28,
	0x0a, 0x10, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x70, 0x63, 0x6c, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53,
	0x70, 0x63, 0x6c, 0x43, 0x68, 0x61, 0x72, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x64, 0x72, 0x5f,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6e, 0x64, 0x72,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x6e, 0x72, 0x5f, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x75, 0x6e, 0x72, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x6e, 0x61,
	0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x28, 0x0a, 0x10,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6e, 0x61, 0x6d, 0x65, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x36, 0x0a, 0x17, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70,
	0x6c, 0x65, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x65,
	0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c,
	0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x65, 0x64, 0x22, 0x93,
	0x01, 0x0a, 0x0f, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x73, 0x12, 0x42, 0x0a, 0x11, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x76, 0x70, 0x61, 0x22, 0xf3, 0x03, 0x0a, 0x13, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x11,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69,
	0x67, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x64, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d,
	0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x34, 0x0a,
	0x16, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x64,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x64, 0x72, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6e, 0x64, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x75, 0x6e, 0x72, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x08, 0x75, 0x6e, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x28, 0x0a,
	0x10, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x66, 0x6f, 0x6f, 0x74, 0x70,
	0x72, 0x69, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x5f, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x6c, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x66, 0x6f, 0x6f,
	0x74, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x4f, 0x76,
	0x65, 0x72, 0x61, 0x6c, 0x6c, 0x12, 0x33, 0x0a, 0x16, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x5f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x67, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x67, 0x65, 0x22, 0x83, 0x07, 0x0a, 0x16, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x11, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x73,
	0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x73, 0x69, 0x12, 0x30, 0x0a,
	0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x34, 0x0a, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x21, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x73, 0x6f, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x1d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x73, 0x6f, 0x32, 0x12,
	0x2c, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x31, 0x0a,
	0x15, 0x69, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x2e, 0x0a, 0x13, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x3b, 0x0a, 0x1a, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x68, 0x61, 0x73, 0x5f, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x48, 0x61, 0x73, 0x50,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x37, 0x0a,
	0x18, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x15, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x1a, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64,
	0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x12, 0x4f, 0x0a, 0x25, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x72,
	0x6f, 0x6d, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x73, 0x6f, 0x32, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x20, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x49, 0x73, 0x6f, 0x32, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x6f, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x12, 0x30,
	0x0a, 0x14, 0x72, 0x6f, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x6f,
	0x61, 0x6d, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x34, 0x0a, 0x16, 0x72, 0x6f, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x14, 0x72, 0x6f, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x21, 0x72, 0x6f, 0x61, 0x6d, 0x69, 0x6e,
	0x67, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x73, 0x6f, 0x32, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x1d, 0x72, 0x6f, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x73, 0x6f, 0x32,
	0x22, 0xb1, 0x04, 0x0a, 0x15, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x11, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x6d, 0x61, 0x7a, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x6d, 0x61, 0x7a, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x6c, 0x69, 0x70, 0x6b, 0x61,
	0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x6c, 0x69, 0x70, 0x6b, 0x61,
	0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x6f, 0x75, 0x73, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x68, 0x6f, 0x75, 0x73, 0x69, 0x6e, 0x67, 0x12, 0x1c, 0x0a, 0x09,
	0x69, 0x6e, 0x64, 0x69, 0x61, 0x6d, 0x61, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6d, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x57, 0x41,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x69, 0x73, 0x57, 0x41, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x22, 0x0a, 0x0c,
	0x6a, 0x65, 0x65, 0x76, 0x61, 0x6e, 0x73, 0x61, 0x61, 0x74, 0x68, 0x69, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6a, 0x65, 0x65, 0x76, 0x61, 0x6e, 0x73, 0x61, 0x61, 0x74, 0x68, 0x69,
	0x12, 0x18, 0x0a, 0x07, 0x6a, 0x69, 0x6f, 0x6d, 0x61, 0x72, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6a, 0x69, 0x6f, 0x6d, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x69,
	0x63, 0x72, 0x6f, 0x73, 0x6f, 0x66, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x73, 0x6f, 0x66, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x74, 0x6d, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x70, 0x61, 0x79, 0x74, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x61, 0x64, 0x69,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x61, 0x64, 0x69, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x6b, 0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x6b, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x77, 0x69, 0x67, 0x67, 0x79, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x77, 0x69, 0x67, 0x67, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x74, 0x6f, 0x69, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x6f, 0x69, 0x12, 0x1a,
	0x0a, 0x08, 0x77, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x77, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x79, 0x61,
	0x74, 0x72, 0x61, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x79, 0x61, 0x74, 0x72, 0x61,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x68, 0x6f, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x7a, 0x6f, 0x68, 0x6f, 0x22, 0xbd, 0x04, 0x0a, 0x15, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x6f,
	0x63, 0x69, 0x61, 0x6c, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x42,
	0x0a, 0x11, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x10, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x61, 0x7a, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x61, 0x7a, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6c,
	0x69, 0x63, 0x6b, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6c, 0x69, 0x63,
	0x6b, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x6c, 0x69, 0x70, 0x6b, 0x61, 0x72, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x6c, 0x69, 0x70, 0x6b, 0x61, 0x72, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x68, 0x6f, 0x75, 0x73, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x68, 0x6f, 0x75, 0x73, 0x69, 0x6e, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x6a, 0x65, 0x65, 0x76, 0x61, 0x6e,
	0x73, 0x61, 0x61, 0x74, 0x68, 0x69, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6a, 0x65,
	0x65, 0x76, 0x61, 0x6e, 0x73, 0x61, 0x61, 0x74, 0x68, 0x69, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x69,
	0x63, 0x72, 0x6f, 0x73, 0x6f, 0x66, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x73, 0x6f, 0x66, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x74,
	0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x61, 0x79, 0x74, 0x6d, 0x12, 0x1c,
	0x0a, 0x09, 0x70, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x71, 0x75, 0x6f, 0x72, 0x61, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x6f,
	0x72, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x61, 0x64, 0x69, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x61, 0x64, 0x69, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x6b,
	0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6b, 0x79, 0x70, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x70, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x70, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x6f,
	0x69, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x6f, 0x69, 0x12, 0x1c, 0x0a, 0x09,
	0x77, 0x6f, 0x72, 0x64, 0x70, 0x72, 0x65, 0x73, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x77, 0x6f, 0x72, 0x64, 0x70, 0x72, 0x65, 0x73, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x79, 0x61,
	0x74, 0x72, 0x61, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x79, 0x61, 0x74, 0x72, 0x61,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x68, 0x6f, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x7a, 0x6f, 0x68, 0x6f, 0x22, 0xed, 0x0a, 0x0a, 0x0e, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x11, 0x61, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x61, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x32, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x64, 0x6d, 0x61, 0x72, 0x6b, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x64, 0x6d, 0x61, 0x72, 0x6b, 0x12,
	0x1b, 0x0a, 0x09, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x61, 0x72, 0x65, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x72, 0x65, 0x61, 0x5f, 0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x65, 0x61, 0x50, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x6f, 0x63, 0x69, 0x65, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x6f, 0x63, 0x69, 0x65, 0x74, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x29, 0x0a, 0x10, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x69, 0x73, 0x6b, 0x12, 0x3c, 0x0a,
	0x1a, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x18, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x5f, 0x66, 0x72, 0x61, 0x75, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x46, 0x72, 0x61, 0x75, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x66, 0x72, 0x61, 0x75, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x46, 0x72, 0x61, 0x75,
	0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f,
	0x66, 0x72, 0x61, 0x75, 0x64, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x11, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x46, 0x72, 0x61, 0x75, 0x64, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x30, 0x0a, 0x14, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x72, 0x75, 0x73, 0x74, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x72, 0x75, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x18, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x2f, 0x0a, 0x13, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x65, 0x6c, 0x65, 0x63, 0x6f, 0x6d, 0x5f, 0x72, 0x69,
	0x73, 0x6b, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x65, 0x6c, 0x65, 0x63, 0x6f,
	0x6d, 0x52, 0x69, 0x73, 0x6b, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x72, 0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x10, 0x69, 0x73, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x61, 0x63, 0x68, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x66, 0x72, 0x61,
	0x75, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x46,
	0x72, 0x61, 0x75, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x66, 0x72,
	0x61, 0x75, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x46, 0x72, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x2e, 0x0a, 0x13, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x66, 0x72, 0x61, 0x75, 0x64, 0x5f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x46, 0x72, 0x61, 0x75, 0x64, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x12, 0x30, 0x0a, 0x14, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x5f, 0x74, 0x72, 0x75, 0x73, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x72, 0x75,
	0x73, 0x74, 0x12, 0x37, 0x0a, 0x18, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1b,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x53, 0x6f, 0x63, 0x69, 0x61,
	0x6c, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x6e,
	0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x18, 0x1c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x1d, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x10, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x75, 0x70, 0x69, 0x5f, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x1e, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x11, 0x75, 0x70, 0x69, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x3a, 0x0a, 0x1a, 0x75, 0x70, 0x69, 0x5f, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x16, 0x75, 0x70, 0x69, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x5f,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x12, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x6b, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x2a, 0x8a, 0x01, 0x0a, 0x0f, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x54, 0x54, 0x52,
	0x49, 0x42, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1e,
	0x0a, 0x1a, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x21,
	0x0a, 0x1d, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x45, 0x44, 0x10,
	0x03, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x69, 0x73, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_bureau_id_proto_rawDescOnce sync.Once
	file_api_risk_bureau_id_proto_rawDescData = file_api_risk_bureau_id_proto_rawDesc
)

func file_api_risk_bureau_id_proto_rawDescGZIP() []byte {
	file_api_risk_bureau_id_proto_rawDescOnce.Do(func() {
		file_api_risk_bureau_id_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_bureau_id_proto_rawDescData)
	})
	return file_api_risk_bureau_id_proto_rawDescData
}

var file_api_risk_bureau_id_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_risk_bureau_id_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_risk_bureau_id_proto_goTypes = []interface{}{
	(AttributeStatus)(0),           // 0: risk.AttributeStatus
	(*BureauIdRiskDetail)(nil),     // 1: risk.BureauIdRiskDetail
	(*Services)(nil),               // 2: risk.Services
	(*EmailAttributes)(nil),        // 3: risk.EmailAttributes
	(*EmailNameAttributes)(nil),    // 4: risk.EmailNameAttributes
	(*PhoneAttributes)(nil),        // 5: risk.PhoneAttributes
	(*PhoneNameAttributes)(nil),    // 6: risk.PhoneNameAttributes
	(*PhoneNetworkAttributes)(nil), // 7: risk.PhoneNetworkAttributes
	(*PhoneSocialAttributes)(nil),  // 8: risk.PhoneSocialAttributes
	(*EmailSocialAttributes)(nil),  // 9: risk.EmailSocialAttributes
	(*RiskAttributes)(nil),         // 10: risk.RiskAttributes
	(*common.PhoneNumber)(nil),     // 11: api.typesv2.common.PhoneNumber
	(*timestamppb.Timestamp)(nil),  // 12: google.protobuf.Timestamp
}
var file_api_risk_bureau_id_proto_depIdxs = []int32{
	11, // 0: risk.BureauIdRiskDetail.phone:type_name -> api.typesv2.common.PhoneNumber
	2,  // 1: risk.BureauIdRiskDetail.services:type_name -> risk.Services
	12, // 2: risk.BureauIdRiskDetail.created_at:type_name -> google.protobuf.Timestamp
	12, // 3: risk.BureauIdRiskDetail.updated_at:type_name -> google.protobuf.Timestamp
	7,  // 4: risk.Services.phone_network_attributes:type_name -> risk.PhoneNetworkAttributes
	5,  // 5: risk.Services.phone_attributes:type_name -> risk.PhoneAttributes
	6,  // 6: risk.Services.phone_name_attributes:type_name -> risk.PhoneNameAttributes
	3,  // 7: risk.Services.email_attributes:type_name -> risk.EmailAttributes
	4,  // 8: risk.Services.email_name_attributes:type_name -> risk.EmailNameAttributes
	9,  // 9: risk.Services.email_social_attributes:type_name -> risk.EmailSocialAttributes
	8,  // 10: risk.Services.phone_social_attributes:type_name -> risk.PhoneSocialAttributes
	10, // 11: risk.Services.risk_attributes:type_name -> risk.RiskAttributes
	0,  // 12: risk.EmailAttributes.attributes_status:type_name -> risk.AttributeStatus
	0,  // 13: risk.EmailNameAttributes.attributes_status:type_name -> risk.AttributeStatus
	0,  // 14: risk.PhoneAttributes.attributes_status:type_name -> risk.AttributeStatus
	0,  // 15: risk.PhoneNameAttributes.attributes_status:type_name -> risk.AttributeStatus
	0,  // 16: risk.PhoneNetworkAttributes.attributes_status:type_name -> risk.AttributeStatus
	0,  // 17: risk.PhoneSocialAttributes.attributes_status:type_name -> risk.AttributeStatus
	0,  // 18: risk.EmailSocialAttributes.attributes_status:type_name -> risk.AttributeStatus
	0,  // 19: risk.RiskAttributes.attributes_status:type_name -> risk.AttributeStatus
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_api_risk_bureau_id_proto_init() }
func file_api_risk_bureau_id_proto_init() {
	if File_api_risk_bureau_id_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_bureau_id_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BureauIdRiskDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_bureau_id_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Services); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_bureau_id_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_bureau_id_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailNameAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_bureau_id_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhoneAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_bureau_id_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhoneNameAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_bureau_id_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhoneNetworkAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_bureau_id_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhoneSocialAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_bureau_id_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailSocialAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_bureau_id_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_bureau_id_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_bureau_id_proto_goTypes,
		DependencyIndexes: file_api_risk_bureau_id_proto_depIdxs,
		EnumInfos:         file_api_risk_bureau_id_proto_enumTypes,
		MessageInfos:      file_api_risk_bureau_id_proto_msgTypes,
	}.Build()
	File_api_risk_bureau_id_proto = out.File
	file_api_risk_bureau_id_proto_rawDesc = nil
	file_api_risk_bureau_id_proto_goTypes = nil
	file_api_risk_bureau_id_proto_depIdxs = nil
}
