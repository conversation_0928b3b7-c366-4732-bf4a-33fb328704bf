// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/mnrl/mnrl_report.proto

package mnrl

import (
	enums "github.com/epifi/gamma/api/risk/enums"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MnrlReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                  string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	MobileNumber        string                 `protobuf:"bytes,2,opt,name=mobile_number,json=mobileNumber,proto3" json:"mobile_number,omitempty"`
	LsaName             enums.LsaName          `protobuf:"varint,3,opt,name=lsa_name,json=lsaName,proto3,enum=enums.LsaName" json:"lsa_name,omitempty"`
	TspName             string                 `protobuf:"bytes,4,opt,name=tsp_name,json=tspName,proto3" json:"tsp_name,omitempty"`
	DateOfDisconnection *date.Date             `protobuf:"bytes,6,opt,name=date_of_disconnection,json=dateOfDisconnection,proto3" json:"date_of_disconnection,omitempty"`
	ActionReasons       string                 `protobuf:"bytes,7,opt,name=action_reasons,json=actionReasons,proto3" json:"action_reasons,omitempty"`
	Usecase             enums.MnrlUsecase      `protobuf:"varint,8,opt,name=usecase,proto3,enum=enums.MnrlUsecase" json:"usecase,omitempty"`
	CreatedAt           *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt           *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *MnrlReport) Reset() {
	*x = MnrlReport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_mnrl_mnrl_report_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MnrlReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MnrlReport) ProtoMessage() {}

func (x *MnrlReport) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_mnrl_mnrl_report_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MnrlReport.ProtoReflect.Descriptor instead.
func (*MnrlReport) Descriptor() ([]byte, []int) {
	return file_api_risk_mnrl_mnrl_report_proto_rawDescGZIP(), []int{0}
}

func (x *MnrlReport) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MnrlReport) GetMobileNumber() string {
	if x != nil {
		return x.MobileNumber
	}
	return ""
}

func (x *MnrlReport) GetLsaName() enums.LsaName {
	if x != nil {
		return x.LsaName
	}
	return enums.LsaName(0)
}

func (x *MnrlReport) GetTspName() string {
	if x != nil {
		return x.TspName
	}
	return ""
}

func (x *MnrlReport) GetDateOfDisconnection() *date.Date {
	if x != nil {
		return x.DateOfDisconnection
	}
	return nil
}

func (x *MnrlReport) GetActionReasons() string {
	if x != nil {
		return x.ActionReasons
	}
	return ""
}

func (x *MnrlReport) GetUsecase() enums.MnrlUsecase {
	if x != nil {
		return x.Usecase
	}
	return enums.MnrlUsecase(0)
}

func (x *MnrlReport) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *MnrlReport) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_api_risk_mnrl_mnrl_report_proto protoreflect.FileDescriptor

var file_api_risk_mnrl_mnrl_report_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6d, 0x6e, 0x72, 0x6c, 0x2f,
	0x6d, 0x6e, 0x72, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x04, 0x72, 0x69, 0x73, 0x6b, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69,
	0x73, 0x6b, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x99, 0x03, 0x0a,
	0x0a, 0x4d, 0x6e, 0x72, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x29, 0x0a, 0x08, 0x6c, 0x73, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4c, 0x73, 0x61, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x07, 0x6c, 0x73, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74,
	0x73, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74,
	0x73, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x15, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f,
	0x66, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x13, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a,
	0x0e, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x73, 0x12, 0x2c, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x63, 0x61, 0x73, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4d, 0x6e,
	0x72, 0x6c, 0x55, 0x73, 0x65, 0x63, 0x61, 0x73, 0x65, 0x52, 0x07, 0x75, 0x73, 0x65, 0x63, 0x61,
	0x73, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x4c, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6d, 0x6e, 0x72, 0x6c,
	0x5a, 0x24, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73,
	0x6b, 0x2f, 0x6d, 0x6e, 0x72, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_mnrl_mnrl_report_proto_rawDescOnce sync.Once
	file_api_risk_mnrl_mnrl_report_proto_rawDescData = file_api_risk_mnrl_mnrl_report_proto_rawDesc
)

func file_api_risk_mnrl_mnrl_report_proto_rawDescGZIP() []byte {
	file_api_risk_mnrl_mnrl_report_proto_rawDescOnce.Do(func() {
		file_api_risk_mnrl_mnrl_report_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_mnrl_mnrl_report_proto_rawDescData)
	})
	return file_api_risk_mnrl_mnrl_report_proto_rawDescData
}

var file_api_risk_mnrl_mnrl_report_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_risk_mnrl_mnrl_report_proto_goTypes = []interface{}{
	(*MnrlReport)(nil),            // 0: risk.MnrlReport
	(enums.LsaName)(0),            // 1: enums.LsaName
	(*date.Date)(nil),             // 2: google.type.Date
	(enums.MnrlUsecase)(0),        // 3: enums.MnrlUsecase
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
}
var file_api_risk_mnrl_mnrl_report_proto_depIdxs = []int32{
	1, // 0: risk.MnrlReport.lsa_name:type_name -> enums.LsaName
	2, // 1: risk.MnrlReport.date_of_disconnection:type_name -> google.type.Date
	3, // 2: risk.MnrlReport.usecase:type_name -> enums.MnrlUsecase
	4, // 3: risk.MnrlReport.created_at:type_name -> google.protobuf.Timestamp
	4, // 4: risk.MnrlReport.updated_at:type_name -> google.protobuf.Timestamp
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_risk_mnrl_mnrl_report_proto_init() }
func file_api_risk_mnrl_mnrl_report_proto_init() {
	if File_api_risk_mnrl_mnrl_report_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_mnrl_mnrl_report_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MnrlReport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_mnrl_mnrl_report_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_mnrl_mnrl_report_proto_goTypes,
		DependencyIndexes: file_api_risk_mnrl_mnrl_report_proto_depIdxs,
		MessageInfos:      file_api_risk_mnrl_mnrl_report_proto_msgTypes,
	}.Build()
	File_api_risk_mnrl_mnrl_report_proto = out.File
	file_api_risk_mnrl_mnrl_report_proto_rawDesc = nil
	file_api_risk_mnrl_mnrl_report_proto_goTypes = nil
	file_api_risk_mnrl_mnrl_report_proto_depIdxs = nil
}
