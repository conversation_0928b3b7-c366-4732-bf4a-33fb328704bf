// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/risk/mnrl
package mnrl

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	AddMnrlReportMethod                 = "AddMnrlReport"
	AddMnrlSuspectedFlaggedMobileMethod = "AddMnrlSuspectedFlaggedMobile"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &MnrlReportIngestEvent{}
var _ queue.ConsumerRequest = &MnrlSuspectedFlaggedMobileIngestEvent{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *MnrlReportIngestEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *MnrlSuspectedFlaggedMobileIngestEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterAddMnrlReportMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterAddMnrlReportMethodToSubscriber(subscriber queue.Subscriber, srv MnrlConsumerServer) {
	subscriber.RegisterService(&MnrlConsumer_ServiceDesc, srv, AddMnrlReportMethod)
}

// RegisterAddMnrlSuspectedFlaggedMobileMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterAddMnrlSuspectedFlaggedMobileMethodToSubscriber(subscriber queue.Subscriber, srv MnrlConsumerServer) {
	subscriber.RegisterService(&MnrlConsumer_ServiceDesc, srv, AddMnrlSuspectedFlaggedMobileMethod)
}
