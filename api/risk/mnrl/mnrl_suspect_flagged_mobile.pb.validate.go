// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/mnrl/mnrl_suspect_flagged_mobile.proto

package mnrl

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/risk/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.MnrlUsecase(0)
)

// Validate checks the field values on MnrlSuspectFlaggedMobile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MnrlSuspectFlaggedMobile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MnrlSuspectFlaggedMobile with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MnrlSuspectFlaggedMobileMultiError, or nil if none found.
func (m *MnrlSuspectFlaggedMobile) ValidateAll() error {
	return m.validate(true)
}

func (m *MnrlSuspectFlaggedMobile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MobileNumber

	// no validation rules for Reason

	if all {
		switch v := interface{}(m.GetDateForReverification()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MnrlSuspectFlaggedMobileValidationError{
					field:  "DateForReverification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MnrlSuspectFlaggedMobileValidationError{
					field:  "DateForReverification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateForReverification()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MnrlSuspectFlaggedMobileValidationError{
				field:  "DateForReverification",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Usecase

	// no validation rules for LsaCode

	// no validation rules for TspName

	// no validation rules for SensitivityIndex

	// no validation rules for DistributionDetails

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MnrlSuspectFlaggedMobileValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MnrlSuspectFlaggedMobileValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MnrlSuspectFlaggedMobileValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MnrlSuspectFlaggedMobileValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MnrlSuspectFlaggedMobileValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MnrlSuspectFlaggedMobileValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MnrlSuspectFlaggedMobileMultiError(errors)
	}

	return nil
}

// MnrlSuspectFlaggedMobileMultiError is an error wrapping multiple validation
// errors returned by MnrlSuspectFlaggedMobile.ValidateAll() if the designated
// constraints aren't met.
type MnrlSuspectFlaggedMobileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MnrlSuspectFlaggedMobileMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MnrlSuspectFlaggedMobileMultiError) AllErrors() []error { return m }

// MnrlSuspectFlaggedMobileValidationError is the validation error returned by
// MnrlSuspectFlaggedMobile.Validate if the designated constraints aren't met.
type MnrlSuspectFlaggedMobileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MnrlSuspectFlaggedMobileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MnrlSuspectFlaggedMobileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MnrlSuspectFlaggedMobileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MnrlSuspectFlaggedMobileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MnrlSuspectFlaggedMobileValidationError) ErrorName() string {
	return "MnrlSuspectFlaggedMobileValidationError"
}

// Error satisfies the builtin error interface
func (e MnrlSuspectFlaggedMobileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMnrlSuspectFlaggedMobile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MnrlSuspectFlaggedMobileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MnrlSuspectFlaggedMobileValidationError{}
