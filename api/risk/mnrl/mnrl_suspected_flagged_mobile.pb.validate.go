// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/mnrl/mnrl_suspected_flagged_mobile.proto

package mnrl

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/risk/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.MnrlUsecase(0)
)

// Validate checks the field values on MnrlSuspectedFlaggedMobile with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MnrlSuspectedFlaggedMobile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MnrlSuspectedFlaggedMobile with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MnrlSuspectedFlaggedMobileMultiError, or nil if none found.
func (m *MnrlSuspectedFlaggedMobile) ValidateAll() error {
	return m.validate(true)
}

func (m *MnrlSuspectedFlaggedMobile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MobileNumber

	// no validation rules for Reason

	if all {
		switch v := interface{}(m.GetReverifiedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MnrlSuspectedFlaggedMobileValidationError{
					field:  "ReverifiedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MnrlSuspectedFlaggedMobileValidationError{
					field:  "ReverifiedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReverifiedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MnrlSuspectedFlaggedMobileValidationError{
				field:  "ReverifiedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Usecase

	// no validation rules for LsaCode

	// no validation rules for TspName

	// no validation rules for SensitivityIndex

	// no validation rules for DistributionDetails

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MnrlSuspectedFlaggedMobileValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MnrlSuspectedFlaggedMobileValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MnrlSuspectedFlaggedMobileValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MnrlSuspectedFlaggedMobileValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MnrlSuspectedFlaggedMobileValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MnrlSuspectedFlaggedMobileValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MnrlSuspectedFlaggedMobileMultiError(errors)
	}

	return nil
}

// MnrlSuspectedFlaggedMobileMultiError is an error wrapping multiple
// validation errors returned by MnrlSuspectedFlaggedMobile.ValidateAll() if
// the designated constraints aren't met.
type MnrlSuspectedFlaggedMobileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MnrlSuspectedFlaggedMobileMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MnrlSuspectedFlaggedMobileMultiError) AllErrors() []error { return m }

// MnrlSuspectedFlaggedMobileValidationError is the validation error returned
// by MnrlSuspectedFlaggedMobile.Validate if the designated constraints aren't met.
type MnrlSuspectedFlaggedMobileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MnrlSuspectedFlaggedMobileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MnrlSuspectedFlaggedMobileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MnrlSuspectedFlaggedMobileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MnrlSuspectedFlaggedMobileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MnrlSuspectedFlaggedMobileValidationError) ErrorName() string {
	return "MnrlSuspectedFlaggedMobileValidationError"
}

// Error satisfies the builtin error interface
func (e MnrlSuspectedFlaggedMobileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMnrlSuspectedFlaggedMobile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MnrlSuspectedFlaggedMobileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MnrlSuspectedFlaggedMobileValidationError{}
