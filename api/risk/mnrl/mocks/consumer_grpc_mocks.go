// Code generated by MockGen. DO NOT EDIT.
// Source: api/risk/mnrl/consumer_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	mnrl "github.com/epifi/gamma/api/risk/mnrl"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockMnrlConsumerClient is a mock of MnrlConsumerClient interface.
type MockMnrlConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockMnrlConsumerClientMockRecorder
}

// MockMnrlConsumerClientMockRecorder is the mock recorder for MockMnrlConsumerClient.
type MockMnrlConsumerClientMockRecorder struct {
	mock *MockMnrlConsumerClient
}

// NewMockMnrlConsumerClient creates a new mock instance.
func NewMockMnrlConsumerClient(ctrl *gomock.Controller) *MockMnrlConsumerClient {
	mock := &MockMnrlConsumerClient{ctrl: ctrl}
	mock.recorder = &MockMnrlConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMnrlConsumerClient) EXPECT() *MockMnrlConsumerClientMockRecorder {
	return m.recorder
}

// AddMnrlReport mocks base method.
func (m *MockMnrlConsumerClient) AddMnrlReport(ctx context.Context, in *mnrl.MnrlReportIngestEvent, opts ...grpc.CallOption) (*mnrl.AddMnrlReportResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddMnrlReport", varargs...)
	ret0, _ := ret[0].(*mnrl.AddMnrlReportResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMnrlReport indicates an expected call of AddMnrlReport.
func (mr *MockMnrlConsumerClientMockRecorder) AddMnrlReport(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMnrlReport", reflect.TypeOf((*MockMnrlConsumerClient)(nil).AddMnrlReport), varargs...)
}

// AddMnrlSuspectedFlaggedMobile mocks base method.
func (m *MockMnrlConsumerClient) AddMnrlSuspectedFlaggedMobile(ctx context.Context, in *mnrl.MnrlSuspectedFlaggedMobileIngestEvent, opts ...grpc.CallOption) (*mnrl.AddMnrlSuspectedFlaggedMobileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddMnrlSuspectedFlaggedMobile", varargs...)
	ret0, _ := ret[0].(*mnrl.AddMnrlSuspectedFlaggedMobileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMnrlSuspectedFlaggedMobile indicates an expected call of AddMnrlSuspectedFlaggedMobile.
func (mr *MockMnrlConsumerClientMockRecorder) AddMnrlSuspectedFlaggedMobile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMnrlSuspectedFlaggedMobile", reflect.TypeOf((*MockMnrlConsumerClient)(nil).AddMnrlSuspectedFlaggedMobile), varargs...)
}

// MockMnrlConsumerServer is a mock of MnrlConsumerServer interface.
type MockMnrlConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockMnrlConsumerServerMockRecorder
}

// MockMnrlConsumerServerMockRecorder is the mock recorder for MockMnrlConsumerServer.
type MockMnrlConsumerServerMockRecorder struct {
	mock *MockMnrlConsumerServer
}

// NewMockMnrlConsumerServer creates a new mock instance.
func NewMockMnrlConsumerServer(ctrl *gomock.Controller) *MockMnrlConsumerServer {
	mock := &MockMnrlConsumerServer{ctrl: ctrl}
	mock.recorder = &MockMnrlConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMnrlConsumerServer) EXPECT() *MockMnrlConsumerServerMockRecorder {
	return m.recorder
}

// AddMnrlReport mocks base method.
func (m *MockMnrlConsumerServer) AddMnrlReport(arg0 context.Context, arg1 *mnrl.MnrlReportIngestEvent) (*mnrl.AddMnrlReportResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMnrlReport", arg0, arg1)
	ret0, _ := ret[0].(*mnrl.AddMnrlReportResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMnrlReport indicates an expected call of AddMnrlReport.
func (mr *MockMnrlConsumerServerMockRecorder) AddMnrlReport(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMnrlReport", reflect.TypeOf((*MockMnrlConsumerServer)(nil).AddMnrlReport), arg0, arg1)
}

// AddMnrlSuspectedFlaggedMobile mocks base method.
func (m *MockMnrlConsumerServer) AddMnrlSuspectedFlaggedMobile(arg0 context.Context, arg1 *mnrl.MnrlSuspectedFlaggedMobileIngestEvent) (*mnrl.AddMnrlSuspectedFlaggedMobileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMnrlSuspectedFlaggedMobile", arg0, arg1)
	ret0, _ := ret[0].(*mnrl.AddMnrlSuspectedFlaggedMobileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMnrlSuspectedFlaggedMobile indicates an expected call of AddMnrlSuspectedFlaggedMobile.
func (mr *MockMnrlConsumerServerMockRecorder) AddMnrlSuspectedFlaggedMobile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMnrlSuspectedFlaggedMobile", reflect.TypeOf((*MockMnrlConsumerServer)(nil).AddMnrlSuspectedFlaggedMobile), arg0, arg1)
}

// MockUnsafeMnrlConsumerServer is a mock of UnsafeMnrlConsumerServer interface.
type MockUnsafeMnrlConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeMnrlConsumerServerMockRecorder
}

// MockUnsafeMnrlConsumerServerMockRecorder is the mock recorder for MockUnsafeMnrlConsumerServer.
type MockUnsafeMnrlConsumerServerMockRecorder struct {
	mock *MockUnsafeMnrlConsumerServer
}

// NewMockUnsafeMnrlConsumerServer creates a new mock instance.
func NewMockUnsafeMnrlConsumerServer(ctrl *gomock.Controller) *MockUnsafeMnrlConsumerServer {
	mock := &MockUnsafeMnrlConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeMnrlConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeMnrlConsumerServer) EXPECT() *MockUnsafeMnrlConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedMnrlConsumerServer mocks base method.
func (m *MockUnsafeMnrlConsumerServer) mustEmbedUnimplementedMnrlConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMnrlConsumerServer")
}

// mustEmbedUnimplementedMnrlConsumerServer indicates an expected call of mustEmbedUnimplementedMnrlConsumerServer.
func (mr *MockUnsafeMnrlConsumerServerMockRecorder) mustEmbedUnimplementedMnrlConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMnrlConsumerServer", reflect.TypeOf((*MockUnsafeMnrlConsumerServer)(nil).mustEmbedUnimplementedMnrlConsumerServer))
}
