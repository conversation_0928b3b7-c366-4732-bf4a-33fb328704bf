// go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/mnrl/consumer.proto

package mnrl

import (
	queue "github.com/epifi/be-common/api/queue"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MnrlReportIngestEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	MnrlReports   []*MnrlReport                `protobuf:"bytes,2,rep,name=mnrl_reports,json=mnrlReports,proto3" json:"mnrl_reports,omitempty"`
}

func (x *MnrlReportIngestEvent) Reset() {
	*x = MnrlReportIngestEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_mnrl_consumer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MnrlReportIngestEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MnrlReportIngestEvent) ProtoMessage() {}

func (x *MnrlReportIngestEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_mnrl_consumer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MnrlReportIngestEvent.ProtoReflect.Descriptor instead.
func (*MnrlReportIngestEvent) Descriptor() ([]byte, []int) {
	return file_api_risk_mnrl_consumer_proto_rawDescGZIP(), []int{0}
}

func (x *MnrlReportIngestEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *MnrlReportIngestEvent) GetMnrlReports() []*MnrlReport {
	if x != nil {
		return x.MnrlReports
	}
	return nil
}

type MnrlSuspectedFlaggedMobileIngestEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader               *queue.ConsumerRequestHeader  `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	MnrlSuspectedFlaggedMobiles []*MnrlSuspectedFlaggedMobile `protobuf:"bytes,2,rep,name=mnrl_suspected_flagged_mobiles,json=mnrlSuspectedFlaggedMobiles,proto3" json:"mnrl_suspected_flagged_mobiles,omitempty"`
}

func (x *MnrlSuspectedFlaggedMobileIngestEvent) Reset() {
	*x = MnrlSuspectedFlaggedMobileIngestEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_mnrl_consumer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MnrlSuspectedFlaggedMobileIngestEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MnrlSuspectedFlaggedMobileIngestEvent) ProtoMessage() {}

func (x *MnrlSuspectedFlaggedMobileIngestEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_mnrl_consumer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MnrlSuspectedFlaggedMobileIngestEvent.ProtoReflect.Descriptor instead.
func (*MnrlSuspectedFlaggedMobileIngestEvent) Descriptor() ([]byte, []int) {
	return file_api_risk_mnrl_consumer_proto_rawDescGZIP(), []int{1}
}

func (x *MnrlSuspectedFlaggedMobileIngestEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *MnrlSuspectedFlaggedMobileIngestEvent) GetMnrlSuspectedFlaggedMobiles() []*MnrlSuspectedFlaggedMobile {
	if x != nil {
		return x.MnrlSuspectedFlaggedMobiles
	}
	return nil
}

type AddMnrlReportResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *AddMnrlReportResponse) Reset() {
	*x = AddMnrlReportResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_mnrl_consumer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddMnrlReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMnrlReportResponse) ProtoMessage() {}

func (x *AddMnrlReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_mnrl_consumer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMnrlReportResponse.ProtoReflect.Descriptor instead.
func (*AddMnrlReportResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_mnrl_consumer_proto_rawDescGZIP(), []int{2}
}

func (x *AddMnrlReportResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type AddMnrlSuspectedFlaggedMobileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *AddMnrlSuspectedFlaggedMobileResponse) Reset() {
	*x = AddMnrlSuspectedFlaggedMobileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_mnrl_consumer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddMnrlSuspectedFlaggedMobileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMnrlSuspectedFlaggedMobileResponse) ProtoMessage() {}

func (x *AddMnrlSuspectedFlaggedMobileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_mnrl_consumer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMnrlSuspectedFlaggedMobileResponse.ProtoReflect.Descriptor instead.
func (*AddMnrlSuspectedFlaggedMobileResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_mnrl_consumer_proto_rawDescGZIP(), []int{3}
}

func (x *AddMnrlSuspectedFlaggedMobileResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_risk_mnrl_consumer_proto protoreflect.FileDescriptor

var file_api_risk_mnrl_consumer_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6d, 0x6e, 0x72, 0x6c, 0x2f,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6d, 0x6e, 0x72, 0x6c, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6d, 0x6e, 0x72, 0x6c, 0x2f, 0x6d, 0x6e, 0x72, 0x6c, 0x5f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6d, 0x6e, 0x72, 0x6c, 0x2f, 0x6d, 0x6e, 0x72, 0x6c,
	0x5f, 0x73, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x67,
	0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x91, 0x01, 0x0a, 0x15, 0x4d, 0x6e, 0x72, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e,
	0x67, 0x65, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x33,
	0x0a, 0x0c, 0x6d, 0x6e, 0x72, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4d, 0x6e, 0x72, 0x6c,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x0b, 0x6d, 0x6e, 0x72, 0x6c, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x22, 0xd3, 0x01, 0x0a, 0x25, 0x4d, 0x6e, 0x72, 0x6c, 0x53, 0x75, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x67, 0x65, 0x64, 0x4d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x49, 0x6e, 0x67, 0x65, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x65, 0x0a, 0x1e, 0x6d, 0x6e, 0x72, 0x6c, 0x5f, 0x73, 0x75, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x67, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x4d, 0x6e, 0x72, 0x6c, 0x53, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x46,
	0x6c, 0x61, 0x67, 0x67, 0x65, 0x64, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x1b, 0x6d, 0x6e,
	0x72, 0x6c, 0x53, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x67,
	0x65, 0x64, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x73, 0x22, 0x5f, 0x0a, 0x15, 0x41, 0x64, 0x64,
	0x4d, 0x6e, 0x72, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x6f, 0x0a, 0x25, 0x41, 0x64,
	0x64, 0x4d, 0x6e, 0x72, 0x6c, 0x53, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x46, 0x6c,
	0x61, 0x67, 0x67, 0x65, 0x64, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0xe9, 0x01, 0x0a, 0x0c,
	0x4d, 0x6e, 0x72, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x12, 0x53, 0x0a, 0x0d,
	0x41, 0x64, 0x64, 0x4d, 0x6e, 0x72, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x20, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6d, 0x6e, 0x72, 0x6c, 0x2e, 0x4d, 0x6e, 0x72, 0x6c, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x67, 0x65, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a,
	0x20, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6d, 0x6e, 0x72, 0x6c, 0x2e, 0x41, 0x64, 0x64, 0x4d,
	0x6e, 0x72, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x83, 0x01, 0x0a, 0x1d, 0x41, 0x64, 0x64, 0x4d, 0x6e, 0x72, 0x6c, 0x53, 0x75, 0x73,
	0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x67, 0x65, 0x64, 0x4d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x12, 0x30, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6d, 0x6e, 0x72, 0x6c, 0x2e,
	0x4d, 0x6e, 0x72, 0x6c, 0x53, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x46, 0x6c, 0x61,
	0x67, 0x67, 0x65, 0x64, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x67, 0x65, 0x73, 0x74,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x30, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6d, 0x6e, 0x72,
	0x6c, 0x2e, 0x41, 0x64, 0x64, 0x4d, 0x6e, 0x72, 0x6c, 0x53, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x67, 0x65, 0x64, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x4c, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6d, 0x6e, 0x72, 0x6c, 0x5a,
	0x24, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b,
	0x2f, 0x6d, 0x6e, 0x72, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_mnrl_consumer_proto_rawDescOnce sync.Once
	file_api_risk_mnrl_consumer_proto_rawDescData = file_api_risk_mnrl_consumer_proto_rawDesc
)

func file_api_risk_mnrl_consumer_proto_rawDescGZIP() []byte {
	file_api_risk_mnrl_consumer_proto_rawDescOnce.Do(func() {
		file_api_risk_mnrl_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_mnrl_consumer_proto_rawDescData)
	})
	return file_api_risk_mnrl_consumer_proto_rawDescData
}

var file_api_risk_mnrl_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_risk_mnrl_consumer_proto_goTypes = []interface{}{
	(*MnrlReportIngestEvent)(nil),                 // 0: risk.mnrl.MnrlReportIngestEvent
	(*MnrlSuspectedFlaggedMobileIngestEvent)(nil), // 1: risk.mnrl.MnrlSuspectedFlaggedMobileIngestEvent
	(*AddMnrlReportResponse)(nil),                 // 2: risk.mnrl.AddMnrlReportResponse
	(*AddMnrlSuspectedFlaggedMobileResponse)(nil), // 3: risk.mnrl.AddMnrlSuspectedFlaggedMobileResponse
	(*queue.ConsumerRequestHeader)(nil),           // 4: queue.ConsumerRequestHeader
	(*MnrlReport)(nil),                            // 5: risk.MnrlReport
	(*MnrlSuspectedFlaggedMobile)(nil),            // 6: risk.MnrlSuspectedFlaggedMobile
	(*queue.ConsumerResponseHeader)(nil),          // 7: queue.ConsumerResponseHeader
}
var file_api_risk_mnrl_consumer_proto_depIdxs = []int32{
	4, // 0: risk.mnrl.MnrlReportIngestEvent.request_header:type_name -> queue.ConsumerRequestHeader
	5, // 1: risk.mnrl.MnrlReportIngestEvent.mnrl_reports:type_name -> risk.MnrlReport
	4, // 2: risk.mnrl.MnrlSuspectedFlaggedMobileIngestEvent.request_header:type_name -> queue.ConsumerRequestHeader
	6, // 3: risk.mnrl.MnrlSuspectedFlaggedMobileIngestEvent.mnrl_suspected_flagged_mobiles:type_name -> risk.MnrlSuspectedFlaggedMobile
	7, // 4: risk.mnrl.AddMnrlReportResponse.response_header:type_name -> queue.ConsumerResponseHeader
	7, // 5: risk.mnrl.AddMnrlSuspectedFlaggedMobileResponse.response_header:type_name -> queue.ConsumerResponseHeader
	0, // 6: risk.mnrl.MnrlConsumer.AddMnrlReport:input_type -> risk.mnrl.MnrlReportIngestEvent
	1, // 7: risk.mnrl.MnrlConsumer.AddMnrlSuspectedFlaggedMobile:input_type -> risk.mnrl.MnrlSuspectedFlaggedMobileIngestEvent
	2, // 8: risk.mnrl.MnrlConsumer.AddMnrlReport:output_type -> risk.mnrl.AddMnrlReportResponse
	3, // 9: risk.mnrl.MnrlConsumer.AddMnrlSuspectedFlaggedMobile:output_type -> risk.mnrl.AddMnrlSuspectedFlaggedMobileResponse
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_risk_mnrl_consumer_proto_init() }
func file_api_risk_mnrl_consumer_proto_init() {
	if File_api_risk_mnrl_consumer_proto != nil {
		return
	}
	file_api_risk_mnrl_mnrl_report_proto_init()
	file_api_risk_mnrl_mnrl_suspected_flagged_mobile_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_mnrl_consumer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MnrlReportIngestEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_mnrl_consumer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MnrlSuspectedFlaggedMobileIngestEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_mnrl_consumer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddMnrlReportResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_mnrl_consumer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddMnrlSuspectedFlaggedMobileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_mnrl_consumer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_risk_mnrl_consumer_proto_goTypes,
		DependencyIndexes: file_api_risk_mnrl_consumer_proto_depIdxs,
		MessageInfos:      file_api_risk_mnrl_consumer_proto_msgTypes,
	}.Build()
	File_api_risk_mnrl_consumer_proto = out.File
	file_api_risk_mnrl_consumer_proto_rawDesc = nil
	file_api_risk_mnrl_consumer_proto_goTypes = nil
	file_api_risk_mnrl_consumer_proto_depIdxs = nil
}
