// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/mnrl/mnrl_report.proto

package mnrl

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/risk/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.LsaName(0)
)

// Validate checks the field values on MnrlReport with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MnrlReport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MnrlReport with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MnrlReportMultiError, or
// nil if none found.
func (m *MnrlReport) ValidateAll() error {
	return m.validate(true)
}

func (m *MnrlReport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MobileNumber

	// no validation rules for LsaName

	// no validation rules for TspName

	if all {
		switch v := interface{}(m.GetDateOfDisconnection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MnrlReportValidationError{
					field:  "DateOfDisconnection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MnrlReportValidationError{
					field:  "DateOfDisconnection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateOfDisconnection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MnrlReportValidationError{
				field:  "DateOfDisconnection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActionReasons

	// no validation rules for Usecase

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MnrlReportValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MnrlReportValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MnrlReportValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MnrlReportValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MnrlReportValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MnrlReportValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MnrlReportMultiError(errors)
	}

	return nil
}

// MnrlReportMultiError is an error wrapping multiple validation errors
// returned by MnrlReport.ValidateAll() if the designated constraints aren't met.
type MnrlReportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MnrlReportMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MnrlReportMultiError) AllErrors() []error { return m }

// MnrlReportValidationError is the validation error returned by
// MnrlReport.Validate if the designated constraints aren't met.
type MnrlReportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MnrlReportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MnrlReportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MnrlReportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MnrlReportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MnrlReportValidationError) ErrorName() string { return "MnrlReportValidationError" }

// Error satisfies the builtin error interface
func (e MnrlReportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMnrlReport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MnrlReportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MnrlReportValidationError{}
