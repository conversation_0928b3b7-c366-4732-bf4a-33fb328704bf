// go:generate gen_queue_pb

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/risk/mnrl/consumer.proto

package mnrl

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MnrlConsumer_AddMnrlReport_FullMethodName                 = "/risk.mnrl.MnrlConsumer/AddMnrlReport"
	MnrlConsumer_AddMnrlSuspectedFlaggedMobile_FullMethodName = "/risk.mnrl.MnrlConsumer/AddMnrlSuspectedFlaggedMobile"
)

// MnrlConsumerClient is the client API for MnrlConsumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MnrlConsumerClient interface {
	AddMnrlReport(ctx context.Context, in *MnrlReportIngestEvent, opts ...grpc.CallOption) (*AddMnrlReportResponse, error)
	AddMnrlSuspectedFlaggedMobile(ctx context.Context, in *MnrlSuspectedFlaggedMobileIngestEvent, opts ...grpc.CallOption) (*AddMnrlSuspectedFlaggedMobileResponse, error)
}

type mnrlConsumerClient struct {
	cc grpc.ClientConnInterface
}

func NewMnrlConsumerClient(cc grpc.ClientConnInterface) MnrlConsumerClient {
	return &mnrlConsumerClient{cc}
}

func (c *mnrlConsumerClient) AddMnrlReport(ctx context.Context, in *MnrlReportIngestEvent, opts ...grpc.CallOption) (*AddMnrlReportResponse, error) {
	out := new(AddMnrlReportResponse)
	err := c.cc.Invoke(ctx, MnrlConsumer_AddMnrlReport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mnrlConsumerClient) AddMnrlSuspectedFlaggedMobile(ctx context.Context, in *MnrlSuspectedFlaggedMobileIngestEvent, opts ...grpc.CallOption) (*AddMnrlSuspectedFlaggedMobileResponse, error) {
	out := new(AddMnrlSuspectedFlaggedMobileResponse)
	err := c.cc.Invoke(ctx, MnrlConsumer_AddMnrlSuspectedFlaggedMobile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MnrlConsumerServer is the server API for MnrlConsumer service.
// All implementations should embed UnimplementedMnrlConsumerServer
// for forward compatibility
type MnrlConsumerServer interface {
	AddMnrlReport(context.Context, *MnrlReportIngestEvent) (*AddMnrlReportResponse, error)
	AddMnrlSuspectedFlaggedMobile(context.Context, *MnrlSuspectedFlaggedMobileIngestEvent) (*AddMnrlSuspectedFlaggedMobileResponse, error)
}

// UnimplementedMnrlConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedMnrlConsumerServer struct {
}

func (UnimplementedMnrlConsumerServer) AddMnrlReport(context.Context, *MnrlReportIngestEvent) (*AddMnrlReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddMnrlReport not implemented")
}
func (UnimplementedMnrlConsumerServer) AddMnrlSuspectedFlaggedMobile(context.Context, *MnrlSuspectedFlaggedMobileIngestEvent) (*AddMnrlSuspectedFlaggedMobileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddMnrlSuspectedFlaggedMobile not implemented")
}

// UnsafeMnrlConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MnrlConsumerServer will
// result in compilation errors.
type UnsafeMnrlConsumerServer interface {
	mustEmbedUnimplementedMnrlConsumerServer()
}

func RegisterMnrlConsumerServer(s grpc.ServiceRegistrar, srv MnrlConsumerServer) {
	s.RegisterService(&MnrlConsumer_ServiceDesc, srv)
}

func _MnrlConsumer_AddMnrlReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MnrlReportIngestEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MnrlConsumerServer).AddMnrlReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MnrlConsumer_AddMnrlReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MnrlConsumerServer).AddMnrlReport(ctx, req.(*MnrlReportIngestEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _MnrlConsumer_AddMnrlSuspectedFlaggedMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MnrlSuspectedFlaggedMobileIngestEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MnrlConsumerServer).AddMnrlSuspectedFlaggedMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MnrlConsumer_AddMnrlSuspectedFlaggedMobile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MnrlConsumerServer).AddMnrlSuspectedFlaggedMobile(ctx, req.(*MnrlSuspectedFlaggedMobileIngestEvent))
	}
	return interceptor(ctx, in, info, handler)
}

// MnrlConsumer_ServiceDesc is the grpc.ServiceDesc for MnrlConsumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MnrlConsumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "risk.mnrl.MnrlConsumer",
	HandlerType: (*MnrlConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddMnrlReport",
			Handler:    _MnrlConsumer_AddMnrlReport_Handler,
		},
		{
			MethodName: "AddMnrlSuspectedFlaggedMobile",
			Handler:    _MnrlConsumer_AddMnrlSuspectedFlaggedMobile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/risk/mnrl/consumer.proto",
}
