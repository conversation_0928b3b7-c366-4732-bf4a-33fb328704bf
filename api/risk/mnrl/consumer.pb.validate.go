// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/mnrl/consumer.proto

package mnrl

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MnrlReportIngestEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MnrlReportIngestEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MnrlReportIngestEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MnrlReportIngestEventMultiError, or nil if none found.
func (m *MnrlReportIngestEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *MnrlReportIngestEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MnrlReportIngestEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MnrlReportIngestEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MnrlReportIngestEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMnrlReports() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MnrlReportIngestEventValidationError{
						field:  fmt.Sprintf("MnrlReports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MnrlReportIngestEventValidationError{
						field:  fmt.Sprintf("MnrlReports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MnrlReportIngestEventValidationError{
					field:  fmt.Sprintf("MnrlReports[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MnrlReportIngestEventMultiError(errors)
	}

	return nil
}

// MnrlReportIngestEventMultiError is an error wrapping multiple validation
// errors returned by MnrlReportIngestEvent.ValidateAll() if the designated
// constraints aren't met.
type MnrlReportIngestEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MnrlReportIngestEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MnrlReportIngestEventMultiError) AllErrors() []error { return m }

// MnrlReportIngestEventValidationError is the validation error returned by
// MnrlReportIngestEvent.Validate if the designated constraints aren't met.
type MnrlReportIngestEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MnrlReportIngestEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MnrlReportIngestEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MnrlReportIngestEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MnrlReportIngestEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MnrlReportIngestEventValidationError) ErrorName() string {
	return "MnrlReportIngestEventValidationError"
}

// Error satisfies the builtin error interface
func (e MnrlReportIngestEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMnrlReportIngestEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MnrlReportIngestEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MnrlReportIngestEventValidationError{}

// Validate checks the field values on MnrlSuspectedFlaggedMobileIngestEvent
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *MnrlSuspectedFlaggedMobileIngestEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MnrlSuspectedFlaggedMobileIngestEvent
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// MnrlSuspectedFlaggedMobileIngestEventMultiError, or nil if none found.
func (m *MnrlSuspectedFlaggedMobileIngestEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *MnrlSuspectedFlaggedMobileIngestEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MnrlSuspectedFlaggedMobileIngestEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MnrlSuspectedFlaggedMobileIngestEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MnrlSuspectedFlaggedMobileIngestEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMnrlSuspectedFlaggedMobiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MnrlSuspectedFlaggedMobileIngestEventValidationError{
						field:  fmt.Sprintf("MnrlSuspectedFlaggedMobiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MnrlSuspectedFlaggedMobileIngestEventValidationError{
						field:  fmt.Sprintf("MnrlSuspectedFlaggedMobiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MnrlSuspectedFlaggedMobileIngestEventValidationError{
					field:  fmt.Sprintf("MnrlSuspectedFlaggedMobiles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MnrlSuspectedFlaggedMobileIngestEventMultiError(errors)
	}

	return nil
}

// MnrlSuspectedFlaggedMobileIngestEventMultiError is an error wrapping
// multiple validation errors returned by
// MnrlSuspectedFlaggedMobileIngestEvent.ValidateAll() if the designated
// constraints aren't met.
type MnrlSuspectedFlaggedMobileIngestEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MnrlSuspectedFlaggedMobileIngestEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MnrlSuspectedFlaggedMobileIngestEventMultiError) AllErrors() []error { return m }

// MnrlSuspectedFlaggedMobileIngestEventValidationError is the validation error
// returned by MnrlSuspectedFlaggedMobileIngestEvent.Validate if the
// designated constraints aren't met.
type MnrlSuspectedFlaggedMobileIngestEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MnrlSuspectedFlaggedMobileIngestEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MnrlSuspectedFlaggedMobileIngestEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MnrlSuspectedFlaggedMobileIngestEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MnrlSuspectedFlaggedMobileIngestEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MnrlSuspectedFlaggedMobileIngestEventValidationError) ErrorName() string {
	return "MnrlSuspectedFlaggedMobileIngestEventValidationError"
}

// Error satisfies the builtin error interface
func (e MnrlSuspectedFlaggedMobileIngestEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMnrlSuspectedFlaggedMobileIngestEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MnrlSuspectedFlaggedMobileIngestEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MnrlSuspectedFlaggedMobileIngestEventValidationError{}

// Validate checks the field values on AddMnrlReportResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddMnrlReportResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddMnrlReportResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddMnrlReportResponseMultiError, or nil if none found.
func (m *AddMnrlReportResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddMnrlReportResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddMnrlReportResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddMnrlReportResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddMnrlReportResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddMnrlReportResponseMultiError(errors)
	}

	return nil
}

// AddMnrlReportResponseMultiError is an error wrapping multiple validation
// errors returned by AddMnrlReportResponse.ValidateAll() if the designated
// constraints aren't met.
type AddMnrlReportResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddMnrlReportResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddMnrlReportResponseMultiError) AllErrors() []error { return m }

// AddMnrlReportResponseValidationError is the validation error returned by
// AddMnrlReportResponse.Validate if the designated constraints aren't met.
type AddMnrlReportResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddMnrlReportResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddMnrlReportResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddMnrlReportResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddMnrlReportResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddMnrlReportResponseValidationError) ErrorName() string {
	return "AddMnrlReportResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddMnrlReportResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddMnrlReportResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddMnrlReportResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddMnrlReportResponseValidationError{}

// Validate checks the field values on AddMnrlSuspectedFlaggedMobileResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AddMnrlSuspectedFlaggedMobileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddMnrlSuspectedFlaggedMobileResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AddMnrlSuspectedFlaggedMobileResponseMultiError, or nil if none found.
func (m *AddMnrlSuspectedFlaggedMobileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddMnrlSuspectedFlaggedMobileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddMnrlSuspectedFlaggedMobileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddMnrlSuspectedFlaggedMobileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddMnrlSuspectedFlaggedMobileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddMnrlSuspectedFlaggedMobileResponseMultiError(errors)
	}

	return nil
}

// AddMnrlSuspectedFlaggedMobileResponseMultiError is an error wrapping
// multiple validation errors returned by
// AddMnrlSuspectedFlaggedMobileResponse.ValidateAll() if the designated
// constraints aren't met.
type AddMnrlSuspectedFlaggedMobileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddMnrlSuspectedFlaggedMobileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddMnrlSuspectedFlaggedMobileResponseMultiError) AllErrors() []error { return m }

// AddMnrlSuspectedFlaggedMobileResponseValidationError is the validation error
// returned by AddMnrlSuspectedFlaggedMobileResponse.Validate if the
// designated constraints aren't met.
type AddMnrlSuspectedFlaggedMobileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddMnrlSuspectedFlaggedMobileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddMnrlSuspectedFlaggedMobileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddMnrlSuspectedFlaggedMobileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddMnrlSuspectedFlaggedMobileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddMnrlSuspectedFlaggedMobileResponseValidationError) ErrorName() string {
	return "AddMnrlSuspectedFlaggedMobileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddMnrlSuspectedFlaggedMobileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddMnrlSuspectedFlaggedMobileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddMnrlSuspectedFlaggedMobileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddMnrlSuspectedFlaggedMobileResponseValidationError{}
