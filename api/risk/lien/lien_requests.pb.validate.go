// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/lien/lien_requests.proto

package lien

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// define the regex for a UUID once up-front
var _lien_requests_uuidPattern = regexp.MustCompile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")

// Validate checks the field values on LienRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LienRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LienRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LienRequestMultiError, or
// nil if none found.
func (m *LienRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LienRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if err := m._validateUuid(m.GetId()); err != nil {
		err = LienRequestValidationError{
			field:  "Id",
			reason: "value must be a valid UUID",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := LienRequestType_name[int32(m.GetReqType())]; !ok {
		err := LienRequestValidationError{
			field:  "ReqType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for LienId

	// no validation rules for Status

	// no validation rules for LienType

	if utf8.RuneCountInString(m.GetSavingsAccountNumber()) < 1 {
		err := LienRequestValidationError{
			field:  "SavingsAccountNumber",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Amount

	// no validation rules for CurrencyCode

	// no validation rules for ReasonCode

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LienRequestValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LienRequestValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LienRequestValidationError{
				field:  "StartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LienRequestValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LienRequestValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LienRequestValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ChannelRequestId

	if utf8.RuneCountInString(m.GetChannel()) < 1 {
		err := LienRequestValidationError{
			field:  "Channel",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CbsStatus

	// no validation rules for CbsResponse

	// no validation rules for ApiMessage

	// no validation rules for NewAmountValue

	// no validation rules for OldAmountValue

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LienRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LienRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LienRequestValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LienRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LienRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LienRequestValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAtUnix()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LienRequestValidationError{
					field:  "DeletedAtUnix",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LienRequestValidationError{
					field:  "DeletedAtUnix",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAtUnix()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LienRequestValidationError{
				field:  "DeletedAtUnix",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LienRequestMultiError(errors)
	}

	return nil
}

func (m *LienRequest) _validateUuid(uuid string) error {
	if matched := _lien_requests_uuidPattern.MatchString(uuid); !matched {
		return errors.New("invalid uuid format")
	}

	return nil
}

// LienRequestMultiError is an error wrapping multiple validation errors
// returned by LienRequest.ValidateAll() if the designated constraints aren't met.
type LienRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LienRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LienRequestMultiError) AllErrors() []error { return m }

// LienRequestValidationError is the validation error returned by
// LienRequest.Validate if the designated constraints aren't met.
type LienRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LienRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LienRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LienRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LienRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LienRequestValidationError) ErrorName() string { return "LienRequestValidationError" }

// Error satisfies the builtin error interface
func (e LienRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLienRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LienRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LienRequestValidationError{}
