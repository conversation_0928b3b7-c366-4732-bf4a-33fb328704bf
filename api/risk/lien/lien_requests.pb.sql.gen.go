// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/risk/lien/lien_requests.pb.go

package lien

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the LienRequestType in string format in DB
func (p LienRequestType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LienRequestType while reading from DB
func (p *LienRequestType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LienRequestType_value[val]
	if !ok {
		return fmt.Errorf("unexpected LienRequestType value: %s", val)
	}
	*p = LienRequestType(valInt)
	return nil
}

// Marshaler interface implementation for LienRequestType
func (x LienRequestType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LienRequestType
func (x *LienRequestType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LienRequestType(LienRequestType_value[val])
	return nil
}
