//go:generate gen_sql -types=LienRequestType

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/lien/lien_requests.proto

package lien

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// LienRequestType defines the type of lien request
type LienRequestType int32

const (
	LienRequestType_LIEN_REQUEST_TYPE_UNSPECIFIED LienRequestType = 0
	// Add a new lien
	LienRequestType_LIEN_REQUEST_TYPE_ADD LienRequestType = 1
	// Enquire about existing liens
	LienRequestType_LIEN_REQUEST_TYPE_ENQUIRE LienRequestType = 2
	// Modify an existing lien
	LienRequestType_LIEN_REQUEST_TYPE_MODIFY LienRequestType = 3
	// Remove an existing lien
	LienRequestType_LIEN_REQUEST_TYPE_REMOVE LienRequestType = 4
)

// Enum value maps for LienRequestType.
var (
	LienRequestType_name = map[int32]string{
		0: "LIEN_REQUEST_TYPE_UNSPECIFIED",
		1: "LIEN_REQUEST_TYPE_ADD",
		2: "LIEN_REQUEST_TYPE_ENQUIRE",
		3: "LIEN_REQUEST_TYPE_MODIFY",
		4: "LIEN_REQUEST_TYPE_REMOVE",
	}
	LienRequestType_value = map[string]int32{
		"LIEN_REQUEST_TYPE_UNSPECIFIED": 0,
		"LIEN_REQUEST_TYPE_ADD":         1,
		"LIEN_REQUEST_TYPE_ENQUIRE":     2,
		"LIEN_REQUEST_TYPE_MODIFY":      3,
		"LIEN_REQUEST_TYPE_REMOVE":      4,
	}
)

func (x LienRequestType) Enum() *LienRequestType {
	p := new(LienRequestType)
	*p = x
	return p
}

func (x LienRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LienRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_lien_lien_requests_proto_enumTypes[0].Descriptor()
}

func (LienRequestType) Type() protoreflect.EnumType {
	return &file_api_risk_lien_lien_requests_proto_enumTypes[0]
}

func (x LienRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LienRequestType.Descriptor instead.
func (LienRequestType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_lien_lien_requests_proto_rawDescGZIP(), []int{0}
}

// LienRequest represents a lien request on a savings account
type LienRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for the lien request
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The request type in the API call (ADD/ENQUIRE/MODIFY/REMOVE)
	ReqType LienRequestType `protobuf:"varint,2,opt,name=req_type,json=reqType,proto3,enum=risk.lien_requests.LienRequestType" json:"req_type,omitempty"`
	// The id of the lien which is created in the bank system
	LienId string `protobuf:"bytes,3,opt,name=lien_id,json=lienId,proto3" json:"lien_id,omitempty"`
	// The status of the request
	Status string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	// Type of lien
	LienType string `protobuf:"bytes,5,opt,name=lien_type,json=lienType,proto3" json:"lien_type,omitempty"`
	// Saving account number for which the lien action is being taken
	SavingsAccountNumber string `protobuf:"bytes,6,opt,name=savings_account_number,json=savingsAccountNumber,proto3" json:"savings_account_number,omitempty"`
	// The amount to mark as lien
	Amount float32 `protobuf:"fixed32,7,opt,name=amount,proto3" json:"amount,omitempty"`
	// Currency code (e.g., INR/USD)
	CurrencyCode string `protobuf:"bytes,8,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// The reason for placing the lien
	ReasonCode string `protobuf:"bytes,9,opt,name=reason_code,json=reasonCode,proto3" json:"reason_code,omitempty"`
	// Additional remarks
	Remarks string `protobuf:"bytes,10,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// Time from which the lien should start
	StartDate *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// Time until when the lien should last
	EndDate *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// Channel's request identifier
	ChannelRequestId string `protobuf:"bytes,13,opt,name=channel_request_id,json=channelRequestId,proto3" json:"channel_request_id,omitempty"`
	// Channel of the request
	Channel string `protobuf:"bytes,14,opt,name=channel,proto3" json:"channel,omitempty"`
	// CBS status returned by bank API
	CbsStatus string `protobuf:"bytes,15,opt,name=cbs_status,json=cbsStatus,proto3" json:"cbs_status,omitempty"`
	// CBS response returned by bank API
	CbsResponse string `protobuf:"bytes,16,opt,name=cbs_response,json=cbsResponse,proto3" json:"cbs_response,omitempty"`
	// Message returned by the API
	ApiMessage string `protobuf:"bytes,17,opt,name=api_message,json=apiMessage,proto3" json:"api_message,omitempty"`
	// New amount value (used in case of modify request)
	NewAmountValue float32 `protobuf:"fixed32,18,opt,name=new_amount_value,json=newAmountValue,proto3" json:"new_amount_value,omitempty"`
	// Old amount value (used in case of modify request)
	OldAmountValue float32                `protobuf:"fixed32,19,opt,name=old_amount_value,json=oldAmountValue,proto3" json:"old_amount_value,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAtUnix  *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=deleted_at_unix,json=deletedAtUnix,proto3" json:"deleted_at_unix,omitempty"`
}

func (x *LienRequest) Reset() {
	*x = LienRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lien_lien_requests_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LienRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LienRequest) ProtoMessage() {}

func (x *LienRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lien_lien_requests_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LienRequest.ProtoReflect.Descriptor instead.
func (*LienRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_lien_lien_requests_proto_rawDescGZIP(), []int{0}
}

func (x *LienRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LienRequest) GetReqType() LienRequestType {
	if x != nil {
		return x.ReqType
	}
	return LienRequestType_LIEN_REQUEST_TYPE_UNSPECIFIED
}

func (x *LienRequest) GetLienId() string {
	if x != nil {
		return x.LienId
	}
	return ""
}

func (x *LienRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *LienRequest) GetLienType() string {
	if x != nil {
		return x.LienType
	}
	return ""
}

func (x *LienRequest) GetSavingsAccountNumber() string {
	if x != nil {
		return x.SavingsAccountNumber
	}
	return ""
}

func (x *LienRequest) GetAmount() float32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *LienRequest) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *LienRequest) GetReasonCode() string {
	if x != nil {
		return x.ReasonCode
	}
	return ""
}

func (x *LienRequest) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *LienRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *LienRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *LienRequest) GetChannelRequestId() string {
	if x != nil {
		return x.ChannelRequestId
	}
	return ""
}

func (x *LienRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *LienRequest) GetCbsStatus() string {
	if x != nil {
		return x.CbsStatus
	}
	return ""
}

func (x *LienRequest) GetCbsResponse() string {
	if x != nil {
		return x.CbsResponse
	}
	return ""
}

func (x *LienRequest) GetApiMessage() string {
	if x != nil {
		return x.ApiMessage
	}
	return ""
}

func (x *LienRequest) GetNewAmountValue() float32 {
	if x != nil {
		return x.NewAmountValue
	}
	return 0
}

func (x *LienRequest) GetOldAmountValue() float32 {
	if x != nil {
		return x.OldAmountValue
	}
	return 0
}

func (x *LienRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LienRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *LienRequest) GetDeletedAtUnix() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAtUnix
	}
	return nil
}

var File_api_risk_lien_lien_requests_proto protoreflect.FileDescriptor

var file_api_risk_lien_lien_requests_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x69, 0x65, 0x6e, 0x2f,
	0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x12, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xaa, 0x07, 0x0a, 0x0b, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x18, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x72, 0x03, 0xb0, 0x01, 0x01, 0x52, 0x02, 0x69, 0x64, 0x12, 0x48, 0x0a, 0x08, 0x72,
	0x65, 0x71, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x73, 0x2e, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x72, 0x65,
	0x71, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x69, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x69, 0x65, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x16, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x14, 0x73, 0x61,
	0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x12,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x62, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x62, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c,
	0x63, 0x62, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x28, 0x0a, 0x10, 0x6e, 0x65, 0x77, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x6e, 0x65, 0x77, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x6c,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x6f, 0x6c, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x42, 0x0a, 0x0f, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x78, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x55, 0x6e, 0x69, 0x78, 0x2a, 0xaa,
	0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x49, 0x45, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x49, 0x45, 0x4e, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x01,
	0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x49, 0x45, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49, 0x52, 0x45, 0x10, 0x02, 0x12,
	0x1c, 0x0a, 0x18, 0x4c, 0x49, 0x45, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x10, 0x03, 0x12, 0x1c, 0x0a,
	0x18, 0x4c, 0x49, 0x45, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x52, 0x45, 0x4d, 0x4f, 0x56, 0x45, 0x10, 0x04, 0x42, 0x4c, 0x0a, 0x24, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6c,
	0x69, 0x65, 0x6e, 0x5a, 0x24, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x69, 0x65, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_risk_lien_lien_requests_proto_rawDescOnce sync.Once
	file_api_risk_lien_lien_requests_proto_rawDescData = file_api_risk_lien_lien_requests_proto_rawDesc
)

func file_api_risk_lien_lien_requests_proto_rawDescGZIP() []byte {
	file_api_risk_lien_lien_requests_proto_rawDescOnce.Do(func() {
		file_api_risk_lien_lien_requests_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_lien_lien_requests_proto_rawDescData)
	})
	return file_api_risk_lien_lien_requests_proto_rawDescData
}

var file_api_risk_lien_lien_requests_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_risk_lien_lien_requests_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_risk_lien_lien_requests_proto_goTypes = []interface{}{
	(LienRequestType)(0),          // 0: risk.lien_requests.LienRequestType
	(*LienRequest)(nil),           // 1: risk.lien_requests.LienRequest
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
}
var file_api_risk_lien_lien_requests_proto_depIdxs = []int32{
	0, // 0: risk.lien_requests.LienRequest.req_type:type_name -> risk.lien_requests.LienRequestType
	2, // 1: risk.lien_requests.LienRequest.start_date:type_name -> google.protobuf.Timestamp
	2, // 2: risk.lien_requests.LienRequest.end_date:type_name -> google.protobuf.Timestamp
	2, // 3: risk.lien_requests.LienRequest.created_at:type_name -> google.protobuf.Timestamp
	2, // 4: risk.lien_requests.LienRequest.updated_at:type_name -> google.protobuf.Timestamp
	2, // 5: risk.lien_requests.LienRequest.deleted_at_unix:type_name -> google.protobuf.Timestamp
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_risk_lien_lien_requests_proto_init() }
func file_api_risk_lien_lien_requests_proto_init() {
	if File_api_risk_lien_lien_requests_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_lien_lien_requests_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LienRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_lien_lien_requests_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_lien_lien_requests_proto_goTypes,
		DependencyIndexes: file_api_risk_lien_lien_requests_proto_depIdxs,
		EnumInfos:         file_api_risk_lien_lien_requests_proto_enumTypes,
		MessageInfos:      file_api_risk_lien_lien_requests_proto_msgTypes,
	}.Build()
	File_api_risk_lien_lien_requests_proto = out.File
	file_api_risk_lien_lien_requests_proto_rawDesc = nil
	file_api_risk_lien_lien_requests_proto_goTypes = nil
	file_api_risk_lien_lien_requests_proto_depIdxs = nil
}
