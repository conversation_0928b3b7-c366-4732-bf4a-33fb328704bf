// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/lien/lien_manager.proto

package lien

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AddLienRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddLienRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddLienRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddLienRequestMultiError,
// or nil if none found.
func (m *AddLienRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddLienRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetAccountNumber()) < 1 {
		err := AddLienRequestValidationError{
			field:  "AccountNumber",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Amount

	// no validation rules for CurrencyCode

	// no validation rules for ReasonCode

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddLienRequestValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddLienRequestValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddLienRequestValidationError{
				field:  "StartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddLienRequestValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddLienRequestValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddLienRequestValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ChannelRequestId

	// no validation rules for BankActionId

	if len(errors) > 0 {
		return AddLienRequestMultiError(errors)
	}

	return nil
}

// AddLienRequestMultiError is an error wrapping multiple validation errors
// returned by AddLienRequest.ValidateAll() if the designated constraints
// aren't met.
type AddLienRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddLienRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddLienRequestMultiError) AllErrors() []error { return m }

// AddLienRequestValidationError is the validation error returned by
// AddLienRequest.Validate if the designated constraints aren't met.
type AddLienRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddLienRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddLienRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddLienRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddLienRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddLienRequestValidationError) ErrorName() string { return "AddLienRequestValidationError" }

// Error satisfies the builtin error interface
func (e AddLienRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddLienRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddLienRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddLienRequestValidationError{}

// Validate checks the field values on AddLienResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddLienResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddLienResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddLienResponseMultiError, or nil if none found.
func (m *AddLienResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddLienResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CbsStatus

	// no validation rules for CbsResponse

	// no validation rules for ApiMessage

	if len(errors) > 0 {
		return AddLienResponseMultiError(errors)
	}

	return nil
}

// AddLienResponseMultiError is an error wrapping multiple validation errors
// returned by AddLienResponse.ValidateAll() if the designated constraints
// aren't met.
type AddLienResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddLienResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddLienResponseMultiError) AllErrors() []error { return m }

// AddLienResponseValidationError is the validation error returned by
// AddLienResponse.Validate if the designated constraints aren't met.
type AddLienResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddLienResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddLienResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddLienResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddLienResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddLienResponseValidationError) ErrorName() string { return "AddLienResponseValidationError" }

// Error satisfies the builtin error interface
func (e AddLienResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddLienResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddLienResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddLienResponseValidationError{}

// Validate checks the field values on EnquireLienRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnquireLienRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnquireLienRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnquireLienRequestMultiError, or nil if none found.
func (m *EnquireLienRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireLienRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetAccountNumber()) < 1 {
		err := EnquireLienRequestValidationError{
			field:  "AccountNumber",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetRequestId()) < 1 {
		err := EnquireLienRequestValidationError{
			field:  "RequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return EnquireLienRequestMultiError(errors)
	}

	return nil
}

// EnquireLienRequestMultiError is an error wrapping multiple validation errors
// returned by EnquireLienRequest.ValidateAll() if the designated constraints
// aren't met.
type EnquireLienRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireLienRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireLienRequestMultiError) AllErrors() []error { return m }

// EnquireLienRequestValidationError is the validation error returned by
// EnquireLienRequest.Validate if the designated constraints aren't met.
type EnquireLienRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireLienRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnquireLienRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnquireLienRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnquireLienRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnquireLienRequestValidationError) ErrorName() string {
	return "EnquireLienRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireLienRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireLienRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireLienRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireLienRequestValidationError{}

// Validate checks the field values on EnquireLienResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnquireLienResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnquireLienResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnquireLienResponseMultiError, or nil if none found.
func (m *EnquireLienResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireLienResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LienId

	// no validation rules for ApiMessage

	if all {
		switch v := interface{}(m.GetLienDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnquireLienResponseValidationError{
					field:  "LienDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnquireLienResponseValidationError{
					field:  "LienDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLienDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnquireLienResponseValidationError{
				field:  "LienDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EnquireLienResponseMultiError(errors)
	}

	return nil
}

// EnquireLienResponseMultiError is an error wrapping multiple validation
// errors returned by EnquireLienResponse.ValidateAll() if the designated
// constraints aren't met.
type EnquireLienResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireLienResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireLienResponseMultiError) AllErrors() []error { return m }

// EnquireLienResponseValidationError is the validation error returned by
// EnquireLienResponse.Validate if the designated constraints aren't met.
type EnquireLienResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireLienResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnquireLienResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnquireLienResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnquireLienResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnquireLienResponseValidationError) ErrorName() string {
	return "EnquireLienResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireLienResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireLienResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireLienResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireLienResponseValidationError{}

// Validate checks the field values on LienDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LienDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LienDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LienDetailsMultiError, or
// nil if none found.
func (m *LienDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LienDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Amount

	// no validation rules for CurrencyCode

	// no validation rules for ReasonCode

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LienDetailsValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LienDetailsValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LienDetailsValidationError{
				field:  "StartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LienDetailsValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LienDetailsValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LienDetailsValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LienDetailsMultiError(errors)
	}

	return nil
}

// LienDetailsMultiError is an error wrapping multiple validation errors
// returned by LienDetails.ValidateAll() if the designated constraints aren't met.
type LienDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LienDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LienDetailsMultiError) AllErrors() []error { return m }

// LienDetailsValidationError is the validation error returned by
// LienDetails.Validate if the designated constraints aren't met.
type LienDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LienDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LienDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LienDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LienDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LienDetailsValidationError) ErrorName() string { return "LienDetailsValidationError" }

// Error satisfies the builtin error interface
func (e LienDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLienDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LienDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LienDetailsValidationError{}

// Validate checks the field values on GetCommsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCommsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCommsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCommsRequestMultiError, or nil if none found.
func (m *GetCommsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCommsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountNumber

	if all {
		switch v := interface{}(m.GetStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCommsRequestValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCommsRequestValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCommsRequestValidationError{
				field:  "StartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCommsRequestValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCommsRequestValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCommsRequestValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCommsRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCommsRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCommsRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCommsRequestMultiError(errors)
	}

	return nil
}

// GetCommsRequestMultiError is an error wrapping multiple validation errors
// returned by GetCommsRequest.ValidateAll() if the designated constraints
// aren't met.
type GetCommsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCommsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCommsRequestMultiError) AllErrors() []error { return m }

// GetCommsRequestValidationError is the validation error returned by
// GetCommsRequest.Validate if the designated constraints aren't met.
type GetCommsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCommsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCommsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCommsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCommsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCommsRequestValidationError) ErrorName() string { return "GetCommsRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetCommsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCommsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCommsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCommsRequestValidationError{}

// Validate checks the field values on GetCommsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCommsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCommsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCommsResponseMultiError, or nil if none found.
func (m *GetCommsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCommsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCommunications() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCommsResponseValidationError{
						field:  fmt.Sprintf("Communications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCommsResponseValidationError{
						field:  fmt.Sprintf("Communications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCommsResponseValidationError{
					field:  fmt.Sprintf("Communications[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCommsResponseMultiError(errors)
	}

	return nil
}

// GetCommsResponseMultiError is an error wrapping multiple validation errors
// returned by GetCommsResponse.ValidateAll() if the designated constraints
// aren't met.
type GetCommsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCommsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCommsResponseMultiError) AllErrors() []error { return m }

// GetCommsResponseValidationError is the validation error returned by
// GetCommsResponse.Validate if the designated constraints aren't met.
type GetCommsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCommsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCommsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCommsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCommsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCommsResponseValidationError) ErrorName() string { return "GetCommsResponseValidationError" }

// Error satisfies the builtin error interface
func (e GetCommsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCommsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCommsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCommsResponseValidationError{}
