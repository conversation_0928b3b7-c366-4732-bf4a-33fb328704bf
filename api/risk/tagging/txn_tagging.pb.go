//go:generate gen_sql -types=TransactionTag

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/tagging/txn_tagging.proto

package tagging

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TransactionTag int32

const (
	TransactionTag_TRANSACTION_TAG_UNSPECIFIED     TransactionTag = 0
	TransactionTag_TRANSACTION_TAG_FAMILY_TRANSFER TransactionTag = 1
)

// Enum value maps for TransactionTag.
var (
	TransactionTag_name = map[int32]string{
		0: "TRANSACTION_TAG_UNSPECIFIED",
		1: "TRANSACTION_TAG_FAMILY_TRANSFER",
	}
	TransactionTag_value = map[string]int32{
		"TRANSACTION_TAG_UNSPECIFIED":     0,
		"TRANSACTION_TAG_FAMILY_TRANSFER": 1,
	}
)

func (x TransactionTag) Enum() *TransactionTag {
	p := new(TransactionTag)
	*p = x
	return p
}

func (x TransactionTag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionTag) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_tagging_txn_tagging_proto_enumTypes[0].Descriptor()
}

func (TransactionTag) Type() protoreflect.EnumType {
	return &file_api_risk_tagging_txn_tagging_proto_enumTypes[0]
}

func (x TransactionTag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionTag.Descriptor instead.
func (TransactionTag) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_tagging_txn_tagging_proto_rawDescGZIP(), []int{0}
}

type TransactionTagMapping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifier for the mapping row
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// internal fi txn id
	TxnId     string                 `protobuf:"bytes,2,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
	Tag       TransactionTag         `protobuf:"varint,3,opt,name=tag,proto3,enum=risk.tagging.TransactionTag" json:"tag,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *TransactionTagMapping) Reset() {
	*x = TransactionTagMapping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_tagging_txn_tagging_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionTagMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionTagMapping) ProtoMessage() {}

func (x *TransactionTagMapping) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_tagging_txn_tagging_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionTagMapping.ProtoReflect.Descriptor instead.
func (*TransactionTagMapping) Descriptor() ([]byte, []int) {
	return file_api_risk_tagging_txn_tagging_proto_rawDescGZIP(), []int{0}
}

func (x *TransactionTagMapping) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TransactionTagMapping) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

func (x *TransactionTagMapping) GetTag() TransactionTag {
	if x != nil {
		return x.Tag
	}
	return TransactionTag_TRANSACTION_TAG_UNSPECIFIED
}

func (x *TransactionTagMapping) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TransactionTagMapping) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_api_risk_tagging_txn_tagging_proto protoreflect.FileDescriptor

var file_api_risk_tagging_txn_tagging_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x74, 0x61, 0x67, 0x67, 0x69,
	0x6e, 0x67, 0x2f, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x61, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x74, 0x61, 0x67, 0x67, 0x69,
	0x6e, 0x67, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xe4, 0x01, 0x0a, 0x15, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x78, 0x6e, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x74, 0x61, 0x67, 0x67, 0x69, 0x6e, 0x67,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x52,
	0x03, 0x74, 0x61, 0x67, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0x56, 0x0a, 0x0e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x12, 0x1f, 0x0a, 0x1b,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x41, 0x47, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a,
	0x1f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x41, 0x47,
	0x5f, 0x46, 0x41, 0x4d, 0x49, 0x4c, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52,
	0x10, 0x01, 0x42, 0x52, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x74, 0x61, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x5a, 0x27, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x74,
	0x61, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_tagging_txn_tagging_proto_rawDescOnce sync.Once
	file_api_risk_tagging_txn_tagging_proto_rawDescData = file_api_risk_tagging_txn_tagging_proto_rawDesc
)

func file_api_risk_tagging_txn_tagging_proto_rawDescGZIP() []byte {
	file_api_risk_tagging_txn_tagging_proto_rawDescOnce.Do(func() {
		file_api_risk_tagging_txn_tagging_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_tagging_txn_tagging_proto_rawDescData)
	})
	return file_api_risk_tagging_txn_tagging_proto_rawDescData
}

var file_api_risk_tagging_txn_tagging_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_risk_tagging_txn_tagging_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_risk_tagging_txn_tagging_proto_goTypes = []interface{}{
	(TransactionTag)(0),           // 0: risk.tagging.TransactionTag
	(*TransactionTagMapping)(nil), // 1: risk.tagging.TransactionTagMapping
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
}
var file_api_risk_tagging_txn_tagging_proto_depIdxs = []int32{
	0, // 0: risk.tagging.TransactionTagMapping.tag:type_name -> risk.tagging.TransactionTag
	2, // 1: risk.tagging.TransactionTagMapping.created_at:type_name -> google.protobuf.Timestamp
	2, // 2: risk.tagging.TransactionTagMapping.updated_at:type_name -> google.protobuf.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_risk_tagging_txn_tagging_proto_init() }
func file_api_risk_tagging_txn_tagging_proto_init() {
	if File_api_risk_tagging_txn_tagging_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_tagging_txn_tagging_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionTagMapping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_tagging_txn_tagging_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_tagging_txn_tagging_proto_goTypes,
		DependencyIndexes: file_api_risk_tagging_txn_tagging_proto_depIdxs,
		EnumInfos:         file_api_risk_tagging_txn_tagging_proto_enumTypes,
		MessageInfos:      file_api_risk_tagging_txn_tagging_proto_msgTypes,
	}.Build()
	File_api_risk_tagging_txn_tagging_proto = out.File
	file_api_risk_tagging_txn_tagging_proto_rawDesc = nil
	file_api_risk_tagging_txn_tagging_proto_goTypes = nil
	file_api_risk_tagging_txn_tagging_proto_depIdxs = nil
}
