// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/risk/tagging/txn_tagging.pb.go

package tagging

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the TransactionTag in string format in DB
func (p TransactionTag) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing TransactionTag while reading from DB
func (p *TransactionTag) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := TransactionTag_value[val]
	if !ok {
		return fmt.Errorf("unexpected TransactionTag value: %s", val)
	}
	*p = TransactionTag(valInt)
	return nil
}

// Marshaler interface implementation for TransactionTag
func (x TransactionTag) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for TransactionTag
func (x *TransactionTag) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = TransactionTag(TransactionTag_value[val])
	return nil
}
