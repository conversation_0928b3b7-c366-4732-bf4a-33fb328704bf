// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/workflow/workflow.proto

package workflow

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	workflow "github.com/epifi/be-common/api/celestial/workflow"
	risk "github.com/epifi/gamma/api/risk"
	review "github.com/epifi/gamma/api/risk/case_management/review"
	enums "github.com/epifi/gamma/api/risk/enums"
	lea "github.com/epifi/gamma/api/risk/lea"
	risk1 "github.com/epifi/gamma/api/vendorgateway/crm/risk"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BankActionUnfreezeResponse_Status int32

const (
	// workflow execution was successful
	// no tech or business failure within workflow
	BankActionUnfreezeResponse_OK BankActionUnfreezeResponse_Status = 0
	// business failure occurred within workflow
	// non-retryable error
	BankActionUnfreezeResponse_INTERNAL BankActionUnfreezeResponse_Status = 13
	// tech failure occurred within workflow
	// retryable error
	BankActionUnfreezeResponse_MANUAL_INTERVENTION BankActionUnfreezeResponse_Status = 100
)

// Enum value maps for BankActionUnfreezeResponse_Status.
var (
	BankActionUnfreezeResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		100: "MANUAL_INTERVENTION",
	}
	BankActionUnfreezeResponse_Status_value = map[string]int32{
		"OK":                  0,
		"INTERNAL":            13,
		"MANUAL_INTERVENTION": 100,
	}
)

func (x BankActionUnfreezeResponse_Status) Enum() *BankActionUnfreezeResponse_Status {
	p := new(BankActionUnfreezeResponse_Status)
	*p = x
	return p
}

func (x BankActionUnfreezeResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BankActionUnfreezeResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_workflow_workflow_proto_enumTypes[0].Descriptor()
}

func (BankActionUnfreezeResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_workflow_workflow_proto_enumTypes[0]
}

func (x BankActionUnfreezeResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BankActionUnfreezeResponse_Status.Descriptor instead.
func (BankActionUnfreezeResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{1, 0}
}

type BankActionFullFreezeResponse_Status int32

const (
	// workflow execution was successful
	// no tech or business failure within workflow
	BankActionFullFreezeResponse_OK BankActionFullFreezeResponse_Status = 0
	// business failure occurred within workflow
	// non-retryable error
	BankActionFullFreezeResponse_INTERNAL BankActionFullFreezeResponse_Status = 13
	// tech failure occurred within workflow
	// retryable error
	BankActionFullFreezeResponse_MANUAL_INTERVENTION BankActionFullFreezeResponse_Status = 100
)

// Enum value maps for BankActionFullFreezeResponse_Status.
var (
	BankActionFullFreezeResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		100: "MANUAL_INTERVENTION",
	}
	BankActionFullFreezeResponse_Status_value = map[string]int32{
		"OK":                  0,
		"INTERNAL":            13,
		"MANUAL_INTERVENTION": 100,
	}
)

func (x BankActionFullFreezeResponse_Status) Enum() *BankActionFullFreezeResponse_Status {
	p := new(BankActionFullFreezeResponse_Status)
	*p = x
	return p
}

func (x BankActionFullFreezeResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BankActionFullFreezeResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_workflow_workflow_proto_enumTypes[1].Descriptor()
}

func (BankActionFullFreezeResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_workflow_workflow_proto_enumTypes[1]
}

func (x BankActionFullFreezeResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BankActionFullFreezeResponse_Status.Descriptor instead.
func (BankActionFullFreezeResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{3, 0}
}

type BankActionCreditFreezeResponse_Status int32

const (
	// workflow execution was successful
	// no tech or business failure within workflow
	BankActionCreditFreezeResponse_OK BankActionCreditFreezeResponse_Status = 0
	// business failure occurred within workflow
	// non-retryable error
	BankActionCreditFreezeResponse_INTERNAL BankActionCreditFreezeResponse_Status = 13
	// tech failure occurred within workflow
	// retryable error
	BankActionCreditFreezeResponse_MANUAL_INTERVENTION BankActionCreditFreezeResponse_Status = 100
)

// Enum value maps for BankActionCreditFreezeResponse_Status.
var (
	BankActionCreditFreezeResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		100: "MANUAL_INTERVENTION",
	}
	BankActionCreditFreezeResponse_Status_value = map[string]int32{
		"OK":                  0,
		"INTERNAL":            13,
		"MANUAL_INTERVENTION": 100,
	}
)

func (x BankActionCreditFreezeResponse_Status) Enum() *BankActionCreditFreezeResponse_Status {
	p := new(BankActionCreditFreezeResponse_Status)
	*p = x
	return p
}

func (x BankActionCreditFreezeResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BankActionCreditFreezeResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_workflow_workflow_proto_enumTypes[2].Descriptor()
}

func (BankActionCreditFreezeResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_workflow_workflow_proto_enumTypes[2]
}

func (x BankActionCreditFreezeResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BankActionCreditFreezeResponse_Status.Descriptor instead.
func (BankActionCreditFreezeResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{5, 0}
}

type BankActionDebitFreezeResponse_Status int32

const (
	// workflow execution was successful
	// no tech or business failure within workflow
	BankActionDebitFreezeResponse_OK BankActionDebitFreezeResponse_Status = 0
	// business failure occurred within workflow
	// non-retryable error
	BankActionDebitFreezeResponse_INTERNAL BankActionDebitFreezeResponse_Status = 13
	// tech failure occurred within workflow
	// retryable error
	BankActionDebitFreezeResponse_MANUAL_INTERVENTION BankActionDebitFreezeResponse_Status = 100
)

// Enum value maps for BankActionDebitFreezeResponse_Status.
var (
	BankActionDebitFreezeResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		100: "MANUAL_INTERVENTION",
	}
	BankActionDebitFreezeResponse_Status_value = map[string]int32{
		"OK":                  0,
		"INTERNAL":            13,
		"MANUAL_INTERVENTION": 100,
	}
)

func (x BankActionDebitFreezeResponse_Status) Enum() *BankActionDebitFreezeResponse_Status {
	p := new(BankActionDebitFreezeResponse_Status)
	*p = x
	return p
}

func (x BankActionDebitFreezeResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BankActionDebitFreezeResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_workflow_workflow_proto_enumTypes[3].Descriptor()
}

func (BankActionDebitFreezeResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_workflow_workflow_proto_enumTypes[3]
}

func (x BankActionDebitFreezeResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BankActionDebitFreezeResponse_Status.Descriptor instead.
func (BankActionDebitFreezeResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{7, 0}
}

type ProcessUnifiedLEAComplaintResponse_Status int32

const (
	// workflow execution was successful
	// no tech or business failure within workflow
	ProcessUnifiedLEAComplaintResponse_OK ProcessUnifiedLEAComplaintResponse_Status = 0
	// business failure occurred within workflow
	// non-retryable error
	ProcessUnifiedLEAComplaintResponse_INTERNAL ProcessUnifiedLEAComplaintResponse_Status = 13
)

// Enum value maps for ProcessUnifiedLEAComplaintResponse_Status.
var (
	ProcessUnifiedLEAComplaintResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	ProcessUnifiedLEAComplaintResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x ProcessUnifiedLEAComplaintResponse_Status) Enum() *ProcessUnifiedLEAComplaintResponse_Status {
	p := new(ProcessUnifiedLEAComplaintResponse_Status)
	*p = x
	return p
}

func (x ProcessUnifiedLEAComplaintResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessUnifiedLEAComplaintResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_workflow_workflow_proto_enumTypes[4].Descriptor()
}

func (ProcessUnifiedLEAComplaintResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_workflow_workflow_proto_enumTypes[4]
}

func (x ProcessUnifiedLEAComplaintResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessUnifiedLEAComplaintResponse_Status.Descriptor instead.
func (ProcessUnifiedLEAComplaintResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{9, 0}
}

type BankActionUnfreezeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// workflow id under consideration
	WorkflowId string `protobuf:"bytes,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	ActorId    string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// action is the flow current activity leads to
	Action enums.Action `protobuf:"varint,3,opt,name=action,proto3,enum=enums.Action" json:"action,omitempty"`
	// reason to change account state
	RequestReason *risk.RequestReason `protobuf:"bytes,4,opt,name=request_reason,json=requestReason,proto3" json:"request_reason,omitempty"`
	// comms template to be triggered
	CommsTemplate []enums.CommsTemplate `protobuf:"varint,5,rep,packed,name=comms_template,json=commsTemplate,proto3,enum=enums.CommsTemplate" json:"comms_template,omitempty"`
}

func (x *BankActionUnfreezeRequest) Reset() {
	*x = BankActionUnfreezeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankActionUnfreezeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankActionUnfreezeRequest) ProtoMessage() {}

func (x *BankActionUnfreezeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankActionUnfreezeRequest.ProtoReflect.Descriptor instead.
func (*BankActionUnfreezeRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{0}
}

func (x *BankActionUnfreezeRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *BankActionUnfreezeRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *BankActionUnfreezeRequest) GetAction() enums.Action {
	if x != nil {
		return x.Action
	}
	return enums.Action(0)
}

func (x *BankActionUnfreezeRequest) GetRequestReason() *risk.RequestReason {
	if x != nil {
		return x.RequestReason
	}
	return nil
}

func (x *BankActionUnfreezeRequest) GetCommsTemplate() []enums.CommsTemplate {
	if x != nil {
		return x.CommsTemplate
	}
	return nil
}

type BankActionUnfreezeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common workflow response header
	ResponseHeader *workflow.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *BankActionUnfreezeResponse) Reset() {
	*x = BankActionUnfreezeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankActionUnfreezeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankActionUnfreezeResponse) ProtoMessage() {}

func (x *BankActionUnfreezeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankActionUnfreezeResponse.ProtoReflect.Descriptor instead.
func (*BankActionUnfreezeResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{1}
}

func (x *BankActionUnfreezeResponse) GetResponseHeader() *workflow.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type BankActionFullFreezeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// workflow id under consideration
	WorkflowId string `protobuf:"bytes,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	ActorId    string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// action is the flow current activity leads to
	Action enums.Action `protobuf:"varint,3,opt,name=action,proto3,enum=enums.Action" json:"action,omitempty"`
	// reason to change account state
	RequestReason *risk.RequestReason `protobuf:"bytes,4,opt,name=request_reason,json=requestReason,proto3" json:"request_reason,omitempty"`
	// comms template to be triggered
	CommsTemplate []enums.CommsTemplate `protobuf:"varint,5,rep,packed,name=comms_template,json=commsTemplate,proto3,enum=enums.CommsTemplate" json:"comms_template,omitempty"`
	// case against which action is being taken
	CaseId string `protobuf:"bytes,7,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	// email of analyst
	AnalystEmail string `protobuf:"bytes,8,opt,name=analyst_email,json=analystEmail,proto3" json:"analyst_email,omitempty"`
}

func (x *BankActionFullFreezeRequest) Reset() {
	*x = BankActionFullFreezeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankActionFullFreezeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankActionFullFreezeRequest) ProtoMessage() {}

func (x *BankActionFullFreezeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankActionFullFreezeRequest.ProtoReflect.Descriptor instead.
func (*BankActionFullFreezeRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{2}
}

func (x *BankActionFullFreezeRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *BankActionFullFreezeRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *BankActionFullFreezeRequest) GetAction() enums.Action {
	if x != nil {
		return x.Action
	}
	return enums.Action(0)
}

func (x *BankActionFullFreezeRequest) GetRequestReason() *risk.RequestReason {
	if x != nil {
		return x.RequestReason
	}
	return nil
}

func (x *BankActionFullFreezeRequest) GetCommsTemplate() []enums.CommsTemplate {
	if x != nil {
		return x.CommsTemplate
	}
	return nil
}

func (x *BankActionFullFreezeRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *BankActionFullFreezeRequest) GetAnalystEmail() string {
	if x != nil {
		return x.AnalystEmail
	}
	return ""
}

type BankActionFullFreezeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common workflow response header
	ResponseHeader *workflow.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *BankActionFullFreezeResponse) Reset() {
	*x = BankActionFullFreezeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankActionFullFreezeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankActionFullFreezeResponse) ProtoMessage() {}

func (x *BankActionFullFreezeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankActionFullFreezeResponse.ProtoReflect.Descriptor instead.
func (*BankActionFullFreezeResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{3}
}

func (x *BankActionFullFreezeResponse) GetResponseHeader() *workflow.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type BankActionCreditFreezeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// workflow id under consideration
	WorkflowId string `protobuf:"bytes,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	ActorId    string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// action is the flow current activity leads to
	Action enums.Action `protobuf:"varint,3,opt,name=action,proto3,enum=enums.Action" json:"action,omitempty"`
	// reason to change account state
	RequestReason *risk.RequestReason `protobuf:"bytes,4,opt,name=request_reason,json=requestReason,proto3" json:"request_reason,omitempty"`
	// comms template to be triggered
	CommsTemplate []enums.CommsTemplate `protobuf:"varint,5,rep,packed,name=comms_template,json=commsTemplate,proto3,enum=enums.CommsTemplate" json:"comms_template,omitempty"`
	// case against which action is being taken
	// deprecated in favor of case
	CaseId string `protobuf:"bytes,7,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	// email of analyst
	AnalystEmail string `protobuf:"bytes,8,opt,name=analyst_email,json=analystEmail,proto3" json:"analyst_email,omitempty"`
	// case for which action was taken
	Case *review.Case `protobuf:"bytes,9,opt,name=case,proto3" json:"case,omitempty"`
}

func (x *BankActionCreditFreezeRequest) Reset() {
	*x = BankActionCreditFreezeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankActionCreditFreezeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankActionCreditFreezeRequest) ProtoMessage() {}

func (x *BankActionCreditFreezeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankActionCreditFreezeRequest.ProtoReflect.Descriptor instead.
func (*BankActionCreditFreezeRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{4}
}

func (x *BankActionCreditFreezeRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *BankActionCreditFreezeRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *BankActionCreditFreezeRequest) GetAction() enums.Action {
	if x != nil {
		return x.Action
	}
	return enums.Action(0)
}

func (x *BankActionCreditFreezeRequest) GetRequestReason() *risk.RequestReason {
	if x != nil {
		return x.RequestReason
	}
	return nil
}

func (x *BankActionCreditFreezeRequest) GetCommsTemplate() []enums.CommsTemplate {
	if x != nil {
		return x.CommsTemplate
	}
	return nil
}

func (x *BankActionCreditFreezeRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *BankActionCreditFreezeRequest) GetAnalystEmail() string {
	if x != nil {
		return x.AnalystEmail
	}
	return ""
}

func (x *BankActionCreditFreezeRequest) GetCase() *review.Case {
	if x != nil {
		return x.Case
	}
	return nil
}

type BankActionCreditFreezeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common workflow response header
	ResponseHeader *workflow.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *BankActionCreditFreezeResponse) Reset() {
	*x = BankActionCreditFreezeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankActionCreditFreezeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankActionCreditFreezeResponse) ProtoMessage() {}

func (x *BankActionCreditFreezeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankActionCreditFreezeResponse.ProtoReflect.Descriptor instead.
func (*BankActionCreditFreezeResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{5}
}

func (x *BankActionCreditFreezeResponse) GetResponseHeader() *workflow.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type BankActionDebitFreezeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// workflow id under consideration
	WorkflowId string `protobuf:"bytes,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	ActorId    string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// reason to change account state
	RequestReason *risk.RequestReason `protobuf:"bytes,3,opt,name=request_reason,json=requestReason,proto3" json:"request_reason,omitempty"`
	// comms template to be triggered
	CommsTemplates []enums.CommsTemplate `protobuf:"varint,4,rep,packed,name=comms_templates,json=commsTemplates,proto3,enum=enums.CommsTemplate" json:"comms_templates,omitempty"`
	// case against which action is being taken
	CaseId string `protobuf:"bytes,7,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	// email of analyst
	AnalystEmail string `protobuf:"bytes,8,opt,name=analyst_email,json=analystEmail,proto3" json:"analyst_email,omitempty"`
}

func (x *BankActionDebitFreezeRequest) Reset() {
	*x = BankActionDebitFreezeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankActionDebitFreezeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankActionDebitFreezeRequest) ProtoMessage() {}

func (x *BankActionDebitFreezeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankActionDebitFreezeRequest.ProtoReflect.Descriptor instead.
func (*BankActionDebitFreezeRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{6}
}

func (x *BankActionDebitFreezeRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *BankActionDebitFreezeRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *BankActionDebitFreezeRequest) GetRequestReason() *risk.RequestReason {
	if x != nil {
		return x.RequestReason
	}
	return nil
}

func (x *BankActionDebitFreezeRequest) GetCommsTemplates() []enums.CommsTemplate {
	if x != nil {
		return x.CommsTemplates
	}
	return nil
}

func (x *BankActionDebitFreezeRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *BankActionDebitFreezeRequest) GetAnalystEmail() string {
	if x != nil {
		return x.AnalystEmail
	}
	return ""
}

type BankActionDebitFreezeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common workflow response header
	ResponseHeader *workflow.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *BankActionDebitFreezeResponse) Reset() {
	*x = BankActionDebitFreezeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankActionDebitFreezeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankActionDebitFreezeResponse) ProtoMessage() {}

func (x *BankActionDebitFreezeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankActionDebitFreezeResponse.ProtoReflect.Descriptor instead.
func (*BankActionDebitFreezeResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{7}
}

func (x *BankActionDebitFreezeResponse) GetResponseHeader() *workflow.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// ProcessUnifiedLeaComplaintRequest will process the unified lea complaint and perform tasks
// like send comms, app access update and create case in case management
type ProcessUnifiedLEAComplaintRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unified lea complaint which we want to process
	UnifiedLeaComplaint *lea.UnifiedLeaComplaint `protobuf:"bytes,1,opt,name=unified_lea_complaint,json=unifiedLeaComplaint,proto3" json:"unified_lea_complaint,omitempty"`
}

func (x *ProcessUnifiedLEAComplaintRequest) Reset() {
	*x = ProcessUnifiedLEAComplaintRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessUnifiedLEAComplaintRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessUnifiedLEAComplaintRequest) ProtoMessage() {}

func (x *ProcessUnifiedLEAComplaintRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessUnifiedLEAComplaintRequest.ProtoReflect.Descriptor instead.
func (*ProcessUnifiedLEAComplaintRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{8}
}

func (x *ProcessUnifiedLEAComplaintRequest) GetUnifiedLeaComplaint() *lea.UnifiedLeaComplaint {
	if x != nil {
		return x.UnifiedLeaComplaint
	}
	return nil
}

type ProcessUnifiedLEAComplaintResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common workflow response header
	ResponseHeader *workflow.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessUnifiedLEAComplaintResponse) Reset() {
	*x = ProcessUnifiedLEAComplaintResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessUnifiedLEAComplaintResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessUnifiedLEAComplaintResponse) ProtoMessage() {}

func (x *ProcessUnifiedLEAComplaintResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessUnifiedLEAComplaintResponse.ProtoReflect.Descriptor instead.
func (*ProcessUnifiedLEAComplaintResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{9}
}

func (x *ProcessUnifiedLEAComplaintResponse) GetResponseHeader() *workflow.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// ApplyLienRequest is the request for the ApplyLien workflow
type ApplyLienRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Request header with authentication information
	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	WorkflowId    string                  `protobuf:"bytes,2,opt,name=workflowId,proto3" json:"workflowId,omitempty"`
	// additional parameters for action request
	Parameters *review.AccountLienParameters `protobuf:"bytes,3,opt,name=parameters,proto3" json:"parameters,omitempty"`
	// email of analyst taking the action
	AnalystEmail string `protobuf:"bytes,4,opt,name=analyst_email,json=analystEmail,proto3" json:"analyst_email,omitempty"`
	ActorId      string `protobuf:"bytes,5,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// case for which action was taken
	Case *review.Case `protobuf:"bytes,6,opt,name=case,proto3" json:"case,omitempty"`
	// account number for which lien is applied
	AccountNumber string `protobuf:"bytes,7,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
}

func (x *ApplyLienRequest) Reset() {
	*x = ApplyLienRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyLienRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyLienRequest) ProtoMessage() {}

func (x *ApplyLienRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyLienRequest.ProtoReflect.Descriptor instead.
func (*ApplyLienRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{10}
}

func (x *ApplyLienRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ApplyLienRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *ApplyLienRequest) GetParameters() *review.AccountLienParameters {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *ApplyLienRequest) GetAnalystEmail() string {
	if x != nil {
		return x.AnalystEmail
	}
	return ""
}

func (x *ApplyLienRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ApplyLienRequest) GetCase() *review.Case {
	if x != nil {
		return x.Case
	}
	return nil
}

func (x *ApplyLienRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

// ApplyLienResponse is the response from the ApplyLien workflow
type ApplyLienResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response header with status information
	ResponseHeader *workflow.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ApplyLienResponse) Reset() {
	*x = ApplyLienResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyLienResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyLienResponse) ProtoMessage() {}

func (x *ApplyLienResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyLienResponse.ProtoReflect.Descriptor instead.
func (*ApplyLienResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{11}
}

func (x *ApplyLienResponse) GetResponseHeader() *workflow.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type CaseReprioritisationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Request header with authentication information
	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Filters for fetching specific cases from Freshdesk
	Filters *CaseFetchFilters `protobuf:"bytes,2,opt,name=filters,proto3" json:"filters,omitempty"`
	// Whether this is a dry run (no actual updates)
	DryRun bool `protobuf:"varint,3,opt,name=dry_run,json=dryRun,proto3" json:"dry_run,omitempty"`
	// Make the following flag true if there is a manual upload of S3 url and that needs to be passed
	// The uploaded S3 file must have comma separated ticket ids
	IsManualUpload bool `protobuf:"varint,4,opt,name=is_manual_upload,json=isManualUpload,proto3" json:"is_manual_upload,omitempty"`
	// If is_manual_upload is true then following S3 url would be used
	TicketsS3Url string `protobuf:"bytes,5,opt,name=tickets_s3_url,json=ticketsS3Url,proto3" json:"tickets_s3_url,omitempty"`
}

func (x *CaseReprioritisationRequest) Reset() {
	*x = CaseReprioritisationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaseReprioritisationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseReprioritisationRequest) ProtoMessage() {}

func (x *CaseReprioritisationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseReprioritisationRequest.ProtoReflect.Descriptor instead.
func (*CaseReprioritisationRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{12}
}

func (x *CaseReprioritisationRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CaseReprioritisationRequest) GetFilters() *CaseFetchFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *CaseReprioritisationRequest) GetDryRun() bool {
	if x != nil {
		return x.DryRun
	}
	return false
}

func (x *CaseReprioritisationRequest) GetIsManualUpload() bool {
	if x != nil {
		return x.IsManualUpload
	}
	return false
}

func (x *CaseReprioritisationRequest) GetTicketsS3Url() string {
	if x != nil {
		return x.TicketsS3Url
	}
	return ""
}

type CaseReprioritisationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response header with status information
	ResponseHeader *workflow.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// Summary of the reprioritization process
	Summary *CaseReprioritisationSummary `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
}

func (x *CaseReprioritisationResponse) Reset() {
	*x = CaseReprioritisationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaseReprioritisationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseReprioritisationResponse) ProtoMessage() {}

func (x *CaseReprioritisationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseReprioritisationResponse.ProtoReflect.Descriptor instead.
func (*CaseReprioritisationResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{13}
}

func (x *CaseReprioritisationResponse) GetResponseHeader() *workflow.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *CaseReprioritisationResponse) GetSummary() *CaseReprioritisationSummary {
	if x != nil {
		return x.Summary
	}
	return nil
}

type CaseFetchFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Freshdesk ticket status filter (open, pending, etc.)
	Status []int32 `protobuf:"varint,1,rep,packed,name=status,proto3" json:"status,omitempty"`
	// Whether to include only unassigned tickets
	UnassignedOnly bool `protobuf:"varint,2,opt,name=unassigned_only,json=unassignedOnly,proto3" json:"unassigned_only,omitempty"`
	// Priority filter
	Priority []int32 `protobuf:"varint,3,rep,packed,name=priority,proto3" json:"priority,omitempty"`
	// Date range filters
	CreatedAfter *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_after,json=createdAfter,proto3" json:"created_after,omitempty"` // ISO format
	UpdatedAfter *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_after,json=updatedAfter,proto3" json:"updated_after,omitempty"` // ISO format
	// Tags to filter by
	Tags []string `protobuf:"bytes,6,rep,name=tags,proto3" json:"tags,omitempty"`
	// Review type
	ReviewTypes []risk1.ReviewType `protobuf:"varint,7,rep,packed,name=review_types,json=reviewTypes,proto3,enum=vendorgateway.crm.risk.ReviewType" json:"review_types,omitempty"`
}

func (x *CaseFetchFilters) Reset() {
	*x = CaseFetchFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaseFetchFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseFetchFilters) ProtoMessage() {}

func (x *CaseFetchFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseFetchFilters.ProtoReflect.Descriptor instead.
func (*CaseFetchFilters) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{14}
}

func (x *CaseFetchFilters) GetStatus() []int32 {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CaseFetchFilters) GetUnassignedOnly() bool {
	if x != nil {
		return x.UnassignedOnly
	}
	return false
}

func (x *CaseFetchFilters) GetPriority() []int32 {
	if x != nil {
		return x.Priority
	}
	return nil
}

func (x *CaseFetchFilters) GetCreatedAfter() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAfter
	}
	return nil
}

func (x *CaseFetchFilters) GetUpdatedAfter() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAfter
	}
	return nil
}

func (x *CaseFetchFilters) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *CaseFetchFilters) GetReviewTypes() []risk1.ReviewType {
	if x != nil {
		return x.ReviewTypes
	}
	return nil
}

type CaseReprioritisationSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of ticket IDs that had errors during processing
	ErroredTicketIds []string `protobuf:"bytes,1,rep,name=errored_ticket_ids,json=erroredTicketIds,proto3" json:"errored_ticket_ids,omitempty"`
}

func (x *CaseReprioritisationSummary) Reset() {
	*x = CaseReprioritisationSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaseReprioritisationSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseReprioritisationSummary) ProtoMessage() {}

func (x *CaseReprioritisationSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseReprioritisationSummary.ProtoReflect.Descriptor instead.
func (*CaseReprioritisationSummary) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{15}
}

func (x *CaseReprioritisationSummary) GetErroredTicketIds() []string {
	if x != nil {
		return x.ErroredTicketIds
	}
	return nil
}

// GetScoresForCases Workflow (W2) Messages
type GetScoresForCasesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Request header with authentication information
	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// S3 path containing case data to process
	S3Path string `protobuf:"bytes,2,opt,name=s3_path,json=s3Path,proto3" json:"s3_path,omitempty"`
	// Maximum number of concurrent DS model calls
	MaxConcurrentDsCalls int32 `protobuf:"varint,3,opt,name=max_concurrent_ds_calls,json=maxConcurrentDsCalls,proto3" json:"max_concurrent_ds_calls,omitempty"`
	// Batch ID for tracking (from parent workflow)
	BatchId string `protobuf:"bytes,4,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`
}

func (x *GetScoresForCasesRequest) Reset() {
	*x = GetScoresForCasesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScoresForCasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScoresForCasesRequest) ProtoMessage() {}

func (x *GetScoresForCasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScoresForCasesRequest.ProtoReflect.Descriptor instead.
func (*GetScoresForCasesRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{16}
}

func (x *GetScoresForCasesRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *GetScoresForCasesRequest) GetS3Path() string {
	if x != nil {
		return x.S3Path
	}
	return ""
}

func (x *GetScoresForCasesRequest) GetMaxConcurrentDsCalls() int32 {
	if x != nil {
		return x.MaxConcurrentDsCalls
	}
	return 0
}

func (x *GetScoresForCasesRequest) GetBatchId() string {
	if x != nil {
		return x.BatchId
	}
	return ""
}

type GetScoresForCasesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response header with status information
	ResponseHeader *workflow.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// List of tickets with their calculated confidence scores
	TicketsWithScores []*TicketWithScore `protobuf:"bytes,2,rep,name=tickets_with_scores,json=ticketsWithScores,proto3" json:"tickets_with_scores,omitempty"`
	// List of ticket IDs that failed during processing
	ErroredTicketIds []string `protobuf:"bytes,3,rep,name=errored_ticket_ids,json=erroredTicketIds,proto3" json:"errored_ticket_ids,omitempty"`
}

func (x *GetScoresForCasesResponse) Reset() {
	*x = GetScoresForCasesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScoresForCasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScoresForCasesResponse) ProtoMessage() {}

func (x *GetScoresForCasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScoresForCasesResponse.ProtoReflect.Descriptor instead.
func (*GetScoresForCasesResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{17}
}

func (x *GetScoresForCasesResponse) GetResponseHeader() *workflow.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *GetScoresForCasesResponse) GetTicketsWithScores() []*TicketWithScore {
	if x != nil {
		return x.TicketsWithScores
	}
	return nil
}

func (x *GetScoresForCasesResponse) GetErroredTicketIds() []string {
	if x != nil {
		return x.ErroredTicketIds
	}
	return nil
}

// Basic ticket information structure for case reprioritization
type TicketInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Ticket ID (Freshdesk ticket ID)
	TicketId string `protobuf:"bytes,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
}

func (x *TicketInfo) Reset() {
	*x = TicketInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TicketInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TicketInfo) ProtoMessage() {}

func (x *TicketInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TicketInfo.ProtoReflect.Descriptor instead.
func (*TicketInfo) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{18}
}

func (x *TicketInfo) GetTicketId() string {
	if x != nil {
		return x.TicketId
	}
	return ""
}

// Ticket information with calculated confidence score
type TicketWithScore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Ticket ID (Freshdesk ticket ID)
	TicketId string `protobuf:"bytes,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	// Calculated score 1 from DS model (0-100 integer)
	Model1Score float32 `protobuf:"fixed32,2,opt,name=model1_score,json=model1Score,proto3" json:"model1_score,omitempty"`
	// Name 1 of the DS model used for scoring
	Model1Name string `protobuf:"bytes,3,opt,name=model1_name,json=model1Name,proto3" json:"model1_name,omitempty"`
	// Calculated score 2 from DS model (0-100 integer)
	Model2Score float32 `protobuf:"fixed32,4,opt,name=model2_score,json=model2Score,proto3" json:"model2_score,omitempty"`
	// Name 2 of the DS model used for scoring
	Model2Name string `protobuf:"bytes,5,opt,name=model2_name,json=model2Name,proto3" json:"model2_name,omitempty"`
}

func (x *TicketWithScore) Reset() {
	*x = TicketWithScore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TicketWithScore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TicketWithScore) ProtoMessage() {}

func (x *TicketWithScore) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TicketWithScore.ProtoReflect.Descriptor instead.
func (*TicketWithScore) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{19}
}

func (x *TicketWithScore) GetTicketId() string {
	if x != nil {
		return x.TicketId
	}
	return ""
}

func (x *TicketWithScore) GetModel1Score() float32 {
	if x != nil {
		return x.Model1Score
	}
	return 0
}

func (x *TicketWithScore) GetModel1Name() string {
	if x != nil {
		return x.Model1Name
	}
	return ""
}

func (x *TicketWithScore) GetModel2Score() float32 {
	if x != nil {
		return x.Model2Score
	}
	return 0
}

func (x *TicketWithScore) GetModel2Name() string {
	if x != nil {
		return x.Model2Name
	}
	return ""
}

// BulkUpdateWorkflow (W3) Messages
type BulkUpdateWorkflowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Request header with authentication information
	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Group key to identify the score group (e.g., "score_0.85_model_default")
	GroupKey string `protobuf:"bytes,2,opt,name=group_key,json=groupKey,proto3" json:"group_key,omitempty"`
	// List of ticket IDs to be updated with the confidence score
	TicketIds []string `protobuf:"bytes,3,rep,name=ticket_ids,json=ticketIds,proto3" json:"ticket_ids,omitempty"`
	// Name of the DS model 1 used for scoring
	Model1Name string `protobuf:"bytes,4,opt,name=model1_name,json=model1Name,proto3" json:"model1_name,omitempty"`
	// Name of the DS model 2 used for scoring
	Model2Name string `protobuf:"bytes,5,opt,name=model2_name,json=model2Name,proto3" json:"model2_name,omitempty"`
	// Score of the DS model 1 used for scoring
	Model1Score float32 `protobuf:"fixed32,6,opt,name=model1_score,json=model1Score,proto3" json:"model1_score,omitempty"`
	// Score of the DS model 2 used for scoring
	Model2Score     float32                           `protobuf:"fixed32,7,opt,name=model2_score,json=model2Score,proto3" json:"model2_score,omitempty"`
	ConfidenceScore float32                           `protobuf:"fixed32,8,opt,name=confidence_score,json=confidenceScore,proto3" json:"confidence_score,omitempty"`
	UpdateFieldMask []enums.TicketBulkUpdateFieldMask `protobuf:"varint,9,rep,packed,name=update_field_mask,json=updateFieldMask,proto3,enum=enums.TicketBulkUpdateFieldMask" json:"update_field_mask,omitempty"`
}

func (x *BulkUpdateWorkflowRequest) Reset() {
	*x = BulkUpdateWorkflowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkUpdateWorkflowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkUpdateWorkflowRequest) ProtoMessage() {}

func (x *BulkUpdateWorkflowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkUpdateWorkflowRequest.ProtoReflect.Descriptor instead.
func (*BulkUpdateWorkflowRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{20}
}

func (x *BulkUpdateWorkflowRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *BulkUpdateWorkflowRequest) GetGroupKey() string {
	if x != nil {
		return x.GroupKey
	}
	return ""
}

func (x *BulkUpdateWorkflowRequest) GetTicketIds() []string {
	if x != nil {
		return x.TicketIds
	}
	return nil
}

func (x *BulkUpdateWorkflowRequest) GetModel1Name() string {
	if x != nil {
		return x.Model1Name
	}
	return ""
}

func (x *BulkUpdateWorkflowRequest) GetModel2Name() string {
	if x != nil {
		return x.Model2Name
	}
	return ""
}

func (x *BulkUpdateWorkflowRequest) GetModel1Score() float32 {
	if x != nil {
		return x.Model1Score
	}
	return 0
}

func (x *BulkUpdateWorkflowRequest) GetModel2Score() float32 {
	if x != nil {
		return x.Model2Score
	}
	return 0
}

func (x *BulkUpdateWorkflowRequest) GetConfidenceScore() float32 {
	if x != nil {
		return x.ConfidenceScore
	}
	return 0
}

func (x *BulkUpdateWorkflowRequest) GetUpdateFieldMask() []enums.TicketBulkUpdateFieldMask {
	if x != nil {
		return x.UpdateFieldMask
	}
	return nil
}

type BulkUpdateWorkflowResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response header with status information
	ResponseHeader *workflow.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// Job ID returned from the BulkUpdateTickets API
	JobId string `protobuf:"bytes,2,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	// List of ticket IDs that were successfully updated
	SuccessfulTicketIds []string `protobuf:"bytes,3,rep,name=successful_ticket_ids,json=successfulTicketIds,proto3" json:"successful_ticket_ids,omitempty"`
	// List of ticket IDs that failed to update
	ErroredTicketIds []string `protobuf:"bytes,4,rep,name=errored_ticket_ids,json=erroredTicketIds,proto3" json:"errored_ticket_ids,omitempty"`
}

func (x *BulkUpdateWorkflowResponse) Reset() {
	*x = BulkUpdateWorkflowResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_workflow_workflow_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkUpdateWorkflowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkUpdateWorkflowResponse) ProtoMessage() {}

func (x *BulkUpdateWorkflowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_workflow_workflow_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkUpdateWorkflowResponse.ProtoReflect.Descriptor instead.
func (*BulkUpdateWorkflowResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_workflow_workflow_proto_rawDescGZIP(), []int{21}
}

func (x *BulkUpdateWorkflowResponse) GetResponseHeader() *workflow.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *BulkUpdateWorkflowResponse) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *BulkUpdateWorkflowResponse) GetSuccessfulTicketIds() []string {
	if x != nil {
		return x.SuccessfulTicketIds
	}
	return nil
}

func (x *BulkUpdateWorkflowResponse) GetErroredTicketIds() []string {
	if x != nil {
		return x.ErroredTicketIds
	}
	return nil
}

var File_api_risk_workflow_workflow_proto protoreflect.FileDescriptor

var file_api_risk_workflow_workflow_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0d, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b,
	0x2f, 0x62, 0x61, 0x6e, 0x6b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a,
	0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x65, 0x61, 0x2f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x5f, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x63, 0x72, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x6b,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x93, 0x02, 0x0a, 0x19, 0x42, 0x61, 0x6e, 0x6b, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x3b, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x63,
	0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0xa2, 0x01, 0x0a,
	0x1a, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x66, 0x72, 0x65,
	0x65, 0x7a, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x37, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x41, 0x4e, 0x55,
	0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x64, 0x22, 0xd3, 0x02, 0x0a, 0x1b, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x46, 0x75, 0x6c, 0x6c, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0d, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x3a, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0e,
	0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d,
	0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65,
	0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0xa4, 0x01, 0x0a, 0x1c, 0x42, 0x61, 0x6e, 0x6b,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x75, 0x6c, 0x6c, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x37, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x64, 0x22, 0x8c,
	0x03, 0x0a, 0x1d, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x28, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2f,
	0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d,
	0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x3a, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0e, 0x63,
	0x6f, 0x6d, 0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x73,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x5f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x35, 0x0a, 0x04, 0x63, 0x61, 0x73, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x04, 0x63, 0x61, 0x73, 0x65, 0x22, 0xa6, 0x01,
	0x0a, 0x1e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x37, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x17, 0x0a,
	0x13, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x64, 0x22, 0xa5, 0x02, 0x0a, 0x1c, 0x42, 0x61, 0x6e, 0x6b, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x62, 0x69, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x3d, 0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0xa5,
	0x01, 0x0a, 0x1d, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x62,
	0x69, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x37, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x17, 0x0a,
	0x13, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x64, 0x22, 0x72, 0x0a, 0x21, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4d, 0x0a, 0x15, 0x75,
	0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x13, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x65,
	0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x22, 0x91, 0x01, 0x0a, 0x22, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x45, 0x41,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x1e,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xee,
	0x02, 0x0a, 0x10, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65,
	0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1e, 0x0a,
	0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x52, 0x0a,
	0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x65, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x5f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x35, 0x0a, 0x04, 0x63, 0x61, 0x73, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x61,
	0x73, 0x65, 0x52, 0x04, 0x63, 0x61, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22,
	0x60, 0x0a, 0x11, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0x8b, 0x02, 0x0a, 0x1b, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x70, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x07, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x61, 0x73,
	0x65, 0x46, 0x65, 0x74, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x07, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x72, 0x79, 0x5f, 0x72, 0x75,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x64, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x12,
	0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x75, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x4d, 0x61, 0x6e,
	0x75, 0x61, 0x6c, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x73, 0x5f, 0x73, 0x33, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x53, 0x33, 0x55, 0x72, 0x6c, 0x22,
	0xb1, 0x01, 0x0a, 0x1c, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x44, 0x0a,
	0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x22, 0xcc, 0x02, 0x0a, 0x10, 0x43, 0x61, 0x73, 0x65, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x27, 0x0a, 0x0f, 0x75, 0x6e, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x6f,
	0x6e, 0x6c, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x75, 0x6e, 0x61, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x3f, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x45, 0x0a, 0x0c, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x63, 0x72, 0x6d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x22, 0x4b, 0x0a, 0x1b, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x70, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x12, 0x2c, 0x0a, 0x12, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x65, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x73, 0x22,
	0xcf, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x46, 0x6f, 0x72,
	0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x33, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x33, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x35, 0x0a, 0x17, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x73, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x14, 0x6d, 0x61, 0x78, 0x43, 0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x44,
	0x73, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x49,
	0x64, 0x22, 0xe6, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x46,
	0x6f, 0x72, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x13,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x57, 0x69, 0x74, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x11, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x73, 0x57, 0x69, 0x74, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x12,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x65,
	0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x73, 0x22, 0x29, 0x0a, 0x0a, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x49, 0x64, 0x22, 0xb6, 0x01, 0x0a, 0x0f, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x57, 0x69, 0x74, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x31,
	0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x31, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x31, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x31, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x32, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x32, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x32, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x32, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa2,
	0x03, 0x0a, 0x19, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49,
	0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x31, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x31, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x32, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x32,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x31, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x31, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x32, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x32, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x4c, 0x0a, 0x11, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x20, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42,
	0x75, 0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x52, 0x0f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d,
	0x61, 0x73, 0x6b, 0x22, 0xe2, 0x01, 0x0a, 0x1a, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65,
	0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x66, 0x75, 0x6c, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75,
	0x6c, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x65, 0x64, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x73, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_workflow_workflow_proto_rawDescOnce sync.Once
	file_api_risk_workflow_workflow_proto_rawDescData = file_api_risk_workflow_workflow_proto_rawDesc
)

func file_api_risk_workflow_workflow_proto_rawDescGZIP() []byte {
	file_api_risk_workflow_workflow_proto_rawDescOnce.Do(func() {
		file_api_risk_workflow_workflow_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_workflow_workflow_proto_rawDescData)
	})
	return file_api_risk_workflow_workflow_proto_rawDescData
}

var file_api_risk_workflow_workflow_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_api_risk_workflow_workflow_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_api_risk_workflow_workflow_proto_goTypes = []interface{}{
	(BankActionUnfreezeResponse_Status)(0),         // 0: risk.workflow.BankActionUnfreezeResponse.Status
	(BankActionFullFreezeResponse_Status)(0),       // 1: risk.workflow.BankActionFullFreezeResponse.Status
	(BankActionCreditFreezeResponse_Status)(0),     // 2: risk.workflow.BankActionCreditFreezeResponse.Status
	(BankActionDebitFreezeResponse_Status)(0),      // 3: risk.workflow.BankActionDebitFreezeResponse.Status
	(ProcessUnifiedLEAComplaintResponse_Status)(0), // 4: risk.workflow.ProcessUnifiedLEAComplaintResponse.Status
	(*BankActionUnfreezeRequest)(nil),              // 5: risk.workflow.BankActionUnfreezeRequest
	(*BankActionUnfreezeResponse)(nil),             // 6: risk.workflow.BankActionUnfreezeResponse
	(*BankActionFullFreezeRequest)(nil),            // 7: risk.workflow.BankActionFullFreezeRequest
	(*BankActionFullFreezeResponse)(nil),           // 8: risk.workflow.BankActionFullFreezeResponse
	(*BankActionCreditFreezeRequest)(nil),          // 9: risk.workflow.BankActionCreditFreezeRequest
	(*BankActionCreditFreezeResponse)(nil),         // 10: risk.workflow.BankActionCreditFreezeResponse
	(*BankActionDebitFreezeRequest)(nil),           // 11: risk.workflow.BankActionDebitFreezeRequest
	(*BankActionDebitFreezeResponse)(nil),          // 12: risk.workflow.BankActionDebitFreezeResponse
	(*ProcessUnifiedLEAComplaintRequest)(nil),      // 13: risk.workflow.ProcessUnifiedLEAComplaintRequest
	(*ProcessUnifiedLEAComplaintResponse)(nil),     // 14: risk.workflow.ProcessUnifiedLEAComplaintResponse
	(*ApplyLienRequest)(nil),                       // 15: risk.workflow.ApplyLienRequest
	(*ApplyLienResponse)(nil),                      // 16: risk.workflow.ApplyLienResponse
	(*CaseReprioritisationRequest)(nil),            // 17: risk.workflow.CaseReprioritisationRequest
	(*CaseReprioritisationResponse)(nil),           // 18: risk.workflow.CaseReprioritisationResponse
	(*CaseFetchFilters)(nil),                       // 19: risk.workflow.CaseFetchFilters
	(*CaseReprioritisationSummary)(nil),            // 20: risk.workflow.CaseReprioritisationSummary
	(*GetScoresForCasesRequest)(nil),               // 21: risk.workflow.GetScoresForCasesRequest
	(*GetScoresForCasesResponse)(nil),              // 22: risk.workflow.GetScoresForCasesResponse
	(*TicketInfo)(nil),                             // 23: risk.workflow.TicketInfo
	(*TicketWithScore)(nil),                        // 24: risk.workflow.TicketWithScore
	(*BulkUpdateWorkflowRequest)(nil),              // 25: risk.workflow.BulkUpdateWorkflowRequest
	(*BulkUpdateWorkflowResponse)(nil),             // 26: risk.workflow.BulkUpdateWorkflowResponse
	(enums.Action)(0),                              // 27: enums.Action
	(*risk.RequestReason)(nil),                     // 28: risk.RequestReason
	(enums.CommsTemplate)(0),                       // 29: enums.CommsTemplate
	(*workflow.ResponseHeader)(nil),                // 30: celestial.workflow.ResponseHeader
	(*review.Case)(nil),                            // 31: risk.case_management.review.Case
	(*lea.UnifiedLeaComplaint)(nil),                // 32: risk.UnifiedLeaComplaint
	(*workflow.RequestHeader)(nil),                 // 33: celestial.workflow.RequestHeader
	(*review.AccountLienParameters)(nil),           // 34: risk.case_management.review.AccountLienParameters
	(*timestamppb.Timestamp)(nil),                  // 35: google.protobuf.Timestamp
	(risk1.ReviewType)(0),                          // 36: vendorgateway.crm.risk.ReviewType
	(enums.TicketBulkUpdateFieldMask)(0),           // 37: enums.TicketBulkUpdateFieldMask
}
var file_api_risk_workflow_workflow_proto_depIdxs = []int32{
	27, // 0: risk.workflow.BankActionUnfreezeRequest.action:type_name -> enums.Action
	28, // 1: risk.workflow.BankActionUnfreezeRequest.request_reason:type_name -> risk.RequestReason
	29, // 2: risk.workflow.BankActionUnfreezeRequest.comms_template:type_name -> enums.CommsTemplate
	30, // 3: risk.workflow.BankActionUnfreezeResponse.response_header:type_name -> celestial.workflow.ResponseHeader
	27, // 4: risk.workflow.BankActionFullFreezeRequest.action:type_name -> enums.Action
	28, // 5: risk.workflow.BankActionFullFreezeRequest.request_reason:type_name -> risk.RequestReason
	29, // 6: risk.workflow.BankActionFullFreezeRequest.comms_template:type_name -> enums.CommsTemplate
	30, // 7: risk.workflow.BankActionFullFreezeResponse.response_header:type_name -> celestial.workflow.ResponseHeader
	27, // 8: risk.workflow.BankActionCreditFreezeRequest.action:type_name -> enums.Action
	28, // 9: risk.workflow.BankActionCreditFreezeRequest.request_reason:type_name -> risk.RequestReason
	29, // 10: risk.workflow.BankActionCreditFreezeRequest.comms_template:type_name -> enums.CommsTemplate
	31, // 11: risk.workflow.BankActionCreditFreezeRequest.case:type_name -> risk.case_management.review.Case
	30, // 12: risk.workflow.BankActionCreditFreezeResponse.response_header:type_name -> celestial.workflow.ResponseHeader
	28, // 13: risk.workflow.BankActionDebitFreezeRequest.request_reason:type_name -> risk.RequestReason
	29, // 14: risk.workflow.BankActionDebitFreezeRequest.comms_templates:type_name -> enums.CommsTemplate
	30, // 15: risk.workflow.BankActionDebitFreezeResponse.response_header:type_name -> celestial.workflow.ResponseHeader
	32, // 16: risk.workflow.ProcessUnifiedLEAComplaintRequest.unified_lea_complaint:type_name -> risk.UnifiedLeaComplaint
	30, // 17: risk.workflow.ProcessUnifiedLEAComplaintResponse.response_header:type_name -> celestial.workflow.ResponseHeader
	33, // 18: risk.workflow.ApplyLienRequest.request_header:type_name -> celestial.workflow.RequestHeader
	34, // 19: risk.workflow.ApplyLienRequest.parameters:type_name -> risk.case_management.review.AccountLienParameters
	31, // 20: risk.workflow.ApplyLienRequest.case:type_name -> risk.case_management.review.Case
	30, // 21: risk.workflow.ApplyLienResponse.response_header:type_name -> celestial.workflow.ResponseHeader
	33, // 22: risk.workflow.CaseReprioritisationRequest.request_header:type_name -> celestial.workflow.RequestHeader
	19, // 23: risk.workflow.CaseReprioritisationRequest.filters:type_name -> risk.workflow.CaseFetchFilters
	30, // 24: risk.workflow.CaseReprioritisationResponse.response_header:type_name -> celestial.workflow.ResponseHeader
	20, // 25: risk.workflow.CaseReprioritisationResponse.summary:type_name -> risk.workflow.CaseReprioritisationSummary
	35, // 26: risk.workflow.CaseFetchFilters.created_after:type_name -> google.protobuf.Timestamp
	35, // 27: risk.workflow.CaseFetchFilters.updated_after:type_name -> google.protobuf.Timestamp
	36, // 28: risk.workflow.CaseFetchFilters.review_types:type_name -> vendorgateway.crm.risk.ReviewType
	33, // 29: risk.workflow.GetScoresForCasesRequest.request_header:type_name -> celestial.workflow.RequestHeader
	30, // 30: risk.workflow.GetScoresForCasesResponse.response_header:type_name -> celestial.workflow.ResponseHeader
	24, // 31: risk.workflow.GetScoresForCasesResponse.tickets_with_scores:type_name -> risk.workflow.TicketWithScore
	33, // 32: risk.workflow.BulkUpdateWorkflowRequest.request_header:type_name -> celestial.workflow.RequestHeader
	37, // 33: risk.workflow.BulkUpdateWorkflowRequest.update_field_mask:type_name -> enums.TicketBulkUpdateFieldMask
	30, // 34: risk.workflow.BulkUpdateWorkflowResponse.response_header:type_name -> celestial.workflow.ResponseHeader
	35, // [35:35] is the sub-list for method output_type
	35, // [35:35] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_api_risk_workflow_workflow_proto_init() }
func file_api_risk_workflow_workflow_proto_init() {
	if File_api_risk_workflow_workflow_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_workflow_workflow_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankActionUnfreezeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankActionUnfreezeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankActionFullFreezeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankActionFullFreezeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankActionCreditFreezeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankActionCreditFreezeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankActionDebitFreezeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankActionDebitFreezeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessUnifiedLEAComplaintRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessUnifiedLEAComplaintResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyLienRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyLienResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaseReprioritisationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaseReprioritisationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaseFetchFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaseReprioritisationSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScoresForCasesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScoresForCasesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TicketInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TicketWithScore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkUpdateWorkflowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_workflow_workflow_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkUpdateWorkflowResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_workflow_workflow_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_workflow_workflow_proto_goTypes,
		DependencyIndexes: file_api_risk_workflow_workflow_proto_depIdxs,
		EnumInfos:         file_api_risk_workflow_workflow_proto_enumTypes,
		MessageInfos:      file_api_risk_workflow_workflow_proto_msgTypes,
	}.Build()
	File_api_risk_workflow_workflow_proto = out.File
	file_api_risk_workflow_workflow_proto_rawDesc = nil
	file_api_risk_workflow_workflow_proto_goTypes = nil
	file_api_risk_workflow_workflow_proto_depIdxs = nil
}
