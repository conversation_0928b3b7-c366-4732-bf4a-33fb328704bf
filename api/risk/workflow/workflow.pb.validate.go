// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/workflow/workflow.proto

package workflow

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/risk/enums"

	risk "github.com/epifi/gamma/api/vendorgateway/crm/risk"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.Action(0)

	_ = risk.ReviewType(0)
)

// Validate checks the field values on BankActionUnfreezeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BankActionUnfreezeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankActionUnfreezeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BankActionUnfreezeRequestMultiError, or nil if none found.
func (m *BankActionUnfreezeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BankActionUnfreezeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetWorkflowId()) < 1 {
		err := BankActionUnfreezeRequestValidationError{
			field:  "WorkflowId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := BankActionUnfreezeRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _BankActionUnfreezeRequest_Action_NotInLookup[m.GetAction()]; ok {
		err := BankActionUnfreezeRequestValidationError{
			field:  "Action",
			reason: "value must not be in list [ACTION_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRequestReason()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankActionUnfreezeRequestValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankActionUnfreezeRequestValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestReason()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankActionUnfreezeRequestValidationError{
				field:  "RequestReason",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BankActionUnfreezeRequestMultiError(errors)
	}

	return nil
}

// BankActionUnfreezeRequestMultiError is an error wrapping multiple validation
// errors returned by BankActionUnfreezeRequest.ValidateAll() if the
// designated constraints aren't met.
type BankActionUnfreezeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankActionUnfreezeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankActionUnfreezeRequestMultiError) AllErrors() []error { return m }

// BankActionUnfreezeRequestValidationError is the validation error returned by
// BankActionUnfreezeRequest.Validate if the designated constraints aren't met.
type BankActionUnfreezeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankActionUnfreezeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankActionUnfreezeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankActionUnfreezeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankActionUnfreezeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankActionUnfreezeRequestValidationError) ErrorName() string {
	return "BankActionUnfreezeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BankActionUnfreezeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankActionUnfreezeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankActionUnfreezeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankActionUnfreezeRequestValidationError{}

var _BankActionUnfreezeRequest_Action_NotInLookup = map[enums.Action]struct{}{
	0: {},
}

// Validate checks the field values on BankActionUnfreezeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BankActionUnfreezeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankActionUnfreezeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BankActionUnfreezeResponseMultiError, or nil if none found.
func (m *BankActionUnfreezeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BankActionUnfreezeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankActionUnfreezeResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankActionUnfreezeResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankActionUnfreezeResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BankActionUnfreezeResponseMultiError(errors)
	}

	return nil
}

// BankActionUnfreezeResponseMultiError is an error wrapping multiple
// validation errors returned by BankActionUnfreezeResponse.ValidateAll() if
// the designated constraints aren't met.
type BankActionUnfreezeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankActionUnfreezeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankActionUnfreezeResponseMultiError) AllErrors() []error { return m }

// BankActionUnfreezeResponseValidationError is the validation error returned
// by BankActionUnfreezeResponse.Validate if the designated constraints aren't met.
type BankActionUnfreezeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankActionUnfreezeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankActionUnfreezeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankActionUnfreezeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankActionUnfreezeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankActionUnfreezeResponseValidationError) ErrorName() string {
	return "BankActionUnfreezeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BankActionUnfreezeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankActionUnfreezeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankActionUnfreezeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankActionUnfreezeResponseValidationError{}

// Validate checks the field values on BankActionFullFreezeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BankActionFullFreezeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankActionFullFreezeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BankActionFullFreezeRequestMultiError, or nil if none found.
func (m *BankActionFullFreezeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BankActionFullFreezeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetWorkflowId()) < 1 {
		err := BankActionFullFreezeRequestValidationError{
			field:  "WorkflowId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := BankActionFullFreezeRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _BankActionFullFreezeRequest_Action_NotInLookup[m.GetAction()]; ok {
		err := BankActionFullFreezeRequestValidationError{
			field:  "Action",
			reason: "value must not be in list [ACTION_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRequestReason()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankActionFullFreezeRequestValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankActionFullFreezeRequestValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestReason()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankActionFullFreezeRequestValidationError{
				field:  "RequestReason",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CaseId

	// no validation rules for AnalystEmail

	if len(errors) > 0 {
		return BankActionFullFreezeRequestMultiError(errors)
	}

	return nil
}

// BankActionFullFreezeRequestMultiError is an error wrapping multiple
// validation errors returned by BankActionFullFreezeRequest.ValidateAll() if
// the designated constraints aren't met.
type BankActionFullFreezeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankActionFullFreezeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankActionFullFreezeRequestMultiError) AllErrors() []error { return m }

// BankActionFullFreezeRequestValidationError is the validation error returned
// by BankActionFullFreezeRequest.Validate if the designated constraints
// aren't met.
type BankActionFullFreezeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankActionFullFreezeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankActionFullFreezeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankActionFullFreezeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankActionFullFreezeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankActionFullFreezeRequestValidationError) ErrorName() string {
	return "BankActionFullFreezeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BankActionFullFreezeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankActionFullFreezeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankActionFullFreezeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankActionFullFreezeRequestValidationError{}

var _BankActionFullFreezeRequest_Action_NotInLookup = map[enums.Action]struct{}{
	0: {},
}

// Validate checks the field values on BankActionFullFreezeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BankActionFullFreezeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankActionFullFreezeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BankActionFullFreezeResponseMultiError, or nil if none found.
func (m *BankActionFullFreezeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BankActionFullFreezeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankActionFullFreezeResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankActionFullFreezeResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankActionFullFreezeResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BankActionFullFreezeResponseMultiError(errors)
	}

	return nil
}

// BankActionFullFreezeResponseMultiError is an error wrapping multiple
// validation errors returned by BankActionFullFreezeResponse.ValidateAll() if
// the designated constraints aren't met.
type BankActionFullFreezeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankActionFullFreezeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankActionFullFreezeResponseMultiError) AllErrors() []error { return m }

// BankActionFullFreezeResponseValidationError is the validation error returned
// by BankActionFullFreezeResponse.Validate if the designated constraints
// aren't met.
type BankActionFullFreezeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankActionFullFreezeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankActionFullFreezeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankActionFullFreezeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankActionFullFreezeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankActionFullFreezeResponseValidationError) ErrorName() string {
	return "BankActionFullFreezeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BankActionFullFreezeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankActionFullFreezeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankActionFullFreezeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankActionFullFreezeResponseValidationError{}

// Validate checks the field values on BankActionCreditFreezeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BankActionCreditFreezeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankActionCreditFreezeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BankActionCreditFreezeRequestMultiError, or nil if none found.
func (m *BankActionCreditFreezeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BankActionCreditFreezeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetWorkflowId()) < 1 {
		err := BankActionCreditFreezeRequestValidationError{
			field:  "WorkflowId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := BankActionCreditFreezeRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _BankActionCreditFreezeRequest_Action_NotInLookup[m.GetAction()]; ok {
		err := BankActionCreditFreezeRequestValidationError{
			field:  "Action",
			reason: "value must not be in list [ACTION_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRequestReason()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankActionCreditFreezeRequestValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankActionCreditFreezeRequestValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestReason()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankActionCreditFreezeRequestValidationError{
				field:  "RequestReason",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CaseId

	// no validation rules for AnalystEmail

	if all {
		switch v := interface{}(m.GetCase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankActionCreditFreezeRequestValidationError{
					field:  "Case",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankActionCreditFreezeRequestValidationError{
					field:  "Case",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankActionCreditFreezeRequestValidationError{
				field:  "Case",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BankActionCreditFreezeRequestMultiError(errors)
	}

	return nil
}

// BankActionCreditFreezeRequestMultiError is an error wrapping multiple
// validation errors returned by BankActionCreditFreezeRequest.ValidateAll()
// if the designated constraints aren't met.
type BankActionCreditFreezeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankActionCreditFreezeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankActionCreditFreezeRequestMultiError) AllErrors() []error { return m }

// BankActionCreditFreezeRequestValidationError is the validation error
// returned by BankActionCreditFreezeRequest.Validate if the designated
// constraints aren't met.
type BankActionCreditFreezeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankActionCreditFreezeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankActionCreditFreezeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankActionCreditFreezeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankActionCreditFreezeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankActionCreditFreezeRequestValidationError) ErrorName() string {
	return "BankActionCreditFreezeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BankActionCreditFreezeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankActionCreditFreezeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankActionCreditFreezeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankActionCreditFreezeRequestValidationError{}

var _BankActionCreditFreezeRequest_Action_NotInLookup = map[enums.Action]struct{}{
	0: {},
}

// Validate checks the field values on BankActionCreditFreezeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BankActionCreditFreezeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankActionCreditFreezeResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BankActionCreditFreezeResponseMultiError, or nil if none found.
func (m *BankActionCreditFreezeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BankActionCreditFreezeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankActionCreditFreezeResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankActionCreditFreezeResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankActionCreditFreezeResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BankActionCreditFreezeResponseMultiError(errors)
	}

	return nil
}

// BankActionCreditFreezeResponseMultiError is an error wrapping multiple
// validation errors returned by BankActionCreditFreezeResponse.ValidateAll()
// if the designated constraints aren't met.
type BankActionCreditFreezeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankActionCreditFreezeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankActionCreditFreezeResponseMultiError) AllErrors() []error { return m }

// BankActionCreditFreezeResponseValidationError is the validation error
// returned by BankActionCreditFreezeResponse.Validate if the designated
// constraints aren't met.
type BankActionCreditFreezeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankActionCreditFreezeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankActionCreditFreezeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankActionCreditFreezeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankActionCreditFreezeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankActionCreditFreezeResponseValidationError) ErrorName() string {
	return "BankActionCreditFreezeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BankActionCreditFreezeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankActionCreditFreezeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankActionCreditFreezeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankActionCreditFreezeResponseValidationError{}

// Validate checks the field values on BankActionDebitFreezeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BankActionDebitFreezeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankActionDebitFreezeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BankActionDebitFreezeRequestMultiError, or nil if none found.
func (m *BankActionDebitFreezeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BankActionDebitFreezeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetWorkflowId()) < 1 {
		err := BankActionDebitFreezeRequestValidationError{
			field:  "WorkflowId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := BankActionDebitFreezeRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRequestReason()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankActionDebitFreezeRequestValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankActionDebitFreezeRequestValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestReason()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankActionDebitFreezeRequestValidationError{
				field:  "RequestReason",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CaseId

	// no validation rules for AnalystEmail

	if len(errors) > 0 {
		return BankActionDebitFreezeRequestMultiError(errors)
	}

	return nil
}

// BankActionDebitFreezeRequestMultiError is an error wrapping multiple
// validation errors returned by BankActionDebitFreezeRequest.ValidateAll() if
// the designated constraints aren't met.
type BankActionDebitFreezeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankActionDebitFreezeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankActionDebitFreezeRequestMultiError) AllErrors() []error { return m }

// BankActionDebitFreezeRequestValidationError is the validation error returned
// by BankActionDebitFreezeRequest.Validate if the designated constraints
// aren't met.
type BankActionDebitFreezeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankActionDebitFreezeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankActionDebitFreezeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankActionDebitFreezeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankActionDebitFreezeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankActionDebitFreezeRequestValidationError) ErrorName() string {
	return "BankActionDebitFreezeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BankActionDebitFreezeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankActionDebitFreezeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankActionDebitFreezeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankActionDebitFreezeRequestValidationError{}

// Validate checks the field values on BankActionDebitFreezeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BankActionDebitFreezeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankActionDebitFreezeResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BankActionDebitFreezeResponseMultiError, or nil if none found.
func (m *BankActionDebitFreezeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BankActionDebitFreezeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankActionDebitFreezeResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankActionDebitFreezeResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankActionDebitFreezeResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BankActionDebitFreezeResponseMultiError(errors)
	}

	return nil
}

// BankActionDebitFreezeResponseMultiError is an error wrapping multiple
// validation errors returned by BankActionDebitFreezeResponse.ValidateAll()
// if the designated constraints aren't met.
type BankActionDebitFreezeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankActionDebitFreezeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankActionDebitFreezeResponseMultiError) AllErrors() []error { return m }

// BankActionDebitFreezeResponseValidationError is the validation error
// returned by BankActionDebitFreezeResponse.Validate if the designated
// constraints aren't met.
type BankActionDebitFreezeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankActionDebitFreezeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankActionDebitFreezeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankActionDebitFreezeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankActionDebitFreezeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankActionDebitFreezeResponseValidationError) ErrorName() string {
	return "BankActionDebitFreezeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BankActionDebitFreezeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankActionDebitFreezeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankActionDebitFreezeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankActionDebitFreezeResponseValidationError{}

// Validate checks the field values on ProcessUnifiedLEAComplaintRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessUnifiedLEAComplaintRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessUnifiedLEAComplaintRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessUnifiedLEAComplaintRequestMultiError, or nil if none found.
func (m *ProcessUnifiedLEAComplaintRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessUnifiedLEAComplaintRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUnifiedLeaComplaint()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessUnifiedLEAComplaintRequestValidationError{
					field:  "UnifiedLeaComplaint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessUnifiedLEAComplaintRequestValidationError{
					field:  "UnifiedLeaComplaint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnifiedLeaComplaint()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessUnifiedLEAComplaintRequestValidationError{
				field:  "UnifiedLeaComplaint",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessUnifiedLEAComplaintRequestMultiError(errors)
	}

	return nil
}

// ProcessUnifiedLEAComplaintRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessUnifiedLEAComplaintRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessUnifiedLEAComplaintRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessUnifiedLEAComplaintRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessUnifiedLEAComplaintRequestMultiError) AllErrors() []error { return m }

// ProcessUnifiedLEAComplaintRequestValidationError is the validation error
// returned by ProcessUnifiedLEAComplaintRequest.Validate if the designated
// constraints aren't met.
type ProcessUnifiedLEAComplaintRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessUnifiedLEAComplaintRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessUnifiedLEAComplaintRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessUnifiedLEAComplaintRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessUnifiedLEAComplaintRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessUnifiedLEAComplaintRequestValidationError) ErrorName() string {
	return "ProcessUnifiedLEAComplaintRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessUnifiedLEAComplaintRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessUnifiedLEAComplaintRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessUnifiedLEAComplaintRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessUnifiedLEAComplaintRequestValidationError{}

// Validate checks the field values on ProcessUnifiedLEAComplaintResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessUnifiedLEAComplaintResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessUnifiedLEAComplaintResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessUnifiedLEAComplaintResponseMultiError, or nil if none found.
func (m *ProcessUnifiedLEAComplaintResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessUnifiedLEAComplaintResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessUnifiedLEAComplaintResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessUnifiedLEAComplaintResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessUnifiedLEAComplaintResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessUnifiedLEAComplaintResponseMultiError(errors)
	}

	return nil
}

// ProcessUnifiedLEAComplaintResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessUnifiedLEAComplaintResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessUnifiedLEAComplaintResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessUnifiedLEAComplaintResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessUnifiedLEAComplaintResponseMultiError) AllErrors() []error { return m }

// ProcessUnifiedLEAComplaintResponseValidationError is the validation error
// returned by ProcessUnifiedLEAComplaintResponse.Validate if the designated
// constraints aren't met.
type ProcessUnifiedLEAComplaintResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessUnifiedLEAComplaintResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessUnifiedLEAComplaintResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessUnifiedLEAComplaintResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessUnifiedLEAComplaintResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessUnifiedLEAComplaintResponseValidationError) ErrorName() string {
	return "ProcessUnifiedLEAComplaintResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessUnifiedLEAComplaintResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessUnifiedLEAComplaintResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessUnifiedLEAComplaintResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessUnifiedLEAComplaintResponseValidationError{}

// Validate checks the field values on ApplyLienRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ApplyLienRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplyLienRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplyLienRequestMultiError, or nil if none found.
func (m *ApplyLienRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplyLienRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplyLienRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplyLienRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplyLienRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for WorkflowId

	if all {
		switch v := interface{}(m.GetParameters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplyLienRequestValidationError{
					field:  "Parameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplyLienRequestValidationError{
					field:  "Parameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParameters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplyLienRequestValidationError{
				field:  "Parameters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AnalystEmail

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetCase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplyLienRequestValidationError{
					field:  "Case",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplyLienRequestValidationError{
					field:  "Case",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplyLienRequestValidationError{
				field:  "Case",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountNumber

	if len(errors) > 0 {
		return ApplyLienRequestMultiError(errors)
	}

	return nil
}

// ApplyLienRequestMultiError is an error wrapping multiple validation errors
// returned by ApplyLienRequest.ValidateAll() if the designated constraints
// aren't met.
type ApplyLienRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplyLienRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplyLienRequestMultiError) AllErrors() []error { return m }

// ApplyLienRequestValidationError is the validation error returned by
// ApplyLienRequest.Validate if the designated constraints aren't met.
type ApplyLienRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplyLienRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplyLienRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplyLienRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplyLienRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplyLienRequestValidationError) ErrorName() string { return "ApplyLienRequestValidationError" }

// Error satisfies the builtin error interface
func (e ApplyLienRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplyLienRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplyLienRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplyLienRequestValidationError{}

// Validate checks the field values on ApplyLienResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ApplyLienResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplyLienResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplyLienResponseMultiError, or nil if none found.
func (m *ApplyLienResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplyLienResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplyLienResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplyLienResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplyLienResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ApplyLienResponseMultiError(errors)
	}

	return nil
}

// ApplyLienResponseMultiError is an error wrapping multiple validation errors
// returned by ApplyLienResponse.ValidateAll() if the designated constraints
// aren't met.
type ApplyLienResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplyLienResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplyLienResponseMultiError) AllErrors() []error { return m }

// ApplyLienResponseValidationError is the validation error returned by
// ApplyLienResponse.Validate if the designated constraints aren't met.
type ApplyLienResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplyLienResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplyLienResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplyLienResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplyLienResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplyLienResponseValidationError) ErrorName() string {
	return "ApplyLienResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ApplyLienResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplyLienResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplyLienResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplyLienResponseValidationError{}

// Validate checks the field values on CaseReprioritisationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CaseReprioritisationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseReprioritisationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CaseReprioritisationRequestMultiError, or nil if none found.
func (m *CaseReprioritisationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseReprioritisationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaseReprioritisationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaseReprioritisationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaseReprioritisationRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaseReprioritisationRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaseReprioritisationRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaseReprioritisationRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DryRun

	// no validation rules for IsManualUpload

	// no validation rules for TicketsS3Url

	if len(errors) > 0 {
		return CaseReprioritisationRequestMultiError(errors)
	}

	return nil
}

// CaseReprioritisationRequestMultiError is an error wrapping multiple
// validation errors returned by CaseReprioritisationRequest.ValidateAll() if
// the designated constraints aren't met.
type CaseReprioritisationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseReprioritisationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseReprioritisationRequestMultiError) AllErrors() []error { return m }

// CaseReprioritisationRequestValidationError is the validation error returned
// by CaseReprioritisationRequest.Validate if the designated constraints
// aren't met.
type CaseReprioritisationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseReprioritisationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseReprioritisationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseReprioritisationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseReprioritisationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseReprioritisationRequestValidationError) ErrorName() string {
	return "CaseReprioritisationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CaseReprioritisationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseReprioritisationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseReprioritisationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseReprioritisationRequestValidationError{}

// Validate checks the field values on CaseReprioritisationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CaseReprioritisationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseReprioritisationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CaseReprioritisationResponseMultiError, or nil if none found.
func (m *CaseReprioritisationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseReprioritisationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaseReprioritisationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaseReprioritisationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaseReprioritisationResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaseReprioritisationResponseValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaseReprioritisationResponseValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaseReprioritisationResponseValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CaseReprioritisationResponseMultiError(errors)
	}

	return nil
}

// CaseReprioritisationResponseMultiError is an error wrapping multiple
// validation errors returned by CaseReprioritisationResponse.ValidateAll() if
// the designated constraints aren't met.
type CaseReprioritisationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseReprioritisationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseReprioritisationResponseMultiError) AllErrors() []error { return m }

// CaseReprioritisationResponseValidationError is the validation error returned
// by CaseReprioritisationResponse.Validate if the designated constraints
// aren't met.
type CaseReprioritisationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseReprioritisationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseReprioritisationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseReprioritisationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseReprioritisationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseReprioritisationResponseValidationError) ErrorName() string {
	return "CaseReprioritisationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CaseReprioritisationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseReprioritisationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseReprioritisationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseReprioritisationResponseValidationError{}

// Validate checks the field values on CaseFetchFilters with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CaseFetchFilters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseFetchFilters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CaseFetchFiltersMultiError, or nil if none found.
func (m *CaseFetchFilters) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseFetchFilters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UnassignedOnly

	if all {
		switch v := interface{}(m.GetCreatedAfter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaseFetchFiltersValidationError{
					field:  "CreatedAfter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaseFetchFiltersValidationError{
					field:  "CreatedAfter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAfter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaseFetchFiltersValidationError{
				field:  "CreatedAfter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAfter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaseFetchFiltersValidationError{
					field:  "UpdatedAfter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaseFetchFiltersValidationError{
					field:  "UpdatedAfter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAfter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaseFetchFiltersValidationError{
				field:  "UpdatedAfter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CaseFetchFiltersMultiError(errors)
	}

	return nil
}

// CaseFetchFiltersMultiError is an error wrapping multiple validation errors
// returned by CaseFetchFilters.ValidateAll() if the designated constraints
// aren't met.
type CaseFetchFiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseFetchFiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseFetchFiltersMultiError) AllErrors() []error { return m }

// CaseFetchFiltersValidationError is the validation error returned by
// CaseFetchFilters.Validate if the designated constraints aren't met.
type CaseFetchFiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseFetchFiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseFetchFiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseFetchFiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseFetchFiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseFetchFiltersValidationError) ErrorName() string { return "CaseFetchFiltersValidationError" }

// Error satisfies the builtin error interface
func (e CaseFetchFiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseFetchFilters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseFetchFiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseFetchFiltersValidationError{}

// Validate checks the field values on CaseReprioritisationSummary with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CaseReprioritisationSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseReprioritisationSummary with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CaseReprioritisationSummaryMultiError, or nil if none found.
func (m *CaseReprioritisationSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseReprioritisationSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CaseReprioritisationSummaryMultiError(errors)
	}

	return nil
}

// CaseReprioritisationSummaryMultiError is an error wrapping multiple
// validation errors returned by CaseReprioritisationSummary.ValidateAll() if
// the designated constraints aren't met.
type CaseReprioritisationSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseReprioritisationSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseReprioritisationSummaryMultiError) AllErrors() []error { return m }

// CaseReprioritisationSummaryValidationError is the validation error returned
// by CaseReprioritisationSummary.Validate if the designated constraints
// aren't met.
type CaseReprioritisationSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseReprioritisationSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseReprioritisationSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseReprioritisationSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseReprioritisationSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseReprioritisationSummaryValidationError) ErrorName() string {
	return "CaseReprioritisationSummaryValidationError"
}

// Error satisfies the builtin error interface
func (e CaseReprioritisationSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseReprioritisationSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseReprioritisationSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseReprioritisationSummaryValidationError{}

// Validate checks the field values on GetScoresForCasesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetScoresForCasesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScoresForCasesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetScoresForCasesRequestMultiError, or nil if none found.
func (m *GetScoresForCasesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScoresForCasesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetScoresForCasesRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetScoresForCasesRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetScoresForCasesRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for S3Path

	// no validation rules for MaxConcurrentDsCalls

	// no validation rules for BatchId

	if len(errors) > 0 {
		return GetScoresForCasesRequestMultiError(errors)
	}

	return nil
}

// GetScoresForCasesRequestMultiError is an error wrapping multiple validation
// errors returned by GetScoresForCasesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetScoresForCasesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScoresForCasesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScoresForCasesRequestMultiError) AllErrors() []error { return m }

// GetScoresForCasesRequestValidationError is the validation error returned by
// GetScoresForCasesRequest.Validate if the designated constraints aren't met.
type GetScoresForCasesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScoresForCasesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScoresForCasesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScoresForCasesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScoresForCasesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScoresForCasesRequestValidationError) ErrorName() string {
	return "GetScoresForCasesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetScoresForCasesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScoresForCasesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScoresForCasesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScoresForCasesRequestValidationError{}

// Validate checks the field values on GetScoresForCasesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetScoresForCasesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScoresForCasesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetScoresForCasesResponseMultiError, or nil if none found.
func (m *GetScoresForCasesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScoresForCasesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetScoresForCasesResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetScoresForCasesResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetScoresForCasesResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTicketsWithScores() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetScoresForCasesResponseValidationError{
						field:  fmt.Sprintf("TicketsWithScores[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetScoresForCasesResponseValidationError{
						field:  fmt.Sprintf("TicketsWithScores[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetScoresForCasesResponseValidationError{
					field:  fmt.Sprintf("TicketsWithScores[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetScoresForCasesResponseMultiError(errors)
	}

	return nil
}

// GetScoresForCasesResponseMultiError is an error wrapping multiple validation
// errors returned by GetScoresForCasesResponse.ValidateAll() if the
// designated constraints aren't met.
type GetScoresForCasesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScoresForCasesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScoresForCasesResponseMultiError) AllErrors() []error { return m }

// GetScoresForCasesResponseValidationError is the validation error returned by
// GetScoresForCasesResponse.Validate if the designated constraints aren't met.
type GetScoresForCasesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScoresForCasesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScoresForCasesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScoresForCasesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScoresForCasesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScoresForCasesResponseValidationError) ErrorName() string {
	return "GetScoresForCasesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetScoresForCasesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScoresForCasesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScoresForCasesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScoresForCasesResponseValidationError{}

// Validate checks the field values on TicketInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TicketInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TicketInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TicketInfoMultiError, or
// nil if none found.
func (m *TicketInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *TicketInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TicketId

	if len(errors) > 0 {
		return TicketInfoMultiError(errors)
	}

	return nil
}

// TicketInfoMultiError is an error wrapping multiple validation errors
// returned by TicketInfo.ValidateAll() if the designated constraints aren't met.
type TicketInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TicketInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TicketInfoMultiError) AllErrors() []error { return m }

// TicketInfoValidationError is the validation error returned by
// TicketInfo.Validate if the designated constraints aren't met.
type TicketInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TicketInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TicketInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TicketInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TicketInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TicketInfoValidationError) ErrorName() string { return "TicketInfoValidationError" }

// Error satisfies the builtin error interface
func (e TicketInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTicketInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TicketInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TicketInfoValidationError{}

// Validate checks the field values on TicketWithScore with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TicketWithScore) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TicketWithScore with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TicketWithScoreMultiError, or nil if none found.
func (m *TicketWithScore) ValidateAll() error {
	return m.validate(true)
}

func (m *TicketWithScore) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TicketId

	// no validation rules for Model1Score

	// no validation rules for Model1Name

	// no validation rules for Model2Score

	// no validation rules for Model2Name

	if len(errors) > 0 {
		return TicketWithScoreMultiError(errors)
	}

	return nil
}

// TicketWithScoreMultiError is an error wrapping multiple validation errors
// returned by TicketWithScore.ValidateAll() if the designated constraints
// aren't met.
type TicketWithScoreMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TicketWithScoreMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TicketWithScoreMultiError) AllErrors() []error { return m }

// TicketWithScoreValidationError is the validation error returned by
// TicketWithScore.Validate if the designated constraints aren't met.
type TicketWithScoreValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TicketWithScoreValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TicketWithScoreValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TicketWithScoreValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TicketWithScoreValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TicketWithScoreValidationError) ErrorName() string { return "TicketWithScoreValidationError" }

// Error satisfies the builtin error interface
func (e TicketWithScoreValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTicketWithScore.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TicketWithScoreValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TicketWithScoreValidationError{}

// Validate checks the field values on BulkUpdateWorkflowRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkUpdateWorkflowRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkUpdateWorkflowRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkUpdateWorkflowRequestMultiError, or nil if none found.
func (m *BulkUpdateWorkflowRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkUpdateWorkflowRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BulkUpdateWorkflowRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BulkUpdateWorkflowRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BulkUpdateWorkflowRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GroupKey

	// no validation rules for Model1Name

	// no validation rules for Model2Name

	// no validation rules for Model1Score

	// no validation rules for Model2Score

	// no validation rules for ConfidenceScore

	if len(errors) > 0 {
		return BulkUpdateWorkflowRequestMultiError(errors)
	}

	return nil
}

// BulkUpdateWorkflowRequestMultiError is an error wrapping multiple validation
// errors returned by BulkUpdateWorkflowRequest.ValidateAll() if the
// designated constraints aren't met.
type BulkUpdateWorkflowRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkUpdateWorkflowRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkUpdateWorkflowRequestMultiError) AllErrors() []error { return m }

// BulkUpdateWorkflowRequestValidationError is the validation error returned by
// BulkUpdateWorkflowRequest.Validate if the designated constraints aren't met.
type BulkUpdateWorkflowRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkUpdateWorkflowRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkUpdateWorkflowRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkUpdateWorkflowRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkUpdateWorkflowRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkUpdateWorkflowRequestValidationError) ErrorName() string {
	return "BulkUpdateWorkflowRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BulkUpdateWorkflowRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkUpdateWorkflowRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkUpdateWorkflowRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkUpdateWorkflowRequestValidationError{}

// Validate checks the field values on BulkUpdateWorkflowResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkUpdateWorkflowResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkUpdateWorkflowResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkUpdateWorkflowResponseMultiError, or nil if none found.
func (m *BulkUpdateWorkflowResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkUpdateWorkflowResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BulkUpdateWorkflowResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BulkUpdateWorkflowResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BulkUpdateWorkflowResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for JobId

	if len(errors) > 0 {
		return BulkUpdateWorkflowResponseMultiError(errors)
	}

	return nil
}

// BulkUpdateWorkflowResponseMultiError is an error wrapping multiple
// validation errors returned by BulkUpdateWorkflowResponse.ValidateAll() if
// the designated constraints aren't met.
type BulkUpdateWorkflowResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkUpdateWorkflowResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkUpdateWorkflowResponseMultiError) AllErrors() []error { return m }

// BulkUpdateWorkflowResponseValidationError is the validation error returned
// by BulkUpdateWorkflowResponse.Validate if the designated constraints aren't met.
type BulkUpdateWorkflowResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkUpdateWorkflowResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkUpdateWorkflowResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkUpdateWorkflowResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkUpdateWorkflowResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkUpdateWorkflowResponseValidationError) ErrorName() string {
	return "BulkUpdateWorkflowResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BulkUpdateWorkflowResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkUpdateWorkflowResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkUpdateWorkflowResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkUpdateWorkflowResponseValidationError{}
