// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/payload/payload.proto

package payload

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TotalFreezeAccountDetailsSentSignal with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TotalFreezeAccountDetailsSentSignal) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TotalFreezeAccountDetailsSentSignal
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// TotalFreezeAccountDetailsSentSignalMultiError, or nil if none found.
func (m *TotalFreezeAccountDetailsSentSignal) ValidateAll() error {
	return m.validate(true)
}

func (m *TotalFreezeAccountDetailsSentSignal) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TotalFreezeAccountDetailsSentSignalMultiError(errors)
	}

	return nil
}

// TotalFreezeAccountDetailsSentSignalMultiError is an error wrapping multiple
// validation errors returned by
// TotalFreezeAccountDetailsSentSignal.ValidateAll() if the designated
// constraints aren't met.
type TotalFreezeAccountDetailsSentSignalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TotalFreezeAccountDetailsSentSignalMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TotalFreezeAccountDetailsSentSignalMultiError) AllErrors() []error { return m }

// TotalFreezeAccountDetailsSentSignalValidationError is the validation error
// returned by TotalFreezeAccountDetailsSentSignal.Validate if the designated
// constraints aren't met.
type TotalFreezeAccountDetailsSentSignalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TotalFreezeAccountDetailsSentSignalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TotalFreezeAccountDetailsSentSignalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TotalFreezeAccountDetailsSentSignalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TotalFreezeAccountDetailsSentSignalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TotalFreezeAccountDetailsSentSignalValidationError) ErrorName() string {
	return "TotalFreezeAccountDetailsSentSignalValidationError"
}

// Error satisfies the builtin error interface
func (e TotalFreezeAccountDetailsSentSignalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTotalFreezeAccountDetailsSentSignal.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TotalFreezeAccountDetailsSentSignalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TotalFreezeAccountDetailsSentSignalValidationError{}

// Validate checks the field values on UnfreezeAccountDetailsSentSignal with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UnfreezeAccountDetailsSentSignal) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnfreezeAccountDetailsSentSignal with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UnfreezeAccountDetailsSentSignalMultiError, or nil if none found.
func (m *UnfreezeAccountDetailsSentSignal) ValidateAll() error {
	return m.validate(true)
}

func (m *UnfreezeAccountDetailsSentSignal) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UnfreezeAccountDetailsSentSignalMultiError(errors)
	}

	return nil
}

// UnfreezeAccountDetailsSentSignalMultiError is an error wrapping multiple
// validation errors returned by
// UnfreezeAccountDetailsSentSignal.ValidateAll() if the designated
// constraints aren't met.
type UnfreezeAccountDetailsSentSignalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnfreezeAccountDetailsSentSignalMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnfreezeAccountDetailsSentSignalMultiError) AllErrors() []error { return m }

// UnfreezeAccountDetailsSentSignalValidationError is the validation error
// returned by UnfreezeAccountDetailsSentSignal.Validate if the designated
// constraints aren't met.
type UnfreezeAccountDetailsSentSignalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnfreezeAccountDetailsSentSignalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnfreezeAccountDetailsSentSignalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnfreezeAccountDetailsSentSignalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnfreezeAccountDetailsSentSignalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnfreezeAccountDetailsSentSignalValidationError) ErrorName() string {
	return "UnfreezeAccountDetailsSentSignalValidationError"
}

// Error satisfies the builtin error interface
func (e UnfreezeAccountDetailsSentSignalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnfreezeAccountDetailsSentSignal.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnfreezeAccountDetailsSentSignalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnfreezeAccountDetailsSentSignalValidationError{}

// Validate checks the field values on CreditFreezeAccountDetailsSentSignal
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreditFreezeAccountDetailsSentSignal) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditFreezeAccountDetailsSentSignal
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreditFreezeAccountDetailsSentSignalMultiError, or nil if none found.
func (m *CreditFreezeAccountDetailsSentSignal) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditFreezeAccountDetailsSentSignal) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CreditFreezeAccountDetailsSentSignalMultiError(errors)
	}

	return nil
}

// CreditFreezeAccountDetailsSentSignalMultiError is an error wrapping multiple
// validation errors returned by
// CreditFreezeAccountDetailsSentSignal.ValidateAll() if the designated
// constraints aren't met.
type CreditFreezeAccountDetailsSentSignalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditFreezeAccountDetailsSentSignalMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditFreezeAccountDetailsSentSignalMultiError) AllErrors() []error { return m }

// CreditFreezeAccountDetailsSentSignalValidationError is the validation error
// returned by CreditFreezeAccountDetailsSentSignal.Validate if the designated
// constraints aren't met.
type CreditFreezeAccountDetailsSentSignalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditFreezeAccountDetailsSentSignalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditFreezeAccountDetailsSentSignalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditFreezeAccountDetailsSentSignalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditFreezeAccountDetailsSentSignalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditFreezeAccountDetailsSentSignalValidationError) ErrorName() string {
	return "CreditFreezeAccountDetailsSentSignalValidationError"
}

// Error satisfies the builtin error interface
func (e CreditFreezeAccountDetailsSentSignalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditFreezeAccountDetailsSentSignal.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditFreezeAccountDetailsSentSignalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditFreezeAccountDetailsSentSignalValidationError{}

// Validate checks the field values on DebitFreezeAccountDetailsSentSignal with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DebitFreezeAccountDetailsSentSignal) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DebitFreezeAccountDetailsSentSignal
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DebitFreezeAccountDetailsSentSignalMultiError, or nil if none found.
func (m *DebitFreezeAccountDetailsSentSignal) ValidateAll() error {
	return m.validate(true)
}

func (m *DebitFreezeAccountDetailsSentSignal) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DebitFreezeAccountDetailsSentSignalMultiError(errors)
	}

	return nil
}

// DebitFreezeAccountDetailsSentSignalMultiError is an error wrapping multiple
// validation errors returned by
// DebitFreezeAccountDetailsSentSignal.ValidateAll() if the designated
// constraints aren't met.
type DebitFreezeAccountDetailsSentSignalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DebitFreezeAccountDetailsSentSignalMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DebitFreezeAccountDetailsSentSignalMultiError) AllErrors() []error { return m }

// DebitFreezeAccountDetailsSentSignalValidationError is the validation error
// returned by DebitFreezeAccountDetailsSentSignal.Validate if the designated
// constraints aren't met.
type DebitFreezeAccountDetailsSentSignalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DebitFreezeAccountDetailsSentSignalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DebitFreezeAccountDetailsSentSignalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DebitFreezeAccountDetailsSentSignalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DebitFreezeAccountDetailsSentSignalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DebitFreezeAccountDetailsSentSignalValidationError) ErrorName() string {
	return "DebitFreezeAccountDetailsSentSignalValidationError"
}

// Error satisfies the builtin error interface
func (e DebitFreezeAccountDetailsSentSignalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDebitFreezeAccountDetailsSentSignal.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DebitFreezeAccountDetailsSentSignalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DebitFreezeAccountDetailsSentSignalValidationError{}

// Validate checks the field values on BankActionDataPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BankActionDataPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankActionDataPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BankActionDataPayloadMultiError, or nil if none found.
func (m *BankActionDataPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *BankActionDataPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTotalBankActionData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankActionDataPayloadValidationError{
					field:  "TotalBankActionData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankActionDataPayloadValidationError{
					field:  "TotalBankActionData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalBankActionData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankActionDataPayloadValidationError{
				field:  "TotalBankActionData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BankActionDataPayloadMultiError(errors)
	}

	return nil
}

// BankActionDataPayloadMultiError is an error wrapping multiple validation
// errors returned by BankActionDataPayload.ValidateAll() if the designated
// constraints aren't met.
type BankActionDataPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankActionDataPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankActionDataPayloadMultiError) AllErrors() []error { return m }

// BankActionDataPayloadValidationError is the validation error returned by
// BankActionDataPayload.Validate if the designated constraints aren't met.
type BankActionDataPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankActionDataPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankActionDataPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankActionDataPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankActionDataPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankActionDataPayloadValidationError) ErrorName() string {
	return "BankActionDataPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e BankActionDataPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankActionDataPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankActionDataPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankActionDataPayloadValidationError{}

// Validate checks the field values on BankActionData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BankActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankActionData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BankActionDataMultiError,
// or nil if none found.
func (m *BankActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *BankActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileName

	// no validation rules for BankActionDataFile

	if len(errors) > 0 {
		return BankActionDataMultiError(errors)
	}

	return nil
}

// BankActionDataMultiError is an error wrapping multiple validation errors
// returned by BankActionData.ValidateAll() if the designated constraints
// aren't met.
type BankActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankActionDataMultiError) AllErrors() []error { return m }

// BankActionDataValidationError is the validation error returned by
// BankActionData.Validate if the designated constraints aren't met.
type BankActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankActionDataValidationError) ErrorName() string { return "BankActionDataValidationError" }

// Error satisfies the builtin error interface
func (e BankActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankActionDataValidationError{}

// Validate checks the field values on RiskDataSenderRequestPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskDataSenderRequestPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskDataSenderRequestPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskDataSenderRequestPayloadMultiError, or nil if none found.
func (m *RiskDataSenderRequestPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskDataSenderRequestPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FromEmail

	// no validation rules for ToEmail

	if len(errors) > 0 {
		return RiskDataSenderRequestPayloadMultiError(errors)
	}

	return nil
}

// RiskDataSenderRequestPayloadMultiError is an error wrapping multiple
// validation errors returned by RiskDataSenderRequestPayload.ValidateAll() if
// the designated constraints aren't met.
type RiskDataSenderRequestPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskDataSenderRequestPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskDataSenderRequestPayloadMultiError) AllErrors() []error { return m }

// RiskDataSenderRequestPayloadValidationError is the validation error returned
// by RiskDataSenderRequestPayload.Validate if the designated constraints
// aren't met.
type RiskDataSenderRequestPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskDataSenderRequestPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskDataSenderRequestPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskDataSenderRequestPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskDataSenderRequestPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskDataSenderRequestPayloadValidationError) ErrorName() string {
	return "RiskDataSenderRequestPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e RiskDataSenderRequestPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskDataSenderRequestPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskDataSenderRequestPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskDataSenderRequestPayloadValidationError{}
