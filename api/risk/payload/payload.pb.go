// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/payload/payload.proto

package payload

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// payload to be sent with signal when freeze account data is sent to bank
// It is kept empty for now, because the signal itself denotes that some action has been taken
type TotalFreezeAccountDetailsSentSignal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TotalFreezeAccountDetailsSentSignal) Reset() {
	*x = TotalFreezeAccountDetailsSentSignal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_payload_payload_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TotalFreezeAccountDetailsSentSignal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TotalFreezeAccountDetailsSentSignal) ProtoMessage() {}

func (x *TotalFreezeAccountDetailsSentSignal) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_payload_payload_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TotalFreezeAccountDetailsSentSignal.ProtoReflect.Descriptor instead.
func (*TotalFreezeAccountDetailsSentSignal) Descriptor() ([]byte, []int) {
	return file_api_risk_payload_payload_proto_rawDescGZIP(), []int{0}
}

// payload to be sent with signal when unfreeze account data is sent to bank
// It is kept empty for now, because the signal itself denotes that some action has been taken
type UnfreezeAccountDetailsSentSignal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UnfreezeAccountDetailsSentSignal) Reset() {
	*x = UnfreezeAccountDetailsSentSignal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_payload_payload_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnfreezeAccountDetailsSentSignal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnfreezeAccountDetailsSentSignal) ProtoMessage() {}

func (x *UnfreezeAccountDetailsSentSignal) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_payload_payload_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnfreezeAccountDetailsSentSignal.ProtoReflect.Descriptor instead.
func (*UnfreezeAccountDetailsSentSignal) Descriptor() ([]byte, []int) {
	return file_api_risk_payload_payload_proto_rawDescGZIP(), []int{1}
}

// payload to be sent with signal when credit freeze account data is sent to bank
// It is kept empty for now, because the signal itself denotes that some action has been taken
type CreditFreezeAccountDetailsSentSignal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreditFreezeAccountDetailsSentSignal) Reset() {
	*x = CreditFreezeAccountDetailsSentSignal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_payload_payload_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditFreezeAccountDetailsSentSignal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditFreezeAccountDetailsSentSignal) ProtoMessage() {}

func (x *CreditFreezeAccountDetailsSentSignal) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_payload_payload_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditFreezeAccountDetailsSentSignal.ProtoReflect.Descriptor instead.
func (*CreditFreezeAccountDetailsSentSignal) Descriptor() ([]byte, []int) {
	return file_api_risk_payload_payload_proto_rawDescGZIP(), []int{2}
}

// payload to be sent with signal when debit freeze account data is sent to bank
// It is kept empty for now, because the signal itself denotes that some action has been taken
type DebitFreezeAccountDetailsSentSignal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DebitFreezeAccountDetailsSentSignal) Reset() {
	*x = DebitFreezeAccountDetailsSentSignal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_payload_payload_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebitFreezeAccountDetailsSentSignal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebitFreezeAccountDetailsSentSignal) ProtoMessage() {}

func (x *DebitFreezeAccountDetailsSentSignal) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_payload_payload_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebitFreezeAccountDetailsSentSignal.ProtoReflect.Descriptor instead.
func (*DebitFreezeAccountDetailsSentSignal) Descriptor() ([]byte, []int) {
	return file_api_risk_payload_payload_proto_rawDescGZIP(), []int{3}
}

// payload consisting data to be sent to bank
type BankActionDataPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalBankActionData *BankActionData `protobuf:"bytes,1,opt,name=total_bank_action_data,json=totalBankActionData,proto3" json:"total_bank_action_data,omitempty"`
}

func (x *BankActionDataPayload) Reset() {
	*x = BankActionDataPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_payload_payload_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankActionDataPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankActionDataPayload) ProtoMessage() {}

func (x *BankActionDataPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_payload_payload_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankActionDataPayload.ProtoReflect.Descriptor instead.
func (*BankActionDataPayload) Descriptor() ([]byte, []int) {
	return file_api_risk_payload_payload_proto_rawDescGZIP(), []int{4}
}

func (x *BankActionDataPayload) GetTotalBankActionData() *BankActionData {
	if x != nil {
		return x.TotalBankActionData
	}
	return nil
}

// data required for sending mail to the bank
type BankActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the file sent
	FileName string `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	// file sent to the bank
	BankActionDataFile []byte `protobuf:"bytes,2,opt,name=bank_action_data_file,json=bankActionDataFile,proto3" json:"bank_action_data_file,omitempty"`
	// total freeze client_req_ids are required for sending signals
	TfSignalClientReqIds []string `protobuf:"bytes,3,rep,name=tf_signal_client_req_ids,json=tfSignalClientReqIds,proto3" json:"tf_signal_client_req_ids,omitempty"`
	// credit freeze client_req_ids are required for sending signals
	CfSignalClientReqIds []string `protobuf:"bytes,4,rep,name=cf_signal_client_req_ids,json=cfSignalClientReqIds,proto3" json:"cf_signal_client_req_ids,omitempty"`
	// unfreeze client_req_ids are required for sending signals
	UfSignalClientReqIds []string `protobuf:"bytes,5,rep,name=uf_signal_client_req_ids,json=ufSignalClientReqIds,proto3" json:"uf_signal_client_req_ids,omitempty"`
	// debit freeze client_req_ids are required for sending signals
	DfSignalClientReqIds []string `protobuf:"bytes,6,rep,name=df_signal_client_req_ids,json=dfSignalClientReqIds,proto3" json:"df_signal_client_req_ids,omitempty"`
}

func (x *BankActionData) Reset() {
	*x = BankActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_payload_payload_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankActionData) ProtoMessage() {}

func (x *BankActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_payload_payload_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankActionData.ProtoReflect.Descriptor instead.
func (*BankActionData) Descriptor() ([]byte, []int) {
	return file_api_risk_payload_payload_proto_rawDescGZIP(), []int{5}
}

func (x *BankActionData) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *BankActionData) GetBankActionDataFile() []byte {
	if x != nil {
		return x.BankActionDataFile
	}
	return nil
}

func (x *BankActionData) GetTfSignalClientReqIds() []string {
	if x != nil {
		return x.TfSignalClientReqIds
	}
	return nil
}

func (x *BankActionData) GetCfSignalClientReqIds() []string {
	if x != nil {
		return x.CfSignalClientReqIds
	}
	return nil
}

func (x *BankActionData) GetUfSignalClientReqIds() []string {
	if x != nil {
		return x.UfSignalClientReqIds
	}
	return nil
}

func (x *BankActionData) GetDfSignalClientReqIds() []string {
	if x != nil {
		return x.DfSignalClientReqIds
	}
	return nil
}

// request payload to be sent while triggering workflow execution
type RiskDataSenderRequestPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FromEmail string `protobuf:"bytes,1,opt,name=from_email,json=fromEmail,proto3" json:"from_email,omitempty"`
	ToEmail   string `protobuf:"bytes,2,opt,name=to_email,json=toEmail,proto3" json:"to_email,omitempty"`
}

func (x *RiskDataSenderRequestPayload) Reset() {
	*x = RiskDataSenderRequestPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_payload_payload_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskDataSenderRequestPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskDataSenderRequestPayload) ProtoMessage() {}

func (x *RiskDataSenderRequestPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_payload_payload_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskDataSenderRequestPayload.ProtoReflect.Descriptor instead.
func (*RiskDataSenderRequestPayload) Descriptor() ([]byte, []int) {
	return file_api_risk_payload_payload_proto_rawDescGZIP(), []int{6}
}

func (x *RiskDataSenderRequestPayload) GetFromEmail() string {
	if x != nil {
		return x.FromEmail
	}
	return ""
}

func (x *RiskDataSenderRequestPayload) GetToEmail() string {
	if x != nil {
		return x.ToEmail
	}
	return ""
}

var File_api_risk_payload_payload_proto protoreflect.FileDescriptor

var file_api_risk_payload_payload_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x2f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0c, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0x25,
	0x0a, 0x23, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x53, 0x65, 0x6e, 0x74, 0x53,
	0x69, 0x67, 0x6e, 0x61, 0x6c, 0x22, 0x22, 0x0a, 0x20, 0x55, 0x6e, 0x66, 0x72, 0x65, 0x65, 0x7a,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x53,
	0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x22, 0x26, 0x0a, 0x24, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x53, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61,
	0x6c, 0x22, 0x25, 0x0a, 0x23, 0x44, 0x65, 0x62, 0x69, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x53, 0x65,
	0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x22, 0x6a, 0x0a, 0x15, 0x42, 0x61, 0x6e, 0x6b,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x12, 0x51, 0x0a, 0x16, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x22, 0xc0, 0x02, 0x0a, 0x0e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x12, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x36, 0x0a, 0x18, 0x74, 0x66, 0x5f, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x74, 0x66, 0x53, 0x69, 0x67,
	0x6e, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x73, 0x12,
	0x36, 0x0a, 0x18, 0x63, 0x66, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x14, 0x63, 0x66, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x73, 0x12, 0x36, 0x0a, 0x18, 0x75, 0x66, 0x5f, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x75, 0x66, 0x53, 0x69, 0x67,
	0x6e, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x73, 0x12,
	0x36, 0x0a, 0x18, 0x64, 0x66, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x14, 0x64, 0x66, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x73, 0x22, 0x58, 0x0a, 0x1c, 0x52, 0x69, 0x73, 0x6b, 0x44,
	0x61, 0x74, 0x61, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x5f,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x72, 0x6f,
	0x6d, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x6f, 0x5f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x6f, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x42, 0x52, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x5a, 0x27, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x70, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_payload_payload_proto_rawDescOnce sync.Once
	file_api_risk_payload_payload_proto_rawDescData = file_api_risk_payload_payload_proto_rawDesc
)

func file_api_risk_payload_payload_proto_rawDescGZIP() []byte {
	file_api_risk_payload_payload_proto_rawDescOnce.Do(func() {
		file_api_risk_payload_payload_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_payload_payload_proto_rawDescData)
	})
	return file_api_risk_payload_payload_proto_rawDescData
}

var file_api_risk_payload_payload_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_risk_payload_payload_proto_goTypes = []interface{}{
	(*TotalFreezeAccountDetailsSentSignal)(nil),  // 0: risk.payload.TotalFreezeAccountDetailsSentSignal
	(*UnfreezeAccountDetailsSentSignal)(nil),     // 1: risk.payload.UnfreezeAccountDetailsSentSignal
	(*CreditFreezeAccountDetailsSentSignal)(nil), // 2: risk.payload.CreditFreezeAccountDetailsSentSignal
	(*DebitFreezeAccountDetailsSentSignal)(nil),  // 3: risk.payload.DebitFreezeAccountDetailsSentSignal
	(*BankActionDataPayload)(nil),                // 4: risk.payload.BankActionDataPayload
	(*BankActionData)(nil),                       // 5: risk.payload.BankActionData
	(*RiskDataSenderRequestPayload)(nil),         // 6: risk.payload.RiskDataSenderRequestPayload
}
var file_api_risk_payload_payload_proto_depIdxs = []int32{
	5, // 0: risk.payload.BankActionDataPayload.total_bank_action_data:type_name -> risk.payload.BankActionData
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_risk_payload_payload_proto_init() }
func file_api_risk_payload_payload_proto_init() {
	if File_api_risk_payload_payload_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_payload_payload_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TotalFreezeAccountDetailsSentSignal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_payload_payload_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnfreezeAccountDetailsSentSignal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_payload_payload_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditFreezeAccountDetailsSentSignal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_payload_payload_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebitFreezeAccountDetailsSentSignal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_payload_payload_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankActionDataPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_payload_payload_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_payload_payload_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskDataSenderRequestPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_payload_payload_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_payload_payload_proto_goTypes,
		DependencyIndexes: file_api_risk_payload_payload_proto_depIdxs,
		MessageInfos:      file_api_risk_payload_payload_proto_msgTypes,
	}.Build()
	File_api_risk_payload_payload_proto = out.File
	file_api_risk_payload_payload_proto_rawDesc = nil
	file_api_risk_payload_payload_proto_goTypes = nil
	file_api_risk_payload_payload_proto_depIdxs = nil
}
