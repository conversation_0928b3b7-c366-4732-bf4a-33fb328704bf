// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/lea/unified_lea_comms.proto

package lea

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	enums "github.com/epifi/gamma/api/risk/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = enums.AccountFreezeStatus(0)
)

// Validate checks the field values on HandlingParams with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HandlingParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandlingParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HandlingParamsMultiError,
// or nil if none found.
func (m *HandlingParams) ValidateAll() error {
	return m.validate(true)
}

func (m *HandlingParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := HandlingParamsValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAccountId()) < 1 {
		err := HandlingParamsValidationError{
			field:  "AccountId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OperationalStatus

	if all {
		switch v := interface{}(m.GetLienAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HandlingParamsValidationError{
					field:  "LienAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HandlingParamsValidationError{
					field:  "LienAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLienAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HandlingParamsValidationError{
				field:  "LienAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RemoveAppAccess

	for idx, item := range m.GetComplaints() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HandlingParamsValidationError{
						field:  fmt.Sprintf("Complaints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HandlingParamsValidationError{
						field:  fmt.Sprintf("Complaints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HandlingParamsValidationError{
					field:  fmt.Sprintf("Complaints[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ShouldSendComms

	// no validation rules for ActionTakenByBank

	if utf8.RuneCountInString(m.GetEntityId()) < 1 {
		err := HandlingParamsValidationError{
			field:  "EntityId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetRuleIdentifiers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HandlingParamsValidationError{
						field:  fmt.Sprintf("RuleIdentifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HandlingParamsValidationError{
						field:  fmt.Sprintf("RuleIdentifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HandlingParamsValidationError{
					field:  fmt.Sprintf("RuleIdentifiers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetLayerNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HandlingParamsValidationError{
					field:  "LayerNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HandlingParamsValidationError{
					field:  "LayerNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLayerNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HandlingParamsValidationError{
				field:  "LayerNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HandlingParamsMultiError(errors)
	}

	return nil
}

// HandlingParamsMultiError is an error wrapping multiple validation errors
// returned by HandlingParams.ValidateAll() if the designated constraints
// aren't met.
type HandlingParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandlingParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandlingParamsMultiError) AllErrors() []error { return m }

// HandlingParamsValidationError is the validation error returned by
// HandlingParams.Validate if the designated constraints aren't met.
type HandlingParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandlingParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandlingParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandlingParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandlingParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandlingParamsValidationError) ErrorName() string { return "HandlingParamsValidationError" }

// Error satisfies the builtin error interface
func (e HandlingParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandlingParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandlingParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandlingParamsValidationError{}

// Validate checks the field values on HandlingParamsComplaint with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandlingParamsComplaint) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandlingParamsComplaint with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandlingParamsComplaintMultiError, or nil if none found.
func (m *HandlingParamsComplaint) ValidateAll() error {
	return m.validate(true)
}

func (m *HandlingParamsComplaint) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComplaintId

	if all {
		switch v := interface{}(m.GetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HandlingParamsComplaintValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HandlingParamsComplaintValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HandlingParamsComplaintValidationError{
				field:  "Date",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContactDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HandlingParamsComplaintValidationError{
					field:  "ContactDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HandlingParamsComplaintValidationError{
					field:  "ContactDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContactDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HandlingParamsComplaintValidationError{
				field:  "ContactDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ComplaintOriginState

	if all {
		switch v := interface{}(m.GetDisputedTransactions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HandlingParamsComplaintValidationError{
					field:  "DisputedTransactions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HandlingParamsComplaintValidationError{
					field:  "DisputedTransactions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisputedTransactions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HandlingParamsComplaintValidationError{
				field:  "DisputedTransactions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HandlingParamsComplaintMultiError(errors)
	}

	return nil
}

// HandlingParamsComplaintMultiError is an error wrapping multiple validation
// errors returned by HandlingParamsComplaint.ValidateAll() if the designated
// constraints aren't met.
type HandlingParamsComplaintMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandlingParamsComplaintMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandlingParamsComplaintMultiError) AllErrors() []error { return m }

// HandlingParamsComplaintValidationError is the validation error returned by
// HandlingParamsComplaint.Validate if the designated constraints aren't met.
type HandlingParamsComplaintValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandlingParamsComplaintValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandlingParamsComplaintValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandlingParamsComplaintValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandlingParamsComplaintValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandlingParamsComplaintValidationError) ErrorName() string {
	return "HandlingParamsComplaintValidationError"
}

// Error satisfies the builtin error interface
func (e HandlingParamsComplaintValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandlingParamsComplaint.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandlingParamsComplaintValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandlingParamsComplaintValidationError{}
