// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/lea/unified_lea_complaint.proto

package lea

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	accounts "github.com/epifi/gamma/api/accounts"

	enums "github.com/epifi/gamma/api/risk/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = accounts.Type(0)

	_ = enums.AccountFreezeStatus(0)
)

// Validate checks the field values on UnifiedLeaComplaint with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnifiedLeaComplaint) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnifiedLeaComplaint with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnifiedLeaComplaintMultiError, or nil if none found.
func (m *UnifiedLeaComplaint) ValidateAll() error {
	return m.validate(true)
}

func (m *UnifiedLeaComplaint) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ComplaintId

	// no validation rules for ActorId

	// no validation rules for AccountNumber

	// no validation rules for AccountType

	if all {
		switch v := interface{}(m.GetDates()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "Dates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "Dates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDates()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnifiedLeaComplaintValidationError{
				field:  "Dates",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActionType

	// no validation rules for OperationalStatus

	if all {
		switch v := interface{}(m.GetLien()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "Lien",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "Lien",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLien()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnifiedLeaComplaintValidationError{
				field:  "Lien",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Reason

	// no validation rules for Remark

	// no validation rules for FreezeCode

	if all {
		switch v := interface{}(m.GetLayerNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "LayerNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "LayerNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLayerNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnifiedLeaComplaintValidationError{
				field:  "LayerNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisputedTransactions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "DisputedTransactions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "DisputedTransactions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisputedTransactions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnifiedLeaComplaintValidationError{
				field:  "DisputedTransactions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProcessingStatus

	// no validation rules for LeaOrigin

	if all {
		switch v := interface{}(m.GetReporterContactDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "ReporterContactDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "ReporterContactDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReporterContactDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnifiedLeaComplaintValidationError{
				field:  "ReporterContactDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OriginGeographicalState

	if all {
		switch v := interface{}(m.GetAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnifiedLeaComplaintValidationError{
				field:  "AdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnifiedLeaComplaintValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnifiedLeaComplaintValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAtUnix()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "DeletedAtUnix",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnifiedLeaComplaintValidationError{
					field:  "DeletedAtUnix",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAtUnix()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnifiedLeaComplaintValidationError{
				field:  "DeletedAtUnix",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UnifiedLeaComplaintMultiError(errors)
	}

	return nil
}

// UnifiedLeaComplaintMultiError is an error wrapping multiple validation
// errors returned by UnifiedLeaComplaint.ValidateAll() if the designated
// constraints aren't met.
type UnifiedLeaComplaintMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnifiedLeaComplaintMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnifiedLeaComplaintMultiError) AllErrors() []error { return m }

// UnifiedLeaComplaintValidationError is the validation error returned by
// UnifiedLeaComplaint.Validate if the designated constraints aren't met.
type UnifiedLeaComplaintValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnifiedLeaComplaintValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnifiedLeaComplaintValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnifiedLeaComplaintValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnifiedLeaComplaintValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnifiedLeaComplaintValidationError) ErrorName() string {
	return "UnifiedLeaComplaintValidationError"
}

// Error satisfies the builtin error interface
func (e UnifiedLeaComplaintValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnifiedLeaComplaint.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnifiedLeaComplaintValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnifiedLeaComplaintValidationError{}

// Validate checks the field values on Lien with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Lien) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lien with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in LienMultiError, or nil if none found.
func (m *Lien) ValidateAll() error {
	return m.validate(true)
}

func (m *Lien) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnRef

	// no validation rules for Amount

	if all {
		switch v := interface{}(m.GetTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LienValidationError{
					field:  "Timestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LienValidationError{
					field:  "Timestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LienValidationError{
				field:  "Timestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawData

	if len(errors) > 0 {
		return LienMultiError(errors)
	}

	return nil
}

// LienMultiError is an error wrapping multiple validation errors returned by
// Lien.ValidateAll() if the designated constraints aren't met.
type LienMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LienMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LienMultiError) AllErrors() []error { return m }

// LienValidationError is the validation error returned by Lien.Validate if the
// designated constraints aren't met.
type LienValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LienValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LienValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LienValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LienValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LienValidationError) ErrorName() string { return "LienValidationError" }

// Error satisfies the builtin error interface
func (e LienValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLien.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LienValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LienValidationError{}

// Validate checks the field values on DisputedTransactions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DisputedTransactions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DisputedTransactions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DisputedTransactionsMultiError, or nil if none found.
func (m *DisputedTransactions) ValidateAll() error {
	return m.validate(true)
}

func (m *DisputedTransactions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DisputedTransactionsValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DisputedTransactionsValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DisputedTransactionsValidationError{
					field:  fmt.Sprintf("Transactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DisputedTransactionsMultiError(errors)
	}

	return nil
}

// DisputedTransactionsMultiError is an error wrapping multiple validation
// errors returned by DisputedTransactions.ValidateAll() if the designated
// constraints aren't met.
type DisputedTransactionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DisputedTransactionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DisputedTransactionsMultiError) AllErrors() []error { return m }

// DisputedTransactionsValidationError is the validation error returned by
// DisputedTransactions.Validate if the designated constraints aren't met.
type DisputedTransactionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DisputedTransactionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DisputedTransactionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DisputedTransactionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DisputedTransactionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DisputedTransactionsValidationError) ErrorName() string {
	return "DisputedTransactionsValidationError"
}

// Error satisfies the builtin error interface
func (e DisputedTransactionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDisputedTransactions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DisputedTransactionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DisputedTransactionsValidationError{}

// Validate checks the field values on ReporterContactDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReporterContactDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReporterContactDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReporterContactDetailsMultiError, or nil if none found.
func (m *ReporterContactDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ReporterContactDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNodalCyberCellOfficerDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReporterContactDetailsValidationError{
					field:  "NodalCyberCellOfficerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReporterContactDetailsValidationError{
					field:  "NodalCyberCellOfficerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNodalCyberCellOfficerDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReporterContactDetailsValidationError{
				field:  "NodalCyberCellOfficerDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGrievanceOfficerDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReporterContactDetailsValidationError{
					field:  "GrievanceOfficerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReporterContactDetailsValidationError{
					field:  "GrievanceOfficerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGrievanceOfficerDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReporterContactDetailsValidationError{
				field:  "GrievanceOfficerDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReporterContactDetailsMultiError(errors)
	}

	return nil
}

// ReporterContactDetailsMultiError is an error wrapping multiple validation
// errors returned by ReporterContactDetails.ValidateAll() if the designated
// constraints aren't met.
type ReporterContactDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReporterContactDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReporterContactDetailsMultiError) AllErrors() []error { return m }

// ReporterContactDetailsValidationError is the validation error returned by
// ReporterContactDetails.Validate if the designated constraints aren't met.
type ReporterContactDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReporterContactDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReporterContactDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReporterContactDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReporterContactDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReporterContactDetailsValidationError) ErrorName() string {
	return "ReporterContactDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ReporterContactDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReporterContactDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReporterContactDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReporterContactDetailsValidationError{}

// Validate checks the field values on AdditionalDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AdditionalDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdditionalDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdditionalDetailsMultiError, or nil if none found.
func (m *AdditionalDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AdditionalDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AdditionalDetailsMultiError(errors)
	}

	return nil
}

// AdditionalDetailsMultiError is an error wrapping multiple validation errors
// returned by AdditionalDetails.ValidateAll() if the designated constraints
// aren't met.
type AdditionalDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdditionalDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdditionalDetailsMultiError) AllErrors() []error { return m }

// AdditionalDetailsValidationError is the validation error returned by
// AdditionalDetails.Validate if the designated constraints aren't met.
type AdditionalDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdditionalDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdditionalDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdditionalDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdditionalDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdditionalDetailsValidationError) ErrorName() string {
	return "AdditionalDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AdditionalDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdditionalDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdditionalDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdditionalDetailsValidationError{}

// Validate checks the field values on Dates with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Dates) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Dates with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in DatesMultiError, or nil if none found.
func (m *Dates) ValidateAll() error {
	return m.validate(true)
}

func (m *Dates) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuditDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DatesValidationError{
					field:  "AuditDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DatesValidationError{
					field:  "AuditDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuditDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DatesValidationError{
				field:  "AuditDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccountClosureDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DatesValidationError{
					field:  "AccountClosureDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DatesValidationError{
					field:  "AccountClosureDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountClosureDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DatesValidationError{
				field:  "AccountClosureDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetComplaintDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DatesValidationError{
					field:  "ComplaintDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DatesValidationError{
					field:  "ComplaintDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetComplaintDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DatesValidationError{
				field:  "ComplaintDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDetailsReceivedDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DatesValidationError{
					field:  "DetailsReceivedDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DatesValidationError{
					field:  "DetailsReceivedDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetailsReceivedDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DatesValidationError{
				field:  "DetailsReceivedDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DatesMultiError(errors)
	}

	return nil
}

// DatesMultiError is an error wrapping multiple validation errors returned by
// Dates.ValidateAll() if the designated constraints aren't met.
type DatesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DatesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DatesMultiError) AllErrors() []error { return m }

// DatesValidationError is the validation error returned by Dates.Validate if
// the designated constraints aren't met.
type DatesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DatesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DatesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DatesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DatesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DatesValidationError) ErrorName() string { return "DatesValidationError" }

// Error satisfies the builtin error interface
func (e DatesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDates.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DatesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DatesValidationError{}

// Validate checks the field values on DisputedTransactions_Transaction with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DisputedTransactions_Transaction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DisputedTransactions_Transaction with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DisputedTransactions_TransactionMultiError, or nil if none found.
func (m *DisputedTransactions_Transaction) ValidateAll() error {
	return m.validate(true)
}

func (m *DisputedTransactions_Transaction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnRef

	if all {
		switch v := interface{}(m.GetTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DisputedTransactions_TransactionValidationError{
					field:  "Timestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DisputedTransactions_TransactionValidationError{
					field:  "Timestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DisputedTransactions_TransactionValidationError{
				field:  "Timestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Amount

	// no validation rules for DisputedAmount

	if len(errors) > 0 {
		return DisputedTransactions_TransactionMultiError(errors)
	}

	return nil
}

// DisputedTransactions_TransactionMultiError is an error wrapping multiple
// validation errors returned by
// DisputedTransactions_Transaction.ValidateAll() if the designated
// constraints aren't met.
type DisputedTransactions_TransactionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DisputedTransactions_TransactionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DisputedTransactions_TransactionMultiError) AllErrors() []error { return m }

// DisputedTransactions_TransactionValidationError is the validation error
// returned by DisputedTransactions_Transaction.Validate if the designated
// constraints aren't met.
type DisputedTransactions_TransactionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DisputedTransactions_TransactionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DisputedTransactions_TransactionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DisputedTransactions_TransactionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DisputedTransactions_TransactionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DisputedTransactions_TransactionValidationError) ErrorName() string {
	return "DisputedTransactions_TransactionValidationError"
}

// Error satisfies the builtin error interface
func (e DisputedTransactions_TransactionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDisputedTransactions_Transaction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DisputedTransactions_TransactionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DisputedTransactions_TransactionValidationError{}

// Validate checks the field values on ReporterContactDetails_Details with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReporterContactDetails_Details) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReporterContactDetails_Details with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ReporterContactDetails_DetailsMultiError, or nil if none found.
func (m *ReporterContactDetails_Details) ValidateAll() error {
	return m.validate(true)
}

func (m *ReporterContactDetails_Details) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for PhoneNumber

	// no validation rules for Email

	// no validation rules for RawData

	if len(errors) > 0 {
		return ReporterContactDetails_DetailsMultiError(errors)
	}

	return nil
}

// ReporterContactDetails_DetailsMultiError is an error wrapping multiple
// validation errors returned by ReporterContactDetails_Details.ValidateAll()
// if the designated constraints aren't met.
type ReporterContactDetails_DetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReporterContactDetails_DetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReporterContactDetails_DetailsMultiError) AllErrors() []error { return m }

// ReporterContactDetails_DetailsValidationError is the validation error
// returned by ReporterContactDetails_Details.Validate if the designated
// constraints aren't met.
type ReporterContactDetails_DetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReporterContactDetails_DetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReporterContactDetails_DetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReporterContactDetails_DetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReporterContactDetails_DetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReporterContactDetails_DetailsValidationError) ErrorName() string {
	return "ReporterContactDetails_DetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ReporterContactDetails_DetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReporterContactDetails_Details.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReporterContactDetails_DetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReporterContactDetails_DetailsValidationError{}
