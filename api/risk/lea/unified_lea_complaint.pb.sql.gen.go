// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/risk/lea/unified_lea_complaint.pb.go

package lea

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"encoding/json"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Valuer interface implementation for storing the ActionType in string format in DB
func (p ActionType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ActionType while reading from DB
func (p *ActionType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ActionType_value[val]
	if !ok {
		return fmt.Errorf("unexpected ActionType value: %s", val)
	}
	*p = ActionType(valInt)
	return nil
}

// Marshaler interface implementation for ActionType
func (x ActionType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ActionType
func (x *ActionType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ActionType(ActionType_value[val])
	return nil
}

// Valuer interface implementation for storing the ProcessingStatus in string format in DB
func (p ProcessingStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ProcessingStatus while reading from DB
func (p *ProcessingStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ProcessingStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected ProcessingStatus value: %s", val)
	}
	*p = ProcessingStatus(valInt)
	return nil
}

// Marshaler interface implementation for ProcessingStatus
func (x ProcessingStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ProcessingStatus
func (x *ProcessingStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ProcessingStatus(ProcessingStatus_value[val])
	return nil
}

// Scanner interface implementation for parsing Lien while reading from DB
func (a *Lien) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the Lien in string format in DB
func (a *Lien) Value() (driver.Value, error) {
	return protojson.Marshal(a)
}

// Marshaler interface implementation for Lien
func (a *Lien) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for Lien
func (a *Lien) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing DisputedTransactions while reading from DB
func (a *DisputedTransactions) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the DisputedTransactions in string format in DB
func (a *DisputedTransactions) Value() (driver.Value, error) {
	return protojson.Marshal(a)
}

// Marshaler interface implementation for DisputedTransactions
func (a *DisputedTransactions) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for DisputedTransactions
func (a *DisputedTransactions) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing ReporterContactDetails while reading from DB
func (a *ReporterContactDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the ReporterContactDetails in string format in DB
func (a *ReporterContactDetails) Value() (driver.Value, error) {
	return protojson.Marshal(a)
}

// Marshaler interface implementation for ReporterContactDetails
func (a *ReporterContactDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for ReporterContactDetails
func (a *ReporterContactDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing AdditionalDetails while reading from DB
func (a *AdditionalDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the AdditionalDetails in string format in DB
func (a *AdditionalDetails) Value() (driver.Value, error) {
	return protojson.Marshal(a)
}

// Marshaler interface implementation for AdditionalDetails
func (a *AdditionalDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for AdditionalDetails
func (a *AdditionalDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing Dates while reading from DB
func (a *Dates) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the Dates in string format in DB
func (a *Dates) Value() (driver.Value, error) {
	return protojson.Marshal(a)
}

// Marshaler interface implementation for Dates
func (a *Dates) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for Dates
func (a *Dates) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
