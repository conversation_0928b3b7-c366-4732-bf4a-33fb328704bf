//go:generate gen_sql -types=Lien,DisputedTransactions,ReporterContactDetails,AdditionalDetails,Dates,ActionType,ProcessingStatus

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/lea/unified_lea_complaint.proto

package lea

import (
	accounts "github.com/epifi/gamma/api/accounts"
	enums "github.com/epifi/gamma/api/risk/enums"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UnifiedLeaComplaintFieldMask int32

const (
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_UNSPECIFIED                UnifiedLeaComplaintFieldMask = 0
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_COMPLAINT_ID               UnifiedLeaComplaintFieldMask = 1
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_ACCOUNT_TYPE               UnifiedLeaComplaintFieldMask = 2
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_ACTION_TYPE                UnifiedLeaComplaintFieldMask = 3
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_ACCOUNT_OPERATIONAL_STATUS UnifiedLeaComplaintFieldMask = 4
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_LIEN                       UnifiedLeaComplaintFieldMask = 5
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_REASON                     UnifiedLeaComplaintFieldMask = 6
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_REMARK                     UnifiedLeaComplaintFieldMask = 7
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_FREEZE_CODE                UnifiedLeaComplaintFieldMask = 8
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_LAYER_NUMBER               UnifiedLeaComplaintFieldMask = 9
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_DISPUTED_TRANSACTIONS      UnifiedLeaComplaintFieldMask = 10
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_PROCESSING_STATUS          UnifiedLeaComplaintFieldMask = 11
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_LEA_ORIGIN                 UnifiedLeaComplaintFieldMask = 12
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_REPORTER_CONTACT_DETAILS   UnifiedLeaComplaintFieldMask = 13
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_ORIGIN_GEOGRAPHICAL_STATE  UnifiedLeaComplaintFieldMask = 14
	UnifiedLeaComplaintFieldMask_UNIFIED_LEA_COMPLAINT_FIELD_MASK_DATES                      UnifiedLeaComplaintFieldMask = 15
)

// Enum value maps for UnifiedLeaComplaintFieldMask.
var (
	UnifiedLeaComplaintFieldMask_name = map[int32]string{
		0:  "UNIFIED_LEA_COMPLAINT_FIELD_MASK_UNSPECIFIED",
		1:  "UNIFIED_LEA_COMPLAINT_FIELD_MASK_COMPLAINT_ID",
		2:  "UNIFIED_LEA_COMPLAINT_FIELD_MASK_ACCOUNT_TYPE",
		3:  "UNIFIED_LEA_COMPLAINT_FIELD_MASK_ACTION_TYPE",
		4:  "UNIFIED_LEA_COMPLAINT_FIELD_MASK_ACCOUNT_OPERATIONAL_STATUS",
		5:  "UNIFIED_LEA_COMPLAINT_FIELD_MASK_LIEN",
		6:  "UNIFIED_LEA_COMPLAINT_FIELD_MASK_REASON",
		7:  "UNIFIED_LEA_COMPLAINT_FIELD_MASK_REMARK",
		8:  "UNIFIED_LEA_COMPLAINT_FIELD_MASK_FREEZE_CODE",
		9:  "UNIFIED_LEA_COMPLAINT_FIELD_MASK_LAYER_NUMBER",
		10: "UNIFIED_LEA_COMPLAINT_FIELD_MASK_DISPUTED_TRANSACTIONS",
		11: "UNIFIED_LEA_COMPLAINT_FIELD_MASK_PROCESSING_STATUS",
		12: "UNIFIED_LEA_COMPLAINT_FIELD_MASK_LEA_ORIGIN",
		13: "UNIFIED_LEA_COMPLAINT_FIELD_MASK_REPORTER_CONTACT_DETAILS",
		14: "UNIFIED_LEA_COMPLAINT_FIELD_MASK_ORIGIN_GEOGRAPHICAL_STATE",
		15: "UNIFIED_LEA_COMPLAINT_FIELD_MASK_DATES",
	}
	UnifiedLeaComplaintFieldMask_value = map[string]int32{
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_UNSPECIFIED":                0,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_COMPLAINT_ID":               1,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_ACCOUNT_TYPE":               2,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_ACTION_TYPE":                3,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_ACCOUNT_OPERATIONAL_STATUS": 4,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_LIEN":                       5,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_REASON":                     6,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_REMARK":                     7,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_FREEZE_CODE":                8,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_LAYER_NUMBER":               9,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_DISPUTED_TRANSACTIONS":      10,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_PROCESSING_STATUS":          11,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_LEA_ORIGIN":                 12,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_REPORTER_CONTACT_DETAILS":   13,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_ORIGIN_GEOGRAPHICAL_STATE":  14,
		"UNIFIED_LEA_COMPLAINT_FIELD_MASK_DATES":                      15,
	}
)

func (x UnifiedLeaComplaintFieldMask) Enum() *UnifiedLeaComplaintFieldMask {
	p := new(UnifiedLeaComplaintFieldMask)
	*p = x
	return p
}

func (x UnifiedLeaComplaintFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UnifiedLeaComplaintFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_lea_unified_lea_complaint_proto_enumTypes[0].Descriptor()
}

func (UnifiedLeaComplaintFieldMask) Type() protoreflect.EnumType {
	return &file_api_risk_lea_unified_lea_complaint_proto_enumTypes[0]
}

func (x UnifiedLeaComplaintFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UnifiedLeaComplaintFieldMask.Descriptor instead.
func (UnifiedLeaComplaintFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_lea_unified_lea_complaint_proto_rawDescGZIP(), []int{0}
}

type ActionType int32

const (
	ActionType_ACTION_TYPE_UNSPECIFIED ActionType = 0
	// this denotes that the operational status of the account has been changed
	ActionType_ACTION_TYPE_UPDATE_OPER_STATUS ActionType = 1
	// this denotes that a lien has been applied on the account
	// i.e an amount has been blocked in the user's account
	ActionType_ACTION_TYPE_LIEN ActionType = 2
	// this denotes that we do not expect any action to be taken
	ActionType_ACTION_TYPE_NO_ACTION ActionType = 3
)

// Enum value maps for ActionType.
var (
	ActionType_name = map[int32]string{
		0: "ACTION_TYPE_UNSPECIFIED",
		1: "ACTION_TYPE_UPDATE_OPER_STATUS",
		2: "ACTION_TYPE_LIEN",
		3: "ACTION_TYPE_NO_ACTION",
	}
	ActionType_value = map[string]int32{
		"ACTION_TYPE_UNSPECIFIED":        0,
		"ACTION_TYPE_UPDATE_OPER_STATUS": 1,
		"ACTION_TYPE_LIEN":               2,
		"ACTION_TYPE_NO_ACTION":          3,
	}
)

func (x ActionType) Enum() *ActionType {
	p := new(ActionType)
	*p = x
	return p
}

func (x ActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_lea_unified_lea_complaint_proto_enumTypes[1].Descriptor()
}

func (ActionType) Type() protoreflect.EnumType {
	return &file_api_risk_lea_unified_lea_complaint_proto_enumTypes[1]
}

func (x ActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActionType.Descriptor instead.
func (ActionType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_lea_unified_lea_complaint_proto_rawDescGZIP(), []int{1}
}

type ProcessingStatus int32

const (
	ProcessingStatus_PROCESSING_STATUS_UNSPECIFIED                  ProcessingStatus = 0
	ProcessingStatus_PROCESSING_STATUS_RAW_DETAILS_PRESENT          ProcessingStatus = 1
	ProcessingStatus_PROCESSING_STATUS_FINAL_ACTION_DETAILS_PRESENT ProcessingStatus = 2
)

// Enum value maps for ProcessingStatus.
var (
	ProcessingStatus_name = map[int32]string{
		0: "PROCESSING_STATUS_UNSPECIFIED",
		1: "PROCESSING_STATUS_RAW_DETAILS_PRESENT",
		2: "PROCESSING_STATUS_FINAL_ACTION_DETAILS_PRESENT",
	}
	ProcessingStatus_value = map[string]int32{
		"PROCESSING_STATUS_UNSPECIFIED":                  0,
		"PROCESSING_STATUS_RAW_DETAILS_PRESENT":          1,
		"PROCESSING_STATUS_FINAL_ACTION_DETAILS_PRESENT": 2,
	}
)

func (x ProcessingStatus) Enum() *ProcessingStatus {
	p := new(ProcessingStatus)
	*p = x
	return p
}

func (x ProcessingStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessingStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_lea_unified_lea_complaint_proto_enumTypes[2].Descriptor()
}

func (ProcessingStatus) Type() protoreflect.EnumType {
	return &file_api_risk_lea_unified_lea_complaint_proto_enumTypes[2]
}

func (x ProcessingStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessingStatus.Descriptor instead.
func (ProcessingStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_lea_unified_lea_complaint_proto_rawDescGZIP(), []int{2}
}

// contains details of LEA complaint we received from bank
type UnifiedLeaComplaint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Police/NCCRP complaint number for identifying this particular complaint
	// Can be null in cases where we don't get this identifier, and can be backfilled
	ComplaintId string `protobuf:"bytes,2,opt,name=complaint_id,json=complaintId,proto3" json:"complaint_id,omitempty"`
	// Identifier for the actor associated with the complaint
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Identifier for the account related to the complaint
	AccountNumber string `protobuf:"bytes,4,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// Account type for which there was a complaint
	AccountType accounts.Type `protobuf:"varint,5,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
	// Dates related to the complaint like AuditDate, ComplaintDate
	Dates *Dates `protobuf:"bytes,6,opt,name=dates,proto3" json:"dates,omitempty"`
	// Type of action taken regarding the complaint
	ActionType ActionType `protobuf:"varint,7,opt,name=action_type,json=actionType,proto3,enum=risk.ActionType" json:"action_type,omitempty"`
	// Status of the account we got in the LEA complaint
	OperationalStatus enums.AccountFreezeStatus `protobuf:"varint,8,opt,name=operational_status,json=operationalStatus,proto3,enum=enums.AccountFreezeStatus" json:"operational_status,omitempty"`
	// Details of any lien placed on the account
	Lien *Lien `protobuf:"bytes,9,opt,name=lien,proto3" json:"lien,omitempty"`
	// Reasons provided for the complaint
	Reason string `protobuf:"bytes,10,opt,name=reason,proto3" json:"reason,omitempty"`
	// Additional remarks about the complaint, can contain transaction ref, date etc
	Remark string `protobuf:"bytes,11,opt,name=remark,proto3" json:"remark,omitempty"`
	// Code indicating the type of freeze applied to account
	FreezeCode string `protobuf:"bytes,12,opt,name=freeze_code,json=freezeCode,proto3" json:"freeze_code,omitempty"`
	// Layer number represents an account's position in chain of fund transfers
	LayerNumber *typesv2.NullInt32 `protobuf:"bytes,13,opt,name=layer_number,json=layerNumber,proto3" json:"layer_number,omitempty"`
	// Details of any disputed transactions
	DisputedTransactions *DisputedTransactions `protobuf:"bytes,14,opt,name=disputed_transactions,json=disputedTransactions,proto3" json:"disputed_transactions,omitempty"`
	// Current status of the complaint, eg - only raw detials are present for the complaint
	// or the final action has been taken by the bank
	ProcessingStatus ProcessingStatus `protobuf:"varint,15,opt,name=processing_status,json=processingStatus,proto3,enum=risk.ProcessingStatus" json:"processing_status,omitempty"`
	// Origin of the complaint
	LeaOrigin enums.LEAReportOrigin `protobuf:"varint,16,opt,name=lea_origin,json=leaOrigin,proto3,enum=enums.LEAReportOrigin" json:"lea_origin,omitempty"`
	// Contact details of the institution where the complaint was reported
	ReporterContactDetails *ReporterContactDetails `protobuf:"bytes,17,opt,name=reporter_contact_details,json=reporterContactDetails,proto3" json:"reporter_contact_details,omitempty"`
	// Geographical state where the complaint reported
	OriginGeographicalState string `protobuf:"bytes,18,opt,name=origin_geographical_state,json=originGeographicalState,proto3" json:"origin_geographical_state,omitempty"`
	// Any additional details regarding the complaint
	AdditionalDetails *AdditionalDetails     `protobuf:"bytes,19,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAtUnix     *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=deleted_at_unix,json=deletedAtUnix,proto3" json:"deleted_at_unix,omitempty"`
}

func (x *UnifiedLeaComplaint) Reset() {
	*x = UnifiedLeaComplaint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnifiedLeaComplaint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnifiedLeaComplaint) ProtoMessage() {}

func (x *UnifiedLeaComplaint) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnifiedLeaComplaint.ProtoReflect.Descriptor instead.
func (*UnifiedLeaComplaint) Descriptor() ([]byte, []int) {
	return file_api_risk_lea_unified_lea_complaint_proto_rawDescGZIP(), []int{0}
}

func (x *UnifiedLeaComplaint) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UnifiedLeaComplaint) GetComplaintId() string {
	if x != nil {
		return x.ComplaintId
	}
	return ""
}

func (x *UnifiedLeaComplaint) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UnifiedLeaComplaint) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *UnifiedLeaComplaint) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

func (x *UnifiedLeaComplaint) GetDates() *Dates {
	if x != nil {
		return x.Dates
	}
	return nil
}

func (x *UnifiedLeaComplaint) GetActionType() ActionType {
	if x != nil {
		return x.ActionType
	}
	return ActionType_ACTION_TYPE_UNSPECIFIED
}

func (x *UnifiedLeaComplaint) GetOperationalStatus() enums.AccountFreezeStatus {
	if x != nil {
		return x.OperationalStatus
	}
	return enums.AccountFreezeStatus(0)
}

func (x *UnifiedLeaComplaint) GetLien() *Lien {
	if x != nil {
		return x.Lien
	}
	return nil
}

func (x *UnifiedLeaComplaint) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *UnifiedLeaComplaint) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *UnifiedLeaComplaint) GetFreezeCode() string {
	if x != nil {
		return x.FreezeCode
	}
	return ""
}

func (x *UnifiedLeaComplaint) GetLayerNumber() *typesv2.NullInt32 {
	if x != nil {
		return x.LayerNumber
	}
	return nil
}

func (x *UnifiedLeaComplaint) GetDisputedTransactions() *DisputedTransactions {
	if x != nil {
		return x.DisputedTransactions
	}
	return nil
}

func (x *UnifiedLeaComplaint) GetProcessingStatus() ProcessingStatus {
	if x != nil {
		return x.ProcessingStatus
	}
	return ProcessingStatus_PROCESSING_STATUS_UNSPECIFIED
}

func (x *UnifiedLeaComplaint) GetLeaOrigin() enums.LEAReportOrigin {
	if x != nil {
		return x.LeaOrigin
	}
	return enums.LEAReportOrigin(0)
}

func (x *UnifiedLeaComplaint) GetReporterContactDetails() *ReporterContactDetails {
	if x != nil {
		return x.ReporterContactDetails
	}
	return nil
}

func (x *UnifiedLeaComplaint) GetOriginGeographicalState() string {
	if x != nil {
		return x.OriginGeographicalState
	}
	return ""
}

func (x *UnifiedLeaComplaint) GetAdditionalDetails() *AdditionalDetails {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

func (x *UnifiedLeaComplaint) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *UnifiedLeaComplaint) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *UnifiedLeaComplaint) GetDeletedAtUnix() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAtUnix
	}
	return nil
}

type Lien struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnRef    string                 `protobuf:"bytes,1,opt,name=txn_ref,json=txnRef,proto3" json:"txn_ref,omitempty"`
	Amount    float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	RawData   string                 `protobuf:"bytes,4,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
}

func (x *Lien) Reset() {
	*x = Lien{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lien) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lien) ProtoMessage() {}

func (x *Lien) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lien.ProtoReflect.Descriptor instead.
func (*Lien) Descriptor() ([]byte, []int) {
	return file_api_risk_lea_unified_lea_complaint_proto_rawDescGZIP(), []int{1}
}

func (x *Lien) GetTxnRef() string {
	if x != nil {
		return x.TxnRef
	}
	return ""
}

func (x *Lien) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Lien) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *Lien) GetRawData() string {
	if x != nil {
		return x.RawData
	}
	return ""
}

type DisputedTransactions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Transactions []*DisputedTransactions_Transaction `protobuf:"bytes,1,rep,name=transactions,proto3" json:"transactions,omitempty"`
}

func (x *DisputedTransactions) Reset() {
	*x = DisputedTransactions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisputedTransactions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisputedTransactions) ProtoMessage() {}

func (x *DisputedTransactions) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisputedTransactions.ProtoReflect.Descriptor instead.
func (*DisputedTransactions) Descriptor() ([]byte, []int) {
	return file_api_risk_lea_unified_lea_complaint_proto_rawDescGZIP(), []int{2}
}

func (x *DisputedTransactions) GetTransactions() []*DisputedTransactions_Transaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

type ReporterContactDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodalCyberCellOfficerDetails *ReporterContactDetails_Details `protobuf:"bytes,1,opt,name=nodal_cyber_cell_officer_details,json=nodalCyberCellOfficerDetails,proto3" json:"nodal_cyber_cell_officer_details,omitempty"`
	GrievanceOfficerDetails      *ReporterContactDetails_Details `protobuf:"bytes,2,opt,name=grievance_officer_details,json=grievanceOfficerDetails,proto3" json:"grievance_officer_details,omitempty"`
}

func (x *ReporterContactDetails) Reset() {
	*x = ReporterContactDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReporterContactDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReporterContactDetails) ProtoMessage() {}

func (x *ReporterContactDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReporterContactDetails.ProtoReflect.Descriptor instead.
func (*ReporterContactDetails) Descriptor() ([]byte, []int) {
	return file_api_risk_lea_unified_lea_complaint_proto_rawDescGZIP(), []int{3}
}

func (x *ReporterContactDetails) GetNodalCyberCellOfficerDetails() *ReporterContactDetails_Details {
	if x != nil {
		return x.NodalCyberCellOfficerDetails
	}
	return nil
}

func (x *ReporterContactDetails) GetGrievanceOfficerDetails() *ReporterContactDetails_Details {
	if x != nil {
		return x.GrievanceOfficerDetails
	}
	return nil
}

type AdditionalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AdditionalDetails) Reset() {
	*x = AdditionalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdditionalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalDetails) ProtoMessage() {}

func (x *AdditionalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalDetails.ProtoReflect.Descriptor instead.
func (*AdditionalDetails) Descriptor() ([]byte, []int) {
	return file_api_risk_lea_unified_lea_complaint_proto_rawDescGZIP(), []int{4}
}

type Dates struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Date when we got the file from the bank
	AuditDate *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=audit_date,json=auditDate,proto3" json:"audit_date,omitempty"`
	// Date when the account was closed
	AccountClosureDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=account_closure_date,json=accountClosureDate,proto3" json:"account_closure_date,omitempty"`
	// Date when the complaint was filed
	ComplaintDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=complaint_date,json=complaintDate,proto3" json:"complaint_date,omitempty"`
	// Date when we received file from bank
	DetailsReceivedDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=details_received_date,json=detailsReceivedDate,proto3" json:"details_received_date,omitempty"`
}

func (x *Dates) Reset() {
	*x = Dates{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Dates) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dates) ProtoMessage() {}

func (x *Dates) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dates.ProtoReflect.Descriptor instead.
func (*Dates) Descriptor() ([]byte, []int) {
	return file_api_risk_lea_unified_lea_complaint_proto_rawDescGZIP(), []int{5}
}

func (x *Dates) GetAuditDate() *timestamppb.Timestamp {
	if x != nil {
		return x.AuditDate
	}
	return nil
}

func (x *Dates) GetAccountClosureDate() *timestamppb.Timestamp {
	if x != nil {
		return x.AccountClosureDate
	}
	return nil
}

func (x *Dates) GetComplaintDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ComplaintDate
	}
	return nil
}

func (x *Dates) GetDetailsReceivedDate() *timestamppb.Timestamp {
	if x != nil {
		return x.DetailsReceivedDate
	}
	return nil
}

type DisputedTransactions_Transaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnRef         string                 `protobuf:"bytes,1,opt,name=txn_ref,json=txnRef,proto3" json:"txn_ref,omitempty"`
	Timestamp      *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Amount         float64                `protobuf:"fixed64,3,opt,name=amount,proto3" json:"amount,omitempty"`
	DisputedAmount float64                `protobuf:"fixed64,4,opt,name=disputed_amount,json=disputedAmount,proto3" json:"disputed_amount,omitempty"`
}

func (x *DisputedTransactions_Transaction) Reset() {
	*x = DisputedTransactions_Transaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisputedTransactions_Transaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisputedTransactions_Transaction) ProtoMessage() {}

func (x *DisputedTransactions_Transaction) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisputedTransactions_Transaction.ProtoReflect.Descriptor instead.
func (*DisputedTransactions_Transaction) Descriptor() ([]byte, []int) {
	return file_api_risk_lea_unified_lea_complaint_proto_rawDescGZIP(), []int{2, 0}
}

func (x *DisputedTransactions_Transaction) GetTxnRef() string {
	if x != nil {
		return x.TxnRef
	}
	return ""
}

func (x *DisputedTransactions_Transaction) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *DisputedTransactions_Transaction) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *DisputedTransactions_Transaction) GetDisputedAmount() float64 {
	if x != nil {
		return x.DisputedAmount
	}
	return 0
}

type ReporterContactDetails_Details struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	PhoneNumber string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Email       string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	RawData     string `protobuf:"bytes,4,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
}

func (x *ReporterContactDetails_Details) Reset() {
	*x = ReporterContactDetails_Details{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReporterContactDetails_Details) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReporterContactDetails_Details) ProtoMessage() {}

func (x *ReporterContactDetails_Details) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lea_unified_lea_complaint_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReporterContactDetails_Details.ProtoReflect.Descriptor instead.
func (*ReporterContactDetails_Details) Descriptor() ([]byte, []int) {
	return file_api_risk_lea_unified_lea_complaint_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ReporterContactDetails_Details) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ReporterContactDetails_Details) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *ReporterContactDetails_Details) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ReporterContactDetails_Details) GetRawData() string {
	if x != nil {
		return x.RawData
	}
	return ""
}

var File_api_risk_lea_unified_lea_complaint_proto protoreflect.FileDescriptor

var file_api_risk_lea_unified_lea_complaint_proto_rawDesc = []byte{
	0x0a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x65, 0x61, 0x2f, 0x75,
	0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x72, 0x69, 0x73, 0x6b,
	0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6e, 0x75, 0x6c, 0x6c, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xed, 0x08, 0x0a, 0x13,
	0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x64,
	0x61, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x73, 0x52, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x31,
	0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x49, 0x0a, 0x12, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x72, 0x65,
	0x65, 0x7a, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x04,
	0x6c, 0x69, 0x65, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b,
	0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x39, 0x0a,
	0x0c, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x4e, 0x75, 0x6c, 0x6c, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x0b, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x4f, 0x0a, 0x15, 0x64, 0x69, 0x73, 0x70,
	0x75, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x14, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x43, 0x0a, 0x11, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x35,
	0x0a, 0x0a, 0x6c, 0x65, 0x61, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4c, 0x45, 0x41, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x52, 0x09, 0x6c, 0x65, 0x61, 0x4f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x12, 0x56, 0x0a, 0x18, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x16, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3a, 0x0a,
	0x19, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x67, 0x65, 0x6f, 0x67, 0x72, 0x61, 0x70, 0x68,
	0x69, 0x63, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x17, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x47, 0x65, 0x6f, 0x67, 0x72, 0x61, 0x70, 0x68,
	0x69, 0x63, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x46, 0x0a, 0x12, 0x61, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x42, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x78, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x55, 0x6e, 0x69, 0x78, 0x22, 0x8c, 0x01, 0x0a, 0x04,
	0x4c, 0x69, 0x65, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x6e, 0x52, 0x65, 0x66, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x22, 0x86, 0x02, 0x0a, 0x14, 0x44,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x4a, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a,
	0xa1, 0x01, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x78, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x78, 0x6e, 0x52, 0x65, 0x66, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x69,
	0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0xdb, 0x02, 0x0a, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x6c,
	0x0a, 0x20, 0x6e, 0x6f, 0x64, 0x61, 0x6c, 0x5f, 0x63, 0x79, 0x62, 0x65, 0x72, 0x5f, 0x63, 0x65,
	0x6c, 0x6c, 0x5f, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x1c,
	0x6e, 0x6f, 0x64, 0x61, 0x6c, 0x43, 0x79, 0x62, 0x65, 0x72, 0x43, 0x65, 0x6c, 0x6c, 0x4f, 0x66,
	0x66, 0x69, 0x63, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x60, 0x0a, 0x19,
	0x67, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x65,
	0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x17, 0x67, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6e, 0x63, 0x65,
	0x4f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x71,
	0x0a, 0x07, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74,
	0x61, 0x22, 0x13, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xa3, 0x02, 0x0a, 0x05, 0x44, 0x61, 0x74, 0x65, 0x73,
	0x12, 0x39, 0x0a, 0x0a, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x61, 0x75, 0x64, 0x69, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x4c, 0x0a, 0x14, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x4e, 0x0a, 0x15,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x13, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x2a, 0xe3, 0x06, 0x0a,
	0x1c, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x30, 0x0a,
	0x2c, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x31, 0x0a, 0x2d, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43,
	0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x49, 0x44,
	0x10, 0x01, 0x12, 0x31, 0x0a, 0x2d, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4c, 0x45,
	0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x10, 0x02, 0x12, 0x30, 0x0a, 0x2c, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x03, 0x12, 0x3f, 0x0a, 0x3b, 0x55, 0x4e, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x04, 0x12, 0x29, 0x0a, 0x25, 0x55, 0x4e, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e,
	0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x49, 0x45,
	0x4e, 0x10, 0x05, 0x12, 0x2b, 0x0a, 0x27, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4c,
	0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0x06,
	0x12, 0x2b, 0x0a, 0x27, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f,
	0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x4d, 0x41, 0x52, 0x4b, 0x10, 0x07, 0x12, 0x30, 0x0a,
	0x2c, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x08, 0x12,
	0x31, 0x0a, 0x2d, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43,
	0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52,
	0x10, 0x09, 0x12, 0x3a, 0x0a, 0x36, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4c, 0x45,
	0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x44, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x0a, 0x12, 0x36,
	0x0a, 0x32, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f,
	0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0x0b, 0x12, 0x2f, 0x0a, 0x2b, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x4f,
	0x52, 0x49, 0x47, 0x49, 0x4e, 0x10, 0x0c, 0x12, 0x3d, 0x0a, 0x39, 0x55, 0x4e, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x50, 0x4f,
	0x52, 0x54, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x10, 0x0d, 0x12, 0x3e, 0x0a, 0x3a, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x52, 0x49, 0x47, 0x49,
	0x4e, 0x5f, 0x47, 0x45, 0x4f, 0x47, 0x52, 0x41, 0x50, 0x48, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x10, 0x0e, 0x12, 0x2a, 0x0a, 0x26, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x53,
	0x10, 0x0f, 0x2a, 0x7e, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a,
	0x1e, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10,
	0x01, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4c, 0x49, 0x45, 0x4e, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x03, 0x2a, 0x94, 0x01, 0x0a, 0x10, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x52, 0x4f, 0x43, 0x45,
	0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x50, 0x52,
	0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x52, 0x41, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x53,
	0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x32, 0x0a, 0x2e, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53,
	0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4c,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f,
	0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x42, 0x4a, 0x0a, 0x23, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6c, 0x65, 0x61,
	0x5a, 0x23, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73,
	0x6b, 0x2f, 0x6c, 0x65, 0x61, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_lea_unified_lea_complaint_proto_rawDescOnce sync.Once
	file_api_risk_lea_unified_lea_complaint_proto_rawDescData = file_api_risk_lea_unified_lea_complaint_proto_rawDesc
)

func file_api_risk_lea_unified_lea_complaint_proto_rawDescGZIP() []byte {
	file_api_risk_lea_unified_lea_complaint_proto_rawDescOnce.Do(func() {
		file_api_risk_lea_unified_lea_complaint_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_lea_unified_lea_complaint_proto_rawDescData)
	})
	return file_api_risk_lea_unified_lea_complaint_proto_rawDescData
}

var file_api_risk_lea_unified_lea_complaint_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_risk_lea_unified_lea_complaint_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_risk_lea_unified_lea_complaint_proto_goTypes = []interface{}{
	(UnifiedLeaComplaintFieldMask)(0),        // 0: risk.UnifiedLeaComplaintFieldMask
	(ActionType)(0),                          // 1: risk.ActionType
	(ProcessingStatus)(0),                    // 2: risk.ProcessingStatus
	(*UnifiedLeaComplaint)(nil),              // 3: risk.UnifiedLeaComplaint
	(*Lien)(nil),                             // 4: risk.Lien
	(*DisputedTransactions)(nil),             // 5: risk.DisputedTransactions
	(*ReporterContactDetails)(nil),           // 6: risk.ReporterContactDetails
	(*AdditionalDetails)(nil),                // 7: risk.AdditionalDetails
	(*Dates)(nil),                            // 8: risk.Dates
	(*DisputedTransactions_Transaction)(nil), // 9: risk.DisputedTransactions.Transaction
	(*ReporterContactDetails_Details)(nil),   // 10: risk.ReporterContactDetails.Details
	(accounts.Type)(0),                       // 11: accounts.Type
	(enums.AccountFreezeStatus)(0),           // 12: enums.AccountFreezeStatus
	(*typesv2.NullInt32)(nil),                // 13: api.typesv2.NullInt32
	(enums.LEAReportOrigin)(0),               // 14: enums.LEAReportOrigin
	(*timestamppb.Timestamp)(nil),            // 15: google.protobuf.Timestamp
}
var file_api_risk_lea_unified_lea_complaint_proto_depIdxs = []int32{
	11, // 0: risk.UnifiedLeaComplaint.account_type:type_name -> accounts.Type
	8,  // 1: risk.UnifiedLeaComplaint.dates:type_name -> risk.Dates
	1,  // 2: risk.UnifiedLeaComplaint.action_type:type_name -> risk.ActionType
	12, // 3: risk.UnifiedLeaComplaint.operational_status:type_name -> enums.AccountFreezeStatus
	4,  // 4: risk.UnifiedLeaComplaint.lien:type_name -> risk.Lien
	13, // 5: risk.UnifiedLeaComplaint.layer_number:type_name -> api.typesv2.NullInt32
	5,  // 6: risk.UnifiedLeaComplaint.disputed_transactions:type_name -> risk.DisputedTransactions
	2,  // 7: risk.UnifiedLeaComplaint.processing_status:type_name -> risk.ProcessingStatus
	14, // 8: risk.UnifiedLeaComplaint.lea_origin:type_name -> enums.LEAReportOrigin
	6,  // 9: risk.UnifiedLeaComplaint.reporter_contact_details:type_name -> risk.ReporterContactDetails
	7,  // 10: risk.UnifiedLeaComplaint.additional_details:type_name -> risk.AdditionalDetails
	15, // 11: risk.UnifiedLeaComplaint.created_at:type_name -> google.protobuf.Timestamp
	15, // 12: risk.UnifiedLeaComplaint.updated_at:type_name -> google.protobuf.Timestamp
	15, // 13: risk.UnifiedLeaComplaint.deleted_at_unix:type_name -> google.protobuf.Timestamp
	15, // 14: risk.Lien.timestamp:type_name -> google.protobuf.Timestamp
	9,  // 15: risk.DisputedTransactions.transactions:type_name -> risk.DisputedTransactions.Transaction
	10, // 16: risk.ReporterContactDetails.nodal_cyber_cell_officer_details:type_name -> risk.ReporterContactDetails.Details
	10, // 17: risk.ReporterContactDetails.grievance_officer_details:type_name -> risk.ReporterContactDetails.Details
	15, // 18: risk.Dates.audit_date:type_name -> google.protobuf.Timestamp
	15, // 19: risk.Dates.account_closure_date:type_name -> google.protobuf.Timestamp
	15, // 20: risk.Dates.complaint_date:type_name -> google.protobuf.Timestamp
	15, // 21: risk.Dates.details_received_date:type_name -> google.protobuf.Timestamp
	15, // 22: risk.DisputedTransactions.Transaction.timestamp:type_name -> google.protobuf.Timestamp
	23, // [23:23] is the sub-list for method output_type
	23, // [23:23] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_api_risk_lea_unified_lea_complaint_proto_init() }
func file_api_risk_lea_unified_lea_complaint_proto_init() {
	if File_api_risk_lea_unified_lea_complaint_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_lea_unified_lea_complaint_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnifiedLeaComplaint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lea_unified_lea_complaint_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lien); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lea_unified_lea_complaint_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisputedTransactions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lea_unified_lea_complaint_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReporterContactDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lea_unified_lea_complaint_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdditionalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lea_unified_lea_complaint_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Dates); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lea_unified_lea_complaint_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisputedTransactions_Transaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lea_unified_lea_complaint_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReporterContactDetails_Details); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_lea_unified_lea_complaint_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_lea_unified_lea_complaint_proto_goTypes,
		DependencyIndexes: file_api_risk_lea_unified_lea_complaint_proto_depIdxs,
		EnumInfos:         file_api_risk_lea_unified_lea_complaint_proto_enumTypes,
		MessageInfos:      file_api_risk_lea_unified_lea_complaint_proto_msgTypes,
	}.Build()
	File_api_risk_lea_unified_lea_complaint_proto = out.File
	file_api_risk_lea_unified_lea_complaint_proto_rawDesc = nil
	file_api_risk_lea_unified_lea_complaint_proto_goTypes = nil
	file_api_risk_lea_unified_lea_complaint_proto_depIdxs = nil
}
