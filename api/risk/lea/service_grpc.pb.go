// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/risk/lea/service.proto

package lea

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Lea_UpsertUnifiedLeaComplaint_FullMethodName = "/risk.Lea/UpsertUnifiedLeaComplaint"
	Lea_GetUnifiedLeaComplaint_FullMethodName    = "/risk.Lea/GetUnifiedLeaComplaint"
)

// LeaClient is the client API for Lea service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LeaClient interface {
	// Creates or updates unified lea complaint entry
	// Account number and processing status are mandatory parameters
	// returns status
	// INVALID ARGUMENT - if mandatory params are missing, or wrong combination of params are passed wrt processing status
	// ALREADY EXISTS - if an entry already exists for the given account number and complaint id with the same processing status
	// INTERNAL SERVER ERROR - if there is any failure in processing
	// OK - if the processing is done successfully
	UpsertUnifiedLeaComplaint(ctx context.Context, in *UpsertUnifiedLeaComplaintRequest, opts ...grpc.CallOption) (*UpsertUnifiedLeaComplaintResponse, error)
	// Fetches unified lea complaint entry
	// Supports currently only actor id as identifier
	// returns status and unified lea complaint entry
	GetUnifiedLeaComplaint(ctx context.Context, in *GetUnifiedLeaComplaintRequest, opts ...grpc.CallOption) (*GetUnifiedLeaComplaintResponse, error)
}

type leaClient struct {
	cc grpc.ClientConnInterface
}

func NewLeaClient(cc grpc.ClientConnInterface) LeaClient {
	return &leaClient{cc}
}

func (c *leaClient) UpsertUnifiedLeaComplaint(ctx context.Context, in *UpsertUnifiedLeaComplaintRequest, opts ...grpc.CallOption) (*UpsertUnifiedLeaComplaintResponse, error) {
	out := new(UpsertUnifiedLeaComplaintResponse)
	err := c.cc.Invoke(ctx, Lea_UpsertUnifiedLeaComplaint_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *leaClient) GetUnifiedLeaComplaint(ctx context.Context, in *GetUnifiedLeaComplaintRequest, opts ...grpc.CallOption) (*GetUnifiedLeaComplaintResponse, error) {
	out := new(GetUnifiedLeaComplaintResponse)
	err := c.cc.Invoke(ctx, Lea_GetUnifiedLeaComplaint_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LeaServer is the server API for Lea service.
// All implementations should embed UnimplementedLeaServer
// for forward compatibility
type LeaServer interface {
	// Creates or updates unified lea complaint entry
	// Account number and processing status are mandatory parameters
	// returns status
	// INVALID ARGUMENT - if mandatory params are missing, or wrong combination of params are passed wrt processing status
	// ALREADY EXISTS - if an entry already exists for the given account number and complaint id with the same processing status
	// INTERNAL SERVER ERROR - if there is any failure in processing
	// OK - if the processing is done successfully
	UpsertUnifiedLeaComplaint(context.Context, *UpsertUnifiedLeaComplaintRequest) (*UpsertUnifiedLeaComplaintResponse, error)
	// Fetches unified lea complaint entry
	// Supports currently only actor id as identifier
	// returns status and unified lea complaint entry
	GetUnifiedLeaComplaint(context.Context, *GetUnifiedLeaComplaintRequest) (*GetUnifiedLeaComplaintResponse, error)
}

// UnimplementedLeaServer should be embedded to have forward compatible implementations.
type UnimplementedLeaServer struct {
}

func (UnimplementedLeaServer) UpsertUnifiedLeaComplaint(context.Context, *UpsertUnifiedLeaComplaintRequest) (*UpsertUnifiedLeaComplaintResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertUnifiedLeaComplaint not implemented")
}
func (UnimplementedLeaServer) GetUnifiedLeaComplaint(context.Context, *GetUnifiedLeaComplaintRequest) (*GetUnifiedLeaComplaintResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUnifiedLeaComplaint not implemented")
}

// UnsafeLeaServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LeaServer will
// result in compilation errors.
type UnsafeLeaServer interface {
	mustEmbedUnimplementedLeaServer()
}

func RegisterLeaServer(s grpc.ServiceRegistrar, srv LeaServer) {
	s.RegisterService(&Lea_ServiceDesc, srv)
}

func _Lea_UpsertUnifiedLeaComplaint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertUnifiedLeaComplaintRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LeaServer).UpsertUnifiedLeaComplaint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lea_UpsertUnifiedLeaComplaint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LeaServer).UpsertUnifiedLeaComplaint(ctx, req.(*UpsertUnifiedLeaComplaintRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lea_GetUnifiedLeaComplaint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUnifiedLeaComplaintRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LeaServer).GetUnifiedLeaComplaint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lea_GetUnifiedLeaComplaint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LeaServer).GetUnifiedLeaComplaint(ctx, req.(*GetUnifiedLeaComplaintRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Lea_ServiceDesc is the grpc.ServiceDesc for Lea service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Lea_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "risk.Lea",
	HandlerType: (*LeaServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpsertUnifiedLeaComplaint",
			Handler:    _Lea_UpsertUnifiedLeaComplaint_Handler,
		},
		{
			MethodName: "GetUnifiedLeaComplaint",
			Handler:    _Lea_GetUnifiedLeaComplaint_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/risk/lea/service.proto",
}
