// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/lea/service.proto

package lea

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/risk/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.AccountFreezeStatus(0)
)

// Validate checks the field values on UpsertUnifiedLeaComplaintRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpsertUnifiedLeaComplaintRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpsertUnifiedLeaComplaintRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpsertUnifiedLeaComplaintRequestMultiError, or nil if none found.
func (m *UpsertUnifiedLeaComplaintRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpsertUnifiedLeaComplaintRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComplaintId

	// no validation rules for CustId

	if utf8.RuneCountInString(m.GetAccountNumber()) < 1 {
		err := UpsertUnifiedLeaComplaintRequestValidationError{
			field:  "AccountNumber",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDates()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpsertUnifiedLeaComplaintRequestValidationError{
					field:  "Dates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpsertUnifiedLeaComplaintRequestValidationError{
					field:  "Dates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDates()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpsertUnifiedLeaComplaintRequestValidationError{
				field:  "Dates",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActionType

	// no validation rules for OperationalStatus

	if all {
		switch v := interface{}(m.GetLien()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpsertUnifiedLeaComplaintRequestValidationError{
					field:  "Lien",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpsertUnifiedLeaComplaintRequestValidationError{
					field:  "Lien",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLien()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpsertUnifiedLeaComplaintRequestValidationError{
				field:  "Lien",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Reason

	// no validation rules for Remark

	// no validation rules for FreezeCode

	if all {
		switch v := interface{}(m.GetLayerNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpsertUnifiedLeaComplaintRequestValidationError{
					field:  "LayerNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpsertUnifiedLeaComplaintRequestValidationError{
					field:  "LayerNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLayerNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpsertUnifiedLeaComplaintRequestValidationError{
				field:  "LayerNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisputedTransactions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpsertUnifiedLeaComplaintRequestValidationError{
					field:  "DisputedTransactions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpsertUnifiedLeaComplaintRequestValidationError{
					field:  "DisputedTransactions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisputedTransactions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpsertUnifiedLeaComplaintRequestValidationError{
				field:  "DisputedTransactions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _UpsertUnifiedLeaComplaintRequest_ProcessingStatus_NotInLookup[m.GetProcessingStatus()]; ok {
		err := UpsertUnifiedLeaComplaintRequestValidationError{
			field:  "ProcessingStatus",
			reason: "value must not be in list [PROCESSING_STATUS_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for LeaOrigin

	if all {
		switch v := interface{}(m.GetReporterContactDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpsertUnifiedLeaComplaintRequestValidationError{
					field:  "ReporterContactDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpsertUnifiedLeaComplaintRequestValidationError{
					field:  "ReporterContactDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReporterContactDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpsertUnifiedLeaComplaintRequestValidationError{
				field:  "ReporterContactDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OriginGeographicalState

	if all {
		switch v := interface{}(m.GetAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpsertUnifiedLeaComplaintRequestValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpsertUnifiedLeaComplaintRequestValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpsertUnifiedLeaComplaintRequestValidationError{
				field:  "AdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpsertUnifiedLeaComplaintRequestMultiError(errors)
	}

	return nil
}

// UpsertUnifiedLeaComplaintRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpsertUnifiedLeaComplaintRequest.ValidateAll() if the designated
// constraints aren't met.
type UpsertUnifiedLeaComplaintRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpsertUnifiedLeaComplaintRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpsertUnifiedLeaComplaintRequestMultiError) AllErrors() []error { return m }

// UpsertUnifiedLeaComplaintRequestValidationError is the validation error
// returned by UpsertUnifiedLeaComplaintRequest.Validate if the designated
// constraints aren't met.
type UpsertUnifiedLeaComplaintRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpsertUnifiedLeaComplaintRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpsertUnifiedLeaComplaintRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpsertUnifiedLeaComplaintRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpsertUnifiedLeaComplaintRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpsertUnifiedLeaComplaintRequestValidationError) ErrorName() string {
	return "UpsertUnifiedLeaComplaintRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpsertUnifiedLeaComplaintRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpsertUnifiedLeaComplaintRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpsertUnifiedLeaComplaintRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpsertUnifiedLeaComplaintRequestValidationError{}

var _UpsertUnifiedLeaComplaintRequest_ProcessingStatus_NotInLookup = map[ProcessingStatus]struct{}{
	0: {},
}

// Validate checks the field values on UpsertUnifiedLeaComplaintResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpsertUnifiedLeaComplaintResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpsertUnifiedLeaComplaintResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpsertUnifiedLeaComplaintResponseMultiError, or nil if none found.
func (m *UpsertUnifiedLeaComplaintResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpsertUnifiedLeaComplaintResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpsertUnifiedLeaComplaintResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpsertUnifiedLeaComplaintResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpsertUnifiedLeaComplaintResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpsertUnifiedLeaComplaintResponseMultiError(errors)
	}

	return nil
}

// UpsertUnifiedLeaComplaintResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpsertUnifiedLeaComplaintResponse.ValidateAll() if the designated
// constraints aren't met.
type UpsertUnifiedLeaComplaintResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpsertUnifiedLeaComplaintResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpsertUnifiedLeaComplaintResponseMultiError) AllErrors() []error { return m }

// UpsertUnifiedLeaComplaintResponseValidationError is the validation error
// returned by UpsertUnifiedLeaComplaintResponse.Validate if the designated
// constraints aren't met.
type UpsertUnifiedLeaComplaintResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpsertUnifiedLeaComplaintResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpsertUnifiedLeaComplaintResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpsertUnifiedLeaComplaintResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpsertUnifiedLeaComplaintResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpsertUnifiedLeaComplaintResponseValidationError) ErrorName() string {
	return "UpsertUnifiedLeaComplaintResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpsertUnifiedLeaComplaintResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpsertUnifiedLeaComplaintResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpsertUnifiedLeaComplaintResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpsertUnifiedLeaComplaintResponseValidationError{}

// Validate checks the field values on GetUnifiedLeaComplaintRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUnifiedLeaComplaintRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUnifiedLeaComplaintRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetUnifiedLeaComplaintRequestMultiError, or nil if none found.
func (m *GetUnifiedLeaComplaintRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUnifiedLeaComplaintRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Limit

	switch v := m.Identifier.(type) {
	case *GetUnifiedLeaComplaintRequest_ActorId:
		if v == nil {
			err := GetUnifiedLeaComplaintRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if utf8.RuneCountInString(m.GetActorId()) < 1 {
			err := GetUnifiedLeaComplaintRequestValidationError{
				field:  "ActorId",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetUnifiedLeaComplaintRequestMultiError(errors)
	}

	return nil
}

// GetUnifiedLeaComplaintRequestMultiError is an error wrapping multiple
// validation errors returned by GetUnifiedLeaComplaintRequest.ValidateAll()
// if the designated constraints aren't met.
type GetUnifiedLeaComplaintRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUnifiedLeaComplaintRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUnifiedLeaComplaintRequestMultiError) AllErrors() []error { return m }

// GetUnifiedLeaComplaintRequestValidationError is the validation error
// returned by GetUnifiedLeaComplaintRequest.Validate if the designated
// constraints aren't met.
type GetUnifiedLeaComplaintRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUnifiedLeaComplaintRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUnifiedLeaComplaintRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUnifiedLeaComplaintRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUnifiedLeaComplaintRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUnifiedLeaComplaintRequestValidationError) ErrorName() string {
	return "GetUnifiedLeaComplaintRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUnifiedLeaComplaintRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUnifiedLeaComplaintRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUnifiedLeaComplaintRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUnifiedLeaComplaintRequestValidationError{}

// Validate checks the field values on GetUnifiedLeaComplaintResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUnifiedLeaComplaintResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUnifiedLeaComplaintResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetUnifiedLeaComplaintResponseMultiError, or nil if none found.
func (m *GetUnifiedLeaComplaintResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUnifiedLeaComplaintResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUnifiedLeaComplaintResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUnifiedLeaComplaintResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUnifiedLeaComplaintResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUnifiedLeaComplaints() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUnifiedLeaComplaintResponseValidationError{
						field:  fmt.Sprintf("UnifiedLeaComplaints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUnifiedLeaComplaintResponseValidationError{
						field:  fmt.Sprintf("UnifiedLeaComplaints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUnifiedLeaComplaintResponseValidationError{
					field:  fmt.Sprintf("UnifiedLeaComplaints[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetUnifiedLeaComplaintResponseMultiError(errors)
	}

	return nil
}

// GetUnifiedLeaComplaintResponseMultiError is an error wrapping multiple
// validation errors returned by GetUnifiedLeaComplaintResponse.ValidateAll()
// if the designated constraints aren't met.
type GetUnifiedLeaComplaintResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUnifiedLeaComplaintResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUnifiedLeaComplaintResponseMultiError) AllErrors() []error { return m }

// GetUnifiedLeaComplaintResponseValidationError is the validation error
// returned by GetUnifiedLeaComplaintResponse.Validate if the designated
// constraints aren't met.
type GetUnifiedLeaComplaintResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUnifiedLeaComplaintResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUnifiedLeaComplaintResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUnifiedLeaComplaintResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUnifiedLeaComplaintResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUnifiedLeaComplaintResponseValidationError) ErrorName() string {
	return "GetUnifiedLeaComplaintResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUnifiedLeaComplaintResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUnifiedLeaComplaintResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUnifiedLeaComplaintResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUnifiedLeaComplaintResponseValidationError{}
