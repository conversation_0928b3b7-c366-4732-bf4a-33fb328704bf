// Code generated by MockGen. DO NOT EDIT.
// Source: api/risk/lea/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	lea "github.com/epifi/gamma/api/risk/lea"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockLeaClient is a mock of LeaClient interface.
type MockLeaClient struct {
	ctrl     *gomock.Controller
	recorder *MockLeaClientMockRecorder
}

// MockLeaClientMockRecorder is the mock recorder for MockLeaClient.
type MockLeaClientMockRecorder struct {
	mock *MockLeaClient
}

// NewMockLeaClient creates a new mock instance.
func NewMockLeaClient(ctrl *gomock.Controller) *MockLeaClient {
	mock := &MockLeaClient{ctrl: ctrl}
	mock.recorder = &MockLeaClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLeaClient) EXPECT() *MockLeaClientMockRecorder {
	return m.recorder
}

// GetUnifiedLeaComplaint mocks base method.
func (m *MockLeaClient) GetUnifiedLeaComplaint(ctx context.Context, in *lea.GetUnifiedLeaComplaintRequest, opts ...grpc.CallOption) (*lea.GetUnifiedLeaComplaintResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUnifiedLeaComplaint", varargs...)
	ret0, _ := ret[0].(*lea.GetUnifiedLeaComplaintResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUnifiedLeaComplaint indicates an expected call of GetUnifiedLeaComplaint.
func (mr *MockLeaClientMockRecorder) GetUnifiedLeaComplaint(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnifiedLeaComplaint", reflect.TypeOf((*MockLeaClient)(nil).GetUnifiedLeaComplaint), varargs...)
}

// UpsertUnifiedLeaComplaint mocks base method.
func (m *MockLeaClient) UpsertUnifiedLeaComplaint(ctx context.Context, in *lea.UpsertUnifiedLeaComplaintRequest, opts ...grpc.CallOption) (*lea.UpsertUnifiedLeaComplaintResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertUnifiedLeaComplaint", varargs...)
	ret0, _ := ret[0].(*lea.UpsertUnifiedLeaComplaintResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertUnifiedLeaComplaint indicates an expected call of UpsertUnifiedLeaComplaint.
func (mr *MockLeaClientMockRecorder) UpsertUnifiedLeaComplaint(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertUnifiedLeaComplaint", reflect.TypeOf((*MockLeaClient)(nil).UpsertUnifiedLeaComplaint), varargs...)
}

// MockLeaServer is a mock of LeaServer interface.
type MockLeaServer struct {
	ctrl     *gomock.Controller
	recorder *MockLeaServerMockRecorder
}

// MockLeaServerMockRecorder is the mock recorder for MockLeaServer.
type MockLeaServerMockRecorder struct {
	mock *MockLeaServer
}

// NewMockLeaServer creates a new mock instance.
func NewMockLeaServer(ctrl *gomock.Controller) *MockLeaServer {
	mock := &MockLeaServer{ctrl: ctrl}
	mock.recorder = &MockLeaServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLeaServer) EXPECT() *MockLeaServerMockRecorder {
	return m.recorder
}

// GetUnifiedLeaComplaint mocks base method.
func (m *MockLeaServer) GetUnifiedLeaComplaint(arg0 context.Context, arg1 *lea.GetUnifiedLeaComplaintRequest) (*lea.GetUnifiedLeaComplaintResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnifiedLeaComplaint", arg0, arg1)
	ret0, _ := ret[0].(*lea.GetUnifiedLeaComplaintResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUnifiedLeaComplaint indicates an expected call of GetUnifiedLeaComplaint.
func (mr *MockLeaServerMockRecorder) GetUnifiedLeaComplaint(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnifiedLeaComplaint", reflect.TypeOf((*MockLeaServer)(nil).GetUnifiedLeaComplaint), arg0, arg1)
}

// UpsertUnifiedLeaComplaint mocks base method.
func (m *MockLeaServer) UpsertUnifiedLeaComplaint(arg0 context.Context, arg1 *lea.UpsertUnifiedLeaComplaintRequest) (*lea.UpsertUnifiedLeaComplaintResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertUnifiedLeaComplaint", arg0, arg1)
	ret0, _ := ret[0].(*lea.UpsertUnifiedLeaComplaintResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertUnifiedLeaComplaint indicates an expected call of UpsertUnifiedLeaComplaint.
func (mr *MockLeaServerMockRecorder) UpsertUnifiedLeaComplaint(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertUnifiedLeaComplaint", reflect.TypeOf((*MockLeaServer)(nil).UpsertUnifiedLeaComplaint), arg0, arg1)
}

// MockUnsafeLeaServer is a mock of UnsafeLeaServer interface.
type MockUnsafeLeaServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeLeaServerMockRecorder
}

// MockUnsafeLeaServerMockRecorder is the mock recorder for MockUnsafeLeaServer.
type MockUnsafeLeaServerMockRecorder struct {
	mock *MockUnsafeLeaServer
}

// NewMockUnsafeLeaServer creates a new mock instance.
func NewMockUnsafeLeaServer(ctrl *gomock.Controller) *MockUnsafeLeaServer {
	mock := &MockUnsafeLeaServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeLeaServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeLeaServer) EXPECT() *MockUnsafeLeaServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedLeaServer mocks base method.
func (m *MockUnsafeLeaServer) mustEmbedUnimplementedLeaServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedLeaServer")
}

// mustEmbedUnimplementedLeaServer indicates an expected call of mustEmbedUnimplementedLeaServer.
func (mr *MockUnsafeLeaServerMockRecorder) mustEmbedUnimplementedLeaServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedLeaServer", reflect.TypeOf((*MockUnsafeLeaServer)(nil).mustEmbedUnimplementedLeaServer))
}
