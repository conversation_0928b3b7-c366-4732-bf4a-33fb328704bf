// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/lea/service.proto

package lea

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	enums "github.com/epifi/gamma/api/risk/enums"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpsertUnifiedLeaComplaintResponse_Status int32

const (
	UpsertUnifiedLeaComplaintResponse_OK UpsertUnifiedLeaComplaintResponse_Status = 0
	// error in some rpc while processing
	UpsertUnifiedLeaComplaintResponse_INTERNAL_SERVER_ERROR UpsertUnifiedLeaComplaintResponse_Status = 13
	// invalid argument passed in request
	UpsertUnifiedLeaComplaintResponse_INVALID_ARGUMENT UpsertUnifiedLeaComplaintResponse_Status = 3
	// an entry already exists for account number and complaint id with the same processing status
	UpsertUnifiedLeaComplaintResponse_ALREADY_EXISTS UpsertUnifiedLeaComplaintResponse_Status = 6
)

// Enum value maps for UpsertUnifiedLeaComplaintResponse_Status.
var (
	UpsertUnifiedLeaComplaintResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL_SERVER_ERROR",
		3:  "INVALID_ARGUMENT",
		6:  "ALREADY_EXISTS",
	}
	UpsertUnifiedLeaComplaintResponse_Status_value = map[string]int32{
		"OK":                    0,
		"INTERNAL_SERVER_ERROR": 13,
		"INVALID_ARGUMENT":      3,
		"ALREADY_EXISTS":        6,
	}
)

func (x UpsertUnifiedLeaComplaintResponse_Status) Enum() *UpsertUnifiedLeaComplaintResponse_Status {
	p := new(UpsertUnifiedLeaComplaintResponse_Status)
	*p = x
	return p
}

func (x UpsertUnifiedLeaComplaintResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpsertUnifiedLeaComplaintResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_lea_service_proto_enumTypes[0].Descriptor()
}

func (UpsertUnifiedLeaComplaintResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_lea_service_proto_enumTypes[0]
}

func (x UpsertUnifiedLeaComplaintResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpsertUnifiedLeaComplaintResponse_Status.Descriptor instead.
func (UpsertUnifiedLeaComplaintResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_lea_service_proto_rawDescGZIP(), []int{1, 0}
}

type GetUnifiedLeaComplaintResponse_Status int32

const (
	GetUnifiedLeaComplaintResponse_OK GetUnifiedLeaComplaintResponse_Status = 0
	// invalid argument passed in request
	GetUnifiedLeaComplaintResponse_INVALID_ARGUMENT GetUnifiedLeaComplaintResponse_Status = 3
	// no entry found for the given account number and complaint id
	GetUnifiedLeaComplaintResponse_NOT_FOUND GetUnifiedLeaComplaintResponse_Status = 5
	// error in some rpc while processing
	GetUnifiedLeaComplaintResponse_INTERNAL_SERVER_ERROR GetUnifiedLeaComplaintResponse_Status = 13
)

// Enum value maps for GetUnifiedLeaComplaintResponse_Status.
var (
	GetUnifiedLeaComplaintResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "NOT_FOUND",
		13: "INTERNAL_SERVER_ERROR",
	}
	GetUnifiedLeaComplaintResponse_Status_value = map[string]int32{
		"OK":                    0,
		"INVALID_ARGUMENT":      3,
		"NOT_FOUND":             5,
		"INTERNAL_SERVER_ERROR": 13,
	}
)

func (x GetUnifiedLeaComplaintResponse_Status) Enum() *GetUnifiedLeaComplaintResponse_Status {
	p := new(GetUnifiedLeaComplaintResponse_Status)
	*p = x
	return p
}

func (x GetUnifiedLeaComplaintResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetUnifiedLeaComplaintResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_lea_service_proto_enumTypes[1].Descriptor()
}

func (GetUnifiedLeaComplaintResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_lea_service_proto_enumTypes[1]
}

func (x GetUnifiedLeaComplaintResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetUnifiedLeaComplaintResponse_Status.Descriptor instead.
func (GetUnifiedLeaComplaintResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_lea_service_proto_rawDescGZIP(), []int{3, 0}
}

// most of the values here refer to api/risk/lea/unified_lea_complaint.proto
type UpsertUnifiedLeaComplaintRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComplaintId             string                    `protobuf:"bytes,1,opt,name=complaint_id,json=complaintId,proto3" json:"complaint_id,omitempty"`
	CustId                  string                    `protobuf:"bytes,2,opt,name=cust_id,json=custId,proto3" json:"cust_id,omitempty"`
	AccountNumber           string                    `protobuf:"bytes,3,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	Dates                   *Dates                    `protobuf:"bytes,4,opt,name=dates,proto3" json:"dates,omitempty"`
	ActionType              ActionType                `protobuf:"varint,5,opt,name=action_type,json=actionType,proto3,enum=risk.ActionType" json:"action_type,omitempty"`
	OperationalStatus       enums.AccountFreezeStatus `protobuf:"varint,6,opt,name=operational_status,json=operationalStatus,proto3,enum=enums.AccountFreezeStatus" json:"operational_status,omitempty"`
	Lien                    *Lien                     `protobuf:"bytes,7,opt,name=lien,proto3" json:"lien,omitempty"`
	Reason                  string                    `protobuf:"bytes,8,opt,name=reason,proto3" json:"reason,omitempty"`
	Remark                  string                    `protobuf:"bytes,9,opt,name=remark,proto3" json:"remark,omitempty"`
	FreezeCode              string                    `protobuf:"bytes,10,opt,name=freeze_code,json=freezeCode,proto3" json:"freeze_code,omitempty"`
	LayerNumber             *typesv2.NullInt32        `protobuf:"bytes,11,opt,name=layer_number,json=layerNumber,proto3" json:"layer_number,omitempty"`
	DisputedTransactions    *DisputedTransactions     `protobuf:"bytes,12,opt,name=disputed_transactions,json=disputedTransactions,proto3" json:"disputed_transactions,omitempty"`
	ProcessingStatus        ProcessingStatus          `protobuf:"varint,13,opt,name=processing_status,json=processingStatus,proto3,enum=risk.ProcessingStatus" json:"processing_status,omitempty"`
	LeaOrigin               enums.LEAReportOrigin     `protobuf:"varint,14,opt,name=lea_origin,json=leaOrigin,proto3,enum=enums.LEAReportOrigin" json:"lea_origin,omitempty"`
	ReporterContactDetails  *ReporterContactDetails   `protobuf:"bytes,15,opt,name=reporter_contact_details,json=reporterContactDetails,proto3" json:"reporter_contact_details,omitempty"`
	OriginGeographicalState string                    `protobuf:"bytes,16,opt,name=origin_geographical_state,json=originGeographicalState,proto3" json:"origin_geographical_state,omitempty"`
	AdditionalDetails       *AdditionalDetails        `protobuf:"bytes,17,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
}

func (x *UpsertUnifiedLeaComplaintRequest) Reset() {
	*x = UpsertUnifiedLeaComplaintRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lea_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertUnifiedLeaComplaintRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertUnifiedLeaComplaintRequest) ProtoMessage() {}

func (x *UpsertUnifiedLeaComplaintRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lea_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertUnifiedLeaComplaintRequest.ProtoReflect.Descriptor instead.
func (*UpsertUnifiedLeaComplaintRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_lea_service_proto_rawDescGZIP(), []int{0}
}

func (x *UpsertUnifiedLeaComplaintRequest) GetComplaintId() string {
	if x != nil {
		return x.ComplaintId
	}
	return ""
}

func (x *UpsertUnifiedLeaComplaintRequest) GetCustId() string {
	if x != nil {
		return x.CustId
	}
	return ""
}

func (x *UpsertUnifiedLeaComplaintRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *UpsertUnifiedLeaComplaintRequest) GetDates() *Dates {
	if x != nil {
		return x.Dates
	}
	return nil
}

func (x *UpsertUnifiedLeaComplaintRequest) GetActionType() ActionType {
	if x != nil {
		return x.ActionType
	}
	return ActionType_ACTION_TYPE_UNSPECIFIED
}

func (x *UpsertUnifiedLeaComplaintRequest) GetOperationalStatus() enums.AccountFreezeStatus {
	if x != nil {
		return x.OperationalStatus
	}
	return enums.AccountFreezeStatus(0)
}

func (x *UpsertUnifiedLeaComplaintRequest) GetLien() *Lien {
	if x != nil {
		return x.Lien
	}
	return nil
}

func (x *UpsertUnifiedLeaComplaintRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *UpsertUnifiedLeaComplaintRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *UpsertUnifiedLeaComplaintRequest) GetFreezeCode() string {
	if x != nil {
		return x.FreezeCode
	}
	return ""
}

func (x *UpsertUnifiedLeaComplaintRequest) GetLayerNumber() *typesv2.NullInt32 {
	if x != nil {
		return x.LayerNumber
	}
	return nil
}

func (x *UpsertUnifiedLeaComplaintRequest) GetDisputedTransactions() *DisputedTransactions {
	if x != nil {
		return x.DisputedTransactions
	}
	return nil
}

func (x *UpsertUnifiedLeaComplaintRequest) GetProcessingStatus() ProcessingStatus {
	if x != nil {
		return x.ProcessingStatus
	}
	return ProcessingStatus_PROCESSING_STATUS_UNSPECIFIED
}

func (x *UpsertUnifiedLeaComplaintRequest) GetLeaOrigin() enums.LEAReportOrigin {
	if x != nil {
		return x.LeaOrigin
	}
	return enums.LEAReportOrigin(0)
}

func (x *UpsertUnifiedLeaComplaintRequest) GetReporterContactDetails() *ReporterContactDetails {
	if x != nil {
		return x.ReporterContactDetails
	}
	return nil
}

func (x *UpsertUnifiedLeaComplaintRequest) GetOriginGeographicalState() string {
	if x != nil {
		return x.OriginGeographicalState
	}
	return ""
}

func (x *UpsertUnifiedLeaComplaintRequest) GetAdditionalDetails() *AdditionalDetails {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

type UpsertUnifiedLeaComplaintResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpsertUnifiedLeaComplaintResponse) Reset() {
	*x = UpsertUnifiedLeaComplaintResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lea_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertUnifiedLeaComplaintResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertUnifiedLeaComplaintResponse) ProtoMessage() {}

func (x *UpsertUnifiedLeaComplaintResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lea_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertUnifiedLeaComplaintResponse.ProtoReflect.Descriptor instead.
func (*UpsertUnifiedLeaComplaintResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_lea_service_proto_rawDescGZIP(), []int{1}
}

func (x *UpsertUnifiedLeaComplaintResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetUnifiedLeaComplaintRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetUnifiedLeaComplaintRequest_ActorId
	Identifier isGetUnifiedLeaComplaintRequest_Identifier `protobuf_oneof:"identifier"`
	// number of LEA Complaints to be returned, 0 if all the LEA complaints need to be returned
	Limit int64 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *GetUnifiedLeaComplaintRequest) Reset() {
	*x = GetUnifiedLeaComplaintRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lea_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUnifiedLeaComplaintRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUnifiedLeaComplaintRequest) ProtoMessage() {}

func (x *GetUnifiedLeaComplaintRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lea_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUnifiedLeaComplaintRequest.ProtoReflect.Descriptor instead.
func (*GetUnifiedLeaComplaintRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_lea_service_proto_rawDescGZIP(), []int{2}
}

func (m *GetUnifiedLeaComplaintRequest) GetIdentifier() isGetUnifiedLeaComplaintRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetUnifiedLeaComplaintRequest) GetActorId() string {
	if x, ok := x.GetIdentifier().(*GetUnifiedLeaComplaintRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *GetUnifiedLeaComplaintRequest) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type isGetUnifiedLeaComplaintRequest_Identifier interface {
	isGetUnifiedLeaComplaintRequest_Identifier()
}

type GetUnifiedLeaComplaintRequest_ActorId struct {
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3,oneof"` // Add other identifiers here in the future
}

func (*GetUnifiedLeaComplaintRequest_ActorId) isGetUnifiedLeaComplaintRequest_Identifier() {}

type GetUnifiedLeaComplaintResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status               *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	UnifiedLeaComplaints []*UnifiedLeaComplaint `protobuf:"bytes,2,rep,name=unified_lea_complaints,json=unifiedLeaComplaints,proto3" json:"unified_lea_complaints,omitempty"`
}

func (x *GetUnifiedLeaComplaintResponse) Reset() {
	*x = GetUnifiedLeaComplaintResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lea_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUnifiedLeaComplaintResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUnifiedLeaComplaintResponse) ProtoMessage() {}

func (x *GetUnifiedLeaComplaintResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lea_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUnifiedLeaComplaintResponse.ProtoReflect.Descriptor instead.
func (*GetUnifiedLeaComplaintResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_lea_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetUnifiedLeaComplaintResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUnifiedLeaComplaintResponse) GetUnifiedLeaComplaints() []*UnifiedLeaComplaint {
	if x != nil {
		return x.UnifiedLeaComplaints
	}
	return nil
}

var File_api_risk_lea_service_proto protoreflect.FileDescriptor

var file_api_risk_lea_service_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x65, 0x61, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x72, 0x69,
	0x73, 0x6b, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x65, 0x61, 0x2f, 0x75, 0x6e, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x5f, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6e, 0x75, 0x6c, 0x6c,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8e, 0x07, 0x0a, 0x20, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x55,
	0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x63, 0x75, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x75, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x73, 0x52, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x31, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x49, 0x0a, 0x12, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x11, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x04, 0x6c, 0x69, 0x65, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4c, 0x69, 0x65, 0x6e,
	0x52, 0x04, 0x6c, 0x69, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x72, 0x65,
	0x65, 0x7a, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x39, 0x0a, 0x0c, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6c, 0x6c,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x0b, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x4f, 0x0a, 0x15, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65,
	0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x14, 0x64,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x4d, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00,
	0x52, 0x10, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x35, 0x0a, 0x0a, 0x6c, 0x65, 0x61, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4c,
	0x45, 0x41, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x52, 0x09,
	0x6c, 0x65, 0x61, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12, 0x56, 0x0a, 0x18, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x16, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x3a, 0x0a, 0x19, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x67, 0x65, 0x6f, 0x67,
	0x72, 0x61, 0x70, 0x68, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x47, 0x65, 0x6f, 0x67,
	0x72, 0x61, 0x70, 0x68, 0x69, 0x63, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x46, 0x0a,
	0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x11, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x9f, 0x01, 0x0a, 0x21, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74,
	0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x55, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x53,
	0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x0d, 0x12, 0x14, 0x0a,
	0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x45,
	0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x06, 0x22, 0x69, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x55, 0x6e,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x22, 0xe8, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4f, 0x0a, 0x16, 0x75, 0x6e,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x14, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x65,
	0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x22, 0x50, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x53,
	0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x0d, 0x32, 0xd8, 0x01,
	0x0a, 0x03, 0x4c, 0x65, 0x61, 0x12, 0x6c, 0x0a, 0x19, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x55,
	0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x74, 0x12, 0x26, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74,
	0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c,
	0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x12, 0x23, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c,
	0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x4a, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6c, 0x65, 0x61, 0x5a,
	0x23, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b,
	0x2f, 0x6c, 0x65, 0x61, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_lea_service_proto_rawDescOnce sync.Once
	file_api_risk_lea_service_proto_rawDescData = file_api_risk_lea_service_proto_rawDesc
)

func file_api_risk_lea_service_proto_rawDescGZIP() []byte {
	file_api_risk_lea_service_proto_rawDescOnce.Do(func() {
		file_api_risk_lea_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_lea_service_proto_rawDescData)
	})
	return file_api_risk_lea_service_proto_rawDescData
}

var file_api_risk_lea_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_risk_lea_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_risk_lea_service_proto_goTypes = []interface{}{
	(UpsertUnifiedLeaComplaintResponse_Status)(0), // 0: risk.UpsertUnifiedLeaComplaintResponse.Status
	(GetUnifiedLeaComplaintResponse_Status)(0),    // 1: risk.GetUnifiedLeaComplaintResponse.Status
	(*UpsertUnifiedLeaComplaintRequest)(nil),      // 2: risk.UpsertUnifiedLeaComplaintRequest
	(*UpsertUnifiedLeaComplaintResponse)(nil),     // 3: risk.UpsertUnifiedLeaComplaintResponse
	(*GetUnifiedLeaComplaintRequest)(nil),         // 4: risk.GetUnifiedLeaComplaintRequest
	(*GetUnifiedLeaComplaintResponse)(nil),        // 5: risk.GetUnifiedLeaComplaintResponse
	(*Dates)(nil),                                 // 6: risk.Dates
	(ActionType)(0),                               // 7: risk.ActionType
	(enums.AccountFreezeStatus)(0),                // 8: enums.AccountFreezeStatus
	(*Lien)(nil),                                  // 9: risk.Lien
	(*typesv2.NullInt32)(nil),                     // 10: api.typesv2.NullInt32
	(*DisputedTransactions)(nil),                  // 11: risk.DisputedTransactions
	(ProcessingStatus)(0),                         // 12: risk.ProcessingStatus
	(enums.LEAReportOrigin)(0),                    // 13: enums.LEAReportOrigin
	(*ReporterContactDetails)(nil),                // 14: risk.ReporterContactDetails
	(*AdditionalDetails)(nil),                     // 15: risk.AdditionalDetails
	(*rpc.Status)(nil),                            // 16: rpc.Status
	(*UnifiedLeaComplaint)(nil),                   // 17: risk.UnifiedLeaComplaint
}
var file_api_risk_lea_service_proto_depIdxs = []int32{
	6,  // 0: risk.UpsertUnifiedLeaComplaintRequest.dates:type_name -> risk.Dates
	7,  // 1: risk.UpsertUnifiedLeaComplaintRequest.action_type:type_name -> risk.ActionType
	8,  // 2: risk.UpsertUnifiedLeaComplaintRequest.operational_status:type_name -> enums.AccountFreezeStatus
	9,  // 3: risk.UpsertUnifiedLeaComplaintRequest.lien:type_name -> risk.Lien
	10, // 4: risk.UpsertUnifiedLeaComplaintRequest.layer_number:type_name -> api.typesv2.NullInt32
	11, // 5: risk.UpsertUnifiedLeaComplaintRequest.disputed_transactions:type_name -> risk.DisputedTransactions
	12, // 6: risk.UpsertUnifiedLeaComplaintRequest.processing_status:type_name -> risk.ProcessingStatus
	13, // 7: risk.UpsertUnifiedLeaComplaintRequest.lea_origin:type_name -> enums.LEAReportOrigin
	14, // 8: risk.UpsertUnifiedLeaComplaintRequest.reporter_contact_details:type_name -> risk.ReporterContactDetails
	15, // 9: risk.UpsertUnifiedLeaComplaintRequest.additional_details:type_name -> risk.AdditionalDetails
	16, // 10: risk.UpsertUnifiedLeaComplaintResponse.status:type_name -> rpc.Status
	16, // 11: risk.GetUnifiedLeaComplaintResponse.status:type_name -> rpc.Status
	17, // 12: risk.GetUnifiedLeaComplaintResponse.unified_lea_complaints:type_name -> risk.UnifiedLeaComplaint
	2,  // 13: risk.Lea.UpsertUnifiedLeaComplaint:input_type -> risk.UpsertUnifiedLeaComplaintRequest
	4,  // 14: risk.Lea.GetUnifiedLeaComplaint:input_type -> risk.GetUnifiedLeaComplaintRequest
	3,  // 15: risk.Lea.UpsertUnifiedLeaComplaint:output_type -> risk.UpsertUnifiedLeaComplaintResponse
	5,  // 16: risk.Lea.GetUnifiedLeaComplaint:output_type -> risk.GetUnifiedLeaComplaintResponse
	15, // [15:17] is the sub-list for method output_type
	13, // [13:15] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_api_risk_lea_service_proto_init() }
func file_api_risk_lea_service_proto_init() {
	if File_api_risk_lea_service_proto != nil {
		return
	}
	file_api_risk_lea_unified_lea_complaint_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_lea_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertUnifiedLeaComplaintRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lea_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertUnifiedLeaComplaintResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lea_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUnifiedLeaComplaintRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lea_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUnifiedLeaComplaintResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_lea_service_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*GetUnifiedLeaComplaintRequest_ActorId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_lea_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_risk_lea_service_proto_goTypes,
		DependencyIndexes: file_api_risk_lea_service_proto_depIdxs,
		EnumInfos:         file_api_risk_lea_service_proto_enumTypes,
		MessageInfos:      file_api_risk_lea_service_proto_msgTypes,
	}.Build()
	File_api_risk_lea_service_proto = out.File
	file_api_risk_lea_service_proto_rawDesc = nil
	file_api_risk_lea_service_proto_goTypes = nil
	file_api_risk_lea_service_proto_depIdxs = nil
}
