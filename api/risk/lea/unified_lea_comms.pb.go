// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/lea/unified_lea_comms.proto

package lea

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	common "github.com/epifi/be-common/api/typesv2/common"
	case_management "github.com/epifi/gamma/api/risk/case_management"
	enums "github.com/epifi/gamma/api/risk/enums"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Intended handling for an LEA complaint
// Can be used in multiple events including sending comms
type HandlingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId   string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// Status of the account we got in the LEA complaint
	OperationalStatus enums.AccountFreezeStatus `protobuf:"varint,3,opt,name=operational_status,json=operationalStatus,proto3,enum=enums.AccountFreezeStatus" json:"operational_status,omitempty"`
	// actual lien amount applied on the account
	LienAmount *money.Money `protobuf:"bytes,4,opt,name=lien_amount,json=lienAmount,proto3" json:"lien_amount,omitempty"`
	// whether to remove app access or not
	RemoveAppAccess bool                       `protobuf:"varint,5,opt,name=remove_app_access,json=removeAppAccess,proto3" json:"remove_app_access,omitempty"`
	Complaints      []*HandlingParamsComplaint `protobuf:"bytes,6,rep,name=complaints,proto3" json:"complaints,omitempty"`
	// if this is true we should send comms
	ShouldSendComms bool `protobuf:"varint,7,opt,name=should_send_comms,json=shouldSendComms,proto3" json:"should_send_comms,omitempty"`
	// defines if the action has been taken by bank
	// BOOLEAN_ENUM_UNSPECIFIED -> bank api is failing with permanent failure, so we cannot determine the state
	ActionTakenByBank common.BooleanEnum `protobuf:"varint,8,opt,name=action_taken_by_bank,json=actionTakenByBank,proto3,enum=api.typesv2.common.BooleanEnum" json:"action_taken_by_bank,omitempty"`
	// entity id of the actor in LEA
	EntityId string `protobuf:"bytes,9,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	// list of rules against which we will create alerts for this LEA
	RuleIdentifiers []*case_management.RuleIdentifier `protobuf:"bytes,10,rep,name=rule_identifiers,json=ruleIdentifiers,proto3" json:"rule_identifiers,omitempty"`
	// layer number of the complaint
	LayerNumber *typesv2.NullInt32 `protobuf:"bytes,11,opt,name=layer_number,json=layerNumber,proto3" json:"layer_number,omitempty"`
}

func (x *HandlingParams) Reset() {
	*x = HandlingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lea_unified_lea_comms_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandlingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandlingParams) ProtoMessage() {}

func (x *HandlingParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lea_unified_lea_comms_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandlingParams.ProtoReflect.Descriptor instead.
func (*HandlingParams) Descriptor() ([]byte, []int) {
	return file_api_risk_lea_unified_lea_comms_proto_rawDescGZIP(), []int{0}
}

func (x *HandlingParams) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *HandlingParams) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *HandlingParams) GetOperationalStatus() enums.AccountFreezeStatus {
	if x != nil {
		return x.OperationalStatus
	}
	return enums.AccountFreezeStatus(0)
}

func (x *HandlingParams) GetLienAmount() *money.Money {
	if x != nil {
		return x.LienAmount
	}
	return nil
}

func (x *HandlingParams) GetRemoveAppAccess() bool {
	if x != nil {
		return x.RemoveAppAccess
	}
	return false
}

func (x *HandlingParams) GetComplaints() []*HandlingParamsComplaint {
	if x != nil {
		return x.Complaints
	}
	return nil
}

func (x *HandlingParams) GetShouldSendComms() bool {
	if x != nil {
		return x.ShouldSendComms
	}
	return false
}

func (x *HandlingParams) GetActionTakenByBank() common.BooleanEnum {
	if x != nil {
		return x.ActionTakenByBank
	}
	return common.BooleanEnum(0)
}

func (x *HandlingParams) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

func (x *HandlingParams) GetRuleIdentifiers() []*case_management.RuleIdentifier {
	if x != nil {
		return x.RuleIdentifiers
	}
	return nil
}

func (x *HandlingParams) GetLayerNumber() *typesv2.NullInt32 {
	if x != nil {
		return x.LayerNumber
	}
	return nil
}

// simplified complaint relevant to end user
type HandlingParamsComplaint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComplaintId string `protobuf:"bytes,1,opt,name=complaint_id,json=complaintId,proto3" json:"complaint_id,omitempty"`
	// date of complaint relevant to end user
	Date                 *timestamppb.Timestamp  `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	ContactDetails       *ReporterContactDetails `protobuf:"bytes,3,opt,name=contact_details,json=contactDetails,proto3" json:"contact_details,omitempty"`
	ComplaintOriginState string                  `protobuf:"bytes,4,opt,name=complaint_origin_state,json=complaintOriginState,proto3" json:"complaint_origin_state,omitempty"`
	DisputedTransactions *DisputedTransactions   `protobuf:"bytes,5,opt,name=disputed_transactions,json=disputedTransactions,proto3" json:"disputed_transactions,omitempty"`
}

func (x *HandlingParamsComplaint) Reset() {
	*x = HandlingParamsComplaint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lea_unified_lea_comms_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandlingParamsComplaint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandlingParamsComplaint) ProtoMessage() {}

func (x *HandlingParamsComplaint) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lea_unified_lea_comms_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandlingParamsComplaint.ProtoReflect.Descriptor instead.
func (*HandlingParamsComplaint) Descriptor() ([]byte, []int) {
	return file_api_risk_lea_unified_lea_comms_proto_rawDescGZIP(), []int{0, 0}
}

func (x *HandlingParamsComplaint) GetComplaintId() string {
	if x != nil {
		return x.ComplaintId
	}
	return ""
}

func (x *HandlingParamsComplaint) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *HandlingParamsComplaint) GetContactDetails() *ReporterContactDetails {
	if x != nil {
		return x.ContactDetails
	}
	return nil
}

func (x *HandlingParamsComplaint) GetComplaintOriginState() string {
	if x != nil {
		return x.ComplaintOriginState
	}
	return ""
}

func (x *HandlingParamsComplaint) GetDisputedTransactions() *DisputedTransactions {
	if x != nil {
		return x.DisputedTransactions
	}
	return nil
}

var File_api_risk_lea_unified_lea_comms_proto protoreflect.FileDescriptor

var file_api_risk_lea_unified_lea_comms_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x65, 0x61, 0x2f, 0x75,
	0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x72, 0x69, 0x73, 0x6b, 0x1a, 0x23, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x65, 0x61, 0x2f, 0x75, 0x6e, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x5f, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c,
	0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6e, 0x75, 0x6c, 0x6c, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa7, 0x07, 0x0a, 0x0e, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x12, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x11, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x6c, 0x69,
	0x65, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0f, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x41, 0x70, 0x70, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x12, 0x3e, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e,
	0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x73,
	0x65, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0f, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x73,
	0x12, 0x50, 0x0a, 0x14, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x61, 0x6b, 0x65, 0x6e,
	0x5f, 0x62, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x11, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x6b, 0x65, 0x6e, 0x42, 0x79, 0x42, 0x61,
	0x6e, 0x6b, 0x12, 0x24, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x10, 0x72, 0x75, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x0f, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x12, 0x39, 0x0a, 0x0c, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e, 0x75,
	0x6c, 0x6c, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x0b, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x1a, 0xac, 0x02, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x45, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x34, 0x0a, 0x16,
	0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x4f, 0x0a, 0x15, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65,
	0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x14, 0x64,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x42, 0x4a, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x65, 0x61, 0x5a, 0x23, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x65, 0x61, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_lea_unified_lea_comms_proto_rawDescOnce sync.Once
	file_api_risk_lea_unified_lea_comms_proto_rawDescData = file_api_risk_lea_unified_lea_comms_proto_rawDesc
)

func file_api_risk_lea_unified_lea_comms_proto_rawDescGZIP() []byte {
	file_api_risk_lea_unified_lea_comms_proto_rawDescOnce.Do(func() {
		file_api_risk_lea_unified_lea_comms_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_lea_unified_lea_comms_proto_rawDescData)
	})
	return file_api_risk_lea_unified_lea_comms_proto_rawDescData
}

var file_api_risk_lea_unified_lea_comms_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_risk_lea_unified_lea_comms_proto_goTypes = []interface{}{
	(*HandlingParams)(nil),                 // 0: risk.HandlingParams
	(*HandlingParamsComplaint)(nil),        // 1: risk.HandlingParams.complaint
	(enums.AccountFreezeStatus)(0),         // 2: enums.AccountFreezeStatus
	(*money.Money)(nil),                    // 3: google.type.Money
	(common.BooleanEnum)(0),                // 4: api.typesv2.common.BooleanEnum
	(*case_management.RuleIdentifier)(nil), // 5: risk.case_management.RuleIdentifier
	(*typesv2.NullInt32)(nil),              // 6: api.typesv2.NullInt32
	(*timestamppb.Timestamp)(nil),          // 7: google.protobuf.Timestamp
	(*ReporterContactDetails)(nil),         // 8: risk.ReporterContactDetails
	(*DisputedTransactions)(nil),           // 9: risk.DisputedTransactions
}
var file_api_risk_lea_unified_lea_comms_proto_depIdxs = []int32{
	2, // 0: risk.HandlingParams.operational_status:type_name -> enums.AccountFreezeStatus
	3, // 1: risk.HandlingParams.lien_amount:type_name -> google.type.Money
	1, // 2: risk.HandlingParams.complaints:type_name -> risk.HandlingParams.complaint
	4, // 3: risk.HandlingParams.action_taken_by_bank:type_name -> api.typesv2.common.BooleanEnum
	5, // 4: risk.HandlingParams.rule_identifiers:type_name -> risk.case_management.RuleIdentifier
	6, // 5: risk.HandlingParams.layer_number:type_name -> api.typesv2.NullInt32
	7, // 6: risk.HandlingParams.complaint.date:type_name -> google.protobuf.Timestamp
	8, // 7: risk.HandlingParams.complaint.contact_details:type_name -> risk.ReporterContactDetails
	9, // 8: risk.HandlingParams.complaint.disputed_transactions:type_name -> risk.DisputedTransactions
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_api_risk_lea_unified_lea_comms_proto_init() }
func file_api_risk_lea_unified_lea_comms_proto_init() {
	if File_api_risk_lea_unified_lea_comms_proto != nil {
		return
	}
	file_api_risk_lea_unified_lea_complaint_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_lea_unified_lea_comms_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandlingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lea_unified_lea_comms_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandlingParamsComplaint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_lea_unified_lea_comms_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_lea_unified_lea_comms_proto_goTypes,
		DependencyIndexes: file_api_risk_lea_unified_lea_comms_proto_depIdxs,
		MessageInfos:      file_api_risk_lea_unified_lea_comms_proto_msgTypes,
	}.Build()
	File_api_risk_lea_unified_lea_comms_proto = out.File
	file_api_risk_lea_unified_lea_comms_proto_rawDesc = nil
	file_api_risk_lea_unified_lea_comms_proto_goTypes = nil
	file_api_risk_lea_unified_lea_comms_proto_depIdxs = nil
}
