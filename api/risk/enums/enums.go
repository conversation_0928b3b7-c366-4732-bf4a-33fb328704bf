package enums

import (
	"errors"

	"github.com/samber/lo"

	commspb "github.com/epifi/gamma/api/comms"
)

var (
	ActionNotSupportedErr       = errors.New("unknown action")
	commsFreezeEmailTemplateMap = map[CommsTemplate]commspb.EmailType{
		CommsTemplate_COMMS_TEMPLATE_EMAIL_LEA_C_F:       commspb.EmailType_RISKOPS_USER_LEA_FULL_FREEZE,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_M_KYC:         commspb.EmailType_RISKOPS_USER_M_KYC,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_H_KYC:         commspb.EmailType_RISKOPS_USER_HIGH_RISK_ACTIVITY_KYC,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_LEA_E_F:       commspb.EmailType_RISKOPS_USER_LEA_FULL_FREEZE,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_H_TM:          commspb.EmailType_RISKOPS_USER_TXN_MONITOR_SOW,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_L_TM:          commspb.EmailType_RISKOPS_USER_LOW_RISK_ACTIVITY,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_TF_CF:         commspb.EmailType_RISKOPS_USER_TOTAL_TO_CREDIT_FREEZE,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_CF:            commspb.EmailType_RISKOPS_USER_ONBOARDING_RISK_CF,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_TF_C:          commspb.EmailType_RISKOPS_USER_TOTAL_TO_CREDIT_FREEZE,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_M_CF:          commspb.EmailType_RISKOPS_USER_MEDIUM_RISK_CF,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_AFU_F:         commspb.EmailType_RISKOPS_USER_AFU_F,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_INT_ATM:       commspb.EmailType_RISKOPS_USER_INT_ATM,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_SD:            commspb.EmailType_RISKOPS_USER_SD,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_CL_AC:         commspb.EmailType_RISKOPS_USER_CL_AC,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_ONB_DENIED:    commspb.EmailType_RISKOPS_USER_ONB_DENIED,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_H_P_KYC:       commspb.EmailType_RISKOPS_USER_H_P_KYC,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_INT_ATM_C:     commspb.EmailType_RISKOPS_USER_INT_ATM_C,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_TM_RULE_1:     commspb.EmailType_RISKOPS_USER_TM_RULE_1,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_TM_RULE_2:     commspb.EmailType_RISKOPS_USER_TM_RULE_2,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_TM_RULE_3:     commspb.EmailType_RISKOPS_USER_TM_RULE_3,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_INACTIVITY_CF: commspb.EmailType_RISKOPS_USER_INACTIVITY_CREDIT_FREEZE,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_ACC_DF:        commspb.EmailType_RISKOPS_USER_ACC_DF,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_VKYC_CF:       commspb.EmailType_RISKOPS_USER_VKYC_CF,
		CommsTemplate_COMMS_TEMPLATE_EMAIL_M_KYC_PCF:     commspb.EmailType_RISKOPS_USER_M_KYC_PCF,
	}

	commsUnFreezeEmailTemplateMap = map[CommsTemplate]commspb.EmailType{
		CommsTemplate_COMMS_TEMPLATE_EMAIL_ACC_UNF: commspb.EmailType_RISKOPS_USER_ACC_UNFREEZE,
		CommsTemplate_COMMS_TEMPLATE_ACC_UNF_CL:    commspb.EmailType_RISKOPS_USER_ACC_UNF_CL,
	}
)

func (c CommsTemplate) ToCommsEmailTemplate() commspb.EmailType {
	return commsFreezeEmailTemplateMap[c]
}

func (c CommsTemplate) ToCommsUnfreezeEmailTemplate() commspb.EmailType {
	return commsUnFreezeEmailTemplateMap[c]
}

func (s State) IsSuccess() bool {
	return s == State_STATE_SUCCESS || s == State_STATE_SUCCESS_MANUAL_OVERRIDE
}

func (s State) IsTerminal() bool {
	return s == State_STATE_SUCCESS || s == State_STATE_REFUSED || s == State_STATE_CANCELED ||
		s == State_STATE_SUCCESS_MANUAL_OVERRIDE || s == State_STATE_REJECT_MANUAL_OVERRIDE
}

func (s State) IsManualState() bool {
	return s == State_STATE_SUCCESS_MANUAL_OVERRIDE || s == State_STATE_REJECT_MANUAL_OVERRIDE
}

func (s State) IsIntermediate() bool {
	return !s.IsTerminal()
}

func GetInProgressStates() []State {
	return []State{
		State_STATE_SENT_TO_BANK,
		State_STATE_INITIATED,
	}
}

func (a Action) IsFreezeAction() bool {
	return lo.Contains(GetFreezeActions(), a)
}

func GetFreezeActions() []Action {
	return []Action{
		Action_ACTION_CREDIT_FREEZE,
		Action_ACTION_FULL_FREEZE,
		Action_ACTION_DEBIT_FREEZE,
	}
}

// GetAccountFreezeStatusAfterAction returns account freeze status after applying action, throws error for unknown action types
func (s AccountFreezeStatus) GetAccountFreezeStatusAfterAction(action Action) (AccountFreezeStatus, error) {
	switch action {
	case Action_ACTION_UNFREEZE:
		return AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_UNFROZEN, nil
	case Action_ACTION_FULL_FREEZE:
		return AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE, nil
	case Action_ACTION_CREDIT_FREEZE:
		return AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE, nil
	case Action_ACTION_DEBIT_FREEZE:
		return AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE, nil
	default:
		return s, ActionNotSupportedErr
	}
}

func (s AccountFreezeStatus) ToAction() Action {
	switch s {
	case AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE:
		return Action_ACTION_FULL_FREEZE
	case AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_UNFROZEN:
		return Action_ACTION_UNFREEZE
	case AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE:
		return Action_ACTION_CREDIT_FREEZE
	case AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE:
		return Action_ACTION_DEBIT_FREEZE
	default:
		return Action_ACTION_UNSPECIFIED
	}
}

func (s AccountFreezeStatus) IsFreezeAction() bool {
	switch s {
	case AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE, AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE,
		AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE:
		return true
	default:
		return false
	}
}
