//go:generate gen_sql -types=ScreenerCriteria,ScreenerAction,LEAReportOrigin,DisputeState,DisputeVerdict

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/enums/enums.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Action is the flow current activity leads to
type Action int32

const (
	Action_ACTION_UNSPECIFIED   Action = 0
	Action_ACTION_FULL_FREEZE   Action = 1
	Action_ACTION_CREDIT_FREEZE Action = 2
	Action_ACTION_UNFREEZE      Action = 3
	Action_ACTION_DEBIT_FREEZE  Action = 4
	Action_ACTION_LIEN          Action = 5
)

// Enum value maps for Action.
var (
	Action_name = map[int32]string{
		0: "ACTION_UNSPECIFIED",
		1: "ACTION_FULL_FREEZE",
		2: "ACTION_CREDIT_FREEZE",
		3: "ACTION_UNFREEZE",
		4: "ACTION_DEBIT_FREEZE",
		5: "ACTION_LIEN",
	}
	Action_value = map[string]int32{
		"ACTION_UNSPECIFIED":   0,
		"ACTION_FULL_FREEZE":   1,
		"ACTION_CREDIT_FREEZE": 2,
		"ACTION_UNFREEZE":      3,
		"ACTION_DEBIT_FREEZE":  4,
		"ACTION_LIEN":          5,
	}
)

func (x Action) Enum() *Action {
	p := new(Action)
	*p = x
	return p
}

func (x Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Action) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[0].Descriptor()
}

func (Action) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[0]
}

func (x Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Action.Descriptor instead.
func (Action) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{0}
}

// State will determine what's the current user position is in the workflow for that Action
type State int32

const (
	State_STATE_UNSPECIFIED State = 0
	// db entry added to be processed, next step is to send to BANK
	State_STATE_INITIATED State = 1
	// mail/ comms triggered to bank for action on their side
	State_STATE_SENT_TO_BANK State = 2
	// action performed by bank AND fi : this marks a successful workflow
	State_STATE_SUCCESS State = 3
	// refused by bank, reason will reflect in failure_Reason
	State_STATE_REFUSED State = 4
	// manual intervention needed eg: state of user from bank doesn't change after 'x' retries
	State_STATE_MANUAL_INTERVENTION State = 5
	// cancelled workflow, no further steps to be taken for this
	State_STATE_CANCELED State = 6
	// At times, workflows may not go to terminal state as intended due to external dependencies e.g., vendor API failures
	// To overcome such limitations, manual overrides are supported
	// Below enum is the counter part of STATE_SUCCESS except that it is updated manually
	State_STATE_SUCCESS_MANUAL_OVERRIDE State = 7
	// At times we want to manually reject the workflow e.g., dev/ risk-ops human errors
	// To overcome such limitations, manual overrides are supported
	State_STATE_REJECT_MANUAL_OVERRIDE State = 8
	// failed to perform required action, reason will reflect in failure_reason
	State_STATE_FAILED State = 9
)

// Enum value maps for State.
var (
	State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "STATE_INITIATED",
		2: "STATE_SENT_TO_BANK",
		3: "STATE_SUCCESS",
		4: "STATE_REFUSED",
		5: "STATE_MANUAL_INTERVENTION",
		6: "STATE_CANCELED",
		7: "STATE_SUCCESS_MANUAL_OVERRIDE",
		8: "STATE_REJECT_MANUAL_OVERRIDE",
		9: "STATE_FAILED",
	}
	State_value = map[string]int32{
		"STATE_UNSPECIFIED":             0,
		"STATE_INITIATED":               1,
		"STATE_SENT_TO_BANK":            2,
		"STATE_SUCCESS":                 3,
		"STATE_REFUSED":                 4,
		"STATE_MANUAL_INTERVENTION":     5,
		"STATE_CANCELED":                6,
		"STATE_SUCCESS_MANUAL_OVERRIDE": 7,
		"STATE_REJECT_MANUAL_OVERRIDE":  8,
		"STATE_FAILED":                  9,
	}
)

func (x State) Enum() *State {
	p := new(State)
	*p = x
	return p
}

func (x State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (State) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[1].Descriptor()
}

func (State) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[1]
}

func (x State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use State.Descriptor instead.
func (State) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{1}
}

// Provenance: the beginning of something's existence; something's origin.
// e.g. FI (FI initiated), BANK (BANK initiated), LEA (Law enforcement agency initiated)
// This enum represents different entry provenance of request in the system
type Provenance int32

const (
	Provenance_PROVENANCE_UNSPECIFIED Provenance = 0
	Provenance_PROVENANCE_FI          Provenance = 1
	Provenance_PROVENANCE_BANK        Provenance = 2
	Provenance_PROVENANCE_LEA         Provenance = 3
)

// Enum value maps for Provenance.
var (
	Provenance_name = map[int32]string{
		0: "PROVENANCE_UNSPECIFIED",
		1: "PROVENANCE_FI",
		2: "PROVENANCE_BANK",
		3: "PROVENANCE_LEA",
	}
	Provenance_value = map[string]int32{
		"PROVENANCE_UNSPECIFIED": 0,
		"PROVENANCE_FI":          1,
		"PROVENANCE_BANK":        2,
		"PROVENANCE_LEA":         3,
	}
)

func (x Provenance) Enum() *Provenance {
	p := new(Provenance)
	*p = x
	return p
}

func (x Provenance) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Provenance) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[2].Descriptor()
}

func (Provenance) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[2]
}

func (x Provenance) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Provenance.Descriptor instead.
func (Provenance) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{2}
}

type RequestReason int32

const (
	RequestReason_REQUEST_REASON_UNSPECIFIED        RequestReason = 0
	RequestReason_REQUEST_REASON_FRAUDULENT_ACCOUNT RequestReason = 1
	// For other remarks will contain detailed remark
	RequestReason_REQUEST_REASON_OTHER RequestReason = 2
	// freeze reason: kyc issues like face match, liveness, name match
	RequestReason_REQUEST_REASON_CORE_KYC_ISSUE RequestReason = 3
	// freeze reason: suspicious email patterns, AA info mismatch, location based issues
	// and also if data analytics has pointed a user as risky
	RequestReason_REQUEST_REASON_PROFILE_INDICATORS RequestReason = 4
	// freeze reason: penny drop abuse as name suggests
	RequestReason_REQUEST_REASON_PENNY_DROP_ABUSE RequestReason = 5
	// freeze reason: received funds from known fraudsters
	RequestReason_REQUEST_REASON_RECEIVED_FUNDS_FROM_FRAUDSTERS RequestReason = 6
	// freeze reason: high number of withdrawals from atms
	RequestReason_REQUEST_REASON_HIGH_ATM_WITHDRAWAL_COUNT RequestReason = 7
	// freeze reason: intersection rule in transaction monitoring
	RequestReason_REQUEST_REASON_TM_ALERT RequestReason = 8
	// freeze reason: transaction monitoring profile mismatch
	RequestReason_REQUEST_REASON_TM_PROFILE_MISMATCH RequestReason = 9
	// unfreeze reason: after due diligence
	RequestReason_REQUEST_REASON_DUE_DILIGENCE RequestReason = 10
	// unfreeze reason: after customer outcall
	RequestReason_REQUEST_REASON_CUSTOMER_OUTCALL RequestReason = 11
	// freeze reason: user's account is deleted since it was requested by user
	RequestReason_REQUEST_REASON_ACCOUNT_DELETION_REQUEST RequestReason = 12
	// freeze reason: denotes user's account is closed due to min kyc expiry
	RequestReason_REQUEST_REASON_MIN_KYC_ACCOUNT_EXPIRY RequestReason = 13
	// account frozen under LEA
	RequestReason_REQUEST_REASON_LEA_COMPLAINT RequestReason = 14
	// account inquired under LEA
	RequestReason_REQUEST_REASON_LEA_ENQUIRY RequestReason = 15
	// account frozen under NPCI
	RequestReason_REQUEST_REASON_NPCI_COMPLAINT RequestReason = 16
	// account frozen under FEDERAL rules
	RequestReason_REQUEST_REASON_FEDERAL_RULES RequestReason = 17
	// unfreeze reason: LEA
	RequestReason_REQUEST_REASON_LEA_UNFREEZE RequestReason = 18
	// freeze reason : LSO user having vkyc pending
	RequestReason_REQUEST_REASON_LSO_USER_VKYC_PENDING RequestReason = 19
	// freeze reason: block onboarding (no comms triggered)
	RequestReason_REQUEST_REASON_BLOCK_ONBOARDING RequestReason = 20
	// unfreeze reason to allow usage for cc/pl
	RequestReason_REQUEST_REASON_CC_OR_PL_USER RequestReason = 21
	// unfreeze reason: False Positive
	RequestReason_REQUEST_REASON_FALSE_POSITIVE RequestReason = 22
	// freeze reason: account was inactive for a certain period of time
	RequestReason_REQUEST_REASON_INACTIVITY RequestReason = 23
	// freeze reason: users who were part of revkyc queue and did not complete re vkyc
	RequestReason_REQUEST_REASON_RE_VKYC_NOT_COMPLETED RequestReason = 24
)

// Enum value maps for RequestReason.
var (
	RequestReason_name = map[int32]string{
		0:  "REQUEST_REASON_UNSPECIFIED",
		1:  "REQUEST_REASON_FRAUDULENT_ACCOUNT",
		2:  "REQUEST_REASON_OTHER",
		3:  "REQUEST_REASON_CORE_KYC_ISSUE",
		4:  "REQUEST_REASON_PROFILE_INDICATORS",
		5:  "REQUEST_REASON_PENNY_DROP_ABUSE",
		6:  "REQUEST_REASON_RECEIVED_FUNDS_FROM_FRAUDSTERS",
		7:  "REQUEST_REASON_HIGH_ATM_WITHDRAWAL_COUNT",
		8:  "REQUEST_REASON_TM_ALERT",
		9:  "REQUEST_REASON_TM_PROFILE_MISMATCH",
		10: "REQUEST_REASON_DUE_DILIGENCE",
		11: "REQUEST_REASON_CUSTOMER_OUTCALL",
		12: "REQUEST_REASON_ACCOUNT_DELETION_REQUEST",
		13: "REQUEST_REASON_MIN_KYC_ACCOUNT_EXPIRY",
		14: "REQUEST_REASON_LEA_COMPLAINT",
		15: "REQUEST_REASON_LEA_ENQUIRY",
		16: "REQUEST_REASON_NPCI_COMPLAINT",
		17: "REQUEST_REASON_FEDERAL_RULES",
		18: "REQUEST_REASON_LEA_UNFREEZE",
		19: "REQUEST_REASON_LSO_USER_VKYC_PENDING",
		20: "REQUEST_REASON_BLOCK_ONBOARDING",
		21: "REQUEST_REASON_CC_OR_PL_USER",
		22: "REQUEST_REASON_FALSE_POSITIVE",
		23: "REQUEST_REASON_INACTIVITY",
		24: "REQUEST_REASON_RE_VKYC_NOT_COMPLETED",
	}
	RequestReason_value = map[string]int32{
		"REQUEST_REASON_UNSPECIFIED":                    0,
		"REQUEST_REASON_FRAUDULENT_ACCOUNT":             1,
		"REQUEST_REASON_OTHER":                          2,
		"REQUEST_REASON_CORE_KYC_ISSUE":                 3,
		"REQUEST_REASON_PROFILE_INDICATORS":             4,
		"REQUEST_REASON_PENNY_DROP_ABUSE":               5,
		"REQUEST_REASON_RECEIVED_FUNDS_FROM_FRAUDSTERS": 6,
		"REQUEST_REASON_HIGH_ATM_WITHDRAWAL_COUNT":      7,
		"REQUEST_REASON_TM_ALERT":                       8,
		"REQUEST_REASON_TM_PROFILE_MISMATCH":            9,
		"REQUEST_REASON_DUE_DILIGENCE":                  10,
		"REQUEST_REASON_CUSTOMER_OUTCALL":               11,
		"REQUEST_REASON_ACCOUNT_DELETION_REQUEST":       12,
		"REQUEST_REASON_MIN_KYC_ACCOUNT_EXPIRY":         13,
		"REQUEST_REASON_LEA_COMPLAINT":                  14,
		"REQUEST_REASON_LEA_ENQUIRY":                    15,
		"REQUEST_REASON_NPCI_COMPLAINT":                 16,
		"REQUEST_REASON_FEDERAL_RULES":                  17,
		"REQUEST_REASON_LEA_UNFREEZE":                   18,
		"REQUEST_REASON_LSO_USER_VKYC_PENDING":          19,
		"REQUEST_REASON_BLOCK_ONBOARDING":               20,
		"REQUEST_REASON_CC_OR_PL_USER":                  21,
		"REQUEST_REASON_FALSE_POSITIVE":                 22,
		"REQUEST_REASON_INACTIVITY":                     23,
		"REQUEST_REASON_RE_VKYC_NOT_COMPLETED":          24,
	}
)

func (x RequestReason) Enum() *RequestReason {
	p := new(RequestReason)
	*p = x
	return p
}

func (x RequestReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RequestReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[3].Descriptor()
}

func (RequestReason) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[3]
}

func (x RequestReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RequestReason.Descriptor instead.
func (RequestReason) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{3}
}

// exceptions that can occur in workflow
// states reason why overall state machine failed
// https://docs.google.com/document/d/1aWbyWW36X5STm2a3v8WuZWImbuazJPltFIcLvmnEe54/edit#bookmark=id.cvbvozdz65b8
type FailureReason int32

const (
	FailureReason_FAILURE_REASON_UNSPECIFIED                     FailureReason = 0
	FailureReason_FAILURE_REASON_DROP_DEDUPE_ALREADY_MARKED      FailureReason = 1
	FailureReason_FAILURE_REASON_DROP_DEDUPE_ALREADY_SENT        FailureReason = 2
	FailureReason_FAILURE_REASON_DROP_DEDUPE_ALREADY_FROZEN      FailureReason = 3
	FailureReason_FAILURE_REASON_DROP_DEDUPE_LEA_FROZEN          FailureReason = 4
	FailureReason_FAILURE_REASON_DROP_CONFLICT_SENT_FOR_UNFREEZE FailureReason = 5
	FailureReason_FAILURE_REASON_CANCELLED                       FailureReason = 6
	// the requested action failed as account is closed
	FailureReason_FAILURE_REASON_ACCOUNT_CLOSED FailureReason = 7
	// the requested action failed as there is a permanent input mismatch in bank API
	// e.g. in account status FEDERAL API if there is mismatch in mobile number a permanent error is returned
	FailureReason_FAILURE_REASON_ACCOUNT_STATUS_INPUT_MISMATCH FailureReason = 8
)

// Enum value maps for FailureReason.
var (
	FailureReason_name = map[int32]string{
		0: "FAILURE_REASON_UNSPECIFIED",
		1: "FAILURE_REASON_DROP_DEDUPE_ALREADY_MARKED",
		2: "FAILURE_REASON_DROP_DEDUPE_ALREADY_SENT",
		3: "FAILURE_REASON_DROP_DEDUPE_ALREADY_FROZEN",
		4: "FAILURE_REASON_DROP_DEDUPE_LEA_FROZEN",
		5: "FAILURE_REASON_DROP_CONFLICT_SENT_FOR_UNFREEZE",
		6: "FAILURE_REASON_CANCELLED",
		7: "FAILURE_REASON_ACCOUNT_CLOSED",
		8: "FAILURE_REASON_ACCOUNT_STATUS_INPUT_MISMATCH",
	}
	FailureReason_value = map[string]int32{
		"FAILURE_REASON_UNSPECIFIED":                     0,
		"FAILURE_REASON_DROP_DEDUPE_ALREADY_MARKED":      1,
		"FAILURE_REASON_DROP_DEDUPE_ALREADY_SENT":        2,
		"FAILURE_REASON_DROP_DEDUPE_ALREADY_FROZEN":      3,
		"FAILURE_REASON_DROP_DEDUPE_LEA_FROZEN":          4,
		"FAILURE_REASON_DROP_CONFLICT_SENT_FOR_UNFREEZE": 5,
		"FAILURE_REASON_CANCELLED":                       6,
		"FAILURE_REASON_ACCOUNT_CLOSED":                  7,
		"FAILURE_REASON_ACCOUNT_STATUS_INPUT_MISMATCH":   8,
	}
)

func (x FailureReason) Enum() *FailureReason {
	p := new(FailureReason)
	*p = x
	return p
}

func (x FailureReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FailureReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[4].Descriptor()
}

func (FailureReason) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[4]
}

func (x FailureReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FailureReason.Descriptor instead.
func (FailureReason) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{4}
}

// ENUMS for comms email trigger
// reason for keeping this as seperate entity
// as comms for user isn't tightly coupled
// with reason for block/ unblock
// https://docs.google.com/document/d/1Pvm5zHYTaG25jzFNB4zZvlrFaX_SU5ziKN883dNPlw8/edit#
type CommsTemplate int32

const (
	CommsTemplate_COMMS_TEMPLATE_UNSPECIFIED         CommsTemplate = 0
	CommsTemplate_COMMS_TEMPLATE_EMAIL_LEA_C_F       CommsTemplate = 1
	CommsTemplate_COMMS_TEMPLATE_EMAIL_LEA_E_F       CommsTemplate = 2
	CommsTemplate_COMMS_TEMPLATE_EMAIL_H_KYC         CommsTemplate = 3
	CommsTemplate_COMMS_TEMPLATE_EMAIL_M_KYC         CommsTemplate = 4
	CommsTemplate_COMMS_TEMPLATE_EMAIL_H_TM          CommsTemplate = 5
	CommsTemplate_COMMS_TEMPLATE_EMAIL_L_TM          CommsTemplate = 6
	CommsTemplate_COMMS_TEMPLATE_EMAIL_TF_CF         CommsTemplate = 7
	CommsTemplate_COMMS_TEMPLATE_EMAIL_TF_C          CommsTemplate = 8
	CommsTemplate_COMMS_TEMPLATE_EMAIL_ACC_UNF       CommsTemplate = 9
	CommsTemplate_COMMS_TEMPLATE_EMAIL_CF            CommsTemplate = 10
	CommsTemplate_COMMS_TEMPLATE_EMAIL_AFU_F         CommsTemplate = 11
	CommsTemplate_COMMS_TEMPLATE_EMAIL_INT_ATM       CommsTemplate = 12
	CommsTemplate_COMMS_TEMPLATE_EMAIL_SD            CommsTemplate = 13
	CommsTemplate_COMMS_TEMPLATE_EMAIL_M_CF          CommsTemplate = 14
	CommsTemplate_COMMS_TEMPLATE_ACC_UNF_CL          CommsTemplate = 15
	CommsTemplate_COMMS_TEMPLATE_EMAIL_CL_AC         CommsTemplate = 16
	CommsTemplate_COMMS_TEMPLATE_EMAIL_ONB_DENIED    CommsTemplate = 17
	CommsTemplate_COMMS_TEMPLATE_EMAIL_H_P_KYC       CommsTemplate = 18
	CommsTemplate_COMMS_TEMPLATE_EMAIL_INT_ATM_C     CommsTemplate = 19
	CommsTemplate_COMMS_TEMPLATE_EMAIL_TM_RULE_1     CommsTemplate = 20
	CommsTemplate_COMMS_TEMPLATE_EMAIL_TM_RULE_2     CommsTemplate = 21
	CommsTemplate_COMMS_TEMPLATE_EMAIL_TM_RULE_3     CommsTemplate = 22
	CommsTemplate_COMMS_TEMPLATE_EMAIL_INACTIVITY_CF CommsTemplate = 23
	CommsTemplate_COMMS_TEMPLATE_EMAIL_ACC_DF        CommsTemplate = 24
	CommsTemplate_COMMS_TEMPLATE_EMAIL_VKYC_CF       CommsTemplate = 25
	CommsTemplate_COMMS_TEMPLATE_EMAIL_M_KYC_PCF     CommsTemplate = 26
)

// Enum value maps for CommsTemplate.
var (
	CommsTemplate_name = map[int32]string{
		0:  "COMMS_TEMPLATE_UNSPECIFIED",
		1:  "COMMS_TEMPLATE_EMAIL_LEA_C_F",
		2:  "COMMS_TEMPLATE_EMAIL_LEA_E_F",
		3:  "COMMS_TEMPLATE_EMAIL_H_KYC",
		4:  "COMMS_TEMPLATE_EMAIL_M_KYC",
		5:  "COMMS_TEMPLATE_EMAIL_H_TM",
		6:  "COMMS_TEMPLATE_EMAIL_L_TM",
		7:  "COMMS_TEMPLATE_EMAIL_TF_CF",
		8:  "COMMS_TEMPLATE_EMAIL_TF_C",
		9:  "COMMS_TEMPLATE_EMAIL_ACC_UNF",
		10: "COMMS_TEMPLATE_EMAIL_CF",
		11: "COMMS_TEMPLATE_EMAIL_AFU_F",
		12: "COMMS_TEMPLATE_EMAIL_INT_ATM",
		13: "COMMS_TEMPLATE_EMAIL_SD",
		14: "COMMS_TEMPLATE_EMAIL_M_CF",
		15: "COMMS_TEMPLATE_ACC_UNF_CL",
		16: "COMMS_TEMPLATE_EMAIL_CL_AC",
		17: "COMMS_TEMPLATE_EMAIL_ONB_DENIED",
		18: "COMMS_TEMPLATE_EMAIL_H_P_KYC",
		19: "COMMS_TEMPLATE_EMAIL_INT_ATM_C",
		20: "COMMS_TEMPLATE_EMAIL_TM_RULE_1",
		21: "COMMS_TEMPLATE_EMAIL_TM_RULE_2",
		22: "COMMS_TEMPLATE_EMAIL_TM_RULE_3",
		23: "COMMS_TEMPLATE_EMAIL_INACTIVITY_CF",
		24: "COMMS_TEMPLATE_EMAIL_ACC_DF",
		25: "COMMS_TEMPLATE_EMAIL_VKYC_CF",
		26: "COMMS_TEMPLATE_EMAIL_M_KYC_PCF",
	}
	CommsTemplate_value = map[string]int32{
		"COMMS_TEMPLATE_UNSPECIFIED":         0,
		"COMMS_TEMPLATE_EMAIL_LEA_C_F":       1,
		"COMMS_TEMPLATE_EMAIL_LEA_E_F":       2,
		"COMMS_TEMPLATE_EMAIL_H_KYC":         3,
		"COMMS_TEMPLATE_EMAIL_M_KYC":         4,
		"COMMS_TEMPLATE_EMAIL_H_TM":          5,
		"COMMS_TEMPLATE_EMAIL_L_TM":          6,
		"COMMS_TEMPLATE_EMAIL_TF_CF":         7,
		"COMMS_TEMPLATE_EMAIL_TF_C":          8,
		"COMMS_TEMPLATE_EMAIL_ACC_UNF":       9,
		"COMMS_TEMPLATE_EMAIL_CF":            10,
		"COMMS_TEMPLATE_EMAIL_AFU_F":         11,
		"COMMS_TEMPLATE_EMAIL_INT_ATM":       12,
		"COMMS_TEMPLATE_EMAIL_SD":            13,
		"COMMS_TEMPLATE_EMAIL_M_CF":          14,
		"COMMS_TEMPLATE_ACC_UNF_CL":          15,
		"COMMS_TEMPLATE_EMAIL_CL_AC":         16,
		"COMMS_TEMPLATE_EMAIL_ONB_DENIED":    17,
		"COMMS_TEMPLATE_EMAIL_H_P_KYC":       18,
		"COMMS_TEMPLATE_EMAIL_INT_ATM_C":     19,
		"COMMS_TEMPLATE_EMAIL_TM_RULE_1":     20,
		"COMMS_TEMPLATE_EMAIL_TM_RULE_2":     21,
		"COMMS_TEMPLATE_EMAIL_TM_RULE_3":     22,
		"COMMS_TEMPLATE_EMAIL_INACTIVITY_CF": 23,
		"COMMS_TEMPLATE_EMAIL_ACC_DF":        24,
		"COMMS_TEMPLATE_EMAIL_VKYC_CF":       25,
		"COMMS_TEMPLATE_EMAIL_M_KYC_PCF":     26,
	}
)

func (x CommsTemplate) Enum() *CommsTemplate {
	p := new(CommsTemplate)
	*p = x
	return p
}

func (x CommsTemplate) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommsTemplate) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[5].Descriptor()
}

func (CommsTemplate) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[5]
}

func (x CommsTemplate) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommsTemplate.Descriptor instead.
func (CommsTemplate) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{5}
}

// EntityType is the object sent to Risk Evaluators.
// These are usually a mix of multiple entities like customer can be mix of actor, user, liveness_scores etc.
type EntityType int32

const (
	EntityType_ENTITY_TYPE_UNSPECIFIED     EntityType = 0
	EntityType_ENTITY_TYPE_CUSTOMER        EntityType = 1
	EntityType_ENTITY_TYPE_ACCOUNT         EntityType = 2
	EntityType_ENTITY_TYPE_PAYMENT_ADDRESS EntityType = 3
	EntityType_ENTITY_TYPE_TRANSACTION     EntityType = 4
	EntityType_ENTITY_TYPE_CONTROL_LIST    EntityType = 5
)

// Enum value maps for EntityType.
var (
	EntityType_name = map[int32]string{
		0: "ENTITY_TYPE_UNSPECIFIED",
		1: "ENTITY_TYPE_CUSTOMER",
		2: "ENTITY_TYPE_ACCOUNT",
		3: "ENTITY_TYPE_PAYMENT_ADDRESS",
		4: "ENTITY_TYPE_TRANSACTION",
		5: "ENTITY_TYPE_CONTROL_LIST",
	}
	EntityType_value = map[string]int32{
		"ENTITY_TYPE_UNSPECIFIED":     0,
		"ENTITY_TYPE_CUSTOMER":        1,
		"ENTITY_TYPE_ACCOUNT":         2,
		"ENTITY_TYPE_PAYMENT_ADDRESS": 3,
		"ENTITY_TYPE_TRANSACTION":     4,
		"ENTITY_TYPE_CONTROL_LIST":    5,
	}
)

func (x EntityType) Enum() *EntityType {
	p := new(EntityType)
	*p = x
	return p
}

func (x EntityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EntityType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[6].Descriptor()
}

func (EntityType) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[6]
}

func (x EntityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EntityType.Descriptor instead.
func (EntityType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{6}
}

// AccountFreezeStatus signifies actual account status (restrictions)
type AccountFreezeStatus int32

const (
	AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_UNSPECIFIED AccountFreezeStatus = 0
	// User account was found fraudulent and total freeze was applied on the account
	AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE AccountFreezeStatus = 1
	// Account is restricted by credit freeze
	AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE AccountFreezeStatus = 2
	// Account is restricted by debit freeze
	AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE AccountFreezeStatus = 3
	// There are no restrictions on the account.
	AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_UNFROZEN AccountFreezeStatus = 4
)

// Enum value maps for AccountFreezeStatus.
var (
	AccountFreezeStatus_name = map[int32]string{
		0: "ACCOUNT_FREEZE_STATUS_UNSPECIFIED",
		1: "ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE",
		2: "ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE",
		3: "ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE",
		4: "ACCOUNT_FREEZE_STATUS_UNFROZEN",
	}
	AccountFreezeStatus_value = map[string]int32{
		"ACCOUNT_FREEZE_STATUS_UNSPECIFIED":   0,
		"ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE":  1,
		"ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE": 2,
		"ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE":  3,
		"ACCOUNT_FREEZE_STATUS_UNFROZEN":      4,
	}
)

func (x AccountFreezeStatus) Enum() *AccountFreezeStatus {
	p := new(AccountFreezeStatus)
	*p = x
	return p
}

func (x AccountFreezeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountFreezeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[7].Descriptor()
}

func (AccountFreezeStatus) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[7]
}

func (x AccountFreezeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountFreezeStatus.Descriptor instead.
func (AccountFreezeStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{7}
}

// Enum to represent different criteria for which risk screener checks can be invoked
// Set of checks performed and threshold for failure could be different based on what purpose the screener is getting invoked for
// EX: checks(and threshold) required in fi-lite onboarding vs savings account onboarding will differ
type ScreenerCriteria int32

const (
	ScreenerCriteria_SCREENER_CRITERIA_UNSPECIFIED ScreenerCriteria = 0
	// user is onboarding onto fi savings account
	ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING ScreenerCriteria = 1
	// user is onboarding on app only for fi lite features
	ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING ScreenerCriteria = 2
	// user is reonboarding on the app
	ScreenerCriteria_SCREENER_CRITERIA_REOOBE ScreenerCriteria = 3
	// user is doing savings account onboarding via B2B salary flow
	ScreenerCriteria_SCREENER_CRITERIA_B2B_SAVINGS_ACCOUNT_ONBOARDING ScreenerCriteria = 4
	// user is onboarding for personal loans with liquiloans
	ScreenerCriteria_SCREENER_CRITERIA_LOANS_PL_FI_LITE_LIQUILOANS ScreenerCriteria = 5
	// user is onboarding for federal CREDIT CARD through fi lite flow
	ScreenerCriteria_SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_ONBOARDING ScreenerCriteria = 6
	// user has downloaded their credit report, and we are adding an offer in an async process
	ScreenerCriteria_SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_OFFER_CREATION ScreenerCriteria = 7
	// to be used for generic set of checks if the user is going through loans journey
	ScreenerCriteria_SCREENER_CRITERIA_LOANS_ONBOARDING ScreenerCriteria = 8
	// user is doing savings account onboarding with non-resident region
	ScreenerCriteria_SCREENER_CRITERIA_NR_SAVINGS_ACCOUNT_ONBOARDING ScreenerCriteria = 9
	// user is onboarding without savings account - direct to home
	ScreenerCriteria_SCREENER_CRITERIA_D2H_ONBOARDING ScreenerCriteria = 10
	// user onboarding for stock guardian loans
	ScreenerCriteria_SCREENER_CRITERIA_STOCK_GUARDIAN_LOANS ScreenerCriteria = 11
)

// Enum value maps for ScreenerCriteria.
var (
	ScreenerCriteria_name = map[int32]string{
		0:  "SCREENER_CRITERIA_UNSPECIFIED",
		1:  "SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING",
		2:  "SCREENER_CRITERIA_FI_LITE_ONBOARDING",
		3:  "SCREENER_CRITERIA_REOOBE",
		4:  "SCREENER_CRITERIA_B2B_SAVINGS_ACCOUNT_ONBOARDING",
		5:  "SCREENER_CRITERIA_LOANS_PL_FI_LITE_LIQUILOANS",
		6:  "SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_ONBOARDING",
		7:  "SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_OFFER_CREATION",
		8:  "SCREENER_CRITERIA_LOANS_ONBOARDING",
		9:  "SCREENER_CRITERIA_NR_SAVINGS_ACCOUNT_ONBOARDING",
		10: "SCREENER_CRITERIA_D2H_ONBOARDING",
		11: "SCREENER_CRITERIA_STOCK_GUARDIAN_LOANS",
	}
	ScreenerCriteria_value = map[string]int32{
		"SCREENER_CRITERIA_UNSPECIFIED":                        0,
		"SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING":         1,
		"SCREENER_CRITERIA_FI_LITE_ONBOARDING":                 2,
		"SCREENER_CRITERIA_REOOBE":                             3,
		"SCREENER_CRITERIA_B2B_SAVINGS_ACCOUNT_ONBOARDING":     4,
		"SCREENER_CRITERIA_LOANS_PL_FI_LITE_LIQUILOANS":        5,
		"SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_ONBOARDING":     6,
		"SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_OFFER_CREATION": 7,
		"SCREENER_CRITERIA_LOANS_ONBOARDING":                   8,
		"SCREENER_CRITERIA_NR_SAVINGS_ACCOUNT_ONBOARDING":      9,
		"SCREENER_CRITERIA_D2H_ONBOARDING":                     10,
		"SCREENER_CRITERIA_STOCK_GUARDIAN_LOANS":               11,
	}
)

func (x ScreenerCriteria) Enum() *ScreenerCriteria {
	p := new(ScreenerCriteria)
	*p = x
	return p
}

func (x ScreenerCriteria) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScreenerCriteria) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[8].Descriptor()
}

func (ScreenerCriteria) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[8]
}

func (x ScreenerCriteria) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScreenerCriteria.Descriptor instead.
func (ScreenerCriteria) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{8}
}

type ScreenerAction int32

const (
	ScreenerAction_SCREENER_ACTION_UNSPECIFIED ScreenerAction = 0
	// all the checks have passed and user can be moved to next stage
	ScreenerAction_SCREENER_ACTION_PASS ScreenerAction = 1
	// user has failed screener checks and needs to be manually reviewed before proceeding further
	ScreenerAction_SCREENER_ACTION_MANUAL_REVIEW ScreenerAction = 2
	// user has failed some critical screener checks and should be blocked from proceeding further
	ScreenerAction_SCREENER_ACTION_FAIL ScreenerAction = 3
)

// Enum value maps for ScreenerAction.
var (
	ScreenerAction_name = map[int32]string{
		0: "SCREENER_ACTION_UNSPECIFIED",
		1: "SCREENER_ACTION_PASS",
		2: "SCREENER_ACTION_MANUAL_REVIEW",
		3: "SCREENER_ACTION_FAIL",
	}
	ScreenerAction_value = map[string]int32{
		"SCREENER_ACTION_UNSPECIFIED":   0,
		"SCREENER_ACTION_PASS":          1,
		"SCREENER_ACTION_MANUAL_REVIEW": 2,
		"SCREENER_ACTION_FAIL":          3,
	}
)

func (x ScreenerAction) Enum() *ScreenerAction {
	p := new(ScreenerAction)
	*p = x
	return p
}

func (x ScreenerAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScreenerAction) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[9].Descriptor()
}

func (ScreenerAction) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[9]
}

func (x ScreenerAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScreenerAction.Descriptor instead.
func (ScreenerAction) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{9}
}

// Signifies risk level of the user against a risk check.
// These level can be used to take actions against the user such as
// fail or pass onboarding/re-onboarding attempt or send for manual review.
type RiskSeverity int32

const (
	RiskSeverity_RISK_SEVERITY_UNSPECIFIED RiskSeverity = 0
	RiskSeverity_RISK_SEVERITY_LOW         RiskSeverity = 1
	RiskSeverity_RISK_SEVERITY_MEDIUM      RiskSeverity = 2
	RiskSeverity_RISK_SEVERITY_HIGH        RiskSeverity = 3
	RiskSeverity_RISK_SEVERITY_CRITICAL    RiskSeverity = 4
)

// Enum value maps for RiskSeverity.
var (
	RiskSeverity_name = map[int32]string{
		0: "RISK_SEVERITY_UNSPECIFIED",
		1: "RISK_SEVERITY_LOW",
		2: "RISK_SEVERITY_MEDIUM",
		3: "RISK_SEVERITY_HIGH",
		4: "RISK_SEVERITY_CRITICAL",
	}
	RiskSeverity_value = map[string]int32{
		"RISK_SEVERITY_UNSPECIFIED": 0,
		"RISK_SEVERITY_LOW":         1,
		"RISK_SEVERITY_MEDIUM":      2,
		"RISK_SEVERITY_HIGH":        3,
		"RISK_SEVERITY_CRITICAL":    4,
	}
)

func (x RiskSeverity) Enum() *RiskSeverity {
	p := new(RiskSeverity)
	*p = x
	return p
}

func (x RiskSeverity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskSeverity) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[10].Descriptor()
}

func (RiskSeverity) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[10]
}

func (x RiskSeverity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskSeverity.Descriptor instead.
func (RiskSeverity) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{10}
}

// common enum to be used in cases where data freshness needs to be decided by clients for certain data
type DataFreshness int32

const (
	DataFreshness_DATA_FRESHNESS_UNSPECIFIED DataFreshness = 0
	// makes a fresh vendor call
	DataFreshness_DATA_FRESHNESS_REAL_TIME DataFreshness = 1
	// data is fetched from db if it not older than x mins, otherwise makes a vendor call
	// worst case staleness could be upto 10 mins
	DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME DataFreshness = 3
	// data is fetched from db if it is not older than x days, otherwise makes a vendor call
	// worst case staleness could be upto a week
	DataFreshness_DATA_FRESHNESS_RECENT DataFreshness = 2
	// data will be fetched from db only, return record not exist if data unavailable
	DataFreshness_DATA_FRESHNESS_LAST_KNOWN DataFreshness = 4
)

// Enum value maps for DataFreshness.
var (
	DataFreshness_name = map[int32]string{
		0: "DATA_FRESHNESS_UNSPECIFIED",
		1: "DATA_FRESHNESS_REAL_TIME",
		3: "DATA_FRESHNESS_NEAR_REAL_TIME",
		2: "DATA_FRESHNESS_RECENT",
		4: "DATA_FRESHNESS_LAST_KNOWN",
	}
	DataFreshness_value = map[string]int32{
		"DATA_FRESHNESS_UNSPECIFIED":    0,
		"DATA_FRESHNESS_REAL_TIME":      1,
		"DATA_FRESHNESS_NEAR_REAL_TIME": 3,
		"DATA_FRESHNESS_RECENT":         2,
		"DATA_FRESHNESS_LAST_KNOWN":     4,
	}
)

func (x DataFreshness) Enum() *DataFreshness {
	p := new(DataFreshness)
	*p = x
	return p
}

func (x DataFreshness) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataFreshness) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[11].Descriptor()
}

func (DataFreshness) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[11]
}

func (x DataFreshness) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataFreshness.Descriptor instead.
func (DataFreshness) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{11}
}

// enums to identify origin point for a LEA report
type LEAReportOrigin int32

const (
	// No proper origin
	LEAReportOrigin_LEA_REPORT_ORIGIN_UNSPECIFIED LEAReportOrigin = 0
	// Complaint on Fi accounts
	LEAReportOrigin_LEA_REPORT_ORIGIN_FI_FEDERAL LEAReportOrigin = 1
	// Complaint on Jupiter/other Fed account and not Fi
	LEAReportOrigin_LEA_REPORT_ORIGIN_FEDERAL LEAReportOrigin = 2
	// NPCI complaint
	LEAReportOrigin_LEA_REPORT_ORIGIN_NPCI LEAReportOrigin = 3
	// Complaints received through other banks
	LEAReportOrigin_LEA_REPORT_ORIGIN_OTHER_BANK LEAReportOrigin = 4
	// Complaints directly from police stations
	LEAReportOrigin_LEA_REPORT_ORIGIN_POLICE_STATION LEAReportOrigin = 5
	// National Cyber Crime Reporting Portal
	LEAReportOrigin_LEA_REPORT_ORIGIN_NCCRP LEAReportOrigin = 6
)

// Enum value maps for LEAReportOrigin.
var (
	LEAReportOrigin_name = map[int32]string{
		0: "LEA_REPORT_ORIGIN_UNSPECIFIED",
		1: "LEA_REPORT_ORIGIN_FI_FEDERAL",
		2: "LEA_REPORT_ORIGIN_FEDERAL",
		3: "LEA_REPORT_ORIGIN_NPCI",
		4: "LEA_REPORT_ORIGIN_OTHER_BANK",
		5: "LEA_REPORT_ORIGIN_POLICE_STATION",
		6: "LEA_REPORT_ORIGIN_NCCRP",
	}
	LEAReportOrigin_value = map[string]int32{
		"LEA_REPORT_ORIGIN_UNSPECIFIED":    0,
		"LEA_REPORT_ORIGIN_FI_FEDERAL":     1,
		"LEA_REPORT_ORIGIN_FEDERAL":        2,
		"LEA_REPORT_ORIGIN_NPCI":           3,
		"LEA_REPORT_ORIGIN_OTHER_BANK":     4,
		"LEA_REPORT_ORIGIN_POLICE_STATION": 5,
		"LEA_REPORT_ORIGIN_NCCRP":          6,
	}
)

func (x LEAReportOrigin) Enum() *LEAReportOrigin {
	p := new(LEAReportOrigin)
	*p = x
	return p
}

func (x LEAReportOrigin) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LEAReportOrigin) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[12].Descriptor()
}

func (LEAReportOrigin) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[12]
}

func (x LEAReportOrigin) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LEAReportOrigin.Descriptor instead.
func (LEAReportOrigin) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{12}
}

// Current state of dispute.
type DisputeState int32

const (
	DisputeState_DISPUTE_STATE_UNSPECIFIED DisputeState = 0
	DisputeState_DISPUTE_STATE_INITIATED   DisputeState = 1
	DisputeState_DISPUTE_STATE_CLOSED      DisputeState = 2
)

// Enum value maps for DisputeState.
var (
	DisputeState_name = map[int32]string{
		0: "DISPUTE_STATE_UNSPECIFIED",
		1: "DISPUTE_STATE_INITIATED",
		2: "DISPUTE_STATE_CLOSED",
	}
	DisputeState_value = map[string]int32{
		"DISPUTE_STATE_UNSPECIFIED": 0,
		"DISPUTE_STATE_INITIATED":   1,
		"DISPUTE_STATE_CLOSED":      2,
	}
)

func (x DisputeState) Enum() *DisputeState {
	p := new(DisputeState)
	*p = x
	return p
}

func (x DisputeState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[13].Descriptor()
}

func (DisputeState) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[13]
}

func (x DisputeState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeState.Descriptor instead.
func (DisputeState) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{13}
}

type DisputeVerdict int32

const (
	DisputeVerdict_DISPUTE_VERDICT_UNSPECIFIED DisputeVerdict = 0
	// Dispute request is accepted and the amount is reversed.
	DisputeVerdict_DISPUTE_VERDICT_ACCEPTED DisputeVerdict = 1
	DisputeVerdict_DISPUTE_VERDICT_REJECTED DisputeVerdict = 2
)

// Enum value maps for DisputeVerdict.
var (
	DisputeVerdict_name = map[int32]string{
		0: "DISPUTE_VERDICT_UNSPECIFIED",
		1: "DISPUTE_VERDICT_ACCEPTED",
		2: "DISPUTE_VERDICT_REJECTED",
	}
	DisputeVerdict_value = map[string]int32{
		"DISPUTE_VERDICT_UNSPECIFIED": 0,
		"DISPUTE_VERDICT_ACCEPTED":    1,
		"DISPUTE_VERDICT_REJECTED":    2,
	}
)

func (x DisputeVerdict) Enum() *DisputeVerdict {
	p := new(DisputeVerdict)
	*p = x
	return p
}

func (x DisputeVerdict) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeVerdict) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[14].Descriptor()
}

func (DisputeVerdict) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[14]
}

func (x DisputeVerdict) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeVerdict.Descriptor instead.
func (DisputeVerdict) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{14}
}

type AccountOperationalStatus int32

const (
	AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_UNSPECIFIED AccountOperationalStatus = 0
	AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_ACTIVE      AccountOperationalStatus = 1
	AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_INACTIVE    AccountOperationalStatus = 2
	AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_DORMANT     AccountOperationalStatus = 3
	AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_CLOSED      AccountOperationalStatus = 4
)

// Enum value maps for AccountOperationalStatus.
var (
	AccountOperationalStatus_name = map[int32]string{
		0: "ACCOUNT_OPERATIONAL_STATUS_UNSPECIFIED",
		1: "ACCOUNT_OPERATIONAL_STATUS_ACTIVE",
		2: "ACCOUNT_OPERATIONAL_STATUS_INACTIVE",
		3: "ACCOUNT_OPERATIONAL_STATUS_DORMANT",
		4: "ACCOUNT_OPERATIONAL_STATUS_CLOSED",
	}
	AccountOperationalStatus_value = map[string]int32{
		"ACCOUNT_OPERATIONAL_STATUS_UNSPECIFIED": 0,
		"ACCOUNT_OPERATIONAL_STATUS_ACTIVE":      1,
		"ACCOUNT_OPERATIONAL_STATUS_INACTIVE":    2,
		"ACCOUNT_OPERATIONAL_STATUS_DORMANT":     3,
		"ACCOUNT_OPERATIONAL_STATUS_CLOSED":      4,
	}
)

func (x AccountOperationalStatus) Enum() *AccountOperationalStatus {
	p := new(AccountOperationalStatus)
	*p = x
	return p
}

func (x AccountOperationalStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountOperationalStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[15].Descriptor()
}

func (AccountOperationalStatus) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[15]
}

func (x AccountOperationalStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountOperationalStatus.Descriptor instead.
func (AccountOperationalStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{15}
}

type ProcessedFreezeReason int32

const (
	ProcessedFreezeReason_PROCESSED_FREEZE_REASON_UNSPECIFIED            ProcessedFreezeReason = 0
	ProcessedFreezeReason_PROCESSED_FREEZE_REASON_LEA_REPORTED           ProcessedFreezeReason = 1
	ProcessedFreezeReason_PROCESSED_FREEZE_REASON_LEA_REPORTED_MID_LAYER ProcessedFreezeReason = 2
	ProcessedFreezeReason_PROCESSED_FREEZE_REASON_VKYC_ISSUE             ProcessedFreezeReason = 3
	ProcessedFreezeReason_PROCESSED_FREEZE_REASON_REKYC_OVERDUE          ProcessedFreezeReason = 4
	ProcessedFreezeReason_PROCESSED_FREEZE_REASON_SUSPICIOUS_ACTIVITY    ProcessedFreezeReason = 5
	ProcessedFreezeReason_PROCESSED_FREEZE_REASON_UNCATEGORIZED_FREEZE   ProcessedFreezeReason = 6
	ProcessedFreezeReason_PROCESSED_FREEZE_REASON_NO_FREEZE_CODE         ProcessedFreezeReason = 7
)

// Enum value maps for ProcessedFreezeReason.
var (
	ProcessedFreezeReason_name = map[int32]string{
		0: "PROCESSED_FREEZE_REASON_UNSPECIFIED",
		1: "PROCESSED_FREEZE_REASON_LEA_REPORTED",
		2: "PROCESSED_FREEZE_REASON_LEA_REPORTED_MID_LAYER",
		3: "PROCESSED_FREEZE_REASON_VKYC_ISSUE",
		4: "PROCESSED_FREEZE_REASON_REKYC_OVERDUE",
		5: "PROCESSED_FREEZE_REASON_SUSPICIOUS_ACTIVITY",
		6: "PROCESSED_FREEZE_REASON_UNCATEGORIZED_FREEZE",
		7: "PROCESSED_FREEZE_REASON_NO_FREEZE_CODE",
	}
	ProcessedFreezeReason_value = map[string]int32{
		"PROCESSED_FREEZE_REASON_UNSPECIFIED":            0,
		"PROCESSED_FREEZE_REASON_LEA_REPORTED":           1,
		"PROCESSED_FREEZE_REASON_LEA_REPORTED_MID_LAYER": 2,
		"PROCESSED_FREEZE_REASON_VKYC_ISSUE":             3,
		"PROCESSED_FREEZE_REASON_REKYC_OVERDUE":          4,
		"PROCESSED_FREEZE_REASON_SUSPICIOUS_ACTIVITY":    5,
		"PROCESSED_FREEZE_REASON_UNCATEGORIZED_FREEZE":   6,
		"PROCESSED_FREEZE_REASON_NO_FREEZE_CODE":         7,
	}
)

func (x ProcessedFreezeReason) Enum() *ProcessedFreezeReason {
	p := new(ProcessedFreezeReason)
	*p = x
	return p
}

func (x ProcessedFreezeReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessedFreezeReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[16].Descriptor()
}

func (ProcessedFreezeReason) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[16]
}

func (x ProcessedFreezeReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessedFreezeReason.Descriptor instead.
func (ProcessedFreezeReason) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{16}
}

type TicketBulkUpdateFieldMask int32

const (
	TicketBulkUpdateFieldMask_TICKET_BULK_UPDATE_FIELD_MASK_UNSPECIFIED TicketBulkUpdateFieldMask = 0
	TicketBulkUpdateFieldMask_MODEL_1_NAME                              TicketBulkUpdateFieldMask = 1
	TicketBulkUpdateFieldMask_MODEL_2_NAME                              TicketBulkUpdateFieldMask = 2
	TicketBulkUpdateFieldMask_MODEL_1_SCORE                             TicketBulkUpdateFieldMask = 3
	TicketBulkUpdateFieldMask_MODEL_2_SCORE                             TicketBulkUpdateFieldMask = 4
	TicketBulkUpdateFieldMask_CONFIDENCE_SCORE                          TicketBulkUpdateFieldMask = 5
)

// Enum value maps for TicketBulkUpdateFieldMask.
var (
	TicketBulkUpdateFieldMask_name = map[int32]string{
		0: "TICKET_BULK_UPDATE_FIELD_MASK_UNSPECIFIED",
		1: "MODEL_1_NAME",
		2: "MODEL_2_NAME",
		3: "MODEL_1_SCORE",
		4: "MODEL_2_SCORE",
		5: "CONFIDENCE_SCORE",
	}
	TicketBulkUpdateFieldMask_value = map[string]int32{
		"TICKET_BULK_UPDATE_FIELD_MASK_UNSPECIFIED": 0,
		"MODEL_1_NAME":     1,
		"MODEL_2_NAME":     2,
		"MODEL_1_SCORE":    3,
		"MODEL_2_SCORE":    4,
		"CONFIDENCE_SCORE": 5,
	}
)

func (x TicketBulkUpdateFieldMask) Enum() *TicketBulkUpdateFieldMask {
	p := new(TicketBulkUpdateFieldMask)
	*p = x
	return p
}

func (x TicketBulkUpdateFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TicketBulkUpdateFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[17].Descriptor()
}

func (TicketBulkUpdateFieldMask) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[17]
}

func (x TicketBulkUpdateFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TicketBulkUpdateFieldMask.Descriptor instead.
func (TicketBulkUpdateFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{17}
}

type LsaName int32

const (
	LsaName_LSA_NAME_UNSPECIFIED        LsaName = 0
	LsaName_LSA_NAME_ANDHRA_PRADESH     LsaName = 1
	LsaName_LSA_NAME_ASSAM              LsaName = 2
	LsaName_LSA_NAME_BIHAR              LsaName = 3
	LsaName_LSA_NAME_DELHI              LsaName = 4
	LsaName_LSA_NAME_GUJARAT            LsaName = 5
	LsaName_LSA_NAME_HIMACHAL_PRADESH   LsaName = 6
	LsaName_LSA_NAME_HARYANA            LsaName = 7
	LsaName_LSA_NAME_JAMMU_KASHMIR      LsaName = 8
	LsaName_LSA_NAME_KARNATAKA          LsaName = 9
	LsaName_LSA_NAME_KERALA             LsaName = 10
	LsaName_LSA_NAME_KOLKATA            LsaName = 11
	LsaName_LSA_NAME_MADHYA_PRADESH     LsaName = 12
	LsaName_LSA_NAME_MAHARASHTRA        LsaName = 13
	LsaName_LSA_NAME_MUMBAI             LsaName = 14
	LsaName_LSA_NAME_NORTH_EAST         LsaName = 15
	LsaName_LSA_NAME_ODISHA             LsaName = 16
	LsaName_LSA_NAME_PUNJAB             LsaName = 17
	LsaName_LSA_NAME_RAJASTHAN          LsaName = 18
	LsaName_LSA_NAME_TAMIL_NADU         LsaName = 19
	LsaName_LSA_NAME_UTTAR_PRADESH_EAST LsaName = 20
	LsaName_LSA_NAME_UTTAR_PRADESH_WEST LsaName = 21
	LsaName_LSA_NAME_WEST_BENGAL        LsaName = 22
)

// Enum value maps for LsaName.
var (
	LsaName_name = map[int32]string{
		0:  "LSA_NAME_UNSPECIFIED",
		1:  "LSA_NAME_ANDHRA_PRADESH",
		2:  "LSA_NAME_ASSAM",
		3:  "LSA_NAME_BIHAR",
		4:  "LSA_NAME_DELHI",
		5:  "LSA_NAME_GUJARAT",
		6:  "LSA_NAME_HIMACHAL_PRADESH",
		7:  "LSA_NAME_HARYANA",
		8:  "LSA_NAME_JAMMU_KASHMIR",
		9:  "LSA_NAME_KARNATAKA",
		10: "LSA_NAME_KERALA",
		11: "LSA_NAME_KOLKATA",
		12: "LSA_NAME_MADHYA_PRADESH",
		13: "LSA_NAME_MAHARASHTRA",
		14: "LSA_NAME_MUMBAI",
		15: "LSA_NAME_NORTH_EAST",
		16: "LSA_NAME_ODISHA",
		17: "LSA_NAME_PUNJAB",
		18: "LSA_NAME_RAJASTHAN",
		19: "LSA_NAME_TAMIL_NADU",
		20: "LSA_NAME_UTTAR_PRADESH_EAST",
		21: "LSA_NAME_UTTAR_PRADESH_WEST",
		22: "LSA_NAME_WEST_BENGAL",
	}
	LsaName_value = map[string]int32{
		"LSA_NAME_UNSPECIFIED":        0,
		"LSA_NAME_ANDHRA_PRADESH":     1,
		"LSA_NAME_ASSAM":              2,
		"LSA_NAME_BIHAR":              3,
		"LSA_NAME_DELHI":              4,
		"LSA_NAME_GUJARAT":            5,
		"LSA_NAME_HIMACHAL_PRADESH":   6,
		"LSA_NAME_HARYANA":            7,
		"LSA_NAME_JAMMU_KASHMIR":      8,
		"LSA_NAME_KARNATAKA":          9,
		"LSA_NAME_KERALA":             10,
		"LSA_NAME_KOLKATA":            11,
		"LSA_NAME_MADHYA_PRADESH":     12,
		"LSA_NAME_MAHARASHTRA":        13,
		"LSA_NAME_MUMBAI":             14,
		"LSA_NAME_NORTH_EAST":         15,
		"LSA_NAME_ODISHA":             16,
		"LSA_NAME_PUNJAB":             17,
		"LSA_NAME_RAJASTHAN":          18,
		"LSA_NAME_TAMIL_NADU":         19,
		"LSA_NAME_UTTAR_PRADESH_EAST": 20,
		"LSA_NAME_UTTAR_PRADESH_WEST": 21,
		"LSA_NAME_WEST_BENGAL":        22,
	}
)

func (x LsaName) Enum() *LsaName {
	p := new(LsaName)
	*p = x
	return p
}

func (x LsaName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LsaName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[18].Descriptor()
}

func (LsaName) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[18]
}

func (x LsaName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LsaName.Descriptor instead.
func (LsaName) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{18}
}

type MnrlUsecase int32

const (
	MnrlUsecase_MNRL_USECASE_UNSPECIFIED              MnrlUsecase = 0
	MnrlUsecase_MNRL_USECASE_DOT_FAKE                 MnrlUsecase = 1
	MnrlUsecase_MNRL_USECASE_LEA                      MnrlUsecase = 2
	MnrlUsecase_MNRL_USECASE_NONCOMPLIANT             MnrlUsecase = 3
	MnrlUsecase_MNRL_USECASE_SUSPECTED_FLAGGED_MOBILE MnrlUsecase = 4
)

// Enum value maps for MnrlUsecase.
var (
	MnrlUsecase_name = map[int32]string{
		0: "MNRL_USECASE_UNSPECIFIED",
		1: "MNRL_USECASE_DOT_FAKE",
		2: "MNRL_USECASE_LEA",
		3: "MNRL_USECASE_NONCOMPLIANT",
		4: "MNRL_USECASE_SUSPECTED_FLAGGED_MOBILE",
	}
	MnrlUsecase_value = map[string]int32{
		"MNRL_USECASE_UNSPECIFIED":              0,
		"MNRL_USECASE_DOT_FAKE":                 1,
		"MNRL_USECASE_LEA":                      2,
		"MNRL_USECASE_NONCOMPLIANT":             3,
		"MNRL_USECASE_SUSPECTED_FLAGGED_MOBILE": 4,
	}
)

func (x MnrlUsecase) Enum() *MnrlUsecase {
	p := new(MnrlUsecase)
	*p = x
	return p
}

func (x MnrlUsecase) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MnrlUsecase) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[19].Descriptor()
}

func (MnrlUsecase) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[19]
}

func (x MnrlUsecase) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MnrlUsecase.Descriptor instead.
func (MnrlUsecase) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{19}
}

type SensitivityIndex int32

const (
	SensitivityIndex_SENSITIVITY_INDEX_UNSPECIFIED SensitivityIndex = 0
	SensitivityIndex_SENSITIVITY_INDEX_MEDIUM      SensitivityIndex = 1
	SensitivityIndex_SENSITIVITY_INDEX_HIGH        SensitivityIndex = 2
	SensitivityIndex_SENSITIVITY_INDEX_VERY_HIGH   SensitivityIndex = 3
)

// Enum value maps for SensitivityIndex.
var (
	SensitivityIndex_name = map[int32]string{
		0: "SENSITIVITY_INDEX_UNSPECIFIED",
		1: "SENSITIVITY_INDEX_MEDIUM",
		2: "SENSITIVITY_INDEX_HIGH",
		3: "SENSITIVITY_INDEX_VERY_HIGH",
	}
	SensitivityIndex_value = map[string]int32{
		"SENSITIVITY_INDEX_UNSPECIFIED": 0,
		"SENSITIVITY_INDEX_MEDIUM":      1,
		"SENSITIVITY_INDEX_HIGH":        2,
		"SENSITIVITY_INDEX_VERY_HIGH":   3,
	}
)

func (x SensitivityIndex) Enum() *SensitivityIndex {
	p := new(SensitivityIndex)
	*p = x
	return p
}

func (x SensitivityIndex) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SensitivityIndex) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_enums_enums_proto_enumTypes[20].Descriptor()
}

func (SensitivityIndex) Type() protoreflect.EnumType {
	return &file_api_risk_enums_enums_proto_enumTypes[20]
}

func (x SensitivityIndex) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SensitivityIndex.Descriptor instead.
func (SensitivityIndex) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_enums_enums_proto_rawDescGZIP(), []int{20}
}

var File_api_risk_enums_enums_proto protoreflect.FileDescriptor

var file_api_risk_enums_enums_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2a, 0x91, 0x01, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16,
	0x0a, 0x12, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x10, 0x01, 0x12, 0x18,
	0x0a, 0x14, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x10, 0x03, 0x12, 0x17, 0x0a,
	0x13, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x46, 0x52,
	0x45, 0x45, 0x5a, 0x45, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x4c, 0x49, 0x45, 0x4e, 0x10, 0x05, 0x2a, 0xfb, 0x01, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x16, 0x0a,
	0x12, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x4f, 0x5f, 0x42,
	0x41, 0x4e, 0x4b, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x53, 0x45, 0x44, 0x10, 0x04, 0x12, 0x1d, 0x0a, 0x19, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x06, 0x12, 0x21,
	0x0a, 0x1d, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f,
	0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x52, 0x49, 0x44, 0x45, 0x10,
	0x07, 0x12, 0x20, 0x0a, 0x1c, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43,
	0x54, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x52, 0x49, 0x44,
	0x45, 0x10, 0x08, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0x09, 0x2a, 0x64, 0x0a, 0x0a, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x11, 0x0a, 0x0d, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x46, 0x49,
	0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x52, 0x4f, 0x56, 0x45,
	0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x4c, 0x45, 0x41, 0x10, 0x03, 0x2a, 0xa9, 0x07, 0x0a, 0x0d,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1e, 0x0a,
	0x1a, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a,
	0x21, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x46, 0x52, 0x41, 0x55, 0x44, 0x55, 0x4c, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x02, 0x12, 0x21,
	0x0a, 0x1d, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10,
	0x03, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x44, 0x49,
	0x43, 0x41, 0x54, 0x4f, 0x52, 0x53, 0x10, 0x04, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x4e, 0x4e, 0x59,
	0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x41, 0x42, 0x55, 0x53, 0x45, 0x10, 0x05, 0x12, 0x31, 0x0a,
	0x2d, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x46,
	0x52, 0x4f, 0x4d, 0x5f, 0x46, 0x52, 0x41, 0x55, 0x44, 0x53, 0x54, 0x45, 0x52, 0x53, 0x10, 0x06,
	0x12, 0x2c, 0x0a, 0x28, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x48, 0x49, 0x47, 0x48, 0x5f, 0x41, 0x54, 0x4d, 0x5f, 0x57, 0x49, 0x54, 0x48,
	0x44, 0x52, 0x41, 0x57, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x07, 0x12, 0x1b,
	0x0a, 0x17, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x54, 0x4d, 0x5f, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x10, 0x08, 0x12, 0x26, 0x0a, 0x22, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x4d,
	0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x10, 0x09, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x44, 0x55, 0x45, 0x5f, 0x44, 0x49, 0x4c, 0x49, 0x47, 0x45,
	0x4e, 0x43, 0x45, 0x10, 0x0a, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52,
	0x5f, 0x4f, 0x55, 0x54, 0x43, 0x41, 0x4c, 0x4c, 0x10, 0x0b, 0x12, 0x2b, 0x0a, 0x27, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x0c, 0x12, 0x29, 0x0a, 0x25, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4d, 0x49, 0x4e, 0x5f, 0x4b, 0x59,
	0x43, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x59,
	0x10, 0x0d, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45,
	0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49,
	0x4e, 0x54, 0x10, 0x0e, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49,
	0x52, 0x59, 0x10, 0x0f, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4e, 0x50, 0x43, 0x49, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x4c, 0x41, 0x49, 0x4e, 0x54, 0x10, 0x10, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41,
	0x4c, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x53, 0x10, 0x11, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x41, 0x5f,
	0x55, 0x4e, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x10, 0x12, 0x12, 0x28, 0x0a, 0x24, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4c, 0x53, 0x4f,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x10, 0x13, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x4e, 0x42,
	0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x14, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x43, 0x43, 0x5f, 0x4f,
	0x52, 0x5f, 0x50, 0x4c, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x15, 0x12, 0x21, 0x0a, 0x1d, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x46, 0x41,
	0x4c, 0x53, 0x45, 0x5f, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0x10, 0x16, 0x12, 0x1d,
	0x0a, 0x19, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x10, 0x17, 0x12, 0x28, 0x0a,
	0x24, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x18, 0x2a, 0x8c, 0x03, 0x0a, 0x0d, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x41, 0x49,
	0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2d, 0x0a, 0x29, 0x46, 0x41, 0x49,
	0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x44, 0x52, 0x4f, 0x50,
	0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f,
	0x4d, 0x41, 0x52, 0x4b, 0x45, 0x44, 0x10, 0x01, 0x12, 0x2b, 0x0a, 0x27, 0x46, 0x41, 0x49, 0x4c,
	0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f,
	0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x53,
	0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x2d, 0x0a, 0x29, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x44, 0x45, 0x44,
	0x55, 0x50, 0x45, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x46, 0x52, 0x4f, 0x5a,
	0x45, 0x4e, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x44, 0x45, 0x44, 0x55,
	0x50, 0x45, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x46, 0x52, 0x4f, 0x5a, 0x45, 0x4e, 0x10, 0x04, 0x12,
	0x32, 0x0a, 0x2e, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f,
	0x4e, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x4c, 0x49, 0x43, 0x54, 0x5f,
	0x53, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x46, 0x52, 0x45, 0x45, 0x5a,
	0x45, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10,
	0x06, 0x12, 0x21, 0x0a, 0x1d, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53,
	0x45, 0x44, 0x10, 0x07, 0x12, 0x30, 0x0a, 0x2c, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x5f, 0x4d, 0x49, 0x53, 0x4d,
	0x41, 0x54, 0x43, 0x48, 0x10, 0x08, 0x2a, 0x92, 0x07, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x6d, 0x73,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d,
	0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x4d,
	0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x5f, 0x46, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f,
	0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x45, 0x5f, 0x46, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a,
	0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45,
	0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x48, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a,
	0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45,
	0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4d, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x04, 0x12, 0x1d, 0x0a, 0x19,
	0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45,
	0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x48, 0x5f, 0x54, 0x4d, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19, 0x43,
	0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x5f, 0x4c, 0x5f, 0x54, 0x4d, 0x10, 0x06, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f,
	0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x54, 0x46, 0x5f, 0x43, 0x46, 0x10, 0x07, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f,
	0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x54, 0x46, 0x5f, 0x43, 0x10, 0x08, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f, 0x4d,
	0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x5f, 0x41, 0x43, 0x43, 0x5f, 0x55, 0x4e, 0x46, 0x10, 0x09, 0x12, 0x1b, 0x0a, 0x17, 0x43,
	0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x5f, 0x43, 0x46, 0x10, 0x0a, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d,
	0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x5f, 0x41, 0x46, 0x55, 0x5f, 0x46, 0x10, 0x0b, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x4d,
	0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x5f, 0x49, 0x4e, 0x54, 0x5f, 0x41, 0x54, 0x4d, 0x10, 0x0c, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x4f,
	0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x53, 0x44, 0x10, 0x0d, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d, 0x53,
	0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x4d, 0x5f, 0x43, 0x46, 0x10, 0x0e, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f,
	0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x5f, 0x55, 0x4e, 0x46,
	0x5f, 0x43, 0x4c, 0x10, 0x0f, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54,
	0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x43, 0x4c,
	0x5f, 0x41, 0x43, 0x10, 0x10, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54,
	0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4f, 0x4e,
	0x42, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45, 0x44, 0x10, 0x11, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f,
	0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x48, 0x5f, 0x50, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x12, 0x12, 0x22, 0x0a, 0x1e,
	0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45,
	0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x5f, 0x41, 0x54, 0x4d, 0x5f, 0x43, 0x10, 0x13,
	0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41,
	0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x4d, 0x5f, 0x52, 0x55, 0x4c, 0x45,
	0x5f, 0x31, 0x10, 0x14, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45,
	0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x4d, 0x5f,
	0x52, 0x55, 0x4c, 0x45, 0x5f, 0x32, 0x10, 0x15, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x4d,
	0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x5f, 0x54, 0x4d, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x5f, 0x33, 0x10, 0x16, 0x12, 0x26, 0x0a, 0x22,
	0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45,
	0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x46, 0x10, 0x17, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45,
	0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41, 0x43, 0x43,
	0x5f, 0x44, 0x46, 0x10, 0x18, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54,
	0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x56, 0x4b,
	0x59, 0x43, 0x5f, 0x43, 0x46, 0x10, 0x19, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x4d, 0x53,
	0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x4d, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x43, 0x46, 0x10, 0x1a, 0x2a, 0xb8, 0x01, 0x0a, 0x0a,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x4e,
	0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x45, 0x4e, 0x54, 0x49, 0x54,
	0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10,
	0x01, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x4e,
	0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x45,
	0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x4e, 0x54, 0x49,
	0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f,
	0x4c, 0x49, 0x53, 0x54, 0x10, 0x05, 0x2a, 0xd9, 0x01, 0x0a, 0x13, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25,
	0x0a, 0x21, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54,
	0x4f, 0x54, 0x41, 0x4c, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x10, 0x01, 0x12, 0x27, 0x0a,
	0x23, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x46, 0x52,
	0x45, 0x45, 0x5a, 0x45, 0x10, 0x02, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x10, 0x03, 0x12, 0x22,
	0x0a, 0x1e, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x46, 0x52, 0x4f, 0x5a, 0x45, 0x4e,
	0x10, 0x04, 0x2a, 0xb7, 0x04, 0x0a, 0x10, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43,
	0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x12, 0x21, 0x0a, 0x1d, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x30, 0x0a, 0x2c, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f,
	0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x28, 0x0a, 0x24,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49,
	0x41, 0x5f, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x45, 0x52, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x52, 0x45, 0x4f, 0x4f,
	0x42, 0x45, 0x10, 0x03, 0x12, 0x34, 0x0a, 0x30, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52,
	0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x42, 0x32, 0x42, 0x5f, 0x53, 0x41,
	0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x31, 0x0a, 0x2d, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f,
	0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x50, 0x4c, 0x5f, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45,
	0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x10, 0x05, 0x12, 0x34, 0x0a,
	0x30, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52,
	0x49, 0x41, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x06, 0x12, 0x38, 0x0a, 0x34, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f,
	0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c,
	0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x26, 0x0a,
	0x22, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52,
	0x49, 0x41, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x08, 0x12, 0x33, 0x0a, 0x2f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45,
	0x52, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x4e, 0x52, 0x5f, 0x53, 0x41,
	0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x09, 0x12, 0x24, 0x0a, 0x20, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x5f,
	0x44, 0x32, 0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x0a,
	0x12, 0x2a, 0x0a, 0x26, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x49,
	0x54, 0x45, 0x52, 0x49, 0x41, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x47, 0x55, 0x41, 0x52,
	0x44, 0x49, 0x41, 0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x10, 0x0b, 0x2a, 0x88, 0x01, 0x0a,
	0x0e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1f, 0x0a, 0x1b, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x18, 0x0a, 0x14, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x41,
	0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x02, 0x12, 0x18, 0x0a,
	0x14, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x03, 0x2a, 0x92, 0x01, 0x0a, 0x0c, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x49, 0x53, 0x4b,
	0x5f, 0x53, 0x45, 0x56, 0x45, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x49, 0x53, 0x4b, 0x5f,
	0x53, 0x45, 0x56, 0x45, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x4f, 0x57, 0x10, 0x01, 0x12, 0x18,
	0x0a, 0x14, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x53, 0x45, 0x56, 0x45, 0x52, 0x49, 0x54, 0x59, 0x5f,
	0x4d, 0x45, 0x44, 0x49, 0x55, 0x4d, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x49, 0x53, 0x4b,
	0x5f, 0x53, 0x45, 0x56, 0x45, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x49, 0x47, 0x48, 0x10, 0x03,
	0x12, 0x1a, 0x0a, 0x16, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x53, 0x45, 0x56, 0x45, 0x52, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x04, 0x2a, 0xaa, 0x01, 0x0a,
	0x0d, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x65, 0x73, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x1e,
	0x0a, 0x1a, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c,
	0x0a, 0x18, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4e,
	0x45, 0x41, 0x52, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x03, 0x12,
	0x19, 0x0a, 0x15, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x4e, 0x45, 0x53,
	0x53, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4c, 0x41, 0x53,
	0x54, 0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x04, 0x2a, 0xf6, 0x01, 0x0a, 0x0f, 0x4c, 0x45,
	0x41, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12, 0x21, 0x0a,
	0x1d, 0x4c, 0x45, 0x41, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x52, 0x49, 0x47,
	0x49, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x45, 0x41, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4f,
	0x52, 0x49, 0x47, 0x49, 0x4e, 0x5f, 0x46, 0x49, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c,
	0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x45, 0x41, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x4f, 0x52, 0x49, 0x47, 0x49, 0x4e, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x10,
	0x02, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x45, 0x41, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f,
	0x4f, 0x52, 0x49, 0x47, 0x49, 0x4e, 0x5f, 0x4e, 0x50, 0x43, 0x49, 0x10, 0x03, 0x12, 0x20, 0x0a,
	0x1c, 0x4c, 0x45, 0x41, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x52, 0x49, 0x47,
	0x49, 0x4e, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x04, 0x12,
	0x24, 0x0a, 0x20, 0x4c, 0x45, 0x41, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x52,
	0x49, 0x47, 0x49, 0x4e, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x45, 0x41, 0x5f, 0x52, 0x45, 0x50,
	0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x52, 0x49, 0x47, 0x49, 0x4e, 0x5f, 0x4e, 0x43, 0x43, 0x52, 0x50,
	0x10, 0x06, 0x2a, 0x64, 0x0a, 0x0c, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1b, 0x0a, 0x17, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x18,
	0x0a, 0x14, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x6d, 0x0a, 0x0e, 0x44, 0x69, 0x73, 0x70,
	0x75, 0x74, 0x65, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x49,
	0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x44,
	0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x41,
	0x43, 0x43, 0x45, 0x50, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x49, 0x53,
	0x50, 0x55, 0x54, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x4a,
	0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x02, 0x2a, 0xe5, 0x01, 0x0a, 0x18, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x26, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x25, 0x0a, 0x21, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02,
	0x12, 0x26, 0x0a, 0x22, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44,
	0x4f, 0x52, 0x4d, 0x41, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x04, 0x2a,
	0x80, 0x03, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x46, 0x72, 0x65,
	0x65, 0x7a, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x23, 0x50, 0x52, 0x4f,
	0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x52, 0x45,
	0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x28, 0x0a, 0x24, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x5f,
	0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4c, 0x45,
	0x41, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x32, 0x0a, 0x2e,
	0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x52, 0x45, 0x50, 0x4f,
	0x52, 0x54, 0x45, 0x44, 0x5f, 0x4d, 0x49, 0x44, 0x5f, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x10, 0x02,
	0x12, 0x26, 0x0a, 0x22, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x5f, 0x46, 0x52,
	0x45, 0x45, 0x5a, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x56, 0x4b, 0x59, 0x43,
	0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25, 0x50, 0x52, 0x4f, 0x43,
	0x45, 0x53, 0x53, 0x45, 0x44, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x44, 0x55,
	0x45, 0x10, 0x04, 0x12, 0x2f, 0x0a, 0x2b, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44,
	0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x53, 0x50, 0x49, 0x43, 0x49, 0x4f, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49,
	0x54, 0x59, 0x10, 0x05, 0x12, 0x30, 0x0a, 0x2c, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45,
	0x44, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x55, 0x4e, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x5f, 0x46, 0x52,
	0x45, 0x45, 0x5a, 0x45, 0x10, 0x06, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x45, 0x44, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f,
	0x4e, 0x5f, 0x4e, 0x4f, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x10, 0x07, 0x2a, 0xaa, 0x01, 0x0a, 0x19, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x75, 0x6c,
	0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b,
	0x12, 0x2d, 0x0a, 0x29, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x42, 0x55, 0x4c, 0x4b, 0x5f,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x10, 0x0a, 0x0c, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x31, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10,
	0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x32, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x31, 0x5f, 0x53,
	0x43, 0x4f, 0x52, 0x45, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f,
	0x32, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x4f, 0x4e,
	0x46, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x10, 0x05, 0x2a,
	0xc2, 0x04, 0x0a, 0x07, 0x4c, 0x73, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x4c,
	0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x41, 0x4e, 0x44, 0x48, 0x52, 0x41, 0x5f, 0x50, 0x52, 0x41, 0x44, 0x45, 0x53, 0x48,
	0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x41,
	0x53, 0x53, 0x41, 0x4d, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x5f, 0x42, 0x49, 0x48, 0x41, 0x52, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x53,
	0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x44, 0x45, 0x4c, 0x48, 0x49, 0x10, 0x04, 0x12, 0x14,
	0x0a, 0x10, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x47, 0x55, 0x4a, 0x41, 0x52,
	0x41, 0x54, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x48, 0x49, 0x4d, 0x41, 0x43, 0x48, 0x41, 0x4c, 0x5f, 0x50, 0x52, 0x41, 0x44, 0x45, 0x53,
	0x48, 0x10, 0x06, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x48, 0x41, 0x52, 0x59, 0x41, 0x4e, 0x41, 0x10, 0x07, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x53, 0x41,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4a, 0x41, 0x4d, 0x4d, 0x55, 0x5f, 0x4b, 0x41, 0x53, 0x48,
	0x4d, 0x49, 0x52, 0x10, 0x08, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x4b, 0x41, 0x52, 0x4e, 0x41, 0x54, 0x41, 0x4b, 0x41, 0x10, 0x09, 0x12, 0x13, 0x0a,
	0x0f, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4b, 0x45, 0x52, 0x41, 0x4c, 0x41,
	0x10, 0x0a, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4b,
	0x4f, 0x4c, 0x4b, 0x41, 0x54, 0x41, 0x10, 0x0b, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x53, 0x41, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x41, 0x44, 0x48, 0x59, 0x41, 0x5f, 0x50, 0x52, 0x41, 0x44,
	0x45, 0x53, 0x48, 0x10, 0x0c, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x4d, 0x41, 0x48, 0x41, 0x52, 0x41, 0x53, 0x48, 0x54, 0x52, 0x41, 0x10, 0x0d, 0x12,
	0x13, 0x0a, 0x0f, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x55, 0x4d, 0x42,
	0x41, 0x49, 0x10, 0x0e, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x4e, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x45, 0x41, 0x53, 0x54, 0x10, 0x0f, 0x12, 0x13, 0x0a,
	0x0f, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4f, 0x44, 0x49, 0x53, 0x48, 0x41,
	0x10, 0x10, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x50,
	0x55, 0x4e, 0x4a, 0x41, 0x42, 0x10, 0x11, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x53, 0x41, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x52, 0x41, 0x4a, 0x41, 0x53, 0x54, 0x48, 0x41, 0x4e, 0x10, 0x12, 0x12,
	0x17, 0x0a, 0x13, 0x4c, 0x53, 0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x54, 0x41, 0x4d, 0x49,
	0x4c, 0x5f, 0x4e, 0x41, 0x44, 0x55, 0x10, 0x13, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x53, 0x41, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x54, 0x54, 0x41, 0x52, 0x5f, 0x50, 0x52, 0x41, 0x44, 0x45,
	0x53, 0x48, 0x5f, 0x45, 0x41, 0x53, 0x54, 0x10, 0x14, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x53, 0x41,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x54, 0x54, 0x41, 0x52, 0x5f, 0x50, 0x52, 0x41, 0x44,
	0x45, 0x53, 0x48, 0x5f, 0x57, 0x45, 0x53, 0x54, 0x10, 0x15, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x53,
	0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x57, 0x45, 0x53, 0x54, 0x5f, 0x42, 0x45, 0x4e, 0x47,
	0x41, 0x4c, 0x10, 0x16, 0x2a, 0xa6, 0x01, 0x0a, 0x0b, 0x4d, 0x6e, 0x72, 0x6c, 0x55, 0x73, 0x65,
	0x63, 0x61, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x4e, 0x52, 0x4c, 0x5f, 0x55, 0x53, 0x45,
	0x43, 0x41, 0x53, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x4d, 0x4e, 0x52, 0x4c, 0x5f, 0x55, 0x53, 0x45, 0x43, 0x41,
	0x53, 0x45, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x46, 0x41, 0x4b, 0x45, 0x10, 0x01, 0x12, 0x14, 0x0a,
	0x10, 0x4d, 0x4e, 0x52, 0x4c, 0x5f, 0x55, 0x53, 0x45, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x4c, 0x45,
	0x41, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x4d, 0x4e, 0x52, 0x4c, 0x5f, 0x55, 0x53, 0x45, 0x43,
	0x41, 0x53, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x49, 0x41, 0x4e, 0x54,
	0x10, 0x03, 0x12, 0x29, 0x0a, 0x25, 0x4d, 0x4e, 0x52, 0x4c, 0x5f, 0x55, 0x53, 0x45, 0x43, 0x41,
	0x53, 0x45, 0x5f, 0x53, 0x55, 0x53, 0x50, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4c, 0x41,
	0x47, 0x47, 0x45, 0x44, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x10, 0x04, 0x2a, 0x90, 0x01,
	0x0a, 0x10, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x12, 0x21, 0x0a, 0x1d, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x49, 0x54,
	0x59, 0x5f, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49,
	0x56, 0x49, 0x54, 0x59, 0x5f, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x55,
	0x4d, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x49,
	0x54, 0x59, 0x5f, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f, 0x48, 0x49, 0x47, 0x48, 0x10, 0x02, 0x12,
	0x1f, 0x0a, 0x1b, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x49,
	0x4e, 0x44, 0x45, 0x58, 0x5f, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x48, 0x49, 0x47, 0x48, 0x10, 0x03,
	0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_enums_enums_proto_rawDescOnce sync.Once
	file_api_risk_enums_enums_proto_rawDescData = file_api_risk_enums_enums_proto_rawDesc
)

func file_api_risk_enums_enums_proto_rawDescGZIP() []byte {
	file_api_risk_enums_enums_proto_rawDescOnce.Do(func() {
		file_api_risk_enums_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_enums_enums_proto_rawDescData)
	})
	return file_api_risk_enums_enums_proto_rawDescData
}

var file_api_risk_enums_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 21)
var file_api_risk_enums_enums_proto_goTypes = []interface{}{
	(Action)(0),                    // 0: enums.Action
	(State)(0),                     // 1: enums.State
	(Provenance)(0),                // 2: enums.Provenance
	(RequestReason)(0),             // 3: enums.RequestReason
	(FailureReason)(0),             // 4: enums.FailureReason
	(CommsTemplate)(0),             // 5: enums.CommsTemplate
	(EntityType)(0),                // 6: enums.EntityType
	(AccountFreezeStatus)(0),       // 7: enums.AccountFreezeStatus
	(ScreenerCriteria)(0),          // 8: enums.ScreenerCriteria
	(ScreenerAction)(0),            // 9: enums.ScreenerAction
	(RiskSeverity)(0),              // 10: enums.RiskSeverity
	(DataFreshness)(0),             // 11: enums.DataFreshness
	(LEAReportOrigin)(0),           // 12: enums.LEAReportOrigin
	(DisputeState)(0),              // 13: enums.DisputeState
	(DisputeVerdict)(0),            // 14: enums.DisputeVerdict
	(AccountOperationalStatus)(0),  // 15: enums.AccountOperationalStatus
	(ProcessedFreezeReason)(0),     // 16: enums.ProcessedFreezeReason
	(TicketBulkUpdateFieldMask)(0), // 17: enums.TicketBulkUpdateFieldMask
	(LsaName)(0),                   // 18: enums.LsaName
	(MnrlUsecase)(0),               // 19: enums.MnrlUsecase
	(SensitivityIndex)(0),          // 20: enums.SensitivityIndex
}
var file_api_risk_enums_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_risk_enums_enums_proto_init() }
func file_api_risk_enums_enums_proto_init() {
	if File_api_risk_enums_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_enums_enums_proto_rawDesc,
			NumEnums:      21,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_enums_enums_proto_goTypes,
		DependencyIndexes: file_api_risk_enums_enums_proto_depIdxs,
		EnumInfos:         file_api_risk_enums_enums_proto_enumTypes,
	}.Build()
	File_api_risk_enums_enums_proto = out.File
	file_api_risk_enums_enums_proto_rawDesc = nil
	file_api_risk_enums_enums_proto_goTypes = nil
	file_api_risk_enums_enums_proto_depIdxs = nil
}
