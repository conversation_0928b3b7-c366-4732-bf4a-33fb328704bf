// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/risk/enums/enums.pb.go

package enums

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the ScreenerCriteria in string format in DB
func (p ScreenerCriteria) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ScreenerCriteria while reading from DB
func (p *ScreenerCriteria) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ScreenerCriteria_value[val]
	if !ok {
		return fmt.Errorf("unexpected ScreenerCriteria value: %s", val)
	}
	*p = ScreenerCriteria(valInt)
	return nil
}

// Marshaler interface implementation for ScreenerCriteria
func (x ScreenerCriteria) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ScreenerCriteria
func (x *ScreenerCriteria) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ScreenerCriteria(ScreenerCriteria_value[val])
	return nil
}

// Valuer interface implementation for storing the ScreenerAction in string format in DB
func (p ScreenerAction) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ScreenerAction while reading from DB
func (p *ScreenerAction) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ScreenerAction_value[val]
	if !ok {
		return fmt.Errorf("unexpected ScreenerAction value: %s", val)
	}
	*p = ScreenerAction(valInt)
	return nil
}

// Marshaler interface implementation for ScreenerAction
func (x ScreenerAction) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ScreenerAction
func (x *ScreenerAction) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ScreenerAction(ScreenerAction_value[val])
	return nil
}

// Valuer interface implementation for storing the LEAReportOrigin in string format in DB
func (p LEAReportOrigin) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LEAReportOrigin while reading from DB
func (p *LEAReportOrigin) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LEAReportOrigin_value[val]
	if !ok {
		return fmt.Errorf("unexpected LEAReportOrigin value: %s", val)
	}
	*p = LEAReportOrigin(valInt)
	return nil
}

// Marshaler interface implementation for LEAReportOrigin
func (x LEAReportOrigin) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LEAReportOrigin
func (x *LEAReportOrigin) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LEAReportOrigin(LEAReportOrigin_value[val])
	return nil
}

// Valuer interface implementation for storing the DisputeState in string format in DB
func (p DisputeState) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing DisputeState while reading from DB
func (p *DisputeState) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := DisputeState_value[val]
	if !ok {
		return fmt.Errorf("unexpected DisputeState value: %s", val)
	}
	*p = DisputeState(valInt)
	return nil
}

// Marshaler interface implementation for DisputeState
func (x DisputeState) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for DisputeState
func (x *DisputeState) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = DisputeState(DisputeState_value[val])
	return nil
}

// Valuer interface implementation for storing the DisputeVerdict in string format in DB
func (p DisputeVerdict) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing DisputeVerdict while reading from DB
func (p *DisputeVerdict) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := DisputeVerdict_value[val]
	if !ok {
		return fmt.Errorf("unexpected DisputeVerdict value: %s", val)
	}
	*p = DisputeVerdict(valInt)
	return nil
}

// Marshaler interface implementation for DisputeVerdict
func (x DisputeVerdict) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for DisputeVerdict
func (x *DisputeVerdict) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = DisputeVerdict(DisputeVerdict_value[val])
	return nil
}
