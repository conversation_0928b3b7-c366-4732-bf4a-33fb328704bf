package enums

import (
	"database/sql/driver"
	"fmt"
)

// Valuer interface implementation for storing the data  in string format in DB
func (u Action) Value() (driver.Value, error) {
	return u.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (u *Action) Scan(input interface{}) error {
	if u == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := Action_value[val]
	*u = Action(valInt)
	return nil
}

// Valuer interface implementation for storing the data  in string format in DB
func (u State) Value() (driver.Value, error) {
	return u.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (u *State) Scan(input interface{}) error {
	if u == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := State_value[val]
	*u = State(valInt)
	return nil
}

// Valuer interface implementation for storing the data  in string format in DB
func (u Provenance) Value() (driver.Value, error) {
	return u.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (u *Provenance) Scan(input interface{}) error {
	if u == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := Provenance_value[val]
	*u = Provenance(valInt)
	return nil
}

// Valuer interface implementation for storing the data  in string format in DB
func (u FailureReason) Value() (driver.Value, error) {
	return u.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (u *FailureReason) Scan(input interface{}) error {
	if u == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := FailureReason_value[val]
	*u = FailureReason(valInt)
	return nil
}

// Valuer interface implementation for storing the data  in string format in DB
func (u RequestReason) Value() (driver.Value, error) {
	return u.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (u *RequestReason) Scan(input interface{}) error {
	if u == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := RequestReason_value[val]
	*u = RequestReason(valInt)
	return nil
}

// Valuer interface implementation for storing the data  in string format in DB
func (u AccountFreezeStatus) Value() (driver.Value, error) {
	return u.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (u *AccountFreezeStatus) Scan(input interface{}) error {
	if u == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := AccountFreezeStatus_value[val]
	*u = AccountFreezeStatus(valInt)
	return nil
}
