// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/activity/activity.proto

package activity

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	activity "github.com/epifi/be-common/api/celestial/activity"
	workflow "github.com/epifi/be-common/api/celestial/workflow"
	notification "github.com/epifi/gamma/api/celestial/activity/notification"
	comms "github.com/epifi/gamma/api/comms"
	risk "github.com/epifi/gamma/api/risk"
	case_management "github.com/epifi/gamma/api/risk/case_management"
	enums "github.com/epifi/gamma/api/risk/enums"
	lea "github.com/epifi/gamma/api/risk/lea"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AddToBankAction adds new entry to risk_bank_actions table
// returns permanent error if no case management action maps
// to bank action, ignores duplicate row error
type AddToBankActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// workflow id under consideration
	WorkflowId string `protobuf:"bytes,2,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	// action is the flow current activity leads to
	Action enums.Action `protobuf:"varint,3,opt,name=action,proto3,enum=enums.Action" json:"action,omitempty"`
	// reason to change account state
	RequestReason *risk.RequestReason `protobuf:"bytes,4,opt,name=request_reason,json=requestReason,proto3" json:"request_reason,omitempty"`
	// comms template to be triggered
	CommsTemplate []enums.CommsTemplate `protobuf:"varint,5,rep,packed,name=comms_template,json=commsTemplate,proto3,enum=enums.CommsTemplate" json:"comms_template,omitempty"`
	ActorId       string                `protobuf:"bytes,6,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *AddToBankActionRequest) Reset() {
	*x = AddToBankActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddToBankActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddToBankActionRequest) ProtoMessage() {}

func (x *AddToBankActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddToBankActionRequest.ProtoReflect.Descriptor instead.
func (*AddToBankActionRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{0}
}

func (x *AddToBankActionRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *AddToBankActionRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *AddToBankActionRequest) GetAction() enums.Action {
	if x != nil {
		return x.Action
	}
	return enums.Action(0)
}

func (x *AddToBankActionRequest) GetRequestReason() *risk.RequestReason {
	if x != nil {
		return x.RequestReason
	}
	return nil
}

func (x *AddToBankActionRequest) GetCommsTemplate() []enums.CommsTemplate {
	if x != nil {
		return x.CommsTemplate
	}
	return nil
}

func (x *AddToBankActionRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type AddToBankActionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// id of the bank action entry created
	BankActionId string `protobuf:"bytes,2,opt,name=bank_action_id,json=bankActionId,proto3" json:"bank_action_id,omitempty"`
}

func (x *AddToBankActionResponse) Reset() {
	*x = AddToBankActionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddToBankActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddToBankActionResponse) ProtoMessage() {}

func (x *AddToBankActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddToBankActionResponse.ProtoReflect.Descriptor instead.
func (*AddToBankActionResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{1}
}

func (x *AddToBankActionResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *AddToBankActionResponse) GetBankActionId() string {
	if x != nil {
		return x.BankActionId
	}
	return ""
}

type GetReminderPointRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// name of workflow we need to fetch reminders for
	WorkflowType *workflow.TypeEnum `protobuf:"bytes,2,opt,name=workflow_type,json=workflowType,proto3" json:"workflow_type,omitempty"`
	// start time of the reminder, post this reminders times will be fetched
	// if not populated, reminders will be fetched from current time
	ReminderStartTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=reminder_start_time,json=reminderStartTime,proto3" json:"reminder_start_time,omitempty"`
	ActorId           string                 `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// optional field to identify request
	// like for bank actions will be client request id to identify exact request
	//
	// Types that are assignable to RequestIdentifier:
	//
	//	*GetReminderPointRequest_ClientRequestId
	RequestIdentifier isGetReminderPointRequest_RequestIdentifier `protobuf_oneof:"request_identifier"`
	// form id for which reminders need to be fetched
	FormId string `protobuf:"bytes,6,opt,name=form_id,json=formId,proto3" json:"form_id,omitempty"`
}

func (x *GetReminderPointRequest) Reset() {
	*x = GetReminderPointRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReminderPointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReminderPointRequest) ProtoMessage() {}

func (x *GetReminderPointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReminderPointRequest.ProtoReflect.Descriptor instead.
func (*GetReminderPointRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{2}
}

func (x *GetReminderPointRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *GetReminderPointRequest) GetWorkflowType() *workflow.TypeEnum {
	if x != nil {
		return x.WorkflowType
	}
	return nil
}

func (x *GetReminderPointRequest) GetReminderStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ReminderStartTime
	}
	return nil
}

func (x *GetReminderPointRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (m *GetReminderPointRequest) GetRequestIdentifier() isGetReminderPointRequest_RequestIdentifier {
	if m != nil {
		return m.RequestIdentifier
	}
	return nil
}

func (x *GetReminderPointRequest) GetClientRequestId() string {
	if x, ok := x.GetRequestIdentifier().(*GetReminderPointRequest_ClientRequestId); ok {
		return x.ClientRequestId
	}
	return ""
}

func (x *GetReminderPointRequest) GetFormId() string {
	if x != nil {
		return x.FormId
	}
	return ""
}

type isGetReminderPointRequest_RequestIdentifier interface {
	isGetReminderPointRequest_RequestIdentifier()
}

type GetReminderPointRequest_ClientRequestId struct {
	// client request id for bank actions
	ClientRequestId string `protobuf:"bytes,5,opt,name=client_request_id,json=clientRequestId,proto3,oneof"`
}

func (*GetReminderPointRequest_ClientRequestId) isGetReminderPointRequest_RequestIdentifier() {}

type GetReminderPointResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// time point at which reminders need to be triggered
	// if no reminder is available, reminders will be stopped
	// the workflow will wait till the the below reminder point
	NextReminderPoint *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=next_reminder_point,json=nextReminderPoint,proto3" json:"next_reminder_point,omitempty"`
}

func (x *GetReminderPointResponse) Reset() {
	*x = GetReminderPointResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReminderPointResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReminderPointResponse) ProtoMessage() {}

func (x *GetReminderPointResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReminderPointResponse.ProtoReflect.Descriptor instead.
func (*GetReminderPointResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{3}
}

func (x *GetReminderPointResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *GetReminderPointResponse) GetNextReminderPoint() *timestamppb.Timestamp {
	if x != nil {
		return x.NextReminderPoint
	}
	return nil
}

// ValidateAndGetLeaHandler validates the lea complaint and fetches the handling params for the same
type ValidateAndGetLEAHandlerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// workflow id under consideration
	WorkflowId string `protobuf:"bytes,2,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	// unified lea complaint to be validated
	UnifiedLeaComplaint *lea.UnifiedLeaComplaint `protobuf:"bytes,3,opt,name=unified_lea_complaint,json=unifiedLeaComplaint,proto3" json:"unified_lea_complaint,omitempty"`
}

func (x *ValidateAndGetLEAHandlerRequest) Reset() {
	*x = ValidateAndGetLEAHandlerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateAndGetLEAHandlerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateAndGetLEAHandlerRequest) ProtoMessage() {}

func (x *ValidateAndGetLEAHandlerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateAndGetLEAHandlerRequest.ProtoReflect.Descriptor instead.
func (*ValidateAndGetLEAHandlerRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{4}
}

func (x *ValidateAndGetLEAHandlerRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ValidateAndGetLEAHandlerRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *ValidateAndGetLEAHandlerRequest) GetUnifiedLeaComplaint() *lea.UnifiedLeaComplaint {
	if x != nil {
		return x.UnifiedLeaComplaint
	}
	return nil
}

type ValidateAndGetLEAHandlerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// Intended handling for a unified LEA complaint
	HandlingParams *lea.HandlingParams `protobuf:"bytes,2,opt,name=handling_params,json=handlingParams,proto3" json:"handling_params,omitempty"`
}

func (x *ValidateAndGetLEAHandlerResponse) Reset() {
	*x = ValidateAndGetLEAHandlerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateAndGetLEAHandlerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateAndGetLEAHandlerResponse) ProtoMessage() {}

func (x *ValidateAndGetLEAHandlerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateAndGetLEAHandlerResponse.ProtoReflect.Descriptor instead.
func (*ValidateAndGetLEAHandlerResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{5}
}

func (x *ValidateAndGetLEAHandlerResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *ValidateAndGetLEAHandlerResponse) GetHandlingParams() *lea.HandlingParams {
	if x != nil {
		return x.HandlingParams
	}
	return nil
}

// Request payload for the GetNotificationTemplateForBankActions Activity
type GetNotificationTemplateForBankActionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Used to create and send form urls in the notification template
	FormId string `protobuf:"bytes,2,opt,name=form_id,json=formId,proto3" json:"form_id,omitempty"`
	// client request id for bank actions
	BankActionClientReqId string `protobuf:"bytes,3,opt,name=bank_action_client_req_id,json=bankActionClientReqId,proto3" json:"bank_action_client_req_id,omitempty"`
	// if the notification is a reminder
	IsReminder bool `protobuf:"varint,4,opt,name=is_reminder,json=isReminder,proto3" json:"is_reminder,omitempty"`
}

func (x *GetNotificationTemplateForBankActionsRequest) Reset() {
	*x = GetNotificationTemplateForBankActionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNotificationTemplateForBankActionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationTemplateForBankActionsRequest) ProtoMessage() {}

func (x *GetNotificationTemplateForBankActionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationTemplateForBankActionsRequest.ProtoReflect.Descriptor instead.
func (*GetNotificationTemplateForBankActionsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{6}
}

func (x *GetNotificationTemplateForBankActionsRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *GetNotificationTemplateForBankActionsRequest) GetFormId() string {
	if x != nil {
		return x.FormId
	}
	return ""
}

func (x *GetNotificationTemplateForBankActionsRequest) GetBankActionClientReqId() string {
	if x != nil {
		return x.BankActionClientReqId
	}
	return ""
}

func (x *GetNotificationTemplateForBankActionsRequest) GetIsReminder() bool {
	if x != nil {
		return x.IsReminder
	}
	return false
}

// Response payload for the GetNotificationTemplateForBankActions Activity
type GetNotificationTemplateForBankActionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// Contain notifications to be sent to the end users
	Notifications *notification.Notification `protobuf:"bytes,2,opt,name=notifications,proto3" json:"notifications,omitempty"`
}

func (x *GetNotificationTemplateForBankActionsResponse) Reset() {
	*x = GetNotificationTemplateForBankActionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNotificationTemplateForBankActionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationTemplateForBankActionsResponse) ProtoMessage() {}

func (x *GetNotificationTemplateForBankActionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationTemplateForBankActionsResponse.ProtoReflect.Descriptor instead.
func (*GetNotificationTemplateForBankActionsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{7}
}

func (x *GetNotificationTemplateForBankActionsResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *GetNotificationTemplateForBankActionsResponse) GetNotifications() *notification.Notification {
	if x != nil {
		return x.Notifications
	}
	return nil
}

// AppAccessRevokeRequest will revoke/restore the app access depending on Action param
type AppAccessUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// actor id for which we have to revoke app access
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// action taken against the actor id
	Action enums.Action `protobuf:"varint,3,opt,name=action,proto3,enum=enums.Action" json:"action,omitempty"`
	// reason for which we want to apply action
	RequestReason *risk.RequestReason `protobuf:"bytes,4,opt,name=request_reason,json=requestReason,proto3" json:"request_reason,omitempty"`
	// the flow via which this activity has been triggered
	InitiatedBy string `protobuf:"bytes,5,opt,name=initiated_by,json=initiatedBy,proto3" json:"initiated_by,omitempty"`
}

func (x *AppAccessUpdateRequest) Reset() {
	*x = AppAccessUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppAccessUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppAccessUpdateRequest) ProtoMessage() {}

func (x *AppAccessUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppAccessUpdateRequest.ProtoReflect.Descriptor instead.
func (*AppAccessUpdateRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{8}
}

func (x *AppAccessUpdateRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *AppAccessUpdateRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *AppAccessUpdateRequest) GetAction() enums.Action {
	if x != nil {
		return x.Action
	}
	return enums.Action(0)
}

func (x *AppAccessUpdateRequest) GetRequestReason() *risk.RequestReason {
	if x != nil {
		return x.RequestReason
	}
	return nil
}

func (x *AppAccessUpdateRequest) GetInitiatedBy() string {
	if x != nil {
		return x.InitiatedBy
	}
	return ""
}

type AppAccessUpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *AppAccessUpdateResponse) Reset() {
	*x = AppAccessUpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppAccessUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppAccessUpdateResponse) ProtoMessage() {}

func (x *AppAccessUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppAccessUpdateResponse.ProtoReflect.Descriptor instead.
func (*AppAccessUpdateResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{9}
}

func (x *AppAccessUpdateResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// CreateAlerts will create alerts
type CreateAsyncAlertsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// list of alerts we want to create
	RawAlerts []*case_management.RawAlert `protobuf:"bytes,2,rep,name=raw_alerts,json=rawAlerts,proto3" json:"raw_alerts,omitempty"`
}

func (x *CreateAsyncAlertsRequest) Reset() {
	*x = CreateAsyncAlertsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAsyncAlertsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAsyncAlertsRequest) ProtoMessage() {}

func (x *CreateAsyncAlertsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAsyncAlertsRequest.ProtoReflect.Descriptor instead.
func (*CreateAsyncAlertsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{10}
}

func (x *CreateAsyncAlertsRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CreateAsyncAlertsRequest) GetRawAlerts() []*case_management.RawAlert {
	if x != nil {
		return x.RawAlerts
	}
	return nil
}

type CreateAsyncAlertsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// count of alerts that failed to be created
	FailureCount uint32 `protobuf:"varint,2,opt,name=failure_count,json=failureCount,proto3" json:"failure_count,omitempty"`
	// list of alerts that failed to create
	Failures []*CreateAsyncAlertsResponse_Failure `protobuf:"bytes,3,rep,name=failures,proto3" json:"failures,omitempty"`
}

func (x *CreateAsyncAlertsResponse) Reset() {
	*x = CreateAsyncAlertsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAsyncAlertsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAsyncAlertsResponse) ProtoMessage() {}

func (x *CreateAsyncAlertsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAsyncAlertsResponse.ProtoReflect.Descriptor instead.
func (*CreateAsyncAlertsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{11}
}

func (x *CreateAsyncAlertsResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *CreateAsyncAlertsResponse) GetFailureCount() uint32 {
	if x != nil {
		return x.FailureCount
	}
	return 0
}

func (x *CreateAsyncAlertsResponse) GetFailures() []*CreateAsyncAlertsResponse_Failure {
	if x != nil {
		return x.Failures
	}
	return nil
}

// GetUnifiedLEAComms accepts handling_params and returns comms to be triggered for end user
type GetUnifiedLEACommsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// handling params of the unified LEA complaint
	HandlingParams *lea.HandlingParams `protobuf:"bytes,2,opt,name=handling_params,json=handlingParams,proto3" json:"handling_params,omitempty"`
	// if the comms we want to send is a reminder comms
	IsReminder bool `protobuf:"varint,3,opt,name=is_reminder,json=isReminder,proto3" json:"is_reminder,omitempty"`
}

func (x *GetUnifiedLEACommsRequest) Reset() {
	*x = GetUnifiedLEACommsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUnifiedLEACommsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUnifiedLEACommsRequest) ProtoMessage() {}

func (x *GetUnifiedLEACommsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUnifiedLEACommsRequest.ProtoReflect.Descriptor instead.
func (*GetUnifiedLEACommsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{12}
}

func (x *GetUnifiedLEACommsRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *GetUnifiedLEACommsRequest) GetHandlingParams() *lea.HandlingParams {
	if x != nil {
		return x.HandlingParams
	}
	return nil
}

func (x *GetUnifiedLEACommsRequest) GetIsReminder() bool {
	if x != nil {
		return x.IsReminder
	}
	return false
}

type GetUnifiedLEACommsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	Communications []*comms.Communication   `protobuf:"bytes,2,rep,name=communications,proto3" json:"communications,omitempty"`
}

func (x *GetUnifiedLEACommsResponse) Reset() {
	*x = GetUnifiedLEACommsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUnifiedLEACommsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUnifiedLEACommsResponse) ProtoMessage() {}

func (x *GetUnifiedLEACommsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUnifiedLEACommsResponse.ProtoReflect.Descriptor instead.
func (*GetUnifiedLEACommsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{13}
}

func (x *GetUnifiedLEACommsResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *GetUnifiedLEACommsResponse) GetCommunications() []*comms.Communication {
	if x != nil {
		return x.Communications
	}
	return nil
}

// ApplyLienRequest is the request for the ApplyLien workflow
type ApplyLienRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Request header with authentication information
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Account number on which the lien is to be applied
	AccountNumber string `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// Amount for the lien
	Amount float32 `protobuf:"fixed32,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// Currency code for the lien amount
	CurrencyCode string `protobuf:"bytes,4,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// Reason code for the lien
	ReasonCode string `protobuf:"bytes,5,opt,name=reason_code,json=reasonCode,proto3" json:"reason_code,omitempty"`
	// Remarks for the lien
	Remarks string `protobuf:"bytes,6,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// Start date for the lien
	StartDate *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// End date for the lien
	EndDate *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
}

func (x *ApplyLienRequest) Reset() {
	*x = ApplyLienRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyLienRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyLienRequest) ProtoMessage() {}

func (x *ApplyLienRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyLienRequest.ProtoReflect.Descriptor instead.
func (*ApplyLienRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{14}
}

func (x *ApplyLienRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ApplyLienRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *ApplyLienRequest) GetAmount() float32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *ApplyLienRequest) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *ApplyLienRequest) GetReasonCode() string {
	if x != nil {
		return x.ReasonCode
	}
	return ""
}

func (x *ApplyLienRequest) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *ApplyLienRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ApplyLienRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

// ApplyLienResponse is the response from the ApplyLien workflow
type ApplyLienResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response header with status information
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ApplyLienResponse) Reset() {
	*x = ApplyLienResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyLienResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyLienResponse) ProtoMessage() {}

func (x *ApplyLienResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyLienResponse.ProtoReflect.Descriptor instead.
func (*ApplyLienResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{15}
}

func (x *ApplyLienResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// AddLienRequest is the request for the AddLien activity
type AddLienRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Request header with authentication information
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Account number on which the lien is to be applied
	AccountNumber string `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// Amount for the lien
	Amount float32 `protobuf:"fixed32,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// Currency code for the lien amount
	CurrencyCode string `protobuf:"bytes,4,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// Reason code for the lien
	ReasonCode string `protobuf:"bytes,5,opt,name=reason_code,json=reasonCode,proto3" json:"reason_code,omitempty"`
	// Remarks for the lien
	Remarks string `protobuf:"bytes,6,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// Duration for which the lien is to be applied in hours
	LienDurationInHours int32 `protobuf:"varint,7,opt,name=lien_duration_in_hours,json=lienDurationInHours,proto3" json:"lien_duration_in_hours,omitempty"`
	// Channel request ID for the lien request
	ChannelRequestId string `protobuf:"bytes,8,opt,name=channel_request_id,json=channelRequestId,proto3" json:"channel_request_id,omitempty"`
	// id of the bank action entry created
	BankActionId string `protobuf:"bytes,9,opt,name=bank_action_id,json=bankActionId,proto3" json:"bank_action_id,omitempty"`
}

func (x *AddLienRequest) Reset() {
	*x = AddLienRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLienRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLienRequest) ProtoMessage() {}

func (x *AddLienRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLienRequest.ProtoReflect.Descriptor instead.
func (*AddLienRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{16}
}

func (x *AddLienRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *AddLienRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *AddLienRequest) GetAmount() float32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *AddLienRequest) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *AddLienRequest) GetReasonCode() string {
	if x != nil {
		return x.ReasonCode
	}
	return ""
}

func (x *AddLienRequest) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *AddLienRequest) GetLienDurationInHours() int32 {
	if x != nil {
		return x.LienDurationInHours
	}
	return 0
}

func (x *AddLienRequest) GetChannelRequestId() string {
	if x != nil {
		return x.ChannelRequestId
	}
	return ""
}

func (x *AddLienRequest) GetBankActionId() string {
	if x != nil {
		return x.BankActionId
	}
	return ""
}

// AddLienResponse is the response from the AddLien activity
type AddLienResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response header with status information
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *AddLienResponse) Reset() {
	*x = AddLienResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLienResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLienResponse) ProtoMessage() {}

func (x *AddLienResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLienResponse.ProtoReflect.Descriptor instead.
func (*AddLienResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{17}
}

func (x *AddLienResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// EnquireLienRequest is the request for the EnquireLien activity
type VerifyLienRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Request header with authentication information
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Account number for which to enquire the lien
	AccountNumber string `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	RequestId     string `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *VerifyLienRequest) Reset() {
	*x = VerifyLienRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyLienRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyLienRequest) ProtoMessage() {}

func (x *VerifyLienRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyLienRequest.ProtoReflect.Descriptor instead.
func (*VerifyLienRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{18}
}

func (x *VerifyLienRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *VerifyLienRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *VerifyLienRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

// EnquireLienResponse is the response from the EnquireLien activity
type VerifyLienResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response header with status information
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *VerifyLienResponse) Reset() {
	*x = VerifyLienResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyLienResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyLienResponse) ProtoMessage() {}

func (x *VerifyLienResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyLienResponse.ProtoReflect.Descriptor instead.
func (*VerifyLienResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{19}
}

func (x *VerifyLienResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// EnquireLienRequest is the request for the EnquireLien activity
type CheckForExistingLienRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Request header with authentication information
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Account number for which to enquire the lien
	AccountNumber string `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
}

func (x *CheckForExistingLienRequest) Reset() {
	*x = CheckForExistingLienRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckForExistingLienRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckForExistingLienRequest) ProtoMessage() {}

func (x *CheckForExistingLienRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckForExistingLienRequest.ProtoReflect.Descriptor instead.
func (*CheckForExistingLienRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{20}
}

func (x *CheckForExistingLienRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CheckForExistingLienRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

// EnquireLienResponse is the response from the EnquireLien activity
type CheckForExistingLienResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response header with status information
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// This indicates if there is an existing lien on the account
	HasExistingLien bool `protobuf:"varint,2,opt,name=has_existing_lien,json=hasExistingLien,proto3" json:"has_existing_lien,omitempty"`
}

func (x *CheckForExistingLienResponse) Reset() {
	*x = CheckForExistingLienResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckForExistingLienResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckForExistingLienResponse) ProtoMessage() {}

func (x *CheckForExistingLienResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckForExistingLienResponse.ProtoReflect.Descriptor instead.
func (*CheckForExistingLienResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{21}
}

func (x *CheckForExistingLienResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *CheckForExistingLienResponse) GetHasExistingLien() bool {
	if x != nil {
		return x.HasExistingLien
	}
	return false
}

type GetCommsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Request header with authentication information
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Account number for which to get communications
	AccountNumber string `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// time from which the lien will be applied
	StartDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// time until which the lien will be applied
	EndDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// amount that is marked as lien for the account
	Amount *money.Money `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *GetCommsRequest) Reset() {
	*x = GetCommsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommsRequest) ProtoMessage() {}

func (x *GetCommsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommsRequest.ProtoReflect.Descriptor instead.
func (*GetCommsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{22}
}

func (x *GetCommsRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *GetCommsRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *GetCommsRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetCommsRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *GetCommsRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

type GetCommsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response header with status information
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// List of communications to be sent
	Communications []*comms.Communication `protobuf:"bytes,2,rep,name=communications,proto3" json:"communications,omitempty"`
	EntityId       string                 `protobuf:"bytes,3,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
}

func (x *GetCommsResponse) Reset() {
	*x = GetCommsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommsResponse) ProtoMessage() {}

func (x *GetCommsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommsResponse.ProtoReflect.Descriptor instead.
func (*GetCommsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{23}
}

func (x *GetCommsResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *GetCommsResponse) GetCommunications() []*comms.Communication {
	if x != nil {
		return x.Communications
	}
	return nil
}

func (x *GetCommsResponse) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

type UpdateBankActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Request header with authentication information
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	ClientReqId   string                  `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	State         enums.State             `protobuf:"varint,3,opt,name=state,proto3,enum=enums.State" json:"state,omitempty"`
}

func (x *UpdateBankActionRequest) Reset() {
	*x = UpdateBankActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBankActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBankActionRequest) ProtoMessage() {}

func (x *UpdateBankActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBankActionRequest.ProtoReflect.Descriptor instead.
func (*UpdateBankActionRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{24}
}

func (x *UpdateBankActionRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *UpdateBankActionRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *UpdateBankActionRequest) GetState() enums.State {
	if x != nil {
		return x.State
	}
	return enums.State(0)
}

// UpdateBankActionResponse is the response from the UpdateBankAction activity
type UpdateBankActionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response header with status information
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *UpdateBankActionResponse) Reset() {
	*x = UpdateBankActionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBankActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBankActionResponse) ProtoMessage() {}

func (x *UpdateBankActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBankActionResponse.ProtoReflect.Descriptor instead.
func (*UpdateBankActionResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateBankActionResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// Generalized distributed lock acquisition request
type AcquireDistributedLockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Lock key to acquire
	LockKey string `protobuf:"bytes,2,opt,name=lock_key,json=lockKey,proto3" json:"lock_key,omitempty"`
	// Duration for which the lock should be held
	// max lock duration supported is 15 mins
	LockDuration *durationpb.Duration `protobuf:"bytes,3,opt,name=lock_duration,json=lockDuration,proto3" json:"lock_duration,omitempty"`
}

func (x *AcquireDistributedLockRequest) Reset() {
	*x = AcquireDistributedLockRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcquireDistributedLockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcquireDistributedLockRequest) ProtoMessage() {}

func (x *AcquireDistributedLockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcquireDistributedLockRequest.ProtoReflect.Descriptor instead.
func (*AcquireDistributedLockRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{26}
}

func (x *AcquireDistributedLockRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *AcquireDistributedLockRequest) GetLockKey() string {
	if x != nil {
		return x.LockKey
	}
	return ""
}

func (x *AcquireDistributedLockRequest) GetLockDuration() *durationpb.Duration {
	if x != nil {
		return x.LockDuration
	}
	return nil
}

// Generalized distributed lock acquisition response
type AcquireDistributedLockResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// Lock expiry time
	LockExpiryTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=lock_expiry_time,json=lockExpiryTime,proto3" json:"lock_expiry_time,omitempty"`
	// would be used to release the lock
	LockToken string `protobuf:"bytes,3,opt,name=lock_token,json=lockToken,proto3" json:"lock_token,omitempty"`
}

func (x *AcquireDistributedLockResponse) Reset() {
	*x = AcquireDistributedLockResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcquireDistributedLockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcquireDistributedLockResponse) ProtoMessage() {}

func (x *AcquireDistributedLockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcquireDistributedLockResponse.ProtoReflect.Descriptor instead.
func (*AcquireDistributedLockResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{27}
}

func (x *AcquireDistributedLockResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *AcquireDistributedLockResponse) GetLockExpiryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LockExpiryTime
	}
	return nil
}

func (x *AcquireDistributedLockResponse) GetLockToken() string {
	if x != nil {
		return x.LockToken
	}
	return ""
}

// Generalized distributed lock release request
type ReleaseDistributedLockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Lock key to release
	LockKey string `protobuf:"bytes,2,opt,name=lock_key,json=lockKey,proto3" json:"lock_key,omitempty"`
	// Lock token to release
	LockToken string `protobuf:"bytes,3,opt,name=lock_token,json=lockToken,proto3" json:"lock_token,omitempty"`
}

func (x *ReleaseDistributedLockRequest) Reset() {
	*x = ReleaseDistributedLockRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseDistributedLockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseDistributedLockRequest) ProtoMessage() {}

func (x *ReleaseDistributedLockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseDistributedLockRequest.ProtoReflect.Descriptor instead.
func (*ReleaseDistributedLockRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{28}
}

func (x *ReleaseDistributedLockRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ReleaseDistributedLockRequest) GetLockKey() string {
	if x != nil {
		return x.LockKey
	}
	return ""
}

func (x *ReleaseDistributedLockRequest) GetLockToken() string {
	if x != nil {
		return x.LockToken
	}
	return ""
}

// Generalized distributed lock release response
type ReleaseDistributedLockResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ReleaseDistributedLockResponse) Reset() {
	*x = ReleaseDistributedLockResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseDistributedLockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseDistributedLockResponse) ProtoMessage() {}

func (x *ReleaseDistributedLockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseDistributedLockResponse.ProtoReflect.Descriptor instead.
func (*ReleaseDistributedLockResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{29}
}

func (x *ReleaseDistributedLockResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// Update bulk status for Freshdesk tickets request
type UpdateBulkStatusForFreshdeskTicketsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// List of ticket IDs to update
	TicketIds []string `protobuf:"bytes,2,rep,name=ticket_ids,json=ticketIds,proto3" json:"ticket_ids,omitempty"`
	// Model 1 name to update
	Model_1Name string `protobuf:"bytes,3,opt,name=model_1_name,json=model1Name,proto3" json:"model_1_name,omitempty"`
	// Model 1 score to update
	Model_1Score float32 `protobuf:"fixed32,4,opt,name=model_1_score,json=model1Score,proto3" json:"model_1_score,omitempty"`
	// Model 2 name to update
	Model_2Name string `protobuf:"bytes,5,opt,name=model_2_name,json=model2Name,proto3" json:"model_2_name,omitempty"`
	// Model 2 score to update
	Model_2Score float32 `protobuf:"fixed32,6,opt,name=model_2_score,json=model2Score,proto3" json:"model_2_score,omitempty"`
	// Confidence Score to update
	ConfidenceScore float32 `protobuf:"fixed32,7,opt,name=confidence_score,json=confidenceScore,proto3" json:"confidence_score,omitempty"`
	// Update field mask
	UpdateFieldMask []enums.TicketBulkUpdateFieldMask `protobuf:"varint,8,rep,packed,name=update_field_mask,json=updateFieldMask,proto3,enum=enums.TicketBulkUpdateFieldMask" json:"update_field_mask,omitempty"`
}

func (x *UpdateBulkStatusForFreshdeskTicketsRequest) Reset() {
	*x = UpdateBulkStatusForFreshdeskTicketsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBulkStatusForFreshdeskTicketsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBulkStatusForFreshdeskTicketsRequest) ProtoMessage() {}

func (x *UpdateBulkStatusForFreshdeskTicketsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBulkStatusForFreshdeskTicketsRequest.ProtoReflect.Descriptor instead.
func (*UpdateBulkStatusForFreshdeskTicketsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{30}
}

func (x *UpdateBulkStatusForFreshdeskTicketsRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *UpdateBulkStatusForFreshdeskTicketsRequest) GetTicketIds() []string {
	if x != nil {
		return x.TicketIds
	}
	return nil
}

func (x *UpdateBulkStatusForFreshdeskTicketsRequest) GetModel_1Name() string {
	if x != nil {
		return x.Model_1Name
	}
	return ""
}

func (x *UpdateBulkStatusForFreshdeskTicketsRequest) GetModel_1Score() float32 {
	if x != nil {
		return x.Model_1Score
	}
	return 0
}

func (x *UpdateBulkStatusForFreshdeskTicketsRequest) GetModel_2Name() string {
	if x != nil {
		return x.Model_2Name
	}
	return ""
}

func (x *UpdateBulkStatusForFreshdeskTicketsRequest) GetModel_2Score() float32 {
	if x != nil {
		return x.Model_2Score
	}
	return 0
}

func (x *UpdateBulkStatusForFreshdeskTicketsRequest) GetConfidenceScore() float32 {
	if x != nil {
		return x.ConfidenceScore
	}
	return 0
}

func (x *UpdateBulkStatusForFreshdeskTicketsRequest) GetUpdateFieldMask() []enums.TicketBulkUpdateFieldMask {
	if x != nil {
		return x.UpdateFieldMask
	}
	return nil
}

// Update bulk status for Freshdesk tickets response
type UpdateBulkStatusForFreshdeskTicketsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// Job ID returned from the bulk update API
	JobId string `protobuf:"bytes,2,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	// HREF URL to check job status
	Href string `protobuf:"bytes,3,opt,name=href,proto3" json:"href,omitempty"`
}

func (x *UpdateBulkStatusForFreshdeskTicketsResponse) Reset() {
	*x = UpdateBulkStatusForFreshdeskTicketsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBulkStatusForFreshdeskTicketsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBulkStatusForFreshdeskTicketsResponse) ProtoMessage() {}

func (x *UpdateBulkStatusForFreshdeskTicketsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBulkStatusForFreshdeskTicketsResponse.ProtoReflect.Descriptor instead.
func (*UpdateBulkStatusForFreshdeskTicketsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{31}
}

func (x *UpdateBulkStatusForFreshdeskTicketsResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *UpdateBulkStatusForFreshdeskTicketsResponse) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *UpdateBulkStatusForFreshdeskTicketsResponse) GetHref() string {
	if x != nil {
		return x.Href
	}
	return ""
}

// Check job status for Freshdesk request
type CheckJobStatusForFreshdeskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Job ID to check status for
	JobId string `protobuf:"bytes,2,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
}

func (x *CheckJobStatusForFreshdeskRequest) Reset() {
	*x = CheckJobStatusForFreshdeskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckJobStatusForFreshdeskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckJobStatusForFreshdeskRequest) ProtoMessage() {}

func (x *CheckJobStatusForFreshdeskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckJobStatusForFreshdeskRequest.ProtoReflect.Descriptor instead.
func (*CheckJobStatusForFreshdeskRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{32}
}

func (x *CheckJobStatusForFreshdeskRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CheckJobStatusForFreshdeskRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

// Check job status for Freshdesk response
type CheckJobStatusForFreshdeskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// Job status: "IN PROGRESS", "COMPLETED", or "FAILED"
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// Progress percentage (0-100)
	Progress int32 `protobuf:"varint,3,opt,name=progress,proto3" json:"progress,omitempty"`
	// Indicates if the job is completed
	IsCompleted bool `protobuf:"varint,4,opt,name=is_completed,json=isCompleted,proto3" json:"is_completed,omitempty"`
	// Indicates if the job failed
	IsFailed bool `protobuf:"varint,5,opt,name=is_failed,json=isFailed,proto3" json:"is_failed,omitempty"`
	// List of all the cases that are processed
	CaseData []*CheckJobStatusForFreshdeskResponse_CaseData `protobuf:"bytes,6,rep,name=case_data,json=caseData,proto3" json:"case_data,omitempty"`
}

func (x *CheckJobStatusForFreshdeskResponse) Reset() {
	*x = CheckJobStatusForFreshdeskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckJobStatusForFreshdeskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckJobStatusForFreshdeskResponse) ProtoMessage() {}

func (x *CheckJobStatusForFreshdeskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckJobStatusForFreshdeskResponse.ProtoReflect.Descriptor instead.
func (*CheckJobStatusForFreshdeskResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{33}
}

func (x *CheckJobStatusForFreshdeskResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *CheckJobStatusForFreshdeskResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CheckJobStatusForFreshdeskResponse) GetProgress() int32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *CheckJobStatusForFreshdeskResponse) GetIsCompleted() bool {
	if x != nil {
		return x.IsCompleted
	}
	return false
}

func (x *CheckJobStatusForFreshdeskResponse) GetIsFailed() bool {
	if x != nil {
		return x.IsFailed
	}
	return false
}

func (x *CheckJobStatusForFreshdeskResponse) GetCaseData() []*CheckJobStatusForFreshdeskResponse_CaseData {
	if x != nil {
		return x.CaseData
	}
	return nil
}

type CreateAsyncAlertsResponse_Failure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Alert  *case_management.RawAlert `protobuf:"bytes,1,opt,name=alert,proto3" json:"alert,omitempty"`
	Reason string                    `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *CreateAsyncAlertsResponse_Failure) Reset() {
	*x = CreateAsyncAlertsResponse_Failure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAsyncAlertsResponse_Failure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAsyncAlertsResponse_Failure) ProtoMessage() {}

func (x *CreateAsyncAlertsResponse_Failure) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAsyncAlertsResponse_Failure.ProtoReflect.Descriptor instead.
func (*CreateAsyncAlertsResponse_Failure) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{11, 0}
}

func (x *CreateAsyncAlertsResponse_Failure) GetAlert() *case_management.RawAlert {
	if x != nil {
		return x.Alert
	}
	return nil
}

func (x *CreateAsyncAlertsResponse_Failure) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// Cases processed and their reasons
type CheckJobStatusForFreshdeskResponse_CaseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Error string `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Field string `protobuf:"bytes,3,opt,name=field,proto3" json:"field,omitempty"`
	Code  string `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *CheckJobStatusForFreshdeskResponse_CaseData) Reset() {
	*x = CheckJobStatusForFreshdeskResponse_CaseData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_activity_activity_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckJobStatusForFreshdeskResponse_CaseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckJobStatusForFreshdeskResponse_CaseData) ProtoMessage() {}

func (x *CheckJobStatusForFreshdeskResponse_CaseData) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_activity_activity_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckJobStatusForFreshdeskResponse_CaseData.ProtoReflect.Descriptor instead.
func (*CheckJobStatusForFreshdeskResponse_CaseData) Descriptor() ([]byte, []int) {
	return file_api_risk_activity_activity_proto_rawDescGZIP(), []int{33, 0}
}

func (x *CheckJobStatusForFreshdeskResponse_CaseData) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CheckJobStatusForFreshdeskResponse_CaseData) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *CheckJobStatusForFreshdeskResponse_CaseData) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *CheckJobStatusForFreshdeskResponse_CaseData) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

var File_api_risk_activity_activity_proto protoreflect.FileDescriptor

var file_api_risk_activity_activity_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0d, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x69, 0x73, 0x6b, 0x2f, 0x62, 0x61, 0x6e, 0x6b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69,
	0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a,
	0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x65, 0x61, 0x2f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x5f, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x65, 0x61, 0x2f, 0x75,
	0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xda, 0x02,
	0x0a, 0x16, 0x41, 0x64, 0x64, 0x54, 0x6f, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x28, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0e, 0x63, 0x6f, 0x6d,
	0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x8c, 0x01, 0x0a, 0x17, 0x41,
	0x64, 0x64, 0x54, 0x6f, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x61, 0x6e,
	0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xf3, 0x02, 0x0a, 0x17, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x41, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69,
	0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x4a, 0x0a, 0x13, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x11, 0x72, 0x65, 0x6d,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x42, 0x14, 0x0a, 0x12, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22,
	0xb3, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4a, 0x0a, 0x13, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x11, 0x6e, 0x65, 0x78, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0xdb, 0x01, 0x0a, 0x1f, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x6e, 0x64, 0x47, 0x65, 0x74, 0x4c, 0x45, 0x41, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x15, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f,
	0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x55, 0x6e, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x13,
	0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x22, 0xae, 0x01, 0x0a, 0x20, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x6e, 0x64, 0x47, 0x65, 0x74, 0x4c, 0x45, 0x41, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x0f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x52, 0x0e, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x22, 0xec, 0x01, 0x0a, 0x2c, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x17, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x19, 0x62, 0x61, 0x6e, 0x6b,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x62, 0x61, 0x6e,
	0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x52, 0x65, 0x6d, 0x69, 0x6e,
	0x64, 0x65, 0x72, 0x22, 0xd1, 0x01, 0x0a, 0x2d, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x46,
	0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x53, 0x0a, 0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x96, 0x02, 0x0a, 0x16, 0x41, 0x70, 0x70, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x0d, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x3a, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x21, 0x0a,
	0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x22, 0x66, 0x0a, 0x17, 0x41, 0x70, 0x70, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xa3, 0x01, 0x0a, 0x18, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x3d, 0x0a, 0x0a, 0x72, 0x61, 0x77, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x61, 0x77, 0x41, 0x6c,
	0x65, 0x72, 0x74, 0x52, 0x09, 0x72, 0x61, 0x77, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x22, 0xb4,
	0x02, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x41, 0x6c,
	0x65, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4c,
	0x0a, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x52, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x1a, 0x57, 0x0a, 0x07,
	0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x34, 0x0a, 0x05, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x61,
	0x77, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xc5, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65,
	0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3d, 0x0a,
	0x0f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0e, 0x68, 0x61,
	0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x69, 0x73, 0x5f, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x69, 0x73, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x22, 0xa7, 0x01,
	0x0a, 0x1a, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4c, 0x45, 0x41, 0x43,
	0x6f, 0x6d, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0e, 0x63, 0x6f, 0x6d,
	0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xed, 0x02, 0x0a, 0x10, 0x41, 0x70, 0x70, 0x6c,
	0x79, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0x60, 0x0a, 0x11, 0x41, 0x70, 0x70, 0x6c, 0x79,
	0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x82, 0x03, 0x0a, 0x0e, 0x41, 0x64,
	0x64, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x33, 0x0a, 0x16, 0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x6c, 0x69, 0x65, 0x6e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x61, 0x6e, 0x6b,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x5e,
	0x0a, 0x0f, 0x41, 0x64, 0x64, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xa3,
	0x01, 0x0a, 0x11, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63,
	0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x22, 0x61, 0x0a, 0x12, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4c, 0x69,
	0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x8e, 0x01, 0x0a, 0x1b, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x65, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x97, 0x01, 0x0a, 0x1c, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x65,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x68, 0x61, 0x73, 0x5f, 0x65, 0x78,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0f, 0x68, 0x61, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x4c, 0x69,
	0x65, 0x6e, 0x22, 0xa0, 0x02, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xba, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x75,
	0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x49, 0x64, 0x22, 0xab, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x61, 0x6e,
	0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48,
	0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69,
	0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x22, 0x67, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xda, 0x01, 0x0a, 0x1d, 0x41, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64,
	0x4c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x07, 0x6c, 0x6f, 0x63, 0x6b, 0x4b, 0x65, 0x79, 0x12, 0x4b, 0x0a, 0x0d, 0x6c, 0x6f, 0x63,
	0x6b, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0xfa, 0x42, 0x08,
	0xaa, 0x01, 0x05, 0x1a, 0x03, 0x08, 0x84, 0x07, 0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xd2, 0x01, 0x0a, 0x1e, 0x41, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x10, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6c, 0x6f,
	0x63, 0x6b, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xac, 0x01, 0x0a, 0x1d,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x64, 0x4c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x6b, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x07, 0x6c, 0x6f, 0x63, 0x6b, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6c,
	0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x6d, 0x0a, 0x1e, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64,
	0x4c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x9a, 0x03, 0x0a, 0x2a, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x75, 0x6c, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x6f,
	0x72, 0x46, 0x72, 0x65, 0x73, 0x68, 0x64, 0x65, 0x73, 0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64,
	0x73, 0x12, 0x20, 0x0a, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x31, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x31, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x31, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x31, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x32, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x32, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x32, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x32, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x29, 0x0a,
	0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x4c, 0x0a, 0x11, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x22, 0xa5, 0x01, 0x0a, 0x2b, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x75, 0x6c, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x6f, 0x72, 0x46, 0x72,
	0x65, 0x73, 0x68, 0x64, 0x65, 0x73, 0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x72,
	0x65, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x72, 0x65, 0x66, 0x22, 0x8d,
	0x01, 0x0a, 0x21, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x46, 0x6f, 0x72, 0x46, 0x72, 0x65, 0x73, 0x68, 0x64, 0x65, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63,
	0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1e,
	0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x9a,
	0x03, 0x0a, 0x22, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x46, 0x6f, 0x72, 0x46, 0x72, 0x65, 0x73, 0x68, 0x64, 0x65, 0x73, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73,
	0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x12, 0x57, 0x0a, 0x09, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4a,
	0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x6f, 0x72, 0x46, 0x72, 0x65, 0x73, 0x68,
	0x64, 0x65, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x61, 0x73,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x63, 0x61, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x1a,
	0x5a, 0x0a, 0x08, 0x43, 0x61, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x54, 0x0a, 0x28, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_activity_activity_proto_rawDescOnce sync.Once
	file_api_risk_activity_activity_proto_rawDescData = file_api_risk_activity_activity_proto_rawDesc
)

func file_api_risk_activity_activity_proto_rawDescGZIP() []byte {
	file_api_risk_activity_activity_proto_rawDescOnce.Do(func() {
		file_api_risk_activity_activity_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_activity_activity_proto_rawDescData)
	})
	return file_api_risk_activity_activity_proto_rawDescData
}

var file_api_risk_activity_activity_proto_msgTypes = make([]protoimpl.MessageInfo, 36)
var file_api_risk_activity_activity_proto_goTypes = []interface{}{
	(*AddToBankActionRequest)(nil),                        // 0: risk.activity.AddToBankActionRequest
	(*AddToBankActionResponse)(nil),                       // 1: risk.activity.AddToBankActionResponse
	(*GetReminderPointRequest)(nil),                       // 2: risk.activity.GetReminderPointRequest
	(*GetReminderPointResponse)(nil),                      // 3: risk.activity.GetReminderPointResponse
	(*ValidateAndGetLEAHandlerRequest)(nil),               // 4: risk.activity.ValidateAndGetLEAHandlerRequest
	(*ValidateAndGetLEAHandlerResponse)(nil),              // 5: risk.activity.ValidateAndGetLEAHandlerResponse
	(*GetNotificationTemplateForBankActionsRequest)(nil),  // 6: risk.activity.GetNotificationTemplateForBankActionsRequest
	(*GetNotificationTemplateForBankActionsResponse)(nil), // 7: risk.activity.GetNotificationTemplateForBankActionsResponse
	(*AppAccessUpdateRequest)(nil),                        // 8: risk.activity.AppAccessUpdateRequest
	(*AppAccessUpdateResponse)(nil),                       // 9: risk.activity.AppAccessUpdateResponse
	(*CreateAsyncAlertsRequest)(nil),                      // 10: risk.activity.CreateAsyncAlertsRequest
	(*CreateAsyncAlertsResponse)(nil),                     // 11: risk.activity.CreateAsyncAlertsResponse
	(*GetUnifiedLEACommsRequest)(nil),                     // 12: risk.activity.GetUnifiedLEACommsRequest
	(*GetUnifiedLEACommsResponse)(nil),                    // 13: risk.activity.GetUnifiedLEACommsResponse
	(*ApplyLienRequest)(nil),                              // 14: risk.activity.ApplyLienRequest
	(*ApplyLienResponse)(nil),                             // 15: risk.activity.ApplyLienResponse
	(*AddLienRequest)(nil),                                // 16: risk.activity.AddLienRequest
	(*AddLienResponse)(nil),                               // 17: risk.activity.AddLienResponse
	(*VerifyLienRequest)(nil),                             // 18: risk.activity.VerifyLienRequest
	(*VerifyLienResponse)(nil),                            // 19: risk.activity.VerifyLienResponse
	(*CheckForExistingLienRequest)(nil),                   // 20: risk.activity.CheckForExistingLienRequest
	(*CheckForExistingLienResponse)(nil),                  // 21: risk.activity.CheckForExistingLienResponse
	(*GetCommsRequest)(nil),                               // 22: risk.activity.GetCommsRequest
	(*GetCommsResponse)(nil),                              // 23: risk.activity.GetCommsResponse
	(*UpdateBankActionRequest)(nil),                       // 24: risk.activity.UpdateBankActionRequest
	(*UpdateBankActionResponse)(nil),                      // 25: risk.activity.UpdateBankActionResponse
	(*AcquireDistributedLockRequest)(nil),                 // 26: risk.activity.AcquireDistributedLockRequest
	(*AcquireDistributedLockResponse)(nil),                // 27: risk.activity.AcquireDistributedLockResponse
	(*ReleaseDistributedLockRequest)(nil),                 // 28: risk.activity.ReleaseDistributedLockRequest
	(*ReleaseDistributedLockResponse)(nil),                // 29: risk.activity.ReleaseDistributedLockResponse
	(*UpdateBulkStatusForFreshdeskTicketsRequest)(nil),    // 30: risk.activity.UpdateBulkStatusForFreshdeskTicketsRequest
	(*UpdateBulkStatusForFreshdeskTicketsResponse)(nil),   // 31: risk.activity.UpdateBulkStatusForFreshdeskTicketsResponse
	(*CheckJobStatusForFreshdeskRequest)(nil),             // 32: risk.activity.CheckJobStatusForFreshdeskRequest
	(*CheckJobStatusForFreshdeskResponse)(nil),            // 33: risk.activity.CheckJobStatusForFreshdeskResponse
	(*CreateAsyncAlertsResponse_Failure)(nil),             // 34: risk.activity.CreateAsyncAlertsResponse.Failure
	(*CheckJobStatusForFreshdeskResponse_CaseData)(nil),   // 35: risk.activity.CheckJobStatusForFreshdeskResponse.CaseData
	(*activity.RequestHeader)(nil),                        // 36: celestial.activity.RequestHeader
	(enums.Action)(0),                                     // 37: enums.Action
	(*risk.RequestReason)(nil),                            // 38: risk.RequestReason
	(enums.CommsTemplate)(0),                              // 39: enums.CommsTemplate
	(*activity.ResponseHeader)(nil),                       // 40: celestial.activity.ResponseHeader
	(*workflow.TypeEnum)(nil),                             // 41: celestial.workflow.TypeEnum
	(*timestamppb.Timestamp)(nil),                         // 42: google.protobuf.Timestamp
	(*lea.UnifiedLeaComplaint)(nil),                       // 43: risk.UnifiedLeaComplaint
	(*lea.HandlingParams)(nil),                            // 44: risk.HandlingParams
	(*notification.Notification)(nil),                     // 45: celestial.activity.notification.Notification
	(*case_management.RawAlert)(nil),                      // 46: risk.case_management.RawAlert
	(*comms.Communication)(nil),                           // 47: comms.Communication
	(*money.Money)(nil),                                   // 48: google.type.Money
	(enums.State)(0),                                      // 49: enums.State
	(*durationpb.Duration)(nil),                           // 50: google.protobuf.Duration
	(enums.TicketBulkUpdateFieldMask)(0),                  // 51: enums.TicketBulkUpdateFieldMask
}
var file_api_risk_activity_activity_proto_depIdxs = []int32{
	36, // 0: risk.activity.AddToBankActionRequest.request_header:type_name -> celestial.activity.RequestHeader
	37, // 1: risk.activity.AddToBankActionRequest.action:type_name -> enums.Action
	38, // 2: risk.activity.AddToBankActionRequest.request_reason:type_name -> risk.RequestReason
	39, // 3: risk.activity.AddToBankActionRequest.comms_template:type_name -> enums.CommsTemplate
	40, // 4: risk.activity.AddToBankActionResponse.response_header:type_name -> celestial.activity.ResponseHeader
	36, // 5: risk.activity.GetReminderPointRequest.request_header:type_name -> celestial.activity.RequestHeader
	41, // 6: risk.activity.GetReminderPointRequest.workflow_type:type_name -> celestial.workflow.TypeEnum
	42, // 7: risk.activity.GetReminderPointRequest.reminder_start_time:type_name -> google.protobuf.Timestamp
	40, // 8: risk.activity.GetReminderPointResponse.response_header:type_name -> celestial.activity.ResponseHeader
	42, // 9: risk.activity.GetReminderPointResponse.next_reminder_point:type_name -> google.protobuf.Timestamp
	36, // 10: risk.activity.ValidateAndGetLEAHandlerRequest.request_header:type_name -> celestial.activity.RequestHeader
	43, // 11: risk.activity.ValidateAndGetLEAHandlerRequest.unified_lea_complaint:type_name -> risk.UnifiedLeaComplaint
	40, // 12: risk.activity.ValidateAndGetLEAHandlerResponse.response_header:type_name -> celestial.activity.ResponseHeader
	44, // 13: risk.activity.ValidateAndGetLEAHandlerResponse.handling_params:type_name -> risk.HandlingParams
	36, // 14: risk.activity.GetNotificationTemplateForBankActionsRequest.request_header:type_name -> celestial.activity.RequestHeader
	40, // 15: risk.activity.GetNotificationTemplateForBankActionsResponse.response_header:type_name -> celestial.activity.ResponseHeader
	45, // 16: risk.activity.GetNotificationTemplateForBankActionsResponse.notifications:type_name -> celestial.activity.notification.Notification
	36, // 17: risk.activity.AppAccessUpdateRequest.request_header:type_name -> celestial.activity.RequestHeader
	37, // 18: risk.activity.AppAccessUpdateRequest.action:type_name -> enums.Action
	38, // 19: risk.activity.AppAccessUpdateRequest.request_reason:type_name -> risk.RequestReason
	40, // 20: risk.activity.AppAccessUpdateResponse.response_header:type_name -> celestial.activity.ResponseHeader
	36, // 21: risk.activity.CreateAsyncAlertsRequest.request_header:type_name -> celestial.activity.RequestHeader
	46, // 22: risk.activity.CreateAsyncAlertsRequest.raw_alerts:type_name -> risk.case_management.RawAlert
	40, // 23: risk.activity.CreateAsyncAlertsResponse.response_header:type_name -> celestial.activity.ResponseHeader
	34, // 24: risk.activity.CreateAsyncAlertsResponse.failures:type_name -> risk.activity.CreateAsyncAlertsResponse.Failure
	36, // 25: risk.activity.GetUnifiedLEACommsRequest.request_header:type_name -> celestial.activity.RequestHeader
	44, // 26: risk.activity.GetUnifiedLEACommsRequest.handling_params:type_name -> risk.HandlingParams
	40, // 27: risk.activity.GetUnifiedLEACommsResponse.response_header:type_name -> celestial.activity.ResponseHeader
	47, // 28: risk.activity.GetUnifiedLEACommsResponse.communications:type_name -> comms.Communication
	36, // 29: risk.activity.ApplyLienRequest.request_header:type_name -> celestial.activity.RequestHeader
	42, // 30: risk.activity.ApplyLienRequest.start_date:type_name -> google.protobuf.Timestamp
	42, // 31: risk.activity.ApplyLienRequest.end_date:type_name -> google.protobuf.Timestamp
	40, // 32: risk.activity.ApplyLienResponse.response_header:type_name -> celestial.activity.ResponseHeader
	36, // 33: risk.activity.AddLienRequest.request_header:type_name -> celestial.activity.RequestHeader
	40, // 34: risk.activity.AddLienResponse.response_header:type_name -> celestial.activity.ResponseHeader
	36, // 35: risk.activity.VerifyLienRequest.request_header:type_name -> celestial.activity.RequestHeader
	40, // 36: risk.activity.VerifyLienResponse.response_header:type_name -> celestial.activity.ResponseHeader
	36, // 37: risk.activity.CheckForExistingLienRequest.request_header:type_name -> celestial.activity.RequestHeader
	40, // 38: risk.activity.CheckForExistingLienResponse.response_header:type_name -> celestial.activity.ResponseHeader
	36, // 39: risk.activity.GetCommsRequest.request_header:type_name -> celestial.activity.RequestHeader
	42, // 40: risk.activity.GetCommsRequest.start_date:type_name -> google.protobuf.Timestamp
	42, // 41: risk.activity.GetCommsRequest.end_date:type_name -> google.protobuf.Timestamp
	48, // 42: risk.activity.GetCommsRequest.amount:type_name -> google.type.Money
	40, // 43: risk.activity.GetCommsResponse.response_header:type_name -> celestial.activity.ResponseHeader
	47, // 44: risk.activity.GetCommsResponse.communications:type_name -> comms.Communication
	36, // 45: risk.activity.UpdateBankActionRequest.request_header:type_name -> celestial.activity.RequestHeader
	49, // 46: risk.activity.UpdateBankActionRequest.state:type_name -> enums.State
	40, // 47: risk.activity.UpdateBankActionResponse.response_header:type_name -> celestial.activity.ResponseHeader
	36, // 48: risk.activity.AcquireDistributedLockRequest.request_header:type_name -> celestial.activity.RequestHeader
	50, // 49: risk.activity.AcquireDistributedLockRequest.lock_duration:type_name -> google.protobuf.Duration
	40, // 50: risk.activity.AcquireDistributedLockResponse.response_header:type_name -> celestial.activity.ResponseHeader
	42, // 51: risk.activity.AcquireDistributedLockResponse.lock_expiry_time:type_name -> google.protobuf.Timestamp
	36, // 52: risk.activity.ReleaseDistributedLockRequest.request_header:type_name -> celestial.activity.RequestHeader
	40, // 53: risk.activity.ReleaseDistributedLockResponse.response_header:type_name -> celestial.activity.ResponseHeader
	36, // 54: risk.activity.UpdateBulkStatusForFreshdeskTicketsRequest.request_header:type_name -> celestial.activity.RequestHeader
	51, // 55: risk.activity.UpdateBulkStatusForFreshdeskTicketsRequest.update_field_mask:type_name -> enums.TicketBulkUpdateFieldMask
	40, // 56: risk.activity.UpdateBulkStatusForFreshdeskTicketsResponse.response_header:type_name -> celestial.activity.ResponseHeader
	36, // 57: risk.activity.CheckJobStatusForFreshdeskRequest.request_header:type_name -> celestial.activity.RequestHeader
	40, // 58: risk.activity.CheckJobStatusForFreshdeskResponse.response_header:type_name -> celestial.activity.ResponseHeader
	35, // 59: risk.activity.CheckJobStatusForFreshdeskResponse.case_data:type_name -> risk.activity.CheckJobStatusForFreshdeskResponse.CaseData
	46, // 60: risk.activity.CreateAsyncAlertsResponse.Failure.alert:type_name -> risk.case_management.RawAlert
	61, // [61:61] is the sub-list for method output_type
	61, // [61:61] is the sub-list for method input_type
	61, // [61:61] is the sub-list for extension type_name
	61, // [61:61] is the sub-list for extension extendee
	0,  // [0:61] is the sub-list for field type_name
}

func init() { file_api_risk_activity_activity_proto_init() }
func file_api_risk_activity_activity_proto_init() {
	if File_api_risk_activity_activity_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_activity_activity_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddToBankActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddToBankActionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReminderPointRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReminderPointResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateAndGetLEAHandlerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateAndGetLEAHandlerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNotificationTemplateForBankActionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNotificationTemplateForBankActionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppAccessUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppAccessUpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAsyncAlertsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAsyncAlertsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUnifiedLEACommsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUnifiedLEACommsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyLienRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyLienResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLienRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLienResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyLienRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyLienResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckForExistingLienRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckForExistingLienResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBankActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBankActionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcquireDistributedLockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcquireDistributedLockResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseDistributedLockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseDistributedLockResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBulkStatusForFreshdeskTicketsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBulkStatusForFreshdeskTicketsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckJobStatusForFreshdeskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckJobStatusForFreshdeskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAsyncAlertsResponse_Failure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_activity_activity_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckJobStatusForFreshdeskResponse_CaseData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_activity_activity_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*GetReminderPointRequest_ClientRequestId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_activity_activity_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   36,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_activity_activity_proto_goTypes,
		DependencyIndexes: file_api_risk_activity_activity_proto_depIdxs,
		MessageInfos:      file_api_risk_activity_activity_proto_msgTypes,
	}.Build()
	File_api_risk_activity_activity_proto = out.File
	file_api_risk_activity_activity_proto_rawDesc = nil
	file_api_risk_activity_activity_proto_goTypes = nil
	file_api_risk_activity_activity_proto_depIdxs = nil
}
