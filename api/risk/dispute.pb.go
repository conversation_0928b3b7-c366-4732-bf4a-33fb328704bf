//go:generate gen_sql -types=DisputeAdditionalDetails,DisputeRejectionDetails,DisputeVerdictDetails

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/internal/dispute.proto

package risk

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	accounts "github.com/epifi/gamma/api/accounts"
	enums "github.com/epifi/gamma/api/risk/enums"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Dispute stores complaints raised by users against transactions.
// It is primarily intended for fraud/false disputes.
type Dispute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Account linked to disputed transaction.
	AccountId   string        `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	AccountType accounts.Type `protobuf:"varint,4,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
	// Transaction id of disputed transaction.
	TransactionId  string                 `protobuf:"bytes,5,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	State          enums.DisputeState     `protobuf:"varint,6,opt,name=state,proto3,enum=enums.DisputeState" json:"state,omitempty"`
	Verdict        enums.DisputeVerdict   `protobuf:"varint,7,opt,name=verdict,proto3,enum=enums.DisputeVerdict" json:"verdict,omitempty"`
	VerdictDetails *DisputeVerdictDetails `protobuf:"bytes,8,opt,name=verdict_details,json=verdictDetails,proto3" json:"verdict_details,omitempty"`
	// It can be between 0 and transaction amount.
	DisputedAmount    *typesv2.Money            `protobuf:"bytes,9,opt,name=disputed_amount,json=disputedAmount,proto3" json:"disputed_amount,omitempty"`
	AdditionalDetails *DisputeAdditionalDetails `protobuf:"bytes,10,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
	// Federal case id uniquely identifies a raised dispute in Federal's DMP system.
	FederalCaseId string `protobuf:"bytes,11,opt,name=federal_case_id,json=federalCaseId,proto3" json:"federal_case_id,omitempty"`
	// Dispute initiation timestamp.
	// Note: Mostly this will be only date, time part could be missing.
	InitiatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=initiated_at,json=initiatedAt,proto3" json:"initiated_at,omitempty"`
	// Dispute resolution timestamp.
	ResolvedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=resolved_at,json=resolvedAt,proto3" json:"resolved_at,omitempty"`
	// Last bank update timestamp on dispute.
	BankUpdatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=bank_updated_at,json=bankUpdatedAt,proto3" json:"bank_updated_at,omitempty"`
	// Message creation timestamp. Unrelated to dispute creation
	// since it is generated from the bank disputes report.
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *Dispute) Reset() {
	*x = Dispute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_internal_dispute_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Dispute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dispute) ProtoMessage() {}

func (x *Dispute) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_internal_dispute_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dispute.ProtoReflect.Descriptor instead.
func (*Dispute) Descriptor() ([]byte, []int) {
	return file_api_risk_internal_dispute_proto_rawDescGZIP(), []int{0}
}

func (x *Dispute) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Dispute) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *Dispute) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *Dispute) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

func (x *Dispute) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *Dispute) GetState() enums.DisputeState {
	if x != nil {
		return x.State
	}
	return enums.DisputeState(0)
}

func (x *Dispute) GetVerdict() enums.DisputeVerdict {
	if x != nil {
		return x.Verdict
	}
	return enums.DisputeVerdict(0)
}

func (x *Dispute) GetVerdictDetails() *DisputeVerdictDetails {
	if x != nil {
		return x.VerdictDetails
	}
	return nil
}

func (x *Dispute) GetDisputedAmount() *typesv2.Money {
	if x != nil {
		return x.DisputedAmount
	}
	return nil
}

func (x *Dispute) GetAdditionalDetails() *DisputeAdditionalDetails {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

func (x *Dispute) GetFederalCaseId() string {
	if x != nil {
		return x.FederalCaseId
	}
	return ""
}

func (x *Dispute) GetInitiatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.InitiatedAt
	}
	return nil
}

func (x *Dispute) GetResolvedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ResolvedAt
	}
	return nil
}

func (x *Dispute) GetBankUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.BankUpdatedAt
	}
	return nil
}

func (x *Dispute) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Dispute) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Dispute) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

type DisputeVerdictDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Details:
	//
	//	*DisputeVerdictDetails_RejectionDetails
	Details isDisputeVerdictDetails_Details `protobuf_oneof:"details"`
}

func (x *DisputeVerdictDetails) Reset() {
	*x = DisputeVerdictDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_internal_dispute_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisputeVerdictDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisputeVerdictDetails) ProtoMessage() {}

func (x *DisputeVerdictDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_internal_dispute_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisputeVerdictDetails.ProtoReflect.Descriptor instead.
func (*DisputeVerdictDetails) Descriptor() ([]byte, []int) {
	return file_api_risk_internal_dispute_proto_rawDescGZIP(), []int{1}
}

func (m *DisputeVerdictDetails) GetDetails() isDisputeVerdictDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *DisputeVerdictDetails) GetRejectionDetails() *DisputeRejectionDetails {
	if x, ok := x.GetDetails().(*DisputeVerdictDetails_RejectionDetails); ok {
		return x.RejectionDetails
	}
	return nil
}

type isDisputeVerdictDetails_Details interface {
	isDisputeVerdictDetails_Details()
}

type DisputeVerdictDetails_RejectionDetails struct {
	RejectionDetails *DisputeRejectionDetails `protobuf:"bytes,1,opt,name=rejection_details,json=rejectionDetails,proto3,oneof"`
}

func (*DisputeVerdictDetails_RejectionDetails) isDisputeVerdictDetails_Details() {}

type DisputeRejectionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RejectionReason string `protobuf:"bytes,1,opt,name=rejection_reason,json=rejectionReason,proto3" json:"rejection_reason,omitempty"`
}

func (x *DisputeRejectionDetails) Reset() {
	*x = DisputeRejectionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_internal_dispute_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisputeRejectionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisputeRejectionDetails) ProtoMessage() {}

func (x *DisputeRejectionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_internal_dispute_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisputeRejectionDetails.ProtoReflect.Descriptor instead.
func (*DisputeRejectionDetails) Descriptor() ([]byte, []int) {
	return file_api_risk_internal_dispute_proto_rawDescGZIP(), []int{2}
}

func (x *DisputeRejectionDetails) GetRejectionReason() string {
	if x != nil {
		return x.RejectionReason
	}
	return ""
}

type DisputeAdditionalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Federal CBS generated code for each transaction.
	CbsId string `protobuf:"bytes,1,opt,name=cbs_id,json=cbsId,proto3" json:"cbs_id,omitempty"`
	// Utr/RRN number of the disputed transaction.
	Utr string `protobuf:"bytes,2,opt,name=utr,proto3" json:"utr,omitempty"`
	// Dispute source, e.g., customer care, customer portal etc.
	Source string `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	// Person initiating dispute, e.g., user, customer care agent etc.
	InitiatedBy string `protobuf:"bytes,4,opt,name=initiated_by,json=initiatedBy,proto3" json:"initiated_by,omitempty"`
	// Point where complaint/dispute is registered at federal's end.
	// E.g., Federal Bank, Head Office or branch depending on the source of dispute.
	RegisteredAt string `protobuf:"bytes,5,opt,name=registered_at,json=registeredAt,proto3" json:"registered_at,omitempty"`
	// Transaction channel such as UPI, Ecomm etc..
	Channel string `protobuf:"bytes,6,opt,name=channel,proto3" json:"channel,omitempty"`
	// Bank Comment on dispute.
	Comment string `protobuf:"bytes,7,opt,name=comment,proto3" json:"comment,omitempty"`
	// Disputed transaction timestamp.
	// Note: Mostly this will be only date, time part could be missing.
	TransactionAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=transaction_at,json=transactionAt,proto3" json:"transaction_at,omitempty"`
	TransactionAmount *money.Money           `protobuf:"bytes,9,opt,name=transaction_amount,json=transactionAmount,proto3" json:"transaction_amount,omitempty"`
	AtmId             string                 `protobuf:"bytes,10,opt,name=atm_id,json=atmId,proto3" json:"atm_id,omitempty"`
}

func (x *DisputeAdditionalDetails) Reset() {
	*x = DisputeAdditionalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_internal_dispute_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisputeAdditionalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisputeAdditionalDetails) ProtoMessage() {}

func (x *DisputeAdditionalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_internal_dispute_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisputeAdditionalDetails.ProtoReflect.Descriptor instead.
func (*DisputeAdditionalDetails) Descriptor() ([]byte, []int) {
	return file_api_risk_internal_dispute_proto_rawDescGZIP(), []int{3}
}

func (x *DisputeAdditionalDetails) GetCbsId() string {
	if x != nil {
		return x.CbsId
	}
	return ""
}

func (x *DisputeAdditionalDetails) GetUtr() string {
	if x != nil {
		return x.Utr
	}
	return ""
}

func (x *DisputeAdditionalDetails) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *DisputeAdditionalDetails) GetInitiatedBy() string {
	if x != nil {
		return x.InitiatedBy
	}
	return ""
}

func (x *DisputeAdditionalDetails) GetRegisteredAt() string {
	if x != nil {
		return x.RegisteredAt
	}
	return ""
}

func (x *DisputeAdditionalDetails) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *DisputeAdditionalDetails) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *DisputeAdditionalDetails) GetTransactionAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionAt
	}
	return nil
}

func (x *DisputeAdditionalDetails) GetTransactionAmount() *money.Money {
	if x != nil {
		return x.TransactionAmount
	}
	return nil
}

func (x *DisputeAdditionalDetails) GetAtmId() string {
	if x != nil {
		return x.AtmId
	}
	return ""
}

var File_api_risk_internal_dispute_proto protoreflect.FileDescriptor

var file_api_risk_internal_dispute_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x04, 0x72, 0x69, 0x73, 0x6b, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69,
	0x73, 0x6b, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xaf, 0x07, 0x0a, 0x07, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x33, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x13, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x44,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x12, 0x44, 0x0a, 0x0f, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x56, 0x65,
	0x72, 0x64, 0x69, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x76, 0x65,
	0x72, 0x64, 0x69, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x45, 0x0a, 0x0f,
	0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x57, 0x0a, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x11, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2f, 0x0a, 0x0f,
	0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0d,
	0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x47, 0x0a,
	0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0b, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x4c, 0x0a, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02,
	0x08, 0x01, 0x52, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x22, 0x70, 0x0a, 0x15, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x56, 0x65, 0x72,
	0x64, 0x69, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4c, 0x0a, 0x11, 0x72,
	0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x69,
	0x73, 0x70, 0x75, 0x74, 0x65, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x10, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x22, 0x44, 0x0a, 0x17, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x52,
	0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x29, 0x0a, 0x10, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x6a, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xf4, 0x02, 0x0a, 0x18, 0x44,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x62, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x62, 0x73, 0x49, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x74, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x74, 0x72,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x72,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x41, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x12, 0x41, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x74,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x74, 0x6d, 0x49,
	0x64, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x69, 0x73, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_internal_dispute_proto_rawDescOnce sync.Once
	file_api_risk_internal_dispute_proto_rawDescData = file_api_risk_internal_dispute_proto_rawDesc
)

func file_api_risk_internal_dispute_proto_rawDescGZIP() []byte {
	file_api_risk_internal_dispute_proto_rawDescOnce.Do(func() {
		file_api_risk_internal_dispute_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_internal_dispute_proto_rawDescData)
	})
	return file_api_risk_internal_dispute_proto_rawDescData
}

var file_api_risk_internal_dispute_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_risk_internal_dispute_proto_goTypes = []interface{}{
	(*Dispute)(nil),                  // 0: risk.Dispute
	(*DisputeVerdictDetails)(nil),    // 1: risk.DisputeVerdictDetails
	(*DisputeRejectionDetails)(nil),  // 2: risk.DisputeRejectionDetails
	(*DisputeAdditionalDetails)(nil), // 3: risk.DisputeAdditionalDetails
	(accounts.Type)(0),               // 4: accounts.Type
	(enums.DisputeState)(0),          // 5: enums.DisputeState
	(enums.DisputeVerdict)(0),        // 6: enums.DisputeVerdict
	(*typesv2.Money)(nil),            // 7: api.typesv2.Money
	(*timestamppb.Timestamp)(nil),    // 8: google.protobuf.Timestamp
	(*money.Money)(nil),              // 9: google.type.Money
}
var file_api_risk_internal_dispute_proto_depIdxs = []int32{
	4,  // 0: risk.Dispute.account_type:type_name -> accounts.Type
	5,  // 1: risk.Dispute.state:type_name -> enums.DisputeState
	6,  // 2: risk.Dispute.verdict:type_name -> enums.DisputeVerdict
	1,  // 3: risk.Dispute.verdict_details:type_name -> risk.DisputeVerdictDetails
	7,  // 4: risk.Dispute.disputed_amount:type_name -> api.typesv2.Money
	3,  // 5: risk.Dispute.additional_details:type_name -> risk.DisputeAdditionalDetails
	8,  // 6: risk.Dispute.initiated_at:type_name -> google.protobuf.Timestamp
	8,  // 7: risk.Dispute.resolved_at:type_name -> google.protobuf.Timestamp
	8,  // 8: risk.Dispute.bank_updated_at:type_name -> google.protobuf.Timestamp
	8,  // 9: risk.Dispute.created_at:type_name -> google.protobuf.Timestamp
	8,  // 10: risk.Dispute.updated_at:type_name -> google.protobuf.Timestamp
	8,  // 11: risk.Dispute.deleted_at:type_name -> google.protobuf.Timestamp
	2,  // 12: risk.DisputeVerdictDetails.rejection_details:type_name -> risk.DisputeRejectionDetails
	8,  // 13: risk.DisputeAdditionalDetails.transaction_at:type_name -> google.protobuf.Timestamp
	9,  // 14: risk.DisputeAdditionalDetails.transaction_amount:type_name -> google.type.Money
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_api_risk_internal_dispute_proto_init() }
func file_api_risk_internal_dispute_proto_init() {
	if File_api_risk_internal_dispute_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_internal_dispute_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Dispute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_internal_dispute_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisputeVerdictDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_internal_dispute_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisputeRejectionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_internal_dispute_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisputeAdditionalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_internal_dispute_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*DisputeVerdictDetails_RejectionDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_internal_dispute_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_internal_dispute_proto_goTypes,
		DependencyIndexes: file_api_risk_internal_dispute_proto_depIdxs,
		MessageInfos:      file_api_risk_internal_dispute_proto_msgTypes,
	}.Build()
	File_api_risk_internal_dispute_proto = out.File
	file_api_risk_internal_dispute_proto_rawDesc = nil
	file_api_risk_internal_dispute_proto_goTypes = nil
	file_api_risk_internal_dispute_proto_depIdxs = nil
}
