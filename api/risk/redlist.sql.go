package risk

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/logger"
)

// Value is Valuer interface implementation for storing the data in string format in DB
func (x *RedListerReason) Value() (driver.Value, error) {
	if x == nil {
		return nil, nil
	}
	return protojson.Marshal(x)
}

// Scan is Scanner interface implementation for parsing data while reading from DB
func (x *RedListerReason) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, x)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}

func (x *Metadata) Value() (driver.Value, error) {
	if x == nil {
		return nil, nil
	}
	return protojson.Marshal(x)
}
func (x *Metadata) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, x)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}

func (x RedListCategory) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan Scanner interface implementation for parsing data while reading from DB
func (x *RedListCategory) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}
	valInt, ok := RedListCategory_value[val]
	if !ok {
		logger.ErrorNoCtx(fmt.Sprintf("encountered unknown risk param : %v", val))
	}
	*x = RedListCategory(valInt)
	return nil
}
