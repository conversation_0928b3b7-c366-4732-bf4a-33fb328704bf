// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/screener/enums.proto

package screener

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Each screening needs to result in one of the given verdict values
type Verdict int32

const (
	Verdict_VERDICT_UNSPECIFIED Verdict = 0
	// screener attempt has passed for user
	Verdict_VERDICT_PASS Verdict = 1
	// essential and/or checks with high scores have failed
	// marking overall screener attempt fail
	Verdict_VERDICT_FAIL Verdict = 2
)

// Enum value maps for Verdict.
var (
	Verdict_name = map[int32]string{
		0: "VERDICT_UNSPECIFIED",
		1: "VERDICT_PASS",
		2: "VERDICT_FAIL",
	}
	Verdict_value = map[string]int32{
		"VERDICT_UNSPECIFIED": 0,
		"VERDICT_PASS":        1,
		"VERDICT_FAIL":        2,
	}
)

func (x Verdict) Enum() *Verdict {
	p := new(Verdict)
	*p = x
	return p
}

func (x Verdict) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Verdict) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_screener_enums_proto_enumTypes[0].Descriptor()
}

func (Verdict) Type() protoreflect.EnumType {
	return &file_api_risk_screener_enums_proto_enumTypes[0]
}

func (x Verdict) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Verdict.Descriptor instead.
func (Verdict) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_screener_enums_proto_rawDescGZIP(), []int{0}
}

// Enum to represent current status for a screener check
type ScreenerStatus int32

const (
	ScreenerStatus_SCREENER_STATUS_UNSPECIFIED ScreenerStatus = 0
	// checks in progress
	ScreenerStatus_SCREENER_STATUS_IN_PROGRESS ScreenerStatus = 1
	// currently under risk-ops manual review
	ScreenerStatus_SCREENER_STATUS_IN_MANUAL_REVIEW ScreenerStatus = 2
	// all processing stages for screener
	// has completed
	// refer to verdict for final result
	ScreenerStatus_SCREENER_STATUS_DONE ScreenerStatus = 3
	// any technical failure will result in this state
	ScreenerStatus_SCREENER_STATUS_PROCESSING_FAILED ScreenerStatus = 4
)

// Enum value maps for ScreenerStatus.
var (
	ScreenerStatus_name = map[int32]string{
		0: "SCREENER_STATUS_UNSPECIFIED",
		1: "SCREENER_STATUS_IN_PROGRESS",
		2: "SCREENER_STATUS_IN_MANUAL_REVIEW",
		3: "SCREENER_STATUS_DONE",
		4: "SCREENER_STATUS_PROCESSING_FAILED",
	}
	ScreenerStatus_value = map[string]int32{
		"SCREENER_STATUS_UNSPECIFIED":       0,
		"SCREENER_STATUS_IN_PROGRESS":       1,
		"SCREENER_STATUS_IN_MANUAL_REVIEW":  2,
		"SCREENER_STATUS_DONE":              3,
		"SCREENER_STATUS_PROCESSING_FAILED": 4,
	}
)

func (x ScreenerStatus) Enum() *ScreenerStatus {
	p := new(ScreenerStatus)
	*p = x
	return p
}

func (x ScreenerStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScreenerStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_screener_enums_proto_enumTypes[1].Descriptor()
}

func (ScreenerStatus) Type() protoreflect.EnumType {
	return &file_api_risk_screener_enums_proto_enumTypes[1]
}

func (x ScreenerStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScreenerStatus.Descriptor instead.
func (ScreenerStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_screener_enums_proto_rawDescGZIP(), []int{1}
}

// indicates whether a screener check was run in
// shadow or in active state
type RunMode int32

const (
	RunMode_RUN_MODE_UNSPECIFIED RunMode = 0
	// will affect overall screener result
	RunMode_RUN_MODE_ACTIVE RunMode = 1
	// passive mode
	RunMode_RUN_MODE_SHADOW RunMode = 2
)

// Enum value maps for RunMode.
var (
	RunMode_name = map[int32]string{
		0: "RUN_MODE_UNSPECIFIED",
		1: "RUN_MODE_ACTIVE",
		2: "RUN_MODE_SHADOW",
	}
	RunMode_value = map[string]int32{
		"RUN_MODE_UNSPECIFIED": 0,
		"RUN_MODE_ACTIVE":      1,
		"RUN_MODE_SHADOW":      2,
	}
)

func (x RunMode) Enum() *RunMode {
	p := new(RunMode)
	*p = x
	return p
}

func (x RunMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RunMode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_screener_enums_proto_enumTypes[2].Descriptor()
}

func (RunMode) Type() protoreflect.EnumType {
	return &file_api_risk_screener_enums_proto_enumTypes[2]
}

func (x RunMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RunMode.Descriptor instead.
func (RunMode) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_screener_enums_proto_rawDescGZIP(), []int{2}
}

// PotentialRiskFlag indicates plausible risk factors observed during the screener checks
// in certain case these factors might not be sufficient to block the user from risk POV but
// it can be used by calling service to add friction/mitigations for the given risk factor
// EX: For risk factor succh as throwaway device/phone_number or new_phone_number,
// calling service can add mitigation like alternate phone number verification etc.
type PotentialRiskFlag int32

const (
	PotentialRiskFlag_POTENTIAL_RISK_FLAG_UNSPECIFIED PotentialRiskFlag = 0
	// user is potentially using a throway credential for onboarding
	PotentialRiskFlag_POTENTIAL_RISK_FLAG_DISPOSABLE_CREDENTIALS PotentialRiskFlag = 1
	// user is associated with previously identified bad actors
	// association could be via contact or liveness etc.
	PotentialRiskFlag_POTENTIAL_RISK_FLAG_ASSOCIATION_WITH_BAD_ACTOR PotentialRiskFlag = 2
	// location the user is onboarding from could be potentially risky
	PotentialRiskFlag_POTENTIAL_RISK_FLAG_RISKY_LOCATION PotentialRiskFlag = 3
	// credentials used for onboarding by the user are
	PotentialRiskFlag_POTENTIAL_RISK_FLAG_BAD_CREDENTIALS PotentialRiskFlag = 4
	PotentialRiskFlag_POTENTIAL_RISK_FLAG_RISKY_PROFILE   PotentialRiskFlag = 5
)

// Enum value maps for PotentialRiskFlag.
var (
	PotentialRiskFlag_name = map[int32]string{
		0: "POTENTIAL_RISK_FLAG_UNSPECIFIED",
		1: "POTENTIAL_RISK_FLAG_DISPOSABLE_CREDENTIALS",
		2: "POTENTIAL_RISK_FLAG_ASSOCIATION_WITH_BAD_ACTOR",
		3: "POTENTIAL_RISK_FLAG_RISKY_LOCATION",
		4: "POTENTIAL_RISK_FLAG_BAD_CREDENTIALS",
		5: "POTENTIAL_RISK_FLAG_RISKY_PROFILE",
	}
	PotentialRiskFlag_value = map[string]int32{
		"POTENTIAL_RISK_FLAG_UNSPECIFIED":                0,
		"POTENTIAL_RISK_FLAG_DISPOSABLE_CREDENTIALS":     1,
		"POTENTIAL_RISK_FLAG_ASSOCIATION_WITH_BAD_ACTOR": 2,
		"POTENTIAL_RISK_FLAG_RISKY_LOCATION":             3,
		"POTENTIAL_RISK_FLAG_BAD_CREDENTIALS":            4,
		"POTENTIAL_RISK_FLAG_RISKY_PROFILE":              5,
	}
)

func (x PotentialRiskFlag) Enum() *PotentialRiskFlag {
	p := new(PotentialRiskFlag)
	*p = x
	return p
}

func (x PotentialRiskFlag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PotentialRiskFlag) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_screener_enums_proto_enumTypes[3].Descriptor()
}

func (PotentialRiskFlag) Type() protoreflect.EnumType {
	return &file_api_risk_screener_enums_proto_enumTypes[3]
}

func (x PotentialRiskFlag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PotentialRiskFlag.Descriptor instead.
func (PotentialRiskFlag) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_screener_enums_proto_rawDescGZIP(), []int{3}
}

var File_api_risk_screener_enums_proto protoreflect.FileDescriptor

var file_api_risk_screener_enums_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x65, 0x72, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0d, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2a, 0x46,
	0x0a, 0x07, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x12, 0x17, 0x0a, 0x13, 0x56, 0x45, 0x52,
	0x44, 0x49, 0x43, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x50, 0x41,
	0x53, 0x53, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x2a, 0xb9, 0x01, 0x0a, 0x0e, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e,
	0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49,
	0x4e, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10,
	0x02, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x4e, 0x45, 0x10, 0x03, 0x12, 0x25, 0x0a, 0x21, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50,
	0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x04, 0x2a, 0x4d, 0x0a, 0x07, 0x52, 0x75, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x14, 0x52, 0x55, 0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x55, 0x4e, 0x5f, 0x4d,
	0x4f, 0x44, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f,
	0x52, 0x55, 0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x48, 0x41, 0x44, 0x4f, 0x57, 0x10,
	0x02, 0x2a, 0x94, 0x02, 0x0a, 0x11, 0x50, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52,
	0x69, 0x73, 0x6b, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x23, 0x0a, 0x1f, 0x50, 0x4f, 0x54, 0x45, 0x4e,
	0x54, 0x49, 0x41, 0x4c, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2e, 0x0a, 0x2a,
	0x50, 0x4f, 0x54, 0x45, 0x4e, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46,
	0x4c, 0x41, 0x47, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x43,
	0x52, 0x45, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x41, 0x4c, 0x53, 0x10, 0x01, 0x12, 0x32, 0x0a, 0x2e,
	0x50, 0x4f, 0x54, 0x45, 0x4e, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46,
	0x4c, 0x41, 0x47, 0x5f, 0x41, 0x53, 0x53, 0x4f, 0x43, 0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x57, 0x49, 0x54, 0x48, 0x5f, 0x42, 0x41, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x02,
	0x12, 0x26, 0x0a, 0x22, 0x50, 0x4f, 0x54, 0x45, 0x4e, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x52, 0x49,
	0x53, 0x4b, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x59, 0x5f, 0x4c, 0x4f,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x27, 0x0a, 0x23, 0x50, 0x4f, 0x54, 0x45,
	0x4e, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f,
	0x42, 0x41, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x41, 0x4c, 0x53, 0x10,
	0x04, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x4f, 0x54, 0x45, 0x4e, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x52,
	0x49, 0x53, 0x4b, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x59, 0x5f, 0x50,
	0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x05, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x65, 0x72, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_screener_enums_proto_rawDescOnce sync.Once
	file_api_risk_screener_enums_proto_rawDescData = file_api_risk_screener_enums_proto_rawDesc
)

func file_api_risk_screener_enums_proto_rawDescGZIP() []byte {
	file_api_risk_screener_enums_proto_rawDescOnce.Do(func() {
		file_api_risk_screener_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_screener_enums_proto_rawDescData)
	})
	return file_api_risk_screener_enums_proto_rawDescData
}

var file_api_risk_screener_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_risk_screener_enums_proto_goTypes = []interface{}{
	(Verdict)(0),           // 0: risk.screener.Verdict
	(ScreenerStatus)(0),    // 1: risk.screener.ScreenerStatus
	(RunMode)(0),           // 2: risk.screener.RunMode
	(PotentialRiskFlag)(0), // 3: risk.screener.PotentialRiskFlag
}
var file_api_risk_screener_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_risk_screener_enums_proto_init() }
func file_api_risk_screener_enums_proto_init() {
	if File_api_risk_screener_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_screener_enums_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_screener_enums_proto_goTypes,
		DependencyIndexes: file_api_risk_screener_enums_proto_depIdxs,
		EnumInfos:         file_api_risk_screener_enums_proto_enumTypes,
	}.Build()
	File_api_risk_screener_enums_proto = out.File
	file_api_risk_screener_enums_proto_rawDesc = nil
	file_api_risk_screener_enums_proto_goTypes = nil
	file_api_risk_screener_enums_proto_depIdxs = nil
}
