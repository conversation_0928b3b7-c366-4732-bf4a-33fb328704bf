// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/risk/screener/enums.pb.go

package screener

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the Verdict in string format in DB
func (p Verdict) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Verdict while reading from DB
func (p *Verdict) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Verdict_value[val]
	if !ok {
		return fmt.Errorf("unexpected Verdict value: %s", val)
	}
	*p = Verdict(valInt)
	return nil
}

// Marshaler interface implementation for Verdict
func (x Verdict) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Verdict
func (x *Verdict) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Verdict(Verdict_value[val])
	return nil
}

// Valuer interface implementation for storing the ScreenerStatus in string format in DB
func (p ScreenerStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ScreenerStatus while reading from DB
func (p *ScreenerStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ScreenerStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected ScreenerStatus value: %s", val)
	}
	*p = ScreenerStatus(valInt)
	return nil
}

// Marshaler interface implementation for ScreenerStatus
func (x ScreenerStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ScreenerStatus
func (x *ScreenerStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ScreenerStatus(ScreenerStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the RunMode in string format in DB
func (p RunMode) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing RunMode while reading from DB
func (p *RunMode) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := RunMode_value[val]
	if !ok {
		return fmt.Errorf("unexpected RunMode value: %s", val)
	}
	*p = RunMode(valInt)
	return nil
}

// Marshaler interface implementation for RunMode
func (x RunMode) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for RunMode
func (x *RunMode) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = RunMode(RunMode_value[val])
	return nil
}
