//go:generate gen_sql -types=ManualVerdictMetadata, VerdictDetails

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/screener/screener.proto

package screener

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	enums "github.com/epifi/gamma/api/risk/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// screener attempt field mask contains an entry for each screener column
type ScreenerAttemptFieldMask int32

const (
	ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_UNSPECIFIED          ScreenerAttemptFieldMask = 0
	ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_ALL                  ScreenerAttemptFieldMask = 1
	ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_ID                   ScreenerAttemptFieldMask = 2
	ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_CLIENT_REQUEST_ID    ScreenerAttemptFieldMask = 3
	ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_CRITERIA             ScreenerAttemptFieldMask = 4
	ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_ACTOR_ID             ScreenerAttemptFieldMask = 5
	ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_STATUS               ScreenerAttemptFieldMask = 6
	ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_VERDICT              ScreenerAttemptFieldMask = 7
	ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_SCORE                ScreenerAttemptFieldMask = 8
	ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_CREATED_AT           ScreenerAttemptFieldMask = 9
	ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_UPDATED_AT           ScreenerAttemptFieldMask = 10
	ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_DELETED_AT           ScreenerAttemptFieldMask = 11
	ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_VERDICT_DETAILS      ScreenerAttemptFieldMask = 12
	ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_POTENTIAL_RISK_FLAGS ScreenerAttemptFieldMask = 13
)

// Enum value maps for ScreenerAttemptFieldMask.
var (
	ScreenerAttemptFieldMask_name = map[int32]string{
		0:  "SCREENER_ATTEMPT_FIELD_MASK_UNSPECIFIED",
		1:  "SCREENER_ATTEMPT_FIELD_MASK_ALL",
		2:  "SCREENER_ATTEMPT_FIELD_MASK_ID",
		3:  "SCREENER_ATTEMPT_FIELD_MASK_CLIENT_REQUEST_ID",
		4:  "SCREENER_ATTEMPT_FIELD_MASK_CRITERIA",
		5:  "SCREENER_ATTEMPT_FIELD_MASK_ACTOR_ID",
		6:  "SCREENER_ATTEMPT_FIELD_MASK_STATUS",
		7:  "SCREENER_ATTEMPT_FIELD_MASK_VERDICT",
		8:  "SCREENER_ATTEMPT_FIELD_MASK_SCORE",
		9:  "SCREENER_ATTEMPT_FIELD_MASK_CREATED_AT",
		10: "SCREENER_ATTEMPT_FIELD_MASK_UPDATED_AT",
		11: "SCREENER_ATTEMPT_FIELD_MASK_DELETED_AT",
		12: "SCREENER_ATTEMPT_FIELD_MASK_VERDICT_DETAILS",
		13: "SCREENER_ATTEMPT_FIELD_MASK_POTENTIAL_RISK_FLAGS",
	}
	ScreenerAttemptFieldMask_value = map[string]int32{
		"SCREENER_ATTEMPT_FIELD_MASK_UNSPECIFIED":          0,
		"SCREENER_ATTEMPT_FIELD_MASK_ALL":                  1,
		"SCREENER_ATTEMPT_FIELD_MASK_ID":                   2,
		"SCREENER_ATTEMPT_FIELD_MASK_CLIENT_REQUEST_ID":    3,
		"SCREENER_ATTEMPT_FIELD_MASK_CRITERIA":             4,
		"SCREENER_ATTEMPT_FIELD_MASK_ACTOR_ID":             5,
		"SCREENER_ATTEMPT_FIELD_MASK_STATUS":               6,
		"SCREENER_ATTEMPT_FIELD_MASK_VERDICT":              7,
		"SCREENER_ATTEMPT_FIELD_MASK_SCORE":                8,
		"SCREENER_ATTEMPT_FIELD_MASK_CREATED_AT":           9,
		"SCREENER_ATTEMPT_FIELD_MASK_UPDATED_AT":           10,
		"SCREENER_ATTEMPT_FIELD_MASK_DELETED_AT":           11,
		"SCREENER_ATTEMPT_FIELD_MASK_VERDICT_DETAILS":      12,
		"SCREENER_ATTEMPT_FIELD_MASK_POTENTIAL_RISK_FLAGS": 13,
	}
)

func (x ScreenerAttemptFieldMask) Enum() *ScreenerAttemptFieldMask {
	p := new(ScreenerAttemptFieldMask)
	*p = x
	return p
}

func (x ScreenerAttemptFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScreenerAttemptFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_screener_screener_proto_enumTypes[0].Descriptor()
}

func (ScreenerAttemptFieldMask) Type() protoreflect.EnumType {
	return &file_api_risk_screener_screener_proto_enumTypes[0]
}

func (x ScreenerAttemptFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScreenerAttemptFieldMask.Descriptor instead.
func (ScreenerAttemptFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_screener_screener_proto_rawDescGZIP(), []int{0}
}

// screener attempt table contains an entry for each screener attempt done
type ScreenerAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// client request id passed by client
	// for this screener attempt
	ClientRequestId string `protobuf:"bytes,2,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// one of the risk screener criteria values
	Criteria enums.ScreenerCriteria `protobuf:"varint,3,opt,name=criteria,proto3,enum=enums.ScreenerCriteria" json:"criteria,omitempty"`
	// actor for which screening is done
	ActorId string `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// current status for screener attempt
	Status ScreenerStatus `protobuf:"varint,5,opt,name=status,proto3,enum=risk.screener.ScreenerStatus" json:"status,omitempty"`
	// final result for current screener attempt
	Verdict Verdict `protobuf:"varint,6,opt,name=verdict,proto3,enum=risk.screener.Verdict" json:"verdict,omitempty"`
	// overall attempt risk score, in the range of 0 to 1
	Score         float32                `protobuf:"fixed32,7,opt,name=score,proto3" json:"score,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAtUnix int64                  `protobuf:"varint,10,opt,name=deleted_at_unix,json=deletedAtUnix,proto3" json:"deleted_at_unix,omitempty"`
	// verdict details of screener attempt
	ManualVerdictMetadata *ManualVerdictMetadata `protobuf:"bytes,11,opt,name=manual_verdict_metadata,json=manualVerdictMetadata,proto3" json:"manual_verdict_metadata,omitempty"`
	// PotentialRiskFlag indicates plausible risk flags observed during the screener checks
	PotentialRiskFlags []PotentialRiskFlag `protobuf:"varint,12,rep,packed,name=potential_risk_flags,json=potentialRiskFlags,proto3,enum=risk.screener.PotentialRiskFlag" json:"potential_risk_flags,omitempty"`
}

func (x *ScreenerAttempt) Reset() {
	*x = ScreenerAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_screener_screener_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenerAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenerAttempt) ProtoMessage() {}

func (x *ScreenerAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_screener_screener_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenerAttempt.ProtoReflect.Descriptor instead.
func (*ScreenerAttempt) Descriptor() ([]byte, []int) {
	return file_api_risk_screener_screener_proto_rawDescGZIP(), []int{0}
}

func (x *ScreenerAttempt) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ScreenerAttempt) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *ScreenerAttempt) GetCriteria() enums.ScreenerCriteria {
	if x != nil {
		return x.Criteria
	}
	return enums.ScreenerCriteria(0)
}

func (x *ScreenerAttempt) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ScreenerAttempt) GetStatus() ScreenerStatus {
	if x != nil {
		return x.Status
	}
	return ScreenerStatus_SCREENER_STATUS_UNSPECIFIED
}

func (x *ScreenerAttempt) GetVerdict() Verdict {
	if x != nil {
		return x.Verdict
	}
	return Verdict_VERDICT_UNSPECIFIED
}

func (x *ScreenerAttempt) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ScreenerAttempt) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ScreenerAttempt) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *ScreenerAttempt) GetDeletedAtUnix() int64 {
	if x != nil {
		return x.DeletedAtUnix
	}
	return 0
}

func (x *ScreenerAttempt) GetManualVerdictMetadata() *ManualVerdictMetadata {
	if x != nil {
		return x.ManualVerdictMetadata
	}
	return nil
}

func (x *ScreenerAttempt) GetPotentialRiskFlags() []PotentialRiskFlag {
	if x != nil {
		return x.PotentialRiskFlags
	}
	return nil
}

type ManualVerdictMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VerdictDetails []*VerdictDetails `protobuf:"bytes,1,rep,name=verdict_details,json=verdictDetails,proto3" json:"verdict_details,omitempty"`
}

func (x *ManualVerdictMetadata) Reset() {
	*x = ManualVerdictMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_screener_screener_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualVerdictMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualVerdictMetadata) ProtoMessage() {}

func (x *ManualVerdictMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_screener_screener_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualVerdictMetadata.ProtoReflect.Descriptor instead.
func (*ManualVerdictMetadata) Descriptor() ([]byte, []int) {
	return file_api_risk_screener_screener_proto_rawDescGZIP(), []int{1}
}

func (x *ManualVerdictMetadata) GetVerdictDetails() []*VerdictDetails {
	if x != nil {
		return x.VerdictDetails
	}
	return nil
}

type VerdictDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AnalystEmail    string         `protobuf:"bytes,1,opt,name=analyst_email,json=analystEmail,proto3" json:"analyst_email,omitempty"`
	Reason          string         `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	PreviousStatus  ScreenerStatus `protobuf:"varint,3,opt,name=previous_status,json=previousStatus,proto3,enum=risk.screener.ScreenerStatus" json:"previous_status,omitempty"`
	PreviousVerdict Verdict        `protobuf:"varint,4,opt,name=previous_verdict,json=previousVerdict,proto3,enum=risk.screener.Verdict" json:"previous_verdict,omitempty"`
}

func (x *VerdictDetails) Reset() {
	*x = VerdictDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_screener_screener_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerdictDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerdictDetails) ProtoMessage() {}

func (x *VerdictDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_screener_screener_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerdictDetails.ProtoReflect.Descriptor instead.
func (*VerdictDetails) Descriptor() ([]byte, []int) {
	return file_api_risk_screener_screener_proto_rawDescGZIP(), []int{2}
}

func (x *VerdictDetails) GetAnalystEmail() string {
	if x != nil {
		return x.AnalystEmail
	}
	return ""
}

func (x *VerdictDetails) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *VerdictDetails) GetPreviousStatus() ScreenerStatus {
	if x != nil {
		return x.PreviousStatus
	}
	return ScreenerStatus_SCREENER_STATUS_UNSPECIFIED
}

func (x *VerdictDetails) GetPreviousVerdict() Verdict {
	if x != nil {
		return x.PreviousVerdict
	}
	return Verdict_VERDICT_UNSPECIFIED
}

// represents mapping between risk data i.e. risk check result and risk screener attempt along with the run mode
// table can have m (risk_data) -> n (screener_attempts) mappings
type ScreenerAttemptRiskDataMapping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// primary identifier from risk data table
	RiskDataId string `protobuf:"bytes,2,opt,name=risk_data_id,json=riskDataId,proto3" json:"risk_data_id,omitempty"`
	// primary identifier from screener attempts table
	ScreenerAttemptId string `protobuf:"bytes,3,opt,name=screener_attempt_id,json=screenerAttemptId,proto3" json:"screener_attempt_id,omitempty"`
	// run mode for current mapping, since risk_data is a re-usable field
	// we can have multiple screener_attempts with different screener criteria
	// linked with one risk data, but their run modes can be different
	RunMode   RunMode                `protobuf:"varint,4,opt,name=run_mode,json=runMode,proto3,enum=risk.screener.RunMode" json:"run_mode,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *ScreenerAttemptRiskDataMapping) Reset() {
	*x = ScreenerAttemptRiskDataMapping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_screener_screener_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenerAttemptRiskDataMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenerAttemptRiskDataMapping) ProtoMessage() {}

func (x *ScreenerAttemptRiskDataMapping) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_screener_screener_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenerAttemptRiskDataMapping.ProtoReflect.Descriptor instead.
func (*ScreenerAttemptRiskDataMapping) Descriptor() ([]byte, []int) {
	return file_api_risk_screener_screener_proto_rawDescGZIP(), []int{3}
}

func (x *ScreenerAttemptRiskDataMapping) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ScreenerAttemptRiskDataMapping) GetRiskDataId() string {
	if x != nil {
		return x.RiskDataId
	}
	return ""
}

func (x *ScreenerAttemptRiskDataMapping) GetScreenerAttemptId() string {
	if x != nil {
		return x.ScreenerAttemptId
	}
	return ""
}

func (x *ScreenerAttemptRiskDataMapping) GetRunMode() RunMode {
	if x != nil {
		return x.RunMode
	}
	return RunMode_RUN_MODE_UNSPECIFIED
}

func (x *ScreenerAttemptRiskDataMapping) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ScreenerAttemptRiskDataMapping) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// ScreenerVerdictRequest contains request entities
// required to mark verdict on a screener attempt
type ScreenerVerdictRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	CaseId  string `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	// screener attempt id for which verdict is to be applied
	ScreenerAttemptId string  `protobuf:"bytes,3,opt,name=screener_attempt_id,json=screenerAttemptId,proto3" json:"screener_attempt_id,omitempty"`
	Verdict           Verdict `protobuf:"varint,4,opt,name=verdict,proto3,enum=risk.screener.Verdict" json:"verdict,omitempty"`
}

func (x *ScreenerVerdictRequest) Reset() {
	*x = ScreenerVerdictRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_screener_screener_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenerVerdictRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenerVerdictRequest) ProtoMessage() {}

func (x *ScreenerVerdictRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_screener_screener_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenerVerdictRequest.ProtoReflect.Descriptor instead.
func (*ScreenerVerdictRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_screener_screener_proto_rawDescGZIP(), []int{4}
}

func (x *ScreenerVerdictRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ScreenerVerdictRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *ScreenerVerdictRequest) GetScreenerAttemptId() string {
	if x != nil {
		return x.ScreenerAttemptId
	}
	return ""
}

func (x *ScreenerVerdictRequest) GetVerdict() Verdict {
	if x != nil {
		return x.Verdict
	}
	return Verdict_VERDICT_UNSPECIFIED
}

// an attempt can be uniquely identified by criteria and client req id combination
type CriteriaAndClientReqIDIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScreenerCriteria enums.ScreenerCriteria `protobuf:"varint,1,opt,name=screener_criteria,json=screenerCriteria,proto3,enum=enums.ScreenerCriteria" json:"screener_criteria,omitempty"`
	ClientRequestId  string                 `protobuf:"bytes,2,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
}

func (x *CriteriaAndClientReqIDIdentifier) Reset() {
	*x = CriteriaAndClientReqIDIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_screener_screener_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CriteriaAndClientReqIDIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CriteriaAndClientReqIDIdentifier) ProtoMessage() {}

func (x *CriteriaAndClientReqIDIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_screener_screener_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CriteriaAndClientReqIDIdentifier.ProtoReflect.Descriptor instead.
func (*CriteriaAndClientReqIDIdentifier) Descriptor() ([]byte, []int) {
	return file_api_risk_screener_screener_proto_rawDescGZIP(), []int{5}
}

func (x *CriteriaAndClientReqIDIdentifier) GetScreenerCriteria() enums.ScreenerCriteria {
	if x != nil {
		return x.ScreenerCriteria
	}
	return enums.ScreenerCriteria(0)
}

func (x *CriteriaAndClientReqIDIdentifier) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

// AttemptIdentifier contains set of identifiers which can be used to uniquely identify a screener attempt
type AttemptIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*AttemptIdentifier_AttemptId
	//	*AttemptIdentifier_CriteriaClientReqId
	Identifier isAttemptIdentifier_Identifier `protobuf_oneof:"identifier"`
}

func (x *AttemptIdentifier) Reset() {
	*x = AttemptIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_screener_screener_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttemptIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttemptIdentifier) ProtoMessage() {}

func (x *AttemptIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_screener_screener_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttemptIdentifier.ProtoReflect.Descriptor instead.
func (*AttemptIdentifier) Descriptor() ([]byte, []int) {
	return file_api_risk_screener_screener_proto_rawDescGZIP(), []int{6}
}

func (m *AttemptIdentifier) GetIdentifier() isAttemptIdentifier_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *AttemptIdentifier) GetAttemptId() string {
	if x, ok := x.GetIdentifier().(*AttemptIdentifier_AttemptId); ok {
		return x.AttemptId
	}
	return ""
}

func (x *AttemptIdentifier) GetCriteriaClientReqId() *CriteriaAndClientReqIDIdentifier {
	if x, ok := x.GetIdentifier().(*AttemptIdentifier_CriteriaClientReqId); ok {
		return x.CriteriaClientReqId
	}
	return nil
}

type isAttemptIdentifier_Identifier interface {
	isAttemptIdentifier_Identifier()
}

type AttemptIdentifier_AttemptId struct {
	// unique row id for attempt
	AttemptId string `protobuf:"bytes,1,opt,name=attempt_id,json=attemptId,proto3,oneof"`
}

type AttemptIdentifier_CriteriaClientReqId struct {
	// an attempt can be uniquely identified by criteria and client req id combination
	CriteriaClientReqId *CriteriaAndClientReqIDIdentifier `protobuf:"bytes,2,opt,name=criteria_client_req_id,json=criteriaClientReqId,proto3,oneof"`
}

func (*AttemptIdentifier_AttemptId) isAttemptIdentifier_Identifier() {}

func (*AttemptIdentifier_CriteriaClientReqId) isAttemptIdentifier_Identifier() {}

// AdditionalCheckDetails contains additional details related to the checks for analysis
// this can contain check specific information like certain model response fields
// or raw details used for evaluating the given check etc.
type AdditionalCheckDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Details:
	//
	//	*AdditionalCheckDetails_UpiVpaNameMatch
	Details isAdditionalCheckDetails_Details `protobuf_oneof:"details"`
}

func (x *AdditionalCheckDetails) Reset() {
	*x = AdditionalCheckDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_screener_screener_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdditionalCheckDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalCheckDetails) ProtoMessage() {}

func (x *AdditionalCheckDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_screener_screener_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalCheckDetails.ProtoReflect.Descriptor instead.
func (*AdditionalCheckDetails) Descriptor() ([]byte, []int) {
	return file_api_risk_screener_screener_proto_rawDescGZIP(), []int{7}
}

func (m *AdditionalCheckDetails) GetDetails() isAdditionalCheckDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *AdditionalCheckDetails) GetUpiVpaNameMatch() *UPIVPANameMatch {
	if x, ok := x.GetDetails().(*AdditionalCheckDetails_UpiVpaNameMatch); ok {
		return x.UpiVpaNameMatch
	}
	return nil
}

type isAdditionalCheckDetails_Details interface {
	isAdditionalCheckDetails_Details()
}

type AdditionalCheckDetails_UpiVpaNameMatch struct {
	// will be return in case of UPI-VPA name match check
	UpiVpaNameMatch *UPIVPANameMatch `protobuf:"bytes,2,opt,name=upi_vpa_name_match,json=upiVpaNameMatch,proto3,oneof"`
}

func (*AdditionalCheckDetails_UpiVpaNameMatch) isAdditionalCheckDetails_Details() {}

// contains additional upi vpa name match details
// will contain list of vpa names used for performing name match
type UPIVPANameMatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VpaNames []string `protobuf:"bytes,1,rep,name=vpa_names,json=vpaNames,proto3" json:"vpa_names,omitempty"`
}

func (x *UPIVPANameMatch) Reset() {
	*x = UPIVPANameMatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_screener_screener_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UPIVPANameMatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UPIVPANameMatch) ProtoMessage() {}

func (x *UPIVPANameMatch) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_screener_screener_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UPIVPANameMatch.ProtoReflect.Descriptor instead.
func (*UPIVPANameMatch) Descriptor() ([]byte, []int) {
	return file_api_risk_screener_screener_proto_rawDescGZIP(), []int{8}
}

func (x *UPIVPANameMatch) GetVpaNames() []string {
	if x != nil {
		return x.VpaNames
	}
	return nil
}

var File_api_risk_screener_screener_proto protoreflect.FileDescriptor

var file_api_risk_screener_screener_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x65, 0x72, 0x2f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0d, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65,
	0x72, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x90, 0x05, 0x0a, 0x0f, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x08, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x52, 0x08, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x12, 0x22, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x3f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2e,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x30, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65,
	0x72, 0x2e, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69,
	0x63, 0x74, 0x12, 0x25, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x02, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x0a, 0x0a, 0x1d, 0x00, 0x00, 0xc8, 0x42, 0x2d, 0x00, 0x00,
	0x00, 0x00, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x26, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x75, 0x6e,
	0x69, 0x78, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x55, 0x6e, 0x69, 0x78, 0x12, 0x5c, 0x0a, 0x17, 0x6d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x5f, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x56,
	0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x15,
	0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x52, 0x0a, 0x14, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69,
	0x61, 0x6c, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x0c, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x69, 0x73,
	0x6b, 0x46, 0x6c, 0x61, 0x67, 0x52, 0x12, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c,
	0x52, 0x69, 0x73, 0x6b, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x22, 0x5f, 0x0a, 0x15, 0x4d, 0x61, 0x6e,
	0x75, 0x61, 0x6c, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x46, 0x0a, 0x0f, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x72, 0x64,
	0x69, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x76, 0x65, 0x72, 0x64,
	0x69, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xec, 0x01, 0x0a, 0x0e, 0x56,
	0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x0f, 0x70, 0x72,
	0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x70, 0x72,
	0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x10,
	0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f,
	0x75, 0x73, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x22, 0xc7, 0x02, 0x0a, 0x1e, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x69, 0x73,
	0x6b, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x0c,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x72, 0x69, 0x73,
	0x6b, 0x44, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x13, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x65, 0x72, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x11, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x64,
	0x12, 0x3b, 0x0a, 0x08, 0x72, 0x75, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x20, 0x00, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x22, 0xca, 0x01, 0x0a, 0x16, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72,
	0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x63, 0x61,
	0x73, 0x65, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72,
	0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74,
	0x22, 0x94, 0x01, 0x0a, 0x20, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x41, 0x6e, 0x64,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x44, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x11, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65,
	0x72, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x17, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65,
	0x72, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x52, 0x10, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x65, 0x72, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x12, 0x2a, 0x0a, 0x11, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0xaa, 0x01, 0x0a, 0x11, 0x41, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x1f, 0x0a,
	0x0a, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x09, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x64, 0x12, 0x66,
	0x0a, 0x16, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2e, 0x43,
	0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x41, 0x6e, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x49, 0x44, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x48,
	0x00, 0x52, 0x13, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x22, 0x72, 0x0a, 0x16, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4d,
	0x0a, 0x12, 0x75, 0x70, 0x69, 0x5f, 0x76, 0x70, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2e, 0x55, 0x50, 0x49, 0x56, 0x50,
	0x41, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x48, 0x00, 0x52, 0x0f, 0x75, 0x70,
	0x69, 0x56, 0x70, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x42, 0x09, 0x0a,
	0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x2e, 0x0a, 0x0f, 0x55, 0x50, 0x49, 0x56,
	0x50, 0x41, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x76,
	0x70, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x76, 0x70, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x2a, 0xfa, 0x04, 0x0a, 0x18, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2b, 0x0a, 0x27, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45,
	0x52, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x31, 0x0a, 0x2d, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e,
	0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x28,
	0x0a, 0x24, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d,
	0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52,
	0x49, 0x54, 0x45, 0x52, 0x49, 0x41, 0x10, 0x04, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44,
	0x10, 0x05, 0x12, 0x26, 0x0a, 0x22, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x06, 0x12, 0x27, 0x0a, 0x23, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x52, 0x44, 0x49, 0x43,
	0x54, 0x10, 0x07, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f,
	0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x10, 0x08, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x09, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54,
	0x10, 0x0a, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0b, 0x12, 0x2f,
	0x0a, 0x2b, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d,
	0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45,
	0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x0c, 0x12,
	0x34, 0x0a, 0x30, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x45,
	0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50,
	0x4f, 0x54, 0x45, 0x4e, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x4c,
	0x41, 0x47, 0x53, 0x10, 0x0d, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65,
	0x72, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69,
	0x73, 0x6b, 0x2f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_risk_screener_screener_proto_rawDescOnce sync.Once
	file_api_risk_screener_screener_proto_rawDescData = file_api_risk_screener_screener_proto_rawDesc
)

func file_api_risk_screener_screener_proto_rawDescGZIP() []byte {
	file_api_risk_screener_screener_proto_rawDescOnce.Do(func() {
		file_api_risk_screener_screener_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_screener_screener_proto_rawDescData)
	})
	return file_api_risk_screener_screener_proto_rawDescData
}

var file_api_risk_screener_screener_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_risk_screener_screener_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_risk_screener_screener_proto_goTypes = []interface{}{
	(ScreenerAttemptFieldMask)(0),            // 0: risk.screener.ScreenerAttemptFieldMask
	(*ScreenerAttempt)(nil),                  // 1: risk.screener.ScreenerAttempt
	(*ManualVerdictMetadata)(nil),            // 2: risk.screener.ManualVerdictMetadata
	(*VerdictDetails)(nil),                   // 3: risk.screener.VerdictDetails
	(*ScreenerAttemptRiskDataMapping)(nil),   // 4: risk.screener.ScreenerAttemptRiskDataMapping
	(*ScreenerVerdictRequest)(nil),           // 5: risk.screener.ScreenerVerdictRequest
	(*CriteriaAndClientReqIDIdentifier)(nil), // 6: risk.screener.CriteriaAndClientReqIDIdentifier
	(*AttemptIdentifier)(nil),                // 7: risk.screener.AttemptIdentifier
	(*AdditionalCheckDetails)(nil),           // 8: risk.screener.AdditionalCheckDetails
	(*UPIVPANameMatch)(nil),                  // 9: risk.screener.UPIVPANameMatch
	(enums.ScreenerCriteria)(0),              // 10: enums.ScreenerCriteria
	(ScreenerStatus)(0),                      // 11: risk.screener.ScreenerStatus
	(Verdict)(0),                             // 12: risk.screener.Verdict
	(*timestamppb.Timestamp)(nil),            // 13: google.protobuf.Timestamp
	(PotentialRiskFlag)(0),                   // 14: risk.screener.PotentialRiskFlag
	(RunMode)(0),                             // 15: risk.screener.RunMode
}
var file_api_risk_screener_screener_proto_depIdxs = []int32{
	10, // 0: risk.screener.ScreenerAttempt.criteria:type_name -> enums.ScreenerCriteria
	11, // 1: risk.screener.ScreenerAttempt.status:type_name -> risk.screener.ScreenerStatus
	12, // 2: risk.screener.ScreenerAttempt.verdict:type_name -> risk.screener.Verdict
	13, // 3: risk.screener.ScreenerAttempt.created_at:type_name -> google.protobuf.Timestamp
	13, // 4: risk.screener.ScreenerAttempt.updated_at:type_name -> google.protobuf.Timestamp
	2,  // 5: risk.screener.ScreenerAttempt.manual_verdict_metadata:type_name -> risk.screener.ManualVerdictMetadata
	14, // 6: risk.screener.ScreenerAttempt.potential_risk_flags:type_name -> risk.screener.PotentialRiskFlag
	3,  // 7: risk.screener.ManualVerdictMetadata.verdict_details:type_name -> risk.screener.VerdictDetails
	11, // 8: risk.screener.VerdictDetails.previous_status:type_name -> risk.screener.ScreenerStatus
	12, // 9: risk.screener.VerdictDetails.previous_verdict:type_name -> risk.screener.Verdict
	15, // 10: risk.screener.ScreenerAttemptRiskDataMapping.run_mode:type_name -> risk.screener.RunMode
	13, // 11: risk.screener.ScreenerAttemptRiskDataMapping.created_at:type_name -> google.protobuf.Timestamp
	13, // 12: risk.screener.ScreenerAttemptRiskDataMapping.updated_at:type_name -> google.protobuf.Timestamp
	12, // 13: risk.screener.ScreenerVerdictRequest.verdict:type_name -> risk.screener.Verdict
	10, // 14: risk.screener.CriteriaAndClientReqIDIdentifier.screener_criteria:type_name -> enums.ScreenerCriteria
	6,  // 15: risk.screener.AttemptIdentifier.criteria_client_req_id:type_name -> risk.screener.CriteriaAndClientReqIDIdentifier
	9,  // 16: risk.screener.AdditionalCheckDetails.upi_vpa_name_match:type_name -> risk.screener.UPIVPANameMatch
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_api_risk_screener_screener_proto_init() }
func file_api_risk_screener_screener_proto_init() {
	if File_api_risk_screener_screener_proto != nil {
		return
	}
	file_api_risk_screener_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_screener_screener_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenerAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_screener_screener_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualVerdictMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_screener_screener_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerdictDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_screener_screener_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenerAttemptRiskDataMapping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_screener_screener_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenerVerdictRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_screener_screener_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CriteriaAndClientReqIDIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_screener_screener_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttemptIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_screener_screener_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdditionalCheckDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_screener_screener_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UPIVPANameMatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_screener_screener_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*AttemptIdentifier_AttemptId)(nil),
		(*AttemptIdentifier_CriteriaClientReqId)(nil),
	}
	file_api_risk_screener_screener_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*AdditionalCheckDetails_UpiVpaNameMatch)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_screener_screener_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_screener_screener_proto_goTypes,
		DependencyIndexes: file_api_risk_screener_screener_proto_depIdxs,
		EnumInfos:         file_api_risk_screener_screener_proto_enumTypes,
		MessageInfos:      file_api_risk_screener_screener_proto_msgTypes,
	}.Build()
	File_api_risk_screener_screener_proto = out.File
	file_api_risk_screener_screener_proto_rawDesc = nil
	file_api_risk_screener_screener_proto_goTypes = nil
	file_api_risk_screener_screener_proto_depIdxs = nil
}
