// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/screener/screener.proto

package screener

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/risk/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.ScreenerCriteria(0)
)

// Validate checks the field values on ScreenerAttempt with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ScreenerAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreenerAttempt with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScreenerAttemptMultiError, or nil if none found.
func (m *ScreenerAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreenerAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ClientRequestId

	// no validation rules for Criteria

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := ScreenerAttemptValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _ScreenerAttempt_Status_NotInLookup[m.GetStatus()]; ok {
		err := ScreenerAttemptValidationError{
			field:  "Status",
			reason: "value must not be in list [SCREENER_STATUS_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Verdict

	if val := m.GetScore(); val < 0 || val > 100 {
		err := ScreenerAttemptValidationError{
			field:  "Score",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScreenerAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScreenerAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScreenerAttemptValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScreenerAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScreenerAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScreenerAttemptValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeletedAtUnix

	if all {
		switch v := interface{}(m.GetManualVerdictMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScreenerAttemptValidationError{
					field:  "ManualVerdictMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScreenerAttemptValidationError{
					field:  "ManualVerdictMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetManualVerdictMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScreenerAttemptValidationError{
				field:  "ManualVerdictMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ScreenerAttemptMultiError(errors)
	}

	return nil
}

// ScreenerAttemptMultiError is an error wrapping multiple validation errors
// returned by ScreenerAttempt.ValidateAll() if the designated constraints
// aren't met.
type ScreenerAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreenerAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreenerAttemptMultiError) AllErrors() []error { return m }

// ScreenerAttemptValidationError is the validation error returned by
// ScreenerAttempt.Validate if the designated constraints aren't met.
type ScreenerAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreenerAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreenerAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreenerAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreenerAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreenerAttemptValidationError) ErrorName() string { return "ScreenerAttemptValidationError" }

// Error satisfies the builtin error interface
func (e ScreenerAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreenerAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreenerAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreenerAttemptValidationError{}

var _ScreenerAttempt_Status_NotInLookup = map[ScreenerStatus]struct{}{
	0: {},
}

// Validate checks the field values on ManualVerdictMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ManualVerdictMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ManualVerdictMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ManualVerdictMetadataMultiError, or nil if none found.
func (m *ManualVerdictMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *ManualVerdictMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetVerdictDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ManualVerdictMetadataValidationError{
						field:  fmt.Sprintf("VerdictDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ManualVerdictMetadataValidationError{
						field:  fmt.Sprintf("VerdictDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ManualVerdictMetadataValidationError{
					field:  fmt.Sprintf("VerdictDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ManualVerdictMetadataMultiError(errors)
	}

	return nil
}

// ManualVerdictMetadataMultiError is an error wrapping multiple validation
// errors returned by ManualVerdictMetadata.ValidateAll() if the designated
// constraints aren't met.
type ManualVerdictMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ManualVerdictMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ManualVerdictMetadataMultiError) AllErrors() []error { return m }

// ManualVerdictMetadataValidationError is the validation error returned by
// ManualVerdictMetadata.Validate if the designated constraints aren't met.
type ManualVerdictMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ManualVerdictMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ManualVerdictMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ManualVerdictMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ManualVerdictMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ManualVerdictMetadataValidationError) ErrorName() string {
	return "ManualVerdictMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e ManualVerdictMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sManualVerdictMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ManualVerdictMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ManualVerdictMetadataValidationError{}

// Validate checks the field values on VerdictDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VerdictDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerdictDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VerdictDetailsMultiError,
// or nil if none found.
func (m *VerdictDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *VerdictDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AnalystEmail

	// no validation rules for Reason

	if _, ok := _VerdictDetails_PreviousStatus_NotInLookup[m.GetPreviousStatus()]; ok {
		err := VerdictDetailsValidationError{
			field:  "PreviousStatus",
			reason: "value must not be in list [SCREENER_STATUS_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _VerdictDetails_PreviousVerdict_NotInLookup[m.GetPreviousVerdict()]; ok {
		err := VerdictDetailsValidationError{
			field:  "PreviousVerdict",
			reason: "value must not be in list [VERDICT_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return VerdictDetailsMultiError(errors)
	}

	return nil
}

// VerdictDetailsMultiError is an error wrapping multiple validation errors
// returned by VerdictDetails.ValidateAll() if the designated constraints
// aren't met.
type VerdictDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerdictDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerdictDetailsMultiError) AllErrors() []error { return m }

// VerdictDetailsValidationError is the validation error returned by
// VerdictDetails.Validate if the designated constraints aren't met.
type VerdictDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerdictDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerdictDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerdictDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerdictDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerdictDetailsValidationError) ErrorName() string { return "VerdictDetailsValidationError" }

// Error satisfies the builtin error interface
func (e VerdictDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerdictDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerdictDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerdictDetailsValidationError{}

var _VerdictDetails_PreviousStatus_NotInLookup = map[ScreenerStatus]struct{}{
	0: {},
}

var _VerdictDetails_PreviousVerdict_NotInLookup = map[Verdict]struct{}{
	0: {},
}

// Validate checks the field values on ScreenerAttemptRiskDataMapping with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScreenerAttemptRiskDataMapping) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreenerAttemptRiskDataMapping with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ScreenerAttemptRiskDataMappingMultiError, or nil if none found.
func (m *ScreenerAttemptRiskDataMapping) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreenerAttemptRiskDataMapping) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if utf8.RuneCountInString(m.GetRiskDataId()) < 1 {
		err := ScreenerAttemptRiskDataMappingValidationError{
			field:  "RiskDataId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetScreenerAttemptId()) < 1 {
		err := ScreenerAttemptRiskDataMappingValidationError{
			field:  "ScreenerAttemptId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _ScreenerAttemptRiskDataMapping_RunMode_NotInLookup[m.GetRunMode()]; ok {
		err := ScreenerAttemptRiskDataMappingValidationError{
			field:  "RunMode",
			reason: "value must not be in list [RUN_MODE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScreenerAttemptRiskDataMappingValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScreenerAttemptRiskDataMappingValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScreenerAttemptRiskDataMappingValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScreenerAttemptRiskDataMappingValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScreenerAttemptRiskDataMappingValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScreenerAttemptRiskDataMappingValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ScreenerAttemptRiskDataMappingMultiError(errors)
	}

	return nil
}

// ScreenerAttemptRiskDataMappingMultiError is an error wrapping multiple
// validation errors returned by ScreenerAttemptRiskDataMapping.ValidateAll()
// if the designated constraints aren't met.
type ScreenerAttemptRiskDataMappingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreenerAttemptRiskDataMappingMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreenerAttemptRiskDataMappingMultiError) AllErrors() []error { return m }

// ScreenerAttemptRiskDataMappingValidationError is the validation error
// returned by ScreenerAttemptRiskDataMapping.Validate if the designated
// constraints aren't met.
type ScreenerAttemptRiskDataMappingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreenerAttemptRiskDataMappingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreenerAttemptRiskDataMappingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreenerAttemptRiskDataMappingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreenerAttemptRiskDataMappingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreenerAttemptRiskDataMappingValidationError) ErrorName() string {
	return "ScreenerAttemptRiskDataMappingValidationError"
}

// Error satisfies the builtin error interface
func (e ScreenerAttemptRiskDataMappingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreenerAttemptRiskDataMapping.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreenerAttemptRiskDataMappingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreenerAttemptRiskDataMappingValidationError{}

var _ScreenerAttemptRiskDataMapping_RunMode_NotInLookup = map[RunMode]struct{}{
	0: {},
}

// Validate checks the field values on ScreenerVerdictRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScreenerVerdictRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreenerVerdictRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScreenerVerdictRequestMultiError, or nil if none found.
func (m *ScreenerVerdictRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreenerVerdictRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := ScreenerVerdictRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetCaseId()) < 1 {
		err := ScreenerVerdictRequestValidationError{
			field:  "CaseId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ScreenerAttemptId

	if _, ok := _ScreenerVerdictRequest_Verdict_NotInLookup[m.GetVerdict()]; ok {
		err := ScreenerVerdictRequestValidationError{
			field:  "Verdict",
			reason: "value must not be in list [VERDICT_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ScreenerVerdictRequestMultiError(errors)
	}

	return nil
}

// ScreenerVerdictRequestMultiError is an error wrapping multiple validation
// errors returned by ScreenerVerdictRequest.ValidateAll() if the designated
// constraints aren't met.
type ScreenerVerdictRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreenerVerdictRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreenerVerdictRequestMultiError) AllErrors() []error { return m }

// ScreenerVerdictRequestValidationError is the validation error returned by
// ScreenerVerdictRequest.Validate if the designated constraints aren't met.
type ScreenerVerdictRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreenerVerdictRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreenerVerdictRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreenerVerdictRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreenerVerdictRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreenerVerdictRequestValidationError) ErrorName() string {
	return "ScreenerVerdictRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ScreenerVerdictRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreenerVerdictRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreenerVerdictRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreenerVerdictRequestValidationError{}

var _ScreenerVerdictRequest_Verdict_NotInLookup = map[Verdict]struct{}{
	0: {},
}

// Validate checks the field values on CriteriaAndClientReqIDIdentifier with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CriteriaAndClientReqIDIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CriteriaAndClientReqIDIdentifier with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CriteriaAndClientReqIDIdentifierMultiError, or nil if none found.
func (m *CriteriaAndClientReqIDIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *CriteriaAndClientReqIDIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ScreenerCriteria

	// no validation rules for ClientRequestId

	if len(errors) > 0 {
		return CriteriaAndClientReqIDIdentifierMultiError(errors)
	}

	return nil
}

// CriteriaAndClientReqIDIdentifierMultiError is an error wrapping multiple
// validation errors returned by
// CriteriaAndClientReqIDIdentifier.ValidateAll() if the designated
// constraints aren't met.
type CriteriaAndClientReqIDIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CriteriaAndClientReqIDIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CriteriaAndClientReqIDIdentifierMultiError) AllErrors() []error { return m }

// CriteriaAndClientReqIDIdentifierValidationError is the validation error
// returned by CriteriaAndClientReqIDIdentifier.Validate if the designated
// constraints aren't met.
type CriteriaAndClientReqIDIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CriteriaAndClientReqIDIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CriteriaAndClientReqIDIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CriteriaAndClientReqIDIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CriteriaAndClientReqIDIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CriteriaAndClientReqIDIdentifierValidationError) ErrorName() string {
	return "CriteriaAndClientReqIDIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e CriteriaAndClientReqIDIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCriteriaAndClientReqIDIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CriteriaAndClientReqIDIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CriteriaAndClientReqIDIdentifierValidationError{}

// Validate checks the field values on AttemptIdentifier with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AttemptIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttemptIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AttemptIdentifierMultiError, or nil if none found.
func (m *AttemptIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *AttemptIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *AttemptIdentifier_AttemptId:
		if v == nil {
			err := AttemptIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for AttemptId
	case *AttemptIdentifier_CriteriaClientReqId:
		if v == nil {
			err := AttemptIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCriteriaClientReqId()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AttemptIdentifierValidationError{
						field:  "CriteriaClientReqId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AttemptIdentifierValidationError{
						field:  "CriteriaClientReqId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCriteriaClientReqId()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AttemptIdentifierValidationError{
					field:  "CriteriaClientReqId",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AttemptIdentifierMultiError(errors)
	}

	return nil
}

// AttemptIdentifierMultiError is an error wrapping multiple validation errors
// returned by AttemptIdentifier.ValidateAll() if the designated constraints
// aren't met.
type AttemptIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttemptIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttemptIdentifierMultiError) AllErrors() []error { return m }

// AttemptIdentifierValidationError is the validation error returned by
// AttemptIdentifier.Validate if the designated constraints aren't met.
type AttemptIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttemptIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttemptIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttemptIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttemptIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttemptIdentifierValidationError) ErrorName() string {
	return "AttemptIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e AttemptIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttemptIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttemptIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttemptIdentifierValidationError{}

// Validate checks the field values on AdditionalCheckDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AdditionalCheckDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdditionalCheckDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdditionalCheckDetailsMultiError, or nil if none found.
func (m *AdditionalCheckDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AdditionalCheckDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Details.(type) {
	case *AdditionalCheckDetails_UpiVpaNameMatch:
		if v == nil {
			err := AdditionalCheckDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpiVpaNameMatch()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AdditionalCheckDetailsValidationError{
						field:  "UpiVpaNameMatch",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AdditionalCheckDetailsValidationError{
						field:  "UpiVpaNameMatch",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpiVpaNameMatch()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AdditionalCheckDetailsValidationError{
					field:  "UpiVpaNameMatch",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AdditionalCheckDetailsMultiError(errors)
	}

	return nil
}

// AdditionalCheckDetailsMultiError is an error wrapping multiple validation
// errors returned by AdditionalCheckDetails.ValidateAll() if the designated
// constraints aren't met.
type AdditionalCheckDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdditionalCheckDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdditionalCheckDetailsMultiError) AllErrors() []error { return m }

// AdditionalCheckDetailsValidationError is the validation error returned by
// AdditionalCheckDetails.Validate if the designated constraints aren't met.
type AdditionalCheckDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdditionalCheckDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdditionalCheckDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdditionalCheckDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdditionalCheckDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdditionalCheckDetailsValidationError) ErrorName() string {
	return "AdditionalCheckDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AdditionalCheckDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdditionalCheckDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdditionalCheckDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdditionalCheckDetailsValidationError{}

// Validate checks the field values on UPIVPANameMatch with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UPIVPANameMatch) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UPIVPANameMatch with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UPIVPANameMatchMultiError, or nil if none found.
func (m *UPIVPANameMatch) ValidateAll() error {
	return m.validate(true)
}

func (m *UPIVPANameMatch) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UPIVPANameMatchMultiError(errors)
	}

	return nil
}

// UPIVPANameMatchMultiError is an error wrapping multiple validation errors
// returned by UPIVPANameMatch.ValidateAll() if the designated constraints
// aren't met.
type UPIVPANameMatchMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UPIVPANameMatchMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UPIVPANameMatchMultiError) AllErrors() []error { return m }

// UPIVPANameMatchValidationError is the validation error returned by
// UPIVPANameMatch.Validate if the designated constraints aren't met.
type UPIVPANameMatchValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UPIVPANameMatchValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UPIVPANameMatchValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UPIVPANameMatchValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UPIVPANameMatchValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UPIVPANameMatchValidationError) ErrorName() string { return "UPIVPANameMatchValidationError" }

// Error satisfies the builtin error interface
func (e UPIVPANameMatchValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUPIVPANameMatch.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UPIVPANameMatchValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UPIVPANameMatchValidationError{}
