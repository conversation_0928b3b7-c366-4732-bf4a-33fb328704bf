// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/risk/screener/screener.pb.go

package screener

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing ManualVerdictMetadata while reading from DB
func (a *ManualVerdictMetadata) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return backport.SafeUnmarshal(unmarshalOptions.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the ManualVerdictMetadata in string format in DB
func (a *ManualVerdictMetadata) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for ManualVerdictMetadata
func (a *ManualVerdictMetadata) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for ManualVerdictMetadata
func (a *ManualVerdictMetadata) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
