package screener

import (
	"database/sql/driver"
	"fmt"

	"github.com/lib/pq"

	"github.com/epifi/gamma/api/risk/case_management/enums"
)

var (
	verdictEnumMap = map[enums.Verdict]Verdict{
		enums.Verdict_VERDICT_FAIL:        Verdict_VERDICT_FAIL,
		enums.Verdict_VERDICT_PASS:        Verdict_VERDICT_PASS,
		enums.Verdict_VERDICT_UNSPECIFIED: Verdict_VERDICT_UNSPECIFIED,
	}
)

func FromCMVerdict(v enums.Verdict) Verdict {
	return verdictEnumMap[v]
}

type PotentialRiskFlags []PotentialRiskFlag

func (x PotentialRiskFlags) Value() (driver.Value, error) {
	var flags []string
	for _, f := range x {
		flags = append(flags, f.String())
	}
	strArr := pq.StringArray(flags)
	return strArr.Value()
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *PotentialRiskFlags) Scan(src interface{}) error {
	strArr := pq.StringArray([]string{})
	if err := strArr.Scan(src); err != nil {
		return fmt.Errorf("failed to scan []string using pq.StringArray, str: %v", src)
	}

	var flags []PotentialRiskFlag
	for _, v := range strArr {
		flags = append(flags, PotentialRiskFlag(PotentialRiskFlag_value[v]))
	}

	*x = flags
	return nil
}
