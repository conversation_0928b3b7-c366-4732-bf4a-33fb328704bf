// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/essential/contact_association.proto

package essential

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetIngressContactSignalsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetIngressContactSignalsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetIngressContactSignalsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetIngressContactSignalsRequestMultiError, or nil if none found.
func (m *GetIngressContactSignalsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetIngressContactSignalsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HashedPhoneNumber

	// no validation rules for BreakAfterForLeaCount

	// no validation rules for BreakAfterForBlockedCount

	// no validation rules for ShouldFetchLeaCount

	// no validation rules for ShouldFetchBlockedCount

	if len(errors) > 0 {
		return GetIngressContactSignalsRequestMultiError(errors)
	}

	return nil
}

// GetIngressContactSignalsRequestMultiError is an error wrapping multiple
// validation errors returned by GetIngressContactSignalsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetIngressContactSignalsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetIngressContactSignalsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetIngressContactSignalsRequestMultiError) AllErrors() []error { return m }

// GetIngressContactSignalsRequestValidationError is the validation error
// returned by GetIngressContactSignalsRequest.Validate if the designated
// constraints aren't met.
type GetIngressContactSignalsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetIngressContactSignalsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetIngressContactSignalsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetIngressContactSignalsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetIngressContactSignalsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetIngressContactSignalsRequestValidationError) ErrorName() string {
	return "GetIngressContactSignalsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetIngressContactSignalsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetIngressContactSignalsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetIngressContactSignalsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetIngressContactSignalsRequestValidationError{}
