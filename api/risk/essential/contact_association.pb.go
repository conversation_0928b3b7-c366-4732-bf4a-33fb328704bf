// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/essential/contact_association.proto

package essential

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetIngressContactSignalsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// hashed phone number for which we want to check contact association
	HashedPhoneNumber string `protobuf:"bytes,1,opt,name=hashed_phone_number,json=hashedPhoneNumber,proto3" json:"hashed_phone_number,omitempty"`
	// when we get BreakAfterForLEACount LEA contact association we will stop checking for LEA contact association
	// this check will be ignored if BreakAfterForLEACount = 0
	BreakAfterForLeaCount int32 `protobuf:"varint,2,opt,name=break_after_for_lea_count,json=breakAfterForLeaCount,proto3" json:"break_after_for_lea_count,omitempty"`
	// when we get BreakAfterForBlockedCount blocked contact association we will stop checking for blocked contact association
	// this check will be ignored if BreakAfterForBlockedCount = 0
	BreakAfterForBlockedCount int32 `protobuf:"varint,3,opt,name=break_after_for_blocked_count,json=breakAfterForBlockedCount,proto3" json:"break_after_for_blocked_count,omitempty"`
	// If this is true then only we will fetch LEA contact association count
	ShouldFetchLeaCount bool `protobuf:"varint,4,opt,name=should_fetch_lea_count,json=shouldFetchLeaCount,proto3" json:"should_fetch_lea_count,omitempty"`
	// If this is true then only we will fetch blocked contact association count
	ShouldFetchBlockedCount bool `protobuf:"varint,5,opt,name=should_fetch_blocked_count,json=shouldFetchBlockedCount,proto3" json:"should_fetch_blocked_count,omitempty"`
}

func (x *GetIngressContactSignalsRequest) Reset() {
	*x = GetIngressContactSignalsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_essential_contact_association_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIngressContactSignalsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIngressContactSignalsRequest) ProtoMessage() {}

func (x *GetIngressContactSignalsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_essential_contact_association_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIngressContactSignalsRequest.ProtoReflect.Descriptor instead.
func (*GetIngressContactSignalsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_essential_contact_association_proto_rawDescGZIP(), []int{0}
}

func (x *GetIngressContactSignalsRequest) GetHashedPhoneNumber() string {
	if x != nil {
		return x.HashedPhoneNumber
	}
	return ""
}

func (x *GetIngressContactSignalsRequest) GetBreakAfterForLeaCount() int32 {
	if x != nil {
		return x.BreakAfterForLeaCount
	}
	return 0
}

func (x *GetIngressContactSignalsRequest) GetBreakAfterForBlockedCount() int32 {
	if x != nil {
		return x.BreakAfterForBlockedCount
	}
	return 0
}

func (x *GetIngressContactSignalsRequest) GetShouldFetchLeaCount() bool {
	if x != nil {
		return x.ShouldFetchLeaCount
	}
	return false
}

func (x *GetIngressContactSignalsRequest) GetShouldFetchBlockedCount() bool {
	if x != nil {
		return x.ShouldFetchBlockedCount
	}
	return false
}

var File_api_risk_essential_contact_association_proto protoreflect.FileDescriptor

var file_api_risk_essential_contact_association_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x65, 0x73, 0x73, 0x65, 0x6e,
	0x74, 0x69, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x61, 0x73, 0x73,
	0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x65, 0x73, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x22, 0xbf,
	0x02, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x38, 0x0a, 0x19, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x5f, 0x61, 0x66, 0x74, 0x65,
	0x72, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x41, 0x66, 0x74, 0x65,
	0x72, 0x46, 0x6f, 0x72, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x40, 0x0a, 0x1d,
	0x62, 0x72, 0x65, 0x61, 0x6b, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x66, 0x6f, 0x72, 0x5f,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x19, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x41, 0x66, 0x74, 0x65, 0x72, 0x46,
	0x6f, 0x72, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33,
	0x0a, 0x16, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x6c,
	0x65, 0x61, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13,
	0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x65, 0x61, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x1a, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x65, 0x73, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x5a, 0x29, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x65,
	0x73, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_essential_contact_association_proto_rawDescOnce sync.Once
	file_api_risk_essential_contact_association_proto_rawDescData = file_api_risk_essential_contact_association_proto_rawDesc
)

func file_api_risk_essential_contact_association_proto_rawDescGZIP() []byte {
	file_api_risk_essential_contact_association_proto_rawDescOnce.Do(func() {
		file_api_risk_essential_contact_association_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_essential_contact_association_proto_rawDescData)
	})
	return file_api_risk_essential_contact_association_proto_rawDescData
}

var file_api_risk_essential_contact_association_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_risk_essential_contact_association_proto_goTypes = []interface{}{
	(*GetIngressContactSignalsRequest)(nil), // 0: risk.essential.GetIngressContactSignalsRequest
}
var file_api_risk_essential_contact_association_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_risk_essential_contact_association_proto_init() }
func file_api_risk_essential_contact_association_proto_init() {
	if File_api_risk_essential_contact_association_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_essential_contact_association_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIngressContactSignalsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_essential_contact_association_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_essential_contact_association_proto_goTypes,
		DependencyIndexes: file_api_risk_essential_contact_association_proto_depIdxs,
		MessageInfos:      file_api_risk_essential_contact_association_proto_msgTypes,
	}.Build()
	File_api_risk_essential_contact_association_proto = out.File
	file_api_risk_essential_contact_association_proto_rawDesc = nil
	file_api_risk_essential_contact_association_proto_goTypes = nil
	file_api_risk_essential_contact_association_proto_depIdxs = nil
}
