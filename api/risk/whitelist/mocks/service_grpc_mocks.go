// Code generated by MockGen. DO NOT EDIT.
// Source: api/risk/whitelist/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	whitelist "github.com/epifi/gamma/api/risk/whitelist"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockWhiteListClient is a mock of WhiteListClient interface.
type MockWhiteListClient struct {
	ctrl     *gomock.Controller
	recorder *MockWhiteListClientMockRecorder
}

// MockWhiteListClientMockRecorder is the mock recorder for MockWhiteListClient.
type MockWhiteListClientMockRecorder struct {
	mock *MockWhiteListClient
}

// NewMockWhiteListClient creates a new mock instance.
func NewMockWhiteListClient(ctrl *gomock.Controller) *MockWhiteListClient {
	mock := &MockWhiteListClient{ctrl: ctrl}
	mock.recorder = &MockWhiteListClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWhiteListClient) EXPECT() *MockWhiteListClientMockRecorder {
	return m.recorder
}

// BulkAddMembers mocks base method.
func (m *MockWhiteListClient) BulkAddMembers(ctx context.Context, in *whitelist.BulkAddMembersRequest, opts ...grpc.CallOption) (*whitelist.BulkAddMembersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BulkAddMembers", varargs...)
	ret0, _ := ret[0].(*whitelist.BulkAddMembersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkAddMembers indicates an expected call of BulkAddMembers.
func (mr *MockWhiteListClientMockRecorder) BulkAddMembers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkAddMembers", reflect.TypeOf((*MockWhiteListClient)(nil).BulkAddMembers), varargs...)
}

// BulkRemoveMembers mocks base method.
func (m *MockWhiteListClient) BulkRemoveMembers(ctx context.Context, in *whitelist.BulkRemoveMembersRequest, opts ...grpc.CallOption) (*whitelist.BulkRemoveMembersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BulkRemoveMembers", varargs...)
	ret0, _ := ret[0].(*whitelist.BulkRemoveMembersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkRemoveMembers indicates an expected call of BulkRemoveMembers.
func (mr *MockWhiteListClientMockRecorder) BulkRemoveMembers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkRemoveMembers", reflect.TypeOf((*MockWhiteListClient)(nil).BulkRemoveMembers), varargs...)
}

// MockWhiteListServer is a mock of WhiteListServer interface.
type MockWhiteListServer struct {
	ctrl     *gomock.Controller
	recorder *MockWhiteListServerMockRecorder
}

// MockWhiteListServerMockRecorder is the mock recorder for MockWhiteListServer.
type MockWhiteListServerMockRecorder struct {
	mock *MockWhiteListServer
}

// NewMockWhiteListServer creates a new mock instance.
func NewMockWhiteListServer(ctrl *gomock.Controller) *MockWhiteListServer {
	mock := &MockWhiteListServer{ctrl: ctrl}
	mock.recorder = &MockWhiteListServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWhiteListServer) EXPECT() *MockWhiteListServerMockRecorder {
	return m.recorder
}

// BulkAddMembers mocks base method.
func (m *MockWhiteListServer) BulkAddMembers(arg0 context.Context, arg1 *whitelist.BulkAddMembersRequest) (*whitelist.BulkAddMembersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkAddMembers", arg0, arg1)
	ret0, _ := ret[0].(*whitelist.BulkAddMembersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkAddMembers indicates an expected call of BulkAddMembers.
func (mr *MockWhiteListServerMockRecorder) BulkAddMembers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkAddMembers", reflect.TypeOf((*MockWhiteListServer)(nil).BulkAddMembers), arg0, arg1)
}

// BulkRemoveMembers mocks base method.
func (m *MockWhiteListServer) BulkRemoveMembers(arg0 context.Context, arg1 *whitelist.BulkRemoveMembersRequest) (*whitelist.BulkRemoveMembersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkRemoveMembers", arg0, arg1)
	ret0, _ := ret[0].(*whitelist.BulkRemoveMembersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkRemoveMembers indicates an expected call of BulkRemoveMembers.
func (mr *MockWhiteListServerMockRecorder) BulkRemoveMembers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkRemoveMembers", reflect.TypeOf((*MockWhiteListServer)(nil).BulkRemoveMembers), arg0, arg1)
}

// MockUnsafeWhiteListServer is a mock of UnsafeWhiteListServer interface.
type MockUnsafeWhiteListServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeWhiteListServerMockRecorder
}

// MockUnsafeWhiteListServerMockRecorder is the mock recorder for MockUnsafeWhiteListServer.
type MockUnsafeWhiteListServerMockRecorder struct {
	mock *MockUnsafeWhiteListServer
}

// NewMockUnsafeWhiteListServer creates a new mock instance.
func NewMockUnsafeWhiteListServer(ctrl *gomock.Controller) *MockUnsafeWhiteListServer {
	mock := &MockUnsafeWhiteListServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeWhiteListServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeWhiteListServer) EXPECT() *MockUnsafeWhiteListServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedWhiteListServer mocks base method.
func (m *MockUnsafeWhiteListServer) mustEmbedUnimplementedWhiteListServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedWhiteListServer")
}

// mustEmbedUnimplementedWhiteListServer indicates an expected call of mustEmbedUnimplementedWhiteListServer.
func (mr *MockUnsafeWhiteListServerMockRecorder) mustEmbedUnimplementedWhiteListServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedWhiteListServer", reflect.TypeOf((*MockUnsafeWhiteListServer)(nil).mustEmbedUnimplementedWhiteListServer))
}
