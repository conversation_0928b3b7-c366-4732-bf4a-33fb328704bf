// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/whitelist/service.proto

package whitelist

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on BulkRemoveMembersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkRemoveMembersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkRemoveMembersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkRemoveMembersRequestMultiError, or nil if none found.
func (m *BulkRemoveMembersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkRemoveMembersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetMemberItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BulkRemoveMembersRequestValidationError{
						field:  fmt.Sprintf("MemberItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BulkRemoveMembersRequestValidationError{
						field:  fmt.Sprintf("MemberItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BulkRemoveMembersRequestValidationError{
					field:  fmt.Sprintf("MemberItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if utf8.RuneCountInString(m.GetDeletedBy()) < 1 {
		err := BulkRemoveMembersRequestValidationError{
			field:  "DeletedBy",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetReason()) < 1 {
		err := BulkRemoveMembersRequestValidationError{
			field:  "Reason",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BulkRemoveMembersRequestMultiError(errors)
	}

	return nil
}

// BulkRemoveMembersRequestMultiError is an error wrapping multiple validation
// errors returned by BulkRemoveMembersRequest.ValidateAll() if the designated
// constraints aren't met.
type BulkRemoveMembersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkRemoveMembersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkRemoveMembersRequestMultiError) AllErrors() []error { return m }

// BulkRemoveMembersRequestValidationError is the validation error returned by
// BulkRemoveMembersRequest.Validate if the designated constraints aren't met.
type BulkRemoveMembersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkRemoveMembersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkRemoveMembersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkRemoveMembersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkRemoveMembersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkRemoveMembersRequestValidationError) ErrorName() string {
	return "BulkRemoveMembersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BulkRemoveMembersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkRemoveMembersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkRemoveMembersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkRemoveMembersRequestValidationError{}

// Validate checks the field values on BulkRemoveMembersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkRemoveMembersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkRemoveMembersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkRemoveMembersResponseMultiError, or nil if none found.
func (m *BulkRemoveMembersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkRemoveMembersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BulkRemoveMembersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BulkRemoveMembersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BulkRemoveMembersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFailures() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BulkRemoveMembersResponseValidationError{
						field:  fmt.Sprintf("Failures[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BulkRemoveMembersResponseValidationError{
						field:  fmt.Sprintf("Failures[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BulkRemoveMembersResponseValidationError{
					field:  fmt.Sprintf("Failures[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BulkRemoveMembersResponseMultiError(errors)
	}

	return nil
}

// BulkRemoveMembersResponseMultiError is an error wrapping multiple validation
// errors returned by BulkRemoveMembersResponse.ValidateAll() if the
// designated constraints aren't met.
type BulkRemoveMembersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkRemoveMembersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkRemoveMembersResponseMultiError) AllErrors() []error { return m }

// BulkRemoveMembersResponseValidationError is the validation error returned by
// BulkRemoveMembersResponse.Validate if the designated constraints aren't met.
type BulkRemoveMembersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkRemoveMembersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkRemoveMembersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkRemoveMembersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkRemoveMembersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkRemoveMembersResponseValidationError) ErrorName() string {
	return "BulkRemoveMembersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BulkRemoveMembersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkRemoveMembersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkRemoveMembersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkRemoveMembersResponseValidationError{}

// Validate checks the field values on BulkAddMembersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkAddMembersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkAddMembersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkAddMembersRequestMultiError, or nil if none found.
func (m *BulkAddMembersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkAddMembersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetMemberItem() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BulkAddMembersRequestValidationError{
						field:  fmt.Sprintf("MemberItem[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BulkAddMembersRequestValidationError{
						field:  fmt.Sprintf("MemberItem[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BulkAddMembersRequestValidationError{
					field:  fmt.Sprintf("MemberItem[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AddedBy

	if len(errors) > 0 {
		return BulkAddMembersRequestMultiError(errors)
	}

	return nil
}

// BulkAddMembersRequestMultiError is an error wrapping multiple validation
// errors returned by BulkAddMembersRequest.ValidateAll() if the designated
// constraints aren't met.
type BulkAddMembersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkAddMembersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkAddMembersRequestMultiError) AllErrors() []error { return m }

// BulkAddMembersRequestValidationError is the validation error returned by
// BulkAddMembersRequest.Validate if the designated constraints aren't met.
type BulkAddMembersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkAddMembersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkAddMembersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkAddMembersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkAddMembersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkAddMembersRequestValidationError) ErrorName() string {
	return "BulkAddMembersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BulkAddMembersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkAddMembersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkAddMembersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkAddMembersRequestValidationError{}

// Validate checks the field values on BulkAddMembersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkAddMembersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkAddMembersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkAddMembersResponseMultiError, or nil if none found.
func (m *BulkAddMembersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkAddMembersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BulkAddMembersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BulkAddMembersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BulkAddMembersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFailures() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BulkAddMembersResponseValidationError{
						field:  fmt.Sprintf("Failures[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BulkAddMembersResponseValidationError{
						field:  fmt.Sprintf("Failures[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BulkAddMembersResponseValidationError{
					field:  fmt.Sprintf("Failures[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BulkAddMembersResponseMultiError(errors)
	}

	return nil
}

// BulkAddMembersResponseMultiError is an error wrapping multiple validation
// errors returned by BulkAddMembersResponse.ValidateAll() if the designated
// constraints aren't met.
type BulkAddMembersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkAddMembersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkAddMembersResponseMultiError) AllErrors() []error { return m }

// BulkAddMembersResponseValidationError is the validation error returned by
// BulkAddMembersResponse.Validate if the designated constraints aren't met.
type BulkAddMembersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkAddMembersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkAddMembersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkAddMembersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkAddMembersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkAddMembersResponseValidationError) ErrorName() string {
	return "BulkAddMembersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BulkAddMembersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkAddMembersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkAddMembersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkAddMembersResponseValidationError{}

// Validate checks the field values on MembersFailure with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MembersFailure) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MembersFailure with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MembersFailureMultiError,
// or nil if none found.
func (m *MembersFailure) ValidateAll() error {
	return m.validate(true)
}

func (m *MembersFailure) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMemberItem()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MembersFailureValidationError{
					field:  "MemberItem",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MembersFailureValidationError{
					field:  "MemberItem",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMemberItem()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MembersFailureValidationError{
				field:  "MemberItem",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ErrorString

	if len(errors) > 0 {
		return MembersFailureMultiError(errors)
	}

	return nil
}

// MembersFailureMultiError is an error wrapping multiple validation errors
// returned by MembersFailure.ValidateAll() if the designated constraints
// aren't met.
type MembersFailureMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MembersFailureMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MembersFailureMultiError) AllErrors() []error { return m }

// MembersFailureValidationError is the validation error returned by
// MembersFailure.Validate if the designated constraints aren't met.
type MembersFailureValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MembersFailureValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MembersFailureValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MembersFailureValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MembersFailureValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MembersFailureValidationError) ErrorName() string { return "MembersFailureValidationError" }

// Error satisfies the builtin error interface
func (e MembersFailureValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMembersFailure.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MembersFailureValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MembersFailureValidationError{}
