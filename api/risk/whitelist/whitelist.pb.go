//go:generate gen_sql -types=Category,IdentifierType,MemberItem,DeletionMetadata

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/whitelist/whitelist.proto

package whitelist

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Category used to specify the type of members that we store in whitelist.
type Category int32

const (
	Category_CATEGORY_UNSPECIFIED Category = 0
	// Current Fi Employees
	Category_CATEGORY_FI_EMPLOYEES Category = 1
	// EX Fi Employees (who have left now)
	Category_CATEGORY_EX_FI_EMPLOYEES Category = 2
	// Investors in Fi
	Category_CATEGORY_FI_INVESTOR Category = 3
	// Federal Bank employees
	Category_CATEGORY_FEDERAL_BANK_EMPLOYEES Category = 4
	// Friends and Family of current Fi employees
	Category_CATEGORY_FI_EMPLOYEES_FNF Category = 5
	// Regulator / Actor / Actress / Senior Executive of a company
	Category_CATEGORY_FI_VIP Category = 6
)

// Enum value maps for Category.
var (
	Category_name = map[int32]string{
		0: "CATEGORY_UNSPECIFIED",
		1: "CATEGORY_FI_EMPLOYEES",
		2: "CATEGORY_EX_FI_EMPLOYEES",
		3: "CATEGORY_FI_INVESTOR",
		4: "CATEGORY_FEDERAL_BANK_EMPLOYEES",
		5: "CATEGORY_FI_EMPLOYEES_FNF",
		6: "CATEGORY_FI_VIP",
	}
	Category_value = map[string]int32{
		"CATEGORY_UNSPECIFIED":            0,
		"CATEGORY_FI_EMPLOYEES":           1,
		"CATEGORY_EX_FI_EMPLOYEES":        2,
		"CATEGORY_FI_INVESTOR":            3,
		"CATEGORY_FEDERAL_BANK_EMPLOYEES": 4,
		"CATEGORY_FI_EMPLOYEES_FNF":       5,
		"CATEGORY_FI_VIP":                 6,
	}
)

func (x Category) Enum() *Category {
	p := new(Category)
	*p = x
	return p
}

func (x Category) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Category) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_whitelist_whitelist_proto_enumTypes[0].Descriptor()
}

func (Category) Type() protoreflect.EnumType {
	return &file_api_risk_whitelist_whitelist_proto_enumTypes[0]
}

func (x Category) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Category.Descriptor instead.
func (Category) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_whitelist_whitelist_proto_rawDescGZIP(), []int{0}
}

// whitelist field mask contains an entry for each whitelist column
type WhiteListFieldMask int32

const (
	WhiteListFieldMask_WHITELIST_FIELD_MASK_UNSPECIFIED       WhiteListFieldMask = 0
	WhiteListFieldMask_WHITELIST_FIELD_MASK_ALL               WhiteListFieldMask = 1
	WhiteListFieldMask_WHITELIST_FIELD_MASK_ID                WhiteListFieldMask = 2
	WhiteListFieldMask_WHITELIST_FIELD_MASK_IDENTIFIER_TYPE   WhiteListFieldMask = 3
	WhiteListFieldMask_WHITELIST_FIELD_MASK_IDENTIFIER_VALUE  WhiteListFieldMask = 4
	WhiteListFieldMask_WHITELIST_FIELD_MASK_ADDED_BY          WhiteListFieldMask = 5
	WhiteListFieldMask_WHITELIST_FIELD_MASK_DELETION_METADATA WhiteListFieldMask = 6
	WhiteListFieldMask_WHITELIST_FIELD_MASK_CREATED_AT        WhiteListFieldMask = 7
	WhiteListFieldMask_WHITELIST_FIELD_MASK_UPDATED_AT        WhiteListFieldMask = 8
	WhiteListFieldMask_WHITELIST_FIELD_MASK_DELETED_AT_UNIX   WhiteListFieldMask = 9
)

// Enum value maps for WhiteListFieldMask.
var (
	WhiteListFieldMask_name = map[int32]string{
		0: "WHITELIST_FIELD_MASK_UNSPECIFIED",
		1: "WHITELIST_FIELD_MASK_ALL",
		2: "WHITELIST_FIELD_MASK_ID",
		3: "WHITELIST_FIELD_MASK_IDENTIFIER_TYPE",
		4: "WHITELIST_FIELD_MASK_IDENTIFIER_VALUE",
		5: "WHITELIST_FIELD_MASK_ADDED_BY",
		6: "WHITELIST_FIELD_MASK_DELETION_METADATA",
		7: "WHITELIST_FIELD_MASK_CREATED_AT",
		8: "WHITELIST_FIELD_MASK_UPDATED_AT",
		9: "WHITELIST_FIELD_MASK_DELETED_AT_UNIX",
	}
	WhiteListFieldMask_value = map[string]int32{
		"WHITELIST_FIELD_MASK_UNSPECIFIED":       0,
		"WHITELIST_FIELD_MASK_ALL":               1,
		"WHITELIST_FIELD_MASK_ID":                2,
		"WHITELIST_FIELD_MASK_IDENTIFIER_TYPE":   3,
		"WHITELIST_FIELD_MASK_IDENTIFIER_VALUE":  4,
		"WHITELIST_FIELD_MASK_ADDED_BY":          5,
		"WHITELIST_FIELD_MASK_DELETION_METADATA": 6,
		"WHITELIST_FIELD_MASK_CREATED_AT":        7,
		"WHITELIST_FIELD_MASK_UPDATED_AT":        8,
		"WHITELIST_FIELD_MASK_DELETED_AT_UNIX":   9,
	}
)

func (x WhiteListFieldMask) Enum() *WhiteListFieldMask {
	p := new(WhiteListFieldMask)
	*p = x
	return p
}

func (x WhiteListFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WhiteListFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_whitelist_whitelist_proto_enumTypes[1].Descriptor()
}

func (WhiteListFieldMask) Type() protoreflect.EnumType {
	return &file_api_risk_whitelist_whitelist_proto_enumTypes[1]
}

func (x WhiteListFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WhiteListFieldMask.Descriptor instead.
func (WhiteListFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_whitelist_whitelist_proto_rawDescGZIP(), []int{1}
}

// type of identifier associated with each entry
type IdentifierType int32

const (
	IdentifierType_IDENTIFIER_TYPE_UNSPECIFIED IdentifierType = 0
	// identifier type is actor
	IdentifierType_IDENTIFIER_TYPE_ACTOR_ID IdentifierType = 1
)

// Enum value maps for IdentifierType.
var (
	IdentifierType_name = map[int32]string{
		0: "IDENTIFIER_TYPE_UNSPECIFIED",
		1: "IDENTIFIER_TYPE_ACTOR_ID",
	}
	IdentifierType_value = map[string]int32{
		"IDENTIFIER_TYPE_UNSPECIFIED": 0,
		"IDENTIFIER_TYPE_ACTOR_ID":    1,
	}
)

func (x IdentifierType) Enum() *IdentifierType {
	p := new(IdentifierType)
	*p = x
	return p
}

func (x IdentifierType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IdentifierType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_whitelist_whitelist_proto_enumTypes[2].Descriptor()
}

func (IdentifierType) Type() protoreflect.EnumType {
	return &file_api_risk_whitelist_whitelist_proto_enumTypes[2]
}

func (x IdentifierType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IdentifierType.Descriptor instead.
func (IdentifierType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_whitelist_whitelist_proto_rawDescGZIP(), []int{2}
}

type Member struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary key
	Id         string      `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	MemberItem *MemberItem `protobuf:"bytes,2,opt,name=member_item,json=memberItem,proto3" json:"member_item,omitempty"`
	// indicates who added this entry, should be email id of person
	AddedBy string `protobuf:"bytes,3,opt,name=added_by,json=addedBy,proto3" json:"added_by,omitempty"`
	// will be present only for deleted records
	DeletionMetadata *DeletionMetadata      `protobuf:"bytes,4,opt,name=deletion_metadata,json=deletionMetadata,proto3" json:"deletion_metadata,omitempty"`
	CreatedAt        *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt        *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAtUnix    int64                  `protobuf:"varint,7,opt,name=deleted_at_unix,json=deletedAtUnix,proto3" json:"deleted_at_unix,omitempty"`
}

func (x *Member) Reset() {
	*x = Member{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_whitelist_whitelist_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Member) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Member) ProtoMessage() {}

func (x *Member) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_whitelist_whitelist_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Member.ProtoReflect.Descriptor instead.
func (*Member) Descriptor() ([]byte, []int) {
	return file_api_risk_whitelist_whitelist_proto_rawDescGZIP(), []int{0}
}

func (x *Member) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Member) GetMemberItem() *MemberItem {
	if x != nil {
		return x.MemberItem
	}
	return nil
}

func (x *Member) GetAddedBy() string {
	if x != nil {
		return x.AddedBy
	}
	return ""
}

func (x *Member) GetDeletionMetadata() *DeletionMetadata {
	if x != nil {
		return x.DeletionMetadata
	}
	return nil
}

func (x *Member) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Member) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Member) GetDeletedAtUnix() int64 {
	if x != nil {
		return x.DeletedAtUnix
	}
	return 0
}

type MemberItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category       Category       `protobuf:"varint,1,opt,name=category,proto3,enum=risk.whitelist.Category" json:"category,omitempty"`
	IdentifierType IdentifierType `protobuf:"varint,2,opt,name=identifier_type,json=identifierType,proto3,enum=risk.whitelist.IdentifierType" json:"identifier_type,omitempty"`
	// identifier value associated with each identifier type
	//
	// Types that are assignable to IdentifierValue:
	//
	//	*MemberItem_ActorId
	IdentifierValue isMemberItem_IdentifierValue `protobuf_oneof:"identifier_value"`
}

func (x *MemberItem) Reset() {
	*x = MemberItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_whitelist_whitelist_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MemberItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberItem) ProtoMessage() {}

func (x *MemberItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_whitelist_whitelist_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberItem.ProtoReflect.Descriptor instead.
func (*MemberItem) Descriptor() ([]byte, []int) {
	return file_api_risk_whitelist_whitelist_proto_rawDescGZIP(), []int{1}
}

func (x *MemberItem) GetCategory() Category {
	if x != nil {
		return x.Category
	}
	return Category_CATEGORY_UNSPECIFIED
}

func (x *MemberItem) GetIdentifierType() IdentifierType {
	if x != nil {
		return x.IdentifierType
	}
	return IdentifierType_IDENTIFIER_TYPE_UNSPECIFIED
}

func (m *MemberItem) GetIdentifierValue() isMemberItem_IdentifierValue {
	if m != nil {
		return m.IdentifierValue
	}
	return nil
}

func (x *MemberItem) GetActorId() string {
	if x, ok := x.GetIdentifierValue().(*MemberItem_ActorId); ok {
		return x.ActorId
	}
	return ""
}

type isMemberItem_IdentifierValue interface {
	isMemberItem_IdentifierValue()
}

type MemberItem_ActorId struct {
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3,oneof"`
}

func (*MemberItem_ActorId) isMemberItem_IdentifierValue() {}

type DeletionMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// should be email id of person who wants to delete whitelist members
	DeletedBy string `protobuf:"bytes,1,opt,name=deleted_by,json=deletedBy,proto3" json:"deleted_by,omitempty"`
	// reason for deletion of whitelist member
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *DeletionMetadata) Reset() {
	*x = DeletionMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_whitelist_whitelist_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletionMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletionMetadata) ProtoMessage() {}

func (x *DeletionMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_whitelist_whitelist_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletionMetadata.ProtoReflect.Descriptor instead.
func (*DeletionMetadata) Descriptor() ([]byte, []int) {
	return file_api_risk_whitelist_whitelist_proto_rawDescGZIP(), []int{2}
}

func (x *DeletionMetadata) GetDeletedBy() string {
	if x != nil {
		return x.DeletedBy
	}
	return ""
}

func (x *DeletionMetadata) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

var File_api_risk_whitelist_whitelist_proto protoreflect.FileDescriptor

var file_api_risk_whitelist_whitelist_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x77, 0x68, 0x69, 0x74, 0x65,
	0x6c, 0x69, 0x73, 0x74, 0x2f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x77, 0x68, 0x69, 0x74, 0x65,
	0x6c, 0x69, 0x73, 0x74, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe6,
	0x02, 0x0a, 0x06, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2e,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x61, 0x64, 0x64, 0x65, 0x64, 0x42, 0x79, 0x12, 0x4d, 0x0a, 0x11, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x77, 0x68, 0x69,
	0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x10, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x26, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x75, 0x6e,
	0x69, 0x78, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x55, 0x6e, 0x69, 0x78, 0x22, 0xd0, 0x01, 0x0a, 0x0a, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x3e, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x51, 0x0a, 0x0f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74,
	0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x5b, 0x0a, 0x10, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x26,
	0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1f, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x2a, 0xd0, 0x01, 0x0a, 0x08, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19,
	0x0a, 0x15, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x5f, 0x45, 0x4d,
	0x50, 0x4c, 0x4f, 0x59, 0x45, 0x45, 0x53, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x45, 0x58, 0x5f, 0x46, 0x49, 0x5f, 0x45, 0x4d, 0x50, 0x4c,
	0x4f, 0x59, 0x45, 0x45, 0x53, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x10,
	0x03, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x45,
	0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f,
	0x59, 0x45, 0x45, 0x53, 0x10, 0x04, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x46, 0x49, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x45, 0x45, 0x53, 0x5f,
	0x46, 0x4e, 0x46, 0x10, 0x05, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x46, 0x49, 0x5f, 0x56, 0x49, 0x50, 0x10, 0x06, 0x2a, 0x8d, 0x03, 0x0a, 0x12, 0x57,
	0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x12, 0x24, 0x0a, 0x20, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x57, 0x48, 0x49, 0x54, 0x45,
	0x4c, 0x49, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x41, 0x4c, 0x4c, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49,
	0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44,
	0x10, 0x02, 0x12, 0x28, 0x0a, 0x24, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25,
	0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f,
	0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x04, 0x12, 0x21, 0x0a, 0x1d, 0x57, 0x48, 0x49, 0x54, 0x45,
	0x4c, 0x49, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x41, 0x44, 0x44, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x10, 0x05, 0x12, 0x2a, 0x0a, 0x26, 0x57, 0x48,
	0x49, 0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x45, 0x54, 0x41,
	0x44, 0x41, 0x54, 0x41, 0x10, 0x06, 0x12, 0x23, 0x0a, 0x1f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c,
	0x49, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x07, 0x12, 0x23, 0x0a, 0x1f, 0x57,
	0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x08,
	0x12, 0x28, 0x0a, 0x24, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44,
	0x5f, 0x41, 0x54, 0x5f, 0x55, 0x4e, 0x49, 0x58, 0x10, 0x09, 0x2a, 0x4f, 0x0a, 0x0e, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b,
	0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a,
	0x18, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x42, 0x56, 0x0a, 0x29, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x77,
	0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c,
	0x69, 0x73, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_whitelist_whitelist_proto_rawDescOnce sync.Once
	file_api_risk_whitelist_whitelist_proto_rawDescData = file_api_risk_whitelist_whitelist_proto_rawDesc
)

func file_api_risk_whitelist_whitelist_proto_rawDescGZIP() []byte {
	file_api_risk_whitelist_whitelist_proto_rawDescOnce.Do(func() {
		file_api_risk_whitelist_whitelist_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_whitelist_whitelist_proto_rawDescData)
	})
	return file_api_risk_whitelist_whitelist_proto_rawDescData
}

var file_api_risk_whitelist_whitelist_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_risk_whitelist_whitelist_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_risk_whitelist_whitelist_proto_goTypes = []interface{}{
	(Category)(0),                 // 0: risk.whitelist.Category
	(WhiteListFieldMask)(0),       // 1: risk.whitelist.WhiteListFieldMask
	(IdentifierType)(0),           // 2: risk.whitelist.IdentifierType
	(*Member)(nil),                // 3: risk.whitelist.Member
	(*MemberItem)(nil),            // 4: risk.whitelist.MemberItem
	(*DeletionMetadata)(nil),      // 5: risk.whitelist.DeletionMetadata
	(*timestamppb.Timestamp)(nil), // 6: google.protobuf.Timestamp
}
var file_api_risk_whitelist_whitelist_proto_depIdxs = []int32{
	4, // 0: risk.whitelist.Member.member_item:type_name -> risk.whitelist.MemberItem
	5, // 1: risk.whitelist.Member.deletion_metadata:type_name -> risk.whitelist.DeletionMetadata
	6, // 2: risk.whitelist.Member.created_at:type_name -> google.protobuf.Timestamp
	6, // 3: risk.whitelist.Member.updated_at:type_name -> google.protobuf.Timestamp
	0, // 4: risk.whitelist.MemberItem.category:type_name -> risk.whitelist.Category
	2, // 5: risk.whitelist.MemberItem.identifier_type:type_name -> risk.whitelist.IdentifierType
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_risk_whitelist_whitelist_proto_init() }
func file_api_risk_whitelist_whitelist_proto_init() {
	if File_api_risk_whitelist_whitelist_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_whitelist_whitelist_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Member); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_whitelist_whitelist_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MemberItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_whitelist_whitelist_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletionMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_whitelist_whitelist_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*MemberItem_ActorId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_whitelist_whitelist_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_whitelist_whitelist_proto_goTypes,
		DependencyIndexes: file_api_risk_whitelist_whitelist_proto_depIdxs,
		EnumInfos:         file_api_risk_whitelist_whitelist_proto_enumTypes,
		MessageInfos:      file_api_risk_whitelist_whitelist_proto_msgTypes,
	}.Build()
	File_api_risk_whitelist_whitelist_proto = out.File
	file_api_risk_whitelist_whitelist_proto_rawDesc = nil
	file_api_risk_whitelist_whitelist_proto_goTypes = nil
	file_api_risk_whitelist_whitelist_proto_depIdxs = nil
}
