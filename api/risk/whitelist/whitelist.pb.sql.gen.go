// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/risk/whitelist/whitelist.pb.go

package whitelist

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"encoding/json"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Valuer interface implementation for storing the Category in string format in DB
func (p Category) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Category while reading from DB
func (p *Category) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Category_value[val]
	if !ok {
		return fmt.Errorf("unexpected Category value: %s", val)
	}
	*p = Category(valInt)
	return nil
}

// Marshaler interface implementation for Category
func (x Category) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Category
func (x *Category) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Category(Category_value[val])
	return nil
}

// Valuer interface implementation for storing the IdentifierType in string format in DB
func (p IdentifierType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing IdentifierType while reading from DB
func (p *IdentifierType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := IdentifierType_value[val]
	if !ok {
		return fmt.Errorf("unexpected IdentifierType value: %s", val)
	}
	*p = IdentifierType(valInt)
	return nil
}

// Marshaler interface implementation for IdentifierType
func (x IdentifierType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for IdentifierType
func (x *IdentifierType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = IdentifierType(IdentifierType_value[val])
	return nil
}

// Scanner interface implementation for parsing MemberItem while reading from DB
func (a *MemberItem) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return backport.SafeUnmarshal(unmarshalOptions.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the MemberItem in string format in DB
func (a *MemberItem) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for MemberItem
func (a *MemberItem) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for MemberItem
func (a *MemberItem) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing DeletionMetadata while reading from DB
func (a *DeletionMetadata) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return backport.SafeUnmarshal(unmarshalOptions.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the DeletionMetadata in string format in DB
func (a *DeletionMetadata) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for DeletionMetadata
func (a *DeletionMetadata) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for DeletionMetadata
func (a *DeletionMetadata) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
