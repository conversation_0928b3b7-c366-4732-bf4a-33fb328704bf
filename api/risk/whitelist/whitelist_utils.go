package whitelist

func (m *MemberItem) GetIdentifierStringValue() string {
	if m.GetIdentifierType() == IdentifierType_IDENTIFIER_TYPE_ACTOR_ID {
		return m.GetActorId()
	}
	return ""
}

func GetIdentifierProtoValue(identifierType IdentifierType, identifierValue string) isMemberItem_IdentifierValue {
	if identifierType == IdentifierType_IDENTIFIER_TYPE_ACTOR_ID {
		return &MemberItem_ActorId{
			ActorId: identifierValue,
		}
	}
	return nil
}

var (
	shouldSkipReviewMap = map[Category]bool{
		Category_CATEGORY_FI_EMPLOYEES:    true,
		Category_CATEGORY_EX_FI_EMPLOYEES: true,
		Category_CATEGORY_FI_INVESTOR:     true,
		Category_CATEGORY_FI_VIP:          true,
	}

	allowedForL2Review = map[Category]bool{
		Category_CATEGORY_FI_EMPLOYEES:           true,
		Category_CATEGORY_EX_FI_EMPLOYEES:        true,
		Category_CATEGORY_FI_INVESTOR:            true,
		Category_CATEGORY_FI_VIP:                 true,
		Category_CATEGORY_FEDERAL_BANK_EMPLOYEES: true,
		Category_CATEGORY_FI_EMPLOYEES_FNF:       true,
	}
)

func (w Category) ShouldSkipCaseReview() bool {
	_, ok := shouldSkipReviewMap[w]
	return ok
}

func (w Category) ShouldSendForL2Review() bool {
	_, ok := allowedForL2Review[w]
	return ok
}
