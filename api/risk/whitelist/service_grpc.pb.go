// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/risk/whitelist/service.proto

package whitelist

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	WhiteList_BulkAddMembers_FullMethodName    = "/risk.whitelist.WhiteList/BulkAddMembers"
	WhiteList_BulkRemoveMembers_FullMethodName = "/risk.whitelist.WhiteList/BulkRemoveMembers"
)

// WhiteListClient is the client API for WhiteList service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WhiteListClient interface {
	// BulkAddMembers rpc is used to add whitelist members in bulk.
	// This rpc supports addition of maximum 1000 members in single call.
	// returns "INTERNAL" status if addition is failed for all the members
	// returns "OK" rpc status when
	// 1) All the entries are added successfully
	// 2) Partial failure case i.e. some entries are added successfully and failed for a subset(will return a list of failures for the failed cases)
	BulkAddMembers(ctx context.Context, in *BulkAddMembersRequest, opts ...grpc.CallOption) (*BulkAddMembersResponse, error)
	// BulkRemoveMembers rpc is used to remove a set of members from the whitelist
	// This rpc supports removing maximum of 1000 members in single call
	// returns "OK" rpc status when
	// 1) All the members passed are removed successfully
	// 2) Partial failure case i.e. if some members are removed successfully and a subset has failed(will return a list of failures for the failed cases)
	BulkRemoveMembers(ctx context.Context, in *BulkRemoveMembersRequest, opts ...grpc.CallOption) (*BulkRemoveMembersResponse, error)
}

type whiteListClient struct {
	cc grpc.ClientConnInterface
}

func NewWhiteListClient(cc grpc.ClientConnInterface) WhiteListClient {
	return &whiteListClient{cc}
}

func (c *whiteListClient) BulkAddMembers(ctx context.Context, in *BulkAddMembersRequest, opts ...grpc.CallOption) (*BulkAddMembersResponse, error) {
	out := new(BulkAddMembersResponse)
	err := c.cc.Invoke(ctx, WhiteList_BulkAddMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *whiteListClient) BulkRemoveMembers(ctx context.Context, in *BulkRemoveMembersRequest, opts ...grpc.CallOption) (*BulkRemoveMembersResponse, error) {
	out := new(BulkRemoveMembersResponse)
	err := c.cc.Invoke(ctx, WhiteList_BulkRemoveMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WhiteListServer is the server API for WhiteList service.
// All implementations should embed UnimplementedWhiteListServer
// for forward compatibility
type WhiteListServer interface {
	// BulkAddMembers rpc is used to add whitelist members in bulk.
	// This rpc supports addition of maximum 1000 members in single call.
	// returns "INTERNAL" status if addition is failed for all the members
	// returns "OK" rpc status when
	// 1) All the entries are added successfully
	// 2) Partial failure case i.e. some entries are added successfully and failed for a subset(will return a list of failures for the failed cases)
	BulkAddMembers(context.Context, *BulkAddMembersRequest) (*BulkAddMembersResponse, error)
	// BulkRemoveMembers rpc is used to remove a set of members from the whitelist
	// This rpc supports removing maximum of 1000 members in single call
	// returns "OK" rpc status when
	// 1) All the members passed are removed successfully
	// 2) Partial failure case i.e. if some members are removed successfully and a subset has failed(will return a list of failures for the failed cases)
	BulkRemoveMembers(context.Context, *BulkRemoveMembersRequest) (*BulkRemoveMembersResponse, error)
}

// UnimplementedWhiteListServer should be embedded to have forward compatible implementations.
type UnimplementedWhiteListServer struct {
}

func (UnimplementedWhiteListServer) BulkAddMembers(context.Context, *BulkAddMembersRequest) (*BulkAddMembersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BulkAddMembers not implemented")
}
func (UnimplementedWhiteListServer) BulkRemoveMembers(context.Context, *BulkRemoveMembersRequest) (*BulkRemoveMembersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BulkRemoveMembers not implemented")
}

// UnsafeWhiteListServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WhiteListServer will
// result in compilation errors.
type UnsafeWhiteListServer interface {
	mustEmbedUnimplementedWhiteListServer()
}

func RegisterWhiteListServer(s grpc.ServiceRegistrar, srv WhiteListServer) {
	s.RegisterService(&WhiteList_ServiceDesc, srv)
}

func _WhiteList_BulkAddMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulkAddMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhiteListServer).BulkAddMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhiteList_BulkAddMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhiteListServer).BulkAddMembers(ctx, req.(*BulkAddMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WhiteList_BulkRemoveMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulkRemoveMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhiteListServer).BulkRemoveMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhiteList_BulkRemoveMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhiteListServer).BulkRemoveMembers(ctx, req.(*BulkRemoveMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WhiteList_ServiceDesc is the grpc.ServiceDesc for WhiteList service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WhiteList_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "risk.whitelist.WhiteList",
	HandlerType: (*WhiteListServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BulkAddMembers",
			Handler:    _WhiteList_BulkAddMembers_Handler,
		},
		{
			MethodName: "BulkRemoveMembers",
			Handler:    _WhiteList_BulkRemoveMembers_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/risk/whitelist/service.proto",
}
