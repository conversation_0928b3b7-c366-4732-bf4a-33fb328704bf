// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/whitelist/whitelist.proto

package whitelist

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Member with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Member) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Member with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in MemberMultiError, or nil if none found.
func (m *Member) ValidateAll() error {
	return m.validate(true)
}

func (m *Member) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetMemberItem()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemberValidationError{
					field:  "MemberItem",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemberValidationError{
					field:  "MemberItem",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMemberItem()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemberValidationError{
				field:  "MemberItem",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetAddedBy()) < 1 {
		err := MemberValidationError{
			field:  "AddedBy",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDeletionMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemberValidationError{
					field:  "DeletionMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemberValidationError{
					field:  "DeletionMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletionMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemberValidationError{
				field:  "DeletionMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemberValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemberValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemberValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemberValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemberValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemberValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeletedAtUnix

	if len(errors) > 0 {
		return MemberMultiError(errors)
	}

	return nil
}

// MemberMultiError is an error wrapping multiple validation errors returned by
// Member.ValidateAll() if the designated constraints aren't met.
type MemberMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemberMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemberMultiError) AllErrors() []error { return m }

// MemberValidationError is the validation error returned by Member.Validate if
// the designated constraints aren't met.
type MemberValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemberValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemberValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemberValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemberValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemberValidationError) ErrorName() string { return "MemberValidationError" }

// Error satisfies the builtin error interface
func (e MemberValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMember.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemberValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemberValidationError{}

// Validate checks the field values on MemberItem with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MemberItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemberItem with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MemberItemMultiError, or
// nil if none found.
func (m *MemberItem) ValidateAll() error {
	return m.validate(true)
}

func (m *MemberItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _MemberItem_Category_NotInLookup[m.GetCategory()]; ok {
		err := MemberItemValidationError{
			field:  "Category",
			reason: "value must not be in list [CATEGORY_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _MemberItem_IdentifierType_NotInLookup[m.GetIdentifierType()]; ok {
		err := MemberItemValidationError{
			field:  "IdentifierType",
			reason: "value must not be in list [IDENTIFIER_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.IdentifierValue.(type) {
	case *MemberItem_ActorId:
		if v == nil {
			err := MemberItemValidationError{
				field:  "IdentifierValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return MemberItemMultiError(errors)
	}

	return nil
}

// MemberItemMultiError is an error wrapping multiple validation errors
// returned by MemberItem.ValidateAll() if the designated constraints aren't met.
type MemberItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemberItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemberItemMultiError) AllErrors() []error { return m }

// MemberItemValidationError is the validation error returned by
// MemberItem.Validate if the designated constraints aren't met.
type MemberItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemberItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemberItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemberItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemberItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemberItemValidationError) ErrorName() string { return "MemberItemValidationError" }

// Error satisfies the builtin error interface
func (e MemberItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemberItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemberItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemberItemValidationError{}

var _MemberItem_Category_NotInLookup = map[Category]struct{}{
	0: {},
}

var _MemberItem_IdentifierType_NotInLookup = map[IdentifierType]struct{}{
	0: {},
}

// Validate checks the field values on DeletionMetadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeletionMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletionMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletionMetadataMultiError, or nil if none found.
func (m *DeletionMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletionMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetDeletedBy()) < 1 {
		err := DeletionMetadataValidationError{
			field:  "DeletedBy",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetReason()) < 1 {
		err := DeletionMetadataValidationError{
			field:  "Reason",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeletionMetadataMultiError(errors)
	}

	return nil
}

// DeletionMetadataMultiError is an error wrapping multiple validation errors
// returned by DeletionMetadata.ValidateAll() if the designated constraints
// aren't met.
type DeletionMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletionMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletionMetadataMultiError) AllErrors() []error { return m }

// DeletionMetadataValidationError is the validation error returned by
// DeletionMetadata.Validate if the designated constraints aren't met.
type DeletionMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletionMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletionMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletionMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletionMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletionMetadataValidationError) ErrorName() string { return "DeletionMetadataValidationError" }

// Error satisfies the builtin error interface
func (e DeletionMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletionMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletionMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletionMetadataValidationError{}
