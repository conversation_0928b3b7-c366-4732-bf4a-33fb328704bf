// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/whitelist/service.proto

package whitelist

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BulkRemoveMembersResponse_Status int32

const (
	BulkRemoveMembersResponse_OK BulkRemoveMembersResponse_Status = 0
	// Invalid argument passed in the request
	BulkRemoveMembersResponse_INVALID_ARGUMENT BulkRemoveMembersResponse_Status = 3
	// internal error while processing the request
	BulkRemoveMembersResponse_INTERNAL BulkRemoveMembersResponse_Status = 13
)

// Enum value maps for BulkRemoveMembersResponse_Status.
var (
	BulkRemoveMembersResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	BulkRemoveMembersResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x BulkRemoveMembersResponse_Status) Enum() *BulkRemoveMembersResponse_Status {
	p := new(BulkRemoveMembersResponse_Status)
	*p = x
	return p
}

func (x BulkRemoveMembersResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BulkRemoveMembersResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_whitelist_service_proto_enumTypes[0].Descriptor()
}

func (BulkRemoveMembersResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_whitelist_service_proto_enumTypes[0]
}

func (x BulkRemoveMembersResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BulkRemoveMembersResponse_Status.Descriptor instead.
func (BulkRemoveMembersResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_whitelist_service_proto_rawDescGZIP(), []int{1, 0}
}

type BulkAddMembersResponse_Status int32

const (
	BulkAddMembersResponse_OK BulkAddMembersResponse_Status = 0
	// invalid argument passed in request
	BulkAddMembersResponse_INVALID_ARGUMENT BulkAddMembersResponse_Status = 3
	// internal error while processing the request
	BulkAddMembersResponse_INTERNAL BulkAddMembersResponse_Status = 13
)

// Enum value maps for BulkAddMembersResponse_Status.
var (
	BulkAddMembersResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	BulkAddMembersResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x BulkAddMembersResponse_Status) Enum() *BulkAddMembersResponse_Status {
	p := new(BulkAddMembersResponse_Status)
	*p = x
	return p
}

func (x BulkAddMembersResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BulkAddMembersResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_whitelist_service_proto_enumTypes[1].Descriptor()
}

func (BulkAddMembersResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_whitelist_service_proto_enumTypes[1]
}

func (x BulkAddMembersResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BulkAddMembersResponse_Status.Descriptor instead.
func (BulkAddMembersResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_whitelist_service_proto_rawDescGZIP(), []int{3, 0}
}

type BulkRemoveMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MemberItems []*MemberItem `protobuf:"bytes,1,rep,name=member_items,json=memberItems,proto3" json:"member_items,omitempty"`
	// should be email id of person who wants to delete whitelist members
	DeletedBy string `protobuf:"bytes,2,opt,name=deleted_by,json=deletedBy,proto3" json:"deleted_by,omitempty"`
	// reason of deleting whitelist members
	Reason string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *BulkRemoveMembersRequest) Reset() {
	*x = BulkRemoveMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_whitelist_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkRemoveMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkRemoveMembersRequest) ProtoMessage() {}

func (x *BulkRemoveMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_whitelist_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkRemoveMembersRequest.ProtoReflect.Descriptor instead.
func (*BulkRemoveMembersRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_whitelist_service_proto_rawDescGZIP(), []int{0}
}

func (x *BulkRemoveMembersRequest) GetMemberItems() []*MemberItem {
	if x != nil {
		return x.MemberItems
	}
	return nil
}

func (x *BulkRemoveMembersRequest) GetDeletedBy() string {
	if x != nil {
		return x.DeletedBy
	}
	return ""
}

func (x *BulkRemoveMembersRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type BulkRemoveMembersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of failed cases with reason
	Failures []*MembersFailure `protobuf:"bytes,2,rep,name=failures,proto3" json:"failures,omitempty"`
}

func (x *BulkRemoveMembersResponse) Reset() {
	*x = BulkRemoveMembersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_whitelist_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkRemoveMembersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkRemoveMembersResponse) ProtoMessage() {}

func (x *BulkRemoveMembersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_whitelist_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkRemoveMembersResponse.ProtoReflect.Descriptor instead.
func (*BulkRemoveMembersResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_whitelist_service_proto_rawDescGZIP(), []int{1}
}

func (x *BulkRemoveMembersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *BulkRemoveMembersResponse) GetFailures() []*MembersFailure {
	if x != nil {
		return x.Failures
	}
	return nil
}

type BulkAddMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MemberItem []*MemberItem `protobuf:"bytes,1,rep,name=member_item,json=memberItem,proto3" json:"member_item,omitempty"`
	// should be email id of person who wants to add whitelist members
	AddedBy string `protobuf:"bytes,2,opt,name=added_by,json=addedBy,proto3" json:"added_by,omitempty"`
}

func (x *BulkAddMembersRequest) Reset() {
	*x = BulkAddMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_whitelist_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkAddMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkAddMembersRequest) ProtoMessage() {}

func (x *BulkAddMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_whitelist_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkAddMembersRequest.ProtoReflect.Descriptor instead.
func (*BulkAddMembersRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_whitelist_service_proto_rawDescGZIP(), []int{2}
}

func (x *BulkAddMembersRequest) GetMemberItem() []*MemberItem {
	if x != nil {
		return x.MemberItem
	}
	return nil
}

func (x *BulkAddMembersRequest) GetAddedBy() string {
	if x != nil {
		return x.AddedBy
	}
	return ""
}

type BulkAddMembersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of failed cases with reason
	Failures []*MembersFailure `protobuf:"bytes,2,rep,name=failures,proto3" json:"failures,omitempty"`
}

func (x *BulkAddMembersResponse) Reset() {
	*x = BulkAddMembersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_whitelist_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkAddMembersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkAddMembersResponse) ProtoMessage() {}

func (x *BulkAddMembersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_whitelist_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkAddMembersResponse.ProtoReflect.Descriptor instead.
func (*BulkAddMembersResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_whitelist_service_proto_rawDescGZIP(), []int{3}
}

func (x *BulkAddMembersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *BulkAddMembersResponse) GetFailures() []*MembersFailure {
	if x != nil {
		return x.Failures
	}
	return nil
}

type MembersFailure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// failed whitelist member entry
	MemberItem *MemberItem `protobuf:"bytes,1,opt,name=member_item,json=memberItem,proto3" json:"member_item,omitempty"`
	// description for error
	ErrorString string `protobuf:"bytes,2,opt,name=error_string,json=errorString,proto3" json:"error_string,omitempty"`
}

func (x *MembersFailure) Reset() {
	*x = MembersFailure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_whitelist_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembersFailure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembersFailure) ProtoMessage() {}

func (x *MembersFailure) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_whitelist_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembersFailure.ProtoReflect.Descriptor instead.
func (*MembersFailure) Descriptor() ([]byte, []int) {
	return file_api_risk_whitelist_service_proto_rawDescGZIP(), []int{4}
}

func (x *MembersFailure) GetMemberItem() *MemberItem {
	if x != nil {
		return x.MemberItem
	}
	return nil
}

func (x *MembersFailure) GetErrorString() string {
	if x != nil {
		return x.ErrorString
	}
	return ""
}

var File_api_risk_whitelist_service_proto protoreflect.FileDescriptor

var file_api_risk_whitelist_service_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x77, 0x68, 0x69, 0x74, 0x65,
	0x6c, 0x69, 0x73, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69,
	0x73, 0x74, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x77, 0x68, 0x69,
	0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa2, 0x01, 0x0a, 0x18, 0x42, 0x75, 0x6c, 0x6b, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3d, 0x0a, 0x0c, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x12, 0x26, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1f, 0x0a, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xb2, 0x01, 0x0a, 0x19, 0x42,
	0x75, 0x6c, 0x6b, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a,
	0x08, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74,
	0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52,
	0x08, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22,
	0x6f, 0x0a, 0x15, 0x42, 0x75, 0x6c, 0x6b, 0x41, 0x64, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x65, 0x64, 0x42, 0x79,
	0x22, 0xaf, 0x01, 0x0a, 0x16, 0x42, 0x75, 0x6c, 0x6b, 0x41, 0x64, 0x64, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x3a, 0x0a, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c,
	0x69, 0x73, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x52, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x22, 0x34, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14,
	0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c,
	0x10, 0x0d, 0x22, 0x70, 0x0a, 0x0e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x32, 0xd6, 0x01, 0x0a, 0x09, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x5f, 0x0a, 0x0e, 0x42, 0x75, 0x6c, 0x6b, 0x41, 0x64, 0x64, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x12, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x77, 0x68, 0x69, 0x74,
	0x65, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x41, 0x64, 0x64, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x42, 0x75, 0x6c,
	0x6b, 0x41, 0x64, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x11, 0x42, 0x75, 0x6c, 0x6b, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x28, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c,
	0x69, 0x73, 0x74, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x56, 0x0a,
	0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x77, 0x68, 0x69, 0x74,
	0x65, 0x6c, 0x69, 0x73, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_whitelist_service_proto_rawDescOnce sync.Once
	file_api_risk_whitelist_service_proto_rawDescData = file_api_risk_whitelist_service_proto_rawDesc
)

func file_api_risk_whitelist_service_proto_rawDescGZIP() []byte {
	file_api_risk_whitelist_service_proto_rawDescOnce.Do(func() {
		file_api_risk_whitelist_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_whitelist_service_proto_rawDescData)
	})
	return file_api_risk_whitelist_service_proto_rawDescData
}

var file_api_risk_whitelist_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_risk_whitelist_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_risk_whitelist_service_proto_goTypes = []interface{}{
	(BulkRemoveMembersResponse_Status)(0), // 0: risk.whitelist.BulkRemoveMembersResponse.Status
	(BulkAddMembersResponse_Status)(0),    // 1: risk.whitelist.BulkAddMembersResponse.Status
	(*BulkRemoveMembersRequest)(nil),      // 2: risk.whitelist.BulkRemoveMembersRequest
	(*BulkRemoveMembersResponse)(nil),     // 3: risk.whitelist.BulkRemoveMembersResponse
	(*BulkAddMembersRequest)(nil),         // 4: risk.whitelist.BulkAddMembersRequest
	(*BulkAddMembersResponse)(nil),        // 5: risk.whitelist.BulkAddMembersResponse
	(*MembersFailure)(nil),                // 6: risk.whitelist.MembersFailure
	(*MemberItem)(nil),                    // 7: risk.whitelist.MemberItem
	(*rpc.Status)(nil),                    // 8: rpc.Status
}
var file_api_risk_whitelist_service_proto_depIdxs = []int32{
	7, // 0: risk.whitelist.BulkRemoveMembersRequest.member_items:type_name -> risk.whitelist.MemberItem
	8, // 1: risk.whitelist.BulkRemoveMembersResponse.status:type_name -> rpc.Status
	6, // 2: risk.whitelist.BulkRemoveMembersResponse.failures:type_name -> risk.whitelist.MembersFailure
	7, // 3: risk.whitelist.BulkAddMembersRequest.member_item:type_name -> risk.whitelist.MemberItem
	8, // 4: risk.whitelist.BulkAddMembersResponse.status:type_name -> rpc.Status
	6, // 5: risk.whitelist.BulkAddMembersResponse.failures:type_name -> risk.whitelist.MembersFailure
	7, // 6: risk.whitelist.MembersFailure.member_item:type_name -> risk.whitelist.MemberItem
	4, // 7: risk.whitelist.WhiteList.BulkAddMembers:input_type -> risk.whitelist.BulkAddMembersRequest
	2, // 8: risk.whitelist.WhiteList.BulkRemoveMembers:input_type -> risk.whitelist.BulkRemoveMembersRequest
	5, // 9: risk.whitelist.WhiteList.BulkAddMembers:output_type -> risk.whitelist.BulkAddMembersResponse
	3, // 10: risk.whitelist.WhiteList.BulkRemoveMembers:output_type -> risk.whitelist.BulkRemoveMembersResponse
	9, // [9:11] is the sub-list for method output_type
	7, // [7:9] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_risk_whitelist_service_proto_init() }
func file_api_risk_whitelist_service_proto_init() {
	if File_api_risk_whitelist_service_proto != nil {
		return
	}
	file_api_risk_whitelist_whitelist_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_whitelist_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkRemoveMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_whitelist_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkRemoveMembersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_whitelist_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkAddMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_whitelist_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkAddMembersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_whitelist_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembersFailure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_whitelist_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_risk_whitelist_service_proto_goTypes,
		DependencyIndexes: file_api_risk_whitelist_service_proto_depIdxs,
		EnumInfos:         file_api_risk_whitelist_service_proto_enumTypes,
		MessageInfos:      file_api_risk_whitelist_service_proto_msgTypes,
	}.Build()
	File_api_risk_whitelist_service_proto = out.File
	file_api_risk_whitelist_service_proto_rawDesc = nil
	file_api_risk_whitelist_service_proto_goTypes = nil
	file_api_risk_whitelist_service_proto_depIdxs = nil
}
