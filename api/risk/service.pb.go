// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/service.proto

package risk

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	dynamic_elements "github.com/epifi/gamma/api/dynamic_elements"
	enums "github.com/epifi/gamma/api/risk/enums"
	screener "github.com/epifi/gamma/api/risk/screener"
	tagging "github.com/epifi/gamma/api/risk/tagging"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetBureauIdRiskDetailsResponse_Status int32

const (
	GetBureauIdRiskDetailsResponse_OK GetBureauIdRiskDetailsResponse_Status = 0
	// invalid argument passed
	GetBureauIdRiskDetailsResponse_INVALID_ARGUMENT GetBureauIdRiskDetailsResponse_Status = 3
	// risk bureau info isn't found with id.
	GetBureauIdRiskDetailsResponse_NOT_FOUND GetBureauIdRiskDetailsResponse_Status = 5
	// ISE response due to some internal error in the rpc
	GetBureauIdRiskDetailsResponse_INTERNAL GetBureauIdRiskDetailsResponse_Status = 13
)

// Enum value maps for GetBureauIdRiskDetailsResponse_Status.
var (
	GetBureauIdRiskDetailsResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetBureauIdRiskDetailsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"NOT_FOUND":        5,
		"INTERNAL":         13,
	}
)

func (x GetBureauIdRiskDetailsResponse_Status) Enum() *GetBureauIdRiskDetailsResponse_Status {
	p := new(GetBureauIdRiskDetailsResponse_Status)
	*p = x
	return p
}

func (x GetBureauIdRiskDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetBureauIdRiskDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[0].Descriptor()
}

func (GetBureauIdRiskDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[0]
}

func (x GetBureauIdRiskDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetBureauIdRiskDetailsResponse_Status.Descriptor instead.
func (GetBureauIdRiskDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{3, 0}
}

type BulkInsertBureauIdRiskDetailsResponse_Status int32

const (
	BulkInsertBureauIdRiskDetailsResponse_OK BulkInsertBureauIdRiskDetailsResponse_Status = 0
	// invalid argument passed
	BulkInsertBureauIdRiskDetailsResponse_INVALID_ARGUMENT BulkInsertBureauIdRiskDetailsResponse_Status = 3
	// ISE response due to some internal error in the rpc
	BulkInsertBureauIdRiskDetailsResponse_INTERNAL BulkInsertBureauIdRiskDetailsResponse_Status = 13
)

// Enum value maps for BulkInsertBureauIdRiskDetailsResponse_Status.
var (
	BulkInsertBureauIdRiskDetailsResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	BulkInsertBureauIdRiskDetailsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x BulkInsertBureauIdRiskDetailsResponse_Status) Enum() *BulkInsertBureauIdRiskDetailsResponse_Status {
	p := new(BulkInsertBureauIdRiskDetailsResponse_Status)
	*p = x
	return p
}

func (x BulkInsertBureauIdRiskDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BulkInsertBureauIdRiskDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[1].Descriptor()
}

func (BulkInsertBureauIdRiskDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[1]
}

func (x BulkInsertBureauIdRiskDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BulkInsertBureauIdRiskDetailsResponse_Status.Descriptor instead.
func (BulkInsertBureauIdRiskDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{5, 0}
}

type PassRiskScreenerAttemptResponse_Status int32

const (
	PassRiskScreenerAttemptResponse_OK PassRiskScreenerAttemptResponse_Status = 0
	// invalid argument passed
	PassRiskScreenerAttemptResponse_INVALID_ARGUMENT PassRiskScreenerAttemptResponse_Status = 3
	// ISE response due to some internal error in the rpc
	PassRiskScreenerAttemptResponse_INTERNAL PassRiskScreenerAttemptResponse_Status = 13
)

// Enum value maps for PassRiskScreenerAttemptResponse_Status.
var (
	PassRiskScreenerAttemptResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	PassRiskScreenerAttemptResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x PassRiskScreenerAttemptResponse_Status) Enum() *PassRiskScreenerAttemptResponse_Status {
	p := new(PassRiskScreenerAttemptResponse_Status)
	*p = x
	return p
}

func (x PassRiskScreenerAttemptResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PassRiskScreenerAttemptResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[2].Descriptor()
}

func (PassRiskScreenerAttemptResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[2]
}

func (x PassRiskScreenerAttemptResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PassRiskScreenerAttemptResponse_Status.Descriptor instead.
func (PassRiskScreenerAttemptResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{7, 0}
}

type UpsertDisputeResponse_Status int32

const (
	UpsertDisputeResponse_OK UpsertDisputeResponse_Status = 0
	// Invalid argument passed in the request
	UpsertDisputeResponse_INVALID_ARGUMENT UpsertDisputeResponse_Status = 3
	// ISE resposne due to some internal error in the rpc
	UpsertDisputeResponse_INTERNAL UpsertDisputeResponse_Status = 13
)

// Enum value maps for UpsertDisputeResponse_Status.
var (
	UpsertDisputeResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	UpsertDisputeResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x UpsertDisputeResponse_Status) Enum() *UpsertDisputeResponse_Status {
	p := new(UpsertDisputeResponse_Status)
	*p = x
	return p
}

func (x UpsertDisputeResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpsertDisputeResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[3].Descriptor()
}

func (UpsertDisputeResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[3]
}

func (x UpsertDisputeResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpsertDisputeResponse_Status.Descriptor instead.
func (UpsertDisputeResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{9, 0}
}

type AddLEAComplaintSourceResponse_Status int32

const (
	AddLEAComplaintSourceResponse_OK AddLEAComplaintSourceResponse_Status = 0
	// Invalid argument passed in the request
	AddLEAComplaintSourceResponse_INVALID_ARGUMENT AddLEAComplaintSourceResponse_Status = 3
	// ISE resposne due to some internal error in the rpc
	AddLEAComplaintSourceResponse_INTERNAL_SERVER_ERROR AddLEAComplaintSourceResponse_Status = 13
)

// Enum value maps for AddLEAComplaintSourceResponse_Status.
var (
	AddLEAComplaintSourceResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL_SERVER_ERROR",
	}
	AddLEAComplaintSourceResponse_Status_value = map[string]int32{
		"OK":                    0,
		"INVALID_ARGUMENT":      3,
		"INTERNAL_SERVER_ERROR": 13,
	}
)

func (x AddLEAComplaintSourceResponse_Status) Enum() *AddLEAComplaintSourceResponse_Status {
	p := new(AddLEAComplaintSourceResponse_Status)
	*p = x
	return p
}

func (x AddLEAComplaintSourceResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AddLEAComplaintSourceResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[4].Descriptor()
}

func (AddLEAComplaintSourceResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[4]
}

func (x AddLEAComplaintSourceResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AddLEAComplaintSourceResponse_Status.Descriptor instead.
func (AddLEAComplaintSourceResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{11, 0}
}

type UpdateLEAComplaintResponse_Status int32

const (
	UpdateLEAComplaintResponse_OK UpdateLEAComplaintResponse_Status = 0
	// Invalid argument passed in the request
	UpdateLEAComplaintResponse_INVALID_ARGUMENT UpdateLEAComplaintResponse_Status = 3
	// lea complaint isn't found with id.
	UpdateLEAComplaintResponse_NOT_FOUND UpdateLEAComplaintResponse_Status = 5
	// ISE resposne due to some internal error in the rpc
	UpdateLEAComplaintResponse_INTERNAL_SERVER_ERROR UpdateLEAComplaintResponse_Status = 13
)

// Enum value maps for UpdateLEAComplaintResponse_Status.
var (
	UpdateLEAComplaintResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "NOT_FOUND",
		13: "INTERNAL_SERVER_ERROR",
	}
	UpdateLEAComplaintResponse_Status_value = map[string]int32{
		"OK":                    0,
		"INVALID_ARGUMENT":      3,
		"NOT_FOUND":             5,
		"INTERNAL_SERVER_ERROR": 13,
	}
)

func (x UpdateLEAComplaintResponse_Status) Enum() *UpdateLEAComplaintResponse_Status {
	p := new(UpdateLEAComplaintResponse_Status)
	*p = x
	return p
}

func (x UpdateLEAComplaintResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateLEAComplaintResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[5].Descriptor()
}

func (UpdateLEAComplaintResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[5]
}

func (x UpdateLEAComplaintResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateLEAComplaintResponse_Status.Descriptor instead.
func (UpdateLEAComplaintResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{13, 0}
}

type ProcessLEAComplaintNarrationResponse_Status int32

const (
	ProcessLEAComplaintNarrationResponse_OK ProcessLEAComplaintNarrationResponse_Status = 0
	// Invalid argument passed in the request
	ProcessLEAComplaintNarrationResponse_INVALID_ARGUMENT ProcessLEAComplaintNarrationResponse_Status = 3
	// ISE resposne due to some internal error in the rpc
	ProcessLEAComplaintNarrationResponse_INTERNAL_SERVER_ERROR ProcessLEAComplaintNarrationResponse_Status = 13
)

// Enum value maps for ProcessLEAComplaintNarrationResponse_Status.
var (
	ProcessLEAComplaintNarrationResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL_SERVER_ERROR",
	}
	ProcessLEAComplaintNarrationResponse_Status_value = map[string]int32{
		"OK":                    0,
		"INVALID_ARGUMENT":      3,
		"INTERNAL_SERVER_ERROR": 13,
	}
)

func (x ProcessLEAComplaintNarrationResponse_Status) Enum() *ProcessLEAComplaintNarrationResponse_Status {
	p := new(ProcessLEAComplaintNarrationResponse_Status)
	*p = x
	return p
}

func (x ProcessLEAComplaintNarrationResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessLEAComplaintNarrationResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[6].Descriptor()
}

func (ProcessLEAComplaintNarrationResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[6]
}

func (x ProcessLEAComplaintNarrationResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessLEAComplaintNarrationResponse_Status.Descriptor instead.
func (ProcessLEAComplaintNarrationResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{15, 0}
}

type CreateLEAComplaintResponse_Status int32

const (
	CreateLEAComplaintResponse_OK CreateLEAComplaintResponse_Status = 0
	// Invalid argument passed in the request
	CreateLEAComplaintResponse_INVALID_ARGUMENT CreateLEAComplaintResponse_Status = 3
	// ISE resposne due to some internal error in the rpc
	CreateLEAComplaintResponse_INTERNAL_SERVER_ERROR CreateLEAComplaintResponse_Status = 13
)

// Enum value maps for CreateLEAComplaintResponse_Status.
var (
	CreateLEAComplaintResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL_SERVER_ERROR",
	}
	CreateLEAComplaintResponse_Status_value = map[string]int32{
		"OK":                    0,
		"INVALID_ARGUMENT":      3,
		"INTERNAL_SERVER_ERROR": 13,
	}
)

func (x CreateLEAComplaintResponse_Status) Enum() *CreateLEAComplaintResponse_Status {
	p := new(CreateLEAComplaintResponse_Status)
	*p = x
	return p
}

func (x CreateLEAComplaintResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateLEAComplaintResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[7].Descriptor()
}

func (CreateLEAComplaintResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[7]
}

func (x CreateLEAComplaintResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateLEAComplaintResponse_Status.Descriptor instead.
func (CreateLEAComplaintResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{17, 0}
}

type RiskParamResponse_Status int32

const (
	RiskParamResponse_SUCCESS                  RiskParamResponse_Status = 0
	RiskParamResponse_INTERNAL                 RiskParamResponse_Status = 13
	RiskParamResponse_LOCATION_TOKEN_NOT_FOUND RiskParamResponse_Status = 101
)

// Enum value maps for RiskParamResponse_Status.
var (
	RiskParamResponse_Status_name = map[int32]string{
		0:   "SUCCESS",
		13:  "INTERNAL",
		101: "LOCATION_TOKEN_NOT_FOUND",
	}
	RiskParamResponse_Status_value = map[string]int32{
		"SUCCESS":                  0,
		"INTERNAL":                 13,
		"LOCATION_TOKEN_NOT_FOUND": 101,
	}
)

func (x RiskParamResponse_Status) Enum() *RiskParamResponse_Status {
	p := new(RiskParamResponse_Status)
	*p = x
	return p
}

func (x RiskParamResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskParamResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[8].Descriptor()
}

func (RiskParamResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[8]
}

func (x RiskParamResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskParamResponse_Status.Descriptor instead.
func (RiskParamResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{20, 0}
}

type BulkOverrideBankActionStateResponse_Status int32

const (
	BulkOverrideBankActionStateResponse_OK BulkOverrideBankActionStateResponse_Status = 0
	// internal error while processing the request
	// none of the requests have processed
	BulkOverrideBankActionStateResponse_INTERNAL BulkOverrideBankActionStateResponse_Status = 13
)

// Enum value maps for BulkOverrideBankActionStateResponse_Status.
var (
	BulkOverrideBankActionStateResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	BulkOverrideBankActionStateResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x BulkOverrideBankActionStateResponse_Status) Enum() *BulkOverrideBankActionStateResponse_Status {
	p := new(BulkOverrideBankActionStateResponse_Status)
	*p = x
	return p
}

func (x BulkOverrideBankActionStateResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BulkOverrideBankActionStateResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[9].Descriptor()
}

func (BulkOverrideBankActionStateResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[9]
}

func (x BulkOverrideBankActionStateResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BulkOverrideBankActionStateResponse_Status.Descriptor instead.
func (BulkOverrideBankActionStateResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{29, 0}
}

type BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status int32

const (
	BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_OK BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status = 0
	// request parameters invalid
	BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_INVALID_ARGUMENT BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status = 3
	// Already exists
	BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_ALREADY_EXISTS BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status = 6
	// internal error while processing the request
	BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_INTERNAL BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status = 13
)

// Enum value maps for BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status.
var (
	BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		6:  "ALREADY_EXISTS",
		13: "INTERNAL",
	}
	BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"ALREADY_EXISTS":   6,
		"INTERNAL":         13,
	}
)

func (x BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status) Enum() *BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status {
	p := new(BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status)
	*p = x
	return p
}

func (x BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[10].Descriptor()
}

func (BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[10]
}

func (x BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status.Descriptor instead.
func (BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{29, 0, 0}
}

type ScreenUserResponse_Status int32

const (
	ScreenUserResponse_OK ScreenUserResponse_Status = 0
	// request validation failed
	ScreenUserResponse_INVALID_ARGUMENT ScreenUserResponse_Status = 3
	// internal error while processing the request
	ScreenUserResponse_INTERNAL ScreenUserResponse_Status = 13
)

// Enum value maps for ScreenUserResponse_Status.
var (
	ScreenUserResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	ScreenUserResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x ScreenUserResponse_Status) Enum() *ScreenUserResponse_Status {
	p := new(ScreenUserResponse_Status)
	*p = x
	return p
}

func (x ScreenUserResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScreenUserResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[11].Descriptor()
}

func (ScreenUserResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[11]
}

func (x ScreenUserResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScreenUserResponse_Status.Descriptor instead.
func (ScreenUserResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{31, 0}
}

type FetchLEAComplaintsResponse_Status int32

const (
	FetchLEAComplaintsResponse_OK FetchLEAComplaintsResponse_Status = 0
	// request validation failed
	FetchLEAComplaintsResponse_INVALID_ARGUMENT FetchLEAComplaintsResponse_Status = 3
	// no record found for given identifier
	FetchLEAComplaintsResponse_RECORD_NOT_FOUND FetchLEAComplaintsResponse_Status = 5
	// internal error while processing the request
	FetchLEAComplaintsResponse_INTERNAL FetchLEAComplaintsResponse_Status = 13
)

// Enum value maps for FetchLEAComplaintsResponse_Status.
var (
	FetchLEAComplaintsResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	FetchLEAComplaintsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x FetchLEAComplaintsResponse_Status) Enum() *FetchLEAComplaintsResponse_Status {
	p := new(FetchLEAComplaintsResponse_Status)
	*p = x
	return p
}

func (x FetchLEAComplaintsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FetchLEAComplaintsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[12].Descriptor()
}

func (FetchLEAComplaintsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[12]
}

func (x FetchLEAComplaintsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FetchLEAComplaintsResponse_Status.Descriptor instead.
func (FetchLEAComplaintsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{33, 0}
}

type GetScreenerCheckResultsResponse_Status int32

const (
	GetScreenerCheckResultsResponse_OK GetScreenerCheckResultsResponse_Status = 0
	// request validation failed
	GetScreenerCheckResultsResponse_INVALID_ARGUMENT GetScreenerCheckResultsResponse_Status = 3
	// internal error while processing the request
	GetScreenerCheckResultsResponse_INTERNAL GetScreenerCheckResultsResponse_Status = 13
	// No checks result found
	GetScreenerCheckResultsResponse_RECORD_NOT_FOUND GetScreenerCheckResultsResponse_Status = 5
)

// Enum value maps for GetScreenerCheckResultsResponse_Status.
var (
	GetScreenerCheckResultsResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
		5:  "RECORD_NOT_FOUND",
	}
	GetScreenerCheckResultsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
		"RECORD_NOT_FOUND": 5,
	}
)

func (x GetScreenerCheckResultsResponse_Status) Enum() *GetScreenerCheckResultsResponse_Status {
	p := new(GetScreenerCheckResultsResponse_Status)
	*p = x
	return p
}

func (x GetScreenerCheckResultsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetScreenerCheckResultsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[13].Descriptor()
}

func (GetScreenerCheckResultsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[13]
}

func (x GetScreenerCheckResultsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetScreenerCheckResultsResponse_Status.Descriptor instead.
func (GetScreenerCheckResultsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{35, 0}
}

type GetScreenerCheckDetailsResponse_Status int32

const (
	GetScreenerCheckDetailsResponse_OK GetScreenerCheckDetailsResponse_Status = 0
	// request validation failed
	GetScreenerCheckDetailsResponse_INVALID_ARGUMENT GetScreenerCheckDetailsResponse_Status = 3
	// No checks result found
	GetScreenerCheckDetailsResponse_RECORD_NOT_FOUND GetScreenerCheckDetailsResponse_Status = 5
	// internal error while processing the request
	GetScreenerCheckDetailsResponse_INTERNAL GetScreenerCheckDetailsResponse_Status = 13
)

// Enum value maps for GetScreenerCheckDetailsResponse_Status.
var (
	GetScreenerCheckDetailsResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetScreenerCheckDetailsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetScreenerCheckDetailsResponse_Status) Enum() *GetScreenerCheckDetailsResponse_Status {
	p := new(GetScreenerCheckDetailsResponse_Status)
	*p = x
	return p
}

func (x GetScreenerCheckDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetScreenerCheckDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[14].Descriptor()
}

func (GetScreenerCheckDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[14]
}

func (x GetScreenerCheckDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetScreenerCheckDetailsResponse_Status.Descriptor instead.
func (GetScreenerCheckDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{37, 0}
}

type ScreenActorResponse_Status int32

const (
	ScreenActorResponse_OK ScreenActorResponse_Status = 0
	// request validation failed
	ScreenActorResponse_INVALID_ARGUMENT ScreenActorResponse_Status = 3
	// internal error while processing the request
	ScreenActorResponse_INTERNAL ScreenActorResponse_Status = 13
)

// Enum value maps for ScreenActorResponse_Status.
var (
	ScreenActorResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	ScreenActorResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x ScreenActorResponse_Status) Enum() *ScreenActorResponse_Status {
	p := new(ScreenActorResponse_Status)
	*p = x
	return p
}

func (x ScreenActorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScreenActorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[15].Descriptor()
}

func (ScreenActorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[15]
}

func (x ScreenActorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScreenActorResponse_Status.Descriptor instead.
func (ScreenActorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{39, 0}
}

type GetScreenerAttemptStatusResponse_Status int32

const (
	GetScreenerAttemptStatusResponse_OK GetScreenerAttemptStatusResponse_Status = 0
	// request validation failed
	GetScreenerAttemptStatusResponse_INVALID_ARGUMENT GetScreenerAttemptStatusResponse_Status = 3
	// internal error while processing the request
	GetScreenerAttemptStatusResponse_INTERNAL GetScreenerAttemptStatusResponse_Status = 13
	// no record found for given identifier in request
	GetScreenerAttemptStatusResponse_RECORD_NOT_FOUND GetScreenerAttemptStatusResponse_Status = 5
)

// Enum value maps for GetScreenerAttemptStatusResponse_Status.
var (
	GetScreenerAttemptStatusResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
		5:  "RECORD_NOT_FOUND",
	}
	GetScreenerAttemptStatusResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
		"RECORD_NOT_FOUND": 5,
	}
)

func (x GetScreenerAttemptStatusResponse_Status) Enum() *GetScreenerAttemptStatusResponse_Status {
	p := new(GetScreenerAttemptStatusResponse_Status)
	*p = x
	return p
}

func (x GetScreenerAttemptStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetScreenerAttemptStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[16].Descriptor()
}

func (GetScreenerAttemptStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[16]
}

func (x GetScreenerAttemptStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetScreenerAttemptStatusResponse_Status.Descriptor instead.
func (GetScreenerAttemptStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{41, 0}
}

type UpdateScreenerAttemptResponse_Status int32

const (
	UpdateScreenerAttemptResponse_OK UpdateScreenerAttemptResponse_Status = 0
	// request validation failed
	UpdateScreenerAttemptResponse_INVALID_ARGUMENT UpdateScreenerAttemptResponse_Status = 3
	// no record found for given identifier in request
	UpdateScreenerAttemptResponse_RECORD_NOT_FOUND UpdateScreenerAttemptResponse_Status = 5
	// internal error while processing the request
	UpdateScreenerAttemptResponse_INTERNAL UpdateScreenerAttemptResponse_Status = 13
)

// Enum value maps for UpdateScreenerAttemptResponse_Status.
var (
	UpdateScreenerAttemptResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	UpdateScreenerAttemptResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x UpdateScreenerAttemptResponse_Status) Enum() *UpdateScreenerAttemptResponse_Status {
	p := new(UpdateScreenerAttemptResponse_Status)
	*p = x
	return p
}

func (x UpdateScreenerAttemptResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateScreenerAttemptResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_service_proto_enumTypes[17].Descriptor()
}

func (UpdateScreenerAttemptResponse_Status) Type() protoreflect.EnumType {
	return &file_api_risk_service_proto_enumTypes[17]
}

func (x UpdateScreenerAttemptResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateScreenerAttemptResponse_Status.Descriptor instead.
func (UpdateScreenerAttemptResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{43, 0}
}

type GetTransactionTagsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnId string `protobuf:"bytes,1,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
}

func (x *GetTransactionTagsRequest) Reset() {
	*x = GetTransactionTagsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionTagsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionTagsRequest) ProtoMessage() {}

func (x *GetTransactionTagsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionTagsRequest.ProtoReflect.Descriptor instead.
func (*GetTransactionTagsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetTransactionTagsRequest) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

type GetTransactionTagsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Tags   []tagging.TransactionTag `protobuf:"varint,2,rep,packed,name=tags,proto3,enum=risk.tagging.TransactionTag" json:"tags,omitempty"`
}

func (x *GetTransactionTagsResponse) Reset() {
	*x = GetTransactionTagsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionTagsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionTagsResponse) ProtoMessage() {}

func (x *GetTransactionTagsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionTagsResponse.ProtoReflect.Descriptor instead.
func (*GetTransactionTagsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetTransactionTagsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTransactionTagsResponse) GetTags() []tagging.TransactionTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

type GetBureauIdRiskDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId   string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *GetBureauIdRiskDetailsRequest) Reset() {
	*x = GetBureauIdRiskDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBureauIdRiskDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBureauIdRiskDetailsRequest) ProtoMessage() {}

func (x *GetBureauIdRiskDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBureauIdRiskDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetBureauIdRiskDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetBureauIdRiskDetailsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetBureauIdRiskDetailsRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type GetBureauIdRiskDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status              *rpc.Status           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	BureauIdRiskDetails []*BureauIdRiskDetail `protobuf:"bytes,2,rep,name=bureau_id_risk_details,json=bureauIdRiskDetails,proto3" json:"bureau_id_risk_details,omitempty"`
}

func (x *GetBureauIdRiskDetailsResponse) Reset() {
	*x = GetBureauIdRiskDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBureauIdRiskDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBureauIdRiskDetailsResponse) ProtoMessage() {}

func (x *GetBureauIdRiskDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBureauIdRiskDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetBureauIdRiskDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetBureauIdRiskDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetBureauIdRiskDetailsResponse) GetBureauIdRiskDetails() []*BureauIdRiskDetail {
	if x != nil {
		return x.BureauIdRiskDetails
	}
	return nil
}

type BulkInsertBureauIdRiskDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BureauIdRiskDetails []*BureauIdRiskDetail `protobuf:"bytes,1,rep,name=bureau_id_risk_details,json=bureauIdRiskDetails,proto3" json:"bureau_id_risk_details,omitempty"`
}

func (x *BulkInsertBureauIdRiskDetailsRequest) Reset() {
	*x = BulkInsertBureauIdRiskDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkInsertBureauIdRiskDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkInsertBureauIdRiskDetailsRequest) ProtoMessage() {}

func (x *BulkInsertBureauIdRiskDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkInsertBureauIdRiskDetailsRequest.ProtoReflect.Descriptor instead.
func (*BulkInsertBureauIdRiskDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{4}
}

func (x *BulkInsertBureauIdRiskDetailsRequest) GetBureauIdRiskDetails() []*BureauIdRiskDetail {
	if x != nil {
		return x.BureauIdRiskDetails
	}
	return nil
}

type BulkInsertBureauIdRiskDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *BulkInsertBureauIdRiskDetailsResponse) Reset() {
	*x = BulkInsertBureauIdRiskDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkInsertBureauIdRiskDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkInsertBureauIdRiskDetailsResponse) ProtoMessage() {}

func (x *BulkInsertBureauIdRiskDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkInsertBureauIdRiskDetailsResponse.ProtoReflect.Descriptor instead.
func (*BulkInsertBureauIdRiskDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{5}
}

func (x *BulkInsertBureauIdRiskDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type PassRiskScreenerAttemptRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor_id and screener_criteria used to fetch the active screener attempt for actor
	ActorId          string                 `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ScreenerCriteria enums.ScreenerCriteria `protobuf:"varint,2,opt,name=screener_criteria,json=screenerCriteria,proto3,enum=enums.ScreenerCriteria" json:"screener_criteria,omitempty"`
	// email address of the analyst
	AnalystEmail string `protobuf:"bytes,3,opt,name=analyst_email,json=analystEmail,proto3" json:"analyst_email,omitempty"`
	// reason to pass screener attempt for actor
	ManualPassReason string `protobuf:"bytes,4,opt,name=manual_pass_reason,json=manualPassReason,proto3" json:"manual_pass_reason,omitempty"`
}

func (x *PassRiskScreenerAttemptRequest) Reset() {
	*x = PassRiskScreenerAttemptRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassRiskScreenerAttemptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassRiskScreenerAttemptRequest) ProtoMessage() {}

func (x *PassRiskScreenerAttemptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassRiskScreenerAttemptRequest.ProtoReflect.Descriptor instead.
func (*PassRiskScreenerAttemptRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{6}
}

func (x *PassRiskScreenerAttemptRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *PassRiskScreenerAttemptRequest) GetScreenerCriteria() enums.ScreenerCriteria {
	if x != nil {
		return x.ScreenerCriteria
	}
	return enums.ScreenerCriteria(0)
}

func (x *PassRiskScreenerAttemptRequest) GetAnalystEmail() string {
	if x != nil {
		return x.AnalystEmail
	}
	return ""
}

func (x *PassRiskScreenerAttemptRequest) GetManualPassReason() string {
	if x != nil {
		return x.ManualPassReason
	}
	return ""
}

type PassRiskScreenerAttemptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *PassRiskScreenerAttemptResponse) Reset() {
	*x = PassRiskScreenerAttemptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassRiskScreenerAttemptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassRiskScreenerAttemptResponse) ProtoMessage() {}

func (x *PassRiskScreenerAttemptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassRiskScreenerAttemptResponse.ProtoReflect.Descriptor instead.
func (*PassRiskScreenerAttemptResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{7}
}

func (x *PassRiskScreenerAttemptResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UpsertDisputeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dispute *Dispute `protobuf:"bytes,1,opt,name=dispute,proto3" json:"dispute,omitempty"`
}

func (x *UpsertDisputeRequest) Reset() {
	*x = UpsertDisputeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertDisputeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertDisputeRequest) ProtoMessage() {}

func (x *UpsertDisputeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertDisputeRequest.ProtoReflect.Descriptor instead.
func (*UpsertDisputeRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{8}
}

func (x *UpsertDisputeRequest) GetDispute() *Dispute {
	if x != nil {
		return x.Dispute
	}
	return nil
}

type UpsertDisputeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpsertDisputeResponse) Reset() {
	*x = UpsertDisputeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertDisputeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertDisputeResponse) ProtoMessage() {}

func (x *UpsertDisputeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertDisputeResponse.ProtoReflect.Descriptor instead.
func (*UpsertDisputeResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{9}
}

func (x *UpsertDisputeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type AddLEAComplaintSourceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source *LEAComplaintSource `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
}

func (x *AddLEAComplaintSourceRequest) Reset() {
	*x = AddLEAComplaintSourceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLEAComplaintSourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLEAComplaintSourceRequest) ProtoMessage() {}

func (x *AddLEAComplaintSourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLEAComplaintSourceRequest.ProtoReflect.Descriptor instead.
func (*AddLEAComplaintSourceRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{10}
}

func (x *AddLEAComplaintSourceRequest) GetSource() *LEAComplaintSource {
	if x != nil {
		return x.Source
	}
	return nil
}

type AddLEAComplaintSourceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *AddLEAComplaintSourceResponse) Reset() {
	*x = AddLEAComplaintSourceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLEAComplaintSourceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLEAComplaintSourceResponse) ProtoMessage() {}

func (x *AddLEAComplaintSourceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLEAComplaintSourceResponse.ProtoReflect.Descriptor instead.
func (*AddLEAComplaintSourceResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{11}
}

func (x *AddLEAComplaintSourceResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UpdateLEAComplaintRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Only complaint id and field to be updated is required.
	LeaComplaint *LEAComplaint `protobuf:"bytes,1,opt,name=lea_complaint,json=leaComplaint,proto3" json:"lea_complaint,omitempty"`
	// list of fields to be updated.
	FieldMasks []LEAComplaintFieldMask `protobuf:"varint,2,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=risk.LEAComplaintFieldMask" json:"field_masks,omitempty"`
}

func (x *UpdateLEAComplaintRequest) Reset() {
	*x = UpdateLEAComplaintRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLEAComplaintRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLEAComplaintRequest) ProtoMessage() {}

func (x *UpdateLEAComplaintRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLEAComplaintRequest.ProtoReflect.Descriptor instead.
func (*UpdateLEAComplaintRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateLEAComplaintRequest) GetLeaComplaint() *LEAComplaint {
	if x != nil {
		return x.LeaComplaint
	}
	return nil
}

func (x *UpdateLEAComplaintRequest) GetFieldMasks() []LEAComplaintFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

type UpdateLEAComplaintResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateLEAComplaintResponse) Reset() {
	*x = UpdateLEAComplaintResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLEAComplaintResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLEAComplaintResponse) ProtoMessage() {}

func (x *UpdateLEAComplaintResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLEAComplaintResponse.ProtoReflect.Descriptor instead.
func (*UpdateLEAComplaintResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateLEAComplaintResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ProcessLEAComplaintNarrationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeaComplaintNarrationDetails *LEAComplaintNarrationDetails `protobuf:"bytes,1,opt,name=lea_complaint_narration_details,json=leaComplaintNarrationDetails,proto3" json:"lea_complaint_narration_details,omitempty"`
	AnalystEmail                 string                        `protobuf:"bytes,4,opt,name=analyst_email,json=analystEmail,proto3" json:"analyst_email,omitempty"`
}

func (x *ProcessLEAComplaintNarrationRequest) Reset() {
	*x = ProcessLEAComplaintNarrationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessLEAComplaintNarrationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessLEAComplaintNarrationRequest) ProtoMessage() {}

func (x *ProcessLEAComplaintNarrationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessLEAComplaintNarrationRequest.ProtoReflect.Descriptor instead.
func (*ProcessLEAComplaintNarrationRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{14}
}

func (x *ProcessLEAComplaintNarrationRequest) GetLeaComplaintNarrationDetails() *LEAComplaintNarrationDetails {
	if x != nil {
		return x.LeaComplaintNarrationDetails
	}
	return nil
}

func (x *ProcessLEAComplaintNarrationRequest) GetAnalystEmail() string {
	if x != nil {
		return x.AnalystEmail
	}
	return ""
}

type ProcessLEAComplaintNarrationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ProcessLEAComplaintNarrationResponse) Reset() {
	*x = ProcessLEAComplaintNarrationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessLEAComplaintNarrationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessLEAComplaintNarrationResponse) ProtoMessage() {}

func (x *ProcessLEAComplaintNarrationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessLEAComplaintNarrationResponse.ProtoReflect.Descriptor instead.
func (*ProcessLEAComplaintNarrationResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{15}
}

func (x *ProcessLEAComplaintNarrationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateLEAComplaintRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeaComplaint *LEAComplaint `protobuf:"bytes,1,opt,name=lea_complaint,json=leaComplaint,proto3" json:"lea_complaint,omitempty"`
}

func (x *CreateLEAComplaintRequest) Reset() {
	*x = CreateLEAComplaintRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLEAComplaintRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLEAComplaintRequest) ProtoMessage() {}

func (x *CreateLEAComplaintRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLEAComplaintRequest.ProtoReflect.Descriptor instead.
func (*CreateLEAComplaintRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{16}
}

func (x *CreateLEAComplaintRequest) GetLeaComplaint() *LEAComplaint {
	if x != nil {
		return x.LeaComplaint
	}
	return nil
}

type CreateLEAComplaintResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateLEAComplaintResponse) Reset() {
	*x = CreateLEAComplaintResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLEAComplaintResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLEAComplaintResponse) ProtoMessage() {}

func (x *CreateLEAComplaintResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLEAComplaintResponse.ProtoReflect.Descriptor instead.
func (*CreateLEAComplaintResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{17}
}

func (x *CreateLEAComplaintResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetRiskDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId    string      `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RiskParams []RiskParam `protobuf:"varint,2,rep,packed,name=risk_params,json=riskParams,proto3,enum=risk.RiskParam" json:"risk_params,omitempty"`
	// remove these fields after screener V2 (user intel) service is in prod
	// deprecated in favour of metadata
	//
	// Deprecated: Marked as deprecated in api/risk/service.proto.
	IsDevicePremium common.BooleanEnum `protobuf:"varint,3,opt,name=is_device_premium,json=isDevicePremium,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_device_premium,omitempty"`
	// Deprecated: Marked as deprecated in api/risk/service.proto.
	IsCreditReportFound common.BooleanEnum `protobuf:"varint,4,opt,name=is_credit_report_found,json=isCreditReportFound,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_credit_report_found,omitempty"`
	// Deprecated: Marked as deprecated in api/risk/service.proto.
	HasCreditReportDownloadConsent common.BooleanEnum `protobuf:"varint,5,opt,name=has_credit_report_download_consent,json=hasCreditReportDownloadConsent,proto3,enum=api.typesv2.common.BooleanEnum" json:"has_credit_report_download_consent,omitempty"`
	// Deprecated: Marked as deprecated in api/risk/service.proto.
	GmailPanNameMatchScore float32 `protobuf:"fixed32,6,opt,name=gmail_pan_name_match_score,json=gmailPanNameMatchScore,proto3" json:"gmail_pan_name_match_score,omitempty"`
	// if the field is set to true, data is returned from the DB only.
	// if the data is not available; no data is returned.
	WantCachedData common.BooleanEnum `protobuf:"varint,7,opt,name=want_cached_data,json=wantCachedData,proto3,enum=api.typesv2.common.BooleanEnum" json:"want_cached_data,omitempty"`
	// extra data required for processing a risk param.
	// please check corresponding documentation for the risk params to know about required metadata
	Metadata map[string]string `protobuf:"bytes,8,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetRiskDataRequest) Reset() {
	*x = GetRiskDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRiskDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRiskDataRequest) ProtoMessage() {}

func (x *GetRiskDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRiskDataRequest.ProtoReflect.Descriptor instead.
func (*GetRiskDataRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetRiskDataRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRiskDataRequest) GetRiskParams() []RiskParam {
	if x != nil {
		return x.RiskParams
	}
	return nil
}

// Deprecated: Marked as deprecated in api/risk/service.proto.
func (x *GetRiskDataRequest) GetIsDevicePremium() common.BooleanEnum {
	if x != nil {
		return x.IsDevicePremium
	}
	return common.BooleanEnum(0)
}

// Deprecated: Marked as deprecated in api/risk/service.proto.
func (x *GetRiskDataRequest) GetIsCreditReportFound() common.BooleanEnum {
	if x != nil {
		return x.IsCreditReportFound
	}
	return common.BooleanEnum(0)
}

// Deprecated: Marked as deprecated in api/risk/service.proto.
func (x *GetRiskDataRequest) GetHasCreditReportDownloadConsent() common.BooleanEnum {
	if x != nil {
		return x.HasCreditReportDownloadConsent
	}
	return common.BooleanEnum(0)
}

// Deprecated: Marked as deprecated in api/risk/service.proto.
func (x *GetRiskDataRequest) GetGmailPanNameMatchScore() float32 {
	if x != nil {
		return x.GmailPanNameMatchScore
	}
	return 0
}

func (x *GetRiskDataRequest) GetWantCachedData() common.BooleanEnum {
	if x != nil {
		return x.WantCachedData
	}
	return common.BooleanEnum(0)
}

func (x *GetRiskDataRequest) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type GetRiskDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Deprecated in favour of risk_param_response_map
	//
	// Deprecated: Marked as deprecated in api/risk/service.proto.
	RiskData []*RiskData `protobuf:"bytes,2,rep,name=risk_data,json=riskData,proto3" json:"risk_data,omitempty"`
	// risk_param_response_map is used to store the response for a particular risk param
	// The key for this map is the risk param enum string.
	RiskParamResponseMap map[string]*RiskParamResponse `protobuf:"bytes,3,rep,name=risk_param_response_map,json=riskParamResponseMap,proto3" json:"risk_param_response_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetRiskDataResponse) Reset() {
	*x = GetRiskDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRiskDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRiskDataResponse) ProtoMessage() {}

func (x *GetRiskDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRiskDataResponse.ProtoReflect.Descriptor instead.
func (*GetRiskDataResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetRiskDataResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/risk/service.proto.
func (x *GetRiskDataResponse) GetRiskData() []*RiskData {
	if x != nil {
		return x.RiskData
	}
	return nil
}

func (x *GetRiskDataResponse) GetRiskParamResponseMap() map[string]*RiskParamResponse {
	if x != nil {
		return x.RiskParamResponseMap
	}
	return nil
}

type RiskParamResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status RiskParamResponse_Status `protobuf:"varint,1,opt,name=status,proto3,enum=risk.RiskParamResponse_Status" json:"status,omitempty"`
	// risk_data should be checked only in case of success response
	RiskData *RiskData `protobuf:"bytes,2,opt,name=risk_data,json=riskData,proto3" json:"risk_data,omitempty"`
}

func (x *RiskParamResponse) Reset() {
	*x = RiskParamResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskParamResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskParamResponse) ProtoMessage() {}

func (x *RiskParamResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskParamResponse.ProtoReflect.Descriptor instead.
func (*RiskParamResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{20}
}

func (x *RiskParamResponse) GetStatus() RiskParamResponse_Status {
	if x != nil {
		return x.Status
	}
	return RiskParamResponse_SUCCESS
}

func (x *RiskParamResponse) GetRiskData() *RiskData {
	if x != nil {
		return x.RiskData
	}
	return nil
}

type UpsertRiskDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId   string    `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RiskParam RiskParam `protobuf:"varint,3,opt,name=risk_param,json=riskParam,proto3,enum=risk.RiskParam" json:"risk_param,omitempty"`
	Payload   *Payload  `protobuf:"bytes,4,opt,name=payload,proto3" json:"payload,omitempty"`
	Result    Result    `protobuf:"varint,5,opt,name=result,proto3,enum=risk.Result" json:"result,omitempty"`
}

func (x *UpsertRiskDataRequest) Reset() {
	*x = UpsertRiskDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertRiskDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertRiskDataRequest) ProtoMessage() {}

func (x *UpsertRiskDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertRiskDataRequest.ProtoReflect.Descriptor instead.
func (*UpsertRiskDataRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{21}
}

func (x *UpsertRiskDataRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UpsertRiskDataRequest) GetRiskParam() RiskParam {
	if x != nil {
		return x.RiskParam
	}
	return RiskParam_RISK_PARAM_UNSPECIFIED
}

func (x *UpsertRiskDataRequest) GetPayload() *Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *UpsertRiskDataRequest) GetResult() Result {
	if x != nil {
		return x.Result
	}
	return Result_RESULT_UNSPECIFIED
}

type UpsertRiskDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpsertRiskDataResponse) Reset() {
	*x = UpsertRiskDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertRiskDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertRiskDataResponse) ProtoMessage() {}

func (x *UpsertRiskDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertRiskDataResponse.ProtoReflect.Descriptor instead.
func (*UpsertRiskDataResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{22}
}

func (x *UpsertRiskDataResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type AddSavingsAccountBankActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*AddSavingsAccountBankActionRequest_ActorId
	Identifier        isAddSavingsAccountBankActionRequest_Identifier `protobuf_oneof:"identifier"`
	Action            enums.Action                                    `protobuf:"varint,2,opt,name=action,proto3,enum=enums.Action" json:"action,omitempty"`
	RequestReason     *RequestReason                                  `protobuf:"bytes,3,opt,name=request_reason,json=requestReason,proto3" json:"request_reason,omitempty"`
	UserCommsTemplate []enums.CommsTemplate                           `protobuf:"varint,4,rep,packed,name=userCommsTemplate,proto3,enum=enums.CommsTemplate" json:"userCommsTemplate,omitempty"`
	IsRecon           common.BooleanEnum                              `protobuf:"varint,5,opt,name=is_recon,json=isRecon,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_recon,omitempty"`
	BankActionDate    *timestamppb.Timestamp                          `protobuf:"bytes,6,opt,name=bank_action_date,json=bankActionDate,proto3" json:"bank_action_date,omitempty"`
	// case against which action is being taken
	CaseId string `protobuf:"bytes,7,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	// email of analyst
	AnalystEmail string `protobuf:"bytes,8,opt,name=analyst_email,json=analystEmail,proto3" json:"analyst_email,omitempty"`
}

func (x *AddSavingsAccountBankActionRequest) Reset() {
	*x = AddSavingsAccountBankActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddSavingsAccountBankActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddSavingsAccountBankActionRequest) ProtoMessage() {}

func (x *AddSavingsAccountBankActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddSavingsAccountBankActionRequest.ProtoReflect.Descriptor instead.
func (*AddSavingsAccountBankActionRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{23}
}

func (m *AddSavingsAccountBankActionRequest) GetIdentifier() isAddSavingsAccountBankActionRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *AddSavingsAccountBankActionRequest) GetActorId() string {
	if x, ok := x.GetIdentifier().(*AddSavingsAccountBankActionRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *AddSavingsAccountBankActionRequest) GetAction() enums.Action {
	if x != nil {
		return x.Action
	}
	return enums.Action(0)
}

func (x *AddSavingsAccountBankActionRequest) GetRequestReason() *RequestReason {
	if x != nil {
		return x.RequestReason
	}
	return nil
}

func (x *AddSavingsAccountBankActionRequest) GetUserCommsTemplate() []enums.CommsTemplate {
	if x != nil {
		return x.UserCommsTemplate
	}
	return nil
}

func (x *AddSavingsAccountBankActionRequest) GetIsRecon() common.BooleanEnum {
	if x != nil {
		return x.IsRecon
	}
	return common.BooleanEnum(0)
}

func (x *AddSavingsAccountBankActionRequest) GetBankActionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.BankActionDate
	}
	return nil
}

func (x *AddSavingsAccountBankActionRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *AddSavingsAccountBankActionRequest) GetAnalystEmail() string {
	if x != nil {
		return x.AnalystEmail
	}
	return ""
}

type isAddSavingsAccountBankActionRequest_Identifier interface {
	isAddSavingsAccountBankActionRequest_Identifier()
}

type AddSavingsAccountBankActionRequest_ActorId struct {
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3,oneof"`
}

func (*AddSavingsAccountBankActionRequest_ActorId) isAddSavingsAccountBankActionRequest_Identifier() {
}

type AddSavingsAccountBankActionFailure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestIndex int32  `protobuf:"varint,1,opt,name=request_index,json=requestIndex,proto3" json:"request_index,omitempty"`
	ActorId      string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Reason       string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	ErrorString  string `protobuf:"bytes,4,opt,name=error_string,json=errorString,proto3" json:"error_string,omitempty"`
}

func (x *AddSavingsAccountBankActionFailure) Reset() {
	*x = AddSavingsAccountBankActionFailure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddSavingsAccountBankActionFailure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddSavingsAccountBankActionFailure) ProtoMessage() {}

func (x *AddSavingsAccountBankActionFailure) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddSavingsAccountBankActionFailure.ProtoReflect.Descriptor instead.
func (*AddSavingsAccountBankActionFailure) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{24}
}

func (x *AddSavingsAccountBankActionFailure) GetRequestIndex() int32 {
	if x != nil {
		return x.RequestIndex
	}
	return 0
}

func (x *AddSavingsAccountBankActionFailure) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *AddSavingsAccountBankActionFailure) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AddSavingsAccountBankActionFailure) GetErrorString() string {
	if x != nil {
		return x.ErrorString
	}
	return ""
}

type BulkAddSavingsAccountBankActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Requests   []*AddSavingsAccountBankActionRequest `protobuf:"bytes,1,rep,name=requests,proto3" json:"requests,omitempty"`
	Provenance enums.Provenance                      `protobuf:"varint,2,opt,name=provenance,proto3,enum=enums.Provenance" json:"provenance,omitempty"`
}

func (x *BulkAddSavingsAccountBankActionRequest) Reset() {
	*x = BulkAddSavingsAccountBankActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkAddSavingsAccountBankActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkAddSavingsAccountBankActionRequest) ProtoMessage() {}

func (x *BulkAddSavingsAccountBankActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkAddSavingsAccountBankActionRequest.ProtoReflect.Descriptor instead.
func (*BulkAddSavingsAccountBankActionRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{25}
}

func (x *BulkAddSavingsAccountBankActionRequest) GetRequests() []*AddSavingsAccountBankActionRequest {
	if x != nil {
		return x.Requests
	}
	return nil
}

func (x *BulkAddSavingsAccountBankActionRequest) GetProvenance() enums.Provenance {
	if x != nil {
		return x.Provenance
	}
	return enums.Provenance(0)
}

type BulkAddSavingsAccountBankActionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status                           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Failures []*AddSavingsAccountBankActionFailure `protobuf:"bytes,2,rep,name=failures,proto3" json:"failures,omitempty"`
}

func (x *BulkAddSavingsAccountBankActionResponse) Reset() {
	*x = BulkAddSavingsAccountBankActionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkAddSavingsAccountBankActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkAddSavingsAccountBankActionResponse) ProtoMessage() {}

func (x *BulkAddSavingsAccountBankActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkAddSavingsAccountBankActionResponse.ProtoReflect.Descriptor instead.
func (*BulkAddSavingsAccountBankActionResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{26}
}

func (x *BulkAddSavingsAccountBankActionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *BulkAddSavingsAccountBankActionResponse) GetFailures() []*AddSavingsAccountBankActionFailure {
	if x != nil {
		return x.Failures
	}
	return nil
}

type OverrideBankActionStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor_id as a unique identifier will suffice as an actor can have only one open state
	// ie. SENT_TO_BANK or MANUAL_INTERVENTION, we need to act on ay one of these states
	// any other cases will be errored out
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// only STATE_SUCCESS_MANUAL_OVERRIDE and STATE_REJECT_MANUAL_OVERRIDE allowed here as per design
	RequiredState enums.State `protobuf:"varint,2,opt,name=required_state,json=requiredState,proto3,enum=enums.State" json:"required_state,omitempty"`
	// reason for above action, will be appended on original reason
	OverrideReason string `protobuf:"bytes,3,opt,name=override_reason,json=overrideReason,proto3" json:"override_reason,omitempty"`
}

func (x *OverrideBankActionStateRequest) Reset() {
	*x = OverrideBankActionStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OverrideBankActionStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OverrideBankActionStateRequest) ProtoMessage() {}

func (x *OverrideBankActionStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OverrideBankActionStateRequest.ProtoReflect.Descriptor instead.
func (*OverrideBankActionStateRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{27}
}

func (x *OverrideBankActionStateRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *OverrideBankActionStateRequest) GetRequiredState() enums.State {
	if x != nil {
		return x.RequiredState
	}
	return enums.State(0)
}

func (x *OverrideBankActionStateRequest) GetOverrideReason() string {
	if x != nil {
		return x.OverrideReason
	}
	return ""
}

type BulkOverrideBankActionStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Requests       []*OverrideBankActionStateRequest `protobuf:"bytes,1,rep,name=requests,proto3" json:"requests,omitempty"`
	RequesterEmail string                            `protobuf:"bytes,2,opt,name=requester_email,json=requesterEmail,proto3" json:"requester_email,omitempty"`
}

func (x *BulkOverrideBankActionStateRequest) Reset() {
	*x = BulkOverrideBankActionStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkOverrideBankActionStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkOverrideBankActionStateRequest) ProtoMessage() {}

func (x *BulkOverrideBankActionStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkOverrideBankActionStateRequest.ProtoReflect.Descriptor instead.
func (*BulkOverrideBankActionStateRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{28}
}

func (x *BulkOverrideBankActionStateRequest) GetRequests() []*OverrideBankActionStateRequest {
	if x != nil {
		return x.Requests
	}
	return nil
}

func (x *BulkOverrideBankActionStateRequest) GetRequesterEmail() string {
	if x != nil {
		return x.RequesterEmail
	}
	return ""
}

type BulkOverrideBankActionStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status                                                           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Failures []*BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure `protobuf:"bytes,2,rep,name=failures,proto3" json:"failures,omitempty"`
}

func (x *BulkOverrideBankActionStateResponse) Reset() {
	*x = BulkOverrideBankActionStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkOverrideBankActionStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkOverrideBankActionStateResponse) ProtoMessage() {}

func (x *BulkOverrideBankActionStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkOverrideBankActionStateResponse.ProtoReflect.Descriptor instead.
func (*BulkOverrideBankActionStateResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{29}
}

func (x *BulkOverrideBankActionStateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *BulkOverrideBankActionStateResponse) GetFailures() []*BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure {
	if x != nil {
		return x.Failures
	}
	return nil
}

type ScreenUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Criteria for which risk screening is being performed
	// Set of checks performed and threshold for failure could be different based on the purpose screening is happening for
	// EX: checks(and threshold) required in fi-lite onboarding vs savings account onboarding will differ
	ScreenerCriteria enums.ScreenerCriteria `protobuf:"varint,2,opt,name=screener_criteria,json=screenerCriteria,proto3,enum=enums.ScreenerCriteria" json:"screener_criteria,omitempty"`
}

func (x *ScreenUserRequest) Reset() {
	*x = ScreenUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenUserRequest) ProtoMessage() {}

func (x *ScreenUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenUserRequest.ProtoReflect.Descriptor instead.
func (*ScreenUserRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{30}
}

func (x *ScreenUserRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ScreenUserRequest) GetScreenerCriteria() enums.ScreenerCriteria {
	if x != nil {
		return x.ScreenerCriteria
	}
	return enums.ScreenerCriteria(0)
}

type ScreenUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status          `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ScreenerAction enums.ScreenerAction `protobuf:"varint,2,opt,name=screener_action,json=screenerAction,proto3,enum=enums.ScreenerAction" json:"screener_action,omitempty"`
}

func (x *ScreenUserResponse) Reset() {
	*x = ScreenUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenUserResponse) ProtoMessage() {}

func (x *ScreenUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenUserResponse.ProtoReflect.Descriptor instead.
func (*ScreenUserResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{31}
}

func (x *ScreenUserResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ScreenUserResponse) GetScreenerAction() enums.ScreenerAction {
	if x != nil {
		return x.ScreenerAction
	}
	return enums.ScreenerAction(0)
}

type FetchLEAComplaintsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*FetchLEAComplaintsRequest_ActorId
	//	*FetchLEAComplaintsRequest_AccountId
	Identifier isFetchLEAComplaintsRequest_Identifier `protobuf_oneof:"identifier"`
	// max number of responses to be served
	// if left empty, everything would be served
	Limit uint32 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *FetchLEAComplaintsRequest) Reset() {
	*x = FetchLEAComplaintsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchLEAComplaintsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchLEAComplaintsRequest) ProtoMessage() {}

func (x *FetchLEAComplaintsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchLEAComplaintsRequest.ProtoReflect.Descriptor instead.
func (*FetchLEAComplaintsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{32}
}

func (m *FetchLEAComplaintsRequest) GetIdentifier() isFetchLEAComplaintsRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *FetchLEAComplaintsRequest) GetActorId() string {
	if x, ok := x.GetIdentifier().(*FetchLEAComplaintsRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *FetchLEAComplaintsRequest) GetAccountId() string {
	if x, ok := x.GetIdentifier().(*FetchLEAComplaintsRequest_AccountId); ok {
		return x.AccountId
	}
	return ""
}

func (x *FetchLEAComplaintsRequest) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type isFetchLEAComplaintsRequest_Identifier interface {
	isFetchLEAComplaintsRequest_Identifier()
}

type FetchLEAComplaintsRequest_ActorId struct {
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3,oneof"`
}

type FetchLEAComplaintsRequest_AccountId struct {
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3,oneof"`
}

func (*FetchLEAComplaintsRequest_ActorId) isFetchLEAComplaintsRequest_Identifier() {}

func (*FetchLEAComplaintsRequest_AccountId) isFetchLEAComplaintsRequest_Identifier() {}

type FetchLEAComplaintsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Deprecated: Marked as deprecated in api/risk/service.proto.
	LeaComplaints         []*LEAComplaint         `protobuf:"bytes,2,rep,name=lea_complaints,json=leaComplaints,proto3" json:"lea_complaints,omitempty"`
	ExtendedLeaComplaints []*ExtendedLEAComplaint `protobuf:"bytes,3,rep,name=extended_lea_complaints,json=extendedLeaComplaints,proto3" json:"extended_lea_complaints,omitempty"`
}

func (x *FetchLEAComplaintsResponse) Reset() {
	*x = FetchLEAComplaintsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchLEAComplaintsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchLEAComplaintsResponse) ProtoMessage() {}

func (x *FetchLEAComplaintsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchLEAComplaintsResponse.ProtoReflect.Descriptor instead.
func (*FetchLEAComplaintsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{33}
}

func (x *FetchLEAComplaintsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/risk/service.proto.
func (x *FetchLEAComplaintsResponse) GetLeaComplaints() []*LEAComplaint {
	if x != nil {
		return x.LeaComplaints
	}
	return nil
}

func (x *FetchLEAComplaintsResponse) GetExtendedLeaComplaints() []*ExtendedLEAComplaint {
	if x != nil {
		return x.ExtendedLeaComplaints
	}
	return nil
}

type GetScreenerCheckResultsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetScreenerCheckResultsRequest_ActorId
	Identifier isGetScreenerCheckResultsRequest_Identifier `protobuf_oneof:"identifier"`
	Filters    *GetScreenerCheckResultsRequest_Filters     `protobuf:"bytes,2,opt,name=filters,proto3" json:"filters,omitempty"`
}

func (x *GetScreenerCheckResultsRequest) Reset() {
	*x = GetScreenerCheckResultsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScreenerCheckResultsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScreenerCheckResultsRequest) ProtoMessage() {}

func (x *GetScreenerCheckResultsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScreenerCheckResultsRequest.ProtoReflect.Descriptor instead.
func (*GetScreenerCheckResultsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{34}
}

func (m *GetScreenerCheckResultsRequest) GetIdentifier() isGetScreenerCheckResultsRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetScreenerCheckResultsRequest) GetActorId() string {
	if x, ok := x.GetIdentifier().(*GetScreenerCheckResultsRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *GetScreenerCheckResultsRequest) GetFilters() *GetScreenerCheckResultsRequest_Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

type isGetScreenerCheckResultsRequest_Identifier interface {
	isGetScreenerCheckResultsRequest_Identifier()
}

type GetScreenerCheckResultsRequest_ActorId struct {
	// if actor id is passed will return latest screener attempt data for the given actor
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3,oneof"`
}

func (*GetScreenerCheckResultsRequest_ActorId) isGetScreenerCheckResultsRequest_Identifier() {}

type GetScreenerCheckResultsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           *rpc.Status                                    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RiskCheckResults []*GetScreenerCheckResultsResponse_CheckResult `protobuf:"bytes,2,rep,name=risk_check_results,json=riskCheckResults,proto3" json:"risk_check_results,omitempty"`
}

func (x *GetScreenerCheckResultsResponse) Reset() {
	*x = GetScreenerCheckResultsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScreenerCheckResultsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScreenerCheckResultsResponse) ProtoMessage() {}

func (x *GetScreenerCheckResultsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScreenerCheckResultsResponse.ProtoReflect.Descriptor instead.
func (*GetScreenerCheckResultsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetScreenerCheckResultsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetScreenerCheckResultsResponse) GetRiskCheckResults() []*GetScreenerCheckResultsResponse_CheckResult {
	if x != nil {
		return x.RiskCheckResults
	}
	return nil
}

type GetScreenerCheckDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Id of the risk data row which stores a check result
	ResultId string `protobuf:"bytes,1,opt,name=result_id,json=resultId,proto3" json:"result_id,omitempty"`
}

func (x *GetScreenerCheckDetailsRequest) Reset() {
	*x = GetScreenerCheckDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScreenerCheckDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScreenerCheckDetailsRequest) ProtoMessage() {}

func (x *GetScreenerCheckDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScreenerCheckDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetScreenerCheckDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetScreenerCheckDetailsRequest) GetResultId() string {
	if x != nil {
		return x.ResultId
	}
	return ""
}

type GetScreenerCheckDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Stored check result for the id passed in request
	CheckResult *RiskData `protobuf:"bytes,2,opt,name=check_result,json=checkResult,proto3" json:"check_result,omitempty"`
	// Additional details related to the checks for analysis
	// this can contain check specific information like certain model response fields
	// or raw details used for evaluating the given check etc.
	AdditionalCheckDetails *screener.AdditionalCheckDetails `protobuf:"bytes,3,opt,name=additional_check_details,json=additionalCheckDetails,proto3" json:"additional_check_details,omitempty"`
}

func (x *GetScreenerCheckDetailsResponse) Reset() {
	*x = GetScreenerCheckDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScreenerCheckDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScreenerCheckDetailsResponse) ProtoMessage() {}

func (x *GetScreenerCheckDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScreenerCheckDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetScreenerCheckDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetScreenerCheckDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetScreenerCheckDetailsResponse) GetCheckResult() *RiskData {
	if x != nil {
		return x.CheckResult
	}
	return nil
}

func (x *GetScreenerCheckDetailsResponse) GetAdditionalCheckDetails() *screener.AdditionalCheckDetails {
	if x != nil {
		return x.AdditionalCheckDetails
	}
	return nil
}

type ScreenActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Criteria for which risk screening is being performed
	// Set of checks performed and threshold for failure could be different based on the purpose screening is happening for
	// EX: checks(and threshold) required in fi-lite onboarding vs savings account onboarding will differ
	ScreenerCriteria enums.ScreenerCriteria `protobuf:"varint,2,opt,name=screener_criteria,json=screenerCriteria,proto3,enum=enums.ScreenerCriteria" json:"screener_criteria,omitempty"`
	// Required field, Screener service will dedupe the attempt based on criteria + client_request_id field
	ClientRequestId string `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
}

func (x *ScreenActorRequest) Reset() {
	*x = ScreenActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenActorRequest) ProtoMessage() {}

func (x *ScreenActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenActorRequest.ProtoReflect.Descriptor instead.
func (*ScreenActorRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{38}
}

func (x *ScreenActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ScreenActorRequest) GetScreenerCriteria() enums.ScreenerCriteria {
	if x != nil {
		return x.ScreenerCriteria
	}
	return enums.ScreenerCriteria(0)
}

func (x *ScreenActorRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

type ScreenActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Id for the screener attempt, can be used for polling status of screener attempt
	AttemptId      string                  `protobuf:"bytes,2,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
	ScreenerStatus screener.ScreenerStatus `protobuf:"varint,3,opt,name=screener_status,json=screenerStatus,proto3,enum=risk.screener.ScreenerStatus" json:"screener_status,omitempty"`
	Verdict        screener.Verdict        `protobuf:"varint,4,opt,name=verdict,proto3,enum=risk.screener.Verdict" json:"verdict,omitempty"`
	// PotentialRiskFlags indicates plausible risky flags observed during the screener checks
	PotentialRiskFlags []screener.PotentialRiskFlag `protobuf:"varint,5,rep,packed,name=potential_risk_flags,json=potentialRiskFlags,proto3,enum=risk.screener.PotentialRiskFlag" json:"potential_risk_flags,omitempty"`
}

func (x *ScreenActorResponse) Reset() {
	*x = ScreenActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenActorResponse) ProtoMessage() {}

func (x *ScreenActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenActorResponse.ProtoReflect.Descriptor instead.
func (*ScreenActorResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{39}
}

func (x *ScreenActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ScreenActorResponse) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *ScreenActorResponse) GetScreenerStatus() screener.ScreenerStatus {
	if x != nil {
		return x.ScreenerStatus
	}
	return screener.ScreenerStatus(0)
}

func (x *ScreenActorResponse) GetVerdict() screener.Verdict {
	if x != nil {
		return x.Verdict
	}
	return screener.Verdict(0)
}

func (x *ScreenActorResponse) GetPotentialRiskFlags() []screener.PotentialRiskFlag {
	if x != nil {
		return x.PotentialRiskFlags
	}
	return nil
}

type GetScreenerAttemptStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttemptIdentifier *screener.AttemptIdentifier `protobuf:"bytes,1,opt,name=attempt_identifier,json=attemptIdentifier,proto3" json:"attempt_identifier,omitempty"`
}

func (x *GetScreenerAttemptStatusRequest) Reset() {
	*x = GetScreenerAttemptStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScreenerAttemptStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScreenerAttemptStatusRequest) ProtoMessage() {}

func (x *GetScreenerAttemptStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScreenerAttemptStatusRequest.ProtoReflect.Descriptor instead.
func (*GetScreenerAttemptStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{40}
}

func (x *GetScreenerAttemptStatusRequest) GetAttemptIdentifier() *screener.AttemptIdentifier {
	if x != nil {
		return x.AttemptIdentifier
	}
	return nil
}

type GetScreenerAttemptStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status             `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ScreenerStatus screener.ScreenerStatus `protobuf:"varint,2,opt,name=screener_status,json=screenerStatus,proto3,enum=risk.screener.ScreenerStatus" json:"screener_status,omitempty"`
	Verdict        screener.Verdict        `protobuf:"varint,3,opt,name=verdict,proto3,enum=risk.screener.Verdict" json:"verdict,omitempty"`
	// PotentialRiskFlags indicates plausible risky flags observed during the screener checks
	PotentialRiskFlags []screener.PotentialRiskFlag `protobuf:"varint,4,rep,packed,name=potential_risk_flags,json=potentialRiskFlags,proto3,enum=risk.screener.PotentialRiskFlag" json:"potential_risk_flags,omitempty"`
}

func (x *GetScreenerAttemptStatusResponse) Reset() {
	*x = GetScreenerAttemptStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScreenerAttemptStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScreenerAttemptStatusResponse) ProtoMessage() {}

func (x *GetScreenerAttemptStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScreenerAttemptStatusResponse.ProtoReflect.Descriptor instead.
func (*GetScreenerAttemptStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{41}
}

func (x *GetScreenerAttemptStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetScreenerAttemptStatusResponse) GetScreenerStatus() screener.ScreenerStatus {
	if x != nil {
		return x.ScreenerStatus
	}
	return screener.ScreenerStatus(0)
}

func (x *GetScreenerAttemptStatusResponse) GetVerdict() screener.Verdict {
	if x != nil {
		return x.Verdict
	}
	return screener.Verdict(0)
}

func (x *GetScreenerAttemptStatusResponse) GetPotentialRiskFlags() []screener.PotentialRiskFlag {
	if x != nil {
		return x.PotentialRiskFlags
	}
	return nil
}

type UpdateScreenerAttemptRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Screener attempt details
	// Id field is mandatory
	ScreenerAttempt *screener.ScreenerAttempt `protobuf:"bytes,1,opt,name=screener_attempt,json=screenerAttempt,proto3" json:"screener_attempt,omitempty"`
	// Currently only status and verdict can be updated.
	FieldMasks []screener.ScreenerAttemptFieldMask `protobuf:"varint,2,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=risk.screener.ScreenerAttemptFieldMask" json:"field_masks,omitempty"`
}

func (x *UpdateScreenerAttemptRequest) Reset() {
	*x = UpdateScreenerAttemptRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateScreenerAttemptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateScreenerAttemptRequest) ProtoMessage() {}

func (x *UpdateScreenerAttemptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateScreenerAttemptRequest.ProtoReflect.Descriptor instead.
func (*UpdateScreenerAttemptRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{42}
}

func (x *UpdateScreenerAttemptRequest) GetScreenerAttempt() *screener.ScreenerAttempt {
	if x != nil {
		return x.ScreenerAttempt
	}
	return nil
}

func (x *UpdateScreenerAttemptRequest) GetFieldMasks() []screener.ScreenerAttemptFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

type UpdateScreenerAttemptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateScreenerAttemptResponse) Reset() {
	*x = UpdateScreenerAttemptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateScreenerAttemptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateScreenerAttemptResponse) ProtoMessage() {}

func (x *UpdateScreenerAttemptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateScreenerAttemptResponse.ProtoReflect.Descriptor instead.
func (*UpdateScreenerAttemptResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{43}
}

func (x *UpdateScreenerAttemptResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// status for current request
	Status *rpc.Status `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// description for error
	ErrorString string `protobuf:"bytes,3,opt,name=error_string,json=errorString,proto3" json:"error_string,omitempty"`
}

func (x *BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure) Reset() {
	*x = BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure) ProtoMessage() {}

func (x *BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure.ProtoReflect.Descriptor instead.
func (*BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{29, 0}
}

func (x *BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure) GetErrorString() string {
	if x != nil {
		return x.ErrorString
	}
	return ""
}

type GetScreenerCheckResultsRequest_Filters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter by result of the checks
	// if not passed will return checks with bot passed and failed result
	Result Result `protobuf:"varint,1,opt,name=result,proto3,enum=risk.Result" json:"result,omitempty"`
}

func (x *GetScreenerCheckResultsRequest_Filters) Reset() {
	*x = GetScreenerCheckResultsRequest_Filters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScreenerCheckResultsRequest_Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScreenerCheckResultsRequest_Filters) ProtoMessage() {}

func (x *GetScreenerCheckResultsRequest_Filters) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScreenerCheckResultsRequest_Filters.ProtoReflect.Descriptor instead.
func (*GetScreenerCheckResultsRequest_Filters) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{34, 0}
}

func (x *GetScreenerCheckResultsRequest_Filters) GetResult() Result {
	if x != nil {
		return x.Result
	}
	return Result_RESULT_UNSPECIFIED
}

type GetScreenerCheckResultsResponse_CheckResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data *RiskData `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	// indicates whether additional check related details are available and can be fetched using get additional check details rpc
	AreAdditionalDetailsAvailable bool `protobuf:"varint,2,opt,name=are_additional_details_available,json=areAdditionalDetailsAvailable,proto3" json:"are_additional_details_available,omitempty"`
}

func (x *GetScreenerCheckResultsResponse_CheckResult) Reset() {
	*x = GetScreenerCheckResultsResponse_CheckResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScreenerCheckResultsResponse_CheckResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScreenerCheckResultsResponse_CheckResult) ProtoMessage() {}

func (x *GetScreenerCheckResultsResponse_CheckResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScreenerCheckResultsResponse_CheckResult.ProtoReflect.Descriptor instead.
func (*GetScreenerCheckResultsResponse_CheckResult) Descriptor() ([]byte, []int) {
	return file_api_risk_service_proto_rawDescGZIP(), []int{35, 0}
}

func (x *GetScreenerCheckResultsResponse_CheckResult) GetData() *RiskData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetScreenerCheckResultsResponse_CheckResult) GetAreAdditionalDetailsAvailable() bool {
	if x != nil {
		return x.AreAdditionalDetailsAvailable
	}
	return false
}

var File_api_risk_service_proto protoreflect.FileDescriptor

var file_api_risk_service_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x72, 0x69, 0x73, 0x6b, 0x1a, 0x2b,
	0x61, 0x70, 0x69, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x62, 0x61, 0x6e, 0x6b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x2f, 0x62, 0x75, 0x72, 0x65, 0x61, 0x75, 0x5f, 0x69, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x23, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x74, 0x61, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2f,
	0x74, 0x78, 0x6e, 0x5f, 0x74, 0x61, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c,
	0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x32, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x15, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x22, 0x73, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x74, 0x61, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x22, 0x59, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x42, 0x75, 0x72, 0x65, 0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0xd9, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x42,
	0x75, 0x72, 0x65, 0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x4d, 0x0a, 0x16, 0x62, 0x75, 0x72, 0x65, 0x61, 0x75, 0x5f, 0x69, 0x64, 0x5f, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x42, 0x75, 0x72, 0x65, 0x61, 0x75, 0x49, 0x64, 0x52,
	0x69, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x13, 0x62, 0x75, 0x72, 0x65, 0x61,
	0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x43,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x0d, 0x22, 0x75, 0x0a, 0x24, 0x42, 0x75, 0x6c, 0x6b, 0x49, 0x6e, 0x73, 0x65, 0x72,
	0x74, 0x42, 0x75, 0x72, 0x65, 0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4d, 0x0a, 0x16, 0x62,
	0x75, 0x72, 0x65, 0x61, 0x75, 0x5f, 0x69, 0x64, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x42, 0x75, 0x72, 0x65, 0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x13, 0x62, 0x75, 0x72, 0x65, 0x61, 0x75, 0x49, 0x64, 0x52,
	0x69, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x82, 0x01, 0x0a, 0x25, 0x42,
	0x75, 0x6c, 0x6b, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x42, 0x75, 0x72, 0x65, 0x61, 0x75, 0x49,
	0x64, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22,
	0xf9, 0x01, 0x0a, 0x1e, 0x50, 0x61, 0x73, 0x73, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x11, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x65, 0x72, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x65, 0x72, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x20, 0x00, 0x52, 0x10, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x12, 0x2c, 0x0a, 0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x35, 0x0a, 0x12, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x70,
	0x61, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x10, 0x6d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x50, 0x61, 0x73, 0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x7c, 0x0a, 0x1f, 0x50,
	0x61, 0x73, 0x73, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x3f, 0x0a, 0x14, 0x55, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x27, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74,
	0x65, 0x52, 0x07, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x22, 0x72, 0x0a, 0x15, 0x55, 0x70,
	0x73, 0x65, 0x72, 0x74, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03,
	0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x50,
	0x0a, 0x1c, 0x41, 0x64, 0x64, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e,
	0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30,
	0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x22, 0x87, 0x01, 0x0a, 0x1d, 0x41, 0x64, 0x64, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x41, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12,
	0x19, 0x0a, 0x15, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56,
	0x45, 0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x0d, 0x22, 0x92, 0x01, 0x0a, 0x19, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x0d, 0x6c, 0x65, 0x61, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x52, 0x0c, 0x6c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e,
	0x74, 0x12, 0x3c, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4c, 0x45,
	0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d,
	0x61, 0x73, 0x6b, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73, 0x22,
	0x93, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x50, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4e,
	0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0x0d, 0x22, 0xc8, 0x01, 0x0a, 0x23, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x4e, 0x61, 0x72,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x73, 0x0a,
	0x1f, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x5f, 0x6e,
	0x61, 0x72, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4c, 0x45,
	0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x4e, 0x61, 0x72, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x1c, 0x6c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x74, 0x4e, 0x61, 0x72, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x2c, 0x0a, 0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x22, 0x8e, 0x01, 0x0a, 0x24, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x45, 0x41, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x4e, 0x61, 0x72, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x41,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0x0d, 0x22, 0x54, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x45, 0x41, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x37,
	0x0a, 0x0d, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4c, 0x45, 0x41,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x0c, 0x6c, 0x65, 0x61, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x22, 0x84, 0x01, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x41, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f,
	0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x0d, 0x22, 0x89,
	0x05, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x30, 0x0a, 0x0b, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x69, 0x73,
	0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x4f, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x70, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0f, 0x69, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x65, 0x6d,
	0x69, 0x75, 0x6d, 0x12, 0x58, 0x0a, 0x16, 0x69, 0x73, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e,
	0x45, 0x6e, 0x75, 0x6d, 0x42, 0x02, 0x18, 0x01, 0x52, 0x13, 0x69, 0x73, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x6f, 0x0a,
	0x22, 0x68, 0x61, 0x73, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42,
	0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x02, 0x18, 0x01, 0x52, 0x1e,
	0x68, 0x61, 0x73, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x3e,
	0x0a, 0x1a, 0x67, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x70, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x02, 0x42, 0x02, 0x18, 0x01, 0x52, 0x16, 0x67, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x49,
	0x0a, 0x10, 0x77, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0e, 0x77, 0x61, 0x6e, 0x74, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x42, 0x0a, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x0a,
	0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb9, 0x02, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2f, 0x0a, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08,
	0x72, 0x69, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x6a, 0x0a, 0x17, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x14,
	0x72, 0x69, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x4d, 0x61, 0x70, 0x1a, 0x60, 0x0a, 0x19, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbb, 0x01, 0x0a, 0x11, 0x52, 0x69, 0x73, 0x6b, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52,
	0x69, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x72, 0x69, 0x73, 0x6b, 0x44, 0x61, 0x74,
	0x61, 0x22, 0x41, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x52,
	0x69, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x0a, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09,
	0x72, 0x69, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x27, 0x0a, 0x07, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x12, 0x24, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x3d, 0x0a, 0x16, 0x55, 0x70, 0x73, 0x65,
	0x72, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xb6, 0x03, 0x0a, 0x22, 0x41, 0x64, 0x64, 0x53,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6e,
	0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x42,
	0x0a, 0x11, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52,
	0x11, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x12, 0x3a, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61,
	0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x07, 0x69, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x12, 0x44,
	0x0a, 0x10, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x22, 0x9f, 0x01, 0x0a, 0x22, 0x41, 0x64, 0x64, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x21, 0x0a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x22, 0xa1, 0x01, 0x0a, 0x26, 0x42, 0x75, 0x6c, 0x6b, 0x41, 0x64, 0x64, 0x53, 0x61,
	0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6e, 0x6b,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x44, 0x0a,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x64, 0x64, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x73, 0x12, 0x31, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x94, 0x01, 0x0a, 0x27, 0x42, 0x75, 0x6c, 0x6b, 0x41,
	0x64, 0x64, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x0a, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x41, 0x64, 0x64, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x52, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x22, 0xb7, 0x01,
	0x0a, 0x1e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x18, 0x07, 0x18, 0x08, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x0f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x8f, 0x01, 0x0a, 0x22, 0x42, 0x75, 0x6c, 0x6b,
	0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40,
	0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0xa0, 0x03, 0x0a, 0x23, 0x42, 0x75,
	0x6c, 0x6b, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x64, 0x0a, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x42, 0x75, 0x6c, 0x6b, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x42, 0x61, 0x6e, 0x6b,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x42, 0x61, 0x6e, 0x6b,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x52, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x1a, 0xcd, 0x01, 0x0a,
	0x1e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x22, 0x48, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x4c,
	0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x06, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x1e, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x87, 0x01, 0x0a,
	0x11, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x11, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x65, 0x72, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x65, 0x72, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x20, 0x00, 0x52, 0x10, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x22, 0xaf, 0x01, 0x0a, 0x12, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x7d, 0x0a, 0x19, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xa0, 0x02, 0x0a, 0x1a, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x0e, 0x6c,
	0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4c, 0x45, 0x41, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x6c, 0x65, 0x61,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x52, 0x0a, 0x17, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x6c, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x4c, 0x45, 0x41, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x15, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65,
	0x64, 0x4c, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x22, 0x4a,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xc4, 0x01, 0x0a, 0x1e, 0x47,
	0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x1a, 0x2f, 0x0a, 0x07, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x24, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x22, 0xef, 0x02, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65,
	0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5f, 0x0a, 0x12, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65,
	0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x10, 0x72, 0x69, 0x73, 0x6b, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x7a, 0x0a, 0x0b, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x22, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x52, 0x69, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x47,
	0x0a, 0x20, 0x61, 0x72, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1d, 0x61, 0x72, 0x65, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x14, 0x0a,
	0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x05, 0x22, 0x3d, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x49, 0x64, 0x22, 0xa6, 0x02, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x31, 0x0a, 0x0c, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5f,
	0x0a, 0x18, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72,
	0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x16, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22,
	0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47,
	0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52,
	0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xbd, 0x01, 0x0a, 0x12,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x11, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x65, 0x72, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x65, 0x72, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x20, 0x00, 0x52, 0x10, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x12, 0x33, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0xdd, 0x02, 0x0a, 0x13,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x0f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72,
	0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x0e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x30, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x16, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72,
	0x2e, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63,
	0x74, 0x12, 0x52, 0x0a, 0x14, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x72,
	0x69, 0x73, 0x6b, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x20, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2e,
	0x50, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x6c, 0x61,
	0x67, 0x52, 0x12, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x69, 0x73, 0x6b,
	0x46, 0x6c, 0x61, 0x67, 0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x72, 0x0a, 0x1f, 0x47,
	0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4f,
	0x0a, 0x12, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2e, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x11, 0x61, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22,
	0xe1, 0x02, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x0a, 0x0f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x65, 0x72, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x30, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x65, 0x72, 0x2e, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x07, 0x76, 0x65, 0x72, 0x64,
	0x69, 0x63, 0x74, 0x12, 0x52, 0x0a, 0x14, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c,
	0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65,
	0x72, 0x2e, 0x50, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x69, 0x73, 0x6b, 0x46,
	0x6c, 0x61, 0x67, 0x52, 0x12, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x69,
	0x73, 0x6b, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x22, 0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x14, 0x0a,
	0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x05, 0x22, 0xb3, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x10, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72,
	0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2e, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x0f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12,
	0x48, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73, 0x22, 0x90, 0x01, 0x0a, 0x1d, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52,
	0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f,
	0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x32, 0xf9, 0x10, 0x0a,
	0x04, 0x52, 0x69, 0x73, 0x6b, 0x12, 0x47, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x69, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x4b,
	0x0a, 0x0e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x1b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x52, 0x69,
	0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x1f, 0x42,
	0x75, 0x6c, 0x6b, 0x41, 0x64, 0x64, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x41, 0x64, 0x64, 0x53, 0x61, 0x76,
	0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x41, 0x64, 0x64, 0x53, 0x61, 0x76, 0x69, 0x6e,
	0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x1b, 0x42,
	0x75, 0x6c, 0x6b, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x28, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x42, 0x61,
	0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x42, 0x75, 0x6c, 0x6b,
	0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x57, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x61, 0x69, 0x6e, 0x74, 0x12, 0x1f, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x12, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x1f,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x45, 0x41, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x20, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x45, 0x41, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x44, 0x0a, 0x0a, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x17, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x42, 0x0a, 0x0b, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x19, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65,
	0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x14, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2d,
	0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e,
	0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a,
	0x16, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x2f, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x17, 0x47, 0x65,
	0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x75, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x45, 0x41,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x4e, 0x61, 0x72, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x4e, 0x61, 0x72,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x45, 0x41, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x4e, 0x61, 0x72, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x60, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x22, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x41, 0x64, 0x64, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e,
	0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x64, 0x64, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x61, 0x69, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x45, 0x41,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x12, 0x1f, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x45, 0x41, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x60, 0x0a, 0x15,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x22, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48,
	0x0a, 0x0d, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x12,
	0x1a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x69, 0x73,
	0x70, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x17, 0x50, 0x61, 0x73, 0x73,
	0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x12, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x50, 0x61, 0x73, 0x73, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x50, 0x61, 0x73, 0x73, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65,
	0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x78, 0x0a, 0x1d, 0x42, 0x75, 0x6c, 0x6b, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x42, 0x75,
	0x72, 0x65, 0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x2a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x49, 0x6e, 0x73,
	0x65, 0x72, 0x74, 0x42, 0x75, 0x72, 0x65, 0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x42,
	0x75, 0x72, 0x65, 0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x42, 0x75, 0x72, 0x65, 0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x23, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x42,
	0x75, 0x72, 0x65, 0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x72, 0x65, 0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x57, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x61, 0x67, 0x73, 0x12, 0x1f, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5a, 0x1f, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_service_proto_rawDescOnce sync.Once
	file_api_risk_service_proto_rawDescData = file_api_risk_service_proto_rawDesc
)

func file_api_risk_service_proto_rawDescGZIP() []byte {
	file_api_risk_service_proto_rawDescOnce.Do(func() {
		file_api_risk_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_service_proto_rawDescData)
	})
	return file_api_risk_service_proto_rawDescData
}

var file_api_risk_service_proto_enumTypes = make([]protoimpl.EnumInfo, 18)
var file_api_risk_service_proto_msgTypes = make([]protoimpl.MessageInfo, 49)
var file_api_risk_service_proto_goTypes = []interface{}{
	(GetBureauIdRiskDetailsResponse_Status)(0),                                     // 0: risk.GetBureauIdRiskDetailsResponse.Status
	(BulkInsertBureauIdRiskDetailsResponse_Status)(0),                              // 1: risk.BulkInsertBureauIdRiskDetailsResponse.Status
	(PassRiskScreenerAttemptResponse_Status)(0),                                    // 2: risk.PassRiskScreenerAttemptResponse.Status
	(UpsertDisputeResponse_Status)(0),                                              // 3: risk.UpsertDisputeResponse.Status
	(AddLEAComplaintSourceResponse_Status)(0),                                      // 4: risk.AddLEAComplaintSourceResponse.Status
	(UpdateLEAComplaintResponse_Status)(0),                                         // 5: risk.UpdateLEAComplaintResponse.Status
	(ProcessLEAComplaintNarrationResponse_Status)(0),                               // 6: risk.ProcessLEAComplaintNarrationResponse.Status
	(CreateLEAComplaintResponse_Status)(0),                                         // 7: risk.CreateLEAComplaintResponse.Status
	(RiskParamResponse_Status)(0),                                                  // 8: risk.RiskParamResponse.Status
	(BulkOverrideBankActionStateResponse_Status)(0),                                // 9: risk.BulkOverrideBankActionStateResponse.Status
	(BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure_Status)(0), // 10: risk.BulkOverrideBankActionStateResponse.OverrideBankActionStateFailure.Status
	(ScreenUserResponse_Status)(0),                                                 // 11: risk.ScreenUserResponse.Status
	(FetchLEAComplaintsResponse_Status)(0),                                         // 12: risk.FetchLEAComplaintsResponse.Status
	(GetScreenerCheckResultsResponse_Status)(0),                                    // 13: risk.GetScreenerCheckResultsResponse.Status
	(GetScreenerCheckDetailsResponse_Status)(0),                                    // 14: risk.GetScreenerCheckDetailsResponse.Status
	(ScreenActorResponse_Status)(0),                                                // 15: risk.ScreenActorResponse.Status
	(GetScreenerAttemptStatusResponse_Status)(0),                                   // 16: risk.GetScreenerAttemptStatusResponse.Status
	(UpdateScreenerAttemptResponse_Status)(0),                                      // 17: risk.UpdateScreenerAttemptResponse.Status
	(*GetTransactionTagsRequest)(nil),                                              // 18: risk.GetTransactionTagsRequest
	(*GetTransactionTagsResponse)(nil),                                             // 19: risk.GetTransactionTagsResponse
	(*GetBureauIdRiskDetailsRequest)(nil),                                          // 20: risk.GetBureauIdRiskDetailsRequest
	(*GetBureauIdRiskDetailsResponse)(nil),                                         // 21: risk.GetBureauIdRiskDetailsResponse
	(*BulkInsertBureauIdRiskDetailsRequest)(nil),                                   // 22: risk.BulkInsertBureauIdRiskDetailsRequest
	(*BulkInsertBureauIdRiskDetailsResponse)(nil),                                  // 23: risk.BulkInsertBureauIdRiskDetailsResponse
	(*PassRiskScreenerAttemptRequest)(nil),                                         // 24: risk.PassRiskScreenerAttemptRequest
	(*PassRiskScreenerAttemptResponse)(nil),                                        // 25: risk.PassRiskScreenerAttemptResponse
	(*UpsertDisputeRequest)(nil),                                                   // 26: risk.UpsertDisputeRequest
	(*UpsertDisputeResponse)(nil),                                                  // 27: risk.UpsertDisputeResponse
	(*AddLEAComplaintSourceRequest)(nil),                                           // 28: risk.AddLEAComplaintSourceRequest
	(*AddLEAComplaintSourceResponse)(nil),                                          // 29: risk.AddLEAComplaintSourceResponse
	(*UpdateLEAComplaintRequest)(nil),                                              // 30: risk.UpdateLEAComplaintRequest
	(*UpdateLEAComplaintResponse)(nil),                                             // 31: risk.UpdateLEAComplaintResponse
	(*ProcessLEAComplaintNarrationRequest)(nil),                                    // 32: risk.ProcessLEAComplaintNarrationRequest
	(*ProcessLEAComplaintNarrationResponse)(nil),                                   // 33: risk.ProcessLEAComplaintNarrationResponse
	(*CreateLEAComplaintRequest)(nil),                                              // 34: risk.CreateLEAComplaintRequest
	(*CreateLEAComplaintResponse)(nil),                                             // 35: risk.CreateLEAComplaintResponse
	(*GetRiskDataRequest)(nil),                                                     // 36: risk.GetRiskDataRequest
	(*GetRiskDataResponse)(nil),                                                    // 37: risk.GetRiskDataResponse
	(*RiskParamResponse)(nil),                                                      // 38: risk.RiskParamResponse
	(*UpsertRiskDataRequest)(nil),                                                  // 39: risk.UpsertRiskDataRequest
	(*UpsertRiskDataResponse)(nil),                                                 // 40: risk.UpsertRiskDataResponse
	(*AddSavingsAccountBankActionRequest)(nil),                                     // 41: risk.AddSavingsAccountBankActionRequest
	(*AddSavingsAccountBankActionFailure)(nil),                                     // 42: risk.AddSavingsAccountBankActionFailure
	(*BulkAddSavingsAccountBankActionRequest)(nil),                                 // 43: risk.BulkAddSavingsAccountBankActionRequest
	(*BulkAddSavingsAccountBankActionResponse)(nil),                                // 44: risk.BulkAddSavingsAccountBankActionResponse
	(*OverrideBankActionStateRequest)(nil),                                         // 45: risk.OverrideBankActionStateRequest
	(*BulkOverrideBankActionStateRequest)(nil),                                     // 46: risk.BulkOverrideBankActionStateRequest
	(*BulkOverrideBankActionStateResponse)(nil),                                    // 47: risk.BulkOverrideBankActionStateResponse
	(*ScreenUserRequest)(nil),                                                      // 48: risk.ScreenUserRequest
	(*ScreenUserResponse)(nil),                                                     // 49: risk.ScreenUserResponse
	(*FetchLEAComplaintsRequest)(nil),                                              // 50: risk.FetchLEAComplaintsRequest
	(*FetchLEAComplaintsResponse)(nil),                                             // 51: risk.FetchLEAComplaintsResponse
	(*GetScreenerCheckResultsRequest)(nil),                                         // 52: risk.GetScreenerCheckResultsRequest
	(*GetScreenerCheckResultsResponse)(nil),                                        // 53: risk.GetScreenerCheckResultsResponse
	(*GetScreenerCheckDetailsRequest)(nil),                                         // 54: risk.GetScreenerCheckDetailsRequest
	(*GetScreenerCheckDetailsResponse)(nil),                                        // 55: risk.GetScreenerCheckDetailsResponse
	(*ScreenActorRequest)(nil),                                                     // 56: risk.ScreenActorRequest
	(*ScreenActorResponse)(nil),                                                    // 57: risk.ScreenActorResponse
	(*GetScreenerAttemptStatusRequest)(nil),                                        // 58: risk.GetScreenerAttemptStatusRequest
	(*GetScreenerAttemptStatusResponse)(nil),                                       // 59: risk.GetScreenerAttemptStatusResponse
	(*UpdateScreenerAttemptRequest)(nil),                                           // 60: risk.UpdateScreenerAttemptRequest
	(*UpdateScreenerAttemptResponse)(nil),                                          // 61: risk.UpdateScreenerAttemptResponse
	nil,                                                                            // 62: risk.GetRiskDataRequest.MetadataEntry
	nil,                                                                            // 63: risk.GetRiskDataResponse.RiskParamResponseMapEntry
	(*BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure)(nil),     // 64: risk.BulkOverrideBankActionStateResponse.OverrideBankActionStateFailure
	(*GetScreenerCheckResultsRequest_Filters)(nil),                                 // 65: risk.GetScreenerCheckResultsRequest.Filters
	(*GetScreenerCheckResultsResponse_CheckResult)(nil),                            // 66: risk.GetScreenerCheckResultsResponse.CheckResult
	(*rpc.Status)(nil),                                                             // 67: rpc.Status
	(tagging.TransactionTag)(0),                                                    // 68: risk.tagging.TransactionTag
	(*BureauIdRiskDetail)(nil),                                                     // 69: risk.BureauIdRiskDetail
	(enums.ScreenerCriteria)(0),                                                    // 70: enums.ScreenerCriteria
	(*Dispute)(nil),                                                                // 71: risk.Dispute
	(*LEAComplaintSource)(nil),                                                     // 72: risk.LEAComplaintSource
	(*LEAComplaint)(nil),                                                           // 73: risk.LEAComplaint
	(LEAComplaintFieldMask)(0),                                                     // 74: risk.LEAComplaintFieldMask
	(*LEAComplaintNarrationDetails)(nil),                                           // 75: risk.LEAComplaintNarrationDetails
	(RiskParam)(0),                                                                 // 76: risk.RiskParam
	(common.BooleanEnum)(0),                                                        // 77: api.typesv2.common.BooleanEnum
	(*RiskData)(nil),                                                               // 78: risk.RiskData
	(*Payload)(nil),                                                                // 79: risk.Payload
	(Result)(0),                                                                    // 80: risk.Result
	(enums.Action)(0),                                                              // 81: enums.Action
	(*RequestReason)(nil),                                                          // 82: risk.RequestReason
	(enums.CommsTemplate)(0),                                                       // 83: enums.CommsTemplate
	(*timestamppb.Timestamp)(nil),                                                  // 84: google.protobuf.Timestamp
	(enums.Provenance)(0),                                                          // 85: enums.Provenance
	(enums.State)(0),                                                               // 86: enums.State
	(enums.ScreenerAction)(0),                                                      // 87: enums.ScreenerAction
	(*ExtendedLEAComplaint)(nil),                                                   // 88: risk.ExtendedLEAComplaint
	(*screener.AdditionalCheckDetails)(nil),                                        // 89: risk.screener.AdditionalCheckDetails
	(screener.ScreenerStatus)(0),                                                   // 90: risk.screener.ScreenerStatus
	(screener.Verdict)(0),                                                          // 91: risk.screener.Verdict
	(screener.PotentialRiskFlag)(0),                                                // 92: risk.screener.PotentialRiskFlag
	(*screener.AttemptIdentifier)(nil),                                             // 93: risk.screener.AttemptIdentifier
	(*screener.ScreenerAttempt)(nil),                                               // 94: risk.screener.ScreenerAttempt
	(screener.ScreenerAttemptFieldMask)(0),                                         // 95: risk.screener.ScreenerAttemptFieldMask
	(*dynamic_elements.FetchDynamicElementsRequest)(nil),                           // 96: dynamic_elements.FetchDynamicElementsRequest
	(*dynamic_elements.DynamicElementCallbackRequest)(nil),                         // 97: dynamic_elements.DynamicElementCallbackRequest
	(*dynamic_elements.FetchDynamicElementsResponse)(nil),                          // 98: dynamic_elements.FetchDynamicElementsResponse
	(*dynamic_elements.DynamicElementCallbackResponse)(nil),                        // 99: dynamic_elements.DynamicElementCallbackResponse
}
var file_api_risk_service_proto_depIdxs = []int32{
	67, // 0: risk.GetTransactionTagsResponse.status:type_name -> rpc.Status
	68, // 1: risk.GetTransactionTagsResponse.tags:type_name -> risk.tagging.TransactionTag
	67, // 2: risk.GetBureauIdRiskDetailsResponse.status:type_name -> rpc.Status
	69, // 3: risk.GetBureauIdRiskDetailsResponse.bureau_id_risk_details:type_name -> risk.BureauIdRiskDetail
	69, // 4: risk.BulkInsertBureauIdRiskDetailsRequest.bureau_id_risk_details:type_name -> risk.BureauIdRiskDetail
	67, // 5: risk.BulkInsertBureauIdRiskDetailsResponse.status:type_name -> rpc.Status
	70, // 6: risk.PassRiskScreenerAttemptRequest.screener_criteria:type_name -> enums.ScreenerCriteria
	67, // 7: risk.PassRiskScreenerAttemptResponse.status:type_name -> rpc.Status
	71, // 8: risk.UpsertDisputeRequest.dispute:type_name -> risk.Dispute
	67, // 9: risk.UpsertDisputeResponse.status:type_name -> rpc.Status
	72, // 10: risk.AddLEAComplaintSourceRequest.source:type_name -> risk.LEAComplaintSource
	67, // 11: risk.AddLEAComplaintSourceResponse.status:type_name -> rpc.Status
	73, // 12: risk.UpdateLEAComplaintRequest.lea_complaint:type_name -> risk.LEAComplaint
	74, // 13: risk.UpdateLEAComplaintRequest.field_masks:type_name -> risk.LEAComplaintFieldMask
	67, // 14: risk.UpdateLEAComplaintResponse.status:type_name -> rpc.Status
	75, // 15: risk.ProcessLEAComplaintNarrationRequest.lea_complaint_narration_details:type_name -> risk.LEAComplaintNarrationDetails
	67, // 16: risk.ProcessLEAComplaintNarrationResponse.status:type_name -> rpc.Status
	73, // 17: risk.CreateLEAComplaintRequest.lea_complaint:type_name -> risk.LEAComplaint
	67, // 18: risk.CreateLEAComplaintResponse.status:type_name -> rpc.Status
	76, // 19: risk.GetRiskDataRequest.risk_params:type_name -> risk.RiskParam
	77, // 20: risk.GetRiskDataRequest.is_device_premium:type_name -> api.typesv2.common.BooleanEnum
	77, // 21: risk.GetRiskDataRequest.is_credit_report_found:type_name -> api.typesv2.common.BooleanEnum
	77, // 22: risk.GetRiskDataRequest.has_credit_report_download_consent:type_name -> api.typesv2.common.BooleanEnum
	77, // 23: risk.GetRiskDataRequest.want_cached_data:type_name -> api.typesv2.common.BooleanEnum
	62, // 24: risk.GetRiskDataRequest.metadata:type_name -> risk.GetRiskDataRequest.MetadataEntry
	67, // 25: risk.GetRiskDataResponse.status:type_name -> rpc.Status
	78, // 26: risk.GetRiskDataResponse.risk_data:type_name -> risk.RiskData
	63, // 27: risk.GetRiskDataResponse.risk_param_response_map:type_name -> risk.GetRiskDataResponse.RiskParamResponseMapEntry
	8,  // 28: risk.RiskParamResponse.status:type_name -> risk.RiskParamResponse.Status
	78, // 29: risk.RiskParamResponse.risk_data:type_name -> risk.RiskData
	76, // 30: risk.UpsertRiskDataRequest.risk_param:type_name -> risk.RiskParam
	79, // 31: risk.UpsertRiskDataRequest.payload:type_name -> risk.Payload
	80, // 32: risk.UpsertRiskDataRequest.result:type_name -> risk.Result
	67, // 33: risk.UpsertRiskDataResponse.status:type_name -> rpc.Status
	81, // 34: risk.AddSavingsAccountBankActionRequest.action:type_name -> enums.Action
	82, // 35: risk.AddSavingsAccountBankActionRequest.request_reason:type_name -> risk.RequestReason
	83, // 36: risk.AddSavingsAccountBankActionRequest.userCommsTemplate:type_name -> enums.CommsTemplate
	77, // 37: risk.AddSavingsAccountBankActionRequest.is_recon:type_name -> api.typesv2.common.BooleanEnum
	84, // 38: risk.AddSavingsAccountBankActionRequest.bank_action_date:type_name -> google.protobuf.Timestamp
	41, // 39: risk.BulkAddSavingsAccountBankActionRequest.requests:type_name -> risk.AddSavingsAccountBankActionRequest
	85, // 40: risk.BulkAddSavingsAccountBankActionRequest.provenance:type_name -> enums.Provenance
	67, // 41: risk.BulkAddSavingsAccountBankActionResponse.status:type_name -> rpc.Status
	42, // 42: risk.BulkAddSavingsAccountBankActionResponse.failures:type_name -> risk.AddSavingsAccountBankActionFailure
	86, // 43: risk.OverrideBankActionStateRequest.required_state:type_name -> enums.State
	45, // 44: risk.BulkOverrideBankActionStateRequest.requests:type_name -> risk.OverrideBankActionStateRequest
	67, // 45: risk.BulkOverrideBankActionStateResponse.status:type_name -> rpc.Status
	64, // 46: risk.BulkOverrideBankActionStateResponse.failures:type_name -> risk.BulkOverrideBankActionStateResponse.OverrideBankActionStateFailure
	70, // 47: risk.ScreenUserRequest.screener_criteria:type_name -> enums.ScreenerCriteria
	67, // 48: risk.ScreenUserResponse.status:type_name -> rpc.Status
	87, // 49: risk.ScreenUserResponse.screener_action:type_name -> enums.ScreenerAction
	67, // 50: risk.FetchLEAComplaintsResponse.status:type_name -> rpc.Status
	73, // 51: risk.FetchLEAComplaintsResponse.lea_complaints:type_name -> risk.LEAComplaint
	88, // 52: risk.FetchLEAComplaintsResponse.extended_lea_complaints:type_name -> risk.ExtendedLEAComplaint
	65, // 53: risk.GetScreenerCheckResultsRequest.filters:type_name -> risk.GetScreenerCheckResultsRequest.Filters
	67, // 54: risk.GetScreenerCheckResultsResponse.status:type_name -> rpc.Status
	66, // 55: risk.GetScreenerCheckResultsResponse.risk_check_results:type_name -> risk.GetScreenerCheckResultsResponse.CheckResult
	67, // 56: risk.GetScreenerCheckDetailsResponse.status:type_name -> rpc.Status
	78, // 57: risk.GetScreenerCheckDetailsResponse.check_result:type_name -> risk.RiskData
	89, // 58: risk.GetScreenerCheckDetailsResponse.additional_check_details:type_name -> risk.screener.AdditionalCheckDetails
	70, // 59: risk.ScreenActorRequest.screener_criteria:type_name -> enums.ScreenerCriteria
	67, // 60: risk.ScreenActorResponse.status:type_name -> rpc.Status
	90, // 61: risk.ScreenActorResponse.screener_status:type_name -> risk.screener.ScreenerStatus
	91, // 62: risk.ScreenActorResponse.verdict:type_name -> risk.screener.Verdict
	92, // 63: risk.ScreenActorResponse.potential_risk_flags:type_name -> risk.screener.PotentialRiskFlag
	93, // 64: risk.GetScreenerAttemptStatusRequest.attempt_identifier:type_name -> risk.screener.AttemptIdentifier
	67, // 65: risk.GetScreenerAttemptStatusResponse.status:type_name -> rpc.Status
	90, // 66: risk.GetScreenerAttemptStatusResponse.screener_status:type_name -> risk.screener.ScreenerStatus
	91, // 67: risk.GetScreenerAttemptStatusResponse.verdict:type_name -> risk.screener.Verdict
	92, // 68: risk.GetScreenerAttemptStatusResponse.potential_risk_flags:type_name -> risk.screener.PotentialRiskFlag
	94, // 69: risk.UpdateScreenerAttemptRequest.screener_attempt:type_name -> risk.screener.ScreenerAttempt
	95, // 70: risk.UpdateScreenerAttemptRequest.field_masks:type_name -> risk.screener.ScreenerAttemptFieldMask
	67, // 71: risk.UpdateScreenerAttemptResponse.status:type_name -> rpc.Status
	38, // 72: risk.GetRiskDataResponse.RiskParamResponseMapEntry.value:type_name -> risk.RiskParamResponse
	67, // 73: risk.BulkOverrideBankActionStateResponse.OverrideBankActionStateFailure.status:type_name -> rpc.Status
	80, // 74: risk.GetScreenerCheckResultsRequest.Filters.result:type_name -> risk.Result
	78, // 75: risk.GetScreenerCheckResultsResponse.CheckResult.data:type_name -> risk.RiskData
	36, // 76: risk.Risk.GetRiskData:input_type -> risk.GetRiskDataRequest
	39, // 77: risk.Risk.UpsertRiskData:input_type -> risk.UpsertRiskDataRequest
	43, // 78: risk.Risk.BulkAddSavingsAccountBankAction:input_type -> risk.BulkAddSavingsAccountBankActionRequest
	46, // 79: risk.Risk.BulkOverrideBankActionState:input_type -> risk.BulkOverrideBankActionStateRequest
	34, // 80: risk.Risk.CreateLEAComplaint:input_type -> risk.CreateLEAComplaintRequest
	50, // 81: risk.Risk.FetchLEAComplaints:input_type -> risk.FetchLEAComplaintsRequest
	48, // 82: risk.Risk.ScreenUser:input_type -> risk.ScreenUserRequest
	56, // 83: risk.Risk.ScreenActor:input_type -> risk.ScreenActorRequest
	58, // 84: risk.Risk.GetScreenerAttemptStatus:input_type -> risk.GetScreenerAttemptStatusRequest
	96, // 85: risk.Risk.FetchDynamicElements:input_type -> dynamic_elements.FetchDynamicElementsRequest
	97, // 86: risk.Risk.DynamicElementCallback:input_type -> dynamic_elements.DynamicElementCallbackRequest
	52, // 87: risk.Risk.GetScreenerCheckResults:input_type -> risk.GetScreenerCheckResultsRequest
	32, // 88: risk.Risk.ProcessLEAComplaintNarration:input_type -> risk.ProcessLEAComplaintNarrationRequest
	54, // 89: risk.Risk.GetScreenerCheckDetails:input_type -> risk.GetScreenerCheckDetailsRequest
	28, // 90: risk.Risk.AddLEAComplaintSource:input_type -> risk.AddLEAComplaintSourceRequest
	30, // 91: risk.Risk.UpdateLEAComplaint:input_type -> risk.UpdateLEAComplaintRequest
	60, // 92: risk.Risk.UpdateScreenerAttempt:input_type -> risk.UpdateScreenerAttemptRequest
	26, // 93: risk.Risk.UpsertDispute:input_type -> risk.UpsertDisputeRequest
	24, // 94: risk.Risk.PassRiskScreenerAttempt:input_type -> risk.PassRiskScreenerAttemptRequest
	22, // 95: risk.Risk.BulkInsertBureauIdRiskDetails:input_type -> risk.BulkInsertBureauIdRiskDetailsRequest
	20, // 96: risk.Risk.GetBureauIdRiskDetails:input_type -> risk.GetBureauIdRiskDetailsRequest
	18, // 97: risk.Risk.GetTransactionTags:input_type -> risk.GetTransactionTagsRequest
	37, // 98: risk.Risk.GetRiskData:output_type -> risk.GetRiskDataResponse
	40, // 99: risk.Risk.UpsertRiskData:output_type -> risk.UpsertRiskDataResponse
	44, // 100: risk.Risk.BulkAddSavingsAccountBankAction:output_type -> risk.BulkAddSavingsAccountBankActionResponse
	47, // 101: risk.Risk.BulkOverrideBankActionState:output_type -> risk.BulkOverrideBankActionStateResponse
	35, // 102: risk.Risk.CreateLEAComplaint:output_type -> risk.CreateLEAComplaintResponse
	51, // 103: risk.Risk.FetchLEAComplaints:output_type -> risk.FetchLEAComplaintsResponse
	49, // 104: risk.Risk.ScreenUser:output_type -> risk.ScreenUserResponse
	57, // 105: risk.Risk.ScreenActor:output_type -> risk.ScreenActorResponse
	59, // 106: risk.Risk.GetScreenerAttemptStatus:output_type -> risk.GetScreenerAttemptStatusResponse
	98, // 107: risk.Risk.FetchDynamicElements:output_type -> dynamic_elements.FetchDynamicElementsResponse
	99, // 108: risk.Risk.DynamicElementCallback:output_type -> dynamic_elements.DynamicElementCallbackResponse
	53, // 109: risk.Risk.GetScreenerCheckResults:output_type -> risk.GetScreenerCheckResultsResponse
	33, // 110: risk.Risk.ProcessLEAComplaintNarration:output_type -> risk.ProcessLEAComplaintNarrationResponse
	55, // 111: risk.Risk.GetScreenerCheckDetails:output_type -> risk.GetScreenerCheckDetailsResponse
	29, // 112: risk.Risk.AddLEAComplaintSource:output_type -> risk.AddLEAComplaintSourceResponse
	31, // 113: risk.Risk.UpdateLEAComplaint:output_type -> risk.UpdateLEAComplaintResponse
	61, // 114: risk.Risk.UpdateScreenerAttempt:output_type -> risk.UpdateScreenerAttemptResponse
	27, // 115: risk.Risk.UpsertDispute:output_type -> risk.UpsertDisputeResponse
	25, // 116: risk.Risk.PassRiskScreenerAttempt:output_type -> risk.PassRiskScreenerAttemptResponse
	23, // 117: risk.Risk.BulkInsertBureauIdRiskDetails:output_type -> risk.BulkInsertBureauIdRiskDetailsResponse
	21, // 118: risk.Risk.GetBureauIdRiskDetails:output_type -> risk.GetBureauIdRiskDetailsResponse
	19, // 119: risk.Risk.GetTransactionTags:output_type -> risk.GetTransactionTagsResponse
	98, // [98:120] is the sub-list for method output_type
	76, // [76:98] is the sub-list for method input_type
	76, // [76:76] is the sub-list for extension type_name
	76, // [76:76] is the sub-list for extension extendee
	0,  // [0:76] is the sub-list for field type_name
}

func init() { file_api_risk_service_proto_init() }
func file_api_risk_service_proto_init() {
	if File_api_risk_service_proto != nil {
		return
	}
	file_api_risk_bankactions_risk_bank_actions_proto_init()
	file_api_risk_bureau_id_proto_init()
	file_api_risk_internal_data_source_proto_init()
	file_api_risk_internal_dispute_proto_init()
	file_api_risk_internal_lea_complaint_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionTagsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionTagsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBureauIdRiskDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBureauIdRiskDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkInsertBureauIdRiskDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkInsertBureauIdRiskDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PassRiskScreenerAttemptRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PassRiskScreenerAttemptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertDisputeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertDisputeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLEAComplaintSourceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLEAComplaintSourceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLEAComplaintRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLEAComplaintResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessLEAComplaintNarrationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessLEAComplaintNarrationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLEAComplaintRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLEAComplaintResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRiskDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRiskDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskParamResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertRiskDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertRiskDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddSavingsAccountBankActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddSavingsAccountBankActionFailure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkAddSavingsAccountBankActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkAddSavingsAccountBankActionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OverrideBankActionStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkOverrideBankActionStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkOverrideBankActionStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchLEAComplaintsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchLEAComplaintsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScreenerCheckResultsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScreenerCheckResultsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScreenerCheckDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScreenerCheckDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScreenerAttemptStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScreenerAttemptStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateScreenerAttemptRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateScreenerAttemptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScreenerCheckResultsRequest_Filters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScreenerCheckResultsResponse_CheckResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_service_proto_msgTypes[23].OneofWrappers = []interface{}{
		(*AddSavingsAccountBankActionRequest_ActorId)(nil),
	}
	file_api_risk_service_proto_msgTypes[32].OneofWrappers = []interface{}{
		(*FetchLEAComplaintsRequest_ActorId)(nil),
		(*FetchLEAComplaintsRequest_AccountId)(nil),
	}
	file_api_risk_service_proto_msgTypes[34].OneofWrappers = []interface{}{
		(*GetScreenerCheckResultsRequest_ActorId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_service_proto_rawDesc,
			NumEnums:      18,
			NumMessages:   49,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_risk_service_proto_goTypes,
		DependencyIndexes: file_api_risk_service_proto_depIdxs,
		EnumInfos:         file_api_risk_service_proto_enumTypes,
		MessageInfos:      file_api_risk_service_proto_msgTypes,
	}.Build()
	File_api_risk_service_proto = out.File
	file_api_risk_service_proto_rawDesc = nil
	file_api_risk_service_proto_goTypes = nil
	file_api_risk_service_proto_depIdxs = nil
}
