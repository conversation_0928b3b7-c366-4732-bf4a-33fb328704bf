// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/internal/redlist.proto

package risk

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	common "github.com/epifi/be-common/api/typesv2/common"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RedListCategory is used to specify the type of the risk that we store in the red list
type RedListCategory int32

const (
	RedListCategory_REDLIST_CATEGORY_UNSPECIFIED RedListCategory = 0
	// Pincode sourced from user geolocation
	RedListCategory_REDLIST_CATEGORY_LOCATION_PINCODE RedListCategory = 1
	// Co-ordinate sources from user geolocation
	RedListCategory_REDLIST_CATEGORY_LOCATION_LATLONG RedListCategory = 2
	// Sourced from DS team
	RedListCategory_REDLIST_CATEGORY_HASHED_CONTACT RedListCategory = 3
	// Pincode sourced from user address
	RedListCategory_REDLIST_CATEGORY_KYC_PINCODE RedListCategory = 4
	RedListCategory_REDLIST_CATEGORY_FINITE_CODE RedListCategory = 5
	RedListCategory_REDLIST_CATEGORY_DEVICE      RedListCategory = 6
	// Unhashed Phone number that is red listed
	RedListCategory_REDLIST_CATEGORY_PHONE_NUMBER RedListCategory = 7
	// Unhashed email that is red listed
	RedListCategory_REDLIST_CATEGORY_EMAIL RedListCategory = 8
	// Device ID that is redlisted <maufacturer.model.deviceid>
	RedListCategory_REDLIST_CATEGORY_DEVICE_ID RedListCategory = 9
	// suspicious atms
	RedListCategory_REDLIST_CATEGORY_ATM RedListCategory = 10
	// suspicious merchants
	RedListCategory_REDLIST_CATEGORY_MERCHANTS RedListCategory = 11
	// risky stated from where user has onboarded,
	// can be combined with other category such as atm to create screening rules
	RedListCategory_REDLIST_CATEGORY_ONBOARDING_STATE RedListCategory = 12
	// Actor is placed into a watchlist due to different reasons like past suspicious activities, suspicious onboarding or similar attributes
	// This can used for different use cases like prioritising reviews for actor if any risk rules are tripped or using as a feature in models, rules etc.
	// A user can be added to watchlist by an analyst post a review or via system based on different triggers
	RedListCategory_REDLIST_CATEGORY_ACTOR RedListCategory = 13
	// this denotes the risky IP address, contains IPV4 and IPV6 options
	RedListCategory_RED_LIST_CATEGORY_IP_ADDRESS RedListCategory = 14
	// list of all the risky merchants
	RedListCategory_RED_LIST_CATEGORY_RISKY_MERCHANTS RedListCategory = 15
)

// Enum value maps for RedListCategory.
var (
	RedListCategory_name = map[int32]string{
		0:  "REDLIST_CATEGORY_UNSPECIFIED",
		1:  "REDLIST_CATEGORY_LOCATION_PINCODE",
		2:  "REDLIST_CATEGORY_LOCATION_LATLONG",
		3:  "REDLIST_CATEGORY_HASHED_CONTACT",
		4:  "REDLIST_CATEGORY_KYC_PINCODE",
		5:  "REDLIST_CATEGORY_FINITE_CODE",
		6:  "REDLIST_CATEGORY_DEVICE",
		7:  "REDLIST_CATEGORY_PHONE_NUMBER",
		8:  "REDLIST_CATEGORY_EMAIL",
		9:  "REDLIST_CATEGORY_DEVICE_ID",
		10: "REDLIST_CATEGORY_ATM",
		11: "REDLIST_CATEGORY_MERCHANTS",
		12: "REDLIST_CATEGORY_ONBOARDING_STATE",
		13: "REDLIST_CATEGORY_ACTOR",
		14: "RED_LIST_CATEGORY_IP_ADDRESS",
		15: "RED_LIST_CATEGORY_RISKY_MERCHANTS",
	}
	RedListCategory_value = map[string]int32{
		"REDLIST_CATEGORY_UNSPECIFIED":      0,
		"REDLIST_CATEGORY_LOCATION_PINCODE": 1,
		"REDLIST_CATEGORY_LOCATION_LATLONG": 2,
		"REDLIST_CATEGORY_HASHED_CONTACT":   3,
		"REDLIST_CATEGORY_KYC_PINCODE":      4,
		"REDLIST_CATEGORY_FINITE_CODE":      5,
		"REDLIST_CATEGORY_DEVICE":           6,
		"REDLIST_CATEGORY_PHONE_NUMBER":     7,
		"REDLIST_CATEGORY_EMAIL":            8,
		"REDLIST_CATEGORY_DEVICE_ID":        9,
		"REDLIST_CATEGORY_ATM":              10,
		"REDLIST_CATEGORY_MERCHANTS":        11,
		"REDLIST_CATEGORY_ONBOARDING_STATE": 12,
		"REDLIST_CATEGORY_ACTOR":            13,
		"RED_LIST_CATEGORY_IP_ADDRESS":      14,
		"RED_LIST_CATEGORY_RISKY_MERCHANTS": 15,
	}
)

func (x RedListCategory) Enum() *RedListCategory {
	p := new(RedListCategory)
	*p = x
	return p
}

func (x RedListCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RedListCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_internal_redlist_proto_enumTypes[0].Descriptor()
}

func (RedListCategory) Type() protoreflect.EnumType {
	return &file_api_risk_internal_redlist_proto_enumTypes[0]
}

func (x RedListCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RedListCategory.Descriptor instead.
func (RedListCategory) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_internal_redlist_proto_rawDescGZIP(), []int{0}
}

type RedListerFieldMask int32

const (
	RedListerFieldMask_FIELD_MASK_UNSPECIFIED RedListerFieldMask = 0
	RedListerFieldMask_CATEGORY               RedListerFieldMask = 1
	RedListerFieldMask_VALUE                  RedListerFieldMask = 2
	RedListerFieldMask_RISK_SCORE             RedListerFieldMask = 3
	RedListerFieldMask_REASON                 RedListerFieldMask = 4
)

// Enum value maps for RedListerFieldMask.
var (
	RedListerFieldMask_name = map[int32]string{
		0: "FIELD_MASK_UNSPECIFIED",
		1: "CATEGORY",
		2: "VALUE",
		3: "RISK_SCORE",
		4: "REASON",
	}
	RedListerFieldMask_value = map[string]int32{
		"FIELD_MASK_UNSPECIFIED": 0,
		"CATEGORY":               1,
		"VALUE":                  2,
		"RISK_SCORE":             3,
		"REASON":                 4,
	}
)

func (x RedListerFieldMask) Enum() *RedListerFieldMask {
	p := new(RedListerFieldMask)
	*p = x
	return p
}

func (x RedListerFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RedListerFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_internal_redlist_proto_enumTypes[1].Descriptor()
}

func (RedListerFieldMask) Type() protoreflect.EnumType {
	return &file_api_risk_internal_redlist_proto_enumTypes[1]
}

func (x RedListerFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RedListerFieldMask.Descriptor instead.
func (RedListerFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_internal_redlist_proto_rawDescGZIP(), []int{1}
}

type RedListerSource int32

const (
	RedListerSource_UNSPECIFIED RedListerSource = 0
	// Source from AFU Manual Review failures
	RedListerSource_AFU_MANUAL_REVIEW RedListerSource = 1
	// Sourced from DS Pipeline
	RedListerSource_DATA_SCIENCE_PIPELINES RedListerSource = 2
	// Sourced from our internal Risk Analysts
	RedListerSource_RISK_ANALYST_INTERNAL RedListerSource = 3
	// Sourced from external Risk Analysts
	RedListerSource_RISK_ANALYST_EXTERNAL RedListerSource = 4
	// Sourced from risk screener.
	// Example: Risky users and their auth factors can be added to red list on auto block.
	RedListerSource_RISK_SCREENER RedListerSource = 5
)

// Enum value maps for RedListerSource.
var (
	RedListerSource_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "AFU_MANUAL_REVIEW",
		2: "DATA_SCIENCE_PIPELINES",
		3: "RISK_ANALYST_INTERNAL",
		4: "RISK_ANALYST_EXTERNAL",
		5: "RISK_SCREENER",
	}
	RedListerSource_value = map[string]int32{
		"UNSPECIFIED":            0,
		"AFU_MANUAL_REVIEW":      1,
		"DATA_SCIENCE_PIPELINES": 2,
		"RISK_ANALYST_INTERNAL":  3,
		"RISK_ANALYST_EXTERNAL":  4,
		"RISK_SCREENER":          5,
	}
)

func (x RedListerSource) Enum() *RedListerSource {
	p := new(RedListerSource)
	*p = x
	return p
}

func (x RedListerSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RedListerSource) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_internal_redlist_proto_enumTypes[2].Descriptor()
}

func (RedListerSource) Type() protoreflect.EnumType {
	return &file_api_risk_internal_redlist_proto_enumTypes[2]
}

func (x RedListerSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RedListerSource.Descriptor instead.
func (RedListerSource) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_internal_redlist_proto_rawDescGZIP(), []int{2}
}

type IPAddressDetail_IPAddressType int32

const (
	IPAddressDetail_IP_ADDRESS_TYPE_UNSPECIFIED IPAddressDetail_IPAddressType = 0
	IPAddressDetail_IP_ADDRESS_TYPE_IPV4        IPAddressDetail_IPAddressType = 1
	IPAddressDetail_IP_ADDRESS_TYPE_IPV6        IPAddressDetail_IPAddressType = 2
)

// Enum value maps for IPAddressDetail_IPAddressType.
var (
	IPAddressDetail_IPAddressType_name = map[int32]string{
		0: "IP_ADDRESS_TYPE_UNSPECIFIED",
		1: "IP_ADDRESS_TYPE_IPV4",
		2: "IP_ADDRESS_TYPE_IPV6",
	}
	IPAddressDetail_IPAddressType_value = map[string]int32{
		"IP_ADDRESS_TYPE_UNSPECIFIED": 0,
		"IP_ADDRESS_TYPE_IPV4":        1,
		"IP_ADDRESS_TYPE_IPV6":        2,
	}
)

func (x IPAddressDetail_IPAddressType) Enum() *IPAddressDetail_IPAddressType {
	p := new(IPAddressDetail_IPAddressType)
	*p = x
	return p
}

func (x IPAddressDetail_IPAddressType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IPAddressDetail_IPAddressType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_internal_redlist_proto_enumTypes[3].Descriptor()
}

func (IPAddressDetail_IPAddressType) Type() protoreflect.EnumType {
	return &file_api_risk_internal_redlist_proto_enumTypes[3]
}

func (x IPAddressDetail_IPAddressType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IPAddressDetail_IPAddressType.Descriptor instead.
func (IPAddressDetail_IPAddressType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_internal_redlist_proto_rawDescGZIP(), []int{5, 0}
}

type RedLister struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Key *RedListPair `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	// RiskScore defines the risk of an entry in a fixed range [0-100]
	RiskScore float32                `protobuf:"fixed32,3,opt,name=risk_score,json=riskScore,proto3" json:"risk_score,omitempty"`
	Reason    *RedListerReason       `protobuf:"bytes,7,opt,name=reason,proto3" json:"reason,omitempty"`
	Metadata  *Metadata              `protobuf:"bytes,8,opt,name=metadata,proto3" json:"metadata,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt int64                  `protobuf:"varint,6,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// Expiration time for redlist entry. Record won't appear in results after expiry.
	// Entry won't expire if left empty.
	ExpiresAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
}

func (x *RedLister) Reset() {
	*x = RedLister{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_internal_redlist_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedLister) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedLister) ProtoMessage() {}

func (x *RedLister) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_internal_redlist_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedLister.ProtoReflect.Descriptor instead.
func (*RedLister) Descriptor() ([]byte, []int) {
	return file_api_risk_internal_redlist_proto_rawDescGZIP(), []int{0}
}

func (x *RedLister) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RedLister) GetKey() *RedListPair {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *RedLister) GetRiskScore() float32 {
	if x != nil {
		return x.RiskScore
	}
	return 0
}

func (x *RedLister) GetReason() *RedListerReason {
	if x != nil {
		return x.Reason
	}
	return nil
}

func (x *RedLister) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *RedLister) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *RedLister) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *RedLister) GetDeletedAt() int64 {
	if x != nil {
		return x.DeletedAt
	}
	return 0
}

func (x *RedLister) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

type Metadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ManualUpdateMetadata *Metadata_ManualUpdateMetadata `protobuf:"bytes,1,opt,name=manual_update_metadata,json=manualUpdateMetadata,proto3" json:"manual_update_metadata,omitempty"`
	AfuRedListMetadata   *Metadata_AfuRedListMetadata   `protobuf:"bytes,2,opt,name=afu_red_list_metadata,json=afuRedListMetadata,proto3" json:"afu_red_list_metadata,omitempty"`
	ManualReviewMetata   *Metadata_ManualReviewMetadata `protobuf:"bytes,3,opt,name=manual_review_metata,json=manualReviewMetata,proto3" json:"manual_review_metata,omitempty"`
}

func (x *Metadata) Reset() {
	*x = Metadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_internal_redlist_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metadata) ProtoMessage() {}

func (x *Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_internal_redlist_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metadata.ProtoReflect.Descriptor instead.
func (*Metadata) Descriptor() ([]byte, []int) {
	return file_api_risk_internal_redlist_proto_rawDescGZIP(), []int{1}
}

func (x *Metadata) GetManualUpdateMetadata() *Metadata_ManualUpdateMetadata {
	if x != nil {
		return x.ManualUpdateMetadata
	}
	return nil
}

func (x *Metadata) GetAfuRedListMetadata() *Metadata_AfuRedListMetadata {
	if x != nil {
		return x.AfuRedListMetadata
	}
	return nil
}

func (x *Metadata) GetManualReviewMetata() *Metadata_ManualReviewMetadata {
	if x != nil {
		return x.ManualReviewMetata
	}
	return nil
}

type RedListerReason struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawReason string          `protobuf:"bytes,1,opt,name=raw_reason,json=rawReason,proto3" json:"raw_reason,omitempty"`
	Source    RedListerSource `protobuf:"varint,2,opt,name=source,proto3,enum=risk.RedListerSource" json:"source,omitempty"`
}

func (x *RedListerReason) Reset() {
	*x = RedListerReason{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_internal_redlist_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedListerReason) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedListerReason) ProtoMessage() {}

func (x *RedListerReason) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_internal_redlist_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedListerReason.ProtoReflect.Descriptor instead.
func (*RedListerReason) Descriptor() ([]byte, []int) {
	return file_api_risk_internal_redlist_proto_rawDescGZIP(), []int{2}
}

func (x *RedListerReason) GetRawReason() string {
	if x != nil {
		return x.RawReason
	}
	return ""
}

func (x *RedListerReason) GetSource() RedListerSource {
	if x != nil {
		return x.Source
	}
	return RedListerSource_UNSPECIFIED
}

type AtmRedList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AtmPi string `protobuf:"bytes,1,opt,name=atmPi,proto3" json:"atmPi,omitempty"`
}

func (x *AtmRedList) Reset() {
	*x = AtmRedList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_internal_redlist_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtmRedList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtmRedList) ProtoMessage() {}

func (x *AtmRedList) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_internal_redlist_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtmRedList.ProtoReflect.Descriptor instead.
func (*AtmRedList) Descriptor() ([]byte, []int) {
	return file_api_risk_internal_redlist_proto_rawDescGZIP(), []int{3}
}

func (x *AtmRedList) GetAtmPi() string {
	if x != nil {
		return x.AtmPi
	}
	return ""
}

type RedListPair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category RedListCategory `protobuf:"varint,1,opt,name=category,proto3,enum=risk.RedListCategory" json:"category,omitempty"`
	// Types that are assignable to Value:
	//
	//	*RedListPair_Pincode
	//	*RedListPair_LatLong
	//	*RedListPair_HashedContact
	//	*RedListPair_FiniteCode
	//	*RedListPair_Device
	//	*RedListPair_PhoneNum
	//	*RedListPair_Email
	//	*RedListPair_DeviceId
	//	*RedListPair_Atm
	//	*RedListPair_Merchant
	//	*RedListPair_OnboardingState
	//	*RedListPair_ActorId
	//	*RedListPair_IpAddressDetail
	//	*RedListPair_RiskyMerchant
	Value isRedListPair_Value `protobuf_oneof:"value"`
}

func (x *RedListPair) Reset() {
	*x = RedListPair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_internal_redlist_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedListPair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedListPair) ProtoMessage() {}

func (x *RedListPair) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_internal_redlist_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedListPair.ProtoReflect.Descriptor instead.
func (*RedListPair) Descriptor() ([]byte, []int) {
	return file_api_risk_internal_redlist_proto_rawDescGZIP(), []int{4}
}

func (x *RedListPair) GetCategory() RedListCategory {
	if x != nil {
		return x.Category
	}
	return RedListCategory_REDLIST_CATEGORY_UNSPECIFIED
}

func (m *RedListPair) GetValue() isRedListPair_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *RedListPair) GetPincode() string {
	if x, ok := x.GetValue().(*RedListPair_Pincode); ok {
		return x.Pincode
	}
	return ""
}

func (x *RedListPair) GetLatLong() *typesv2.LatLongWithPrecision {
	if x, ok := x.GetValue().(*RedListPair_LatLong); ok {
		return x.LatLong
	}
	return nil
}

func (x *RedListPair) GetHashedContact() string {
	if x, ok := x.GetValue().(*RedListPair_HashedContact); ok {
		return x.HashedContact
	}
	return ""
}

func (x *RedListPair) GetFiniteCode() string {
	if x, ok := x.GetValue().(*RedListPair_FiniteCode); ok {
		return x.FiniteCode
	}
	return ""
}

func (x *RedListPair) GetDevice() *common.Device {
	if x, ok := x.GetValue().(*RedListPair_Device); ok {
		return x.Device
	}
	return nil
}

func (x *RedListPair) GetPhoneNum() *common.PhoneNumber {
	if x, ok := x.GetValue().(*RedListPair_PhoneNum); ok {
		return x.PhoneNum
	}
	return nil
}

func (x *RedListPair) GetEmail() string {
	if x, ok := x.GetValue().(*RedListPair_Email); ok {
		return x.Email
	}
	return ""
}

func (x *RedListPair) GetDeviceId() string {
	if x, ok := x.GetValue().(*RedListPair_DeviceId); ok {
		return x.DeviceId
	}
	return ""
}

func (x *RedListPair) GetAtm() *AtmRedList {
	if x, ok := x.GetValue().(*RedListPair_Atm); ok {
		return x.Atm
	}
	return nil
}

func (x *RedListPair) GetMerchant() string {
	if x, ok := x.GetValue().(*RedListPair_Merchant); ok {
		return x.Merchant
	}
	return ""
}

func (x *RedListPair) GetOnboardingState() string {
	if x, ok := x.GetValue().(*RedListPair_OnboardingState); ok {
		return x.OnboardingState
	}
	return ""
}

func (x *RedListPair) GetActorId() string {
	if x, ok := x.GetValue().(*RedListPair_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *RedListPair) GetIpAddressDetail() *IPAddressDetail {
	if x, ok := x.GetValue().(*RedListPair_IpAddressDetail); ok {
		return x.IpAddressDetail
	}
	return nil
}

func (x *RedListPair) GetRiskyMerchant() *RiskyMerchant {
	if x, ok := x.GetValue().(*RedListPair_RiskyMerchant); ok {
		return x.RiskyMerchant
	}
	return nil
}

type isRedListPair_Value interface {
	isRedListPair_Value()
}

type RedListPair_Pincode struct {
	Pincode string `protobuf:"bytes,2,opt,name=pincode,proto3,oneof"`
}

type RedListPair_LatLong struct {
	LatLong *typesv2.LatLongWithPrecision `protobuf:"bytes,3,opt,name=lat_long,json=latLong,proto3,oneof"`
}

type RedListPair_HashedContact struct {
	HashedContact string `protobuf:"bytes,4,opt,name=hashed_contact,json=hashedContact,proto3,oneof"`
}

type RedListPair_FiniteCode struct {
	FiniteCode string `protobuf:"bytes,5,opt,name=finite_code,json=finiteCode,proto3,oneof"`
}

type RedListPair_Device struct {
	Device *common.Device `protobuf:"bytes,6,opt,name=device,proto3,oneof"`
}

type RedListPair_PhoneNum struct {
	// This is stored/checked as it is, un-hashed. If you want to store/ check for hashed contact
	// use hashed_contact instead
	PhoneNum *common.PhoneNumber `protobuf:"bytes,7,opt,name=phone_num,json=phoneNum,proto3,oneof"`
}

type RedListPair_Email struct {
	Email string `protobuf:"bytes,8,opt,name=email,proto3,oneof"`
}

type RedListPair_DeviceId struct {
	DeviceId string `protobuf:"bytes,9,opt,name=device_id,json=deviceId,proto3,oneof"`
}

type RedListPair_Atm struct {
	Atm *AtmRedList `protobuf:"bytes,10,opt,name=atm,proto3,oneof"`
}

type RedListPair_Merchant struct {
	Merchant string `protobuf:"bytes,11,opt,name=merchant,proto3,oneof"`
}

type RedListPair_OnboardingState struct {
	OnboardingState string `protobuf:"bytes,12,opt,name=onboarding_state,json=onboardingState,proto3,oneof"`
}

type RedListPair_ActorId struct {
	ActorId string `protobuf:"bytes,13,opt,name=actor_id,json=actorId,proto3,oneof"`
}

type RedListPair_IpAddressDetail struct {
	IpAddressDetail *IPAddressDetail `protobuf:"bytes,14,opt,name=ip_address_detail,json=ipAddressDetail,proto3,oneof"`
}

type RedListPair_RiskyMerchant struct {
	RiskyMerchant *RiskyMerchant `protobuf:"bytes,15,opt,name=risky_merchant,json=riskyMerchant,proto3,oneof"`
}

func (*RedListPair_Pincode) isRedListPair_Value() {}

func (*RedListPair_LatLong) isRedListPair_Value() {}

func (*RedListPair_HashedContact) isRedListPair_Value() {}

func (*RedListPair_FiniteCode) isRedListPair_Value() {}

func (*RedListPair_Device) isRedListPair_Value() {}

func (*RedListPair_PhoneNum) isRedListPair_Value() {}

func (*RedListPair_Email) isRedListPair_Value() {}

func (*RedListPair_DeviceId) isRedListPair_Value() {}

func (*RedListPair_Atm) isRedListPair_Value() {}

func (*RedListPair_Merchant) isRedListPair_Value() {}

func (*RedListPair_OnboardingState) isRedListPair_Value() {}

func (*RedListPair_ActorId) isRedListPair_Value() {}

func (*RedListPair_IpAddressDetail) isRedListPair_Value() {}

func (*RedListPair_RiskyMerchant) isRedListPair_Value() {}

type IPAddressDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IpAddressType IPAddressDetail_IPAddressType `protobuf:"varint,1,opt,name=ip_address_type,json=ipAddressType,proto3,enum=risk.IPAddressDetail_IPAddressType" json:"ip_address_type,omitempty"`
	IpAddress     string                        `protobuf:"bytes,2,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
}

func (x *IPAddressDetail) Reset() {
	*x = IPAddressDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_internal_redlist_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IPAddressDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPAddressDetail) ProtoMessage() {}

func (x *IPAddressDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_internal_redlist_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPAddressDetail.ProtoReflect.Descriptor instead.
func (*IPAddressDetail) Descriptor() ([]byte, []int) {
	return file_api_risk_internal_redlist_proto_rawDescGZIP(), []int{5}
}

func (x *IPAddressDetail) GetIpAddressType() IPAddressDetail_IPAddressType {
	if x != nil {
		return x.IpAddressType
	}
	return IPAddressDetail_IP_ADDRESS_TYPE_UNSPECIFIED
}

func (x *IPAddressDetail) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

type RiskyMerchant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MerchantName string `protobuf:"bytes,1,opt,name=merchant_name,json=merchantName,proto3" json:"merchant_name,omitempty"`
}

func (x *RiskyMerchant) Reset() {
	*x = RiskyMerchant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_internal_redlist_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskyMerchant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskyMerchant) ProtoMessage() {}

func (x *RiskyMerchant) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_internal_redlist_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskyMerchant.ProtoReflect.Descriptor instead.
func (*RiskyMerchant) Descriptor() ([]byte, []int) {
	return file_api_risk_internal_redlist_proto_rawDescGZIP(), []int{6}
}

func (x *RiskyMerchant) GetMerchantName() string {
	if x != nil {
		return x.MerchantName
	}
	return ""
}

type Metadata_ManualUpdateMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// email of the agent that stored/deleted the redlister through dev action
	UpdatedByEmail string `protobuf:"bytes,1,opt,name=updated_by_email,json=updatedByEmail,proto3" json:"updated_by_email,omitempty"`
	// reason why the agent stored/deleted the redlister through dev action
	UpdateReason string `protobuf:"bytes,2,opt,name=update_reason,json=updateReason,proto3" json:"update_reason,omitempty"`
}

func (x *Metadata_ManualUpdateMetadata) Reset() {
	*x = Metadata_ManualUpdateMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_internal_redlist_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Metadata_ManualUpdateMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metadata_ManualUpdateMetadata) ProtoMessage() {}

func (x *Metadata_ManualUpdateMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_internal_redlist_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metadata_ManualUpdateMetadata.ProtoReflect.Descriptor instead.
func (*Metadata_ManualUpdateMetadata) Descriptor() ([]byte, []int) {
	return file_api_risk_internal_redlist_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Metadata_ManualUpdateMetadata) GetUpdatedByEmail() string {
	if x != nil {
		return x.UpdatedByEmail
	}
	return ""
}

func (x *Metadata_ManualUpdateMetadata) GetUpdateReason() string {
	if x != nil {
		return x.UpdateReason
	}
	return ""
}

type Metadata_AfuRedListMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// afu_manual_review_suspect_score is used to calculate the red list score for a user based on the defined decision model
	// The suspect_score is determined by the severity of each manual review failure.
	AfuManualReviewSuspectScore int64 `protobuf:"varint,1,opt,name=afu_manual_review_suspect_score,json=afuManualReviewSuspectScore,proto3" json:"afu_manual_review_suspect_score,omitempty"`
	// suspect_level_failure_count maps the number of failures per user for different levels like:
	//
	//	map[string]int{
	//	   HIGH:    4,
	//	   LOW:     2,
	//	}
	SuspectLevelFailureCount map[string]int64 `protobuf:"bytes,2,rep,name=suspect_level_failure_count,json=suspectLevelFailureCount,proto3" json:"suspect_level_failure_count,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *Metadata_AfuRedListMetadata) Reset() {
	*x = Metadata_AfuRedListMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_internal_redlist_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Metadata_AfuRedListMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metadata_AfuRedListMetadata) ProtoMessage() {}

func (x *Metadata_AfuRedListMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_internal_redlist_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metadata_AfuRedListMetadata.ProtoReflect.Descriptor instead.
func (*Metadata_AfuRedListMetadata) Descriptor() ([]byte, []int) {
	return file_api_risk_internal_redlist_proto_rawDescGZIP(), []int{1, 1}
}

func (x *Metadata_AfuRedListMetadata) GetAfuManualReviewSuspectScore() int64 {
	if x != nil {
		return x.AfuManualReviewSuspectScore
	}
	return 0
}

func (x *Metadata_AfuRedListMetadata) GetSuspectLevelFailureCount() map[string]int64 {
	if x != nil {
		return x.SuspectLevelFailureCount
	}
	return nil
}

// meta data to be stored if actor was added to redlist from the case review flow
type Metadata_ManualReviewMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// email of the agent that added the actor to redlist
	UpdatedByEmail string `protobuf:"bytes,1,opt,name=updated_by_email,json=updatedByEmail,proto3" json:"updated_by_email,omitempty"`
	// id of the case which was being reviewed when the actor was added to redlist
	CaseId string `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
}

func (x *Metadata_ManualReviewMetadata) Reset() {
	*x = Metadata_ManualReviewMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_internal_redlist_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Metadata_ManualReviewMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metadata_ManualReviewMetadata) ProtoMessage() {}

func (x *Metadata_ManualReviewMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_internal_redlist_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metadata_ManualReviewMetadata.ProtoReflect.Descriptor instead.
func (*Metadata_ManualReviewMetadata) Descriptor() ([]byte, []int) {
	return file_api_risk_internal_redlist_proto_rawDescGZIP(), []int{1, 2}
}

func (x *Metadata_ManualReviewMetadata) GetUpdatedByEmail() string {
	if x != nil {
		return x.UpdatedByEmail
	}
	return ""
}

func (x *Metadata_ManualReviewMetadata) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

var File_api_risk_internal_redlist_proto protoreflect.FileDescriptor

var file_api_risk_internal_redlist_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x72, 0x65, 0x64, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x04, 0x72, 0x69, 0x73, 0x6b, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6f,
	0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9b, 0x03, 0x0a, 0x09, 0x52, 0x65, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x61, 0x69, 0x72, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2e, 0x0a, 0x0a, 0x72,
	0x69, 0x73, 0x6b, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x42,
	0x0f, 0xfa, 0x42, 0x0c, 0x0a, 0x0a, 0x1d, 0x00, 0x00, 0xc8, 0x42, 0x2d, 0x00, 0x00, 0x00, 0x00,
	0x52, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x73, 0x41, 0x74, 0x22, 0xfe, 0x05, 0x0a, 0x08, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x59, 0x0a, 0x16, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x14, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x54,
	0x0a, 0x15, 0x61, 0x66, 0x75, 0x5f, 0x72, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x66,
	0x75, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x52, 0x12, 0x61, 0x66, 0x75, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x55, 0x0a, 0x14, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x12, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x4d, 0x65, 0x74, 0x61, 0x74, 0x61, 0x1a, 0x65, 0x0a, 0x14, 0x4d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x10, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x0a,
	0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x1a, 0xa7, 0x02, 0x0a, 0x12, 0x41, 0x66, 0x75, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x44, 0x0a, 0x1f, 0x61, 0x66, 0x75,
	0x5f, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x73,
	0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x1b, 0x61, 0x66, 0x75, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x53, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x7e, 0x0a, 0x1b, 0x73, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x66, 0x75, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x53, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x18, 0x73, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a,
	0x4b, 0x0a, 0x1d, 0x53, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x46,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x59, 0x0a, 0x14,
	0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x10, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x22, 0x5f, 0x0a, 0x0f, 0x52, 0x65, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x61,
	0x77, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x61, 0x77, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x22, 0x0a, 0x0a, 0x41, 0x74, 0x6d, 0x52,
	0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x74, 0x6d, 0x50, 0x69, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x74, 0x6d, 0x50, 0x69, 0x22, 0xb9, 0x05, 0x0a,
	0x0b, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x69, 0x72, 0x12, 0x3b, 0x0a, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52,
	0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x07, 0x70, 0x69, 0x6e,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x70, 0x69,
	0x6e, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x3e, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x5f, 0x6c, 0x6f, 0x6e,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6f, 0x6e, 0x67, 0x57, 0x69, 0x74,
	0x68, 0x50, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x07, 0x6c, 0x61,
	0x74, 0x4c, 0x6f, 0x6e, 0x67, 0x12, 0x27, 0x0a, 0x0e, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x0d, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x21,
	0x0a, 0x0b, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x34, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x48, 0x00, 0x52, 0x08, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x1d, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x24,
	0x0a, 0x03, 0x61, 0x74, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x41, 0x74, 0x6d, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52,
	0x03, 0x61, 0x74, 0x6d, 0x12, 0x1c, 0x0a, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x10, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0f,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x11,
	0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x49,
	0x50, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48, 0x00,
	0x52, 0x0f, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x3c, 0x0a, 0x0e, 0x72, 0x69, 0x73, 0x6b, 0x79, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x52, 0x69, 0x73, 0x6b, 0x79, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x48, 0x00,
	0x52, 0x0d, 0x72, 0x69, 0x73, 0x6b, 0x79, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x42,
	0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xed, 0x01, 0x0a, 0x0f, 0x49, 0x50, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x55, 0x0a, 0x0f,
	0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x49, 0x50, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x49, 0x50, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x22, 0x64, 0x0a, 0x0d, 0x49, 0x50, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x50, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53,
	0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x50, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45,
	0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x50, 0x56, 0x34, 0x10, 0x01, 0x12, 0x18,
	0x0a, 0x14, 0x49, 0x50, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x49, 0x50, 0x56, 0x36, 0x10, 0x02, 0x22, 0x3d, 0x0a, 0x0d, 0x52, 0x69, 0x73, 0x6b,
	0x79, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x0d, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x2a, 0xac, 0x04, 0x0a, 0x0f, 0x52, 0x65, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x1c, 0x52,
	0x45, 0x44, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a,
	0x21, 0x52, 0x45, 0x44, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x49, 0x4e, 0x43, 0x4f,
	0x44, 0x45, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x45, 0x44, 0x4c, 0x49, 0x53, 0x54, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4c, 0x41, 0x54, 0x4c, 0x4f, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x52,
	0x45, 0x44, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x48, 0x41, 0x53, 0x48, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x10, 0x03,
	0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x44, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x49, 0x4e, 0x43, 0x4f, 0x44, 0x45,
	0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x44, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x4f,
	0x44, 0x45, 0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x44, 0x4c, 0x49, 0x53, 0x54, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10,
	0x06, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x44, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42,
	0x45, 0x52, 0x10, 0x07, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x44, 0x4c, 0x49, 0x53, 0x54, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x08,
	0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x44, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x44, 0x10, 0x09,
	0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x44, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x54, 0x4d, 0x10, 0x0a, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45,
	0x44, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d,
	0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x53, 0x10, 0x0b, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x45,
	0x44, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f,
	0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10,
	0x0c, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x44, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x0d, 0x12, 0x20, 0x0a,
	0x1c, 0x52, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x49, 0x50, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0x0e, 0x12,
	0x25, 0x0a, 0x21, 0x52, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x59, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48,
	0x41, 0x4e, 0x54, 0x53, 0x10, 0x0f, 0x2a, 0x65, 0x0a, 0x12, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x1a, 0x0a, 0x16,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10,
	0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x10,
	0x03, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0x04, 0x2a, 0x9e, 0x01,
	0x0a, 0x0f, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x46, 0x55, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c,
	0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x53, 0x43, 0x49, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x50, 0x49, 0x50, 0x45, 0x4c, 0x49,
	0x4e, 0x45, 0x53, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x41, 0x4e,
	0x41, 0x4c, 0x59, 0x53, 0x54, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x03,
	0x12, 0x19, 0x0a, 0x15, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x54,
	0x5f, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x52,
	0x49, 0x53, 0x4b, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x10, 0x05, 0x42, 0x42,
	0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69,
	0x73, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_internal_redlist_proto_rawDescOnce sync.Once
	file_api_risk_internal_redlist_proto_rawDescData = file_api_risk_internal_redlist_proto_rawDesc
)

func file_api_risk_internal_redlist_proto_rawDescGZIP() []byte {
	file_api_risk_internal_redlist_proto_rawDescOnce.Do(func() {
		file_api_risk_internal_redlist_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_internal_redlist_proto_rawDescData)
	})
	return file_api_risk_internal_redlist_proto_rawDescData
}

var file_api_risk_internal_redlist_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_risk_internal_redlist_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_risk_internal_redlist_proto_goTypes = []interface{}{
	(RedListCategory)(0),                  // 0: risk.RedListCategory
	(RedListerFieldMask)(0),               // 1: risk.RedListerFieldMask
	(RedListerSource)(0),                  // 2: risk.RedListerSource
	(IPAddressDetail_IPAddressType)(0),    // 3: risk.IPAddressDetail.IPAddressType
	(*RedLister)(nil),                     // 4: risk.RedLister
	(*Metadata)(nil),                      // 5: risk.Metadata
	(*RedListerReason)(nil),               // 6: risk.RedListerReason
	(*AtmRedList)(nil),                    // 7: risk.AtmRedList
	(*RedListPair)(nil),                   // 8: risk.RedListPair
	(*IPAddressDetail)(nil),               // 9: risk.IPAddressDetail
	(*RiskyMerchant)(nil),                 // 10: risk.RiskyMerchant
	(*Metadata_ManualUpdateMetadata)(nil), // 11: risk.Metadata.ManualUpdateMetadata
	(*Metadata_AfuRedListMetadata)(nil),   // 12: risk.Metadata.AfuRedListMetadata
	(*Metadata_ManualReviewMetadata)(nil), // 13: risk.Metadata.ManualReviewMetadata
	nil,                                   // 14: risk.Metadata.AfuRedListMetadata.SuspectLevelFailureCountEntry
	(*timestamppb.Timestamp)(nil),         // 15: google.protobuf.Timestamp
	(*typesv2.LatLongWithPrecision)(nil),  // 16: api.typesv2.LatLongWithPrecision
	(*common.Device)(nil),                 // 17: api.typesv2.common.Device
	(*common.PhoneNumber)(nil),            // 18: api.typesv2.common.PhoneNumber
}
var file_api_risk_internal_redlist_proto_depIdxs = []int32{
	8,  // 0: risk.RedLister.key:type_name -> risk.RedListPair
	6,  // 1: risk.RedLister.reason:type_name -> risk.RedListerReason
	5,  // 2: risk.RedLister.metadata:type_name -> risk.Metadata
	15, // 3: risk.RedLister.created_at:type_name -> google.protobuf.Timestamp
	15, // 4: risk.RedLister.updated_at:type_name -> google.protobuf.Timestamp
	15, // 5: risk.RedLister.expires_at:type_name -> google.protobuf.Timestamp
	11, // 6: risk.Metadata.manual_update_metadata:type_name -> risk.Metadata.ManualUpdateMetadata
	12, // 7: risk.Metadata.afu_red_list_metadata:type_name -> risk.Metadata.AfuRedListMetadata
	13, // 8: risk.Metadata.manual_review_metata:type_name -> risk.Metadata.ManualReviewMetadata
	2,  // 9: risk.RedListerReason.source:type_name -> risk.RedListerSource
	0,  // 10: risk.RedListPair.category:type_name -> risk.RedListCategory
	16, // 11: risk.RedListPair.lat_long:type_name -> api.typesv2.LatLongWithPrecision
	17, // 12: risk.RedListPair.device:type_name -> api.typesv2.common.Device
	18, // 13: risk.RedListPair.phone_num:type_name -> api.typesv2.common.PhoneNumber
	7,  // 14: risk.RedListPair.atm:type_name -> risk.AtmRedList
	9,  // 15: risk.RedListPair.ip_address_detail:type_name -> risk.IPAddressDetail
	10, // 16: risk.RedListPair.risky_merchant:type_name -> risk.RiskyMerchant
	3,  // 17: risk.IPAddressDetail.ip_address_type:type_name -> risk.IPAddressDetail.IPAddressType
	14, // 18: risk.Metadata.AfuRedListMetadata.suspect_level_failure_count:type_name -> risk.Metadata.AfuRedListMetadata.SuspectLevelFailureCountEntry
	19, // [19:19] is the sub-list for method output_type
	19, // [19:19] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_api_risk_internal_redlist_proto_init() }
func file_api_risk_internal_redlist_proto_init() {
	if File_api_risk_internal_redlist_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_internal_redlist_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedLister); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_internal_redlist_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Metadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_internal_redlist_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedListerReason); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_internal_redlist_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtmRedList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_internal_redlist_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedListPair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_internal_redlist_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IPAddressDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_internal_redlist_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskyMerchant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_internal_redlist_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Metadata_ManualUpdateMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_internal_redlist_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Metadata_AfuRedListMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_internal_redlist_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Metadata_ManualReviewMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_internal_redlist_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*RedListPair_Pincode)(nil),
		(*RedListPair_LatLong)(nil),
		(*RedListPair_HashedContact)(nil),
		(*RedListPair_FiniteCode)(nil),
		(*RedListPair_Device)(nil),
		(*RedListPair_PhoneNum)(nil),
		(*RedListPair_Email)(nil),
		(*RedListPair_DeviceId)(nil),
		(*RedListPair_Atm)(nil),
		(*RedListPair_Merchant)(nil),
		(*RedListPair_OnboardingState)(nil),
		(*RedListPair_ActorId)(nil),
		(*RedListPair_IpAddressDetail)(nil),
		(*RedListPair_RiskyMerchant)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_internal_redlist_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_internal_redlist_proto_goTypes,
		DependencyIndexes: file_api_risk_internal_redlist_proto_depIdxs,
		EnumInfos:         file_api_risk_internal_redlist_proto_enumTypes,
		MessageInfos:      file_api_risk_internal_redlist_proto_msgTypes,
	}.Build()
	File_api_risk_internal_redlist_proto = out.File
	file_api_risk_internal_redlist_proto_rawDesc = nil
	file_api_risk_internal_redlist_proto_goTypes = nil
	file_api_risk_internal_redlist_proto_depIdxs = nil
}
