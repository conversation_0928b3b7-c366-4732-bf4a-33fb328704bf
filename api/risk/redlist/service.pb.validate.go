// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/redlist/service.proto

package redlist

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	risk "github.com/epifi/gamma/api/risk"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = risk.RedListCategory(0)
)

// Validate checks the field values on CheckRedListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckRedListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckRedListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckRedListRequestMultiError, or nil if none found.
func (m *CheckRedListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckRedListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetKeys() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CheckRedListRequestValidationError{
						field:  fmt.Sprintf("Keys[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CheckRedListRequestValidationError{
						field:  fmt.Sprintf("Keys[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CheckRedListRequestValidationError{
					field:  fmt.Sprintf("Keys[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CheckRedListRequestMultiError(errors)
	}

	return nil
}

// CheckRedListRequestMultiError is an error wrapping multiple validation
// errors returned by CheckRedListRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckRedListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckRedListRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckRedListRequestMultiError) AllErrors() []error { return m }

// CheckRedListRequestValidationError is the validation error returned by
// CheckRedListRequest.Validate if the designated constraints aren't met.
type CheckRedListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckRedListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckRedListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckRedListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckRedListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckRedListRequestValidationError) ErrorName() string {
	return "CheckRedListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckRedListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckRedListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckRedListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckRedListRequestValidationError{}

// Validate checks the field values on CheckRedListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckRedListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckRedListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckRedListResponseMultiError, or nil if none found.
func (m *CheckRedListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckRedListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckRedListResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckRedListResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckRedListResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMembers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CheckRedListResponseValidationError{
						field:  fmt.Sprintf("Members[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CheckRedListResponseValidationError{
						field:  fmt.Sprintf("Members[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CheckRedListResponseValidationError{
					field:  fmt.Sprintf("Members[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CheckRedListResponseMultiError(errors)
	}

	return nil
}

// CheckRedListResponseMultiError is an error wrapping multiple validation
// errors returned by CheckRedListResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckRedListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckRedListResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckRedListResponseMultiError) AllErrors() []error { return m }

// CheckRedListResponseValidationError is the validation error returned by
// CheckRedListResponse.Validate if the designated constraints aren't met.
type CheckRedListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckRedListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckRedListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckRedListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckRedListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckRedListResponseValidationError) ErrorName() string {
	return "CheckRedListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckRedListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckRedListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckRedListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckRedListResponseValidationError{}

// Validate checks the field values on UpsertRedListerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpsertRedListerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpsertRedListerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpsertRedListerRequestMultiError, or nil if none found.
func (m *UpsertRedListerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpsertRedListerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetMembers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpsertRedListerRequestValidationError{
						field:  fmt.Sprintf("Members[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpsertRedListerRequestValidationError{
						field:  fmt.Sprintf("Members[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpsertRedListerRequestValidationError{
					field:  fmt.Sprintf("Members[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpsertRedListerRequestMultiError(errors)
	}

	return nil
}

// UpsertRedListerRequestMultiError is an error wrapping multiple validation
// errors returned by UpsertRedListerRequest.ValidateAll() if the designated
// constraints aren't met.
type UpsertRedListerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpsertRedListerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpsertRedListerRequestMultiError) AllErrors() []error { return m }

// UpsertRedListerRequestValidationError is the validation error returned by
// UpsertRedListerRequest.Validate if the designated constraints aren't met.
type UpsertRedListerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpsertRedListerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpsertRedListerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpsertRedListerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpsertRedListerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpsertRedListerRequestValidationError) ErrorName() string {
	return "UpsertRedListerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpsertRedListerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpsertRedListerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpsertRedListerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpsertRedListerRequestValidationError{}

// Validate checks the field values on UpsertRedListerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpsertRedListerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpsertRedListerResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpsertRedListerResponseMultiError, or nil if none found.
func (m *UpsertRedListerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpsertRedListerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpsertRedListerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpsertRedListerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpsertRedListerResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMembers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpsertRedListerResponseValidationError{
						field:  fmt.Sprintf("Members[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpsertRedListerResponseValidationError{
						field:  fmt.Sprintf("Members[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpsertRedListerResponseValidationError{
					field:  fmt.Sprintf("Members[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpsertRedListerResponseMultiError(errors)
	}

	return nil
}

// UpsertRedListerResponseMultiError is an error wrapping multiple validation
// errors returned by UpsertRedListerResponse.ValidateAll() if the designated
// constraints aren't met.
type UpsertRedListerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpsertRedListerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpsertRedListerResponseMultiError) AllErrors() []error { return m }

// UpsertRedListerResponseValidationError is the validation error returned by
// UpsertRedListerResponse.Validate if the designated constraints aren't met.
type UpsertRedListerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpsertRedListerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpsertRedListerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpsertRedListerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpsertRedListerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpsertRedListerResponseValidationError) ErrorName() string {
	return "UpsertRedListerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpsertRedListerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpsertRedListerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpsertRedListerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpsertRedListerResponseValidationError{}

// Validate checks the field values on RemoveRedListerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemoveRedListerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveRedListerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemoveRedListerRequestMultiError, or nil if none found.
func (m *RemoveRedListerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveRedListerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetMembers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RemoveRedListerRequestValidationError{
						field:  fmt.Sprintf("Members[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RemoveRedListerRequestValidationError{
						field:  fmt.Sprintf("Members[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RemoveRedListerRequestValidationError{
					field:  fmt.Sprintf("Members[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetPairs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RemoveRedListerRequestValidationError{
						field:  fmt.Sprintf("Pairs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RemoveRedListerRequestValidationError{
						field:  fmt.Sprintf("Pairs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RemoveRedListerRequestValidationError{
					field:  fmt.Sprintf("Pairs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RemoveRedListerRequestMultiError(errors)
	}

	return nil
}

// RemoveRedListerRequestMultiError is an error wrapping multiple validation
// errors returned by RemoveRedListerRequest.ValidateAll() if the designated
// constraints aren't met.
type RemoveRedListerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveRedListerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveRedListerRequestMultiError) AllErrors() []error { return m }

// RemoveRedListerRequestValidationError is the validation error returned by
// RemoveRedListerRequest.Validate if the designated constraints aren't met.
type RemoveRedListerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveRedListerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveRedListerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveRedListerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveRedListerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveRedListerRequestValidationError) ErrorName() string {
	return "RemoveRedListerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveRedListerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveRedListerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveRedListerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveRedListerRequestValidationError{}

// Validate checks the field values on RemoveRedListerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemoveRedListerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveRedListerResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemoveRedListerResponseMultiError, or nil if none found.
func (m *RemoveRedListerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveRedListerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RemoveRedListerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RemoveRedListerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RemoveRedListerResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RemoveRedListerResponseMultiError(errors)
	}

	return nil
}

// RemoveRedListerResponseMultiError is an error wrapping multiple validation
// errors returned by RemoveRedListerResponse.ValidateAll() if the designated
// constraints aren't met.
type RemoveRedListerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveRedListerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveRedListerResponseMultiError) AllErrors() []error { return m }

// RemoveRedListerResponseValidationError is the validation error returned by
// RemoveRedListerResponse.Validate if the designated constraints aren't met.
type RemoveRedListerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveRedListerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveRedListerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveRedListerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveRedListerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveRedListerResponseValidationError) ErrorName() string {
	return "RemoveRedListerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveRedListerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveRedListerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveRedListerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveRedListerResponseValidationError{}

// Validate checks the field values on ProcessRedListMemberRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessRedListMemberRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessRedListMemberRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessRedListMemberRequestMultiError, or nil if none found.
func (m *ProcessRedListMemberRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessRedListMemberRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Member

	// no validation rules for CsvData

	// no validation rules for UpdatedByEmail

	if len(errors) > 0 {
		return ProcessRedListMemberRequestMultiError(errors)
	}

	return nil
}

// ProcessRedListMemberRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessRedListMemberRequest.ValidateAll() if
// the designated constraints aren't met.
type ProcessRedListMemberRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessRedListMemberRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessRedListMemberRequestMultiError) AllErrors() []error { return m }

// ProcessRedListMemberRequestValidationError is the validation error returned
// by ProcessRedListMemberRequest.Validate if the designated constraints
// aren't met.
type ProcessRedListMemberRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessRedListMemberRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessRedListMemberRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessRedListMemberRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessRedListMemberRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessRedListMemberRequestValidationError) ErrorName() string {
	return "ProcessRedListMemberRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessRedListMemberRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessRedListMemberRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessRedListMemberRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessRedListMemberRequestValidationError{}

// Validate checks the field values on ProcessRedListMemberResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessRedListMemberResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessRedListMemberResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessRedListMemberResponseMultiError, or nil if none found.
func (m *ProcessRedListMemberResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessRedListMemberResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessRedListMemberResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessRedListMemberResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessRedListMemberResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessRedListMemberResponseMultiError(errors)
	}

	return nil
}

// ProcessRedListMemberResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessRedListMemberResponse.ValidateAll() if
// the designated constraints aren't met.
type ProcessRedListMemberResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessRedListMemberResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessRedListMemberResponseMultiError) AllErrors() []error { return m }

// ProcessRedListMemberResponseValidationError is the validation error returned
// by ProcessRedListMemberResponse.Validate if the designated constraints
// aren't met.
type ProcessRedListMemberResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessRedListMemberResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessRedListMemberResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessRedListMemberResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessRedListMemberResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessRedListMemberResponseValidationError) ErrorName() string {
	return "ProcessRedListMemberResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessRedListMemberResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessRedListMemberResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessRedListMemberResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessRedListMemberResponseValidationError{}
