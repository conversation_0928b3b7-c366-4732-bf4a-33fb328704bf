// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/redlist/service.proto

package redlist

import (
	rpc "github.com/epifi/be-common/api/rpc"
	risk "github.com/epifi/gamma/api/risk"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CheckRedListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keys []*risk.RedListPair `protobuf:"bytes,1,rep,name=keys,proto3" json:"keys,omitempty"`
}

func (x *CheckRedListRequest) Reset() {
	*x = CheckRedListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_redlist_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckRedListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckRedListRequest) ProtoMessage() {}

func (x *CheckRedListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_redlist_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckRedListRequest.ProtoReflect.Descriptor instead.
func (*CheckRedListRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_redlist_service_proto_rawDescGZIP(), []int{0}
}

func (x *CheckRedListRequest) GetKeys() []*risk.RedListPair {
	if x != nil {
		return x.Keys
	}
	return nil
}

type CheckRedListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Members []*risk.RedLister `protobuf:"bytes,2,rep,name=members,proto3" json:"members,omitempty"`
}

func (x *CheckRedListResponse) Reset() {
	*x = CheckRedListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_redlist_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckRedListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckRedListResponse) ProtoMessage() {}

func (x *CheckRedListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_redlist_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckRedListResponse.ProtoReflect.Descriptor instead.
func (*CheckRedListResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_redlist_service_proto_rawDescGZIP(), []int{1}
}

func (x *CheckRedListResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckRedListResponse) GetMembers() []*risk.RedLister {
	if x != nil {
		return x.Members
	}
	return nil
}

type UpsertRedListerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Members []*risk.RedLister `protobuf:"bytes,1,rep,name=members,proto3" json:"members,omitempty"`
}

func (x *UpsertRedListerRequest) Reset() {
	*x = UpsertRedListerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_redlist_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertRedListerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertRedListerRequest) ProtoMessage() {}

func (x *UpsertRedListerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_redlist_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertRedListerRequest.ProtoReflect.Descriptor instead.
func (*UpsertRedListerRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_redlist_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpsertRedListerRequest) GetMembers() []*risk.RedLister {
	if x != nil {
		return x.Members
	}
	return nil
}

type UpsertRedListerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Members []*risk.RedLister `protobuf:"bytes,2,rep,name=members,proto3" json:"members,omitempty"`
}

func (x *UpsertRedListerResponse) Reset() {
	*x = UpsertRedListerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_redlist_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertRedListerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertRedListerResponse) ProtoMessage() {}

func (x *UpsertRedListerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_redlist_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertRedListerResponse.ProtoReflect.Descriptor instead.
func (*UpsertRedListerResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_redlist_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpsertRedListerResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpsertRedListerResponse) GetMembers() []*risk.RedLister {
	if x != nil {
		return x.Members
	}
	return nil
}

type RemoveRedListerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/risk/redlist/service.proto.
	Members []*risk.RedLister   `protobuf:"bytes,1,rep,name=members,proto3" json:"members,omitempty"`
	Pairs   []*risk.RedListPair `protobuf:"bytes,2,rep,name=pairs,proto3" json:"pairs,omitempty"`
}

func (x *RemoveRedListerRequest) Reset() {
	*x = RemoveRedListerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_redlist_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveRedListerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveRedListerRequest) ProtoMessage() {}

func (x *RemoveRedListerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_redlist_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveRedListerRequest.ProtoReflect.Descriptor instead.
func (*RemoveRedListerRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_redlist_service_proto_rawDescGZIP(), []int{4}
}

// Deprecated: Marked as deprecated in api/risk/redlist/service.proto.
func (x *RemoveRedListerRequest) GetMembers() []*risk.RedLister {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *RemoveRedListerRequest) GetPairs() []*risk.RedListPair {
	if x != nil {
		return x.Pairs
	}
	return nil
}

type RemoveRedListerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *RemoveRedListerResponse) Reset() {
	*x = RemoveRedListerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_redlist_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveRedListerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveRedListerResponse) ProtoMessage() {}

func (x *RemoveRedListerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_redlist_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveRedListerResponse.ProtoReflect.Descriptor instead.
func (*RemoveRedListerResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_redlist_service_proto_rawDescGZIP(), []int{5}
}

func (x *RemoveRedListerResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ProcessRedListMemberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Member         risk.RedListCategory `protobuf:"varint,1,opt,name=member,proto3,enum=risk.RedListCategory" json:"member,omitempty"`
	CsvData        []byte               `protobuf:"bytes,2,opt,name=csv_data,json=csvData,proto3" json:"csv_data,omitempty"`
	UpdatedByEmail string               `protobuf:"bytes,3,opt,name=updated_by_email,json=updatedByEmail,proto3" json:"updated_by_email,omitempty"`
}

func (x *ProcessRedListMemberRequest) Reset() {
	*x = ProcessRedListMemberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_redlist_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessRedListMemberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessRedListMemberRequest) ProtoMessage() {}

func (x *ProcessRedListMemberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_redlist_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessRedListMemberRequest.ProtoReflect.Descriptor instead.
func (*ProcessRedListMemberRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_redlist_service_proto_rawDescGZIP(), []int{6}
}

func (x *ProcessRedListMemberRequest) GetMember() risk.RedListCategory {
	if x != nil {
		return x.Member
	}
	return risk.RedListCategory(0)
}

func (x *ProcessRedListMemberRequest) GetCsvData() []byte {
	if x != nil {
		return x.CsvData
	}
	return nil
}

func (x *ProcessRedListMemberRequest) GetUpdatedByEmail() string {
	if x != nil {
		return x.UpdatedByEmail
	}
	return ""
}

type ProcessRedListMemberResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ProcessRedListMemberResponse) Reset() {
	*x = ProcessRedListMemberResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_redlist_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessRedListMemberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessRedListMemberResponse) ProtoMessage() {}

func (x *ProcessRedListMemberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_redlist_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessRedListMemberResponse.ProtoReflect.Descriptor instead.
func (*ProcessRedListMemberResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_redlist_service_proto_rawDescGZIP(), []int{7}
}

func (x *ProcessRedListMemberResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_api_risk_redlist_service_proto protoreflect.FileDescriptor

var file_api_risk_redlist_service_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x72, 0x65, 0x64, 0x6c, 0x69,
	0x73, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x07, 0x72, 0x65, 0x64, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x72, 0x65, 0x64,
	0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x3c, 0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x69, 0x72, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x22, 0x66,
	0x0a, 0x14, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x07, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x07, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x22, 0x43, 0x0a, 0x16, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74,
	0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x29, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x22, 0x69, 0x0a, 0x17, 0x55,
	0x70, 0x73, 0x65, 0x72, 0x74, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x07, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x07, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x22, 0x70, 0x0a, 0x16, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2d, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12,
	0x27, 0x0a, 0x05, 0x70, 0x61, 0x69, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x69,
	0x72, 0x52, 0x05, 0x70, 0x61, 0x69, 0x72, 0x73, 0x22, 0x3e, 0x0a, 0x17, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x91, 0x01, 0x0a, 0x1b, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52,
	0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x73, 0x76, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x63, 0x73, 0x76, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x28, 0x0a, 0x10, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x43, 0x0a, 0x1c,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x32, 0xe7, 0x02, 0x0a, 0x07, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4b, 0x0a,
	0x0c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c, 0x2e,
	0x72, 0x65, 0x64, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x72, 0x65,
	0x64, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0f, 0x55, 0x70,
	0x73, 0x65, 0x72, 0x74, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x2e,
	0x72, 0x65, 0x64, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x52, 0x65,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20,
	0x2e, 0x72, 0x65, 0x64, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x52,
	0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x54, 0x0a, 0x0f, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x12, 0x1f, 0x2e, 0x72, 0x65, 0x64, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x72, 0x65, 0x64, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x14, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x24,
	0x2e, 0x72, 0x65, 0x64, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x72, 0x65, 0x64, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x52, 0x0a, 0x27, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x72,
	0x65, 0x64, 0x6c, 0x69, 0x73, 0x74, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x72, 0x65, 0x64, 0x6c, 0x69, 0x73, 0x74, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_redlist_service_proto_rawDescOnce sync.Once
	file_api_risk_redlist_service_proto_rawDescData = file_api_risk_redlist_service_proto_rawDesc
)

func file_api_risk_redlist_service_proto_rawDescGZIP() []byte {
	file_api_risk_redlist_service_proto_rawDescOnce.Do(func() {
		file_api_risk_redlist_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_redlist_service_proto_rawDescData)
	})
	return file_api_risk_redlist_service_proto_rawDescData
}

var file_api_risk_redlist_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_risk_redlist_service_proto_goTypes = []interface{}{
	(*CheckRedListRequest)(nil),          // 0: redlist.CheckRedListRequest
	(*CheckRedListResponse)(nil),         // 1: redlist.CheckRedListResponse
	(*UpsertRedListerRequest)(nil),       // 2: redlist.UpsertRedListerRequest
	(*UpsertRedListerResponse)(nil),      // 3: redlist.UpsertRedListerResponse
	(*RemoveRedListerRequest)(nil),       // 4: redlist.RemoveRedListerRequest
	(*RemoveRedListerResponse)(nil),      // 5: redlist.RemoveRedListerResponse
	(*ProcessRedListMemberRequest)(nil),  // 6: redlist.ProcessRedListMemberRequest
	(*ProcessRedListMemberResponse)(nil), // 7: redlist.ProcessRedListMemberResponse
	(*risk.RedListPair)(nil),             // 8: risk.RedListPair
	(*rpc.Status)(nil),                   // 9: rpc.Status
	(*risk.RedLister)(nil),               // 10: risk.RedLister
	(risk.RedListCategory)(0),            // 11: risk.RedListCategory
}
var file_api_risk_redlist_service_proto_depIdxs = []int32{
	8,  // 0: redlist.CheckRedListRequest.keys:type_name -> risk.RedListPair
	9,  // 1: redlist.CheckRedListResponse.status:type_name -> rpc.Status
	10, // 2: redlist.CheckRedListResponse.members:type_name -> risk.RedLister
	10, // 3: redlist.UpsertRedListerRequest.members:type_name -> risk.RedLister
	9,  // 4: redlist.UpsertRedListerResponse.status:type_name -> rpc.Status
	10, // 5: redlist.UpsertRedListerResponse.members:type_name -> risk.RedLister
	10, // 6: redlist.RemoveRedListerRequest.members:type_name -> risk.RedLister
	8,  // 7: redlist.RemoveRedListerRequest.pairs:type_name -> risk.RedListPair
	9,  // 8: redlist.RemoveRedListerResponse.status:type_name -> rpc.Status
	11, // 9: redlist.ProcessRedListMemberRequest.member:type_name -> risk.RedListCategory
	9,  // 10: redlist.ProcessRedListMemberResponse.status:type_name -> rpc.Status
	0,  // 11: redlist.RedList.CheckRedList:input_type -> redlist.CheckRedListRequest
	2,  // 12: redlist.RedList.UpsertRedLister:input_type -> redlist.UpsertRedListerRequest
	4,  // 13: redlist.RedList.RemoveRedLister:input_type -> redlist.RemoveRedListerRequest
	6,  // 14: redlist.RedList.ProcessRedListMember:input_type -> redlist.ProcessRedListMemberRequest
	1,  // 15: redlist.RedList.CheckRedList:output_type -> redlist.CheckRedListResponse
	3,  // 16: redlist.RedList.UpsertRedLister:output_type -> redlist.UpsertRedListerResponse
	5,  // 17: redlist.RedList.RemoveRedLister:output_type -> redlist.RemoveRedListerResponse
	7,  // 18: redlist.RedList.ProcessRedListMember:output_type -> redlist.ProcessRedListMemberResponse
	15, // [15:19] is the sub-list for method output_type
	11, // [11:15] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_api_risk_redlist_service_proto_init() }
func file_api_risk_redlist_service_proto_init() {
	if File_api_risk_redlist_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_redlist_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckRedListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_redlist_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckRedListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_redlist_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertRedListerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_redlist_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertRedListerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_redlist_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveRedListerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_redlist_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveRedListerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_redlist_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessRedListMemberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_redlist_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessRedListMemberResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_redlist_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_risk_redlist_service_proto_goTypes,
		DependencyIndexes: file_api_risk_redlist_service_proto_depIdxs,
		MessageInfos:      file_api_risk_redlist_service_proto_msgTypes,
	}.Build()
	File_api_risk_redlist_service_proto = out.File
	file_api_risk_redlist_service_proto_rawDesc = nil
	file_api_risk_redlist_service_proto_goTypes = nil
	file_api_risk_redlist_service_proto_depIdxs = nil
}
