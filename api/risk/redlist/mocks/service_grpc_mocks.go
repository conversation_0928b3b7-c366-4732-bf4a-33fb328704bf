// Code generated by MockGen. DO NOT EDIT.
// Source: api/risk/redlist/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	redlist "github.com/epifi/gamma/api/risk/redlist"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockRedListClient is a mock of RedListClient interface.
type MockRedListClient struct {
	ctrl     *gomock.Controller
	recorder *MockRedListClientMockRecorder
}

// MockRedListClientMockRecorder is the mock recorder for MockRedListClient.
type MockRedListClientMockRecorder struct {
	mock *MockRedListClient
}

// NewMockRedListClient creates a new mock instance.
func NewMockRedListClient(ctrl *gomock.Controller) *MockRedListClient {
	mock := &MockRedListClient{ctrl: ctrl}
	mock.recorder = &MockRedListClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRedListClient) EXPECT() *MockRedListClientMockRecorder {
	return m.recorder
}

// CheckRedList mocks base method.
func (m *MockRedListClient) CheckRedList(ctx context.Context, in *redlist.CheckRedListRequest, opts ...grpc.CallOption) (*redlist.CheckRedListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckRedList", varargs...)
	ret0, _ := ret[0].(*redlist.CheckRedListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckRedList indicates an expected call of CheckRedList.
func (mr *MockRedListClientMockRecorder) CheckRedList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRedList", reflect.TypeOf((*MockRedListClient)(nil).CheckRedList), varargs...)
}

// ProcessRedListMember mocks base method.
func (m *MockRedListClient) ProcessRedListMember(ctx context.Context, in *redlist.ProcessRedListMemberRequest, opts ...grpc.CallOption) (*redlist.ProcessRedListMemberResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessRedListMember", varargs...)
	ret0, _ := ret[0].(*redlist.ProcessRedListMemberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessRedListMember indicates an expected call of ProcessRedListMember.
func (mr *MockRedListClientMockRecorder) ProcessRedListMember(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessRedListMember", reflect.TypeOf((*MockRedListClient)(nil).ProcessRedListMember), varargs...)
}

// RemoveRedLister mocks base method.
func (m *MockRedListClient) RemoveRedLister(ctx context.Context, in *redlist.RemoveRedListerRequest, opts ...grpc.CallOption) (*redlist.RemoveRedListerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RemoveRedLister", varargs...)
	ret0, _ := ret[0].(*redlist.RemoveRedListerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveRedLister indicates an expected call of RemoveRedLister.
func (mr *MockRedListClientMockRecorder) RemoveRedLister(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveRedLister", reflect.TypeOf((*MockRedListClient)(nil).RemoveRedLister), varargs...)
}

// UpsertRedLister mocks base method.
func (m *MockRedListClient) UpsertRedLister(ctx context.Context, in *redlist.UpsertRedListerRequest, opts ...grpc.CallOption) (*redlist.UpsertRedListerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertRedLister", varargs...)
	ret0, _ := ret[0].(*redlist.UpsertRedListerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertRedLister indicates an expected call of UpsertRedLister.
func (mr *MockRedListClientMockRecorder) UpsertRedLister(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertRedLister", reflect.TypeOf((*MockRedListClient)(nil).UpsertRedLister), varargs...)
}

// MockRedListServer is a mock of RedListServer interface.
type MockRedListServer struct {
	ctrl     *gomock.Controller
	recorder *MockRedListServerMockRecorder
}

// MockRedListServerMockRecorder is the mock recorder for MockRedListServer.
type MockRedListServerMockRecorder struct {
	mock *MockRedListServer
}

// NewMockRedListServer creates a new mock instance.
func NewMockRedListServer(ctrl *gomock.Controller) *MockRedListServer {
	mock := &MockRedListServer{ctrl: ctrl}
	mock.recorder = &MockRedListServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRedListServer) EXPECT() *MockRedListServerMockRecorder {
	return m.recorder
}

// CheckRedList mocks base method.
func (m *MockRedListServer) CheckRedList(arg0 context.Context, arg1 *redlist.CheckRedListRequest) (*redlist.CheckRedListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRedList", arg0, arg1)
	ret0, _ := ret[0].(*redlist.CheckRedListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckRedList indicates an expected call of CheckRedList.
func (mr *MockRedListServerMockRecorder) CheckRedList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRedList", reflect.TypeOf((*MockRedListServer)(nil).CheckRedList), arg0, arg1)
}

// ProcessRedListMember mocks base method.
func (m *MockRedListServer) ProcessRedListMember(arg0 context.Context, arg1 *redlist.ProcessRedListMemberRequest) (*redlist.ProcessRedListMemberResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessRedListMember", arg0, arg1)
	ret0, _ := ret[0].(*redlist.ProcessRedListMemberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessRedListMember indicates an expected call of ProcessRedListMember.
func (mr *MockRedListServerMockRecorder) ProcessRedListMember(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessRedListMember", reflect.TypeOf((*MockRedListServer)(nil).ProcessRedListMember), arg0, arg1)
}

// RemoveRedLister mocks base method.
func (m *MockRedListServer) RemoveRedLister(arg0 context.Context, arg1 *redlist.RemoveRedListerRequest) (*redlist.RemoveRedListerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveRedLister", arg0, arg1)
	ret0, _ := ret[0].(*redlist.RemoveRedListerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveRedLister indicates an expected call of RemoveRedLister.
func (mr *MockRedListServerMockRecorder) RemoveRedLister(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveRedLister", reflect.TypeOf((*MockRedListServer)(nil).RemoveRedLister), arg0, arg1)
}

// UpsertRedLister mocks base method.
func (m *MockRedListServer) UpsertRedLister(arg0 context.Context, arg1 *redlist.UpsertRedListerRequest) (*redlist.UpsertRedListerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertRedLister", arg0, arg1)
	ret0, _ := ret[0].(*redlist.UpsertRedListerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertRedLister indicates an expected call of UpsertRedLister.
func (mr *MockRedListServerMockRecorder) UpsertRedLister(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertRedLister", reflect.TypeOf((*MockRedListServer)(nil).UpsertRedLister), arg0, arg1)
}

// MockUnsafeRedListServer is a mock of UnsafeRedListServer interface.
type MockUnsafeRedListServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeRedListServerMockRecorder
}

// MockUnsafeRedListServerMockRecorder is the mock recorder for MockUnsafeRedListServer.
type MockUnsafeRedListServerMockRecorder struct {
	mock *MockUnsafeRedListServer
}

// NewMockUnsafeRedListServer creates a new mock instance.
func NewMockUnsafeRedListServer(ctrl *gomock.Controller) *MockUnsafeRedListServer {
	mock := &MockUnsafeRedListServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeRedListServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeRedListServer) EXPECT() *MockUnsafeRedListServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedRedListServer mocks base method.
func (m *MockUnsafeRedListServer) mustEmbedUnimplementedRedListServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedRedListServer")
}

// mustEmbedUnimplementedRedListServer indicates an expected call of mustEmbedUnimplementedRedListServer.
func (mr *MockUnsafeRedListServerMockRecorder) mustEmbedUnimplementedRedListServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedRedListServer", reflect.TypeOf((*MockUnsafeRedListServer)(nil).mustEmbedUnimplementedRedListServer))
}
