// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/risk/redlist/service.proto

package redlist

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RedList_CheckRedList_FullMethodName         = "/redlist.RedList/CheckRedList"
	RedList_UpsertRedLister_FullMethodName      = "/redlist.RedList/UpsertRedLister"
	RedList_RemoveRedLister_FullMethodName      = "/redlist.RedList/RemoveRedLister"
	RedList_ProcessRedListMember_FullMethodName = "/redlist.RedList/ProcessRedListMember"
)

// RedListClient is the client API for RedList service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RedListClient interface {
	// GetRedListRisk RPC is used to fetch the risk members based on categories and values
	CheckRedList(ctx context.Context, in *CheckRedListRequest, opts ...grpc.CallOption) (*CheckRedListResponse, error)
	UpsertRedLister(ctx context.Context, in *UpsertRedListerRequest, opts ...grpc.CallOption) (*UpsertRedListerResponse, error)
	RemoveRedLister(ctx context.Context, in *RemoveRedListerRequest, opts ...grpc.CallOption) (*RemoveRedListerResponse, error)
	ProcessRedListMember(ctx context.Context, in *ProcessRedListMemberRequest, opts ...grpc.CallOption) (*ProcessRedListMemberResponse, error)
}

type redListClient struct {
	cc grpc.ClientConnInterface
}

func NewRedListClient(cc grpc.ClientConnInterface) RedListClient {
	return &redListClient{cc}
}

func (c *redListClient) CheckRedList(ctx context.Context, in *CheckRedListRequest, opts ...grpc.CallOption) (*CheckRedListResponse, error) {
	out := new(CheckRedListResponse)
	err := c.cc.Invoke(ctx, RedList_CheckRedList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *redListClient) UpsertRedLister(ctx context.Context, in *UpsertRedListerRequest, opts ...grpc.CallOption) (*UpsertRedListerResponse, error) {
	out := new(UpsertRedListerResponse)
	err := c.cc.Invoke(ctx, RedList_UpsertRedLister_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *redListClient) RemoveRedLister(ctx context.Context, in *RemoveRedListerRequest, opts ...grpc.CallOption) (*RemoveRedListerResponse, error) {
	out := new(RemoveRedListerResponse)
	err := c.cc.Invoke(ctx, RedList_RemoveRedLister_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *redListClient) ProcessRedListMember(ctx context.Context, in *ProcessRedListMemberRequest, opts ...grpc.CallOption) (*ProcessRedListMemberResponse, error) {
	out := new(ProcessRedListMemberResponse)
	err := c.cc.Invoke(ctx, RedList_ProcessRedListMember_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RedListServer is the server API for RedList service.
// All implementations should embed UnimplementedRedListServer
// for forward compatibility
type RedListServer interface {
	// GetRedListRisk RPC is used to fetch the risk members based on categories and values
	CheckRedList(context.Context, *CheckRedListRequest) (*CheckRedListResponse, error)
	UpsertRedLister(context.Context, *UpsertRedListerRequest) (*UpsertRedListerResponse, error)
	RemoveRedLister(context.Context, *RemoveRedListerRequest) (*RemoveRedListerResponse, error)
	ProcessRedListMember(context.Context, *ProcessRedListMemberRequest) (*ProcessRedListMemberResponse, error)
}

// UnimplementedRedListServer should be embedded to have forward compatible implementations.
type UnimplementedRedListServer struct {
}

func (UnimplementedRedListServer) CheckRedList(context.Context, *CheckRedListRequest) (*CheckRedListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckRedList not implemented")
}
func (UnimplementedRedListServer) UpsertRedLister(context.Context, *UpsertRedListerRequest) (*UpsertRedListerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertRedLister not implemented")
}
func (UnimplementedRedListServer) RemoveRedLister(context.Context, *RemoveRedListerRequest) (*RemoveRedListerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveRedLister not implemented")
}
func (UnimplementedRedListServer) ProcessRedListMember(context.Context, *ProcessRedListMemberRequest) (*ProcessRedListMemberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessRedListMember not implemented")
}

// UnsafeRedListServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RedListServer will
// result in compilation errors.
type UnsafeRedListServer interface {
	mustEmbedUnimplementedRedListServer()
}

func RegisterRedListServer(s grpc.ServiceRegistrar, srv RedListServer) {
	s.RegisterService(&RedList_ServiceDesc, srv)
}

func _RedList_CheckRedList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckRedListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RedListServer).CheckRedList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RedList_CheckRedList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RedListServer).CheckRedList(ctx, req.(*CheckRedListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RedList_UpsertRedLister_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertRedListerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RedListServer).UpsertRedLister(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RedList_UpsertRedLister_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RedListServer).UpsertRedLister(ctx, req.(*UpsertRedListerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RedList_RemoveRedLister_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveRedListerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RedListServer).RemoveRedLister(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RedList_RemoveRedLister_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RedListServer).RemoveRedLister(ctx, req.(*RemoveRedListerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RedList_ProcessRedListMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessRedListMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RedListServer).ProcessRedListMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RedList_ProcessRedListMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RedListServer).ProcessRedListMember(ctx, req.(*ProcessRedListMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RedList_ServiceDesc is the grpc.ServiceDesc for RedList service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RedList_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "redlist.RedList",
	HandlerType: (*RedListServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckRedList",
			Handler:    _RedList_CheckRedList_Handler,
		},
		{
			MethodName: "UpsertRedLister",
			Handler:    _RedList_UpsertRedLister_Handler,
		},
		{
			MethodName: "RemoveRedLister",
			Handler:    _RedList_RemoveRedLister_Handler,
		},
		{
			MethodName: "ProcessRedListMember",
			Handler:    _RedList_ProcessRedListMember_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/risk/redlist/service.proto",
}
