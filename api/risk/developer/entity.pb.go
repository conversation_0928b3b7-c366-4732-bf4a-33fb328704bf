// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/developer/entity.proto

package developer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Different entity types exposed from risk service
type Entity int32

const (
	Entity_RISK_ENTITY_UNSPECIFIED     Entity = 0
	Entity_RISK_BANK_ACTIONS           Entity = 1
	Entity_RISK_DATA                   Entity = 2
	Entity_RISK_ALERTS                 Entity = 3
	Entity_RISK_REVIEW_ACTIONS         Entity = 4
	Entity_RISK_SCREENER_ATTEMPTS      Entity = 5
	Entity_RISK_ENTITY_FORM            Entity = 6
	Entity_RISK_UNIFIED_LEA_COMPLAINTS Entity = 7
	Entity_RISK_LIEN_REQUESTS          Entity = 8
)

// Enum value maps for Entity.
var (
	Entity_name = map[int32]string{
		0: "RISK_ENTITY_UNSPECIFIED",
		1: "RISK_BANK_ACTIONS",
		2: "RISK_DATA",
		3: "RISK_ALERTS",
		4: "RISK_REVIEW_ACTIONS",
		5: "RISK_SCREENER_ATTEMPTS",
		6: "RISK_ENTITY_FORM",
		7: "RISK_UNIFIED_LEA_COMPLAINTS",
		8: "RISK_LIEN_REQUESTS",
	}
	Entity_value = map[string]int32{
		"RISK_ENTITY_UNSPECIFIED":     0,
		"RISK_BANK_ACTIONS":           1,
		"RISK_DATA":                   2,
		"RISK_ALERTS":                 3,
		"RISK_REVIEW_ACTIONS":         4,
		"RISK_SCREENER_ATTEMPTS":      5,
		"RISK_ENTITY_FORM":            6,
		"RISK_UNIFIED_LEA_COMPLAINTS": 7,
		"RISK_LIEN_REQUESTS":          8,
	}
)

func (x Entity) Enum() *Entity {
	p := new(Entity)
	*p = x
	return p
}

func (x Entity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Entity) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_developer_entity_proto_enumTypes[0].Descriptor()
}

func (Entity) Type() protoreflect.EnumType {
	return &file_api_risk_developer_entity_proto_enumTypes[0]
}

func (x Entity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Entity.Descriptor instead.
func (Entity) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_developer_entity_proto_rawDescGZIP(), []int{0}
}

var File_api_risk_developer_entity_proto protoreflect.FileDescriptor

var file_api_risk_developer_entity_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x72, 0x2a, 0xe0, 0x01, 0x0a, 0x06, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1b, 0x0a, 0x17,
	0x52, 0x49, 0x53, 0x4b, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x49, 0x53,
	0x4b, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x01,
	0x12, 0x0d, 0x0a, 0x09, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x02, 0x12,
	0x0f, 0x0a, 0x0b, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x53, 0x10, 0x03,
	0x12, 0x17, 0x0a, 0x13, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x49, 0x53,
	0x4b, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d,
	0x50, 0x54, 0x53, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x45, 0x4e,
	0x54, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x52,
	0x49, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f,
	0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x53, 0x10, 0x07, 0x12, 0x16, 0x0a, 0x12,
	0x52, 0x49, 0x53, 0x4b, 0x5f, 0x4c, 0x49, 0x45, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x53, 0x10, 0x08, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x72, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69,
	0x73, 0x6b, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_developer_entity_proto_rawDescOnce sync.Once
	file_api_risk_developer_entity_proto_rawDescData = file_api_risk_developer_entity_proto_rawDesc
)

func file_api_risk_developer_entity_proto_rawDescGZIP() []byte {
	file_api_risk_developer_entity_proto_rawDescOnce.Do(func() {
		file_api_risk_developer_entity_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_developer_entity_proto_rawDescData)
	})
	return file_api_risk_developer_entity_proto_rawDescData
}

var file_api_risk_developer_entity_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_risk_developer_entity_proto_goTypes = []interface{}{
	(Entity)(0), // 0: risk.developer.Entity
}
var file_api_risk_developer_entity_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_risk_developer_entity_proto_init() }
func file_api_risk_developer_entity_proto_init() {
	if File_api_risk_developer_entity_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_developer_entity_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_developer_entity_proto_goTypes,
		DependencyIndexes: file_api_risk_developer_entity_proto_depIdxs,
		EnumInfos:         file_api_risk_developer_entity_proto_enumTypes,
	}.Build()
	File_api_risk_developer_entity_proto = out.File
	file_api_risk_developer_entity_proto_rawDesc = nil
	file_api_risk_developer_entity_proto_goTypes = nil
	file_api_risk_developer_entity_proto_depIdxs = nil
}
