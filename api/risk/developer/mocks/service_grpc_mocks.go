// Code generated by MockGen. DO NOT EDIT.
// Source: api/risk/developer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockDeveloperClient is a mock of DeveloperClient interface.
type MockDeveloperClient struct {
	ctrl     *gomock.Controller
	recorder *MockDeveloperClientMockRecorder
}

// MockDeveloperClientMockRecorder is the mock recorder for MockDeveloperClient.
type MockDeveloperClientMockRecorder struct {
	mock *MockDeveloperClient
}

// NewMockDeveloperClient creates a new mock instance.
func NewMockDeveloperClient(ctrl *gomock.Controller) *MockDeveloperClient {
	mock := &MockDeveloperClient{ctrl: ctrl}
	mock.recorder = &MockDeveloperClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeveloperClient) EXPECT() *MockDeveloperClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDeveloperClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDeveloperClientMockRecorder) GetData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDeveloperClient)(nil).GetData), varargs...)
}

// GetEntityList mocks base method.
func (m *MockDeveloperClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntityList", varargs...)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDeveloperClientMockRecorder) GetEntityList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDeveloperClient)(nil).GetEntityList), varargs...)
}

// GetParameterList mocks base method.
func (m *MockDeveloperClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParameterList", varargs...)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDeveloperClientMockRecorder) GetParameterList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDeveloperClient)(nil).GetParameterList), varargs...)
}

// MockDeveloperServer is a mock of DeveloperServer interface.
type MockDeveloperServer struct {
	ctrl     *gomock.Controller
	recorder *MockDeveloperServerMockRecorder
}

// MockDeveloperServerMockRecorder is the mock recorder for MockDeveloperServer.
type MockDeveloperServerMockRecorder struct {
	mock *MockDeveloperServer
}

// NewMockDeveloperServer creates a new mock instance.
func NewMockDeveloperServer(ctrl *gomock.Controller) *MockDeveloperServer {
	mock := &MockDeveloperServer{ctrl: ctrl}
	mock.recorder = &MockDeveloperServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeveloperServer) EXPECT() *MockDeveloperServerMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDeveloperServer) GetData(arg0 context.Context, arg1 *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDeveloperServerMockRecorder) GetData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDeveloperServer)(nil).GetData), arg0, arg1)
}

// GetEntityList mocks base method.
func (m *MockDeveloperServer) GetEntityList(arg0 context.Context, arg1 *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDeveloperServerMockRecorder) GetEntityList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDeveloperServer)(nil).GetEntityList), arg0, arg1)
}

// GetParameterList mocks base method.
func (m *MockDeveloperServer) GetParameterList(arg0 context.Context, arg1 *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameterList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDeveloperServerMockRecorder) GetParameterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDeveloperServer)(nil).GetParameterList), arg0, arg1)
}

// MockUnsafeDeveloperServer is a mock of UnsafeDeveloperServer interface.
type MockUnsafeDeveloperServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeDeveloperServerMockRecorder
}

// MockUnsafeDeveloperServerMockRecorder is the mock recorder for MockUnsafeDeveloperServer.
type MockUnsafeDeveloperServerMockRecorder struct {
	mock *MockUnsafeDeveloperServer
}

// NewMockUnsafeDeveloperServer creates a new mock instance.
func NewMockUnsafeDeveloperServer(ctrl *gomock.Controller) *MockUnsafeDeveloperServer {
	mock := &MockUnsafeDeveloperServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeDeveloperServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeDeveloperServer) EXPECT() *MockUnsafeDeveloperServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedDeveloperServer mocks base method.
func (m *MockUnsafeDeveloperServer) mustEmbedUnimplementedDeveloperServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedDeveloperServer")
}

// mustEmbedUnimplementedDeveloperServer indicates an expected call of mustEmbedUnimplementedDeveloperServer.
func (mr *MockUnsafeDeveloperServerMockRecorder) mustEmbedUnimplementedDeveloperServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedDeveloperServer", reflect.TypeOf((*MockUnsafeDeveloperServer)(nil).mustEmbedUnimplementedDeveloperServer))
}
