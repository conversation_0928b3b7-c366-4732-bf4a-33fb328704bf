// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/risk/developer/service.proto

package developer

import (
	context "context"
	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Developer_GetEntityList_FullMethodName    = "/risk.developer.Developer/GetEntityList"
	Developer_GetParameterList_FullMethodName = "/risk.developer.Developer/GetParameterList"
	Developer_GetData_FullMethodName          = "/risk.developer.Developer/GetData"
)

// DeveloperClient is the client API for Developer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DeveloperClient interface {
	// service to fetch list of entities for which risk can return the data
	GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error)
	// For each entity as defined above, the parameter required to fetch that data will be different
	// This service will return appropriate params based on entity passed in request
	GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error)
	// The actual get data API call where we will make a DB call to get the required data
	GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error)
}

type developerClient struct {
	cc grpc.ClientConnInterface
}

func NewDeveloperClient(cc grpc.ClientConnInterface) DeveloperClient {
	return &developerClient{cc}
}

func (c *developerClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	out := new(db_state.GetEntityListResponse)
	err := c.cc.Invoke(ctx, Developer_GetEntityList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *developerClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	out := new(db_state.GetParameterListResponse)
	err := c.cc.Invoke(ctx, Developer_GetParameterList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *developerClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	out := new(db_state.GetDataResponse)
	err := c.cc.Invoke(ctx, Developer_GetData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DeveloperServer is the server API for Developer service.
// All implementations should embed UnimplementedDeveloperServer
// for forward compatibility
type DeveloperServer interface {
	// service to fetch list of entities for which risk can return the data
	GetEntityList(context.Context, *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error)
	// For each entity as defined above, the parameter required to fetch that data will be different
	// This service will return appropriate params based on entity passed in request
	GetParameterList(context.Context, *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error)
	// The actual get data API call where we will make a DB call to get the required data
	GetData(context.Context, *db_state.GetDataRequest) (*db_state.GetDataResponse, error)
}

// UnimplementedDeveloperServer should be embedded to have forward compatible implementations.
type UnimplementedDeveloperServer struct {
}

func (UnimplementedDeveloperServer) GetEntityList(context.Context, *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntityList not implemented")
}
func (UnimplementedDeveloperServer) GetParameterList(context.Context, *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetParameterList not implemented")
}
func (UnimplementedDeveloperServer) GetData(context.Context, *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetData not implemented")
}

// UnsafeDeveloperServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DeveloperServer will
// result in compilation errors.
type UnsafeDeveloperServer interface {
	mustEmbedUnimplementedDeveloperServer()
}

func RegisterDeveloperServer(s grpc.ServiceRegistrar, srv DeveloperServer) {
	s.RegisterService(&Developer_ServiceDesc, srv)
}

func _Developer_GetEntityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetEntityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeveloperServer).GetEntityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Developer_GetEntityList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeveloperServer).GetEntityList(ctx, req.(*db_state.GetEntityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Developer_GetParameterList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetParameterListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeveloperServer).GetParameterList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Developer_GetParameterList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeveloperServer).GetParameterList(ctx, req.(*db_state.GetParameterListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Developer_GetData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeveloperServer).GetData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Developer_GetData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeveloperServer).GetData(ctx, req.(*db_state.GetDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Developer_ServiceDesc is the grpc.ServiceDesc for Developer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Developer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "risk.developer.Developer",
	HandlerType: (*DeveloperServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEntityList",
			Handler:    _Developer_GetEntityList_Handler,
		},
		{
			MethodName: "GetParameterList",
			Handler:    _Developer_GetParameterList_Handler,
		},
		{
			MethodName: "GetData",
			Handler:    _Developer_GetData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/risk/developer/service.proto",
}
