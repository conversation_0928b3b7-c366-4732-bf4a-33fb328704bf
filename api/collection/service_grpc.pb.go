// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/collection/service.proto

package collection

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Collection_InitiateUpdatePayment_FullMethodName = "/collection.Collection/InitiateUpdatePayment"
	Collection_GetLeadDetails_FullMethodName        = "/collection.Collection/GetLeadDetails"
)

// CollectionClient is the client API for Collection service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CollectionClient interface {
	// RPC to initiate payment update to collections vendor. Once user makes a payment we will instantaneously update Credgenics portal
	// with payment details. This is to ensure that the Collection agents are instantaneously updated with payment details.
	// LMS of vendor has a days delay to recon in our system.
	InitiateUpdatePayment(ctx context.Context, in *InitiateUpdatePaymentRequest, opts ...grpc.CallOption) (*InitiateUpdatePaymentResponse, error)
	// RPC to get lead details from laon_id fron vendor and vendor eg 12345,credgenics
	GetLeadDetails(ctx context.Context, in *GetLeadDetailsRequest, opts ...grpc.CallOption) (*GetLeadDetailsResponse, error)
}

type collectionClient struct {
	cc grpc.ClientConnInterface
}

func NewCollectionClient(cc grpc.ClientConnInterface) CollectionClient {
	return &collectionClient{cc}
}

func (c *collectionClient) InitiateUpdatePayment(ctx context.Context, in *InitiateUpdatePaymentRequest, opts ...grpc.CallOption) (*InitiateUpdatePaymentResponse, error) {
	out := new(InitiateUpdatePaymentResponse)
	err := c.cc.Invoke(ctx, Collection_InitiateUpdatePayment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *collectionClient) GetLeadDetails(ctx context.Context, in *GetLeadDetailsRequest, opts ...grpc.CallOption) (*GetLeadDetailsResponse, error) {
	out := new(GetLeadDetailsResponse)
	err := c.cc.Invoke(ctx, Collection_GetLeadDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CollectionServer is the server API for Collection service.
// All implementations should embed UnimplementedCollectionServer
// for forward compatibility
type CollectionServer interface {
	// RPC to initiate payment update to collections vendor. Once user makes a payment we will instantaneously update Credgenics portal
	// with payment details. This is to ensure that the Collection agents are instantaneously updated with payment details.
	// LMS of vendor has a days delay to recon in our system.
	InitiateUpdatePayment(context.Context, *InitiateUpdatePaymentRequest) (*InitiateUpdatePaymentResponse, error)
	// RPC to get lead details from laon_id fron vendor and vendor eg 12345,credgenics
	GetLeadDetails(context.Context, *GetLeadDetailsRequest) (*GetLeadDetailsResponse, error)
}

// UnimplementedCollectionServer should be embedded to have forward compatible implementations.
type UnimplementedCollectionServer struct {
}

func (UnimplementedCollectionServer) InitiateUpdatePayment(context.Context, *InitiateUpdatePaymentRequest) (*InitiateUpdatePaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateUpdatePayment not implemented")
}
func (UnimplementedCollectionServer) GetLeadDetails(context.Context, *GetLeadDetailsRequest) (*GetLeadDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLeadDetails not implemented")
}

// UnsafeCollectionServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CollectionServer will
// result in compilation errors.
type UnsafeCollectionServer interface {
	mustEmbedUnimplementedCollectionServer()
}

func RegisterCollectionServer(s grpc.ServiceRegistrar, srv CollectionServer) {
	s.RegisterService(&Collection_ServiceDesc, srv)
}

func _Collection_InitiateUpdatePayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateUpdatePaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CollectionServer).InitiateUpdatePayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Collection_InitiateUpdatePayment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CollectionServer).InitiateUpdatePayment(ctx, req.(*InitiateUpdatePaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Collection_GetLeadDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLeadDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CollectionServer).GetLeadDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Collection_GetLeadDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CollectionServer).GetLeadDetails(ctx, req.(*GetLeadDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Collection_ServiceDesc is the grpc.ServiceDesc for Collection service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Collection_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "collection.Collection",
	HandlerType: (*CollectionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InitiateUpdatePayment",
			Handler:    _Collection_InitiateUpdatePayment_Handler,
		},
		{
			MethodName: "GetLeadDetails",
			Handler:    _Collection_GetLeadDetails_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/collection/service.proto",
}
