//go:generate gen_sql -types=DataProvenance,VendorStatus,RecoveryStatus

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/collection/common.proto

package collection

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// DataProvenance represents the source of a given data
// for example, if a communication entry is created from a vendor's webhook, the data provenance for it
// will be set to DATA_PROVENANCE_VENDOR_WEBHOOK
type DataProvenance int32

const (
	DataProvenance_DATA_PROVENANCE_UNSPECIFIED    DataProvenance = 0
	DataProvenance_DATA_PROVENANCE_VENDOR_WEBHOOK DataProvenance = 1 // TODO(mounish): add other sources like daily sync, manual upload, reconciliation, etc
)

// Enum value maps for DataProvenance.
var (
	DataProvenance_name = map[int32]string{
		0: "DATA_PROVENANCE_UNSPECIFIED",
		1: "DATA_PROVENANCE_VENDOR_WEBHOOK",
	}
	DataProvenance_value = map[string]int32{
		"DATA_PROVENANCE_UNSPECIFIED":    0,
		"DATA_PROVENANCE_VENDOR_WEBHOOK": 1,
	}
)

func (x DataProvenance) Enum() *DataProvenance {
	p := new(DataProvenance)
	*p = x
	return p
}

func (x DataProvenance) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataProvenance) Descriptor() protoreflect.EnumDescriptor {
	return file_api_collection_common_proto_enumTypes[0].Descriptor()
}

func (DataProvenance) Type() protoreflect.EnumType {
	return &file_api_collection_common_proto_enumTypes[0]
}

func (x DataProvenance) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataProvenance.Descriptor instead.
func (DataProvenance) EnumDescriptor() ([]byte, []int) {
	return file_api_collection_common_proto_rawDescGZIP(), []int{0}
}

// VendorStatus represents the creation status of a given entry at the vendor
type VendorStatus int32

const (
	VendorStatus_VENDOR_STATUS_UNSPECIFIED VendorStatus = 0
	// the entry is yet to be created at the vendor
	VendorStatus_VENDOR_STATUS_PENDING VendorStatus = 1
	// the entry has been successfully created at the vendor
	VendorStatus_VENDOR_STATUS_SUCCESS VendorStatus = 2
)

// Enum value maps for VendorStatus.
var (
	VendorStatus_name = map[int32]string{
		0: "VENDOR_STATUS_UNSPECIFIED",
		1: "VENDOR_STATUS_PENDING",
		2: "VENDOR_STATUS_SUCCESS",
	}
	VendorStatus_value = map[string]int32{
		"VENDOR_STATUS_UNSPECIFIED": 0,
		"VENDOR_STATUS_PENDING":     1,
		"VENDOR_STATUS_SUCCESS":     2,
	}
)

func (x VendorStatus) Enum() *VendorStatus {
	p := new(VendorStatus)
	*p = x
	return p
}

func (x VendorStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VendorStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_collection_common_proto_enumTypes[1].Descriptor()
}

func (VendorStatus) Type() protoreflect.EnumType {
	return &file_api_collection_common_proto_enumTypes[1]
}

func (x VendorStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VendorStatus.Descriptor instead.
func (VendorStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_collection_common_proto_rawDescGZIP(), []int{1}
}

type RecoveryStatus int32

const (
	RecoveryStatus_RECOVERY_STATUS_UNSPECIFIED RecoveryStatus = 0
	// the amount has been completely recovered
	RecoveryStatus_RECOVERY_STATUS_RECOVERED RecoveryStatus = 1
	// the amount has not been recovered at all
	RecoveryStatus_RECOVERY_STATUS_NOT_RECOVERED RecoveryStatus = 2
	// the amount has been partially recovered
	RecoveryStatus_RECOVERY_STATUS_PARTIALLY_RECOVERED RecoveryStatus = 3
)

// Enum value maps for RecoveryStatus.
var (
	RecoveryStatus_name = map[int32]string{
		0: "RECOVERY_STATUS_UNSPECIFIED",
		1: "RECOVERY_STATUS_RECOVERED",
		2: "RECOVERY_STATUS_NOT_RECOVERED",
		3: "RECOVERY_STATUS_PARTIALLY_RECOVERED",
	}
	RecoveryStatus_value = map[string]int32{
		"RECOVERY_STATUS_UNSPECIFIED":         0,
		"RECOVERY_STATUS_RECOVERED":           1,
		"RECOVERY_STATUS_NOT_RECOVERED":       2,
		"RECOVERY_STATUS_PARTIALLY_RECOVERED": 3,
	}
)

func (x RecoveryStatus) Enum() *RecoveryStatus {
	p := new(RecoveryStatus)
	*p = x
	return p
}

func (x RecoveryStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecoveryStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_collection_common_proto_enumTypes[2].Descriptor()
}

func (RecoveryStatus) Type() protoreflect.EnumType {
	return &file_api_collection_common_proto_enumTypes[2]
}

func (x RecoveryStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecoveryStatus.Descriptor instead.
func (RecoveryStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_collection_common_proto_rawDescGZIP(), []int{2}
}

var File_api_collection_common_proto protoreflect.FileDescriptor

var file_api_collection_common_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2a, 0x55, 0x0a,
	0x0e, 0x44, 0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x1f, 0x0a, 0x1b, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x22, 0x0a, 0x1e, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41,
	0x4e, 0x43, 0x45, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x57, 0x45, 0x42, 0x48, 0x4f,
	0x4f, 0x4b, 0x10, 0x01, 0x2a, 0x63, 0x0a, 0x0c, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x19,
	0x0a, 0x15, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x02, 0x2a, 0x9c, 0x01, 0x0a, 0x0e, 0x52, 0x65,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x1b,
	0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a,
	0x19, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d,
	0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x02, 0x12,
	0x27, 0x0a, 0x23, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x52, 0x45, 0x43,
	0x4f, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x03, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_collection_common_proto_rawDescOnce sync.Once
	file_api_collection_common_proto_rawDescData = file_api_collection_common_proto_rawDesc
)

func file_api_collection_common_proto_rawDescGZIP() []byte {
	file_api_collection_common_proto_rawDescOnce.Do(func() {
		file_api_collection_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_collection_common_proto_rawDescData)
	})
	return file_api_collection_common_proto_rawDescData
}

var file_api_collection_common_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_collection_common_proto_goTypes = []interface{}{
	(DataProvenance)(0), // 0: api.collection.DataProvenance
	(VendorStatus)(0),   // 1: api.collection.VendorStatus
	(RecoveryStatus)(0), // 2: api.collection.RecoveryStatus
}
var file_api_collection_common_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_collection_common_proto_init() }
func file_api_collection_common_proto_init() {
	if File_api_collection_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_collection_common_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_collection_common_proto_goTypes,
		DependencyIndexes: file_api_collection_common_proto_depIdxs,
		EnumInfos:         file_api_collection_common_proto_enumTypes,
	}.Build()
	File_api_collection_common_proto = out.File
	file_api_collection_common_proto_rawDesc = nil
	file_api_collection_common_proto_goTypes = nil
	file_api_collection_common_proto_depIdxs = nil
}
