// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/stockguardian/sgvendorgateway/bre/service.proto

package bre

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Decision int32

const (
	Decision_DECISION_UNSPECIFIED Decision = 0
	Decision_DECISION_APPROVED    Decision = 1
	Decision_DECISION_REJECTED    Decision = 2
)

// Enum value maps for Decision.
var (
	Decision_name = map[int32]string{
		0: "DECISION_UNSPECIFIED",
		1: "DECISION_APPROVED",
		2: "DECISION_REJECTED",
	}
	Decision_value = map[string]int32{
		"DECISION_UNSPECIFIED": 0,
		"DECISION_APPROVED":    1,
		"DECISION_REJECTED":    2,
	}
)

func (x Decision) Enum() *Decision {
	p := new(Decision)
	*p = x
	return p
}

func (x Decision) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Decision) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgvendorgateway_bre_service_proto_enumTypes[0].Descriptor()
}

func (Decision) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgvendorgateway_bre_service_proto_enumTypes[0]
}

func (x Decision) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Decision.Descriptor instead.
func (Decision) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDescGZIP(), []int{0}
}

type GetLoanDecisioningRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer for whom to fetch loan decisioning
	CustomerId     string                `protobuf:"bytes,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	ClientId       string                `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Dob            *date.Date            `protobuf:"bytes,3,opt,name=dob,proto3" json:"dob,omitempty"`
	EmploymentType common.EmploymentType `protobuf:"varint,4,opt,name=employment_type,json=employmentType,proto3,enum=api.typesv2.common.EmploymentType" json:"employment_type,omitempty"`
	MonthlyIncome  *money.Money          `protobuf:"bytes,5,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income,omitempty"`
	Name           *common.Name          `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	Gender         common.Gender         `protobuf:"varint,7,opt,name=gender,proto3,enum=api.typesv2.common.Gender" json:"gender,omitempty"`
	Pan            string                `protobuf:"bytes,8,opt,name=pan,proto3" json:"pan,omitempty"`
	Address        *common.PostalAddress `protobuf:"bytes,9,opt,name=address,proto3" json:"address,omitempty"`
	EmployerName   string                `protobuf:"bytes,10,opt,name=employer_name,json=employerName,proto3" json:"employer_name,omitempty"`
	WorkEmail      string                `protobuf:"bytes,11,opt,name=work_email,json=workEmail,proto3" json:"work_email,omitempty"`
	SchemeId       string                `protobuf:"bytes,13,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	BatchId        string                `protobuf:"bytes,14,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`
	PolicyParams   *PolicyParams         `protobuf:"bytes,16,opt,name=policy_params,json=policyParams,proto3" json:"policy_params,omitempty"`
}

func (x *GetLoanDecisioningRequest) Reset() {
	*x = GetLoanDecisioningRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgvendorgateway_bre_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDecisioningRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDecisioningRequest) ProtoMessage() {}

func (x *GetLoanDecisioningRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgvendorgateway_bre_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDecisioningRequest.ProtoReflect.Descriptor instead.
func (*GetLoanDecisioningRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetLoanDecisioningRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *GetLoanDecisioningRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *GetLoanDecisioningRequest) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *GetLoanDecisioningRequest) GetEmploymentType() common.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return common.EmploymentType(0)
}

func (x *GetLoanDecisioningRequest) GetMonthlyIncome() *money.Money {
	if x != nil {
		return x.MonthlyIncome
	}
	return nil
}

func (x *GetLoanDecisioningRequest) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *GetLoanDecisioningRequest) GetGender() common.Gender {
	if x != nil {
		return x.Gender
	}
	return common.Gender(0)
}

func (x *GetLoanDecisioningRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *GetLoanDecisioningRequest) GetAddress() *common.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *GetLoanDecisioningRequest) GetEmployerName() string {
	if x != nil {
		return x.EmployerName
	}
	return ""
}

func (x *GetLoanDecisioningRequest) GetWorkEmail() string {
	if x != nil {
		return x.WorkEmail
	}
	return ""
}

func (x *GetLoanDecisioningRequest) GetSchemeId() string {
	if x != nil {
		return x.SchemeId
	}
	return ""
}

func (x *GetLoanDecisioningRequest) GetBatchId() string {
	if x != nil {
		return x.BatchId
	}
	return ""
}

func (x *GetLoanDecisioningRequest) GetPolicyParams() *PolicyParams {
	if x != nil {
		return x.PolicyParams
	}
	return nil
}

type PolicyParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PricingScheme     string  `protobuf:"bytes,1,opt,name=pricing_scheme,json=pricingScheme,proto3" json:"pricing_scheme,omitempty"`
	EverVkycAttempted string  `protobuf:"bytes,2,opt,name=ever_vkyc_attempted,json=everVkycAttempted,proto3" json:"ever_vkyc_attempted,omitempty"`
	PdScore           float64 `protobuf:"fixed64,3,opt,name=pd_score,json=pdScore,proto3" json:"pd_score,omitempty"`
	PdScoreVersion    string  `protobuf:"bytes,4,opt,name=pd_score_version,json=pdScoreVersion,proto3" json:"pd_score_version,omitempty"`
	SchemeId          string  `protobuf:"bytes,5,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	PricingSchemeBre  string  `protobuf:"bytes,6,opt,name=pricing_scheme_bre,json=pricingSchemeBre,proto3" json:"pricing_scheme_bre,omitempty"`
	BatchId           string  `protobuf:"bytes,7,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`
}

func (x *PolicyParams) Reset() {
	*x = PolicyParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgvendorgateway_bre_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolicyParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicyParams) ProtoMessage() {}

func (x *PolicyParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgvendorgateway_bre_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicyParams.ProtoReflect.Descriptor instead.
func (*PolicyParams) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDescGZIP(), []int{1}
}

func (x *PolicyParams) GetPricingScheme() string {
	if x != nil {
		return x.PricingScheme
	}
	return ""
}

func (x *PolicyParams) GetEverVkycAttempted() string {
	if x != nil {
		return x.EverVkycAttempted
	}
	return ""
}

func (x *PolicyParams) GetPdScore() float64 {
	if x != nil {
		return x.PdScore
	}
	return 0
}

func (x *PolicyParams) GetPdScoreVersion() string {
	if x != nil {
		return x.PdScoreVersion
	}
	return ""
}

func (x *PolicyParams) GetSchemeId() string {
	if x != nil {
		return x.SchemeId
	}
	return ""
}

func (x *PolicyParams) GetPricingSchemeBre() string {
	if x != nil {
		return x.PricingSchemeBre
	}
	return ""
}

func (x *PolicyParams) GetBatchId() string {
	if x != nil {
		return x.BatchId
	}
	return ""
}

type GetLoanDecisioningResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          *rpc.Status                              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LoanDecision    Decision                                 `protobuf:"varint,2,opt,name=loan_decision,json=loanDecision,proto3,enum=stockguardian.sgvendorgateway.bre.Decision" json:"loan_decision,omitempty"`
	RawBreResponse  []byte                                   `protobuf:"bytes,3,opt,name=raw_bre_response,json=rawBreResponse,proto3" json:"raw_bre_response,omitempty"`
	OfferDetails    *GetLoanDecisioningResponse_OfferDetails `protobuf:"bytes,4,opt,name=offer_details,json=offerDetails,proto3" json:"offer_details,omitempty"`
	SchemeId        string                                   `protobuf:"bytes,5,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	BatchId         string                                   `protobuf:"bytes,6,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`
	CustId          string                                   `protobuf:"bytes,7,opt,name=cust_id,json=custId,proto3" json:"cust_id,omitempty"`
	LoanProgram     string                                   `protobuf:"bytes,8,opt,name=loan_program,json=loanProgram,proto3" json:"loan_program,omitempty"`
	ExternalReasons []string                                 `protobuf:"bytes,9,rep,name=external_reasons,json=externalReasons,proto3" json:"external_reasons,omitempty"`
	PolicyParams    *PolicyParams                            `protobuf:"bytes,11,opt,name=policy_params,json=policyParams,proto3" json:"policy_params,omitempty"`
}

func (x *GetLoanDecisioningResponse) Reset() {
	*x = GetLoanDecisioningResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgvendorgateway_bre_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDecisioningResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDecisioningResponse) ProtoMessage() {}

func (x *GetLoanDecisioningResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgvendorgateway_bre_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDecisioningResponse.ProtoReflect.Descriptor instead.
func (*GetLoanDecisioningResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetLoanDecisioningResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLoanDecisioningResponse) GetLoanDecision() Decision {
	if x != nil {
		return x.LoanDecision
	}
	return Decision_DECISION_UNSPECIFIED
}

func (x *GetLoanDecisioningResponse) GetRawBreResponse() []byte {
	if x != nil {
		return x.RawBreResponse
	}
	return nil
}

func (x *GetLoanDecisioningResponse) GetOfferDetails() *GetLoanDecisioningResponse_OfferDetails {
	if x != nil {
		return x.OfferDetails
	}
	return nil
}

func (x *GetLoanDecisioningResponse) GetSchemeId() string {
	if x != nil {
		return x.SchemeId
	}
	return ""
}

func (x *GetLoanDecisioningResponse) GetBatchId() string {
	if x != nil {
		return x.BatchId
	}
	return ""
}

func (x *GetLoanDecisioningResponse) GetCustId() string {
	if x != nil {
		return x.CustId
	}
	return ""
}

func (x *GetLoanDecisioningResponse) GetLoanProgram() string {
	if x != nil {
		return x.LoanProgram
	}
	return ""
}

func (x *GetLoanDecisioningResponse) GetExternalReasons() []string {
	if x != nil {
		return x.ExternalReasons
	}
	return nil
}

func (x *GetLoanDecisioningResponse) GetPolicyParams() *PolicyParams {
	if x != nil {
		return x.PolicyParams
	}
	return nil
}

type GetLoanDecisioningResponse_OfferDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinAmount               *money.Money           `protobuf:"bytes,1,opt,name=min_amount,json=minAmount,proto3" json:"min_amount,omitempty"`
	MaxAmount               *money.Money           `protobuf:"bytes,2,opt,name=max_amount,json=maxAmount,proto3" json:"max_amount,omitempty"`
	MaxEmiAmount            *money.Money           `protobuf:"bytes,3,opt,name=max_emi_amount,json=maxEmiAmount,proto3" json:"max_emi_amount,omitempty"`
	InterestPercentage      float64                `protobuf:"fixed64,4,opt,name=interest_percentage,json=interestPercentage,proto3" json:"interest_percentage,omitempty"`
	ProcessingFeePercentage float64                `protobuf:"fixed64,5,opt,name=processing_fee_percentage,json=processingFeePercentage,proto3" json:"processing_fee_percentage,omitempty"`
	GstPercentage           float64                `protobuf:"fixed64,6,opt,name=gst_percentage,json=gstPercentage,proto3" json:"gst_percentage,omitempty"`
	MinTenureInMonths       int32                  `protobuf:"varint,7,opt,name=min_tenure_in_months,json=minTenureInMonths,proto3" json:"min_tenure_in_months,omitempty"`
	MaxTenureInMonths       int32                  `protobuf:"varint,8,opt,name=max_tenure_in_months,json=maxTenureInMonths,proto3" json:"max_tenure_in_months,omitempty"`
	EmiDueDate              *date.Date             `protobuf:"bytes,9,opt,name=emi_due_date,json=emiDueDate,proto3" json:"emi_due_date,omitempty"`
	ValidTill               *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=valid_till,json=validTill,proto3" json:"valid_till,omitempty"`
}

func (x *GetLoanDecisioningResponse_OfferDetails) Reset() {
	*x = GetLoanDecisioningResponse_OfferDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgvendorgateway_bre_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDecisioningResponse_OfferDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDecisioningResponse_OfferDetails) ProtoMessage() {}

func (x *GetLoanDecisioningResponse_OfferDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgvendorgateway_bre_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDecisioningResponse_OfferDetails.ProtoReflect.Descriptor instead.
func (*GetLoanDecisioningResponse_OfferDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *GetLoanDecisioningResponse_OfferDetails) GetMinAmount() *money.Money {
	if x != nil {
		return x.MinAmount
	}
	return nil
}

func (x *GetLoanDecisioningResponse_OfferDetails) GetMaxAmount() *money.Money {
	if x != nil {
		return x.MaxAmount
	}
	return nil
}

func (x *GetLoanDecisioningResponse_OfferDetails) GetMaxEmiAmount() *money.Money {
	if x != nil {
		return x.MaxEmiAmount
	}
	return nil
}

func (x *GetLoanDecisioningResponse_OfferDetails) GetInterestPercentage() float64 {
	if x != nil {
		return x.InterestPercentage
	}
	return 0
}

func (x *GetLoanDecisioningResponse_OfferDetails) GetProcessingFeePercentage() float64 {
	if x != nil {
		return x.ProcessingFeePercentage
	}
	return 0
}

func (x *GetLoanDecisioningResponse_OfferDetails) GetGstPercentage() float64 {
	if x != nil {
		return x.GstPercentage
	}
	return 0
}

func (x *GetLoanDecisioningResponse_OfferDetails) GetMinTenureInMonths() int32 {
	if x != nil {
		return x.MinTenureInMonths
	}
	return 0
}

func (x *GetLoanDecisioningResponse_OfferDetails) GetMaxTenureInMonths() int32 {
	if x != nil {
		return x.MaxTenureInMonths
	}
	return 0
}

func (x *GetLoanDecisioningResponse_OfferDetails) GetEmiDueDate() *date.Date {
	if x != nil {
		return x.EmiDueDate
	}
	return nil
}

func (x *GetLoanDecisioningResponse_OfferDetails) GetValidTill() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidTill
	}
	return nil
}

var File_api_stockguardian_sgvendorgateway_bre_service_proto protoreflect.FileDescriptor

var file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDesc = []byte{
	0x0a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x62, 0x72, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72,
	0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x62, 0x72, 0x65, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x67,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x89, 0x05, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x4b, 0x0a,
	0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x49,
	0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x3b, 0x0a, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x77,
	0x6f, 0x72, 0x6b, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x77, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x49, 0x64, 0x12, 0x54, 0x0a, 0x0d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0c, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x90, 0x02, 0x0a, 0x0c, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x12, 0x2e, 0x0a, 0x13, 0x65, 0x76, 0x65, 0x72, 0x5f, 0x76, 0x6b, 0x79, 0x63, 0x5f, 0x61, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x65,
	0x76, 0x65, 0x72, 0x56, 0x6b, 0x79, 0x63, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x70, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x07, 0x70, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x70,
	0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x42, 0x72, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x22, 0xba, 0x08, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x50, 0x0a, 0x0d, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x28, 0x0a, 0x10, 0x72, 0x61, 0x77, 0x5f, 0x62, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x72, 0x61, 0x77,
	0x42, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6f, 0x0a, 0x0d, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69,
	0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65,
	0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x74,
	0x63, 0x68, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x75, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x73, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x12, 0x29, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x12, 0x54, 0x0a, 0x0d, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69,
	0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x52, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x94, 0x04, 0x0a, 0x0c, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x31, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d,
	0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x0e, 0x6d, 0x61, 0x78, 0x5f,
	0x65, 0x6d, 0x69, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x6d, 0x61, 0x78, 0x45, 0x6d, 0x69, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x13, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x19, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x46, 0x65, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x67, 0x73, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x67, 0x73, 0x74, 0x50, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x6d, 0x69, 0x6e, 0x5f, 0x74, 0x65,
	0x6e, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6d, 0x69, 0x6e, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x49,
	0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x6d, 0x61, 0x78, 0x5f, 0x74,
	0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6d, 0x61, 0x78, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65,
	0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x33, 0x0a, 0x0c, 0x65, 0x6d, 0x69, 0x5f,
	0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x0a, 0x65, 0x6d, 0x69, 0x44, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a,
	0x0a, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x54, 0x69, 0x6c, 0x6c, 0x2a, 0x52, 0x0a, 0x08, 0x44, 0x65, 0x63, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15,
	0x0a, 0x11, 0x44, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f,
	0x56, 0x45, 0x44, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f,
	0x4e, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x02, 0x32, 0xa0, 0x01, 0x0a,
	0x0a, 0x42, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69,
	0x6e, 0x67, 0x12, 0x3c, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69,
	0x61, 0x6e, 0x2e, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65,
	0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3d, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2e, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x62, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x63, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x82, 0x01, 0x0a, 0x3f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f, 0x74, 0x74, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e,
	0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x62, 0x72, 0x65, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f, 0x74, 0x74, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2f, 0x73, 0x67, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2f, 0x62, 0x72, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDescOnce sync.Once
	file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDescData = file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDesc
)

func file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDescGZIP() []byte {
	file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDescOnce.Do(func() {
		file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDescData)
	})
	return file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDescData
}

var file_api_stockguardian_sgvendorgateway_bre_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_stockguardian_sgvendorgateway_bre_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_stockguardian_sgvendorgateway_bre_service_proto_goTypes = []interface{}{
	(Decision)(0),                                   // 0: stockguardian.sgvendorgateway.bre.Decision
	(*GetLoanDecisioningRequest)(nil),               // 1: stockguardian.sgvendorgateway.bre.GetLoanDecisioningRequest
	(*PolicyParams)(nil),                            // 2: stockguardian.sgvendorgateway.bre.PolicyParams
	(*GetLoanDecisioningResponse)(nil),              // 3: stockguardian.sgvendorgateway.bre.GetLoanDecisioningResponse
	(*GetLoanDecisioningResponse_OfferDetails)(nil), // 4: stockguardian.sgvendorgateway.bre.GetLoanDecisioningResponse.OfferDetails
	(*date.Date)(nil),                               // 5: google.type.Date
	(common.EmploymentType)(0),                      // 6: api.typesv2.common.EmploymentType
	(*money.Money)(nil),                             // 7: google.type.Money
	(*common.Name)(nil),                             // 8: api.typesv2.common.Name
	(common.Gender)(0),                              // 9: api.typesv2.common.Gender
	(*common.PostalAddress)(nil),                    // 10: api.typesv2.common.PostalAddress
	(*rpc.Status)(nil),                              // 11: rpc.Status
	(*timestamppb.Timestamp)(nil),                   // 12: google.protobuf.Timestamp
}
var file_api_stockguardian_sgvendorgateway_bre_service_proto_depIdxs = []int32{
	5,  // 0: stockguardian.sgvendorgateway.bre.GetLoanDecisioningRequest.dob:type_name -> google.type.Date
	6,  // 1: stockguardian.sgvendorgateway.bre.GetLoanDecisioningRequest.employment_type:type_name -> api.typesv2.common.EmploymentType
	7,  // 2: stockguardian.sgvendorgateway.bre.GetLoanDecisioningRequest.monthly_income:type_name -> google.type.Money
	8,  // 3: stockguardian.sgvendorgateway.bre.GetLoanDecisioningRequest.name:type_name -> api.typesv2.common.Name
	9,  // 4: stockguardian.sgvendorgateway.bre.GetLoanDecisioningRequest.gender:type_name -> api.typesv2.common.Gender
	10, // 5: stockguardian.sgvendorgateway.bre.GetLoanDecisioningRequest.address:type_name -> api.typesv2.common.PostalAddress
	2,  // 6: stockguardian.sgvendorgateway.bre.GetLoanDecisioningRequest.policy_params:type_name -> stockguardian.sgvendorgateway.bre.PolicyParams
	11, // 7: stockguardian.sgvendorgateway.bre.GetLoanDecisioningResponse.status:type_name -> rpc.Status
	0,  // 8: stockguardian.sgvendorgateway.bre.GetLoanDecisioningResponse.loan_decision:type_name -> stockguardian.sgvendorgateway.bre.Decision
	4,  // 9: stockguardian.sgvendorgateway.bre.GetLoanDecisioningResponse.offer_details:type_name -> stockguardian.sgvendorgateway.bre.GetLoanDecisioningResponse.OfferDetails
	2,  // 10: stockguardian.sgvendorgateway.bre.GetLoanDecisioningResponse.policy_params:type_name -> stockguardian.sgvendorgateway.bre.PolicyParams
	7,  // 11: stockguardian.sgvendorgateway.bre.GetLoanDecisioningResponse.OfferDetails.min_amount:type_name -> google.type.Money
	7,  // 12: stockguardian.sgvendorgateway.bre.GetLoanDecisioningResponse.OfferDetails.max_amount:type_name -> google.type.Money
	7,  // 13: stockguardian.sgvendorgateway.bre.GetLoanDecisioningResponse.OfferDetails.max_emi_amount:type_name -> google.type.Money
	5,  // 14: stockguardian.sgvendorgateway.bre.GetLoanDecisioningResponse.OfferDetails.emi_due_date:type_name -> google.type.Date
	12, // 15: stockguardian.sgvendorgateway.bre.GetLoanDecisioningResponse.OfferDetails.valid_till:type_name -> google.protobuf.Timestamp
	1,  // 16: stockguardian.sgvendorgateway.bre.BreService.GetLoanDecisioning:input_type -> stockguardian.sgvendorgateway.bre.GetLoanDecisioningRequest
	3,  // 17: stockguardian.sgvendorgateway.bre.BreService.GetLoanDecisioning:output_type -> stockguardian.sgvendorgateway.bre.GetLoanDecisioningResponse
	17, // [17:18] is the sub-list for method output_type
	16, // [16:17] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_api_stockguardian_sgvendorgateway_bre_service_proto_init() }
func file_api_stockguardian_sgvendorgateway_bre_service_proto_init() {
	if File_api_stockguardian_sgvendorgateway_bre_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_stockguardian_sgvendorgateway_bre_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDecisioningRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgvendorgateway_bre_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolicyParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgvendorgateway_bre_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDecisioningResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgvendorgateway_bre_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDecisioningResponse_OfferDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_stockguardian_sgvendorgateway_bre_service_proto_goTypes,
		DependencyIndexes: file_api_stockguardian_sgvendorgateway_bre_service_proto_depIdxs,
		EnumInfos:         file_api_stockguardian_sgvendorgateway_bre_service_proto_enumTypes,
		MessageInfos:      file_api_stockguardian_sgvendorgateway_bre_service_proto_msgTypes,
	}.Build()
	File_api_stockguardian_sgvendorgateway_bre_service_proto = out.File
	file_api_stockguardian_sgvendorgateway_bre_service_proto_rawDesc = nil
	file_api_stockguardian_sgvendorgateway_bre_service_proto_goTypes = nil
	file_api_stockguardian_sgvendorgateway_bre_service_proto_depIdxs = nil
}
