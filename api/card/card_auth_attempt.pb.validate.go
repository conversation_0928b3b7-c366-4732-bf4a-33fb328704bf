// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/card_auth_attempt.proto

package card

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CardAuthAttempt with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CardAuthAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardAuthAttempt with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardAuthAttemptMultiError, or nil if none found.
func (m *CardAuthAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *CardAuthAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttemptId

	// no validation rules for CardId

	// no validation rules for CardAction

	// no validation rules for State

	// no validation rules for AuthType

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardAuthAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardAuthAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardAuthAttemptValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardAuthAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardAuthAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardAuthAttemptValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActionState

	if all {
		switch v := interface{}(m.GetActionStageDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardAuthAttemptValidationError{
					field:  "ActionStageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardAuthAttemptValidationError{
					field:  "ActionStageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionStageDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardAuthAttemptValidationError{
				field:  "ActionStageDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardAuthAttemptValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardAuthAttemptValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardAuthAttemptValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardAuthAttemptMultiError(errors)
	}

	return nil
}

// CardAuthAttemptMultiError is an error wrapping multiple validation errors
// returned by CardAuthAttempt.ValidateAll() if the designated constraints
// aren't met.
type CardAuthAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardAuthAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardAuthAttemptMultiError) AllErrors() []error { return m }

// CardAuthAttemptValidationError is the validation error returned by
// CardAuthAttempt.Validate if the designated constraints aren't met.
type CardAuthAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardAuthAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardAuthAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardAuthAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardAuthAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardAuthAttemptValidationError) ErrorName() string { return "CardAuthAttemptValidationError" }

// Error satisfies the builtin error interface
func (e CardAuthAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardAuthAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardAuthAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardAuthAttemptValidationError{}

// Validate checks the field values on ActionStageDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActionStageDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActionStageDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActionStageDetailsMultiError, or nil if none found.
func (m *ActionStageDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ActionStageDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetStageMapping()))
		i := 0
		for key := range m.GetStageMapping() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetStageMapping()[key]
			_ = val

			// no validation rules for StageMapping[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ActionStageDetailsValidationError{
							field:  fmt.Sprintf("StageMapping[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ActionStageDetailsValidationError{
							field:  fmt.Sprintf("StageMapping[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ActionStageDetailsValidationError{
						field:  fmt.Sprintf("StageMapping[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return ActionStageDetailsMultiError(errors)
	}

	return nil
}

// ActionStageDetailsMultiError is an error wrapping multiple validation errors
// returned by ActionStageDetails.ValidateAll() if the designated constraints
// aren't met.
type ActionStageDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionStageDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionStageDetailsMultiError) AllErrors() []error { return m }

// ActionStageDetailsValidationError is the validation error returned by
// ActionStageDetails.Validate if the designated constraints aren't met.
type ActionStageDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionStageDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionStageDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionStageDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionStageDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionStageDetailsValidationError) ErrorName() string {
	return "ActionStageDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ActionStageDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActionStageDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionStageDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionStageDetailsValidationError{}

// Validate checks the field values on StageInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StageInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StageInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StageInfoMultiError, or nil
// if none found.
func (m *StageInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *StageInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageInfoValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureCode

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return StageInfoMultiError(errors)
	}

	return nil
}

// StageInfoMultiError is an error wrapping multiple validation errors returned
// by StageInfo.ValidateAll() if the designated constraints aren't met.
type StageInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StageInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StageInfoMultiError) AllErrors() []error { return m }

// StageInfoValidationError is the validation error returned by
// StageInfo.Validate if the designated constraints aren't met.
type StageInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StageInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StageInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StageInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StageInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StageInfoValidationError) ErrorName() string { return "StageInfoValidationError" }

// Error satisfies the builtin error interface
func (e StageInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStageInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StageInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StageInfoValidationError{}
