// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/control.proto

package card

import (
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// List of possible card controls that can be defined by the user.
// Each card control is defined by ControlDefinition which uniquely identifies a valid card control definition.
// Each entry in CardControlType is an alias for a list of ControlDefinition.
// For example:
//
// 1. BLOCK/SUSPEND implies:
// - transactionType = {POS, ATM, ECOMM, NFC} // a list
// - locationType = {DOM, INTL} // a list
// - ACTION = ENABLE/DISABLE
//
// 2. ECOMM_ON_OFF is an alias for:
// - transactionType = ECOMMERCE
// - locationType = {DOM, INTL} // a list
// - ACTION = ENABLE/DISABLE
type CardControlType int32

const (
	CardControlType_CARD_CTRL_TYPE_UNSPECIFIED CardControlType = 0
	CardControlType_BLOCK                      CardControlType = 1
	CardControlType_SUSPEND                    CardControlType = 2
	CardControlType_ECOMM_ON_OFF               CardControlType = 3
	CardControlType_DOMESTIC_ON_OFF            CardControlType = 4
	CardControlType_INTERNATIONAL_ON_OFF       CardControlType = 5
	CardControlType_ATM_ON_OFF                 CardControlType = 6
	CardControlType_NFC_ON_OFF                 CardControlType = 7
	CardControlType_POS_ON_OFF                 CardControlType = 8
)

// Enum value maps for CardControlType.
var (
	CardControlType_name = map[int32]string{
		0: "CARD_CTRL_TYPE_UNSPECIFIED",
		1: "BLOCK",
		2: "SUSPEND",
		3: "ECOMM_ON_OFF",
		4: "DOMESTIC_ON_OFF",
		5: "INTERNATIONAL_ON_OFF",
		6: "ATM_ON_OFF",
		7: "NFC_ON_OFF",
		8: "POS_ON_OFF",
	}
	CardControlType_value = map[string]int32{
		"CARD_CTRL_TYPE_UNSPECIFIED": 0,
		"BLOCK":                      1,
		"SUSPEND":                    2,
		"ECOMM_ON_OFF":               3,
		"DOMESTIC_ON_OFF":            4,
		"INTERNATIONAL_ON_OFF":       5,
		"ATM_ON_OFF":                 6,
		"NFC_ON_OFF":                 7,
		"POS_ON_OFF":                 8,
	}
)

func (x CardControlType) Enum() *CardControlType {
	p := new(CardControlType)
	*p = x
	return p
}

func (x CardControlType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardControlType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_proto_enumTypes[0].Descriptor()
}

func (CardControlType) Type() protoreflect.EnumType {
	return &file_api_card_control_proto_enumTypes[0]
}

func (x CardControlType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardControlType.Descriptor instead.
func (CardControlType) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_proto_rawDescGZIP(), []int{0}
}

// CardTransactionType define the types of transactions that are supported for a card.
type CardTransactionType int32

const (
	CardTransactionType_CARD_TXN_TYPE_UNSPECIFIED CardTransactionType = 0
	CardTransactionType_ATM                       CardTransactionType = 1
	CardTransactionType_POS                       CardTransactionType = 2
	CardTransactionType_ECOMMERCE                 CardTransactionType = 3
	CardTransactionType_NFC                       CardTransactionType = 4 // CONTACTLESS
	CardTransactionType_AUTO_PAY                  CardTransactionType = 5
	CardTransactionType_TXN_ALL                   CardTransactionType = 6
)

// Enum value maps for CardTransactionType.
var (
	CardTransactionType_name = map[int32]string{
		0: "CARD_TXN_TYPE_UNSPECIFIED",
		1: "ATM",
		2: "POS",
		3: "ECOMMERCE",
		4: "NFC",
		5: "AUTO_PAY",
		6: "TXN_ALL",
	}
	CardTransactionType_value = map[string]int32{
		"CARD_TXN_TYPE_UNSPECIFIED": 0,
		"ATM":                       1,
		"POS":                       2,
		"ECOMMERCE":                 3,
		"NFC":                       4,
		"AUTO_PAY":                  5,
		"TXN_ALL":                   6,
	}
)

func (x CardTransactionType) Enum() *CardTransactionType {
	p := new(CardTransactionType)
	*p = x
	return p
}

func (x CardTransactionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardTransactionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_proto_enumTypes[1].Descriptor()
}

func (CardTransactionType) Type() protoreflect.EnumType {
	return &file_api_card_control_proto_enumTypes[1]
}

func (x CardTransactionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardTransactionType.Descriptor instead.
func (CardTransactionType) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_proto_rawDescGZIP(), []int{1}
}

// CardUsageLocationType defines the location where we would want to control the card usage.
type CardUsageLocationType int32

const (
	CardUsageLocationType_LOCATION_TYPE_UNSPECIFIED CardUsageLocationType = 0
	// Defaults to India. Set a control for DOMESTIC attaches the control to the card usage in India.
	CardUsageLocationType_DOMESTIC CardUsageLocationType = 1
	// Any country but India. Set a control for INTERNATIONAL attaches the control to the card usage outside India.
	CardUsageLocationType_INTERNATIONAL CardUsageLocationType = 2
	// Alias for a list of locations defined in this enum.
	CardUsageLocationType_LOCATION_ALL CardUsageLocationType = 3
)

// Enum value maps for CardUsageLocationType.
var (
	CardUsageLocationType_name = map[int32]string{
		0: "LOCATION_TYPE_UNSPECIFIED",
		1: "DOMESTIC",
		2: "INTERNATIONAL",
		3: "LOCATION_ALL",
	}
	CardUsageLocationType_value = map[string]int32{
		"LOCATION_TYPE_UNSPECIFIED": 0,
		"DOMESTIC":                  1,
		"INTERNATIONAL":             2,
		"LOCATION_ALL":              3,
	}
)

func (x CardUsageLocationType) Enum() *CardUsageLocationType {
	p := new(CardUsageLocationType)
	*p = x
	return p
}

func (x CardUsageLocationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardUsageLocationType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_proto_enumTypes[2].Descriptor()
}

func (CardUsageLocationType) Type() protoreflect.EnumType {
	return &file_api_card_control_proto_enumTypes[2]
}

func (x CardUsageLocationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardUsageLocationType.Descriptor instead.
func (CardUsageLocationType) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_proto_rawDescGZIP(), []int{2}
}

// CardControlAction defines the action to be taken for the given card control definition.
// One can switch-on i.e. ENABLE or switch-off a specific card control.
type CardControlAction int32

const (
	CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED CardControlAction = 0
	CardControlAction_ENABLE                       CardControlAction = 1
	CardControlAction_DISABLE                      CardControlAction = 2
)

// Enum value maps for CardControlAction.
var (
	CardControlAction_name = map[int32]string{
		0: "CARD_CTRL_ACTION_UNSPECIFIED",
		1: "ENABLE",
		2: "DISABLE",
	}
	CardControlAction_value = map[string]int32{
		"CARD_CTRL_ACTION_UNSPECIFIED": 0,
		"ENABLE":                       1,
		"DISABLE":                      2,
	}
)

func (x CardControlAction) Enum() *CardControlAction {
	p := new(CardControlAction)
	*p = x
	return p
}

func (x CardControlAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardControlAction) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_proto_enumTypes[3].Descriptor()
}

func (CardControlAction) Type() protoreflect.EnumType {
	return &file_api_card_control_proto_enumTypes[3]
}

func (x CardControlAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardControlAction.Descriptor instead.
func (CardControlAction) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_proto_rawDescGZIP(), []int{3}
}

// CardControlOverallState denotes the overall states for the card controls. A few card controls are initialised during
// onboarding which marks the state as INITIALIZED.
//
// The overall state represents the high level state of all the card controls combined. This is not to be confused with
// individual card control state which is determined using CardControlAction
type CardControlOverallState int32

const (
	CardControlOverallState_CARD_CTRL_STATE_UNSPECIFIED CardControlOverallState = 0
	CardControlOverallState_INITIALIZED                 CardControlOverallState = 1
)

// Enum value maps for CardControlOverallState.
var (
	CardControlOverallState_name = map[int32]string{
		0: "CARD_CTRL_STATE_UNSPECIFIED",
		1: "INITIALIZED",
	}
	CardControlOverallState_value = map[string]int32{
		"CARD_CTRL_STATE_UNSPECIFIED": 0,
		"INITIALIZED":                 1,
	}
)

func (x CardControlOverallState) Enum() *CardControlOverallState {
	p := new(CardControlOverallState)
	*p = x
	return p
}

func (x CardControlOverallState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardControlOverallState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_proto_enumTypes[4].Descriptor()
}

func (CardControlOverallState) Type() protoreflect.EnumType {
	return &file_api_card_control_proto_enumTypes[4]
}

func (x CardControlOverallState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardControlOverallState.Descriptor instead.
func (CardControlOverallState) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_proto_rawDescGZIP(), []int{4}
}

// A collection of card control definition per card.
// Note: this will support basic card controls i.e. any combination of transaction_type, location_type and
// action(ENABLE/DISABLE).
// Expenditure limit management for transaction type and merchant type is out of scope of this.
type ControlData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Defs []*ControlDefinition `protobuf:"bytes,1,rep,name=defs,proto3" json:"defs,omitempty"`
	// Represents the current action applied to the card transaction type.
	// Represents a map from CardTransactionType (converted to string) to CardControlAction.
	TxnStates map[string]CardControlAction `protobuf:"bytes,2,rep,name=txn_states,json=txnStates,proto3" json:"txn_states,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=card.CardControlAction"`
	// Represents the current action applied to the card usage location type.
	// Represents a map from CardUsageLocationType (converted to string) to CardControlAction.
	LocStates map[string]CardControlAction `protobuf:"bytes,3,rep,name=loc_states,json=locStates,proto3" json:"loc_states,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=card.CardControlAction"`
	// the overall state of the card controls
	OverallState CardControlOverallState `protobuf:"varint,4,opt,name=overall_state,json=overallState,proto3,enum=card.CardControlOverallState" json:"overall_state,omitempty"`
}

func (x *ControlData) Reset() {
	*x = ControlData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ControlData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlData) ProtoMessage() {}

func (x *ControlData) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlData.ProtoReflect.Descriptor instead.
func (*ControlData) Descriptor() ([]byte, []int) {
	return file_api_card_control_proto_rawDescGZIP(), []int{0}
}

func (x *ControlData) GetDefs() []*ControlDefinition {
	if x != nil {
		return x.Defs
	}
	return nil
}

func (x *ControlData) GetTxnStates() map[string]CardControlAction {
	if x != nil {
		return x.TxnStates
	}
	return nil
}

func (x *ControlData) GetLocStates() map[string]CardControlAction {
	if x != nil {
		return x.LocStates
	}
	return nil
}

func (x *ControlData) GetOverallState() CardControlOverallState {
	if x != nil {
		return x.OverallState
	}
	return CardControlOverallState_CARD_CTRL_STATE_UNSPECIFIED
}

// ControlDefinition maintains all the data necessary to define the card controls.
// A card control is defined by the card transaction type and the usage location type.
// For example: enabling a card for ATM txns for Domestic usage.
type ControlDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the type of card transaction
	TransactionType CardTransactionType `protobuf:"varint,3,opt,name=transactionType,proto3,enum=card.CardTransactionType" json:"transactionType,omitempty"`
	// the location constraint to be put on the card usage for the given transaction type
	LocationType CardUsageLocationType `protobuf:"varint,4,opt,name=locationType,proto3,enum=card.CardUsageLocationType" json:"locationType,omitempty"`
	// denotes the action taken to turn on/off the card_control
	Action CardControlAction `protobuf:"varint,5,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
	// vendor could be the Issuing bank where the savings bank account is opened or a card_network i.e. a financial service
	// company that enables, processes and settles payments between card issuing banks and merchant banks worldwide.
	Vendor vendorgateway.Vendor `protobuf:"varint,7,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
}

func (x *ControlDefinition) Reset() {
	*x = ControlDefinition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ControlDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlDefinition) ProtoMessage() {}

func (x *ControlDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlDefinition.ProtoReflect.Descriptor instead.
func (*ControlDefinition) Descriptor() ([]byte, []int) {
	return file_api_card_control_proto_rawDescGZIP(), []int{1}
}

func (x *ControlDefinition) GetTransactionType() CardTransactionType {
	if x != nil {
		return x.TransactionType
	}
	return CardTransactionType_CARD_TXN_TYPE_UNSPECIFIED
}

func (x *ControlDefinition) GetLocationType() CardUsageLocationType {
	if x != nil {
		return x.LocationType
	}
	return CardUsageLocationType_LOCATION_TYPE_UNSPECIFIED
}

func (x *ControlDefinition) GetAction() CardControlAction {
	if x != nil {
		return x.Action
	}
	return CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED
}

func (x *ControlDefinition) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

var File_api_card_control_proto protoreflect.FileDescriptor

var file_api_card_control_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x63, 0x61, 0x72, 0x64, 0x1a, 0x1e,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xae,
	0x03, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2b,
	0x0a, 0x04, 0x64, 0x65, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x64, 0x65, 0x66, 0x73, 0x12, 0x3f, 0x0a, 0x0a, 0x74,
	0x78, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x61,
	0x74, 0x61, 0x2e, 0x54, 0x78, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x09, 0x74, 0x78, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x0a,
	0x6c, 0x6f, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x4c, 0x6f, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x42, 0x0a,
	0x0d, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x0c, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x1a, 0x55, 0x0a, 0x0e, 0x54, 0x78, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x55, 0x0a, 0x0e, 0x4c, 0x6f, 0x63, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xf9, 0x01, 0x0a, 0x11, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x55, 0x73, 0x61, 0x67,
	0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x06,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2a, 0xba, 0x01, 0x0a, 0x0f,
	0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1e, 0x0a, 0x1a, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x54, 0x52, 0x4c, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x09, 0x0a, 0x05, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55,
	0x53, 0x50, 0x45, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x45, 0x43, 0x4f, 0x4d, 0x4d,
	0x5f, 0x4f, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x4f, 0x4d,
	0x45, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x4f, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x04, 0x12, 0x18,
	0x0a, 0x14, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f,
	0x4f, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x54, 0x4d, 0x5f,
	0x4f, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x46, 0x43, 0x5f,
	0x4f, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x07, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x4f, 0x53, 0x5f,
	0x4f, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x08, 0x2a, 0x79, 0x0a, 0x13, 0x43, 0x61, 0x72, 0x64,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1d, 0x0a, 0x19, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07,
	0x0a, 0x03, 0x41, 0x54, 0x4d, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x4f, 0x53, 0x10, 0x02,
	0x12, 0x0d, 0x0a, 0x09, 0x45, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x52, 0x43, 0x45, 0x10, 0x03, 0x12,
	0x07, 0x0a, 0x03, 0x4e, 0x46, 0x43, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x55, 0x54, 0x4f,
	0x5f, 0x50, 0x41, 0x59, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x58, 0x4e, 0x5f, 0x41, 0x4c,
	0x4c, 0x10, 0x06, 0x2a, 0x69, 0x0a, 0x15, 0x43, 0x61, 0x72, 0x64, 0x55, 0x73, 0x61, 0x67, 0x65,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19,
	0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x44,
	0x4f, 0x4d, 0x45, 0x53, 0x54, 0x49, 0x43, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c,
	0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x03, 0x2a, 0x4e,
	0x0a, 0x11, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x54, 0x52, 0x4c,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x2a, 0x4b,
	0x0a, 0x17, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x76, 0x65,
	0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x43, 0x54, 0x52, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e,
	0x49, 0x54, 0x49, 0x41, 0x4c, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x01, 0x42, 0x42, 0x0a, 0x1f, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x5a, 0x1f,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_control_proto_rawDescOnce sync.Once
	file_api_card_control_proto_rawDescData = file_api_card_control_proto_rawDesc
)

func file_api_card_control_proto_rawDescGZIP() []byte {
	file_api_card_control_proto_rawDescOnce.Do(func() {
		file_api_card_control_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_control_proto_rawDescData)
	})
	return file_api_card_control_proto_rawDescData
}

var file_api_card_control_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_api_card_control_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_card_control_proto_goTypes = []interface{}{
	(CardControlType)(0),         // 0: card.CardControlType
	(CardTransactionType)(0),     // 1: card.CardTransactionType
	(CardUsageLocationType)(0),   // 2: card.CardUsageLocationType
	(CardControlAction)(0),       // 3: card.CardControlAction
	(CardControlOverallState)(0), // 4: card.CardControlOverallState
	(*ControlData)(nil),          // 5: card.ControlData
	(*ControlDefinition)(nil),    // 6: card.ControlDefinition
	nil,                          // 7: card.ControlData.TxnStatesEntry
	nil,                          // 8: card.ControlData.LocStatesEntry
	(vendorgateway.Vendor)(0),    // 9: vendorgateway.Vendor
}
var file_api_card_control_proto_depIdxs = []int32{
	6,  // 0: card.ControlData.defs:type_name -> card.ControlDefinition
	7,  // 1: card.ControlData.txn_states:type_name -> card.ControlData.TxnStatesEntry
	8,  // 2: card.ControlData.loc_states:type_name -> card.ControlData.LocStatesEntry
	4,  // 3: card.ControlData.overall_state:type_name -> card.CardControlOverallState
	1,  // 4: card.ControlDefinition.transactionType:type_name -> card.CardTransactionType
	2,  // 5: card.ControlDefinition.locationType:type_name -> card.CardUsageLocationType
	3,  // 6: card.ControlDefinition.action:type_name -> card.CardControlAction
	9,  // 7: card.ControlDefinition.vendor:type_name -> vendorgateway.Vendor
	3,  // 8: card.ControlData.TxnStatesEntry.value:type_name -> card.CardControlAction
	3,  // 9: card.ControlData.LocStatesEntry.value:type_name -> card.CardControlAction
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_api_card_control_proto_init() }
func file_api_card_control_proto_init() {
	if File_api_card_control_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_control_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ControlData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ControlDefinition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_control_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_control_proto_goTypes,
		DependencyIndexes: file_api_card_control_proto_depIdxs,
		EnumInfos:         file_api_card_control_proto_enumTypes,
		MessageInfos:      file_api_card_control_proto_msgTypes,
	}.Build()
	File_api_card_control_proto = out.File
	file_api_card_control_proto_rawDesc = nil
	file_api_card_control_proto_goTypes = nil
	file_api_card_control_proto_depIdxs = nil
}
