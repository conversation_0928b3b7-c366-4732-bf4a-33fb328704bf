// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/card_notifications.proto

package card

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	enums "github.com/epifi/gamma/api/card/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = enums.NotificationType(0)
)

// Validate checks the field values on CardNotification with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CardNotification) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardNotification with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardNotificationMultiError, or nil if none found.
func (m *CardNotification) ValidateAll() error {
	return m.validate(true)
}

func (m *CardNotification) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CardId

	// no validation rules for RetrievalReferenceNumber

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetNotificationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "NotificationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "NotificationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotificationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardNotificationValidationError{
				field:  "NotificationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NotificationType

	if all {
		switch v := interface{}(m.GetMerchantDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "MerchantDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "MerchantDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMerchantDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardNotificationValidationError{
				field:  "MerchantDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuthSwitchDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "AuthSwitchDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "AuthSwitchDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthSwitchDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardNotificationValidationError{
				field:  "AuthSwitchDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRemitterDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "RemitterDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "RemitterDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRemitterDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardNotificationValidationError{
				field:  "RemitterDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetDetailedStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "DetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "DetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetailedStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardNotificationValidationError{
				field:  "DetailedStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NotificationSource

	if all {
		switch v := interface{}(m.GetDedupeId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "DedupeId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "DedupeId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDedupeId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardNotificationValidationError{
				field:  "DedupeId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNotificationEventTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "NotificationEventTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "NotificationEventTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotificationEventTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardNotificationValidationError{
				field:  "NotificationEventTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardNotificationValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardNotificationValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardNotificationValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardNotificationValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExternalRefId

	if len(errors) > 0 {
		return CardNotificationMultiError(errors)
	}

	return nil
}

// CardNotificationMultiError is an error wrapping multiple validation errors
// returned by CardNotification.ValidateAll() if the designated constraints
// aren't met.
type CardNotificationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardNotificationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardNotificationMultiError) AllErrors() []error { return m }

// CardNotificationValidationError is the validation error returned by
// CardNotification.Validate if the designated constraints aren't met.
type CardNotificationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardNotificationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardNotificationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardNotificationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardNotificationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardNotificationValidationError) ErrorName() string { return "CardNotificationValidationError" }

// Error satisfies the builtin error interface
func (e CardNotificationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardNotification.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardNotificationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardNotificationValidationError{}

// Validate checks the field values on DetailedStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DetailedStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetailedStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DetailedStatusMultiError,
// or nil if none found.
func (m *DetailedStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *DetailedStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StatusCode

	// no validation rules for StatusDescription

	// no validation rules for RawStatusCode

	// no validation rules for RawStatusDescription

	if len(errors) > 0 {
		return DetailedStatusMultiError(errors)
	}

	return nil
}

// DetailedStatusMultiError is an error wrapping multiple validation errors
// returned by DetailedStatus.ValidateAll() if the designated constraints
// aren't met.
type DetailedStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetailedStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetailedStatusMultiError) AllErrors() []error { return m }

// DetailedStatusValidationError is the validation error returned by
// DetailedStatus.Validate if the designated constraints aren't met.
type DetailedStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetailedStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetailedStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetailedStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetailedStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetailedStatusValidationError) ErrorName() string { return "DetailedStatusValidationError" }

// Error satisfies the builtin error interface
func (e DetailedStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetailedStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetailedStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetailedStatusValidationError{}

// Validate checks the field values on NotificationDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NotificationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NotificationDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NotificationDetailsMultiError, or nil if none found.
func (m *NotificationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *NotificationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RawNotificationData

	// no validation rules for MessageType

	switch v := m.Details.(type) {
	case *NotificationDetails_FinancialSwitchNotificationDetails:
		if v == nil {
			err := NotificationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFinancialSwitchNotificationDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NotificationDetailsValidationError{
						field:  "FinancialSwitchNotificationDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NotificationDetailsValidationError{
						field:  "FinancialSwitchNotificationDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFinancialSwitchNotificationDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NotificationDetailsValidationError{
					field:  "FinancialSwitchNotificationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *NotificationDetails_NonFinancialSwitchNotificationDetails:
		if v == nil {
			err := NotificationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNonFinancialSwitchNotificationDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NotificationDetailsValidationError{
						field:  "NonFinancialSwitchNotificationDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NotificationDetailsValidationError{
						field:  "NonFinancialSwitchNotificationDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNonFinancialSwitchNotificationDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NotificationDetailsValidationError{
					field:  "NonFinancialSwitchNotificationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return NotificationDetailsMultiError(errors)
	}

	return nil
}

// NotificationDetailsMultiError is an error wrapping multiple validation
// errors returned by NotificationDetails.ValidateAll() if the designated
// constraints aren't met.
type NotificationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NotificationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NotificationDetailsMultiError) AllErrors() []error { return m }

// NotificationDetailsValidationError is the validation error returned by
// NotificationDetails.Validate if the designated constraints aren't met.
type NotificationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NotificationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NotificationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NotificationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NotificationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NotificationDetailsValidationError) ErrorName() string {
	return "NotificationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e NotificationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNotificationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NotificationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NotificationDetailsValidationError{}

// Validate checks the field values on FinancialSwitchNotificationDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FinancialSwitchNotificationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FinancialSwitchNotificationDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FinancialSwitchNotificationDetailsMultiError, or nil if none found.
func (m *FinancialSwitchNotificationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FinancialSwitchNotificationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnCategory

	if all {
		switch v := interface{}(m.GetTxnTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FinancialSwitchNotificationDetailsValidationError{
					field:  "TxnTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FinancialSwitchNotificationDetailsValidationError{
					field:  "TxnTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FinancialSwitchNotificationDetailsValidationError{
				field:  "TxnTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTxnDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FinancialSwitchNotificationDetailsValidationError{
					field:  "TxnDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FinancialSwitchNotificationDetailsValidationError{
					field:  "TxnDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FinancialSwitchNotificationDetailsValidationError{
				field:  "TxnDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTxnAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FinancialSwitchNotificationDetailsValidationError{
					field:  "TxnAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FinancialSwitchNotificationDetailsValidationError{
					field:  "TxnAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FinancialSwitchNotificationDetailsValidationError{
				field:  "TxnAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TxnRemarks

	// no validation rules for CountryCode

	// no validation rules for IsForexMarkupTransaction

	// no validation rules for IsDccTransaction

	// no validation rules for IsCardTapTransaction

	// no validation rules for IsDeviceTapTransaction

	if all {
		switch v := interface{}(m.GetTxnAmountInOrgCurrency()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FinancialSwitchNotificationDetailsValidationError{
					field:  "TxnAmountInOrgCurrency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FinancialSwitchNotificationDetailsValidationError{
					field:  "TxnAmountInOrgCurrency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnAmountInOrgCurrency()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FinancialSwitchNotificationDetailsValidationError{
				field:  "TxnAmountInOrgCurrency",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AtmPincode

	if len(errors) > 0 {
		return FinancialSwitchNotificationDetailsMultiError(errors)
	}

	return nil
}

// FinancialSwitchNotificationDetailsMultiError is an error wrapping multiple
// validation errors returned by
// FinancialSwitchNotificationDetails.ValidateAll() if the designated
// constraints aren't met.
type FinancialSwitchNotificationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FinancialSwitchNotificationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FinancialSwitchNotificationDetailsMultiError) AllErrors() []error { return m }

// FinancialSwitchNotificationDetailsValidationError is the validation error
// returned by FinancialSwitchNotificationDetails.Validate if the designated
// constraints aren't met.
type FinancialSwitchNotificationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FinancialSwitchNotificationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FinancialSwitchNotificationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FinancialSwitchNotificationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FinancialSwitchNotificationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FinancialSwitchNotificationDetailsValidationError) ErrorName() string {
	return "FinancialSwitchNotificationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FinancialSwitchNotificationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFinancialSwitchNotificationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FinancialSwitchNotificationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FinancialSwitchNotificationDetailsValidationError{}

// Validate checks the field values on NonFinancialSwitchNotificationDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *NonFinancialSwitchNotificationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NonFinancialSwitchNotificationDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// NonFinancialSwitchNotificationDetailsMultiError, or nil if none found.
func (m *NonFinancialSwitchNotificationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *NonFinancialSwitchNotificationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return NonFinancialSwitchNotificationDetailsMultiError(errors)
	}

	return nil
}

// NonFinancialSwitchNotificationDetailsMultiError is an error wrapping
// multiple validation errors returned by
// NonFinancialSwitchNotificationDetails.ValidateAll() if the designated
// constraints aren't met.
type NonFinancialSwitchNotificationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NonFinancialSwitchNotificationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NonFinancialSwitchNotificationDetailsMultiError) AllErrors() []error { return m }

// NonFinancialSwitchNotificationDetailsValidationError is the validation error
// returned by NonFinancialSwitchNotificationDetails.Validate if the
// designated constraints aren't met.
type NonFinancialSwitchNotificationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NonFinancialSwitchNotificationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NonFinancialSwitchNotificationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NonFinancialSwitchNotificationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NonFinancialSwitchNotificationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NonFinancialSwitchNotificationDetailsValidationError) ErrorName() string {
	return "NonFinancialSwitchNotificationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e NonFinancialSwitchNotificationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNonFinancialSwitchNotificationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NonFinancialSwitchNotificationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NonFinancialSwitchNotificationDetailsValidationError{}

// Validate checks the field values on MerchantDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MerchantDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MerchantDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MerchantDetailsMultiError, or nil if none found.
func (m *MerchantDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *MerchantDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MerchantName

	// no validation rules for MerchantLocation

	// no validation rules for MerchantId

	// no validation rules for SubMerchantId

	// no validation rules for Mcc

	// no validation rules for TerminalId

	if len(errors) > 0 {
		return MerchantDetailsMultiError(errors)
	}

	return nil
}

// MerchantDetailsMultiError is an error wrapping multiple validation errors
// returned by MerchantDetails.ValidateAll() if the designated constraints
// aren't met.
type MerchantDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MerchantDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MerchantDetailsMultiError) AllErrors() []error { return m }

// MerchantDetailsValidationError is the validation error returned by
// MerchantDetails.Validate if the designated constraints aren't met.
type MerchantDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MerchantDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MerchantDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MerchantDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MerchantDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MerchantDetailsValidationError) ErrorName() string { return "MerchantDetailsValidationError" }

// Error satisfies the builtin error interface
func (e MerchantDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMerchantDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MerchantDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MerchantDetailsValidationError{}

// Validate checks the field values on AuthSwitchDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AuthSwitchDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthSwitchDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthSwitchDetailsMultiError, or nil if none found.
func (m *AuthSwitchDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthSwitchDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AuthCode

	// no validation rules for AuthorizationSwitch

	if len(errors) > 0 {
		return AuthSwitchDetailsMultiError(errors)
	}

	return nil
}

// AuthSwitchDetailsMultiError is an error wrapping multiple validation errors
// returned by AuthSwitchDetails.ValidateAll() if the designated constraints
// aren't met.
type AuthSwitchDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthSwitchDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthSwitchDetailsMultiError) AllErrors() []error { return m }

// AuthSwitchDetailsValidationError is the validation error returned by
// AuthSwitchDetails.Validate if the designated constraints aren't met.
type AuthSwitchDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthSwitchDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthSwitchDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthSwitchDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthSwitchDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthSwitchDetailsValidationError) ErrorName() string {
	return "AuthSwitchDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AuthSwitchDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthSwitchDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthSwitchDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthSwitchDetailsValidationError{}

// Validate checks the field values on RemitterDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RemitterDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemitterDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemitterDetailsMultiError, or nil if none found.
func (m *RemitterDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *RemitterDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RemitterInstrumentType

	// no validation rules for RemitterCode

	// no validation rules for PaymentGateway

	if len(errors) > 0 {
		return RemitterDetailsMultiError(errors)
	}

	return nil
}

// RemitterDetailsMultiError is an error wrapping multiple validation errors
// returned by RemitterDetails.ValidateAll() if the designated constraints
// aren't met.
type RemitterDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemitterDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemitterDetailsMultiError) AllErrors() []error { return m }

// RemitterDetailsValidationError is the validation error returned by
// RemitterDetails.Validate if the designated constraints aren't met.
type RemitterDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemitterDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemitterDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemitterDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemitterDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemitterDetailsValidationError) ErrorName() string { return "RemitterDetailsValidationError" }

// Error satisfies the builtin error interface
func (e RemitterDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemitterDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemitterDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemitterDetailsValidationError{}

// Validate checks the field values on DedupeId with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DedupeId) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DedupeId with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DedupeIdMultiError, or nil
// if none found.
func (m *DedupeId) ValidateAll() error {
	return m.validate(true)
}

func (m *DedupeId) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for RetrievalReferenceNumber

	if all {
		switch v := interface{}(m.GetTxnTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DedupeIdValidationError{
					field:  "TxnTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DedupeIdValidationError{
					field:  "TxnTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DedupeIdValidationError{
				field:  "TxnTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	if len(errors) > 0 {
		return DedupeIdMultiError(errors)
	}

	return nil
}

// DedupeIdMultiError is an error wrapping multiple validation errors returned
// by DedupeId.ValidateAll() if the designated constraints aren't met.
type DedupeIdMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DedupeIdMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DedupeIdMultiError) AllErrors() []error { return m }

// DedupeIdValidationError is the validation error returned by
// DedupeId.Validate if the designated constraints aren't met.
type DedupeIdValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DedupeIdValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DedupeIdValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DedupeIdValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DedupeIdValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DedupeIdValidationError) ErrorName() string { return "DedupeIdValidationError" }

// Error satisfies the builtin error interface
func (e DedupeIdValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDedupeId.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DedupeIdValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DedupeIdValidationError{}
