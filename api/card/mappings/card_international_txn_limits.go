package mappings

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"github.com/mohae/deepcopy"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
)

// CardInternationalTxnLimits - struct to unmarshal a single json object
type CardInternationalTxnLimits struct {
	Vendor      string           `json:"Vendor"`
	CountryCode string           `json:"CountryCode"`
	Limits      map[string]int32 `json:"Limits"`
	CountryName string           `json:"CountryName"`
	CountryFlag string           `json:"CountryFlag"`
}

// CardInternationalTxnLimitsJsonMappings - struct to unmarshal json file
type CardInternationalTxnLimitsJsonMappings struct {
	CardInternationalTxnLimits []CardInternationalTxnLimits `json:"cardInternationalTxnLimits"`
}

var (
	cardInternationalTxnLimitsMap = make(map[commonvgpb.Vendor]map[string]*CardInternationalTxnLimits)
)

func (c *CardInternationalTxnLimits) GetAtmWithdrawalLimit() int32 {
	atmLimit, ok := c.Limits["ATM"]
	if !ok {
		logger.ErrorNoCtx("limits not found for atm withdrawal", zap.String(logger.COUNTRY, c.CountryCode))
		atmLimit = 0
	}
	return atmLimit
}

// GetCardInternationalTxnLimit returns CardInternationalTxnLimits for a given vendor and countryCode
func GetCardInternationalTxnLimit(vendor commonvgpb.Vendor, countryCode string) *CardInternationalTxnLimits {
	if _, ok := cardInternationalTxnLimitsMap[vendor]; ok {
		if _, countryCodeOk := cardInternationalTxnLimitsMap[vendor][countryCode]; countryCodeOk {
			cardInternationalTxnLimits := cardInternationalTxnLimitsMap[vendor][countryCode]

			deepCopy := deepcopy.Copy(cardInternationalTxnLimits).(*CardInternationalTxnLimits)
			return deepCopy
		}
	}

	logger.ErrorNoCtx(fmt.Sprintf("No card txn limits found for this country %s, vendor %s", countryCode, vendor))
	return &CardInternationalTxnLimits{
		Vendor:      vendor.String(),
		CountryCode: countryCode,
		Limits:      map[string]int32{},
		CountryName: "",
		CountryFlag: "",
	}
}

// GetAllInternationalTxnLimit returns international transaction limits for given vendor
// it returns a map of countryCode to TxnLimits
func GetAllInternationalTxnLimit(vendor commonvgpb.Vendor) map[string]*CardInternationalTxnLimits {
	allTxnLimit, ok := cardInternationalTxnLimitsMap[vendor]
	if !ok {
		logger.ErrorNoCtx(fmt.Sprintf("No card txn limits found for this vendor: %s", vendor))
		return nil
	}

	// Deep copy the map using the deepcopy package
	deepCopy := deepcopy.Copy(allTxnLimit).(map[string]*CardInternationalTxnLimits)

	return deepCopy
}

// LoadCardInternationalTxnLimit loads json file with card txn limits and converts it into a map
func LoadCardInternationalTxnLimit(cardInternationalTxnLimitsFilePath string) error {
	cfgDir, err := cfg.GetConfigDir()
	if err != nil {
		return fmt.Errorf("could not get config directory: %w", err)
	}
	file, err := os.ReadFile(filepath.Join(cfgDir, cardInternationalTxnLimitsFilePath)) //nolint:gosec
	if err != nil {
		return fmt.Errorf("failed to open card international txns limits mapping file, %v", err)
	}

	cardInternalTxnLimitsJsonMappings := CardInternationalTxnLimitsJsonMappings{}
	err = json.Unmarshal(file, &cardInternalTxnLimitsJsonMappings)
	if err != nil {
		return fmt.Errorf("failed to unmarshal card response status code json to response status code json struct, %v", err)
	}

	for _, cardTxnLimits := range cardInternalTxnLimitsJsonMappings.CardInternationalTxnLimits {
		// check if vendor string is a correct enum string
		vendorInt, ok := commonvgpb.Vendor_value[cardTxnLimits.Vendor]
		if !ok {
			logger.ErrorNoCtx(fmt.Sprintf("Invalid vendor received while mapping card txn limits: %s", cardTxnLimits.Vendor))
			continue
		}
		parseToCardTxnLimitsMap(cardTxnLimits, commonvgpb.Vendor(vendorInt))
	}

	return nil
}

// parseToCardTxnLimitsMap maps countryCode to CardInternationalTxnLimits
func parseToCardTxnLimitsMap(cardTxnLimits CardInternationalTxnLimits, vendor commonvgpb.Vendor) {
	if _, ok := cardInternationalTxnLimitsMap[vendor]; !ok {
		cardInternationalTxnLimitsMap[vendor] = make(map[string]*CardInternationalTxnLimits)
	}
	cardInternationalTxnLimitsMap[vendor][cardTxnLimits.CountryCode] = &cardTxnLimits
}
