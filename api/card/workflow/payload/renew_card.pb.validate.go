// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/workflow/payload/renew_card.proto

package payload

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RenewCardPayload with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RenewCardPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RenewCardPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RenewCardPayloadMultiError, or nil if none found.
func (m *RenewCardPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *RenewCardPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for ActorId

	if len(errors) > 0 {
		return RenewCardPayloadMultiError(errors)
	}

	return nil
}

// RenewCardPayloadMultiError is an error wrapping multiple validation errors
// returned by RenewCardPayload.ValidateAll() if the designated constraints
// aren't met.
type RenewCardPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RenewCardPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RenewCardPayloadMultiError) AllErrors() []error { return m }

// RenewCardPayloadValidationError is the validation error returned by
// RenewCardPayload.Validate if the designated constraints aren't met.
type RenewCardPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RenewCardPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RenewCardPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RenewCardPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RenewCardPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RenewCardPayloadValidationError) ErrorName() string { return "RenewCardPayloadValidationError" }

// Error satisfies the builtin error interface
func (e RenewCardPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRenewCardPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RenewCardPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RenewCardPayloadValidationError{}
