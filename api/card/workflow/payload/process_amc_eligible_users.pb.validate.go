// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/workflow/payload/process_amc_eligible_users.proto

package payload

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ProcessAmcEligibleUsers with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessAmcEligibleUsers) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessAmcEligibleUsers with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessAmcEligibleUsersMultiError, or nil if none found.
func (m *ProcessAmcEligibleUsers) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessAmcEligibleUsers) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for S3Path

	if all {
		switch v := interface{}(m.GetFileGenDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessAmcEligibleUsersValidationError{
					field:  "FileGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessAmcEligibleUsersValidationError{
					field:  "FileGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileGenDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessAmcEligibleUsersValidationError{
				field:  "FileGenDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessAmcEligibleUsersMultiError(errors)
	}

	return nil
}

// ProcessAmcEligibleUsersMultiError is an error wrapping multiple validation
// errors returned by ProcessAmcEligibleUsers.ValidateAll() if the designated
// constraints aren't met.
type ProcessAmcEligibleUsersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessAmcEligibleUsersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessAmcEligibleUsersMultiError) AllErrors() []error { return m }

// ProcessAmcEligibleUsersValidationError is the validation error returned by
// ProcessAmcEligibleUsers.Validate if the designated constraints aren't met.
type ProcessAmcEligibleUsersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessAmcEligibleUsersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessAmcEligibleUsersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessAmcEligibleUsersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessAmcEligibleUsersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessAmcEligibleUsersValidationError) ErrorName() string {
	return "ProcessAmcEligibleUsersValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessAmcEligibleUsersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessAmcEligibleUsers.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessAmcEligibleUsersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessAmcEligibleUsersValidationError{}

// Validate checks the field values on ProcessAmcEligibleUsersBatch with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessAmcEligibleUsersBatch) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessAmcEligibleUsersBatch with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessAmcEligibleUsersBatchMultiError, or nil if none found.
func (m *ProcessAmcEligibleUsersBatch) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessAmcEligibleUsersBatch) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EligibleUsersS3Path

	// no validation rules for FailedUsersS3Path

	// no validation rules for BatchNumber

	if all {
		switch v := interface{}(m.GetFileGenDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessAmcEligibleUsersBatchValidationError{
					field:  "FileGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessAmcEligibleUsersBatchValidationError{
					field:  "FileGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileGenDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessAmcEligibleUsersBatchValidationError{
				field:  "FileGenDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UseBaseFileS3Path

	if len(errors) > 0 {
		return ProcessAmcEligibleUsersBatchMultiError(errors)
	}

	return nil
}

// ProcessAmcEligibleUsersBatchMultiError is an error wrapping multiple
// validation errors returned by ProcessAmcEligibleUsersBatch.ValidateAll() if
// the designated constraints aren't met.
type ProcessAmcEligibleUsersBatchMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessAmcEligibleUsersBatchMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessAmcEligibleUsersBatchMultiError) AllErrors() []error { return m }

// ProcessAmcEligibleUsersBatchValidationError is the validation error returned
// by ProcessAmcEligibleUsersBatch.Validate if the designated constraints
// aren't met.
type ProcessAmcEligibleUsersBatchValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessAmcEligibleUsersBatchValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessAmcEligibleUsersBatchValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessAmcEligibleUsersBatchValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessAmcEligibleUsersBatchValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessAmcEligibleUsersBatchValidationError) ErrorName() string {
	return "ProcessAmcEligibleUsersBatchValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessAmcEligibleUsersBatchValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessAmcEligibleUsersBatch.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessAmcEligibleUsersBatchValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessAmcEligibleUsersBatchValidationError{}
