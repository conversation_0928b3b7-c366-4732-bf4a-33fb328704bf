// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/workflow/payload/physical_card_dispatch.proto

package payload

import (
	provisioning "github.com/epifi/gamma/api/card/provisioning"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PhysicalCardDispatchInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// card id of the card which needs to be dispatched
	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// address type of the user's address where card needs to be delivered
	AddressType typesv2.AddressType `protobuf:"varint,2,opt,name=address_type,json=addressType,proto3,enum=api.typesv2.AddressType" json:"address_type,omitempty"`
	// amount to be paid to execute transaction
	Amount *typesv2.Money `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// client request id associated with the payment initiated
	PaymentClientReqId string `protobuf:"bytes,4,opt,name=payment_client_req_id,json=paymentClientReqId,proto3" json:"payment_client_req_id,omitempty"`
	// actor id of the user associated with card
	ActorId string `protobuf:"bytes,5,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// entry_point is used to identify from which flow physical dispatch was trigger
	EntryPoint                    provisioning.UIEntryPoint `protobuf:"varint,6,opt,name=entry_point,json=entryPoint,proto3,enum=card.provisioning.UIEntryPoint" json:"entry_point,omitempty"`
	IsChargesCollectionApiEnabled bool                      `protobuf:"varint,7,opt,name=is_charges_collection_api_enabled,json=isChargesCollectionApiEnabled,proto3" json:"is_charges_collection_api_enabled,omitempty"`
	AmountWithoutGst              *typesv2.Money            `protobuf:"bytes,8,opt,name=amount_without_gst,json=amountWithoutGst,proto3" json:"amount_without_gst,omitempty"`
	// Flag to control charges collection flow, whether to go via v2 flow or v1 flow.
	// V2 signifying internal changes at card side, at vg layer endpoints are same only.
	IsChargesCollectionApiV2Enabled bool `protobuf:"varint,9,opt,name=is_charges_collection_api_v2_enabled,json=isChargesCollectionApiV2Enabled,proto3" json:"is_charges_collection_api_v2_enabled,omitempty"`
}

func (x *PhysicalCardDispatchInfo) Reset() {
	*x = PhysicalCardDispatchInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_workflow_payload_physical_card_dispatch_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhysicalCardDispatchInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhysicalCardDispatchInfo) ProtoMessage() {}

func (x *PhysicalCardDispatchInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_workflow_payload_physical_card_dispatch_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhysicalCardDispatchInfo.ProtoReflect.Descriptor instead.
func (*PhysicalCardDispatchInfo) Descriptor() ([]byte, []int) {
	return file_api_card_workflow_payload_physical_card_dispatch_proto_rawDescGZIP(), []int{0}
}

func (x *PhysicalCardDispatchInfo) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *PhysicalCardDispatchInfo) GetAddressType() typesv2.AddressType {
	if x != nil {
		return x.AddressType
	}
	return typesv2.AddressType(0)
}

func (x *PhysicalCardDispatchInfo) GetAmount() *typesv2.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *PhysicalCardDispatchInfo) GetPaymentClientReqId() string {
	if x != nil {
		return x.PaymentClientReqId
	}
	return ""
}

func (x *PhysicalCardDispatchInfo) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *PhysicalCardDispatchInfo) GetEntryPoint() provisioning.UIEntryPoint {
	if x != nil {
		return x.EntryPoint
	}
	return provisioning.UIEntryPoint(0)
}

func (x *PhysicalCardDispatchInfo) GetIsChargesCollectionApiEnabled() bool {
	if x != nil {
		return x.IsChargesCollectionApiEnabled
	}
	return false
}

func (x *PhysicalCardDispatchInfo) GetAmountWithoutGst() *typesv2.Money {
	if x != nil {
		return x.AmountWithoutGst
	}
	return nil
}

func (x *PhysicalCardDispatchInfo) GetIsChargesCollectionApiV2Enabled() bool {
	if x != nil {
		return x.IsChargesCollectionApiV2Enabled
	}
	return false
}

var File_api_card_workflow_payload_physical_card_dispatch_proto protoreflect.FileDescriptor

var file_api_card_workflow_payload_physical_card_dispatch_proto_rawDesc = []byte{
	0x0a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2f, 0x70, 0x68, 0x79, 0x73,
	0x69, 0x63, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x87, 0x04, 0x0a, 0x18, 0x50,
	0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x12, 0x3b, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x15, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67,
	0x2e, 0x55, 0x49, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x0a, 0x65,
	0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x48, 0x0a, 0x21, 0x69, 0x73, 0x5f,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x1d, 0x69, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x69, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x40, 0x0a, 0x12, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x77, 0x69,
	0x74, 0x68, 0x6f, 0x75, 0x74, 0x5f, 0x67, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x10, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68, 0x6f,
	0x75, 0x74, 0x47, 0x73, 0x74, 0x12, 0x4d, 0x0a, 0x24, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61,
	0x70, 0x69, 0x5f, 0x76, 0x32, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x1f, 0x69, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x69, 0x56, 0x32, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x42, 0x64, 0x0a, 0x30, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x5a, 0x30, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x2f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_card_workflow_payload_physical_card_dispatch_proto_rawDescOnce sync.Once
	file_api_card_workflow_payload_physical_card_dispatch_proto_rawDescData = file_api_card_workflow_payload_physical_card_dispatch_proto_rawDesc
)

func file_api_card_workflow_payload_physical_card_dispatch_proto_rawDescGZIP() []byte {
	file_api_card_workflow_payload_physical_card_dispatch_proto_rawDescOnce.Do(func() {
		file_api_card_workflow_payload_physical_card_dispatch_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_workflow_payload_physical_card_dispatch_proto_rawDescData)
	})
	return file_api_card_workflow_payload_physical_card_dispatch_proto_rawDescData
}

var file_api_card_workflow_payload_physical_card_dispatch_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_card_workflow_payload_physical_card_dispatch_proto_goTypes = []interface{}{
	(*PhysicalCardDispatchInfo)(nil), // 0: payload.PhysicalCardDispatchInfo
	(typesv2.AddressType)(0),         // 1: api.typesv2.AddressType
	(*typesv2.Money)(nil),            // 2: api.typesv2.Money
	(provisioning.UIEntryPoint)(0),   // 3: card.provisioning.UIEntryPoint
}
var file_api_card_workflow_payload_physical_card_dispatch_proto_depIdxs = []int32{
	1, // 0: payload.PhysicalCardDispatchInfo.address_type:type_name -> api.typesv2.AddressType
	2, // 1: payload.PhysicalCardDispatchInfo.amount:type_name -> api.typesv2.Money
	3, // 2: payload.PhysicalCardDispatchInfo.entry_point:type_name -> card.provisioning.UIEntryPoint
	2, // 3: payload.PhysicalCardDispatchInfo.amount_without_gst:type_name -> api.typesv2.Money
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_card_workflow_payload_physical_card_dispatch_proto_init() }
func file_api_card_workflow_payload_physical_card_dispatch_proto_init() {
	if File_api_card_workflow_payload_physical_card_dispatch_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_workflow_payload_physical_card_dispatch_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhysicalCardDispatchInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_workflow_payload_physical_card_dispatch_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_workflow_payload_physical_card_dispatch_proto_goTypes,
		DependencyIndexes: file_api_card_workflow_payload_physical_card_dispatch_proto_depIdxs,
		MessageInfos:      file_api_card_workflow_payload_physical_card_dispatch_proto_msgTypes,
	}.Build()
	File_api_card_workflow_payload_physical_card_dispatch_proto = out.File
	file_api_card_workflow_payload_physical_card_dispatch_proto_rawDesc = nil
	file_api_card_workflow_payload_physical_card_dispatch_proto_goTypes = nil
	file_api_card_workflow_payload_physical_card_dispatch_proto_depIdxs = nil
}
