// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/workflow/payload/physical_card_dispatch.proto

package payload

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	provisioning "github.com/epifi/gamma/api/card/provisioning"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = provisioning.UIEntryPoint(0)

	_ = typesv2.AddressType(0)
)

// Validate checks the field values on PhysicalCardDispatchInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PhysicalCardDispatchInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PhysicalCardDispatchInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PhysicalCardDispatchInfoMultiError, or nil if none found.
func (m *PhysicalCardDispatchInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PhysicalCardDispatchInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for AddressType

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PhysicalCardDispatchInfoValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PhysicalCardDispatchInfoValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PhysicalCardDispatchInfoValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PaymentClientReqId

	// no validation rules for ActorId

	// no validation rules for EntryPoint

	// no validation rules for IsChargesCollectionApiEnabled

	if all {
		switch v := interface{}(m.GetAmountWithoutGst()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PhysicalCardDispatchInfoValidationError{
					field:  "AmountWithoutGst",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PhysicalCardDispatchInfoValidationError{
					field:  "AmountWithoutGst",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountWithoutGst()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PhysicalCardDispatchInfoValidationError{
				field:  "AmountWithoutGst",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsChargesCollectionApiV2Enabled

	if len(errors) > 0 {
		return PhysicalCardDispatchInfoMultiError(errors)
	}

	return nil
}

// PhysicalCardDispatchInfoMultiError is an error wrapping multiple validation
// errors returned by PhysicalCardDispatchInfo.ValidateAll() if the designated
// constraints aren't met.
type PhysicalCardDispatchInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PhysicalCardDispatchInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PhysicalCardDispatchInfoMultiError) AllErrors() []error { return m }

// PhysicalCardDispatchInfoValidationError is the validation error returned by
// PhysicalCardDispatchInfo.Validate if the designated constraints aren't met.
type PhysicalCardDispatchInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PhysicalCardDispatchInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PhysicalCardDispatchInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PhysicalCardDispatchInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PhysicalCardDispatchInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PhysicalCardDispatchInfoValidationError) ErrorName() string {
	return "PhysicalCardDispatchInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PhysicalCardDispatchInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPhysicalCardDispatchInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PhysicalCardDispatchInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PhysicalCardDispatchInfoValidationError{}
