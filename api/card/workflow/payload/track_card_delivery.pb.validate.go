// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/workflow/payload/track_card_delivery.proto

package payload

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TrackCardDeliveryPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TrackCardDeliveryPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TrackCardDeliveryPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TrackCardDeliveryPayloadMultiError, or nil if none found.
func (m *TrackCardDeliveryPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *TrackCardDeliveryPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for ActorId

	if len(errors) > 0 {
		return TrackCardDeliveryPayloadMultiError(errors)
	}

	return nil
}

// TrackCardDeliveryPayloadMultiError is an error wrapping multiple validation
// errors returned by TrackCardDeliveryPayload.ValidateAll() if the designated
// constraints aren't met.
type TrackCardDeliveryPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TrackCardDeliveryPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TrackCardDeliveryPayloadMultiError) AllErrors() []error { return m }

// TrackCardDeliveryPayloadValidationError is the validation error returned by
// TrackCardDeliveryPayload.Validate if the designated constraints aren't met.
type TrackCardDeliveryPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TrackCardDeliveryPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TrackCardDeliveryPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TrackCardDeliveryPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TrackCardDeliveryPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TrackCardDeliveryPayloadValidationError) ErrorName() string {
	return "TrackCardDeliveryPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e TrackCardDeliveryPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTrackCardDeliveryPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TrackCardDeliveryPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TrackCardDeliveryPayloadValidationError{}
