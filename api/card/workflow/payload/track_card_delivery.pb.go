// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/workflow/payload/track_card_delivery.proto

package payload

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrackCardDeliveryPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of card to be tracked
	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// actor of user
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *TrackCardDeliveryPayload) Reset() {
	*x = TrackCardDeliveryPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_workflow_payload_track_card_delivery_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackCardDeliveryPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackCardDeliveryPayload) ProtoMessage() {}

func (x *TrackCardDeliveryPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_workflow_payload_track_card_delivery_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackCardDeliveryPayload.ProtoReflect.Descriptor instead.
func (*TrackCardDeliveryPayload) Descriptor() ([]byte, []int) {
	return file_api_card_workflow_payload_track_card_delivery_proto_rawDescGZIP(), []int{0}
}

func (x *TrackCardDeliveryPayload) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *TrackCardDeliveryPayload) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

var File_api_card_workflow_payload_track_card_delivery_proto protoreflect.FileDescriptor

var file_api_card_workflow_payload_track_card_delivery_proto_rawDesc = []byte{
	0x0a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0x4e,
	0x0a, 0x18, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x42, 0x64,
	0x0a, 0x30, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x5a, 0x30, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x61, 0x72, 0x64, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_workflow_payload_track_card_delivery_proto_rawDescOnce sync.Once
	file_api_card_workflow_payload_track_card_delivery_proto_rawDescData = file_api_card_workflow_payload_track_card_delivery_proto_rawDesc
)

func file_api_card_workflow_payload_track_card_delivery_proto_rawDescGZIP() []byte {
	file_api_card_workflow_payload_track_card_delivery_proto_rawDescOnce.Do(func() {
		file_api_card_workflow_payload_track_card_delivery_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_workflow_payload_track_card_delivery_proto_rawDescData)
	})
	return file_api_card_workflow_payload_track_card_delivery_proto_rawDescData
}

var file_api_card_workflow_payload_track_card_delivery_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_card_workflow_payload_track_card_delivery_proto_goTypes = []interface{}{
	(*TrackCardDeliveryPayload)(nil), // 0: payload.TrackCardDeliveryPayload
}
var file_api_card_workflow_payload_track_card_delivery_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_card_workflow_payload_track_card_delivery_proto_init() }
func file_api_card_workflow_payload_track_card_delivery_proto_init() {
	if File_api_card_workflow_payload_track_card_delivery_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_workflow_payload_track_card_delivery_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackCardDeliveryPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_workflow_payload_track_card_delivery_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_workflow_payload_track_card_delivery_proto_goTypes,
		DependencyIndexes: file_api_card_workflow_payload_track_card_delivery_proto_depIdxs,
		MessageInfos:      file_api_card_workflow_payload_track_card_delivery_proto_msgTypes,
	}.Build()
	File_api_card_workflow_payload_track_card_delivery_proto = out.File
	file_api_card_workflow_payload_track_card_delivery_proto_rawDesc = nil
	file_api_card_workflow_payload_track_card_delivery_proto_goTypes = nil
	file_api_card_workflow_payload_track_card_delivery_proto_depIdxs = nil
}
