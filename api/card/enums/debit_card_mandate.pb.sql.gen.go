// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/card/enums/debit_card_mandate.pb.go

package enums

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the DebitCardMandateRegistrationProvenance in string format in DB
func (p DebitCardMandateRegistrationProvenance) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing DebitCardMandateRegistrationProvenance while reading from DB
func (p *DebitCardMandateRegistrationProvenance) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := DebitCardMandateRegistrationProvenance_value[val]
	if !ok {
		return fmt.Errorf("unexpected DebitCardMandateRegistrationProvenance value: %s", val)
	}
	*p = DebitCardMandateRegistrationProvenance(valInt)
	return nil
}

// Marshaler interface implementation for DebitCardMandateRegistrationProvenance
func (x DebitCardMandateRegistrationProvenance) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for DebitCardMandateRegistrationProvenance
func (x *DebitCardMandateRegistrationProvenance) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = DebitCardMandateRegistrationProvenance(DebitCardMandateRegistrationProvenance_value[val])
	return nil
}
