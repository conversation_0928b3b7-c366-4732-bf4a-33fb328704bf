// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/card/enums/notification.pb.go

package enums

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the NotificationType in string format in DB
func (p NotificationType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing NotificationType while reading from DB
func (p *NotificationType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := NotificationType_value[val]
	if !ok {
		return fmt.Errorf("unexpected NotificationType value: %s", val)
	}
	*p = NotificationType(valInt)
	return nil
}

// Marshaler interface implementation for NotificationType
func (x NotificationType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for NotificationType
func (x *NotificationType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = NotificationType(NotificationType_value[val])
	return nil
}

// Valuer interface implementation for storing the TransactionState in string format in DB
func (p TransactionState) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing TransactionState while reading from DB
func (p *TransactionState) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := TransactionState_value[val]
	if !ok {
		return fmt.Errorf("unexpected TransactionState value: %s", val)
	}
	*p = TransactionState(valInt)
	return nil
}

// Marshaler interface implementation for TransactionState
func (x TransactionState) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for TransactionState
func (x *TransactionState) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = TransactionState(TransactionState_value[val])
	return nil
}

// Valuer interface implementation for storing the NotificationSource in string format in DB
func (p NotificationSource) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing NotificationSource while reading from DB
func (p *NotificationSource) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := NotificationSource_value[val]
	if !ok {
		return fmt.Errorf("unexpected NotificationSource value: %s", val)
	}
	*p = NotificationSource(valInt)
	return nil
}

// Marshaler interface implementation for NotificationSource
func (x NotificationSource) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for NotificationSource
func (x *NotificationSource) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = NotificationSource(NotificationSource_value[val])
	return nil
}
