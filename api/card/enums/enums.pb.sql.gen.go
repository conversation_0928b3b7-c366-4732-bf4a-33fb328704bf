// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/card/enums/enums.pb.go

package enums

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the RefundStatus in string format in DB
func (p RefundStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing RefundStatus while reading from DB
func (p *RefundStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := RefundStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected RefundStatus value: %s", val)
	}
	*p = RefundStatus(valInt)
	return nil
}

// Marshaler interface implementation for RefundStatus
func (x RefundStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for RefundStatus
func (x *RefundStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = RefundStatus(RefundStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the RefundSubStatus in string format in DB
func (p RefundSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing RefundSubStatus while reading from DB
func (p *RefundSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := RefundSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected RefundSubStatus value: %s", val)
	}
	*p = RefundSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for RefundSubStatus
func (x RefundSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for RefundSubStatus
func (x *RefundSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = RefundSubStatus(RefundSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the RefundProcessingMode in string format in DB
func (p RefundProcessingMode) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing RefundProcessingMode while reading from DB
func (p *RefundProcessingMode) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := RefundProcessingMode_value[val]
	if !ok {
		return fmt.Errorf("unexpected RefundProcessingMode value: %s", val)
	}
	*p = RefundProcessingMode(valInt)
	return nil
}

// Marshaler interface implementation for RefundProcessingMode
func (x RefundProcessingMode) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for RefundProcessingMode
func (x *RefundProcessingMode) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = RefundProcessingMode(RefundProcessingMode_value[val])
	return nil
}

// Valuer interface implementation for storing the TransactionType in string format in DB
func (p TransactionType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing TransactionType while reading from DB
func (p *TransactionType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := TransactionType_value[val]
	if !ok {
		return fmt.Errorf("unexpected TransactionType value: %s", val)
	}
	*p = TransactionType(valInt)
	return nil
}

// Marshaler interface implementation for TransactionType
func (x TransactionType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for TransactionType
func (x *TransactionType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = TransactionType(TransactionType_value[val])
	return nil
}

// Valuer interface implementation for storing the CardRequestWorkflow in string format in DB
func (p CardRequestWorkflow) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing CardRequestWorkflow while reading from DB
func (p *CardRequestWorkflow) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := CardRequestWorkflow_value[val]
	if !ok {
		return fmt.Errorf("unexpected CardRequestWorkflow value: %s", val)
	}
	*p = CardRequestWorkflow(valInt)
	return nil
}

// Marshaler interface implementation for CardRequestWorkflow
func (x CardRequestWorkflow) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for CardRequestWorkflow
func (x *CardRequestWorkflow) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = CardRequestWorkflow(CardRequestWorkflow_value[val])
	return nil
}

// Valuer interface implementation for storing the CardRequestStatus in string format in DB
func (p CardRequestStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing CardRequestStatus while reading from DB
func (p *CardRequestStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := CardRequestStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected CardRequestStatus value: %s", val)
	}
	*p = CardRequestStatus(valInt)
	return nil
}

// Marshaler interface implementation for CardRequestStatus
func (x CardRequestStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for CardRequestStatus
func (x *CardRequestStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = CardRequestStatus(CardRequestStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the CardRequestStageName in string format in DB
func (p CardRequestStageName) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing CardRequestStageName while reading from DB
func (p *CardRequestStageName) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := CardRequestStageName_value[val]
	if !ok {
		return fmt.Errorf("unexpected CardRequestStageName value: %s", val)
	}
	*p = CardRequestStageName(valInt)
	return nil
}

// Marshaler interface implementation for CardRequestStageName
func (x CardRequestStageName) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for CardRequestStageName
func (x *CardRequestStageName) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = CardRequestStageName(CardRequestStageName_value[val])
	return nil
}

// Valuer interface implementation for storing the CardRequestStageStatus in string format in DB
func (p CardRequestStageStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing CardRequestStageStatus while reading from DB
func (p *CardRequestStageStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := CardRequestStageStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected CardRequestStageStatus value: %s", val)
	}
	*p = CardRequestStageStatus(valInt)
	return nil
}

// Marshaler interface implementation for CardRequestStageStatus
func (x CardRequestStageStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for CardRequestStageStatus
func (x *CardRequestStageStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = CardRequestStageStatus(CardRequestStageStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the CardRequestStageSubStatus in string format in DB
func (p CardRequestStageSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing CardRequestStageSubStatus while reading from DB
func (p *CardRequestStageSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := CardRequestStageSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected CardRequestStageSubStatus value: %s", val)
	}
	*p = CardRequestStageSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for CardRequestStageSubStatus
func (x CardRequestStageSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for CardRequestStageSubStatus
func (x *CardRequestStageSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = CardRequestStageSubStatus(CardRequestStageSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the OperationalStatus in string format in DB
func (p OperationalStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing OperationalStatus while reading from DB
func (p *OperationalStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := OperationalStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected OperationalStatus value: %s", val)
	}
	*p = OperationalStatus(valInt)
	return nil
}

// Marshaler interface implementation for OperationalStatus
func (x OperationalStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for OperationalStatus
func (x *OperationalStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = OperationalStatus(OperationalStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the FreezeStatus in string format in DB
func (p FreezeStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing FreezeStatus while reading from DB
func (p *FreezeStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := FreezeStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected FreezeStatus value: %s", val)
	}
	*p = FreezeStatus(valInt)
	return nil
}

// Marshaler interface implementation for FreezeStatus
func (x FreezeStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for FreezeStatus
func (x *FreezeStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = FreezeStatus(FreezeStatus_value[val])
	return nil
}
