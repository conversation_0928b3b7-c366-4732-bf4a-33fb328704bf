// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=RefundStatus,RefundProcessingMode,TransactionType,RefundSubStatus,CardRequestWorkflow,CardRequestStatus,CardRequestStageName,CardRequestStageStatus,CardRequestStageSubStatus,FreezeStatus,OperationalStatus

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/enums/enums.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Enum denoting the possible states of a refund.
type RefundStatus int32

const (
	RefundStatus_REFUND_STATUS_UNSPECIFIED RefundStatus = 0
	// initial state of refund before the evaluation workflow has started
	RefundStatus_REFUND_STATUS_CREATED RefundStatus = 1
	// refund has been approved and ready for calculation
	RefundStatus_REFUND_STATUS_APPROVED RefundStatus = 2
	// refund has been rejected based on user's tier or any other circumstances
	RefundStatus_REFUND_STATUS_REJECTED RefundStatus = 3
	// refund details have been sent to bank
	RefundStatus_REFUND_STATUS_PROCESSED RefundStatus = 4
	// refund has been reverted due to some reason
	RefundStatus_REFUND_STATUS_REVERTED RefundStatus = 5
	// refund has been calculated and validated and not sent to the bank
	RefundStatus_REFUND_STATUS_EVALUATED RefundStatus = 6
	// refund has been processed by the bank and credited to the user
	RefundStatus_REFUND_STATUS_COMPLETED RefundStatus = 7
	// when only the parent/original transaction has been processed, & not the child(forex/DCC/TCS) transaction
	RefundStatus_REFUND_STATUS_CREATION_INCOMPLETE RefundStatus = 8
)

// Enum value maps for RefundStatus.
var (
	RefundStatus_name = map[int32]string{
		0: "REFUND_STATUS_UNSPECIFIED",
		1: "REFUND_STATUS_CREATED",
		2: "REFUND_STATUS_APPROVED",
		3: "REFUND_STATUS_REJECTED",
		4: "REFUND_STATUS_PROCESSED",
		5: "REFUND_STATUS_REVERTED",
		6: "REFUND_STATUS_EVALUATED",
		7: "REFUND_STATUS_COMPLETED",
		8: "REFUND_STATUS_CREATION_INCOMPLETE",
	}
	RefundStatus_value = map[string]int32{
		"REFUND_STATUS_UNSPECIFIED":         0,
		"REFUND_STATUS_CREATED":             1,
		"REFUND_STATUS_APPROVED":            2,
		"REFUND_STATUS_REJECTED":            3,
		"REFUND_STATUS_PROCESSED":           4,
		"REFUND_STATUS_REVERTED":            5,
		"REFUND_STATUS_EVALUATED":           6,
		"REFUND_STATUS_COMPLETED":           7,
		"REFUND_STATUS_CREATION_INCOMPLETE": 8,
	}
)

func (x RefundStatus) Enum() *RefundStatus {
	p := new(RefundStatus)
	*p = x
	return p
}

func (x RefundStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RefundStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[0].Descriptor()
}

func (RefundStatus) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[0]
}

func (x RefundStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RefundStatus.Descriptor instead.
func (RefundStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{0}
}

type RefundSubStatus int32

const (
	RefundSubStatus_REFUND_SUB_STATUS_UNSPECIFIED RefundSubStatus = 0
	// sub status to signify that the refund was rejected due to user tier
	RefundSubStatus_REFUND_SUB_STATUS_REJECTED_BY_TIER RefundSubStatus = 1
	// sub status to signify that the refund was capped to an amount lesser than the forex charged
	// this could be a result of tiering
	RefundSubStatus_REFUND_SUB_STATUS_REFUND_CAPPED RefundSubStatus = 2
	// the user account was in an inactive operational status hence making it ineligible for credit
	RefundSubStatus_REFUND_SUB_STATUS_ACCOUNT_NON_OPERATIONAL RefundSubStatus = 3
	// country code was not determined and hence no decision was taken on refund
	RefundSubStatus_REFUND_SUB_STATUS_UNDETERMINED_COUNTRY_CODE RefundSubStatus = 4
	// refund was kept on hold since the txn was done to an invalid merchant .
	// this will include txns done on crypto, etc.
	RefundSubStatus_REFUND_SUB_STATUS_INVALID_MERCHANT RefundSubStatus = 5
	// when a transaction is not eligible for a forex refund, e.x. DCC/TCS
	RefundSubStatus_REFUND_SUB_STATUS_INVALID_TRANSACTION_TYPE RefundSubStatus = 6
	// when a transaction is not eligible for a forex refund because it has been reversed by the bank due to cancelled transaction
	RefundSubStatus_REFUND_SUB_STATUS_ORIGINAL_TRANSACTION_REVERSED RefundSubStatus = 7
)

// Enum value maps for RefundSubStatus.
var (
	RefundSubStatus_name = map[int32]string{
		0: "REFUND_SUB_STATUS_UNSPECIFIED",
		1: "REFUND_SUB_STATUS_REJECTED_BY_TIER",
		2: "REFUND_SUB_STATUS_REFUND_CAPPED",
		3: "REFUND_SUB_STATUS_ACCOUNT_NON_OPERATIONAL",
		4: "REFUND_SUB_STATUS_UNDETERMINED_COUNTRY_CODE",
		5: "REFUND_SUB_STATUS_INVALID_MERCHANT",
		6: "REFUND_SUB_STATUS_INVALID_TRANSACTION_TYPE",
		7: "REFUND_SUB_STATUS_ORIGINAL_TRANSACTION_REVERSED",
	}
	RefundSubStatus_value = map[string]int32{
		"REFUND_SUB_STATUS_UNSPECIFIED":                   0,
		"REFUND_SUB_STATUS_REJECTED_BY_TIER":              1,
		"REFUND_SUB_STATUS_REFUND_CAPPED":                 2,
		"REFUND_SUB_STATUS_ACCOUNT_NON_OPERATIONAL":       3,
		"REFUND_SUB_STATUS_UNDETERMINED_COUNTRY_CODE":     4,
		"REFUND_SUB_STATUS_INVALID_MERCHANT":              5,
		"REFUND_SUB_STATUS_INVALID_TRANSACTION_TYPE":      6,
		"REFUND_SUB_STATUS_ORIGINAL_TRANSACTION_REVERSED": 7,
	}
)

func (x RefundSubStatus) Enum() *RefundSubStatus {
	p := new(RefundSubStatus)
	*p = x
	return p
}

func (x RefundSubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RefundSubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[1].Descriptor()
}

func (RefundSubStatus) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[1]
}

func (x RefundSubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RefundSubStatus.Descriptor instead.
func (RefundSubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{1}
}

type RefundProcessingMode int32

const (
	RefundProcessingMode_REFUND_PROCESSING_MODE_UNSPECIFIED RefundProcessingMode = 0
	RefundProcessingMode_REFUND_PROCESSING_MODE_REAL_TIME   RefundProcessingMode = 1
	RefundProcessingMode_REFUND_PROCESSING_MODE_BATCH       RefundProcessingMode = 2
)

// Enum value maps for RefundProcessingMode.
var (
	RefundProcessingMode_name = map[int32]string{
		0: "REFUND_PROCESSING_MODE_UNSPECIFIED",
		1: "REFUND_PROCESSING_MODE_REAL_TIME",
		2: "REFUND_PROCESSING_MODE_BATCH",
	}
	RefundProcessingMode_value = map[string]int32{
		"REFUND_PROCESSING_MODE_UNSPECIFIED": 0,
		"REFUND_PROCESSING_MODE_REAL_TIME":   1,
		"REFUND_PROCESSING_MODE_BATCH":       2,
	}
)

func (x RefundProcessingMode) Enum() *RefundProcessingMode {
	p := new(RefundProcessingMode)
	*p = x
	return p
}

func (x RefundProcessingMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RefundProcessingMode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[2].Descriptor()
}

func (RefundProcessingMode) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[2]
}

func (x RefundProcessingMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RefundProcessingMode.Descriptor instead.
func (RefundProcessingMode) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{2}
}

type DcForexTxnRefundFieldMask int32

const (
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_UNSPECIFIED               DcForexTxnRefundFieldMask = 0
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_ID                        DcForexTxnRefundFieldMask = 1
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_TXN_ID                    DcForexTxnRefundFieldMask = 2
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_ACTOR_ID                  DcForexTxnRefundFieldMask = 3
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_TOTAL_TXN_AMOUNT          DcForexTxnRefundFieldMask = 4
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_AMOUNT             DcForexTxnRefundFieldMask = 5
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_TXN_TIME                  DcForexTxnRefundFieldMask = 6
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_STATUS             DcForexTxnRefundFieldMask = 7
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_PROCESS_IDENTIFIER DcForexTxnRefundFieldMask = 8
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_TXN_TIME_USER_TIER        DcForexTxnRefundFieldMask = 9
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_PROCESSING_MODE    DcForexTxnRefundFieldMask = 10
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_CREATED_AT                DcForexTxnRefundFieldMask = 11
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_UPDATED_AT                DcForexTxnRefundFieldMask = 12
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_DELETED_AT                DcForexTxnRefundFieldMask = 13
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_ORCH_ID                   DcForexTxnRefundFieldMask = 14
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_TXN_TYPE                  DcForexTxnRefundFieldMask = 15
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_TXN_ID             DcForexTxnRefundFieldMask = 16
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_FOREX_CHARGES_INFO        DcForexTxnRefundFieldMask = 17
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_SUB_STATUS         DcForexTxnRefundFieldMask = 18
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_DEDUPE_IDENTIFIER         DcForexTxnRefundFieldMask = 19
	DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_ORIGINAL_TRANSACTION      DcForexTxnRefundFieldMask = 20
)

// Enum value maps for DcForexTxnRefundFieldMask.
var (
	DcForexTxnRefundFieldMask_name = map[int32]string{
		0:  "DC_FOREX_TXN_REFUND_FIELD_MASK_UNSPECIFIED",
		1:  "DC_FOREX_TXN_REFUND_FIELD_MASK_ID",
		2:  "DC_FOREX_TXN_REFUND_FIELD_MASK_TXN_ID",
		3:  "DC_FOREX_TXN_REFUND_FIELD_MASK_ACTOR_ID",
		4:  "DC_FOREX_TXN_REFUND_FIELD_MASK_TOTAL_TXN_AMOUNT",
		5:  "DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_AMOUNT",
		6:  "DC_FOREX_TXN_REFUND_FIELD_MASK_TXN_TIME",
		7:  "DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_STATUS",
		8:  "DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_PROCESS_IDENTIFIER",
		9:  "DC_FOREX_TXN_REFUND_FIELD_MASK_TXN_TIME_USER_TIER",
		10: "DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_PROCESSING_MODE",
		11: "DC_FOREX_TXN_REFUND_FIELD_MASK_CREATED_AT",
		12: "DC_FOREX_TXN_REFUND_FIELD_MASK_UPDATED_AT",
		13: "DC_FOREX_TXN_REFUND_FIELD_MASK_DELETED_AT",
		14: "DC_FOREX_TXN_REFUND_FIELD_MASK_ORCH_ID",
		15: "DC_FOREX_TXN_REFUND_FIELD_MASK_TXN_TYPE",
		16: "DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_TXN_ID",
		17: "DC_FOREX_TXN_REFUND_FIELD_MASK_FOREX_CHARGES_INFO",
		18: "DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_SUB_STATUS",
		19: "DC_FOREX_TXN_REFUND_FIELD_MASK_DEDUPE_IDENTIFIER",
		20: "DC_FOREX_TXN_REFUND_FIELD_MASK_ORIGINAL_TRANSACTION",
	}
	DcForexTxnRefundFieldMask_value = map[string]int32{
		"DC_FOREX_TXN_REFUND_FIELD_MASK_UNSPECIFIED":               0,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_ID":                        1,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_TXN_ID":                    2,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_ACTOR_ID":                  3,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_TOTAL_TXN_AMOUNT":          4,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_AMOUNT":             5,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_TXN_TIME":                  6,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_STATUS":             7,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_PROCESS_IDENTIFIER": 8,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_TXN_TIME_USER_TIER":        9,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_PROCESSING_MODE":    10,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_CREATED_AT":                11,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_UPDATED_AT":                12,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_DELETED_AT":                13,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_ORCH_ID":                   14,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_TXN_TYPE":                  15,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_TXN_ID":             16,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_FOREX_CHARGES_INFO":        17,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_SUB_STATUS":         18,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_DEDUPE_IDENTIFIER":         19,
		"DC_FOREX_TXN_REFUND_FIELD_MASK_ORIGINAL_TRANSACTION":      20,
	}
)

func (x DcForexTxnRefundFieldMask) Enum() *DcForexTxnRefundFieldMask {
	p := new(DcForexTxnRefundFieldMask)
	*p = x
	return p
}

func (x DcForexTxnRefundFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DcForexTxnRefundFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[3].Descriptor()
}

func (DcForexTxnRefundFieldMask) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[3]
}

func (x DcForexTxnRefundFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DcForexTxnRefundFieldMask.Descriptor instead.
func (DcForexTxnRefundFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{3}
}

type TransactionType int32

const (
	TransactionType_TRANSACTION_TYPE_UNSPECIFIED TransactionType = 0
	TransactionType_TRANSACTION_TYPE_CREDIT      TransactionType = 1
	TransactionType_TRANSACTION_TYPE_DEBIT       TransactionType = 2
)

// Enum value maps for TransactionType.
var (
	TransactionType_name = map[int32]string{
		0: "TRANSACTION_TYPE_UNSPECIFIED",
		1: "TRANSACTION_TYPE_CREDIT",
		2: "TRANSACTION_TYPE_DEBIT",
	}
	TransactionType_value = map[string]int32{
		"TRANSACTION_TYPE_UNSPECIFIED": 0,
		"TRANSACTION_TYPE_CREDIT":      1,
		"TRANSACTION_TYPE_DEBIT":       2,
	}
)

func (x TransactionType) Enum() *TransactionType {
	p := new(TransactionType)
	*p = x
	return p
}

func (x TransactionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[4].Descriptor()
}

func (TransactionType) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[4]
}

func (x TransactionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionType.Descriptor instead.
func (TransactionType) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{4}
}

type CardRequestWorkflow int32

const (
	CardRequestWorkflow_CARD_REQUEST_WORKFLOW_UNSPECIFIED         CardRequestWorkflow = 0
	CardRequestWorkflow_CARD_REQUEST_WORKFLOW_RENEW_CARD          CardRequestWorkflow = 1
	CardRequestWorkflow_CARD_REQUEST_WORKFLOW_PROCESS_AMC_CHARGES CardRequestWorkflow = 2
)

// Enum value maps for CardRequestWorkflow.
var (
	CardRequestWorkflow_name = map[int32]string{
		0: "CARD_REQUEST_WORKFLOW_UNSPECIFIED",
		1: "CARD_REQUEST_WORKFLOW_RENEW_CARD",
		2: "CARD_REQUEST_WORKFLOW_PROCESS_AMC_CHARGES",
	}
	CardRequestWorkflow_value = map[string]int32{
		"CARD_REQUEST_WORKFLOW_UNSPECIFIED":         0,
		"CARD_REQUEST_WORKFLOW_RENEW_CARD":          1,
		"CARD_REQUEST_WORKFLOW_PROCESS_AMC_CHARGES": 2,
	}
)

func (x CardRequestWorkflow) Enum() *CardRequestWorkflow {
	p := new(CardRequestWorkflow)
	*p = x
	return p
}

func (x CardRequestWorkflow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardRequestWorkflow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[5].Descriptor()
}

func (CardRequestWorkflow) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[5]
}

func (x CardRequestWorkflow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardRequestWorkflow.Descriptor instead.
func (CardRequestWorkflow) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{5}
}

type CardRequestStatus int32

const (
	CardRequestStatus_CARD_REQUEST_STATUS_UNSPECIFIED         CardRequestStatus = 0
	CardRequestStatus_CARD_REQUEST_STATUS_CREATED             CardRequestStatus = 1
	CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS         CardRequestStatus = 2
	CardRequestStatus_CARD_REQUEST_STATUS_FAILED              CardRequestStatus = 3
	CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS             CardRequestStatus = 4
	CardRequestStatus_CARD_REQUEST_STATUS_MANUAL_INTERVENTION CardRequestStatus = 5
	CardRequestStatus_CARD_REQUEST_STATUS_PERMANENT_FAILURE   CardRequestStatus = 6
)

// Enum value maps for CardRequestStatus.
var (
	CardRequestStatus_name = map[int32]string{
		0: "CARD_REQUEST_STATUS_UNSPECIFIED",
		1: "CARD_REQUEST_STATUS_CREATED",
		2: "CARD_REQUEST_STATUS_IN_PROGRESS",
		3: "CARD_REQUEST_STATUS_FAILED",
		4: "CARD_REQUEST_STATUS_SUCCESS",
		5: "CARD_REQUEST_STATUS_MANUAL_INTERVENTION",
		6: "CARD_REQUEST_STATUS_PERMANENT_FAILURE",
	}
	CardRequestStatus_value = map[string]int32{
		"CARD_REQUEST_STATUS_UNSPECIFIED":         0,
		"CARD_REQUEST_STATUS_CREATED":             1,
		"CARD_REQUEST_STATUS_IN_PROGRESS":         2,
		"CARD_REQUEST_STATUS_FAILED":              3,
		"CARD_REQUEST_STATUS_SUCCESS":             4,
		"CARD_REQUEST_STATUS_MANUAL_INTERVENTION": 5,
		"CARD_REQUEST_STATUS_PERMANENT_FAILURE":   6,
	}
)

func (x CardRequestStatus) Enum() *CardRequestStatus {
	p := new(CardRequestStatus)
	*p = x
	return p
}

func (x CardRequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardRequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[6].Descriptor()
}

func (CardRequestStatus) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[6]
}

func (x CardRequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardRequestStatus.Descriptor instead.
func (CardRequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{6}
}

type CardRequestStageName int32

const (
	CardRequestStageName_CARD_REQUEST_STAGE_NAME_UNSPECIFIED            CardRequestStageName = 0
	CardRequestStageName_CARD_REQUEST_STAGE_NAME_BLOCK_CARD             CardRequestStageName = 1
	CardRequestStageName_CARD_REQUEST_STAGE_NAME_CREATE_CARD            CardRequestStageName = 2
	CardRequestStageName_CARD_REQUEST_STAGE_NAME_CHARGE_CARD            CardRequestStageName = 3
	CardRequestStageName_CARD_REQUEST_STAGE_NAME_PHYSICAL_CARD_DISPATCH CardRequestStageName = 4
)

// Enum value maps for CardRequestStageName.
var (
	CardRequestStageName_name = map[int32]string{
		0: "CARD_REQUEST_STAGE_NAME_UNSPECIFIED",
		1: "CARD_REQUEST_STAGE_NAME_BLOCK_CARD",
		2: "CARD_REQUEST_STAGE_NAME_CREATE_CARD",
		3: "CARD_REQUEST_STAGE_NAME_CHARGE_CARD",
		4: "CARD_REQUEST_STAGE_NAME_PHYSICAL_CARD_DISPATCH",
	}
	CardRequestStageName_value = map[string]int32{
		"CARD_REQUEST_STAGE_NAME_UNSPECIFIED":            0,
		"CARD_REQUEST_STAGE_NAME_BLOCK_CARD":             1,
		"CARD_REQUEST_STAGE_NAME_CREATE_CARD":            2,
		"CARD_REQUEST_STAGE_NAME_CHARGE_CARD":            3,
		"CARD_REQUEST_STAGE_NAME_PHYSICAL_CARD_DISPATCH": 4,
	}
)

func (x CardRequestStageName) Enum() *CardRequestStageName {
	p := new(CardRequestStageName)
	*p = x
	return p
}

func (x CardRequestStageName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardRequestStageName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[7].Descriptor()
}

func (CardRequestStageName) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[7]
}

func (x CardRequestStageName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardRequestStageName.Descriptor instead.
func (CardRequestStageName) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{7}
}

type CardRequestStageStatus int32

const (
	CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_UNSPECIFIED CardRequestStageStatus = 0
	CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_CREATED     CardRequestStageStatus = 1
	CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_IN_PROGRESS CardRequestStageStatus = 2
	CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_FAILED      CardRequestStageStatus = 3
	CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_SUCCESS     CardRequestStageStatus = 4
	CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_SKIPPED     CardRequestStageStatus = 5
)

// Enum value maps for CardRequestStageStatus.
var (
	CardRequestStageStatus_name = map[int32]string{
		0: "CARD_REQUEST_STAGE_STATUS_UNSPECIFIED",
		1: "CARD_REQUEST_STAGE_STATUS_CREATED",
		2: "CARD_REQUEST_STAGE_STATUS_IN_PROGRESS",
		3: "CARD_REQUEST_STAGE_STATUS_FAILED",
		4: "CARD_REQUEST_STAGE_STATUS_SUCCESS",
		5: "CARD_REQUEST_STAGE_STATUS_SKIPPED",
	}
	CardRequestStageStatus_value = map[string]int32{
		"CARD_REQUEST_STAGE_STATUS_UNSPECIFIED": 0,
		"CARD_REQUEST_STAGE_STATUS_CREATED":     1,
		"CARD_REQUEST_STAGE_STATUS_IN_PROGRESS": 2,
		"CARD_REQUEST_STAGE_STATUS_FAILED":      3,
		"CARD_REQUEST_STAGE_STATUS_SUCCESS":     4,
		"CARD_REQUEST_STAGE_STATUS_SKIPPED":     5,
	}
)

func (x CardRequestStageStatus) Enum() *CardRequestStageStatus {
	p := new(CardRequestStageStatus)
	*p = x
	return p
}

func (x CardRequestStageStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardRequestStageStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[8].Descriptor()
}

func (CardRequestStageStatus) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[8]
}

func (x CardRequestStageStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardRequestStageStatus.Descriptor instead.
func (CardRequestStageStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{8}
}

type CardRequestStageSubStatus int32

const (
	CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_UNSPECIFIED                 CardRequestStageSubStatus = 0
	CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_CREATE_CARD_INITIATED       CardRequestStageSubStatus = 1
	CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_PHYSICAL_DISPATCH_INITIATED CardRequestStageSubStatus = 2
	CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_SUCCESS                     CardRequestStageSubStatus = 3
)

// Enum value maps for CardRequestStageSubStatus.
var (
	CardRequestStageSubStatus_name = map[int32]string{
		0: "CARD_REQUEST_STAGE_SUB_STATUS_UNSPECIFIED",
		1: "CARD_REQUEST_STAGE_SUB_STATUS_CREATE_CARD_INITIATED",
		2: "CARD_REQUEST_STAGE_SUB_STATUS_PHYSICAL_DISPATCH_INITIATED",
		3: "CARD_REQUEST_STAGE_SUB_STATUS_SUCCESS",
	}
	CardRequestStageSubStatus_value = map[string]int32{
		"CARD_REQUEST_STAGE_SUB_STATUS_UNSPECIFIED":                 0,
		"CARD_REQUEST_STAGE_SUB_STATUS_CREATE_CARD_INITIATED":       1,
		"CARD_REQUEST_STAGE_SUB_STATUS_PHYSICAL_DISPATCH_INITIATED": 2,
		"CARD_REQUEST_STAGE_SUB_STATUS_SUCCESS":                     3,
	}
)

func (x CardRequestStageSubStatus) Enum() *CardRequestStageSubStatus {
	p := new(CardRequestStageSubStatus)
	*p = x
	return p
}

func (x CardRequestStageSubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardRequestStageSubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[9].Descriptor()
}

func (CardRequestStageSubStatus) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[9]
}

func (x CardRequestStageSubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardRequestStageSubStatus.Descriptor instead.
func (CardRequestStageSubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{9}
}

type CardRequestFieldMask int32

const (
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_UNSPECIFIED      CardRequestFieldMask = 0
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID               CardRequestFieldMask = 1
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_CARD_ID          CardRequestFieldMask = 2
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ACTOR_ID         CardRequestFieldMask = 3
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ORCHESTRATION_ID CardRequestFieldMask = 4
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_VENDOR           CardRequestFieldMask = 5
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS  CardRequestFieldMask = 6
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION      CardRequestFieldMask = 7
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STAGE_DETAILS    CardRequestFieldMask = 8
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_WORKFLOW         CardRequestFieldMask = 9
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS           CardRequestFieldMask = 10
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_PROVENANCE       CardRequestFieldMask = 11
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_CREATED_AT       CardRequestFieldMask = 12
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_UPDATED_AT       CardRequestFieldMask = 13
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_DELETED_AT       CardRequestFieldMask = 14
)

// Enum value maps for CardRequestFieldMask.
var (
	CardRequestFieldMask_name = map[int32]string{
		0:  "CARD_REQUEST_FIELD_MASK_UNSPECIFIED",
		1:  "CARD_REQUEST_FIELD_MASK_ID",
		2:  "CARD_REQUEST_FIELD_MASK_CARD_ID",
		3:  "CARD_REQUEST_FIELD_MASK_ACTOR_ID",
		4:  "CARD_REQUEST_FIELD_MASK_ORCHESTRATION_ID",
		5:  "CARD_REQUEST_FIELD_MASK_VENDOR",
		6:  "CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS",
		7:  "CARD_REQUEST_FIELD_MASK_NEXT_ACTION",
		8:  "CARD_REQUEST_FIELD_MASK_STAGE_DETAILS",
		9:  "CARD_REQUEST_FIELD_MASK_WORKFLOW",
		10: "CARD_REQUEST_FIELD_MASK_STATUS",
		11: "CARD_REQUEST_FIELD_MASK_PROVENANCE",
		12: "CARD_REQUEST_FIELD_MASK_CREATED_AT",
		13: "CARD_REQUEST_FIELD_MASK_UPDATED_AT",
		14: "CARD_REQUEST_FIELD_MASK_DELETED_AT",
	}
	CardRequestFieldMask_value = map[string]int32{
		"CARD_REQUEST_FIELD_MASK_UNSPECIFIED":      0,
		"CARD_REQUEST_FIELD_MASK_ID":               1,
		"CARD_REQUEST_FIELD_MASK_CARD_ID":          2,
		"CARD_REQUEST_FIELD_MASK_ACTOR_ID":         3,
		"CARD_REQUEST_FIELD_MASK_ORCHESTRATION_ID": 4,
		"CARD_REQUEST_FIELD_MASK_VENDOR":           5,
		"CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS":  6,
		"CARD_REQUEST_FIELD_MASK_NEXT_ACTION":      7,
		"CARD_REQUEST_FIELD_MASK_STAGE_DETAILS":    8,
		"CARD_REQUEST_FIELD_MASK_WORKFLOW":         9,
		"CARD_REQUEST_FIELD_MASK_STATUS":           10,
		"CARD_REQUEST_FIELD_MASK_PROVENANCE":       11,
		"CARD_REQUEST_FIELD_MASK_CREATED_AT":       12,
		"CARD_REQUEST_FIELD_MASK_UPDATED_AT":       13,
		"CARD_REQUEST_FIELD_MASK_DELETED_AT":       14,
	}
)

func (x CardRequestFieldMask) Enum() *CardRequestFieldMask {
	p := new(CardRequestFieldMask)
	*p = x
	return p
}

func (x CardRequestFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardRequestFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[10].Descriptor()
}

func (CardRequestFieldMask) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[10]
}

func (x CardRequestFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardRequestFieldMask.Descriptor instead.
func (CardRequestFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{10}
}

type CardNudgeType int32

const (
	CardNudgeType_CARD_NUDGE_TYPE_UNSPECIFIED         CardNudgeType = 0
	CardNudgeType_CARD_NUDGE_TYPE_ORDER_PHYSICAL_CARD CardNudgeType = 1
	CardNudgeType_CARD_NUDGE_TYPE_TRACK_CARD_DELIVERY CardNudgeType = 2
)

// Enum value maps for CardNudgeType.
var (
	CardNudgeType_name = map[int32]string{
		0: "CARD_NUDGE_TYPE_UNSPECIFIED",
		1: "CARD_NUDGE_TYPE_ORDER_PHYSICAL_CARD",
		2: "CARD_NUDGE_TYPE_TRACK_CARD_DELIVERY",
	}
	CardNudgeType_value = map[string]int32{
		"CARD_NUDGE_TYPE_UNSPECIFIED":         0,
		"CARD_NUDGE_TYPE_ORDER_PHYSICAL_CARD": 1,
		"CARD_NUDGE_TYPE_TRACK_CARD_DELIVERY": 2,
	}
)

func (x CardNudgeType) Enum() *CardNudgeType {
	p := new(CardNudgeType)
	*p = x
	return p
}

func (x CardNudgeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardNudgeType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[11].Descriptor()
}

func (CardNudgeType) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[11]
}

func (x CardNudgeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardNudgeType.Descriptor instead.
func (CardNudgeType) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{11}
}

type OperationalStatus int32

const (
	OperationalStatus_OPERATIONAL_STATUS_UNSPECIFIED OperationalStatus = 0
	OperationalStatus_OPERATIONAL_STATUS_ACTIVE      OperationalStatus = 1
	// an account is inactive if there has been no transaction for more than a year.
	// can be reversed by a user driven banking action like payment, withdrawal etc.
	OperationalStatus_OPERATIONAL_STATUS_INACTIVE OperationalStatus = 2
	// an account is dormant if there has been no transaction for more than two years.
	// a user needs to do KYC with the bank again to reverse this
	OperationalStatus_OPERATIONAL_STATUS_DORMANT OperationalStatus = 3
	OperationalStatus_OPERATIONAL_STATUS_CLOSED  OperationalStatus = 4
)

// Enum value maps for OperationalStatus.
var (
	OperationalStatus_name = map[int32]string{
		0: "OPERATIONAL_STATUS_UNSPECIFIED",
		1: "OPERATIONAL_STATUS_ACTIVE",
		2: "OPERATIONAL_STATUS_INACTIVE",
		3: "OPERATIONAL_STATUS_DORMANT",
		4: "OPERATIONAL_STATUS_CLOSED",
	}
	OperationalStatus_value = map[string]int32{
		"OPERATIONAL_STATUS_UNSPECIFIED": 0,
		"OPERATIONAL_STATUS_ACTIVE":      1,
		"OPERATIONAL_STATUS_INACTIVE":    2,
		"OPERATIONAL_STATUS_DORMANT":     3,
		"OPERATIONAL_STATUS_CLOSED":      4,
	}
)

func (x OperationalStatus) Enum() *OperationalStatus {
	p := new(OperationalStatus)
	*p = x
	return p
}

func (x OperationalStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OperationalStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[12].Descriptor()
}

func (OperationalStatus) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[12]
}

func (x OperationalStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OperationalStatus.Descriptor instead.
func (OperationalStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{12}
}

type FreezeStatus int32

const (
	FreezeStatus_FREEZE_STATUS_UNSPECIFIED   FreezeStatus = 0
	FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE  FreezeStatus = 1
	FreezeStatus_FREEZE_STATUS_CREDIT_FREEZE FreezeStatus = 2
	FreezeStatus_FREEZE_STATUS_DEBIT_FREEZE  FreezeStatus = 3
)

// Enum value maps for FreezeStatus.
var (
	FreezeStatus_name = map[int32]string{
		0: "FREEZE_STATUS_UNSPECIFIED",
		1: "FREEZE_STATUS_TOTAL_FREEZE",
		2: "FREEZE_STATUS_CREDIT_FREEZE",
		3: "FREEZE_STATUS_DEBIT_FREEZE",
	}
	FreezeStatus_value = map[string]int32{
		"FREEZE_STATUS_UNSPECIFIED":   0,
		"FREEZE_STATUS_TOTAL_FREEZE":  1,
		"FREEZE_STATUS_CREDIT_FREEZE": 2,
		"FREEZE_STATUS_DEBIT_FREEZE":  3,
	}
)

func (x FreezeStatus) Enum() *FreezeStatus {
	p := new(FreezeStatus)
	*p = x
	return p
}

func (x FreezeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FreezeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[13].Descriptor()
}

func (FreezeStatus) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[13]
}

func (x FreezeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FreezeStatus.Descriptor instead.
func (FreezeStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{13}
}

type OrderPhysicalCardUiEntryPoint int32

const (
	OrderPhysicalCardUiEntryPoint_ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_UNSPECIFIED OrderPhysicalCardUiEntryPoint = 0
	// Implies that physical card creation initiated during onboarding
	OrderPhysicalCardUiEntryPoint_ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_ONBOARDING OrderPhysicalCardUiEntryPoint = 1
	// Implies that physical card creation initiated from renew card flow
	OrderPhysicalCardUiEntryPoint_ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_RENEW_CARD_FLOW OrderPhysicalCardUiEntryPoint = 2
)

// Enum value maps for OrderPhysicalCardUiEntryPoint.
var (
	OrderPhysicalCardUiEntryPoint_name = map[int32]string{
		0: "ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_UNSPECIFIED",
		1: "ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_ONBOARDING",
		2: "ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_RENEW_CARD_FLOW",
	}
	OrderPhysicalCardUiEntryPoint_value = map[string]int32{
		"ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_UNSPECIFIED":     0,
		"ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_ONBOARDING":      1,
		"ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_RENEW_CARD_FLOW": 2,
	}
)

func (x OrderPhysicalCardUiEntryPoint) Enum() *OrderPhysicalCardUiEntryPoint {
	p := new(OrderPhysicalCardUiEntryPoint)
	*p = x
	return p
}

func (x OrderPhysicalCardUiEntryPoint) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderPhysicalCardUiEntryPoint) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_enums_proto_enumTypes[14].Descriptor()
}

func (OrderPhysicalCardUiEntryPoint) Type() protoreflect.EnumType {
	return &file_api_card_enums_enums_proto_enumTypes[14]
}

func (x OrderPhysicalCardUiEntryPoint) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderPhysicalCardUiEntryPoint.Descriptor instead.
func (OrderPhysicalCardUiEntryPoint) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_enums_proto_rawDescGZIP(), []int{14}
}

var File_api_card_enums_enums_proto protoreflect.FileDescriptor

var file_api_card_enums_enums_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a, 0x9a, 0x02, 0x0a, 0x0c, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x46,
	0x55, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x45, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x44, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x02, 0x12,
	0x1a, 0x0a, 0x16, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x52,
	0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f,
	0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x54,
	0x45, 0x44, 0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x45, 0x44, 0x10,
	0x06, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x07, 0x12, 0x25,
	0x0a, 0x21, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x45, 0x54, 0x45, 0x10, 0x08, 0x2a, 0xee, 0x02, 0x0a, 0x0f, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x46,
	0x55, 0x4e, 0x44, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22,
	0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x54, 0x49,
	0x45, 0x52, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44,
	0x5f, 0x43, 0x41, 0x50, 0x50, 0x45, 0x44, 0x10, 0x02, 0x12, 0x2d, 0x0a, 0x29, 0x52, 0x45, 0x46,
	0x55, 0x4e, 0x44, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x03, 0x12, 0x2f, 0x0a, 0x2b, 0x52, 0x45, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x44, 0x45, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x52, 0x59, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x04, 0x12, 0x26, 0x0a, 0x22, 0x52, 0x45, 0x46,
	0x55, 0x4e, 0x44, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x10,
	0x05, 0x12, 0x2e, 0x0a, 0x2a, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10,
	0x06, 0x12, 0x33, 0x0a, 0x2f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x52, 0x49, 0x47, 0x49, 0x4e, 0x41, 0x4c, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x45,
	0x52, 0x53, 0x45, 0x44, 0x10, 0x07, 0x2a, 0x86, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12,
	0x26, 0x0a, 0x22, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x52, 0x45, 0x46, 0x55, 0x4e,
	0x44, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x4f, 0x44,
	0x45, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x20, 0x0a,
	0x1c, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49,
	0x4e, 0x47, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x41, 0x54, 0x43, 0x48, 0x10, 0x02, 0x2a,
	0xb4, 0x08, 0x0a, 0x19, 0x44, 0x63, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x54, 0x78, 0x6e, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2e, 0x0a,
	0x2a, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45,
	0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a,
	0x21, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45,
	0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x49, 0x44, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58,
	0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12,
	0x2b, 0x0a, 0x27, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e, 0x5f,
	0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x33, 0x0a, 0x2f,
	0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45, 0x46,
	0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54,
	0x4f, 0x54, 0x41, 0x4c, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10,
	0x04, 0x12, 0x30, 0x0a, 0x2c, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58,
	0x4e, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e,
	0x54, 0x10, 0x05, 0x12, 0x2b, 0x0a, 0x27, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f,
	0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x06,
	0x12, 0x30, 0x0a, 0x2c, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e,
	0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x10, 0x07, 0x12, 0x3c, 0x0a, 0x38, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54,
	0x58, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x43,
	0x45, 0x53, 0x53, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x10, 0x08,
	0x12, 0x35, 0x0a, 0x31, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e,
	0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x54, 0x49, 0x45, 0x52, 0x10, 0x09, 0x12, 0x39, 0x0a, 0x35, 0x44, 0x43, 0x5f, 0x46, 0x4f,
	0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44,
	0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x4f, 0x44, 0x45,
	0x10, 0x0a, 0x12, 0x2d, 0x0a, 0x29, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54,
	0x58, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10,
	0x0b, 0x12, 0x2d, 0x0a, 0x29, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58,
	0x4e, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0c,
	0x12, 0x2d, 0x0a, 0x29, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e,
	0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0d, 0x12,
	0x2a, 0x0a, 0x26, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e, 0x5f,
	0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x4f, 0x52, 0x43, 0x48, 0x5f, 0x49, 0x44, 0x10, 0x0e, 0x12, 0x2b, 0x0a, 0x27, 0x44,
	0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x58,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x0f, 0x12, 0x30, 0x0a, 0x2c, 0x44, 0x43, 0x5f, 0x46,
	0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e,
	0x44, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x10, 0x12, 0x35, 0x0a, 0x31, 0x44, 0x43,
	0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e,
	0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x4f, 0x52,
	0x45, 0x58, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x53, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10,
	0x11, 0x12, 0x34, 0x0a, 0x30, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58,
	0x4e, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x12, 0x12, 0x34, 0x0a, 0x30, 0x44, 0x43, 0x5f, 0x46, 0x4f,
	0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45,
	0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x10, 0x13, 0x12, 0x37, 0x0a,
	0x33, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45,
	0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x4f, 0x52, 0x49, 0x47, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x14, 0x2a, 0x6c, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x42,
	0x49, 0x54, 0x10, 0x02, 0x2a, 0x91, 0x01, 0x0a, 0x13, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x25, 0x0a, 0x21,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x57, 0x4f, 0x52,
	0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x52, 0x45, 0x4e,
	0x45, 0x57, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x2d, 0x0a, 0x29, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x4d, 0x43, 0x5f, 0x43,
	0x48, 0x41, 0x52, 0x47, 0x45, 0x53, 0x10, 0x02, 0x2a, 0x97, 0x02, 0x0a, 0x11, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23,
	0x0a, 0x1f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x44, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x04, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45,
	0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50,
	0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x10, 0x06, 0x2a, 0xed, 0x01, 0x0a, 0x14, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x23, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x10, 0x02, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x03, 0x12, 0x32,
	0x0a, 0x2e, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43,
	0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48,
	0x10, 0x04, 0x2a, 0x89, 0x02, 0x0a, 0x16, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a,
	0x25, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12,
	0x29, 0x0a, 0x25, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f,
	0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03,
	0x12, 0x25, 0x0a, 0x21, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x04, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x4b, 0x49, 0x50, 0x50, 0x45, 0x44, 0x10, 0x05, 0x2a, 0xed,
	0x01, 0x0a, 0x19, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x0a, 0x29,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x37, 0x0a, 0x33, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54,
	0x45, 0x44, 0x10, 0x01, 0x12, 0x3d, 0x0a, 0x39, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x44,
	0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45,
	0x44, 0x10, 0x02, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x2a, 0xe7,
	0x04, 0x0a, 0x14, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01,
	0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x2c, 0x0a, 0x28, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x52, 0x43, 0x48, 0x45, 0x53, 0x54, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x05, 0x12, 0x2b, 0x0a,
	0x27, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x06, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4e, 0x45, 0x58, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x07, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53,
	0x54, 0x41, 0x47, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x08, 0x12, 0x24,
	0x0a, 0x20, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x46, 0x4c,
	0x4f, 0x57, 0x10, 0x09, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x0a, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x0b,
	0x12, 0x26, 0x0a, 0x22, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0c, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0d,
	0x12, 0x26, 0x0a, 0x22, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0e, 0x2a, 0x82, 0x01, 0x0a, 0x0d, 0x43, 0x61, 0x72,
	0x64, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x4e, 0x55, 0x44, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x27, 0x0a, 0x23, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x4e, 0x55, 0x44, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x55, 0x44,
	0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x10, 0x02, 0x2a, 0xb6, 0x01,
	0x0a, 0x11, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x1e, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x4f,
	0x52, 0x4d, 0x41, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c,
	0x4f, 0x53, 0x45, 0x44, 0x10, 0x04, 0x2a, 0x8e, 0x01, 0x0a, 0x0c, 0x46, 0x72, 0x65, 0x65, 0x7a,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x46, 0x52, 0x45, 0x45, 0x5a,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x4f, 0x54, 0x41, 0x4c, 0x5f, 0x46, 0x52,
	0x45, 0x45, 0x5a, 0x45, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x46,
	0x52, 0x45, 0x45, 0x5a, 0x45, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x52, 0x45, 0x45, 0x5a,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x46,
	0x52, 0x45, 0x45, 0x5a, 0x45, 0x10, 0x03, 0x2a, 0xbe, 0x01, 0x0a, 0x1d, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x55, 0x69, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x2e, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x55, 0x49, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x31, 0x0a,
	0x2d, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x55, 0x49, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f,
	0x49, 0x4e, 0x54, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01,
	0x12, 0x36, 0x0a, 0x32, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43,
	0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x55, 0x49, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59,
	0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x4e, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x02, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61,
	0x72, 0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_enums_enums_proto_rawDescOnce sync.Once
	file_api_card_enums_enums_proto_rawDescData = file_api_card_enums_enums_proto_rawDesc
)

func file_api_card_enums_enums_proto_rawDescGZIP() []byte {
	file_api_card_enums_enums_proto_rawDescOnce.Do(func() {
		file_api_card_enums_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_enums_enums_proto_rawDescData)
	})
	return file_api_card_enums_enums_proto_rawDescData
}

var file_api_card_enums_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 15)
var file_api_card_enums_enums_proto_goTypes = []interface{}{
	(RefundStatus)(0),                  // 0: card.enums.RefundStatus
	(RefundSubStatus)(0),               // 1: card.enums.RefundSubStatus
	(RefundProcessingMode)(0),          // 2: card.enums.RefundProcessingMode
	(DcForexTxnRefundFieldMask)(0),     // 3: card.enums.DcForexTxnRefundFieldMask
	(TransactionType)(0),               // 4: card.enums.TransactionType
	(CardRequestWorkflow)(0),           // 5: card.enums.CardRequestWorkflow
	(CardRequestStatus)(0),             // 6: card.enums.CardRequestStatus
	(CardRequestStageName)(0),          // 7: card.enums.CardRequestStageName
	(CardRequestStageStatus)(0),        // 8: card.enums.CardRequestStageStatus
	(CardRequestStageSubStatus)(0),     // 9: card.enums.CardRequestStageSubStatus
	(CardRequestFieldMask)(0),          // 10: card.enums.CardRequestFieldMask
	(CardNudgeType)(0),                 // 11: card.enums.CardNudgeType
	(OperationalStatus)(0),             // 12: card.enums.OperationalStatus
	(FreezeStatus)(0),                  // 13: card.enums.FreezeStatus
	(OrderPhysicalCardUiEntryPoint)(0), // 14: card.enums.OrderPhysicalCardUiEntryPoint
}
var file_api_card_enums_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_card_enums_enums_proto_init() }
func file_api_card_enums_enums_proto_init() {
	if File_api_card_enums_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_enums_enums_proto_rawDesc,
			NumEnums:      15,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_enums_enums_proto_goTypes,
		DependencyIndexes: file_api_card_enums_enums_proto_depIdxs,
		EnumInfos:         file_api_card_enums_enums_proto_enumTypes,
	}.Build()
	File_api_card_enums_enums_proto = out.File
	file_api_card_enums_enums_proto_rawDesc = nil
	file_api_card_enums_enums_proto_goTypes = nil
	file_api_card_enums_enums_proto_depIdxs = nil
}
