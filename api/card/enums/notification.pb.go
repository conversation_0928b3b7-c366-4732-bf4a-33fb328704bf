// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=NotificationType,TransactionState,NotificationSource

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/enums/notification.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Enum denoting the type of message received for card switch notifications
type MessageType int32

const (
	MessageType_MESSAGE_TYPE_UNSPECIFIED       MessageType = 0
	MessageType_MESSAGE_TYPE_REQUEST           MessageType = 1
	MessageType_MESSAGE_TYPE_RESPONSE          MessageType = 2
	MessageType_MESSAGE_TYPE_ADVICE            MessageType = 3
	MessageType_MESSAGE_TYPE_REVERSAL_REQUEST  MessageType = 4
	MessageType_MESSAGE_TYPE_REVERSAL_RESPONSE MessageType = 5
	MessageType_MESSAGE_TYPE_REVERSAL          MessageType = 6
	MessageType_MESSAGE_TYPE_LOG_ONLY          MessageType = 7
)

// Enum value maps for MessageType.
var (
	MessageType_name = map[int32]string{
		0: "MESSAGE_TYPE_UNSPECIFIED",
		1: "MESSAGE_TYPE_REQUEST",
		2: "MESSAGE_TYPE_RESPONSE",
		3: "MESSAGE_TYPE_ADVICE",
		4: "MESSAGE_TYPE_REVERSAL_REQUEST",
		5: "MESSAGE_TYPE_REVERSAL_RESPONSE",
		6: "MESSAGE_TYPE_REVERSAL",
		7: "MESSAGE_TYPE_LOG_ONLY",
	}
	MessageType_value = map[string]int32{
		"MESSAGE_TYPE_UNSPECIFIED":       0,
		"MESSAGE_TYPE_REQUEST":           1,
		"MESSAGE_TYPE_RESPONSE":          2,
		"MESSAGE_TYPE_ADVICE":            3,
		"MESSAGE_TYPE_REVERSAL_REQUEST":  4,
		"MESSAGE_TYPE_REVERSAL_RESPONSE": 5,
		"MESSAGE_TYPE_REVERSAL":          6,
		"MESSAGE_TYPE_LOG_ONLY":          7,
	}
)

func (x MessageType) Enum() *MessageType {
	p := new(MessageType)
	*p = x
	return p
}

func (x MessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_notification_proto_enumTypes[0].Descriptor()
}

func (MessageType) Type() protoreflect.EnumType {
	return &file_api_card_enums_notification_proto_enumTypes[0]
}

func (x MessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageType.Descriptor instead.
func (MessageType) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_notification_proto_rawDescGZIP(), []int{0}
}

// Enum denoting the card transaction category for different card transactions like ECOMM, ATM, POS and NFC(contactless)
type CardTransactionCategory int32

const (
	CardTransactionCategory_CARD_TRANSACTION_CATEGORY_UNSPECIFIED    CardTransactionCategory = 0
	CardTransactionCategory_CARD_TRANSACTION_CATEGORY_ECOMM          CardTransactionCategory = 1
	CardTransactionCategory_CARD_TRANSACTION_CATEGORY_ATM_WITHDRAWAL CardTransactionCategory = 2
	CardTransactionCategory_CARD_TRANSACTION_CATEGORY_NFC            CardTransactionCategory = 3
	CardTransactionCategory_CARD_TRANSACTION_CATEGORY_POS            CardTransactionCategory = 4
)

// Enum value maps for CardTransactionCategory.
var (
	CardTransactionCategory_name = map[int32]string{
		0: "CARD_TRANSACTION_CATEGORY_UNSPECIFIED",
		1: "CARD_TRANSACTION_CATEGORY_ECOMM",
		2: "CARD_TRANSACTION_CATEGORY_ATM_WITHDRAWAL",
		3: "CARD_TRANSACTION_CATEGORY_NFC",
		4: "CARD_TRANSACTION_CATEGORY_POS",
	}
	CardTransactionCategory_value = map[string]int32{
		"CARD_TRANSACTION_CATEGORY_UNSPECIFIED":    0,
		"CARD_TRANSACTION_CATEGORY_ECOMM":          1,
		"CARD_TRANSACTION_CATEGORY_ATM_WITHDRAWAL": 2,
		"CARD_TRANSACTION_CATEGORY_NFC":            3,
		"CARD_TRANSACTION_CATEGORY_POS":            4,
	}
)

func (x CardTransactionCategory) Enum() *CardTransactionCategory {
	p := new(CardTransactionCategory)
	*p = x
	return p
}

func (x CardTransactionCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardTransactionCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_notification_proto_enumTypes[1].Descriptor()
}

func (CardTransactionCategory) Type() protoreflect.EnumType {
	return &file_api_card_enums_notification_proto_enumTypes[1]
}

func (x CardTransactionCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardTransactionCategory.Descriptor instead.
func (CardTransactionCategory) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_notification_proto_rawDescGZIP(), []int{1}
}

// Type of notification event received from vendor
type NotificationType int32

const (
	NotificationType_NOTIFICATION_TYPE_UNSPECIFIED                                   NotificationType = 0
	NotificationType_NOTIFICATION_TYPE_MOBILE_BANKING_REGISTRATION                   NotificationType = 1
	NotificationType_NOTIFICATION_TYPE_PIN_CHANGE                                    NotificationType = 2
	NotificationType_NOTIFICATION_TYPE_PIN_SET                                       NotificationType = 3
	NotificationType_NOTIFICATION_TYPE_LIMIT_UPDATE                                  NotificationType = 4
	NotificationType_NOTIFICATION_TYPE_CARD_STATE_UPDATE                             NotificationType = 5
	NotificationType_NOTIFICATION_TYPE_TOKEN_COMPLETION                              NotificationType = 6
	NotificationType_NOTIFICATION_TYPE_TOKEN_EVENT                                   NotificationType = 7
	NotificationType_NOTIFICATION_TYPE_TOKEN_AUTHORIZATION_TRANSACTION               NotificationType = 8
	NotificationType_NOTIFICATION_TYPE_WITHDRAWAL                                    NotificationType = 9
	NotificationType_NOTIFICATION_TYPE_VISA_MONEY_TRANSFER                           NotificationType = 10
	NotificationType_NOTIFICATION_TYPE_REFUND_TRANSACTION                            NotificationType = 11
	NotificationType_NOTIFICATION_TYPE_POS_ECOMM_PURCHASE                            NotificationType = 12
	NotificationType_NOTIFICATION_TYPE_RECURRING_PAYMENT_TRANSACTION                 NotificationType = 13
	NotificationType_NOTIFICATION_TYPE_RECURRING_REGISTRATION_TRANSACTION            NotificationType = 14
	NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL                          NotificationType = 15
	NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CUSTOMER_DEBIT           NotificationType = 16
	NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CUSTOMER_CREDIT          NotificationType = 17
	NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CARD_INITIATED           NotificationType = 18
	NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_OTHER_BANK_CARD_FEDERAL_BANK_ATM NotificationType = 19
	NotificationType_NOTIFICATION_TYPE_MINI_STATEMENT                                NotificationType = 20
	NotificationType_NOTIFICATION_TYPE_BALANCE_ENQUIRY                               NotificationType = 21
	NotificationType_NOTIFICATION_TYPE_PIN_VALIDATION                                NotificationType = 22
	NotificationType_NOTIFICATION_TYPE_CARD_VALIDATION                               NotificationType = 23
	NotificationType_NOTIFICATION_TYPE_ACCOUNT_STATUS_ENQUIRY                        NotificationType = 24
)

// Enum value maps for NotificationType.
var (
	NotificationType_name = map[int32]string{
		0:  "NOTIFICATION_TYPE_UNSPECIFIED",
		1:  "NOTIFICATION_TYPE_MOBILE_BANKING_REGISTRATION",
		2:  "NOTIFICATION_TYPE_PIN_CHANGE",
		3:  "NOTIFICATION_TYPE_PIN_SET",
		4:  "NOTIFICATION_TYPE_LIMIT_UPDATE",
		5:  "NOTIFICATION_TYPE_CARD_STATE_UPDATE",
		6:  "NOTIFICATION_TYPE_TOKEN_COMPLETION",
		7:  "NOTIFICATION_TYPE_TOKEN_EVENT",
		8:  "NOTIFICATION_TYPE_TOKEN_AUTHORIZATION_TRANSACTION",
		9:  "NOTIFICATION_TYPE_WITHDRAWAL",
		10: "NOTIFICATION_TYPE_VISA_MONEY_TRANSFER",
		11: "NOTIFICATION_TYPE_REFUND_TRANSACTION",
		12: "NOTIFICATION_TYPE_POS_ECOMM_PURCHASE",
		13: "NOTIFICATION_TYPE_RECURRING_PAYMENT_TRANSACTION",
		14: "NOTIFICATION_TYPE_RECURRING_REGISTRATION_TRANSACTION",
		15: "NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL",
		16: "NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CUSTOMER_DEBIT",
		17: "NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CUSTOMER_CREDIT",
		18: "NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CARD_INITIATED",
		19: "NOTIFICATION_TYPE_CARD_TO_CARD_OTHER_BANK_CARD_FEDERAL_BANK_ATM",
		20: "NOTIFICATION_TYPE_MINI_STATEMENT",
		21: "NOTIFICATION_TYPE_BALANCE_ENQUIRY",
		22: "NOTIFICATION_TYPE_PIN_VALIDATION",
		23: "NOTIFICATION_TYPE_CARD_VALIDATION",
		24: "NOTIFICATION_TYPE_ACCOUNT_STATUS_ENQUIRY",
	}
	NotificationType_value = map[string]int32{
		"NOTIFICATION_TYPE_UNSPECIFIED":                                   0,
		"NOTIFICATION_TYPE_MOBILE_BANKING_REGISTRATION":                   1,
		"NOTIFICATION_TYPE_PIN_CHANGE":                                    2,
		"NOTIFICATION_TYPE_PIN_SET":                                       3,
		"NOTIFICATION_TYPE_LIMIT_UPDATE":                                  4,
		"NOTIFICATION_TYPE_CARD_STATE_UPDATE":                             5,
		"NOTIFICATION_TYPE_TOKEN_COMPLETION":                              6,
		"NOTIFICATION_TYPE_TOKEN_EVENT":                                   7,
		"NOTIFICATION_TYPE_TOKEN_AUTHORIZATION_TRANSACTION":               8,
		"NOTIFICATION_TYPE_WITHDRAWAL":                                    9,
		"NOTIFICATION_TYPE_VISA_MONEY_TRANSFER":                           10,
		"NOTIFICATION_TYPE_REFUND_TRANSACTION":                            11,
		"NOTIFICATION_TYPE_POS_ECOMM_PURCHASE":                            12,
		"NOTIFICATION_TYPE_RECURRING_PAYMENT_TRANSACTION":                 13,
		"NOTIFICATION_TYPE_RECURRING_REGISTRATION_TRANSACTION":            14,
		"NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL":                          15,
		"NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CUSTOMER_DEBIT":           16,
		"NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CUSTOMER_CREDIT":          17,
		"NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CARD_INITIATED":           18,
		"NOTIFICATION_TYPE_CARD_TO_CARD_OTHER_BANK_CARD_FEDERAL_BANK_ATM": 19,
		"NOTIFICATION_TYPE_MINI_STATEMENT":                                20,
		"NOTIFICATION_TYPE_BALANCE_ENQUIRY":                               21,
		"NOTIFICATION_TYPE_PIN_VALIDATION":                                22,
		"NOTIFICATION_TYPE_CARD_VALIDATION":                               23,
		"NOTIFICATION_TYPE_ACCOUNT_STATUS_ENQUIRY":                        24,
	}
)

func (x NotificationType) Enum() *NotificationType {
	p := new(NotificationType)
	*p = x
	return p
}

func (x NotificationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_notification_proto_enumTypes[2].Descriptor()
}

func (NotificationType) Type() protoreflect.EnumType {
	return &file_api_card_enums_notification_proto_enumTypes[2]
}

func (x NotificationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationType.Descriptor instead.
func (NotificationType) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_notification_proto_rawDescGZIP(), []int{2}
}

// Indicates the manner in which transactions have entered the authorization switch
type AuthorizationSwitch int32

const (
	AuthorizationSwitch_AUTHORIZATION_SWITCH_UNSPECIFIED                                AuthorizationSwitch = 0
	AuthorizationSwitch_AUTHORIZATION_SWITCH_TELEPHONIC_DEVICE_REQUEST                  AuthorizationSwitch = 1
	AuthorizationSwitch_AUTHORIZATION_SWITCH_MAIL_OR_TELEPHONE_ORDER                    AuthorizationSwitch = 2
	AuthorizationSwitch_AUTHORIZATION_SWITCH_SECURITY_ALERT                             AuthorizationSwitch = 3
	AuthorizationSwitch_AUTHORIZATION_SWITCH_CUSTOMER_IDENTITY_VERIFIED                 AuthorizationSwitch = 4
	AuthorizationSwitch_AUTHORIZATION_SWITCH_SUSPECTED_FRAUD                            AuthorizationSwitch = 5
	AuthorizationSwitch_AUTHORIZATION_SWITCH_SECURITY_REASONS                           AuthorizationSwitch = 6
	AuthorizationSwitch_AUTHORIZATION_SWITCH_REPRESENTMENT_OF_ITEM                      AuthorizationSwitch = 7
	AuthorizationSwitch_AUTHORIZATION_SWITCH_PUBLIC_UTILITY_TERMINAL                    AuthorizationSwitch = 8
	AuthorizationSwitch_AUTHORIZATION_SWITCH_CUSTOMER_TERMINAL                          AuthorizationSwitch = 9
	AuthorizationSwitch_AUTHORIZATION_SWITCH_ADMINISTRATION_TERMINAL                    AuthorizationSwitch = 10
	AuthorizationSwitch_AUTHORIZATION_SWITCH_RETURNED_ITEM                              AuthorizationSwitch = 11
	AuthorizationSwitch_AUTHORIZATION_SWITCH_MANUAL_REVERSAL                            AuthorizationSwitch = 12
	AuthorizationSwitch_AUTHORIZATION_SWITCH_TERMINAL_ERROR_OR_COUNTED                  AuthorizationSwitch = 13
	AuthorizationSwitch_AUTHORIZATION_SWITCH_TERMINAL_ERROR_OR_NOT_COUNTED              AuthorizationSwitch = 14
	AuthorizationSwitch_AUTHORIZATION_SWITCH_DEPOSIT_OUT_OF_BALANCE_OR_APPLIED_CONTENTS AuthorizationSwitch = 15
	AuthorizationSwitch_AUTHORIZATION_SWITCH_PAYMENT_OUT_OF_BALANCE_OR_APPLIED_CONTENTS AuthorizationSwitch = 16
	AuthorizationSwitch_AUTHORIZATION_SWITCH_WITHDRAWAL_HAD_ERROR_REVERSED              AuthorizationSwitch = 17
	AuthorizationSwitch_AUTHORIZATION_SWITCH_UNATTENDED_TERMINAL_UNABLE_TO_RETAIN_CARD  AuthorizationSwitch = 18
	AuthorizationSwitch_AUTHORIZATION_SWITCH_RESERVED_FOR_ISO_USE                       AuthorizationSwitch = 19
	AuthorizationSwitch_AUTHORIZATION_SWITCH_RESERVED_FOR_NATIONAL_USE                  AuthorizationSwitch = 20
	AuthorizationSwitch_AUTHORIZATION_SWITCH_ADDRESS_VERIFICATION                       AuthorizationSwitch = 21
	AuthorizationSwitch_AUTHORIZATION_SWITCH_RESERVED_FOR_PRIVATE_USE                   AuthorizationSwitch = 22
)

// Enum value maps for AuthorizationSwitch.
var (
	AuthorizationSwitch_name = map[int32]string{
		0:  "AUTHORIZATION_SWITCH_UNSPECIFIED",
		1:  "AUTHORIZATION_SWITCH_TELEPHONIC_DEVICE_REQUEST",
		2:  "AUTHORIZATION_SWITCH_MAIL_OR_TELEPHONE_ORDER",
		3:  "AUTHORIZATION_SWITCH_SECURITY_ALERT",
		4:  "AUTHORIZATION_SWITCH_CUSTOMER_IDENTITY_VERIFIED",
		5:  "AUTHORIZATION_SWITCH_SUSPECTED_FRAUD",
		6:  "AUTHORIZATION_SWITCH_SECURITY_REASONS",
		7:  "AUTHORIZATION_SWITCH_REPRESENTMENT_OF_ITEM",
		8:  "AUTHORIZATION_SWITCH_PUBLIC_UTILITY_TERMINAL",
		9:  "AUTHORIZATION_SWITCH_CUSTOMER_TERMINAL",
		10: "AUTHORIZATION_SWITCH_ADMINISTRATION_TERMINAL",
		11: "AUTHORIZATION_SWITCH_RETURNED_ITEM",
		12: "AUTHORIZATION_SWITCH_MANUAL_REVERSAL",
		13: "AUTHORIZATION_SWITCH_TERMINAL_ERROR_OR_COUNTED",
		14: "AUTHORIZATION_SWITCH_TERMINAL_ERROR_OR_NOT_COUNTED",
		15: "AUTHORIZATION_SWITCH_DEPOSIT_OUT_OF_BALANCE_OR_APPLIED_CONTENTS",
		16: "AUTHORIZATION_SWITCH_PAYMENT_OUT_OF_BALANCE_OR_APPLIED_CONTENTS",
		17: "AUTHORIZATION_SWITCH_WITHDRAWAL_HAD_ERROR_REVERSED",
		18: "AUTHORIZATION_SWITCH_UNATTENDED_TERMINAL_UNABLE_TO_RETAIN_CARD",
		19: "AUTHORIZATION_SWITCH_RESERVED_FOR_ISO_USE",
		20: "AUTHORIZATION_SWITCH_RESERVED_FOR_NATIONAL_USE",
		21: "AUTHORIZATION_SWITCH_ADDRESS_VERIFICATION",
		22: "AUTHORIZATION_SWITCH_RESERVED_FOR_PRIVATE_USE",
	}
	AuthorizationSwitch_value = map[string]int32{
		"AUTHORIZATION_SWITCH_UNSPECIFIED":                                0,
		"AUTHORIZATION_SWITCH_TELEPHONIC_DEVICE_REQUEST":                  1,
		"AUTHORIZATION_SWITCH_MAIL_OR_TELEPHONE_ORDER":                    2,
		"AUTHORIZATION_SWITCH_SECURITY_ALERT":                             3,
		"AUTHORIZATION_SWITCH_CUSTOMER_IDENTITY_VERIFIED":                 4,
		"AUTHORIZATION_SWITCH_SUSPECTED_FRAUD":                            5,
		"AUTHORIZATION_SWITCH_SECURITY_REASONS":                           6,
		"AUTHORIZATION_SWITCH_REPRESENTMENT_OF_ITEM":                      7,
		"AUTHORIZATION_SWITCH_PUBLIC_UTILITY_TERMINAL":                    8,
		"AUTHORIZATION_SWITCH_CUSTOMER_TERMINAL":                          9,
		"AUTHORIZATION_SWITCH_ADMINISTRATION_TERMINAL":                    10,
		"AUTHORIZATION_SWITCH_RETURNED_ITEM":                              11,
		"AUTHORIZATION_SWITCH_MANUAL_REVERSAL":                            12,
		"AUTHORIZATION_SWITCH_TERMINAL_ERROR_OR_COUNTED":                  13,
		"AUTHORIZATION_SWITCH_TERMINAL_ERROR_OR_NOT_COUNTED":              14,
		"AUTHORIZATION_SWITCH_DEPOSIT_OUT_OF_BALANCE_OR_APPLIED_CONTENTS": 15,
		"AUTHORIZATION_SWITCH_PAYMENT_OUT_OF_BALANCE_OR_APPLIED_CONTENTS": 16,
		"AUTHORIZATION_SWITCH_WITHDRAWAL_HAD_ERROR_REVERSED":              17,
		"AUTHORIZATION_SWITCH_UNATTENDED_TERMINAL_UNABLE_TO_RETAIN_CARD":  18,
		"AUTHORIZATION_SWITCH_RESERVED_FOR_ISO_USE":                       19,
		"AUTHORIZATION_SWITCH_RESERVED_FOR_NATIONAL_USE":                  20,
		"AUTHORIZATION_SWITCH_ADDRESS_VERIFICATION":                       21,
		"AUTHORIZATION_SWITCH_RESERVED_FOR_PRIVATE_USE":                   22,
	}
)

func (x AuthorizationSwitch) Enum() *AuthorizationSwitch {
	p := new(AuthorizationSwitch)
	*p = x
	return p
}

func (x AuthorizationSwitch) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthorizationSwitch) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_notification_proto_enumTypes[3].Descriptor()
}

func (AuthorizationSwitch) Type() protoreflect.EnumType {
	return &file_api_card_enums_notification_proto_enumTypes[3]
}

func (x AuthorizationSwitch) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthorizationSwitch.Descriptor instead.
func (AuthorizationSwitch) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_notification_proto_rawDescGZIP(), []int{3}
}

// Entry mode of transaction in the switch
type TransactionEntryMode int32

const (
	TransactionEntryMode_TRANSACTION_ENTRY_MODE_UNSPECIFIED                         TransactionEntryMode = 0
	TransactionEntryMode_TRANSACTION_ENTRY_MODE_MANUAL                              TransactionEntryMode = 1
	TransactionEntryMode_TRANSACTION_ENTRY_MODE_MAGNETIC_STRIPE                     TransactionEntryMode = 2
	TransactionEntryMode_TRANSACTION_ENTRY_MODE_BAR_CODE                            TransactionEntryMode = 3
	TransactionEntryMode_TRANSACTION_ENTRY_MODE_OCR                                 TransactionEntryMode = 4
	TransactionEntryMode_TRANSACTION_ENTRY_MODE_INTEGRATED_CIRCUIT_CARD             TransactionEntryMode = 5
	TransactionEntryMode_TRANSACTION_ENTRY_MODE_ISO_USE                             TransactionEntryMode = 6
	TransactionEntryMode_TRANSACTION_ENTRY_MODE_INTEGRATED_CIRCUIT_CARD_CONTACTLESS TransactionEntryMode = 7
	TransactionEntryMode_TRANSACTION_ENTRY_MODE_NATIONAL_USE                        TransactionEntryMode = 8
	TransactionEntryMode_TRANSACTION_ENTRY_MODE_PRIVATE_USE                         TransactionEntryMode = 9
	TransactionEntryMode_TRANSACTION_ENTRY_MODE_MAGNETIC_STRIPE_CONTACTLESS         TransactionEntryMode = 10
)

// Enum value maps for TransactionEntryMode.
var (
	TransactionEntryMode_name = map[int32]string{
		0:  "TRANSACTION_ENTRY_MODE_UNSPECIFIED",
		1:  "TRANSACTION_ENTRY_MODE_MANUAL",
		2:  "TRANSACTION_ENTRY_MODE_MAGNETIC_STRIPE",
		3:  "TRANSACTION_ENTRY_MODE_BAR_CODE",
		4:  "TRANSACTION_ENTRY_MODE_OCR",
		5:  "TRANSACTION_ENTRY_MODE_INTEGRATED_CIRCUIT_CARD",
		6:  "TRANSACTION_ENTRY_MODE_ISO_USE",
		7:  "TRANSACTION_ENTRY_MODE_INTEGRATED_CIRCUIT_CARD_CONTACTLESS",
		8:  "TRANSACTION_ENTRY_MODE_NATIONAL_USE",
		9:  "TRANSACTION_ENTRY_MODE_PRIVATE_USE",
		10: "TRANSACTION_ENTRY_MODE_MAGNETIC_STRIPE_CONTACTLESS",
	}
	TransactionEntryMode_value = map[string]int32{
		"TRANSACTION_ENTRY_MODE_UNSPECIFIED":                         0,
		"TRANSACTION_ENTRY_MODE_MANUAL":                              1,
		"TRANSACTION_ENTRY_MODE_MAGNETIC_STRIPE":                     2,
		"TRANSACTION_ENTRY_MODE_BAR_CODE":                            3,
		"TRANSACTION_ENTRY_MODE_OCR":                                 4,
		"TRANSACTION_ENTRY_MODE_INTEGRATED_CIRCUIT_CARD":             5,
		"TRANSACTION_ENTRY_MODE_ISO_USE":                             6,
		"TRANSACTION_ENTRY_MODE_INTEGRATED_CIRCUIT_CARD_CONTACTLESS": 7,
		"TRANSACTION_ENTRY_MODE_NATIONAL_USE":                        8,
		"TRANSACTION_ENTRY_MODE_PRIVATE_USE":                         9,
		"TRANSACTION_ENTRY_MODE_MAGNETIC_STRIPE_CONTACTLESS":         10,
	}
)

func (x TransactionEntryMode) Enum() *TransactionEntryMode {
	p := new(TransactionEntryMode)
	*p = x
	return p
}

func (x TransactionEntryMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionEntryMode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_notification_proto_enumTypes[4].Descriptor()
}

func (TransactionEntryMode) Type() protoreflect.EnumType {
	return &file_api_card_enums_notification_proto_enumTypes[4]
}

func (x TransactionEntryMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionEntryMode.Descriptor instead.
func (TransactionEntryMode) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_notification_proto_rawDescGZIP(), []int{4}
}

// Transaction status, we will only get terminal transaction status in the notifications
type TransactionState int32

const (
	TransactionState_TRANSACTION_STATE_UNSPECIFIED TransactionState = 0
	TransactionState_TRANSACTION_STATE_SUCCESS     TransactionState = 1
	TransactionState_TRANSACTION_STATE_FAILURE     TransactionState = 2
)

// Enum value maps for TransactionState.
var (
	TransactionState_name = map[int32]string{
		0: "TRANSACTION_STATE_UNSPECIFIED",
		1: "TRANSACTION_STATE_SUCCESS",
		2: "TRANSACTION_STATE_FAILURE",
	}
	TransactionState_value = map[string]int32{
		"TRANSACTION_STATE_UNSPECIFIED": 0,
		"TRANSACTION_STATE_SUCCESS":     1,
		"TRANSACTION_STATE_FAILURE":     2,
	}
)

func (x TransactionState) Enum() *TransactionState {
	p := new(TransactionState)
	*p = x
	return p
}

func (x TransactionState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_notification_proto_enumTypes[5].Descriptor()
}

func (TransactionState) Type() protoreflect.EnumType {
	return &file_api_card_enums_notification_proto_enumTypes[5]
}

func (x TransactionState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionState.Descriptor instead.
func (TransactionState) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_notification_proto_rawDescGZIP(), []int{5}
}

type SwitchNotificationResponse int32

const (
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNSPECIFIED                     SwitchNotificationResponse = 0
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNABLE_TO_PROCESS_TRANSACTION   SwitchNotificationResponse = 1
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INCORRECT_PIN                   SwitchNotificationResponse = 2
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNABLE_TO_AUTHORIZE_TRANSACTION SwitchNotificationResponse = 3
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CARD_EXPIRED                    SwitchNotificationResponse = 4
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_ECOM_TRANSACTIONS_NOT_ENABLED   SwitchNotificationResponse = 5
	// Limit set by user in fi app
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DAILY_TRANSACTIONS_AMT_LIMIT_REACHED      SwitchNotificationResponse = 6
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_POS_NOT_SUPPORTED                         SwitchNotificationResponse = 7
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_PIN_TRIES_EXCEEDED_FOR_TRANSACTIONS       SwitchNotificationResponse = 8
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DUPLICATE_TRANSACTION                     SwitchNotificationResponse = 9
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_TRANSACTION_DECLINED                      SwitchNotificationResponse = 10
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_TRANSACTION_TYPE_NOT_SUPPORTED            SwitchNotificationResponse = 11
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INVALID_TRANSACTION                       SwitchNotificationResponse = 12
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INTERNATIONAL_TRANSACTIONS_NOT_ENABLED    SwitchNotificationResponse = 13
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CONTACTLESS_CARD_USAGE_NOT_ENABLED        SwitchNotificationResponse = 14
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INSUFFICIENT_FUNDS_FOR_TRANSACTION        SwitchNotificationResponse = 15
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DAILY_WITHDRAWAL_LIMIT_REACHED            SwitchNotificationResponse = 16
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_LOW_FUNDS_FOR_TRANSACTION                 SwitchNotificationResponse = 17
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INVALID_EXPIRY_DATE                       SwitchNotificationResponse = 18
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_NFC_NOT_ENABLED                           SwitchNotificationResponse = 19
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_PRM_DECLINED                              SwitchNotificationResponse = 20
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CVV_ERROR                                 SwitchNotificationResponse = 21
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DAILY_CONTACTLESS_PAYMENTS_LIMIT_EXCEEDED SwitchNotificationResponse = 22
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CARD_OFF_FOR_TRANSACTIONS                 SwitchNotificationResponse = 23
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_HOST_DOWN                                 SwitchNotificationResponse = 24
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DOMESTIC_TRANSACTIONS_NOT_ENABLED         SwitchNotificationResponse = 25
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_ELA_FAILURE                               SwitchNotificationResponse = 26
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_POS_USAGE_NOT_ENABLED                     SwitchNotificationResponse = 27
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_ATM_USAGE_NOT_ENABLED                     SwitchNotificationResponse = 28
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CAF_NOT_FOUND                             SwitchNotificationResponse = 29
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_ATM_FLAG_OFF                              SwitchNotificationResponse = 30
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_LOST_OR_STOLEN_CARD                       SwitchNotificationResponse = 31
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INELIGIBLE_ACCOUNT                        SwitchNotificationResponse = 32
	// switch interface hub decline
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_SI_HUB_DECLINE     SwitchNotificationResponse = 33
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNAUTHORIZED_USAGE SwitchNotificationResponse = 34
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INVALID_CARD       SwitchNotificationResponse = 35
	// Max daily limit set by bank
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_AMOUNT_OVER_DAILY_MAX SwitchNotificationResponse = 36
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_MESSAGE_EDIT_ERROR    SwitchNotificationResponse = 37
	// Exceeds withdrawal limit of card
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_AMOUNT_OVER_WITHDRAWAL_LIMIT SwitchNotificationResponse = 38
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CAF_STATUS_DECLINE           SwitchNotificationResponse = 39
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_FALLBACK_DECLINE             SwitchNotificationResponse = 40
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_APPROVED_NO_BALANCES         SwitchNotificationResponse = 41
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DEST_NOT_AVAILABLE           SwitchNotificationResponse = 42
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_ATC_CHECK_FAILURE            SwitchNotificationResponse = 43
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_TOKEN_IN_APP_FLAG_OFF        SwitchNotificationResponse = 44
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_SYSTEM_ERROR                 SwitchNotificationResponse = 45
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_NO_IDF                       SwitchNotificationResponse = 46
	// Security check failure (cryptogram failure)
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_ARQC_FAILURE       SwitchNotificationResponse = 47
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_TOKEN_COF_FLAG_OFF SwitchNotificationResponse = 48
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INVALID_TXN_DATE   SwitchNotificationResponse = 49
	// CAF STATUS 3 (indicating that card to be captured in CAF page 7)
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CARD_TO_BE_CAPTURED_IN_CAF SwitchNotificationResponse = 50
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_BAD_CARD_STATUS            SwitchNotificationResponse = 51
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_RESERVED_B24_CODE          SwitchNotificationResponse = 52
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_HSM_PARAM_ERROR            SwitchNotificationResponse = 53
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_MAX_CREDIT_PER_REFUND      SwitchNotificationResponse = 54
	// Allowed number of uses exceeded
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_USAGE_LIMIT_EXCEEDED SwitchNotificationResponse = 55
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_TOKEN_NFC_FLAG_OFF   SwitchNotificationResponse = 56
	SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_HOST_NOT_AVAILABLE   SwitchNotificationResponse = 57
)

// Enum value maps for SwitchNotificationResponse.
var (
	SwitchNotificationResponse_name = map[int32]string{
		0:  "SWITCH_NOTIFICATION_RESPONSE_UNSPECIFIED",
		1:  "SWITCH_NOTIFICATION_RESPONSE_UNABLE_TO_PROCESS_TRANSACTION",
		2:  "SWITCH_NOTIFICATION_RESPONSE_INCORRECT_PIN",
		3:  "SWITCH_NOTIFICATION_RESPONSE_UNABLE_TO_AUTHORIZE_TRANSACTION",
		4:  "SWITCH_NOTIFICATION_RESPONSE_CARD_EXPIRED",
		5:  "SWITCH_NOTIFICATION_RESPONSE_ECOM_TRANSACTIONS_NOT_ENABLED",
		6:  "SWITCH_NOTIFICATION_RESPONSE_DAILY_TRANSACTIONS_AMT_LIMIT_REACHED",
		7:  "SWITCH_NOTIFICATION_RESPONSE_POS_NOT_SUPPORTED",
		8:  "SWITCH_NOTIFICATION_RESPONSE_PIN_TRIES_EXCEEDED_FOR_TRANSACTIONS",
		9:  "SWITCH_NOTIFICATION_RESPONSE_DUPLICATE_TRANSACTION",
		10: "SWITCH_NOTIFICATION_RESPONSE_TRANSACTION_DECLINED",
		11: "SWITCH_NOTIFICATION_RESPONSE_TRANSACTION_TYPE_NOT_SUPPORTED",
		12: "SWITCH_NOTIFICATION_RESPONSE_INVALID_TRANSACTION",
		13: "SWITCH_NOTIFICATION_RESPONSE_INTERNATIONAL_TRANSACTIONS_NOT_ENABLED",
		14: "SWITCH_NOTIFICATION_RESPONSE_CONTACTLESS_CARD_USAGE_NOT_ENABLED",
		15: "SWITCH_NOTIFICATION_RESPONSE_INSUFFICIENT_FUNDS_FOR_TRANSACTION",
		16: "SWITCH_NOTIFICATION_RESPONSE_DAILY_WITHDRAWAL_LIMIT_REACHED",
		17: "SWITCH_NOTIFICATION_RESPONSE_LOW_FUNDS_FOR_TRANSACTION",
		18: "SWITCH_NOTIFICATION_RESPONSE_INVALID_EXPIRY_DATE",
		19: "SWITCH_NOTIFICATION_RESPONSE_NFC_NOT_ENABLED",
		20: "SWITCH_NOTIFICATION_RESPONSE_PRM_DECLINED",
		21: "SWITCH_NOTIFICATION_RESPONSE_CVV_ERROR",
		22: "SWITCH_NOTIFICATION_RESPONSE_DAILY_CONTACTLESS_PAYMENTS_LIMIT_EXCEEDED",
		23: "SWITCH_NOTIFICATION_RESPONSE_CARD_OFF_FOR_TRANSACTIONS",
		24: "SWITCH_NOTIFICATION_RESPONSE_HOST_DOWN",
		25: "SWITCH_NOTIFICATION_RESPONSE_DOMESTIC_TRANSACTIONS_NOT_ENABLED",
		26: "SWITCH_NOTIFICATION_RESPONSE_ELA_FAILURE",
		27: "SWITCH_NOTIFICATION_RESPONSE_POS_USAGE_NOT_ENABLED",
		28: "SWITCH_NOTIFICATION_RESPONSE_ATM_USAGE_NOT_ENABLED",
		29: "SWITCH_NOTIFICATION_RESPONSE_CAF_NOT_FOUND",
		30: "SWITCH_NOTIFICATION_RESPONSE_ATM_FLAG_OFF",
		31: "SWITCH_NOTIFICATION_RESPONSE_LOST_OR_STOLEN_CARD",
		32: "SWITCH_NOTIFICATION_RESPONSE_INELIGIBLE_ACCOUNT",
		33: "SWITCH_NOTIFICATION_RESPONSE_SI_HUB_DECLINE",
		34: "SWITCH_NOTIFICATION_RESPONSE_UNAUTHORIZED_USAGE",
		35: "SWITCH_NOTIFICATION_RESPONSE_INVALID_CARD",
		36: "SWITCH_NOTIFICATION_RESPONSE_AMOUNT_OVER_DAILY_MAX",
		37: "SWITCH_NOTIFICATION_RESPONSE_MESSAGE_EDIT_ERROR",
		38: "SWITCH_NOTIFICATION_RESPONSE_AMOUNT_OVER_WITHDRAWAL_LIMIT",
		39: "SWITCH_NOTIFICATION_RESPONSE_CAF_STATUS_DECLINE",
		40: "SWITCH_NOTIFICATION_RESPONSE_FALLBACK_DECLINE",
		41: "SWITCH_NOTIFICATION_RESPONSE_APPROVED_NO_BALANCES",
		42: "SWITCH_NOTIFICATION_RESPONSE_DEST_NOT_AVAILABLE",
		43: "SWITCH_NOTIFICATION_RESPONSE_ATC_CHECK_FAILURE",
		44: "SWITCH_NOTIFICATION_RESPONSE_TOKEN_IN_APP_FLAG_OFF",
		45: "SWITCH_NOTIFICATION_RESPONSE_SYSTEM_ERROR",
		46: "SWITCH_NOTIFICATION_RESPONSE_NO_IDF",
		47: "SWITCH_NOTIFICATION_RESPONSE_ARQC_FAILURE",
		48: "SWITCH_NOTIFICATION_RESPONSE_TOKEN_COF_FLAG_OFF",
		49: "SWITCH_NOTIFICATION_RESPONSE_INVALID_TXN_DATE",
		50: "SWITCH_NOTIFICATION_RESPONSE_CARD_TO_BE_CAPTURED_IN_CAF",
		51: "SWITCH_NOTIFICATION_RESPONSE_BAD_CARD_STATUS",
		52: "SWITCH_NOTIFICATION_RESPONSE_RESERVED_B24_CODE",
		53: "SWITCH_NOTIFICATION_RESPONSE_HSM_PARAM_ERROR",
		54: "SWITCH_NOTIFICATION_RESPONSE_MAX_CREDIT_PER_REFUND",
		55: "SWITCH_NOTIFICATION_RESPONSE_USAGE_LIMIT_EXCEEDED",
		56: "SWITCH_NOTIFICATION_RESPONSE_TOKEN_NFC_FLAG_OFF",
		57: "SWITCH_NOTIFICATION_RESPONSE_HOST_NOT_AVAILABLE",
	}
	SwitchNotificationResponse_value = map[string]int32{
		"SWITCH_NOTIFICATION_RESPONSE_UNSPECIFIED":                               0,
		"SWITCH_NOTIFICATION_RESPONSE_UNABLE_TO_PROCESS_TRANSACTION":             1,
		"SWITCH_NOTIFICATION_RESPONSE_INCORRECT_PIN":                             2,
		"SWITCH_NOTIFICATION_RESPONSE_UNABLE_TO_AUTHORIZE_TRANSACTION":           3,
		"SWITCH_NOTIFICATION_RESPONSE_CARD_EXPIRED":                              4,
		"SWITCH_NOTIFICATION_RESPONSE_ECOM_TRANSACTIONS_NOT_ENABLED":             5,
		"SWITCH_NOTIFICATION_RESPONSE_DAILY_TRANSACTIONS_AMT_LIMIT_REACHED":      6,
		"SWITCH_NOTIFICATION_RESPONSE_POS_NOT_SUPPORTED":                         7,
		"SWITCH_NOTIFICATION_RESPONSE_PIN_TRIES_EXCEEDED_FOR_TRANSACTIONS":       8,
		"SWITCH_NOTIFICATION_RESPONSE_DUPLICATE_TRANSACTION":                     9,
		"SWITCH_NOTIFICATION_RESPONSE_TRANSACTION_DECLINED":                      10,
		"SWITCH_NOTIFICATION_RESPONSE_TRANSACTION_TYPE_NOT_SUPPORTED":            11,
		"SWITCH_NOTIFICATION_RESPONSE_INVALID_TRANSACTION":                       12,
		"SWITCH_NOTIFICATION_RESPONSE_INTERNATIONAL_TRANSACTIONS_NOT_ENABLED":    13,
		"SWITCH_NOTIFICATION_RESPONSE_CONTACTLESS_CARD_USAGE_NOT_ENABLED":        14,
		"SWITCH_NOTIFICATION_RESPONSE_INSUFFICIENT_FUNDS_FOR_TRANSACTION":        15,
		"SWITCH_NOTIFICATION_RESPONSE_DAILY_WITHDRAWAL_LIMIT_REACHED":            16,
		"SWITCH_NOTIFICATION_RESPONSE_LOW_FUNDS_FOR_TRANSACTION":                 17,
		"SWITCH_NOTIFICATION_RESPONSE_INVALID_EXPIRY_DATE":                       18,
		"SWITCH_NOTIFICATION_RESPONSE_NFC_NOT_ENABLED":                           19,
		"SWITCH_NOTIFICATION_RESPONSE_PRM_DECLINED":                              20,
		"SWITCH_NOTIFICATION_RESPONSE_CVV_ERROR":                                 21,
		"SWITCH_NOTIFICATION_RESPONSE_DAILY_CONTACTLESS_PAYMENTS_LIMIT_EXCEEDED": 22,
		"SWITCH_NOTIFICATION_RESPONSE_CARD_OFF_FOR_TRANSACTIONS":                 23,
		"SWITCH_NOTIFICATION_RESPONSE_HOST_DOWN":                                 24,
		"SWITCH_NOTIFICATION_RESPONSE_DOMESTIC_TRANSACTIONS_NOT_ENABLED":         25,
		"SWITCH_NOTIFICATION_RESPONSE_ELA_FAILURE":                               26,
		"SWITCH_NOTIFICATION_RESPONSE_POS_USAGE_NOT_ENABLED":                     27,
		"SWITCH_NOTIFICATION_RESPONSE_ATM_USAGE_NOT_ENABLED":                     28,
		"SWITCH_NOTIFICATION_RESPONSE_CAF_NOT_FOUND":                             29,
		"SWITCH_NOTIFICATION_RESPONSE_ATM_FLAG_OFF":                              30,
		"SWITCH_NOTIFICATION_RESPONSE_LOST_OR_STOLEN_CARD":                       31,
		"SWITCH_NOTIFICATION_RESPONSE_INELIGIBLE_ACCOUNT":                        32,
		"SWITCH_NOTIFICATION_RESPONSE_SI_HUB_DECLINE":                            33,
		"SWITCH_NOTIFICATION_RESPONSE_UNAUTHORIZED_USAGE":                        34,
		"SWITCH_NOTIFICATION_RESPONSE_INVALID_CARD":                              35,
		"SWITCH_NOTIFICATION_RESPONSE_AMOUNT_OVER_DAILY_MAX":                     36,
		"SWITCH_NOTIFICATION_RESPONSE_MESSAGE_EDIT_ERROR":                        37,
		"SWITCH_NOTIFICATION_RESPONSE_AMOUNT_OVER_WITHDRAWAL_LIMIT":              38,
		"SWITCH_NOTIFICATION_RESPONSE_CAF_STATUS_DECLINE":                        39,
		"SWITCH_NOTIFICATION_RESPONSE_FALLBACK_DECLINE":                          40,
		"SWITCH_NOTIFICATION_RESPONSE_APPROVED_NO_BALANCES":                      41,
		"SWITCH_NOTIFICATION_RESPONSE_DEST_NOT_AVAILABLE":                        42,
		"SWITCH_NOTIFICATION_RESPONSE_ATC_CHECK_FAILURE":                         43,
		"SWITCH_NOTIFICATION_RESPONSE_TOKEN_IN_APP_FLAG_OFF":                     44,
		"SWITCH_NOTIFICATION_RESPONSE_SYSTEM_ERROR":                              45,
		"SWITCH_NOTIFICATION_RESPONSE_NO_IDF":                                    46,
		"SWITCH_NOTIFICATION_RESPONSE_ARQC_FAILURE":                              47,
		"SWITCH_NOTIFICATION_RESPONSE_TOKEN_COF_FLAG_OFF":                        48,
		"SWITCH_NOTIFICATION_RESPONSE_INVALID_TXN_DATE":                          49,
		"SWITCH_NOTIFICATION_RESPONSE_CARD_TO_BE_CAPTURED_IN_CAF":                50,
		"SWITCH_NOTIFICATION_RESPONSE_BAD_CARD_STATUS":                           51,
		"SWITCH_NOTIFICATION_RESPONSE_RESERVED_B24_CODE":                         52,
		"SWITCH_NOTIFICATION_RESPONSE_HSM_PARAM_ERROR":                           53,
		"SWITCH_NOTIFICATION_RESPONSE_MAX_CREDIT_PER_REFUND":                     54,
		"SWITCH_NOTIFICATION_RESPONSE_USAGE_LIMIT_EXCEEDED":                      55,
		"SWITCH_NOTIFICATION_RESPONSE_TOKEN_NFC_FLAG_OFF":                        56,
		"SWITCH_NOTIFICATION_RESPONSE_HOST_NOT_AVAILABLE":                        57,
	}
)

func (x SwitchNotificationResponse) Enum() *SwitchNotificationResponse {
	p := new(SwitchNotificationResponse)
	*p = x
	return p
}

func (x SwitchNotificationResponse) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SwitchNotificationResponse) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_notification_proto_enumTypes[6].Descriptor()
}

func (SwitchNotificationResponse) Type() protoreflect.EnumType {
	return &file_api_card_enums_notification_proto_enumTypes[6]
}

func (x SwitchNotificationResponse) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SwitchNotificationResponse.Descriptor instead.
func (SwitchNotificationResponse) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_notification_proto_rawDescGZIP(), []int{6}
}

// source of the notification eg - switch, acs
type NotificationSource int32

const (
	NotificationSource_NOTIFICATION_SOURCE_UNSPECIFIED NotificationSource = 0
	NotificationSource_NOTIFICATION_SOURCE_SWITCH      NotificationSource = 1
)

// Enum value maps for NotificationSource.
var (
	NotificationSource_name = map[int32]string{
		0: "NOTIFICATION_SOURCE_UNSPECIFIED",
		1: "NOTIFICATION_SOURCE_SWITCH",
	}
	NotificationSource_value = map[string]int32{
		"NOTIFICATION_SOURCE_UNSPECIFIED": 0,
		"NOTIFICATION_SOURCE_SWITCH":      1,
	}
)

func (x NotificationSource) Enum() *NotificationSource {
	p := new(NotificationSource)
	*p = x
	return p
}

func (x NotificationSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationSource) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_notification_proto_enumTypes[7].Descriptor()
}

func (NotificationSource) Type() protoreflect.EnumType {
	return &file_api_card_enums_notification_proto_enumTypes[7]
}

func (x NotificationSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationSource.Descriptor instead.
func (NotificationSource) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_notification_proto_rawDescGZIP(), []int{7}
}

type CardNotificationFieldMask int32

const (
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_UNSPECIFIED                CardNotificationFieldMask = 0
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_ID                         CardNotificationFieldMask = 1
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_CARD_ID                    CardNotificationFieldMask = 2
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_RETRIEVAL_REFERENCE_NUMBER CardNotificationFieldMask = 3
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_REQUEST_ID                 CardNotificationFieldMask = 4
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_DETAILS       CardNotificationFieldMask = 5
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_TYPE          CardNotificationFieldMask = 6
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_MERCHANT_DETAILS           CardNotificationFieldMask = 7
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_AUTH_SWITCH_DETAILS        CardNotificationFieldMask = 8
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_REMITTER_DETAILS           CardNotificationFieldMask = 9
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_STATUS                     CardNotificationFieldMask = 10
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_DETAILED_STATUS            CardNotificationFieldMask = 11
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_SOURCE        CardNotificationFieldMask = 12
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_DEDUPE_ID                  CardNotificationFieldMask = 13
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_EVENT_TIME    CardNotificationFieldMask = 14
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_CREATED_AT                 CardNotificationFieldMask = 15
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_UPDATED_AT                 CardNotificationFieldMask = 16
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_DELETED_AT                 CardNotificationFieldMask = 17
	CardNotificationFieldMask_CARD_NOTIFICATION_FIELD_MASK_EXTERNAL_REF_ID            CardNotificationFieldMask = 18
)

// Enum value maps for CardNotificationFieldMask.
var (
	CardNotificationFieldMask_name = map[int32]string{
		0:  "CARD_NOTIFICATION_FIELD_MASK_UNSPECIFIED",
		1:  "CARD_NOTIFICATION_FIELD_MASK_ID",
		2:  "CARD_NOTIFICATION_FIELD_MASK_CARD_ID",
		3:  "CARD_NOTIFICATION_FIELD_MASK_RETRIEVAL_REFERENCE_NUMBER",
		4:  "CARD_NOTIFICATION_FIELD_MASK_REQUEST_ID",
		5:  "CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_DETAILS",
		6:  "CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_TYPE",
		7:  "CARD_NOTIFICATION_FIELD_MASK_MERCHANT_DETAILS",
		8:  "CARD_NOTIFICATION_FIELD_MASK_AUTH_SWITCH_DETAILS",
		9:  "CARD_NOTIFICATION_FIELD_MASK_REMITTER_DETAILS",
		10: "CARD_NOTIFICATION_FIELD_MASK_STATUS",
		11: "CARD_NOTIFICATION_FIELD_MASK_DETAILED_STATUS",
		12: "CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_SOURCE",
		13: "CARD_NOTIFICATION_FIELD_MASK_DEDUPE_ID",
		14: "CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_EVENT_TIME",
		15: "CARD_NOTIFICATION_FIELD_MASK_CREATED_AT",
		16: "CARD_NOTIFICATION_FIELD_MASK_UPDATED_AT",
		17: "CARD_NOTIFICATION_FIELD_MASK_DELETED_AT",
		18: "CARD_NOTIFICATION_FIELD_MASK_EXTERNAL_REF_ID",
	}
	CardNotificationFieldMask_value = map[string]int32{
		"CARD_NOTIFICATION_FIELD_MASK_UNSPECIFIED":                0,
		"CARD_NOTIFICATION_FIELD_MASK_ID":                         1,
		"CARD_NOTIFICATION_FIELD_MASK_CARD_ID":                    2,
		"CARD_NOTIFICATION_FIELD_MASK_RETRIEVAL_REFERENCE_NUMBER": 3,
		"CARD_NOTIFICATION_FIELD_MASK_REQUEST_ID":                 4,
		"CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_DETAILS":       5,
		"CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_TYPE":          6,
		"CARD_NOTIFICATION_FIELD_MASK_MERCHANT_DETAILS":           7,
		"CARD_NOTIFICATION_FIELD_MASK_AUTH_SWITCH_DETAILS":        8,
		"CARD_NOTIFICATION_FIELD_MASK_REMITTER_DETAILS":           9,
		"CARD_NOTIFICATION_FIELD_MASK_STATUS":                     10,
		"CARD_NOTIFICATION_FIELD_MASK_DETAILED_STATUS":            11,
		"CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_SOURCE":        12,
		"CARD_NOTIFICATION_FIELD_MASK_DEDUPE_ID":                  13,
		"CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_EVENT_TIME":    14,
		"CARD_NOTIFICATION_FIELD_MASK_CREATED_AT":                 15,
		"CARD_NOTIFICATION_FIELD_MASK_UPDATED_AT":                 16,
		"CARD_NOTIFICATION_FIELD_MASK_DELETED_AT":                 17,
		"CARD_NOTIFICATION_FIELD_MASK_EXTERNAL_REF_ID":            18,
	}
)

func (x CardNotificationFieldMask) Enum() *CardNotificationFieldMask {
	p := new(CardNotificationFieldMask)
	*p = x
	return p
}

func (x CardNotificationFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardNotificationFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_notification_proto_enumTypes[8].Descriptor()
}

func (CardNotificationFieldMask) Type() protoreflect.EnumType {
	return &file_api_card_enums_notification_proto_enumTypes[8]
}

func (x CardNotificationFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardNotificationFieldMask.Descriptor instead.
func (CardNotificationFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_notification_proto_rawDescGZIP(), []int{8}
}

var File_api_card_enums_notification_proto protoreflect.FileDescriptor

var file_api_card_enums_notification_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a,
	0xf6, 0x01, 0x0a, 0x0b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1c, 0x0a, 0x18, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a,
	0x14, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x4d, 0x45, 0x53, 0x53, 0x41,
	0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45,
	0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x41, 0x44, 0x56, 0x49, 0x43, 0x45, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x4d,
	0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x45,
	0x52, 0x53, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x04, 0x12, 0x22,
	0x0a, 0x1e, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52,
	0x45, 0x56, 0x45, 0x52, 0x53, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45,
	0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x41, 0x4c, 0x10, 0x06, 0x12, 0x19, 0x0a,
	0x15, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4f,
	0x47, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x07, 0x2a, 0xdd, 0x01, 0x0a, 0x17, 0x43, 0x61, 0x72,
	0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x23, 0x0a, 0x1f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x45, 0x43, 0x4f,
	0x4d, 0x4d, 0x10, 0x01, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x41, 0x54, 0x4d, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x4c,
	0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x4e, 0x46, 0x43, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x04, 0x2a, 0xf3, 0x08, 0x0a, 0x10, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a,
	0x1d, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x31, 0x0a, 0x2d, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x42, 0x41, 0x4e,
	0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x43, 0x48, 0x41,
	0x4e, 0x47, 0x45, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53,
	0x45, 0x54, 0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x04, 0x12, 0x27, 0x0a, 0x23, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10,
	0x05, 0x12, 0x26, 0x0a, 0x22, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x21, 0x0a, 0x1d, 0x4e, 0x4f, 0x54,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54,
	0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x10, 0x07, 0x12, 0x35, 0x0a, 0x31,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x08, 0x12, 0x20, 0x0a, 0x1c, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41,
	0x57, 0x41, 0x4c, 0x10, 0x09, 0x12, 0x29, 0x0a, 0x25, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x49, 0x53, 0x41, 0x5f,
	0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x10, 0x0a,
	0x12, 0x28, 0x0a, 0x24, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0b, 0x12, 0x28, 0x0a, 0x24, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x50, 0x4f, 0x53, 0x5f, 0x45, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41,
	0x53, 0x45, 0x10, 0x0c, 0x12, 0x33, 0x0a, 0x2f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52,
	0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0d, 0x12, 0x38, 0x0a, 0x34, 0x4e, 0x4f, 0x54,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52,
	0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x0e, 0x12, 0x2a, 0x0a, 0x26, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x4f,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x10, 0x0f, 0x12,
	0x39, 0x0a, 0x35, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x45, 0x52, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x10, 0x10, 0x12, 0x3a, 0x0a, 0x36, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x45, 0x44,
	0x45, 0x52, 0x41, 0x4c, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x10, 0x11, 0x12, 0x39, 0x0a, 0x35, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x54, 0x4f, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10,
	0x12, 0x12, 0x43, 0x0a, 0x3f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x42, 0x41, 0x4e, 0x4b,
	0x5f, 0x41, 0x54, 0x4d, 0x10, 0x13, 0x12, 0x24, 0x0a, 0x20, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x49, 0x4e, 0x49,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x14, 0x12, 0x25, 0x0a, 0x21,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49, 0x52,
	0x59, 0x10, 0x15, 0x12, 0x24, 0x0a, 0x20, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x16, 0x12, 0x25, 0x0a, 0x21, 0x4e, 0x4f, 0x54,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x17,
	0x12, 0x2c, 0x0a, 0x28, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49, 0x52, 0x59, 0x10, 0x18, 0x2a, 0x9d,
	0x09, 0x0a, 0x13, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52,
	0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x32, 0x0a, 0x2e,
	0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57,
	0x49, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x45, 0x4c, 0x45, 0x50, 0x48, 0x4f, 0x4e, 0x49, 0x43, 0x5f,
	0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x01,
	0x12, 0x30, 0x0a, 0x2c, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4f, 0x52,
	0x5f, 0x54, 0x45, 0x4c, 0x45, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52,
	0x10, 0x02, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52,
	0x49, 0x54, 0x59, 0x5f, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x10, 0x03, 0x12, 0x33, 0x0a, 0x2f, 0x41,
	0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49,
	0x54, 0x43, 0x48, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x45,
	0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x04,
	0x12, 0x28, 0x0a, 0x24, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x55, 0x53, 0x50, 0x45, 0x43, 0x54,
	0x45, 0x44, 0x5f, 0x46, 0x52, 0x41, 0x55, 0x44, 0x10, 0x05, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x55,
	0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54,
	0x43, 0x48, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x53, 0x10, 0x06, 0x12, 0x2e, 0x0a, 0x2a, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49,
	0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x45,
	0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x46, 0x5f, 0x49,
	0x54, 0x45, 0x4d, 0x10, 0x07, 0x12, 0x30, 0x0a, 0x2c, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49,
	0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x50, 0x55,
	0x42, 0x4c, 0x49, 0x43, 0x5f, 0x55, 0x54, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x45, 0x52,
	0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x10, 0x08, 0x12, 0x2a, 0x0a, 0x26, 0x41, 0x55, 0x54, 0x48, 0x4f,
	0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41,
	0x4c, 0x10, 0x09, 0x12, 0x30, 0x0a, 0x2c, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x41, 0x44, 0x4d, 0x49,
	0x4e, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49,
	0x4e, 0x41, 0x4c, 0x10, 0x0a, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49,
	0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x45,
	0x54, 0x55, 0x52, 0x4e, 0x45, 0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x10, 0x0b, 0x12, 0x28, 0x0a,
	0x24, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56,
	0x45, 0x52, 0x53, 0x41, 0x4c, 0x10, 0x0c, 0x12, 0x32, 0x0a, 0x2e, 0x41, 0x55, 0x54, 0x48, 0x4f,
	0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f,
	0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x4f,
	0x52, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x45, 0x44, 0x10, 0x0d, 0x12, 0x36, 0x0a, 0x32, 0x41,
	0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49,
	0x54, 0x43, 0x48, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x52, 0x52,
	0x4f, 0x52, 0x5f, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x45,
	0x44, 0x10, 0x0e, 0x12, 0x43, 0x0a, 0x3f, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x44, 0x45, 0x50, 0x4f,
	0x53, 0x49, 0x54, 0x5f, 0x4f, 0x55, 0x54, 0x5f, 0x4f, 0x46, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x44, 0x5f, 0x43, 0x4f,
	0x4e, 0x54, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x0f, 0x12, 0x43, 0x0a, 0x3f, 0x41, 0x55, 0x54, 0x48,
	0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48,
	0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x55, 0x54, 0x5f, 0x4f, 0x46, 0x5f,
	0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49,
	0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x10, 0x12, 0x36, 0x0a,
	0x32, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x4c,
	0x5f, 0x48, 0x41, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52,
	0x53, 0x45, 0x44, 0x10, 0x11, 0x12, 0x42, 0x0a, 0x3e, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49,
	0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x55, 0x4e,
	0x41, 0x54, 0x54, 0x45, 0x4e, 0x44, 0x45, 0x44, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41,
	0x4c, 0x5f, 0x55, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x4f, 0x5f, 0x52, 0x45, 0x54, 0x41,
	0x49, 0x4e, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x12, 0x12, 0x2d, 0x0a, 0x29, 0x41, 0x55, 0x54,
	0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43,
	0x48, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x52, 0x56, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x49,
	0x53, 0x4f, 0x5f, 0x55, 0x53, 0x45, 0x10, 0x13, 0x12, 0x32, 0x0a, 0x2e, 0x41, 0x55, 0x54, 0x48,
	0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48,
	0x5f, 0x52, 0x45, 0x53, 0x45, 0x52, 0x56, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4e, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x53, 0x45, 0x10, 0x14, 0x12, 0x2d, 0x0a, 0x29,
	0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57,
	0x49, 0x54, 0x43, 0x48, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x56, 0x45, 0x52,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x15, 0x12, 0x31, 0x0a, 0x2d, 0x41,
	0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x57, 0x49,
	0x54, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x52, 0x56, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52,
	0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x10, 0x16, 0x2a, 0xf3,
	0x03, 0x0a, 0x14, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x4d, 0x4f, 0x44,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x21, 0x0a, 0x1d, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45,
	0x4e, 0x54, 0x52, 0x59, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c,
	0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x4d, 0x41, 0x47,
	0x4e, 0x45, 0x54, 0x49, 0x43, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x10, 0x02, 0x12, 0x23,
	0x0a, 0x1f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4e,
	0x54, 0x52, 0x59, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x41, 0x52, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x4f, 0x43,
	0x52, 0x10, 0x04, 0x12, 0x32, 0x0a, 0x2e, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x47, 0x52, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x49, 0x52, 0x43, 0x55, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x05, 0x12, 0x22, 0x0a, 0x1e, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x4d, 0x4f, 0x44,
	0x45, 0x5f, 0x49, 0x53, 0x4f, 0x5f, 0x55, 0x53, 0x45, 0x10, 0x06, 0x12, 0x3e, 0x0a, 0x3a, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59,
	0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x41, 0x54, 0x45, 0x44,
	0x5f, 0x43, 0x49, 0x52, 0x43, 0x55, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4f,
	0x4e, 0x54, 0x41, 0x43, 0x54, 0x4c, 0x45, 0x53, 0x53, 0x10, 0x07, 0x12, 0x27, 0x0a, 0x23, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59,
	0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x55,
	0x53, 0x45, 0x10, 0x08, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x50,
	0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x10, 0x09, 0x12, 0x36, 0x0a, 0x32,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x54, 0x52,
	0x59, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x4d, 0x41, 0x47, 0x4e, 0x45, 0x54, 0x49, 0x43, 0x5f,
	0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x4c, 0x45,
	0x53, 0x53, 0x10, 0x0a, 0x2a, 0x73, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x2a, 0xa3, 0x19, 0x0a, 0x1a, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x28, 0x53, 0x57, 0x49, 0x54,
	0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x3e, 0x0a, 0x3a, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x55, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x4f,
	0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x2e, 0x0a, 0x2a, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54,
	0x5f, 0x50, 0x49, 0x4e, 0x10, 0x02, 0x12, 0x40, 0x0a, 0x3c, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x55, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x4f,
	0x5f, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x57, 0x49, 0x54,
	0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x45, 0x58,
	0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x04, 0x12, 0x3e, 0x0a, 0x3a, 0x53, 0x57, 0x49, 0x54, 0x43,
	0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x45, 0x43, 0x4f, 0x4d, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e,
	0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x45, 0x0a, 0x41, 0x53, 0x57, 0x49, 0x54, 0x43,
	0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x44, 0x41, 0x49, 0x4c, 0x59, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x41, 0x4d, 0x54, 0x5f, 0x4c,
	0x49, 0x4d, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x43, 0x48, 0x45, 0x44, 0x10, 0x06, 0x12, 0x32,
	0x0a, 0x2e, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x50,
	0x4f, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44,
	0x10, 0x07, 0x12, 0x44, 0x0a, 0x40, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e,
	0x53, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x54, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x43,
	0x45, 0x45, 0x44, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x08, 0x12, 0x36, 0x0a, 0x32, 0x53, 0x57, 0x49, 0x54,
	0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x54, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x09,
	0x12, 0x35, 0x0a, 0x31, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x43,
	0x4c, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x3f, 0x0a, 0x3b, 0x53, 0x57, 0x49, 0x54, 0x43,
	0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x55, 0x50,
	0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x10, 0x0b, 0x12, 0x34, 0x0a, 0x30, 0x53, 0x57, 0x49, 0x54,
	0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0c, 0x12, 0x47,
	0x0a, 0x43, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e,
	0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x0d, 0x12, 0x43, 0x0a, 0x3f, 0x53, 0x57, 0x49, 0x54, 0x43,
	0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x4c,
	0x45, 0x53, 0x53, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x55, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x0e, 0x12, 0x43, 0x0a, 0x3f,
	0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x49, 0x4e, 0x53,
	0x55, 0x46, 0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x0f, 0x12, 0x3f, 0x0a, 0x3b, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53,
	0x45, 0x5f, 0x44, 0x41, 0x49, 0x4c, 0x59, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57,
	0x41, 0x4c, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x43, 0x48, 0x45, 0x44,
	0x10, 0x10, 0x12, 0x3a, 0x0a, 0x36, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e,
	0x53, 0x45, 0x5f, 0x4c, 0x4f, 0x57, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x46, 0x4f, 0x52,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x11, 0x12, 0x34,
	0x0a, 0x30, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x59, 0x5f, 0x44, 0x41,
	0x54, 0x45, 0x10, 0x12, 0x12, 0x30, 0x0a, 0x2c, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e,
	0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50,
	0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x4e, 0x46, 0x43, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x41,
	0x42, 0x4c, 0x45, 0x44, 0x10, 0x13, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x50, 0x52, 0x4d, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x49,
	0x4e, 0x45, 0x44, 0x10, 0x14, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x56, 0x56, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0x15, 0x12, 0x4a, 0x0a, 0x46, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53,
	0x45, 0x5f, 0x44, 0x41, 0x49, 0x4c, 0x59, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x4c,
	0x45, 0x53, 0x53, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x4c, 0x49, 0x4d,
	0x49, 0x54, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x16, 0x12, 0x3a, 0x0a,
	0x36, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x17, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x57, 0x49,
	0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x48, 0x4f, 0x53, 0x54, 0x5f, 0x44,
	0x4f, 0x57, 0x4e, 0x10, 0x18, 0x12, 0x42, 0x0a, 0x3e, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x44, 0x4f, 0x4d, 0x45, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x19, 0x12, 0x2c, 0x0a, 0x28, 0x53, 0x57, 0x49,
	0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x45, 0x4c, 0x41, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x1a, 0x12, 0x36, 0x0a, 0x32, 0x53, 0x57, 0x49, 0x54, 0x43,
	0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x50, 0x4f, 0x53, 0x5f, 0x55, 0x53, 0x41, 0x47,
	0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x1b, 0x12,
	0x36, 0x0a, 0x32, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f,
	0x41, 0x54, 0x4d, 0x5f, 0x55, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e,
	0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x1c, 0x12, 0x2e, 0x0a, 0x2a, 0x53, 0x57, 0x49, 0x54, 0x43,
	0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x41, 0x46, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x1d, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x57, 0x49, 0x54, 0x43,
	0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x54, 0x4d, 0x5f, 0x46, 0x4c, 0x41, 0x47,
	0x5f, 0x4f, 0x46, 0x46, 0x10, 0x1e, 0x12, 0x34, 0x0a, 0x30, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x4c, 0x4f, 0x53, 0x54, 0x5f, 0x4f, 0x52, 0x5f, 0x53,
	0x54, 0x4f, 0x4c, 0x45, 0x4e, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x1f, 0x12, 0x33, 0x0a, 0x2f,
	0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x49, 0x4e, 0x45,
	0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10,
	0x20, 0x12, 0x2f, 0x0a, 0x2b, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53,
	0x45, 0x5f, 0x53, 0x49, 0x5f, 0x48, 0x55, 0x42, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x49, 0x4e, 0x45,
	0x10, 0x21, 0x12, 0x33, 0x0a, 0x2f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e,
	0x53, 0x45, 0x5f, 0x55, 0x4e, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x5f,
	0x55, 0x53, 0x41, 0x47, 0x45, 0x10, 0x22, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x57, 0x49, 0x54, 0x43,
	0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x10, 0x23, 0x12, 0x36, 0x0a, 0x32, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x56,
	0x45, 0x52, 0x5f, 0x44, 0x41, 0x49, 0x4c, 0x59, 0x5f, 0x4d, 0x41, 0x58, 0x10, 0x24, 0x12, 0x33,
	0x0a, 0x2f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x4d,
	0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0x25, 0x12, 0x3d, 0x0a, 0x39, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f,
	0x4e, 0x53, 0x45, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x5f,
	0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x4c, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54,
	0x10, 0x26, 0x12, 0x33, 0x0a, 0x2f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e,
	0x53, 0x45, 0x5f, 0x43, 0x41, 0x46, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45,
	0x43, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x27, 0x12, 0x31, 0x0a, 0x2d, 0x53, 0x57, 0x49, 0x54, 0x43,
	0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x46, 0x41, 0x4c, 0x4c, 0x42, 0x41, 0x43, 0x4b,
	0x5f, 0x44, 0x45, 0x43, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x28, 0x12, 0x35, 0x0a, 0x31, 0x53, 0x57,
	0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f,
	0x56, 0x45, 0x44, 0x5f, 0x4e, 0x4f, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x53, 0x10,
	0x29, 0x12, 0x33, 0x0a, 0x2f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53,
	0x45, 0x5f, 0x44, 0x45, 0x53, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c,
	0x41, 0x42, 0x4c, 0x45, 0x10, 0x2a, 0x12, 0x32, 0x0a, 0x2e, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x54, 0x43, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x2b, 0x12, 0x36, 0x0a, 0x32, 0x53, 0x57,
	0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e,
	0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x4f, 0x46, 0x46,
	0x10, 0x2c, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e,
	0x53, 0x45, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0x2d, 0x12, 0x27, 0x0a, 0x23, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53,
	0x45, 0x5f, 0x4e, 0x4f, 0x5f, 0x49, 0x44, 0x46, 0x10, 0x2e, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x57,
	0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x52, 0x51, 0x43, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x2f, 0x12, 0x33, 0x0a, 0x2f, 0x53, 0x57, 0x49,
	0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f,
	0x43, 0x4f, 0x46, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x30, 0x12, 0x31,
	0x0a, 0x2d, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10,
	0x31, 0x12, 0x3b, 0x0a, 0x37, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53,
	0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x42, 0x45, 0x5f, 0x43, 0x41, 0x50,
	0x54, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x5f, 0x43, 0x41, 0x46, 0x10, 0x32, 0x12, 0x30,
	0x0a, 0x2c, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x42,
	0x41, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x33,
	0x12, 0x32, 0x0a, 0x2e, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45,
	0x5f, 0x52, 0x45, 0x53, 0x45, 0x52, 0x56, 0x45, 0x44, 0x5f, 0x42, 0x32, 0x34, 0x5f, 0x43, 0x4f,
	0x44, 0x45, 0x10, 0x34, 0x12, 0x30, 0x0a, 0x2c, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e,
	0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50,
	0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x48, 0x53, 0x4d, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x35, 0x12, 0x36, 0x0a, 0x32, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x50, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x36, 0x12, 0x35,
	0x0a, 0x31, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x55,
	0x53, 0x41, 0x47, 0x45, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45,
	0x44, 0x45, 0x44, 0x10, 0x37, 0x12, 0x33, 0x0a, 0x2f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x4e, 0x46, 0x43, 0x5f,
	0x46, 0x4c, 0x41, 0x47, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x38, 0x12, 0x33, 0x0a, 0x2f, 0x53, 0x57,
	0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x48, 0x4f, 0x53, 0x54, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x39, 0x2a,
	0x59, 0x0a, 0x12, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43,
	0x45, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x10, 0x01, 0x2a, 0xb9, 0x07, 0x0a, 0x19, 0x43,
	0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e,
	0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x3b, 0x0a, 0x37, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x56, 0x41, 0x4c, 0x5f,
	0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52,
	0x10, 0x03, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12,
	0x35, 0x0a, 0x31, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x10, 0x05, 0x12, 0x32, 0x0a, 0x2e, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e,
	0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x06, 0x12, 0x31, 0x0a, 0x2d, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48,
	0x41, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x07, 0x12, 0x34, 0x0a,
	0x30, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x10, 0x08, 0x12, 0x31, 0x0a, 0x2d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x10, 0x09, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e,
	0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x0a, 0x12,
	0x30, 0x0a, 0x2c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10,
	0x0b, 0x12, 0x34, 0x0a, 0x30, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x10, 0x0c, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x49,
	0x44, 0x10, 0x0d, 0x12, 0x38, 0x0a, 0x34, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x0e, 0x12, 0x2b, 0x0a,
	0x27, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0f, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x10, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f,
	0x41, 0x54, 0x10, 0x11, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x52, 0x45,
	0x46, 0x5f, 0x49, 0x44, 0x10, 0x12, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x5a,
	0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_enums_notification_proto_rawDescOnce sync.Once
	file_api_card_enums_notification_proto_rawDescData = file_api_card_enums_notification_proto_rawDesc
)

func file_api_card_enums_notification_proto_rawDescGZIP() []byte {
	file_api_card_enums_notification_proto_rawDescOnce.Do(func() {
		file_api_card_enums_notification_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_enums_notification_proto_rawDescData)
	})
	return file_api_card_enums_notification_proto_rawDescData
}

var file_api_card_enums_notification_proto_enumTypes = make([]protoimpl.EnumInfo, 9)
var file_api_card_enums_notification_proto_goTypes = []interface{}{
	(MessageType)(0),                // 0: card.enums.MessageType
	(CardTransactionCategory)(0),    // 1: card.enums.CardTransactionCategory
	(NotificationType)(0),           // 2: card.enums.NotificationType
	(AuthorizationSwitch)(0),        // 3: card.enums.AuthorizationSwitch
	(TransactionEntryMode)(0),       // 4: card.enums.TransactionEntryMode
	(TransactionState)(0),           // 5: card.enums.TransactionState
	(SwitchNotificationResponse)(0), // 6: card.enums.SwitchNotificationResponse
	(NotificationSource)(0),         // 7: card.enums.NotificationSource
	(CardNotificationFieldMask)(0),  // 8: card.enums.CardNotificationFieldMask
}
var file_api_card_enums_notification_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_card_enums_notification_proto_init() }
func file_api_card_enums_notification_proto_init() {
	if File_api_card_enums_notification_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_enums_notification_proto_rawDesc,
			NumEnums:      9,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_enums_notification_proto_goTypes,
		DependencyIndexes: file_api_card_enums_notification_proto_depIdxs,
		EnumInfos:         file_api_card_enums_notification_proto_enumTypes,
	}.Build()
	File_api_card_enums_notification_proto = out.File
	file_api_card_enums_notification_proto_rawDesc = nil
	file_api_card_enums_notification_proto_goTypes = nil
	file_api_card_enums_notification_proto_depIdxs = nil
}
