//go:generate gen_sql -types=DebitCardMandateRegistrationProvenance

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/enums/debit_card_mandate.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DebitCardMandateRegistrationProvenance int32

const (
	DebitCardMandateRegistrationProvenance_DEBIT_CARD_MANDATE_REGISTRATION_PROVENANCE_UNSPECIFIED DebitCardMandateRegistrationProvenance = 0
	DebitCardMandateRegistrationProvenance_DEBIT_CARD_MANDATE_REGISTRATION_PROVENANCE_FI_APP      DebitCardMandateRegistrationProvenance = 1
	DebitCardMandateRegistrationProvenance_DEBIT_CARD_MANDATE_REGISTRATION_PROVENANCE_EXTERNAL    DebitCardMandateRegistrationProvenance = 2
)

// Enum value maps for DebitCardMandateRegistrationProvenance.
var (
	DebitCardMandateRegistrationProvenance_name = map[int32]string{
		0: "DEBIT_CARD_MANDATE_REGISTRATION_PROVENANCE_UNSPECIFIED",
		1: "DEBIT_CARD_MANDATE_REGISTRATION_PROVENANCE_FI_APP",
		2: "DEBIT_CARD_MANDATE_REGISTRATION_PROVENANCE_EXTERNAL",
	}
	DebitCardMandateRegistrationProvenance_value = map[string]int32{
		"DEBIT_CARD_MANDATE_REGISTRATION_PROVENANCE_UNSPECIFIED": 0,
		"DEBIT_CARD_MANDATE_REGISTRATION_PROVENANCE_FI_APP":      1,
		"DEBIT_CARD_MANDATE_REGISTRATION_PROVENANCE_EXTERNAL":    2,
	}
)

func (x DebitCardMandateRegistrationProvenance) Enum() *DebitCardMandateRegistrationProvenance {
	p := new(DebitCardMandateRegistrationProvenance)
	*p = x
	return p
}

func (x DebitCardMandateRegistrationProvenance) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DebitCardMandateRegistrationProvenance) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_enums_debit_card_mandate_proto_enumTypes[0].Descriptor()
}

func (DebitCardMandateRegistrationProvenance) Type() protoreflect.EnumType {
	return &file_api_card_enums_debit_card_mandate_proto_enumTypes[0]
}

func (x DebitCardMandateRegistrationProvenance) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DebitCardMandateRegistrationProvenance.Descriptor instead.
func (DebitCardMandateRegistrationProvenance) EnumDescriptor() ([]byte, []int) {
	return file_api_card_enums_debit_card_mandate_proto_rawDescGZIP(), []int{0}
}

var File_api_card_enums_debit_card_mandate_proto protoreflect.FileDescriptor

var file_api_card_enums_debit_card_mandate_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a, 0xd4, 0x01, 0x0a, 0x26, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x3a, 0x0a, 0x36, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4d,
	0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x35, 0x0a, 0x31,
	0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x46, 0x49, 0x5f, 0x41, 0x50,
	0x50, 0x10, 0x01, 0x12, 0x37, 0x0a, 0x33, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43,
	0x45, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x02, 0x42, 0x4e, 0x0a, 0x25,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_enums_debit_card_mandate_proto_rawDescOnce sync.Once
	file_api_card_enums_debit_card_mandate_proto_rawDescData = file_api_card_enums_debit_card_mandate_proto_rawDesc
)

func file_api_card_enums_debit_card_mandate_proto_rawDescGZIP() []byte {
	file_api_card_enums_debit_card_mandate_proto_rawDescOnce.Do(func() {
		file_api_card_enums_debit_card_mandate_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_enums_debit_card_mandate_proto_rawDescData)
	})
	return file_api_card_enums_debit_card_mandate_proto_rawDescData
}

var file_api_card_enums_debit_card_mandate_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_card_enums_debit_card_mandate_proto_goTypes = []interface{}{
	(DebitCardMandateRegistrationProvenance)(0), // 0: card.enums.DebitCardMandateRegistrationProvenance
}
var file_api_card_enums_debit_card_mandate_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_card_enums_debit_card_mandate_proto_init() }
func file_api_card_enums_debit_card_mandate_proto_init() {
	if File_api_card_enums_debit_card_mandate_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_enums_debit_card_mandate_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_enums_debit_card_mandate_proto_goTypes,
		DependencyIndexes: file_api_card_enums_debit_card_mandate_proto_depIdxs,
		EnumInfos:         file_api_card_enums_debit_card_mandate_proto_enumTypes,
	}.Build()
	File_api_card_enums_debit_card_mandate_proto = out.File
	file_api_card_enums_debit_card_mandate_proto_rawDesc = nil
	file_api_card_enums_debit_card_mandate_proto_goTypes = nil
	file_api_card_enums_debit_card_mandate_proto_depIdxs = nil
}
