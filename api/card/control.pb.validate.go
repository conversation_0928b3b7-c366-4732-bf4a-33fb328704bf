// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/control.proto

package card

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on ControlData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ControlData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ControlData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ControlDataMultiError, or
// nil if none found.
func (m *ControlData) ValidateAll() error {
	return m.validate(true)
}

func (m *ControlData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDefs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ControlDataValidationError{
						field:  fmt.Sprintf("Defs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ControlDataValidationError{
						field:  fmt.Sprintf("Defs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ControlDataValidationError{
					field:  fmt.Sprintf("Defs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TxnStates

	// no validation rules for LocStates

	// no validation rules for OverallState

	if len(errors) > 0 {
		return ControlDataMultiError(errors)
	}

	return nil
}

// ControlDataMultiError is an error wrapping multiple validation errors
// returned by ControlData.ValidateAll() if the designated constraints aren't met.
type ControlDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ControlDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ControlDataMultiError) AllErrors() []error { return m }

// ControlDataValidationError is the validation error returned by
// ControlData.Validate if the designated constraints aren't met.
type ControlDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ControlDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ControlDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ControlDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ControlDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ControlDataValidationError) ErrorName() string { return "ControlDataValidationError" }

// Error satisfies the builtin error interface
func (e ControlDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sControlData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ControlDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ControlDataValidationError{}

// Validate checks the field values on ControlDefinition with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ControlDefinition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ControlDefinition with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ControlDefinitionMultiError, or nil if none found.
func (m *ControlDefinition) ValidateAll() error {
	return m.validate(true)
}

func (m *ControlDefinition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransactionType

	// no validation rules for LocationType

	// no validation rules for Action

	// no validation rules for Vendor

	if len(errors) > 0 {
		return ControlDefinitionMultiError(errors)
	}

	return nil
}

// ControlDefinitionMultiError is an error wrapping multiple validation errors
// returned by ControlDefinition.ValidateAll() if the designated constraints
// aren't met.
type ControlDefinitionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ControlDefinitionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ControlDefinitionMultiError) AllErrors() []error { return m }

// ControlDefinitionValidationError is the validation error returned by
// ControlDefinition.Validate if the designated constraints aren't met.
type ControlDefinitionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ControlDefinitionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ControlDefinitionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ControlDefinitionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ControlDefinitionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ControlDefinitionValidationError) ErrorName() string {
	return "ControlDefinitionValidationError"
}

// Error satisfies the builtin error interface
func (e ControlDefinitionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sControlDefinition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ControlDefinitionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ControlDefinitionValidationError{}
