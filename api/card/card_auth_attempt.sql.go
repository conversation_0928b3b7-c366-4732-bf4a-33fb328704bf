// nolint:dupl
package card

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x AuthState) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *AuthState) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := AuthState_value[val]
	*x = AuthState(valInt)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x CardAction) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *CardAction) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := CardAction_value[val]
	*x = CardAction(valInt)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x AuthType) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *AuthType) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := AuthType_value[val]
	*x = AuthType(valInt)
	return nil
}

func (s *ActionStageDetails) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, s)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}

func (s *ActionStageDetails) Value() (driver.Value, error) {
	if s == nil {
		return nil, nil
	}
	return protojson.Marshal(s)
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x ActionState) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *ActionState) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := ActionState_value[val]
	*x = ActionState(valInt)
	return nil
}
