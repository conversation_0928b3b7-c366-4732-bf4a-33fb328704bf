// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/card_sku.proto

package card

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CardSKU with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CardSKU) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardSKU with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CardSKUMultiError, or nil if none found.
func (m *CardSKU) ValidateAll() error {
	return m.validate(true)
}

func (m *CardSKU) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardSkuType

	// no validation rules for FreeCardReplacements

	if all {
		switch v := interface{}(m.GetVendorCardSku()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardSKUValidationError{
					field:  "VendorCardSku",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardSKUValidationError{
					field:  "VendorCardSku",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorCardSku()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardSKUValidationError{
				field:  "VendorCardSku",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardSKUMultiError(errors)
	}

	return nil
}

// CardSKUMultiError is an error wrapping multiple validation errors returned
// by CardSKU.ValidateAll() if the designated constraints aren't met.
type CardSKUMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardSKUMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardSKUMultiError) AllErrors() []error { return m }

// CardSKUValidationError is the validation error returned by CardSKU.Validate
// if the designated constraints aren't met.
type CardSKUValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardSKUValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardSKUValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardSKUValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardSKUValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardSKUValidationError) ErrorName() string { return "CardSKUValidationError" }

// Error satisfies the builtin error interface
func (e CardSKUValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardSKU.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardSKUValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardSKUValidationError{}

// Validate checks the field values on VendorCardSKU with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VendorCardSKU) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VendorCardSKU with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VendorCardSKUMultiError, or
// nil if none found.
func (m *VendorCardSKU) ValidateAll() error {
	return m.validate(true)
}

func (m *VendorCardSKU) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFederal()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VendorCardSKUValidationError{
					field:  "Federal",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VendorCardSKUValidationError{
					field:  "Federal",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFederal()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VendorCardSKUValidationError{
				field:  "Federal",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VendorCardSKUMultiError(errors)
	}

	return nil
}

// VendorCardSKUMultiError is an error wrapping multiple validation errors
// returned by VendorCardSKU.ValidateAll() if the designated constraints
// aren't met.
type VendorCardSKUMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VendorCardSKUMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VendorCardSKUMultiError) AllErrors() []error { return m }

// VendorCardSKUValidationError is the validation error returned by
// VendorCardSKU.Validate if the designated constraints aren't met.
type VendorCardSKUValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VendorCardSKUValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VendorCardSKUValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VendorCardSKUValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VendorCardSKUValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VendorCardSKUValidationError) ErrorName() string { return "VendorCardSKUValidationError" }

// Error satisfies the builtin error interface
func (e VendorCardSKUValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVendorCardSKU.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VendorCardSKUValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VendorCardSKUValidationError{}

// Validate checks the field values on FederalCardSKU with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FederalCardSKU) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FederalCardSKU with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FederalCardSKUMultiError,
// or nil if none found.
func (m *FederalCardSKU) ValidateAll() error {
	return m.validate(true)
}

func (m *FederalCardSKU) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FreeReplacementCardSubType

	// no validation rules for ChargeableCardSubType

	if len(errors) > 0 {
		return FederalCardSKUMultiError(errors)
	}

	return nil
}

// FederalCardSKUMultiError is an error wrapping multiple validation errors
// returned by FederalCardSKU.ValidateAll() if the designated constraints
// aren't met.
type FederalCardSKUMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FederalCardSKUMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FederalCardSKUMultiError) AllErrors() []error { return m }

// FederalCardSKUValidationError is the validation error returned by
// FederalCardSKU.Validate if the designated constraints aren't met.
type FederalCardSKUValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FederalCardSKUValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FederalCardSKUValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FederalCardSKUValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FederalCardSKUValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FederalCardSKUValidationError) ErrorName() string { return "FederalCardSKUValidationError" }

// Error satisfies the builtin error interface
func (e FederalCardSKUValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFederalCardSKU.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FederalCardSKUValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FederalCardSKUValidationError{}
