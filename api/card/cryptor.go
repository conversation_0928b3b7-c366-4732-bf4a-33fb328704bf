package card

import (
	"context"
	"crypto/sha256"
	"encoding/base64"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/crypto/aes"
)

type CardAesCbcCryptor struct {
	crypto.Cryptor
}

// Accepts a key, a hex encoded string and returns a new aes cbc encryption block
func NewCardAesCbcCryptor(hexEncodedKey string) (*CardAesCbcCryptor, error) {
	return &CardAesCbcCryptor{Cryptor: aes.NewAesCbcCryptor(hexEncodedKey)}, nil
}

// Encryption Logic for physical card activation qr code:
// 1. SHA-256 hash of data
// 2. Encrypt data using AES256-CBC
// 3. Base64 format of encrypted data
func (c *CardAesCbcCryptor) Encrypt(ctx context.Context, data []byte, iv string) ([]byte, error) {
	// if there is no data then no need to encrypt
	if len(data) == 0 {
		return nil, nil
	}
	sha256Hash := sha256.Sum256(data)
	cipherText, err := c.Cryptor.Encrypt(ctx, sha256Hash[:], iv)
	if err != nil {
		return nil, err
	}

	// base64 encoding of encrypted data
	base64EncodedData := base64.StdEncoding.EncodeToString(cipherText)

	return []byte(base64EncodedData), nil
}

func (c *CardAesCbcCryptor) Decrypt(ctx context.Context, data []byte, iv string) ([]byte, error) {
	// data is base 64 encoded
	data, err := base64.StdEncoding.DecodeString(string(data))
	if err != nil {
		return nil, errors.Wrap(err, "error decoding base64 encoded response")
	}

	plainText, err := c.Cryptor.Decrypt(ctx, data, iv)
	if err != nil {
		return nil, err
	}
	return plainText, nil
}
