// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/card/forex_txn_refunds.pb.go

package card

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing ForexChargesInfo while reading from DB
func (a *ForexChargesInfo) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return backport.SafeUnmarshal(unmarshalOptions.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the ForexChargesInfo in string format in DB
func (a *ForexChargesInfo) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for ForexChargesInfo
func (a *ForexChargesInfo) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for ForexChargesInfo
func (a *ForexChargesInfo) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
