// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/cx/service.proto

package cx

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on FailDebitCardCreationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FailDebitCardCreationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FailDebitCardCreationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FailDebitCardCreationRequestMultiError, or nil if none found.
func (m *FailDebitCardCreationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FailDebitCardCreationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	if len(errors) > 0 {
		return FailDebitCardCreationRequestMultiError(errors)
	}

	return nil
}

// FailDebitCardCreationRequestMultiError is an error wrapping multiple
// validation errors returned by FailDebitCardCreationRequest.ValidateAll() if
// the designated constraints aren't met.
type FailDebitCardCreationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FailDebitCardCreationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FailDebitCardCreationRequestMultiError) AllErrors() []error { return m }

// FailDebitCardCreationRequestValidationError is the validation error returned
// by FailDebitCardCreationRequest.Validate if the designated constraints
// aren't met.
type FailDebitCardCreationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FailDebitCardCreationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FailDebitCardCreationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FailDebitCardCreationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FailDebitCardCreationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FailDebitCardCreationRequestValidationError) ErrorName() string {
	return "FailDebitCardCreationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FailDebitCardCreationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFailDebitCardCreationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FailDebitCardCreationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FailDebitCardCreationRequestValidationError{}

// Validate checks the field values on FailDebitCardCreationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FailDebitCardCreationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FailDebitCardCreationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FailDebitCardCreationResponseMultiError, or nil if none found.
func (m *FailDebitCardCreationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FailDebitCardCreationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FailDebitCardCreationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FailDebitCardCreationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FailDebitCardCreationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FailDebitCardCreationResponseMultiError(errors)
	}

	return nil
}

// FailDebitCardCreationResponseMultiError is an error wrapping multiple
// validation errors returned by FailDebitCardCreationResponse.ValidateAll()
// if the designated constraints aren't met.
type FailDebitCardCreationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FailDebitCardCreationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FailDebitCardCreationResponseMultiError) AllErrors() []error { return m }

// FailDebitCardCreationResponseValidationError is the validation error
// returned by FailDebitCardCreationResponse.Validate if the designated
// constraints aren't met.
type FailDebitCardCreationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FailDebitCardCreationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FailDebitCardCreationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FailDebitCardCreationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FailDebitCardCreationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FailDebitCardCreationResponseValidationError) ErrorName() string {
	return "FailDebitCardCreationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FailDebitCardCreationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFailDebitCardCreationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FailDebitCardCreationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FailDebitCardCreationResponseValidationError{}

// Validate checks the field values on
// MarkCardDeliveryTrackingStateAsReceivedRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MarkCardDeliveryTrackingStateAsReceivedRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// MarkCardDeliveryTrackingStateAsReceivedRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// MarkCardDeliveryTrackingStateAsReceivedRequestMultiError, or nil if none found.
func (m *MarkCardDeliveryTrackingStateAsReceivedRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MarkCardDeliveryTrackingStateAsReceivedRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	if len(errors) > 0 {
		return MarkCardDeliveryTrackingStateAsReceivedRequestMultiError(errors)
	}

	return nil
}

// MarkCardDeliveryTrackingStateAsReceivedRequestMultiError is an error
// wrapping multiple validation errors returned by
// MarkCardDeliveryTrackingStateAsReceivedRequest.ValidateAll() if the
// designated constraints aren't met.
type MarkCardDeliveryTrackingStateAsReceivedRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MarkCardDeliveryTrackingStateAsReceivedRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MarkCardDeliveryTrackingStateAsReceivedRequestMultiError) AllErrors() []error { return m }

// MarkCardDeliveryTrackingStateAsReceivedRequestValidationError is the
// validation error returned by
// MarkCardDeliveryTrackingStateAsReceivedRequest.Validate if the designated
// constraints aren't met.
type MarkCardDeliveryTrackingStateAsReceivedRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MarkCardDeliveryTrackingStateAsReceivedRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MarkCardDeliveryTrackingStateAsReceivedRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e MarkCardDeliveryTrackingStateAsReceivedRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MarkCardDeliveryTrackingStateAsReceivedRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MarkCardDeliveryTrackingStateAsReceivedRequestValidationError) ErrorName() string {
	return "MarkCardDeliveryTrackingStateAsReceivedRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MarkCardDeliveryTrackingStateAsReceivedRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMarkCardDeliveryTrackingStateAsReceivedRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MarkCardDeliveryTrackingStateAsReceivedRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MarkCardDeliveryTrackingStateAsReceivedRequestValidationError{}

// Validate checks the field values on
// MarkCardDeliveryTrackingStateAsReceivedResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MarkCardDeliveryTrackingStateAsReceivedResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// MarkCardDeliveryTrackingStateAsReceivedResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// MarkCardDeliveryTrackingStateAsReceivedResponseMultiError, or nil if none found.
func (m *MarkCardDeliveryTrackingStateAsReceivedResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MarkCardDeliveryTrackingStateAsReceivedResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarkCardDeliveryTrackingStateAsReceivedResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarkCardDeliveryTrackingStateAsReceivedResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarkCardDeliveryTrackingStateAsReceivedResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MarkCardDeliveryTrackingStateAsReceivedResponseMultiError(errors)
	}

	return nil
}

// MarkCardDeliveryTrackingStateAsReceivedResponseMultiError is an error
// wrapping multiple validation errors returned by
// MarkCardDeliveryTrackingStateAsReceivedResponse.ValidateAll() if the
// designated constraints aren't met.
type MarkCardDeliveryTrackingStateAsReceivedResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MarkCardDeliveryTrackingStateAsReceivedResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MarkCardDeliveryTrackingStateAsReceivedResponseMultiError) AllErrors() []error { return m }

// MarkCardDeliveryTrackingStateAsReceivedResponseValidationError is the
// validation error returned by
// MarkCardDeliveryTrackingStateAsReceivedResponse.Validate if the designated
// constraints aren't met.
type MarkCardDeliveryTrackingStateAsReceivedResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MarkCardDeliveryTrackingStateAsReceivedResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e MarkCardDeliveryTrackingStateAsReceivedResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e MarkCardDeliveryTrackingStateAsReceivedResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MarkCardDeliveryTrackingStateAsReceivedResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MarkCardDeliveryTrackingStateAsReceivedResponseValidationError) ErrorName() string {
	return "MarkCardDeliveryTrackingStateAsReceivedResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MarkCardDeliveryTrackingStateAsReceivedResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMarkCardDeliveryTrackingStateAsReceivedResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MarkCardDeliveryTrackingStateAsReceivedResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MarkCardDeliveryTrackingStateAsReceivedResponseValidationError{}

// Validate checks the field values on MapForexTransactionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MapForexTransactionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MapForexTransactionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MapForexTransactionRequestMultiError, or nil if none found.
func (m *MapForexTransactionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MapForexTransactionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ParentTxnId

	// no validation rules for ChildTxnId

	if len(errors) > 0 {
		return MapForexTransactionRequestMultiError(errors)
	}

	return nil
}

// MapForexTransactionRequestMultiError is an error wrapping multiple
// validation errors returned by MapForexTransactionRequest.ValidateAll() if
// the designated constraints aren't met.
type MapForexTransactionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MapForexTransactionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MapForexTransactionRequestMultiError) AllErrors() []error { return m }

// MapForexTransactionRequestValidationError is the validation error returned
// by MapForexTransactionRequest.Validate if the designated constraints aren't met.
type MapForexTransactionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MapForexTransactionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MapForexTransactionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MapForexTransactionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MapForexTransactionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MapForexTransactionRequestValidationError) ErrorName() string {
	return "MapForexTransactionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MapForexTransactionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMapForexTransactionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MapForexTransactionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MapForexTransactionRequestValidationError{}

// Validate checks the field values on MapForexTransactionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MapForexTransactionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MapForexTransactionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MapForexTransactionResponseMultiError, or nil if none found.
func (m *MapForexTransactionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MapForexTransactionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MapForexTransactionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MapForexTransactionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MapForexTransactionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MapForexTransactionResponseMultiError(errors)
	}

	return nil
}

// MapForexTransactionResponseMultiError is an error wrapping multiple
// validation errors returned by MapForexTransactionResponse.ValidateAll() if
// the designated constraints aren't met.
type MapForexTransactionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MapForexTransactionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MapForexTransactionResponseMultiError) AllErrors() []error { return m }

// MapForexTransactionResponseValidationError is the validation error returned
// by MapForexTransactionResponse.Validate if the designated constraints
// aren't met.
type MapForexTransactionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MapForexTransactionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MapForexTransactionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MapForexTransactionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MapForexTransactionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MapForexTransactionResponseValidationError) ErrorName() string {
	return "MapForexTransactionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MapForexTransactionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMapForexTransactionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MapForexTransactionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MapForexTransactionResponseValidationError{}
