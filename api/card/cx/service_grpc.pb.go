// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/card/cx/service.proto

package cx

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Cx_MarkCardDeliveryTrackingStateAsReceived_FullMethodName = "/card.cx.Cx/MarkCardDeliveryTrackingStateAsReceived"
	Cx_MapForexTransaction_FullMethodName                     = "/card.cx.Cx/MapForexTransaction"
	Cx_FailDebitCardCreation_FullMethodName                   = "/card.cx.Cx/FailDebitCardCreation"
)

// CxClient is the client API for Cx service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CxClient interface {
	// MarkCardDeliveryTrackingStateAsReceived is invoked by Cx dev-action
	// it takes card id and marks the card delivery tracking status to RECEIVED_BY_USER in DB. This will be mainly done to
	// activate cards of the users who have lost their box or not able to activate their cards because of any particular reason
	MarkCardDeliveryTrackingStateAsReceived(ctx context.Context, in *MarkCardDeliveryTrackingStateAsReceivedRequest, opts ...grpc.CallOption) (*MarkCardDeliveryTrackingStateAsReceivedResponse, error)
	// MapForexTransaction is invoked by cx dev-action to manually map a parent international txn with
	// its forex refund child txn.
	MapForexTransaction(ctx context.Context, in *MapForexTransactionRequest, opts ...grpc.CallOption) (*MapForexTransactionResponse, error)
	// FailDebitCardCreation fails the debit card creation request enabling user to re-try.
	// It takes cardId as input. It has handling for physical/digital form and renewal workflow cases.
	FailDebitCardCreation(ctx context.Context, in *FailDebitCardCreationRequest, opts ...grpc.CallOption) (*FailDebitCardCreationResponse, error)
}

type cxClient struct {
	cc grpc.ClientConnInterface
}

func NewCxClient(cc grpc.ClientConnInterface) CxClient {
	return &cxClient{cc}
}

func (c *cxClient) MarkCardDeliveryTrackingStateAsReceived(ctx context.Context, in *MarkCardDeliveryTrackingStateAsReceivedRequest, opts ...grpc.CallOption) (*MarkCardDeliveryTrackingStateAsReceivedResponse, error) {
	out := new(MarkCardDeliveryTrackingStateAsReceivedResponse)
	err := c.cc.Invoke(ctx, Cx_MarkCardDeliveryTrackingStateAsReceived_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cxClient) MapForexTransaction(ctx context.Context, in *MapForexTransactionRequest, opts ...grpc.CallOption) (*MapForexTransactionResponse, error) {
	out := new(MapForexTransactionResponse)
	err := c.cc.Invoke(ctx, Cx_MapForexTransaction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cxClient) FailDebitCardCreation(ctx context.Context, in *FailDebitCardCreationRequest, opts ...grpc.CallOption) (*FailDebitCardCreationResponse, error) {
	out := new(FailDebitCardCreationResponse)
	err := c.cc.Invoke(ctx, Cx_FailDebitCardCreation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CxServer is the server API for Cx service.
// All implementations should embed UnimplementedCxServer
// for forward compatibility
type CxServer interface {
	// MarkCardDeliveryTrackingStateAsReceived is invoked by Cx dev-action
	// it takes card id and marks the card delivery tracking status to RECEIVED_BY_USER in DB. This will be mainly done to
	// activate cards of the users who have lost their box or not able to activate their cards because of any particular reason
	MarkCardDeliveryTrackingStateAsReceived(context.Context, *MarkCardDeliveryTrackingStateAsReceivedRequest) (*MarkCardDeliveryTrackingStateAsReceivedResponse, error)
	// MapForexTransaction is invoked by cx dev-action to manually map a parent international txn with
	// its forex refund child txn.
	MapForexTransaction(context.Context, *MapForexTransactionRequest) (*MapForexTransactionResponse, error)
	// FailDebitCardCreation fails the debit card creation request enabling user to re-try.
	// It takes cardId as input. It has handling for physical/digital form and renewal workflow cases.
	FailDebitCardCreation(context.Context, *FailDebitCardCreationRequest) (*FailDebitCardCreationResponse, error)
}

// UnimplementedCxServer should be embedded to have forward compatible implementations.
type UnimplementedCxServer struct {
}

func (UnimplementedCxServer) MarkCardDeliveryTrackingStateAsReceived(context.Context, *MarkCardDeliveryTrackingStateAsReceivedRequest) (*MarkCardDeliveryTrackingStateAsReceivedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkCardDeliveryTrackingStateAsReceived not implemented")
}
func (UnimplementedCxServer) MapForexTransaction(context.Context, *MapForexTransactionRequest) (*MapForexTransactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MapForexTransaction not implemented")
}
func (UnimplementedCxServer) FailDebitCardCreation(context.Context, *FailDebitCardCreationRequest) (*FailDebitCardCreationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FailDebitCardCreation not implemented")
}

// UnsafeCxServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CxServer will
// result in compilation errors.
type UnsafeCxServer interface {
	mustEmbedUnimplementedCxServer()
}

func RegisterCxServer(s grpc.ServiceRegistrar, srv CxServer) {
	s.RegisterService(&Cx_ServiceDesc, srv)
}

func _Cx_MarkCardDeliveryTrackingStateAsReceived_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkCardDeliveryTrackingStateAsReceivedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxServer).MarkCardDeliveryTrackingStateAsReceived(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cx_MarkCardDeliveryTrackingStateAsReceived_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxServer).MarkCardDeliveryTrackingStateAsReceived(ctx, req.(*MarkCardDeliveryTrackingStateAsReceivedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cx_MapForexTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MapForexTransactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxServer).MapForexTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cx_MapForexTransaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxServer).MapForexTransaction(ctx, req.(*MapForexTransactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cx_FailDebitCardCreation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FailDebitCardCreationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxServer).FailDebitCardCreation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cx_FailDebitCardCreation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxServer).FailDebitCardCreation(ctx, req.(*FailDebitCardCreationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Cx_ServiceDesc is the grpc.ServiceDesc for Cx service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Cx_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "card.cx.Cx",
	HandlerType: (*CxServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MarkCardDeliveryTrackingStateAsReceived",
			Handler:    _Cx_MarkCardDeliveryTrackingStateAsReceived_Handler,
		},
		{
			MethodName: "MapForexTransaction",
			Handler:    _Cx_MapForexTransaction_Handler,
		},
		{
			MethodName: "FailDebitCardCreation",
			Handler:    _Cx_FailDebitCardCreation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/card/cx/service.proto",
}
