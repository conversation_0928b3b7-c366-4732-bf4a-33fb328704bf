// Code generated by MockGen. DO NOT EDIT.
// Source: api/card/cx/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	cx "github.com/epifi/gamma/api/card/cx"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCxClient is a mock of CxClient interface.
type MockCxClient struct {
	ctrl     *gomock.Controller
	recorder *MockCxClientMockRecorder
}

// MockCxClientMockRecorder is the mock recorder for MockCxClient.
type MockCxClientMockRecorder struct {
	mock *MockCxClient
}

// NewMockCxClient creates a new mock instance.
func NewMockCxClient(ctrl *gomock.Controller) *MockCxClient {
	mock := &MockCxClient{ctrl: ctrl}
	mock.recorder = &MockCxClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCxClient) EXPECT() *MockCxClientMockRecorder {
	return m.recorder
}

// FailDebitCardCreation mocks base method.
func (m *MockCxClient) FailDebitCardCreation(ctx context.Context, in *cx.FailDebitCardCreationRequest, opts ...grpc.CallOption) (*cx.FailDebitCardCreationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FailDebitCardCreation", varargs...)
	ret0, _ := ret[0].(*cx.FailDebitCardCreationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FailDebitCardCreation indicates an expected call of FailDebitCardCreation.
func (mr *MockCxClientMockRecorder) FailDebitCardCreation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FailDebitCardCreation", reflect.TypeOf((*MockCxClient)(nil).FailDebitCardCreation), varargs...)
}

// MapForexTransaction mocks base method.
func (m *MockCxClient) MapForexTransaction(ctx context.Context, in *cx.MapForexTransactionRequest, opts ...grpc.CallOption) (*cx.MapForexTransactionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MapForexTransaction", varargs...)
	ret0, _ := ret[0].(*cx.MapForexTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MapForexTransaction indicates an expected call of MapForexTransaction.
func (mr *MockCxClientMockRecorder) MapForexTransaction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MapForexTransaction", reflect.TypeOf((*MockCxClient)(nil).MapForexTransaction), varargs...)
}

// MarkCardDeliveryTrackingStateAsReceived mocks base method.
func (m *MockCxClient) MarkCardDeliveryTrackingStateAsReceived(ctx context.Context, in *cx.MarkCardDeliveryTrackingStateAsReceivedRequest, opts ...grpc.CallOption) (*cx.MarkCardDeliveryTrackingStateAsReceivedResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MarkCardDeliveryTrackingStateAsReceived", varargs...)
	ret0, _ := ret[0].(*cx.MarkCardDeliveryTrackingStateAsReceivedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MarkCardDeliveryTrackingStateAsReceived indicates an expected call of MarkCardDeliveryTrackingStateAsReceived.
func (mr *MockCxClientMockRecorder) MarkCardDeliveryTrackingStateAsReceived(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkCardDeliveryTrackingStateAsReceived", reflect.TypeOf((*MockCxClient)(nil).MarkCardDeliveryTrackingStateAsReceived), varargs...)
}

// MockCxServer is a mock of CxServer interface.
type MockCxServer struct {
	ctrl     *gomock.Controller
	recorder *MockCxServerMockRecorder
}

// MockCxServerMockRecorder is the mock recorder for MockCxServer.
type MockCxServerMockRecorder struct {
	mock *MockCxServer
}

// NewMockCxServer creates a new mock instance.
func NewMockCxServer(ctrl *gomock.Controller) *MockCxServer {
	mock := &MockCxServer{ctrl: ctrl}
	mock.recorder = &MockCxServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCxServer) EXPECT() *MockCxServerMockRecorder {
	return m.recorder
}

// FailDebitCardCreation mocks base method.
func (m *MockCxServer) FailDebitCardCreation(arg0 context.Context, arg1 *cx.FailDebitCardCreationRequest) (*cx.FailDebitCardCreationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FailDebitCardCreation", arg0, arg1)
	ret0, _ := ret[0].(*cx.FailDebitCardCreationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FailDebitCardCreation indicates an expected call of FailDebitCardCreation.
func (mr *MockCxServerMockRecorder) FailDebitCardCreation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FailDebitCardCreation", reflect.TypeOf((*MockCxServer)(nil).FailDebitCardCreation), arg0, arg1)
}

// MapForexTransaction mocks base method.
func (m *MockCxServer) MapForexTransaction(arg0 context.Context, arg1 *cx.MapForexTransactionRequest) (*cx.MapForexTransactionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MapForexTransaction", arg0, arg1)
	ret0, _ := ret[0].(*cx.MapForexTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MapForexTransaction indicates an expected call of MapForexTransaction.
func (mr *MockCxServerMockRecorder) MapForexTransaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MapForexTransaction", reflect.TypeOf((*MockCxServer)(nil).MapForexTransaction), arg0, arg1)
}

// MarkCardDeliveryTrackingStateAsReceived mocks base method.
func (m *MockCxServer) MarkCardDeliveryTrackingStateAsReceived(arg0 context.Context, arg1 *cx.MarkCardDeliveryTrackingStateAsReceivedRequest) (*cx.MarkCardDeliveryTrackingStateAsReceivedResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkCardDeliveryTrackingStateAsReceived", arg0, arg1)
	ret0, _ := ret[0].(*cx.MarkCardDeliveryTrackingStateAsReceivedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MarkCardDeliveryTrackingStateAsReceived indicates an expected call of MarkCardDeliveryTrackingStateAsReceived.
func (mr *MockCxServerMockRecorder) MarkCardDeliveryTrackingStateAsReceived(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkCardDeliveryTrackingStateAsReceived", reflect.TypeOf((*MockCxServer)(nil).MarkCardDeliveryTrackingStateAsReceived), arg0, arg1)
}

// MockUnsafeCxServer is a mock of UnsafeCxServer interface.
type MockUnsafeCxServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCxServerMockRecorder
}

// MockUnsafeCxServerMockRecorder is the mock recorder for MockUnsafeCxServer.
type MockUnsafeCxServerMockRecorder struct {
	mock *MockUnsafeCxServer
}

// NewMockUnsafeCxServer creates a new mock instance.
func NewMockUnsafeCxServer(ctrl *gomock.Controller) *MockUnsafeCxServer {
	mock := &MockUnsafeCxServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCxServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCxServer) EXPECT() *MockUnsafeCxServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCxServer mocks base method.
func (m *MockUnsafeCxServer) mustEmbedUnimplementedCxServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCxServer")
}

// mustEmbedUnimplementedCxServer indicates an expected call of mustEmbedUnimplementedCxServer.
func (mr *MockUnsafeCxServerMockRecorder) mustEmbedUnimplementedCxServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCxServer", reflect.TypeOf((*MockUnsafeCxServer)(nil).mustEmbedUnimplementedCxServer))
}
