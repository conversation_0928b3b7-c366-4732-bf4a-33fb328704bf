// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/cx/service.proto

package cx

import (
	rpc "github.com/epifi/be-common/api/rpc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FailDebitCardCreationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *FailDebitCardCreationRequest) Reset() {
	*x = FailDebitCardCreationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_cx_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FailDebitCardCreationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FailDebitCardCreationRequest) ProtoMessage() {}

func (x *FailDebitCardCreationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_cx_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FailDebitCardCreationRequest.ProtoReflect.Descriptor instead.
func (*FailDebitCardCreationRequest) Descriptor() ([]byte, []int) {
	return file_api_card_cx_service_proto_rawDescGZIP(), []int{0}
}

func (x *FailDebitCardCreationRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

type FailDebitCardCreationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *FailDebitCardCreationResponse) Reset() {
	*x = FailDebitCardCreationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_cx_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FailDebitCardCreationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FailDebitCardCreationResponse) ProtoMessage() {}

func (x *FailDebitCardCreationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_cx_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FailDebitCardCreationResponse.ProtoReflect.Descriptor instead.
func (*FailDebitCardCreationResponse) Descriptor() ([]byte, []int) {
	return file_api_card_cx_service_proto_rawDescGZIP(), []int{1}
}

func (x *FailDebitCardCreationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type MarkCardDeliveryTrackingStateAsReceivedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *MarkCardDeliveryTrackingStateAsReceivedRequest) Reset() {
	*x = MarkCardDeliveryTrackingStateAsReceivedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_cx_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkCardDeliveryTrackingStateAsReceivedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkCardDeliveryTrackingStateAsReceivedRequest) ProtoMessage() {}

func (x *MarkCardDeliveryTrackingStateAsReceivedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_cx_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkCardDeliveryTrackingStateAsReceivedRequest.ProtoReflect.Descriptor instead.
func (*MarkCardDeliveryTrackingStateAsReceivedRequest) Descriptor() ([]byte, []int) {
	return file_api_card_cx_service_proto_rawDescGZIP(), []int{2}
}

func (x *MarkCardDeliveryTrackingStateAsReceivedRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

type MarkCardDeliveryTrackingStateAsReceivedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *MarkCardDeliveryTrackingStateAsReceivedResponse) Reset() {
	*x = MarkCardDeliveryTrackingStateAsReceivedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_cx_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkCardDeliveryTrackingStateAsReceivedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkCardDeliveryTrackingStateAsReceivedResponse) ProtoMessage() {}

func (x *MarkCardDeliveryTrackingStateAsReceivedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_cx_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkCardDeliveryTrackingStateAsReceivedResponse.ProtoReflect.Descriptor instead.
func (*MarkCardDeliveryTrackingStateAsReceivedResponse) Descriptor() ([]byte, []int) {
	return file_api_card_cx_service_proto_rawDescGZIP(), []int{3}
}

func (x *MarkCardDeliveryTrackingStateAsReceivedResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type MapForexTransactionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParentTxnId string `protobuf:"bytes,1,opt,name=parent_txn_id,json=parentTxnId,proto3" json:"parent_txn_id,omitempty"`
	ChildTxnId  string `protobuf:"bytes,2,opt,name=child_txn_id,json=childTxnId,proto3" json:"child_txn_id,omitempty"`
}

func (x *MapForexTransactionRequest) Reset() {
	*x = MapForexTransactionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_cx_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MapForexTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapForexTransactionRequest) ProtoMessage() {}

func (x *MapForexTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_cx_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapForexTransactionRequest.ProtoReflect.Descriptor instead.
func (*MapForexTransactionRequest) Descriptor() ([]byte, []int) {
	return file_api_card_cx_service_proto_rawDescGZIP(), []int{4}
}

func (x *MapForexTransactionRequest) GetParentTxnId() string {
	if x != nil {
		return x.ParentTxnId
	}
	return ""
}

func (x *MapForexTransactionRequest) GetChildTxnId() string {
	if x != nil {
		return x.ChildTxnId
	}
	return ""
}

type MapForexTransactionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *MapForexTransactionResponse) Reset() {
	*x = MapForexTransactionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_cx_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MapForexTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapForexTransactionResponse) ProtoMessage() {}

func (x *MapForexTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_cx_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapForexTransactionResponse.ProtoReflect.Descriptor instead.
func (*MapForexTransactionResponse) Descriptor() ([]byte, []int) {
	return file_api_card_cx_service_proto_rawDescGZIP(), []int{5}
}

func (x *MapForexTransactionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_api_card_cx_service_proto protoreflect.FileDescriptor

var file_api_card_cx_service_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x78, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x63, 0x78, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x37, 0x0a, 0x1c, 0x46, 0x61,
	0x69, 0x6c, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x22, 0x44, 0x0a, 0x1d, 0x46, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x62, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x49, 0x0a, 0x2e, 0x4d, 0x61, 0x72,
	0x6b, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x41, 0x73, 0x52, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61,
	0x72, 0x64, 0x49, 0x64, 0x22, 0x56, 0x0a, 0x2f, 0x4d, 0x61, 0x72, 0x6b, 0x43, 0x61, 0x72, 0x64,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x41, 0x73, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x62, 0x0a, 0x1a,
	0x4d, 0x61, 0x70, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x20,
	0x0a, 0x0c, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x54, 0x78, 0x6e, 0x49, 0x64,
	0x22, 0x42, 0x0a, 0x1b, 0x4d, 0x61, 0x70, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x32, 0xed, 0x02, 0x0a, 0x02, 0x43, 0x78, 0x12, 0x9c, 0x01, 0x0a, 0x27,
	0x4d, 0x61, 0x72, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x41, 0x73, 0x52,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x12, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63,
	0x78, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x41,
	0x73, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x38, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x43,
	0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x41, 0x73, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x60, 0x0a, 0x13, 0x4d, 0x61,
	0x70, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x4d, 0x61, 0x70, 0x46,
	0x6f, 0x72, 0x65, 0x78, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x78,
	0x2e, 0x4d, 0x61, 0x70, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x15,
	0x46, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x78, 0x2e,
	0x46, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x62, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x78, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x78, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_cx_service_proto_rawDescOnce sync.Once
	file_api_card_cx_service_proto_rawDescData = file_api_card_cx_service_proto_rawDesc
)

func file_api_card_cx_service_proto_rawDescGZIP() []byte {
	file_api_card_cx_service_proto_rawDescOnce.Do(func() {
		file_api_card_cx_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_cx_service_proto_rawDescData)
	})
	return file_api_card_cx_service_proto_rawDescData
}

var file_api_card_cx_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_card_cx_service_proto_goTypes = []interface{}{
	(*FailDebitCardCreationRequest)(nil),                    // 0: card.cx.FailDebitCardCreationRequest
	(*FailDebitCardCreationResponse)(nil),                   // 1: card.cx.FailDebitCardCreationResponse
	(*MarkCardDeliveryTrackingStateAsReceivedRequest)(nil),  // 2: card.cx.MarkCardDeliveryTrackingStateAsReceivedRequest
	(*MarkCardDeliveryTrackingStateAsReceivedResponse)(nil), // 3: card.cx.MarkCardDeliveryTrackingStateAsReceivedResponse
	(*MapForexTransactionRequest)(nil),                      // 4: card.cx.MapForexTransactionRequest
	(*MapForexTransactionResponse)(nil),                     // 5: card.cx.MapForexTransactionResponse
	(*rpc.Status)(nil),                                      // 6: rpc.Status
}
var file_api_card_cx_service_proto_depIdxs = []int32{
	6, // 0: card.cx.FailDebitCardCreationResponse.status:type_name -> rpc.Status
	6, // 1: card.cx.MarkCardDeliveryTrackingStateAsReceivedResponse.status:type_name -> rpc.Status
	6, // 2: card.cx.MapForexTransactionResponse.status:type_name -> rpc.Status
	2, // 3: card.cx.Cx.MarkCardDeliveryTrackingStateAsReceived:input_type -> card.cx.MarkCardDeliveryTrackingStateAsReceivedRequest
	4, // 4: card.cx.Cx.MapForexTransaction:input_type -> card.cx.MapForexTransactionRequest
	0, // 5: card.cx.Cx.FailDebitCardCreation:input_type -> card.cx.FailDebitCardCreationRequest
	3, // 6: card.cx.Cx.MarkCardDeliveryTrackingStateAsReceived:output_type -> card.cx.MarkCardDeliveryTrackingStateAsReceivedResponse
	5, // 7: card.cx.Cx.MapForexTransaction:output_type -> card.cx.MapForexTransactionResponse
	1, // 8: card.cx.Cx.FailDebitCardCreation:output_type -> card.cx.FailDebitCardCreationResponse
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_card_cx_service_proto_init() }
func file_api_card_cx_service_proto_init() {
	if File_api_card_cx_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_cx_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FailDebitCardCreationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_cx_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FailDebitCardCreationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_cx_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkCardDeliveryTrackingStateAsReceivedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_cx_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkCardDeliveryTrackingStateAsReceivedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_cx_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MapForexTransactionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_cx_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MapForexTransactionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_cx_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_card_cx_service_proto_goTypes,
		DependencyIndexes: file_api_card_cx_service_proto_depIdxs,
		MessageInfos:      file_api_card_cx_service_proto_msgTypes,
	}.Build()
	File_api_card_cx_service_proto = out.File
	file_api_card_cx_service_proto_rawDesc = nil
	file_api_card_cx_service_proto_goTypes = nil
	file_api_card_cx_service_proto_depIdxs = nil
}
