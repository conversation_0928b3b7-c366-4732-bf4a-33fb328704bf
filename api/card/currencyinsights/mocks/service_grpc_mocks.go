// Code generated by MockGen. DO NOT EDIT.
// Source: api/card/currencyinsights/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	currencyinsights "github.com/epifi/gamma/api/card/currencyinsights"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCurrencyInsightsClient is a mock of CurrencyInsightsClient interface.
type MockCurrencyInsightsClient struct {
	ctrl     *gomock.Controller
	recorder *MockCurrencyInsightsClientMockRecorder
}

// MockCurrencyInsightsClientMockRecorder is the mock recorder for MockCurrencyInsightsClient.
type MockCurrencyInsightsClientMockRecorder struct {
	mock *MockCurrencyInsightsClient
}

// NewMockCurrencyInsightsClient creates a new mock instance.
func NewMockCurrencyInsightsClient(ctrl *gomock.Controller) *MockCurrencyInsightsClient {
	mock := &MockCurrencyInsightsClient{ctrl: ctrl}
	mock.recorder = &MockCurrencyInsightsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCurrencyInsightsClient) EXPECT() *MockCurrencyInsightsClientMockRecorder {
	return m.recorder
}

// ConvertMoneyToLocalCurrency mocks base method.
func (m *MockCurrencyInsightsClient) ConvertMoneyToLocalCurrency(ctx context.Context, in *currencyinsights.ConvertMoneyToLocalCurrencyRequest, opts ...grpc.CallOption) (*currencyinsights.ConvertMoneyToLocalCurrencyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConvertMoneyToLocalCurrency", varargs...)
	ret0, _ := ret[0].(*currencyinsights.ConvertMoneyToLocalCurrencyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConvertMoneyToLocalCurrency indicates an expected call of ConvertMoneyToLocalCurrency.
func (mr *MockCurrencyInsightsClientMockRecorder) ConvertMoneyToLocalCurrency(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConvertMoneyToLocalCurrency", reflect.TypeOf((*MockCurrencyInsightsClient)(nil).ConvertMoneyToLocalCurrency), varargs...)
}

// GetNearbyAtmLocations mocks base method.
func (m *MockCurrencyInsightsClient) GetNearbyAtmLocations(ctx context.Context, in *currencyinsights.GetNearbyAtmLocationsRequest, opts ...grpc.CallOption) (*currencyinsights.GetNearbyAtmLocationsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNearbyAtmLocations", varargs...)
	ret0, _ := ret[0].(*currencyinsights.GetNearbyAtmLocationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNearbyAtmLocations indicates an expected call of GetNearbyAtmLocations.
func (mr *MockCurrencyInsightsClientMockRecorder) GetNearbyAtmLocations(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNearbyAtmLocations", reflect.TypeOf((*MockCurrencyInsightsClient)(nil).GetNearbyAtmLocations), varargs...)
}

// MockCurrencyInsightsServer is a mock of CurrencyInsightsServer interface.
type MockCurrencyInsightsServer struct {
	ctrl     *gomock.Controller
	recorder *MockCurrencyInsightsServerMockRecorder
}

// MockCurrencyInsightsServerMockRecorder is the mock recorder for MockCurrencyInsightsServer.
type MockCurrencyInsightsServerMockRecorder struct {
	mock *MockCurrencyInsightsServer
}

// NewMockCurrencyInsightsServer creates a new mock instance.
func NewMockCurrencyInsightsServer(ctrl *gomock.Controller) *MockCurrencyInsightsServer {
	mock := &MockCurrencyInsightsServer{ctrl: ctrl}
	mock.recorder = &MockCurrencyInsightsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCurrencyInsightsServer) EXPECT() *MockCurrencyInsightsServerMockRecorder {
	return m.recorder
}

// ConvertMoneyToLocalCurrency mocks base method.
func (m *MockCurrencyInsightsServer) ConvertMoneyToLocalCurrency(arg0 context.Context, arg1 *currencyinsights.ConvertMoneyToLocalCurrencyRequest) (*currencyinsights.ConvertMoneyToLocalCurrencyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConvertMoneyToLocalCurrency", arg0, arg1)
	ret0, _ := ret[0].(*currencyinsights.ConvertMoneyToLocalCurrencyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConvertMoneyToLocalCurrency indicates an expected call of ConvertMoneyToLocalCurrency.
func (mr *MockCurrencyInsightsServerMockRecorder) ConvertMoneyToLocalCurrency(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConvertMoneyToLocalCurrency", reflect.TypeOf((*MockCurrencyInsightsServer)(nil).ConvertMoneyToLocalCurrency), arg0, arg1)
}

// GetNearbyAtmLocations mocks base method.
func (m *MockCurrencyInsightsServer) GetNearbyAtmLocations(arg0 context.Context, arg1 *currencyinsights.GetNearbyAtmLocationsRequest) (*currencyinsights.GetNearbyAtmLocationsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNearbyAtmLocations", arg0, arg1)
	ret0, _ := ret[0].(*currencyinsights.GetNearbyAtmLocationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNearbyAtmLocations indicates an expected call of GetNearbyAtmLocations.
func (mr *MockCurrencyInsightsServerMockRecorder) GetNearbyAtmLocations(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNearbyAtmLocations", reflect.TypeOf((*MockCurrencyInsightsServer)(nil).GetNearbyAtmLocations), arg0, arg1)
}

// MockUnsafeCurrencyInsightsServer is a mock of UnsafeCurrencyInsightsServer interface.
type MockUnsafeCurrencyInsightsServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCurrencyInsightsServerMockRecorder
}

// MockUnsafeCurrencyInsightsServerMockRecorder is the mock recorder for MockUnsafeCurrencyInsightsServer.
type MockUnsafeCurrencyInsightsServerMockRecorder struct {
	mock *MockUnsafeCurrencyInsightsServer
}

// NewMockUnsafeCurrencyInsightsServer creates a new mock instance.
func NewMockUnsafeCurrencyInsightsServer(ctrl *gomock.Controller) *MockUnsafeCurrencyInsightsServer {
	mock := &MockUnsafeCurrencyInsightsServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCurrencyInsightsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCurrencyInsightsServer) EXPECT() *MockUnsafeCurrencyInsightsServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCurrencyInsightsServer mocks base method.
func (m *MockUnsafeCurrencyInsightsServer) mustEmbedUnimplementedCurrencyInsightsServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCurrencyInsightsServer")
}

// mustEmbedUnimplementedCurrencyInsightsServer indicates an expected call of mustEmbedUnimplementedCurrencyInsightsServer.
func (mr *MockUnsafeCurrencyInsightsServerMockRecorder) mustEmbedUnimplementedCurrencyInsightsServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCurrencyInsightsServer", reflect.TypeOf((*MockUnsafeCurrencyInsightsServer)(nil).mustEmbedUnimplementedCurrencyInsightsServer))
}
