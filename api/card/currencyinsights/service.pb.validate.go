// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/currencyinsights/service.proto

package currencyinsights

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = typesv2.ATMProperty(0)
)

// Validate checks the field values on ConvertMoneyToLocalCurrencyRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ConvertMoneyToLocalCurrencyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConvertMoneyToLocalCurrencyRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ConvertMoneyToLocalCurrencyRequestMultiError, or nil if none found.
func (m *ConvertMoneyToLocalCurrencyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConvertMoneyToLocalCurrencyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetMoney()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConvertMoneyToLocalCurrencyRequestValidationError{
					field:  "Money",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConvertMoneyToLocalCurrencyRequestValidationError{
					field:  "Money",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMoney()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConvertMoneyToLocalCurrencyRequestValidationError{
				field:  "Money",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConvertMoneyToLocalCurrencyRequestMultiError(errors)
	}

	return nil
}

// ConvertMoneyToLocalCurrencyRequestMultiError is an error wrapping multiple
// validation errors returned by
// ConvertMoneyToLocalCurrencyRequest.ValidateAll() if the designated
// constraints aren't met.
type ConvertMoneyToLocalCurrencyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConvertMoneyToLocalCurrencyRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConvertMoneyToLocalCurrencyRequestMultiError) AllErrors() []error { return m }

// ConvertMoneyToLocalCurrencyRequestValidationError is the validation error
// returned by ConvertMoneyToLocalCurrencyRequest.Validate if the designated
// constraints aren't met.
type ConvertMoneyToLocalCurrencyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConvertMoneyToLocalCurrencyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConvertMoneyToLocalCurrencyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConvertMoneyToLocalCurrencyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConvertMoneyToLocalCurrencyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConvertMoneyToLocalCurrencyRequestValidationError) ErrorName() string {
	return "ConvertMoneyToLocalCurrencyRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConvertMoneyToLocalCurrencyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConvertMoneyToLocalCurrencyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConvertMoneyToLocalCurrencyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConvertMoneyToLocalCurrencyRequestValidationError{}

// Validate checks the field values on ConvertMoneyToLocalCurrencyResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ConvertMoneyToLocalCurrencyResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConvertMoneyToLocalCurrencyResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ConvertMoneyToLocalCurrencyResponseMultiError, or nil if none found.
func (m *ConvertMoneyToLocalCurrencyResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConvertMoneyToLocalCurrencyResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConvertMoneyToLocalCurrencyResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConvertMoneyToLocalCurrencyResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConvertMoneyToLocalCurrencyResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMoney()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConvertMoneyToLocalCurrencyResponseValidationError{
					field:  "Money",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConvertMoneyToLocalCurrencyResponseValidationError{
					field:  "Money",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMoney()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConvertMoneyToLocalCurrencyResponseValidationError{
				field:  "Money",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConvertMoneyToLocalCurrencyResponseMultiError(errors)
	}

	return nil
}

// ConvertMoneyToLocalCurrencyResponseMultiError is an error wrapping multiple
// validation errors returned by
// ConvertMoneyToLocalCurrencyResponse.ValidateAll() if the designated
// constraints aren't met.
type ConvertMoneyToLocalCurrencyResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConvertMoneyToLocalCurrencyResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConvertMoneyToLocalCurrencyResponseMultiError) AllErrors() []error { return m }

// ConvertMoneyToLocalCurrencyResponseValidationError is the validation error
// returned by ConvertMoneyToLocalCurrencyResponse.Validate if the designated
// constraints aren't met.
type ConvertMoneyToLocalCurrencyResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConvertMoneyToLocalCurrencyResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConvertMoneyToLocalCurrencyResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConvertMoneyToLocalCurrencyResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConvertMoneyToLocalCurrencyResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConvertMoneyToLocalCurrencyResponseValidationError) ErrorName() string {
	return "ConvertMoneyToLocalCurrencyResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConvertMoneyToLocalCurrencyResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConvertMoneyToLocalCurrencyResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConvertMoneyToLocalCurrencyResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConvertMoneyToLocalCurrencyResponseValidationError{}

// Validate checks the field values on GetNearbyAtmLocationsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNearbyAtmLocationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNearbyAtmLocationsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNearbyAtmLocationsRequestMultiError, or nil if none found.
func (m *GetNearbyAtmLocationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNearbyAtmLocationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LocationToken

	// no validation rules for QueryRadiusInKm

	if all {
		switch v := interface{}(m.GetFilterOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNearbyAtmLocationsRequestValidationError{
					field:  "FilterOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNearbyAtmLocationsRequestValidationError{
					field:  "FilterOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilterOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNearbyAtmLocationsRequestValidationError{
				field:  "FilterOptions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNearbyAtmLocationsRequestMultiError(errors)
	}

	return nil
}

// GetNearbyAtmLocationsRequestMultiError is an error wrapping multiple
// validation errors returned by GetNearbyAtmLocationsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetNearbyAtmLocationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNearbyAtmLocationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNearbyAtmLocationsRequestMultiError) AllErrors() []error { return m }

// GetNearbyAtmLocationsRequestValidationError is the validation error returned
// by GetNearbyAtmLocationsRequest.Validate if the designated constraints
// aren't met.
type GetNearbyAtmLocationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNearbyAtmLocationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNearbyAtmLocationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNearbyAtmLocationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNearbyAtmLocationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNearbyAtmLocationsRequestValidationError) ErrorName() string {
	return "GetNearbyAtmLocationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNearbyAtmLocationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNearbyAtmLocationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNearbyAtmLocationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNearbyAtmLocationsRequestValidationError{}

// Validate checks the field values on GetNearbyAtmLocationsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNearbyAtmLocationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNearbyAtmLocationsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetNearbyAtmLocationsResponseMultiError, or nil if none found.
func (m *GetNearbyAtmLocationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNearbyAtmLocationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNearbyAtmLocationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNearbyAtmLocationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNearbyAtmLocationsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFoundAtms() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNearbyAtmLocationsResponseValidationError{
						field:  fmt.Sprintf("FoundAtms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNearbyAtmLocationsResponseValidationError{
						field:  fmt.Sprintf("FoundAtms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNearbyAtmLocationsResponseValidationError{
					field:  fmt.Sprintf("FoundAtms[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetNearbyAtmLocationsResponseMultiError(errors)
	}

	return nil
}

// GetNearbyAtmLocationsResponseMultiError is an error wrapping multiple
// validation errors returned by GetNearbyAtmLocationsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetNearbyAtmLocationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNearbyAtmLocationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNearbyAtmLocationsResponseMultiError) AllErrors() []error { return m }

// GetNearbyAtmLocationsResponseValidationError is the validation error
// returned by GetNearbyAtmLocationsResponse.Validate if the designated
// constraints aren't met.
type GetNearbyAtmLocationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNearbyAtmLocationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNearbyAtmLocationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNearbyAtmLocationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNearbyAtmLocationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNearbyAtmLocationsResponseValidationError) ErrorName() string {
	return "GetNearbyAtmLocationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNearbyAtmLocationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNearbyAtmLocationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNearbyAtmLocationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNearbyAtmLocationsResponseValidationError{}

// Validate checks the field values on
// GetNearbyAtmLocationsRequest_FilterOptions with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetNearbyAtmLocationsRequest_FilterOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetNearbyAtmLocationsRequest_FilterOptions with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetNearbyAtmLocationsRequest_FilterOptionsMultiError, or nil if none found.
func (m *GetNearbyAtmLocationsRequest_FilterOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNearbyAtmLocationsRequest_FilterOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetNearbyAtmLocationsRequest_FilterOptionsMultiError(errors)
	}

	return nil
}

// GetNearbyAtmLocationsRequest_FilterOptionsMultiError is an error wrapping
// multiple validation errors returned by
// GetNearbyAtmLocationsRequest_FilterOptions.ValidateAll() if the designated
// constraints aren't met.
type GetNearbyAtmLocationsRequest_FilterOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNearbyAtmLocationsRequest_FilterOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNearbyAtmLocationsRequest_FilterOptionsMultiError) AllErrors() []error { return m }

// GetNearbyAtmLocationsRequest_FilterOptionsValidationError is the validation
// error returned by GetNearbyAtmLocationsRequest_FilterOptions.Validate if
// the designated constraints aren't met.
type GetNearbyAtmLocationsRequest_FilterOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNearbyAtmLocationsRequest_FilterOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNearbyAtmLocationsRequest_FilterOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNearbyAtmLocationsRequest_FilterOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNearbyAtmLocationsRequest_FilterOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNearbyAtmLocationsRequest_FilterOptionsValidationError) ErrorName() string {
	return "GetNearbyAtmLocationsRequest_FilterOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e GetNearbyAtmLocationsRequest_FilterOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNearbyAtmLocationsRequest_FilterOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNearbyAtmLocationsRequest_FilterOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNearbyAtmLocationsRequest_FilterOptionsValidationError{}
