// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/currencyinsights/service.proto

package currencyinsights

import (
	rpc "github.com/epifi/be-common/api/rpc"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConvertMoneyToLocalCurrencyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string       `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Money   *money.Money `protobuf:"bytes,2,opt,name=money,proto3" json:"money,omitempty"`
}

func (x *ConvertMoneyToLocalCurrencyRequest) Reset() {
	*x = ConvertMoneyToLocalCurrencyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_currencyinsights_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConvertMoneyToLocalCurrencyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertMoneyToLocalCurrencyRequest) ProtoMessage() {}

func (x *ConvertMoneyToLocalCurrencyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_currencyinsights_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertMoneyToLocalCurrencyRequest.ProtoReflect.Descriptor instead.
func (*ConvertMoneyToLocalCurrencyRequest) Descriptor() ([]byte, []int) {
	return file_api_card_currencyinsights_service_proto_rawDescGZIP(), []int{0}
}

func (x *ConvertMoneyToLocalCurrencyRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ConvertMoneyToLocalCurrencyRequest) GetMoney() *money.Money {
	if x != nil {
		return x.Money
	}
	return nil
}

type ConvertMoneyToLocalCurrencyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Money  *money.Money `protobuf:"bytes,2,opt,name=money,proto3" json:"money,omitempty"`
}

func (x *ConvertMoneyToLocalCurrencyResponse) Reset() {
	*x = ConvertMoneyToLocalCurrencyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_currencyinsights_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConvertMoneyToLocalCurrencyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertMoneyToLocalCurrencyResponse) ProtoMessage() {}

func (x *ConvertMoneyToLocalCurrencyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_currencyinsights_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertMoneyToLocalCurrencyResponse.ProtoReflect.Descriptor instead.
func (*ConvertMoneyToLocalCurrencyResponse) Descriptor() ([]byte, []int) {
	return file_api_card_currencyinsights_service_proto_rawDescGZIP(), []int{1}
}

func (x *ConvertMoneyToLocalCurrencyResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ConvertMoneyToLocalCurrencyResponse) GetMoney() *money.Money {
	if x != nil {
		return x.Money
	}
	return nil
}

type GetNearbyAtmLocationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location token for the user
	LocationToken string `protobuf:"bytes,1,opt,name=location_token,json=locationToken,proto3" json:"location_token,omitempty"`
	// radius of the area to be queried for the atms' search
	QueryRadiusInKm float64 `protobuf:"fixed64,2,opt,name=query_radius_in_km,json=queryRadiusInKm,proto3" json:"query_radius_in_km,omitempty"`
	// Optional: optional parameter to restrict the result based on certain props that atms may have
	FilterOptions *GetNearbyAtmLocationsRequest_FilterOptions `protobuf:"bytes,3,opt,name=filter_options,json=filterOptions,proto3" json:"filter_options,omitempty"`
}

func (x *GetNearbyAtmLocationsRequest) Reset() {
	*x = GetNearbyAtmLocationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_currencyinsights_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNearbyAtmLocationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNearbyAtmLocationsRequest) ProtoMessage() {}

func (x *GetNearbyAtmLocationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_currencyinsights_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNearbyAtmLocationsRequest.ProtoReflect.Descriptor instead.
func (*GetNearbyAtmLocationsRequest) Descriptor() ([]byte, []int) {
	return file_api_card_currencyinsights_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetNearbyAtmLocationsRequest) GetLocationToken() string {
	if x != nil {
		return x.LocationToken
	}
	return ""
}

func (x *GetNearbyAtmLocationsRequest) GetQueryRadiusInKm() float64 {
	if x != nil {
		return x.QueryRadiusInKm
	}
	return 0
}

func (x *GetNearbyAtmLocationsRequest) GetFilterOptions() *GetNearbyAtmLocationsRequest_FilterOptions {
	if x != nil {
		return x.FilterOptions
	}
	return nil
}

type GetNearbyAtmLocationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status    *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	FoundAtms []*typesv2.ATM `protobuf:"bytes,2,rep,name=found_atms,json=foundAtms,proto3" json:"found_atms,omitempty"`
}

func (x *GetNearbyAtmLocationsResponse) Reset() {
	*x = GetNearbyAtmLocationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_currencyinsights_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNearbyAtmLocationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNearbyAtmLocationsResponse) ProtoMessage() {}

func (x *GetNearbyAtmLocationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_currencyinsights_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNearbyAtmLocationsResponse.ProtoReflect.Descriptor instead.
func (*GetNearbyAtmLocationsResponse) Descriptor() ([]byte, []int) {
	return file_api_card_currencyinsights_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetNearbyAtmLocationsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetNearbyAtmLocationsResponse) GetFoundAtms() []*typesv2.ATM {
	if x != nil {
		return x.FoundAtms
	}
	return nil
}

type GetNearbyAtmLocationsRequest_FilterOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PropertyFilters []typesv2.ATMProperty `protobuf:"varint,1,rep,packed,name=property_filters,json=propertyFilters,proto3,enum=api.typesv2.ATMProperty" json:"property_filters,omitempty"`
}

func (x *GetNearbyAtmLocationsRequest_FilterOptions) Reset() {
	*x = GetNearbyAtmLocationsRequest_FilterOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_currencyinsights_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNearbyAtmLocationsRequest_FilterOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNearbyAtmLocationsRequest_FilterOptions) ProtoMessage() {}

func (x *GetNearbyAtmLocationsRequest_FilterOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_currencyinsights_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNearbyAtmLocationsRequest_FilterOptions.ProtoReflect.Descriptor instead.
func (*GetNearbyAtmLocationsRequest_FilterOptions) Descriptor() ([]byte, []int) {
	return file_api_card_currencyinsights_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *GetNearbyAtmLocationsRequest_FilterOptions) GetPropertyFilters() []typesv2.ATMProperty {
	if x != nil {
		return x.PropertyFilters
	}
	return nil
}

var File_api_card_currencyinsights_service_proto protoreflect.FileDescriptor

var file_api_card_currencyinsights_service_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x69, 0x0a, 0x22,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x54, 0x6f, 0x4c, 0x6f,
	0x63, 0x61, 0x6c, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a,
	0x05, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x22, 0x74, 0x0a, 0x23, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x74, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x54, 0x6f, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x22, 0xb2, 0x02,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x61, 0x72, 0x62, 0x79, 0x41, 0x74, 0x6d, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2b, 0x0a, 0x12, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x72,
	0x61, 0x64, 0x69, 0x75, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x6b, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x49, 0x6e,
	0x4b, 0x6d, 0x12, 0x68, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x61, 0x72, 0x62, 0x79, 0x41, 0x74, 0x6d, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0d, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x54, 0x0a, 0x0d,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x43, 0x0a,
	0x10, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x54, 0x4d, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x22, 0x75, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x61, 0x72, 0x62, 0x79, 0x41,
	0x74, 0x6d, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2f, 0x0a, 0x0a, 0x66, 0x6f, 0x75, 0x6e,
	0x64, 0x5f, 0x61, 0x74, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x54, 0x4d, 0x52, 0x09,
	0x66, 0x6f, 0x75, 0x6e, 0x64, 0x41, 0x74, 0x6d, 0x73, 0x32, 0xae, 0x02, 0x0a, 0x10, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x82,
	0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x61, 0x72, 0x62, 0x79, 0x41, 0x74, 0x6d, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x61, 0x72, 0x62, 0x79, 0x41, 0x74, 0x6d, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x61, 0x72, 0x62, 0x79, 0x41,
	0x74, 0x6d, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x94, 0x01, 0x0a, 0x1b, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x54, 0x6f, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x39, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x74, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x54, 0x6f, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x54, 0x6f, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x64, 0x0a, 0x30, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x5a, 0x30,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_currencyinsights_service_proto_rawDescOnce sync.Once
	file_api_card_currencyinsights_service_proto_rawDescData = file_api_card_currencyinsights_service_proto_rawDesc
)

func file_api_card_currencyinsights_service_proto_rawDescGZIP() []byte {
	file_api_card_currencyinsights_service_proto_rawDescOnce.Do(func() {
		file_api_card_currencyinsights_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_currencyinsights_service_proto_rawDescData)
	})
	return file_api_card_currencyinsights_service_proto_rawDescData
}

var file_api_card_currencyinsights_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_card_currencyinsights_service_proto_goTypes = []interface{}{
	(*ConvertMoneyToLocalCurrencyRequest)(nil),         // 0: card.currencyinsights.ConvertMoneyToLocalCurrencyRequest
	(*ConvertMoneyToLocalCurrencyResponse)(nil),        // 1: card.currencyinsights.ConvertMoneyToLocalCurrencyResponse
	(*GetNearbyAtmLocationsRequest)(nil),               // 2: card.currencyinsights.GetNearbyAtmLocationsRequest
	(*GetNearbyAtmLocationsResponse)(nil),              // 3: card.currencyinsights.GetNearbyAtmLocationsResponse
	(*GetNearbyAtmLocationsRequest_FilterOptions)(nil), // 4: card.currencyinsights.GetNearbyAtmLocationsRequest.FilterOptions
	(*money.Money)(nil),                                // 5: google.type.Money
	(*rpc.Status)(nil),                                 // 6: rpc.Status
	(*typesv2.ATM)(nil),                                // 7: api.typesv2.ATM
	(typesv2.ATMProperty)(0),                           // 8: api.typesv2.ATMProperty
}
var file_api_card_currencyinsights_service_proto_depIdxs = []int32{
	5, // 0: card.currencyinsights.ConvertMoneyToLocalCurrencyRequest.money:type_name -> google.type.Money
	6, // 1: card.currencyinsights.ConvertMoneyToLocalCurrencyResponse.status:type_name -> rpc.Status
	5, // 2: card.currencyinsights.ConvertMoneyToLocalCurrencyResponse.money:type_name -> google.type.Money
	4, // 3: card.currencyinsights.GetNearbyAtmLocationsRequest.filter_options:type_name -> card.currencyinsights.GetNearbyAtmLocationsRequest.FilterOptions
	6, // 4: card.currencyinsights.GetNearbyAtmLocationsResponse.status:type_name -> rpc.Status
	7, // 5: card.currencyinsights.GetNearbyAtmLocationsResponse.found_atms:type_name -> api.typesv2.ATM
	8, // 6: card.currencyinsights.GetNearbyAtmLocationsRequest.FilterOptions.property_filters:type_name -> api.typesv2.ATMProperty
	2, // 7: card.currencyinsights.CurrencyInsights.GetNearbyAtmLocations:input_type -> card.currencyinsights.GetNearbyAtmLocationsRequest
	0, // 8: card.currencyinsights.CurrencyInsights.ConvertMoneyToLocalCurrency:input_type -> card.currencyinsights.ConvertMoneyToLocalCurrencyRequest
	3, // 9: card.currencyinsights.CurrencyInsights.GetNearbyAtmLocations:output_type -> card.currencyinsights.GetNearbyAtmLocationsResponse
	1, // 10: card.currencyinsights.CurrencyInsights.ConvertMoneyToLocalCurrency:output_type -> card.currencyinsights.ConvertMoneyToLocalCurrencyResponse
	9, // [9:11] is the sub-list for method output_type
	7, // [7:9] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_card_currencyinsights_service_proto_init() }
func file_api_card_currencyinsights_service_proto_init() {
	if File_api_card_currencyinsights_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_currencyinsights_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConvertMoneyToLocalCurrencyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_currencyinsights_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConvertMoneyToLocalCurrencyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_currencyinsights_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNearbyAtmLocationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_currencyinsights_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNearbyAtmLocationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_currencyinsights_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNearbyAtmLocationsRequest_FilterOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_currencyinsights_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_card_currencyinsights_service_proto_goTypes,
		DependencyIndexes: file_api_card_currencyinsights_service_proto_depIdxs,
		MessageInfos:      file_api_card_currencyinsights_service_proto_msgTypes,
	}.Build()
	File_api_card_currencyinsights_service_proto = out.File
	file_api_card_currencyinsights_service_proto_rawDesc = nil
	file_api_card_currencyinsights_service_proto_goTypes = nil
	file_api_card_currencyinsights_service_proto_depIdxs = nil
}
