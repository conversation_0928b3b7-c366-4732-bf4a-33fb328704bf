// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/card/currencyinsights/service.proto

package currencyinsights

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CurrencyInsights_GetNearbyAtmLocations_FullMethodName       = "/card.currencyinsights.CurrencyInsights/GetNearbyAtmLocations"
	CurrencyInsights_ConvertMoneyToLocalCurrency_FullMethodName = "/card.currencyinsights.CurrencyInsights/ConvertMoneyToLocalCurrency"
)

// CurrencyInsightsClient is the client API for CurrencyInsights service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CurrencyInsightsClient interface {
	// rpc GetNearbyAtmsLocations fetches nearby atms for on user based on the area radius and filter options provided
	GetNearbyAtmLocations(ctx context.Context, in *GetNearbyAtmLocationsRequest, opts ...grpc.CallOption) (*GetNearbyAtmLocationsResponse, error)
	// ConvertMoneyToLocalCurrency takes money as input and converts it into local currency money.
	// This will mostly be used for in-app views only for ex- convert sa balance to local currency.
	// It will fetch the current location of the user from cache, which is updated via `ProcessUserDevicePropertiesUpdateEvent` consumer RPC,
	// which consumer userLocationUpdate event.
	ConvertMoneyToLocalCurrency(ctx context.Context, in *ConvertMoneyToLocalCurrencyRequest, opts ...grpc.CallOption) (*ConvertMoneyToLocalCurrencyResponse, error)
}

type currencyInsightsClient struct {
	cc grpc.ClientConnInterface
}

func NewCurrencyInsightsClient(cc grpc.ClientConnInterface) CurrencyInsightsClient {
	return &currencyInsightsClient{cc}
}

func (c *currencyInsightsClient) GetNearbyAtmLocations(ctx context.Context, in *GetNearbyAtmLocationsRequest, opts ...grpc.CallOption) (*GetNearbyAtmLocationsResponse, error) {
	out := new(GetNearbyAtmLocationsResponse)
	err := c.cc.Invoke(ctx, CurrencyInsights_GetNearbyAtmLocations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *currencyInsightsClient) ConvertMoneyToLocalCurrency(ctx context.Context, in *ConvertMoneyToLocalCurrencyRequest, opts ...grpc.CallOption) (*ConvertMoneyToLocalCurrencyResponse, error) {
	out := new(ConvertMoneyToLocalCurrencyResponse)
	err := c.cc.Invoke(ctx, CurrencyInsights_ConvertMoneyToLocalCurrency_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CurrencyInsightsServer is the server API for CurrencyInsights service.
// All implementations should embed UnimplementedCurrencyInsightsServer
// for forward compatibility
type CurrencyInsightsServer interface {
	// rpc GetNearbyAtmsLocations fetches nearby atms for on user based on the area radius and filter options provided
	GetNearbyAtmLocations(context.Context, *GetNearbyAtmLocationsRequest) (*GetNearbyAtmLocationsResponse, error)
	// ConvertMoneyToLocalCurrency takes money as input and converts it into local currency money.
	// This will mostly be used for in-app views only for ex- convert sa balance to local currency.
	// It will fetch the current location of the user from cache, which is updated via `ProcessUserDevicePropertiesUpdateEvent` consumer RPC,
	// which consumer userLocationUpdate event.
	ConvertMoneyToLocalCurrency(context.Context, *ConvertMoneyToLocalCurrencyRequest) (*ConvertMoneyToLocalCurrencyResponse, error)
}

// UnimplementedCurrencyInsightsServer should be embedded to have forward compatible implementations.
type UnimplementedCurrencyInsightsServer struct {
}

func (UnimplementedCurrencyInsightsServer) GetNearbyAtmLocations(context.Context, *GetNearbyAtmLocationsRequest) (*GetNearbyAtmLocationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNearbyAtmLocations not implemented")
}
func (UnimplementedCurrencyInsightsServer) ConvertMoneyToLocalCurrency(context.Context, *ConvertMoneyToLocalCurrencyRequest) (*ConvertMoneyToLocalCurrencyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConvertMoneyToLocalCurrency not implemented")
}

// UnsafeCurrencyInsightsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CurrencyInsightsServer will
// result in compilation errors.
type UnsafeCurrencyInsightsServer interface {
	mustEmbedUnimplementedCurrencyInsightsServer()
}

func RegisterCurrencyInsightsServer(s grpc.ServiceRegistrar, srv CurrencyInsightsServer) {
	s.RegisterService(&CurrencyInsights_ServiceDesc, srv)
}

func _CurrencyInsights_GetNearbyAtmLocations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNearbyAtmLocationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CurrencyInsightsServer).GetNearbyAtmLocations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CurrencyInsights_GetNearbyAtmLocations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CurrencyInsightsServer).GetNearbyAtmLocations(ctx, req.(*GetNearbyAtmLocationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CurrencyInsights_ConvertMoneyToLocalCurrency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConvertMoneyToLocalCurrencyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CurrencyInsightsServer).ConvertMoneyToLocalCurrency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CurrencyInsights_ConvertMoneyToLocalCurrency_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CurrencyInsightsServer).ConvertMoneyToLocalCurrency(ctx, req.(*ConvertMoneyToLocalCurrencyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CurrencyInsights_ServiceDesc is the grpc.ServiceDesc for CurrencyInsights service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CurrencyInsights_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "card.currencyinsights.CurrencyInsights",
	HandlerType: (*CurrencyInsightsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNearbyAtmLocations",
			Handler:    _CurrencyInsights_GetNearbyAtmLocations_Handler,
		},
		{
			MethodName: "ConvertMoneyToLocalCurrency",
			Handler:    _CurrencyInsights_ConvertMoneyToLocalCurrency_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/card/currencyinsights/service.proto",
}
