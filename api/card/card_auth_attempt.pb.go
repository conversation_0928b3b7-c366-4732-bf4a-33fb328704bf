// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/card_auth_attempt.proto

package card

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ActionState int32

const (
	ActionState_ACTION_STATE_UNSPECIFIED ActionState = 0
	// State when auth for the action is initiated
	ActionState_ACTION_STATE_INITIATED ActionState = 1
	// Action performed successfully
	ActionState_ACTION_STATE_SUCCESS ActionState = 2
	// Action failed
	ActionState_ACTION_STATE_FAILURE ActionState = 3
)

// Enum value maps for ActionState.
var (
	ActionState_name = map[int32]string{
		0: "ACTION_STATE_UNSPECIFIED",
		1: "ACTION_STATE_INITIATED",
		2: "ACTION_STATE_SUCCESS",
		3: "ACTION_STATE_FAILURE",
	}
	ActionState_value = map[string]int32{
		"ACTION_STATE_UNSPECIFIED": 0,
		"ACTION_STATE_INITIATED":   1,
		"ACTION_STATE_SUCCESS":     2,
		"ACTION_STATE_FAILURE":     3,
	}
)

func (x ActionState) Enum() *ActionState {
	p := new(ActionState)
	*p = x
	return p
}

func (x ActionState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_auth_attempt_proto_enumTypes[0].Descriptor()
}

func (ActionState) Type() protoreflect.EnumType {
	return &file_api_card_card_auth_attempt_proto_enumTypes[0]
}

func (x ActionState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActionState.Descriptor instead.
func (ActionState) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_auth_attempt_proto_rawDescGZIP(), []int{0}
}

// Stage corresponding to a given action
// Currently we have only added for pin set/reset stages
type ActionStage int32

const (
	ActionStage_ACTION_STAGE_UNSPECIFIED ActionStage = 0
	ActionStage_CARD_PIN_OTP_GENERATION  ActionStage = 1
	ActionStage_CARD_PIN_SET_AT_VENDOR   ActionStage = 2
	ActionStage_CARD_PIN_RESET_AT_VENDOR ActionStage = 3
)

// Enum value maps for ActionStage.
var (
	ActionStage_name = map[int32]string{
		0: "ACTION_STAGE_UNSPECIFIED",
		1: "CARD_PIN_OTP_GENERATION",
		2: "CARD_PIN_SET_AT_VENDOR",
		3: "CARD_PIN_RESET_AT_VENDOR",
	}
	ActionStage_value = map[string]int32{
		"ACTION_STAGE_UNSPECIFIED": 0,
		"CARD_PIN_OTP_GENERATION":  1,
		"CARD_PIN_SET_AT_VENDOR":   2,
		"CARD_PIN_RESET_AT_VENDOR": 3,
	}
)

func (x ActionStage) Enum() *ActionStage {
	p := new(ActionStage)
	*p = x
	return p
}

func (x ActionStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionStage) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_auth_attempt_proto_enumTypes[1].Descriptor()
}

func (ActionStage) Type() protoreflect.EnumType {
	return &file_api_card_card_auth_attempt_proto_enumTypes[1]
}

func (x ActionStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActionStage.Descriptor instead.
func (ActionStage) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_auth_attempt_proto_rawDescGZIP(), []int{1}
}

type AuthType int32

const (
	AuthType_AUTH_TYPE_UNSPECIFIED      AuthType = 0
	AuthType_LIVENESS_PLUS_FACEMATCH    AuthType = 1
	AuthType_NPCI_SECURE_PIN_VALIDATION AuthType = 2
)

// Enum value maps for AuthType.
var (
	AuthType_name = map[int32]string{
		0: "AUTH_TYPE_UNSPECIFIED",
		1: "LIVENESS_PLUS_FACEMATCH",
		2: "NPCI_SECURE_PIN_VALIDATION",
	}
	AuthType_value = map[string]int32{
		"AUTH_TYPE_UNSPECIFIED":      0,
		"LIVENESS_PLUS_FACEMATCH":    1,
		"NPCI_SECURE_PIN_VALIDATION": 2,
	}
)

func (x AuthType) Enum() *AuthType {
	p := new(AuthType)
	*p = x
	return p
}

func (x AuthType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_auth_attempt_proto_enumTypes[2].Descriptor()
}

func (AuthType) Type() protoreflect.EnumType {
	return &file_api_card_card_auth_attempt_proto_enumTypes[2]
}

func (x AuthType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthType.Descriptor instead.
func (AuthType) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_auth_attempt_proto_rawDescGZIP(), []int{2}
}

type CardAction int32

const (
	CardAction_CARD_ACTION_UNSPECIFIED CardAction = 0
	// Activate the card (Set atm pin).
	CardAction_ACTIVATE CardAction = 1
	// Action type for reset pin.
	CardAction_RESET_PIN CardAction = 2
)

// Enum value maps for CardAction.
var (
	CardAction_name = map[int32]string{
		0: "CARD_ACTION_UNSPECIFIED",
		1: "ACTIVATE",
		2: "RESET_PIN",
	}
	CardAction_value = map[string]int32{
		"CARD_ACTION_UNSPECIFIED": 0,
		"ACTIVATE":                1,
		"RESET_PIN":               2,
	}
)

func (x CardAction) Enum() *CardAction {
	p := new(CardAction)
	*p = x
	return p
}

func (x CardAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardAction) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_auth_attempt_proto_enumTypes[3].Descriptor()
}

func (CardAction) Type() protoreflect.EnumType {
	return &file_api_card_card_auth_attempt_proto_enumTypes[3]
}

func (x CardAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardAction.Descriptor instead.
func (CardAction) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_auth_attempt_proto_rawDescGZIP(), []int{3}
}

type AuthState int32

const (
	AuthState_AUTH_STATE_UNSPECIFIED AuthState = 0
	AuthState_SUCCESS                AuthState = 1
	AuthState_PENDING                AuthState = 2
	AuthState_LIVENESS_FAILED        AuthState = 3
	AuthState_FACEMATCH_FAILED       AuthState = 4
	AuthState_FAILED                 AuthState = 5
	AuthState_CONSUMED               AuthState = 6
)

// Enum value maps for AuthState.
var (
	AuthState_name = map[int32]string{
		0: "AUTH_STATE_UNSPECIFIED",
		1: "SUCCESS",
		2: "PENDING",
		3: "LIVENESS_FAILED",
		4: "FACEMATCH_FAILED",
		5: "FAILED",
		6: "CONSUMED",
	}
	AuthState_value = map[string]int32{
		"AUTH_STATE_UNSPECIFIED": 0,
		"SUCCESS":                1,
		"PENDING":                2,
		"LIVENESS_FAILED":        3,
		"FACEMATCH_FAILED":       4,
		"FAILED":                 5,
		"CONSUMED":               6,
	}
)

func (x AuthState) Enum() *AuthState {
	p := new(AuthState)
	*p = x
	return p
}

func (x AuthState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_auth_attempt_proto_enumTypes[4].Descriptor()
}

func (AuthState) Type() protoreflect.EnumType {
	return &file_api_card_card_auth_attempt_proto_enumTypes[4]
}

func (x AuthState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthState.Descriptor instead.
func (AuthState) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_auth_attempt_proto_rawDescGZIP(), []int{4}
}

// CardAuthAttemptFieldMask is the enum representation of all the CardAuthAttempt fields.
// Meant to be used as field mask to help with database updates.
type CardAuthAttemptFieldMask int32

const (
	CardAuthAttemptFieldMask_CARD_AUTH_ATTEMPT_FIELD_MASK_UNSPECIFIED CardAuthAttemptFieldMask = 0
	CardAuthAttemptFieldMask_CARD_AUTH_ATTEMPT_AUTH_STATE             CardAuthAttemptFieldMask = 1
	CardAuthAttemptFieldMask_CARD_AUTH_ATTEMPT_ACTION_STATE           CardAuthAttemptFieldMask = 2
	CardAuthAttemptFieldMask_CARD_AUTH_ATTEMPT_ACTION_STAGE_DETAILS   CardAuthAttemptFieldMask = 3
	CardAuthAttemptFieldMask_CARD_AUTH_ATTEMPT_ATTEMPT_ID             CardAuthAttemptFieldMask = 4
	CardAuthAttemptFieldMask_CARD_AUTH_ATTEMPT_CARD_ID                CardAuthAttemptFieldMask = 5
	CardAuthAttemptFieldMask_CARD_AUTH_ATTEMPT_ACTION                 CardAuthAttemptFieldMask = 6
	CardAuthAttemptFieldMask_CARD_AUTH_ATTEMPT_AUTH_TYPE              CardAuthAttemptFieldMask = 7
	CardAuthAttemptFieldMask_CARD_AUTH_ATTEMPT_CREATED_AT             CardAuthAttemptFieldMask = 8
	CardAuthAttemptFieldMask_CARD_AUTH_ATTEMPT_UPDATED_AT             CardAuthAttemptFieldMask = 9
	CardAuthAttemptFieldMask_CARD_AUTH_ATTEMPT_DELETED_AT             CardAuthAttemptFieldMask = 10
)

// Enum value maps for CardAuthAttemptFieldMask.
var (
	CardAuthAttemptFieldMask_name = map[int32]string{
		0:  "CARD_AUTH_ATTEMPT_FIELD_MASK_UNSPECIFIED",
		1:  "CARD_AUTH_ATTEMPT_AUTH_STATE",
		2:  "CARD_AUTH_ATTEMPT_ACTION_STATE",
		3:  "CARD_AUTH_ATTEMPT_ACTION_STAGE_DETAILS",
		4:  "CARD_AUTH_ATTEMPT_ATTEMPT_ID",
		5:  "CARD_AUTH_ATTEMPT_CARD_ID",
		6:  "CARD_AUTH_ATTEMPT_ACTION",
		7:  "CARD_AUTH_ATTEMPT_AUTH_TYPE",
		8:  "CARD_AUTH_ATTEMPT_CREATED_AT",
		9:  "CARD_AUTH_ATTEMPT_UPDATED_AT",
		10: "CARD_AUTH_ATTEMPT_DELETED_AT",
	}
	CardAuthAttemptFieldMask_value = map[string]int32{
		"CARD_AUTH_ATTEMPT_FIELD_MASK_UNSPECIFIED": 0,
		"CARD_AUTH_ATTEMPT_AUTH_STATE":             1,
		"CARD_AUTH_ATTEMPT_ACTION_STATE":           2,
		"CARD_AUTH_ATTEMPT_ACTION_STAGE_DETAILS":   3,
		"CARD_AUTH_ATTEMPT_ATTEMPT_ID":             4,
		"CARD_AUTH_ATTEMPT_CARD_ID":                5,
		"CARD_AUTH_ATTEMPT_ACTION":                 6,
		"CARD_AUTH_ATTEMPT_AUTH_TYPE":              7,
		"CARD_AUTH_ATTEMPT_CREATED_AT":             8,
		"CARD_AUTH_ATTEMPT_UPDATED_AT":             9,
		"CARD_AUTH_ATTEMPT_DELETED_AT":             10,
	}
)

func (x CardAuthAttemptFieldMask) Enum() *CardAuthAttemptFieldMask {
	p := new(CardAuthAttemptFieldMask)
	*p = x
	return p
}

func (x CardAuthAttemptFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardAuthAttemptFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_auth_attempt_proto_enumTypes[5].Descriptor()
}

func (CardAuthAttemptFieldMask) Type() protoreflect.EnumType {
	return &file_api_card_card_auth_attempt_proto_enumTypes[5]
}

func (x CardAuthAttemptFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardAuthAttemptFieldMask.Descriptor instead.
func (CardAuthAttemptFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_auth_attempt_proto_rawDescGZIP(), []int{5}
}

type CardAuthAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Primary identifier
	AttemptId  string     `protobuf:"bytes,1,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
	CardId     string     `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	CardAction CardAction `protobuf:"varint,3,opt,name=card_action,json=cardAction,proto3,enum=card.CardAction" json:"card_action,omitempty"`
	State      AuthState  `protobuf:"varint,4,opt,name=state,proto3,enum=card.AuthState" json:"state,omitempty"`
	AuthType   AuthType   `protobuf:"varint,5,opt,name=auth_type,json=authType,proto3,enum=card.AuthType" json:"auth_type,omitempty"`
	// CardAuthLivenessAttempt creation timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// CardAuthLivenessAttempt updated at timestamp
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Terminal state corresponding to the card action for which auth was initiated
	ActionState ActionState `protobuf:"varint,8,opt,name=action_state,json=actionState,proto3,enum=card.ActionState" json:"action_state,omitempty"`
	// Stage details corresponding to each stage performed to complete an action along with the
	// failure response codes and reason from vendor and the stage updated at timestamp
	ActionStageDetails *ActionStageDetails    `protobuf:"bytes,9,opt,name=action_stage_details,json=actionStageDetails,proto3" json:"action_stage_details,omitempty"`
	DeletedAt          *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *CardAuthAttempt) Reset() {
	*x = CardAuthAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_auth_attempt_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardAuthAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardAuthAttempt) ProtoMessage() {}

func (x *CardAuthAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_auth_attempt_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardAuthAttempt.ProtoReflect.Descriptor instead.
func (*CardAuthAttempt) Descriptor() ([]byte, []int) {
	return file_api_card_card_auth_attempt_proto_rawDescGZIP(), []int{0}
}

func (x *CardAuthAttempt) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *CardAuthAttempt) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CardAuthAttempt) GetCardAction() CardAction {
	if x != nil {
		return x.CardAction
	}
	return CardAction_CARD_ACTION_UNSPECIFIED
}

func (x *CardAuthAttempt) GetState() AuthState {
	if x != nil {
		return x.State
	}
	return AuthState_AUTH_STATE_UNSPECIFIED
}

func (x *CardAuthAttempt) GetAuthType() AuthType {
	if x != nil {
		return x.AuthType
	}
	return AuthType_AUTH_TYPE_UNSPECIFIED
}

func (x *CardAuthAttempt) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CardAuthAttempt) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CardAuthAttempt) GetActionState() ActionState {
	if x != nil {
		return x.ActionState
	}
	return ActionState_ACTION_STATE_UNSPECIFIED
}

func (x *CardAuthAttempt) GetActionStageDetails() *ActionStageDetails {
	if x != nil {
		return x.ActionStageDetails
	}
	return nil
}

func (x *CardAuthAttempt) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

type ActionStageDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Details regarding the states of stage are maintained here
	StageMapping map[string]*StageInfo `protobuf:"bytes,1,rep,name=stage_mapping,json=stageMapping,proto3" json:"stage_mapping,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ActionStageDetails) Reset() {
	*x = ActionStageDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_auth_attempt_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActionStageDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionStageDetails) ProtoMessage() {}

func (x *ActionStageDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_auth_attempt_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionStageDetails.ProtoReflect.Descriptor instead.
func (*ActionStageDetails) Descriptor() ([]byte, []int) {
	return file_api_card_card_auth_attempt_proto_rawDescGZIP(), []int{1}
}

func (x *ActionStageDetails) GetStageMapping() map[string]*StageInfo {
	if x != nil {
		return x.StageMapping
	}
	return nil
}

type StageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// State of the stage
	State     ActionState            `protobuf:"varint,1,opt,name=state,proto3,enum=card.ActionState" json:"state,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// If the stage involved vendor interaction then the response code/reason received from vendor in case if
	// failure scenarios.
	FailureCode   string `protobuf:"bytes,3,opt,name=failure_code,json=failureCode,proto3" json:"failure_code,omitempty"`
	FailureReason string `protobuf:"bytes,4,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *StageInfo) Reset() {
	*x = StageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_auth_attempt_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageInfo) ProtoMessage() {}

func (x *StageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_auth_attempt_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageInfo.ProtoReflect.Descriptor instead.
func (*StageInfo) Descriptor() ([]byte, []int) {
	return file_api_card_card_auth_attempt_proto_rawDescGZIP(), []int{2}
}

func (x *StageInfo) GetState() ActionState {
	if x != nil {
		return x.State
	}
	return ActionState_ACTION_STATE_UNSPECIFIED
}

func (x *StageInfo) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *StageInfo) GetFailureCode() string {
	if x != nil {
		return x.FailureCode
	}
	return ""
}

func (x *StageInfo) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

var File_api_card_card_auth_attempt_proto protoreflect.FileDescriptor

var file_api_card_card_auth_attempt_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x61, 0x75, 0x74, 0x68, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x04, 0x63, 0x61, 0x72, 0x64, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x83, 0x04, 0x0a, 0x0f, 0x43, 0x61,
	0x72, 0x64, 0x41, 0x75, 0x74, 0x68, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x63, 0x61,
	0x72, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x41,
	0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x2b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x34, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x4a, 0x0a, 0x14, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x12, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0xb7, 0x01, 0x0a, 0x12, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4f, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f,
	0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4d, 0x61, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x1a, 0x50, 0x0a, 0x11, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x25,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb9, 0x01, 0x0a, 0x09, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x27, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x2a, 0x7b, 0x0a, 0x0b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x18,
	0x0a, 0x14, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x10, 0x03, 0x2a, 0x82, 0x01, 0x0a, 0x0b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1b, 0x0a, 0x17, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x4f, 0x54, 0x50,
	0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x1a, 0x0a,
	0x16, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x54,
	0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x54, 0x5f, 0x56,
	0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x03, 0x2a, 0x62, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b,
	0x0a, 0x17, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x4c, 0x55, 0x53, 0x5f,
	0x46, 0x41, 0x43, 0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x4e,
	0x50, 0x43, 0x49, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x2a, 0x46, 0x0a, 0x0a, 0x43,
	0x61, 0x72, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41,
	0x54, 0x45, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x45, 0x53, 0x45, 0x54, 0x5f, 0x50, 0x49,
	0x4e, 0x10, 0x02, 0x2a, 0x86, 0x01, 0x0a, 0x09, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x49, 0x56, 0x45, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10,
	0x46, 0x41, 0x43, 0x45, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0c,
	0x0a, 0x08, 0x43, 0x4f, 0x4e, 0x53, 0x55, 0x4d, 0x45, 0x44, 0x10, 0x06, 0x2a, 0xa0, 0x03, 0x0a,
	0x18, 0x43, 0x61, 0x72, 0x64, 0x41, 0x75, 0x74, 0x68, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x41, 0x55, 0x54,
	0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x02, 0x12, 0x2a, 0x0a,
	0x26, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d,
	0x50, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x1d, 0x0a, 0x19, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x44, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x07, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x08, 0x12, 0x20, 0x0a, 0x1c, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x09, 0x12, 0x20, 0x0a,
	0x1c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d,
	0x50, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0a, 0x42,
	0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x61, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_card_auth_attempt_proto_rawDescOnce sync.Once
	file_api_card_card_auth_attempt_proto_rawDescData = file_api_card_card_auth_attempt_proto_rawDesc
)

func file_api_card_card_auth_attempt_proto_rawDescGZIP() []byte {
	file_api_card_card_auth_attempt_proto_rawDescOnce.Do(func() {
		file_api_card_card_auth_attempt_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_card_auth_attempt_proto_rawDescData)
	})
	return file_api_card_card_auth_attempt_proto_rawDescData
}

var file_api_card_card_auth_attempt_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_card_card_auth_attempt_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_card_card_auth_attempt_proto_goTypes = []interface{}{
	(ActionState)(0),              // 0: card.ActionState
	(ActionStage)(0),              // 1: card.ActionStage
	(AuthType)(0),                 // 2: card.AuthType
	(CardAction)(0),               // 3: card.CardAction
	(AuthState)(0),                // 4: card.AuthState
	(CardAuthAttemptFieldMask)(0), // 5: card.CardAuthAttemptFieldMask
	(*CardAuthAttempt)(nil),       // 6: card.CardAuthAttempt
	(*ActionStageDetails)(nil),    // 7: card.ActionStageDetails
	(*StageInfo)(nil),             // 8: card.StageInfo
	nil,                           // 9: card.ActionStageDetails.StageMappingEntry
	(*timestamppb.Timestamp)(nil), // 10: google.protobuf.Timestamp
}
var file_api_card_card_auth_attempt_proto_depIdxs = []int32{
	3,  // 0: card.CardAuthAttempt.card_action:type_name -> card.CardAction
	4,  // 1: card.CardAuthAttempt.state:type_name -> card.AuthState
	2,  // 2: card.CardAuthAttempt.auth_type:type_name -> card.AuthType
	10, // 3: card.CardAuthAttempt.created_at:type_name -> google.protobuf.Timestamp
	10, // 4: card.CardAuthAttempt.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 5: card.CardAuthAttempt.action_state:type_name -> card.ActionState
	7,  // 6: card.CardAuthAttempt.action_stage_details:type_name -> card.ActionStageDetails
	10, // 7: card.CardAuthAttempt.deleted_at:type_name -> google.protobuf.Timestamp
	9,  // 8: card.ActionStageDetails.stage_mapping:type_name -> card.ActionStageDetails.StageMappingEntry
	0,  // 9: card.StageInfo.state:type_name -> card.ActionState
	10, // 10: card.StageInfo.updated_at:type_name -> google.protobuf.Timestamp
	8,  // 11: card.ActionStageDetails.StageMappingEntry.value:type_name -> card.StageInfo
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_api_card_card_auth_attempt_proto_init() }
func file_api_card_card_auth_attempt_proto_init() {
	if File_api_card_card_auth_attempt_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_card_auth_attempt_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardAuthAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_card_auth_attempt_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActionStageDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_card_auth_attempt_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_card_auth_attempt_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_card_auth_attempt_proto_goTypes,
		DependencyIndexes: file_api_card_card_auth_attempt_proto_depIdxs,
		EnumInfos:         file_api_card_card_auth_attempt_proto_enumTypes,
		MessageInfos:      file_api_card_card_auth_attempt_proto_msgTypes,
	}.Build()
	File_api_card_card_auth_attempt_proto = out.File
	file_api_card_card_auth_attempt_proto_rawDesc = nil
	file_api_card_card_auth_attempt_proto_goTypes = nil
	file_api_card_card_auth_attempt_proto_depIdxs = nil
}
