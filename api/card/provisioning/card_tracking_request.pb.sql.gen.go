// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/card/provisioning/card_tracking_request.pb.go

package provisioning

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing TrackingMetadata while reading from DB
func (a *TrackingMetadata) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the TrackingMetadata in string format in DB
func (a *TrackingMetadata) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for TrackingMetadata
func (a *TrackingMetadata) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for TrackingMetadata
func (a *TrackingMetadata) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
