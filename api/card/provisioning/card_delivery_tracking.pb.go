// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/provisioning/card_delivery_tracking.proto

package provisioning

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CardDeliveryTrackingState int32

const (
	CardDeliveryTrackingState_CARD_DELIVERY_STATE_UNSPECIFIED CardDeliveryTrackingState = 0
	// Card id delivered by the courier partner
	CardDeliveryTrackingState_DELIVERED_BY_PARTNER CardDeliveryTrackingState = 2
	// Card is received by the user.
	// We will update state to this only after successful qr code verification
	// User can enable NFC, ATM and POS txns in this state
	CardDeliveryTrackingState_RECEIVED_BY_USER CardDeliveryTrackingState = 3
)

// Enum value maps for CardDeliveryTrackingState.
var (
	CardDeliveryTrackingState_name = map[int32]string{
		0: "CARD_DELIVERY_STATE_UNSPECIFIED",
		2: "DELIVERED_BY_PARTNER",
		3: "RECEIVED_BY_USER",
	}
	CardDeliveryTrackingState_value = map[string]int32{
		"CARD_DELIVERY_STATE_UNSPECIFIED": 0,
		"DELIVERED_BY_PARTNER":            2,
		"RECEIVED_BY_USER":                3,
	}
)

func (x CardDeliveryTrackingState) Enum() *CardDeliveryTrackingState {
	p := new(CardDeliveryTrackingState)
	*p = x
	return p
}

func (x CardDeliveryTrackingState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardDeliveryTrackingState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_card_delivery_tracking_proto_enumTypes[0].Descriptor()
}

func (CardDeliveryTrackingState) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_card_delivery_tracking_proto_enumTypes[0]
}

func (x CardDeliveryTrackingState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardDeliveryTrackingState.Descriptor instead.
func (CardDeliveryTrackingState) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_delivery_tracking_proto_rawDescGZIP(), []int{0}
}

// CardFieldMask is the enum representation of all the card fields.
// Meant to be used as field mask to help with database updates.
type CardDeliveryTrackingFieldMask int32

const (
	CardDeliveryTrackingFieldMask_CARD_DELIVERY_TRACKING_FIELD_MASK_UNSPECIFIED CardDeliveryTrackingFieldMask = 0
	CardDeliveryTrackingFieldMask_CARD_DELIVERY_TRACKING_CARD_DELIVERY_STATE    CardDeliveryTrackingFieldMask = 2
	CardDeliveryTrackingFieldMask_CARD_DELIVERY_TRACKING_CARD_QR_DATA           CardDeliveryTrackingFieldMask = 3
	CardDeliveryTrackingFieldMask_CARD_DELIVERY_TRACKING_CARD_ID                CardDeliveryTrackingFieldMask = 4
	CardDeliveryTrackingFieldMask_CARD_DELIVERY_TRACKING_CREATED_AT             CardDeliveryTrackingFieldMask = 5
	CardDeliveryTrackingFieldMask_CARD_DELIVERY_TRACKING_UPDATED_AT             CardDeliveryTrackingFieldMask = 6
	CardDeliveryTrackingFieldMask_CARD_DELIVERY_TRACKING_DELETED_AT             CardDeliveryTrackingFieldMask = 7
)

// Enum value maps for CardDeliveryTrackingFieldMask.
var (
	CardDeliveryTrackingFieldMask_name = map[int32]string{
		0: "CARD_DELIVERY_TRACKING_FIELD_MASK_UNSPECIFIED",
		2: "CARD_DELIVERY_TRACKING_CARD_DELIVERY_STATE",
		3: "CARD_DELIVERY_TRACKING_CARD_QR_DATA",
		4: "CARD_DELIVERY_TRACKING_CARD_ID",
		5: "CARD_DELIVERY_TRACKING_CREATED_AT",
		6: "CARD_DELIVERY_TRACKING_UPDATED_AT",
		7: "CARD_DELIVERY_TRACKING_DELETED_AT",
	}
	CardDeliveryTrackingFieldMask_value = map[string]int32{
		"CARD_DELIVERY_TRACKING_FIELD_MASK_UNSPECIFIED": 0,
		"CARD_DELIVERY_TRACKING_CARD_DELIVERY_STATE":    2,
		"CARD_DELIVERY_TRACKING_CARD_QR_DATA":           3,
		"CARD_DELIVERY_TRACKING_CARD_ID":                4,
		"CARD_DELIVERY_TRACKING_CREATED_AT":             5,
		"CARD_DELIVERY_TRACKING_UPDATED_AT":             6,
		"CARD_DELIVERY_TRACKING_DELETED_AT":             7,
	}
)

func (x CardDeliveryTrackingFieldMask) Enum() *CardDeliveryTrackingFieldMask {
	p := new(CardDeliveryTrackingFieldMask)
	*p = x
	return p
}

func (x CardDeliveryTrackingFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardDeliveryTrackingFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_card_delivery_tracking_proto_enumTypes[1].Descriptor()
}

func (CardDeliveryTrackingFieldMask) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_card_delivery_tracking_proto_enumTypes[1]
}

func (x CardDeliveryTrackingFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardDeliveryTrackingFieldMask.Descriptor instead.
func (CardDeliveryTrackingFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_delivery_tracking_proto_rawDescGZIP(), []int{1}
}

type CardDeliveryTracking struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Primary identifier of a card in database model.
	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// The state of the card delivery.
	State      CardDeliveryTrackingState `protobuf:"varint,2,opt,name=state,proto3,enum=card.provisioning.CardDeliveryTrackingState" json:"state,omitempty"`
	CardQrData *CardQRData               `protobuf:"bytes,3,opt,name=card_qr_data,json=cardQrData,proto3" json:"card_qr_data,omitempty"`
	// creation timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// update timestamp
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *CardDeliveryTracking) Reset() {
	*x = CardDeliveryTracking{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_delivery_tracking_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardDeliveryTracking) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardDeliveryTracking) ProtoMessage() {}

func (x *CardDeliveryTracking) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_delivery_tracking_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardDeliveryTracking.ProtoReflect.Descriptor instead.
func (*CardDeliveryTracking) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_delivery_tracking_proto_rawDescGZIP(), []int{0}
}

func (x *CardDeliveryTracking) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CardDeliveryTracking) GetState() CardDeliveryTrackingState {
	if x != nil {
		return x.State
	}
	return CardDeliveryTrackingState_CARD_DELIVERY_STATE_UNSPECIFIED
}

func (x *CardDeliveryTracking) GetCardQrData() *CardQRData {
	if x != nil {
		return x.CardQrData
	}
	return nil
}

func (x *CardDeliveryTracking) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CardDeliveryTracking) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CardDeliveryTracking) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

// Data required for physical card activation (qr code scan) and attributes related to it.
type CardQRData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaskedCardNumber   string              `protobuf:"bytes,1,opt,name=masked_card_number,json=maskedCardNumber,proto3" json:"masked_card_number,omitempty"`
	PhoneNumber        *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	PostalCode         string              `protobuf:"bytes,3,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	PhoneNumberUpdated bool                `protobuf:"varint,4,opt,name=phone_number_updated,json=phoneNumberUpdated,proto3" json:"phone_number_updated,omitempty"`
}

func (x *CardQRData) Reset() {
	*x = CardQRData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_delivery_tracking_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardQRData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardQRData) ProtoMessage() {}

func (x *CardQRData) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_delivery_tracking_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardQRData.ProtoReflect.Descriptor instead.
func (*CardQRData) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_delivery_tracking_proto_rawDescGZIP(), []int{1}
}

func (x *CardQRData) GetMaskedCardNumber() string {
	if x != nil {
		return x.MaskedCardNumber
	}
	return ""
}

func (x *CardQRData) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *CardQRData) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *CardQRData) GetPhoneNumberUpdated() bool {
	if x != nil {
		return x.PhoneNumberUpdated
	}
	return false
}

var File_api_card_provisioning_card_delivery_tracking_proto protoreflect.FileDescriptor

var file_api_card_provisioning_card_delivery_tracking_proto_rawDesc = []byte{
	0x0a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xe5, 0x02, 0x0a, 0x14, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49,
	0x64, 0x12, 0x42, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x71, 0x72,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e,
	0x43, 0x61, 0x72, 0x64, 0x51, 0x52, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x64,
	0x51, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xd1, 0x01, 0x0a, 0x0a, 0x43, 0x61, 0x72, 0x64,
	0x51, 0x52, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70,
	0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x2a, 0x70, 0x0a, 0x19, 0x43,
	0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a,
	0x14, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x50, 0x41,
	0x52, 0x54, 0x4e, 0x45, 0x52, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x45, 0x49,
	0x56, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x03, 0x2a, 0xc4, 0x02,
	0x0a, 0x1d, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12,
	0x31, 0x0a, 0x2d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59,
	0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56,
	0x45, 0x52, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45,
	0x10, 0x02, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56,
	0x45, 0x52, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x51, 0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x54, 0x52, 0x41,
	0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12,
	0x25, 0x0a, 0x21, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59,
	0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x05, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44,
	0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x06, 0x12, 0x25, 0x0a,
	0x21, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x54,
	0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f,
	0x41, 0x54, 0x10, 0x07, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x69, 0x6e, 0x67, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69,
	0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_provisioning_card_delivery_tracking_proto_rawDescOnce sync.Once
	file_api_card_provisioning_card_delivery_tracking_proto_rawDescData = file_api_card_provisioning_card_delivery_tracking_proto_rawDesc
)

func file_api_card_provisioning_card_delivery_tracking_proto_rawDescGZIP() []byte {
	file_api_card_provisioning_card_delivery_tracking_proto_rawDescOnce.Do(func() {
		file_api_card_provisioning_card_delivery_tracking_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_provisioning_card_delivery_tracking_proto_rawDescData)
	})
	return file_api_card_provisioning_card_delivery_tracking_proto_rawDescData
}

var file_api_card_provisioning_card_delivery_tracking_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_card_provisioning_card_delivery_tracking_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_card_provisioning_card_delivery_tracking_proto_goTypes = []interface{}{
	(CardDeliveryTrackingState)(0),     // 0: card.provisioning.CardDeliveryTrackingState
	(CardDeliveryTrackingFieldMask)(0), // 1: card.provisioning.CardDeliveryTrackingFieldMask
	(*CardDeliveryTracking)(nil),       // 2: card.provisioning.CardDeliveryTracking
	(*CardQRData)(nil),                 // 3: card.provisioning.CardQRData
	(*timestamppb.Timestamp)(nil),      // 4: google.protobuf.Timestamp
	(*common.PhoneNumber)(nil),         // 5: api.typesv2.common.PhoneNumber
}
var file_api_card_provisioning_card_delivery_tracking_proto_depIdxs = []int32{
	0, // 0: card.provisioning.CardDeliveryTracking.state:type_name -> card.provisioning.CardDeliveryTrackingState
	3, // 1: card.provisioning.CardDeliveryTracking.card_qr_data:type_name -> card.provisioning.CardQRData
	4, // 2: card.provisioning.CardDeliveryTracking.created_at:type_name -> google.protobuf.Timestamp
	4, // 3: card.provisioning.CardDeliveryTracking.updated_at:type_name -> google.protobuf.Timestamp
	4, // 4: card.provisioning.CardDeliveryTracking.deleted_at:type_name -> google.protobuf.Timestamp
	5, // 5: card.provisioning.CardQRData.phone_number:type_name -> api.typesv2.common.PhoneNumber
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_card_provisioning_card_delivery_tracking_proto_init() }
func file_api_card_provisioning_card_delivery_tracking_proto_init() {
	if File_api_card_provisioning_card_delivery_tracking_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_provisioning_card_delivery_tracking_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardDeliveryTracking); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_delivery_tracking_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardQRData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_provisioning_card_delivery_tracking_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_provisioning_card_delivery_tracking_proto_goTypes,
		DependencyIndexes: file_api_card_provisioning_card_delivery_tracking_proto_depIdxs,
		EnumInfos:         file_api_card_provisioning_card_delivery_tracking_proto_enumTypes,
		MessageInfos:      file_api_card_provisioning_card_delivery_tracking_proto_msgTypes,
	}.Build()
	File_api_card_provisioning_card_delivery_tracking_proto = out.File
	file_api_card_provisioning_card_delivery_tracking_proto_rawDesc = nil
	file_api_card_provisioning_card_delivery_tracking_proto_goTypes = nil
	file_api_card_provisioning_card_delivery_tracking_proto_depIdxs = nil
}
