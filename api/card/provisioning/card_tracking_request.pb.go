// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=TrackingMetadata

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/provisioning/card_tracking_request.proto

package provisioning

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CardTrackingRequestState int32

const (
	CardTrackingRequestState_CARD_TRACKING_REQUEST_STATE_UNSPECIFIED CardTrackingRequestState = 0
	CardTrackingRequestState_FETCHED_DETAILS_FROM_BANK               CardTrackingRequestState = 1
	CardTrackingRequestState_REGISTERED_DETAILS_AT_SHIPWAY           CardTrackingRequestState = 2
)

// Enum value maps for CardTrackingRequestState.
var (
	CardTrackingRequestState_name = map[int32]string{
		0: "CARD_TRACKING_REQUEST_STATE_UNSPECIFIED",
		1: "FETCHED_DETAILS_FROM_BANK",
		2: "REGISTERED_DETAILS_AT_SHIPWAY",
	}
	CardTrackingRequestState_value = map[string]int32{
		"CARD_TRACKING_REQUEST_STATE_UNSPECIFIED": 0,
		"FETCHED_DETAILS_FROM_BANK":               1,
		"REGISTERED_DETAILS_AT_SHIPWAY":           2,
	}
)

func (x CardTrackingRequestState) Enum() *CardTrackingRequestState {
	p := new(CardTrackingRequestState)
	*p = x
	return p
}

func (x CardTrackingRequestState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardTrackingRequestState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_card_tracking_request_proto_enumTypes[0].Descriptor()
}

func (CardTrackingRequestState) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_card_tracking_request_proto_enumTypes[0]
}

func (x CardTrackingRequestState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardTrackingRequestState.Descriptor instead.
func (CardTrackingRequestState) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_tracking_request_proto_rawDescGZIP(), []int{0}
}

type CardTrackingDeliveryState int32

const (
	CardTrackingDeliveryState_CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED CardTrackingDeliveryState = 0
	// Shipment can be tracked post this state.
	CardTrackingDeliveryState_SHIPPED CardTrackingDeliveryState = 1
	// it means that your package is on its way to its final destination
	CardTrackingDeliveryState_IN_TRANSIT CardTrackingDeliveryState = 2
	// Shipment is out for delivery
	CardTrackingDeliveryState_OUT_FOR_DELIVERY CardTrackingDeliveryState = 3
	// Shipment is delivered
	CardTrackingDeliveryState_DELIVERED CardTrackingDeliveryState = 4
	// Shipment is returned to bank due to some issue in delivery
	CardTrackingDeliveryState_RETURNED_TO_ORIGIN CardTrackingDeliveryState = 5
)

// Enum value maps for CardTrackingDeliveryState.
var (
	CardTrackingDeliveryState_name = map[int32]string{
		0: "CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED",
		1: "SHIPPED",
		2: "IN_TRANSIT",
		3: "OUT_FOR_DELIVERY",
		4: "DELIVERED",
		5: "RETURNED_TO_ORIGIN",
	}
	CardTrackingDeliveryState_value = map[string]int32{
		"CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED": 0,
		"SHIPPED":            1,
		"IN_TRANSIT":         2,
		"OUT_FOR_DELIVERY":   3,
		"DELIVERED":          4,
		"RETURNED_TO_ORIGIN": 5,
	}
)

func (x CardTrackingDeliveryState) Enum() *CardTrackingDeliveryState {
	p := new(CardTrackingDeliveryState)
	*p = x
	return p
}

func (x CardTrackingDeliveryState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardTrackingDeliveryState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_card_tracking_request_proto_enumTypes[1].Descriptor()
}

func (CardTrackingDeliveryState) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_card_tracking_request_proto_enumTypes[1]
}

func (x CardTrackingDeliveryState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardTrackingDeliveryState.Descriptor instead.
func (CardTrackingDeliveryState) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_tracking_request_proto_rawDescGZIP(), []int{1}
}

type CardTrackingRequestFieldMask int32

const (
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_FIELD_MASK_UNSPECIFIED        CardTrackingRequestFieldMask = 0
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_AWB                           CardTrackingRequestFieldMask = 1
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_CARRIER                       CardTrackingRequestFieldMask = 2
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_REQUEST_STATE                 CardTrackingRequestFieldMask = 3
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_DELIVERY_STATE                CardTrackingRequestFieldMask = 4
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_SCANS                         CardTrackingRequestFieldMask = 5
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_CREATED_AT                    CardTrackingRequestFieldMask = 6
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_UPDATED_AT                    CardTrackingRequestFieldMask = 7
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_FETCH_AWB_RETRY_COUNT         CardTrackingRequestFieldMask = 8
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_REGISTER_SHIPMENT_RETRY_COUNT CardTrackingRequestFieldMask = 9
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_PICKUP_DATE                   CardTrackingRequestFieldMask = 10
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_UPLOADED_AT                   CardTrackingRequestFieldMask = 11
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_PRINTING_VENDOR               CardTrackingRequestFieldMask = 12
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_ID                            CardTrackingRequestFieldMask = 13
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_CARD_ID                       CardTrackingRequestFieldMask = 14
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_ORDER_ID                      CardTrackingRequestFieldMask = 15
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_DELETED_AT                    CardTrackingRequestFieldMask = 16
	CardTrackingRequestFieldMask_CARD_TRACKING_REQUEST_TRACKING_METADATA             CardTrackingRequestFieldMask = 17
)

// Enum value maps for CardTrackingRequestFieldMask.
var (
	CardTrackingRequestFieldMask_name = map[int32]string{
		0:  "CARD_TRACKING_REQUEST_FIELD_MASK_UNSPECIFIED",
		1:  "CARD_TRACKING_REQUEST_AWB",
		2:  "CARD_TRACKING_REQUEST_CARRIER",
		3:  "CARD_TRACKING_REQUEST_REQUEST_STATE",
		4:  "CARD_TRACKING_REQUEST_DELIVERY_STATE",
		5:  "CARD_TRACKING_REQUEST_SCANS",
		6:  "CARD_TRACKING_REQUEST_CREATED_AT",
		7:  "CARD_TRACKING_REQUEST_UPDATED_AT",
		8:  "CARD_TRACKING_REQUEST_FETCH_AWB_RETRY_COUNT",
		9:  "CARD_TRACKING_REQUEST_REGISTER_SHIPMENT_RETRY_COUNT",
		10: "CARD_TRACKING_REQUEST_PICKUP_DATE",
		11: "CARD_TRACKING_REQUEST_UPLOADED_AT",
		12: "CARD_TRACKING_REQUEST_PRINTING_VENDOR",
		13: "CARD_TRACKING_REQUEST_ID",
		14: "CARD_TRACKING_REQUEST_CARD_ID",
		15: "CARD_TRACKING_REQUEST_ORDER_ID",
		16: "CARD_TRACKING_REQUEST_DELETED_AT",
		17: "CARD_TRACKING_REQUEST_TRACKING_METADATA",
	}
	CardTrackingRequestFieldMask_value = map[string]int32{
		"CARD_TRACKING_REQUEST_FIELD_MASK_UNSPECIFIED":        0,
		"CARD_TRACKING_REQUEST_AWB":                           1,
		"CARD_TRACKING_REQUEST_CARRIER":                       2,
		"CARD_TRACKING_REQUEST_REQUEST_STATE":                 3,
		"CARD_TRACKING_REQUEST_DELIVERY_STATE":                4,
		"CARD_TRACKING_REQUEST_SCANS":                         5,
		"CARD_TRACKING_REQUEST_CREATED_AT":                    6,
		"CARD_TRACKING_REQUEST_UPDATED_AT":                    7,
		"CARD_TRACKING_REQUEST_FETCH_AWB_RETRY_COUNT":         8,
		"CARD_TRACKING_REQUEST_REGISTER_SHIPMENT_RETRY_COUNT": 9,
		"CARD_TRACKING_REQUEST_PICKUP_DATE":                   10,
		"CARD_TRACKING_REQUEST_UPLOADED_AT":                   11,
		"CARD_TRACKING_REQUEST_PRINTING_VENDOR":               12,
		"CARD_TRACKING_REQUEST_ID":                            13,
		"CARD_TRACKING_REQUEST_CARD_ID":                       14,
		"CARD_TRACKING_REQUEST_ORDER_ID":                      15,
		"CARD_TRACKING_REQUEST_DELETED_AT":                    16,
		"CARD_TRACKING_REQUEST_TRACKING_METADATA":             17,
	}
)

func (x CardTrackingRequestFieldMask) Enum() *CardTrackingRequestFieldMask {
	p := new(CardTrackingRequestFieldMask)
	*p = x
	return p
}

func (x CardTrackingRequestFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardTrackingRequestFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_card_tracking_request_proto_enumTypes[2].Descriptor()
}

func (CardTrackingRequestFieldMask) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_card_tracking_request_proto_enumTypes[2]
}

func (x CardTrackingRequestFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardTrackingRequestFieldMask.Descriptor instead.
func (CardTrackingRequestFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_tracking_request_proto_rawDescGZIP(), []int{2}
}

// Vendor printing the card
type CardPrintingVendor int32

const (
	CardPrintingVendor_CARD_PRINTING_VENDOR_UNSPECIFIED CardPrintingVendor = 0
	CardPrintingVendor_SESHAASAI                        CardPrintingVendor = 1
	CardPrintingVendor_MCT                              CardPrintingVendor = 2
)

// Enum value maps for CardPrintingVendor.
var (
	CardPrintingVendor_name = map[int32]string{
		0: "CARD_PRINTING_VENDOR_UNSPECIFIED",
		1: "SESHAASAI",
		2: "MCT",
	}
	CardPrintingVendor_value = map[string]int32{
		"CARD_PRINTING_VENDOR_UNSPECIFIED": 0,
		"SESHAASAI":                        1,
		"MCT":                              2,
	}
)

func (x CardPrintingVendor) Enum() *CardPrintingVendor {
	p := new(CardPrintingVendor)
	*p = x
	return p
}

func (x CardPrintingVendor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardPrintingVendor) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_card_tracking_request_proto_enumTypes[3].Descriptor()
}

func (CardPrintingVendor) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_card_tracking_request_proto_enumTypes[3]
}

func (x CardPrintingVendor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardPrintingVendor.Descriptor instead.
func (CardPrintingVendor) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_tracking_request_proto_rawDescGZIP(), []int{3}
}

type CardTrackingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Primary identifier to a card tracking request model.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// unique identifier for each card
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// Unique identifier for a each tracking request at Shipway
	OrderId string `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// awb number of the tracking request
	Awb string `protobuf:"bytes,4,opt,name=awb,proto3" json:"awb,omitempty"`
	// carrier partner of the tracking request (DELHIVERY, BLUEDART etc)
	Carrier string `protobuf:"bytes,5,opt,name=carrier,proto3" json:"carrier,omitempty"`
	// state of the tracking request
	RequestState CardTrackingRequestState `protobuf:"varint,6,opt,name=request_state,json=requestState,proto3,enum=card.provisioning.CardTrackingRequestState" json:"request_state,omitempty"`
	// delivery state of the shipment
	DeliveryState CardTrackingDeliveryState `protobuf:"varint,7,opt,name=delivery_state,json=deliveryState,proto3,enum=card.provisioning.CardTrackingDeliveryState" json:"delivery_state,omitempty"`
	// A package is scanned multiple times throughout the journey. On every scan,
	// there is an update in tracking information. Storing information for all the scans.
	Scans                      []*Scan                `protobuf:"bytes,8,rep,name=scans,proto3" json:"scans,omitempty"`
	FetchAwbRetryCount         int32                  `protobuf:"varint,9,opt,name=fetch_awb_retry_count,json=fetchAwbRetryCount,proto3" json:"fetch_awb_retry_count,omitempty"`
	RegisterShipmentRetryCount int32                  `protobuf:"varint,10,opt,name=register_shipment_retry_count,json=registerShipmentRetryCount,proto3" json:"register_shipment_retry_count,omitempty"`
	CreatedAt                  *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt                  *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt                  *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// Date at which shipment was picked up
	PickupDate *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=pickup_date,json=pickupDate,proto3" json:"pickup_date,omitempty"`
	// time at which tracking details such as awb and courier partner are received
	UploadedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=uploaded_at,json=uploadedAt,proto3" json:"uploaded_at,omitempty"`
	// Vendor printing the card
	PrintingVendor CardPrintingVendor `protobuf:"varint,16,opt,name=printing_vendor,json=printingVendor,proto3,enum=card.provisioning.CardPrintingVendor" json:"printing_vendor,omitempty"`
	// store details related to delivery tracking
	TrackingMetadata *TrackingMetadata `protobuf:"bytes,17,opt,name=tracking_metadata,json=trackingMetadata,proto3" json:"tracking_metadata,omitempty"`
}

func (x *CardTrackingRequest) Reset() {
	*x = CardTrackingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_tracking_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardTrackingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardTrackingRequest) ProtoMessage() {}

func (x *CardTrackingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_tracking_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardTrackingRequest.ProtoReflect.Descriptor instead.
func (*CardTrackingRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_tracking_request_proto_rawDescGZIP(), []int{0}
}

func (x *CardTrackingRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CardTrackingRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CardTrackingRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *CardTrackingRequest) GetAwb() string {
	if x != nil {
		return x.Awb
	}
	return ""
}

func (x *CardTrackingRequest) GetCarrier() string {
	if x != nil {
		return x.Carrier
	}
	return ""
}

func (x *CardTrackingRequest) GetRequestState() CardTrackingRequestState {
	if x != nil {
		return x.RequestState
	}
	return CardTrackingRequestState_CARD_TRACKING_REQUEST_STATE_UNSPECIFIED
}

func (x *CardTrackingRequest) GetDeliveryState() CardTrackingDeliveryState {
	if x != nil {
		return x.DeliveryState
	}
	return CardTrackingDeliveryState_CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED
}

func (x *CardTrackingRequest) GetScans() []*Scan {
	if x != nil {
		return x.Scans
	}
	return nil
}

func (x *CardTrackingRequest) GetFetchAwbRetryCount() int32 {
	if x != nil {
		return x.FetchAwbRetryCount
	}
	return 0
}

func (x *CardTrackingRequest) GetRegisterShipmentRetryCount() int32 {
	if x != nil {
		return x.RegisterShipmentRetryCount
	}
	return 0
}

func (x *CardTrackingRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CardTrackingRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CardTrackingRequest) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *CardTrackingRequest) GetPickupDate() *timestamppb.Timestamp {
	if x != nil {
		return x.PickupDate
	}
	return nil
}

func (x *CardTrackingRequest) GetUploadedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UploadedAt
	}
	return nil
}

func (x *CardTrackingRequest) GetPrintingVendor() CardPrintingVendor {
	if x != nil {
		return x.PrintingVendor
	}
	return CardPrintingVendor_CARD_PRINTING_VENDOR_UNSPECIFIED
}

func (x *CardTrackingRequest) GetTrackingMetadata() *TrackingMetadata {
	if x != nil {
		return x.TrackingMetadata
	}
	return nil
}

type TrackingMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// delivery tracking wf orch id
	ClientReqId string `protobuf:"bytes,1,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// time at which card is expected to be delivered
	ExpectedDeliveryDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=expected_delivery_date,json=expectedDeliveryDate,proto3" json:"expected_delivery_date,omitempty"`
}

func (x *TrackingMetadata) Reset() {
	*x = TrackingMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_tracking_request_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackingMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackingMetadata) ProtoMessage() {}

func (x *TrackingMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_tracking_request_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackingMetadata.ProtoReflect.Descriptor instead.
func (*TrackingMetadata) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_tracking_request_proto_rawDescGZIP(), []int{1}
}

func (x *TrackingMetadata) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *TrackingMetadata) GetExpectedDeliveryDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpectedDeliveryDate
	}
	return nil
}

type Scan struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Location          string                    `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	UpdatedAt         *timestamppb.Timestamp    `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	StatusDescription string                    `protobuf:"bytes,3,opt,name=status_description,json=statusDescription,proto3" json:"status_description,omitempty"`
	DeliveryState     CardTrackingDeliveryState `protobuf:"varint,4,opt,name=delivery_state,json=deliveryState,proto3,enum=card.provisioning.CardTrackingDeliveryState" json:"delivery_state,omitempty"`
}

func (x *Scan) Reset() {
	*x = Scan{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_tracking_request_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Scan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scan) ProtoMessage() {}

func (x *Scan) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_tracking_request_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scan.ProtoReflect.Descriptor instead.
func (*Scan) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_tracking_request_proto_rawDescGZIP(), []int{2}
}

func (x *Scan) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *Scan) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Scan) GetStatusDescription() string {
	if x != nil {
		return x.StatusDescription
	}
	return ""
}

func (x *Scan) GetDeliveryState() CardTrackingDeliveryState {
	if x != nil {
		return x.DeliveryState
	}
	return CardTrackingDeliveryState_CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED
}

var File_api_card_provisioning_card_tracking_request_proto protoreflect.FileDescriptor

var file_api_card_provisioning_card_tracking_request_proto_rawDesc = []byte{
	0x0a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9e, 0x07, 0x0a, 0x13, 0x43, 0x61, 0x72, 0x64,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x77, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x61, 0x77, 0x62, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12,
	0x50, 0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x53, 0x0a, 0x0e, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x0a, 0x05, 0x73, 0x63, 0x61, 0x6e, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x05,
	0x73, 0x63, 0x61, 0x6e, 0x73, 0x12, 0x31, 0x0a, 0x15, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x61,
	0x77, 0x62, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x66, 0x65, 0x74, 0x63, 0x68, 0x41, 0x77, 0x62, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x41, 0x0a, 0x1d, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x5f, 0x73, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x1a, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0b,
	0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x70,
	0x69, 0x63, 0x6b, 0x75, 0x70, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x4e, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x25, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x50, 0x72, 0x69, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x50, 0x0a, 0x11, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x10, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x88, 0x01, 0x0a, 0x10, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a,
	0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49,
	0x64, 0x12, 0x50, 0x0a, 0x16, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x65,
	0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x44,
	0x61, 0x74, 0x65, 0x22, 0xe1, 0x01, 0x0a, 0x04, 0x53, 0x63, 0x61, 0x6e, 0x12, 0x1a, 0x0a, 0x08,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x2d, 0x0a, 0x12, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x53, 0x0a, 0x0e, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43,
	0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2a, 0x89, 0x01, 0x0a, 0x18, 0x43, 0x61, 0x72, 0x64,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41,
	0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1d, 0x0a, 0x19, 0x46, 0x45, 0x54, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x01,
	0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x41, 0x54, 0x5f, 0x53, 0x48, 0x49, 0x50, 0x57, 0x41,
	0x59, 0x10, 0x02, 0x2a, 0xa3, 0x01, 0x0a, 0x19, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49,
	0x4e, 0x47, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x53, 0x48, 0x49, 0x50, 0x50, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a,
	0x49, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x54, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10,
	0x4f, 0x55, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59,
	0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10,
	0x04, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x45, 0x44, 0x5f, 0x54, 0x4f,
	0x5f, 0x4f, 0x52, 0x49, 0x47, 0x49, 0x4e, 0x10, 0x05, 0x2a, 0xed, 0x05, 0x0a, 0x1c, 0x43, 0x61,
	0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x41, 0x57, 0x42, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x52, 0x49, 0x45, 0x52, 0x10, 0x02, 0x12, 0x27,
	0x0a, 0x23, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x03, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10,
	0x04, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49,
	0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x43, 0x41, 0x4e, 0x53,
	0x10, 0x05, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b,
	0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x06, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x07, 0x12, 0x2f,
	0x0a, 0x2b, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x41, 0x57,
	0x42, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x08, 0x12,
	0x37, 0x0a, 0x33, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45,
	0x52, 0x5f, 0x53, 0x48, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x59,
	0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x09, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x50, 0x49, 0x43, 0x4b, 0x55, 0x50, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x0a, 0x12,
	0x25, 0x0a, 0x21, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x0b, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54,
	0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x50, 0x52, 0x49, 0x4e, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10,
	0x0c, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49,
	0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x0d, 0x12,
	0x21, 0x0a, 0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x44,
	0x10, 0x0e, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b,
	0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x5f, 0x49, 0x44, 0x10, 0x0f, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54,
	0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x10, 0x12, 0x2b, 0x0a, 0x27,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x4d,
	0x45, 0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x10, 0x11, 0x2a, 0x52, 0x0a, 0x12, 0x43, 0x61, 0x72,
	0x64, 0x50, 0x72, 0x69, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12,
	0x24, 0x0a, 0x20, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x49, 0x4e, 0x54, 0x49, 0x4e, 0x47,
	0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x45, 0x53, 0x48, 0x41, 0x41, 0x53,
	0x41, 0x49, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x4d, 0x43, 0x54, 0x10, 0x02, 0x42, 0x5c, 0x0a,
	0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x5a, 0x2c, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_card_provisioning_card_tracking_request_proto_rawDescOnce sync.Once
	file_api_card_provisioning_card_tracking_request_proto_rawDescData = file_api_card_provisioning_card_tracking_request_proto_rawDesc
)

func file_api_card_provisioning_card_tracking_request_proto_rawDescGZIP() []byte {
	file_api_card_provisioning_card_tracking_request_proto_rawDescOnce.Do(func() {
		file_api_card_provisioning_card_tracking_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_provisioning_card_tracking_request_proto_rawDescData)
	})
	return file_api_card_provisioning_card_tracking_request_proto_rawDescData
}

var file_api_card_provisioning_card_tracking_request_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_card_provisioning_card_tracking_request_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_card_provisioning_card_tracking_request_proto_goTypes = []interface{}{
	(CardTrackingRequestState)(0),     // 0: card.provisioning.CardTrackingRequestState
	(CardTrackingDeliveryState)(0),    // 1: card.provisioning.CardTrackingDeliveryState
	(CardTrackingRequestFieldMask)(0), // 2: card.provisioning.CardTrackingRequestFieldMask
	(CardPrintingVendor)(0),           // 3: card.provisioning.CardPrintingVendor
	(*CardTrackingRequest)(nil),       // 4: card.provisioning.CardTrackingRequest
	(*TrackingMetadata)(nil),          // 5: card.provisioning.TrackingMetadata
	(*Scan)(nil),                      // 6: card.provisioning.Scan
	(*timestamppb.Timestamp)(nil),     // 7: google.protobuf.Timestamp
}
var file_api_card_provisioning_card_tracking_request_proto_depIdxs = []int32{
	0,  // 0: card.provisioning.CardTrackingRequest.request_state:type_name -> card.provisioning.CardTrackingRequestState
	1,  // 1: card.provisioning.CardTrackingRequest.delivery_state:type_name -> card.provisioning.CardTrackingDeliveryState
	6,  // 2: card.provisioning.CardTrackingRequest.scans:type_name -> card.provisioning.Scan
	7,  // 3: card.provisioning.CardTrackingRequest.created_at:type_name -> google.protobuf.Timestamp
	7,  // 4: card.provisioning.CardTrackingRequest.updated_at:type_name -> google.protobuf.Timestamp
	7,  // 5: card.provisioning.CardTrackingRequest.deleted_at:type_name -> google.protobuf.Timestamp
	7,  // 6: card.provisioning.CardTrackingRequest.pickup_date:type_name -> google.protobuf.Timestamp
	7,  // 7: card.provisioning.CardTrackingRequest.uploaded_at:type_name -> google.protobuf.Timestamp
	3,  // 8: card.provisioning.CardTrackingRequest.printing_vendor:type_name -> card.provisioning.CardPrintingVendor
	5,  // 9: card.provisioning.CardTrackingRequest.tracking_metadata:type_name -> card.provisioning.TrackingMetadata
	7,  // 10: card.provisioning.TrackingMetadata.expected_delivery_date:type_name -> google.protobuf.Timestamp
	7,  // 11: card.provisioning.Scan.updated_at:type_name -> google.protobuf.Timestamp
	1,  // 12: card.provisioning.Scan.delivery_state:type_name -> card.provisioning.CardTrackingDeliveryState
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_api_card_provisioning_card_tracking_request_proto_init() }
func file_api_card_provisioning_card_tracking_request_proto_init() {
	if File_api_card_provisioning_card_tracking_request_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_provisioning_card_tracking_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardTrackingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_tracking_request_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackingMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_tracking_request_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Scan); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_provisioning_card_tracking_request_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_provisioning_card_tracking_request_proto_goTypes,
		DependencyIndexes: file_api_card_provisioning_card_tracking_request_proto_depIdxs,
		EnumInfos:         file_api_card_provisioning_card_tracking_request_proto_enumTypes,
		MessageInfos:      file_api_card_provisioning_card_tracking_request_proto_msgTypes,
	}.Build()
	File_api_card_provisioning_card_tracking_request_proto = out.File
	file_api_card_provisioning_card_tracking_request_proto_rawDesc = nil
	file_api_card_provisioning_card_tracking_request_proto_goTypes = nil
	file_api_card_provisioning_card_tracking_request_proto_depIdxs = nil
}
