// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/provisioning/service.proto

package provisioning

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	card "github.com/epifi/gamma/api/card"

	common "github.com/epifi/be-common/api/typesv2/common"

	enums "github.com/epifi/gamma/api/card/enums"

	external "github.com/epifi/gamma/api/tiering/external"

	typesv2 "github.com/epifi/gamma/api/typesv2"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = card.CardAction(0)

	_ = common.UserGroup(0)

	_ = enums.RefundStatus(0)

	_ = external.Tier(0)

	_ = typesv2.AddressType(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on GetCardSwitchNotificationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCardSwitchNotificationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardSwitchNotificationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCardSwitchNotificationRequestMultiError, or nil if none found.
func (m *GetCardSwitchNotificationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardSwitchNotificationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *GetCardSwitchNotificationRequest_Rrn:
		if v == nil {
			err := GetCardSwitchNotificationRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Rrn
	case *GetCardSwitchNotificationRequest_ExternalRefId:
		if v == nil {
			err := GetCardSwitchNotificationRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ExternalRefId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetCardSwitchNotificationRequestMultiError(errors)
	}

	return nil
}

// GetCardSwitchNotificationRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetCardSwitchNotificationRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCardSwitchNotificationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardSwitchNotificationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardSwitchNotificationRequestMultiError) AllErrors() []error { return m }

// GetCardSwitchNotificationRequestValidationError is the validation error
// returned by GetCardSwitchNotificationRequest.Validate if the designated
// constraints aren't met.
type GetCardSwitchNotificationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardSwitchNotificationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardSwitchNotificationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardSwitchNotificationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardSwitchNotificationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardSwitchNotificationRequestValidationError) ErrorName() string {
	return "GetCardSwitchNotificationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardSwitchNotificationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardSwitchNotificationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardSwitchNotificationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardSwitchNotificationRequestValidationError{}

// Validate checks the field values on GetCardSwitchNotificationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCardSwitchNotificationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardSwitchNotificationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCardSwitchNotificationResponseMultiError, or nil if none found.
func (m *GetCardSwitchNotificationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardSwitchNotificationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardSwitchNotificationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardSwitchNotificationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardSwitchNotificationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardNotification()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardSwitchNotificationResponseValidationError{
					field:  "CardNotification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardSwitchNotificationResponseValidationError{
					field:  "CardNotification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardNotification()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardSwitchNotificationResponseValidationError{
				field:  "CardNotification",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCardSwitchNotificationResponseMultiError(errors)
	}

	return nil
}

// GetCardSwitchNotificationResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetCardSwitchNotificationResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCardSwitchNotificationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardSwitchNotificationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardSwitchNotificationResponseMultiError) AllErrors() []error { return m }

// GetCardSwitchNotificationResponseValidationError is the validation error
// returned by GetCardSwitchNotificationResponse.Validate if the designated
// constraints aren't met.
type GetCardSwitchNotificationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardSwitchNotificationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardSwitchNotificationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardSwitchNotificationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardSwitchNotificationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardSwitchNotificationResponseValidationError) ErrorName() string {
	return "GetCardSwitchNotificationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardSwitchNotificationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardSwitchNotificationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardSwitchNotificationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardSwitchNotificationResponseValidationError{}

// Validate checks the field values on GetHomeLayoutConfigurationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetHomeLayoutConfigurationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetHomeLayoutConfigurationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetHomeLayoutConfigurationRequestMultiError, or nil if none found.
func (m *GetHomeLayoutConfigurationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetHomeLayoutConfigurationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetHomeLayoutConfigurationRequestMultiError(errors)
	}

	return nil
}

// GetHomeLayoutConfigurationRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetHomeLayoutConfigurationRequest.ValidateAll() if the designated
// constraints aren't met.
type GetHomeLayoutConfigurationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetHomeLayoutConfigurationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetHomeLayoutConfigurationRequestMultiError) AllErrors() []error { return m }

// GetHomeLayoutConfigurationRequestValidationError is the validation error
// returned by GetHomeLayoutConfigurationRequest.Validate if the designated
// constraints aren't met.
type GetHomeLayoutConfigurationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetHomeLayoutConfigurationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetHomeLayoutConfigurationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetHomeLayoutConfigurationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetHomeLayoutConfigurationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetHomeLayoutConfigurationRequestValidationError) ErrorName() string {
	return "GetHomeLayoutConfigurationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetHomeLayoutConfigurationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetHomeLayoutConfigurationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetHomeLayoutConfigurationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetHomeLayoutConfigurationRequestValidationError{}

// Validate checks the field values on GetHomeLayoutConfigurationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetHomeLayoutConfigurationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetHomeLayoutConfigurationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetHomeLayoutConfigurationResponseMultiError, or nil if none found.
func (m *GetHomeLayoutConfigurationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetHomeLayoutConfigurationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHomeLayoutConfigurationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHomeLayoutConfigurationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHomeLayoutConfigurationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LayoutId

	if all {
		switch v := interface{}(m.GetCardUsageData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHomeLayoutConfigurationResponseValidationError{
					field:  "CardUsageData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHomeLayoutConfigurationResponseValidationError{
					field:  "CardUsageData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardUsageData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHomeLayoutConfigurationResponseValidationError{
				field:  "CardUsageData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetHomeLayoutConfigurationResponseMultiError(errors)
	}

	return nil
}

// GetHomeLayoutConfigurationResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetHomeLayoutConfigurationResponse.ValidateAll() if the designated
// constraints aren't met.
type GetHomeLayoutConfigurationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetHomeLayoutConfigurationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetHomeLayoutConfigurationResponseMultiError) AllErrors() []error { return m }

// GetHomeLayoutConfigurationResponseValidationError is the validation error
// returned by GetHomeLayoutConfigurationResponse.Validate if the designated
// constraints aren't met.
type GetHomeLayoutConfigurationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetHomeLayoutConfigurationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetHomeLayoutConfigurationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetHomeLayoutConfigurationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetHomeLayoutConfigurationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetHomeLayoutConfigurationResponseValidationError) ErrorName() string {
	return "GetHomeLayoutConfigurationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetHomeLayoutConfigurationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetHomeLayoutConfigurationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetHomeLayoutConfigurationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetHomeLayoutConfigurationResponseValidationError{}

// Validate checks the field values on UserCardData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserCardData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserCardData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserCardDataMultiError, or
// nil if none found.
func (m *UserCardData) ValidateAll() error {
	return m.validate(true)
}

func (m *UserCardData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsTravelModeOn

	for idx, item := range m.GetCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserCardDataValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserCardDataValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserCardDataValidationError{
					field:  fmt.Sprintf("Cards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCurrentCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserCardDataValidationError{
					field:  "CurrentCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserCardDataValidationError{
					field:  "CurrentCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserCardDataValidationError{
				field:  "CurrentCard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CurrentTier

	if all {
		switch v := interface{}(m.GetLatestPhysicalDispatchRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserCardDataValidationError{
					field:  "LatestPhysicalDispatchRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserCardDataValidationError{
					field:  "LatestPhysicalDispatchRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatestPhysicalDispatchRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserCardDataValidationError{
				field:  "LatestPhysicalDispatchRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardDeliveryTrackingInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserCardDataValidationError{
					field:  "CardDeliveryTrackingInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserCardDataValidationError{
					field:  "CardDeliveryTrackingInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardDeliveryTrackingInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserCardDataValidationError{
				field:  "CardDeliveryTrackingInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserCardDataMultiError(errors)
	}

	return nil
}

// UserCardDataMultiError is an error wrapping multiple validation errors
// returned by UserCardData.ValidateAll() if the designated constraints aren't met.
type UserCardDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserCardDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserCardDataMultiError) AllErrors() []error { return m }

// UserCardDataValidationError is the validation error returned by
// UserCardData.Validate if the designated constraints aren't met.
type UserCardDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserCardDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserCardDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserCardDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserCardDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserCardDataValidationError) ErrorName() string { return "UserCardDataValidationError" }

// Error satisfies the builtin error interface
func (e UserCardDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserCardData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserCardDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserCardDataValidationError{}

// Validate checks the field values on FetchForexRefundAggregatesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FetchForexRefundAggregatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchForexRefundAggregatesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FetchForexRefundAggregatesRequestMultiError, or nil if none found.
func (m *FetchForexRefundAggregatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchForexRefundAggregatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if m.GetStartTime() == nil {
		err := FetchForexRefundAggregatesRequestValidationError{
			field:  "StartTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetEndTime() == nil {
		err := FetchForexRefundAggregatesRequestValidationError{
			field:  "EndTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FetchForexRefundAggregatesRequestMultiError(errors)
	}

	return nil
}

// FetchForexRefundAggregatesRequestMultiError is an error wrapping multiple
// validation errors returned by
// FetchForexRefundAggregatesRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchForexRefundAggregatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchForexRefundAggregatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchForexRefundAggregatesRequestMultiError) AllErrors() []error { return m }

// FetchForexRefundAggregatesRequestValidationError is the validation error
// returned by FetchForexRefundAggregatesRequest.Validate if the designated
// constraints aren't met.
type FetchForexRefundAggregatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchForexRefundAggregatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchForexRefundAggregatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchForexRefundAggregatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchForexRefundAggregatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchForexRefundAggregatesRequestValidationError) ErrorName() string {
	return "FetchForexRefundAggregatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchForexRefundAggregatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchForexRefundAggregatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchForexRefundAggregatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchForexRefundAggregatesRequestValidationError{}

// Validate checks the field values on FetchForexRefundAggregatesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FetchForexRefundAggregatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchForexRefundAggregatesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FetchForexRefundAggregatesResponseMultiError, or nil if none found.
func (m *FetchForexRefundAggregatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchForexRefundAggregatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchForexRefundAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchForexRefundAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchForexRefundAggregatesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetRefundAggregates()))
		i := 0
		for key := range m.GetRefundAggregates() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetRefundAggregates()[key]
			_ = val

			// no validation rules for RefundAggregates[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, FetchForexRefundAggregatesResponseValidationError{
							field:  fmt.Sprintf("RefundAggregates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, FetchForexRefundAggregatesResponseValidationError{
							field:  fmt.Sprintf("RefundAggregates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return FetchForexRefundAggregatesResponseValidationError{
						field:  fmt.Sprintf("RefundAggregates[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return FetchForexRefundAggregatesResponseMultiError(errors)
	}

	return nil
}

// FetchForexRefundAggregatesResponseMultiError is an error wrapping multiple
// validation errors returned by
// FetchForexRefundAggregatesResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchForexRefundAggregatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchForexRefundAggregatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchForexRefundAggregatesResponseMultiError) AllErrors() []error { return m }

// FetchForexRefundAggregatesResponseValidationError is the validation error
// returned by FetchForexRefundAggregatesResponse.Validate if the designated
// constraints aren't met.
type FetchForexRefundAggregatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchForexRefundAggregatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchForexRefundAggregatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchForexRefundAggregatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchForexRefundAggregatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchForexRefundAggregatesResponseValidationError) ErrorName() string {
	return "FetchForexRefundAggregatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchForexRefundAggregatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchForexRefundAggregatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchForexRefundAggregatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchForexRefundAggregatesResponseValidationError{}

// Validate checks the field values on FetchCardRenewalChargesForUserRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *FetchCardRenewalChargesForUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardRenewalChargesForUserRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FetchCardRenewalChargesForUserRequestMultiError, or nil if none found.
func (m *FetchCardRenewalChargesForUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardRenewalChargesForUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return FetchCardRenewalChargesForUserRequestMultiError(errors)
	}

	return nil
}

// FetchCardRenewalChargesForUserRequestMultiError is an error wrapping
// multiple validation errors returned by
// FetchCardRenewalChargesForUserRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchCardRenewalChargesForUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardRenewalChargesForUserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardRenewalChargesForUserRequestMultiError) AllErrors() []error { return m }

// FetchCardRenewalChargesForUserRequestValidationError is the validation error
// returned by FetchCardRenewalChargesForUserRequest.Validate if the
// designated constraints aren't met.
type FetchCardRenewalChargesForUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardRenewalChargesForUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardRenewalChargesForUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardRenewalChargesForUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardRenewalChargesForUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardRenewalChargesForUserRequestValidationError) ErrorName() string {
	return "FetchCardRenewalChargesForUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardRenewalChargesForUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardRenewalChargesForUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardRenewalChargesForUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardRenewalChargesForUserRequestValidationError{}

// Validate checks the field values on FetchCardRenewalChargesForUserResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *FetchCardRenewalChargesForUserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FetchCardRenewalChargesForUserResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// FetchCardRenewalChargesForUserResponseMultiError, or nil if none found.
func (m *FetchCardRenewalChargesForUserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardRenewalChargesForUserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCardRenewalChargesForUserResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCardRenewalChargesForUserResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCardRenewalChargesForUserResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCardRenewalChargesForUserResponseValidationError{
					field:  "TotalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCardRenewalChargesForUserResponseValidationError{
					field:  "TotalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCardRenewalChargesForUserResponseValidationError{
				field:  "TotalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmountWithoutGst()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCardRenewalChargesForUserResponseValidationError{
					field:  "AmountWithoutGst",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCardRenewalChargesForUserResponseValidationError{
					field:  "AmountWithoutGst",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountWithoutGst()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCardRenewalChargesForUserResponseValidationError{
				field:  "AmountWithoutGst",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchCardRenewalChargesForUserResponseMultiError(errors)
	}

	return nil
}

// FetchCardRenewalChargesForUserResponseMultiError is an error wrapping
// multiple validation errors returned by
// FetchCardRenewalChargesForUserResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchCardRenewalChargesForUserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardRenewalChargesForUserResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardRenewalChargesForUserResponseMultiError) AllErrors() []error { return m }

// FetchCardRenewalChargesForUserResponseValidationError is the validation
// error returned by FetchCardRenewalChargesForUserResponse.Validate if the
// designated constraints aren't met.
type FetchCardRenewalChargesForUserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardRenewalChargesForUserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardRenewalChargesForUserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardRenewalChargesForUserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardRenewalChargesForUserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardRenewalChargesForUserResponseValidationError) ErrorName() string {
	return "FetchCardRenewalChargesForUserResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardRenewalChargesForUserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardRenewalChargesForUserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardRenewalChargesForUserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardRenewalChargesForUserResponseValidationError{}

// Validate checks the field values on GetForexRefundsByActorIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetForexRefundsByActorIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetForexRefundsByActorIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetForexRefundsByActorIdRequestMultiError, or nil if none found.
func (m *GetForexRefundsByActorIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetForexRefundsByActorIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetForexRefundsByActorIdRequestMultiError(errors)
	}

	return nil
}

// GetForexRefundsByActorIdRequestMultiError is an error wrapping multiple
// validation errors returned by GetForexRefundsByActorIdRequest.ValidateAll()
// if the designated constraints aren't met.
type GetForexRefundsByActorIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetForexRefundsByActorIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetForexRefundsByActorIdRequestMultiError) AllErrors() []error { return m }

// GetForexRefundsByActorIdRequestValidationError is the validation error
// returned by GetForexRefundsByActorIdRequest.Validate if the designated
// constraints aren't met.
type GetForexRefundsByActorIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetForexRefundsByActorIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetForexRefundsByActorIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetForexRefundsByActorIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetForexRefundsByActorIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetForexRefundsByActorIdRequestValidationError) ErrorName() string {
	return "GetForexRefundsByActorIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetForexRefundsByActorIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetForexRefundsByActorIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetForexRefundsByActorIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetForexRefundsByActorIdRequestValidationError{}

// Validate checks the field values on GetForexRefundsByActorIdResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetForexRefundsByActorIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetForexRefundsByActorIdResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetForexRefundsByActorIdResponseMultiError, or nil if none found.
func (m *GetForexRefundsByActorIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetForexRefundsByActorIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForexRefundsByActorIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForexRefundsByActorIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForexRefundsByActorIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRefunds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetForexRefundsByActorIdResponseValidationError{
						field:  fmt.Sprintf("Refunds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetForexRefundsByActorIdResponseValidationError{
						field:  fmt.Sprintf("Refunds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetForexRefundsByActorIdResponseValidationError{
					field:  fmt.Sprintf("Refunds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetForexRefundsByActorIdResponseMultiError(errors)
	}

	return nil
}

// GetForexRefundsByActorIdResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetForexRefundsByActorIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetForexRefundsByActorIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetForexRefundsByActorIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetForexRefundsByActorIdResponseMultiError) AllErrors() []error { return m }

// GetForexRefundsByActorIdResponseValidationError is the validation error
// returned by GetForexRefundsByActorIdResponse.Validate if the designated
// constraints aren't met.
type GetForexRefundsByActorIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetForexRefundsByActorIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetForexRefundsByActorIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetForexRefundsByActorIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetForexRefundsByActorIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetForexRefundsByActorIdResponseValidationError) ErrorName() string {
	return "GetForexRefundsByActorIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetForexRefundsByActorIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetForexRefundsByActorIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetForexRefundsByActorIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetForexRefundsByActorIdResponseValidationError{}

// Validate checks the field values on FetchPhysicalCardDispatchRequestsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *FetchPhysicalCardDispatchRequestsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FetchPhysicalCardDispatchRequestsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FetchPhysicalCardDispatchRequestsRequestMultiError, or nil if none found.
func (m *FetchPhysicalCardDispatchRequestsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchPhysicalCardDispatchRequestsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for ActorId

	// no validation rules for Limit

	if all {
		switch v := interface{}(m.GetCardIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchPhysicalCardDispatchRequestsRequestValidationError{
					field:  "CardIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchPhysicalCardDispatchRequestsRequestValidationError{
					field:  "CardIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchPhysicalCardDispatchRequestsRequestValidationError{
				field:  "CardIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchPhysicalCardDispatchRequestsRequestMultiError(errors)
	}

	return nil
}

// FetchPhysicalCardDispatchRequestsRequestMultiError is an error wrapping
// multiple validation errors returned by
// FetchPhysicalCardDispatchRequestsRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchPhysicalCardDispatchRequestsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchPhysicalCardDispatchRequestsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchPhysicalCardDispatchRequestsRequestMultiError) AllErrors() []error { return m }

// FetchPhysicalCardDispatchRequestsRequestValidationError is the validation
// error returned by FetchPhysicalCardDispatchRequestsRequest.Validate if the
// designated constraints aren't met.
type FetchPhysicalCardDispatchRequestsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchPhysicalCardDispatchRequestsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchPhysicalCardDispatchRequestsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchPhysicalCardDispatchRequestsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchPhysicalCardDispatchRequestsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchPhysicalCardDispatchRequestsRequestValidationError) ErrorName() string {
	return "FetchPhysicalCardDispatchRequestsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchPhysicalCardDispatchRequestsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchPhysicalCardDispatchRequestsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchPhysicalCardDispatchRequestsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchPhysicalCardDispatchRequestsRequestValidationError{}

// Validate checks the field values on CardIdentifier with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CardIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardIdentifier with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CardIdentifierMultiError,
// or nil if none found.
func (m *CardIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *CardIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *CardIdentifier_CardId:
		if v == nil {
			err := CardIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for CardId
	case *CardIdentifier_ActorId:
		if v == nil {
			err := CardIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CardIdentifierMultiError(errors)
	}

	return nil
}

// CardIdentifierMultiError is an error wrapping multiple validation errors
// returned by CardIdentifier.ValidateAll() if the designated constraints
// aren't met.
type CardIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardIdentifierMultiError) AllErrors() []error { return m }

// CardIdentifierValidationError is the validation error returned by
// CardIdentifier.Validate if the designated constraints aren't met.
type CardIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardIdentifierValidationError) ErrorName() string { return "CardIdentifierValidationError" }

// Error satisfies the builtin error interface
func (e CardIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardIdentifierValidationError{}

// Validate checks the field values on
// FetchPhysicalCardDispatchRequestsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FetchPhysicalCardDispatchRequestsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FetchPhysicalCardDispatchRequestsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FetchPhysicalCardDispatchRequestsResponseMultiError, or nil if none found.
func (m *FetchPhysicalCardDispatchRequestsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchPhysicalCardDispatchRequestsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchPhysicalCardDispatchRequestsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchPhysicalCardDispatchRequestsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchPhysicalCardDispatchRequestsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPhysicalCardDispatchRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FetchPhysicalCardDispatchRequestsResponseValidationError{
						field:  fmt.Sprintf("PhysicalCardDispatchRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FetchPhysicalCardDispatchRequestsResponseValidationError{
						field:  fmt.Sprintf("PhysicalCardDispatchRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FetchPhysicalCardDispatchRequestsResponseValidationError{
					field:  fmt.Sprintf("PhysicalCardDispatchRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FetchPhysicalCardDispatchRequestsResponseMultiError(errors)
	}

	return nil
}

// FetchPhysicalCardDispatchRequestsResponseMultiError is an error wrapping
// multiple validation errors returned by
// FetchPhysicalCardDispatchRequestsResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchPhysicalCardDispatchRequestsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchPhysicalCardDispatchRequestsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchPhysicalCardDispatchRequestsResponseMultiError) AllErrors() []error { return m }

// FetchPhysicalCardDispatchRequestsResponseValidationError is the validation
// error returned by FetchPhysicalCardDispatchRequestsResponse.Validate if the
// designated constraints aren't met.
type FetchPhysicalCardDispatchRequestsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchPhysicalCardDispatchRequestsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchPhysicalCardDispatchRequestsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchPhysicalCardDispatchRequestsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchPhysicalCardDispatchRequestsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchPhysicalCardDispatchRequestsResponseValidationError) ErrorName() string {
	return "FetchPhysicalCardDispatchRequestsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchPhysicalCardDispatchRequestsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchPhysicalCardDispatchRequestsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchPhysicalCardDispatchRequestsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchPhysicalCardDispatchRequestsResponseValidationError{}

// Validate checks the field values on GetPaginatedForexRefundsByActorIdRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPaginatedForexRefundsByActorIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetPaginatedForexRefundsByActorIdRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetPaginatedForexRefundsByActorIdRequestMultiError, or nil if none found.
func (m *GetPaginatedForexRefundsByActorIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaginatedForexRefundsByActorIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetPageContextRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaginatedForexRefundsByActorIdRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaginatedForexRefundsByActorIdRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaginatedForexRefundsByActorIdRequestValidationError{
				field:  "PageContextRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPaginatedForexRefundsByActorIdRequestMultiError(errors)
	}

	return nil
}

// GetPaginatedForexRefundsByActorIdRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetPaginatedForexRefundsByActorIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPaginatedForexRefundsByActorIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaginatedForexRefundsByActorIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaginatedForexRefundsByActorIdRequestMultiError) AllErrors() []error { return m }

// GetPaginatedForexRefundsByActorIdRequestValidationError is the validation
// error returned by GetPaginatedForexRefundsByActorIdRequest.Validate if the
// designated constraints aren't met.
type GetPaginatedForexRefundsByActorIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaginatedForexRefundsByActorIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaginatedForexRefundsByActorIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaginatedForexRefundsByActorIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaginatedForexRefundsByActorIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaginatedForexRefundsByActorIdRequestValidationError) ErrorName() string {
	return "GetPaginatedForexRefundsByActorIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaginatedForexRefundsByActorIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaginatedForexRefundsByActorIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaginatedForexRefundsByActorIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaginatedForexRefundsByActorIdRequestValidationError{}

// Validate checks the field values on
// GetPaginatedForexRefundsByActorIdResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetPaginatedForexRefundsByActorIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetPaginatedForexRefundsByActorIdResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetPaginatedForexRefundsByActorIdResponseMultiError, or nil if none found.
func (m *GetPaginatedForexRefundsByActorIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaginatedForexRefundsByActorIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaginatedForexRefundsByActorIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaginatedForexRefundsByActorIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaginatedForexRefundsByActorIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRefunds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPaginatedForexRefundsByActorIdResponseValidationError{
						field:  fmt.Sprintf("Refunds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPaginatedForexRefundsByActorIdResponseValidationError{
						field:  fmt.Sprintf("Refunds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPaginatedForexRefundsByActorIdResponseValidationError{
					field:  fmt.Sprintf("Refunds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContextResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaginatedForexRefundsByActorIdResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaginatedForexRefundsByActorIdResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaginatedForexRefundsByActorIdResponseValidationError{
				field:  "PageContextResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPaginatedForexRefundsByActorIdResponseMultiError(errors)
	}

	return nil
}

// GetPaginatedForexRefundsByActorIdResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetPaginatedForexRefundsByActorIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPaginatedForexRefundsByActorIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaginatedForexRefundsByActorIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaginatedForexRefundsByActorIdResponseMultiError) AllErrors() []error { return m }

// GetPaginatedForexRefundsByActorIdResponseValidationError is the validation
// error returned by GetPaginatedForexRefundsByActorIdResponse.Validate if the
// designated constraints aren't met.
type GetPaginatedForexRefundsByActorIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaginatedForexRefundsByActorIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaginatedForexRefundsByActorIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaginatedForexRefundsByActorIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaginatedForexRefundsByActorIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaginatedForexRefundsByActorIdResponseValidationError) ErrorName() string {
	return "GetPaginatedForexRefundsByActorIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaginatedForexRefundsByActorIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaginatedForexRefundsByActorIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaginatedForexRefundsByActorIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaginatedForexRefundsByActorIdResponseValidationError{}

// Validate checks the field values on GetForexRefundsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetForexRefundsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetForexRefundsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetForexRefundsRequestMultiError, or nil if none found.
func (m *GetForexRefundsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetForexRefundsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *GetForexRefundsRequest_Id:
		if v == nil {
			err := GetForexRefundsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Id
	case *GetForexRefundsRequest_TxnId:
		if v == nil {
			err := GetForexRefundsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for TxnId
	case *GetForexRefundsRequest_ActorId:
		if v == nil {
			err := GetForexRefundsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	case *GetForexRefundsRequest_RefundId:
		if v == nil {
			err := GetForexRefundsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for RefundId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetForexRefundsRequestMultiError(errors)
	}

	return nil
}

// GetForexRefundsRequestMultiError is an error wrapping multiple validation
// errors returned by GetForexRefundsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetForexRefundsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetForexRefundsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetForexRefundsRequestMultiError) AllErrors() []error { return m }

// GetForexRefundsRequestValidationError is the validation error returned by
// GetForexRefundsRequest.Validate if the designated constraints aren't met.
type GetForexRefundsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetForexRefundsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetForexRefundsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetForexRefundsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetForexRefundsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetForexRefundsRequestValidationError) ErrorName() string {
	return "GetForexRefundsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetForexRefundsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetForexRefundsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetForexRefundsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetForexRefundsRequestValidationError{}

// Validate checks the field values on GetForexRefundsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetForexRefundsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetForexRefundsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetForexRefundsResponseMultiError, or nil if none found.
func (m *GetForexRefundsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetForexRefundsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForexRefundsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForexRefundsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForexRefundsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRefunds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetForexRefundsResponseValidationError{
						field:  fmt.Sprintf("Refunds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetForexRefundsResponseValidationError{
						field:  fmt.Sprintf("Refunds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetForexRefundsResponseValidationError{
					field:  fmt.Sprintf("Refunds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetForexRefundsResponseMultiError(errors)
	}

	return nil
}

// GetForexRefundsResponseMultiError is an error wrapping multiple validation
// errors returned by GetForexRefundsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetForexRefundsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetForexRefundsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetForexRefundsResponseMultiError) AllErrors() []error { return m }

// GetForexRefundsResponseValidationError is the validation error returned by
// GetForexRefundsResponse.Validate if the designated constraints aren't met.
type GetForexRefundsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetForexRefundsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetForexRefundsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetForexRefundsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetForexRefundsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetForexRefundsResponseValidationError) ErrorName() string {
	return "GetForexRefundsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetForexRefundsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetForexRefundsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetForexRefundsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetForexRefundsResponseValidationError{}

// Validate checks the field values on ActivatePhysicalCardRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActivatePhysicalCardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivatePhysicalCardRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivatePhysicalCardRequestMultiError, or nil if none found.
func (m *ActivatePhysicalCardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivatePhysicalCardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for RequestId

	// no validation rules for CredBlock

	// no validation rules for ActorId

	if len(errors) > 0 {
		return ActivatePhysicalCardRequestMultiError(errors)
	}

	return nil
}

// ActivatePhysicalCardRequestMultiError is an error wrapping multiple
// validation errors returned by ActivatePhysicalCardRequest.ValidateAll() if
// the designated constraints aren't met.
type ActivatePhysicalCardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivatePhysicalCardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivatePhysicalCardRequestMultiError) AllErrors() []error { return m }

// ActivatePhysicalCardRequestValidationError is the validation error returned
// by ActivatePhysicalCardRequest.Validate if the designated constraints
// aren't met.
type ActivatePhysicalCardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivatePhysicalCardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivatePhysicalCardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivatePhysicalCardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivatePhysicalCardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivatePhysicalCardRequestValidationError) ErrorName() string {
	return "ActivatePhysicalCardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ActivatePhysicalCardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivatePhysicalCardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivatePhysicalCardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivatePhysicalCardRequestValidationError{}

// Validate checks the field values on ActivatePhysicalCardResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActivatePhysicalCardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivatePhysicalCardResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivatePhysicalCardResponseMultiError, or nil if none found.
func (m *ActivatePhysicalCardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivatePhysicalCardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivatePhysicalCardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivatePhysicalCardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivatePhysicalCardResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivatePhysicalCardResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivatePhysicalCardResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivatePhysicalCardResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternalResponseCode

	if len(errors) > 0 {
		return ActivatePhysicalCardResponseMultiError(errors)
	}

	return nil
}

// ActivatePhysicalCardResponseMultiError is an error wrapping multiple
// validation errors returned by ActivatePhysicalCardResponse.ValidateAll() if
// the designated constraints aren't met.
type ActivatePhysicalCardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivatePhysicalCardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivatePhysicalCardResponseMultiError) AllErrors() []error { return m }

// ActivatePhysicalCardResponseValidationError is the validation error returned
// by ActivatePhysicalCardResponse.Validate if the designated constraints
// aren't met.
type ActivatePhysicalCardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivatePhysicalCardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivatePhysicalCardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivatePhysicalCardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivatePhysicalCardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivatePhysicalCardResponseValidationError) ErrorName() string {
	return "ActivatePhysicalCardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ActivatePhysicalCardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivatePhysicalCardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivatePhysicalCardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivatePhysicalCardResponseValidationError{}

// Validate checks the field values on FetchPhysicalCardChargesForUserRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *FetchPhysicalCardChargesForUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FetchPhysicalCardChargesForUserRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// FetchPhysicalCardChargesForUserRequestMultiError, or nil if none found.
func (m *FetchPhysicalCardChargesForUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchPhysicalCardChargesForUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetPostSuccessNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchPhysicalCardChargesForUserRequestValidationError{
					field:  "PostSuccessNextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchPhysicalCardChargesForUserRequestValidationError{
					field:  "PostSuccessNextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostSuccessNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchPhysicalCardChargesForUserRequestValidationError{
				field:  "PostSuccessNextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOnSkipNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchPhysicalCardChargesForUserRequestValidationError{
					field:  "OnSkipNextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchPhysicalCardChargesForUserRequestValidationError{
					field:  "OnSkipNextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOnSkipNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchPhysicalCardChargesForUserRequestValidationError{
				field:  "OnSkipNextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntryPoint

	if len(errors) > 0 {
		return FetchPhysicalCardChargesForUserRequestMultiError(errors)
	}

	return nil
}

// FetchPhysicalCardChargesForUserRequestMultiError is an error wrapping
// multiple validation errors returned by
// FetchPhysicalCardChargesForUserRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchPhysicalCardChargesForUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchPhysicalCardChargesForUserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchPhysicalCardChargesForUserRequestMultiError) AllErrors() []error { return m }

// FetchPhysicalCardChargesForUserRequestValidationError is the validation
// error returned by FetchPhysicalCardChargesForUserRequest.Validate if the
// designated constraints aren't met.
type FetchPhysicalCardChargesForUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchPhysicalCardChargesForUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchPhysicalCardChargesForUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchPhysicalCardChargesForUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchPhysicalCardChargesForUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchPhysicalCardChargesForUserRequestValidationError) ErrorName() string {
	return "FetchPhysicalCardChargesForUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchPhysicalCardChargesForUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchPhysicalCardChargesForUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchPhysicalCardChargesForUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchPhysicalCardChargesForUserRequestValidationError{}

// Validate checks the field values on FetchPhysicalCardChargesForUserResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *FetchPhysicalCardChargesForUserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FetchPhysicalCardChargesForUserResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// FetchPhysicalCardChargesForUserResponseMultiError, or nil if none found.
func (m *FetchPhysicalCardChargesForUserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchPhysicalCardChargesForUserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchPhysicalCardChargesForUserResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchPhysicalCardChargesForUserResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchPhysicalCardChargesForUserResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchPhysicalCardChargesForUserResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchPhysicalCardChargesForUserResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchPhysicalCardChargesForUserResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayableAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchPhysicalCardChargesForUserResponseValidationError{
					field:  "PayableAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchPhysicalCardChargesForUserResponseValidationError{
					field:  "PayableAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayableAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchPhysicalCardChargesForUserResponseValidationError{
				field:  "PayableAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisplayAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchPhysicalCardChargesForUserResponseValidationError{
					field:  "DisplayAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchPhysicalCardChargesForUserResponseValidationError{
					field:  "DisplayAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchPhysicalCardChargesForUserResponseValidationError{
				field:  "DisplayAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchPhysicalCardChargesForUserResponseMultiError(errors)
	}

	return nil
}

// FetchPhysicalCardChargesForUserResponseMultiError is an error wrapping
// multiple validation errors returned by
// FetchPhysicalCardChargesForUserResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchPhysicalCardChargesForUserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchPhysicalCardChargesForUserResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchPhysicalCardChargesForUserResponseMultiError) AllErrors() []error { return m }

// FetchPhysicalCardChargesForUserResponseValidationError is the validation
// error returned by FetchPhysicalCardChargesForUserResponse.Validate if the
// designated constraints aren't met.
type FetchPhysicalCardChargesForUserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchPhysicalCardChargesForUserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchPhysicalCardChargesForUserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchPhysicalCardChargesForUserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchPhysicalCardChargesForUserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchPhysicalCardChargesForUserResponseValidationError) ErrorName() string {
	return "FetchPhysicalCardChargesForUserResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchPhysicalCardChargesForUserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchPhysicalCardChargesForUserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchPhysicalCardChargesForUserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchPhysicalCardChargesForUserResponseValidationError{}

// Validate checks the field values on InitiatePhysicalCardDispatchRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InitiatePhysicalCardDispatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiatePhysicalCardDispatchRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InitiatePhysicalCardDispatchRequestMultiError, or nil if none found.
func (m *InitiatePhysicalCardDispatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiatePhysicalCardDispatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for CardId

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePhysicalCardDispatchRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePhysicalCardDispatchRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePhysicalCardDispatchRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AddressType

	// no validation rules for UiEntryPoint

	if all {
		switch v := interface{}(m.GetRedirectAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePhysicalCardDispatchRequestValidationError{
					field:  "RedirectAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePhysicalCardDispatchRequestValidationError{
					field:  "RedirectAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirectAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePhysicalCardDispatchRequestValidationError{
				field:  "RedirectAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntryPoint

	if len(errors) > 0 {
		return InitiatePhysicalCardDispatchRequestMultiError(errors)
	}

	return nil
}

// InitiatePhysicalCardDispatchRequestMultiError is an error wrapping multiple
// validation errors returned by
// InitiatePhysicalCardDispatchRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiatePhysicalCardDispatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiatePhysicalCardDispatchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiatePhysicalCardDispatchRequestMultiError) AllErrors() []error { return m }

// InitiatePhysicalCardDispatchRequestValidationError is the validation error
// returned by InitiatePhysicalCardDispatchRequest.Validate if the designated
// constraints aren't met.
type InitiatePhysicalCardDispatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiatePhysicalCardDispatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiatePhysicalCardDispatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiatePhysicalCardDispatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiatePhysicalCardDispatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiatePhysicalCardDispatchRequestValidationError) ErrorName() string {
	return "InitiatePhysicalCardDispatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiatePhysicalCardDispatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiatePhysicalCardDispatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiatePhysicalCardDispatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiatePhysicalCardDispatchRequestValidationError{}

// Validate checks the field values on InitiatePhysicalCardDispatchResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *InitiatePhysicalCardDispatchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiatePhysicalCardDispatchResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InitiatePhysicalCardDispatchResponseMultiError, or nil if none found.
func (m *InitiatePhysicalCardDispatchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiatePhysicalCardDispatchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePhysicalCardDispatchResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePhysicalCardDispatchResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePhysicalCardDispatchResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePhysicalCardDispatchResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePhysicalCardDispatchResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePhysicalCardDispatchResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiatePhysicalCardDispatchResponseMultiError(errors)
	}

	return nil
}

// InitiatePhysicalCardDispatchResponseMultiError is an error wrapping multiple
// validation errors returned by
// InitiatePhysicalCardDispatchResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiatePhysicalCardDispatchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiatePhysicalCardDispatchResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiatePhysicalCardDispatchResponseMultiError) AllErrors() []error { return m }

// InitiatePhysicalCardDispatchResponseValidationError is the validation error
// returned by InitiatePhysicalCardDispatchResponse.Validate if the designated
// constraints aren't met.
type InitiatePhysicalCardDispatchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiatePhysicalCardDispatchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiatePhysicalCardDispatchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiatePhysicalCardDispatchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiatePhysicalCardDispatchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiatePhysicalCardDispatchResponseValidationError) ErrorName() string {
	return "InitiatePhysicalCardDispatchResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiatePhysicalCardDispatchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiatePhysicalCardDispatchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiatePhysicalCardDispatchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiatePhysicalCardDispatchResponseValidationError{}

// Validate checks the field values on GetPhysicalCardActivationInfoRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPhysicalCardActivationInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPhysicalCardActivationInfoRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPhysicalCardActivationInfoRequestMultiError, or nil if none found.
func (m *GetPhysicalCardActivationInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPhysicalCardActivationInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *GetPhysicalCardActivationInfoRequest_CardId:
		if v == nil {
			err := GetPhysicalCardActivationInfoRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for CardId
	case *GetPhysicalCardActivationInfoRequest_ActorId:
		if v == nil {
			err := GetPhysicalCardActivationInfoRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetPhysicalCardActivationInfoRequestMultiError(errors)
	}

	return nil
}

// GetPhysicalCardActivationInfoRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetPhysicalCardActivationInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPhysicalCardActivationInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPhysicalCardActivationInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPhysicalCardActivationInfoRequestMultiError) AllErrors() []error { return m }

// GetPhysicalCardActivationInfoRequestValidationError is the validation error
// returned by GetPhysicalCardActivationInfoRequest.Validate if the designated
// constraints aren't met.
type GetPhysicalCardActivationInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPhysicalCardActivationInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPhysicalCardActivationInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPhysicalCardActivationInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPhysicalCardActivationInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPhysicalCardActivationInfoRequestValidationError) ErrorName() string {
	return "GetPhysicalCardActivationInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPhysicalCardActivationInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPhysicalCardActivationInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPhysicalCardActivationInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPhysicalCardActivationInfoRequestValidationError{}

// Validate checks the field values on GetPhysicalCardActivationInfoResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPhysicalCardActivationInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPhysicalCardActivationInfoResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPhysicalCardActivationInfoResponseMultiError, or nil if none found.
func (m *GetPhysicalCardActivationInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPhysicalCardActivationInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPhysicalCardActivationInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPhysicalCardActivationInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPhysicalCardActivationInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardActivationStatus

	if all {
		switch v := interface{}(m.GetCardActivatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPhysicalCardActivationInfoResponseValidationError{
					field:  "CardActivatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPhysicalCardActivationInfoResponseValidationError{
					field:  "CardActivatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardActivatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPhysicalCardActivationInfoResponseValidationError{
				field:  "CardActivatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPhysicalCardActivationInfoResponseMultiError(errors)
	}

	return nil
}

// GetPhysicalCardActivationInfoResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetPhysicalCardActivationInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPhysicalCardActivationInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPhysicalCardActivationInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPhysicalCardActivationInfoResponseMultiError) AllErrors() []error { return m }

// GetPhysicalCardActivationInfoResponseValidationError is the validation error
// returned by GetPhysicalCardActivationInfoResponse.Validate if the
// designated constraints aren't met.
type GetPhysicalCardActivationInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPhysicalCardActivationInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPhysicalCardActivationInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPhysicalCardActivationInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPhysicalCardActivationInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPhysicalCardActivationInfoResponseValidationError) ErrorName() string {
	return "GetPhysicalCardActivationInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPhysicalCardActivationInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPhysicalCardActivationInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPhysicalCardActivationInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPhysicalCardActivationInfoResponseValidationError{}

// Validate checks the field values on GetPhysicalCardDispatchStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPhysicalCardDispatchStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPhysicalCardDispatchStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPhysicalCardDispatchStatusRequestMultiError, or nil if none found.
func (m *GetPhysicalCardDispatchStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPhysicalCardDispatchStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	if len(errors) > 0 {
		return GetPhysicalCardDispatchStatusRequestMultiError(errors)
	}

	return nil
}

// GetPhysicalCardDispatchStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetPhysicalCardDispatchStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPhysicalCardDispatchStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPhysicalCardDispatchStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPhysicalCardDispatchStatusRequestMultiError) AllErrors() []error { return m }

// GetPhysicalCardDispatchStatusRequestValidationError is the validation error
// returned by GetPhysicalCardDispatchStatusRequest.Validate if the designated
// constraints aren't met.
type GetPhysicalCardDispatchStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPhysicalCardDispatchStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPhysicalCardDispatchStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPhysicalCardDispatchStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPhysicalCardDispatchStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPhysicalCardDispatchStatusRequestValidationError) ErrorName() string {
	return "GetPhysicalCardDispatchStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPhysicalCardDispatchStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPhysicalCardDispatchStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPhysicalCardDispatchStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPhysicalCardDispatchStatusRequestValidationError{}

// Validate checks the field values on GetPhysicalCardDispatchStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPhysicalCardDispatchStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPhysicalCardDispatchStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPhysicalCardDispatchStatusResponseMultiError, or nil if none found.
func (m *GetPhysicalCardDispatchStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPhysicalCardDispatchStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPhysicalCardDispatchStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPhysicalCardDispatchStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPhysicalCardDispatchStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPhysicalCardDispatchStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPhysicalCardDispatchStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPhysicalCardDispatchStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPhysicalCardDispatchStatusResponseMultiError(errors)
	}

	return nil
}

// GetPhysicalCardDispatchStatusResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetPhysicalCardDispatchStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPhysicalCardDispatchStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPhysicalCardDispatchStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPhysicalCardDispatchStatusResponseMultiError) AllErrors() []error { return m }

// GetPhysicalCardDispatchStatusResponseValidationError is the validation error
// returned by GetPhysicalCardDispatchStatusResponse.Validate if the
// designated constraints aren't met.
type GetPhysicalCardDispatchStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPhysicalCardDispatchStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPhysicalCardDispatchStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPhysicalCardDispatchStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPhysicalCardDispatchStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPhysicalCardDispatchStatusResponseValidationError) ErrorName() string {
	return "GetPhysicalCardDispatchStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPhysicalCardDispatchStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPhysicalCardDispatchStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPhysicalCardDispatchStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPhysicalCardDispatchStatusResponseValidationError{}

// Validate checks the field values on CreatePhysicalCardDispatchAttemptRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreatePhysicalCardDispatchAttemptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreatePhysicalCardDispatchAttemptRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreatePhysicalCardDispatchAttemptRequestMultiError, or nil if none found.
func (m *CreatePhysicalCardDispatchAttemptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePhysicalCardDispatchAttemptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	if len(errors) > 0 {
		return CreatePhysicalCardDispatchAttemptRequestMultiError(errors)
	}

	return nil
}

// CreatePhysicalCardDispatchAttemptRequestMultiError is an error wrapping
// multiple validation errors returned by
// CreatePhysicalCardDispatchAttemptRequest.ValidateAll() if the designated
// constraints aren't met.
type CreatePhysicalCardDispatchAttemptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePhysicalCardDispatchAttemptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePhysicalCardDispatchAttemptRequestMultiError) AllErrors() []error { return m }

// CreatePhysicalCardDispatchAttemptRequestValidationError is the validation
// error returned by CreatePhysicalCardDispatchAttemptRequest.Validate if the
// designated constraints aren't met.
type CreatePhysicalCardDispatchAttemptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePhysicalCardDispatchAttemptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePhysicalCardDispatchAttemptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePhysicalCardDispatchAttemptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePhysicalCardDispatchAttemptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePhysicalCardDispatchAttemptRequestValidationError) ErrorName() string {
	return "CreatePhysicalCardDispatchAttemptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePhysicalCardDispatchAttemptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePhysicalCardDispatchAttemptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePhysicalCardDispatchAttemptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePhysicalCardDispatchAttemptRequestValidationError{}

// Validate checks the field values on
// CreatePhysicalCardDispatchAttemptResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreatePhysicalCardDispatchAttemptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreatePhysicalCardDispatchAttemptResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreatePhysicalCardDispatchAttemptResponseMultiError, or nil if none found.
func (m *CreatePhysicalCardDispatchAttemptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePhysicalCardDispatchAttemptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreatePhysicalCardDispatchAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreatePhysicalCardDispatchAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreatePhysicalCardDispatchAttemptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreatePhysicalCardDispatchAttemptResponseMultiError(errors)
	}

	return nil
}

// CreatePhysicalCardDispatchAttemptResponseMultiError is an error wrapping
// multiple validation errors returned by
// CreatePhysicalCardDispatchAttemptResponse.ValidateAll() if the designated
// constraints aren't met.
type CreatePhysicalCardDispatchAttemptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePhysicalCardDispatchAttemptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePhysicalCardDispatchAttemptResponseMultiError) AllErrors() []error { return m }

// CreatePhysicalCardDispatchAttemptResponseValidationError is the validation
// error returned by CreatePhysicalCardDispatchAttemptResponse.Validate if the
// designated constraints aren't met.
type CreatePhysicalCardDispatchAttemptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePhysicalCardDispatchAttemptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePhysicalCardDispatchAttemptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePhysicalCardDispatchAttemptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePhysicalCardDispatchAttemptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePhysicalCardDispatchAttemptResponseValidationError) ErrorName() string {
	return "CreatePhysicalCardDispatchAttemptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePhysicalCardDispatchAttemptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePhysicalCardDispatchAttemptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePhysicalCardDispatchAttemptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePhysicalCardDispatchAttemptResponseValidationError{}

// Validate checks the field values on
// InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError, or
// nil if none found.
func (m *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for AddressType

	// no validation rules for ActorId

	if len(errors) > 0 {
		return InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError(errors)
	}

	return nil
}

// InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError is an
// error wrapping multiple validation errors returned by
// InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest.ValidateAll()
// if the designated constraints aren't met.
type InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError) AllErrors() []error {
	return m
}

// InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError
// is the validation error returned by
// InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest.Validate if the
// designated constraints aren't met.
type InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) ErrorName() string {
	return "InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateShippingAddressUpdateAndDispatchPhysicalCardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError{}

// Validate checks the field values on
// InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError, or
// nil if none found.
func (m *InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError(errors)
	}

	return nil
}

// InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError is an
// error wrapping multiple validation errors returned by
// InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse.ValidateAll()
// if the designated constraints aren't met.
type InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError) AllErrors() []error {
	return m
}

// InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError
// is the validation error returned by
// InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse.Validate if
// the designated constraints aren't met.
type InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) ErrorName() string {
	return "InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateShippingAddressUpdateAndDispatchPhysicalCardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{}

// Validate checks the field values on
// CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestMultiError,
// or nil if none found.
func (m *CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	if len(errors) > 0 {
		return CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestMultiError(errors)
	}

	return nil
}

// CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestMultiError is
// an error wrapping multiple validation errors returned by
// CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestMultiError) AllErrors() []error {
	return m
}

// CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestValidationError
// is the validation error returned by
// CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest.Validate if
// the designated constraints aren't met.
type CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestValidationError) ErrorName() string {
	return "CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequestValidationError{}

// Validate checks the field values on
// CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseMultiError,
// or nil if none found.
func (m *CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseMultiError(errors)
	}

	return nil
}

// CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseMultiError is
// an error wrapping multiple validation errors returned by
// CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseMultiError) AllErrors() []error {
	return m
}

// CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseValidationError
// is the validation error returned by
// CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse.Validate if
// the designated constraints aren't met.
type CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseValidationError) ErrorName() string {
	return "CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponseValidationError{}

// Validate checks the field values on GetCardActionAttemptsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardActionAttemptsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardActionAttemptsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCardActionAttemptsRequestMultiError, or nil if none found.
func (m *GetCardActionAttemptsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardActionAttemptsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetCardId()); l < 2 || l > 100 {
		err := GetCardActionAttemptsRequestValidationError{
			field:  "CardId",
			reason: "value length must be between 2 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _GetCardActionAttemptsRequest_Action_NotInLookup[m.GetAction()]; ok {
		err := GetCardActionAttemptsRequestValidationError{
			field:  "Action",
			reason: "value must not be in list [CARD_ACTION_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCardActionAttemptsRequestMultiError(errors)
	}

	return nil
}

// GetCardActionAttemptsRequestMultiError is an error wrapping multiple
// validation errors returned by GetCardActionAttemptsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetCardActionAttemptsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardActionAttemptsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardActionAttemptsRequestMultiError) AllErrors() []error { return m }

// GetCardActionAttemptsRequestValidationError is the validation error returned
// by GetCardActionAttemptsRequest.Validate if the designated constraints
// aren't met.
type GetCardActionAttemptsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardActionAttemptsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardActionAttemptsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardActionAttemptsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardActionAttemptsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardActionAttemptsRequestValidationError) ErrorName() string {
	return "GetCardActionAttemptsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardActionAttemptsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardActionAttemptsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardActionAttemptsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardActionAttemptsRequestValidationError{}

var _GetCardActionAttemptsRequest_Action_NotInLookup = map[card.CardAction]struct{}{
	0: {},
}

// Validate checks the field values on GetCardActionAttemptsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardActionAttemptsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardActionAttemptsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCardActionAttemptsResponseMultiError, or nil if none found.
func (m *GetCardActionAttemptsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardActionAttemptsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardActionAttemptsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardActionAttemptsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardActionAttemptsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActionAttempts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCardActionAttemptsResponseValidationError{
						field:  fmt.Sprintf("ActionAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCardActionAttemptsResponseValidationError{
						field:  fmt.Sprintf("ActionAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCardActionAttemptsResponseValidationError{
					field:  fmt.Sprintf("ActionAttempts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCardActionAttemptsResponseMultiError(errors)
	}

	return nil
}

// GetCardActionAttemptsResponseMultiError is an error wrapping multiple
// validation errors returned by GetCardActionAttemptsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetCardActionAttemptsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardActionAttemptsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardActionAttemptsResponseMultiError) AllErrors() []error { return m }

// GetCardActionAttemptsResponseValidationError is the validation error
// returned by GetCardActionAttemptsResponse.Validate if the designated
// constraints aren't met.
type GetCardActionAttemptsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardActionAttemptsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardActionAttemptsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardActionAttemptsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardActionAttemptsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardActionAttemptsResponseValidationError) ErrorName() string {
	return "GetCardActionAttemptsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardActionAttemptsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardActionAttemptsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardActionAttemptsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardActionAttemptsResponseValidationError{}

// Validate checks the field values on UpdateTrackingDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTrackingDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTrackingDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTrackingDetailsRequestMultiError, or nil if none found.
func (m *UpdateTrackingDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTrackingDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardTrackingUpdateCsvData

	if len(errors) > 0 {
		return UpdateTrackingDetailsRequestMultiError(errors)
	}

	return nil
}

// UpdateTrackingDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateTrackingDetailsRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateTrackingDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTrackingDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTrackingDetailsRequestMultiError) AllErrors() []error { return m }

// UpdateTrackingDetailsRequestValidationError is the validation error returned
// by UpdateTrackingDetailsRequest.Validate if the designated constraints
// aren't met.
type UpdateTrackingDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTrackingDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTrackingDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTrackingDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTrackingDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTrackingDetailsRequestValidationError) ErrorName() string {
	return "UpdateTrackingDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTrackingDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTrackingDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTrackingDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTrackingDetailsRequestValidationError{}

// Validate checks the field values on UpdateTrackingDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTrackingDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTrackingDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateTrackingDetailsResponseMultiError, or nil if none found.
func (m *UpdateTrackingDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTrackingDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTrackingDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTrackingDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTrackingDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateTrackingDetailsResponseMultiError(errors)
	}

	return nil
}

// UpdateTrackingDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateTrackingDetailsResponse.ValidateAll()
// if the designated constraints aren't met.
type UpdateTrackingDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTrackingDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTrackingDetailsResponseMultiError) AllErrors() []error { return m }

// UpdateTrackingDetailsResponseValidationError is the validation error
// returned by UpdateTrackingDetailsResponse.Validate if the designated
// constraints aren't met.
type UpdateTrackingDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTrackingDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTrackingDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTrackingDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTrackingDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTrackingDetailsResponseValidationError) ErrorName() string {
	return "UpdateTrackingDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTrackingDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTrackingDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTrackingDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTrackingDetailsResponseValidationError{}

// Validate checks the field values on ForceCardCreationEnquiryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForceCardCreationEnquiryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForceCardCreationEnquiryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ForceCardCreationEnquiryRequestMultiError, or nil if none found.
func (m *ForceCardCreationEnquiryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ForceCardCreationEnquiryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetCardId()); l < 2 || l > 100 {
		err := ForceCardCreationEnquiryRequestValidationError{
			field:  "CardId",
			reason: "value length must be between 2 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ForceCardCreationEnquiryRequestMultiError(errors)
	}

	return nil
}

// ForceCardCreationEnquiryRequestMultiError is an error wrapping multiple
// validation errors returned by ForceCardCreationEnquiryRequest.ValidateAll()
// if the designated constraints aren't met.
type ForceCardCreationEnquiryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForceCardCreationEnquiryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForceCardCreationEnquiryRequestMultiError) AllErrors() []error { return m }

// ForceCardCreationEnquiryRequestValidationError is the validation error
// returned by ForceCardCreationEnquiryRequest.Validate if the designated
// constraints aren't met.
type ForceCardCreationEnquiryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForceCardCreationEnquiryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForceCardCreationEnquiryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForceCardCreationEnquiryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForceCardCreationEnquiryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForceCardCreationEnquiryRequestValidationError) ErrorName() string {
	return "ForceCardCreationEnquiryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ForceCardCreationEnquiryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForceCardCreationEnquiryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForceCardCreationEnquiryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForceCardCreationEnquiryRequestValidationError{}

// Validate checks the field values on ForceCardCreationEnquiryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ForceCardCreationEnquiryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForceCardCreationEnquiryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ForceCardCreationEnquiryResponseMultiError, or nil if none found.
func (m *ForceCardCreationEnquiryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ForceCardCreationEnquiryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForceCardCreationEnquiryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForceCardCreationEnquiryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForceCardCreationEnquiryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ForceCardCreationEnquiryResponseMultiError(errors)
	}

	return nil
}

// ForceCardCreationEnquiryResponseMultiError is an error wrapping multiple
// validation errors returned by
// ForceCardCreationEnquiryResponse.ValidateAll() if the designated
// constraints aren't met.
type ForceCardCreationEnquiryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForceCardCreationEnquiryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForceCardCreationEnquiryResponseMultiError) AllErrors() []error { return m }

// ForceCardCreationEnquiryResponseValidationError is the validation error
// returned by ForceCardCreationEnquiryResponse.Validate if the designated
// constraints aren't met.
type ForceCardCreationEnquiryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForceCardCreationEnquiryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForceCardCreationEnquiryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForceCardCreationEnquiryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForceCardCreationEnquiryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForceCardCreationEnquiryResponseValidationError) ErrorName() string {
	return "ForceCardCreationEnquiryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ForceCardCreationEnquiryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForceCardCreationEnquiryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForceCardCreationEnquiryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForceCardCreationEnquiryResponseValidationError{}

// Validate checks the field values on ProcessManualCardPinSetRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessManualCardPinSetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessManualCardPinSetRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessManualCardPinSetRequestMultiError, or nil if none found.
func (m *ProcessManualCardPinSetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessManualCardPinSetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetCardId()); l < 2 || l > 100 {
		err := ProcessManualCardPinSetRequestValidationError{
			field:  "CardId",
			reason: "value length must be between 2 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ProcessManualCardPinSetRequestMultiError(errors)
	}

	return nil
}

// ProcessManualCardPinSetRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessManualCardPinSetRequest.ValidateAll()
// if the designated constraints aren't met.
type ProcessManualCardPinSetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessManualCardPinSetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessManualCardPinSetRequestMultiError) AllErrors() []error { return m }

// ProcessManualCardPinSetRequestValidationError is the validation error
// returned by ProcessManualCardPinSetRequest.Validate if the designated
// constraints aren't met.
type ProcessManualCardPinSetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessManualCardPinSetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessManualCardPinSetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessManualCardPinSetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessManualCardPinSetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessManualCardPinSetRequestValidationError) ErrorName() string {
	return "ProcessManualCardPinSetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessManualCardPinSetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessManualCardPinSetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessManualCardPinSetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessManualCardPinSetRequestValidationError{}

// Validate checks the field values on ProcessManualCardPinSetResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessManualCardPinSetResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessManualCardPinSetResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessManualCardPinSetResponseMultiError, or nil if none found.
func (m *ProcessManualCardPinSetResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessManualCardPinSetResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessManualCardPinSetResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessManualCardPinSetResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessManualCardPinSetResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessManualCardPinSetResponseMultiError(errors)
	}

	return nil
}

// ProcessManualCardPinSetResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessManualCardPinSetResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessManualCardPinSetResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessManualCardPinSetResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessManualCardPinSetResponseMultiError) AllErrors() []error { return m }

// ProcessManualCardPinSetResponseValidationError is the validation error
// returned by ProcessManualCardPinSetResponse.Validate if the designated
// constraints aren't met.
type ProcessManualCardPinSetResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessManualCardPinSetResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessManualCardPinSetResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessManualCardPinSetResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessManualCardPinSetResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessManualCardPinSetResponseValidationError) ErrorName() string {
	return "ProcessManualCardPinSetResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessManualCardPinSetResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessManualCardPinSetResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessManualCardPinSetResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessManualCardPinSetResponseValidationError{}

// Validate checks the field values on UploadCardTrackingDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UploadCardTrackingDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadCardTrackingDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UploadCardTrackingDetailsRequestMultiError, or nil if none found.
func (m *UploadCardTrackingDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadCardTrackingDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardTrackingCsvData

	// no validation rules for CardPrintingVendor

	if len(errors) > 0 {
		return UploadCardTrackingDetailsRequestMultiError(errors)
	}

	return nil
}

// UploadCardTrackingDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by
// UploadCardTrackingDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type UploadCardTrackingDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadCardTrackingDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadCardTrackingDetailsRequestMultiError) AllErrors() []error { return m }

// UploadCardTrackingDetailsRequestValidationError is the validation error
// returned by UploadCardTrackingDetailsRequest.Validate if the designated
// constraints aren't met.
type UploadCardTrackingDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadCardTrackingDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadCardTrackingDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadCardTrackingDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadCardTrackingDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadCardTrackingDetailsRequestValidationError) ErrorName() string {
	return "UploadCardTrackingDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadCardTrackingDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadCardTrackingDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadCardTrackingDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadCardTrackingDetailsRequestValidationError{}

// Validate checks the field values on UploadCardTrackingDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UploadCardTrackingDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadCardTrackingDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UploadCardTrackingDetailsResponseMultiError, or nil if none found.
func (m *UploadCardTrackingDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadCardTrackingDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadCardTrackingDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadCardTrackingDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadCardTrackingDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AwbToFailureReason

	// no validation rules for TotalCount

	// no validation rules for SuccessfulCount

	if len(errors) > 0 {
		return UploadCardTrackingDetailsResponseMultiError(errors)
	}

	return nil
}

// UploadCardTrackingDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// UploadCardTrackingDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type UploadCardTrackingDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadCardTrackingDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadCardTrackingDetailsResponseMultiError) AllErrors() []error { return m }

// UploadCardTrackingDetailsResponseValidationError is the validation error
// returned by UploadCardTrackingDetailsResponse.Validate if the designated
// constraints aren't met.
type UploadCardTrackingDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadCardTrackingDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadCardTrackingDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadCardTrackingDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadCardTrackingDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadCardTrackingDetailsResponseValidationError) ErrorName() string {
	return "UploadCardTrackingDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadCardTrackingDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadCardTrackingDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadCardTrackingDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadCardTrackingDetailsResponseValidationError{}

// Validate checks the field values on GetCardShipmentTrackingDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCardShipmentTrackingDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardShipmentTrackingDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCardShipmentTrackingDetailsRequestMultiError, or nil if none found.
func (m *GetCardShipmentTrackingDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardShipmentTrackingDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	if len(errors) > 0 {
		return GetCardShipmentTrackingDetailsRequestMultiError(errors)
	}

	return nil
}

// GetCardShipmentTrackingDetailsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetCardShipmentTrackingDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCardShipmentTrackingDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardShipmentTrackingDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardShipmentTrackingDetailsRequestMultiError) AllErrors() []error { return m }

// GetCardShipmentTrackingDetailsRequestValidationError is the validation error
// returned by GetCardShipmentTrackingDetailsRequest.Validate if the
// designated constraints aren't met.
type GetCardShipmentTrackingDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardShipmentTrackingDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardShipmentTrackingDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardShipmentTrackingDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardShipmentTrackingDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardShipmentTrackingDetailsRequestValidationError) ErrorName() string {
	return "GetCardShipmentTrackingDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardShipmentTrackingDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardShipmentTrackingDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardShipmentTrackingDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardShipmentTrackingDetailsRequestValidationError{}

// Validate checks the field values on GetCardShipmentTrackingDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCardShipmentTrackingDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCardShipmentTrackingDetailsResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetCardShipmentTrackingDetailsResponseMultiError, or nil if none found.
func (m *GetCardShipmentTrackingDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardShipmentTrackingDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardShipmentTrackingDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardShipmentTrackingDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardShipmentTrackingDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTrackingDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardShipmentTrackingDetailsResponseValidationError{
					field:  "TrackingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardShipmentTrackingDetailsResponseValidationError{
					field:  "TrackingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTrackingDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardShipmentTrackingDetailsResponseValidationError{
				field:  "TrackingDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCardShipmentTrackingDetailsResponseMultiError(errors)
	}

	return nil
}

// GetCardShipmentTrackingDetailsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetCardShipmentTrackingDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCardShipmentTrackingDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardShipmentTrackingDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardShipmentTrackingDetailsResponseMultiError) AllErrors() []error { return m }

// GetCardShipmentTrackingDetailsResponseValidationError is the validation
// error returned by GetCardShipmentTrackingDetailsResponse.Validate if the
// designated constraints aren't met.
type GetCardShipmentTrackingDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardShipmentTrackingDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardShipmentTrackingDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardShipmentTrackingDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardShipmentTrackingDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardShipmentTrackingDetailsResponseValidationError) ErrorName() string {
	return "GetCardShipmentTrackingDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardShipmentTrackingDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardShipmentTrackingDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardShipmentTrackingDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardShipmentTrackingDetailsResponseValidationError{}

// Validate checks the field values on UpdateFreeCardReplacementRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateFreeCardReplacementRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFreeCardReplacementRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateFreeCardReplacementRequestMultiError, or nil if none found.
func (m *UpdateFreeCardReplacementRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFreeCardReplacementRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for CardSkuType

	if len(errors) > 0 {
		return UpdateFreeCardReplacementRequestMultiError(errors)
	}

	return nil
}

// UpdateFreeCardReplacementRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateFreeCardReplacementRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateFreeCardReplacementRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFreeCardReplacementRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFreeCardReplacementRequestMultiError) AllErrors() []error { return m }

// UpdateFreeCardReplacementRequestValidationError is the validation error
// returned by UpdateFreeCardReplacementRequest.Validate if the designated
// constraints aren't met.
type UpdateFreeCardReplacementRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFreeCardReplacementRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFreeCardReplacementRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFreeCardReplacementRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFreeCardReplacementRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFreeCardReplacementRequestValidationError) ErrorName() string {
	return "UpdateFreeCardReplacementRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFreeCardReplacementRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFreeCardReplacementRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFreeCardReplacementRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFreeCardReplacementRequestValidationError{}

// Validate checks the field values on UpdateFreeCardReplacementResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateFreeCardReplacementResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFreeCardReplacementResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateFreeCardReplacementResponseMultiError, or nil if none found.
func (m *UpdateFreeCardReplacementResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFreeCardReplacementResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFreeCardReplacementResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFreeCardReplacementResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFreeCardReplacementResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateFreeCardReplacementResponseMultiError(errors)
	}

	return nil
}

// UpdateFreeCardReplacementResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateFreeCardReplacementResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateFreeCardReplacementResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFreeCardReplacementResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFreeCardReplacementResponseMultiError) AllErrors() []error { return m }

// UpdateFreeCardReplacementResponseValidationError is the validation error
// returned by UpdateFreeCardReplacementResponse.Validate if the designated
// constraints aren't met.
type UpdateFreeCardReplacementResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFreeCardReplacementResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFreeCardReplacementResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFreeCardReplacementResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFreeCardReplacementResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFreeCardReplacementResponseValidationError) ErrorName() string {
	return "UpdateFreeCardReplacementResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFreeCardReplacementResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFreeCardReplacementResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFreeCardReplacementResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFreeCardReplacementResponseValidationError{}

// Validate checks the field values on TriggerCardNotificationsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerCardNotificationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerCardNotificationsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerCardNotificationsRequestMultiError, or nil if none found.
func (m *TriggerCardNotificationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerCardNotificationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NotificationType

	if len(errors) > 0 {
		return TriggerCardNotificationsRequestMultiError(errors)
	}

	return nil
}

// TriggerCardNotificationsRequestMultiError is an error wrapping multiple
// validation errors returned by TriggerCardNotificationsRequest.ValidateAll()
// if the designated constraints aren't met.
type TriggerCardNotificationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerCardNotificationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerCardNotificationsRequestMultiError) AllErrors() []error { return m }

// TriggerCardNotificationsRequestValidationError is the validation error
// returned by TriggerCardNotificationsRequest.Validate if the designated
// constraints aren't met.
type TriggerCardNotificationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerCardNotificationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerCardNotificationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerCardNotificationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerCardNotificationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerCardNotificationsRequestValidationError) ErrorName() string {
	return "TriggerCardNotificationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerCardNotificationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerCardNotificationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerCardNotificationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerCardNotificationsRequestValidationError{}

// Validate checks the field values on TriggerCardNotificationsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TriggerCardNotificationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerCardNotificationsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerCardNotificationsResponseMultiError, or nil if none found.
func (m *TriggerCardNotificationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerCardNotificationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TriggerCardNotificationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TriggerCardNotificationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TriggerCardNotificationsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TriggerCardNotificationsResponseMultiError(errors)
	}

	return nil
}

// TriggerCardNotificationsResponseMultiError is an error wrapping multiple
// validation errors returned by
// TriggerCardNotificationsResponse.ValidateAll() if the designated
// constraints aren't met.
type TriggerCardNotificationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerCardNotificationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerCardNotificationsResponseMultiError) AllErrors() []error { return m }

// TriggerCardNotificationsResponseValidationError is the validation error
// returned by TriggerCardNotificationsResponse.Validate if the designated
// constraints aren't met.
type TriggerCardNotificationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerCardNotificationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerCardNotificationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerCardNotificationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerCardNotificationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerCardNotificationsResponseValidationError) ErrorName() string {
	return "TriggerCardNotificationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerCardNotificationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerCardNotificationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerCardNotificationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerCardNotificationsResponseValidationError{}

// Validate checks the field values on InitiateAdditionalAuthAttemptRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *InitiateAdditionalAuthAttemptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateAdditionalAuthAttemptRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InitiateAdditionalAuthAttemptRequestMultiError, or nil if none found.
func (m *InitiateAdditionalAuthAttemptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateAdditionalAuthAttemptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for Action

	// no validation rules for Auth

	if len(errors) > 0 {
		return InitiateAdditionalAuthAttemptRequestMultiError(errors)
	}

	return nil
}

// InitiateAdditionalAuthAttemptRequestMultiError is an error wrapping multiple
// validation errors returned by
// InitiateAdditionalAuthAttemptRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateAdditionalAuthAttemptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateAdditionalAuthAttemptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateAdditionalAuthAttemptRequestMultiError) AllErrors() []error { return m }

// InitiateAdditionalAuthAttemptRequestValidationError is the validation error
// returned by InitiateAdditionalAuthAttemptRequest.Validate if the designated
// constraints aren't met.
type InitiateAdditionalAuthAttemptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateAdditionalAuthAttemptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateAdditionalAuthAttemptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateAdditionalAuthAttemptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateAdditionalAuthAttemptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateAdditionalAuthAttemptRequestValidationError) ErrorName() string {
	return "InitiateAdditionalAuthAttemptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateAdditionalAuthAttemptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateAdditionalAuthAttemptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateAdditionalAuthAttemptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateAdditionalAuthAttemptRequestValidationError{}

// Validate checks the field values on InitiateAdditionalAuthAttemptResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *InitiateAdditionalAuthAttemptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateAdditionalAuthAttemptResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InitiateAdditionalAuthAttemptResponseMultiError, or nil if none found.
func (m *InitiateAdditionalAuthAttemptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateAdditionalAuthAttemptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateAdditionalAuthAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateAdditionalAuthAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateAdditionalAuthAttemptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AttemptId

	if len(errors) > 0 {
		return InitiateAdditionalAuthAttemptResponseMultiError(errors)
	}

	return nil
}

// InitiateAdditionalAuthAttemptResponseMultiError is an error wrapping
// multiple validation errors returned by
// InitiateAdditionalAuthAttemptResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateAdditionalAuthAttemptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateAdditionalAuthAttemptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateAdditionalAuthAttemptResponseMultiError) AllErrors() []error { return m }

// InitiateAdditionalAuthAttemptResponseValidationError is the validation error
// returned by InitiateAdditionalAuthAttemptResponse.Validate if the
// designated constraints aren't met.
type InitiateAdditionalAuthAttemptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateAdditionalAuthAttemptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateAdditionalAuthAttemptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateAdditionalAuthAttemptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateAdditionalAuthAttemptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateAdditionalAuthAttemptResponseValidationError) ErrorName() string {
	return "InitiateAdditionalAuthAttemptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateAdditionalAuthAttemptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateAdditionalAuthAttemptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateAdditionalAuthAttemptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateAdditionalAuthAttemptResponseValidationError{}

// Validate checks the field values on GetAdditionalAuthInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAdditionalAuthInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAdditionalAuthInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAdditionalAuthInfoRequestMultiError, or nil if none found.
func (m *GetAdditionalAuthInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAdditionalAuthInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttemptId

	if len(errors) > 0 {
		return GetAdditionalAuthInfoRequestMultiError(errors)
	}

	return nil
}

// GetAdditionalAuthInfoRequestMultiError is an error wrapping multiple
// validation errors returned by GetAdditionalAuthInfoRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAdditionalAuthInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAdditionalAuthInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAdditionalAuthInfoRequestMultiError) AllErrors() []error { return m }

// GetAdditionalAuthInfoRequestValidationError is the validation error returned
// by GetAdditionalAuthInfoRequest.Validate if the designated constraints
// aren't met.
type GetAdditionalAuthInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAdditionalAuthInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAdditionalAuthInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAdditionalAuthInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAdditionalAuthInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAdditionalAuthInfoRequestValidationError) ErrorName() string {
	return "GetAdditionalAuthInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAdditionalAuthInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAdditionalAuthInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAdditionalAuthInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAdditionalAuthInfoRequestValidationError{}

// Validate checks the field values on GetAdditionalAuthInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAdditionalAuthInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAdditionalAuthInfoResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAdditionalAuthInfoResponseMultiError, or nil if none found.
func (m *GetAdditionalAuthInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAdditionalAuthInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAdditionalAuthInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAdditionalAuthInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAdditionalAuthInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	// no validation rules for Action

	if all {
		switch v := interface{}(m.GetAuthAttempt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAdditionalAuthInfoResponseValidationError{
					field:  "AuthAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAdditionalAuthInfoResponseValidationError{
					field:  "AuthAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthAttempt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAdditionalAuthInfoResponseValidationError{
				field:  "AuthAttempt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAdditionalAuthInfoResponseMultiError(errors)
	}

	return nil
}

// GetAdditionalAuthInfoResponseMultiError is an error wrapping multiple
// validation errors returned by GetAdditionalAuthInfoResponse.ValidateAll()
// if the designated constraints aren't met.
type GetAdditionalAuthInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAdditionalAuthInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAdditionalAuthInfoResponseMultiError) AllErrors() []error { return m }

// GetAdditionalAuthInfoResponseValidationError is the validation error
// returned by GetAdditionalAuthInfoResponse.Validate if the designated
// constraints aren't met.
type GetAdditionalAuthInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAdditionalAuthInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAdditionalAuthInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAdditionalAuthInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAdditionalAuthInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAdditionalAuthInfoResponseValidationError) ErrorName() string {
	return "GetAdditionalAuthInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAdditionalAuthInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAdditionalAuthInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAdditionalAuthInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAdditionalAuthInfoResponseValidationError{}

// Validate checks the field values on UpdateAuthInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAuthInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAuthInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAuthInfoRequestMultiError, or nil if none found.
func (m *UpdateAuthInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAuthInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttemptId

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetUpdatedAuthAttempt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAuthInfoRequestValidationError{
					field:  "UpdatedAuthAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAuthInfoRequestValidationError{
					field:  "UpdatedAuthAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAuthAttempt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAuthInfoRequestValidationError{
				field:  "UpdatedAuthAttempt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateAuthInfoRequestMultiError(errors)
	}

	return nil
}

// UpdateAuthInfoRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateAuthInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateAuthInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAuthInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAuthInfoRequestMultiError) AllErrors() []error { return m }

// UpdateAuthInfoRequestValidationError is the validation error returned by
// UpdateAuthInfoRequest.Validate if the designated constraints aren't met.
type UpdateAuthInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAuthInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAuthInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAuthInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAuthInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAuthInfoRequestValidationError) ErrorName() string {
	return "UpdateAuthInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAuthInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAuthInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAuthInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAuthInfoRequestValidationError{}

// Validate checks the field values on UpdateAuthInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAuthInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAuthInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAuthInfoResponseMultiError, or nil if none found.
func (m *UpdateAuthInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAuthInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAuthInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAuthInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAuthInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	// no validation rules for Action

	if len(errors) > 0 {
		return UpdateAuthInfoResponseMultiError(errors)
	}

	return nil
}

// UpdateAuthInfoResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateAuthInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateAuthInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAuthInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAuthInfoResponseMultiError) AllErrors() []error { return m }

// UpdateAuthInfoResponseValidationError is the validation error returned by
// UpdateAuthInfoResponse.Validate if the designated constraints aren't met.
type UpdateAuthInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAuthInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAuthInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAuthInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAuthInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAuthInfoResponseValidationError) ErrorName() string {
	return "UpdateAuthInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAuthInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAuthInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAuthInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAuthInfoResponseValidationError{}

// Validate checks the field values on CreateCardRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateCardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCardRequestMultiError, or nil if none found.
func (m *CreateCardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetActor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCardRequestValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCardRequestValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCardRequestValidationError{
				field:  "Actor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IssueType

	// no validation rules for IssuingBank

	// no validation rules for Type

	// no validation rules for SavingsAccountId

	// no validation rules for BlockedCardId

	// no validation rules for CardSkuType

	// no validation rules for CardForm

	if len(errors) > 0 {
		return CreateCardRequestMultiError(errors)
	}

	return nil
}

// CreateCardRequestMultiError is an error wrapping multiple validation errors
// returned by CreateCardRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateCardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCardRequestMultiError) AllErrors() []error { return m }

// CreateCardRequestValidationError is the validation error returned by
// CreateCardRequest.Validate if the designated constraints aren't met.
type CreateCardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCardRequestValidationError) ErrorName() string {
	return "CreateCardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCardRequestValidationError{}

// Validate checks the field values on CreateCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCardResponseMultiError, or nil if none found.
func (m *CreateCardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCardResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateCardResponseValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateCardResponseValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateCardResponseValidationError{
					field:  fmt.Sprintf("Cards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateCardResponseMultiError(errors)
	}

	return nil
}

// CreateCardResponseMultiError is an error wrapping multiple validation errors
// returned by CreateCardResponse.ValidateAll() if the designated constraints
// aren't met.
type CreateCardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCardResponseMultiError) AllErrors() []error { return m }

// CreateCardResponseValidationError is the validation error returned by
// CreateCardResponse.Validate if the designated constraints aren't met.
type CreateCardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCardResponseValidationError) ErrorName() string {
	return "CreateCardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCardResponseValidationError{}

// Validate checks the field values on FetchCardCreationStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchCardCreationStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardCreationStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FetchCardCreationStatusRequestMultiError, or nil if none found.
func (m *FetchCardCreationStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardCreationStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IssuingBank

	if len(errors) > 0 {
		return FetchCardCreationStatusRequestMultiError(errors)
	}

	return nil
}

// FetchCardCreationStatusRequestMultiError is an error wrapping multiple
// validation errors returned by FetchCardCreationStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type FetchCardCreationStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardCreationStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardCreationStatusRequestMultiError) AllErrors() []error { return m }

// FetchCardCreationStatusRequestValidationError is the validation error
// returned by FetchCardCreationStatusRequest.Validate if the designated
// constraints aren't met.
type FetchCardCreationStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardCreationStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardCreationStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardCreationStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardCreationStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardCreationStatusRequestValidationError) ErrorName() string {
	return "FetchCardCreationStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardCreationStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardCreationStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardCreationStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardCreationStatusRequestValidationError{}

// Validate checks the field values on CreationStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreationStatesInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreationStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreationStatesInfoMultiError, or nil if none found.
func (m *CreationStatesInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CreationStatesInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreationStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreationStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreationStatesInfoValidationError{
				field:  "Card",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardCreationRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreationStatesInfoValidationError{
					field:  "CardCreationRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreationStatesInfoValidationError{
					field:  "CardCreationRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardCreationRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreationStatesInfoValidationError{
				field:  "CardCreationRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreationStatesInfoMultiError(errors)
	}

	return nil
}

// CreationStatesInfoMultiError is an error wrapping multiple validation errors
// returned by CreationStatesInfo.ValidateAll() if the designated constraints
// aren't met.
type CreationStatesInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreationStatesInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreationStatesInfoMultiError) AllErrors() []error { return m }

// CreationStatesInfoValidationError is the validation error returned by
// CreationStatesInfo.Validate if the designated constraints aren't met.
type CreationStatesInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreationStatesInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreationStatesInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreationStatesInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreationStatesInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreationStatesInfoValidationError) ErrorName() string {
	return "CreationStatesInfoValidationError"
}

// Error satisfies the builtin error interface
func (e CreationStatesInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreationStatesInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreationStatesInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreationStatesInfoValidationError{}

// Validate checks the field values on FetchCardCreationStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchCardCreationStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardCreationStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FetchCardCreationStatusResponseMultiError, or nil if none found.
func (m *FetchCardCreationStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardCreationStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCardCreationStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCardCreationStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCardCreationStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetCreationStates()))
		i := 0
		for key := range m.GetCreationStates() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCreationStates()[key]
			_ = val

			// no validation rules for CreationStates[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, FetchCardCreationStatusResponseValidationError{
							field:  fmt.Sprintf("CreationStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, FetchCardCreationStatusResponseValidationError{
							field:  fmt.Sprintf("CreationStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return FetchCardCreationStatusResponseValidationError{
						field:  fmt.Sprintf("CreationStates[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return FetchCardCreationStatusResponseMultiError(errors)
	}

	return nil
}

// FetchCardCreationStatusResponseMultiError is an error wrapping multiple
// validation errors returned by FetchCardCreationStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type FetchCardCreationStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardCreationStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardCreationStatusResponseMultiError) AllErrors() []error { return m }

// FetchCardCreationStatusResponseValidationError is the validation error
// returned by FetchCardCreationStatusResponse.Validate if the designated
// constraints aren't met.
type FetchCardCreationStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardCreationStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardCreationStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardCreationStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardCreationStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardCreationStatusResponseValidationError) ErrorName() string {
	return "FetchCardCreationStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardCreationStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardCreationStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardCreationStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardCreationStatusResponseValidationError{}

// Validate checks the field values on FetchCardsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FetchCardsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchCardsRequestMultiError, or nil if none found.
func (m *FetchCardsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetActor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCardsRequestValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCardsRequestValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCardsRequestValidationError{
				field:  "Actor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SortedBy

	// no validation rules for Limit

	if len(errors) > 0 {
		return FetchCardsRequestMultiError(errors)
	}

	return nil
}

// FetchCardsRequestMultiError is an error wrapping multiple validation errors
// returned by FetchCardsRequest.ValidateAll() if the designated constraints
// aren't met.
type FetchCardsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardsRequestMultiError) AllErrors() []error { return m }

// FetchCardsRequestValidationError is the validation error returned by
// FetchCardsRequest.Validate if the designated constraints aren't met.
type FetchCardsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardsRequestValidationError) ErrorName() string {
	return "FetchCardsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardsRequestValidationError{}

// Validate checks the field values on FetchCardsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchCardsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchCardsResponseMultiError, or nil if none found.
func (m *FetchCardsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCardsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCardsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCardsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FetchCardsResponseValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FetchCardsResponseValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FetchCardsResponseValidationError{
					field:  fmt.Sprintf("Cards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FetchCardsResponseMultiError(errors)
	}

	return nil
}

// FetchCardsResponseMultiError is an error wrapping multiple validation errors
// returned by FetchCardsResponse.ValidateAll() if the designated constraints
// aren't met.
type FetchCardsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardsResponseMultiError) AllErrors() []error { return m }

// FetchCardsResponseValidationError is the validation error returned by
// FetchCardsResponse.Validate if the designated constraints aren't met.
type FetchCardsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardsResponseValidationError) ErrorName() string {
	return "FetchCardsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardsResponseValidationError{}

// Validate checks the field values on ActivateCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActivateCardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivateCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivateCardRequestMultiError, or nil if none found.
func (m *ActivateCardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivateCardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IssuingBank

	if len(errors) > 0 {
		return ActivateCardRequestMultiError(errors)
	}

	return nil
}

// ActivateCardRequestMultiError is an error wrapping multiple validation
// errors returned by ActivateCardRequest.ValidateAll() if the designated
// constraints aren't met.
type ActivateCardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivateCardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivateCardRequestMultiError) AllErrors() []error { return m }

// ActivateCardRequestValidationError is the validation error returned by
// ActivateCardRequest.Validate if the designated constraints aren't met.
type ActivateCardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivateCardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivateCardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivateCardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivateCardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivateCardRequestValidationError) ErrorName() string {
	return "ActivateCardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ActivateCardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivateCardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivateCardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivateCardRequestValidationError{}

// Validate checks the field values on ActivationStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActivationStatesInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivationStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivationStatesInfoMultiError, or nil if none found.
func (m *ActivationStatesInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivationStatesInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivationStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivationStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivationStatesInfoValidationError{
				field:  "Card",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ActivationStatesInfoMultiError(errors)
	}

	return nil
}

// ActivationStatesInfoMultiError is an error wrapping multiple validation
// errors returned by ActivationStatesInfo.ValidateAll() if the designated
// constraints aren't met.
type ActivationStatesInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivationStatesInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivationStatesInfoMultiError) AllErrors() []error { return m }

// ActivationStatesInfoValidationError is the validation error returned by
// ActivationStatesInfo.Validate if the designated constraints aren't met.
type ActivationStatesInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivationStatesInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivationStatesInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivationStatesInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivationStatesInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivationStatesInfoValidationError) ErrorName() string {
	return "ActivationStatesInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ActivationStatesInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivationStatesInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivationStatesInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivationStatesInfoValidationError{}

// Validate checks the field values on ActivateCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActivateCardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivateCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivateCardResponseMultiError, or nil if none found.
func (m *ActivateCardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivateCardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivateCardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivateCardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivateCardResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetActivationStates()))
		i := 0
		for key := range m.GetActivationStates() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetActivationStates()[key]
			_ = val

			// no validation rules for ActivationStates[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ActivateCardResponseValidationError{
							field:  fmt.Sprintf("ActivationStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ActivateCardResponseValidationError{
							field:  fmt.Sprintf("ActivationStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ActivateCardResponseValidationError{
						field:  fmt.Sprintf("ActivationStates[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return ActivateCardResponseMultiError(errors)
	}

	return nil
}

// ActivateCardResponseMultiError is an error wrapping multiple validation
// errors returned by ActivateCardResponse.ValidateAll() if the designated
// constraints aren't met.
type ActivateCardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivateCardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivateCardResponseMultiError) AllErrors() []error { return m }

// ActivateCardResponseValidationError is the validation error returned by
// ActivateCardResponse.Validate if the designated constraints aren't met.
type ActivateCardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivateCardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivateCardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivateCardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivateCardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivateCardResponseValidationError) ErrorName() string {
	return "ActivateCardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ActivateCardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivateCardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivateCardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivateCardResponseValidationError{}

// Validate checks the field values on FetchCardActivationStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FetchCardActivationStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardActivationStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FetchCardActivationStatusRequestMultiError, or nil if none found.
func (m *FetchCardActivationStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardActivationStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IssuingBank

	if len(errors) > 0 {
		return FetchCardActivationStatusRequestMultiError(errors)
	}

	return nil
}

// FetchCardActivationStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// FetchCardActivationStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchCardActivationStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardActivationStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardActivationStatusRequestMultiError) AllErrors() []error { return m }

// FetchCardActivationStatusRequestValidationError is the validation error
// returned by FetchCardActivationStatusRequest.Validate if the designated
// constraints aren't met.
type FetchCardActivationStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardActivationStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardActivationStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardActivationStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardActivationStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardActivationStatusRequestValidationError) ErrorName() string {
	return "FetchCardActivationStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardActivationStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardActivationStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardActivationStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardActivationStatusRequestValidationError{}

// Validate checks the field values on FetchCardActivationStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FetchCardActivationStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardActivationStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FetchCardActivationStatusResponseMultiError, or nil if none found.
func (m *FetchCardActivationStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardActivationStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCardActivationStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCardActivationStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCardActivationStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetActivationStates()))
		i := 0
		for key := range m.GetActivationStates() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetActivationStates()[key]
			_ = val

			// no validation rules for ActivationStates[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, FetchCardActivationStatusResponseValidationError{
							field:  fmt.Sprintf("ActivationStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, FetchCardActivationStatusResponseValidationError{
							field:  fmt.Sprintf("ActivationStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return FetchCardActivationStatusResponseValidationError{
						field:  fmt.Sprintf("ActivationStates[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return FetchCardActivationStatusResponseMultiError(errors)
	}

	return nil
}

// FetchCardActivationStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// FetchCardActivationStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchCardActivationStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardActivationStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardActivationStatusResponseMultiError) AllErrors() []error { return m }

// FetchCardActivationStatusResponseValidationError is the validation error
// returned by FetchCardActivationStatusResponse.Validate if the designated
// constraints aren't met.
type FetchCardActivationStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardActivationStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardActivationStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardActivationStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardActivationStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardActivationStatusResponseValidationError) ErrorName() string {
	return "FetchCardActivationStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardActivationStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardActivationStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardActivationStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardActivationStatusResponseValidationError{}

// Validate checks the field values on FetchCardDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchCardDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchCardDetailsRequestMultiError, or nil if none found.
func (m *FetchCardDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IssuingBank

	if len(errors) > 0 {
		return FetchCardDetailsRequestMultiError(errors)
	}

	return nil
}

// FetchCardDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by FetchCardDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchCardDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardDetailsRequestMultiError) AllErrors() []error { return m }

// FetchCardDetailsRequestValidationError is the validation error returned by
// FetchCardDetailsRequest.Validate if the designated constraints aren't met.
type FetchCardDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardDetailsRequestValidationError) ErrorName() string {
	return "FetchCardDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardDetailsRequestValidationError{}

// Validate checks the field values on FetchCardDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchCardDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchCardDetailsResponseMultiError, or nil if none found.
func (m *FetchCardDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCardDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCardDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCardDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetCards()))
		i := 0
		for key := range m.GetCards() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCards()[key]
			_ = val

			// no validation rules for Cards[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, FetchCardDetailsResponseValidationError{
							field:  fmt.Sprintf("Cards[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, FetchCardDetailsResponseValidationError{
							field:  fmt.Sprintf("Cards[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return FetchCardDetailsResponseValidationError{
						field:  fmt.Sprintf("Cards[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return FetchCardDetailsResponseMultiError(errors)
	}

	return nil
}

// FetchCardDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by FetchCardDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchCardDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardDetailsResponseMultiError) AllErrors() []error { return m }

// FetchCardDetailsResponseValidationError is the validation error returned by
// FetchCardDetailsResponse.Validate if the designated constraints aren't met.
type FetchCardDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardDetailsResponseValidationError) ErrorName() string {
	return "FetchCardDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardDetailsResponseValidationError{}

// Validate checks the field values on CardDispatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardDispatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardDispatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardDispatchRequestMultiError, or nil if none found.
func (m *CardDispatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CardDispatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetActor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardDispatchRequestValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardDispatchRequestValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardDispatchRequestValidationError{
				field:  "Actor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardDispatchRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardDispatchRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardDispatchRequestValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IssuingBank

	if len(errors) > 0 {
		return CardDispatchRequestMultiError(errors)
	}

	return nil
}

// CardDispatchRequestMultiError is an error wrapping multiple validation
// errors returned by CardDispatchRequest.ValidateAll() if the designated
// constraints aren't met.
type CardDispatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardDispatchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardDispatchRequestMultiError) AllErrors() []error { return m }

// CardDispatchRequestValidationError is the validation error returned by
// CardDispatchRequest.Validate if the designated constraints aren't met.
type CardDispatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardDispatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardDispatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardDispatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardDispatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardDispatchRequestValidationError) ErrorName() string {
	return "CardDispatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CardDispatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardDispatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardDispatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardDispatchRequestValidationError{}

// Validate checks the field values on CardDispatchResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardDispatchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardDispatchResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardDispatchResponseMultiError, or nil if none found.
func (m *CardDispatchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CardDispatchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardDispatchResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardDispatchResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardDispatchResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for State

	if len(errors) > 0 {
		return CardDispatchResponseMultiError(errors)
	}

	return nil
}

// CardDispatchResponseMultiError is an error wrapping multiple validation
// errors returned by CardDispatchResponse.ValidateAll() if the designated
// constraints aren't met.
type CardDispatchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardDispatchResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardDispatchResponseMultiError) AllErrors() []error { return m }

// CardDispatchResponseValidationError is the validation error returned by
// CardDispatchResponse.Validate if the designated constraints aren't met.
type CardDispatchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardDispatchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardDispatchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardDispatchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardDispatchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardDispatchResponseValidationError) ErrorName() string {
	return "CardDispatchResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CardDispatchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardDispatchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardDispatchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardDispatchResponseValidationError{}

// Validate checks the field values on CardDeliveryTrackingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardDeliveryTrackingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardDeliveryTrackingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardDeliveryTrackingRequestMultiError, or nil if none found.
func (m *CardDeliveryTrackingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CardDeliveryTrackingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for CardId

	if len(errors) > 0 {
		return CardDeliveryTrackingRequestMultiError(errors)
	}

	return nil
}

// CardDeliveryTrackingRequestMultiError is an error wrapping multiple
// validation errors returned by CardDeliveryTrackingRequest.ValidateAll() if
// the designated constraints aren't met.
type CardDeliveryTrackingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardDeliveryTrackingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardDeliveryTrackingRequestMultiError) AllErrors() []error { return m }

// CardDeliveryTrackingRequestValidationError is the validation error returned
// by CardDeliveryTrackingRequest.Validate if the designated constraints
// aren't met.
type CardDeliveryTrackingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardDeliveryTrackingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardDeliveryTrackingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardDeliveryTrackingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardDeliveryTrackingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardDeliveryTrackingRequestValidationError) ErrorName() string {
	return "CardDeliveryTrackingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CardDeliveryTrackingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardDeliveryTrackingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardDeliveryTrackingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardDeliveryTrackingRequestValidationError{}

// Validate checks the field values on CardDeliveryTrackingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardDeliveryTrackingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardDeliveryTrackingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardDeliveryTrackingResponseMultiError, or nil if none found.
func (m *CardDeliveryTrackingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CardDeliveryTrackingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardDeliveryTrackingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardDeliveryTrackingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardDeliveryTrackingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardGenerationDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardDeliveryTrackingResponseValidationError{
					field:  "CardGenerationDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardDeliveryTrackingResponseValidationError{
					field:  "CardGenerationDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardGenerationDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardDeliveryTrackingResponseValidationError{
				field:  "CardGenerationDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardDispatchDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardDeliveryTrackingResponseValidationError{
					field:  "CardDispatchDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardDeliveryTrackingResponseValidationError{
					field:  "CardDispatchDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardDispatchDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardDeliveryTrackingResponseValidationError{
				field:  "CardDispatchDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Awb

	if all {
		switch v := interface{}(m.GetCardReturnedDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardDeliveryTrackingResponseValidationError{
					field:  "CardReturnedDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardDeliveryTrackingResponseValidationError{
					field:  "CardReturnedDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardReturnedDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardDeliveryTrackingResponseValidationError{
				field:  "CardReturnedDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CourierPartner

	// no validation rules for Remarks

	// no validation rules for State

	if len(errors) > 0 {
		return CardDeliveryTrackingResponseMultiError(errors)
	}

	return nil
}

// CardDeliveryTrackingResponseMultiError is an error wrapping multiple
// validation errors returned by CardDeliveryTrackingResponse.ValidateAll() if
// the designated constraints aren't met.
type CardDeliveryTrackingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardDeliveryTrackingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardDeliveryTrackingResponseMultiError) AllErrors() []error { return m }

// CardDeliveryTrackingResponseValidationError is the validation error returned
// by CardDeliveryTrackingResponse.Validate if the designated constraints
// aren't met.
type CardDeliveryTrackingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardDeliveryTrackingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardDeliveryTrackingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardDeliveryTrackingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardDeliveryTrackingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardDeliveryTrackingResponseValidationError) ErrorName() string {
	return "CardDeliveryTrackingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CardDeliveryTrackingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardDeliveryTrackingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardDeliveryTrackingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardDeliveryTrackingResponseValidationError{}

// Validate checks the field values on SetCardPinRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SetCardPinRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetCardPinRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetCardPinRequestMultiError, or nil if none found.
func (m *SetCardPinRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetCardPinRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for CredBlock

	// no validation rules for PinSetFlow

	// no validation rules for RequestId

	// no validation rules for UiEntryPoint

	// no validation rules for AuthAttemptId

	if len(errors) > 0 {
		return SetCardPinRequestMultiError(errors)
	}

	return nil
}

// SetCardPinRequestMultiError is an error wrapping multiple validation errors
// returned by SetCardPinRequest.ValidateAll() if the designated constraints
// aren't met.
type SetCardPinRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetCardPinRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetCardPinRequestMultiError) AllErrors() []error { return m }

// SetCardPinRequestValidationError is the validation error returned by
// SetCardPinRequest.Validate if the designated constraints aren't met.
type SetCardPinRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetCardPinRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetCardPinRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetCardPinRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetCardPinRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetCardPinRequestValidationError) ErrorName() string {
	return "SetCardPinRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetCardPinRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetCardPinRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetCardPinRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetCardPinRequestValidationError{}

// Validate checks the field values on SetCardPinResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetCardPinResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetCardPinResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetCardPinResponseMultiError, or nil if none found.
func (m *SetCardPinResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SetCardPinResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetCardPinResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetCardPinResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetCardPinResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return SetCardPinResponseMultiError(errors)
	}

	return nil
}

// SetCardPinResponseMultiError is an error wrapping multiple validation errors
// returned by SetCardPinResponse.ValidateAll() if the designated constraints
// aren't met.
type SetCardPinResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetCardPinResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetCardPinResponseMultiError) AllErrors() []error { return m }

// SetCardPinResponseValidationError is the validation error returned by
// SetCardPinResponse.Validate if the designated constraints aren't met.
type SetCardPinResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetCardPinResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetCardPinResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetCardPinResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetCardPinResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetCardPinResponseValidationError) ErrorName() string {
	return "SetCardPinResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SetCardPinResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetCardPinResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetCardPinResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetCardPinResponseValidationError{}

// Validate checks the field values on ChangeCardPinRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChangeCardPinRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangeCardPinRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangeCardPinRequestMultiError, or nil if none found.
func (m *ChangeCardPinRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangeCardPinRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for CredBlock

	if len(errors) > 0 {
		return ChangeCardPinRequestMultiError(errors)
	}

	return nil
}

// ChangeCardPinRequestMultiError is an error wrapping multiple validation
// errors returned by ChangeCardPinRequest.ValidateAll() if the designated
// constraints aren't met.
type ChangeCardPinRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangeCardPinRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangeCardPinRequestMultiError) AllErrors() []error { return m }

// ChangeCardPinRequestValidationError is the validation error returned by
// ChangeCardPinRequest.Validate if the designated constraints aren't met.
type ChangeCardPinRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangeCardPinRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangeCardPinRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangeCardPinRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangeCardPinRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangeCardPinRequestValidationError) ErrorName() string {
	return "ChangeCardPinRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ChangeCardPinRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangeCardPinRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangeCardPinRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangeCardPinRequestValidationError{}

// Validate checks the field values on ChangeCardPinResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChangeCardPinResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangeCardPinResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangeCardPinResponseMultiError, or nil if none found.
func (m *ChangeCardPinResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangeCardPinResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChangeCardPinResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChangeCardPinResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChangeCardPinResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ChangeCardPinResponseMultiError(errors)
	}

	return nil
}

// ChangeCardPinResponseMultiError is an error wrapping multiple validation
// errors returned by ChangeCardPinResponse.ValidateAll() if the designated
// constraints aren't met.
type ChangeCardPinResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangeCardPinResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangeCardPinResponseMultiError) AllErrors() []error { return m }

// ChangeCardPinResponseValidationError is the validation error returned by
// ChangeCardPinResponse.Validate if the designated constraints aren't met.
type ChangeCardPinResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangeCardPinResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangeCardPinResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangeCardPinResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangeCardPinResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangeCardPinResponseValidationError) ErrorName() string {
	return "ChangeCardPinResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ChangeCardPinResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangeCardPinResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangeCardPinResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangeCardPinResponseValidationError{}

// Validate checks the field values on ResetCardPinRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetCardPinRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetCardPinRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetCardPinRequestMultiError, or nil if none found.
func (m *ResetCardPinRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetCardPinRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for CredBlock

	// no validation rules for PinSetFlow

	// no validation rules for RequestId

	// no validation rules for AuthAttemptId

	if len(errors) > 0 {
		return ResetCardPinRequestMultiError(errors)
	}

	return nil
}

// ResetCardPinRequestMultiError is an error wrapping multiple validation
// errors returned by ResetCardPinRequest.ValidateAll() if the designated
// constraints aren't met.
type ResetCardPinRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetCardPinRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetCardPinRequestMultiError) AllErrors() []error { return m }

// ResetCardPinRequestValidationError is the validation error returned by
// ResetCardPinRequest.Validate if the designated constraints aren't met.
type ResetCardPinRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetCardPinRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetCardPinRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetCardPinRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetCardPinRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetCardPinRequestValidationError) ErrorName() string {
	return "ResetCardPinRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResetCardPinRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetCardPinRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetCardPinRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetCardPinRequestValidationError{}

// Validate checks the field values on ResetCardPinResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetCardPinResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetCardPinResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetCardPinResponseMultiError, or nil if none found.
func (m *ResetCardPinResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetCardPinResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResetCardPinResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResetCardPinResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResetCardPinResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return ResetCardPinResponseMultiError(errors)
	}

	return nil
}

// ResetCardPinResponseMultiError is an error wrapping multiple validation
// errors returned by ResetCardPinResponse.ValidateAll() if the designated
// constraints aren't met.
type ResetCardPinResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetCardPinResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetCardPinResponseMultiError) AllErrors() []error { return m }

// ResetCardPinResponseValidationError is the validation error returned by
// ResetCardPinResponse.Validate if the designated constraints aren't met.
type ResetCardPinResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetCardPinResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetCardPinResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetCardPinResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetCardPinResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetCardPinResponseValidationError) ErrorName() string {
	return "ResetCardPinResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ResetCardPinResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetCardPinResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetCardPinResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetCardPinResponseValidationError{}

// Validate checks the field values on ValidateCardPinRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateCardPinRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateCardPinRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateCardPinRequestMultiError, or nil if none found.
func (m *ValidateCardPinRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateCardPinRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for CredBlock

	if len(errors) > 0 {
		return ValidateCardPinRequestMultiError(errors)
	}

	return nil
}

// ValidateCardPinRequestMultiError is an error wrapping multiple validation
// errors returned by ValidateCardPinRequest.ValidateAll() if the designated
// constraints aren't met.
type ValidateCardPinRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateCardPinRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateCardPinRequestMultiError) AllErrors() []error { return m }

// ValidateCardPinRequestValidationError is the validation error returned by
// ValidateCardPinRequest.Validate if the designated constraints aren't met.
type ValidateCardPinRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateCardPinRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateCardPinRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateCardPinRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateCardPinRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateCardPinRequestValidationError) ErrorName() string {
	return "ValidateCardPinRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateCardPinRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateCardPinRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateCardPinRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateCardPinRequestValidationError{}

// Validate checks the field values on ValidateCardPinResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateCardPinResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateCardPinResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateCardPinResponseMultiError, or nil if none found.
func (m *ValidateCardPinResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateCardPinResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateCardPinResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateCardPinResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateCardPinResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateCardPinResponseMultiError(errors)
	}

	return nil
}

// ValidateCardPinResponseMultiError is an error wrapping multiple validation
// errors returned by ValidateCardPinResponse.ValidateAll() if the designated
// constraints aren't met.
type ValidateCardPinResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateCardPinResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateCardPinResponseMultiError) AllErrors() []error { return m }

// ValidateCardPinResponseValidationError is the validation error returned by
// ValidateCardPinResponse.Validate if the designated constraints aren't met.
type ValidateCardPinResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateCardPinResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateCardPinResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateCardPinResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateCardPinResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateCardPinResponseValidationError) ErrorName() string {
	return "ValidateCardPinResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateCardPinResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateCardPinResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateCardPinResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateCardPinResponseValidationError{}

// Validate checks the field values on CardValidateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardValidateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardValidateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardValidateRequestMultiError, or nil if none found.
func (m *CardValidateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CardValidateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetActor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardValidateRequestValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardValidateRequestValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardValidateRequestValidationError{
				field:  "Actor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IssuingBank

	// no validation rules for CredBlock

	if len(errors) > 0 {
		return CardValidateRequestMultiError(errors)
	}

	return nil
}

// CardValidateRequestMultiError is an error wrapping multiple validation
// errors returned by CardValidateRequest.ValidateAll() if the designated
// constraints aren't met.
type CardValidateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardValidateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardValidateRequestMultiError) AllErrors() []error { return m }

// CardValidateRequestValidationError is the validation error returned by
// CardValidateRequest.Validate if the designated constraints aren't met.
type CardValidateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardValidateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardValidateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardValidateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardValidateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardValidateRequestValidationError) ErrorName() string {
	return "CardValidateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CardValidateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardValidateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardValidateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardValidateRequestValidationError{}

// Validate checks the field values on CardValidateResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardValidateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardValidateResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardValidateResponseMultiError, or nil if none found.
func (m *CardValidateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CardValidateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardValidateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardValidateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardValidateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for State

	if len(errors) > 0 {
		return CardValidateResponseMultiError(errors)
	}

	return nil
}

// CardValidateResponseMultiError is an error wrapping multiple validation
// errors returned by CardValidateResponse.ValidateAll() if the designated
// constraints aren't met.
type CardValidateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardValidateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardValidateResponseMultiError) AllErrors() []error { return m }

// CardValidateResponseValidationError is the validation error returned by
// CardValidateResponse.Validate if the designated constraints aren't met.
type CardValidateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardValidateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardValidateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardValidateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardValidateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardValidateResponseValidationError) ErrorName() string {
	return "CardValidateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CardValidateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardValidateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardValidateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardValidateResponseValidationError{}

// Validate checks the field values on GetCardGroupsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardGroupsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardGroupsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCardGroupsRequestMultiError, or nil if none found.
func (m *GetCardGroupsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardGroupsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetActor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardGroupsRequestValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardGroupsRequestValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardGroupsRequestValidationError{
				field:  "Actor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GetAll

	// no validation rules for NumGroups

	// no validation rules for AscOrderCreatedTime

	if len(errors) > 0 {
		return GetCardGroupsRequestMultiError(errors)
	}

	return nil
}

// GetCardGroupsRequestMultiError is an error wrapping multiple validation
// errors returned by GetCardGroupsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCardGroupsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardGroupsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardGroupsRequestMultiError) AllErrors() []error { return m }

// GetCardGroupsRequestValidationError is the validation error returned by
// GetCardGroupsRequest.Validate if the designated constraints aren't met.
type GetCardGroupsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardGroupsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardGroupsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardGroupsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardGroupsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardGroupsRequestValidationError) ErrorName() string {
	return "GetCardGroupsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardGroupsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardGroupsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardGroupsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardGroupsRequestValidationError{}

// Validate checks the field values on GetCardGroupsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardGroupsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardGroupsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCardGroupsResponseMultiError, or nil if none found.
func (m *GetCardGroupsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardGroupsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardGroupsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardGroupsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardGroupsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCardGroupsResponseValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCardGroupsResponseValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCardGroupsResponseValidationError{
					field:  fmt.Sprintf("Cards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCardGroupsResponseMultiError(errors)
	}

	return nil
}

// GetCardGroupsResponseMultiError is an error wrapping multiple validation
// errors returned by GetCardGroupsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCardGroupsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardGroupsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardGroupsResponseMultiError) AllErrors() []error { return m }

// GetCardGroupsResponseValidationError is the validation error returned by
// GetCardGroupsResponse.Validate if the designated constraints aren't met.
type GetCardGroupsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardGroupsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardGroupsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardGroupsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardGroupsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardGroupsResponseValidationError) ErrorName() string {
	return "GetCardGroupsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardGroupsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardGroupsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardGroupsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardGroupsResponseValidationError{}

// Validate checks the field values on CardPinStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardPinStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardPinStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardPinStatusRequestMultiError, or nil if none found.
func (m *CardPinStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CardPinStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CardPinStatusRequestMultiError(errors)
	}

	return nil
}

// CardPinStatusRequestMultiError is an error wrapping multiple validation
// errors returned by CardPinStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type CardPinStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardPinStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardPinStatusRequestMultiError) AllErrors() []error { return m }

// CardPinStatusRequestValidationError is the validation error returned by
// CardPinStatusRequest.Validate if the designated constraints aren't met.
type CardPinStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardPinStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardPinStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardPinStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardPinStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardPinStatusRequestValidationError) ErrorName() string {
	return "CardPinStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CardPinStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardPinStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardPinStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardPinStatusRequestValidationError{}

// Validate checks the field values on CardPinStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardPinStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardPinStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardPinStatusResponseMultiError, or nil if none found.
func (m *CardPinStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CardPinStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardPinStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardPinStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardPinStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardPinStates

	if len(errors) > 0 {
		return CardPinStatusResponseMultiError(errors)
	}

	return nil
}

// CardPinStatusResponseMultiError is an error wrapping multiple validation
// errors returned by CardPinStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type CardPinStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardPinStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardPinStatusResponseMultiError) AllErrors() []error { return m }

// CardPinStatusResponseValidationError is the validation error returned by
// CardPinStatusResponse.Validate if the designated constraints aren't met.
type CardPinStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardPinStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardPinStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardPinStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardPinStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardPinStatusResponseValidationError) ErrorName() string {
	return "CardPinStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CardPinStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardPinStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardPinStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardPinStatusResponseValidationError{}

// Validate checks the field values on GenerateTxnIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateTxnIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateTxnIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateTxnIdRequestMultiError, or nil if none found.
func (m *GenerateTxnIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateTxnIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnType

	// no validation rules for Vendor

	// no validation rules for CardId

	if len(errors) > 0 {
		return GenerateTxnIdRequestMultiError(errors)
	}

	return nil
}

// GenerateTxnIdRequestMultiError is an error wrapping multiple validation
// errors returned by GenerateTxnIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateTxnIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateTxnIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateTxnIdRequestMultiError) AllErrors() []error { return m }

// GenerateTxnIdRequestValidationError is the validation error returned by
// GenerateTxnIdRequest.Validate if the designated constraints aren't met.
type GenerateTxnIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateTxnIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateTxnIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateTxnIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateTxnIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateTxnIdRequestValidationError) ErrorName() string {
	return "GenerateTxnIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateTxnIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateTxnIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateTxnIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateTxnIdRequestValidationError{}

// Validate checks the field values on GenerateTxnIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateTxnIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateTxnIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateTxnIdResponseMultiError, or nil if none found.
func (m *GenerateTxnIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateTxnIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateTxnIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateTxnIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateTxnIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TxnId

	// no validation rules for MaskedCardNumber

	switch v := m.ResponseParams.(type) {
	case *GenerateTxnIdResponse_PinSetFlow:
		if v == nil {
			err := GenerateTxnIdResponseValidationError{
				field:  "ResponseParams",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PinSetFlow
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GenerateTxnIdResponseMultiError(errors)
	}

	return nil
}

// GenerateTxnIdResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateTxnIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateTxnIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateTxnIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateTxnIdResponseMultiError) AllErrors() []error { return m }

// GenerateTxnIdResponseValidationError is the validation error returned by
// GenerateTxnIdResponse.Validate if the designated constraints aren't met.
type GenerateTxnIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateTxnIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateTxnIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateTxnIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateTxnIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateTxnIdResponseValidationError) ErrorName() string {
	return "GenerateTxnIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateTxnIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateTxnIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateTxnIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateTxnIdResponseValidationError{}

// Validate checks the field values on GetCardDetailsWithCvvRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardDetailsWithCvvRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardDetailsWithCvvRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCardDetailsWithCvvRequestMultiError, or nil if none found.
func (m *GetCardDetailsWithCvvRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardDetailsWithCvvRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for CredBlock

	// no validation rules for RequestId

	if len(errors) > 0 {
		return GetCardDetailsWithCvvRequestMultiError(errors)
	}

	return nil
}

// GetCardDetailsWithCvvRequestMultiError is an error wrapping multiple
// validation errors returned by GetCardDetailsWithCvvRequest.ValidateAll() if
// the designated constraints aren't met.
type GetCardDetailsWithCvvRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardDetailsWithCvvRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardDetailsWithCvvRequestMultiError) AllErrors() []error { return m }

// GetCardDetailsWithCvvRequestValidationError is the validation error returned
// by GetCardDetailsWithCvvRequest.Validate if the designated constraints
// aren't met.
type GetCardDetailsWithCvvRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardDetailsWithCvvRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardDetailsWithCvvRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardDetailsWithCvvRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardDetailsWithCvvRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardDetailsWithCvvRequestValidationError) ErrorName() string {
	return "GetCardDetailsWithCvvRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardDetailsWithCvvRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardDetailsWithCvvRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardDetailsWithCvvRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardDetailsWithCvvRequestValidationError{}

// Validate checks the field values on GetCardDetailsWithCvvResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardDetailsWithCvvResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardDetailsWithCvvResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCardDetailsWithCvvResponseMultiError, or nil if none found.
func (m *GetCardDetailsWithCvvResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardDetailsWithCvvResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardDetailsWithCvvResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardDetailsWithCvvResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardDetailsWithCvvResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardDetailsWithCvvResponseValidationError{
					field:  "CardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardDetailsWithCvvResponseValidationError{
					field:  "CardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardDetailsWithCvvResponseValidationError{
				field:  "CardInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return GetCardDetailsWithCvvResponseMultiError(errors)
	}

	return nil
}

// GetCardDetailsWithCvvResponseMultiError is an error wrapping multiple
// validation errors returned by GetCardDetailsWithCvvResponse.ValidateAll()
// if the designated constraints aren't met.
type GetCardDetailsWithCvvResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardDetailsWithCvvResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardDetailsWithCvvResponseMultiError) AllErrors() []error { return m }

// GetCardDetailsWithCvvResponseValidationError is the validation error
// returned by GetCardDetailsWithCvvResponse.Validate if the designated
// constraints aren't met.
type GetCardDetailsWithCvvResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardDetailsWithCvvResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardDetailsWithCvvResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardDetailsWithCvvResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardDetailsWithCvvResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardDetailsWithCvvResponseValidationError) ErrorName() string {
	return "GetCardDetailsWithCvvResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardDetailsWithCvvResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardDetailsWithCvvResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardDetailsWithCvvResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardDetailsWithCvvResponseValidationError{}

// Validate checks the field values on RenewCardRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RenewCardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RenewCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RenewCardRequestMultiError, or nil if none found.
func (m *RenewCardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RenewCardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for BlockCardReason

	// no validation rules for BlockCardProvenance

	// no validation rules for ActorId

	// no validation rules for AddressType

	// no validation rules for CardForm

	if len(errors) > 0 {
		return RenewCardRequestMultiError(errors)
	}

	return nil
}

// RenewCardRequestMultiError is an error wrapping multiple validation errors
// returned by RenewCardRequest.ValidateAll() if the designated constraints
// aren't met.
type RenewCardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RenewCardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RenewCardRequestMultiError) AllErrors() []error { return m }

// RenewCardRequestValidationError is the validation error returned by
// RenewCardRequest.Validate if the designated constraints aren't met.
type RenewCardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RenewCardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RenewCardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RenewCardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RenewCardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RenewCardRequestValidationError) ErrorName() string { return "RenewCardRequestValidationError" }

// Error satisfies the builtin error interface
func (e RenewCardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRenewCardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RenewCardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RenewCardRequestValidationError{}

// Validate checks the field values on RenewCardResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RenewCardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RenewCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RenewCardResponseMultiError, or nil if none found.
func (m *RenewCardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RenewCardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RenewCardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RenewCardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RenewCardResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RenewCardResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RenewCardResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RenewCardResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RenewCardResponseMultiError(errors)
	}

	return nil
}

// RenewCardResponseMultiError is an error wrapping multiple validation errors
// returned by RenewCardResponse.ValidateAll() if the designated constraints
// aren't met.
type RenewCardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RenewCardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RenewCardResponseMultiError) AllErrors() []error { return m }

// RenewCardResponseValidationError is the validation error returned by
// RenewCardResponse.Validate if the designated constraints aren't met.
type RenewCardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RenewCardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RenewCardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RenewCardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RenewCardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RenewCardResponseValidationError) ErrorName() string {
	return "RenewCardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RenewCardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRenewCardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RenewCardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RenewCardResponseValidationError{}

// Validate checks the field values on RenewCardStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RenewCardStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RenewCardStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RenewCardStatusRequestMultiError, or nil if none found.
func (m *RenewCardStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RenewCardStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	if len(errors) > 0 {
		return RenewCardStatusRequestMultiError(errors)
	}

	return nil
}

// RenewCardStatusRequestMultiError is an error wrapping multiple validation
// errors returned by RenewCardStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type RenewCardStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RenewCardStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RenewCardStatusRequestMultiError) AllErrors() []error { return m }

// RenewCardStatusRequestValidationError is the validation error returned by
// RenewCardStatusRequest.Validate if the designated constraints aren't met.
type RenewCardStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RenewCardStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RenewCardStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RenewCardStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RenewCardStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RenewCardStatusRequestValidationError) ErrorName() string {
	return "RenewCardStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RenewCardStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRenewCardStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RenewCardStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RenewCardStatusRequestValidationError{}

// Validate checks the field values on RenewCardStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RenewCardStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RenewCardStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RenewCardStatusResponseMultiError, or nil if none found.
func (m *RenewCardStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RenewCardStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RenewCardStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RenewCardStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RenewCardStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NewCardId

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RenewCardStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RenewCardStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RenewCardStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return RenewCardStatusResponseMultiError(errors)
	}

	return nil
}

// RenewCardStatusResponseMultiError is an error wrapping multiple validation
// errors returned by RenewCardStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type RenewCardStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RenewCardStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RenewCardStatusResponseMultiError) AllErrors() []error { return m }

// RenewCardStatusResponseValidationError is the validation error returned by
// RenewCardStatusResponse.Validate if the designated constraints aren't met.
type RenewCardStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RenewCardStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RenewCardStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RenewCardStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RenewCardStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RenewCardStatusResponseValidationError) ErrorName() string {
	return "RenewCardStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RenewCardStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRenewCardStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RenewCardStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RenewCardStatusResponseValidationError{}

// Validate checks the field values on VerifyQRCodeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyQRCodeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyQRCodeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyQRCodeRequestMultiError, or nil if none found.
func (m *VerifyQRCodeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyQRCodeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetActor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyQRCodeRequestValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyQRCodeRequestValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyQRCodeRequestValidationError{
				field:  "Actor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	// no validation rules for QrData

	// no validation rules for CardPrintingVendor

	if len(errors) > 0 {
		return VerifyQRCodeRequestMultiError(errors)
	}

	return nil
}

// VerifyQRCodeRequestMultiError is an error wrapping multiple validation
// errors returned by VerifyQRCodeRequest.ValidateAll() if the designated
// constraints aren't met.
type VerifyQRCodeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyQRCodeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyQRCodeRequestMultiError) AllErrors() []error { return m }

// VerifyQRCodeRequestValidationError is the validation error returned by
// VerifyQRCodeRequest.Validate if the designated constraints aren't met.
type VerifyQRCodeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyQRCodeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyQRCodeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyQRCodeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyQRCodeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyQRCodeRequestValidationError) ErrorName() string {
	return "VerifyQRCodeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyQRCodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyQRCodeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyQRCodeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyQRCodeRequestValidationError{}

// Validate checks the field values on VerifyQRCodeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyQRCodeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyQRCodeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyQRCodeResponseMultiError, or nil if none found.
func (m *VerifyQRCodeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyQRCodeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyQRCodeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyQRCodeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyQRCodeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyQRCodeResponseMultiError(errors)
	}

	return nil
}

// VerifyQRCodeResponseMultiError is an error wrapping multiple validation
// errors returned by VerifyQRCodeResponse.ValidateAll() if the designated
// constraints aren't met.
type VerifyQRCodeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyQRCodeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyQRCodeResponseMultiError) AllErrors() []error { return m }

// VerifyQRCodeResponseValidationError is the validation error returned by
// VerifyQRCodeResponse.Validate if the designated constraints aren't met.
type VerifyQRCodeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyQRCodeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyQRCodeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyQRCodeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyQRCodeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyQRCodeResponseValidationError) ErrorName() string {
	return "VerifyQRCodeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyQRCodeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyQRCodeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyQRCodeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyQRCodeResponseValidationError{}

// Validate checks the field values on FetchDeliveryTrackingStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FetchDeliveryTrackingStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchDeliveryTrackingStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FetchDeliveryTrackingStatusRequestMultiError, or nil if none found.
func (m *FetchDeliveryTrackingStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchDeliveryTrackingStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	if len(errors) > 0 {
		return FetchDeliveryTrackingStatusRequestMultiError(errors)
	}

	return nil
}

// FetchDeliveryTrackingStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// FetchDeliveryTrackingStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchDeliveryTrackingStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchDeliveryTrackingStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchDeliveryTrackingStatusRequestMultiError) AllErrors() []error { return m }

// FetchDeliveryTrackingStatusRequestValidationError is the validation error
// returned by FetchDeliveryTrackingStatusRequest.Validate if the designated
// constraints aren't met.
type FetchDeliveryTrackingStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchDeliveryTrackingStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchDeliveryTrackingStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchDeliveryTrackingStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchDeliveryTrackingStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchDeliveryTrackingStatusRequestValidationError) ErrorName() string {
	return "FetchDeliveryTrackingStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchDeliveryTrackingStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchDeliveryTrackingStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchDeliveryTrackingStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchDeliveryTrackingStatusRequestValidationError{}

// Validate checks the field values on FetchDeliveryTrackingStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FetchDeliveryTrackingStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchDeliveryTrackingStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FetchDeliveryTrackingStatusResponseMultiError, or nil if none found.
func (m *FetchDeliveryTrackingStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchDeliveryTrackingStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchDeliveryTrackingStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchDeliveryTrackingStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchDeliveryTrackingStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeliveryState

	// no validation rules for MobileNumberUpdated

	if all {
		switch v := interface{}(m.GetCardActivatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchDeliveryTrackingStatusResponseValidationError{
					field:  "CardActivatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchDeliveryTrackingStatusResponseValidationError{
					field:  "CardActivatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardActivatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchDeliveryTrackingStatusResponseValidationError{
				field:  "CardActivatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchDeliveryTrackingStatusResponseMultiError(errors)
	}

	return nil
}

// FetchDeliveryTrackingStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// FetchDeliveryTrackingStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchDeliveryTrackingStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchDeliveryTrackingStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchDeliveryTrackingStatusResponseMultiError) AllErrors() []error { return m }

// FetchDeliveryTrackingStatusResponseValidationError is the validation error
// returned by FetchDeliveryTrackingStatusResponse.Validate if the designated
// constraints aren't met.
type FetchDeliveryTrackingStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchDeliveryTrackingStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchDeliveryTrackingStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchDeliveryTrackingStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchDeliveryTrackingStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchDeliveryTrackingStatusResponseValidationError) ErrorName() string {
	return "FetchDeliveryTrackingStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchDeliveryTrackingStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchDeliveryTrackingStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchDeliveryTrackingStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchDeliveryTrackingStatusResponseValidationError{}

// Validate checks the field values on FetchTransactionableCardsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FetchTransactionableCardsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchTransactionableCardsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FetchTransactionableCardsRequestMultiError, or nil if none found.
func (m *FetchTransactionableCardsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchTransactionableCardsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return FetchTransactionableCardsRequestMultiError(errors)
	}

	return nil
}

// FetchTransactionableCardsRequestMultiError is an error wrapping multiple
// validation errors returned by
// FetchTransactionableCardsRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchTransactionableCardsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchTransactionableCardsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchTransactionableCardsRequestMultiError) AllErrors() []error { return m }

// FetchTransactionableCardsRequestValidationError is the validation error
// returned by FetchTransactionableCardsRequest.Validate if the designated
// constraints aren't met.
type FetchTransactionableCardsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchTransactionableCardsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchTransactionableCardsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchTransactionableCardsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchTransactionableCardsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchTransactionableCardsRequestValidationError) ErrorName() string {
	return "FetchTransactionableCardsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchTransactionableCardsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchTransactionableCardsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchTransactionableCardsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchTransactionableCardsRequestValidationError{}

// Validate checks the field values on FetchTransactionableCardsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FetchTransactionableCardsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchTransactionableCardsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FetchTransactionableCardsResponseMultiError, or nil if none found.
func (m *FetchTransactionableCardsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchTransactionableCardsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchTransactionableCardsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchTransactionableCardsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchTransactionableCardsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FetchTransactionableCardsResponseValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FetchTransactionableCardsResponseValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FetchTransactionableCardsResponseValidationError{
					field:  fmt.Sprintf("Cards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FetchTransactionableCardsResponseMultiError(errors)
	}

	return nil
}

// FetchTransactionableCardsResponseMultiError is an error wrapping multiple
// validation errors returned by
// FetchTransactionableCardsResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchTransactionableCardsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchTransactionableCardsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchTransactionableCardsResponseMultiError) AllErrors() []error { return m }

// FetchTransactionableCardsResponseValidationError is the validation error
// returned by FetchTransactionableCardsResponse.Validate if the designated
// constraints aren't met.
type FetchTransactionableCardsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchTransactionableCardsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchTransactionableCardsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchTransactionableCardsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchTransactionableCardsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchTransactionableCardsResponseValidationError) ErrorName() string {
	return "FetchTransactionableCardsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchTransactionableCardsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchTransactionableCardsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchTransactionableCardsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchTransactionableCardsResponseValidationError{}

// Validate checks the field values on FetchCardDetailsByAccountIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FetchCardDetailsByAccountIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardDetailsByAccountIdRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FetchCardDetailsByAccountIdRequestMultiError, or nil if none found.
func (m *FetchCardDetailsByAccountIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardDetailsByAccountIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SavingsAccountId

	if len(errors) > 0 {
		return FetchCardDetailsByAccountIdRequestMultiError(errors)
	}

	return nil
}

// FetchCardDetailsByAccountIdRequestMultiError is an error wrapping multiple
// validation errors returned by
// FetchCardDetailsByAccountIdRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchCardDetailsByAccountIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardDetailsByAccountIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardDetailsByAccountIdRequestMultiError) AllErrors() []error { return m }

// FetchCardDetailsByAccountIdRequestValidationError is the validation error
// returned by FetchCardDetailsByAccountIdRequest.Validate if the designated
// constraints aren't met.
type FetchCardDetailsByAccountIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardDetailsByAccountIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardDetailsByAccountIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardDetailsByAccountIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardDetailsByAccountIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardDetailsByAccountIdRequestValidationError) ErrorName() string {
	return "FetchCardDetailsByAccountIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardDetailsByAccountIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardDetailsByAccountIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardDetailsByAccountIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardDetailsByAccountIdRequestValidationError{}

// Validate checks the field values on FetchCardDetailsByAccountIdResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FetchCardDetailsByAccountIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardDetailsByAccountIdResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FetchCardDetailsByAccountIdResponseMultiError, or nil if none found.
func (m *FetchCardDetailsByAccountIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardDetailsByAccountIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCardDetailsByAccountIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCardDetailsByAccountIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCardDetailsByAccountIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCard() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FetchCardDetailsByAccountIdResponseValidationError{
						field:  fmt.Sprintf("Card[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FetchCardDetailsByAccountIdResponseValidationError{
						field:  fmt.Sprintf("Card[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FetchCardDetailsByAccountIdResponseValidationError{
					field:  fmt.Sprintf("Card[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FetchCardDetailsByAccountIdResponseMultiError(errors)
	}

	return nil
}

// FetchCardDetailsByAccountIdResponseMultiError is an error wrapping multiple
// validation errors returned by
// FetchCardDetailsByAccountIdResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchCardDetailsByAccountIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardDetailsByAccountIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardDetailsByAccountIdResponseMultiError) AllErrors() []error { return m }

// FetchCardDetailsByAccountIdResponseValidationError is the validation error
// returned by FetchCardDetailsByAccountIdResponse.Validate if the designated
// constraints aren't met.
type FetchCardDetailsByAccountIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardDetailsByAccountIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardDetailsByAccountIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardDetailsByAccountIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardDetailsByAccountIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardDetailsByAccountIdResponseValidationError) ErrorName() string {
	return "FetchCardDetailsByAccountIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardDetailsByAccountIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardDetailsByAccountIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardDetailsByAccountIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardDetailsByAccountIdResponseValidationError{}

// Validate checks the field values on FetchCardTrackingDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchCardTrackingDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardTrackingDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FetchCardTrackingDetailsRequestMultiError, or nil if none found.
func (m *FetchCardTrackingDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardTrackingDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Limit

	if len(errors) > 0 {
		return FetchCardTrackingDetailsRequestMultiError(errors)
	}

	return nil
}

// FetchCardTrackingDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by FetchCardTrackingDetailsRequest.ValidateAll()
// if the designated constraints aren't met.
type FetchCardTrackingDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardTrackingDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardTrackingDetailsRequestMultiError) AllErrors() []error { return m }

// FetchCardTrackingDetailsRequestValidationError is the validation error
// returned by FetchCardTrackingDetailsRequest.Validate if the designated
// constraints aren't met.
type FetchCardTrackingDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardTrackingDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardTrackingDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardTrackingDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardTrackingDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardTrackingDetailsRequestValidationError) ErrorName() string {
	return "FetchCardTrackingDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardTrackingDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardTrackingDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardTrackingDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardTrackingDetailsRequestValidationError{}

// Validate checks the field values on FetchCardTrackingDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FetchCardTrackingDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardTrackingDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FetchCardTrackingDetailsResponseMultiError, or nil if none found.
func (m *FetchCardTrackingDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardTrackingDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCardTrackingDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCardTrackingDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCardTrackingDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchCardTrackingDetailsResponseMultiError(errors)
	}

	return nil
}

// FetchCardTrackingDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// FetchCardTrackingDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchCardTrackingDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardTrackingDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardTrackingDetailsResponseMultiError) AllErrors() []error { return m }

// FetchCardTrackingDetailsResponseValidationError is the validation error
// returned by FetchCardTrackingDetailsResponse.Validate if the designated
// constraints aren't met.
type FetchCardTrackingDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardTrackingDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardTrackingDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardTrackingDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardTrackingDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardTrackingDetailsResponseValidationError) ErrorName() string {
	return "FetchCardTrackingDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardTrackingDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardTrackingDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardTrackingDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardTrackingDetailsResponseValidationError{}

// Validate checks the field values on ProcessManualCardUnsuspendRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessManualCardUnsuspendRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessManualCardUnsuspendRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessManualCardUnsuspendRequestMultiError, or nil if none found.
func (m *ProcessManualCardUnsuspendRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessManualCardUnsuspendRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for Provenance

	// no validation rules for Reason

	if len(errors) > 0 {
		return ProcessManualCardUnsuspendRequestMultiError(errors)
	}

	return nil
}

// ProcessManualCardUnsuspendRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessManualCardUnsuspendRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessManualCardUnsuspendRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessManualCardUnsuspendRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessManualCardUnsuspendRequestMultiError) AllErrors() []error { return m }

// ProcessManualCardUnsuspendRequestValidationError is the validation error
// returned by ProcessManualCardUnsuspendRequest.Validate if the designated
// constraints aren't met.
type ProcessManualCardUnsuspendRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessManualCardUnsuspendRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessManualCardUnsuspendRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessManualCardUnsuspendRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessManualCardUnsuspendRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessManualCardUnsuspendRequestValidationError) ErrorName() string {
	return "ProcessManualCardUnsuspendRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessManualCardUnsuspendRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessManualCardUnsuspendRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessManualCardUnsuspendRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessManualCardUnsuspendRequestValidationError{}

// Validate checks the field values on ProcessManualCardUnsuspendResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessManualCardUnsuspendResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessManualCardUnsuspendResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessManualCardUnsuspendResponseMultiError, or nil if none found.
func (m *ProcessManualCardUnsuspendResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessManualCardUnsuspendResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessManualCardUnsuspendResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessManualCardUnsuspendResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessManualCardUnsuspendResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessManualCardUnsuspendResponseMultiError(errors)
	}

	return nil
}

// ProcessManualCardUnsuspendResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessManualCardUnsuspendResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessManualCardUnsuspendResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessManualCardUnsuspendResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessManualCardUnsuspendResponseMultiError) AllErrors() []error { return m }

// ProcessManualCardUnsuspendResponseValidationError is the validation error
// returned by ProcessManualCardUnsuspendResponse.Validate if the designated
// constraints aren't met.
type ProcessManualCardUnsuspendResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessManualCardUnsuspendResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessManualCardUnsuspendResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessManualCardUnsuspendResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessManualCardUnsuspendResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessManualCardUnsuspendResponseValidationError) ErrorName() string {
	return "ProcessManualCardUnsuspendResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessManualCardUnsuspendResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessManualCardUnsuspendResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessManualCardUnsuspendResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessManualCardUnsuspendResponseValidationError{}

// Validate checks the field values on GetLatestCardForActorIdsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLatestCardForActorIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLatestCardForActorIdsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLatestCardForActorIdsRequestMultiError, or nil if none found.
func (m *GetLatestCardForActorIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLatestCardForActorIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetActorIds()) < 1 {
		err := GetLatestCardForActorIdsRequestValidationError{
			field:  "ActorIds",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetLatestCardForActorIdsRequestMultiError(errors)
	}

	return nil
}

// GetLatestCardForActorIdsRequestMultiError is an error wrapping multiple
// validation errors returned by GetLatestCardForActorIdsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetLatestCardForActorIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLatestCardForActorIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLatestCardForActorIdsRequestMultiError) AllErrors() []error { return m }

// GetLatestCardForActorIdsRequestValidationError is the validation error
// returned by GetLatestCardForActorIdsRequest.Validate if the designated
// constraints aren't met.
type GetLatestCardForActorIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLatestCardForActorIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLatestCardForActorIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLatestCardForActorIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLatestCardForActorIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLatestCardForActorIdsRequestValidationError) ErrorName() string {
	return "GetLatestCardForActorIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLatestCardForActorIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLatestCardForActorIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLatestCardForActorIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLatestCardForActorIdsRequestValidationError{}

// Validate checks the field values on GetLatestCardForActorIdsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLatestCardForActorIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLatestCardForActorIdsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLatestCardForActorIdsResponseMultiError, or nil if none found.
func (m *GetLatestCardForActorIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLatestCardForActorIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLatestCardForActorIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLatestCardForActorIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLatestCardForActorIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetCards()))
		i := 0
		for key := range m.GetCards() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCards()[key]
			_ = val

			// no validation rules for Cards[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetLatestCardForActorIdsResponseValidationError{
							field:  fmt.Sprintf("Cards[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetLatestCardForActorIdsResponseValidationError{
							field:  fmt.Sprintf("Cards[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetLatestCardForActorIdsResponseValidationError{
						field:  fmt.Sprintf("Cards[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetLatestCardForActorIdsResponseMultiError(errors)
	}

	return nil
}

// GetLatestCardForActorIdsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetLatestCardForActorIdsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLatestCardForActorIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLatestCardForActorIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLatestCardForActorIdsResponseMultiError) AllErrors() []error { return m }

// GetLatestCardForActorIdsResponseValidationError is the validation error
// returned by GetLatestCardForActorIdsResponse.Validate if the designated
// constraints aren't met.
type GetLatestCardForActorIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLatestCardForActorIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLatestCardForActorIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLatestCardForActorIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLatestCardForActorIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLatestCardForActorIdsResponseValidationError) ErrorName() string {
	return "GetLatestCardForActorIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLatestCardForActorIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLatestCardForActorIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLatestCardForActorIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLatestCardForActorIdsResponseValidationError{}

// Validate checks the field values on GetLastPhysicalCardIssuedForUserRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetLastPhysicalCardIssuedForUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLastPhysicalCardIssuedForUserRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetLastPhysicalCardIssuedForUserRequestMultiError, or nil if none found.
func (m *GetLastPhysicalCardIssuedForUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLastPhysicalCardIssuedForUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetLastPhysicalCardIssuedForUserRequestMultiError(errors)
	}

	return nil
}

// GetLastPhysicalCardIssuedForUserRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetLastPhysicalCardIssuedForUserRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLastPhysicalCardIssuedForUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLastPhysicalCardIssuedForUserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLastPhysicalCardIssuedForUserRequestMultiError) AllErrors() []error { return m }

// GetLastPhysicalCardIssuedForUserRequestValidationError is the validation
// error returned by GetLastPhysicalCardIssuedForUserRequest.Validate if the
// designated constraints aren't met.
type GetLastPhysicalCardIssuedForUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLastPhysicalCardIssuedForUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLastPhysicalCardIssuedForUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLastPhysicalCardIssuedForUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLastPhysicalCardIssuedForUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLastPhysicalCardIssuedForUserRequestValidationError) ErrorName() string {
	return "GetLastPhysicalCardIssuedForUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLastPhysicalCardIssuedForUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLastPhysicalCardIssuedForUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLastPhysicalCardIssuedForUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLastPhysicalCardIssuedForUserRequestValidationError{}

// Validate checks the field values on GetLastPhysicalCardIssuedForUserResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetLastPhysicalCardIssuedForUserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLastPhysicalCardIssuedForUserResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetLastPhysicalCardIssuedForUserResponseMultiError, or nil if none found.
func (m *GetLastPhysicalCardIssuedForUserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLastPhysicalCardIssuedForUserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLastPhysicalCardIssuedForUserResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLastPhysicalCardIssuedForUserResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLastPhysicalCardIssuedForUserResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastIssuedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLastPhysicalCardIssuedForUserResponseValidationError{
					field:  "LastIssuedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLastPhysicalCardIssuedForUserResponseValidationError{
					field:  "LastIssuedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastIssuedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLastPhysicalCardIssuedForUserResponseValidationError{
				field:  "LastIssuedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLastPhysicalCardIssuedForUserResponseMultiError(errors)
	}

	return nil
}

// GetLastPhysicalCardIssuedForUserResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetLastPhysicalCardIssuedForUserResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLastPhysicalCardIssuedForUserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLastPhysicalCardIssuedForUserResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLastPhysicalCardIssuedForUserResponseMultiError) AllErrors() []error { return m }

// GetLastPhysicalCardIssuedForUserResponseValidationError is the validation
// error returned by GetLastPhysicalCardIssuedForUserResponse.Validate if the
// designated constraints aren't met.
type GetLastPhysicalCardIssuedForUserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLastPhysicalCardIssuedForUserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLastPhysicalCardIssuedForUserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLastPhysicalCardIssuedForUserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLastPhysicalCardIssuedForUserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLastPhysicalCardIssuedForUserResponseValidationError) ErrorName() string {
	return "GetLastPhysicalCardIssuedForUserResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLastPhysicalCardIssuedForUserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLastPhysicalCardIssuedForUserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLastPhysicalCardIssuedForUserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLastPhysicalCardIssuedForUserResponseValidationError{}

// Validate checks the field values on GetDcInternationalWidgetRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDcInternationalWidgetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDcInternationalWidgetRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetDcInternationalWidgetRequestMultiError, or nil if none found.
func (m *GetDcInternationalWidgetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDcInternationalWidgetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for CountryCode

	if len(errors) > 0 {
		return GetDcInternationalWidgetRequestMultiError(errors)
	}

	return nil
}

// GetDcInternationalWidgetRequestMultiError is an error wrapping multiple
// validation errors returned by GetDcInternationalWidgetRequest.ValidateAll()
// if the designated constraints aren't met.
type GetDcInternationalWidgetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDcInternationalWidgetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDcInternationalWidgetRequestMultiError) AllErrors() []error { return m }

// GetDcInternationalWidgetRequestValidationError is the validation error
// returned by GetDcInternationalWidgetRequest.Validate if the designated
// constraints aren't met.
type GetDcInternationalWidgetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDcInternationalWidgetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDcInternationalWidgetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDcInternationalWidgetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDcInternationalWidgetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDcInternationalWidgetRequestValidationError) ErrorName() string {
	return "GetDcInternationalWidgetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDcInternationalWidgetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDcInternationalWidgetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDcInternationalWidgetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDcInternationalWidgetRequestValidationError{}

// Validate checks the field values on GetDcInternationalWidgetResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetDcInternationalWidgetResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDcInternationalWidgetResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetDcInternationalWidgetResponseMultiError, or nil if none found.
func (m *GetDcInternationalWidgetResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDcInternationalWidgetResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDcInternationalWidgetResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDcInternationalWidgetResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDcInternationalWidgetResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDcInternationalWidgetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDcInternationalWidgetResponseValidationError{
					field:  "DcInternationalWidgetDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDcInternationalWidgetResponseValidationError{
					field:  "DcInternationalWidgetDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDcInternationalWidgetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDcInternationalWidgetResponseValidationError{
				field:  "DcInternationalWidgetDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDcInternationalWidgetResponseMultiError(errors)
	}

	return nil
}

// GetDcInternationalWidgetResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetDcInternationalWidgetResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDcInternationalWidgetResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDcInternationalWidgetResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDcInternationalWidgetResponseMultiError) AllErrors() []error { return m }

// GetDcInternationalWidgetResponseValidationError is the validation error
// returned by GetDcInternationalWidgetResponse.Validate if the designated
// constraints aren't met.
type GetDcInternationalWidgetResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDcInternationalWidgetResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDcInternationalWidgetResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDcInternationalWidgetResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDcInternationalWidgetResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDcInternationalWidgetResponseValidationError) ErrorName() string {
	return "GetDcInternationalWidgetResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDcInternationalWidgetResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDcInternationalWidgetResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDcInternationalWidgetResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDcInternationalWidgetResponseValidationError{}

// Validate checks the field values on DcInternationalWidgetDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DcInternationalWidgetDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DcInternationalWidgetDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DcInternationalWidgetDetailsMultiError, or nil if none found.
func (m *DcInternationalWidgetDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *DcInternationalWidgetDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Country

	// no validation rules for AccountBalance

	// no validation rules for ExchangeRate

	// no validation rules for BottomSectionBackgroundState

	// no validation rules for BottomSectionCtaState

	// no validation rules for BottomSectionText

	// no validation rules for CardId

	// no validation rules for CardSavingsAccountId

	if len(errors) > 0 {
		return DcInternationalWidgetDetailsMultiError(errors)
	}

	return nil
}

// DcInternationalWidgetDetailsMultiError is an error wrapping multiple
// validation errors returned by DcInternationalWidgetDetails.ValidateAll() if
// the designated constraints aren't met.
type DcInternationalWidgetDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DcInternationalWidgetDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DcInternationalWidgetDetailsMultiError) AllErrors() []error { return m }

// DcInternationalWidgetDetailsValidationError is the validation error returned
// by DcInternationalWidgetDetails.Validate if the designated constraints
// aren't met.
type DcInternationalWidgetDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DcInternationalWidgetDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DcInternationalWidgetDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DcInternationalWidgetDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DcInternationalWidgetDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DcInternationalWidgetDetailsValidationError) ErrorName() string {
	return "DcInternationalWidgetDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e DcInternationalWidgetDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDcInternationalWidgetDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DcInternationalWidgetDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DcInternationalWidgetDetailsValidationError{}
