package provisioning

import "github.com/samber/lo"

// IsFirstRequest returns true if this is the first physical card dispatch request for a user.
// It checks if the request type is either unspecified or explicitly marked as first request.
func (r *PhysicalCardDispatchRequest) IsFirstRequest() bool {
	return lo.Contains([]PhysicalCardDispatchRequest_RequestType{
		PhysicalCardDispatchRequest_REQUEST_TYPE_UNSPECIFIED,
		PhysicalCardDispatchRequest_REQUEST_TYPE_FIRST_REQUEST,
	}, r.GetDetails().GetRequestType())
}
