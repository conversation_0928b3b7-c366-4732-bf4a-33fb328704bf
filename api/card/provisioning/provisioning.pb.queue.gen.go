// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/card/provisioning
package provisioning

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessCardCreationMethod                                 = "ProcessCardCreation"
	ProcessCardPiCreationMethod                               = "ProcessCardPiCreation"
	ProcessCardPinSetEventMethod                              = "ProcessCardPinSetEvent"
	ProcessCardRenewalEventMethod                             = "ProcessCardRenewalEvent"
	ProcessCardOnboardingEventMethod                          = "ProcessCardOnboardingEvent"
	ProcessAuthFactorUpdateEventMethod                        = "ProcessAuthFactorUpdateEvent"
	ProcessCardShipmentRegisterEventMethod                    = "ProcessCardShipmentRegisterEvent"
	ProcessCardDeliveryDelayEventMethod                       = "ProcessCardDeliveryDelayEvent"
	ProcessCardDeliveredEventMethod                           = "ProcessCardDeliveredEvent"
	GetTrackingDetailsMethod                                  = "GetTrackingDetails"
	ProcessCardDispatchMethod                                 = "ProcessCardDispatch"
	ProcessShippingAddressUpdateAndDispatchPhysicalCardMethod = "ProcessShippingAddressUpdateAndDispatchPhysicalCard"
	ProcessCardsDispatchedCsvFileMethod                       = "ProcessCardsDispatchedCsvFile"
	ProcessCardAmcChargesEligibleUserFileMethod               = "ProcessCardAmcChargesEligibleUserFile"
	OrderPhysicalCardCriticalNotificationMethod               = "OrderPhysicalCardCriticalNotification"
	ProcessUserDevicePropertiesUpdateEventMethod              = "ProcessUserDevicePropertiesUpdateEvent"

	ProcessCardCreationCallBackMethod         = "ProcessCardCreationCallBack"
	ProcessCardTrackingCallbackMethod         = "ProcessCardTrackingCallback"
	ProcessDispatchPhysicalCardCallbackMethod = "ProcessDispatchPhysicalCardCallback"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &ProcessCardsDispatchedCsvFileRequest{}
var _ queue.ConsumerRequest = &ProcessCardDispatchRequest{}
var _ queue.ConsumerRequest = &ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest{}
var _ queue.ConsumerRequest = &ProcessCardShipmentRegisterEventRequest{}
var _ queue.ConsumerRequest = &ProcessCardDeliveryDelayEventRequest{}
var _ queue.ConsumerRequest = &ProcessCardRenewalEventRequest{}
var _ queue.ConsumerRequest = &ProcessCardCreationRequest{}
var _ queue.ConsumerRequest = &ProcessCardPiCreationRequest{}
var _ queue.ConsumerRequest = &ProcessCardPinSetEventRequest{}
var _ queue.ConsumerRequest = &ProcessCardDeliveredEventRequest{}
var _ queue.ConsumerRequest = &GetTrackingDetailsRequest{}
var _ queue.ConsumerRequest = &ProcessCardAmcChargesEligibleUserFileRequest{}
var _ queue.ConsumerRequest = &OrderPhysicalCardCriticalNotificationRequest{}
var _ queue.ConsumerRequest = &ProcessCardCreationCallBackRequest{}
var _ queue.ConsumerRequest = &ProcessCardTrackingCallbackRequest{}
var _ queue.ConsumerRequest = &ProcessDispatchPhysicalCardCallbackRequest{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCardsDispatchedCsvFileRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCardDispatchRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCardShipmentRegisterEventRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCardDeliveryDelayEventRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCardRenewalEventRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCardCreationRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCardPiCreationRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCardPinSetEventRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCardDeliveredEventRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *GetTrackingDetailsRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCardAmcChargesEligibleUserFileRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *OrderPhysicalCardCriticalNotificationRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCardCreationCallBackRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCardTrackingCallbackRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessDispatchPhysicalCardCallbackRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterProcessCardCreationMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardCreationMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, ProcessCardCreationMethod)
}

// RegisterProcessCardPiCreationMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardPiCreationMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, ProcessCardPiCreationMethod)
}

// RegisterProcessCardPinSetEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardPinSetEventMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, ProcessCardPinSetEventMethod)
}

// RegisterProcessCardRenewalEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardRenewalEventMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, ProcessCardRenewalEventMethod)
}

// RegisterProcessCardOnboardingEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardOnboardingEventMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, ProcessCardOnboardingEventMethod)
}

// RegisterProcessAuthFactorUpdateEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessAuthFactorUpdateEventMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, ProcessAuthFactorUpdateEventMethod)
}

// RegisterProcessCardShipmentRegisterEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardShipmentRegisterEventMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, ProcessCardShipmentRegisterEventMethod)
}

// RegisterProcessCardDeliveryDelayEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardDeliveryDelayEventMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, ProcessCardDeliveryDelayEventMethod)
}

// RegisterProcessCardDeliveredEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardDeliveredEventMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, ProcessCardDeliveredEventMethod)
}

// RegisterGetTrackingDetailsMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterGetTrackingDetailsMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, GetTrackingDetailsMethod)
}

// RegisterProcessCardDispatchMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardDispatchMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, ProcessCardDispatchMethod)
}

// RegisterProcessShippingAddressUpdateAndDispatchPhysicalCardMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessShippingAddressUpdateAndDispatchPhysicalCardMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, ProcessShippingAddressUpdateAndDispatchPhysicalCardMethod)
}

// RegisterProcessCardsDispatchedCsvFileMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardsDispatchedCsvFileMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, ProcessCardsDispatchedCsvFileMethod)
}

// RegisterProcessCardAmcChargesEligibleUserFileMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardAmcChargesEligibleUserFileMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, ProcessCardAmcChargesEligibleUserFileMethod)
}

// RegisterOrderPhysicalCardCriticalNotificationMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterOrderPhysicalCardCriticalNotificationMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, OrderPhysicalCardCriticalNotificationMethod)
}

// RegisterProcessUserDevicePropertiesUpdateEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessUserDevicePropertiesUpdateEventMethodToSubscriber(subscriber queue.Subscriber, srv CardConsumerServer) {
	subscriber.RegisterService(&CardConsumer_ServiceDesc, srv, ProcessUserDevicePropertiesUpdateEventMethod)
}

// RegisterProcessCardCreationCallBackMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardCreationCallBackMethodToSubscriber(subscriber queue.Subscriber, srv CallBackConsumerServer) {
	subscriber.RegisterService(&CallBackConsumer_ServiceDesc, srv, ProcessCardCreationCallBackMethod)
}

// RegisterProcessCardTrackingCallbackMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardTrackingCallbackMethodToSubscriber(subscriber queue.Subscriber, srv CallBackConsumerServer) {
	subscriber.RegisterService(&CallBackConsumer_ServiceDesc, srv, ProcessCardTrackingCallbackMethod)
}

// RegisterProcessDispatchPhysicalCardCallbackMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessDispatchPhysicalCardCallbackMethodToSubscriber(subscriber queue.Subscriber, srv CallBackConsumerServer) {
	subscriber.RegisterService(&CallBackConsumer_ServiceDesc, srv, ProcessDispatchPhysicalCardCallbackMethod)
}
