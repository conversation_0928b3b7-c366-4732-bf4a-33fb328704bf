// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/card/provisioning/service.proto

package provisioning

import (
	context "context"
	dynamic_elements "github.com/epifi/gamma/api/dynamic_elements"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CardProvisioning_CreateCard_FullMethodName                                              = "/card.provisioning.CardProvisioning/CreateCard"
	CardProvisioning_FetchCardCreationStatus_FullMethodName                                 = "/card.provisioning.CardProvisioning/FetchCardCreationStatus"
	CardProvisioning_FetchCards_FullMethodName                                              = "/card.provisioning.CardProvisioning/FetchCards"
	CardProvisioning_ActivateCard_FullMethodName                                            = "/card.provisioning.CardProvisioning/ActivateCard"
	CardProvisioning_FetchCardActivationStatus_FullMethodName                               = "/card.provisioning.CardProvisioning/FetchCardActivationStatus"
	CardProvisioning_DispatchCard_FullMethodName                                            = "/card.provisioning.CardProvisioning/DispatchCard"
	CardProvisioning_CardDeliveryTracking_FullMethodName                                    = "/card.provisioning.CardProvisioning/CardDeliveryTracking"
	CardProvisioning_FetchCardDetails_FullMethodName                                        = "/card.provisioning.CardProvisioning/FetchCardDetails"
	CardProvisioning_FetchCardDetailsByAccountId_FullMethodName                             = "/card.provisioning.CardProvisioning/FetchCardDetailsByAccountId"
	CardProvisioning_SetCardPin_FullMethodName                                              = "/card.provisioning.CardProvisioning/SetCardPin"
	CardProvisioning_ChangeCardPin_FullMethodName                                           = "/card.provisioning.CardProvisioning/ChangeCardPin"
	CardProvisioning_ResetCardPin_FullMethodName                                            = "/card.provisioning.CardProvisioning/ResetCardPin"
	CardProvisioning_ValidateCardPin_FullMethodName                                         = "/card.provisioning.CardProvisioning/ValidateCardPin"
	CardProvisioning_ValidateCard_FullMethodName                                            = "/card.provisioning.CardProvisioning/ValidateCard"
	CardProvisioning_GetCardGroups_FullMethodName                                           = "/card.provisioning.CardProvisioning/GetCardGroups"
	CardProvisioning_CardPinStatus_FullMethodName                                           = "/card.provisioning.CardProvisioning/CardPinStatus"
	CardProvisioning_GenerateTxnId_FullMethodName                                           = "/card.provisioning.CardProvisioning/GenerateTxnId"
	CardProvisioning_GetCardDetailsWithCvv_FullMethodName                                   = "/card.provisioning.CardProvisioning/GetCardDetailsWithCvv"
	CardProvisioning_RenewCard_FullMethodName                                               = "/card.provisioning.CardProvisioning/RenewCard"
	CardProvisioning_RenewCardStatus_FullMethodName                                         = "/card.provisioning.CardProvisioning/RenewCardStatus"
	CardProvisioning_VerifyQRCode_FullMethodName                                            = "/card.provisioning.CardProvisioning/VerifyQRCode"
	CardProvisioning_FetchDeliveryTrackingStatus_FullMethodName                             = "/card.provisioning.CardProvisioning/FetchDeliveryTrackingStatus"
	CardProvisioning_FetchTransactionableCards_FullMethodName                               = "/card.provisioning.CardProvisioning/FetchTransactionableCards"
	CardProvisioning_InitiateAdditionalAuthAttempt_FullMethodName                           = "/card.provisioning.CardProvisioning/InitiateAdditionalAuthAttempt"
	CardProvisioning_GetAdditionalAuthInfo_FullMethodName                                   = "/card.provisioning.CardProvisioning/GetAdditionalAuthInfo"
	CardProvisioning_UpdateAuthInfo_FullMethodName                                          = "/card.provisioning.CardProvisioning/UpdateAuthInfo"
	CardProvisioning_TriggerCardNotifications_FullMethodName                                = "/card.provisioning.CardProvisioning/TriggerCardNotifications"
	CardProvisioning_UpdateFreeCardReplacement_FullMethodName                               = "/card.provisioning.CardProvisioning/UpdateFreeCardReplacement"
	CardProvisioning_FetchCardTrackingDetails_FullMethodName                                = "/card.provisioning.CardProvisioning/FetchCardTrackingDetails"
	CardProvisioning_GetCardShipmentTrackingDetails_FullMethodName                          = "/card.provisioning.CardProvisioning/GetCardShipmentTrackingDetails"
	CardProvisioning_UploadCardTrackingDetails_FullMethodName                               = "/card.provisioning.CardProvisioning/UploadCardTrackingDetails"
	CardProvisioning_ProcessManualCardPinSet_FullMethodName                                 = "/card.provisioning.CardProvisioning/ProcessManualCardPinSet"
	CardProvisioning_ForceCardCreationEnquiry_FullMethodName                                = "/card.provisioning.CardProvisioning/ForceCardCreationEnquiry"
	CardProvisioning_UpdateTrackingDetails_FullMethodName                                   = "/card.provisioning.CardProvisioning/UpdateTrackingDetails"
	CardProvisioning_GetCardActionAttempts_FullMethodName                                   = "/card.provisioning.CardProvisioning/GetCardActionAttempts"
	CardProvisioning_ProcessManualCardUnsuspend_FullMethodName                              = "/card.provisioning.CardProvisioning/ProcessManualCardUnsuspend"
	CardProvisioning_CreatePhysicalCardDispatchAttempt_FullMethodName                       = "/card.provisioning.CardProvisioning/CreatePhysicalCardDispatchAttempt"
	CardProvisioning_InitiateShippingAddressUpdateAndDispatchPhysicalCard_FullMethodName    = "/card.provisioning.CardProvisioning/InitiateShippingAddressUpdateAndDispatchPhysicalCard"
	CardProvisioning_CheckShippingAddressUpdateAndPhysicalCardDispatchStatus_FullMethodName = "/card.provisioning.CardProvisioning/CheckShippingAddressUpdateAndPhysicalCardDispatchStatus"
	CardProvisioning_GetPhysicalCardDispatchStatus_FullMethodName                           = "/card.provisioning.CardProvisioning/GetPhysicalCardDispatchStatus"
	CardProvisioning_GetLatestCardForActorIds_FullMethodName                                = "/card.provisioning.CardProvisioning/GetLatestCardForActorIds"
	CardProvisioning_GetPhysicalCardActivationInfo_FullMethodName                           = "/card.provisioning.CardProvisioning/GetPhysicalCardActivationInfo"
	CardProvisioning_InitiatePhysicalCardDispatch_FullMethodName                            = "/card.provisioning.CardProvisioning/InitiatePhysicalCardDispatch"
	CardProvisioning_FetchPhysicalCardChargesForUser_FullMethodName                         = "/card.provisioning.CardProvisioning/FetchPhysicalCardChargesForUser"
	CardProvisioning_GetLastPhysicalCardIssuedForUser_FullMethodName                        = "/card.provisioning.CardProvisioning/GetLastPhysicalCardIssuedForUser"
	CardProvisioning_ActivatePhysicalCard_FullMethodName                                    = "/card.provisioning.CardProvisioning/ActivatePhysicalCard"
	CardProvisioning_GetForexRefunds_FullMethodName                                         = "/card.provisioning.CardProvisioning/GetForexRefunds"
	CardProvisioning_GetPaginatedForexRefundsByActorId_FullMethodName                       = "/card.provisioning.CardProvisioning/GetPaginatedForexRefundsByActorId"
	CardProvisioning_FetchPhysicalCardDispatchRequests_FullMethodName                       = "/card.provisioning.CardProvisioning/FetchPhysicalCardDispatchRequests"
	CardProvisioning_GetForexRefundsByActorId_FullMethodName                                = "/card.provisioning.CardProvisioning/GetForexRefundsByActorId"
	CardProvisioning_FetchCardRenewalChargesForUser_FullMethodName                          = "/card.provisioning.CardProvisioning/FetchCardRenewalChargesForUser"
	CardProvisioning_FetchForexRefundAggregates_FullMethodName                              = "/card.provisioning.CardProvisioning/FetchForexRefundAggregates"
	CardProvisioning_GetHomeLayoutConfiguration_FullMethodName                              = "/card.provisioning.CardProvisioning/GetHomeLayoutConfiguration"
	CardProvisioning_GetCardSwitchNotification_FullMethodName                               = "/card.provisioning.CardProvisioning/GetCardSwitchNotification"
	CardProvisioning_GetDcInternationalWidget_FullMethodName                                = "/card.provisioning.CardProvisioning/GetDcInternationalWidget"
	CardProvisioning_FetchDynamicElements_FullMethodName                                    = "/card.provisioning.CardProvisioning/FetchDynamicElements"
	CardProvisioning_DynamicElementCallback_FullMethodName                                  = "/card.provisioning.CardProvisioning/DynamicElementCallback"
)

// CardProvisioningClient is the client API for CardProvisioning service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CardProvisioningClient interface {
	// Initiates card creation for the user. The decision to create single or dual card is controlled by the
	// provisioning strategy for the card.
	// If provisioning strategy is a ProvisioningStrategy.SINGLE_NUMBERED_CARD || SINGLE_NUMBERLESS_CARD,
	// a single card is provisioned for the user. The created cards in this case have default FORM as PHYSICAL.
	//
	// If provisioning strategy is a ProvisioningStrategy.DUAL_NUMBERLESS_CARD,
	// 2 cards are created for the user:
	// A PHYSICAL and a DIGITAL card. Post the successful sign up & account creation,
	// all cards will be created for the users. Post the card creation, digital card will be displayed
	// in the epiFi app  & physical cards will be shipped to the shipping address of the user.
	//
	// For initiating a new card creation request the mandatory parameters includes actor id and issuing bank.
	CreateCard(ctx context.Context, in *CreateCardRequest, opts ...grpc.CallOption) (*CreateCardResponse, error)
	// Fetches the status of the card creation request already made.
	FetchCardCreationStatus(ctx context.Context, in *FetchCardCreationStatusRequest, opts ...grpc.CallOption) (*FetchCardCreationStatusResponse, error)
	// RPC fetches all cards for the user based on the input filters. A user is expected to have multiple cards issued.
	// The card filters include:
	// - state: ACTIVATED, EXPIRED etc
	// - issuing bank : FEDERAL etc
	// - network type : VISA, MASTER etc
	// - type : DEBIT or CREDIT
	FetchCards(ctx context.Context, in *FetchCardsRequest, opts ...grpc.CallOption) (*FetchCardsResponse, error)
	// Activates the given cards. The cards must be created by now. By default, both physical and digital
	// debit cards will not be activated. Any usage of the card is only permitted after the
	// activation step.
	// 1. PHYSICAL card needs to be activate for POS, ATM transactions
	// 2. DIGITAL card needs to be activated for ECOM/Online transactions.
	ActivateCard(ctx context.Context, in *ActivateCardRequest, opts ...grpc.CallOption) (*ActivateCardResponse, error)
	// Fetches the status of the card activation requests already made.
	FetchCardActivationStatus(ctx context.Context, in *FetchCardActivationStatusRequest, opts ...grpc.CallOption) (*FetchCardActivationStatusResponse, error)
	// Initiates the dispatch of the PHYSICAL card. Card will be dispatched as per the
	// address mentioned by the user.
	// TODO(anand): As per the RBI guidelines, the user needs to be provide the proof of shipping address within 3 months
	DispatchCard(ctx context.Context, in *CardDispatchRequest, opts ...grpc.CallOption) (*CardDispatchResponse, error)
	// Provides tracking details for given card
	// Deprecated
	CardDeliveryTracking(ctx context.Context, in *CardDeliveryTrackingRequest, opts ...grpc.CallOption) (*CardDeliveryTrackingResponse, error)
	// Fetches all the card related data and the status of the card.
	//
	// Any card information or sensitive information w.r.t bank has to be treated separately.
	// PCI - DSS has compliance requirements and guidelines regarding how such information has
	// to be stored/processed. Any system that touches the card information will fall in the scope of PCI.
	//
	// Hence, card service will have no access to card number, expiry or cvv. These will be stored in a
	// secret vault a.k.a Data Bunker. Card service will use token(issued by the data bunker) to refer
	// to the actual data.
	//
	// TODO(anand): If cvv cannot be stored in data bunker, how do we pass it along to the user?
	FetchCardDetails(ctx context.Context, in *FetchCardDetailsRequest, opts ...grpc.CallOption) (*FetchCardDetailsResponse, error)
	// Fetches card details for the specified savings account id and card state
	FetchCardDetailsByAccountId(ctx context.Context, in *FetchCardDetailsByAccountIdRequest, opts ...grpc.CallOption) (*FetchCardDetailsByAccountIdResponse, error)
	// ==============PIN MANAGEMENT APIS==============
	//
	// Epifi has defined our own specifications around pin management. The common library approach
	// is expected to be adapted by all the partner banks.
	// As part of this, all the pin related use-cases, there exists a encryptedBlock containing
	// all the sensitive information (card pin, secure pin etc).
	//
	// For card related RPCs, the cred block contains following fields:
	//  1. secure pin : same as UPI pin. The secure pin is verified against the customer/account details in the request.
	//     The verification is done at the issuing bank. If success, the pin set, change & reset proceeds.
	//  2. transaction id : TODO(anand) is it same as vendor request id?
	//  3. card pin: a.k.a the atm pin. This is the actual value which is set, changed or reset. In case of pin validation
	//     the card pin is verified against the stored pin. The verification happens at the end of issuing bank.
	//
	// Reference: https://docs.google.com/document/d/1PXrt_tGI5vmKYwpokqUoxVTxeey69zG6US1eNkJIEW0/edit?ts=5ebcc259#
	//
	// RPC facilitates setting the pin for the card.
	// Pin must be set after receiving the card and will be required for all the ATM/POS transactions.
	// Pin setup can be done only once for a particular card. Any subsequent call to set the pin must be rejected.
	// Only pin change and pin reset can be performed post setup.
	SetCardPin(ctx context.Context, in *SetCardPinRequest, opts ...grpc.CallOption) (*SetCardPinResponse, error)
	// RPC facilitates changing the pin for the card to a new value.
	// Change pin is only available to the user if pin was previously set. The card pin can be changed voluntarily
	// and can also be forced by Epifi to be changed periodically. To change the pin, user needs to provide additional
	// authentication like old pin or some other auth-factor ( a secret value etc) registered with the issuing bank.
	// The changed pin must be used for all the transactions that follow.
	ChangeCardPin(ctx context.Context, in *ChangeCardPinRequest, opts ...grpc.CallOption) (*ChangeCardPinResponse, error)
	// RPC facilitates resetting the pin for the card to a new entered value.
	// Reset pin is only available to the user if pin was previously set. Reset pin can be used when the
	// user has forgotten the pin. To reset the pin, user needs to provide additional authentication like a secret
	// value registered with the issuing bank. The pin is reset only if the additional authentication is validated
	// successfully.
	ResetCardPin(ctx context.Context, in *ResetCardPinRequest, opts ...grpc.CallOption) (*ResetCardPinResponse, error)
	// RPC validates if the entered pin was valid for associated card.
	// Validate pin shall return failure if pin was not set or if the entered didn't match the pin set for the card.
	ValidateCardPin(ctx context.Context, in *ValidateCardPinRequest, opts ...grpc.CallOption) (*ValidateCardPinResponse, error)
	// Checks if given card details(CardNumber, Expiry, PIN) are valid.
	// Usecase is around re-oobe scenarios. TODO: understand better and update
	ValidateCard(ctx context.Context, in *CardValidateRequest, opts ...grpc.CallOption) (*CardValidateResponse, error)
	// RPC fetches card groups for the given user. A user is expected to have multiple cards issued.
	// A card group is a collection of cards identified by the same group_id identifier.
	//
	// The resulting card groups are sorted by `created_at` timestamps. Callers need to specify the order(ASC/DESC) of sorting.
	// NOTE: for the same `group_id`, cards are ordered by primary-key
	//
	// Callers can limit the number of card groups to be fetched by setting the `num_groups` field in request.
	// Additionally, caller can request to fetch all the available card groups for the user by setting the `get_all` to true.
	// TODO(anand) [low priority]: evaluate if this needs to be made paginated and prevent fetching all cards.
	// We don't expect large number of cards to be created per user and hence this should be fine for now.
	// TODO(anand): add option to filter the cards by vendor, network_type, card_type, states etc.
	GetCardGroups(ctx context.Context, in *GetCardGroupsRequest, opts ...grpc.CallOption) (*GetCardGroupsResponse, error)
	// Fetches card pin status for card ids in the request
	CardPinStatus(ctx context.Context, in *CardPinStatusRequest, opts ...grpc.CallOption) (*CardPinStatusResponse, error)
	// RPC to generate txn id based on flow type and vendor.
	// This RPC will return a unique txnid for the flow type for a vendor.
	GenerateTxnId(ctx context.Context, in *GenerateTxnIdRequest, opts ...grpc.CallOption) (*GenerateTxnIdResponse, error)
	// Fetches card 16/4/3 in plain text for the card id in request.
	GetCardDetailsWithCvv(ctx context.Context, in *GetCardDetailsWithCvvRequest, opts ...grpc.CallOption) (*GetCardDetailsWithCvvResponse, error)
	// RPC to block card and publish packet to create new card after card is blocked successfully
	RenewCard(ctx context.Context, in *RenewCardRequest, opts ...grpc.CallOption) (*RenewCardResponse, error)
	// RPC to fetch renew card status for the given card id in the request
	// We will get new card id only when the old card is blocked successfully
	// If old card is blocked successfully and new card creation fails then we will again initiate creation of new card
	// and that card id will be sent in the response
	RenewCardStatus(ctx context.Context, in *RenewCardStatusRequest, opts ...grpc.CallOption) (*RenewCardStatusResponse, error)
	// VerifyQRCode rpc decrypts the encrypted data and verify the data present in the qr code.
	// The data present in the qr code will be (Last 4 digits of the card + Last 4 digit of mobile number + last 4 digits of pin code)
	// After successful verification we will mark the card as delivered for the user and
	// user can then enable ATM/POS transaction for that card.
	VerifyQRCode(ctx context.Context, in *VerifyQRCodeRequest, opts ...grpc.CallOption) (*VerifyQRCodeResponse, error)
	// FetchDeliveryTrackingStatus rpc fetches delivery status for the given card id
	FetchDeliveryTrackingStatus(ctx context.Context, in *FetchDeliveryTrackingStatusRequest, opts ...grpc.CallOption) (*FetchDeliveryTrackingStatusResponse, error)
	// FetchTransactionableCards rpc fetches all cards for an actor through which transaction can be made or
	// could have been made in the past. All cards which were activated at least once, qualify.
	FetchTransactionableCards(ctx context.Context, in *FetchTransactionableCardsRequest, opts ...grpc.CallOption) (*FetchTransactionableCardsResponse, error)
	// InitiateAdditionalAuthAttempt rpc creates a new auth attempt and returns the attempt id for the same
	InitiateAdditionalAuthAttempt(ctx context.Context, in *InitiateAdditionalAuthAttemptRequest, opts ...grpc.CallOption) (*InitiateAdditionalAuthAttemptResponse, error)
	// GetAdditionalAuthInfo fetches the card id and action for a given auth attempt id.
	GetAdditionalAuthInfo(ctx context.Context, in *GetAdditionalAuthInfoRequest, opts ...grpc.CallOption) (*GetAdditionalAuthInfoResponse, error)
	// UpdateAuthInfo updates the state of the auth such as SUCCESS, LIVENESS_FAILED, FACEMATCH_FAILED in case of Liveness and FM auth
	UpdateAuthInfo(ctx context.Context, in *UpdateAuthInfoRequest, opts ...grpc.CallOption) (*UpdateAuthInfoResponse, error)
	// Triggers card specific notifications for particular user groups
	TriggerCardNotifications(ctx context.Context, in *TriggerCardNotificationsRequest, opts ...grpc.CallOption) (*TriggerCardNotificationsResponse, error)
	// rpc to update free card replacements for a user. We will charge for card replacement when user reaches the maximum
	// free card replacement count. We might still need to give additional free card to users for cases when there is some issue with the card
	// and we need to give user an extra free card.
	UpdateFreeCardReplacement(ctx context.Context, in *UpdateFreeCardReplacementRequest, opts ...grpc.CallOption) (*UpdateFreeCardReplacementResponse, error)
	// FetchCardTrackingDetails fetches cards for which awb details are not present, and get the awb details from the
	// bank and publishes packet to the queue to register those shipments at Shipway's end.
	// We will only fetch the AWB details for limited number of cards specified in the request based on their created_at
	// timestamp in ascending order.
	// This rpc will be triggered using a cron job which will between specific hours with an hourly frequency.
	// We are running it between specific hours because bank updates the AWB at a given time everyday.
	FetchCardTrackingDetails(ctx context.Context, in *FetchCardTrackingDetailsRequest, opts ...grpc.CallOption) (*FetchCardTrackingDetailsResponse, error)
	// GetCardShipmentTrackingDetails returns the tracking details for a given card id. We will return awb number, carrier,
	// scans and delivery state information.
	GetCardShipmentTrackingDetails(ctx context.Context, in *GetCardShipmentTrackingDetailsRequest, opts ...grpc.CallOption) (*GetCardShipmentTrackingDetailsResponse, error)
	// UploadCardTrackingDetails takes data present in csv file containing the tracking details such as Awb, courier partner
	// and updates it at our end in card tracking requests table and publishes the packet to register them as shipway
	UploadCardTrackingDetails(ctx context.Context, in *UploadCardTrackingDetailsRequest, opts ...grpc.CallOption) (*UploadCardTrackingDetailsResponse, error)
	// ProcessManualCardPinSet will be triggered manually via a script or from sherlock.
	// It will mark pin set done at our end for a card and trigger the events to be published.
	// It is to be used for cases where card pin set is done through Federal IVR.
	// Long term solution is to get these events from federal and remove the manual dependency.
	ProcessManualCardPinSet(ctx context.Context, in *ProcessManualCardPinSetRequest, opts ...grpc.CallOption) (*ProcessManualCardPinSetResponse, error)
	// ForceCardCreationEnquiry rpc will be triggered from Sherlock for card creation requests where we need to do enquiry at vendor's end
	// This can be due various reasons but not limited to -
	// 1. We got failure from vendor but on following up vendor asked us to do enquiry again
	// 2. Retries exhausted for card creation enquiry
	// 3. We got a failure ack but request actually reached vendor and they have asked us to enquire again
	ForceCardCreationEnquiry(ctx context.Context, in *ForceCardCreationEnquiryRequest, opts ...grpc.CallOption) (*ForceCardCreationEnquiryResponse, error)
	// UpdateTrackingDetails takes data present in csv file containing the updated tracking details for shipments for
	// which the initial delivery partner refused and we received a new awb number and tracking partner
	UpdateTrackingDetails(ctx context.Context, in *UpdateTrackingDetailsRequest, opts ...grpc.CallOption) (*UpdateTrackingDetailsResponse, error)
	// GetCardActionAttempts rpc fetches all the attempts for a given card id corresponding to a given action (pin set, pin reset)
	// Each attempt has details regarding if the action was successful or failed, along with the failure reasons
	GetCardActionAttempts(ctx context.Context, in *GetCardActionAttemptsRequest, opts ...grpc.CallOption) (*GetCardActionAttemptsResponse, error)
	// ProcessManualCardUnsuspend rpc will be triggered from Sherlock dev action where we manually need to un-suspend card.
	// Card state will be updated from suspended to activated and pi state will be updated from suspended to verified.
	// It is to be used for cases where card state is un-suspended through Federal IVR and at our end it is still is in
	// suspended state.
	ProcessManualCardUnsuspend(ctx context.Context, in *ProcessManualCardUnsuspendRequest, opts ...grpc.CallOption) (*ProcessManualCardUnsuspendResponse, error)
	// CreatePhysicalCardDispatchAttempt creates a new dispatch request in our db and publishes packet for processing of the dispatch request at vendor
	// for the card id sent in the request.
	// We will validate if card is not a physical card and there are no dispatch request in progress for this card
	// Deprecated this in favour of new rpc InitiatePhysicalCardDispatch
	CreatePhysicalCardDispatchAttempt(ctx context.Context, in *CreatePhysicalCardDispatchAttemptRequest, opts ...grpc.CallOption) (*CreatePhysicalCardDispatchAttemptResponse, error)
	// InitiateShippingAddressUpdateAndDispatchPhysicalCard is invoked by client to issue physical card to a user, it creates shipping preference
	// with the address type received in the request and publishes packet for initiating and checking the status for
	// shipping address and triggering physical dispatch request post successful address update.
	// As the process for issuing physical card is not synchronous client needs to check the current state of the request using
	// CheckPhysicalCardDispatchStatus rpc.
	InitiateShippingAddressUpdateAndDispatchPhysicalCard(ctx context.Context, in *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest, opts ...grpc.CallOption) (*InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse, error)
	// CheckShippingAddressUpdateAndPhysicalCardDispatchStatus is called by client to check the status of shipping address
	// update and physical card dispatch request status
	CheckShippingAddressUpdateAndPhysicalCardDispatchStatus(ctx context.Context, in *CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest, opts ...grpc.CallOption) (*CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse, error)
	// GetPhysicalCardDispatchStatus returns the current status of the physical card dispatch request
	GetPhysicalCardDispatchStatus(ctx context.Context, in *GetPhysicalCardDispatchStatusRequest, opts ...grpc.CallOption) (*GetPhysicalCardDispatchStatusResponse, error)
	// GetLatestCardForActorIds returns the latest card for the actor id's sent in the request, latest card is fetched
	// based on the latest created at timestamp, in case if card not found for any actor we will return the remaining cards.
	// We will have card state as filter for fetching cards only within the given states, if no filter is passed we will
	// fetch cards with any state
	GetLatestCardForActorIds(ctx context.Context, in *GetLatestCardForActorIdsRequest, opts ...grpc.CallOption) (*GetLatestCardForActorIdsResponse, error)
	// GetPhysicalCardActivationInfo to determine card physical activation status. It will take either card id or actor id as input. In case of actor-id
	// we will send the activation status of the latest physical card of the user. If activated we will share more details like activation timestamp etc
	GetPhysicalCardActivationInfo(ctx context.Context, in *GetPhysicalCardActivationInfoRequest, opts ...grpc.CallOption) (*GetPhysicalCardActivationInfoResponse, error)
	// InitiatePhysicalCardDispatch creates a physical card dispatch request & initiates Physical Card Dispatch workflow and
	// creates a fund transfer request which further initiates the Fund Transfer workflow. It returns a deeplink corresponding
	// to the next action i.e. Authorize fund transfer
	InitiatePhysicalCardDispatch(ctx context.Context, in *InitiatePhysicalCardDispatchRequest, opts ...grpc.CallOption) (*InitiatePhysicalCardDispatchResponse, error)
	// FetchPhysicalCardChargesForUser fetches the amount to be paid by different users to order a physical debit card
	FetchPhysicalCardChargesForUser(ctx context.Context, in *FetchPhysicalCardChargesForUserRequest, opts ...grpc.CallOption) (*FetchPhysicalCardChargesForUserResponse, error)
	// GetLastPhysicalCardIssuedForUser returns the timestamp of the most recent physical card issued for the user.
	GetLastPhysicalCardIssuedForUser(ctx context.Context, in *GetLastPhysicalCardIssuedForUserRequest, opts ...grpc.CallOption) (*GetLastPhysicalCardIssuedForUserResponse, error)
	// ActivatePhysicalCard rpc will be used to activate the card by switching on the POS end ECOM for the user.
	ActivatePhysicalCard(ctx context.Context, in *ActivatePhysicalCardRequest, opts ...grpc.CallOption) (*ActivatePhysicalCardResponse, error)
	// GetForexRefunds RPC will fetch forex refund data based on an identifier like id, txn_id, etc.
	GetForexRefunds(ctx context.Context, in *GetForexRefundsRequest, opts ...grpc.CallOption) (*GetForexRefundsResponse, error)
	// GetPaginatedForexRefundsByActorId RPC will fetch a paginated list of forex refunds for an actor
	GetPaginatedForexRefundsByActorId(ctx context.Context, in *GetPaginatedForexRefundsByActorIdRequest, opts ...grpc.CallOption) (*GetPaginatedForexRefundsByActorIdResponse, error)
	// GetPhysicalCardDispatchRequests RPC will fetch all the dispatch requests for card id
	FetchPhysicalCardDispatchRequests(ctx context.Context, in *FetchPhysicalCardDispatchRequestsRequest, opts ...grpc.CallOption) (*FetchPhysicalCardDispatchRequestsResponse, error)
	// GetCompletedForexRefundsByActorId RPC will fetch a list of forex refunds for an actor in completed state
	GetForexRefundsByActorId(ctx context.Context, in *GetForexRefundsByActorIdRequest, opts ...grpc.CallOption) (*GetForexRefundsByActorIdResponse, error)
	// FetchCardRenewalCharges RPC will fetch card renewal charges
	FetchCardRenewalChargesForUser(ctx context.Context, in *FetchCardRenewalChargesForUserRequest, opts ...grpc.CallOption) (*FetchCardRenewalChargesForUserResponse, error)
	// FetchForexRefundAggregates will fetch the aggregate refunds received in a given time range. . This can also give result for a
	// particular user if the actor id request param is not empty.
	FetchForexRefundAggregates(ctx context.Context, in *FetchForexRefundAggregatesRequest, opts ...grpc.CallOption) (*FetchForexRefundAggregatesResponse, error)
	// GetHomeLayoutConfiguration will fetch the home layout configuration,
	// based on which the ordering of the component on the home screen will be decided.
	// We'll store the ordering of the components in `client(frontend)` configs for each expected layout configuration(primarily layout_id) that this RPC can return.
	GetHomeLayoutConfiguration(ctx context.Context, in *GetHomeLayoutConfigurationRequest, opts ...grpc.CallOption) (*GetHomeLayoutConfigurationResponse, error)
	// GetCardSwitchNotification fetches card notification based on a given identifier
	GetCardSwitchNotification(ctx context.Context, in *GetCardSwitchNotificationRequest, opts ...grpc.CallOption) (*GetCardSwitchNotificationResponse, error)
	// rpc to determine is international DC widget is to be shown to the user on Fi home screen
	// also provide the content to be displayed in the widget
	// implementation doc: https://docs.google.com/document/d/1zunr5fcKh3bf07mFEk5ACmQ1ECagU060NQY-YAXEkvo/edit?tab=t.0
	// Figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=14513-39427&t=TEwxuxe0x7VombxZ-4
	GetDcInternationalWidget(ctx context.Context, in *GetDcInternationalWidgetRequest, opts ...grpc.CallOption) (*GetDcInternationalWidgetResponse, error)
	// RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
	// ActorId is a mandatory parameter in the Request
	// Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
	// INVALID ARGUMENT if any mandatory param is missing
	// RECORD NOT FOUND if no elements found for the given user on this screen
	// INTERNAL SERVER ERROR if any error in processing
	// OK if the list of targeted comms elements is fetched successfully
	FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error)
	// DynamicElementCallback rpc processes callback received on user action on any of the dynamic elements
	DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error)
}

type cardProvisioningClient struct {
	cc grpc.ClientConnInterface
}

func NewCardProvisioningClient(cc grpc.ClientConnInterface) CardProvisioningClient {
	return &cardProvisioningClient{cc}
}

func (c *cardProvisioningClient) CreateCard(ctx context.Context, in *CreateCardRequest, opts ...grpc.CallOption) (*CreateCardResponse, error) {
	out := new(CreateCardResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_CreateCard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) FetchCardCreationStatus(ctx context.Context, in *FetchCardCreationStatusRequest, opts ...grpc.CallOption) (*FetchCardCreationStatusResponse, error) {
	out := new(FetchCardCreationStatusResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_FetchCardCreationStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) FetchCards(ctx context.Context, in *FetchCardsRequest, opts ...grpc.CallOption) (*FetchCardsResponse, error) {
	out := new(FetchCardsResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_FetchCards_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) ActivateCard(ctx context.Context, in *ActivateCardRequest, opts ...grpc.CallOption) (*ActivateCardResponse, error) {
	out := new(ActivateCardResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_ActivateCard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) FetchCardActivationStatus(ctx context.Context, in *FetchCardActivationStatusRequest, opts ...grpc.CallOption) (*FetchCardActivationStatusResponse, error) {
	out := new(FetchCardActivationStatusResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_FetchCardActivationStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) DispatchCard(ctx context.Context, in *CardDispatchRequest, opts ...grpc.CallOption) (*CardDispatchResponse, error) {
	out := new(CardDispatchResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_DispatchCard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) CardDeliveryTracking(ctx context.Context, in *CardDeliveryTrackingRequest, opts ...grpc.CallOption) (*CardDeliveryTrackingResponse, error) {
	out := new(CardDeliveryTrackingResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_CardDeliveryTracking_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) FetchCardDetails(ctx context.Context, in *FetchCardDetailsRequest, opts ...grpc.CallOption) (*FetchCardDetailsResponse, error) {
	out := new(FetchCardDetailsResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_FetchCardDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) FetchCardDetailsByAccountId(ctx context.Context, in *FetchCardDetailsByAccountIdRequest, opts ...grpc.CallOption) (*FetchCardDetailsByAccountIdResponse, error) {
	out := new(FetchCardDetailsByAccountIdResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_FetchCardDetailsByAccountId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) SetCardPin(ctx context.Context, in *SetCardPinRequest, opts ...grpc.CallOption) (*SetCardPinResponse, error) {
	out := new(SetCardPinResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_SetCardPin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) ChangeCardPin(ctx context.Context, in *ChangeCardPinRequest, opts ...grpc.CallOption) (*ChangeCardPinResponse, error) {
	out := new(ChangeCardPinResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_ChangeCardPin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) ResetCardPin(ctx context.Context, in *ResetCardPinRequest, opts ...grpc.CallOption) (*ResetCardPinResponse, error) {
	out := new(ResetCardPinResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_ResetCardPin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) ValidateCardPin(ctx context.Context, in *ValidateCardPinRequest, opts ...grpc.CallOption) (*ValidateCardPinResponse, error) {
	out := new(ValidateCardPinResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_ValidateCardPin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) ValidateCard(ctx context.Context, in *CardValidateRequest, opts ...grpc.CallOption) (*CardValidateResponse, error) {
	out := new(CardValidateResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_ValidateCard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetCardGroups(ctx context.Context, in *GetCardGroupsRequest, opts ...grpc.CallOption) (*GetCardGroupsResponse, error) {
	out := new(GetCardGroupsResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetCardGroups_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) CardPinStatus(ctx context.Context, in *CardPinStatusRequest, opts ...grpc.CallOption) (*CardPinStatusResponse, error) {
	out := new(CardPinStatusResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_CardPinStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GenerateTxnId(ctx context.Context, in *GenerateTxnIdRequest, opts ...grpc.CallOption) (*GenerateTxnIdResponse, error) {
	out := new(GenerateTxnIdResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GenerateTxnId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetCardDetailsWithCvv(ctx context.Context, in *GetCardDetailsWithCvvRequest, opts ...grpc.CallOption) (*GetCardDetailsWithCvvResponse, error) {
	out := new(GetCardDetailsWithCvvResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetCardDetailsWithCvv_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) RenewCard(ctx context.Context, in *RenewCardRequest, opts ...grpc.CallOption) (*RenewCardResponse, error) {
	out := new(RenewCardResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_RenewCard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) RenewCardStatus(ctx context.Context, in *RenewCardStatusRequest, opts ...grpc.CallOption) (*RenewCardStatusResponse, error) {
	out := new(RenewCardStatusResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_RenewCardStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) VerifyQRCode(ctx context.Context, in *VerifyQRCodeRequest, opts ...grpc.CallOption) (*VerifyQRCodeResponse, error) {
	out := new(VerifyQRCodeResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_VerifyQRCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) FetchDeliveryTrackingStatus(ctx context.Context, in *FetchDeliveryTrackingStatusRequest, opts ...grpc.CallOption) (*FetchDeliveryTrackingStatusResponse, error) {
	out := new(FetchDeliveryTrackingStatusResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_FetchDeliveryTrackingStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) FetchTransactionableCards(ctx context.Context, in *FetchTransactionableCardsRequest, opts ...grpc.CallOption) (*FetchTransactionableCardsResponse, error) {
	out := new(FetchTransactionableCardsResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_FetchTransactionableCards_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) InitiateAdditionalAuthAttempt(ctx context.Context, in *InitiateAdditionalAuthAttemptRequest, opts ...grpc.CallOption) (*InitiateAdditionalAuthAttemptResponse, error) {
	out := new(InitiateAdditionalAuthAttemptResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_InitiateAdditionalAuthAttempt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetAdditionalAuthInfo(ctx context.Context, in *GetAdditionalAuthInfoRequest, opts ...grpc.CallOption) (*GetAdditionalAuthInfoResponse, error) {
	out := new(GetAdditionalAuthInfoResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetAdditionalAuthInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) UpdateAuthInfo(ctx context.Context, in *UpdateAuthInfoRequest, opts ...grpc.CallOption) (*UpdateAuthInfoResponse, error) {
	out := new(UpdateAuthInfoResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_UpdateAuthInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) TriggerCardNotifications(ctx context.Context, in *TriggerCardNotificationsRequest, opts ...grpc.CallOption) (*TriggerCardNotificationsResponse, error) {
	out := new(TriggerCardNotificationsResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_TriggerCardNotifications_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) UpdateFreeCardReplacement(ctx context.Context, in *UpdateFreeCardReplacementRequest, opts ...grpc.CallOption) (*UpdateFreeCardReplacementResponse, error) {
	out := new(UpdateFreeCardReplacementResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_UpdateFreeCardReplacement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) FetchCardTrackingDetails(ctx context.Context, in *FetchCardTrackingDetailsRequest, opts ...grpc.CallOption) (*FetchCardTrackingDetailsResponse, error) {
	out := new(FetchCardTrackingDetailsResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_FetchCardTrackingDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetCardShipmentTrackingDetails(ctx context.Context, in *GetCardShipmentTrackingDetailsRequest, opts ...grpc.CallOption) (*GetCardShipmentTrackingDetailsResponse, error) {
	out := new(GetCardShipmentTrackingDetailsResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetCardShipmentTrackingDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) UploadCardTrackingDetails(ctx context.Context, in *UploadCardTrackingDetailsRequest, opts ...grpc.CallOption) (*UploadCardTrackingDetailsResponse, error) {
	out := new(UploadCardTrackingDetailsResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_UploadCardTrackingDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) ProcessManualCardPinSet(ctx context.Context, in *ProcessManualCardPinSetRequest, opts ...grpc.CallOption) (*ProcessManualCardPinSetResponse, error) {
	out := new(ProcessManualCardPinSetResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_ProcessManualCardPinSet_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) ForceCardCreationEnquiry(ctx context.Context, in *ForceCardCreationEnquiryRequest, opts ...grpc.CallOption) (*ForceCardCreationEnquiryResponse, error) {
	out := new(ForceCardCreationEnquiryResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_ForceCardCreationEnquiry_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) UpdateTrackingDetails(ctx context.Context, in *UpdateTrackingDetailsRequest, opts ...grpc.CallOption) (*UpdateTrackingDetailsResponse, error) {
	out := new(UpdateTrackingDetailsResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_UpdateTrackingDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetCardActionAttempts(ctx context.Context, in *GetCardActionAttemptsRequest, opts ...grpc.CallOption) (*GetCardActionAttemptsResponse, error) {
	out := new(GetCardActionAttemptsResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetCardActionAttempts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) ProcessManualCardUnsuspend(ctx context.Context, in *ProcessManualCardUnsuspendRequest, opts ...grpc.CallOption) (*ProcessManualCardUnsuspendResponse, error) {
	out := new(ProcessManualCardUnsuspendResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_ProcessManualCardUnsuspend_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) CreatePhysicalCardDispatchAttempt(ctx context.Context, in *CreatePhysicalCardDispatchAttemptRequest, opts ...grpc.CallOption) (*CreatePhysicalCardDispatchAttemptResponse, error) {
	out := new(CreatePhysicalCardDispatchAttemptResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_CreatePhysicalCardDispatchAttempt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) InitiateShippingAddressUpdateAndDispatchPhysicalCard(ctx context.Context, in *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest, opts ...grpc.CallOption) (*InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse, error) {
	out := new(InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_InitiateShippingAddressUpdateAndDispatchPhysicalCard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) CheckShippingAddressUpdateAndPhysicalCardDispatchStatus(ctx context.Context, in *CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest, opts ...grpc.CallOption) (*CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse, error) {
	out := new(CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_CheckShippingAddressUpdateAndPhysicalCardDispatchStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetPhysicalCardDispatchStatus(ctx context.Context, in *GetPhysicalCardDispatchStatusRequest, opts ...grpc.CallOption) (*GetPhysicalCardDispatchStatusResponse, error) {
	out := new(GetPhysicalCardDispatchStatusResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetPhysicalCardDispatchStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetLatestCardForActorIds(ctx context.Context, in *GetLatestCardForActorIdsRequest, opts ...grpc.CallOption) (*GetLatestCardForActorIdsResponse, error) {
	out := new(GetLatestCardForActorIdsResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetLatestCardForActorIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetPhysicalCardActivationInfo(ctx context.Context, in *GetPhysicalCardActivationInfoRequest, opts ...grpc.CallOption) (*GetPhysicalCardActivationInfoResponse, error) {
	out := new(GetPhysicalCardActivationInfoResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetPhysicalCardActivationInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) InitiatePhysicalCardDispatch(ctx context.Context, in *InitiatePhysicalCardDispatchRequest, opts ...grpc.CallOption) (*InitiatePhysicalCardDispatchResponse, error) {
	out := new(InitiatePhysicalCardDispatchResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_InitiatePhysicalCardDispatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) FetchPhysicalCardChargesForUser(ctx context.Context, in *FetchPhysicalCardChargesForUserRequest, opts ...grpc.CallOption) (*FetchPhysicalCardChargesForUserResponse, error) {
	out := new(FetchPhysicalCardChargesForUserResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_FetchPhysicalCardChargesForUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetLastPhysicalCardIssuedForUser(ctx context.Context, in *GetLastPhysicalCardIssuedForUserRequest, opts ...grpc.CallOption) (*GetLastPhysicalCardIssuedForUserResponse, error) {
	out := new(GetLastPhysicalCardIssuedForUserResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetLastPhysicalCardIssuedForUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) ActivatePhysicalCard(ctx context.Context, in *ActivatePhysicalCardRequest, opts ...grpc.CallOption) (*ActivatePhysicalCardResponse, error) {
	out := new(ActivatePhysicalCardResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_ActivatePhysicalCard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetForexRefunds(ctx context.Context, in *GetForexRefundsRequest, opts ...grpc.CallOption) (*GetForexRefundsResponse, error) {
	out := new(GetForexRefundsResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetForexRefunds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetPaginatedForexRefundsByActorId(ctx context.Context, in *GetPaginatedForexRefundsByActorIdRequest, opts ...grpc.CallOption) (*GetPaginatedForexRefundsByActorIdResponse, error) {
	out := new(GetPaginatedForexRefundsByActorIdResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetPaginatedForexRefundsByActorId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) FetchPhysicalCardDispatchRequests(ctx context.Context, in *FetchPhysicalCardDispatchRequestsRequest, opts ...grpc.CallOption) (*FetchPhysicalCardDispatchRequestsResponse, error) {
	out := new(FetchPhysicalCardDispatchRequestsResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_FetchPhysicalCardDispatchRequests_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetForexRefundsByActorId(ctx context.Context, in *GetForexRefundsByActorIdRequest, opts ...grpc.CallOption) (*GetForexRefundsByActorIdResponse, error) {
	out := new(GetForexRefundsByActorIdResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetForexRefundsByActorId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) FetchCardRenewalChargesForUser(ctx context.Context, in *FetchCardRenewalChargesForUserRequest, opts ...grpc.CallOption) (*FetchCardRenewalChargesForUserResponse, error) {
	out := new(FetchCardRenewalChargesForUserResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_FetchCardRenewalChargesForUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) FetchForexRefundAggregates(ctx context.Context, in *FetchForexRefundAggregatesRequest, opts ...grpc.CallOption) (*FetchForexRefundAggregatesResponse, error) {
	out := new(FetchForexRefundAggregatesResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_FetchForexRefundAggregates_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetHomeLayoutConfiguration(ctx context.Context, in *GetHomeLayoutConfigurationRequest, opts ...grpc.CallOption) (*GetHomeLayoutConfigurationResponse, error) {
	out := new(GetHomeLayoutConfigurationResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetHomeLayoutConfiguration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetCardSwitchNotification(ctx context.Context, in *GetCardSwitchNotificationRequest, opts ...grpc.CallOption) (*GetCardSwitchNotificationResponse, error) {
	out := new(GetCardSwitchNotificationResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetCardSwitchNotification_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) GetDcInternationalWidget(ctx context.Context, in *GetDcInternationalWidgetRequest, opts ...grpc.CallOption) (*GetDcInternationalWidgetResponse, error) {
	out := new(GetDcInternationalWidgetResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_GetDcInternationalWidget_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	out := new(dynamic_elements.FetchDynamicElementsResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_FetchDynamicElements_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardProvisioningClient) DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	out := new(dynamic_elements.DynamicElementCallbackResponse)
	err := c.cc.Invoke(ctx, CardProvisioning_DynamicElementCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CardProvisioningServer is the server API for CardProvisioning service.
// All implementations should embed UnimplementedCardProvisioningServer
// for forward compatibility
type CardProvisioningServer interface {
	// Initiates card creation for the user. The decision to create single or dual card is controlled by the
	// provisioning strategy for the card.
	// If provisioning strategy is a ProvisioningStrategy.SINGLE_NUMBERED_CARD || SINGLE_NUMBERLESS_CARD,
	// a single card is provisioned for the user. The created cards in this case have default FORM as PHYSICAL.
	//
	// If provisioning strategy is a ProvisioningStrategy.DUAL_NUMBERLESS_CARD,
	// 2 cards are created for the user:
	// A PHYSICAL and a DIGITAL card. Post the successful sign up & account creation,
	// all cards will be created for the users. Post the card creation, digital card will be displayed
	// in the epiFi app  & physical cards will be shipped to the shipping address of the user.
	//
	// For initiating a new card creation request the mandatory parameters includes actor id and issuing bank.
	CreateCard(context.Context, *CreateCardRequest) (*CreateCardResponse, error)
	// Fetches the status of the card creation request already made.
	FetchCardCreationStatus(context.Context, *FetchCardCreationStatusRequest) (*FetchCardCreationStatusResponse, error)
	// RPC fetches all cards for the user based on the input filters. A user is expected to have multiple cards issued.
	// The card filters include:
	// - state: ACTIVATED, EXPIRED etc
	// - issuing bank : FEDERAL etc
	// - network type : VISA, MASTER etc
	// - type : DEBIT or CREDIT
	FetchCards(context.Context, *FetchCardsRequest) (*FetchCardsResponse, error)
	// Activates the given cards. The cards must be created by now. By default, both physical and digital
	// debit cards will not be activated. Any usage of the card is only permitted after the
	// activation step.
	// 1. PHYSICAL card needs to be activate for POS, ATM transactions
	// 2. DIGITAL card needs to be activated for ECOM/Online transactions.
	ActivateCard(context.Context, *ActivateCardRequest) (*ActivateCardResponse, error)
	// Fetches the status of the card activation requests already made.
	FetchCardActivationStatus(context.Context, *FetchCardActivationStatusRequest) (*FetchCardActivationStatusResponse, error)
	// Initiates the dispatch of the PHYSICAL card. Card will be dispatched as per the
	// address mentioned by the user.
	// TODO(anand): As per the RBI guidelines, the user needs to be provide the proof of shipping address within 3 months
	DispatchCard(context.Context, *CardDispatchRequest) (*CardDispatchResponse, error)
	// Provides tracking details for given card
	// Deprecated
	CardDeliveryTracking(context.Context, *CardDeliveryTrackingRequest) (*CardDeliveryTrackingResponse, error)
	// Fetches all the card related data and the status of the card.
	//
	// Any card information or sensitive information w.r.t bank has to be treated separately.
	// PCI - DSS has compliance requirements and guidelines regarding how such information has
	// to be stored/processed. Any system that touches the card information will fall in the scope of PCI.
	//
	// Hence, card service will have no access to card number, expiry or cvv. These will be stored in a
	// secret vault a.k.a Data Bunker. Card service will use token(issued by the data bunker) to refer
	// to the actual data.
	//
	// TODO(anand): If cvv cannot be stored in data bunker, how do we pass it along to the user?
	FetchCardDetails(context.Context, *FetchCardDetailsRequest) (*FetchCardDetailsResponse, error)
	// Fetches card details for the specified savings account id and card state
	FetchCardDetailsByAccountId(context.Context, *FetchCardDetailsByAccountIdRequest) (*FetchCardDetailsByAccountIdResponse, error)
	// ==============PIN MANAGEMENT APIS==============
	//
	// Epifi has defined our own specifications around pin management. The common library approach
	// is expected to be adapted by all the partner banks.
	// As part of this, all the pin related use-cases, there exists a encryptedBlock containing
	// all the sensitive information (card pin, secure pin etc).
	//
	// For card related RPCs, the cred block contains following fields:
	//  1. secure pin : same as UPI pin. The secure pin is verified against the customer/account details in the request.
	//     The verification is done at the issuing bank. If success, the pin set, change & reset proceeds.
	//  2. transaction id : TODO(anand) is it same as vendor request id?
	//  3. card pin: a.k.a the atm pin. This is the actual value which is set, changed or reset. In case of pin validation
	//     the card pin is verified against the stored pin. The verification happens at the end of issuing bank.
	//
	// Reference: https://docs.google.com/document/d/1PXrt_tGI5vmKYwpokqUoxVTxeey69zG6US1eNkJIEW0/edit?ts=5ebcc259#
	//
	// RPC facilitates setting the pin for the card.
	// Pin must be set after receiving the card and will be required for all the ATM/POS transactions.
	// Pin setup can be done only once for a particular card. Any subsequent call to set the pin must be rejected.
	// Only pin change and pin reset can be performed post setup.
	SetCardPin(context.Context, *SetCardPinRequest) (*SetCardPinResponse, error)
	// RPC facilitates changing the pin for the card to a new value.
	// Change pin is only available to the user if pin was previously set. The card pin can be changed voluntarily
	// and can also be forced by Epifi to be changed periodically. To change the pin, user needs to provide additional
	// authentication like old pin or some other auth-factor ( a secret value etc) registered with the issuing bank.
	// The changed pin must be used for all the transactions that follow.
	ChangeCardPin(context.Context, *ChangeCardPinRequest) (*ChangeCardPinResponse, error)
	// RPC facilitates resetting the pin for the card to a new entered value.
	// Reset pin is only available to the user if pin was previously set. Reset pin can be used when the
	// user has forgotten the pin. To reset the pin, user needs to provide additional authentication like a secret
	// value registered with the issuing bank. The pin is reset only if the additional authentication is validated
	// successfully.
	ResetCardPin(context.Context, *ResetCardPinRequest) (*ResetCardPinResponse, error)
	// RPC validates if the entered pin was valid for associated card.
	// Validate pin shall return failure if pin was not set or if the entered didn't match the pin set for the card.
	ValidateCardPin(context.Context, *ValidateCardPinRequest) (*ValidateCardPinResponse, error)
	// Checks if given card details(CardNumber, Expiry, PIN) are valid.
	// Usecase is around re-oobe scenarios. TODO: understand better and update
	ValidateCard(context.Context, *CardValidateRequest) (*CardValidateResponse, error)
	// RPC fetches card groups for the given user. A user is expected to have multiple cards issued.
	// A card group is a collection of cards identified by the same group_id identifier.
	//
	// The resulting card groups are sorted by `created_at` timestamps. Callers need to specify the order(ASC/DESC) of sorting.
	// NOTE: for the same `group_id`, cards are ordered by primary-key
	//
	// Callers can limit the number of card groups to be fetched by setting the `num_groups` field in request.
	// Additionally, caller can request to fetch all the available card groups for the user by setting the `get_all` to true.
	// TODO(anand) [low priority]: evaluate if this needs to be made paginated and prevent fetching all cards.
	// We don't expect large number of cards to be created per user and hence this should be fine for now.
	// TODO(anand): add option to filter the cards by vendor, network_type, card_type, states etc.
	GetCardGroups(context.Context, *GetCardGroupsRequest) (*GetCardGroupsResponse, error)
	// Fetches card pin status for card ids in the request
	CardPinStatus(context.Context, *CardPinStatusRequest) (*CardPinStatusResponse, error)
	// RPC to generate txn id based on flow type and vendor.
	// This RPC will return a unique txnid for the flow type for a vendor.
	GenerateTxnId(context.Context, *GenerateTxnIdRequest) (*GenerateTxnIdResponse, error)
	// Fetches card 16/4/3 in plain text for the card id in request.
	GetCardDetailsWithCvv(context.Context, *GetCardDetailsWithCvvRequest) (*GetCardDetailsWithCvvResponse, error)
	// RPC to block card and publish packet to create new card after card is blocked successfully
	RenewCard(context.Context, *RenewCardRequest) (*RenewCardResponse, error)
	// RPC to fetch renew card status for the given card id in the request
	// We will get new card id only when the old card is blocked successfully
	// If old card is blocked successfully and new card creation fails then we will again initiate creation of new card
	// and that card id will be sent in the response
	RenewCardStatus(context.Context, *RenewCardStatusRequest) (*RenewCardStatusResponse, error)
	// VerifyQRCode rpc decrypts the encrypted data and verify the data present in the qr code.
	// The data present in the qr code will be (Last 4 digits of the card + Last 4 digit of mobile number + last 4 digits of pin code)
	// After successful verification we will mark the card as delivered for the user and
	// user can then enable ATM/POS transaction for that card.
	VerifyQRCode(context.Context, *VerifyQRCodeRequest) (*VerifyQRCodeResponse, error)
	// FetchDeliveryTrackingStatus rpc fetches delivery status for the given card id
	FetchDeliveryTrackingStatus(context.Context, *FetchDeliveryTrackingStatusRequest) (*FetchDeliveryTrackingStatusResponse, error)
	// FetchTransactionableCards rpc fetches all cards for an actor through which transaction can be made or
	// could have been made in the past. All cards which were activated at least once, qualify.
	FetchTransactionableCards(context.Context, *FetchTransactionableCardsRequest) (*FetchTransactionableCardsResponse, error)
	// InitiateAdditionalAuthAttempt rpc creates a new auth attempt and returns the attempt id for the same
	InitiateAdditionalAuthAttempt(context.Context, *InitiateAdditionalAuthAttemptRequest) (*InitiateAdditionalAuthAttemptResponse, error)
	// GetAdditionalAuthInfo fetches the card id and action for a given auth attempt id.
	GetAdditionalAuthInfo(context.Context, *GetAdditionalAuthInfoRequest) (*GetAdditionalAuthInfoResponse, error)
	// UpdateAuthInfo updates the state of the auth such as SUCCESS, LIVENESS_FAILED, FACEMATCH_FAILED in case of Liveness and FM auth
	UpdateAuthInfo(context.Context, *UpdateAuthInfoRequest) (*UpdateAuthInfoResponse, error)
	// Triggers card specific notifications for particular user groups
	TriggerCardNotifications(context.Context, *TriggerCardNotificationsRequest) (*TriggerCardNotificationsResponse, error)
	// rpc to update free card replacements for a user. We will charge for card replacement when user reaches the maximum
	// free card replacement count. We might still need to give additional free card to users for cases when there is some issue with the card
	// and we need to give user an extra free card.
	UpdateFreeCardReplacement(context.Context, *UpdateFreeCardReplacementRequest) (*UpdateFreeCardReplacementResponse, error)
	// FetchCardTrackingDetails fetches cards for which awb details are not present, and get the awb details from the
	// bank and publishes packet to the queue to register those shipments at Shipway's end.
	// We will only fetch the AWB details for limited number of cards specified in the request based on their created_at
	// timestamp in ascending order.
	// This rpc will be triggered using a cron job which will between specific hours with an hourly frequency.
	// We are running it between specific hours because bank updates the AWB at a given time everyday.
	FetchCardTrackingDetails(context.Context, *FetchCardTrackingDetailsRequest) (*FetchCardTrackingDetailsResponse, error)
	// GetCardShipmentTrackingDetails returns the tracking details for a given card id. We will return awb number, carrier,
	// scans and delivery state information.
	GetCardShipmentTrackingDetails(context.Context, *GetCardShipmentTrackingDetailsRequest) (*GetCardShipmentTrackingDetailsResponse, error)
	// UploadCardTrackingDetails takes data present in csv file containing the tracking details such as Awb, courier partner
	// and updates it at our end in card tracking requests table and publishes the packet to register them as shipway
	UploadCardTrackingDetails(context.Context, *UploadCardTrackingDetailsRequest) (*UploadCardTrackingDetailsResponse, error)
	// ProcessManualCardPinSet will be triggered manually via a script or from sherlock.
	// It will mark pin set done at our end for a card and trigger the events to be published.
	// It is to be used for cases where card pin set is done through Federal IVR.
	// Long term solution is to get these events from federal and remove the manual dependency.
	ProcessManualCardPinSet(context.Context, *ProcessManualCardPinSetRequest) (*ProcessManualCardPinSetResponse, error)
	// ForceCardCreationEnquiry rpc will be triggered from Sherlock for card creation requests where we need to do enquiry at vendor's end
	// This can be due various reasons but not limited to -
	// 1. We got failure from vendor but on following up vendor asked us to do enquiry again
	// 2. Retries exhausted for card creation enquiry
	// 3. We got a failure ack but request actually reached vendor and they have asked us to enquire again
	ForceCardCreationEnquiry(context.Context, *ForceCardCreationEnquiryRequest) (*ForceCardCreationEnquiryResponse, error)
	// UpdateTrackingDetails takes data present in csv file containing the updated tracking details for shipments for
	// which the initial delivery partner refused and we received a new awb number and tracking partner
	UpdateTrackingDetails(context.Context, *UpdateTrackingDetailsRequest) (*UpdateTrackingDetailsResponse, error)
	// GetCardActionAttempts rpc fetches all the attempts for a given card id corresponding to a given action (pin set, pin reset)
	// Each attempt has details regarding if the action was successful or failed, along with the failure reasons
	GetCardActionAttempts(context.Context, *GetCardActionAttemptsRequest) (*GetCardActionAttemptsResponse, error)
	// ProcessManualCardUnsuspend rpc will be triggered from Sherlock dev action where we manually need to un-suspend card.
	// Card state will be updated from suspended to activated and pi state will be updated from suspended to verified.
	// It is to be used for cases where card state is un-suspended through Federal IVR and at our end it is still is in
	// suspended state.
	ProcessManualCardUnsuspend(context.Context, *ProcessManualCardUnsuspendRequest) (*ProcessManualCardUnsuspendResponse, error)
	// CreatePhysicalCardDispatchAttempt creates a new dispatch request in our db and publishes packet for processing of the dispatch request at vendor
	// for the card id sent in the request.
	// We will validate if card is not a physical card and there are no dispatch request in progress for this card
	// Deprecated this in favour of new rpc InitiatePhysicalCardDispatch
	CreatePhysicalCardDispatchAttempt(context.Context, *CreatePhysicalCardDispatchAttemptRequest) (*CreatePhysicalCardDispatchAttemptResponse, error)
	// InitiateShippingAddressUpdateAndDispatchPhysicalCard is invoked by client to issue physical card to a user, it creates shipping preference
	// with the address type received in the request and publishes packet for initiating and checking the status for
	// shipping address and triggering physical dispatch request post successful address update.
	// As the process for issuing physical card is not synchronous client needs to check the current state of the request using
	// CheckPhysicalCardDispatchStatus rpc.
	InitiateShippingAddressUpdateAndDispatchPhysicalCard(context.Context, *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) (*InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse, error)
	// CheckShippingAddressUpdateAndPhysicalCardDispatchStatus is called by client to check the status of shipping address
	// update and physical card dispatch request status
	CheckShippingAddressUpdateAndPhysicalCardDispatchStatus(context.Context, *CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest) (*CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse, error)
	// GetPhysicalCardDispatchStatus returns the current status of the physical card dispatch request
	GetPhysicalCardDispatchStatus(context.Context, *GetPhysicalCardDispatchStatusRequest) (*GetPhysicalCardDispatchStatusResponse, error)
	// GetLatestCardForActorIds returns the latest card for the actor id's sent in the request, latest card is fetched
	// based on the latest created at timestamp, in case if card not found for any actor we will return the remaining cards.
	// We will have card state as filter for fetching cards only within the given states, if no filter is passed we will
	// fetch cards with any state
	GetLatestCardForActorIds(context.Context, *GetLatestCardForActorIdsRequest) (*GetLatestCardForActorIdsResponse, error)
	// GetPhysicalCardActivationInfo to determine card physical activation status. It will take either card id or actor id as input. In case of actor-id
	// we will send the activation status of the latest physical card of the user. If activated we will share more details like activation timestamp etc
	GetPhysicalCardActivationInfo(context.Context, *GetPhysicalCardActivationInfoRequest) (*GetPhysicalCardActivationInfoResponse, error)
	// InitiatePhysicalCardDispatch creates a physical card dispatch request & initiates Physical Card Dispatch workflow and
	// creates a fund transfer request which further initiates the Fund Transfer workflow. It returns a deeplink corresponding
	// to the next action i.e. Authorize fund transfer
	InitiatePhysicalCardDispatch(context.Context, *InitiatePhysicalCardDispatchRequest) (*InitiatePhysicalCardDispatchResponse, error)
	// FetchPhysicalCardChargesForUser fetches the amount to be paid by different users to order a physical debit card
	FetchPhysicalCardChargesForUser(context.Context, *FetchPhysicalCardChargesForUserRequest) (*FetchPhysicalCardChargesForUserResponse, error)
	// GetLastPhysicalCardIssuedForUser returns the timestamp of the most recent physical card issued for the user.
	GetLastPhysicalCardIssuedForUser(context.Context, *GetLastPhysicalCardIssuedForUserRequest) (*GetLastPhysicalCardIssuedForUserResponse, error)
	// ActivatePhysicalCard rpc will be used to activate the card by switching on the POS end ECOM for the user.
	ActivatePhysicalCard(context.Context, *ActivatePhysicalCardRequest) (*ActivatePhysicalCardResponse, error)
	// GetForexRefunds RPC will fetch forex refund data based on an identifier like id, txn_id, etc.
	GetForexRefunds(context.Context, *GetForexRefundsRequest) (*GetForexRefundsResponse, error)
	// GetPaginatedForexRefundsByActorId RPC will fetch a paginated list of forex refunds for an actor
	GetPaginatedForexRefundsByActorId(context.Context, *GetPaginatedForexRefundsByActorIdRequest) (*GetPaginatedForexRefundsByActorIdResponse, error)
	// GetPhysicalCardDispatchRequests RPC will fetch all the dispatch requests for card id
	FetchPhysicalCardDispatchRequests(context.Context, *FetchPhysicalCardDispatchRequestsRequest) (*FetchPhysicalCardDispatchRequestsResponse, error)
	// GetCompletedForexRefundsByActorId RPC will fetch a list of forex refunds for an actor in completed state
	GetForexRefundsByActorId(context.Context, *GetForexRefundsByActorIdRequest) (*GetForexRefundsByActorIdResponse, error)
	// FetchCardRenewalCharges RPC will fetch card renewal charges
	FetchCardRenewalChargesForUser(context.Context, *FetchCardRenewalChargesForUserRequest) (*FetchCardRenewalChargesForUserResponse, error)
	// FetchForexRefundAggregates will fetch the aggregate refunds received in a given time range. . This can also give result for a
	// particular user if the actor id request param is not empty.
	FetchForexRefundAggregates(context.Context, *FetchForexRefundAggregatesRequest) (*FetchForexRefundAggregatesResponse, error)
	// GetHomeLayoutConfiguration will fetch the home layout configuration,
	// based on which the ordering of the component on the home screen will be decided.
	// We'll store the ordering of the components in `client(frontend)` configs for each expected layout configuration(primarily layout_id) that this RPC can return.
	GetHomeLayoutConfiguration(context.Context, *GetHomeLayoutConfigurationRequest) (*GetHomeLayoutConfigurationResponse, error)
	// GetCardSwitchNotification fetches card notification based on a given identifier
	GetCardSwitchNotification(context.Context, *GetCardSwitchNotificationRequest) (*GetCardSwitchNotificationResponse, error)
	// rpc to determine is international DC widget is to be shown to the user on Fi home screen
	// also provide the content to be displayed in the widget
	// implementation doc: https://docs.google.com/document/d/1zunr5fcKh3bf07mFEk5ACmQ1ECagU060NQY-YAXEkvo/edit?tab=t.0
	// Figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=14513-39427&t=TEwxuxe0x7VombxZ-4
	GetDcInternationalWidget(context.Context, *GetDcInternationalWidgetRequest) (*GetDcInternationalWidgetResponse, error)
	// RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
	// ActorId is a mandatory parameter in the Request
	// Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
	// INVALID ARGUMENT if any mandatory param is missing
	// RECORD NOT FOUND if no elements found for the given user on this screen
	// INTERNAL SERVER ERROR if any error in processing
	// OK if the list of targeted comms elements is fetched successfully
	FetchDynamicElements(context.Context, *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error)
	// DynamicElementCallback rpc processes callback received on user action on any of the dynamic elements
	DynamicElementCallback(context.Context, *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error)
}

// UnimplementedCardProvisioningServer should be embedded to have forward compatible implementations.
type UnimplementedCardProvisioningServer struct {
}

func (UnimplementedCardProvisioningServer) CreateCard(context.Context, *CreateCardRequest) (*CreateCardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCard not implemented")
}
func (UnimplementedCardProvisioningServer) FetchCardCreationStatus(context.Context, *FetchCardCreationStatusRequest) (*FetchCardCreationStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchCardCreationStatus not implemented")
}
func (UnimplementedCardProvisioningServer) FetchCards(context.Context, *FetchCardsRequest) (*FetchCardsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchCards not implemented")
}
func (UnimplementedCardProvisioningServer) ActivateCard(context.Context, *ActivateCardRequest) (*ActivateCardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivateCard not implemented")
}
func (UnimplementedCardProvisioningServer) FetchCardActivationStatus(context.Context, *FetchCardActivationStatusRequest) (*FetchCardActivationStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchCardActivationStatus not implemented")
}
func (UnimplementedCardProvisioningServer) DispatchCard(context.Context, *CardDispatchRequest) (*CardDispatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DispatchCard not implemented")
}
func (UnimplementedCardProvisioningServer) CardDeliveryTracking(context.Context, *CardDeliveryTrackingRequest) (*CardDeliveryTrackingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CardDeliveryTracking not implemented")
}
func (UnimplementedCardProvisioningServer) FetchCardDetails(context.Context, *FetchCardDetailsRequest) (*FetchCardDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchCardDetails not implemented")
}
func (UnimplementedCardProvisioningServer) FetchCardDetailsByAccountId(context.Context, *FetchCardDetailsByAccountIdRequest) (*FetchCardDetailsByAccountIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchCardDetailsByAccountId not implemented")
}
func (UnimplementedCardProvisioningServer) SetCardPin(context.Context, *SetCardPinRequest) (*SetCardPinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCardPin not implemented")
}
func (UnimplementedCardProvisioningServer) ChangeCardPin(context.Context, *ChangeCardPinRequest) (*ChangeCardPinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeCardPin not implemented")
}
func (UnimplementedCardProvisioningServer) ResetCardPin(context.Context, *ResetCardPinRequest) (*ResetCardPinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetCardPin not implemented")
}
func (UnimplementedCardProvisioningServer) ValidateCardPin(context.Context, *ValidateCardPinRequest) (*ValidateCardPinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateCardPin not implemented")
}
func (UnimplementedCardProvisioningServer) ValidateCard(context.Context, *CardValidateRequest) (*CardValidateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateCard not implemented")
}
func (UnimplementedCardProvisioningServer) GetCardGroups(context.Context, *GetCardGroupsRequest) (*GetCardGroupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardGroups not implemented")
}
func (UnimplementedCardProvisioningServer) CardPinStatus(context.Context, *CardPinStatusRequest) (*CardPinStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CardPinStatus not implemented")
}
func (UnimplementedCardProvisioningServer) GenerateTxnId(context.Context, *GenerateTxnIdRequest) (*GenerateTxnIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateTxnId not implemented")
}
func (UnimplementedCardProvisioningServer) GetCardDetailsWithCvv(context.Context, *GetCardDetailsWithCvvRequest) (*GetCardDetailsWithCvvResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardDetailsWithCvv not implemented")
}
func (UnimplementedCardProvisioningServer) RenewCard(context.Context, *RenewCardRequest) (*RenewCardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RenewCard not implemented")
}
func (UnimplementedCardProvisioningServer) RenewCardStatus(context.Context, *RenewCardStatusRequest) (*RenewCardStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RenewCardStatus not implemented")
}
func (UnimplementedCardProvisioningServer) VerifyQRCode(context.Context, *VerifyQRCodeRequest) (*VerifyQRCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyQRCode not implemented")
}
func (UnimplementedCardProvisioningServer) FetchDeliveryTrackingStatus(context.Context, *FetchDeliveryTrackingStatusRequest) (*FetchDeliveryTrackingStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchDeliveryTrackingStatus not implemented")
}
func (UnimplementedCardProvisioningServer) FetchTransactionableCards(context.Context, *FetchTransactionableCardsRequest) (*FetchTransactionableCardsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchTransactionableCards not implemented")
}
func (UnimplementedCardProvisioningServer) InitiateAdditionalAuthAttempt(context.Context, *InitiateAdditionalAuthAttemptRequest) (*InitiateAdditionalAuthAttemptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateAdditionalAuthAttempt not implemented")
}
func (UnimplementedCardProvisioningServer) GetAdditionalAuthInfo(context.Context, *GetAdditionalAuthInfoRequest) (*GetAdditionalAuthInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAdditionalAuthInfo not implemented")
}
func (UnimplementedCardProvisioningServer) UpdateAuthInfo(context.Context, *UpdateAuthInfoRequest) (*UpdateAuthInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuthInfo not implemented")
}
func (UnimplementedCardProvisioningServer) TriggerCardNotifications(context.Context, *TriggerCardNotificationsRequest) (*TriggerCardNotificationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerCardNotifications not implemented")
}
func (UnimplementedCardProvisioningServer) UpdateFreeCardReplacement(context.Context, *UpdateFreeCardReplacementRequest) (*UpdateFreeCardReplacementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFreeCardReplacement not implemented")
}
func (UnimplementedCardProvisioningServer) FetchCardTrackingDetails(context.Context, *FetchCardTrackingDetailsRequest) (*FetchCardTrackingDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchCardTrackingDetails not implemented")
}
func (UnimplementedCardProvisioningServer) GetCardShipmentTrackingDetails(context.Context, *GetCardShipmentTrackingDetailsRequest) (*GetCardShipmentTrackingDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardShipmentTrackingDetails not implemented")
}
func (UnimplementedCardProvisioningServer) UploadCardTrackingDetails(context.Context, *UploadCardTrackingDetailsRequest) (*UploadCardTrackingDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadCardTrackingDetails not implemented")
}
func (UnimplementedCardProvisioningServer) ProcessManualCardPinSet(context.Context, *ProcessManualCardPinSetRequest) (*ProcessManualCardPinSetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessManualCardPinSet not implemented")
}
func (UnimplementedCardProvisioningServer) ForceCardCreationEnquiry(context.Context, *ForceCardCreationEnquiryRequest) (*ForceCardCreationEnquiryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ForceCardCreationEnquiry not implemented")
}
func (UnimplementedCardProvisioningServer) UpdateTrackingDetails(context.Context, *UpdateTrackingDetailsRequest) (*UpdateTrackingDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTrackingDetails not implemented")
}
func (UnimplementedCardProvisioningServer) GetCardActionAttempts(context.Context, *GetCardActionAttemptsRequest) (*GetCardActionAttemptsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardActionAttempts not implemented")
}
func (UnimplementedCardProvisioningServer) ProcessManualCardUnsuspend(context.Context, *ProcessManualCardUnsuspendRequest) (*ProcessManualCardUnsuspendResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessManualCardUnsuspend not implemented")
}
func (UnimplementedCardProvisioningServer) CreatePhysicalCardDispatchAttempt(context.Context, *CreatePhysicalCardDispatchAttemptRequest) (*CreatePhysicalCardDispatchAttemptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePhysicalCardDispatchAttempt not implemented")
}
func (UnimplementedCardProvisioningServer) InitiateShippingAddressUpdateAndDispatchPhysicalCard(context.Context, *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) (*InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateShippingAddressUpdateAndDispatchPhysicalCard not implemented")
}
func (UnimplementedCardProvisioningServer) CheckShippingAddressUpdateAndPhysicalCardDispatchStatus(context.Context, *CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest) (*CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckShippingAddressUpdateAndPhysicalCardDispatchStatus not implemented")
}
func (UnimplementedCardProvisioningServer) GetPhysicalCardDispatchStatus(context.Context, *GetPhysicalCardDispatchStatusRequest) (*GetPhysicalCardDispatchStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPhysicalCardDispatchStatus not implemented")
}
func (UnimplementedCardProvisioningServer) GetLatestCardForActorIds(context.Context, *GetLatestCardForActorIdsRequest) (*GetLatestCardForActorIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestCardForActorIds not implemented")
}
func (UnimplementedCardProvisioningServer) GetPhysicalCardActivationInfo(context.Context, *GetPhysicalCardActivationInfoRequest) (*GetPhysicalCardActivationInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPhysicalCardActivationInfo not implemented")
}
func (UnimplementedCardProvisioningServer) InitiatePhysicalCardDispatch(context.Context, *InitiatePhysicalCardDispatchRequest) (*InitiatePhysicalCardDispatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiatePhysicalCardDispatch not implemented")
}
func (UnimplementedCardProvisioningServer) FetchPhysicalCardChargesForUser(context.Context, *FetchPhysicalCardChargesForUserRequest) (*FetchPhysicalCardChargesForUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchPhysicalCardChargesForUser not implemented")
}
func (UnimplementedCardProvisioningServer) GetLastPhysicalCardIssuedForUser(context.Context, *GetLastPhysicalCardIssuedForUserRequest) (*GetLastPhysicalCardIssuedForUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLastPhysicalCardIssuedForUser not implemented")
}
func (UnimplementedCardProvisioningServer) ActivatePhysicalCard(context.Context, *ActivatePhysicalCardRequest) (*ActivatePhysicalCardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivatePhysicalCard not implemented")
}
func (UnimplementedCardProvisioningServer) GetForexRefunds(context.Context, *GetForexRefundsRequest) (*GetForexRefundsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetForexRefunds not implemented")
}
func (UnimplementedCardProvisioningServer) GetPaginatedForexRefundsByActorId(context.Context, *GetPaginatedForexRefundsByActorIdRequest) (*GetPaginatedForexRefundsByActorIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaginatedForexRefundsByActorId not implemented")
}
func (UnimplementedCardProvisioningServer) FetchPhysicalCardDispatchRequests(context.Context, *FetchPhysicalCardDispatchRequestsRequest) (*FetchPhysicalCardDispatchRequestsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchPhysicalCardDispatchRequests not implemented")
}
func (UnimplementedCardProvisioningServer) GetForexRefundsByActorId(context.Context, *GetForexRefundsByActorIdRequest) (*GetForexRefundsByActorIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetForexRefundsByActorId not implemented")
}
func (UnimplementedCardProvisioningServer) FetchCardRenewalChargesForUser(context.Context, *FetchCardRenewalChargesForUserRequest) (*FetchCardRenewalChargesForUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchCardRenewalChargesForUser not implemented")
}
func (UnimplementedCardProvisioningServer) FetchForexRefundAggregates(context.Context, *FetchForexRefundAggregatesRequest) (*FetchForexRefundAggregatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchForexRefundAggregates not implemented")
}
func (UnimplementedCardProvisioningServer) GetHomeLayoutConfiguration(context.Context, *GetHomeLayoutConfigurationRequest) (*GetHomeLayoutConfigurationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHomeLayoutConfiguration not implemented")
}
func (UnimplementedCardProvisioningServer) GetCardSwitchNotification(context.Context, *GetCardSwitchNotificationRequest) (*GetCardSwitchNotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardSwitchNotification not implemented")
}
func (UnimplementedCardProvisioningServer) GetDcInternationalWidget(context.Context, *GetDcInternationalWidgetRequest) (*GetDcInternationalWidgetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDcInternationalWidget not implemented")
}
func (UnimplementedCardProvisioningServer) FetchDynamicElements(context.Context, *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchDynamicElements not implemented")
}
func (UnimplementedCardProvisioningServer) DynamicElementCallback(context.Context, *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DynamicElementCallback not implemented")
}

// UnsafeCardProvisioningServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CardProvisioningServer will
// result in compilation errors.
type UnsafeCardProvisioningServer interface {
	mustEmbedUnimplementedCardProvisioningServer()
}

func RegisterCardProvisioningServer(s grpc.ServiceRegistrar, srv CardProvisioningServer) {
	s.RegisterService(&CardProvisioning_ServiceDesc, srv)
}

func _CardProvisioning_CreateCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).CreateCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_CreateCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).CreateCard(ctx, req.(*CreateCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_FetchCardCreationStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchCardCreationStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).FetchCardCreationStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_FetchCardCreationStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).FetchCardCreationStatus(ctx, req.(*FetchCardCreationStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_FetchCards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchCardsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).FetchCards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_FetchCards_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).FetchCards(ctx, req.(*FetchCardsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_ActivateCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivateCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).ActivateCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_ActivateCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).ActivateCard(ctx, req.(*ActivateCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_FetchCardActivationStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchCardActivationStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).FetchCardActivationStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_FetchCardActivationStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).FetchCardActivationStatus(ctx, req.(*FetchCardActivationStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_DispatchCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardDispatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).DispatchCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_DispatchCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).DispatchCard(ctx, req.(*CardDispatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_CardDeliveryTracking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardDeliveryTrackingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).CardDeliveryTracking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_CardDeliveryTracking_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).CardDeliveryTracking(ctx, req.(*CardDeliveryTrackingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_FetchCardDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchCardDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).FetchCardDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_FetchCardDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).FetchCardDetails(ctx, req.(*FetchCardDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_FetchCardDetailsByAccountId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchCardDetailsByAccountIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).FetchCardDetailsByAccountId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_FetchCardDetailsByAccountId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).FetchCardDetailsByAccountId(ctx, req.(*FetchCardDetailsByAccountIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_SetCardPin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCardPinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).SetCardPin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_SetCardPin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).SetCardPin(ctx, req.(*SetCardPinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_ChangeCardPin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeCardPinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).ChangeCardPin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_ChangeCardPin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).ChangeCardPin(ctx, req.(*ChangeCardPinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_ResetCardPin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetCardPinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).ResetCardPin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_ResetCardPin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).ResetCardPin(ctx, req.(*ResetCardPinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_ValidateCardPin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateCardPinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).ValidateCardPin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_ValidateCardPin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).ValidateCardPin(ctx, req.(*ValidateCardPinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_ValidateCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardValidateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).ValidateCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_ValidateCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).ValidateCard(ctx, req.(*CardValidateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetCardGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetCardGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetCardGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetCardGroups(ctx, req.(*GetCardGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_CardPinStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardPinStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).CardPinStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_CardPinStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).CardPinStatus(ctx, req.(*CardPinStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GenerateTxnId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateTxnIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GenerateTxnId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GenerateTxnId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GenerateTxnId(ctx, req.(*GenerateTxnIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetCardDetailsWithCvv_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardDetailsWithCvvRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetCardDetailsWithCvv(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetCardDetailsWithCvv_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetCardDetailsWithCvv(ctx, req.(*GetCardDetailsWithCvvRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_RenewCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RenewCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).RenewCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_RenewCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).RenewCard(ctx, req.(*RenewCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_RenewCardStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RenewCardStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).RenewCardStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_RenewCardStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).RenewCardStatus(ctx, req.(*RenewCardStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_VerifyQRCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyQRCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).VerifyQRCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_VerifyQRCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).VerifyQRCode(ctx, req.(*VerifyQRCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_FetchDeliveryTrackingStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchDeliveryTrackingStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).FetchDeliveryTrackingStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_FetchDeliveryTrackingStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).FetchDeliveryTrackingStatus(ctx, req.(*FetchDeliveryTrackingStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_FetchTransactionableCards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchTransactionableCardsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).FetchTransactionableCards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_FetchTransactionableCards_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).FetchTransactionableCards(ctx, req.(*FetchTransactionableCardsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_InitiateAdditionalAuthAttempt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateAdditionalAuthAttemptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).InitiateAdditionalAuthAttempt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_InitiateAdditionalAuthAttempt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).InitiateAdditionalAuthAttempt(ctx, req.(*InitiateAdditionalAuthAttemptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetAdditionalAuthInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAdditionalAuthInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetAdditionalAuthInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetAdditionalAuthInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetAdditionalAuthInfo(ctx, req.(*GetAdditionalAuthInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_UpdateAuthInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAuthInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).UpdateAuthInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_UpdateAuthInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).UpdateAuthInfo(ctx, req.(*UpdateAuthInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_TriggerCardNotifications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerCardNotificationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).TriggerCardNotifications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_TriggerCardNotifications_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).TriggerCardNotifications(ctx, req.(*TriggerCardNotificationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_UpdateFreeCardReplacement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFreeCardReplacementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).UpdateFreeCardReplacement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_UpdateFreeCardReplacement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).UpdateFreeCardReplacement(ctx, req.(*UpdateFreeCardReplacementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_FetchCardTrackingDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchCardTrackingDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).FetchCardTrackingDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_FetchCardTrackingDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).FetchCardTrackingDetails(ctx, req.(*FetchCardTrackingDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetCardShipmentTrackingDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardShipmentTrackingDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetCardShipmentTrackingDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetCardShipmentTrackingDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetCardShipmentTrackingDetails(ctx, req.(*GetCardShipmentTrackingDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_UploadCardTrackingDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadCardTrackingDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).UploadCardTrackingDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_UploadCardTrackingDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).UploadCardTrackingDetails(ctx, req.(*UploadCardTrackingDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_ProcessManualCardPinSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessManualCardPinSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).ProcessManualCardPinSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_ProcessManualCardPinSet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).ProcessManualCardPinSet(ctx, req.(*ProcessManualCardPinSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_ForceCardCreationEnquiry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ForceCardCreationEnquiryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).ForceCardCreationEnquiry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_ForceCardCreationEnquiry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).ForceCardCreationEnquiry(ctx, req.(*ForceCardCreationEnquiryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_UpdateTrackingDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTrackingDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).UpdateTrackingDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_UpdateTrackingDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).UpdateTrackingDetails(ctx, req.(*UpdateTrackingDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetCardActionAttempts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardActionAttemptsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetCardActionAttempts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetCardActionAttempts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetCardActionAttempts(ctx, req.(*GetCardActionAttemptsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_ProcessManualCardUnsuspend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessManualCardUnsuspendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).ProcessManualCardUnsuspend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_ProcessManualCardUnsuspend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).ProcessManualCardUnsuspend(ctx, req.(*ProcessManualCardUnsuspendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_CreatePhysicalCardDispatchAttempt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePhysicalCardDispatchAttemptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).CreatePhysicalCardDispatchAttempt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_CreatePhysicalCardDispatchAttempt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).CreatePhysicalCardDispatchAttempt(ctx, req.(*CreatePhysicalCardDispatchAttemptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_InitiateShippingAddressUpdateAndDispatchPhysicalCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).InitiateShippingAddressUpdateAndDispatchPhysicalCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_InitiateShippingAddressUpdateAndDispatchPhysicalCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).InitiateShippingAddressUpdateAndDispatchPhysicalCard(ctx, req.(*InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_CheckShippingAddressUpdateAndPhysicalCardDispatchStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).CheckShippingAddressUpdateAndPhysicalCardDispatchStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_CheckShippingAddressUpdateAndPhysicalCardDispatchStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).CheckShippingAddressUpdateAndPhysicalCardDispatchStatus(ctx, req.(*CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetPhysicalCardDispatchStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPhysicalCardDispatchStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetPhysicalCardDispatchStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetPhysicalCardDispatchStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetPhysicalCardDispatchStatus(ctx, req.(*GetPhysicalCardDispatchStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetLatestCardForActorIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLatestCardForActorIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetLatestCardForActorIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetLatestCardForActorIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetLatestCardForActorIds(ctx, req.(*GetLatestCardForActorIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetPhysicalCardActivationInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPhysicalCardActivationInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetPhysicalCardActivationInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetPhysicalCardActivationInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetPhysicalCardActivationInfo(ctx, req.(*GetPhysicalCardActivationInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_InitiatePhysicalCardDispatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiatePhysicalCardDispatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).InitiatePhysicalCardDispatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_InitiatePhysicalCardDispatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).InitiatePhysicalCardDispatch(ctx, req.(*InitiatePhysicalCardDispatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_FetchPhysicalCardChargesForUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchPhysicalCardChargesForUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).FetchPhysicalCardChargesForUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_FetchPhysicalCardChargesForUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).FetchPhysicalCardChargesForUser(ctx, req.(*FetchPhysicalCardChargesForUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetLastPhysicalCardIssuedForUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastPhysicalCardIssuedForUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetLastPhysicalCardIssuedForUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetLastPhysicalCardIssuedForUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetLastPhysicalCardIssuedForUser(ctx, req.(*GetLastPhysicalCardIssuedForUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_ActivatePhysicalCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivatePhysicalCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).ActivatePhysicalCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_ActivatePhysicalCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).ActivatePhysicalCard(ctx, req.(*ActivatePhysicalCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetForexRefunds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetForexRefundsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetForexRefunds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetForexRefunds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetForexRefunds(ctx, req.(*GetForexRefundsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetPaginatedForexRefundsByActorId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaginatedForexRefundsByActorIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetPaginatedForexRefundsByActorId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetPaginatedForexRefundsByActorId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetPaginatedForexRefundsByActorId(ctx, req.(*GetPaginatedForexRefundsByActorIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_FetchPhysicalCardDispatchRequests_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchPhysicalCardDispatchRequestsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).FetchPhysicalCardDispatchRequests(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_FetchPhysicalCardDispatchRequests_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).FetchPhysicalCardDispatchRequests(ctx, req.(*FetchPhysicalCardDispatchRequestsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetForexRefundsByActorId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetForexRefundsByActorIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetForexRefundsByActorId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetForexRefundsByActorId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetForexRefundsByActorId(ctx, req.(*GetForexRefundsByActorIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_FetchCardRenewalChargesForUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchCardRenewalChargesForUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).FetchCardRenewalChargesForUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_FetchCardRenewalChargesForUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).FetchCardRenewalChargesForUser(ctx, req.(*FetchCardRenewalChargesForUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_FetchForexRefundAggregates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchForexRefundAggregatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).FetchForexRefundAggregates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_FetchForexRefundAggregates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).FetchForexRefundAggregates(ctx, req.(*FetchForexRefundAggregatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetHomeLayoutConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHomeLayoutConfigurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetHomeLayoutConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetHomeLayoutConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetHomeLayoutConfiguration(ctx, req.(*GetHomeLayoutConfigurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetCardSwitchNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardSwitchNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetCardSwitchNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetCardSwitchNotification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetCardSwitchNotification(ctx, req.(*GetCardSwitchNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_GetDcInternationalWidget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDcInternationalWidgetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).GetDcInternationalWidget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_GetDcInternationalWidget_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).GetDcInternationalWidget(ctx, req.(*GetDcInternationalWidgetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_FetchDynamicElements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dynamic_elements.FetchDynamicElementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).FetchDynamicElements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_FetchDynamicElements_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).FetchDynamicElements(ctx, req.(*dynamic_elements.FetchDynamicElementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardProvisioning_DynamicElementCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dynamic_elements.DynamicElementCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardProvisioningServer).DynamicElementCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardProvisioning_DynamicElementCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardProvisioningServer).DynamicElementCallback(ctx, req.(*dynamic_elements.DynamicElementCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CardProvisioning_ServiceDesc is the grpc.ServiceDesc for CardProvisioning service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CardProvisioning_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "card.provisioning.CardProvisioning",
	HandlerType: (*CardProvisioningServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCard",
			Handler:    _CardProvisioning_CreateCard_Handler,
		},
		{
			MethodName: "FetchCardCreationStatus",
			Handler:    _CardProvisioning_FetchCardCreationStatus_Handler,
		},
		{
			MethodName: "FetchCards",
			Handler:    _CardProvisioning_FetchCards_Handler,
		},
		{
			MethodName: "ActivateCard",
			Handler:    _CardProvisioning_ActivateCard_Handler,
		},
		{
			MethodName: "FetchCardActivationStatus",
			Handler:    _CardProvisioning_FetchCardActivationStatus_Handler,
		},
		{
			MethodName: "DispatchCard",
			Handler:    _CardProvisioning_DispatchCard_Handler,
		},
		{
			MethodName: "CardDeliveryTracking",
			Handler:    _CardProvisioning_CardDeliveryTracking_Handler,
		},
		{
			MethodName: "FetchCardDetails",
			Handler:    _CardProvisioning_FetchCardDetails_Handler,
		},
		{
			MethodName: "FetchCardDetailsByAccountId",
			Handler:    _CardProvisioning_FetchCardDetailsByAccountId_Handler,
		},
		{
			MethodName: "SetCardPin",
			Handler:    _CardProvisioning_SetCardPin_Handler,
		},
		{
			MethodName: "ChangeCardPin",
			Handler:    _CardProvisioning_ChangeCardPin_Handler,
		},
		{
			MethodName: "ResetCardPin",
			Handler:    _CardProvisioning_ResetCardPin_Handler,
		},
		{
			MethodName: "ValidateCardPin",
			Handler:    _CardProvisioning_ValidateCardPin_Handler,
		},
		{
			MethodName: "ValidateCard",
			Handler:    _CardProvisioning_ValidateCard_Handler,
		},
		{
			MethodName: "GetCardGroups",
			Handler:    _CardProvisioning_GetCardGroups_Handler,
		},
		{
			MethodName: "CardPinStatus",
			Handler:    _CardProvisioning_CardPinStatus_Handler,
		},
		{
			MethodName: "GenerateTxnId",
			Handler:    _CardProvisioning_GenerateTxnId_Handler,
		},
		{
			MethodName: "GetCardDetailsWithCvv",
			Handler:    _CardProvisioning_GetCardDetailsWithCvv_Handler,
		},
		{
			MethodName: "RenewCard",
			Handler:    _CardProvisioning_RenewCard_Handler,
		},
		{
			MethodName: "RenewCardStatus",
			Handler:    _CardProvisioning_RenewCardStatus_Handler,
		},
		{
			MethodName: "VerifyQRCode",
			Handler:    _CardProvisioning_VerifyQRCode_Handler,
		},
		{
			MethodName: "FetchDeliveryTrackingStatus",
			Handler:    _CardProvisioning_FetchDeliveryTrackingStatus_Handler,
		},
		{
			MethodName: "FetchTransactionableCards",
			Handler:    _CardProvisioning_FetchTransactionableCards_Handler,
		},
		{
			MethodName: "InitiateAdditionalAuthAttempt",
			Handler:    _CardProvisioning_InitiateAdditionalAuthAttempt_Handler,
		},
		{
			MethodName: "GetAdditionalAuthInfo",
			Handler:    _CardProvisioning_GetAdditionalAuthInfo_Handler,
		},
		{
			MethodName: "UpdateAuthInfo",
			Handler:    _CardProvisioning_UpdateAuthInfo_Handler,
		},
		{
			MethodName: "TriggerCardNotifications",
			Handler:    _CardProvisioning_TriggerCardNotifications_Handler,
		},
		{
			MethodName: "UpdateFreeCardReplacement",
			Handler:    _CardProvisioning_UpdateFreeCardReplacement_Handler,
		},
		{
			MethodName: "FetchCardTrackingDetails",
			Handler:    _CardProvisioning_FetchCardTrackingDetails_Handler,
		},
		{
			MethodName: "GetCardShipmentTrackingDetails",
			Handler:    _CardProvisioning_GetCardShipmentTrackingDetails_Handler,
		},
		{
			MethodName: "UploadCardTrackingDetails",
			Handler:    _CardProvisioning_UploadCardTrackingDetails_Handler,
		},
		{
			MethodName: "ProcessManualCardPinSet",
			Handler:    _CardProvisioning_ProcessManualCardPinSet_Handler,
		},
		{
			MethodName: "ForceCardCreationEnquiry",
			Handler:    _CardProvisioning_ForceCardCreationEnquiry_Handler,
		},
		{
			MethodName: "UpdateTrackingDetails",
			Handler:    _CardProvisioning_UpdateTrackingDetails_Handler,
		},
		{
			MethodName: "GetCardActionAttempts",
			Handler:    _CardProvisioning_GetCardActionAttempts_Handler,
		},
		{
			MethodName: "ProcessManualCardUnsuspend",
			Handler:    _CardProvisioning_ProcessManualCardUnsuspend_Handler,
		},
		{
			MethodName: "CreatePhysicalCardDispatchAttempt",
			Handler:    _CardProvisioning_CreatePhysicalCardDispatchAttempt_Handler,
		},
		{
			MethodName: "InitiateShippingAddressUpdateAndDispatchPhysicalCard",
			Handler:    _CardProvisioning_InitiateShippingAddressUpdateAndDispatchPhysicalCard_Handler,
		},
		{
			MethodName: "CheckShippingAddressUpdateAndPhysicalCardDispatchStatus",
			Handler:    _CardProvisioning_CheckShippingAddressUpdateAndPhysicalCardDispatchStatus_Handler,
		},
		{
			MethodName: "GetPhysicalCardDispatchStatus",
			Handler:    _CardProvisioning_GetPhysicalCardDispatchStatus_Handler,
		},
		{
			MethodName: "GetLatestCardForActorIds",
			Handler:    _CardProvisioning_GetLatestCardForActorIds_Handler,
		},
		{
			MethodName: "GetPhysicalCardActivationInfo",
			Handler:    _CardProvisioning_GetPhysicalCardActivationInfo_Handler,
		},
		{
			MethodName: "InitiatePhysicalCardDispatch",
			Handler:    _CardProvisioning_InitiatePhysicalCardDispatch_Handler,
		},
		{
			MethodName: "FetchPhysicalCardChargesForUser",
			Handler:    _CardProvisioning_FetchPhysicalCardChargesForUser_Handler,
		},
		{
			MethodName: "GetLastPhysicalCardIssuedForUser",
			Handler:    _CardProvisioning_GetLastPhysicalCardIssuedForUser_Handler,
		},
		{
			MethodName: "ActivatePhysicalCard",
			Handler:    _CardProvisioning_ActivatePhysicalCard_Handler,
		},
		{
			MethodName: "GetForexRefunds",
			Handler:    _CardProvisioning_GetForexRefunds_Handler,
		},
		{
			MethodName: "GetPaginatedForexRefundsByActorId",
			Handler:    _CardProvisioning_GetPaginatedForexRefundsByActorId_Handler,
		},
		{
			MethodName: "FetchPhysicalCardDispatchRequests",
			Handler:    _CardProvisioning_FetchPhysicalCardDispatchRequests_Handler,
		},
		{
			MethodName: "GetForexRefundsByActorId",
			Handler:    _CardProvisioning_GetForexRefundsByActorId_Handler,
		},
		{
			MethodName: "FetchCardRenewalChargesForUser",
			Handler:    _CardProvisioning_FetchCardRenewalChargesForUser_Handler,
		},
		{
			MethodName: "FetchForexRefundAggregates",
			Handler:    _CardProvisioning_FetchForexRefundAggregates_Handler,
		},
		{
			MethodName: "GetHomeLayoutConfiguration",
			Handler:    _CardProvisioning_GetHomeLayoutConfiguration_Handler,
		},
		{
			MethodName: "GetCardSwitchNotification",
			Handler:    _CardProvisioning_GetCardSwitchNotification_Handler,
		},
		{
			MethodName: "GetDcInternationalWidget",
			Handler:    _CardProvisioning_GetDcInternationalWidget_Handler,
		},
		{
			MethodName: "FetchDynamicElements",
			Handler:    _CardProvisioning_FetchDynamicElements_Handler,
		},
		{
			MethodName: "DynamicElementCallback",
			Handler:    _CardProvisioning_DynamicElementCallback_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/card/provisioning/service.proto",
}
