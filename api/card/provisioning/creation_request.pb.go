//go:generate gen_sql -types=DetailedStatus
// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/provisioning/creation_request.proto

package provisioning

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CardFieldMask is the enum representation of all the card fields.
// Meant to be used as field mask to help with database updates.
type CardCreationRequestFieldMask int32

const (
	CardCreationRequestFieldMask_CARD_CREATION_REQUEST_FIELD_MASK_UNSPECIFIED  CardCreationRequestFieldMask = 0
	CardCreationRequestFieldMask_CARD_CREATION_REQUEST_CARD_ID                 CardCreationRequestFieldMask = 1
	CardCreationRequestFieldMask_CARD_CREATION_REQUEST_STATE                   CardCreationRequestFieldMask = 2
	CardCreationRequestFieldMask_CARD_CREATION_REQUEST_RETRIES                 CardCreationRequestFieldMask = 3
	CardCreationRequestFieldMask_CARD_CREATION_REQUEST_REQUEST_ID              CardCreationRequestFieldMask = 4
	CardCreationRequestFieldMask_CARD_CREATION_REQUEST_FAILURE_RESPONSE_CODE   CardCreationRequestFieldMask = 5
	CardCreationRequestFieldMask_CARD_CREATION_REQUEST_FAILURE_RESPONSE_REASON CardCreationRequestFieldMask = 6
	CardCreationRequestFieldMask_CARD_CREATION_REQUEST_CREATED_AT              CardCreationRequestFieldMask = 7
	CardCreationRequestFieldMask_CARD_CREATION_REQUEST_UPDATED_AT              CardCreationRequestFieldMask = 8
	CardCreationRequestFieldMask_CARD_CREATION_REQUEST_DELETED_AT              CardCreationRequestFieldMask = 9
	CardCreationRequestFieldMask_CARD_CREATION_REQUEST_ID                      CardCreationRequestFieldMask = 10
	CardCreationRequestFieldMask_CARD_CREATION_REQUEST_DETAILED_STATUS         CardCreationRequestFieldMask = 11
)

// Enum value maps for CardCreationRequestFieldMask.
var (
	CardCreationRequestFieldMask_name = map[int32]string{
		0:  "CARD_CREATION_REQUEST_FIELD_MASK_UNSPECIFIED",
		1:  "CARD_CREATION_REQUEST_CARD_ID",
		2:  "CARD_CREATION_REQUEST_STATE",
		3:  "CARD_CREATION_REQUEST_RETRIES",
		4:  "CARD_CREATION_REQUEST_REQUEST_ID",
		5:  "CARD_CREATION_REQUEST_FAILURE_RESPONSE_CODE",
		6:  "CARD_CREATION_REQUEST_FAILURE_RESPONSE_REASON",
		7:  "CARD_CREATION_REQUEST_CREATED_AT",
		8:  "CARD_CREATION_REQUEST_UPDATED_AT",
		9:  "CARD_CREATION_REQUEST_DELETED_AT",
		10: "CARD_CREATION_REQUEST_ID",
		11: "CARD_CREATION_REQUEST_DETAILED_STATUS",
	}
	CardCreationRequestFieldMask_value = map[string]int32{
		"CARD_CREATION_REQUEST_FIELD_MASK_UNSPECIFIED":  0,
		"CARD_CREATION_REQUEST_CARD_ID":                 1,
		"CARD_CREATION_REQUEST_STATE":                   2,
		"CARD_CREATION_REQUEST_RETRIES":                 3,
		"CARD_CREATION_REQUEST_REQUEST_ID":              4,
		"CARD_CREATION_REQUEST_FAILURE_RESPONSE_CODE":   5,
		"CARD_CREATION_REQUEST_FAILURE_RESPONSE_REASON": 6,
		"CARD_CREATION_REQUEST_CREATED_AT":              7,
		"CARD_CREATION_REQUEST_UPDATED_AT":              8,
		"CARD_CREATION_REQUEST_DELETED_AT":              9,
		"CARD_CREATION_REQUEST_ID":                      10,
		"CARD_CREATION_REQUEST_DETAILED_STATUS":         11,
	}
)

func (x CardCreationRequestFieldMask) Enum() *CardCreationRequestFieldMask {
	p := new(CardCreationRequestFieldMask)
	*p = x
	return p
}

func (x CardCreationRequestFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardCreationRequestFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_creation_request_proto_enumTypes[0].Descriptor()
}

func (CardCreationRequestFieldMask) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_creation_request_proto_enumTypes[0]
}

func (x CardCreationRequestFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardCreationRequestFieldMask.Descriptor instead.
func (CardCreationRequestFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_creation_request_proto_rawDescGZIP(), []int{0}
}

type CardCreationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Primary identifier to the request made for card creation.
	// Internally, it is referred as attempt_id
	//
	// Any retry-able error is generally associated with
	// same set of request parameters and doesn't change the attempt id.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Primary identifier to the card database model. Internal to Epifi.
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// The state of the request in the State Machine.
	// A sample state machines:
	// case 1: QUEUED -> INITIATED -> SUCCESS
	// Happy case. Request was successful at the vendor.
	//
	// QUEUED -> MANUAL_INTERVENTION
	// Request was retried multiple times till retry limit was reached.
	// All the retry attempts to initiate the creation request failed.
	//
	// QUEUED -> INITIATED -> FAILED
	// Request was initiated successfully but failed due to an error.
	//
	// QUEUED -> INITIATED -> MANUAL_INTERVENTION
	// Request was initiated successfully. All the tries to fetch the status
	// failed.
	State RequestState `protobuf:"varint,3,opt,name=state,proto3,enum=card.provisioning.RequestState" json:"state,omitempty"`
	// Number of retries already made for the request. If the request
	// is in QUEUED state, an attempt to call the Vendor increments the retries.
	//
	// If the request is in INITIATED state, an attempt to fetch the status also
	// increments the retries.
	Retries uint32 `protobuf:"varint,4,opt,name=retries,proto3" json:"retries,omitempty"`
	// Unique identifier to the request made for card creation for the vendor.
	// Any followup on state changes is done using
	// this id as a reference. Any retry-able error is generally associated with
	// same set of request parameters and doesn't change the request id.
	RequestId string `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// card creation timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// card creation timestamp
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// failure response code sent by vendor
	FailureResponseCode string `protobuf:"bytes,8,opt,name=failure_response_code,json=failureResponseCode,proto3" json:"failure_response_code,omitempty"`
	// failure response reason sent by vendor
	FailureResponseReason string                 `protobuf:"bytes,9,opt,name=failure_response_reason,json=failureResponseReason,proto3" json:"failure_response_reason,omitempty"`
	DeletedAt             *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// will store card creation request metadata such as internal status, raw status etc.
	DetailedStatus *DetailedStatus `protobuf:"bytes,11,opt,name=detailed_status,json=detailedStatus,proto3" json:"detailed_status,omitempty"`
}

func (x *CardCreationRequest) Reset() {
	*x = CardCreationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_creation_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardCreationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardCreationRequest) ProtoMessage() {}

func (x *CardCreationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_creation_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardCreationRequest.ProtoReflect.Descriptor instead.
func (*CardCreationRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_creation_request_proto_rawDescGZIP(), []int{0}
}

func (x *CardCreationRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CardCreationRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CardCreationRequest) GetState() RequestState {
	if x != nil {
		return x.State
	}
	return RequestState_REQUEST_STATE_UNSPECIFIED
}

func (x *CardCreationRequest) GetRetries() uint32 {
	if x != nil {
		return x.Retries
	}
	return 0
}

func (x *CardCreationRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CardCreationRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CardCreationRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CardCreationRequest) GetFailureResponseCode() string {
	if x != nil {
		return x.FailureResponseCode
	}
	return ""
}

func (x *CardCreationRequest) GetFailureResponseReason() string {
	if x != nil {
		return x.FailureResponseReason
	}
	return ""
}

func (x *CardCreationRequest) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *CardCreationRequest) GetDetailedStatus() *DetailedStatus {
	if x != nil {
		return x.DetailedStatus
	}
	return nil
}

type DetailedStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InternalResponseCode        string `protobuf:"bytes,1,opt,name=internal_response_code,json=internalResponseCode,proto3" json:"internal_response_code,omitempty"`
	InternalResponseDescription string `protobuf:"bytes,2,opt,name=internal_response_description,json=internalResponseDescription,proto3" json:"internal_response_description,omitempty"`
	RawResponseCode             string `protobuf:"bytes,3,opt,name=raw_response_code,json=rawResponseCode,proto3" json:"raw_response_code,omitempty"`
	RawResponseDescription      string `protobuf:"bytes,4,opt,name=raw_response_description,json=rawResponseDescription,proto3" json:"raw_response_description,omitempty"`
}

func (x *DetailedStatus) Reset() {
	*x = DetailedStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_creation_request_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailedStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailedStatus) ProtoMessage() {}

func (x *DetailedStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_creation_request_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailedStatus.ProtoReflect.Descriptor instead.
func (*DetailedStatus) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_creation_request_proto_rawDescGZIP(), []int{1}
}

func (x *DetailedStatus) GetInternalResponseCode() string {
	if x != nil {
		return x.InternalResponseCode
	}
	return ""
}

func (x *DetailedStatus) GetInternalResponseDescription() string {
	if x != nil {
		return x.InternalResponseDescription
	}
	return ""
}

func (x *DetailedStatus) GetRawResponseCode() string {
	if x != nil {
		return x.RawResponseCode
	}
	return ""
}

func (x *DetailedStatus) GetRawResponseDescription() string {
	if x != nil {
		return x.RawResponseDescription
	}
	return ""
}

var File_api_card_provisioning_creation_request_proto protoreflect.FileDescriptor

var file_api_card_provisioning_creation_request_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e,
	0x67, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x97, 0x04, 0x0a,
	0x13, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x35, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x4a, 0x0a, 0x0f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xf0, 0x01, 0x0a, 0x0e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x42, 0x0a, 0x1d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x61, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x38, 0x0a, 0x18, 0x72, 0x61, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x16, 0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2a, 0xfc, 0x03, 0x0a, 0x1c, 0x43, 0x61,
	0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12,
	0x1f, 0x0a, 0x1b, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x02,
	0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45,
	0x53, 0x10, 0x03, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x2f, 0x0a, 0x2b, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f,
	0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x05, 0x12, 0x31, 0x0a, 0x2d, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x50,
	0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x24, 0x0a,
	0x20, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41,
	0x54, 0x10, 0x07, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x08, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x09, 0x12,
	0x1c, 0x0a, 0x18, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x0a, 0x12, 0x29, 0x0a,
	0x25, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x0b, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_provisioning_creation_request_proto_rawDescOnce sync.Once
	file_api_card_provisioning_creation_request_proto_rawDescData = file_api_card_provisioning_creation_request_proto_rawDesc
)

func file_api_card_provisioning_creation_request_proto_rawDescGZIP() []byte {
	file_api_card_provisioning_creation_request_proto_rawDescOnce.Do(func() {
		file_api_card_provisioning_creation_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_provisioning_creation_request_proto_rawDescData)
	})
	return file_api_card_provisioning_creation_request_proto_rawDescData
}

var file_api_card_provisioning_creation_request_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_card_provisioning_creation_request_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_card_provisioning_creation_request_proto_goTypes = []interface{}{
	(CardCreationRequestFieldMask)(0), // 0: card.provisioning.CardCreationRequestFieldMask
	(*CardCreationRequest)(nil),       // 1: card.provisioning.CardCreationRequest
	(*DetailedStatus)(nil),            // 2: card.provisioning.DetailedStatus
	(RequestState)(0),                 // 3: card.provisioning.RequestState
	(*timestamppb.Timestamp)(nil),     // 4: google.protobuf.Timestamp
}
var file_api_card_provisioning_creation_request_proto_depIdxs = []int32{
	3, // 0: card.provisioning.CardCreationRequest.state:type_name -> card.provisioning.RequestState
	4, // 1: card.provisioning.CardCreationRequest.created_at:type_name -> google.protobuf.Timestamp
	4, // 2: card.provisioning.CardCreationRequest.updated_at:type_name -> google.protobuf.Timestamp
	4, // 3: card.provisioning.CardCreationRequest.deleted_at:type_name -> google.protobuf.Timestamp
	2, // 4: card.provisioning.CardCreationRequest.detailed_status:type_name -> card.provisioning.DetailedStatus
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_card_provisioning_creation_request_proto_init() }
func file_api_card_provisioning_creation_request_proto_init() {
	if File_api_card_provisioning_creation_request_proto != nil {
		return
	}
	file_api_card_provisioning_provisioning_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_card_provisioning_creation_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardCreationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_creation_request_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailedStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_provisioning_creation_request_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_provisioning_creation_request_proto_goTypes,
		DependencyIndexes: file_api_card_provisioning_creation_request_proto_depIdxs,
		EnumInfos:         file_api_card_provisioning_creation_request_proto_enumTypes,
		MessageInfos:      file_api_card_provisioning_creation_request_proto_msgTypes,
	}.Build()
	File_api_card_provisioning_creation_request_proto = out.File
	file_api_card_provisioning_creation_request_proto_rawDesc = nil
	file_api_card_provisioning_creation_request_proto_goTypes = nil
	file_api_card_provisioning_creation_request_proto_depIdxs = nil
}
