// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/provisioning/activation_request.proto

package provisioning

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CardActivationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Primary identifier to the request made for card activation.
	// Internally, it is referred as attempt_id and for the vendor
	// it may mean a request-id.
	//
	// This is passed along to the vendor. Any followup on state changes is done using
	// this id as a reference. Any retry-able error is generally associated with
	// same set of request parameters and doesn't change the attempt/request id.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Primary identifier to the card database model. Internal to Epifi.
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// The state of the request in the State Machine.
	// A sample state machines:
	// case 1: QUEUED -> INITIATED -> SUCCESS
	// Happy case. Request was successful at the vendor.
	//
	// QUEUED -> MANUAL_INTERVENTION
	// Request was retried multiple times till retry limit was reached.
	// All the retry attempts to initiate the activation request failed.
	//
	// QUEUED -> INITIATED -> FAILED
	// Request was initiated successfully but failed due to an error.
	//
	// QUEUED -> INITIATED -> MANUAL_INTERVENTION
	// Request was initiated successfully. All the tries to fetch the status
	// failed.
	State RequestState `protobuf:"varint,3,opt,name=state,proto3,enum=card.provisioning.RequestState" json:"state,omitempty"`
	// Number of retries already made for the request. If the request
	// is in QUEUED state, an attempt to call the Vendor increments the retries.
	//
	// If the request is in INITIATED state, an attempt to fetch the status also
	// increments the retries.
	Retries uint32 `protobuf:"varint,4,opt,name=retries,proto3" json:"retries,omitempty"`
}

func (x *CardActivationRequest) Reset() {
	*x = CardActivationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_activation_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardActivationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardActivationRequest) ProtoMessage() {}

func (x *CardActivationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_activation_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardActivationRequest.ProtoReflect.Descriptor instead.
func (*CardActivationRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_activation_request_proto_rawDescGZIP(), []int{0}
}

func (x *CardActivationRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CardActivationRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CardActivationRequest) GetState() RequestState {
	if x != nil {
		return x.State
	}
	return RequestState_REQUEST_STATE_UNSPECIFIED
}

func (x *CardActivationRequest) GetRetries() uint32 {
	if x != nil {
		return x.Retries
	}
	return 0
}

var File_api_card_provisioning_activation_request_proto protoreflect.FileDescriptor

var file_api_card_provisioning_activation_request_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x11, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x69, 0x6e, 0x67, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91, 0x01,
	0x0a, 0x15, 0x43, 0x61, 0x72, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x12, 0x35, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x73, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e,
	0x67, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61,
	0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_provisioning_activation_request_proto_rawDescOnce sync.Once
	file_api_card_provisioning_activation_request_proto_rawDescData = file_api_card_provisioning_activation_request_proto_rawDesc
)

func file_api_card_provisioning_activation_request_proto_rawDescGZIP() []byte {
	file_api_card_provisioning_activation_request_proto_rawDescOnce.Do(func() {
		file_api_card_provisioning_activation_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_provisioning_activation_request_proto_rawDescData)
	})
	return file_api_card_provisioning_activation_request_proto_rawDescData
}

var file_api_card_provisioning_activation_request_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_card_provisioning_activation_request_proto_goTypes = []interface{}{
	(*CardActivationRequest)(nil), // 0: card.provisioning.CardActivationRequest
	(RequestState)(0),             // 1: card.provisioning.RequestState
}
var file_api_card_provisioning_activation_request_proto_depIdxs = []int32{
	1, // 0: card.provisioning.CardActivationRequest.state:type_name -> card.provisioning.RequestState
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_card_provisioning_activation_request_proto_init() }
func file_api_card_provisioning_activation_request_proto_init() {
	if File_api_card_provisioning_activation_request_proto != nil {
		return
	}
	file_api_card_provisioning_provisioning_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_card_provisioning_activation_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardActivationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_provisioning_activation_request_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_provisioning_activation_request_proto_goTypes,
		DependencyIndexes: file_api_card_provisioning_activation_request_proto_depIdxs,
		MessageInfos:      file_api_card_provisioning_activation_request_proto_msgTypes,
	}.Build()
	File_api_card_provisioning_activation_request_proto = out.File
	file_api_card_provisioning_activation_request_proto_rawDesc = nil
	file_api_card_provisioning_activation_request_proto_goTypes = nil
	file_api_card_provisioning_activation_request_proto_depIdxs = nil
}
