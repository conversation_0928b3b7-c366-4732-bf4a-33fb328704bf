// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/provisioning/card_consumer.proto

package provisioning

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	card "github.com/epifi/gamma/api/card"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = card.Provenance(0)
)

// Validate checks the field values on
// ProcessUserDevicePropertiesUpdateEventResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessUserDevicePropertiesUpdateEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessUserDevicePropertiesUpdateEventResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ProcessUserDevicePropertiesUpdateEventResponseMultiError, or nil if none found.
func (m *ProcessUserDevicePropertiesUpdateEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessUserDevicePropertiesUpdateEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessUserDevicePropertiesUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessUserDevicePropertiesUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessUserDevicePropertiesUpdateEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessUserDevicePropertiesUpdateEventResponseMultiError(errors)
	}

	return nil
}

// ProcessUserDevicePropertiesUpdateEventResponseMultiError is an error
// wrapping multiple validation errors returned by
// ProcessUserDevicePropertiesUpdateEventResponse.ValidateAll() if the
// designated constraints aren't met.
type ProcessUserDevicePropertiesUpdateEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessUserDevicePropertiesUpdateEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessUserDevicePropertiesUpdateEventResponseMultiError) AllErrors() []error { return m }

// ProcessUserDevicePropertiesUpdateEventResponseValidationError is the
// validation error returned by
// ProcessUserDevicePropertiesUpdateEventResponse.Validate if the designated
// constraints aren't met.
type ProcessUserDevicePropertiesUpdateEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessUserDevicePropertiesUpdateEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessUserDevicePropertiesUpdateEventResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ProcessUserDevicePropertiesUpdateEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessUserDevicePropertiesUpdateEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessUserDevicePropertiesUpdateEventResponseValidationError) ErrorName() string {
	return "ProcessUserDevicePropertiesUpdateEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessUserDevicePropertiesUpdateEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessUserDevicePropertiesUpdateEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessUserDevicePropertiesUpdateEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessUserDevicePropertiesUpdateEventResponseValidationError{}

// Validate checks the field values on ProcessCardsDispatchedCsvFileRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessCardsDispatchedCsvFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardsDispatchedCsvFileRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCardsDispatchedCsvFileRequestMultiError, or nil if none found.
func (m *ProcessCardsDispatchedCsvFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardsDispatchedCsvFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardsDispatchedCsvFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardsDispatchedCsvFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardsDispatchedCsvFileRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessCardsDispatchedCsvFileRequestValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessCardsDispatchedCsvFileRequestValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessCardsDispatchedCsvFileRequestValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcessCardsDispatchedCsvFileRequestMultiError(errors)
	}

	return nil
}

// ProcessCardsDispatchedCsvFileRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCardsDispatchedCsvFileRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardsDispatchedCsvFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardsDispatchedCsvFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardsDispatchedCsvFileRequestMultiError) AllErrors() []error { return m }

// ProcessCardsDispatchedCsvFileRequestValidationError is the validation error
// returned by ProcessCardsDispatchedCsvFileRequest.Validate if the designated
// constraints aren't met.
type ProcessCardsDispatchedCsvFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardsDispatchedCsvFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardsDispatchedCsvFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardsDispatchedCsvFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardsDispatchedCsvFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardsDispatchedCsvFileRequestValidationError) ErrorName() string {
	return "ProcessCardsDispatchedCsvFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardsDispatchedCsvFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardsDispatchedCsvFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardsDispatchedCsvFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardsDispatchedCsvFileRequestValidationError{}

// Validate checks the field values on ProcessCardsDispatchedCsvFileResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessCardsDispatchedCsvFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardsDispatchedCsvFileResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCardsDispatchedCsvFileResponseMultiError, or nil if none found.
func (m *ProcessCardsDispatchedCsvFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardsDispatchedCsvFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardsDispatchedCsvFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardsDispatchedCsvFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardsDispatchedCsvFileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardsDispatchedCsvFileResponseMultiError(errors)
	}

	return nil
}

// ProcessCardsDispatchedCsvFileResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessCardsDispatchedCsvFileResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardsDispatchedCsvFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardsDispatchedCsvFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardsDispatchedCsvFileResponseMultiError) AllErrors() []error { return m }

// ProcessCardsDispatchedCsvFileResponseValidationError is the validation error
// returned by ProcessCardsDispatchedCsvFileResponse.Validate if the
// designated constraints aren't met.
type ProcessCardsDispatchedCsvFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardsDispatchedCsvFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardsDispatchedCsvFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardsDispatchedCsvFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardsDispatchedCsvFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardsDispatchedCsvFileResponseValidationError) ErrorName() string {
	return "ProcessCardsDispatchedCsvFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardsDispatchedCsvFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardsDispatchedCsvFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardsDispatchedCsvFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardsDispatchedCsvFileResponseValidationError{}

// Validate checks the field values on ProcessCardDispatchRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessCardDispatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardDispatchRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessCardDispatchRequestMultiError, or nil if none found.
func (m *ProcessCardDispatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardDispatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardDispatchRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardDispatchRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardDispatchRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if len(errors) > 0 {
		return ProcessCardDispatchRequestMultiError(errors)
	}

	return nil
}

// ProcessCardDispatchRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessCardDispatchRequest.ValidateAll() if
// the designated constraints aren't met.
type ProcessCardDispatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardDispatchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardDispatchRequestMultiError) AllErrors() []error { return m }

// ProcessCardDispatchRequestValidationError is the validation error returned
// by ProcessCardDispatchRequest.Validate if the designated constraints aren't met.
type ProcessCardDispatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardDispatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardDispatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardDispatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardDispatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardDispatchRequestValidationError) ErrorName() string {
	return "ProcessCardDispatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardDispatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardDispatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardDispatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardDispatchRequestValidationError{}

// Validate checks the field values on ProcessCardDispatchResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessCardDispatchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardDispatchResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessCardDispatchResponseMultiError, or nil if none found.
func (m *ProcessCardDispatchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardDispatchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardDispatchResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardDispatchResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardDispatchResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardDispatchResponseMultiError(errors)
	}

	return nil
}

// ProcessCardDispatchResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessCardDispatchResponse.ValidateAll() if
// the designated constraints aren't met.
type ProcessCardDispatchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardDispatchResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardDispatchResponseMultiError) AllErrors() []error { return m }

// ProcessCardDispatchResponseValidationError is the validation error returned
// by ProcessCardDispatchResponse.Validate if the designated constraints
// aren't met.
type ProcessCardDispatchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardDispatchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardDispatchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardDispatchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardDispatchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardDispatchResponseValidationError) ErrorName() string {
	return "ProcessCardDispatchResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardDispatchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardDispatchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardDispatchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardDispatchResponseValidationError{}

// Validate checks the field values on
// ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError, or
// nil if none found.
func (m *ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	if len(errors) > 0 {
		return ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError(errors)
	}

	return nil
}

// ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError is an
// error wrapping multiple validation errors returned by
// ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest.ValidateAll() if
// the designated constraints aren't met.
type ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError) AllErrors() []error {
	return m
}

// ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError is
// the validation error returned by
// ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest.Validate if the
// designated constraints aren't met.
type ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) ErrorName() string {
	return "ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessShippingAddressUpdateAndDispatchPhysicalCardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError{}

// Validate checks the field values on
// ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError, or
// nil if none found.
func (m *ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError(errors)
	}

	return nil
}

// ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError is an
// error wrapping multiple validation errors returned by
// ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError) AllErrors() []error {
	return m
}

// ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError
// is the validation error returned by
// ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse.Validate if the
// designated constraints aren't met.
type ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) ErrorName() string {
	return "ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessShippingAddressUpdateAndDispatchPhysicalCardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{}

// Validate checks the field values on ProcessCardShipmentRegisterEventRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessCardShipmentRegisterEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessCardShipmentRegisterEventRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ProcessCardShipmentRegisterEventRequestMultiError, or nil if none found.
func (m *ProcessCardShipmentRegisterEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardShipmentRegisterEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardShipmentRegisterEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardShipmentRegisterEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardShipmentRegisterEventRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardShipmentRegisterEventRequestMultiError(errors)
	}

	return nil
}

// ProcessCardShipmentRegisterEventRequestMultiError is an error wrapping
// multiple validation errors returned by
// ProcessCardShipmentRegisterEventRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardShipmentRegisterEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardShipmentRegisterEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardShipmentRegisterEventRequestMultiError) AllErrors() []error { return m }

// ProcessCardShipmentRegisterEventRequestValidationError is the validation
// error returned by ProcessCardShipmentRegisterEventRequest.Validate if the
// designated constraints aren't met.
type ProcessCardShipmentRegisterEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardShipmentRegisterEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardShipmentRegisterEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardShipmentRegisterEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardShipmentRegisterEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardShipmentRegisterEventRequestValidationError) ErrorName() string {
	return "ProcessCardShipmentRegisterEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardShipmentRegisterEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardShipmentRegisterEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardShipmentRegisterEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardShipmentRegisterEventRequestValidationError{}

// Validate checks the field values on ProcessCardShipmentRegisterEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessCardShipmentRegisterEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessCardShipmentRegisterEventResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessCardShipmentRegisterEventResponseMultiError, or nil if none found.
func (m *ProcessCardShipmentRegisterEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardShipmentRegisterEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardShipmentRegisterEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardShipmentRegisterEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardShipmentRegisterEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardShipmentRegisterEventResponseMultiError(errors)
	}

	return nil
}

// ProcessCardShipmentRegisterEventResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessCardShipmentRegisterEventResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardShipmentRegisterEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardShipmentRegisterEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardShipmentRegisterEventResponseMultiError) AllErrors() []error { return m }

// ProcessCardShipmentRegisterEventResponseValidationError is the validation
// error returned by ProcessCardShipmentRegisterEventResponse.Validate if the
// designated constraints aren't met.
type ProcessCardShipmentRegisterEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardShipmentRegisterEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardShipmentRegisterEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardShipmentRegisterEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardShipmentRegisterEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardShipmentRegisterEventResponseValidationError) ErrorName() string {
	return "ProcessCardShipmentRegisterEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardShipmentRegisterEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardShipmentRegisterEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardShipmentRegisterEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardShipmentRegisterEventResponseValidationError{}

// Validate checks the field values on ProcessCardDeliveryDelayEventRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessCardDeliveryDelayEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardDeliveryDelayEventRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCardDeliveryDelayEventRequestMultiError, or nil if none found.
func (m *ProcessCardDeliveryDelayEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardDeliveryDelayEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardDeliveryDelayEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardDeliveryDelayEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardDeliveryDelayEventRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	if len(errors) > 0 {
		return ProcessCardDeliveryDelayEventRequestMultiError(errors)
	}

	return nil
}

// ProcessCardDeliveryDelayEventRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCardDeliveryDelayEventRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardDeliveryDelayEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardDeliveryDelayEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardDeliveryDelayEventRequestMultiError) AllErrors() []error { return m }

// ProcessCardDeliveryDelayEventRequestValidationError is the validation error
// returned by ProcessCardDeliveryDelayEventRequest.Validate if the designated
// constraints aren't met.
type ProcessCardDeliveryDelayEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardDeliveryDelayEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardDeliveryDelayEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardDeliveryDelayEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardDeliveryDelayEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardDeliveryDelayEventRequestValidationError) ErrorName() string {
	return "ProcessCardDeliveryDelayEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardDeliveryDelayEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardDeliveryDelayEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardDeliveryDelayEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardDeliveryDelayEventRequestValidationError{}

// Validate checks the field values on ProcessCardDeliveryDelayEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessCardDeliveryDelayEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardDeliveryDelayEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCardDeliveryDelayEventResponseMultiError, or nil if none found.
func (m *ProcessCardDeliveryDelayEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardDeliveryDelayEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardDeliveryDelayEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardDeliveryDelayEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardDeliveryDelayEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardDeliveryDelayEventResponseMultiError(errors)
	}

	return nil
}

// ProcessCardDeliveryDelayEventResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessCardDeliveryDelayEventResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardDeliveryDelayEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardDeliveryDelayEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardDeliveryDelayEventResponseMultiError) AllErrors() []error { return m }

// ProcessCardDeliveryDelayEventResponseValidationError is the validation error
// returned by ProcessCardDeliveryDelayEventResponse.Validate if the
// designated constraints aren't met.
type ProcessCardDeliveryDelayEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardDeliveryDelayEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardDeliveryDelayEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardDeliveryDelayEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardDeliveryDelayEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardDeliveryDelayEventResponseValidationError) ErrorName() string {
	return "ProcessCardDeliveryDelayEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardDeliveryDelayEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardDeliveryDelayEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardDeliveryDelayEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardDeliveryDelayEventResponseValidationError{}

// Validate checks the field values on ProcessCardRenewalEventRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessCardRenewalEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardRenewalEventRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessCardRenewalEventRequestMultiError, or nil if none found.
func (m *ProcessCardRenewalEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardRenewalEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardRenewalEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardRenewalEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardRenewalEventRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	// no validation rules for BlockCardReason

	// no validation rules for BlockCardProvenance

	if len(errors) > 0 {
		return ProcessCardRenewalEventRequestMultiError(errors)
	}

	return nil
}

// ProcessCardRenewalEventRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessCardRenewalEventRequest.ValidateAll()
// if the designated constraints aren't met.
type ProcessCardRenewalEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardRenewalEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardRenewalEventRequestMultiError) AllErrors() []error { return m }

// ProcessCardRenewalEventRequestValidationError is the validation error
// returned by ProcessCardRenewalEventRequest.Validate if the designated
// constraints aren't met.
type ProcessCardRenewalEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardRenewalEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardRenewalEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardRenewalEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardRenewalEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardRenewalEventRequestValidationError) ErrorName() string {
	return "ProcessCardRenewalEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardRenewalEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardRenewalEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardRenewalEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardRenewalEventRequestValidationError{}

// Validate checks the field values on ProcessCardRenewalEventResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessCardRenewalEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardRenewalEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessCardRenewalEventResponseMultiError, or nil if none found.
func (m *ProcessCardRenewalEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardRenewalEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardRenewalEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardRenewalEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardRenewalEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardRenewalEventResponseMultiError(errors)
	}

	return nil
}

// ProcessCardRenewalEventResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessCardRenewalEventResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessCardRenewalEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardRenewalEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardRenewalEventResponseMultiError) AllErrors() []error { return m }

// ProcessCardRenewalEventResponseValidationError is the validation error
// returned by ProcessCardRenewalEventResponse.Validate if the designated
// constraints aren't met.
type ProcessCardRenewalEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardRenewalEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardRenewalEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardRenewalEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardRenewalEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardRenewalEventResponseValidationError) ErrorName() string {
	return "ProcessCardRenewalEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardRenewalEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardRenewalEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardRenewalEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardRenewalEventResponseValidationError{}

// Validate checks the field values on ProcessCardCreationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessCardCreationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardCreationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessCardCreationRequestMultiError, or nil if none found.
func (m *ProcessCardCreationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardCreationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardCreationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardCreationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardCreationRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	if len(errors) > 0 {
		return ProcessCardCreationRequestMultiError(errors)
	}

	return nil
}

// ProcessCardCreationRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessCardCreationRequest.ValidateAll() if
// the designated constraints aren't met.
type ProcessCardCreationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardCreationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardCreationRequestMultiError) AllErrors() []error { return m }

// ProcessCardCreationRequestValidationError is the validation error returned
// by ProcessCardCreationRequest.Validate if the designated constraints aren't met.
type ProcessCardCreationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardCreationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardCreationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardCreationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardCreationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardCreationRequestValidationError) ErrorName() string {
	return "ProcessCardCreationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardCreationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardCreationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardCreationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardCreationRequestValidationError{}

// Validate checks the field values on ProcessCardCreationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessCardCreationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardCreationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessCardCreationResponseMultiError, or nil if none found.
func (m *ProcessCardCreationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardCreationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardCreationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardCreationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardCreationResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardCreationResponseMultiError(errors)
	}

	return nil
}

// ProcessCardCreationResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessCardCreationResponse.ValidateAll() if
// the designated constraints aren't met.
type ProcessCardCreationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardCreationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardCreationResponseMultiError) AllErrors() []error { return m }

// ProcessCardCreationResponseValidationError is the validation error returned
// by ProcessCardCreationResponse.Validate if the designated constraints
// aren't met.
type ProcessCardCreationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardCreationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardCreationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardCreationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardCreationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardCreationResponseValidationError) ErrorName() string {
	return "ProcessCardCreationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardCreationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardCreationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardCreationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardCreationResponseValidationError{}

// Validate checks the field values on ProcessCardPiCreationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessCardPiCreationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardPiCreationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessCardPiCreationRequestMultiError, or nil if none found.
func (m *ProcessCardPiCreationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardPiCreationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardPiCreationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardPiCreationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardPiCreationRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardPiCreationRequestValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardPiCreationRequestValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardPiCreationRequestValidationError{
				field:  "Card",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardPiCreationRequestMultiError(errors)
	}

	return nil
}

// ProcessCardPiCreationRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessCardPiCreationRequest.ValidateAll() if
// the designated constraints aren't met.
type ProcessCardPiCreationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardPiCreationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardPiCreationRequestMultiError) AllErrors() []error { return m }

// ProcessCardPiCreationRequestValidationError is the validation error returned
// by ProcessCardPiCreationRequest.Validate if the designated constraints
// aren't met.
type ProcessCardPiCreationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardPiCreationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardPiCreationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardPiCreationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardPiCreationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardPiCreationRequestValidationError) ErrorName() string {
	return "ProcessCardPiCreationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardPiCreationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardPiCreationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardPiCreationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardPiCreationRequestValidationError{}

// Validate checks the field values on ProcessCardPiCreationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessCardPiCreationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardPiCreationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessCardPiCreationResponseMultiError, or nil if none found.
func (m *ProcessCardPiCreationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardPiCreationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardPiCreationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardPiCreationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardPiCreationResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardPiCreationResponseMultiError(errors)
	}

	return nil
}

// ProcessCardPiCreationResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessCardPiCreationResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessCardPiCreationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardPiCreationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardPiCreationResponseMultiError) AllErrors() []error { return m }

// ProcessCardPiCreationResponseValidationError is the validation error
// returned by ProcessCardPiCreationResponse.Validate if the designated
// constraints aren't met.
type ProcessCardPiCreationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardPiCreationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardPiCreationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardPiCreationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardPiCreationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardPiCreationResponseValidationError) ErrorName() string {
	return "ProcessCardPiCreationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardPiCreationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardPiCreationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardPiCreationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardPiCreationResponseValidationError{}

// Validate checks the field values on ProcessCardPinSetEventRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessCardPinSetEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardPinSetEventRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessCardPinSetEventRequestMultiError, or nil if none found.
func (m *ProcessCardPinSetEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardPinSetEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardPinSetEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardPinSetEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardPinSetEventRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardPinSetEventRequestValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardPinSetEventRequestValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardPinSetEventRequestValidationError{
				field:  "Card",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardPinSetEventRequestMultiError(errors)
	}

	return nil
}

// ProcessCardPinSetEventRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessCardPinSetEventRequest.ValidateAll()
// if the designated constraints aren't met.
type ProcessCardPinSetEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardPinSetEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardPinSetEventRequestMultiError) AllErrors() []error { return m }

// ProcessCardPinSetEventRequestValidationError is the validation error
// returned by ProcessCardPinSetEventRequest.Validate if the designated
// constraints aren't met.
type ProcessCardPinSetEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardPinSetEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardPinSetEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardPinSetEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardPinSetEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardPinSetEventRequestValidationError) ErrorName() string {
	return "ProcessCardPinSetEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardPinSetEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardPinSetEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardPinSetEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardPinSetEventRequestValidationError{}

// Validate checks the field values on ProcessCardPinSetEventResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessCardPinSetEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardPinSetEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessCardPinSetEventResponseMultiError, or nil if none found.
func (m *ProcessCardPinSetEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardPinSetEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardPinSetEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardPinSetEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardPinSetEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardPinSetEventResponseMultiError(errors)
	}

	return nil
}

// ProcessCardPinSetEventResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessCardPinSetEventResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessCardPinSetEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardPinSetEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardPinSetEventResponseMultiError) AllErrors() []error { return m }

// ProcessCardPinSetEventResponseValidationError is the validation error
// returned by ProcessCardPinSetEventResponse.Validate if the designated
// constraints aren't met.
type ProcessCardPinSetEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardPinSetEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardPinSetEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardPinSetEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardPinSetEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardPinSetEventResponseValidationError) ErrorName() string {
	return "ProcessCardPinSetEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardPinSetEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardPinSetEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardPinSetEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardPinSetEventResponseValidationError{}

// Validate checks the field values on ProcessCardOnboardingEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessCardOnboardingEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardOnboardingEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCardOnboardingEventResponseMultiError, or nil if none found.
func (m *ProcessCardOnboardingEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardOnboardingEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardOnboardingEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardOnboardingEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardOnboardingEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardOnboardingEventResponseMultiError(errors)
	}

	return nil
}

// ProcessCardOnboardingEventResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCardOnboardingEventResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardOnboardingEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardOnboardingEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardOnboardingEventResponseMultiError) AllErrors() []error { return m }

// ProcessCardOnboardingEventResponseValidationError is the validation error
// returned by ProcessCardOnboardingEventResponse.Validate if the designated
// constraints aren't met.
type ProcessCardOnboardingEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardOnboardingEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardOnboardingEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardOnboardingEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardOnboardingEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardOnboardingEventResponseValidationError) ErrorName() string {
	return "ProcessCardOnboardingEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardOnboardingEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardOnboardingEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardOnboardingEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardOnboardingEventResponseValidationError{}

// Validate checks the field values on ProcessAuthFactorUpdateEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessAuthFactorUpdateEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessAuthFactorUpdateEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessAuthFactorUpdateEventResponseMultiError, or nil if none found.
func (m *ProcessAuthFactorUpdateEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessAuthFactorUpdateEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessAuthFactorUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessAuthFactorUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessAuthFactorUpdateEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessAuthFactorUpdateEventResponseMultiError(errors)
	}

	return nil
}

// ProcessAuthFactorUpdateEventResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessAuthFactorUpdateEventResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessAuthFactorUpdateEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessAuthFactorUpdateEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessAuthFactorUpdateEventResponseMultiError) AllErrors() []error { return m }

// ProcessAuthFactorUpdateEventResponseValidationError is the validation error
// returned by ProcessAuthFactorUpdateEventResponse.Validate if the designated
// constraints aren't met.
type ProcessAuthFactorUpdateEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessAuthFactorUpdateEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessAuthFactorUpdateEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessAuthFactorUpdateEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessAuthFactorUpdateEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessAuthFactorUpdateEventResponseValidationError) ErrorName() string {
	return "ProcessAuthFactorUpdateEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessAuthFactorUpdateEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessAuthFactorUpdateEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessAuthFactorUpdateEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessAuthFactorUpdateEventResponseValidationError{}

// Validate checks the field values on ProcessCardDeliveredEventRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessCardDeliveredEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardDeliveredEventRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessCardDeliveredEventRequestMultiError, or nil if none found.
func (m *ProcessCardDeliveredEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardDeliveredEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardDeliveredEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardDeliveredEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardDeliveredEventRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	if len(errors) > 0 {
		return ProcessCardDeliveredEventRequestMultiError(errors)
	}

	return nil
}

// ProcessCardDeliveredEventRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCardDeliveredEventRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardDeliveredEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardDeliveredEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardDeliveredEventRequestMultiError) AllErrors() []error { return m }

// ProcessCardDeliveredEventRequestValidationError is the validation error
// returned by ProcessCardDeliveredEventRequest.Validate if the designated
// constraints aren't met.
type ProcessCardDeliveredEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardDeliveredEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardDeliveredEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardDeliveredEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardDeliveredEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardDeliveredEventRequestValidationError) ErrorName() string {
	return "ProcessCardDeliveredEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardDeliveredEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardDeliveredEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardDeliveredEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardDeliveredEventRequestValidationError{}

// Validate checks the field values on ProcessCardDeliveredEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessCardDeliveredEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardDeliveredEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCardDeliveredEventResponseMultiError, or nil if none found.
func (m *ProcessCardDeliveredEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardDeliveredEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardDeliveredEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardDeliveredEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardDeliveredEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardDeliveredEventResponseMultiError(errors)
	}

	return nil
}

// ProcessCardDeliveredEventResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCardDeliveredEventResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardDeliveredEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardDeliveredEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardDeliveredEventResponseMultiError) AllErrors() []error { return m }

// ProcessCardDeliveredEventResponseValidationError is the validation error
// returned by ProcessCardDeliveredEventResponse.Validate if the designated
// constraints aren't met.
type ProcessCardDeliveredEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardDeliveredEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardDeliveredEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardDeliveredEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardDeliveredEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardDeliveredEventResponseValidationError) ErrorName() string {
	return "ProcessCardDeliveredEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardDeliveredEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardDeliveredEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardDeliveredEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardDeliveredEventResponseValidationError{}

// Validate checks the field values on GetTrackingDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTrackingDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTrackingDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTrackingDetailsRequestMultiError, or nil if none found.
func (m *GetTrackingDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTrackingDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTrackingDetailsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTrackingDetailsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTrackingDetailsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	if len(errors) > 0 {
		return GetTrackingDetailsRequestMultiError(errors)
	}

	return nil
}

// GetTrackingDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetTrackingDetailsRequest.ValidateAll() if the
// designated constraints aren't met.
type GetTrackingDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTrackingDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTrackingDetailsRequestMultiError) AllErrors() []error { return m }

// GetTrackingDetailsRequestValidationError is the validation error returned by
// GetTrackingDetailsRequest.Validate if the designated constraints aren't met.
type GetTrackingDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTrackingDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTrackingDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTrackingDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTrackingDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTrackingDetailsRequestValidationError) ErrorName() string {
	return "GetTrackingDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTrackingDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTrackingDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTrackingDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTrackingDetailsRequestValidationError{}

// Validate checks the field values on GetTrackingDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTrackingDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTrackingDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTrackingDetailsResponseMultiError, or nil if none found.
func (m *GetTrackingDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTrackingDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTrackingDetailsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTrackingDetailsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTrackingDetailsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTrackingDetailsResponseMultiError(errors)
	}

	return nil
}

// GetTrackingDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by GetTrackingDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetTrackingDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTrackingDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTrackingDetailsResponseMultiError) AllErrors() []error { return m }

// GetTrackingDetailsResponseValidationError is the validation error returned
// by GetTrackingDetailsResponse.Validate if the designated constraints aren't met.
type GetTrackingDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTrackingDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTrackingDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTrackingDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTrackingDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTrackingDetailsResponseValidationError) ErrorName() string {
	return "GetTrackingDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTrackingDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTrackingDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTrackingDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTrackingDetailsResponseValidationError{}

// Validate checks the field values on
// ProcessCardAmcChargesEligibleUserFileRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessCardAmcChargesEligibleUserFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessCardAmcChargesEligibleUserFileRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessCardAmcChargesEligibleUserFileRequestMultiError, or nil if none found.
func (m *ProcessCardAmcChargesEligibleUserFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardAmcChargesEligibleUserFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardAmcChargesEligibleUserFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardAmcChargesEligibleUserFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardAmcChargesEligibleUserFileRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessCardAmcChargesEligibleUserFileRequestValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessCardAmcChargesEligibleUserFileRequestValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessCardAmcChargesEligibleUserFileRequestValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcessCardAmcChargesEligibleUserFileRequestMultiError(errors)
	}

	return nil
}

// ProcessCardAmcChargesEligibleUserFileRequestMultiError is an error wrapping
// multiple validation errors returned by
// ProcessCardAmcChargesEligibleUserFileRequest.ValidateAll() if the
// designated constraints aren't met.
type ProcessCardAmcChargesEligibleUserFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardAmcChargesEligibleUserFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardAmcChargesEligibleUserFileRequestMultiError) AllErrors() []error { return m }

// ProcessCardAmcChargesEligibleUserFileRequestValidationError is the
// validation error returned by
// ProcessCardAmcChargesEligibleUserFileRequest.Validate if the designated
// constraints aren't met.
type ProcessCardAmcChargesEligibleUserFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardAmcChargesEligibleUserFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardAmcChargesEligibleUserFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardAmcChargesEligibleUserFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardAmcChargesEligibleUserFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardAmcChargesEligibleUserFileRequestValidationError) ErrorName() string {
	return "ProcessCardAmcChargesEligibleUserFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardAmcChargesEligibleUserFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardAmcChargesEligibleUserFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardAmcChargesEligibleUserFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardAmcChargesEligibleUserFileRequestValidationError{}

// Validate checks the field values on
// ProcessCardAmcChargesEligibleUserFileResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessCardAmcChargesEligibleUserFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessCardAmcChargesEligibleUserFileResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessCardAmcChargesEligibleUserFileResponseMultiError, or nil if none found.
func (m *ProcessCardAmcChargesEligibleUserFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardAmcChargesEligibleUserFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardAmcChargesEligibleUserFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardAmcChargesEligibleUserFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardAmcChargesEligibleUserFileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardAmcChargesEligibleUserFileResponseMultiError(errors)
	}

	return nil
}

// ProcessCardAmcChargesEligibleUserFileResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessCardAmcChargesEligibleUserFileResponse.ValidateAll() if the
// designated constraints aren't met.
type ProcessCardAmcChargesEligibleUserFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardAmcChargesEligibleUserFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardAmcChargesEligibleUserFileResponseMultiError) AllErrors() []error { return m }

// ProcessCardAmcChargesEligibleUserFileResponseValidationError is the
// validation error returned by
// ProcessCardAmcChargesEligibleUserFileResponse.Validate if the designated
// constraints aren't met.
type ProcessCardAmcChargesEligibleUserFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardAmcChargesEligibleUserFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardAmcChargesEligibleUserFileResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ProcessCardAmcChargesEligibleUserFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardAmcChargesEligibleUserFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardAmcChargesEligibleUserFileResponseValidationError) ErrorName() string {
	return "ProcessCardAmcChargesEligibleUserFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardAmcChargesEligibleUserFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardAmcChargesEligibleUserFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardAmcChargesEligibleUserFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardAmcChargesEligibleUserFileResponseValidationError{}

// Validate checks the field values on
// OrderPhysicalCardCriticalNotificationRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OrderPhysicalCardCriticalNotificationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// OrderPhysicalCardCriticalNotificationRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// OrderPhysicalCardCriticalNotificationRequestMultiError, or nil if none found.
func (m *OrderPhysicalCardCriticalNotificationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *OrderPhysicalCardCriticalNotificationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderPhysicalCardCriticalNotificationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderPhysicalCardCriticalNotificationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderPhysicalCardCriticalNotificationRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return OrderPhysicalCardCriticalNotificationRequestMultiError(errors)
	}

	return nil
}

// OrderPhysicalCardCriticalNotificationRequestMultiError is an error wrapping
// multiple validation errors returned by
// OrderPhysicalCardCriticalNotificationRequest.ValidateAll() if the
// designated constraints aren't met.
type OrderPhysicalCardCriticalNotificationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrderPhysicalCardCriticalNotificationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrderPhysicalCardCriticalNotificationRequestMultiError) AllErrors() []error { return m }

// OrderPhysicalCardCriticalNotificationRequestValidationError is the
// validation error returned by
// OrderPhysicalCardCriticalNotificationRequest.Validate if the designated
// constraints aren't met.
type OrderPhysicalCardCriticalNotificationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrderPhysicalCardCriticalNotificationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrderPhysicalCardCriticalNotificationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrderPhysicalCardCriticalNotificationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrderPhysicalCardCriticalNotificationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrderPhysicalCardCriticalNotificationRequestValidationError) ErrorName() string {
	return "OrderPhysicalCardCriticalNotificationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e OrderPhysicalCardCriticalNotificationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrderPhysicalCardCriticalNotificationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrderPhysicalCardCriticalNotificationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrderPhysicalCardCriticalNotificationRequestValidationError{}

// Validate checks the field values on
// OrderPhysicalCardCriticalNotificationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OrderPhysicalCardCriticalNotificationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// OrderPhysicalCardCriticalNotificationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// OrderPhysicalCardCriticalNotificationResponseMultiError, or nil if none found.
func (m *OrderPhysicalCardCriticalNotificationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *OrderPhysicalCardCriticalNotificationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderPhysicalCardCriticalNotificationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderPhysicalCardCriticalNotificationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderPhysicalCardCriticalNotificationResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OrderPhysicalCardCriticalNotificationResponseMultiError(errors)
	}

	return nil
}

// OrderPhysicalCardCriticalNotificationResponseMultiError is an error wrapping
// multiple validation errors returned by
// OrderPhysicalCardCriticalNotificationResponse.ValidateAll() if the
// designated constraints aren't met.
type OrderPhysicalCardCriticalNotificationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrderPhysicalCardCriticalNotificationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrderPhysicalCardCriticalNotificationResponseMultiError) AllErrors() []error { return m }

// OrderPhysicalCardCriticalNotificationResponseValidationError is the
// validation error returned by
// OrderPhysicalCardCriticalNotificationResponse.Validate if the designated
// constraints aren't met.
type OrderPhysicalCardCriticalNotificationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrderPhysicalCardCriticalNotificationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrderPhysicalCardCriticalNotificationResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e OrderPhysicalCardCriticalNotificationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrderPhysicalCardCriticalNotificationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrderPhysicalCardCriticalNotificationResponseValidationError) ErrorName() string {
	return "OrderPhysicalCardCriticalNotificationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e OrderPhysicalCardCriticalNotificationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrderPhysicalCardCriticalNotificationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrderPhysicalCardCriticalNotificationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrderPhysicalCardCriticalNotificationResponseValidationError{}
