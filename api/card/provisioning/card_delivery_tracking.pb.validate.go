// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/provisioning/card_delivery_tracking.proto

package provisioning

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CardDeliveryTracking with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardDeliveryTracking) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardDeliveryTracking with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardDeliveryTrackingMultiError, or nil if none found.
func (m *CardDeliveryTracking) ValidateAll() error {
	return m.validate(true)
}

func (m *CardDeliveryTracking) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetCardQrData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardDeliveryTrackingValidationError{
					field:  "CardQrData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardDeliveryTrackingValidationError{
					field:  "CardQrData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardQrData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardDeliveryTrackingValidationError{
				field:  "CardQrData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardDeliveryTrackingValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardDeliveryTrackingValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardDeliveryTrackingValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardDeliveryTrackingValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardDeliveryTrackingValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardDeliveryTrackingValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardDeliveryTrackingValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardDeliveryTrackingValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardDeliveryTrackingValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardDeliveryTrackingMultiError(errors)
	}

	return nil
}

// CardDeliveryTrackingMultiError is an error wrapping multiple validation
// errors returned by CardDeliveryTracking.ValidateAll() if the designated
// constraints aren't met.
type CardDeliveryTrackingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardDeliveryTrackingMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardDeliveryTrackingMultiError) AllErrors() []error { return m }

// CardDeliveryTrackingValidationError is the validation error returned by
// CardDeliveryTracking.Validate if the designated constraints aren't met.
type CardDeliveryTrackingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardDeliveryTrackingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardDeliveryTrackingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardDeliveryTrackingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardDeliveryTrackingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardDeliveryTrackingValidationError) ErrorName() string {
	return "CardDeliveryTrackingValidationError"
}

// Error satisfies the builtin error interface
func (e CardDeliveryTrackingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardDeliveryTracking.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardDeliveryTrackingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardDeliveryTrackingValidationError{}

// Validate checks the field values on CardQRData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CardQRData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardQRData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CardQRDataMultiError, or
// nil if none found.
func (m *CardQRData) ValidateAll() error {
	return m.validate(true)
}

func (m *CardQRData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MaskedCardNumber

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardQRDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardQRDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardQRDataValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PostalCode

	// no validation rules for PhoneNumberUpdated

	if len(errors) > 0 {
		return CardQRDataMultiError(errors)
	}

	return nil
}

// CardQRDataMultiError is an error wrapping multiple validation errors
// returned by CardQRData.ValidateAll() if the designated constraints aren't met.
type CardQRDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardQRDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardQRDataMultiError) AllErrors() []error { return m }

// CardQRDataValidationError is the validation error returned by
// CardQRData.Validate if the designated constraints aren't met.
type CardQRDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardQRDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardQRDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardQRDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardQRDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardQRDataValidationError) ErrorName() string { return "CardQRDataValidationError" }

// Error satisfies the builtin error interface
func (e CardQRDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardQRData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardQRDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardQRDataValidationError{}
