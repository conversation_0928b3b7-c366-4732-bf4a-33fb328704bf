// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/provisioning/physical_card_dispatch_request.proto

package provisioning

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PhysicalCardDispatchRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PhysicalCardDispatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PhysicalCardDispatchRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PhysicalCardDispatchRequestMultiError, or nil if none found.
func (m *PhysicalCardDispatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PhysicalCardDispatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CardId

	// no validation rules for State

	// no validation rules for Retries

	// no validation rules for RequestId

	// no validation rules for FailureResponseCode

	// no validation rules for FailureResponseReason

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PhysicalCardDispatchRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PhysicalCardDispatchRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PhysicalCardDispatchRequestValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PhysicalCardDispatchRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PhysicalCardDispatchRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PhysicalCardDispatchRequestValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PhysicalCardDispatchRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PhysicalCardDispatchRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PhysicalCardDispatchRequestValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientReqId

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PhysicalCardDispatchRequestValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PhysicalCardDispatchRequestValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PhysicalCardDispatchRequestValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SubStatus

	// no validation rules for CurrentStage

	if len(errors) > 0 {
		return PhysicalCardDispatchRequestMultiError(errors)
	}

	return nil
}

// PhysicalCardDispatchRequestMultiError is an error wrapping multiple
// validation errors returned by PhysicalCardDispatchRequest.ValidateAll() if
// the designated constraints aren't met.
type PhysicalCardDispatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PhysicalCardDispatchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PhysicalCardDispatchRequestMultiError) AllErrors() []error { return m }

// PhysicalCardDispatchRequestValidationError is the validation error returned
// by PhysicalCardDispatchRequest.Validate if the designated constraints
// aren't met.
type PhysicalCardDispatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PhysicalCardDispatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PhysicalCardDispatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PhysicalCardDispatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PhysicalCardDispatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PhysicalCardDispatchRequestValidationError) ErrorName() string {
	return "PhysicalCardDispatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PhysicalCardDispatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPhysicalCardDispatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PhysicalCardDispatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PhysicalCardDispatchRequestValidationError{}

// Validate checks the field values on ChargesReversalRewardDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChargesReversalRewardDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChargesReversalRewardDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChargesReversalRewardDetailsMultiError, or nil if none found.
func (m *ChargesReversalRewardDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ChargesReversalRewardDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReversalRewardOfferId

	// no validation rules for ReversalRewardEventId

	if all {
		switch v := interface{}(m.GetReversalRewardAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChargesReversalRewardDetailsValidationError{
					field:  "ReversalRewardAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChargesReversalRewardDetailsValidationError{
					field:  "ReversalRewardAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReversalRewardAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChargesReversalRewardDetailsValidationError{
				field:  "ReversalRewardAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ChargesReversalRewardDetailsMultiError(errors)
	}

	return nil
}

// ChargesReversalRewardDetailsMultiError is an error wrapping multiple
// validation errors returned by ChargesReversalRewardDetails.ValidateAll() if
// the designated constraints aren't met.
type ChargesReversalRewardDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChargesReversalRewardDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChargesReversalRewardDetailsMultiError) AllErrors() []error { return m }

// ChargesReversalRewardDetailsValidationError is the validation error returned
// by ChargesReversalRewardDetails.Validate if the designated constraints
// aren't met.
type ChargesReversalRewardDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChargesReversalRewardDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChargesReversalRewardDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChargesReversalRewardDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChargesReversalRewardDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChargesReversalRewardDetailsValidationError) ErrorName() string {
	return "ChargesReversalRewardDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ChargesReversalRewardDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChargesReversalRewardDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChargesReversalRewardDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChargesReversalRewardDetailsValidationError{}

// Validate checks the field values on
// PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsMultiError,
// or nil if none found.
func (m *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FundTransferClientReqId

	if all {
		switch v := interface{}(m.GetChargesReversalRewardDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{
					field:  "ChargesReversalRewardDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{
					field:  "ChargesReversalRewardDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChargesReversalRewardDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{
				field:  "ChargesReversalRewardDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestType

	switch v := m.ChargesCollectionMetaData.(type) {
	case *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionWithChargesApiInfo:
		if v == nil {
			err := PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{
				field:  "ChargesCollectionMetaData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetChargesCollectionWithChargesApiInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{
						field:  "ChargesCollectionWithChargesApiInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{
						field:  "ChargesCollectionWithChargesApiInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetChargesCollectionWithChargesApiInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{
					field:  "ChargesCollectionWithChargesApiInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionWithPayInfo:
		if v == nil {
			err := PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{
				field:  "ChargesCollectionMetaData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetChargesCollectionWithPayInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{
						field:  "ChargesCollectionWithPayInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{
						field:  "ChargesCollectionWithPayInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetChargesCollectionWithPayInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{
					field:  "ChargesCollectionWithPayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsMultiError(errors)
	}

	return nil
}

// PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsMultiError is
// an error wrapping multiple validation errors returned by
// PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails.ValidateAll()
// if the designated constraints aren't met.
type PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsMultiError) AllErrors() []error {
	return m
}

// PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError
// is the validation error returned by
// PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails.Validate if
// the designated constraints aren't met.
type PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError) ErrorName() string {
	return "PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetailsValidationError{}

// Validate checks the field values on
// PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoMultiError,
// or nil if none found.
func (m *PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VendorRequestId

	// no validation rules for TransactionId

	if len(errors) > 0 {
		return PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoMultiError(errors)
	}

	return nil
}

// PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoMultiError is
// an error wrapping multiple validation errors returned by
// PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo.ValidateAll()
// if the designated constraints aren't met.
type PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoMultiError) AllErrors() []error {
	return m
}

// PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoValidationError
// is the validation error returned by
// PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo.Validate if
// the designated constraints aren't met.
type PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoValidationError) ErrorName() string {
	return "PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfoValidationError{}

// Validate checks the field values on
// PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoMultiError, or nil
// if none found.
func (m *PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FundTransferClintRequestId

	if len(errors) > 0 {
		return PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoMultiError(errors)
	}

	return nil
}

// PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoMultiError is an
// error wrapping multiple validation errors returned by
// PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo.ValidateAll() if
// the designated constraints aren't met.
type PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoMultiError) AllErrors() []error {
	return m
}

// PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoValidationError is
// the validation error returned by
// PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo.Validate if the
// designated constraints aren't met.
type PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoValidationError) ErrorName() string {
	return "PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPhysicalCardDispatchRequest_ChargesCollectionWithPayInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PhysicalCardDispatchRequest_ChargesCollectionWithPayInfoValidationError{}
