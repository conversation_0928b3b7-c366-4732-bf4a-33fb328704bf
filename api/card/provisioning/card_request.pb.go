// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/provisioning/card_request.proto

package provisioning

import (
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	card "github.com/epifi/gamma/api/card"
	enums "github.com/epifi/gamma/api/card/enums"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	external "github.com/epifi/gamma/api/tiering/external"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Message to keep track of the card requests
//
//go:generate gen_sql -types=CardRequestDetails,StageDetails
type CardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary key to identify a card request
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// id for the card for which request is getting formed
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// primary identifier to the actor table, card is being provisioned for this actor
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Orchestration identifier which has started this execution
	OrchestrationId string `protobuf:"bytes,4,opt,name=orchestration_id,json=orchestrationId,proto3" json:"orchestration_id,omitempty"`
	// vendor handling the request
	Vendor vendorgateway.Vendor `protobuf:"varint,5,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	// Metadata for a given request. This might contain the error reason and codes received from the vendor for a request.
	RequestDetails *CardRequestDetails `protobuf:"bytes,6,opt,name=request_details,json=requestDetails,proto3" json:"request_details,omitempty"`
	// Deeplink to redirect to the next screen
	NextAction *deeplink.Deeplink `protobuf:"bytes,7,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	// stage details of different stages card request processing
	StageDetails *StageDetails             `protobuf:"bytes,8,opt,name=stage_details,json=stageDetails,proto3" json:"stage_details,omitempty"`
	Workflow     enums.CardRequestWorkflow `protobuf:"varint,9,opt,name=workflow,proto3,enum=card.enums.CardRequestWorkflow" json:"workflow,omitempty"`
	// Status of the request
	Status enums.CardRequestStatus `protobuf:"varint,10,opt,name=status,proto3,enum=card.enums.CardRequestStatus" json:"status,omitempty"`
	// enum denoting entry point for the request, APP/SHERLOCK etc
	Provenance card.Provenance        `protobuf:"varint,11,opt,name=provenance,proto3,enum=card.Provenance" json:"provenance,omitempty"`
	CreatedAt  *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt  *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt  *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *CardRequest) Reset() {
	*x = CardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardRequest) ProtoMessage() {}

func (x *CardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardRequest.ProtoReflect.Descriptor instead.
func (*CardRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_request_proto_rawDescGZIP(), []int{0}
}

func (x *CardRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CardRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CardRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CardRequest) GetOrchestrationId() string {
	if x != nil {
		return x.OrchestrationId
	}
	return ""
}

func (x *CardRequest) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *CardRequest) GetRequestDetails() *CardRequestDetails {
	if x != nil {
		return x.RequestDetails
	}
	return nil
}

func (x *CardRequest) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *CardRequest) GetStageDetails() *StageDetails {
	if x != nil {
		return x.StageDetails
	}
	return nil
}

func (x *CardRequest) GetWorkflow() enums.CardRequestWorkflow {
	if x != nil {
		return x.Workflow
	}
	return enums.CardRequestWorkflow(0)
}

func (x *CardRequest) GetStatus() enums.CardRequestStatus {
	if x != nil {
		return x.Status
	}
	return enums.CardRequestStatus(0)
}

func (x *CardRequest) GetProvenance() card.Provenance {
	if x != nil {
		return x.Provenance
	}
	return card.Provenance(0)
}

func (x *CardRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CardRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CardRequest) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

type CardRequestDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// one of containing request details based on the workflow type
	//
	// Types that are assignable to Data:
	//
	//	*CardRequestDetails_RenewCardRequestDetails
	//	*CardRequestDetails_AmcChargesDetails
	Data isCardRequestDetails_Data `protobuf_oneof:"Data"`
}

func (x *CardRequestDetails) Reset() {
	*x = CardRequestDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_request_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardRequestDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardRequestDetails) ProtoMessage() {}

func (x *CardRequestDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_request_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardRequestDetails.ProtoReflect.Descriptor instead.
func (*CardRequestDetails) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_request_proto_rawDescGZIP(), []int{1}
}

func (m *CardRequestDetails) GetData() isCardRequestDetails_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *CardRequestDetails) GetRenewCardRequestDetails() *RenewCardRequestDetails {
	if x, ok := x.GetData().(*CardRequestDetails_RenewCardRequestDetails); ok {
		return x.RenewCardRequestDetails
	}
	return nil
}

func (x *CardRequestDetails) GetAmcChargesDetails() *AmcChargesDetails {
	if x, ok := x.GetData().(*CardRequestDetails_AmcChargesDetails); ok {
		return x.AmcChargesDetails
	}
	return nil
}

type isCardRequestDetails_Data interface {
	isCardRequestDetails_Data()
}

type CardRequestDetails_RenewCardRequestDetails struct {
	RenewCardRequestDetails *RenewCardRequestDetails `protobuf:"bytes,1,opt,name=renew_card_request_details,json=renewCardRequestDetails,proto3,oneof"`
}

type CardRequestDetails_AmcChargesDetails struct {
	AmcChargesDetails *AmcChargesDetails `protobuf:"bytes,2,opt,name=amc_charges_details,json=amcChargesDetails,proto3,oneof"`
}

func (*CardRequestDetails_RenewCardRequestDetails) isCardRequestDetails_Data() {}

func (*CardRequestDetails_AmcChargesDetails) isCardRequestDetails_Data() {}

type RenewCardRequestDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reason for blocking the previous card
	BlockCardReason string `protobuf:"bytes,1,opt,name=block_card_reason,json=blockCardReason,proto3" json:"block_card_reason,omitempty"`
	// address type where card needs to be delivered
	AddressType typesv2.AddressType `protobuf:"varint,2,opt,name=address_type,json=addressType,proto3,enum=api.typesv2.AddressType" json:"address_type,omitempty"`
	// card form selected by user for card renewal
	CardForm            card.CardForm   `protobuf:"varint,3,opt,name=card_form,json=cardForm,proto3,enum=card.CardForm" json:"card_form,omitempty"`
	BlockCardProvenance card.Provenance `protobuf:"varint,4,opt,name=block_card_provenance,json=blockCardProvenance,proto3,enum=card.Provenance" json:"block_card_provenance,omitempty"`
}

func (x *RenewCardRequestDetails) Reset() {
	*x = RenewCardRequestDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_request_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RenewCardRequestDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenewCardRequestDetails) ProtoMessage() {}

func (x *RenewCardRequestDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_request_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenewCardRequestDetails.ProtoReflect.Descriptor instead.
func (*RenewCardRequestDetails) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_request_proto_rawDescGZIP(), []int{2}
}

func (x *RenewCardRequestDetails) GetBlockCardReason() string {
	if x != nil {
		return x.BlockCardReason
	}
	return ""
}

func (x *RenewCardRequestDetails) GetAddressType() typesv2.AddressType {
	if x != nil {
		return x.AddressType
	}
	return typesv2.AddressType(0)
}

func (x *RenewCardRequestDetails) GetCardForm() card.CardForm {
	if x != nil {
		return x.CardForm
	}
	return card.CardForm(0)
}

func (x *RenewCardRequestDetails) GetBlockCardProvenance() card.Provenance {
	if x != nil {
		return x.BlockCardProvenance
	}
	return card.Provenance(0)
}

type StageDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardRequestStages map[string]*CardRequestStage `protobuf:"bytes,1,rep,name=card_request_stages,json=cardRequestStages,proto3" json:"card_request_stages,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *StageDetails) Reset() {
	*x = StageDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_request_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageDetails) ProtoMessage() {}

func (x *StageDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_request_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageDetails.ProtoReflect.Descriptor instead.
func (*StageDetails) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_request_proto_rawDescGZIP(), []int{3}
}

func (x *StageDetails) GetCardRequestStages() map[string]*CardRequestStage {
	if x != nil {
		return x.CardRequestStages
	}
	return nil
}

type CardRequestStage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StageName enums.CardRequestStageName      `protobuf:"varint,1,opt,name=stage_name,json=stageName,proto3,enum=card.enums.CardRequestStageName" json:"stage_name,omitempty"`
	Status    enums.CardRequestStageStatus    `protobuf:"varint,2,opt,name=status,proto3,enum=card.enums.CardRequestStageStatus" json:"status,omitempty"`
	SubStatus enums.CardRequestStageSubStatus `protobuf:"varint,3,opt,name=sub_status,json=subStatus,proto3,enum=card.enums.CardRequestStageSubStatus" json:"sub_status,omitempty"`
	StaledAt  *timestamppb.Timestamp          `protobuf:"bytes,11,opt,name=staled_at,json=staledAt,proto3" json:"staled_at,omitempty"`
	CreatedAt *timestamppb.Timestamp          `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp          `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *CardRequestStage) Reset() {
	*x = CardRequestStage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_request_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardRequestStage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardRequestStage) ProtoMessage() {}

func (x *CardRequestStage) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_request_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardRequestStage.ProtoReflect.Descriptor instead.
func (*CardRequestStage) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_request_proto_rawDescGZIP(), []int{4}
}

func (x *CardRequestStage) GetStageName() enums.CardRequestStageName {
	if x != nil {
		return x.StageName
	}
	return enums.CardRequestStageName(0)
}

func (x *CardRequestStage) GetStatus() enums.CardRequestStageStatus {
	if x != nil {
		return x.Status
	}
	return enums.CardRequestStageStatus(0)
}

func (x *CardRequestStage) GetSubStatus() enums.CardRequestStageSubStatus {
	if x != nil {
		return x.SubStatus
	}
	return enums.CardRequestStageSubStatus(0)
}

func (x *CardRequestStage) GetStaledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StaledAt
	}
	return nil
}

func (x *CardRequestStage) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CardRequestStage) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type AmcChargesDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileGenDate           *date.Date              `protobuf:"bytes,1,opt,name=file_gen_date,json=fileGenDate,proto3" json:"file_gen_date,omitempty"`
	BatchNumber           int32                   `protobuf:"varint,2,opt,name=batch_number,json=batchNumber,proto3" json:"batch_number,omitempty"`
	AmcChargeAmount       *money.Money            `protobuf:"bytes,3,opt,name=amc_charge_amount,json=amcChargeAmount,proto3" json:"amc_charge_amount,omitempty"`
	OperationalStatus     enums.OperationalStatus `protobuf:"varint,4,opt,name=operational_status,json=operationalStatus,proto3,enum=card.enums.OperationalStatus" json:"operational_status,omitempty"`
	FreezeStatus          enums.FreezeStatus      `protobuf:"varint,5,opt,name=freeze_status,json=freezeStatus,proto3,enum=card.enums.FreezeStatus" json:"freeze_status,omitempty"`
	AnniversaryDate       *date.Date              `protobuf:"bytes,6,opt,name=anniversary_date,json=anniversaryDate,proto3" json:"anniversary_date,omitempty"`
	TierAtAnniversaryDate external.Tier           `protobuf:"varint,7,opt,name=tier_at_anniversary_date,json=tierAtAnniversaryDate,proto3,enum=tiering.external.Tier" json:"tier_at_anniversary_date,omitempty"`
}

func (x *AmcChargesDetails) Reset() {
	*x = AmcChargesDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_request_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AmcChargesDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AmcChargesDetails) ProtoMessage() {}

func (x *AmcChargesDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_request_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AmcChargesDetails.ProtoReflect.Descriptor instead.
func (*AmcChargesDetails) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_request_proto_rawDescGZIP(), []int{5}
}

func (x *AmcChargesDetails) GetFileGenDate() *date.Date {
	if x != nil {
		return x.FileGenDate
	}
	return nil
}

func (x *AmcChargesDetails) GetBatchNumber() int32 {
	if x != nil {
		return x.BatchNumber
	}
	return 0
}

func (x *AmcChargesDetails) GetAmcChargeAmount() *money.Money {
	if x != nil {
		return x.AmcChargeAmount
	}
	return nil
}

func (x *AmcChargesDetails) GetOperationalStatus() enums.OperationalStatus {
	if x != nil {
		return x.OperationalStatus
	}
	return enums.OperationalStatus(0)
}

func (x *AmcChargesDetails) GetFreezeStatus() enums.FreezeStatus {
	if x != nil {
		return x.FreezeStatus
	}
	return enums.FreezeStatus(0)
}

func (x *AmcChargesDetails) GetAnniversaryDate() *date.Date {
	if x != nil {
		return x.AnniversaryDate
	}
	return nil
}

func (x *AmcChargesDetails) GetTierAtAnniversaryDate() external.Tier {
	if x != nil {
		return x.TierAtAnniversaryDate
	}
	return external.Tier(0)
}

var File_api_card_provisioning_card_request_proto protoreflect.FileDescriptor

var file_api_card_provisioning_card_request_proto_rawDesc = []byte{
	0x0a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x1a, 0x13, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xd6, 0x05, 0x0a, 0x0b, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12,
	0x4e, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a,
	0x0d, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x3b, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xdf, 0x01, 0x0a, 0x12, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x69, 0x0a, 0x1a, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x48, 0x00, 0x52, 0x17, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x56, 0x0a, 0x13,
	0x61, 0x6d, 0x63, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x6d,
	0x63, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48,
	0x00, 0x52, 0x11, 0x61, 0x6d, 0x63, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x42, 0x06, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x22, 0xf5, 0x01, 0x0a,
	0x17, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2b, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x46, 0x6f, 0x72, 0x6d, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x44,
	0x0a, 0x15, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52,
	0x13, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x22, 0xe1, 0x01, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x66, 0x0a, 0x13, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x63, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x73, 0x1a, 0x69, 0x0a,
	0x16, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x84, 0x03, 0x0a, 0x10, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x3f, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3a,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x0a, 0x0a, 0x73, 0x75,
	0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x75, 0x62, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x73, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x37, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x08, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0xc9, 0x03, 0x0a, 0x11, 0x41, 0x6d, 0x63, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x35, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x67, 0x65,
	0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x0b, 0x66, 0x69, 0x6c, 0x65, 0x47, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x62, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x3e, 0x0a, 0x11, 0x61, 0x6d, 0x63, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f,
	0x61, 0x6d, 0x63, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x4c, 0x0a, 0x12, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a,
	0x0d, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c,
	0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x10,
	0x61, 0x6e, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0f, 0x61, 0x6e, 0x6e, 0x69, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x4f, 0x0a, 0x18, 0x74, 0x69,
	0x65, 0x72, 0x5f, 0x61, 0x74, 0x5f, 0x61, 0x6e, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x72,
	0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e,
	0x54, 0x69, 0x65, 0x72, 0x52, 0x15, 0x74, 0x69, 0x65, 0x72, 0x41, 0x74, 0x41, 0x6e, 0x6e, 0x69,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x42, 0x5c, 0x0a, 0x2c, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x5a, 0x2c, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_card_provisioning_card_request_proto_rawDescOnce sync.Once
	file_api_card_provisioning_card_request_proto_rawDescData = file_api_card_provisioning_card_request_proto_rawDesc
)

func file_api_card_provisioning_card_request_proto_rawDescGZIP() []byte {
	file_api_card_provisioning_card_request_proto_rawDescOnce.Do(func() {
		file_api_card_provisioning_card_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_provisioning_card_request_proto_rawDescData)
	})
	return file_api_card_provisioning_card_request_proto_rawDescData
}

var file_api_card_provisioning_card_request_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_card_provisioning_card_request_proto_goTypes = []interface{}{
	(*CardRequest)(nil),                  // 0: card.provisioning.CardRequest
	(*CardRequestDetails)(nil),           // 1: card.provisioning.CardRequestDetails
	(*RenewCardRequestDetails)(nil),      // 2: card.provisioning.RenewCardRequestDetails
	(*StageDetails)(nil),                 // 3: card.provisioning.StageDetails
	(*CardRequestStage)(nil),             // 4: card.provisioning.CardRequestStage
	(*AmcChargesDetails)(nil),            // 5: card.provisioning.AmcChargesDetails
	nil,                                  // 6: card.provisioning.StageDetails.CardRequestStagesEntry
	(vendorgateway.Vendor)(0),            // 7: vendorgateway.Vendor
	(*deeplink.Deeplink)(nil),            // 8: frontend.deeplink.Deeplink
	(enums.CardRequestWorkflow)(0),       // 9: card.enums.CardRequestWorkflow
	(enums.CardRequestStatus)(0),         // 10: card.enums.CardRequestStatus
	(card.Provenance)(0),                 // 11: card.Provenance
	(*timestamppb.Timestamp)(nil),        // 12: google.protobuf.Timestamp
	(typesv2.AddressType)(0),             // 13: api.typesv2.AddressType
	(card.CardForm)(0),                   // 14: card.CardForm
	(enums.CardRequestStageName)(0),      // 15: card.enums.CardRequestStageName
	(enums.CardRequestStageStatus)(0),    // 16: card.enums.CardRequestStageStatus
	(enums.CardRequestStageSubStatus)(0), // 17: card.enums.CardRequestStageSubStatus
	(*date.Date)(nil),                    // 18: google.type.Date
	(*money.Money)(nil),                  // 19: google.type.Money
	(enums.OperationalStatus)(0),         // 20: card.enums.OperationalStatus
	(enums.FreezeStatus)(0),              // 21: card.enums.FreezeStatus
	(external.Tier)(0),                   // 22: tiering.external.Tier
}
var file_api_card_provisioning_card_request_proto_depIdxs = []int32{
	7,  // 0: card.provisioning.CardRequest.vendor:type_name -> vendorgateway.Vendor
	1,  // 1: card.provisioning.CardRequest.request_details:type_name -> card.provisioning.CardRequestDetails
	8,  // 2: card.provisioning.CardRequest.next_action:type_name -> frontend.deeplink.Deeplink
	3,  // 3: card.provisioning.CardRequest.stage_details:type_name -> card.provisioning.StageDetails
	9,  // 4: card.provisioning.CardRequest.workflow:type_name -> card.enums.CardRequestWorkflow
	10, // 5: card.provisioning.CardRequest.status:type_name -> card.enums.CardRequestStatus
	11, // 6: card.provisioning.CardRequest.provenance:type_name -> card.Provenance
	12, // 7: card.provisioning.CardRequest.created_at:type_name -> google.protobuf.Timestamp
	12, // 8: card.provisioning.CardRequest.updated_at:type_name -> google.protobuf.Timestamp
	12, // 9: card.provisioning.CardRequest.deleted_at:type_name -> google.protobuf.Timestamp
	2,  // 10: card.provisioning.CardRequestDetails.renew_card_request_details:type_name -> card.provisioning.RenewCardRequestDetails
	5,  // 11: card.provisioning.CardRequestDetails.amc_charges_details:type_name -> card.provisioning.AmcChargesDetails
	13, // 12: card.provisioning.RenewCardRequestDetails.address_type:type_name -> api.typesv2.AddressType
	14, // 13: card.provisioning.RenewCardRequestDetails.card_form:type_name -> card.CardForm
	11, // 14: card.provisioning.RenewCardRequestDetails.block_card_provenance:type_name -> card.Provenance
	6,  // 15: card.provisioning.StageDetails.card_request_stages:type_name -> card.provisioning.StageDetails.CardRequestStagesEntry
	15, // 16: card.provisioning.CardRequestStage.stage_name:type_name -> card.enums.CardRequestStageName
	16, // 17: card.provisioning.CardRequestStage.status:type_name -> card.enums.CardRequestStageStatus
	17, // 18: card.provisioning.CardRequestStage.sub_status:type_name -> card.enums.CardRequestStageSubStatus
	12, // 19: card.provisioning.CardRequestStage.staled_at:type_name -> google.protobuf.Timestamp
	12, // 20: card.provisioning.CardRequestStage.created_at:type_name -> google.protobuf.Timestamp
	12, // 21: card.provisioning.CardRequestStage.updated_at:type_name -> google.protobuf.Timestamp
	18, // 22: card.provisioning.AmcChargesDetails.file_gen_date:type_name -> google.type.Date
	19, // 23: card.provisioning.AmcChargesDetails.amc_charge_amount:type_name -> google.type.Money
	20, // 24: card.provisioning.AmcChargesDetails.operational_status:type_name -> card.enums.OperationalStatus
	21, // 25: card.provisioning.AmcChargesDetails.freeze_status:type_name -> card.enums.FreezeStatus
	18, // 26: card.provisioning.AmcChargesDetails.anniversary_date:type_name -> google.type.Date
	22, // 27: card.provisioning.AmcChargesDetails.tier_at_anniversary_date:type_name -> tiering.external.Tier
	4,  // 28: card.provisioning.StageDetails.CardRequestStagesEntry.value:type_name -> card.provisioning.CardRequestStage
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_api_card_provisioning_card_request_proto_init() }
func file_api_card_provisioning_card_request_proto_init() {
	if File_api_card_provisioning_card_request_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_provisioning_card_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_request_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardRequestDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_request_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RenewCardRequestDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_request_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_request_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardRequestStage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_request_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AmcChargesDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_card_provisioning_card_request_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*CardRequestDetails_RenewCardRequestDetails)(nil),
		(*CardRequestDetails_AmcChargesDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_provisioning_card_request_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_provisioning_card_request_proto_goTypes,
		DependencyIndexes: file_api_card_provisioning_card_request_proto_depIdxs,
		MessageInfos:      file_api_card_provisioning_card_request_proto_msgTypes,
	}.Build()
	File_api_card_provisioning_card_request_proto = out.File
	file_api_card_provisioning_card_request_proto_rawDesc = nil
	file_api_card_provisioning_card_request_proto_goTypes = nil
	file_api_card_provisioning_card_request_proto_depIdxs = nil
}
