// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/card/provisioning/provisioning.pb.go

package provisioning

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the RequestSubStatus in string format in DB
func (p RequestSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing RequestSubStatus while reading from DB
func (p *RequestSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := RequestSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected RequestSubStatus value: %s", val)
	}
	*p = RequestSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for RequestSubStatus
func (x RequestSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for RequestSubStatus
func (x *RequestSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = RequestSubStatus(RequestSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the DCRequestStage in string format in DB
func (p DCRequestStage) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing DCRequestStage while reading from DB
func (p *DCRequestStage) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := DCRequestStage_value[val]
	if !ok {
		return fmt.Errorf("unexpected DCRequestStage value: %s", val)
	}
	*p = DCRequestStage(valInt)
	return nil
}

// Marshaler interface implementation for DCRequestStage
func (x DCRequestStage) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for DCRequestStage
func (x *DCRequestStage) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = DCRequestStage(DCRequestStage_value[val])
	return nil
}
