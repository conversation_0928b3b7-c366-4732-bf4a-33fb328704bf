// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/provisioning/card_tracking_request.proto

package provisioning

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CardTrackingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardTrackingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardTrackingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardTrackingRequestMultiError, or nil if none found.
func (m *CardTrackingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CardTrackingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CardId

	// no validation rules for OrderId

	// no validation rules for Awb

	// no validation rules for Carrier

	// no validation rules for RequestState

	// no validation rules for DeliveryState

	for idx, item := range m.GetScans() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CardTrackingRequestValidationError{
						field:  fmt.Sprintf("Scans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CardTrackingRequestValidationError{
						field:  fmt.Sprintf("Scans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CardTrackingRequestValidationError{
					field:  fmt.Sprintf("Scans[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for FetchAwbRetryCount

	// no validation rules for RegisterShipmentRetryCount

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardTrackingRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardTrackingRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardTrackingRequestValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardTrackingRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardTrackingRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardTrackingRequestValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardTrackingRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardTrackingRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardTrackingRequestValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPickupDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardTrackingRequestValidationError{
					field:  "PickupDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardTrackingRequestValidationError{
					field:  "PickupDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPickupDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardTrackingRequestValidationError{
				field:  "PickupDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUploadedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardTrackingRequestValidationError{
					field:  "UploadedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardTrackingRequestValidationError{
					field:  "UploadedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUploadedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardTrackingRequestValidationError{
				field:  "UploadedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PrintingVendor

	if all {
		switch v := interface{}(m.GetTrackingMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardTrackingRequestValidationError{
					field:  "TrackingMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardTrackingRequestValidationError{
					field:  "TrackingMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTrackingMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardTrackingRequestValidationError{
				field:  "TrackingMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardTrackingRequestMultiError(errors)
	}

	return nil
}

// CardTrackingRequestMultiError is an error wrapping multiple validation
// errors returned by CardTrackingRequest.ValidateAll() if the designated
// constraints aren't met.
type CardTrackingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardTrackingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardTrackingRequestMultiError) AllErrors() []error { return m }

// CardTrackingRequestValidationError is the validation error returned by
// CardTrackingRequest.Validate if the designated constraints aren't met.
type CardTrackingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardTrackingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardTrackingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardTrackingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardTrackingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardTrackingRequestValidationError) ErrorName() string {
	return "CardTrackingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CardTrackingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardTrackingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardTrackingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardTrackingRequestValidationError{}

// Validate checks the field values on TrackingMetadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TrackingMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TrackingMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TrackingMetadataMultiError, or nil if none found.
func (m *TrackingMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *TrackingMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientReqId

	if all {
		switch v := interface{}(m.GetExpectedDeliveryDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TrackingMetadataValidationError{
					field:  "ExpectedDeliveryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TrackingMetadataValidationError{
					field:  "ExpectedDeliveryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectedDeliveryDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TrackingMetadataValidationError{
				field:  "ExpectedDeliveryDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TrackingMetadataMultiError(errors)
	}

	return nil
}

// TrackingMetadataMultiError is an error wrapping multiple validation errors
// returned by TrackingMetadata.ValidateAll() if the designated constraints
// aren't met.
type TrackingMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TrackingMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TrackingMetadataMultiError) AllErrors() []error { return m }

// TrackingMetadataValidationError is the validation error returned by
// TrackingMetadata.Validate if the designated constraints aren't met.
type TrackingMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TrackingMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TrackingMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TrackingMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TrackingMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TrackingMetadataValidationError) ErrorName() string { return "TrackingMetadataValidationError" }

// Error satisfies the builtin error interface
func (e TrackingMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTrackingMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TrackingMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TrackingMetadataValidationError{}

// Validate checks the field values on Scan with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Scan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Scan with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ScanMultiError, or nil if none found.
func (m *Scan) ValidateAll() error {
	return m.validate(true)
}

func (m *Scan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Location

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScanValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScanValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScanValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StatusDescription

	// no validation rules for DeliveryState

	if len(errors) > 0 {
		return ScanMultiError(errors)
	}

	return nil
}

// ScanMultiError is an error wrapping multiple validation errors returned by
// Scan.ValidateAll() if the designated constraints aren't met.
type ScanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScanMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScanMultiError) AllErrors() []error { return m }

// ScanValidationError is the validation error returned by Scan.Validate if the
// designated constraints aren't met.
type ScanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScanValidationError) ErrorName() string { return "ScanValidationError" }

// Error satisfies the builtin error interface
func (e ScanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScanValidationError{}
