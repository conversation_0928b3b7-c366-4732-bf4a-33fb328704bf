// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/provisioning/physical_card_dispatch_request.proto

package provisioning

import (
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PhysicalCardDispatchRequestFieldMask int32

const (
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_FIELD_MASK_UNSPECIFIED  PhysicalCardDispatchRequestFieldMask = 0
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_CARD_ID                 PhysicalCardDispatchRequestFieldMask = 1
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_STATE                   PhysicalCardDispatchRequestFieldMask = 2
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_RETRIES                 PhysicalCardDispatchRequestFieldMask = 3
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_REQUEST_ID              PhysicalCardDispatchRequestFieldMask = 4
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_FAILURE_RESPONSE_CODE   PhysicalCardDispatchRequestFieldMask = 5
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_FAILURE_RESPONSE_REASON PhysicalCardDispatchRequestFieldMask = 6
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_ID                      PhysicalCardDispatchRequestFieldMask = 7
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_CREATED_AT              PhysicalCardDispatchRequestFieldMask = 8
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_UPDATED_AT              PhysicalCardDispatchRequestFieldMask = 9
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_DELETED_AT              PhysicalCardDispatchRequestFieldMask = 10
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_CLIENT_REQ_ID           PhysicalCardDispatchRequestFieldMask = 11
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_DETAILS                 PhysicalCardDispatchRequestFieldMask = 12
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_SUB_STATUS              PhysicalCardDispatchRequestFieldMask = 13
	PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_CURRENT_STAGE           PhysicalCardDispatchRequestFieldMask = 14
)

// Enum value maps for PhysicalCardDispatchRequestFieldMask.
var (
	PhysicalCardDispatchRequestFieldMask_name = map[int32]string{
		0:  "PHYSICAL_CARD_DISPATCH_REQUEST_FIELD_MASK_UNSPECIFIED",
		1:  "PHYSICAL_CARD_DISPATCH_REQUEST_CARD_ID",
		2:  "PHYSICAL_CARD_DISPATCH_REQUEST_STATE",
		3:  "PHYSICAL_CARD_DISPATCH_REQUEST_RETRIES",
		4:  "PHYSICAL_CARD_DISPATCH_REQUEST_REQUEST_ID",
		5:  "PHYSICAL_CARD_DISPATCH_REQUEST_FAILURE_RESPONSE_CODE",
		6:  "PHYSICAL_CARD_DISPATCH_REQUEST_FAILURE_RESPONSE_REASON",
		7:  "PHYSICAL_CARD_DISPATCH_REQUEST_ID",
		8:  "PHYSICAL_CARD_DISPATCH_REQUEST_CREATED_AT",
		9:  "PHYSICAL_CARD_DISPATCH_REQUEST_UPDATED_AT",
		10: "PHYSICAL_CARD_DISPATCH_REQUEST_DELETED_AT",
		11: "PHYSICAL_CARD_DISPATCH_REQUEST_CLIENT_REQ_ID",
		12: "PHYSICAL_CARD_DISPATCH_REQUEST_DETAILS",
		13: "PHYSICAL_CARD_DISPATCH_REQUEST_SUB_STATUS",
		14: "PHYSICAL_CARD_DISPATCH_REQUEST_CURRENT_STAGE",
	}
	PhysicalCardDispatchRequestFieldMask_value = map[string]int32{
		"PHYSICAL_CARD_DISPATCH_REQUEST_FIELD_MASK_UNSPECIFIED":  0,
		"PHYSICAL_CARD_DISPATCH_REQUEST_CARD_ID":                 1,
		"PHYSICAL_CARD_DISPATCH_REQUEST_STATE":                   2,
		"PHYSICAL_CARD_DISPATCH_REQUEST_RETRIES":                 3,
		"PHYSICAL_CARD_DISPATCH_REQUEST_REQUEST_ID":              4,
		"PHYSICAL_CARD_DISPATCH_REQUEST_FAILURE_RESPONSE_CODE":   5,
		"PHYSICAL_CARD_DISPATCH_REQUEST_FAILURE_RESPONSE_REASON": 6,
		"PHYSICAL_CARD_DISPATCH_REQUEST_ID":                      7,
		"PHYSICAL_CARD_DISPATCH_REQUEST_CREATED_AT":              8,
		"PHYSICAL_CARD_DISPATCH_REQUEST_UPDATED_AT":              9,
		"PHYSICAL_CARD_DISPATCH_REQUEST_DELETED_AT":              10,
		"PHYSICAL_CARD_DISPATCH_REQUEST_CLIENT_REQ_ID":           11,
		"PHYSICAL_CARD_DISPATCH_REQUEST_DETAILS":                 12,
		"PHYSICAL_CARD_DISPATCH_REQUEST_SUB_STATUS":              13,
		"PHYSICAL_CARD_DISPATCH_REQUEST_CURRENT_STAGE":           14,
	}
)

func (x PhysicalCardDispatchRequestFieldMask) Enum() *PhysicalCardDispatchRequestFieldMask {
	p := new(PhysicalCardDispatchRequestFieldMask)
	*p = x
	return p
}

func (x PhysicalCardDispatchRequestFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PhysicalCardDispatchRequestFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_physical_card_dispatch_request_proto_enumTypes[0].Descriptor()
}

func (PhysicalCardDispatchRequestFieldMask) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_physical_card_dispatch_request_proto_enumTypes[0]
}

func (x PhysicalCardDispatchRequestFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PhysicalCardDispatchRequestFieldMask.Descriptor instead.
func (PhysicalCardDispatchRequestFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_physical_card_dispatch_request_proto_rawDescGZIP(), []int{0}
}

type PhysicalCardDispatchRequest_RequestType int32

const (
	PhysicalCardDispatchRequest_REQUEST_TYPE_UNSPECIFIED PhysicalCardDispatchRequest_RequestType = 0
	// All the request placed by user until the first successful request come in this category
	PhysicalCardDispatchRequest_REQUEST_TYPE_FIRST_REQUEST PhysicalCardDispatchRequest_RequestType = 1
	// All the request after the first successful request will come in this category
	PhysicalCardDispatchRequest_REQUEST_TYPE_REPLACEMENT_REQUEST PhysicalCardDispatchRequest_RequestType = 2
)

// Enum value maps for PhysicalCardDispatchRequest_RequestType.
var (
	PhysicalCardDispatchRequest_RequestType_name = map[int32]string{
		0: "REQUEST_TYPE_UNSPECIFIED",
		1: "REQUEST_TYPE_FIRST_REQUEST",
		2: "REQUEST_TYPE_REPLACEMENT_REQUEST",
	}
	PhysicalCardDispatchRequest_RequestType_value = map[string]int32{
		"REQUEST_TYPE_UNSPECIFIED":         0,
		"REQUEST_TYPE_FIRST_REQUEST":       1,
		"REQUEST_TYPE_REPLACEMENT_REQUEST": 2,
	}
)

func (x PhysicalCardDispatchRequest_RequestType) Enum() *PhysicalCardDispatchRequest_RequestType {
	p := new(PhysicalCardDispatchRequest_RequestType)
	*p = x
	return p
}

func (x PhysicalCardDispatchRequest_RequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PhysicalCardDispatchRequest_RequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_physical_card_dispatch_request_proto_enumTypes[1].Descriptor()
}

func (PhysicalCardDispatchRequest_RequestType) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_physical_card_dispatch_request_proto_enumTypes[1]
}

func (x PhysicalCardDispatchRequest_RequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PhysicalCardDispatchRequest_RequestType.Descriptor instead.
func (PhysicalCardDispatchRequest_RequestType) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_physical_card_dispatch_request_proto_rawDescGZIP(), []int{0, 0}
}

type PhysicalCardDispatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary identifier for physical card dispatch requests.
	// This is an UUID generated by us and used as primary key for the table
	// Any retry-able error is generally associated with
	// same set of request parameters and doesn't change the id.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Primary key to the card database model. Internal to Epifi.
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// The state of the request in the State Machine.
	// A sample state machines:
	// case 1: QUEUED -> INITIATED -> SUCCESS
	// Happy case. Request was successful at the vendor.
	//
	// QUEUED -> MANUAL_INTERVENTION
	// Request was retried multiple times till retry limit was reached.
	// All the retry attempts to initiate the dispatch request failed.
	//
	// QUEUED -> INITIATED -> FAILED
	// Request was initiated successfully but failed due to an error.
	//
	// QUEUED -> INITIATED -> MANUAL_INTERVENTION
	// Request was initiated successfully. All the tries to fetch the status
	// failed.
	State RequestState `protobuf:"varint,3,opt,name=state,proto3,enum=card.provisioning.RequestState" json:"state,omitempty"`
	// Number of retries already made for the request. If the request
	// is in QUEUED state, an attempt to call the Vendor increments the retries.
	//
	// If the request is in INITIATED state, an attempt to fetch the status also
	// increments the retries.
	Retries uint32 `protobuf:"varint,4,opt,name=retries,proto3" json:"retries,omitempty"`
	// Unique identifier to the request made for card dispatch for the vendor.
	// Any followup on state changes is done using
	// this id as a reference. Any retry-able error is generally associated with
	// same set of request parameters and doesn't change the request id.
	RequestId string `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// internal code mapping of the failure response code sent by vendor
	FailureResponseCode string `protobuf:"bytes,6,opt,name=failure_response_code,json=failureResponseCode,proto3" json:"failure_response_code,omitempty"`
	// failure response reason sent by vendor
	FailureResponseReason string `protobuf:"bytes,7,opt,name=failure_response_reason,json=failureResponseReason,proto3" json:"failure_response_reason,omitempty"`
	// card creation timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// card creation timestamp
	UpdatedAt   *timestamppb.Timestamp                                          `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt   *timestamppb.Timestamp                                          `protobuf:"bytes,10,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	ClientReqId string                                                          `protobuf:"bytes,11,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	Details     *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails `protobuf:"bytes,12,opt,name=details,proto3" json:"details,omitempty"`
	// sub_status is the status of last executed stage in physical debit card dispatch request flow
	SubStatus RequestSubStatus `protobuf:"varint,13,opt,name=sub_status,json=subStatus,proto3,enum=card.provisioning.RequestSubStatus" json:"sub_status,omitempty"`
	// last executed stage in physical debit card dispatch request flow
	CurrentStage DCRequestStage `protobuf:"varint,14,opt,name=current_stage,json=currentStage,proto3,enum=card.provisioning.DCRequestStage" json:"current_stage,omitempty"`
}

func (x *PhysicalCardDispatchRequest) Reset() {
	*x = PhysicalCardDispatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhysicalCardDispatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhysicalCardDispatchRequest) ProtoMessage() {}

func (x *PhysicalCardDispatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhysicalCardDispatchRequest.ProtoReflect.Descriptor instead.
func (*PhysicalCardDispatchRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_physical_card_dispatch_request_proto_rawDescGZIP(), []int{0}
}

func (x *PhysicalCardDispatchRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PhysicalCardDispatchRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *PhysicalCardDispatchRequest) GetState() RequestState {
	if x != nil {
		return x.State
	}
	return RequestState_REQUEST_STATE_UNSPECIFIED
}

func (x *PhysicalCardDispatchRequest) GetRetries() uint32 {
	if x != nil {
		return x.Retries
	}
	return 0
}

func (x *PhysicalCardDispatchRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PhysicalCardDispatchRequest) GetFailureResponseCode() string {
	if x != nil {
		return x.FailureResponseCode
	}
	return ""
}

func (x *PhysicalCardDispatchRequest) GetFailureResponseReason() string {
	if x != nil {
		return x.FailureResponseReason
	}
	return ""
}

func (x *PhysicalCardDispatchRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PhysicalCardDispatchRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *PhysicalCardDispatchRequest) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *PhysicalCardDispatchRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *PhysicalCardDispatchRequest) GetDetails() *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *PhysicalCardDispatchRequest) GetSubStatus() RequestSubStatus {
	if x != nil {
		return x.SubStatus
	}
	return RequestSubStatus_REQUEST_SUB_STATUS_UNSPECIFIED
}

func (x *PhysicalCardDispatchRequest) GetCurrentStage() DCRequestStage {
	if x != nil {
		return x.CurrentStage
	}
	return DCRequestStage_DC_REQUEST_STAGE_UNSPECIFIED
}

// Generic struct to be followed for any rewards offer details, to be triggered for charges reversal for debit cards,
// reversal can be triggered for any debit card related charge for example - physical debit card charge
type ChargesReversalRewardDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReversalRewardOfferId string       `protobuf:"bytes,1,opt,name=reversal_reward_offer_id,json=reversalRewardOfferId,proto3" json:"reversal_reward_offer_id,omitempty"`
	ReversalRewardEventId string       `protobuf:"bytes,2,opt,name=reversal_reward_event_id,json=reversalRewardEventId,proto3" json:"reversal_reward_event_id,omitempty"`
	ReversalRewardAmount  *money.Money `protobuf:"bytes,3,opt,name=reversal_reward_amount,json=reversalRewardAmount,proto3" json:"reversal_reward_amount,omitempty"`
}

func (x *ChargesReversalRewardDetails) Reset() {
	*x = ChargesReversalRewardDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChargesReversalRewardDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChargesReversalRewardDetails) ProtoMessage() {}

func (x *ChargesReversalRewardDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChargesReversalRewardDetails.ProtoReflect.Descriptor instead.
func (*ChargesReversalRewardDetails) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_physical_card_dispatch_request_proto_rawDescGZIP(), []int{1}
}

func (x *ChargesReversalRewardDetails) GetReversalRewardOfferId() string {
	if x != nil {
		return x.ReversalRewardOfferId
	}
	return ""
}

func (x *ChargesReversalRewardDetails) GetReversalRewardEventId() string {
	if x != nil {
		return x.ReversalRewardEventId
	}
	return ""
}

func (x *ChargesReversalRewardDetails) GetReversalRewardAmount() *money.Money {
	if x != nil {
		return x.ReversalRewardAmount
	}
	return nil
}

type PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client_req_id associated with fund transfer in physical debit card charges flow
	//
	// Deprecated: Marked as deprecated in api/card/provisioning/physical_card_dispatch_request.proto.
	FundTransferClientReqId string `protobuf:"bytes,1,opt,name=fund_transfer_client_req_id,json=fundTransferClientReqId,proto3" json:"fund_transfer_client_req_id,omitempty"`
	// details of reward triggered for reversal of physical card charges
	ChargesReversalRewardDetails *ChargesReversalRewardDetails `protobuf:"bytes,2,opt,name=charges_reversal_reward_details,json=chargesReversalRewardDetails,proto3" json:"charges_reversal_reward_details,omitempty"`
	// amount chargeable for the physical card
	Amount *money.Money `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// RequestType implies whether a particular physical card dispatch request was placed for the first by an actor,
	RequestType PhysicalCardDispatchRequest_RequestType `protobuf:"varint,4,opt,name=request_type,json=requestType,proto3,enum=card.provisioning.PhysicalCardDispatchRequest_RequestType" json:"request_type,omitempty"`
	// Types that are assignable to ChargesCollectionMetaData:
	//
	//	*PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionWithChargesApiInfo
	//	*PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionWithPayInfo
	ChargesCollectionMetaData isPhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionMetaData `protobuf_oneof:"ChargesCollectionMetaData"`
}

func (x *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) Reset() {
	*x = PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) ProtoMessage() {}

func (x *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails.ProtoReflect.Descriptor instead.
func (*PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_physical_card_dispatch_request_proto_rawDescGZIP(), []int{0, 0}
}

// Deprecated: Marked as deprecated in api/card/provisioning/physical_card_dispatch_request.proto.
func (x *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) GetFundTransferClientReqId() string {
	if x != nil {
		return x.FundTransferClientReqId
	}
	return ""
}

func (x *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) GetChargesReversalRewardDetails() *ChargesReversalRewardDetails {
	if x != nil {
		return x.ChargesReversalRewardDetails
	}
	return nil
}

func (x *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) GetRequestType() PhysicalCardDispatchRequest_RequestType {
	if x != nil {
		return x.RequestType
	}
	return PhysicalCardDispatchRequest_REQUEST_TYPE_UNSPECIFIED
}

func (m *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) GetChargesCollectionMetaData() isPhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionMetaData {
	if m != nil {
		return m.ChargesCollectionMetaData
	}
	return nil
}

func (x *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) GetChargesCollectionWithChargesApiInfo() *PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo {
	if x, ok := x.GetChargesCollectionMetaData().(*PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionWithChargesApiInfo); ok {
		return x.ChargesCollectionWithChargesApiInfo
	}
	return nil
}

func (x *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) GetChargesCollectionWithPayInfo() *PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo {
	if x, ok := x.GetChargesCollectionMetaData().(*PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionWithPayInfo); ok {
		return x.ChargesCollectionWithPayInfo
	}
	return nil
}

type isPhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionMetaData interface {
	isPhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionMetaData()
}

type PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionWithChargesApiInfo struct {
	ChargesCollectionWithChargesApiInfo *PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo `protobuf:"bytes,6,opt,name=charges_collection_with_charges_api_info,json=chargesCollectionWithChargesApiInfo,proto3,oneof"`
}

type PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionWithPayInfo struct {
	ChargesCollectionWithPayInfo *PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo `protobuf:"bytes,7,opt,name=charges_collection_with_pay_info,json=chargesCollectionWithPayInfo,proto3,oneof"`
}

func (*PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionWithChargesApiInfo) isPhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionMetaData() {
}

func (*PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionWithPayInfo) isPhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionMetaData() {
}

type PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// below fields are specific to charges collection APIs,
	// and will be used to post a request for charges collection at vendor.
	// VG RPC ref: vendorgateway.openbanking.card.provisioning.CardProvisioning/CollectDCIssuanceFee
	VendorRequestId string `protobuf:"bytes,1,opt,name=vendor_request_id,json=vendorRequestId,proto3" json:"vendor_request_id,omitempty"`
	TransactionId   string `protobuf:"bytes,2,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
}

func (x *PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo) Reset() {
	*x = PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo) ProtoMessage() {}

func (x *PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo.ProtoReflect.Descriptor instead.
func (*PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_physical_card_dispatch_request_proto_rawDescGZIP(), []int{0, 1}
}

func (x *PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo) GetVendorRequestId() string {
	if x != nil {
		return x.VendorRequestId
	}
	return ""
}

func (x *PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

type PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FundTransferClintRequestId string `protobuf:"bytes,1,opt,name=fund_transfer_clint_request_id,json=fundTransferClintRequestId,proto3" json:"fund_transfer_clint_request_id,omitempty"`
}

func (x *PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo) Reset() {
	*x = PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo) ProtoMessage() {}

func (x *PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo.ProtoReflect.Descriptor instead.
func (*PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_physical_card_dispatch_request_proto_rawDescGZIP(), []int{0, 2}
}

func (x *PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo) GetFundTransferClintRequestId() string {
	if x != nil {
		return x.FundTransferClintRequestId
	}
	return ""
}

var File_api_card_provisioning_physical_card_dispatch_request_proto protoreflect.FileDescriptor

var file_api_card_provisioning_physical_card_dispatch_request_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x1a,
	0x28, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x90, 0x0e, 0x0a, 0x1b, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c,
	0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15,
	0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x66, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x36, 0x0a, 0x17, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x15, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x6b, 0x0a,
	0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x51,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69,
	0x6e, 0x67, 0x2e, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x44,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50,
	0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x73, 0x75,
	0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69,
	0x6e, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x09, 0x73, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x46,
	0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x43, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x1a, 0xcc, 0x05, 0x0a, 0x22, 0x50, 0x68, 0x79, 0x73, 0x69,
	0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x40, 0x0a,
	0x1b, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x17, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12,
	0x76, 0x0a, 0x1f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x1c, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x5d, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x68,
	0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0xab, 0x01, 0x0a, 0x28, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x61, 0x70, 0x69, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63,
	0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x41, 0x70, 0x69, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x23, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69,
	0x74, 0x68, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x41, 0x70, 0x69, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x95, 0x01, 0x0a, 0x20, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x70, 0x61, 0x79,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e,
	0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74,
	0x68, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x1c, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74,
	0x68, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x1b, 0x0a, 0x19, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74,
	0x61, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x78, 0x0a, 0x23, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x41, 0x70, 0x69, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x11,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x1a,
	0x62, 0x0a, 0x1c, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x42, 0x0a, 0x1e, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x5f, 0x63, 0x6c, 0x69, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x43, 0x6c, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x22, 0x71, 0x0a, 0x0b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x46, 0x49, 0x52, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x01,
	0x12, 0x24, 0x0a, 0x20, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x52, 0x45, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x10, 0x02, 0x22, 0xda, 0x01, 0x0a, 0x1c, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x72, 0x65, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x37, 0x0a, 0x18, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x16, 0x72, 0x65, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x72,
	0x65, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x2a, 0xfb, 0x05, 0x0a, 0x24, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c,
	0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x39, 0x0a, 0x35,
	0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x49,
	0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x48, 0x59, 0x53, 0x49,
	0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49,
	0x44, 0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x02, 0x12, 0x2a, 0x0a,
	0x26, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44,
	0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x10, 0x03, 0x12, 0x2d, 0x0a, 0x29, 0x50, 0x48, 0x59,
	0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x41,
	0x54, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x38, 0x0a, 0x34, 0x50, 0x48, 0x59, 0x53,
	0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54,
	0x43, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55,
	0x52, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x10, 0x05, 0x12, 0x3a, 0x0a, 0x36, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x25,
	0x0a, 0x21, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x49, 0x44, 0x10, 0x07, 0x12, 0x2d, 0x0a, 0x29, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41,
	0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f,
	0x41, 0x54, 0x10, 0x08, 0x12, 0x2d, 0x0a, 0x29, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41,
	0x54, 0x10, 0x09, 0x12, 0x2d, 0x0a, 0x29, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54,
	0x10, 0x0a, 0x12, 0x30, 0x0a, 0x2c, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x5f,
	0x49, 0x44, 0x10, 0x0b, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x0c,
	0x12, 0x2d, 0x0a, 0x29, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x0d, 0x12,
	0x30, 0x0a, 0x2c, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x10,
	0x0e, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e,
	0x67, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61,
	0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_provisioning_physical_card_dispatch_request_proto_rawDescOnce sync.Once
	file_api_card_provisioning_physical_card_dispatch_request_proto_rawDescData = file_api_card_provisioning_physical_card_dispatch_request_proto_rawDesc
)

func file_api_card_provisioning_physical_card_dispatch_request_proto_rawDescGZIP() []byte {
	file_api_card_provisioning_physical_card_dispatch_request_proto_rawDescOnce.Do(func() {
		file_api_card_provisioning_physical_card_dispatch_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_provisioning_physical_card_dispatch_request_proto_rawDescData)
	})
	return file_api_card_provisioning_physical_card_dispatch_request_proto_rawDescData
}

var file_api_card_provisioning_physical_card_dispatch_request_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_card_provisioning_physical_card_dispatch_request_proto_goTypes = []interface{}{
	(PhysicalCardDispatchRequestFieldMask)(0),                               // 0: card.provisioning.PhysicalCardDispatchRequestFieldMask
	(PhysicalCardDispatchRequest_RequestType)(0),                            // 1: card.provisioning.PhysicalCardDispatchRequest.RequestType
	(*PhysicalCardDispatchRequest)(nil),                                     // 2: card.provisioning.PhysicalCardDispatchRequest
	(*ChargesReversalRewardDetails)(nil),                                    // 3: card.provisioning.ChargesReversalRewardDetails
	(*PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails)(nil),  // 4: card.provisioning.PhysicalCardDispatchRequest.PhysicalCardDispatchRequestDetails
	(*PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo)(nil), // 5: card.provisioning.PhysicalCardDispatchRequest.ChargesCollectionWithChargesApiInfo
	(*PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo)(nil),        // 6: card.provisioning.PhysicalCardDispatchRequest.ChargesCollectionWithPayInfo
	(RequestState)(0),             // 7: card.provisioning.RequestState
	(*timestamppb.Timestamp)(nil), // 8: google.protobuf.Timestamp
	(RequestSubStatus)(0),         // 9: card.provisioning.RequestSubStatus
	(DCRequestStage)(0),           // 10: card.provisioning.DCRequestStage
	(*money.Money)(nil),           // 11: google.type.Money
}
var file_api_card_provisioning_physical_card_dispatch_request_proto_depIdxs = []int32{
	7,  // 0: card.provisioning.PhysicalCardDispatchRequest.state:type_name -> card.provisioning.RequestState
	8,  // 1: card.provisioning.PhysicalCardDispatchRequest.created_at:type_name -> google.protobuf.Timestamp
	8,  // 2: card.provisioning.PhysicalCardDispatchRequest.updated_at:type_name -> google.protobuf.Timestamp
	8,  // 3: card.provisioning.PhysicalCardDispatchRequest.deleted_at:type_name -> google.protobuf.Timestamp
	4,  // 4: card.provisioning.PhysicalCardDispatchRequest.details:type_name -> card.provisioning.PhysicalCardDispatchRequest.PhysicalCardDispatchRequestDetails
	9,  // 5: card.provisioning.PhysicalCardDispatchRequest.sub_status:type_name -> card.provisioning.RequestSubStatus
	10, // 6: card.provisioning.PhysicalCardDispatchRequest.current_stage:type_name -> card.provisioning.DCRequestStage
	11, // 7: card.provisioning.ChargesReversalRewardDetails.reversal_reward_amount:type_name -> google.type.Money
	3,  // 8: card.provisioning.PhysicalCardDispatchRequest.PhysicalCardDispatchRequestDetails.charges_reversal_reward_details:type_name -> card.provisioning.ChargesReversalRewardDetails
	11, // 9: card.provisioning.PhysicalCardDispatchRequest.PhysicalCardDispatchRequestDetails.amount:type_name -> google.type.Money
	1,  // 10: card.provisioning.PhysicalCardDispatchRequest.PhysicalCardDispatchRequestDetails.request_type:type_name -> card.provisioning.PhysicalCardDispatchRequest.RequestType
	5,  // 11: card.provisioning.PhysicalCardDispatchRequest.PhysicalCardDispatchRequestDetails.charges_collection_with_charges_api_info:type_name -> card.provisioning.PhysicalCardDispatchRequest.ChargesCollectionWithChargesApiInfo
	6,  // 12: card.provisioning.PhysicalCardDispatchRequest.PhysicalCardDispatchRequestDetails.charges_collection_with_pay_info:type_name -> card.provisioning.PhysicalCardDispatchRequest.ChargesCollectionWithPayInfo
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_api_card_provisioning_physical_card_dispatch_request_proto_init() }
func file_api_card_provisioning_physical_card_dispatch_request_proto_init() {
	if File_api_card_provisioning_physical_card_dispatch_request_proto != nil {
		return
	}
	file_api_card_provisioning_provisioning_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhysicalCardDispatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChargesReversalRewardDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionWithChargesApiInfo)(nil),
		(*PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails_ChargesCollectionWithPayInfo)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_provisioning_physical_card_dispatch_request_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_provisioning_physical_card_dispatch_request_proto_goTypes,
		DependencyIndexes: file_api_card_provisioning_physical_card_dispatch_request_proto_depIdxs,
		EnumInfos:         file_api_card_provisioning_physical_card_dispatch_request_proto_enumTypes,
		MessageInfos:      file_api_card_provisioning_physical_card_dispatch_request_proto_msgTypes,
	}.Build()
	File_api_card_provisioning_physical_card_dispatch_request_proto = out.File
	file_api_card_provisioning_physical_card_dispatch_request_proto_rawDesc = nil
	file_api_card_provisioning_physical_card_dispatch_request_proto_goTypes = nil
	file_api_card_provisioning_physical_card_dispatch_request_proto_depIdxs = nil
}
