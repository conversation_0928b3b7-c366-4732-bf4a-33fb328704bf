// Code generated by MockGen. DO NOT EDIT.
// Source: api/card/provisioning/card_consumer_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	notification "github.com/epifi/gamma/api/auth/notification"
	provisioning "github.com/epifi/gamma/api/card/provisioning"
	event "github.com/epifi/gamma/api/user/event"
	onboarding "github.com/epifi/gamma/api/user/onboarding"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCardConsumerClient is a mock of CardConsumerClient interface.
type MockCardConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockCardConsumerClientMockRecorder
}

// MockCardConsumerClientMockRecorder is the mock recorder for MockCardConsumerClient.
type MockCardConsumerClientMockRecorder struct {
	mock *MockCardConsumerClient
}

// NewMockCardConsumerClient creates a new mock instance.
func NewMockCardConsumerClient(ctrl *gomock.Controller) *MockCardConsumerClient {
	mock := &MockCardConsumerClient{ctrl: ctrl}
	mock.recorder = &MockCardConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardConsumerClient) EXPECT() *MockCardConsumerClientMockRecorder {
	return m.recorder
}

// GetTrackingDetails mocks base method.
func (m *MockCardConsumerClient) GetTrackingDetails(ctx context.Context, in *provisioning.GetTrackingDetailsRequest, opts ...grpc.CallOption) (*provisioning.GetTrackingDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTrackingDetails", varargs...)
	ret0, _ := ret[0].(*provisioning.GetTrackingDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTrackingDetails indicates an expected call of GetTrackingDetails.
func (mr *MockCardConsumerClientMockRecorder) GetTrackingDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTrackingDetails", reflect.TypeOf((*MockCardConsumerClient)(nil).GetTrackingDetails), varargs...)
}

// OrderPhysicalCardCriticalNotification mocks base method.
func (m *MockCardConsumerClient) OrderPhysicalCardCriticalNotification(ctx context.Context, in *provisioning.OrderPhysicalCardCriticalNotificationRequest, opts ...grpc.CallOption) (*provisioning.OrderPhysicalCardCriticalNotificationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OrderPhysicalCardCriticalNotification", varargs...)
	ret0, _ := ret[0].(*provisioning.OrderPhysicalCardCriticalNotificationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OrderPhysicalCardCriticalNotification indicates an expected call of OrderPhysicalCardCriticalNotification.
func (mr *MockCardConsumerClientMockRecorder) OrderPhysicalCardCriticalNotification(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OrderPhysicalCardCriticalNotification", reflect.TypeOf((*MockCardConsumerClient)(nil).OrderPhysicalCardCriticalNotification), varargs...)
}

// ProcessAuthFactorUpdateEvent mocks base method.
func (m *MockCardConsumerClient) ProcessAuthFactorUpdateEvent(ctx context.Context, in *notification.AuthFactorUpdateEvent, opts ...grpc.CallOption) (*provisioning.ProcessAuthFactorUpdateEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessAuthFactorUpdateEvent", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessAuthFactorUpdateEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAuthFactorUpdateEvent indicates an expected call of ProcessAuthFactorUpdateEvent.
func (mr *MockCardConsumerClientMockRecorder) ProcessAuthFactorUpdateEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAuthFactorUpdateEvent", reflect.TypeOf((*MockCardConsumerClient)(nil).ProcessAuthFactorUpdateEvent), varargs...)
}

// ProcessCardAmcChargesEligibleUserFile mocks base method.
func (m *MockCardConsumerClient) ProcessCardAmcChargesEligibleUserFile(ctx context.Context, in *provisioning.ProcessCardAmcChargesEligibleUserFileRequest, opts ...grpc.CallOption) (*provisioning.ProcessCardAmcChargesEligibleUserFileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardAmcChargesEligibleUserFile", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessCardAmcChargesEligibleUserFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardAmcChargesEligibleUserFile indicates an expected call of ProcessCardAmcChargesEligibleUserFile.
func (mr *MockCardConsumerClientMockRecorder) ProcessCardAmcChargesEligibleUserFile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardAmcChargesEligibleUserFile", reflect.TypeOf((*MockCardConsumerClient)(nil).ProcessCardAmcChargesEligibleUserFile), varargs...)
}

// ProcessCardCreation mocks base method.
func (m *MockCardConsumerClient) ProcessCardCreation(ctx context.Context, in *provisioning.ProcessCardCreationRequest, opts ...grpc.CallOption) (*provisioning.ProcessCardCreationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardCreation", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessCardCreationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardCreation indicates an expected call of ProcessCardCreation.
func (mr *MockCardConsumerClientMockRecorder) ProcessCardCreation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardCreation", reflect.TypeOf((*MockCardConsumerClient)(nil).ProcessCardCreation), varargs...)
}

// ProcessCardDeliveredEvent mocks base method.
func (m *MockCardConsumerClient) ProcessCardDeliveredEvent(ctx context.Context, in *provisioning.ProcessCardDeliveredEventRequest, opts ...grpc.CallOption) (*provisioning.ProcessCardDeliveredEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardDeliveredEvent", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessCardDeliveredEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardDeliveredEvent indicates an expected call of ProcessCardDeliveredEvent.
func (mr *MockCardConsumerClientMockRecorder) ProcessCardDeliveredEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardDeliveredEvent", reflect.TypeOf((*MockCardConsumerClient)(nil).ProcessCardDeliveredEvent), varargs...)
}

// ProcessCardDeliveryDelayEvent mocks base method.
func (m *MockCardConsumerClient) ProcessCardDeliveryDelayEvent(ctx context.Context, in *provisioning.ProcessCardDeliveryDelayEventRequest, opts ...grpc.CallOption) (*provisioning.ProcessCardDeliveryDelayEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardDeliveryDelayEvent", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessCardDeliveryDelayEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardDeliveryDelayEvent indicates an expected call of ProcessCardDeliveryDelayEvent.
func (mr *MockCardConsumerClientMockRecorder) ProcessCardDeliveryDelayEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardDeliveryDelayEvent", reflect.TypeOf((*MockCardConsumerClient)(nil).ProcessCardDeliveryDelayEvent), varargs...)
}

// ProcessCardDispatch mocks base method.
func (m *MockCardConsumerClient) ProcessCardDispatch(ctx context.Context, in *provisioning.ProcessCardDispatchRequest, opts ...grpc.CallOption) (*provisioning.ProcessCardDispatchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardDispatch", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessCardDispatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardDispatch indicates an expected call of ProcessCardDispatch.
func (mr *MockCardConsumerClientMockRecorder) ProcessCardDispatch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardDispatch", reflect.TypeOf((*MockCardConsumerClient)(nil).ProcessCardDispatch), varargs...)
}

// ProcessCardOnboardingEvent mocks base method.
func (m *MockCardConsumerClient) ProcessCardOnboardingEvent(ctx context.Context, in *onboarding.OnboardingStageUpdate, opts ...grpc.CallOption) (*provisioning.ProcessCardOnboardingEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardOnboardingEvent", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessCardOnboardingEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardOnboardingEvent indicates an expected call of ProcessCardOnboardingEvent.
func (mr *MockCardConsumerClientMockRecorder) ProcessCardOnboardingEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardOnboardingEvent", reflect.TypeOf((*MockCardConsumerClient)(nil).ProcessCardOnboardingEvent), varargs...)
}

// ProcessCardPiCreation mocks base method.
func (m *MockCardConsumerClient) ProcessCardPiCreation(ctx context.Context, in *provisioning.ProcessCardPiCreationRequest, opts ...grpc.CallOption) (*provisioning.ProcessCardPiCreationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardPiCreation", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessCardPiCreationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardPiCreation indicates an expected call of ProcessCardPiCreation.
func (mr *MockCardConsumerClientMockRecorder) ProcessCardPiCreation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardPiCreation", reflect.TypeOf((*MockCardConsumerClient)(nil).ProcessCardPiCreation), varargs...)
}

// ProcessCardPinSetEvent mocks base method.
func (m *MockCardConsumerClient) ProcessCardPinSetEvent(ctx context.Context, in *provisioning.ProcessCardPinSetEventRequest, opts ...grpc.CallOption) (*provisioning.ProcessCardPinSetEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardPinSetEvent", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessCardPinSetEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardPinSetEvent indicates an expected call of ProcessCardPinSetEvent.
func (mr *MockCardConsumerClientMockRecorder) ProcessCardPinSetEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardPinSetEvent", reflect.TypeOf((*MockCardConsumerClient)(nil).ProcessCardPinSetEvent), varargs...)
}

// ProcessCardRenewalEvent mocks base method.
func (m *MockCardConsumerClient) ProcessCardRenewalEvent(ctx context.Context, in *provisioning.ProcessCardRenewalEventRequest, opts ...grpc.CallOption) (*provisioning.ProcessCardRenewalEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardRenewalEvent", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessCardRenewalEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardRenewalEvent indicates an expected call of ProcessCardRenewalEvent.
func (mr *MockCardConsumerClientMockRecorder) ProcessCardRenewalEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardRenewalEvent", reflect.TypeOf((*MockCardConsumerClient)(nil).ProcessCardRenewalEvent), varargs...)
}

// ProcessCardShipmentRegisterEvent mocks base method.
func (m *MockCardConsumerClient) ProcessCardShipmentRegisterEvent(ctx context.Context, in *provisioning.ProcessCardShipmentRegisterEventRequest, opts ...grpc.CallOption) (*provisioning.ProcessCardShipmentRegisterEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardShipmentRegisterEvent", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessCardShipmentRegisterEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardShipmentRegisterEvent indicates an expected call of ProcessCardShipmentRegisterEvent.
func (mr *MockCardConsumerClientMockRecorder) ProcessCardShipmentRegisterEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardShipmentRegisterEvent", reflect.TypeOf((*MockCardConsumerClient)(nil).ProcessCardShipmentRegisterEvent), varargs...)
}

// ProcessCardsDispatchedCsvFile mocks base method.
func (m *MockCardConsumerClient) ProcessCardsDispatchedCsvFile(ctx context.Context, in *provisioning.ProcessCardsDispatchedCsvFileRequest, opts ...grpc.CallOption) (*provisioning.ProcessCardsDispatchedCsvFileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardsDispatchedCsvFile", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessCardsDispatchedCsvFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardsDispatchedCsvFile indicates an expected call of ProcessCardsDispatchedCsvFile.
func (mr *MockCardConsumerClientMockRecorder) ProcessCardsDispatchedCsvFile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardsDispatchedCsvFile", reflect.TypeOf((*MockCardConsumerClient)(nil).ProcessCardsDispatchedCsvFile), varargs...)
}

// ProcessShippingAddressUpdateAndDispatchPhysicalCard mocks base method.
func (m *MockCardConsumerClient) ProcessShippingAddressUpdateAndDispatchPhysicalCard(ctx context.Context, in *provisioning.ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest, opts ...grpc.CallOption) (*provisioning.ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessShippingAddressUpdateAndDispatchPhysicalCard", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessShippingAddressUpdateAndDispatchPhysicalCard indicates an expected call of ProcessShippingAddressUpdateAndDispatchPhysicalCard.
func (mr *MockCardConsumerClientMockRecorder) ProcessShippingAddressUpdateAndDispatchPhysicalCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessShippingAddressUpdateAndDispatchPhysicalCard", reflect.TypeOf((*MockCardConsumerClient)(nil).ProcessShippingAddressUpdateAndDispatchPhysicalCard), varargs...)
}

// ProcessUserDevicePropertiesUpdateEvent mocks base method.
func (m *MockCardConsumerClient) ProcessUserDevicePropertiesUpdateEvent(ctx context.Context, in *event.UserDevicePropertyUpdateEvent, opts ...grpc.CallOption) (*provisioning.ProcessUserDevicePropertiesUpdateEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessUserDevicePropertiesUpdateEvent", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessUserDevicePropertiesUpdateEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessUserDevicePropertiesUpdateEvent indicates an expected call of ProcessUserDevicePropertiesUpdateEvent.
func (mr *MockCardConsumerClientMockRecorder) ProcessUserDevicePropertiesUpdateEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessUserDevicePropertiesUpdateEvent", reflect.TypeOf((*MockCardConsumerClient)(nil).ProcessUserDevicePropertiesUpdateEvent), varargs...)
}

// MockCardConsumerServer is a mock of CardConsumerServer interface.
type MockCardConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockCardConsumerServerMockRecorder
}

// MockCardConsumerServerMockRecorder is the mock recorder for MockCardConsumerServer.
type MockCardConsumerServerMockRecorder struct {
	mock *MockCardConsumerServer
}

// NewMockCardConsumerServer creates a new mock instance.
func NewMockCardConsumerServer(ctrl *gomock.Controller) *MockCardConsumerServer {
	mock := &MockCardConsumerServer{ctrl: ctrl}
	mock.recorder = &MockCardConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardConsumerServer) EXPECT() *MockCardConsumerServerMockRecorder {
	return m.recorder
}

// GetTrackingDetails mocks base method.
func (m *MockCardConsumerServer) GetTrackingDetails(arg0 context.Context, arg1 *provisioning.GetTrackingDetailsRequest) (*provisioning.GetTrackingDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTrackingDetails", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetTrackingDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTrackingDetails indicates an expected call of GetTrackingDetails.
func (mr *MockCardConsumerServerMockRecorder) GetTrackingDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTrackingDetails", reflect.TypeOf((*MockCardConsumerServer)(nil).GetTrackingDetails), arg0, arg1)
}

// OrderPhysicalCardCriticalNotification mocks base method.
func (m *MockCardConsumerServer) OrderPhysicalCardCriticalNotification(arg0 context.Context, arg1 *provisioning.OrderPhysicalCardCriticalNotificationRequest) (*provisioning.OrderPhysicalCardCriticalNotificationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OrderPhysicalCardCriticalNotification", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.OrderPhysicalCardCriticalNotificationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OrderPhysicalCardCriticalNotification indicates an expected call of OrderPhysicalCardCriticalNotification.
func (mr *MockCardConsumerServerMockRecorder) OrderPhysicalCardCriticalNotification(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OrderPhysicalCardCriticalNotification", reflect.TypeOf((*MockCardConsumerServer)(nil).OrderPhysicalCardCriticalNotification), arg0, arg1)
}

// ProcessAuthFactorUpdateEvent mocks base method.
func (m *MockCardConsumerServer) ProcessAuthFactorUpdateEvent(arg0 context.Context, arg1 *notification.AuthFactorUpdateEvent) (*provisioning.ProcessAuthFactorUpdateEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessAuthFactorUpdateEvent", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessAuthFactorUpdateEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAuthFactorUpdateEvent indicates an expected call of ProcessAuthFactorUpdateEvent.
func (mr *MockCardConsumerServerMockRecorder) ProcessAuthFactorUpdateEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAuthFactorUpdateEvent", reflect.TypeOf((*MockCardConsumerServer)(nil).ProcessAuthFactorUpdateEvent), arg0, arg1)
}

// ProcessCardAmcChargesEligibleUserFile mocks base method.
func (m *MockCardConsumerServer) ProcessCardAmcChargesEligibleUserFile(arg0 context.Context, arg1 *provisioning.ProcessCardAmcChargesEligibleUserFileRequest) (*provisioning.ProcessCardAmcChargesEligibleUserFileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardAmcChargesEligibleUserFile", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessCardAmcChargesEligibleUserFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardAmcChargesEligibleUserFile indicates an expected call of ProcessCardAmcChargesEligibleUserFile.
func (mr *MockCardConsumerServerMockRecorder) ProcessCardAmcChargesEligibleUserFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardAmcChargesEligibleUserFile", reflect.TypeOf((*MockCardConsumerServer)(nil).ProcessCardAmcChargesEligibleUserFile), arg0, arg1)
}

// ProcessCardCreation mocks base method.
func (m *MockCardConsumerServer) ProcessCardCreation(arg0 context.Context, arg1 *provisioning.ProcessCardCreationRequest) (*provisioning.ProcessCardCreationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardCreation", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessCardCreationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardCreation indicates an expected call of ProcessCardCreation.
func (mr *MockCardConsumerServerMockRecorder) ProcessCardCreation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardCreation", reflect.TypeOf((*MockCardConsumerServer)(nil).ProcessCardCreation), arg0, arg1)
}

// ProcessCardDeliveredEvent mocks base method.
func (m *MockCardConsumerServer) ProcessCardDeliveredEvent(arg0 context.Context, arg1 *provisioning.ProcessCardDeliveredEventRequest) (*provisioning.ProcessCardDeliveredEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardDeliveredEvent", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessCardDeliveredEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardDeliveredEvent indicates an expected call of ProcessCardDeliveredEvent.
func (mr *MockCardConsumerServerMockRecorder) ProcessCardDeliveredEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardDeliveredEvent", reflect.TypeOf((*MockCardConsumerServer)(nil).ProcessCardDeliveredEvent), arg0, arg1)
}

// ProcessCardDeliveryDelayEvent mocks base method.
func (m *MockCardConsumerServer) ProcessCardDeliveryDelayEvent(arg0 context.Context, arg1 *provisioning.ProcessCardDeliveryDelayEventRequest) (*provisioning.ProcessCardDeliveryDelayEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardDeliveryDelayEvent", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessCardDeliveryDelayEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardDeliveryDelayEvent indicates an expected call of ProcessCardDeliveryDelayEvent.
func (mr *MockCardConsumerServerMockRecorder) ProcessCardDeliveryDelayEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardDeliveryDelayEvent", reflect.TypeOf((*MockCardConsumerServer)(nil).ProcessCardDeliveryDelayEvent), arg0, arg1)
}

// ProcessCardDispatch mocks base method.
func (m *MockCardConsumerServer) ProcessCardDispatch(arg0 context.Context, arg1 *provisioning.ProcessCardDispatchRequest) (*provisioning.ProcessCardDispatchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardDispatch", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessCardDispatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardDispatch indicates an expected call of ProcessCardDispatch.
func (mr *MockCardConsumerServerMockRecorder) ProcessCardDispatch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardDispatch", reflect.TypeOf((*MockCardConsumerServer)(nil).ProcessCardDispatch), arg0, arg1)
}

// ProcessCardOnboardingEvent mocks base method.
func (m *MockCardConsumerServer) ProcessCardOnboardingEvent(arg0 context.Context, arg1 *onboarding.OnboardingStageUpdate) (*provisioning.ProcessCardOnboardingEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardOnboardingEvent", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessCardOnboardingEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardOnboardingEvent indicates an expected call of ProcessCardOnboardingEvent.
func (mr *MockCardConsumerServerMockRecorder) ProcessCardOnboardingEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardOnboardingEvent", reflect.TypeOf((*MockCardConsumerServer)(nil).ProcessCardOnboardingEvent), arg0, arg1)
}

// ProcessCardPiCreation mocks base method.
func (m *MockCardConsumerServer) ProcessCardPiCreation(arg0 context.Context, arg1 *provisioning.ProcessCardPiCreationRequest) (*provisioning.ProcessCardPiCreationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardPiCreation", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessCardPiCreationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardPiCreation indicates an expected call of ProcessCardPiCreation.
func (mr *MockCardConsumerServerMockRecorder) ProcessCardPiCreation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardPiCreation", reflect.TypeOf((*MockCardConsumerServer)(nil).ProcessCardPiCreation), arg0, arg1)
}

// ProcessCardPinSetEvent mocks base method.
func (m *MockCardConsumerServer) ProcessCardPinSetEvent(arg0 context.Context, arg1 *provisioning.ProcessCardPinSetEventRequest) (*provisioning.ProcessCardPinSetEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardPinSetEvent", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessCardPinSetEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardPinSetEvent indicates an expected call of ProcessCardPinSetEvent.
func (mr *MockCardConsumerServerMockRecorder) ProcessCardPinSetEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardPinSetEvent", reflect.TypeOf((*MockCardConsumerServer)(nil).ProcessCardPinSetEvent), arg0, arg1)
}

// ProcessCardRenewalEvent mocks base method.
func (m *MockCardConsumerServer) ProcessCardRenewalEvent(arg0 context.Context, arg1 *provisioning.ProcessCardRenewalEventRequest) (*provisioning.ProcessCardRenewalEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardRenewalEvent", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessCardRenewalEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardRenewalEvent indicates an expected call of ProcessCardRenewalEvent.
func (mr *MockCardConsumerServerMockRecorder) ProcessCardRenewalEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardRenewalEvent", reflect.TypeOf((*MockCardConsumerServer)(nil).ProcessCardRenewalEvent), arg0, arg1)
}

// ProcessCardShipmentRegisterEvent mocks base method.
func (m *MockCardConsumerServer) ProcessCardShipmentRegisterEvent(arg0 context.Context, arg1 *provisioning.ProcessCardShipmentRegisterEventRequest) (*provisioning.ProcessCardShipmentRegisterEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardShipmentRegisterEvent", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessCardShipmentRegisterEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardShipmentRegisterEvent indicates an expected call of ProcessCardShipmentRegisterEvent.
func (mr *MockCardConsumerServerMockRecorder) ProcessCardShipmentRegisterEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardShipmentRegisterEvent", reflect.TypeOf((*MockCardConsumerServer)(nil).ProcessCardShipmentRegisterEvent), arg0, arg1)
}

// ProcessCardsDispatchedCsvFile mocks base method.
func (m *MockCardConsumerServer) ProcessCardsDispatchedCsvFile(arg0 context.Context, arg1 *provisioning.ProcessCardsDispatchedCsvFileRequest) (*provisioning.ProcessCardsDispatchedCsvFileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardsDispatchedCsvFile", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessCardsDispatchedCsvFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardsDispatchedCsvFile indicates an expected call of ProcessCardsDispatchedCsvFile.
func (mr *MockCardConsumerServerMockRecorder) ProcessCardsDispatchedCsvFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardsDispatchedCsvFile", reflect.TypeOf((*MockCardConsumerServer)(nil).ProcessCardsDispatchedCsvFile), arg0, arg1)
}

// ProcessShippingAddressUpdateAndDispatchPhysicalCard mocks base method.
func (m *MockCardConsumerServer) ProcessShippingAddressUpdateAndDispatchPhysicalCard(arg0 context.Context, arg1 *provisioning.ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest) (*provisioning.ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessShippingAddressUpdateAndDispatchPhysicalCard", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessShippingAddressUpdateAndDispatchPhysicalCard indicates an expected call of ProcessShippingAddressUpdateAndDispatchPhysicalCard.
func (mr *MockCardConsumerServerMockRecorder) ProcessShippingAddressUpdateAndDispatchPhysicalCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessShippingAddressUpdateAndDispatchPhysicalCard", reflect.TypeOf((*MockCardConsumerServer)(nil).ProcessShippingAddressUpdateAndDispatchPhysicalCard), arg0, arg1)
}

// ProcessUserDevicePropertiesUpdateEvent mocks base method.
func (m *MockCardConsumerServer) ProcessUserDevicePropertiesUpdateEvent(arg0 context.Context, arg1 *event.UserDevicePropertyUpdateEvent) (*provisioning.ProcessUserDevicePropertiesUpdateEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessUserDevicePropertiesUpdateEvent", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessUserDevicePropertiesUpdateEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessUserDevicePropertiesUpdateEvent indicates an expected call of ProcessUserDevicePropertiesUpdateEvent.
func (mr *MockCardConsumerServerMockRecorder) ProcessUserDevicePropertiesUpdateEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessUserDevicePropertiesUpdateEvent", reflect.TypeOf((*MockCardConsumerServer)(nil).ProcessUserDevicePropertiesUpdateEvent), arg0, arg1)
}

// MockUnsafeCardConsumerServer is a mock of UnsafeCardConsumerServer interface.
type MockUnsafeCardConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCardConsumerServerMockRecorder
}

// MockUnsafeCardConsumerServerMockRecorder is the mock recorder for MockUnsafeCardConsumerServer.
type MockUnsafeCardConsumerServerMockRecorder struct {
	mock *MockUnsafeCardConsumerServer
}

// NewMockUnsafeCardConsumerServer creates a new mock instance.
func NewMockUnsafeCardConsumerServer(ctrl *gomock.Controller) *MockUnsafeCardConsumerServer {
	mock := &MockUnsafeCardConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCardConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCardConsumerServer) EXPECT() *MockUnsafeCardConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCardConsumerServer mocks base method.
func (m *MockUnsafeCardConsumerServer) mustEmbedUnimplementedCardConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCardConsumerServer")
}

// mustEmbedUnimplementedCardConsumerServer indicates an expected call of mustEmbedUnimplementedCardConsumerServer.
func (mr *MockUnsafeCardConsumerServerMockRecorder) mustEmbedUnimplementedCardConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCardConsumerServer", reflect.TypeOf((*MockUnsafeCardConsumerServer)(nil).mustEmbedUnimplementedCardConsumerServer))
}
