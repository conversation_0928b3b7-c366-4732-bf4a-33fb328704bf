// Code generated by MockGen. DO NOT EDIT.
// Source: api/card/provisioning/callback_consumer_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	provisioning "github.com/epifi/gamma/api/card/provisioning"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCallBackConsumerClient is a mock of CallBackConsumerClient interface.
type MockCallBackConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockCallBackConsumerClientMockRecorder
}

// MockCallBackConsumerClientMockRecorder is the mock recorder for MockCallBackConsumerClient.
type MockCallBackConsumerClientMockRecorder struct {
	mock *MockCallBackConsumerClient
}

// NewMockCallBackConsumerClient creates a new mock instance.
func NewMockCallBackConsumerClient(ctrl *gomock.Controller) *MockCallBackConsumerClient {
	mock := &MockCallBackConsumerClient{ctrl: ctrl}
	mock.recorder = &MockCallBackConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCallBackConsumerClient) EXPECT() *MockCallBackConsumerClientMockRecorder {
	return m.recorder
}

// ProcessCardCreationCallBack mocks base method.
func (m *MockCallBackConsumerClient) ProcessCardCreationCallBack(ctx context.Context, in *provisioning.ProcessCardCreationCallBackRequest, opts ...grpc.CallOption) (*provisioning.ProcessCardCreationCallBackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardCreationCallBack", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessCardCreationCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardCreationCallBack indicates an expected call of ProcessCardCreationCallBack.
func (mr *MockCallBackConsumerClientMockRecorder) ProcessCardCreationCallBack(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardCreationCallBack", reflect.TypeOf((*MockCallBackConsumerClient)(nil).ProcessCardCreationCallBack), varargs...)
}

// ProcessCardTrackingCallback mocks base method.
func (m *MockCallBackConsumerClient) ProcessCardTrackingCallback(ctx context.Context, in *provisioning.ProcessCardTrackingCallbackRequest, opts ...grpc.CallOption) (*provisioning.ProcessCardTrackingCallbackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardTrackingCallback", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessCardTrackingCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardTrackingCallback indicates an expected call of ProcessCardTrackingCallback.
func (mr *MockCallBackConsumerClientMockRecorder) ProcessCardTrackingCallback(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardTrackingCallback", reflect.TypeOf((*MockCallBackConsumerClient)(nil).ProcessCardTrackingCallback), varargs...)
}

// ProcessDispatchPhysicalCardCallback mocks base method.
func (m *MockCallBackConsumerClient) ProcessDispatchPhysicalCardCallback(ctx context.Context, in *provisioning.ProcessDispatchPhysicalCardCallbackRequest, opts ...grpc.CallOption) (*provisioning.ProcessDispatchPhysicalCardCallbackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessDispatchPhysicalCardCallback", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessDispatchPhysicalCardCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessDispatchPhysicalCardCallback indicates an expected call of ProcessDispatchPhysicalCardCallback.
func (mr *MockCallBackConsumerClientMockRecorder) ProcessDispatchPhysicalCardCallback(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessDispatchPhysicalCardCallback", reflect.TypeOf((*MockCallBackConsumerClient)(nil).ProcessDispatchPhysicalCardCallback), varargs...)
}

// MockCallBackConsumerServer is a mock of CallBackConsumerServer interface.
type MockCallBackConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockCallBackConsumerServerMockRecorder
}

// MockCallBackConsumerServerMockRecorder is the mock recorder for MockCallBackConsumerServer.
type MockCallBackConsumerServerMockRecorder struct {
	mock *MockCallBackConsumerServer
}

// NewMockCallBackConsumerServer creates a new mock instance.
func NewMockCallBackConsumerServer(ctrl *gomock.Controller) *MockCallBackConsumerServer {
	mock := &MockCallBackConsumerServer{ctrl: ctrl}
	mock.recorder = &MockCallBackConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCallBackConsumerServer) EXPECT() *MockCallBackConsumerServerMockRecorder {
	return m.recorder
}

// ProcessCardCreationCallBack mocks base method.
func (m *MockCallBackConsumerServer) ProcessCardCreationCallBack(arg0 context.Context, arg1 *provisioning.ProcessCardCreationCallBackRequest) (*provisioning.ProcessCardCreationCallBackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardCreationCallBack", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessCardCreationCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardCreationCallBack indicates an expected call of ProcessCardCreationCallBack.
func (mr *MockCallBackConsumerServerMockRecorder) ProcessCardCreationCallBack(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardCreationCallBack", reflect.TypeOf((*MockCallBackConsumerServer)(nil).ProcessCardCreationCallBack), arg0, arg1)
}

// ProcessCardTrackingCallback mocks base method.
func (m *MockCallBackConsumerServer) ProcessCardTrackingCallback(arg0 context.Context, arg1 *provisioning.ProcessCardTrackingCallbackRequest) (*provisioning.ProcessCardTrackingCallbackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardTrackingCallback", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessCardTrackingCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardTrackingCallback indicates an expected call of ProcessCardTrackingCallback.
func (mr *MockCallBackConsumerServerMockRecorder) ProcessCardTrackingCallback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardTrackingCallback", reflect.TypeOf((*MockCallBackConsumerServer)(nil).ProcessCardTrackingCallback), arg0, arg1)
}

// ProcessDispatchPhysicalCardCallback mocks base method.
func (m *MockCallBackConsumerServer) ProcessDispatchPhysicalCardCallback(arg0 context.Context, arg1 *provisioning.ProcessDispatchPhysicalCardCallbackRequest) (*provisioning.ProcessDispatchPhysicalCardCallbackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessDispatchPhysicalCardCallback", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessDispatchPhysicalCardCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessDispatchPhysicalCardCallback indicates an expected call of ProcessDispatchPhysicalCardCallback.
func (mr *MockCallBackConsumerServerMockRecorder) ProcessDispatchPhysicalCardCallback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessDispatchPhysicalCardCallback", reflect.TypeOf((*MockCallBackConsumerServer)(nil).ProcessDispatchPhysicalCardCallback), arg0, arg1)
}

// MockUnsafeCallBackConsumerServer is a mock of UnsafeCallBackConsumerServer interface.
type MockUnsafeCallBackConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCallBackConsumerServerMockRecorder
}

// MockUnsafeCallBackConsumerServerMockRecorder is the mock recorder for MockUnsafeCallBackConsumerServer.
type MockUnsafeCallBackConsumerServerMockRecorder struct {
	mock *MockUnsafeCallBackConsumerServer
}

// NewMockUnsafeCallBackConsumerServer creates a new mock instance.
func NewMockUnsafeCallBackConsumerServer(ctrl *gomock.Controller) *MockUnsafeCallBackConsumerServer {
	mock := &MockUnsafeCallBackConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCallBackConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCallBackConsumerServer) EXPECT() *MockUnsafeCallBackConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCallBackConsumerServer mocks base method.
func (m *MockUnsafeCallBackConsumerServer) mustEmbedUnimplementedCallBackConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCallBackConsumerServer")
}

// mustEmbedUnimplementedCallBackConsumerServer indicates an expected call of mustEmbedUnimplementedCallBackConsumerServer.
func (mr *MockUnsafeCallBackConsumerServerMockRecorder) mustEmbedUnimplementedCallBackConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCallBackConsumerServer", reflect.TypeOf((*MockUnsafeCallBackConsumerServer)(nil).mustEmbedUnimplementedCallBackConsumerServer))
}
