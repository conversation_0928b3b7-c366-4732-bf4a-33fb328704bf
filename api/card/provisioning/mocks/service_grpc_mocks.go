// Code generated by MockGen. DO NOT EDIT.
// Source: api/card/provisioning/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	provisioning "github.com/epifi/gamma/api/card/provisioning"
	dynamic_elements "github.com/epifi/gamma/api/dynamic_elements"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCardProvisioningClient is a mock of CardProvisioningClient interface.
type MockCardProvisioningClient struct {
	ctrl     *gomock.Controller
	recorder *MockCardProvisioningClientMockRecorder
}

// MockCardProvisioningClientMockRecorder is the mock recorder for MockCardProvisioningClient.
type MockCardProvisioningClientMockRecorder struct {
	mock *MockCardProvisioningClient
}

// NewMockCardProvisioningClient creates a new mock instance.
func NewMockCardProvisioningClient(ctrl *gomock.Controller) *MockCardProvisioningClient {
	mock := &MockCardProvisioningClient{ctrl: ctrl}
	mock.recorder = &MockCardProvisioningClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardProvisioningClient) EXPECT() *MockCardProvisioningClientMockRecorder {
	return m.recorder
}

// ActivateCard mocks base method.
func (m *MockCardProvisioningClient) ActivateCard(ctx context.Context, in *provisioning.ActivateCardRequest, opts ...grpc.CallOption) (*provisioning.ActivateCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ActivateCard", varargs...)
	ret0, _ := ret[0].(*provisioning.ActivateCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ActivateCard indicates an expected call of ActivateCard.
func (mr *MockCardProvisioningClientMockRecorder) ActivateCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ActivateCard", reflect.TypeOf((*MockCardProvisioningClient)(nil).ActivateCard), varargs...)
}

// ActivatePhysicalCard mocks base method.
func (m *MockCardProvisioningClient) ActivatePhysicalCard(ctx context.Context, in *provisioning.ActivatePhysicalCardRequest, opts ...grpc.CallOption) (*provisioning.ActivatePhysicalCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ActivatePhysicalCard", varargs...)
	ret0, _ := ret[0].(*provisioning.ActivatePhysicalCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ActivatePhysicalCard indicates an expected call of ActivatePhysicalCard.
func (mr *MockCardProvisioningClientMockRecorder) ActivatePhysicalCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ActivatePhysicalCard", reflect.TypeOf((*MockCardProvisioningClient)(nil).ActivatePhysicalCard), varargs...)
}

// CardDeliveryTracking mocks base method.
func (m *MockCardProvisioningClient) CardDeliveryTracking(ctx context.Context, in *provisioning.CardDeliveryTrackingRequest, opts ...grpc.CallOption) (*provisioning.CardDeliveryTrackingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CardDeliveryTracking", varargs...)
	ret0, _ := ret[0].(*provisioning.CardDeliveryTrackingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CardDeliveryTracking indicates an expected call of CardDeliveryTracking.
func (mr *MockCardProvisioningClientMockRecorder) CardDeliveryTracking(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CardDeliveryTracking", reflect.TypeOf((*MockCardProvisioningClient)(nil).CardDeliveryTracking), varargs...)
}

// CardPinStatus mocks base method.
func (m *MockCardProvisioningClient) CardPinStatus(ctx context.Context, in *provisioning.CardPinStatusRequest, opts ...grpc.CallOption) (*provisioning.CardPinStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CardPinStatus", varargs...)
	ret0, _ := ret[0].(*provisioning.CardPinStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CardPinStatus indicates an expected call of CardPinStatus.
func (mr *MockCardProvisioningClientMockRecorder) CardPinStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CardPinStatus", reflect.TypeOf((*MockCardProvisioningClient)(nil).CardPinStatus), varargs...)
}

// ChangeCardPin mocks base method.
func (m *MockCardProvisioningClient) ChangeCardPin(ctx context.Context, in *provisioning.ChangeCardPinRequest, opts ...grpc.CallOption) (*provisioning.ChangeCardPinResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ChangeCardPin", varargs...)
	ret0, _ := ret[0].(*provisioning.ChangeCardPinResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeCardPin indicates an expected call of ChangeCardPin.
func (mr *MockCardProvisioningClientMockRecorder) ChangeCardPin(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeCardPin", reflect.TypeOf((*MockCardProvisioningClient)(nil).ChangeCardPin), varargs...)
}

// CheckShippingAddressUpdateAndPhysicalCardDispatchStatus mocks base method.
func (m *MockCardProvisioningClient) CheckShippingAddressUpdateAndPhysicalCardDispatchStatus(ctx context.Context, in *provisioning.CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest, opts ...grpc.CallOption) (*provisioning.CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckShippingAddressUpdateAndPhysicalCardDispatchStatus", varargs...)
	ret0, _ := ret[0].(*provisioning.CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckShippingAddressUpdateAndPhysicalCardDispatchStatus indicates an expected call of CheckShippingAddressUpdateAndPhysicalCardDispatchStatus.
func (mr *MockCardProvisioningClientMockRecorder) CheckShippingAddressUpdateAndPhysicalCardDispatchStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckShippingAddressUpdateAndPhysicalCardDispatchStatus", reflect.TypeOf((*MockCardProvisioningClient)(nil).CheckShippingAddressUpdateAndPhysicalCardDispatchStatus), varargs...)
}

// CreateCard mocks base method.
func (m *MockCardProvisioningClient) CreateCard(ctx context.Context, in *provisioning.CreateCardRequest, opts ...grpc.CallOption) (*provisioning.CreateCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateCard", varargs...)
	ret0, _ := ret[0].(*provisioning.CreateCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCard indicates an expected call of CreateCard.
func (mr *MockCardProvisioningClientMockRecorder) CreateCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCard", reflect.TypeOf((*MockCardProvisioningClient)(nil).CreateCard), varargs...)
}

// CreatePhysicalCardDispatchAttempt mocks base method.
func (m *MockCardProvisioningClient) CreatePhysicalCardDispatchAttempt(ctx context.Context, in *provisioning.CreatePhysicalCardDispatchAttemptRequest, opts ...grpc.CallOption) (*provisioning.CreatePhysicalCardDispatchAttemptResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePhysicalCardDispatchAttempt", varargs...)
	ret0, _ := ret[0].(*provisioning.CreatePhysicalCardDispatchAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePhysicalCardDispatchAttempt indicates an expected call of CreatePhysicalCardDispatchAttempt.
func (mr *MockCardProvisioningClientMockRecorder) CreatePhysicalCardDispatchAttempt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePhysicalCardDispatchAttempt", reflect.TypeOf((*MockCardProvisioningClient)(nil).CreatePhysicalCardDispatchAttempt), varargs...)
}

// DispatchCard mocks base method.
func (m *MockCardProvisioningClient) DispatchCard(ctx context.Context, in *provisioning.CardDispatchRequest, opts ...grpc.CallOption) (*provisioning.CardDispatchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DispatchCard", varargs...)
	ret0, _ := ret[0].(*provisioning.CardDispatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DispatchCard indicates an expected call of DispatchCard.
func (mr *MockCardProvisioningClientMockRecorder) DispatchCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DispatchCard", reflect.TypeOf((*MockCardProvisioningClient)(nil).DispatchCard), varargs...)
}

// DynamicElementCallback mocks base method.
func (m *MockCardProvisioningClient) DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DynamicElementCallback", varargs...)
	ret0, _ := ret[0].(*dynamic_elements.DynamicElementCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DynamicElementCallback indicates an expected call of DynamicElementCallback.
func (mr *MockCardProvisioningClientMockRecorder) DynamicElementCallback(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DynamicElementCallback", reflect.TypeOf((*MockCardProvisioningClient)(nil).DynamicElementCallback), varargs...)
}

// FetchCardActivationStatus mocks base method.
func (m *MockCardProvisioningClient) FetchCardActivationStatus(ctx context.Context, in *provisioning.FetchCardActivationStatusRequest, opts ...grpc.CallOption) (*provisioning.FetchCardActivationStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchCardActivationStatus", varargs...)
	ret0, _ := ret[0].(*provisioning.FetchCardActivationStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCardActivationStatus indicates an expected call of FetchCardActivationStatus.
func (mr *MockCardProvisioningClientMockRecorder) FetchCardActivationStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCardActivationStatus", reflect.TypeOf((*MockCardProvisioningClient)(nil).FetchCardActivationStatus), varargs...)
}

// FetchCardCreationStatus mocks base method.
func (m *MockCardProvisioningClient) FetchCardCreationStatus(ctx context.Context, in *provisioning.FetchCardCreationStatusRequest, opts ...grpc.CallOption) (*provisioning.FetchCardCreationStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchCardCreationStatus", varargs...)
	ret0, _ := ret[0].(*provisioning.FetchCardCreationStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCardCreationStatus indicates an expected call of FetchCardCreationStatus.
func (mr *MockCardProvisioningClientMockRecorder) FetchCardCreationStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCardCreationStatus", reflect.TypeOf((*MockCardProvisioningClient)(nil).FetchCardCreationStatus), varargs...)
}

// FetchCardDetails mocks base method.
func (m *MockCardProvisioningClient) FetchCardDetails(ctx context.Context, in *provisioning.FetchCardDetailsRequest, opts ...grpc.CallOption) (*provisioning.FetchCardDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchCardDetails", varargs...)
	ret0, _ := ret[0].(*provisioning.FetchCardDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCardDetails indicates an expected call of FetchCardDetails.
func (mr *MockCardProvisioningClientMockRecorder) FetchCardDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCardDetails", reflect.TypeOf((*MockCardProvisioningClient)(nil).FetchCardDetails), varargs...)
}

// FetchCardDetailsByAccountId mocks base method.
func (m *MockCardProvisioningClient) FetchCardDetailsByAccountId(ctx context.Context, in *provisioning.FetchCardDetailsByAccountIdRequest, opts ...grpc.CallOption) (*provisioning.FetchCardDetailsByAccountIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchCardDetailsByAccountId", varargs...)
	ret0, _ := ret[0].(*provisioning.FetchCardDetailsByAccountIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCardDetailsByAccountId indicates an expected call of FetchCardDetailsByAccountId.
func (mr *MockCardProvisioningClientMockRecorder) FetchCardDetailsByAccountId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCardDetailsByAccountId", reflect.TypeOf((*MockCardProvisioningClient)(nil).FetchCardDetailsByAccountId), varargs...)
}

// FetchCardRenewalChargesForUser mocks base method.
func (m *MockCardProvisioningClient) FetchCardRenewalChargesForUser(ctx context.Context, in *provisioning.FetchCardRenewalChargesForUserRequest, opts ...grpc.CallOption) (*provisioning.FetchCardRenewalChargesForUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchCardRenewalChargesForUser", varargs...)
	ret0, _ := ret[0].(*provisioning.FetchCardRenewalChargesForUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCardRenewalChargesForUser indicates an expected call of FetchCardRenewalChargesForUser.
func (mr *MockCardProvisioningClientMockRecorder) FetchCardRenewalChargesForUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCardRenewalChargesForUser", reflect.TypeOf((*MockCardProvisioningClient)(nil).FetchCardRenewalChargesForUser), varargs...)
}

// FetchCardTrackingDetails mocks base method.
func (m *MockCardProvisioningClient) FetchCardTrackingDetails(ctx context.Context, in *provisioning.FetchCardTrackingDetailsRequest, opts ...grpc.CallOption) (*provisioning.FetchCardTrackingDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchCardTrackingDetails", varargs...)
	ret0, _ := ret[0].(*provisioning.FetchCardTrackingDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCardTrackingDetails indicates an expected call of FetchCardTrackingDetails.
func (mr *MockCardProvisioningClientMockRecorder) FetchCardTrackingDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCardTrackingDetails", reflect.TypeOf((*MockCardProvisioningClient)(nil).FetchCardTrackingDetails), varargs...)
}

// FetchCards mocks base method.
func (m *MockCardProvisioningClient) FetchCards(ctx context.Context, in *provisioning.FetchCardsRequest, opts ...grpc.CallOption) (*provisioning.FetchCardsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchCards", varargs...)
	ret0, _ := ret[0].(*provisioning.FetchCardsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCards indicates an expected call of FetchCards.
func (mr *MockCardProvisioningClientMockRecorder) FetchCards(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCards", reflect.TypeOf((*MockCardProvisioningClient)(nil).FetchCards), varargs...)
}

// FetchDeliveryTrackingStatus mocks base method.
func (m *MockCardProvisioningClient) FetchDeliveryTrackingStatus(ctx context.Context, in *provisioning.FetchDeliveryTrackingStatusRequest, opts ...grpc.CallOption) (*provisioning.FetchDeliveryTrackingStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchDeliveryTrackingStatus", varargs...)
	ret0, _ := ret[0].(*provisioning.FetchDeliveryTrackingStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchDeliveryTrackingStatus indicates an expected call of FetchDeliveryTrackingStatus.
func (mr *MockCardProvisioningClientMockRecorder) FetchDeliveryTrackingStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchDeliveryTrackingStatus", reflect.TypeOf((*MockCardProvisioningClient)(nil).FetchDeliveryTrackingStatus), varargs...)
}

// FetchDynamicElements mocks base method.
func (m *MockCardProvisioningClient) FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchDynamicElements", varargs...)
	ret0, _ := ret[0].(*dynamic_elements.FetchDynamicElementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchDynamicElements indicates an expected call of FetchDynamicElements.
func (mr *MockCardProvisioningClientMockRecorder) FetchDynamicElements(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchDynamicElements", reflect.TypeOf((*MockCardProvisioningClient)(nil).FetchDynamicElements), varargs...)
}

// FetchForexRefundAggregates mocks base method.
func (m *MockCardProvisioningClient) FetchForexRefundAggregates(ctx context.Context, in *provisioning.FetchForexRefundAggregatesRequest, opts ...grpc.CallOption) (*provisioning.FetchForexRefundAggregatesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchForexRefundAggregates", varargs...)
	ret0, _ := ret[0].(*provisioning.FetchForexRefundAggregatesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchForexRefundAggregates indicates an expected call of FetchForexRefundAggregates.
func (mr *MockCardProvisioningClientMockRecorder) FetchForexRefundAggregates(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchForexRefundAggregates", reflect.TypeOf((*MockCardProvisioningClient)(nil).FetchForexRefundAggregates), varargs...)
}

// FetchPhysicalCardChargesForUser mocks base method.
func (m *MockCardProvisioningClient) FetchPhysicalCardChargesForUser(ctx context.Context, in *provisioning.FetchPhysicalCardChargesForUserRequest, opts ...grpc.CallOption) (*provisioning.FetchPhysicalCardChargesForUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchPhysicalCardChargesForUser", varargs...)
	ret0, _ := ret[0].(*provisioning.FetchPhysicalCardChargesForUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchPhysicalCardChargesForUser indicates an expected call of FetchPhysicalCardChargesForUser.
func (mr *MockCardProvisioningClientMockRecorder) FetchPhysicalCardChargesForUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchPhysicalCardChargesForUser", reflect.TypeOf((*MockCardProvisioningClient)(nil).FetchPhysicalCardChargesForUser), varargs...)
}

// FetchPhysicalCardDispatchRequests mocks base method.
func (m *MockCardProvisioningClient) FetchPhysicalCardDispatchRequests(ctx context.Context, in *provisioning.FetchPhysicalCardDispatchRequestsRequest, opts ...grpc.CallOption) (*provisioning.FetchPhysicalCardDispatchRequestsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchPhysicalCardDispatchRequests", varargs...)
	ret0, _ := ret[0].(*provisioning.FetchPhysicalCardDispatchRequestsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchPhysicalCardDispatchRequests indicates an expected call of FetchPhysicalCardDispatchRequests.
func (mr *MockCardProvisioningClientMockRecorder) FetchPhysicalCardDispatchRequests(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchPhysicalCardDispatchRequests", reflect.TypeOf((*MockCardProvisioningClient)(nil).FetchPhysicalCardDispatchRequests), varargs...)
}

// FetchTransactionableCards mocks base method.
func (m *MockCardProvisioningClient) FetchTransactionableCards(ctx context.Context, in *provisioning.FetchTransactionableCardsRequest, opts ...grpc.CallOption) (*provisioning.FetchTransactionableCardsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchTransactionableCards", varargs...)
	ret0, _ := ret[0].(*provisioning.FetchTransactionableCardsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchTransactionableCards indicates an expected call of FetchTransactionableCards.
func (mr *MockCardProvisioningClientMockRecorder) FetchTransactionableCards(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchTransactionableCards", reflect.TypeOf((*MockCardProvisioningClient)(nil).FetchTransactionableCards), varargs...)
}

// ForceCardCreationEnquiry mocks base method.
func (m *MockCardProvisioningClient) ForceCardCreationEnquiry(ctx context.Context, in *provisioning.ForceCardCreationEnquiryRequest, opts ...grpc.CallOption) (*provisioning.ForceCardCreationEnquiryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ForceCardCreationEnquiry", varargs...)
	ret0, _ := ret[0].(*provisioning.ForceCardCreationEnquiryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ForceCardCreationEnquiry indicates an expected call of ForceCardCreationEnquiry.
func (mr *MockCardProvisioningClientMockRecorder) ForceCardCreationEnquiry(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForceCardCreationEnquiry", reflect.TypeOf((*MockCardProvisioningClient)(nil).ForceCardCreationEnquiry), varargs...)
}

// GenerateTxnId mocks base method.
func (m *MockCardProvisioningClient) GenerateTxnId(ctx context.Context, in *provisioning.GenerateTxnIdRequest, opts ...grpc.CallOption) (*provisioning.GenerateTxnIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateTxnId", varargs...)
	ret0, _ := ret[0].(*provisioning.GenerateTxnIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateTxnId indicates an expected call of GenerateTxnId.
func (mr *MockCardProvisioningClientMockRecorder) GenerateTxnId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateTxnId", reflect.TypeOf((*MockCardProvisioningClient)(nil).GenerateTxnId), varargs...)
}

// GetAdditionalAuthInfo mocks base method.
func (m *MockCardProvisioningClient) GetAdditionalAuthInfo(ctx context.Context, in *provisioning.GetAdditionalAuthInfoRequest, opts ...grpc.CallOption) (*provisioning.GetAdditionalAuthInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAdditionalAuthInfo", varargs...)
	ret0, _ := ret[0].(*provisioning.GetAdditionalAuthInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAdditionalAuthInfo indicates an expected call of GetAdditionalAuthInfo.
func (mr *MockCardProvisioningClientMockRecorder) GetAdditionalAuthInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAdditionalAuthInfo", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetAdditionalAuthInfo), varargs...)
}

// GetCardActionAttempts mocks base method.
func (m *MockCardProvisioningClient) GetCardActionAttempts(ctx context.Context, in *provisioning.GetCardActionAttemptsRequest, opts ...grpc.CallOption) (*provisioning.GetCardActionAttemptsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCardActionAttempts", varargs...)
	ret0, _ := ret[0].(*provisioning.GetCardActionAttemptsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardActionAttempts indicates an expected call of GetCardActionAttempts.
func (mr *MockCardProvisioningClientMockRecorder) GetCardActionAttempts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardActionAttempts", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetCardActionAttempts), varargs...)
}

// GetCardDetailsWithCvv mocks base method.
func (m *MockCardProvisioningClient) GetCardDetailsWithCvv(ctx context.Context, in *provisioning.GetCardDetailsWithCvvRequest, opts ...grpc.CallOption) (*provisioning.GetCardDetailsWithCvvResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCardDetailsWithCvv", varargs...)
	ret0, _ := ret[0].(*provisioning.GetCardDetailsWithCvvResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardDetailsWithCvv indicates an expected call of GetCardDetailsWithCvv.
func (mr *MockCardProvisioningClientMockRecorder) GetCardDetailsWithCvv(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardDetailsWithCvv", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetCardDetailsWithCvv), varargs...)
}

// GetCardGroups mocks base method.
func (m *MockCardProvisioningClient) GetCardGroups(ctx context.Context, in *provisioning.GetCardGroupsRequest, opts ...grpc.CallOption) (*provisioning.GetCardGroupsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCardGroups", varargs...)
	ret0, _ := ret[0].(*provisioning.GetCardGroupsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardGroups indicates an expected call of GetCardGroups.
func (mr *MockCardProvisioningClientMockRecorder) GetCardGroups(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardGroups", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetCardGroups), varargs...)
}

// GetCardShipmentTrackingDetails mocks base method.
func (m *MockCardProvisioningClient) GetCardShipmentTrackingDetails(ctx context.Context, in *provisioning.GetCardShipmentTrackingDetailsRequest, opts ...grpc.CallOption) (*provisioning.GetCardShipmentTrackingDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCardShipmentTrackingDetails", varargs...)
	ret0, _ := ret[0].(*provisioning.GetCardShipmentTrackingDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardShipmentTrackingDetails indicates an expected call of GetCardShipmentTrackingDetails.
func (mr *MockCardProvisioningClientMockRecorder) GetCardShipmentTrackingDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardShipmentTrackingDetails", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetCardShipmentTrackingDetails), varargs...)
}

// GetCardSwitchNotification mocks base method.
func (m *MockCardProvisioningClient) GetCardSwitchNotification(ctx context.Context, in *provisioning.GetCardSwitchNotificationRequest, opts ...grpc.CallOption) (*provisioning.GetCardSwitchNotificationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCardSwitchNotification", varargs...)
	ret0, _ := ret[0].(*provisioning.GetCardSwitchNotificationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardSwitchNotification indicates an expected call of GetCardSwitchNotification.
func (mr *MockCardProvisioningClientMockRecorder) GetCardSwitchNotification(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardSwitchNotification", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetCardSwitchNotification), varargs...)
}

// GetDcInternationalWidget mocks base method.
func (m *MockCardProvisioningClient) GetDcInternationalWidget(ctx context.Context, in *provisioning.GetDcInternationalWidgetRequest, opts ...grpc.CallOption) (*provisioning.GetDcInternationalWidgetResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDcInternationalWidget", varargs...)
	ret0, _ := ret[0].(*provisioning.GetDcInternationalWidgetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDcInternationalWidget indicates an expected call of GetDcInternationalWidget.
func (mr *MockCardProvisioningClientMockRecorder) GetDcInternationalWidget(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDcInternationalWidget", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetDcInternationalWidget), varargs...)
}

// GetForexRefunds mocks base method.
func (m *MockCardProvisioningClient) GetForexRefunds(ctx context.Context, in *provisioning.GetForexRefundsRequest, opts ...grpc.CallOption) (*provisioning.GetForexRefundsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetForexRefunds", varargs...)
	ret0, _ := ret[0].(*provisioning.GetForexRefundsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForexRefunds indicates an expected call of GetForexRefunds.
func (mr *MockCardProvisioningClientMockRecorder) GetForexRefunds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForexRefunds", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetForexRefunds), varargs...)
}

// GetForexRefundsByActorId mocks base method.
func (m *MockCardProvisioningClient) GetForexRefundsByActorId(ctx context.Context, in *provisioning.GetForexRefundsByActorIdRequest, opts ...grpc.CallOption) (*provisioning.GetForexRefundsByActorIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetForexRefundsByActorId", varargs...)
	ret0, _ := ret[0].(*provisioning.GetForexRefundsByActorIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForexRefundsByActorId indicates an expected call of GetForexRefundsByActorId.
func (mr *MockCardProvisioningClientMockRecorder) GetForexRefundsByActorId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForexRefundsByActorId", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetForexRefundsByActorId), varargs...)
}

// GetHomeLayoutConfiguration mocks base method.
func (m *MockCardProvisioningClient) GetHomeLayoutConfiguration(ctx context.Context, in *provisioning.GetHomeLayoutConfigurationRequest, opts ...grpc.CallOption) (*provisioning.GetHomeLayoutConfigurationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetHomeLayoutConfiguration", varargs...)
	ret0, _ := ret[0].(*provisioning.GetHomeLayoutConfigurationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHomeLayoutConfiguration indicates an expected call of GetHomeLayoutConfiguration.
func (mr *MockCardProvisioningClientMockRecorder) GetHomeLayoutConfiguration(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHomeLayoutConfiguration", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetHomeLayoutConfiguration), varargs...)
}

// GetLastPhysicalCardIssuedForUser mocks base method.
func (m *MockCardProvisioningClient) GetLastPhysicalCardIssuedForUser(ctx context.Context, in *provisioning.GetLastPhysicalCardIssuedForUserRequest, opts ...grpc.CallOption) (*provisioning.GetLastPhysicalCardIssuedForUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLastPhysicalCardIssuedForUser", varargs...)
	ret0, _ := ret[0].(*provisioning.GetLastPhysicalCardIssuedForUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastPhysicalCardIssuedForUser indicates an expected call of GetLastPhysicalCardIssuedForUser.
func (mr *MockCardProvisioningClientMockRecorder) GetLastPhysicalCardIssuedForUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastPhysicalCardIssuedForUser", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetLastPhysicalCardIssuedForUser), varargs...)
}

// GetLatestCardForActorIds mocks base method.
func (m *MockCardProvisioningClient) GetLatestCardForActorIds(ctx context.Context, in *provisioning.GetLatestCardForActorIdsRequest, opts ...grpc.CallOption) (*provisioning.GetLatestCardForActorIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLatestCardForActorIds", varargs...)
	ret0, _ := ret[0].(*provisioning.GetLatestCardForActorIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestCardForActorIds indicates an expected call of GetLatestCardForActorIds.
func (mr *MockCardProvisioningClientMockRecorder) GetLatestCardForActorIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestCardForActorIds", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetLatestCardForActorIds), varargs...)
}

// GetPaginatedForexRefundsByActorId mocks base method.
func (m *MockCardProvisioningClient) GetPaginatedForexRefundsByActorId(ctx context.Context, in *provisioning.GetPaginatedForexRefundsByActorIdRequest, opts ...grpc.CallOption) (*provisioning.GetPaginatedForexRefundsByActorIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPaginatedForexRefundsByActorId", varargs...)
	ret0, _ := ret[0].(*provisioning.GetPaginatedForexRefundsByActorIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaginatedForexRefundsByActorId indicates an expected call of GetPaginatedForexRefundsByActorId.
func (mr *MockCardProvisioningClientMockRecorder) GetPaginatedForexRefundsByActorId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaginatedForexRefundsByActorId", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetPaginatedForexRefundsByActorId), varargs...)
}

// GetPhysicalCardActivationInfo mocks base method.
func (m *MockCardProvisioningClient) GetPhysicalCardActivationInfo(ctx context.Context, in *provisioning.GetPhysicalCardActivationInfoRequest, opts ...grpc.CallOption) (*provisioning.GetPhysicalCardActivationInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPhysicalCardActivationInfo", varargs...)
	ret0, _ := ret[0].(*provisioning.GetPhysicalCardActivationInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPhysicalCardActivationInfo indicates an expected call of GetPhysicalCardActivationInfo.
func (mr *MockCardProvisioningClientMockRecorder) GetPhysicalCardActivationInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPhysicalCardActivationInfo", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetPhysicalCardActivationInfo), varargs...)
}

// GetPhysicalCardDispatchStatus mocks base method.
func (m *MockCardProvisioningClient) GetPhysicalCardDispatchStatus(ctx context.Context, in *provisioning.GetPhysicalCardDispatchStatusRequest, opts ...grpc.CallOption) (*provisioning.GetPhysicalCardDispatchStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPhysicalCardDispatchStatus", varargs...)
	ret0, _ := ret[0].(*provisioning.GetPhysicalCardDispatchStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPhysicalCardDispatchStatus indicates an expected call of GetPhysicalCardDispatchStatus.
func (mr *MockCardProvisioningClientMockRecorder) GetPhysicalCardDispatchStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPhysicalCardDispatchStatus", reflect.TypeOf((*MockCardProvisioningClient)(nil).GetPhysicalCardDispatchStatus), varargs...)
}

// InitiateAdditionalAuthAttempt mocks base method.
func (m *MockCardProvisioningClient) InitiateAdditionalAuthAttempt(ctx context.Context, in *provisioning.InitiateAdditionalAuthAttemptRequest, opts ...grpc.CallOption) (*provisioning.InitiateAdditionalAuthAttemptResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateAdditionalAuthAttempt", varargs...)
	ret0, _ := ret[0].(*provisioning.InitiateAdditionalAuthAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateAdditionalAuthAttempt indicates an expected call of InitiateAdditionalAuthAttempt.
func (mr *MockCardProvisioningClientMockRecorder) InitiateAdditionalAuthAttempt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateAdditionalAuthAttempt", reflect.TypeOf((*MockCardProvisioningClient)(nil).InitiateAdditionalAuthAttempt), varargs...)
}

// InitiatePhysicalCardDispatch mocks base method.
func (m *MockCardProvisioningClient) InitiatePhysicalCardDispatch(ctx context.Context, in *provisioning.InitiatePhysicalCardDispatchRequest, opts ...grpc.CallOption) (*provisioning.InitiatePhysicalCardDispatchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiatePhysicalCardDispatch", varargs...)
	ret0, _ := ret[0].(*provisioning.InitiatePhysicalCardDispatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiatePhysicalCardDispatch indicates an expected call of InitiatePhysicalCardDispatch.
func (mr *MockCardProvisioningClientMockRecorder) InitiatePhysicalCardDispatch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiatePhysicalCardDispatch", reflect.TypeOf((*MockCardProvisioningClient)(nil).InitiatePhysicalCardDispatch), varargs...)
}

// InitiateShippingAddressUpdateAndDispatchPhysicalCard mocks base method.
func (m *MockCardProvisioningClient) InitiateShippingAddressUpdateAndDispatchPhysicalCard(ctx context.Context, in *provisioning.InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest, opts ...grpc.CallOption) (*provisioning.InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateShippingAddressUpdateAndDispatchPhysicalCard", varargs...)
	ret0, _ := ret[0].(*provisioning.InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateShippingAddressUpdateAndDispatchPhysicalCard indicates an expected call of InitiateShippingAddressUpdateAndDispatchPhysicalCard.
func (mr *MockCardProvisioningClientMockRecorder) InitiateShippingAddressUpdateAndDispatchPhysicalCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateShippingAddressUpdateAndDispatchPhysicalCard", reflect.TypeOf((*MockCardProvisioningClient)(nil).InitiateShippingAddressUpdateAndDispatchPhysicalCard), varargs...)
}

// ProcessManualCardPinSet mocks base method.
func (m *MockCardProvisioningClient) ProcessManualCardPinSet(ctx context.Context, in *provisioning.ProcessManualCardPinSetRequest, opts ...grpc.CallOption) (*provisioning.ProcessManualCardPinSetResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessManualCardPinSet", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessManualCardPinSetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessManualCardPinSet indicates an expected call of ProcessManualCardPinSet.
func (mr *MockCardProvisioningClientMockRecorder) ProcessManualCardPinSet(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessManualCardPinSet", reflect.TypeOf((*MockCardProvisioningClient)(nil).ProcessManualCardPinSet), varargs...)
}

// ProcessManualCardUnsuspend mocks base method.
func (m *MockCardProvisioningClient) ProcessManualCardUnsuspend(ctx context.Context, in *provisioning.ProcessManualCardUnsuspendRequest, opts ...grpc.CallOption) (*provisioning.ProcessManualCardUnsuspendResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessManualCardUnsuspend", varargs...)
	ret0, _ := ret[0].(*provisioning.ProcessManualCardUnsuspendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessManualCardUnsuspend indicates an expected call of ProcessManualCardUnsuspend.
func (mr *MockCardProvisioningClientMockRecorder) ProcessManualCardUnsuspend(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessManualCardUnsuspend", reflect.TypeOf((*MockCardProvisioningClient)(nil).ProcessManualCardUnsuspend), varargs...)
}

// RenewCard mocks base method.
func (m *MockCardProvisioningClient) RenewCard(ctx context.Context, in *provisioning.RenewCardRequest, opts ...grpc.CallOption) (*provisioning.RenewCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RenewCard", varargs...)
	ret0, _ := ret[0].(*provisioning.RenewCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RenewCard indicates an expected call of RenewCard.
func (mr *MockCardProvisioningClientMockRecorder) RenewCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RenewCard", reflect.TypeOf((*MockCardProvisioningClient)(nil).RenewCard), varargs...)
}

// RenewCardStatus mocks base method.
func (m *MockCardProvisioningClient) RenewCardStatus(ctx context.Context, in *provisioning.RenewCardStatusRequest, opts ...grpc.CallOption) (*provisioning.RenewCardStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RenewCardStatus", varargs...)
	ret0, _ := ret[0].(*provisioning.RenewCardStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RenewCardStatus indicates an expected call of RenewCardStatus.
func (mr *MockCardProvisioningClientMockRecorder) RenewCardStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RenewCardStatus", reflect.TypeOf((*MockCardProvisioningClient)(nil).RenewCardStatus), varargs...)
}

// ResetCardPin mocks base method.
func (m *MockCardProvisioningClient) ResetCardPin(ctx context.Context, in *provisioning.ResetCardPinRequest, opts ...grpc.CallOption) (*provisioning.ResetCardPinResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResetCardPin", varargs...)
	ret0, _ := ret[0].(*provisioning.ResetCardPinResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResetCardPin indicates an expected call of ResetCardPin.
func (mr *MockCardProvisioningClientMockRecorder) ResetCardPin(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetCardPin", reflect.TypeOf((*MockCardProvisioningClient)(nil).ResetCardPin), varargs...)
}

// SetCardPin mocks base method.
func (m *MockCardProvisioningClient) SetCardPin(ctx context.Context, in *provisioning.SetCardPinRequest, opts ...grpc.CallOption) (*provisioning.SetCardPinResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetCardPin", varargs...)
	ret0, _ := ret[0].(*provisioning.SetCardPinResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetCardPin indicates an expected call of SetCardPin.
func (mr *MockCardProvisioningClientMockRecorder) SetCardPin(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCardPin", reflect.TypeOf((*MockCardProvisioningClient)(nil).SetCardPin), varargs...)
}

// TriggerCardNotifications mocks base method.
func (m *MockCardProvisioningClient) TriggerCardNotifications(ctx context.Context, in *provisioning.TriggerCardNotificationsRequest, opts ...grpc.CallOption) (*provisioning.TriggerCardNotificationsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TriggerCardNotifications", varargs...)
	ret0, _ := ret[0].(*provisioning.TriggerCardNotificationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerCardNotifications indicates an expected call of TriggerCardNotifications.
func (mr *MockCardProvisioningClientMockRecorder) TriggerCardNotifications(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerCardNotifications", reflect.TypeOf((*MockCardProvisioningClient)(nil).TriggerCardNotifications), varargs...)
}

// UpdateAuthInfo mocks base method.
func (m *MockCardProvisioningClient) UpdateAuthInfo(ctx context.Context, in *provisioning.UpdateAuthInfoRequest, opts ...grpc.CallOption) (*provisioning.UpdateAuthInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAuthInfo", varargs...)
	ret0, _ := ret[0].(*provisioning.UpdateAuthInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAuthInfo indicates an expected call of UpdateAuthInfo.
func (mr *MockCardProvisioningClientMockRecorder) UpdateAuthInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAuthInfo", reflect.TypeOf((*MockCardProvisioningClient)(nil).UpdateAuthInfo), varargs...)
}

// UpdateFreeCardReplacement mocks base method.
func (m *MockCardProvisioningClient) UpdateFreeCardReplacement(ctx context.Context, in *provisioning.UpdateFreeCardReplacementRequest, opts ...grpc.CallOption) (*provisioning.UpdateFreeCardReplacementResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateFreeCardReplacement", varargs...)
	ret0, _ := ret[0].(*provisioning.UpdateFreeCardReplacementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateFreeCardReplacement indicates an expected call of UpdateFreeCardReplacement.
func (mr *MockCardProvisioningClientMockRecorder) UpdateFreeCardReplacement(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFreeCardReplacement", reflect.TypeOf((*MockCardProvisioningClient)(nil).UpdateFreeCardReplacement), varargs...)
}

// UpdateTrackingDetails mocks base method.
func (m *MockCardProvisioningClient) UpdateTrackingDetails(ctx context.Context, in *provisioning.UpdateTrackingDetailsRequest, opts ...grpc.CallOption) (*provisioning.UpdateTrackingDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTrackingDetails", varargs...)
	ret0, _ := ret[0].(*provisioning.UpdateTrackingDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTrackingDetails indicates an expected call of UpdateTrackingDetails.
func (mr *MockCardProvisioningClientMockRecorder) UpdateTrackingDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTrackingDetails", reflect.TypeOf((*MockCardProvisioningClient)(nil).UpdateTrackingDetails), varargs...)
}

// UploadCardTrackingDetails mocks base method.
func (m *MockCardProvisioningClient) UploadCardTrackingDetails(ctx context.Context, in *provisioning.UploadCardTrackingDetailsRequest, opts ...grpc.CallOption) (*provisioning.UploadCardTrackingDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UploadCardTrackingDetails", varargs...)
	ret0, _ := ret[0].(*provisioning.UploadCardTrackingDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadCardTrackingDetails indicates an expected call of UploadCardTrackingDetails.
func (mr *MockCardProvisioningClientMockRecorder) UploadCardTrackingDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadCardTrackingDetails", reflect.TypeOf((*MockCardProvisioningClient)(nil).UploadCardTrackingDetails), varargs...)
}

// ValidateCard mocks base method.
func (m *MockCardProvisioningClient) ValidateCard(ctx context.Context, in *provisioning.CardValidateRequest, opts ...grpc.CallOption) (*provisioning.CardValidateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValidateCard", varargs...)
	ret0, _ := ret[0].(*provisioning.CardValidateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateCard indicates an expected call of ValidateCard.
func (mr *MockCardProvisioningClientMockRecorder) ValidateCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateCard", reflect.TypeOf((*MockCardProvisioningClient)(nil).ValidateCard), varargs...)
}

// ValidateCardPin mocks base method.
func (m *MockCardProvisioningClient) ValidateCardPin(ctx context.Context, in *provisioning.ValidateCardPinRequest, opts ...grpc.CallOption) (*provisioning.ValidateCardPinResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValidateCardPin", varargs...)
	ret0, _ := ret[0].(*provisioning.ValidateCardPinResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateCardPin indicates an expected call of ValidateCardPin.
func (mr *MockCardProvisioningClientMockRecorder) ValidateCardPin(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateCardPin", reflect.TypeOf((*MockCardProvisioningClient)(nil).ValidateCardPin), varargs...)
}

// VerifyQRCode mocks base method.
func (m *MockCardProvisioningClient) VerifyQRCode(ctx context.Context, in *provisioning.VerifyQRCodeRequest, opts ...grpc.CallOption) (*provisioning.VerifyQRCodeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyQRCode", varargs...)
	ret0, _ := ret[0].(*provisioning.VerifyQRCodeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyQRCode indicates an expected call of VerifyQRCode.
func (mr *MockCardProvisioningClientMockRecorder) VerifyQRCode(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyQRCode", reflect.TypeOf((*MockCardProvisioningClient)(nil).VerifyQRCode), varargs...)
}

// MockCardProvisioningServer is a mock of CardProvisioningServer interface.
type MockCardProvisioningServer struct {
	ctrl     *gomock.Controller
	recorder *MockCardProvisioningServerMockRecorder
}

// MockCardProvisioningServerMockRecorder is the mock recorder for MockCardProvisioningServer.
type MockCardProvisioningServerMockRecorder struct {
	mock *MockCardProvisioningServer
}

// NewMockCardProvisioningServer creates a new mock instance.
func NewMockCardProvisioningServer(ctrl *gomock.Controller) *MockCardProvisioningServer {
	mock := &MockCardProvisioningServer{ctrl: ctrl}
	mock.recorder = &MockCardProvisioningServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardProvisioningServer) EXPECT() *MockCardProvisioningServerMockRecorder {
	return m.recorder
}

// ActivateCard mocks base method.
func (m *MockCardProvisioningServer) ActivateCard(arg0 context.Context, arg1 *provisioning.ActivateCardRequest) (*provisioning.ActivateCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ActivateCard", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ActivateCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ActivateCard indicates an expected call of ActivateCard.
func (mr *MockCardProvisioningServerMockRecorder) ActivateCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ActivateCard", reflect.TypeOf((*MockCardProvisioningServer)(nil).ActivateCard), arg0, arg1)
}

// ActivatePhysicalCard mocks base method.
func (m *MockCardProvisioningServer) ActivatePhysicalCard(arg0 context.Context, arg1 *provisioning.ActivatePhysicalCardRequest) (*provisioning.ActivatePhysicalCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ActivatePhysicalCard", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ActivatePhysicalCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ActivatePhysicalCard indicates an expected call of ActivatePhysicalCard.
func (mr *MockCardProvisioningServerMockRecorder) ActivatePhysicalCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ActivatePhysicalCard", reflect.TypeOf((*MockCardProvisioningServer)(nil).ActivatePhysicalCard), arg0, arg1)
}

// CardDeliveryTracking mocks base method.
func (m *MockCardProvisioningServer) CardDeliveryTracking(arg0 context.Context, arg1 *provisioning.CardDeliveryTrackingRequest) (*provisioning.CardDeliveryTrackingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CardDeliveryTracking", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.CardDeliveryTrackingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CardDeliveryTracking indicates an expected call of CardDeliveryTracking.
func (mr *MockCardProvisioningServerMockRecorder) CardDeliveryTracking(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CardDeliveryTracking", reflect.TypeOf((*MockCardProvisioningServer)(nil).CardDeliveryTracking), arg0, arg1)
}

// CardPinStatus mocks base method.
func (m *MockCardProvisioningServer) CardPinStatus(arg0 context.Context, arg1 *provisioning.CardPinStatusRequest) (*provisioning.CardPinStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CardPinStatus", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.CardPinStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CardPinStatus indicates an expected call of CardPinStatus.
func (mr *MockCardProvisioningServerMockRecorder) CardPinStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CardPinStatus", reflect.TypeOf((*MockCardProvisioningServer)(nil).CardPinStatus), arg0, arg1)
}

// ChangeCardPin mocks base method.
func (m *MockCardProvisioningServer) ChangeCardPin(arg0 context.Context, arg1 *provisioning.ChangeCardPinRequest) (*provisioning.ChangeCardPinResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeCardPin", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ChangeCardPinResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeCardPin indicates an expected call of ChangeCardPin.
func (mr *MockCardProvisioningServerMockRecorder) ChangeCardPin(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeCardPin", reflect.TypeOf((*MockCardProvisioningServer)(nil).ChangeCardPin), arg0, arg1)
}

// CheckShippingAddressUpdateAndPhysicalCardDispatchStatus mocks base method.
func (m *MockCardProvisioningServer) CheckShippingAddressUpdateAndPhysicalCardDispatchStatus(arg0 context.Context, arg1 *provisioning.CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest) (*provisioning.CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckShippingAddressUpdateAndPhysicalCardDispatchStatus", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckShippingAddressUpdateAndPhysicalCardDispatchStatus indicates an expected call of CheckShippingAddressUpdateAndPhysicalCardDispatchStatus.
func (mr *MockCardProvisioningServerMockRecorder) CheckShippingAddressUpdateAndPhysicalCardDispatchStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckShippingAddressUpdateAndPhysicalCardDispatchStatus", reflect.TypeOf((*MockCardProvisioningServer)(nil).CheckShippingAddressUpdateAndPhysicalCardDispatchStatus), arg0, arg1)
}

// CreateCard mocks base method.
func (m *MockCardProvisioningServer) CreateCard(arg0 context.Context, arg1 *provisioning.CreateCardRequest) (*provisioning.CreateCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCard", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.CreateCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCard indicates an expected call of CreateCard.
func (mr *MockCardProvisioningServerMockRecorder) CreateCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCard", reflect.TypeOf((*MockCardProvisioningServer)(nil).CreateCard), arg0, arg1)
}

// CreatePhysicalCardDispatchAttempt mocks base method.
func (m *MockCardProvisioningServer) CreatePhysicalCardDispatchAttempt(arg0 context.Context, arg1 *provisioning.CreatePhysicalCardDispatchAttemptRequest) (*provisioning.CreatePhysicalCardDispatchAttemptResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePhysicalCardDispatchAttempt", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.CreatePhysicalCardDispatchAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePhysicalCardDispatchAttempt indicates an expected call of CreatePhysicalCardDispatchAttempt.
func (mr *MockCardProvisioningServerMockRecorder) CreatePhysicalCardDispatchAttempt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePhysicalCardDispatchAttempt", reflect.TypeOf((*MockCardProvisioningServer)(nil).CreatePhysicalCardDispatchAttempt), arg0, arg1)
}

// DispatchCard mocks base method.
func (m *MockCardProvisioningServer) DispatchCard(arg0 context.Context, arg1 *provisioning.CardDispatchRequest) (*provisioning.CardDispatchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DispatchCard", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.CardDispatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DispatchCard indicates an expected call of DispatchCard.
func (mr *MockCardProvisioningServerMockRecorder) DispatchCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DispatchCard", reflect.TypeOf((*MockCardProvisioningServer)(nil).DispatchCard), arg0, arg1)
}

// DynamicElementCallback mocks base method.
func (m *MockCardProvisioningServer) DynamicElementCallback(arg0 context.Context, arg1 *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DynamicElementCallback", arg0, arg1)
	ret0, _ := ret[0].(*dynamic_elements.DynamicElementCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DynamicElementCallback indicates an expected call of DynamicElementCallback.
func (mr *MockCardProvisioningServerMockRecorder) DynamicElementCallback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DynamicElementCallback", reflect.TypeOf((*MockCardProvisioningServer)(nil).DynamicElementCallback), arg0, arg1)
}

// FetchCardActivationStatus mocks base method.
func (m *MockCardProvisioningServer) FetchCardActivationStatus(arg0 context.Context, arg1 *provisioning.FetchCardActivationStatusRequest) (*provisioning.FetchCardActivationStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchCardActivationStatus", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.FetchCardActivationStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCardActivationStatus indicates an expected call of FetchCardActivationStatus.
func (mr *MockCardProvisioningServerMockRecorder) FetchCardActivationStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCardActivationStatus", reflect.TypeOf((*MockCardProvisioningServer)(nil).FetchCardActivationStatus), arg0, arg1)
}

// FetchCardCreationStatus mocks base method.
func (m *MockCardProvisioningServer) FetchCardCreationStatus(arg0 context.Context, arg1 *provisioning.FetchCardCreationStatusRequest) (*provisioning.FetchCardCreationStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchCardCreationStatus", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.FetchCardCreationStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCardCreationStatus indicates an expected call of FetchCardCreationStatus.
func (mr *MockCardProvisioningServerMockRecorder) FetchCardCreationStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCardCreationStatus", reflect.TypeOf((*MockCardProvisioningServer)(nil).FetchCardCreationStatus), arg0, arg1)
}

// FetchCardDetails mocks base method.
func (m *MockCardProvisioningServer) FetchCardDetails(arg0 context.Context, arg1 *provisioning.FetchCardDetailsRequest) (*provisioning.FetchCardDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchCardDetails", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.FetchCardDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCardDetails indicates an expected call of FetchCardDetails.
func (mr *MockCardProvisioningServerMockRecorder) FetchCardDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCardDetails", reflect.TypeOf((*MockCardProvisioningServer)(nil).FetchCardDetails), arg0, arg1)
}

// FetchCardDetailsByAccountId mocks base method.
func (m *MockCardProvisioningServer) FetchCardDetailsByAccountId(arg0 context.Context, arg1 *provisioning.FetchCardDetailsByAccountIdRequest) (*provisioning.FetchCardDetailsByAccountIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchCardDetailsByAccountId", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.FetchCardDetailsByAccountIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCardDetailsByAccountId indicates an expected call of FetchCardDetailsByAccountId.
func (mr *MockCardProvisioningServerMockRecorder) FetchCardDetailsByAccountId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCardDetailsByAccountId", reflect.TypeOf((*MockCardProvisioningServer)(nil).FetchCardDetailsByAccountId), arg0, arg1)
}

// FetchCardRenewalChargesForUser mocks base method.
func (m *MockCardProvisioningServer) FetchCardRenewalChargesForUser(arg0 context.Context, arg1 *provisioning.FetchCardRenewalChargesForUserRequest) (*provisioning.FetchCardRenewalChargesForUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchCardRenewalChargesForUser", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.FetchCardRenewalChargesForUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCardRenewalChargesForUser indicates an expected call of FetchCardRenewalChargesForUser.
func (mr *MockCardProvisioningServerMockRecorder) FetchCardRenewalChargesForUser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCardRenewalChargesForUser", reflect.TypeOf((*MockCardProvisioningServer)(nil).FetchCardRenewalChargesForUser), arg0, arg1)
}

// FetchCardTrackingDetails mocks base method.
func (m *MockCardProvisioningServer) FetchCardTrackingDetails(arg0 context.Context, arg1 *provisioning.FetchCardTrackingDetailsRequest) (*provisioning.FetchCardTrackingDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchCardTrackingDetails", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.FetchCardTrackingDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCardTrackingDetails indicates an expected call of FetchCardTrackingDetails.
func (mr *MockCardProvisioningServerMockRecorder) FetchCardTrackingDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCardTrackingDetails", reflect.TypeOf((*MockCardProvisioningServer)(nil).FetchCardTrackingDetails), arg0, arg1)
}

// FetchCards mocks base method.
func (m *MockCardProvisioningServer) FetchCards(arg0 context.Context, arg1 *provisioning.FetchCardsRequest) (*provisioning.FetchCardsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchCards", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.FetchCardsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCards indicates an expected call of FetchCards.
func (mr *MockCardProvisioningServerMockRecorder) FetchCards(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCards", reflect.TypeOf((*MockCardProvisioningServer)(nil).FetchCards), arg0, arg1)
}

// FetchDeliveryTrackingStatus mocks base method.
func (m *MockCardProvisioningServer) FetchDeliveryTrackingStatus(arg0 context.Context, arg1 *provisioning.FetchDeliveryTrackingStatusRequest) (*provisioning.FetchDeliveryTrackingStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchDeliveryTrackingStatus", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.FetchDeliveryTrackingStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchDeliveryTrackingStatus indicates an expected call of FetchDeliveryTrackingStatus.
func (mr *MockCardProvisioningServerMockRecorder) FetchDeliveryTrackingStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchDeliveryTrackingStatus", reflect.TypeOf((*MockCardProvisioningServer)(nil).FetchDeliveryTrackingStatus), arg0, arg1)
}

// FetchDynamicElements mocks base method.
func (m *MockCardProvisioningServer) FetchDynamicElements(arg0 context.Context, arg1 *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchDynamicElements", arg0, arg1)
	ret0, _ := ret[0].(*dynamic_elements.FetchDynamicElementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchDynamicElements indicates an expected call of FetchDynamicElements.
func (mr *MockCardProvisioningServerMockRecorder) FetchDynamicElements(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchDynamicElements", reflect.TypeOf((*MockCardProvisioningServer)(nil).FetchDynamicElements), arg0, arg1)
}

// FetchForexRefundAggregates mocks base method.
func (m *MockCardProvisioningServer) FetchForexRefundAggregates(arg0 context.Context, arg1 *provisioning.FetchForexRefundAggregatesRequest) (*provisioning.FetchForexRefundAggregatesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchForexRefundAggregates", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.FetchForexRefundAggregatesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchForexRefundAggregates indicates an expected call of FetchForexRefundAggregates.
func (mr *MockCardProvisioningServerMockRecorder) FetchForexRefundAggregates(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchForexRefundAggregates", reflect.TypeOf((*MockCardProvisioningServer)(nil).FetchForexRefundAggregates), arg0, arg1)
}

// FetchPhysicalCardChargesForUser mocks base method.
func (m *MockCardProvisioningServer) FetchPhysicalCardChargesForUser(arg0 context.Context, arg1 *provisioning.FetchPhysicalCardChargesForUserRequest) (*provisioning.FetchPhysicalCardChargesForUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchPhysicalCardChargesForUser", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.FetchPhysicalCardChargesForUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchPhysicalCardChargesForUser indicates an expected call of FetchPhysicalCardChargesForUser.
func (mr *MockCardProvisioningServerMockRecorder) FetchPhysicalCardChargesForUser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchPhysicalCardChargesForUser", reflect.TypeOf((*MockCardProvisioningServer)(nil).FetchPhysicalCardChargesForUser), arg0, arg1)
}

// FetchPhysicalCardDispatchRequests mocks base method.
func (m *MockCardProvisioningServer) FetchPhysicalCardDispatchRequests(arg0 context.Context, arg1 *provisioning.FetchPhysicalCardDispatchRequestsRequest) (*provisioning.FetchPhysicalCardDispatchRequestsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchPhysicalCardDispatchRequests", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.FetchPhysicalCardDispatchRequestsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchPhysicalCardDispatchRequests indicates an expected call of FetchPhysicalCardDispatchRequests.
func (mr *MockCardProvisioningServerMockRecorder) FetchPhysicalCardDispatchRequests(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchPhysicalCardDispatchRequests", reflect.TypeOf((*MockCardProvisioningServer)(nil).FetchPhysicalCardDispatchRequests), arg0, arg1)
}

// FetchTransactionableCards mocks base method.
func (m *MockCardProvisioningServer) FetchTransactionableCards(arg0 context.Context, arg1 *provisioning.FetchTransactionableCardsRequest) (*provisioning.FetchTransactionableCardsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchTransactionableCards", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.FetchTransactionableCardsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchTransactionableCards indicates an expected call of FetchTransactionableCards.
func (mr *MockCardProvisioningServerMockRecorder) FetchTransactionableCards(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchTransactionableCards", reflect.TypeOf((*MockCardProvisioningServer)(nil).FetchTransactionableCards), arg0, arg1)
}

// ForceCardCreationEnquiry mocks base method.
func (m *MockCardProvisioningServer) ForceCardCreationEnquiry(arg0 context.Context, arg1 *provisioning.ForceCardCreationEnquiryRequest) (*provisioning.ForceCardCreationEnquiryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ForceCardCreationEnquiry", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ForceCardCreationEnquiryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ForceCardCreationEnquiry indicates an expected call of ForceCardCreationEnquiry.
func (mr *MockCardProvisioningServerMockRecorder) ForceCardCreationEnquiry(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForceCardCreationEnquiry", reflect.TypeOf((*MockCardProvisioningServer)(nil).ForceCardCreationEnquiry), arg0, arg1)
}

// GenerateTxnId mocks base method.
func (m *MockCardProvisioningServer) GenerateTxnId(arg0 context.Context, arg1 *provisioning.GenerateTxnIdRequest) (*provisioning.GenerateTxnIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateTxnId", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GenerateTxnIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateTxnId indicates an expected call of GenerateTxnId.
func (mr *MockCardProvisioningServerMockRecorder) GenerateTxnId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateTxnId", reflect.TypeOf((*MockCardProvisioningServer)(nil).GenerateTxnId), arg0, arg1)
}

// GetAdditionalAuthInfo mocks base method.
func (m *MockCardProvisioningServer) GetAdditionalAuthInfo(arg0 context.Context, arg1 *provisioning.GetAdditionalAuthInfoRequest) (*provisioning.GetAdditionalAuthInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAdditionalAuthInfo", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetAdditionalAuthInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAdditionalAuthInfo indicates an expected call of GetAdditionalAuthInfo.
func (mr *MockCardProvisioningServerMockRecorder) GetAdditionalAuthInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAdditionalAuthInfo", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetAdditionalAuthInfo), arg0, arg1)
}

// GetCardActionAttempts mocks base method.
func (m *MockCardProvisioningServer) GetCardActionAttempts(arg0 context.Context, arg1 *provisioning.GetCardActionAttemptsRequest) (*provisioning.GetCardActionAttemptsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardActionAttempts", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetCardActionAttemptsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardActionAttempts indicates an expected call of GetCardActionAttempts.
func (mr *MockCardProvisioningServerMockRecorder) GetCardActionAttempts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardActionAttempts", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetCardActionAttempts), arg0, arg1)
}

// GetCardDetailsWithCvv mocks base method.
func (m *MockCardProvisioningServer) GetCardDetailsWithCvv(arg0 context.Context, arg1 *provisioning.GetCardDetailsWithCvvRequest) (*provisioning.GetCardDetailsWithCvvResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardDetailsWithCvv", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetCardDetailsWithCvvResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardDetailsWithCvv indicates an expected call of GetCardDetailsWithCvv.
func (mr *MockCardProvisioningServerMockRecorder) GetCardDetailsWithCvv(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardDetailsWithCvv", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetCardDetailsWithCvv), arg0, arg1)
}

// GetCardGroups mocks base method.
func (m *MockCardProvisioningServer) GetCardGroups(arg0 context.Context, arg1 *provisioning.GetCardGroupsRequest) (*provisioning.GetCardGroupsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardGroups", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetCardGroupsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardGroups indicates an expected call of GetCardGroups.
func (mr *MockCardProvisioningServerMockRecorder) GetCardGroups(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardGroups", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetCardGroups), arg0, arg1)
}

// GetCardShipmentTrackingDetails mocks base method.
func (m *MockCardProvisioningServer) GetCardShipmentTrackingDetails(arg0 context.Context, arg1 *provisioning.GetCardShipmentTrackingDetailsRequest) (*provisioning.GetCardShipmentTrackingDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardShipmentTrackingDetails", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetCardShipmentTrackingDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardShipmentTrackingDetails indicates an expected call of GetCardShipmentTrackingDetails.
func (mr *MockCardProvisioningServerMockRecorder) GetCardShipmentTrackingDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardShipmentTrackingDetails", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetCardShipmentTrackingDetails), arg0, arg1)
}

// GetCardSwitchNotification mocks base method.
func (m *MockCardProvisioningServer) GetCardSwitchNotification(arg0 context.Context, arg1 *provisioning.GetCardSwitchNotificationRequest) (*provisioning.GetCardSwitchNotificationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardSwitchNotification", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetCardSwitchNotificationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardSwitchNotification indicates an expected call of GetCardSwitchNotification.
func (mr *MockCardProvisioningServerMockRecorder) GetCardSwitchNotification(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardSwitchNotification", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetCardSwitchNotification), arg0, arg1)
}

// GetDcInternationalWidget mocks base method.
func (m *MockCardProvisioningServer) GetDcInternationalWidget(arg0 context.Context, arg1 *provisioning.GetDcInternationalWidgetRequest) (*provisioning.GetDcInternationalWidgetResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDcInternationalWidget", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetDcInternationalWidgetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDcInternationalWidget indicates an expected call of GetDcInternationalWidget.
func (mr *MockCardProvisioningServerMockRecorder) GetDcInternationalWidget(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDcInternationalWidget", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetDcInternationalWidget), arg0, arg1)
}

// GetForexRefunds mocks base method.
func (m *MockCardProvisioningServer) GetForexRefunds(arg0 context.Context, arg1 *provisioning.GetForexRefundsRequest) (*provisioning.GetForexRefundsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetForexRefunds", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetForexRefundsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForexRefunds indicates an expected call of GetForexRefunds.
func (mr *MockCardProvisioningServerMockRecorder) GetForexRefunds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForexRefunds", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetForexRefunds), arg0, arg1)
}

// GetForexRefundsByActorId mocks base method.
func (m *MockCardProvisioningServer) GetForexRefundsByActorId(arg0 context.Context, arg1 *provisioning.GetForexRefundsByActorIdRequest) (*provisioning.GetForexRefundsByActorIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetForexRefundsByActorId", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetForexRefundsByActorIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForexRefundsByActorId indicates an expected call of GetForexRefundsByActorId.
func (mr *MockCardProvisioningServerMockRecorder) GetForexRefundsByActorId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForexRefundsByActorId", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetForexRefundsByActorId), arg0, arg1)
}

// GetHomeLayoutConfiguration mocks base method.
func (m *MockCardProvisioningServer) GetHomeLayoutConfiguration(arg0 context.Context, arg1 *provisioning.GetHomeLayoutConfigurationRequest) (*provisioning.GetHomeLayoutConfigurationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHomeLayoutConfiguration", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetHomeLayoutConfigurationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHomeLayoutConfiguration indicates an expected call of GetHomeLayoutConfiguration.
func (mr *MockCardProvisioningServerMockRecorder) GetHomeLayoutConfiguration(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHomeLayoutConfiguration", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetHomeLayoutConfiguration), arg0, arg1)
}

// GetLastPhysicalCardIssuedForUser mocks base method.
func (m *MockCardProvisioningServer) GetLastPhysicalCardIssuedForUser(arg0 context.Context, arg1 *provisioning.GetLastPhysicalCardIssuedForUserRequest) (*provisioning.GetLastPhysicalCardIssuedForUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastPhysicalCardIssuedForUser", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetLastPhysicalCardIssuedForUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastPhysicalCardIssuedForUser indicates an expected call of GetLastPhysicalCardIssuedForUser.
func (mr *MockCardProvisioningServerMockRecorder) GetLastPhysicalCardIssuedForUser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastPhysicalCardIssuedForUser", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetLastPhysicalCardIssuedForUser), arg0, arg1)
}

// GetLatestCardForActorIds mocks base method.
func (m *MockCardProvisioningServer) GetLatestCardForActorIds(arg0 context.Context, arg1 *provisioning.GetLatestCardForActorIdsRequest) (*provisioning.GetLatestCardForActorIdsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestCardForActorIds", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetLatestCardForActorIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestCardForActorIds indicates an expected call of GetLatestCardForActorIds.
func (mr *MockCardProvisioningServerMockRecorder) GetLatestCardForActorIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestCardForActorIds", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetLatestCardForActorIds), arg0, arg1)
}

// GetPaginatedForexRefundsByActorId mocks base method.
func (m *MockCardProvisioningServer) GetPaginatedForexRefundsByActorId(arg0 context.Context, arg1 *provisioning.GetPaginatedForexRefundsByActorIdRequest) (*provisioning.GetPaginatedForexRefundsByActorIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaginatedForexRefundsByActorId", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetPaginatedForexRefundsByActorIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaginatedForexRefundsByActorId indicates an expected call of GetPaginatedForexRefundsByActorId.
func (mr *MockCardProvisioningServerMockRecorder) GetPaginatedForexRefundsByActorId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaginatedForexRefundsByActorId", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetPaginatedForexRefundsByActorId), arg0, arg1)
}

// GetPhysicalCardActivationInfo mocks base method.
func (m *MockCardProvisioningServer) GetPhysicalCardActivationInfo(arg0 context.Context, arg1 *provisioning.GetPhysicalCardActivationInfoRequest) (*provisioning.GetPhysicalCardActivationInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPhysicalCardActivationInfo", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetPhysicalCardActivationInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPhysicalCardActivationInfo indicates an expected call of GetPhysicalCardActivationInfo.
func (mr *MockCardProvisioningServerMockRecorder) GetPhysicalCardActivationInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPhysicalCardActivationInfo", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetPhysicalCardActivationInfo), arg0, arg1)
}

// GetPhysicalCardDispatchStatus mocks base method.
func (m *MockCardProvisioningServer) GetPhysicalCardDispatchStatus(arg0 context.Context, arg1 *provisioning.GetPhysicalCardDispatchStatusRequest) (*provisioning.GetPhysicalCardDispatchStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPhysicalCardDispatchStatus", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.GetPhysicalCardDispatchStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPhysicalCardDispatchStatus indicates an expected call of GetPhysicalCardDispatchStatus.
func (mr *MockCardProvisioningServerMockRecorder) GetPhysicalCardDispatchStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPhysicalCardDispatchStatus", reflect.TypeOf((*MockCardProvisioningServer)(nil).GetPhysicalCardDispatchStatus), arg0, arg1)
}

// InitiateAdditionalAuthAttempt mocks base method.
func (m *MockCardProvisioningServer) InitiateAdditionalAuthAttempt(arg0 context.Context, arg1 *provisioning.InitiateAdditionalAuthAttemptRequest) (*provisioning.InitiateAdditionalAuthAttemptResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateAdditionalAuthAttempt", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.InitiateAdditionalAuthAttemptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateAdditionalAuthAttempt indicates an expected call of InitiateAdditionalAuthAttempt.
func (mr *MockCardProvisioningServerMockRecorder) InitiateAdditionalAuthAttempt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateAdditionalAuthAttempt", reflect.TypeOf((*MockCardProvisioningServer)(nil).InitiateAdditionalAuthAttempt), arg0, arg1)
}

// InitiatePhysicalCardDispatch mocks base method.
func (m *MockCardProvisioningServer) InitiatePhysicalCardDispatch(arg0 context.Context, arg1 *provisioning.InitiatePhysicalCardDispatchRequest) (*provisioning.InitiatePhysicalCardDispatchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiatePhysicalCardDispatch", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.InitiatePhysicalCardDispatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiatePhysicalCardDispatch indicates an expected call of InitiatePhysicalCardDispatch.
func (mr *MockCardProvisioningServerMockRecorder) InitiatePhysicalCardDispatch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiatePhysicalCardDispatch", reflect.TypeOf((*MockCardProvisioningServer)(nil).InitiatePhysicalCardDispatch), arg0, arg1)
}

// InitiateShippingAddressUpdateAndDispatchPhysicalCard mocks base method.
func (m *MockCardProvisioningServer) InitiateShippingAddressUpdateAndDispatchPhysicalCard(arg0 context.Context, arg1 *provisioning.InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) (*provisioning.InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateShippingAddressUpdateAndDispatchPhysicalCard", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateShippingAddressUpdateAndDispatchPhysicalCard indicates an expected call of InitiateShippingAddressUpdateAndDispatchPhysicalCard.
func (mr *MockCardProvisioningServerMockRecorder) InitiateShippingAddressUpdateAndDispatchPhysicalCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateShippingAddressUpdateAndDispatchPhysicalCard", reflect.TypeOf((*MockCardProvisioningServer)(nil).InitiateShippingAddressUpdateAndDispatchPhysicalCard), arg0, arg1)
}

// ProcessManualCardPinSet mocks base method.
func (m *MockCardProvisioningServer) ProcessManualCardPinSet(arg0 context.Context, arg1 *provisioning.ProcessManualCardPinSetRequest) (*provisioning.ProcessManualCardPinSetResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessManualCardPinSet", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessManualCardPinSetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessManualCardPinSet indicates an expected call of ProcessManualCardPinSet.
func (mr *MockCardProvisioningServerMockRecorder) ProcessManualCardPinSet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessManualCardPinSet", reflect.TypeOf((*MockCardProvisioningServer)(nil).ProcessManualCardPinSet), arg0, arg1)
}

// ProcessManualCardUnsuspend mocks base method.
func (m *MockCardProvisioningServer) ProcessManualCardUnsuspend(arg0 context.Context, arg1 *provisioning.ProcessManualCardUnsuspendRequest) (*provisioning.ProcessManualCardUnsuspendResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessManualCardUnsuspend", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ProcessManualCardUnsuspendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessManualCardUnsuspend indicates an expected call of ProcessManualCardUnsuspend.
func (mr *MockCardProvisioningServerMockRecorder) ProcessManualCardUnsuspend(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessManualCardUnsuspend", reflect.TypeOf((*MockCardProvisioningServer)(nil).ProcessManualCardUnsuspend), arg0, arg1)
}

// RenewCard mocks base method.
func (m *MockCardProvisioningServer) RenewCard(arg0 context.Context, arg1 *provisioning.RenewCardRequest) (*provisioning.RenewCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RenewCard", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.RenewCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RenewCard indicates an expected call of RenewCard.
func (mr *MockCardProvisioningServerMockRecorder) RenewCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RenewCard", reflect.TypeOf((*MockCardProvisioningServer)(nil).RenewCard), arg0, arg1)
}

// RenewCardStatus mocks base method.
func (m *MockCardProvisioningServer) RenewCardStatus(arg0 context.Context, arg1 *provisioning.RenewCardStatusRequest) (*provisioning.RenewCardStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RenewCardStatus", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.RenewCardStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RenewCardStatus indicates an expected call of RenewCardStatus.
func (mr *MockCardProvisioningServerMockRecorder) RenewCardStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RenewCardStatus", reflect.TypeOf((*MockCardProvisioningServer)(nil).RenewCardStatus), arg0, arg1)
}

// ResetCardPin mocks base method.
func (m *MockCardProvisioningServer) ResetCardPin(arg0 context.Context, arg1 *provisioning.ResetCardPinRequest) (*provisioning.ResetCardPinResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetCardPin", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ResetCardPinResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResetCardPin indicates an expected call of ResetCardPin.
func (mr *MockCardProvisioningServerMockRecorder) ResetCardPin(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetCardPin", reflect.TypeOf((*MockCardProvisioningServer)(nil).ResetCardPin), arg0, arg1)
}

// SetCardPin mocks base method.
func (m *MockCardProvisioningServer) SetCardPin(arg0 context.Context, arg1 *provisioning.SetCardPinRequest) (*provisioning.SetCardPinResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCardPin", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.SetCardPinResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetCardPin indicates an expected call of SetCardPin.
func (mr *MockCardProvisioningServerMockRecorder) SetCardPin(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCardPin", reflect.TypeOf((*MockCardProvisioningServer)(nil).SetCardPin), arg0, arg1)
}

// TriggerCardNotifications mocks base method.
func (m *MockCardProvisioningServer) TriggerCardNotifications(arg0 context.Context, arg1 *provisioning.TriggerCardNotificationsRequest) (*provisioning.TriggerCardNotificationsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerCardNotifications", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.TriggerCardNotificationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerCardNotifications indicates an expected call of TriggerCardNotifications.
func (mr *MockCardProvisioningServerMockRecorder) TriggerCardNotifications(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerCardNotifications", reflect.TypeOf((*MockCardProvisioningServer)(nil).TriggerCardNotifications), arg0, arg1)
}

// UpdateAuthInfo mocks base method.
func (m *MockCardProvisioningServer) UpdateAuthInfo(arg0 context.Context, arg1 *provisioning.UpdateAuthInfoRequest) (*provisioning.UpdateAuthInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAuthInfo", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.UpdateAuthInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAuthInfo indicates an expected call of UpdateAuthInfo.
func (mr *MockCardProvisioningServerMockRecorder) UpdateAuthInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAuthInfo", reflect.TypeOf((*MockCardProvisioningServer)(nil).UpdateAuthInfo), arg0, arg1)
}

// UpdateFreeCardReplacement mocks base method.
func (m *MockCardProvisioningServer) UpdateFreeCardReplacement(arg0 context.Context, arg1 *provisioning.UpdateFreeCardReplacementRequest) (*provisioning.UpdateFreeCardReplacementResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFreeCardReplacement", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.UpdateFreeCardReplacementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateFreeCardReplacement indicates an expected call of UpdateFreeCardReplacement.
func (mr *MockCardProvisioningServerMockRecorder) UpdateFreeCardReplacement(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFreeCardReplacement", reflect.TypeOf((*MockCardProvisioningServer)(nil).UpdateFreeCardReplacement), arg0, arg1)
}

// UpdateTrackingDetails mocks base method.
func (m *MockCardProvisioningServer) UpdateTrackingDetails(arg0 context.Context, arg1 *provisioning.UpdateTrackingDetailsRequest) (*provisioning.UpdateTrackingDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTrackingDetails", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.UpdateTrackingDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTrackingDetails indicates an expected call of UpdateTrackingDetails.
func (mr *MockCardProvisioningServerMockRecorder) UpdateTrackingDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTrackingDetails", reflect.TypeOf((*MockCardProvisioningServer)(nil).UpdateTrackingDetails), arg0, arg1)
}

// UploadCardTrackingDetails mocks base method.
func (m *MockCardProvisioningServer) UploadCardTrackingDetails(arg0 context.Context, arg1 *provisioning.UploadCardTrackingDetailsRequest) (*provisioning.UploadCardTrackingDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadCardTrackingDetails", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.UploadCardTrackingDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadCardTrackingDetails indicates an expected call of UploadCardTrackingDetails.
func (mr *MockCardProvisioningServerMockRecorder) UploadCardTrackingDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadCardTrackingDetails", reflect.TypeOf((*MockCardProvisioningServer)(nil).UploadCardTrackingDetails), arg0, arg1)
}

// ValidateCard mocks base method.
func (m *MockCardProvisioningServer) ValidateCard(arg0 context.Context, arg1 *provisioning.CardValidateRequest) (*provisioning.CardValidateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateCard", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.CardValidateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateCard indicates an expected call of ValidateCard.
func (mr *MockCardProvisioningServerMockRecorder) ValidateCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateCard", reflect.TypeOf((*MockCardProvisioningServer)(nil).ValidateCard), arg0, arg1)
}

// ValidateCardPin mocks base method.
func (m *MockCardProvisioningServer) ValidateCardPin(arg0 context.Context, arg1 *provisioning.ValidateCardPinRequest) (*provisioning.ValidateCardPinResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateCardPin", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.ValidateCardPinResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateCardPin indicates an expected call of ValidateCardPin.
func (mr *MockCardProvisioningServerMockRecorder) ValidateCardPin(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateCardPin", reflect.TypeOf((*MockCardProvisioningServer)(nil).ValidateCardPin), arg0, arg1)
}

// VerifyQRCode mocks base method.
func (m *MockCardProvisioningServer) VerifyQRCode(arg0 context.Context, arg1 *provisioning.VerifyQRCodeRequest) (*provisioning.VerifyQRCodeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyQRCode", arg0, arg1)
	ret0, _ := ret[0].(*provisioning.VerifyQRCodeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyQRCode indicates an expected call of VerifyQRCode.
func (mr *MockCardProvisioningServerMockRecorder) VerifyQRCode(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyQRCode", reflect.TypeOf((*MockCardProvisioningServer)(nil).VerifyQRCode), arg0, arg1)
}

// MockUnsafeCardProvisioningServer is a mock of UnsafeCardProvisioningServer interface.
type MockUnsafeCardProvisioningServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCardProvisioningServerMockRecorder
}

// MockUnsafeCardProvisioningServerMockRecorder is the mock recorder for MockUnsafeCardProvisioningServer.
type MockUnsafeCardProvisioningServerMockRecorder struct {
	mock *MockUnsafeCardProvisioningServer
}

// NewMockUnsafeCardProvisioningServer creates a new mock instance.
func NewMockUnsafeCardProvisioningServer(ctrl *gomock.Controller) *MockUnsafeCardProvisioningServer {
	mock := &MockUnsafeCardProvisioningServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCardProvisioningServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCardProvisioningServer) EXPECT() *MockUnsafeCardProvisioningServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCardProvisioningServer mocks base method.
func (m *MockUnsafeCardProvisioningServer) mustEmbedUnimplementedCardProvisioningServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCardProvisioningServer")
}

// mustEmbedUnimplementedCardProvisioningServer indicates an expected call of mustEmbedUnimplementedCardProvisioningServer.
func (mr *MockUnsafeCardProvisioningServerMockRecorder) mustEmbedUnimplementedCardProvisioningServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCardProvisioningServer", reflect.TypeOf((*MockUnsafeCardProvisioningServer)(nil).mustEmbedUnimplementedCardProvisioningServer))
}
