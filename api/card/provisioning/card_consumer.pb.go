//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/provisioning/card_consumer.proto

package provisioning

import (
	queue "github.com/epifi/be-common/api/queue"
	notification "github.com/epifi/gamma/api/auth/notification"
	s3 "github.com/epifi/gamma/api/aws/s3"
	card "github.com/epifi/gamma/api/card"
	event "github.com/epifi/gamma/api/user/event"
	onboarding "github.com/epifi/gamma/api/user/onboarding"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessUserDevicePropertiesUpdateEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessUserDevicePropertiesUpdateEventResponse) Reset() {
	*x = ProcessUserDevicePropertiesUpdateEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessUserDevicePropertiesUpdateEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessUserDevicePropertiesUpdateEventResponse) ProtoMessage() {}

func (x *ProcessUserDevicePropertiesUpdateEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessUserDevicePropertiesUpdateEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessUserDevicePropertiesUpdateEventResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessUserDevicePropertiesUpdateEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardsDispatchedCsvFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// The notification message that Amazon S3 sends to publish an event is in the JSON format
	Records []*s3.Record `protobuf:"bytes,2,rep,name=records,json=Records,proto3" json:"records,omitempty"`
}

func (x *ProcessCardsDispatchedCsvFileRequest) Reset() {
	*x = ProcessCardsDispatchedCsvFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardsDispatchedCsvFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardsDispatchedCsvFileRequest) ProtoMessage() {}

func (x *ProcessCardsDispatchedCsvFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardsDispatchedCsvFileRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardsDispatchedCsvFileRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessCardsDispatchedCsvFileRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardsDispatchedCsvFileRequest) GetRecords() []*s3.Record {
	if x != nil {
		return x.Records
	}
	return nil
}

type ProcessCardsDispatchedCsvFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardsDispatchedCsvFileResponse) Reset() {
	*x = ProcessCardsDispatchedCsvFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardsDispatchedCsvFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardsDispatchedCsvFileResponse) ProtoMessage() {}

func (x *ProcessCardsDispatchedCsvFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardsDispatchedCsvFileResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardsDispatchedCsvFileResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessCardsDispatchedCsvFileResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardDispatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// unique identifier for the dispatch request
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *ProcessCardDispatchRequest) Reset() {
	*x = ProcessCardDispatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardDispatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardDispatchRequest) ProtoMessage() {}

func (x *ProcessCardDispatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardDispatchRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardDispatchRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessCardDispatchRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardDispatchRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type ProcessCardDispatchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardDispatchResponse) Reset() {
	*x = ProcessCardDispatchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardDispatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardDispatchResponse) ProtoMessage() {}

func (x *ProcessCardDispatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardDispatchResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardDispatchResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{4}
}

func (x *ProcessCardDispatchResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	CardId        string                       `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest) Reset() {
	*x = ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest) ProtoMessage() {}

func (x *ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest.ProtoReflect.Descriptor instead.
func (*ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{5}
}

func (x *ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

type ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse) Reset() {
	*x = ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse) ProtoMessage() {}

func (x *ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse.ProtoReflect.Descriptor instead.
func (*ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{6}
}

func (x *ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardShipmentRegisterEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Unique identifier for each shipment
	OrderIds []string `protobuf:"bytes,2,rep,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
}

func (x *ProcessCardShipmentRegisterEventRequest) Reset() {
	*x = ProcessCardShipmentRegisterEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardShipmentRegisterEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardShipmentRegisterEventRequest) ProtoMessage() {}

func (x *ProcessCardShipmentRegisterEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardShipmentRegisterEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardShipmentRegisterEventRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{7}
}

func (x *ProcessCardShipmentRegisterEventRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardShipmentRegisterEventRequest) GetOrderIds() []string {
	if x != nil {
		return x.OrderIds
	}
	return nil
}

type ProcessCardShipmentRegisterEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardShipmentRegisterEventResponse) Reset() {
	*x = ProcessCardShipmentRegisterEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardShipmentRegisterEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardShipmentRegisterEventResponse) ProtoMessage() {}

func (x *ProcessCardShipmentRegisterEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardShipmentRegisterEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardShipmentRegisterEventResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{8}
}

func (x *ProcessCardShipmentRegisterEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardDeliveryDelayEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	CardId        string                       `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *ProcessCardDeliveryDelayEventRequest) Reset() {
	*x = ProcessCardDeliveryDelayEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardDeliveryDelayEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardDeliveryDelayEventRequest) ProtoMessage() {}

func (x *ProcessCardDeliveryDelayEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardDeliveryDelayEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardDeliveryDelayEventRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{9}
}

func (x *ProcessCardDeliveryDelayEventRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardDeliveryDelayEventRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

type ProcessCardDeliveryDelayEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardDeliveryDelayEventResponse) Reset() {
	*x = ProcessCardDeliveryDelayEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardDeliveryDelayEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardDeliveryDelayEventResponse) ProtoMessage() {}

func (x *ProcessCardDeliveryDelayEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardDeliveryDelayEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardDeliveryDelayEventResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{10}
}

func (x *ProcessCardDeliveryDelayEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardRenewalEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// card_id to be blocked
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// reason for blocking the card
	BlockCardReason string `protobuf:"bytes,3,opt,name=block_card_reason,json=blockCardReason,proto3" json:"block_card_reason,omitempty"`
	// source of card block
	BlockCardProvenance card.Provenance `protobuf:"varint,4,opt,name=block_card_provenance,json=blockCardProvenance,proto3,enum=card.Provenance" json:"block_card_provenance,omitempty"`
}

func (x *ProcessCardRenewalEventRequest) Reset() {
	*x = ProcessCardRenewalEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardRenewalEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardRenewalEventRequest) ProtoMessage() {}

func (x *ProcessCardRenewalEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardRenewalEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardRenewalEventRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{11}
}

func (x *ProcessCardRenewalEventRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardRenewalEventRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *ProcessCardRenewalEventRequest) GetBlockCardReason() string {
	if x != nil {
		return x.BlockCardReason
	}
	return ""
}

func (x *ProcessCardRenewalEventRequest) GetBlockCardProvenance() card.Provenance {
	if x != nil {
		return x.BlockCardProvenance
	}
	return card.Provenance(0)
}

type ProcessCardRenewalEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardRenewalEventResponse) Reset() {
	*x = ProcessCardRenewalEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardRenewalEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardRenewalEventResponse) ProtoMessage() {}

func (x *ProcessCardRenewalEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardRenewalEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardRenewalEventResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{12}
}

func (x *ProcessCardRenewalEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardCreationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// card_id to be processed
	CardId string `protobuf:"bytes,3,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *ProcessCardCreationRequest) Reset() {
	*x = ProcessCardCreationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardCreationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardCreationRequest) ProtoMessage() {}

func (x *ProcessCardCreationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardCreationRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardCreationRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{13}
}

func (x *ProcessCardCreationRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardCreationRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

type ProcessCardCreationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardCreationResponse) Reset() {
	*x = ProcessCardCreationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardCreationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardCreationResponse) ProtoMessage() {}

func (x *ProcessCardCreationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardCreationResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardCreationResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{14}
}

func (x *ProcessCardCreationResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardPiCreationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// The card domain proto
	Card *card.Card `protobuf:"bytes,2,opt,name=card,proto3" json:"card,omitempty"`
}

func (x *ProcessCardPiCreationRequest) Reset() {
	*x = ProcessCardPiCreationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardPiCreationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardPiCreationRequest) ProtoMessage() {}

func (x *ProcessCardPiCreationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardPiCreationRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardPiCreationRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{15}
}

func (x *ProcessCardPiCreationRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardPiCreationRequest) GetCard() *card.Card {
	if x != nil {
		return x.Card
	}
	return nil
}

type ProcessCardPiCreationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardPiCreationResponse) Reset() {
	*x = ProcessCardPiCreationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardPiCreationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardPiCreationResponse) ProtoMessage() {}

func (x *ProcessCardPiCreationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardPiCreationResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardPiCreationResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{16}
}

func (x *ProcessCardPiCreationResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardPinSetEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// The card domain proto
	Card *card.Card `protobuf:"bytes,2,opt,name=card,proto3" json:"card,omitempty"`
}

func (x *ProcessCardPinSetEventRequest) Reset() {
	*x = ProcessCardPinSetEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardPinSetEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardPinSetEventRequest) ProtoMessage() {}

func (x *ProcessCardPinSetEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardPinSetEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardPinSetEventRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{17}
}

func (x *ProcessCardPinSetEventRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardPinSetEventRequest) GetCard() *card.Card {
	if x != nil {
		return x.Card
	}
	return nil
}

type ProcessCardPinSetEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardPinSetEventResponse) Reset() {
	*x = ProcessCardPinSetEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardPinSetEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardPinSetEventResponse) ProtoMessage() {}

func (x *ProcessCardPinSetEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardPinSetEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardPinSetEventResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{18}
}

func (x *ProcessCardPinSetEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardOnboardingEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardOnboardingEventResponse) Reset() {
	*x = ProcessCardOnboardingEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardOnboardingEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardOnboardingEventResponse) ProtoMessage() {}

func (x *ProcessCardOnboardingEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardOnboardingEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardOnboardingEventResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{19}
}

func (x *ProcessCardOnboardingEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessAuthFactorUpdateEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessAuthFactorUpdateEventResponse) Reset() {
	*x = ProcessAuthFactorUpdateEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessAuthFactorUpdateEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessAuthFactorUpdateEventResponse) ProtoMessage() {}

func (x *ProcessAuthFactorUpdateEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessAuthFactorUpdateEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessAuthFactorUpdateEventResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{20}
}

func (x *ProcessAuthFactorUpdateEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardDeliveredEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	CardId        string                       `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *ProcessCardDeliveredEventRequest) Reset() {
	*x = ProcessCardDeliveredEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardDeliveredEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardDeliveredEventRequest) ProtoMessage() {}

func (x *ProcessCardDeliveredEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardDeliveredEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardDeliveredEventRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{21}
}

func (x *ProcessCardDeliveredEventRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardDeliveredEventRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

type ProcessCardDeliveredEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardDeliveredEventResponse) Reset() {
	*x = ProcessCardDeliveredEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardDeliveredEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardDeliveredEventResponse) ProtoMessage() {}

func (x *ProcessCardDeliveredEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardDeliveredEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardDeliveredEventResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{22}
}

func (x *ProcessCardDeliveredEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type GetTrackingDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	CardId        string                       `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *GetTrackingDetailsRequest) Reset() {
	*x = GetTrackingDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTrackingDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTrackingDetailsRequest) ProtoMessage() {}

func (x *GetTrackingDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTrackingDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetTrackingDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{23}
}

func (x *GetTrackingDetailsRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *GetTrackingDetailsRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

type GetTrackingDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,2,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *GetTrackingDetailsResponse) Reset() {
	*x = GetTrackingDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTrackingDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTrackingDetailsResponse) ProtoMessage() {}

func (x *GetTrackingDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTrackingDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetTrackingDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{24}
}

func (x *GetTrackingDetailsResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardAmcChargesEligibleUserFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// The notification message that Amazon S3 sends to publish an event is in the JSON format
	// This will contain the file path where the actual users eligible for amc charging will be available.
	Records []*s3.Record `protobuf:"bytes,2,rep,name=records,json=Records,proto3" json:"records,omitempty"`
}

func (x *ProcessCardAmcChargesEligibleUserFileRequest) Reset() {
	*x = ProcessCardAmcChargesEligibleUserFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardAmcChargesEligibleUserFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardAmcChargesEligibleUserFileRequest) ProtoMessage() {}

func (x *ProcessCardAmcChargesEligibleUserFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardAmcChargesEligibleUserFileRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardAmcChargesEligibleUserFileRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{25}
}

func (x *ProcessCardAmcChargesEligibleUserFileRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardAmcChargesEligibleUserFileRequest) GetRecords() []*s3.Record {
	if x != nil {
		return x.Records
	}
	return nil
}

type ProcessCardAmcChargesEligibleUserFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardAmcChargesEligibleUserFileResponse) Reset() {
	*x = ProcessCardAmcChargesEligibleUserFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardAmcChargesEligibleUserFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardAmcChargesEligibleUserFileResponse) ProtoMessage() {}

func (x *ProcessCardAmcChargesEligibleUserFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardAmcChargesEligibleUserFileResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardAmcChargesEligibleUserFileResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{26}
}

func (x *ProcessCardAmcChargesEligibleUserFileResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type OrderPhysicalCardCriticalNotificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	ActorId       string                       `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *OrderPhysicalCardCriticalNotificationRequest) Reset() {
	*x = OrderPhysicalCardCriticalNotificationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderPhysicalCardCriticalNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderPhysicalCardCriticalNotificationRequest) ProtoMessage() {}

func (x *OrderPhysicalCardCriticalNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderPhysicalCardCriticalNotificationRequest.ProtoReflect.Descriptor instead.
func (*OrderPhysicalCardCriticalNotificationRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{27}
}

func (x *OrderPhysicalCardCriticalNotificationRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *OrderPhysicalCardCriticalNotificationRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type OrderPhysicalCardCriticalNotificationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *OrderPhysicalCardCriticalNotificationResponse) Reset() {
	*x = OrderPhysicalCardCriticalNotificationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderPhysicalCardCriticalNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderPhysicalCardCriticalNotificationResponse) ProtoMessage() {}

func (x *OrderPhysicalCardCriticalNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_consumer_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderPhysicalCardCriticalNotificationResponse.ProtoReflect.Descriptor instead.
func (*OrderPhysicalCardCriticalNotificationResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_consumer_proto_rawDescGZIP(), []int{28}
}

func (x *OrderPhysicalCardCriticalNotificationResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_card_provisioning_card_consumer_proto protoreflect.FileDescriptor

var file_api_card_provisioning_card_consumer_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x1a, 0x34,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x77, 0x73, 0x2f, 0x73, 0x33,
	0x2f, 0x73, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x78,
	0x0a, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x95, 0x01, 0x0a, 0x24, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x73, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x64, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x73, 0x33,
	0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x22, 0x6f, 0x0a, 0x25, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x73,
	0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0x80, 0x01, 0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72,
	0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x22, 0x65, 0x0a, 0x1b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43,
	0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x9a, 0x01, 0x0a, 0x3a,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x44,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x22, 0x85, 0x01, 0x0a, 0x3b, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x53, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x44, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x22, 0x8b, 0x01, 0x0a, 0x27, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64,
	0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x72,
	0x0a, 0x28, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x53, 0x68, 0x69,
	0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x22, 0x84, 0x01, 0x0a, 0x24, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61,
	0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x22, 0x6f, 0x0a, 0x25, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x44, 0x65, 0x6c, 0x61, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xf0, 0x01, 0x0a, 0x1e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x61,
	0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x15, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x50, 0x72,
	0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x13, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x43,
	0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x69, 0x0a,
	0x1f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x6e, 0x65,
	0x77, 0x61, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x7a, 0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61,
	0x72, 0x64, 0x49, 0x64, 0x22, 0x65, 0x0a, 0x1b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43,
	0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x83, 0x01, 0x0a, 0x1c,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x50, 0x69, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x1e, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0a, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x04, 0x63, 0x61, 0x72,
	0x64, 0x22, 0x67, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64,
	0x50, 0x69, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x84, 0x01, 0x0a, 0x1d, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x1e, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0a, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x04, 0x63, 0x61, 0x72,
	0x64, 0x22, 0x68, 0x0a, 0x1e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64,
	0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x6c, 0x0a, 0x22, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x6e, 0x0a, 0x24, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x80, 0x01, 0x0a, 0x20, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43,
	0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x22, 0x6b, 0x0a, 0x21,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x79, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61,
	0x72, 0x64, 0x49, 0x64, 0x22, 0x64, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x9d, 0x01, 0x0a, 0x2c, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x41, 0x6d, 0x63, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x28, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x73, 0x33, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x07, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x77, 0x0a, 0x2d, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x41, 0x6d, 0x63, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x22, 0x8e, 0x01, 0x0a, 0x2c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x68, 0x79,
	0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x69, 0x74, 0x69, 0x63, 0x61,
	0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x22, 0x77, 0x0a, 0x2d, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x68, 0x79,
	0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x69, 0x74, 0x69, 0x63, 0x61,
	0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0x90, 0x12,
	0x0a, 0x0c, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x12, 0x76,
	0x0a, 0x13, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7c, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x43, 0x61, 0x72, 0x64, 0x50, 0x69, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x50,
	0x69, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x30, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64,
	0x50, 0x69, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x7f, 0x0a, 0x16, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43,
	0x61, 0x72, 0x64, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x30,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69,
	0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x50, 0x69,
	0x6e, 0x53, 0x65, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64,
	0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x82, 0x01, 0x0a, 0x17, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x1a, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x67, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x1a, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64,
	0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x76, 0x0a, 0x1c, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x9d, 0x01, 0x0a, 0x20, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72,
	0x64, 0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61,
	0x72, 0x64, 0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x94, 0x01, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72,
	0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x12, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43,
	0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x61, 0x79,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x88, 0x01, 0x0a, 0x19, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x65,
	0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x65, 0x64, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x73, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x76, 0x0a, 0x13, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x12,
	0x2d, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x44,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69,
	0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69,
	0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0xd6, 0x01, 0x0a, 0x33, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x68, 0x69, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x6e, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x50, 0x68, 0x79, 0x73,
	0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x12, 0x4d, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x53, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x44, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x53, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x94, 0x01, 0x0a, 0x1d, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x73, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x64, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x37, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x73, 0x44, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x43, 0x61, 0x72, 0x64, 0x73, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x43,
	0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0xaa, 0x01, 0x0a, 0x25, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64,
	0x41, 0x6d, 0x63, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62,
	0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x3f, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x41, 0x6d, 0x63, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x41, 0x6d, 0x63, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xaa, 0x01,
	0x0a, 0x25, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43,
	0x61, 0x72, 0x64, 0x43, 0x72, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x69,
	0x74, 0x69, 0x63, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72,
	0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x26, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x24, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x41, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x42, 0x2e, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_provisioning_card_consumer_proto_rawDescOnce sync.Once
	file_api_card_provisioning_card_consumer_proto_rawDescData = file_api_card_provisioning_card_consumer_proto_rawDesc
)

func file_api_card_provisioning_card_consumer_proto_rawDescGZIP() []byte {
	file_api_card_provisioning_card_consumer_proto_rawDescOnce.Do(func() {
		file_api_card_provisioning_card_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_provisioning_card_consumer_proto_rawDescData)
	})
	return file_api_card_provisioning_card_consumer_proto_rawDescData
}

var file_api_card_provisioning_card_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_api_card_provisioning_card_consumer_proto_goTypes = []interface{}{
	(*ProcessUserDevicePropertiesUpdateEventResponse)(nil),              // 0: card.provisioning.ProcessUserDevicePropertiesUpdateEventResponse
	(*ProcessCardsDispatchedCsvFileRequest)(nil),                        // 1: card.provisioning.ProcessCardsDispatchedCsvFileRequest
	(*ProcessCardsDispatchedCsvFileResponse)(nil),                       // 2: card.provisioning.ProcessCardsDispatchedCsvFileResponse
	(*ProcessCardDispatchRequest)(nil),                                  // 3: card.provisioning.ProcessCardDispatchRequest
	(*ProcessCardDispatchResponse)(nil),                                 // 4: card.provisioning.ProcessCardDispatchResponse
	(*ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest)(nil),  // 5: card.provisioning.ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest
	(*ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse)(nil), // 6: card.provisioning.ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse
	(*ProcessCardShipmentRegisterEventRequest)(nil),                     // 7: card.provisioning.ProcessCardShipmentRegisterEventRequest
	(*ProcessCardShipmentRegisterEventResponse)(nil),                    // 8: card.provisioning.ProcessCardShipmentRegisterEventResponse
	(*ProcessCardDeliveryDelayEventRequest)(nil),                        // 9: card.provisioning.ProcessCardDeliveryDelayEventRequest
	(*ProcessCardDeliveryDelayEventResponse)(nil),                       // 10: card.provisioning.ProcessCardDeliveryDelayEventResponse
	(*ProcessCardRenewalEventRequest)(nil),                              // 11: card.provisioning.ProcessCardRenewalEventRequest
	(*ProcessCardRenewalEventResponse)(nil),                             // 12: card.provisioning.ProcessCardRenewalEventResponse
	(*ProcessCardCreationRequest)(nil),                                  // 13: card.provisioning.ProcessCardCreationRequest
	(*ProcessCardCreationResponse)(nil),                                 // 14: card.provisioning.ProcessCardCreationResponse
	(*ProcessCardPiCreationRequest)(nil),                                // 15: card.provisioning.ProcessCardPiCreationRequest
	(*ProcessCardPiCreationResponse)(nil),                               // 16: card.provisioning.ProcessCardPiCreationResponse
	(*ProcessCardPinSetEventRequest)(nil),                               // 17: card.provisioning.ProcessCardPinSetEventRequest
	(*ProcessCardPinSetEventResponse)(nil),                              // 18: card.provisioning.ProcessCardPinSetEventResponse
	(*ProcessCardOnboardingEventResponse)(nil),                          // 19: card.provisioning.ProcessCardOnboardingEventResponse
	(*ProcessAuthFactorUpdateEventResponse)(nil),                        // 20: card.provisioning.ProcessAuthFactorUpdateEventResponse
	(*ProcessCardDeliveredEventRequest)(nil),                            // 21: card.provisioning.ProcessCardDeliveredEventRequest
	(*ProcessCardDeliveredEventResponse)(nil),                           // 22: card.provisioning.ProcessCardDeliveredEventResponse
	(*GetTrackingDetailsRequest)(nil),                                   // 23: card.provisioning.GetTrackingDetailsRequest
	(*GetTrackingDetailsResponse)(nil),                                  // 24: card.provisioning.GetTrackingDetailsResponse
	(*ProcessCardAmcChargesEligibleUserFileRequest)(nil),                // 25: card.provisioning.ProcessCardAmcChargesEligibleUserFileRequest
	(*ProcessCardAmcChargesEligibleUserFileResponse)(nil),               // 26: card.provisioning.ProcessCardAmcChargesEligibleUserFileResponse
	(*OrderPhysicalCardCriticalNotificationRequest)(nil),                // 27: card.provisioning.OrderPhysicalCardCriticalNotificationRequest
	(*OrderPhysicalCardCriticalNotificationResponse)(nil),               // 28: card.provisioning.OrderPhysicalCardCriticalNotificationResponse
	(*queue.ConsumerResponseHeader)(nil),                                // 29: queue.ConsumerResponseHeader
	(*queue.ConsumerRequestHeader)(nil),                                 // 30: queue.ConsumerRequestHeader
	(*s3.Record)(nil),                                                   // 31: aws.s3.Record
	(card.Provenance)(0),                                                // 32: card.Provenance
	(*card.Card)(nil),                                                   // 33: card.Card
	(*onboarding.OnboardingStageUpdate)(nil),                            // 34: user.onboarding.OnboardingStageUpdate
	(*notification.AuthFactorUpdateEvent)(nil),                          // 35: auth.AuthFactorUpdateEvent
	(*event.UserDevicePropertyUpdateEvent)(nil),                         // 36: event.UserDevicePropertyUpdateEvent
}
var file_api_card_provisioning_card_consumer_proto_depIdxs = []int32{
	29, // 0: card.provisioning.ProcessUserDevicePropertiesUpdateEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	30, // 1: card.provisioning.ProcessCardsDispatchedCsvFileRequest.request_header:type_name -> queue.ConsumerRequestHeader
	31, // 2: card.provisioning.ProcessCardsDispatchedCsvFileRequest.records:type_name -> aws.s3.Record
	29, // 3: card.provisioning.ProcessCardsDispatchedCsvFileResponse.response_header:type_name -> queue.ConsumerResponseHeader
	30, // 4: card.provisioning.ProcessCardDispatchRequest.request_header:type_name -> queue.ConsumerRequestHeader
	29, // 5: card.provisioning.ProcessCardDispatchResponse.response_header:type_name -> queue.ConsumerResponseHeader
	30, // 6: card.provisioning.ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest.request_header:type_name -> queue.ConsumerRequestHeader
	29, // 7: card.provisioning.ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse.response_header:type_name -> queue.ConsumerResponseHeader
	30, // 8: card.provisioning.ProcessCardShipmentRegisterEventRequest.request_header:type_name -> queue.ConsumerRequestHeader
	29, // 9: card.provisioning.ProcessCardShipmentRegisterEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	30, // 10: card.provisioning.ProcessCardDeliveryDelayEventRequest.request_header:type_name -> queue.ConsumerRequestHeader
	29, // 11: card.provisioning.ProcessCardDeliveryDelayEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	30, // 12: card.provisioning.ProcessCardRenewalEventRequest.request_header:type_name -> queue.ConsumerRequestHeader
	32, // 13: card.provisioning.ProcessCardRenewalEventRequest.block_card_provenance:type_name -> card.Provenance
	29, // 14: card.provisioning.ProcessCardRenewalEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	30, // 15: card.provisioning.ProcessCardCreationRequest.request_header:type_name -> queue.ConsumerRequestHeader
	29, // 16: card.provisioning.ProcessCardCreationResponse.response_header:type_name -> queue.ConsumerResponseHeader
	30, // 17: card.provisioning.ProcessCardPiCreationRequest.request_header:type_name -> queue.ConsumerRequestHeader
	33, // 18: card.provisioning.ProcessCardPiCreationRequest.card:type_name -> card.Card
	29, // 19: card.provisioning.ProcessCardPiCreationResponse.response_header:type_name -> queue.ConsumerResponseHeader
	30, // 20: card.provisioning.ProcessCardPinSetEventRequest.request_header:type_name -> queue.ConsumerRequestHeader
	33, // 21: card.provisioning.ProcessCardPinSetEventRequest.card:type_name -> card.Card
	29, // 22: card.provisioning.ProcessCardPinSetEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	29, // 23: card.provisioning.ProcessCardOnboardingEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	29, // 24: card.provisioning.ProcessAuthFactorUpdateEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	30, // 25: card.provisioning.ProcessCardDeliveredEventRequest.request_header:type_name -> queue.ConsumerRequestHeader
	29, // 26: card.provisioning.ProcessCardDeliveredEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	30, // 27: card.provisioning.GetTrackingDetailsRequest.request_header:type_name -> queue.ConsumerRequestHeader
	29, // 28: card.provisioning.GetTrackingDetailsResponse.response_header:type_name -> queue.ConsumerResponseHeader
	30, // 29: card.provisioning.ProcessCardAmcChargesEligibleUserFileRequest.request_header:type_name -> queue.ConsumerRequestHeader
	31, // 30: card.provisioning.ProcessCardAmcChargesEligibleUserFileRequest.records:type_name -> aws.s3.Record
	29, // 31: card.provisioning.ProcessCardAmcChargesEligibleUserFileResponse.response_header:type_name -> queue.ConsumerResponseHeader
	30, // 32: card.provisioning.OrderPhysicalCardCriticalNotificationRequest.request_header:type_name -> queue.ConsumerRequestHeader
	29, // 33: card.provisioning.OrderPhysicalCardCriticalNotificationResponse.response_header:type_name -> queue.ConsumerResponseHeader
	13, // 34: card.provisioning.CardConsumer.ProcessCardCreation:input_type -> card.provisioning.ProcessCardCreationRequest
	15, // 35: card.provisioning.CardConsumer.ProcessCardPiCreation:input_type -> card.provisioning.ProcessCardPiCreationRequest
	17, // 36: card.provisioning.CardConsumer.ProcessCardPinSetEvent:input_type -> card.provisioning.ProcessCardPinSetEventRequest
	11, // 37: card.provisioning.CardConsumer.ProcessCardRenewalEvent:input_type -> card.provisioning.ProcessCardRenewalEventRequest
	34, // 38: card.provisioning.CardConsumer.ProcessCardOnboardingEvent:input_type -> user.onboarding.OnboardingStageUpdate
	35, // 39: card.provisioning.CardConsumer.ProcessAuthFactorUpdateEvent:input_type -> auth.AuthFactorUpdateEvent
	7,  // 40: card.provisioning.CardConsumer.ProcessCardShipmentRegisterEvent:input_type -> card.provisioning.ProcessCardShipmentRegisterEventRequest
	9,  // 41: card.provisioning.CardConsumer.ProcessCardDeliveryDelayEvent:input_type -> card.provisioning.ProcessCardDeliveryDelayEventRequest
	21, // 42: card.provisioning.CardConsumer.ProcessCardDeliveredEvent:input_type -> card.provisioning.ProcessCardDeliveredEventRequest
	23, // 43: card.provisioning.CardConsumer.GetTrackingDetails:input_type -> card.provisioning.GetTrackingDetailsRequest
	3,  // 44: card.provisioning.CardConsumer.ProcessCardDispatch:input_type -> card.provisioning.ProcessCardDispatchRequest
	5,  // 45: card.provisioning.CardConsumer.ProcessShippingAddressUpdateAndDispatchPhysicalCard:input_type -> card.provisioning.ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest
	1,  // 46: card.provisioning.CardConsumer.ProcessCardsDispatchedCsvFile:input_type -> card.provisioning.ProcessCardsDispatchedCsvFileRequest
	25, // 47: card.provisioning.CardConsumer.ProcessCardAmcChargesEligibleUserFile:input_type -> card.provisioning.ProcessCardAmcChargesEligibleUserFileRequest
	27, // 48: card.provisioning.CardConsumer.OrderPhysicalCardCriticalNotification:input_type -> card.provisioning.OrderPhysicalCardCriticalNotificationRequest
	36, // 49: card.provisioning.CardConsumer.ProcessUserDevicePropertiesUpdateEvent:input_type -> event.UserDevicePropertyUpdateEvent
	14, // 50: card.provisioning.CardConsumer.ProcessCardCreation:output_type -> card.provisioning.ProcessCardCreationResponse
	16, // 51: card.provisioning.CardConsumer.ProcessCardPiCreation:output_type -> card.provisioning.ProcessCardPiCreationResponse
	18, // 52: card.provisioning.CardConsumer.ProcessCardPinSetEvent:output_type -> card.provisioning.ProcessCardPinSetEventResponse
	12, // 53: card.provisioning.CardConsumer.ProcessCardRenewalEvent:output_type -> card.provisioning.ProcessCardRenewalEventResponse
	19, // 54: card.provisioning.CardConsumer.ProcessCardOnboardingEvent:output_type -> card.provisioning.ProcessCardOnboardingEventResponse
	20, // 55: card.provisioning.CardConsumer.ProcessAuthFactorUpdateEvent:output_type -> card.provisioning.ProcessAuthFactorUpdateEventResponse
	8,  // 56: card.provisioning.CardConsumer.ProcessCardShipmentRegisterEvent:output_type -> card.provisioning.ProcessCardShipmentRegisterEventResponse
	10, // 57: card.provisioning.CardConsumer.ProcessCardDeliveryDelayEvent:output_type -> card.provisioning.ProcessCardDeliveryDelayEventResponse
	22, // 58: card.provisioning.CardConsumer.ProcessCardDeliveredEvent:output_type -> card.provisioning.ProcessCardDeliveredEventResponse
	24, // 59: card.provisioning.CardConsumer.GetTrackingDetails:output_type -> card.provisioning.GetTrackingDetailsResponse
	4,  // 60: card.provisioning.CardConsumer.ProcessCardDispatch:output_type -> card.provisioning.ProcessCardDispatchResponse
	6,  // 61: card.provisioning.CardConsumer.ProcessShippingAddressUpdateAndDispatchPhysicalCard:output_type -> card.provisioning.ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse
	2,  // 62: card.provisioning.CardConsumer.ProcessCardsDispatchedCsvFile:output_type -> card.provisioning.ProcessCardsDispatchedCsvFileResponse
	26, // 63: card.provisioning.CardConsumer.ProcessCardAmcChargesEligibleUserFile:output_type -> card.provisioning.ProcessCardAmcChargesEligibleUserFileResponse
	28, // 64: card.provisioning.CardConsumer.OrderPhysicalCardCriticalNotification:output_type -> card.provisioning.OrderPhysicalCardCriticalNotificationResponse
	0,  // 65: card.provisioning.CardConsumer.ProcessUserDevicePropertiesUpdateEvent:output_type -> card.provisioning.ProcessUserDevicePropertiesUpdateEventResponse
	50, // [50:66] is the sub-list for method output_type
	34, // [34:50] is the sub-list for method input_type
	34, // [34:34] is the sub-list for extension type_name
	34, // [34:34] is the sub-list for extension extendee
	0,  // [0:34] is the sub-list for field type_name
}

func init() { file_api_card_provisioning_card_consumer_proto_init() }
func file_api_card_provisioning_card_consumer_proto_init() {
	if File_api_card_provisioning_card_consumer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_provisioning_card_consumer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessUserDevicePropertiesUpdateEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardsDispatchedCsvFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardsDispatchedCsvFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardDispatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardDispatchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardShipmentRegisterEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardShipmentRegisterEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardDeliveryDelayEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardDeliveryDelayEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardRenewalEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardRenewalEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardCreationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardCreationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardPiCreationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardPiCreationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardPinSetEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardPinSetEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardOnboardingEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessAuthFactorUpdateEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardDeliveredEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardDeliveredEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTrackingDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTrackingDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardAmcChargesEligibleUserFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardAmcChargesEligibleUserFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderPhysicalCardCriticalNotificationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_card_consumer_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderPhysicalCardCriticalNotificationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_provisioning_card_consumer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_card_provisioning_card_consumer_proto_goTypes,
		DependencyIndexes: file_api_card_provisioning_card_consumer_proto_depIdxs,
		MessageInfos:      file_api_card_provisioning_card_consumer_proto_msgTypes,
	}.Build()
	File_api_card_provisioning_card_consumer_proto = out.File
	file_api_card_provisioning_card_consumer_proto_rawDesc = nil
	file_api_card_provisioning_card_consumer_proto_goTypes = nil
	file_api_card_provisioning_card_consumer_proto_depIdxs = nil
}
