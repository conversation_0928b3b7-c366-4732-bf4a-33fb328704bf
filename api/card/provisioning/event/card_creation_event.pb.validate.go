// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/provisioning/event/card_creation_event.proto

package event

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CardCreationEvent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CardCreationEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardCreationEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardCreationEventMultiError, or nil if none found.
func (m *CardCreationEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *CardCreationEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardCreationEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardCreationEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardCreationEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardCreationFlow

	// no validation rules for ActorId

	// no validation rules for CardId

	if all {
		switch v := interface{}(m.GetCardCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardCreationEventValidationError{
					field:  "CardCreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardCreationEventValidationError{
					field:  "CardCreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardCreationEventValidationError{
				field:  "CardCreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardCreationEventMultiError(errors)
	}

	return nil
}

// CardCreationEventMultiError is an error wrapping multiple validation errors
// returned by CardCreationEvent.ValidateAll() if the designated constraints
// aren't met.
type CardCreationEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardCreationEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardCreationEventMultiError) AllErrors() []error { return m }

// CardCreationEventValidationError is the validation error returned by
// CardCreationEvent.Validate if the designated constraints aren't met.
type CardCreationEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardCreationEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardCreationEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardCreationEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardCreationEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardCreationEventValidationError) ErrorName() string {
	return "CardCreationEventValidationError"
}

// Error satisfies the builtin error interface
func (e CardCreationEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardCreationEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardCreationEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardCreationEventValidationError{}
