// protolint:disable MAX_LINE_LENGTH
//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/provisioning/event/card_creation_event.proto

package event

import (
	queue "github.com/epifi/be-common/api/queue"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CardCreationEvent_CardCreationFlow int32

const (
	CardCreationEvent_CARD_CREATION_FLOW_UNSPECIFIED      CardCreationEvent_CardCreationFlow = 0
	CardCreationEvent_CARD_CREATION_FLOW_ONBOARDING       CardCreationEvent_CardCreationFlow = 1
	CardCreationEvent_CARD_CREATION_FLOW_REQUEST_NEW_CARD CardCreationEvent_CardCreationFlow = 2
)

// Enum value maps for CardCreationEvent_CardCreationFlow.
var (
	CardCreationEvent_CardCreationFlow_name = map[int32]string{
		0: "CARD_CREATION_FLOW_UNSPECIFIED",
		1: "CARD_CREATION_FLOW_ONBOARDING",
		2: "CARD_CREATION_FLOW_REQUEST_NEW_CARD",
	}
	CardCreationEvent_CardCreationFlow_value = map[string]int32{
		"CARD_CREATION_FLOW_UNSPECIFIED":      0,
		"CARD_CREATION_FLOW_ONBOARDING":       1,
		"CARD_CREATION_FLOW_REQUEST_NEW_CARD": 2,
	}
)

func (x CardCreationEvent_CardCreationFlow) Enum() *CardCreationEvent_CardCreationFlow {
	p := new(CardCreationEvent_CardCreationFlow)
	*p = x
	return p
}

func (x CardCreationEvent_CardCreationFlow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardCreationEvent_CardCreationFlow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_event_card_creation_event_proto_enumTypes[0].Descriptor()
}

func (CardCreationEvent_CardCreationFlow) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_event_card_creation_event_proto_enumTypes[0]
}

func (x CardCreationEvent_CardCreationFlow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardCreationEvent_CardCreationFlow.Descriptor instead.
func (CardCreationEvent_CardCreationFlow) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_event_card_creation_event_proto_rawDescGZIP(), []int{0, 0}
}

// Proto for sending notification on successful card creation
type CardCreationEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// card creation flow determining if card is created during user onboarding of via request new card
	CardCreationFlow CardCreationEvent_CardCreationFlow `protobuf:"varint,2,opt,name=card_creation_flow,json=cardCreationFlow,proto3,enum=card.provisioning.event.CardCreationEvent_CardCreationFlow" json:"card_creation_flow,omitempty"`
	// actor for which the card is created
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// card id of the newly created card
	CardId string `protobuf:"bytes,4,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// timestamp of card creation
	CardCreatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=card_created_at,json=cardCreatedAt,proto3" json:"card_created_at,omitempty"`
}

func (x *CardCreationEvent) Reset() {
	*x = CardCreationEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_event_card_creation_event_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardCreationEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardCreationEvent) ProtoMessage() {}

func (x *CardCreationEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_event_card_creation_event_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardCreationEvent.ProtoReflect.Descriptor instead.
func (*CardCreationEvent) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_event_card_creation_event_proto_rawDescGZIP(), []int{0}
}

func (x *CardCreationEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CardCreationEvent) GetCardCreationFlow() CardCreationEvent_CardCreationFlow {
	if x != nil {
		return x.CardCreationFlow
	}
	return CardCreationEvent_CARD_CREATION_FLOW_UNSPECIFIED
}

func (x *CardCreationEvent) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CardCreationEvent) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CardCreationEvent) GetCardCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CardCreatedAt
	}
	return nil
}

var File_api_card_provisioning_event_card_creation_event_proto protoreflect.FileDescriptor

var file_api_card_provisioning_event_card_creation_event_proto_rawDesc = []byte{
	0x0a, 0x35, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xc0, 0x03, 0x0a, 0x11, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x69,
	0x0a, 0x12, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x66, 0x6c, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x10, 0x63, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x42, 0x0a,
	0x0f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x22, 0x82, 0x01, 0x0a, 0x10, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x27, 0x0a,
	0x23, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4e, 0x45, 0x57, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x10, 0x02, 0x42, 0x68, 0x0a, 0x32, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5a, 0x32, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_provisioning_event_card_creation_event_proto_rawDescOnce sync.Once
	file_api_card_provisioning_event_card_creation_event_proto_rawDescData = file_api_card_provisioning_event_card_creation_event_proto_rawDesc
)

func file_api_card_provisioning_event_card_creation_event_proto_rawDescGZIP() []byte {
	file_api_card_provisioning_event_card_creation_event_proto_rawDescOnce.Do(func() {
		file_api_card_provisioning_event_card_creation_event_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_provisioning_event_card_creation_event_proto_rawDescData)
	})
	return file_api_card_provisioning_event_card_creation_event_proto_rawDescData
}

var file_api_card_provisioning_event_card_creation_event_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_card_provisioning_event_card_creation_event_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_card_provisioning_event_card_creation_event_proto_goTypes = []interface{}{
	(CardCreationEvent_CardCreationFlow)(0), // 0: card.provisioning.event.CardCreationEvent.CardCreationFlow
	(*CardCreationEvent)(nil),               // 1: card.provisioning.event.CardCreationEvent
	(*queue.ConsumerRequestHeader)(nil),     // 2: queue.ConsumerRequestHeader
	(*timestamppb.Timestamp)(nil),           // 3: google.protobuf.Timestamp
}
var file_api_card_provisioning_event_card_creation_event_proto_depIdxs = []int32{
	2, // 0: card.provisioning.event.CardCreationEvent.request_header:type_name -> queue.ConsumerRequestHeader
	0, // 1: card.provisioning.event.CardCreationEvent.card_creation_flow:type_name -> card.provisioning.event.CardCreationEvent.CardCreationFlow
	3, // 2: card.provisioning.event.CardCreationEvent.card_created_at:type_name -> google.protobuf.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_card_provisioning_event_card_creation_event_proto_init() }
func file_api_card_provisioning_event_card_creation_event_proto_init() {
	if File_api_card_provisioning_event_card_creation_event_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_provisioning_event_card_creation_event_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardCreationEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_provisioning_event_card_creation_event_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_provisioning_event_card_creation_event_proto_goTypes,
		DependencyIndexes: file_api_card_provisioning_event_card_creation_event_proto_depIdxs,
		EnumInfos:         file_api_card_provisioning_event_card_creation_event_proto_enumTypes,
		MessageInfos:      file_api_card_provisioning_event_card_creation_event_proto_msgTypes,
	}.Build()
	File_api_card_provisioning_event_card_creation_event_proto = out.File
	file_api_card_provisioning_event_card_creation_event_proto_rawDesc = nil
	file_api_card_provisioning_event_card_creation_event_proto_goTypes = nil
	file_api_card_provisioning_event_card_creation_event_proto_depIdxs = nil
}
