// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/provisioning/card_request.proto

package provisioning

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	card "github.com/epifi/gamma/api/card"

	enums "github.com/epifi/gamma/api/card/enums"

	external "github.com/epifi/gamma/api/tiering/external"

	typesv2 "github.com/epifi/gamma/api/typesv2"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = card.Provenance(0)

	_ = enums.CardRequestWorkflow(0)

	_ = external.Tier(0)

	_ = typesv2.AddressType(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on CardRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CardRequestMultiError, or
// nil if none found.
func (m *CardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CardId

	// no validation rules for ActorId

	// no validation rules for OrchestrationId

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetRequestDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "RequestDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "RequestDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestValidationError{
				field:  "RequestDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStageDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "StageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "StageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStageDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestValidationError{
				field:  "StageDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Workflow

	// no validation rules for Status

	// no validation rules for Provenance

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardRequestMultiError(errors)
	}

	return nil
}

// CardRequestMultiError is an error wrapping multiple validation errors
// returned by CardRequest.ValidateAll() if the designated constraints aren't met.
type CardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardRequestMultiError) AllErrors() []error { return m }

// CardRequestValidationError is the validation error returned by
// CardRequest.Validate if the designated constraints aren't met.
type CardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardRequestValidationError) ErrorName() string { return "CardRequestValidationError" }

// Error satisfies the builtin error interface
func (e CardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardRequestValidationError{}

// Validate checks the field values on CardRequestDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardRequestDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardRequestDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardRequestDetailsMultiError, or nil if none found.
func (m *CardRequestDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CardRequestDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Data.(type) {
	case *CardRequestDetails_RenewCardRequestDetails:
		if v == nil {
			err := CardRequestDetailsValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRenewCardRequestDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CardRequestDetailsValidationError{
						field:  "RenewCardRequestDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CardRequestDetailsValidationError{
						field:  "RenewCardRequestDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRenewCardRequestDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CardRequestDetailsValidationError{
					field:  "RenewCardRequestDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CardRequestDetails_AmcChargesDetails:
		if v == nil {
			err := CardRequestDetailsValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAmcChargesDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CardRequestDetailsValidationError{
						field:  "AmcChargesDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CardRequestDetailsValidationError{
						field:  "AmcChargesDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAmcChargesDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CardRequestDetailsValidationError{
					field:  "AmcChargesDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CardRequestDetailsMultiError(errors)
	}

	return nil
}

// CardRequestDetailsMultiError is an error wrapping multiple validation errors
// returned by CardRequestDetails.ValidateAll() if the designated constraints
// aren't met.
type CardRequestDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardRequestDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardRequestDetailsMultiError) AllErrors() []error { return m }

// CardRequestDetailsValidationError is the validation error returned by
// CardRequestDetails.Validate if the designated constraints aren't met.
type CardRequestDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardRequestDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardRequestDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardRequestDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardRequestDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardRequestDetailsValidationError) ErrorName() string {
	return "CardRequestDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e CardRequestDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardRequestDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardRequestDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardRequestDetailsValidationError{}

// Validate checks the field values on RenewCardRequestDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RenewCardRequestDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RenewCardRequestDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RenewCardRequestDetailsMultiError, or nil if none found.
func (m *RenewCardRequestDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *RenewCardRequestDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BlockCardReason

	// no validation rules for AddressType

	// no validation rules for CardForm

	// no validation rules for BlockCardProvenance

	if len(errors) > 0 {
		return RenewCardRequestDetailsMultiError(errors)
	}

	return nil
}

// RenewCardRequestDetailsMultiError is an error wrapping multiple validation
// errors returned by RenewCardRequestDetails.ValidateAll() if the designated
// constraints aren't met.
type RenewCardRequestDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RenewCardRequestDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RenewCardRequestDetailsMultiError) AllErrors() []error { return m }

// RenewCardRequestDetailsValidationError is the validation error returned by
// RenewCardRequestDetails.Validate if the designated constraints aren't met.
type RenewCardRequestDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RenewCardRequestDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RenewCardRequestDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RenewCardRequestDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RenewCardRequestDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RenewCardRequestDetailsValidationError) ErrorName() string {
	return "RenewCardRequestDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e RenewCardRequestDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRenewCardRequestDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RenewCardRequestDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RenewCardRequestDetailsValidationError{}

// Validate checks the field values on StageDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StageDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StageDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StageDetailsMultiError, or
// nil if none found.
func (m *StageDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *StageDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetCardRequestStages()))
		i := 0
		for key := range m.GetCardRequestStages() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCardRequestStages()[key]
			_ = val

			// no validation rules for CardRequestStages[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, StageDetailsValidationError{
							field:  fmt.Sprintf("CardRequestStages[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, StageDetailsValidationError{
							field:  fmt.Sprintf("CardRequestStages[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return StageDetailsValidationError{
						field:  fmt.Sprintf("CardRequestStages[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return StageDetailsMultiError(errors)
	}

	return nil
}

// StageDetailsMultiError is an error wrapping multiple validation errors
// returned by StageDetails.ValidateAll() if the designated constraints aren't met.
type StageDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StageDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StageDetailsMultiError) AllErrors() []error { return m }

// StageDetailsValidationError is the validation error returned by
// StageDetails.Validate if the designated constraints aren't met.
type StageDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StageDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StageDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StageDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StageDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StageDetailsValidationError) ErrorName() string { return "StageDetailsValidationError" }

// Error satisfies the builtin error interface
func (e StageDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStageDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StageDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StageDetailsValidationError{}

// Validate checks the field values on CardRequestStage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CardRequestStage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardRequestStage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardRequestStageMultiError, or nil if none found.
func (m *CardRequestStage) ValidateAll() error {
	return m.validate(true)
}

func (m *CardRequestStage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StageName

	// no validation rules for Status

	// no validation rules for SubStatus

	if all {
		switch v := interface{}(m.GetStaledAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestStageValidationError{
					field:  "StaledAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestStageValidationError{
					field:  "StaledAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStaledAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestStageValidationError{
				field:  "StaledAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestStageValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestStageValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestStageValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestStageValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestStageValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestStageValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardRequestStageMultiError(errors)
	}

	return nil
}

// CardRequestStageMultiError is an error wrapping multiple validation errors
// returned by CardRequestStage.ValidateAll() if the designated constraints
// aren't met.
type CardRequestStageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardRequestStageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardRequestStageMultiError) AllErrors() []error { return m }

// CardRequestStageValidationError is the validation error returned by
// CardRequestStage.Validate if the designated constraints aren't met.
type CardRequestStageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardRequestStageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardRequestStageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardRequestStageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardRequestStageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardRequestStageValidationError) ErrorName() string { return "CardRequestStageValidationError" }

// Error satisfies the builtin error interface
func (e CardRequestStageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardRequestStage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardRequestStageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardRequestStageValidationError{}

// Validate checks the field values on AmcChargesDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AmcChargesDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AmcChargesDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AmcChargesDetailsMultiError, or nil if none found.
func (m *AmcChargesDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AmcChargesDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFileGenDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AmcChargesDetailsValidationError{
					field:  "FileGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AmcChargesDetailsValidationError{
					field:  "FileGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileGenDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AmcChargesDetailsValidationError{
				field:  "FileGenDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BatchNumber

	if all {
		switch v := interface{}(m.GetAmcChargeAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AmcChargesDetailsValidationError{
					field:  "AmcChargeAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AmcChargesDetailsValidationError{
					field:  "AmcChargeAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmcChargeAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AmcChargesDetailsValidationError{
				field:  "AmcChargeAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OperationalStatus

	// no validation rules for FreezeStatus

	if all {
		switch v := interface{}(m.GetAnniversaryDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AmcChargesDetailsValidationError{
					field:  "AnniversaryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AmcChargesDetailsValidationError{
					field:  "AnniversaryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnniversaryDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AmcChargesDetailsValidationError{
				field:  "AnniversaryDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TierAtAnniversaryDate

	if len(errors) > 0 {
		return AmcChargesDetailsMultiError(errors)
	}

	return nil
}

// AmcChargesDetailsMultiError is an error wrapping multiple validation errors
// returned by AmcChargesDetails.ValidateAll() if the designated constraints
// aren't met.
type AmcChargesDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AmcChargesDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AmcChargesDetailsMultiError) AllErrors() []error { return m }

// AmcChargesDetailsValidationError is the validation error returned by
// AmcChargesDetails.Validate if the designated constraints aren't met.
type AmcChargesDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AmcChargesDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AmcChargesDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AmcChargesDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AmcChargesDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AmcChargesDetailsValidationError) ErrorName() string {
	return "AmcChargesDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AmcChargesDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAmcChargesDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AmcChargesDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AmcChargesDetailsValidationError{}
