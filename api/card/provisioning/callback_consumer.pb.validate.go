// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/provisioning/callback_consumer.proto

package provisioning

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	card "github.com/epifi/gamma/api/card"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = card.CardForm(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on ProcessCardCreationCallBackRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessCardCreationCallBackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardCreationCallBackRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCardCreationCallBackRequestMultiError, or nil if none found.
func (m *ProcessCardCreationCallBackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardCreationCallBackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardCreationCallBackRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardCreationCallBackRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardCreationCallBackRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _ProcessCardCreationCallBackRequest_PartnerBank_NotInLookup[m.GetPartnerBank()]; ok {
		err := ProcessCardCreationCallBackRequestValidationError{
			field:  "PartnerBank",
			reason: "value must not be in list [VENDOR_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetRequestId()) < 1 {
		err := ProcessCardCreationCallBackRequestValidationError{
			field:  "RequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DeviceToken

	// no validation rules for CardId

	// no validation rules for Form

	// no validation rules for CardCategory

	// no validation rules for ResponseCode

	// no validation rules for ResponseDesc

	// no validation rules for State

	for idx, item := range m.GetErrors() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessCardCreationCallBackRequestValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessCardCreationCallBackRequestValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessCardCreationCallBackRequestValidationError{
					field:  fmt.Sprintf("Errors[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCardInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardCreationCallBackRequestValidationError{
					field:  "CardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardCreationCallBackRequestValidationError{
					field:  "CardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardCreationCallBackRequestValidationError{
				field:  "CardInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PinSetToken

	if all {
		switch v := interface{}(m.GetTokenExpireAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardCreationCallBackRequestValidationError{
					field:  "TokenExpireAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardCreationCallBackRequestValidationError{
					field:  "TokenExpireAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTokenExpireAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardCreationCallBackRequestValidationError{
				field:  "TokenExpireAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmbossName

	// no validation rules for FailureResponseCode

	// no validation rules for FailureResponseReason

	if len(errors) > 0 {
		return ProcessCardCreationCallBackRequestMultiError(errors)
	}

	return nil
}

// ProcessCardCreationCallBackRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCardCreationCallBackRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardCreationCallBackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardCreationCallBackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardCreationCallBackRequestMultiError) AllErrors() []error { return m }

// ProcessCardCreationCallBackRequestValidationError is the validation error
// returned by ProcessCardCreationCallBackRequest.Validate if the designated
// constraints aren't met.
type ProcessCardCreationCallBackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardCreationCallBackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardCreationCallBackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardCreationCallBackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardCreationCallBackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardCreationCallBackRequestValidationError) ErrorName() string {
	return "ProcessCardCreationCallBackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardCreationCallBackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardCreationCallBackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardCreationCallBackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardCreationCallBackRequestValidationError{}

var _ProcessCardCreationCallBackRequest_PartnerBank_NotInLookup = map[vendorgateway.Vendor]struct{}{
	0: {},
}

// Validate checks the field values on ProcessCardCreationCallBackResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessCardCreationCallBackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardCreationCallBackResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCardCreationCallBackResponseMultiError, or nil if none found.
func (m *ProcessCardCreationCallBackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardCreationCallBackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardCreationCallBackResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardCreationCallBackResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardCreationCallBackResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardCreationCallBackResponseMultiError(errors)
	}

	return nil
}

// ProcessCardCreationCallBackResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCardCreationCallBackResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardCreationCallBackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardCreationCallBackResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardCreationCallBackResponseMultiError) AllErrors() []error { return m }

// ProcessCardCreationCallBackResponseValidationError is the validation error
// returned by ProcessCardCreationCallBackResponse.Validate if the designated
// constraints aren't met.
type ProcessCardCreationCallBackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardCreationCallBackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardCreationCallBackResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardCreationCallBackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardCreationCallBackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardCreationCallBackResponseValidationError) ErrorName() string {
	return "ProcessCardCreationCallBackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardCreationCallBackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardCreationCallBackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardCreationCallBackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardCreationCallBackResponseValidationError{}

// Validate checks the field values on CallBackError with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CallBackError) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CallBackError with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CallBackErrorMultiError, or
// nil if none found.
func (m *CallBackError) ValidateAll() error {
	return m.validate(true)
}

func (m *CallBackError) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Reason

	if len(errors) > 0 {
		return CallBackErrorMultiError(errors)
	}

	return nil
}

// CallBackErrorMultiError is an error wrapping multiple validation errors
// returned by CallBackError.ValidateAll() if the designated constraints
// aren't met.
type CallBackErrorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CallBackErrorMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CallBackErrorMultiError) AllErrors() []error { return m }

// CallBackErrorValidationError is the validation error returned by
// CallBackError.Validate if the designated constraints aren't met.
type CallBackErrorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CallBackErrorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CallBackErrorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CallBackErrorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CallBackErrorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CallBackErrorValidationError) ErrorName() string { return "CallBackErrorValidationError" }

// Error satisfies the builtin error interface
func (e CallBackErrorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCallBackError.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CallBackErrorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CallBackErrorValidationError{}

// Validate checks the field values on ProcessCardTrackingCallbackRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessCardTrackingCallbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardTrackingCallbackRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCardTrackingCallbackRequestMultiError, or nil if none found.
func (m *ProcessCardTrackingCallbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardTrackingCallbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardTrackingCallbackRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardTrackingCallbackRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardTrackingCallbackRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTrackingDetailsList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessCardTrackingCallbackRequestValidationError{
						field:  fmt.Sprintf("TrackingDetailsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessCardTrackingCallbackRequestValidationError{
						field:  fmt.Sprintf("TrackingDetailsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessCardTrackingCallbackRequestValidationError{
					field:  fmt.Sprintf("TrackingDetailsList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcessCardTrackingCallbackRequestMultiError(errors)
	}

	return nil
}

// ProcessCardTrackingCallbackRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCardTrackingCallbackRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardTrackingCallbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardTrackingCallbackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardTrackingCallbackRequestMultiError) AllErrors() []error { return m }

// ProcessCardTrackingCallbackRequestValidationError is the validation error
// returned by ProcessCardTrackingCallbackRequest.Validate if the designated
// constraints aren't met.
type ProcessCardTrackingCallbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardTrackingCallbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardTrackingCallbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardTrackingCallbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardTrackingCallbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardTrackingCallbackRequestValidationError) ErrorName() string {
	return "ProcessCardTrackingCallbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardTrackingCallbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardTrackingCallbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardTrackingCallbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardTrackingCallbackRequestValidationError{}

// Validate checks the field values on TrackingDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TrackingDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TrackingDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TrackingDetailsMultiError, or nil if none found.
func (m *TrackingDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *TrackingDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrderId

	// no validation rules for DeliveryState

	for idx, item := range m.GetScans() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TrackingDetailsValidationError{
						field:  fmt.Sprintf("Scans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TrackingDetailsValidationError{
						field:  fmt.Sprintf("Scans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TrackingDetailsValidationError{
					field:  fmt.Sprintf("Scans[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPickupDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TrackingDetailsValidationError{
					field:  "PickupDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TrackingDetailsValidationError{
					field:  "PickupDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPickupDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TrackingDetailsValidationError{
				field:  "PickupDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TrackingDetailsMultiError(errors)
	}

	return nil
}

// TrackingDetailsMultiError is an error wrapping multiple validation errors
// returned by TrackingDetails.ValidateAll() if the designated constraints
// aren't met.
type TrackingDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TrackingDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TrackingDetailsMultiError) AllErrors() []error { return m }

// TrackingDetailsValidationError is the validation error returned by
// TrackingDetails.Validate if the designated constraints aren't met.
type TrackingDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TrackingDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TrackingDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TrackingDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TrackingDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TrackingDetailsValidationError) ErrorName() string { return "TrackingDetailsValidationError" }

// Error satisfies the builtin error interface
func (e TrackingDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTrackingDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TrackingDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TrackingDetailsValidationError{}

// Validate checks the field values on ProcessCardTrackingCallbackResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessCardTrackingCallbackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardTrackingCallbackResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCardTrackingCallbackResponseMultiError, or nil if none found.
func (m *ProcessCardTrackingCallbackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardTrackingCallbackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardTrackingCallbackResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardTrackingCallbackResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardTrackingCallbackResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardTrackingCallbackResponseMultiError(errors)
	}

	return nil
}

// ProcessCardTrackingCallbackResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCardTrackingCallbackResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardTrackingCallbackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardTrackingCallbackResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardTrackingCallbackResponseMultiError) AllErrors() []error { return m }

// ProcessCardTrackingCallbackResponseValidationError is the validation error
// returned by ProcessCardTrackingCallbackResponse.Validate if the designated
// constraints aren't met.
type ProcessCardTrackingCallbackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardTrackingCallbackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardTrackingCallbackResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardTrackingCallbackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardTrackingCallbackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardTrackingCallbackResponseValidationError) ErrorName() string {
	return "ProcessCardTrackingCallbackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardTrackingCallbackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardTrackingCallbackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardTrackingCallbackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardTrackingCallbackResponseValidationError{}

// Validate checks the field values on
// ProcessDispatchPhysicalCardCallbackRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessDispatchPhysicalCardCallbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessDispatchPhysicalCardCallbackRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessDispatchPhysicalCardCallbackRequestMultiError, or nil if none found.
func (m *ProcessDispatchPhysicalCardCallbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessDispatchPhysicalCardCallbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessDispatchPhysicalCardCallbackRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessDispatchPhysicalCardCallbackRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessDispatchPhysicalCardCallbackRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _ProcessDispatchPhysicalCardCallbackRequest_PartnerBank_NotInLookup[m.GetPartnerBank()]; ok {
		err := ProcessDispatchPhysicalCardCallbackRequestValidationError{
			field:  "PartnerBank",
			reason: "value must not be in list [VENDOR_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetRequestId()) < 1 {
		err := ProcessDispatchPhysicalCardCallbackRequestValidationError{
			field:  "RequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for InternalResponseCode

	// no validation rules for ResponseReason

	// no validation rules for State

	if len(errors) > 0 {
		return ProcessDispatchPhysicalCardCallbackRequestMultiError(errors)
	}

	return nil
}

// ProcessDispatchPhysicalCardCallbackRequestMultiError is an error wrapping
// multiple validation errors returned by
// ProcessDispatchPhysicalCardCallbackRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessDispatchPhysicalCardCallbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessDispatchPhysicalCardCallbackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessDispatchPhysicalCardCallbackRequestMultiError) AllErrors() []error { return m }

// ProcessDispatchPhysicalCardCallbackRequestValidationError is the validation
// error returned by ProcessDispatchPhysicalCardCallbackRequest.Validate if
// the designated constraints aren't met.
type ProcessDispatchPhysicalCardCallbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessDispatchPhysicalCardCallbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessDispatchPhysicalCardCallbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessDispatchPhysicalCardCallbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessDispatchPhysicalCardCallbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessDispatchPhysicalCardCallbackRequestValidationError) ErrorName() string {
	return "ProcessDispatchPhysicalCardCallbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessDispatchPhysicalCardCallbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessDispatchPhysicalCardCallbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessDispatchPhysicalCardCallbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessDispatchPhysicalCardCallbackRequestValidationError{}

var _ProcessDispatchPhysicalCardCallbackRequest_PartnerBank_NotInLookup = map[vendorgateway.Vendor]struct{}{
	0: {},
}

// Validate checks the field values on
// ProcessDispatchPhysicalCardCallbackResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessDispatchPhysicalCardCallbackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessDispatchPhysicalCardCallbackResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessDispatchPhysicalCardCallbackResponseMultiError, or nil if none found.
func (m *ProcessDispatchPhysicalCardCallbackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessDispatchPhysicalCardCallbackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessDispatchPhysicalCardCallbackResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessDispatchPhysicalCardCallbackResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessDispatchPhysicalCardCallbackResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessDispatchPhysicalCardCallbackResponseMultiError(errors)
	}

	return nil
}

// ProcessDispatchPhysicalCardCallbackResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessDispatchPhysicalCardCallbackResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessDispatchPhysicalCardCallbackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessDispatchPhysicalCardCallbackResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessDispatchPhysicalCardCallbackResponseMultiError) AllErrors() []error { return m }

// ProcessDispatchPhysicalCardCallbackResponseValidationError is the validation
// error returned by ProcessDispatchPhysicalCardCallbackResponse.Validate if
// the designated constraints aren't met.
type ProcessDispatchPhysicalCardCallbackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessDispatchPhysicalCardCallbackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessDispatchPhysicalCardCallbackResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessDispatchPhysicalCardCallbackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessDispatchPhysicalCardCallbackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessDispatchPhysicalCardCallbackResponseValidationError) ErrorName() string {
	return "ProcessDispatchPhysicalCardCallbackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessDispatchPhysicalCardCallbackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessDispatchPhysicalCardCallbackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessDispatchPhysicalCardCallbackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessDispatchPhysicalCardCallbackResponseValidationError{}
