// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/provisioning/callback_consumer.proto

package provisioning

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	queue "github.com/epifi/be-common/api/queue"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	card "github.com/epifi/gamma/api/card"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessCardCreationCallBackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Vendor bank at which transaction is initiated
	PartnerBank vendorgateway.Vendor `protobuf:"varint,2,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// Request ID of the card creation request for which the call back is initiated
	// This request id must be present in the card-creation request DB
	RequestId string `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Device token that is issued by the federal bank at the time of client's device registration
	DeviceToken string `protobuf:"bytes,4,opt,name=device_token,json=deviceToken,proto3" json:"device_token,omitempty"`
	// Unique identifier issued by Federal to identify the card. Only available if the card was created successfully.
	CardId string `protobuf:"bytes,5,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// Physical or the virtual card. Internally we map it to CARD_FORM
	Form card.CardForm `protobuf:"varint,6,opt,name=form,proto3,enum=card.CardForm" json:"form,omitempty"`
	// Denotes the category of the card. Internally we map it to CARD_CATEGORY
	CardCategory string `protobuf:"bytes,7,opt,name=card_category,json=cardCategory,proto3" json:"card_category,omitempty"`
	// Response Code for the Transaction
	ResponseCode string `protobuf:"bytes,8,opt,name=response_code,json=responseCode,proto3" json:"response_code,omitempty"`
	// Response Description for the Transaction
	ResponseDesc string `protobuf:"bytes,9,opt,name=response_desc,json=responseDesc,proto3" json:"response_desc,omitempty"`
	// state of the card creation request. The values allowed should be either success or failure.
	State RequestState `protobuf:"varint,10,opt,name=state,proto3,enum=card.provisioning.RequestState" json:"state,omitempty"`
	// generic error messages from vendors. Only populated in case of errors
	Errors []*CallBackError `protobuf:"bytes,11,rep,name=errors,proto3" json:"errors,omitempty"`
	// card information containing the cvv of user
	CardInfo *card.BasicCardInfo `protobuf:"bytes,12,opt,name=card_info,json=cardInfo,proto3" json:"card_info,omitempty"`
	// Can be used for first pin set instead of otp
	PinSetToken string `protobuf:"bytes,13,opt,name=pin_set_token,json=pinSetToken,proto3" json:"pin_set_token,omitempty"`
	// expiry timestamp for pin set token
	TokenExpireAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=token_expire_at,json=tokenExpireAt,proto3" json:"token_expire_at,omitempty"`
	// name to be printed on the card
	EmbossName string `protobuf:"bytes,15,opt,name=emboss_name,json=embossName,proto3" json:"emboss_name,omitempty"`
	// failure response code sent by vendor
	FailureResponseCode string `protobuf:"bytes,16,opt,name=failure_response_code,json=failureResponseCode,proto3" json:"failure_response_code,omitempty"`
	// failure response reason sent by vendor
	FailureResponseReason string `protobuf:"bytes,17,opt,name=failure_response_reason,json=failureResponseReason,proto3" json:"failure_response_reason,omitempty"`
}

func (x *ProcessCardCreationCallBackRequest) Reset() {
	*x = ProcessCardCreationCallBackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardCreationCallBackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardCreationCallBackRequest) ProtoMessage() {}

func (x *ProcessCardCreationCallBackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardCreationCallBackRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardCreationCallBackRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_callback_consumer_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessCardCreationCallBackRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardCreationCallBackRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessCardCreationCallBackRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ProcessCardCreationCallBackRequest) GetDeviceToken() string {
	if x != nil {
		return x.DeviceToken
	}
	return ""
}

func (x *ProcessCardCreationCallBackRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *ProcessCardCreationCallBackRequest) GetForm() card.CardForm {
	if x != nil {
		return x.Form
	}
	return card.CardForm(0)
}

func (x *ProcessCardCreationCallBackRequest) GetCardCategory() string {
	if x != nil {
		return x.CardCategory
	}
	return ""
}

func (x *ProcessCardCreationCallBackRequest) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *ProcessCardCreationCallBackRequest) GetResponseDesc() string {
	if x != nil {
		return x.ResponseDesc
	}
	return ""
}

func (x *ProcessCardCreationCallBackRequest) GetState() RequestState {
	if x != nil {
		return x.State
	}
	return RequestState_REQUEST_STATE_UNSPECIFIED
}

func (x *ProcessCardCreationCallBackRequest) GetErrors() []*CallBackError {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *ProcessCardCreationCallBackRequest) GetCardInfo() *card.BasicCardInfo {
	if x != nil {
		return x.CardInfo
	}
	return nil
}

func (x *ProcessCardCreationCallBackRequest) GetPinSetToken() string {
	if x != nil {
		return x.PinSetToken
	}
	return ""
}

func (x *ProcessCardCreationCallBackRequest) GetTokenExpireAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TokenExpireAt
	}
	return nil
}

func (x *ProcessCardCreationCallBackRequest) GetEmbossName() string {
	if x != nil {
		return x.EmbossName
	}
	return ""
}

func (x *ProcessCardCreationCallBackRequest) GetFailureResponseCode() string {
	if x != nil {
		return x.FailureResponseCode
	}
	return ""
}

func (x *ProcessCardCreationCallBackRequest) GetFailureResponseReason() string {
	if x != nil {
		return x.FailureResponseReason
	}
	return ""
}

type ProcessCardCreationCallBackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardCreationCallBackResponse) Reset() {
	*x = ProcessCardCreationCallBackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardCreationCallBackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardCreationCallBackResponse) ProtoMessage() {}

func (x *ProcessCardCreationCallBackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardCreationCallBackResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardCreationCallBackResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_callback_consumer_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessCardCreationCallBackResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// Error message received from vendors in callback response.
// In case of failures, the specific error cause can be identified using the reason field.
type CallBackError struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *CallBackError) Reset() {
	*x = CallBackError{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallBackError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallBackError) ProtoMessage() {}

func (x *CallBackError) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallBackError.ProtoReflect.Descriptor instead.
func (*CallBackError) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_callback_consumer_proto_rawDescGZIP(), []int{2}
}

func (x *CallBackError) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CallBackError) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type ProcessCardTrackingCallbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	RequestHeader       *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	TrackingDetailsList []*TrackingDetails           `protobuf:"bytes,2,rep,name=tracking_details_list,json=trackingDetailsList,proto3" json:"tracking_details_list,omitempty"`
}

func (x *ProcessCardTrackingCallbackRequest) Reset() {
	*x = ProcessCardTrackingCallbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardTrackingCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardTrackingCallbackRequest) ProtoMessage() {}

func (x *ProcessCardTrackingCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardTrackingCallbackRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardTrackingCallbackRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_callback_consumer_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessCardTrackingCallbackRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardTrackingCallbackRequest) GetTrackingDetailsList() []*TrackingDetails {
	if x != nil {
		return x.TrackingDetailsList
	}
	return nil
}

type TrackingDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier for each shipment
	OrderId string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// Delivery state of the card shipment
	DeliveryState CardTrackingDeliveryState `protobuf:"varint,2,opt,name=delivery_state,json=deliveryState,proto3,enum=card.provisioning.CardTrackingDeliveryState" json:"delivery_state,omitempty"`
	// A package is scanned multiple times throughout the journey. On every scan,
	// there is an update in tracking information.
	Scans []*Scan `protobuf:"bytes,3,rep,name=scans,proto3" json:"scans,omitempty"`
	// Date at which shipment was picked up
	PickupDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=pickup_date,json=pickupDate,proto3" json:"pickup_date,omitempty"`
}

func (x *TrackingDetails) Reset() {
	*x = TrackingDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackingDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackingDetails) ProtoMessage() {}

func (x *TrackingDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackingDetails.ProtoReflect.Descriptor instead.
func (*TrackingDetails) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_callback_consumer_proto_rawDescGZIP(), []int{4}
}

func (x *TrackingDetails) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *TrackingDetails) GetDeliveryState() CardTrackingDeliveryState {
	if x != nil {
		return x.DeliveryState
	}
	return CardTrackingDeliveryState_CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED
}

func (x *TrackingDetails) GetScans() []*Scan {
	if x != nil {
		return x.Scans
	}
	return nil
}

func (x *TrackingDetails) GetPickupDate() *timestamppb.Timestamp {
	if x != nil {
		return x.PickupDate
	}
	return nil
}

type ProcessCardTrackingCallbackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardTrackingCallbackResponse) Reset() {
	*x = ProcessCardTrackingCallbackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardTrackingCallbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardTrackingCallbackResponse) ProtoMessage() {}

func (x *ProcessCardTrackingCallbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardTrackingCallbackResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardTrackingCallbackResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_callback_consumer_proto_rawDescGZIP(), []int{5}
}

func (x *ProcessCardTrackingCallbackResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessDispatchPhysicalCardCallbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// vendor bank for which card dispatch is initiated
	PartnerBank vendorgateway.Vendor `protobuf:"varint,2,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// Request Id of the card dispatch request for which the call back is initiated
	// This request id must be present in the card_dispatch_requests db
	RequestId string `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// internal response code corresponding to the vendor specific code
	InternalResponseCode string `protobuf:"bytes,4,opt,name=internal_response_code,json=internalResponseCode,proto3" json:"internal_response_code,omitempty"`
	// response reason for the callback
	ResponseReason string `protobuf:"bytes,5,opt,name=response_reason,json=responseReason,proto3" json:"response_reason,omitempty"`
	// state from the callback request
	State RequestState `protobuf:"varint,6,opt,name=state,proto3,enum=card.provisioning.RequestState" json:"state,omitempty"`
}

func (x *ProcessDispatchPhysicalCardCallbackRequest) Reset() {
	*x = ProcessDispatchPhysicalCardCallbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessDispatchPhysicalCardCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessDispatchPhysicalCardCallbackRequest) ProtoMessage() {}

func (x *ProcessDispatchPhysicalCardCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessDispatchPhysicalCardCallbackRequest.ProtoReflect.Descriptor instead.
func (*ProcessDispatchPhysicalCardCallbackRequest) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_callback_consumer_proto_rawDescGZIP(), []int{6}
}

func (x *ProcessDispatchPhysicalCardCallbackRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessDispatchPhysicalCardCallbackRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessDispatchPhysicalCardCallbackRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ProcessDispatchPhysicalCardCallbackRequest) GetInternalResponseCode() string {
	if x != nil {
		return x.InternalResponseCode
	}
	return ""
}

func (x *ProcessDispatchPhysicalCardCallbackRequest) GetResponseReason() string {
	if x != nil {
		return x.ResponseReason
	}
	return ""
}

func (x *ProcessDispatchPhysicalCardCallbackRequest) GetState() RequestState {
	if x != nil {
		return x.State
	}
	return RequestState_REQUEST_STATE_UNSPECIFIED
}

type ProcessDispatchPhysicalCardCallbackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessDispatchPhysicalCardCallbackResponse) Reset() {
	*x = ProcessDispatchPhysicalCardCallbackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessDispatchPhysicalCardCallbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessDispatchPhysicalCardCallbackResponse) ProtoMessage() {}

func (x *ProcessDispatchPhysicalCardCallbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_callback_consumer_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessDispatchPhysicalCardCallbackResponse.ProtoReflect.Descriptor instead.
func (*ProcessDispatchPhysicalCardCallbackResponse) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_callback_consumer_proto_rawDescGZIP(), []int{7}
}

func (x *ProcessDispatchPhysicalCardCallbackResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_card_provisioning_callback_consumer_proto protoreflect.FileDescriptor

var file_api_card_provisioning_callback_consumer_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x11, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69,
	0x6e, 0x67, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72,
	0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e,
	0x67, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xbc, 0x06, 0x0a, 0x22, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x0c,
	0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b,
	0x12, 0x26, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61,
	0x72, 0x64, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x04, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x46, 0x6f,
	0x72, 0x6d, 0x52, 0x04, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x61, 0x72, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x23, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x44, 0x65, 0x73, 0x63, 0x12, 0x35, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x38,
	0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69,
	0x6e, 0x67, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x30, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x69,
	0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x42,
	0x0a, 0x0f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x61,
	0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0d, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6d, 0x62, 0x6f, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6d, 0x62, 0x6f, 0x73, 0x73, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22,
	0x6d, 0x0a, 0x23, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x3b,
	0x0a, 0x0d, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xc1, 0x01, 0x0a, 0x22,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x56, 0x0a, 0x15, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x13, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0xed, 0x01, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x53,
	0x0a, 0x0e, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x2d, 0x0a, 0x05, 0x73, 0x63, 0x61, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x05, 0x73, 0x63, 0x61,
	0x6e, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x44, 0x61, 0x74, 0x65, 0x22,
	0x6d, 0x0a, 0x23, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xf3,
	0x02, 0x0a, 0x2a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61,
	0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x26, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x34,
	0x0a, 0x16, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x35, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x22, 0x75, 0x0a, 0x2b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x44,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43,
	0x61, 0x72, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0xdd, 0x03, 0x0a, 0x10,
	0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x12, 0x8e, 0x01, 0x0a, 0x1b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b,
	0x12, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x8e, 0x01, 0x0a, 0x1b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72,
	0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x12, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72,
	0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0xa6, 0x01, 0x0a, 0x23, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x44, 0x69,
	0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61,
	0x72, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x3d, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x50, 0x68,
	0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x50, 0x68, 0x79,
	0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x5c, 0x0a, 0x2c, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x5a, 0x2c, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_card_provisioning_callback_consumer_proto_rawDescOnce sync.Once
	file_api_card_provisioning_callback_consumer_proto_rawDescData = file_api_card_provisioning_callback_consumer_proto_rawDesc
)

func file_api_card_provisioning_callback_consumer_proto_rawDescGZIP() []byte {
	file_api_card_provisioning_callback_consumer_proto_rawDescOnce.Do(func() {
		file_api_card_provisioning_callback_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_provisioning_callback_consumer_proto_rawDescData)
	})
	return file_api_card_provisioning_callback_consumer_proto_rawDescData
}

var file_api_card_provisioning_callback_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_card_provisioning_callback_consumer_proto_goTypes = []interface{}{
	(*ProcessCardCreationCallBackRequest)(nil),          // 0: card.provisioning.ProcessCardCreationCallBackRequest
	(*ProcessCardCreationCallBackResponse)(nil),         // 1: card.provisioning.ProcessCardCreationCallBackResponse
	(*CallBackError)(nil),                               // 2: card.provisioning.CallBackError
	(*ProcessCardTrackingCallbackRequest)(nil),          // 3: card.provisioning.ProcessCardTrackingCallbackRequest
	(*TrackingDetails)(nil),                             // 4: card.provisioning.TrackingDetails
	(*ProcessCardTrackingCallbackResponse)(nil),         // 5: card.provisioning.ProcessCardTrackingCallbackResponse
	(*ProcessDispatchPhysicalCardCallbackRequest)(nil),  // 6: card.provisioning.ProcessDispatchPhysicalCardCallbackRequest
	(*ProcessDispatchPhysicalCardCallbackResponse)(nil), // 7: card.provisioning.ProcessDispatchPhysicalCardCallbackResponse
	(*queue.ConsumerRequestHeader)(nil),                 // 8: queue.ConsumerRequestHeader
	(vendorgateway.Vendor)(0),                           // 9: vendorgateway.Vendor
	(card.CardForm)(0),                                  // 10: card.CardForm
	(RequestState)(0),                                   // 11: card.provisioning.RequestState
	(*card.BasicCardInfo)(nil),                          // 12: card.BasicCardInfo
	(*timestamppb.Timestamp)(nil),                       // 13: google.protobuf.Timestamp
	(*queue.ConsumerResponseHeader)(nil),                // 14: queue.ConsumerResponseHeader
	(CardTrackingDeliveryState)(0),                      // 15: card.provisioning.CardTrackingDeliveryState
	(*Scan)(nil),                                        // 16: card.provisioning.Scan
}
var file_api_card_provisioning_callback_consumer_proto_depIdxs = []int32{
	8,  // 0: card.provisioning.ProcessCardCreationCallBackRequest.request_header:type_name -> queue.ConsumerRequestHeader
	9,  // 1: card.provisioning.ProcessCardCreationCallBackRequest.partner_bank:type_name -> vendorgateway.Vendor
	10, // 2: card.provisioning.ProcessCardCreationCallBackRequest.form:type_name -> card.CardForm
	11, // 3: card.provisioning.ProcessCardCreationCallBackRequest.state:type_name -> card.provisioning.RequestState
	2,  // 4: card.provisioning.ProcessCardCreationCallBackRequest.errors:type_name -> card.provisioning.CallBackError
	12, // 5: card.provisioning.ProcessCardCreationCallBackRequest.card_info:type_name -> card.BasicCardInfo
	13, // 6: card.provisioning.ProcessCardCreationCallBackRequest.token_expire_at:type_name -> google.protobuf.Timestamp
	14, // 7: card.provisioning.ProcessCardCreationCallBackResponse.response_header:type_name -> queue.ConsumerResponseHeader
	8,  // 8: card.provisioning.ProcessCardTrackingCallbackRequest.request_header:type_name -> queue.ConsumerRequestHeader
	4,  // 9: card.provisioning.ProcessCardTrackingCallbackRequest.tracking_details_list:type_name -> card.provisioning.TrackingDetails
	15, // 10: card.provisioning.TrackingDetails.delivery_state:type_name -> card.provisioning.CardTrackingDeliveryState
	16, // 11: card.provisioning.TrackingDetails.scans:type_name -> card.provisioning.Scan
	13, // 12: card.provisioning.TrackingDetails.pickup_date:type_name -> google.protobuf.Timestamp
	14, // 13: card.provisioning.ProcessCardTrackingCallbackResponse.response_header:type_name -> queue.ConsumerResponseHeader
	8,  // 14: card.provisioning.ProcessDispatchPhysicalCardCallbackRequest.request_header:type_name -> queue.ConsumerRequestHeader
	9,  // 15: card.provisioning.ProcessDispatchPhysicalCardCallbackRequest.partner_bank:type_name -> vendorgateway.Vendor
	11, // 16: card.provisioning.ProcessDispatchPhysicalCardCallbackRequest.state:type_name -> card.provisioning.RequestState
	14, // 17: card.provisioning.ProcessDispatchPhysicalCardCallbackResponse.response_header:type_name -> queue.ConsumerResponseHeader
	0,  // 18: card.provisioning.CallBackConsumer.ProcessCardCreationCallBack:input_type -> card.provisioning.ProcessCardCreationCallBackRequest
	3,  // 19: card.provisioning.CallBackConsumer.ProcessCardTrackingCallback:input_type -> card.provisioning.ProcessCardTrackingCallbackRequest
	6,  // 20: card.provisioning.CallBackConsumer.ProcessDispatchPhysicalCardCallback:input_type -> card.provisioning.ProcessDispatchPhysicalCardCallbackRequest
	1,  // 21: card.provisioning.CallBackConsumer.ProcessCardCreationCallBack:output_type -> card.provisioning.ProcessCardCreationCallBackResponse
	5,  // 22: card.provisioning.CallBackConsumer.ProcessCardTrackingCallback:output_type -> card.provisioning.ProcessCardTrackingCallbackResponse
	7,  // 23: card.provisioning.CallBackConsumer.ProcessDispatchPhysicalCardCallback:output_type -> card.provisioning.ProcessDispatchPhysicalCardCallbackResponse
	21, // [21:24] is the sub-list for method output_type
	18, // [18:21] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_api_card_provisioning_callback_consumer_proto_init() }
func file_api_card_provisioning_callback_consumer_proto_init() {
	if File_api_card_provisioning_callback_consumer_proto != nil {
		return
	}
	file_api_card_provisioning_card_tracking_request_proto_init()
	file_api_card_provisioning_provisioning_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_card_provisioning_callback_consumer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardCreationCallBackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_callback_consumer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardCreationCallBackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_callback_consumer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallBackError); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_callback_consumer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardTrackingCallbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_callback_consumer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackingDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_callback_consumer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardTrackingCallbackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_callback_consumer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessDispatchPhysicalCardCallbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_provisioning_callback_consumer_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessDispatchPhysicalCardCallbackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_provisioning_callback_consumer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_card_provisioning_callback_consumer_proto_goTypes,
		DependencyIndexes: file_api_card_provisioning_callback_consumer_proto_depIdxs,
		MessageInfos:      file_api_card_provisioning_callback_consumer_proto_msgTypes,
	}.Build()
	File_api_card_provisioning_callback_consumer_proto = out.File
	file_api_card_provisioning_callback_consumer_proto_rawDesc = nil
	file_api_card_provisioning_callback_consumer_proto_goTypes = nil
	file_api_card_provisioning_callback_consumer_proto_depIdxs = nil
}
