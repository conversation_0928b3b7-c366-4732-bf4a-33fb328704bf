// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/provisioning/creation_request.proto

package provisioning

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CardCreationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardCreationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardCreationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardCreationRequestMultiError, or nil if none found.
func (m *CardCreationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CardCreationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CardId

	// no validation rules for State

	// no validation rules for Retries

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardCreationRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardCreationRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardCreationRequestValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardCreationRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardCreationRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardCreationRequestValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureResponseCode

	// no validation rules for FailureResponseReason

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardCreationRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardCreationRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardCreationRequestValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDetailedStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardCreationRequestValidationError{
					field:  "DetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardCreationRequestValidationError{
					field:  "DetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetailedStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardCreationRequestValidationError{
				field:  "DetailedStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardCreationRequestMultiError(errors)
	}

	return nil
}

// CardCreationRequestMultiError is an error wrapping multiple validation
// errors returned by CardCreationRequest.ValidateAll() if the designated
// constraints aren't met.
type CardCreationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardCreationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardCreationRequestMultiError) AllErrors() []error { return m }

// CardCreationRequestValidationError is the validation error returned by
// CardCreationRequest.Validate if the designated constraints aren't met.
type CardCreationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardCreationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardCreationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardCreationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardCreationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardCreationRequestValidationError) ErrorName() string {
	return "CardCreationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CardCreationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardCreationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardCreationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardCreationRequestValidationError{}

// Validate checks the field values on DetailedStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DetailedStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetailedStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DetailedStatusMultiError,
// or nil if none found.
func (m *DetailedStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *DetailedStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InternalResponseCode

	// no validation rules for InternalResponseDescription

	// no validation rules for RawResponseCode

	// no validation rules for RawResponseDescription

	if len(errors) > 0 {
		return DetailedStatusMultiError(errors)
	}

	return nil
}

// DetailedStatusMultiError is an error wrapping multiple validation errors
// returned by DetailedStatus.ValidateAll() if the designated constraints
// aren't met.
type DetailedStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetailedStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetailedStatusMultiError) AllErrors() []error { return m }

// DetailedStatusValidationError is the validation error returned by
// DetailedStatus.Validate if the designated constraints aren't met.
type DetailedStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetailedStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetailedStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetailedStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetailedStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetailedStatusValidationError) ErrorName() string { return "DetailedStatusValidationError" }

// Error satisfies the builtin error interface
func (e DetailedStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetailedStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetailedStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetailedStatusValidationError{}
