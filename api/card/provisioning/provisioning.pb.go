// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=DCRequestStage,RequestSubStatus

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/provisioning/provisioning.proto

package provisioning

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Generic states to be followed for card provisioning requests.
type RequestState int32

const (
	RequestState_REQUEST_STATE_UNSPECIFIED RequestState = 0
	// QUEUED - request is queued to the retry queue and will be processed shortly
	RequestState_QUEUED RequestState = 1
	// INITIATED - request is successfully initiated with the partner bank. Awaiting update.
	RequestState_INITIATED RequestState = 2
	// SUCCESS - request was successfully processed at the partner bank.
	RequestState_SUCCESS RequestState = 3
	// FAILED - request failed at the partner bank. Denotes a non-retry-able failure.
	RequestState_FAILED RequestState = 4
	// MANUAL_INTERVENTION- System has exhausted all the retries post transient errors so this needs attention from a human.
	RequestState_MANUAL_INTERVENTION RequestState = 6
)

// Enum value maps for RequestState.
var (
	RequestState_name = map[int32]string{
		0: "REQUEST_STATE_UNSPECIFIED",
		1: "QUEUED",
		2: "INITIATED",
		3: "SUCCESS",
		4: "FAILED",
		6: "MANUAL_INTERVENTION",
	}
	RequestState_value = map[string]int32{
		"REQUEST_STATE_UNSPECIFIED": 0,
		"QUEUED":                    1,
		"INITIATED":                 2,
		"SUCCESS":                   3,
		"FAILED":                    4,
		"MANUAL_INTERVENTION":       6,
	}
)

func (x RequestState) Enum() *RequestState {
	p := new(RequestState)
	*p = x
	return p
}

func (x RequestState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RequestState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_provisioning_proto_enumTypes[0].Descriptor()
}

func (RequestState) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_provisioning_proto_enumTypes[0]
}

func (x RequestState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RequestState.Descriptor instead.
func (RequestState) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_provisioning_proto_rawDescGZIP(), []int{0}
}

type PinRequestType int32

const (
	PinRequestType_PIN_REQUEST_UNSPECIFIED PinRequestType = 0
	// PIN_SETUP - create a pin for the physical card.
	// The pin is used for ATM/POS transactions.
	PinRequestType_PIN_SETUP PinRequestType = 1
	// PIN_CHANGE - change the pin voluntarily. User needs to remember the
	// old pin to be able to change it.
	PinRequestType_PIN_CHANGE PinRequestType = 2
	// PIN_RESET - force set the pin if a user has forgotten it.
	PinRequestType_PIN_RESET PinRequestType = 3
	// PIN_VALIDATE - validate whether the input pin is correct or not.
	PinRequestType_PIN_VALIDATE PinRequestType = 4
)

// Enum value maps for PinRequestType.
var (
	PinRequestType_name = map[int32]string{
		0: "PIN_REQUEST_UNSPECIFIED",
		1: "PIN_SETUP",
		2: "PIN_CHANGE",
		3: "PIN_RESET",
		4: "PIN_VALIDATE",
	}
	PinRequestType_value = map[string]int32{
		"PIN_REQUEST_UNSPECIFIED": 0,
		"PIN_SETUP":               1,
		"PIN_CHANGE":              2,
		"PIN_RESET":               3,
		"PIN_VALIDATE":            4,
	}
)

func (x PinRequestType) Enum() *PinRequestType {
	p := new(PinRequestType)
	*p = x
	return p
}

func (x PinRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PinRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_provisioning_proto_enumTypes[1].Descriptor()
}

func (PinRequestType) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_provisioning_proto_enumTypes[1]
}

func (x PinRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PinRequestType.Descriptor instead.
func (PinRequestType) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_provisioning_proto_rawDescGZIP(), []int{1}
}

// The approach to provision card to the user. We may choose to provision a single vs 2 cards to the user
// based on our strategy.
type ProvisioningStrategy int32

const (
	ProvisioningStrategy_PROVISIONING_STRATEGY_UNSPECIFIED ProvisioningStrategy = 0
	// Traditional strategy. Provision a card with 16/4/3 printed on the physical card.
	// The associated apprehension is that the 16/4/3 is compromised in case of card loss/theft.
	// Moreover, 16/4/3 is never needed for any POS/ATM txn. So why even print it on the card?
	ProvisioningStrategy_SINGLE_NUMBERED_CARD ProvisioningStrategy = 1
	// Do not print the 16/4/3 on the card. Display only on the APP underneath an auth-factor.
	// Shields against 16/4/3 compromise in case the card is lost/theft.
	ProvisioningStrategy_SINGLE_NUMBERLESS_CARD ProvisioningStrategy = 2
	// Epifi Approach. Provision 2 cards:
	// Digital card: to be used for E-commerce transactions only
	// Physical card: to be used for ATM/POS transactions only.
	ProvisioningStrategy_DUAL_NUMBERLESS_CARD ProvisioningStrategy = 3
)

// Enum value maps for ProvisioningStrategy.
var (
	ProvisioningStrategy_name = map[int32]string{
		0: "PROVISIONING_STRATEGY_UNSPECIFIED",
		1: "SINGLE_NUMBERED_CARD",
		2: "SINGLE_NUMBERLESS_CARD",
		3: "DUAL_NUMBERLESS_CARD",
	}
	ProvisioningStrategy_value = map[string]int32{
		"PROVISIONING_STRATEGY_UNSPECIFIED": 0,
		"SINGLE_NUMBERED_CARD":              1,
		"SINGLE_NUMBERLESS_CARD":            2,
		"DUAL_NUMBERLESS_CARD":              3,
	}
)

func (x ProvisioningStrategy) Enum() *ProvisioningStrategy {
	p := new(ProvisioningStrategy)
	*p = x
	return p
}

func (x ProvisioningStrategy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProvisioningStrategy) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_provisioning_proto_enumTypes[2].Descriptor()
}

func (ProvisioningStrategy) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_provisioning_proto_enumTypes[2]
}

func (x ProvisioningStrategy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProvisioningStrategy.Descriptor instead.
func (ProvisioningStrategy) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_provisioning_proto_rawDescGZIP(), []int{2}
}

// RequestSubStatus helps identify reason behind the current state of dispatch,
// in case of any failures, it can be useful to track back the source of the failure
type RequestSubStatus int32

const (
	RequestSubStatus_REQUEST_SUB_STATUS_UNSPECIFIED RequestSubStatus = 0
	// activity retries exhausted
	RequestSubStatus_REQUEST_SUB_STATUS_ACTIVITY_RETRIES_EXHAUSTED RequestSubStatus = 1
	// vendor has acknowledged the request,
	// but client haven't received success status
	RequestSubStatus_REQUEST_SUB_STATUS_PENDING_ON_VENDOR RequestSubStatus = 2
	// request failed on vendor
	RequestSubStatus_REQUEST_SUB_STATUS_FAILED_ON_VENDOR RequestSubStatus = 3
	// vendor call failure
	RequestSubStatus_REQUEST_SUB_STATUS_FAILED_TO_CALL_VENDOR RequestSubStatus = 4
	// activity or workflow executed successfully
	RequestSubStatus_REQUEST_SUB_STATUS_SUCCESS RequestSubStatus = 5
	// activity is in progress
	RequestSubStatus_REQUEST_SUB_STATUS_IN_PROGRESS RequestSubStatus = 6
	// sub_status is set to unknown when, reason behind request state(failed, manual_intervention) can not be identified with certainty
	RequestSubStatus_REQUEST_SUB_STATUS_UNKNOWN RequestSubStatus = 7
	// no data found error received from any RPC call during wf execution and,
	// we are returning permanent failure and marking the workflow status as failed
	RequestSubStatus_REQUEST_SUB_STATUS_NO_DATA_FOUND RequestSubStatus = 8
)

// Enum value maps for RequestSubStatus.
var (
	RequestSubStatus_name = map[int32]string{
		0: "REQUEST_SUB_STATUS_UNSPECIFIED",
		1: "REQUEST_SUB_STATUS_ACTIVITY_RETRIES_EXHAUSTED",
		2: "REQUEST_SUB_STATUS_PENDING_ON_VENDOR",
		3: "REQUEST_SUB_STATUS_FAILED_ON_VENDOR",
		4: "REQUEST_SUB_STATUS_FAILED_TO_CALL_VENDOR",
		5: "REQUEST_SUB_STATUS_SUCCESS",
		6: "REQUEST_SUB_STATUS_IN_PROGRESS",
		7: "REQUEST_SUB_STATUS_UNKNOWN",
		8: "REQUEST_SUB_STATUS_NO_DATA_FOUND",
	}
	RequestSubStatus_value = map[string]int32{
		"REQUEST_SUB_STATUS_UNSPECIFIED":                0,
		"REQUEST_SUB_STATUS_ACTIVITY_RETRIES_EXHAUSTED": 1,
		"REQUEST_SUB_STATUS_PENDING_ON_VENDOR":          2,
		"REQUEST_SUB_STATUS_FAILED_ON_VENDOR":           3,
		"REQUEST_SUB_STATUS_FAILED_TO_CALL_VENDOR":      4,
		"REQUEST_SUB_STATUS_SUCCESS":                    5,
		"REQUEST_SUB_STATUS_IN_PROGRESS":                6,
		"REQUEST_SUB_STATUS_UNKNOWN":                    7,
		"REQUEST_SUB_STATUS_NO_DATA_FOUND":              8,
	}
)

func (x RequestSubStatus) Enum() *RequestSubStatus {
	p := new(RequestSubStatus)
	*p = x
	return p
}

func (x RequestSubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RequestSubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_provisioning_proto_enumTypes[3].Descriptor()
}

func (RequestSubStatus) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_provisioning_proto_enumTypes[3]
}

func (x RequestSubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RequestSubStatus.Descriptor instead.
func (RequestSubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_provisioning_proto_rawDescGZIP(), []int{3}
}

type DCRequestStage int32

const (
	DCRequestStage_DC_REQUEST_STAGE_UNSPECIFIED DCRequestStage = 0
	// request is in checkPaymentStatus stage
	DCRequestStage_DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS DCRequestStage = 1
	// request is in createShippingPreference stage
	DCRequestStage_DC_REQUEST_STAGE_CREATE_SHIPPING_PREFERENCE DCRequestStage = 2
	// request is in updateShippingAddress stage
	DCRequestStage_DC_REQUEST_STAGE_UPDATE_SHIPPING_ADDRESS DCRequestStage = 3
	// request is in physicalCardDispatch stage
	DCRequestStage_DC_REQUEST_STAGE_DISPATCH_PHYSICAL_CARD DCRequestStage = 4
)

// Enum value maps for DCRequestStage.
var (
	DCRequestStage_name = map[int32]string{
		0: "DC_REQUEST_STAGE_UNSPECIFIED",
		1: "DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS",
		2: "DC_REQUEST_STAGE_CREATE_SHIPPING_PREFERENCE",
		3: "DC_REQUEST_STAGE_UPDATE_SHIPPING_ADDRESS",
		4: "DC_REQUEST_STAGE_DISPATCH_PHYSICAL_CARD",
	}
	DCRequestStage_value = map[string]int32{
		"DC_REQUEST_STAGE_UNSPECIFIED":                0,
		"DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS":       1,
		"DC_REQUEST_STAGE_CREATE_SHIPPING_PREFERENCE": 2,
		"DC_REQUEST_STAGE_UPDATE_SHIPPING_ADDRESS":    3,
		"DC_REQUEST_STAGE_DISPATCH_PHYSICAL_CARD":     4,
	}
)

func (x DCRequestStage) Enum() *DCRequestStage {
	p := new(DCRequestStage)
	*p = x
	return p
}

func (x DCRequestStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DCRequestStage) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_provisioning_proto_enumTypes[4].Descriptor()
}

func (DCRequestStage) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_provisioning_proto_enumTypes[4]
}

func (x DCRequestStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DCRequestStage.Descriptor instead.
func (DCRequestStage) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_provisioning_proto_rawDescGZIP(), []int{4}
}

var File_api_card_provisioning_provisioning_proto protoreflect.FileDescriptor

var file_api_card_provisioning_provisioning_proto_rawDesc = []byte{
	0x0a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2a, 0x7a, 0x0a,
	0x0c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a,
	0x19, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06,
	0x51, 0x55, 0x45, 0x55, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4e, 0x49, 0x54,
	0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04,
	0x12, 0x17, 0x0a, 0x13, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x2a, 0x6d, 0x0a, 0x0e, 0x50, 0x69, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x50,
	0x49, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x49, 0x4e, 0x5f,
	0x53, 0x45, 0x54, 0x55, 0x50, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x49, 0x4e, 0x5f, 0x43,
	0x48, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x49, 0x4e, 0x5f, 0x52,
	0x45, 0x53, 0x45, 0x54, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x49, 0x4e, 0x5f, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x41, 0x54, 0x45, 0x10, 0x04, 0x2a, 0x8d, 0x01, 0x0a, 0x14, 0x50, 0x72, 0x6f,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x54, 0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x49, 0x4e, 0x47,
	0x4c, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x4e, 0x55, 0x4d,
	0x42, 0x45, 0x52, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x02, 0x12, 0x18,
	0x0a, 0x14, 0x44, 0x55, 0x41, 0x4c, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x4c, 0x45, 0x53,
	0x53, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x03, 0x2a, 0xf4, 0x02, 0x0a, 0x10, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a,
	0x1e, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x31, 0x0a, 0x2d, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59,
	0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x48, 0x41, 0x55, 0x53, 0x54,
	0x45, 0x44, 0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x4f, 0x4e, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x27,
	0x0a, 0x23, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x4f, 0x4e, 0x5f, 0x56,
	0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x03, 0x12, 0x2c, 0x0a, 0x28, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x56, 0x45, 0x4e,
	0x44, 0x4f, 0x52, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x10, 0x05, 0x12, 0x22, 0x0a, 0x1e, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x06, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x07, 0x12, 0x24, 0x0a, 0x20, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x4e, 0x4f, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x08, 0x2a,
	0xe9, 0x01, 0x0a, 0x0e, 0x44, 0x43, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x44, 0x43, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x44, 0x43, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x01, 0x12,
	0x2f, 0x0a, 0x2b, 0x44, 0x43, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x48, 0x49, 0x50, 0x50,
	0x49, 0x4e, 0x47, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x43, 0x45, 0x10, 0x02,
	0x12, 0x2c, 0x0a, 0x28, 0x44, 0x43, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x48, 0x49, 0x50,
	0x50, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x2b,
	0x0a, 0x27, 0x44, 0x43, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x50, 0x48, 0x59, 0x53,
	0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x04, 0x42, 0x5c, 0x0a, 0x2c, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x5a, 0x2c, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_card_provisioning_provisioning_proto_rawDescOnce sync.Once
	file_api_card_provisioning_provisioning_proto_rawDescData = file_api_card_provisioning_provisioning_proto_rawDesc
)

func file_api_card_provisioning_provisioning_proto_rawDescGZIP() []byte {
	file_api_card_provisioning_provisioning_proto_rawDescOnce.Do(func() {
		file_api_card_provisioning_provisioning_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_provisioning_provisioning_proto_rawDescData)
	})
	return file_api_card_provisioning_provisioning_proto_rawDescData
}

var file_api_card_provisioning_provisioning_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_api_card_provisioning_provisioning_proto_goTypes = []interface{}{
	(RequestState)(0),         // 0: card.provisioning.RequestState
	(PinRequestType)(0),       // 1: card.provisioning.PinRequestType
	(ProvisioningStrategy)(0), // 2: card.provisioning.ProvisioningStrategy
	(RequestSubStatus)(0),     // 3: card.provisioning.RequestSubStatus
	(DCRequestStage)(0),       // 4: card.provisioning.DCRequestStage
}
var file_api_card_provisioning_provisioning_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_card_provisioning_provisioning_proto_init() }
func file_api_card_provisioning_provisioning_proto_init() {
	if File_api_card_provisioning_provisioning_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_provisioning_provisioning_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_provisioning_provisioning_proto_goTypes,
		DependencyIndexes: file_api_card_provisioning_provisioning_proto_depIdxs,
		EnumInfos:         file_api_card_provisioning_provisioning_proto_enumTypes,
	}.Build()
	File_api_card_provisioning_provisioning_proto = out.File
	file_api_card_provisioning_provisioning_proto_rawDesc = nil
	file_api_card_provisioning_provisioning_proto_goTypes = nil
	file_api_card_provisioning_provisioning_proto_depIdxs = nil
}
