package provisioning

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Value - Valuer interface implementation for storing the data in Json format in DB
func (m *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	return protojson.Marshal(m)
}

// Scan - Scanner interface implementation for parsing data while reading from DB
func (m *PhysicalCardDispatchRequest_PhysicalCardDispatchRequestDetails) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, m)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}
