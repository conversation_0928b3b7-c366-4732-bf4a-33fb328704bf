// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/provisioning/activation_request.proto

package provisioning

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CardActivationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardActivationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardActivationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardActivationRequestMultiError, or nil if none found.
func (m *CardActivationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CardActivationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CardId

	// no validation rules for State

	// no validation rules for Retries

	if len(errors) > 0 {
		return CardActivationRequestMultiError(errors)
	}

	return nil
}

// CardActivationRequestMultiError is an error wrapping multiple validation
// errors returned by CardActivationRequest.ValidateAll() if the designated
// constraints aren't met.
type CardActivationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardActivationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardActivationRequestMultiError) AllErrors() []error { return m }

// CardActivationRequestValidationError is the validation error returned by
// CardActivationRequest.Validate if the designated constraints aren't met.
type CardActivationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardActivationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardActivationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardActivationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardActivationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardActivationRequestValidationError) ErrorName() string {
	return "CardActivationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CardActivationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardActivationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardActivationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardActivationRequestValidationError{}
