// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/provisioning/card_pin.proto

package provisioning

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CardPinState int32

const (
	CardPinState_CARD_PIN_STATE_UNSPECIFIED CardPinState = 0
	CardPinState_SET                        CardPinState = 1
	CardPinState_FAILED_AT_VENDOR           CardPinState = 2
)

// Enum value maps for CardPinState.
var (
	CardPinState_name = map[int32]string{
		0: "CARD_PIN_STATE_UNSPECIFIED",
		1: "SET",
		2: "FAILED_AT_VENDOR",
	}
	CardPinState_value = map[string]int32{
		"CARD_PIN_STATE_UNSPECIFIED": 0,
		"SET":                        1,
		"FAILED_AT_VENDOR":           2,
	}
)

func (x CardPinState) Enum() *CardPinState {
	p := new(CardPinState)
	*p = x
	return p
}

func (x CardPinState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardPinState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_card_pin_proto_enumTypes[0].Descriptor()
}

func (CardPinState) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_card_pin_proto_enumTypes[0]
}

func (x CardPinState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardPinState.Descriptor instead.
func (CardPinState) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_pin_proto_rawDescGZIP(), []int{0}
}

// CardFieldMask is the enum representation of all the card fields.
// Meant to be used as field mask to help with database updates.
type CardPinFieldMask int32

const (
	CardPinFieldMask_CARD_PIN_FIELD_MASK_UNSPECIFIED CardPinFieldMask = 0
	CardPinFieldMask_CARD_PIN_CARD_ID                CardPinFieldMask = 2
	CardPinFieldMask_CARD_PIN_STATE                  CardPinFieldMask = 3
	CardPinFieldMask_CARD_PIN_ID                     CardPinFieldMask = 4
	CardPinFieldMask_CARD_PIN_CREATED_AT             CardPinFieldMask = 5
	CardPinFieldMask_CARD_PIN_UPDATED_AT             CardPinFieldMask = 6
	CardPinFieldMask_CARD_PIN_DELETED_AT             CardPinFieldMask = 7
)

// Enum value maps for CardPinFieldMask.
var (
	CardPinFieldMask_name = map[int32]string{
		0: "CARD_PIN_FIELD_MASK_UNSPECIFIED",
		2: "CARD_PIN_CARD_ID",
		3: "CARD_PIN_STATE",
		4: "CARD_PIN_ID",
		5: "CARD_PIN_CREATED_AT",
		6: "CARD_PIN_UPDATED_AT",
		7: "CARD_PIN_DELETED_AT",
	}
	CardPinFieldMask_value = map[string]int32{
		"CARD_PIN_FIELD_MASK_UNSPECIFIED": 0,
		"CARD_PIN_CARD_ID":                2,
		"CARD_PIN_STATE":                  3,
		"CARD_PIN_ID":                     4,
		"CARD_PIN_CREATED_AT":             5,
		"CARD_PIN_UPDATED_AT":             6,
		"CARD_PIN_DELETED_AT":             7,
	}
)

func (x CardPinFieldMask) Enum() *CardPinFieldMask {
	p := new(CardPinFieldMask)
	*p = x
	return p
}

func (x CardPinFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardPinFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_card_pin_proto_enumTypes[1].Descriptor()
}

func (CardPinFieldMask) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_card_pin_proto_enumTypes[1]
}

func (x CardPinFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardPinFieldMask.Descriptor instead.
func (CardPinFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_pin_proto_rawDescGZIP(), []int{1}
}

// PinSetFlow is to mark the type of pin set request.
type PinSetFlow int32

const (
	PinSetFlow_PIN_SET_FLOW_UNSPECIFIED PinSetFlow = 0
	// for pin set via otp auth/credblock
	PinSetFlow_PIN_SET_WITH_OTP PinSetFlow = 1
	// For pin set via token. It will be used while onboarding only.
	PinSetFlow_PIN_SET_WITH_TOKEN PinSetFlow = 2
)

// Enum value maps for PinSetFlow.
var (
	PinSetFlow_name = map[int32]string{
		0: "PIN_SET_FLOW_UNSPECIFIED",
		1: "PIN_SET_WITH_OTP",
		2: "PIN_SET_WITH_TOKEN",
	}
	PinSetFlow_value = map[string]int32{
		"PIN_SET_FLOW_UNSPECIFIED": 0,
		"PIN_SET_WITH_OTP":         1,
		"PIN_SET_WITH_TOKEN":       2,
	}
)

func (x PinSetFlow) Enum() *PinSetFlow {
	p := new(PinSetFlow)
	*p = x
	return p
}

func (x PinSetFlow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PinSetFlow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_provisioning_card_pin_proto_enumTypes[2].Descriptor()
}

func (PinSetFlow) Type() protoreflect.EnumType {
	return &file_api_card_provisioning_card_pin_proto_enumTypes[2]
}

func (x PinSetFlow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PinSetFlow.Descriptor instead.
func (PinSetFlow) EnumDescriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_pin_proto_rawDescGZIP(), []int{2}
}

type CardPin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Primary identifier to the card pin database model.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Primary identifier to the card database model. Internal to Epifi.
	// Links card pin model to the card model using card-id as a foreign-key.
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// The state of the card pin. If the pin was successfully set, the state must be recorded here as SET.
	State CardPinState `protobuf:"varint,3,opt,name=state,proto3,enum=card.provisioning.CardPinState" json:"state,omitempty"`
	// card creation timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// card update timestamp
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *CardPin) Reset() {
	*x = CardPin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_provisioning_card_pin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardPin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardPin) ProtoMessage() {}

func (x *CardPin) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_provisioning_card_pin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardPin.ProtoReflect.Descriptor instead.
func (*CardPin) Descriptor() ([]byte, []int) {
	return file_api_card_provisioning_card_pin_proto_rawDescGZIP(), []int{0}
}

func (x *CardPin) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CardPin) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CardPin) GetState() CardPinState {
	if x != nil {
		return x.State
	}
	return CardPinState_CARD_PIN_STATE_UNSPECIFIED
}

func (x *CardPin) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CardPin) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CardPin) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_card_provisioning_card_pin_proto protoreflect.FileDescriptor

var file_api_card_provisioning_card_pin_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x69, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9a, 0x02, 0x0a, 0x07, 0x43,
	0x61, 0x72, 0x64, 0x50, 0x69, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12,
	0x35, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69,
	0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x50, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0x4d, 0x0a, 0x0c, 0x43, 0x61, 0x72, 0x64, 0x50,
	0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x50, 0x49, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x53, 0x45, 0x54, 0x10, 0x01,
	0x12, 0x14, 0x0a, 0x10, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45,
	0x4e, 0x44, 0x4f, 0x52, 0x10, 0x02, 0x2a, 0xbd, 0x01, 0x0a, 0x10, 0x43, 0x61, 0x72, 0x64, 0x50,
	0x69, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x23, 0x0a, 0x1f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50,
	0x49, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f,
	0x41, 0x54, 0x10, 0x05, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x49, 0x4e,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x06, 0x12, 0x17, 0x0a,
	0x13, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x07, 0x2a, 0x58, 0x0a, 0x0a, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74,
	0x46, 0x6c, 0x6f, 0x77, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x57, 0x49,
	0x54, 0x48, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x49, 0x4e, 0x5f,
	0x53, 0x45, 0x54, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x02,
	0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67,
	0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72,
	0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_provisioning_card_pin_proto_rawDescOnce sync.Once
	file_api_card_provisioning_card_pin_proto_rawDescData = file_api_card_provisioning_card_pin_proto_rawDesc
)

func file_api_card_provisioning_card_pin_proto_rawDescGZIP() []byte {
	file_api_card_provisioning_card_pin_proto_rawDescOnce.Do(func() {
		file_api_card_provisioning_card_pin_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_provisioning_card_pin_proto_rawDescData)
	})
	return file_api_card_provisioning_card_pin_proto_rawDescData
}

var file_api_card_provisioning_card_pin_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_card_provisioning_card_pin_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_card_provisioning_card_pin_proto_goTypes = []interface{}{
	(CardPinState)(0),             // 0: card.provisioning.CardPinState
	(CardPinFieldMask)(0),         // 1: card.provisioning.CardPinFieldMask
	(PinSetFlow)(0),               // 2: card.provisioning.PinSetFlow
	(*CardPin)(nil),               // 3: card.provisioning.CardPin
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
}
var file_api_card_provisioning_card_pin_proto_depIdxs = []int32{
	0, // 0: card.provisioning.CardPin.state:type_name -> card.provisioning.CardPinState
	4, // 1: card.provisioning.CardPin.created_at:type_name -> google.protobuf.Timestamp
	4, // 2: card.provisioning.CardPin.updated_at:type_name -> google.protobuf.Timestamp
	4, // 3: card.provisioning.CardPin.deleted_at:type_name -> google.protobuf.Timestamp
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_card_provisioning_card_pin_proto_init() }
func file_api_card_provisioning_card_pin_proto_init() {
	if File_api_card_provisioning_card_pin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_provisioning_card_pin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardPin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_provisioning_card_pin_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_provisioning_card_pin_proto_goTypes,
		DependencyIndexes: file_api_card_provisioning_card_pin_proto_depIdxs,
		EnumInfos:         file_api_card_provisioning_card_pin_proto_enumTypes,
		MessageInfos:      file_api_card_provisioning_card_pin_proto_msgTypes,
	}.Build()
	File_api_card_provisioning_card_pin_proto = out.File
	file_api_card_provisioning_card_pin_proto_rawDesc = nil
	file_api_card_provisioning_card_pin_proto_goTypes = nil
	file_api_card_provisioning_card_pin_proto_depIdxs = nil
}
