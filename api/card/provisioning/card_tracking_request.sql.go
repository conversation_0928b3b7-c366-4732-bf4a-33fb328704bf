package provisioning

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"encoding/json"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x CardTrackingRequestState) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *CardTrackingRequestState) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := CardTrackingRequestState_value[val]
	*x = CardTrackingRequestState(valInt)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x CardTrackingDeliveryState) Value() (driver.Value, error) {
	return x.String(), nil
}

// <PERSON><PERSON> implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *CardTrackingDeliveryState) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := CardTrackingDeliveryState_value[val]
	*x = CardTrackingDeliveryState(valInt)
	return nil
}

// Valuer interface implementation for storing the data in string format in DB
func (m *Scan) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	return protojson.Marshal(m)
}

// Scanner interface implementation for parsing data while reading from DB
func (m *Scan) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, m)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}

type ScansList []*Scan

// Valuer interface implementation for storing the data in string format in DB
func (o *ScansList) Value() (driver.Value, error) {
	if o == nil {
		return nil, nil
	}
	return json.Marshal(o)
}

// Scanner interface implementation for parsing data while reading from DB
func (o *ScansList) Scan(input interface{}) error {
	val, ok := input.([]byte)
	if !ok {
		err := fmt.Errorf("expected []byte, got %T", input)
		return err
	}
	return json.Unmarshal(val, o)
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x CardPrintingVendor) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *CardPrintingVendor) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := CardPrintingVendor_value[val]
	*x = CardPrintingVendor(valInt)
	return nil
}
