// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/card/provisioning/callback_consumer.proto

package provisioning

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CallBackConsumer_ProcessCardCreationCallBack_FullMethodName         = "/card.provisioning.CallBackConsumer/ProcessCardCreationCallBack"
	CallBackConsumer_ProcessCardTrackingCallback_FullMethodName         = "/card.provisioning.CallBackConsumer/ProcessCardTrackingCallback"
	CallBackConsumer_ProcessDispatchPhysicalCardCallback_FullMethodName = "/card.provisioning.CallBackConsumer/ProcessDispatchPhysicalCardCallback"
)

// CallBackConsumerClient is the client API for CallBackConsumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CallBackConsumerClient interface {
	// RPC updates the state of a card creation request.
	// This method is invoked by queue subscriber to consume final response from vendor's card service.
	ProcessCardCreationCallBack(ctx context.Context, in *ProcessCardCreationCallBackRequest, opts ...grpc.CallOption) (*ProcessCardCreationCallBackResponse, error)
	// ProcessCardTrackingCallback processes the callbacks received for a given shipment.
	// We receive callbacks when there is some state change for the shipment such as from SHIPPED->IN_TRANSIT, IN_TRANSIT->OUT_FOR_DELIVERY etc.
	// We will process these callbacks to communicate and show user their card's tracking details
	ProcessCardTrackingCallback(ctx context.Context, in *ProcessCardTrackingCallbackRequest, opts ...grpc.CallOption) (*ProcessCardTrackingCallbackResponse, error)
	// ProcessDispatchPhysicalCardCallback processes the callback from vendor for physical card dispatch
	// It updates the status of card dispatch request based on vendor's response
	ProcessDispatchPhysicalCardCallback(ctx context.Context, in *ProcessDispatchPhysicalCardCallbackRequest, opts ...grpc.CallOption) (*ProcessDispatchPhysicalCardCallbackResponse, error)
}

type callBackConsumerClient struct {
	cc grpc.ClientConnInterface
}

func NewCallBackConsumerClient(cc grpc.ClientConnInterface) CallBackConsumerClient {
	return &callBackConsumerClient{cc}
}

func (c *callBackConsumerClient) ProcessCardCreationCallBack(ctx context.Context, in *ProcessCardCreationCallBackRequest, opts ...grpc.CallOption) (*ProcessCardCreationCallBackResponse, error) {
	out := new(ProcessCardCreationCallBackResponse)
	err := c.cc.Invoke(ctx, CallBackConsumer_ProcessCardCreationCallBack_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callBackConsumerClient) ProcessCardTrackingCallback(ctx context.Context, in *ProcessCardTrackingCallbackRequest, opts ...grpc.CallOption) (*ProcessCardTrackingCallbackResponse, error) {
	out := new(ProcessCardTrackingCallbackResponse)
	err := c.cc.Invoke(ctx, CallBackConsumer_ProcessCardTrackingCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callBackConsumerClient) ProcessDispatchPhysicalCardCallback(ctx context.Context, in *ProcessDispatchPhysicalCardCallbackRequest, opts ...grpc.CallOption) (*ProcessDispatchPhysicalCardCallbackResponse, error) {
	out := new(ProcessDispatchPhysicalCardCallbackResponse)
	err := c.cc.Invoke(ctx, CallBackConsumer_ProcessDispatchPhysicalCardCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CallBackConsumerServer is the server API for CallBackConsumer service.
// All implementations should embed UnimplementedCallBackConsumerServer
// for forward compatibility
type CallBackConsumerServer interface {
	// RPC updates the state of a card creation request.
	// This method is invoked by queue subscriber to consume final response from vendor's card service.
	ProcessCardCreationCallBack(context.Context, *ProcessCardCreationCallBackRequest) (*ProcessCardCreationCallBackResponse, error)
	// ProcessCardTrackingCallback processes the callbacks received for a given shipment.
	// We receive callbacks when there is some state change for the shipment such as from SHIPPED->IN_TRANSIT, IN_TRANSIT->OUT_FOR_DELIVERY etc.
	// We will process these callbacks to communicate and show user their card's tracking details
	ProcessCardTrackingCallback(context.Context, *ProcessCardTrackingCallbackRequest) (*ProcessCardTrackingCallbackResponse, error)
	// ProcessDispatchPhysicalCardCallback processes the callback from vendor for physical card dispatch
	// It updates the status of card dispatch request based on vendor's response
	ProcessDispatchPhysicalCardCallback(context.Context, *ProcessDispatchPhysicalCardCallbackRequest) (*ProcessDispatchPhysicalCardCallbackResponse, error)
}

// UnimplementedCallBackConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedCallBackConsumerServer struct {
}

func (UnimplementedCallBackConsumerServer) ProcessCardCreationCallBack(context.Context, *ProcessCardCreationCallBackRequest) (*ProcessCardCreationCallBackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardCreationCallBack not implemented")
}
func (UnimplementedCallBackConsumerServer) ProcessCardTrackingCallback(context.Context, *ProcessCardTrackingCallbackRequest) (*ProcessCardTrackingCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardTrackingCallback not implemented")
}
func (UnimplementedCallBackConsumerServer) ProcessDispatchPhysicalCardCallback(context.Context, *ProcessDispatchPhysicalCardCallbackRequest) (*ProcessDispatchPhysicalCardCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessDispatchPhysicalCardCallback not implemented")
}

// UnsafeCallBackConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CallBackConsumerServer will
// result in compilation errors.
type UnsafeCallBackConsumerServer interface {
	mustEmbedUnimplementedCallBackConsumerServer()
}

func RegisterCallBackConsumerServer(s grpc.ServiceRegistrar, srv CallBackConsumerServer) {
	s.RegisterService(&CallBackConsumer_ServiceDesc, srv)
}

func _CallBackConsumer_ProcessCardCreationCallBack_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCardCreationCallBackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallBackConsumerServer).ProcessCardCreationCallBack(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallBackConsumer_ProcessCardCreationCallBack_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallBackConsumerServer).ProcessCardCreationCallBack(ctx, req.(*ProcessCardCreationCallBackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallBackConsumer_ProcessCardTrackingCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCardTrackingCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallBackConsumerServer).ProcessCardTrackingCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallBackConsumer_ProcessCardTrackingCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallBackConsumerServer).ProcessCardTrackingCallback(ctx, req.(*ProcessCardTrackingCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallBackConsumer_ProcessDispatchPhysicalCardCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessDispatchPhysicalCardCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallBackConsumerServer).ProcessDispatchPhysicalCardCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallBackConsumer_ProcessDispatchPhysicalCardCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallBackConsumerServer).ProcessDispatchPhysicalCardCallback(ctx, req.(*ProcessDispatchPhysicalCardCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CallBackConsumer_ServiceDesc is the grpc.ServiceDesc for CallBackConsumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CallBackConsumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "card.provisioning.CallBackConsumer",
	HandlerType: (*CallBackConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessCardCreationCallBack",
			Handler:    _CallBackConsumer_ProcessCardCreationCallBack_Handler,
		},
		{
			MethodName: "ProcessCardTrackingCallback",
			Handler:    _CallBackConsumer_ProcessCardTrackingCallback_Handler,
		},
		{
			MethodName: "ProcessDispatchPhysicalCardCallback",
			Handler:    _CallBackConsumer_ProcessDispatchPhysicalCardCallback_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/card/provisioning/callback_consumer.proto",
}
