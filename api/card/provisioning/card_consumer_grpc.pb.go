//go:generate gen_queue_pb

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/card/provisioning/card_consumer.proto

package provisioning

import (
	context "context"
	notification "github.com/epifi/gamma/api/auth/notification"
	event "github.com/epifi/gamma/api/user/event"
	onboarding "github.com/epifi/gamma/api/user/onboarding"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CardConsumer_ProcessCardCreation_FullMethodName                                 = "/card.provisioning.CardConsumer/ProcessCardCreation"
	CardConsumer_ProcessCardPiCreation_FullMethodName                               = "/card.provisioning.CardConsumer/ProcessCardPiCreation"
	CardConsumer_ProcessCardPinSetEvent_FullMethodName                              = "/card.provisioning.CardConsumer/ProcessCardPinSetEvent"
	CardConsumer_ProcessCardRenewalEvent_FullMethodName                             = "/card.provisioning.CardConsumer/ProcessCardRenewalEvent"
	CardConsumer_ProcessCardOnboardingEvent_FullMethodName                          = "/card.provisioning.CardConsumer/ProcessCardOnboardingEvent"
	CardConsumer_ProcessAuthFactorUpdateEvent_FullMethodName                        = "/card.provisioning.CardConsumer/ProcessAuthFactorUpdateEvent"
	CardConsumer_ProcessCardShipmentRegisterEvent_FullMethodName                    = "/card.provisioning.CardConsumer/ProcessCardShipmentRegisterEvent"
	CardConsumer_ProcessCardDeliveryDelayEvent_FullMethodName                       = "/card.provisioning.CardConsumer/ProcessCardDeliveryDelayEvent"
	CardConsumer_ProcessCardDeliveredEvent_FullMethodName                           = "/card.provisioning.CardConsumer/ProcessCardDeliveredEvent"
	CardConsumer_GetTrackingDetails_FullMethodName                                  = "/card.provisioning.CardConsumer/GetTrackingDetails"
	CardConsumer_ProcessCardDispatch_FullMethodName                                 = "/card.provisioning.CardConsumer/ProcessCardDispatch"
	CardConsumer_ProcessShippingAddressUpdateAndDispatchPhysicalCard_FullMethodName = "/card.provisioning.CardConsumer/ProcessShippingAddressUpdateAndDispatchPhysicalCard"
	CardConsumer_ProcessCardsDispatchedCsvFile_FullMethodName                       = "/card.provisioning.CardConsumer/ProcessCardsDispatchedCsvFile"
	CardConsumer_ProcessCardAmcChargesEligibleUserFile_FullMethodName               = "/card.provisioning.CardConsumer/ProcessCardAmcChargesEligibleUserFile"
	CardConsumer_OrderPhysicalCardCriticalNotification_FullMethodName               = "/card.provisioning.CardConsumer/OrderPhysicalCardCriticalNotification"
	CardConsumer_ProcessUserDevicePropertiesUpdateEvent_FullMethodName              = "/card.provisioning.CardConsumer/ProcessUserDevicePropertiesUpdateEvent"
)

// CardConsumerClient is the client API for CardConsumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CardConsumerClient interface {
	// RPC processes the card creation messages from the queue and progresses the state machine.
	// If the current state is QUEUED, attempts to INITIATE the call to the issuing bank.
	// If the current state is INITIATE, checks the status at the issuing bank for SUCCESS/FAILURE updates.
	// The above can be retried until the max number of attempts. If max retries are reached,
	// the status is set to MANUAL_INTERVENTION and processing is permanently halted.
	ProcessCardCreation(ctx context.Context, in *ProcessCardCreationRequest, opts ...grpc.CallOption) (*ProcessCardCreationResponse, error)
	// RPC processes the request to create card PI entry and registers it with the PI service.
	// The method is invoked by the queue consumer.
	ProcessCardPiCreation(ctx context.Context, in *ProcessCardPiCreationRequest, opts ...grpc.CallOption) (*ProcessCardPiCreationResponse, error)
	// RPC processes the request to trigger events post successful card pin setup. For a newly issued card,
	// after the pin is setup multiple card controls need to be set:
	// - Disable all POS/ATM transactions (on pin setup, card is enabled for these txns).
	// - Enable domestic E-commerce txns.
	// The method is invoked by the queue consumer.
	ProcessCardPinSetEvent(ctx context.Context, in *ProcessCardPinSetEventRequest, opts ...grpc.CallOption) (*ProcessCardPinSetEventResponse, error)
	// RPC processes the request to block card and publish packet to create a new card.
	// New card creation is initiated only after block card is successful
	ProcessCardRenewalEvent(ctx context.Context, in *ProcessCardRenewalEventRequest, opts ...grpc.CallOption) (*ProcessCardRenewalEventResponse, error)
	// RPC process request to trigger events post onboarding stage
	// For example : We need to trigger an in-app notification when user lands on home screen post successful onboarding completion
	// for enabling e-commerce transactions if not already enabled
	ProcessCardOnboardingEvent(ctx context.Context, in *onboarding.OnboardingStageUpdate, opts ...grpc.CallOption) (*ProcessCardOnboardingEventResponse, error)
	// RPC process request to trigger events for auth factor update for re-oobe cases.
	// For example : We need to stop user from scanning the QR code for physical card activation after phone number update
	ProcessAuthFactorUpdateEvent(ctx context.Context, in *notification.AuthFactorUpdateEvent, opts ...grpc.CallOption) (*ProcessAuthFactorUpdateEventResponse, error)
	// We publish a packet after fetching the AWB number from the bank to register them at vendors end.
	// Consumer to register the card shipments at Vendor.
	// After this step we will start receiving shipment updates for the successfully registered shipments.
	ProcessCardShipmentRegisterEvent(ctx context.Context, in *ProcessCardShipmentRegisterEventRequest, opts ...grpc.CallOption) (*ProcessCardShipmentRegisterEventResponse, error)
	// ProcessCardDeliveryDelayEvent triggers communication to users in case card is not yet shipped (delivery initiated).
	ProcessCardDeliveryDelayEvent(ctx context.Context, in *ProcessCardDeliveryDelayEventRequest, opts ...grpc.CallOption) (*ProcessCardDeliveryDelayEventResponse, error)
	// ProcessCardDeliveredEvent consumes event triggered post card is delivered to the user. The event is triggered by the
	// Shipway callback consumer after we receive callback for delivered state.
	// Currently we plan to send periodic communication to users to activate their card via QR code scan post delivery.
	ProcessCardDeliveredEvent(ctx context.Context, in *ProcessCardDeliveredEventRequest, opts ...grpc.CallOption) (*ProcessCardDeliveredEventResponse, error)
	// FetchTrackingDetails will try to fetch tracking details for a card from bank.
	// As tracking details are not present as soon as card gets created we will publish the packet with a time delay.
	GetTrackingDetails(ctx context.Context, in *GetTrackingDetailsRequest, opts ...grpc.CallOption) (*GetTrackingDetailsResponse, error)
	// CreatePhysicalCardDispatchAttempt creates dispatch request for a card and publishes a packet which is
	// consumed by ProcessCardDispatch rpc.
	//
	// ProcessCardDispatch processes the card dispatch messages from the queue and progresses the state machine
	// for a dispatch request.
	// If the current state is QUEUED, attempts to INITIATE the call to the issuing bank.
	// If the current state is INITIATE, checks the status at the issuing bank for SUCCESS/FAILURE updates.
	// The above can be retried until the max number of attempts. If max retries are reached,
	// the status is set to MANUAL_INTERVENTION and processing is permanently halted unless we get a manual trigger for.
	ProcessCardDispatch(ctx context.Context, in *ProcessCardDispatchRequest, opts ...grpc.CallOption) (*ProcessCardDispatchResponse, error)
	// ProcessShippingAddressUpdateAndDispatchPhysicalCard is a consumer rpc which processes the packet published by the InitiateShippingAddressUpdateAndDispatchPhysicalCard rpc.
	// InitiateShippingAddressUpdateAndDispatchPhysicalCard is invoked by client when user requests for a physical card
	// ProcessShippingAddressUpdateAndDispatchPhysicalCard initiates shipping address update and checks the status of the request and triggers
	// CreatePhysicalCardDispatchAttempt rpc to create dispatch request post shipping address update is successful.
	ProcessShippingAddressUpdateAndDispatchPhysicalCard(ctx context.Context, in *ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest, opts ...grpc.CallOption) (*ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse, error)
	// rpc ProcessCardsDispatchedCsvFile process csv file received from vendor for with details of cards dispatched
	// this rpc is invoked via s3 event which is fired when vendor sends csv file to our sftp
	ProcessCardsDispatchedCsvFile(ctx context.Context, in *ProcessCardsDispatchedCsvFileRequest, opts ...grpc.CallOption) (*ProcessCardsDispatchedCsvFileResponse, error)
	ProcessCardAmcChargesEligibleUserFile(ctx context.Context, in *ProcessCardAmcChargesEligibleUserFileRequest, opts ...grpc.CallOption) (*ProcessCardAmcChargesEligibleUserFileResponse, error)
	// This consumer will send a critical in-app notification to the user after 7 days of onboarding to order a physical debit card
	OrderPhysicalCardCriticalNotification(ctx context.Context, in *OrderPhysicalCardCriticalNotificationRequest, opts ...grpc.CallOption) (*OrderPhysicalCardCriticalNotificationResponse, error)
	// ProcessUserDevicePropertiesUpdateEvent is a consumer rpc which processes the packet published to user device update topic
	// It processes user location update event and checks if the current updated location is India or not,
	// if it's not india (i.e. international location) then publish a rudder event and store the location in transient storage
	ProcessUserDevicePropertiesUpdateEvent(ctx context.Context, in *event.UserDevicePropertyUpdateEvent, opts ...grpc.CallOption) (*ProcessUserDevicePropertiesUpdateEventResponse, error)
}

type cardConsumerClient struct {
	cc grpc.ClientConnInterface
}

func NewCardConsumerClient(cc grpc.ClientConnInterface) CardConsumerClient {
	return &cardConsumerClient{cc}
}

func (c *cardConsumerClient) ProcessCardCreation(ctx context.Context, in *ProcessCardCreationRequest, opts ...grpc.CallOption) (*ProcessCardCreationResponse, error) {
	out := new(ProcessCardCreationResponse)
	err := c.cc.Invoke(ctx, CardConsumer_ProcessCardCreation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) ProcessCardPiCreation(ctx context.Context, in *ProcessCardPiCreationRequest, opts ...grpc.CallOption) (*ProcessCardPiCreationResponse, error) {
	out := new(ProcessCardPiCreationResponse)
	err := c.cc.Invoke(ctx, CardConsumer_ProcessCardPiCreation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) ProcessCardPinSetEvent(ctx context.Context, in *ProcessCardPinSetEventRequest, opts ...grpc.CallOption) (*ProcessCardPinSetEventResponse, error) {
	out := new(ProcessCardPinSetEventResponse)
	err := c.cc.Invoke(ctx, CardConsumer_ProcessCardPinSetEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) ProcessCardRenewalEvent(ctx context.Context, in *ProcessCardRenewalEventRequest, opts ...grpc.CallOption) (*ProcessCardRenewalEventResponse, error) {
	out := new(ProcessCardRenewalEventResponse)
	err := c.cc.Invoke(ctx, CardConsumer_ProcessCardRenewalEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) ProcessCardOnboardingEvent(ctx context.Context, in *onboarding.OnboardingStageUpdate, opts ...grpc.CallOption) (*ProcessCardOnboardingEventResponse, error) {
	out := new(ProcessCardOnboardingEventResponse)
	err := c.cc.Invoke(ctx, CardConsumer_ProcessCardOnboardingEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) ProcessAuthFactorUpdateEvent(ctx context.Context, in *notification.AuthFactorUpdateEvent, opts ...grpc.CallOption) (*ProcessAuthFactorUpdateEventResponse, error) {
	out := new(ProcessAuthFactorUpdateEventResponse)
	err := c.cc.Invoke(ctx, CardConsumer_ProcessAuthFactorUpdateEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) ProcessCardShipmentRegisterEvent(ctx context.Context, in *ProcessCardShipmentRegisterEventRequest, opts ...grpc.CallOption) (*ProcessCardShipmentRegisterEventResponse, error) {
	out := new(ProcessCardShipmentRegisterEventResponse)
	err := c.cc.Invoke(ctx, CardConsumer_ProcessCardShipmentRegisterEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) ProcessCardDeliveryDelayEvent(ctx context.Context, in *ProcessCardDeliveryDelayEventRequest, opts ...grpc.CallOption) (*ProcessCardDeliveryDelayEventResponse, error) {
	out := new(ProcessCardDeliveryDelayEventResponse)
	err := c.cc.Invoke(ctx, CardConsumer_ProcessCardDeliveryDelayEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) ProcessCardDeliveredEvent(ctx context.Context, in *ProcessCardDeliveredEventRequest, opts ...grpc.CallOption) (*ProcessCardDeliveredEventResponse, error) {
	out := new(ProcessCardDeliveredEventResponse)
	err := c.cc.Invoke(ctx, CardConsumer_ProcessCardDeliveredEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) GetTrackingDetails(ctx context.Context, in *GetTrackingDetailsRequest, opts ...grpc.CallOption) (*GetTrackingDetailsResponse, error) {
	out := new(GetTrackingDetailsResponse)
	err := c.cc.Invoke(ctx, CardConsumer_GetTrackingDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) ProcessCardDispatch(ctx context.Context, in *ProcessCardDispatchRequest, opts ...grpc.CallOption) (*ProcessCardDispatchResponse, error) {
	out := new(ProcessCardDispatchResponse)
	err := c.cc.Invoke(ctx, CardConsumer_ProcessCardDispatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) ProcessShippingAddressUpdateAndDispatchPhysicalCard(ctx context.Context, in *ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest, opts ...grpc.CallOption) (*ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse, error) {
	out := new(ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse)
	err := c.cc.Invoke(ctx, CardConsumer_ProcessShippingAddressUpdateAndDispatchPhysicalCard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) ProcessCardsDispatchedCsvFile(ctx context.Context, in *ProcessCardsDispatchedCsvFileRequest, opts ...grpc.CallOption) (*ProcessCardsDispatchedCsvFileResponse, error) {
	out := new(ProcessCardsDispatchedCsvFileResponse)
	err := c.cc.Invoke(ctx, CardConsumer_ProcessCardsDispatchedCsvFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) ProcessCardAmcChargesEligibleUserFile(ctx context.Context, in *ProcessCardAmcChargesEligibleUserFileRequest, opts ...grpc.CallOption) (*ProcessCardAmcChargesEligibleUserFileResponse, error) {
	out := new(ProcessCardAmcChargesEligibleUserFileResponse)
	err := c.cc.Invoke(ctx, CardConsumer_ProcessCardAmcChargesEligibleUserFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) OrderPhysicalCardCriticalNotification(ctx context.Context, in *OrderPhysicalCardCriticalNotificationRequest, opts ...grpc.CallOption) (*OrderPhysicalCardCriticalNotificationResponse, error) {
	out := new(OrderPhysicalCardCriticalNotificationResponse)
	err := c.cc.Invoke(ctx, CardConsumer_OrderPhysicalCardCriticalNotification_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardConsumerClient) ProcessUserDevicePropertiesUpdateEvent(ctx context.Context, in *event.UserDevicePropertyUpdateEvent, opts ...grpc.CallOption) (*ProcessUserDevicePropertiesUpdateEventResponse, error) {
	out := new(ProcessUserDevicePropertiesUpdateEventResponse)
	err := c.cc.Invoke(ctx, CardConsumer_ProcessUserDevicePropertiesUpdateEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CardConsumerServer is the server API for CardConsumer service.
// All implementations should embed UnimplementedCardConsumerServer
// for forward compatibility
type CardConsumerServer interface {
	// RPC processes the card creation messages from the queue and progresses the state machine.
	// If the current state is QUEUED, attempts to INITIATE the call to the issuing bank.
	// If the current state is INITIATE, checks the status at the issuing bank for SUCCESS/FAILURE updates.
	// The above can be retried until the max number of attempts. If max retries are reached,
	// the status is set to MANUAL_INTERVENTION and processing is permanently halted.
	ProcessCardCreation(context.Context, *ProcessCardCreationRequest) (*ProcessCardCreationResponse, error)
	// RPC processes the request to create card PI entry and registers it with the PI service.
	// The method is invoked by the queue consumer.
	ProcessCardPiCreation(context.Context, *ProcessCardPiCreationRequest) (*ProcessCardPiCreationResponse, error)
	// RPC processes the request to trigger events post successful card pin setup. For a newly issued card,
	// after the pin is setup multiple card controls need to be set:
	// - Disable all POS/ATM transactions (on pin setup, card is enabled for these txns).
	// - Enable domestic E-commerce txns.
	// The method is invoked by the queue consumer.
	ProcessCardPinSetEvent(context.Context, *ProcessCardPinSetEventRequest) (*ProcessCardPinSetEventResponse, error)
	// RPC processes the request to block card and publish packet to create a new card.
	// New card creation is initiated only after block card is successful
	ProcessCardRenewalEvent(context.Context, *ProcessCardRenewalEventRequest) (*ProcessCardRenewalEventResponse, error)
	// RPC process request to trigger events post onboarding stage
	// For example : We need to trigger an in-app notification when user lands on home screen post successful onboarding completion
	// for enabling e-commerce transactions if not already enabled
	ProcessCardOnboardingEvent(context.Context, *onboarding.OnboardingStageUpdate) (*ProcessCardOnboardingEventResponse, error)
	// RPC process request to trigger events for auth factor update for re-oobe cases.
	// For example : We need to stop user from scanning the QR code for physical card activation after phone number update
	ProcessAuthFactorUpdateEvent(context.Context, *notification.AuthFactorUpdateEvent) (*ProcessAuthFactorUpdateEventResponse, error)
	// We publish a packet after fetching the AWB number from the bank to register them at vendors end.
	// Consumer to register the card shipments at Vendor.
	// After this step we will start receiving shipment updates for the successfully registered shipments.
	ProcessCardShipmentRegisterEvent(context.Context, *ProcessCardShipmentRegisterEventRequest) (*ProcessCardShipmentRegisterEventResponse, error)
	// ProcessCardDeliveryDelayEvent triggers communication to users in case card is not yet shipped (delivery initiated).
	ProcessCardDeliveryDelayEvent(context.Context, *ProcessCardDeliveryDelayEventRequest) (*ProcessCardDeliveryDelayEventResponse, error)
	// ProcessCardDeliveredEvent consumes event triggered post card is delivered to the user. The event is triggered by the
	// Shipway callback consumer after we receive callback for delivered state.
	// Currently we plan to send periodic communication to users to activate their card via QR code scan post delivery.
	ProcessCardDeliveredEvent(context.Context, *ProcessCardDeliveredEventRequest) (*ProcessCardDeliveredEventResponse, error)
	// FetchTrackingDetails will try to fetch tracking details for a card from bank.
	// As tracking details are not present as soon as card gets created we will publish the packet with a time delay.
	GetTrackingDetails(context.Context, *GetTrackingDetailsRequest) (*GetTrackingDetailsResponse, error)
	// CreatePhysicalCardDispatchAttempt creates dispatch request for a card and publishes a packet which is
	// consumed by ProcessCardDispatch rpc.
	//
	// ProcessCardDispatch processes the card dispatch messages from the queue and progresses the state machine
	// for a dispatch request.
	// If the current state is QUEUED, attempts to INITIATE the call to the issuing bank.
	// If the current state is INITIATE, checks the status at the issuing bank for SUCCESS/FAILURE updates.
	// The above can be retried until the max number of attempts. If max retries are reached,
	// the status is set to MANUAL_INTERVENTION and processing is permanently halted unless we get a manual trigger for.
	ProcessCardDispatch(context.Context, *ProcessCardDispatchRequest) (*ProcessCardDispatchResponse, error)
	// ProcessShippingAddressUpdateAndDispatchPhysicalCard is a consumer rpc which processes the packet published by the InitiateShippingAddressUpdateAndDispatchPhysicalCard rpc.
	// InitiateShippingAddressUpdateAndDispatchPhysicalCard is invoked by client when user requests for a physical card
	// ProcessShippingAddressUpdateAndDispatchPhysicalCard initiates shipping address update and checks the status of the request and triggers
	// CreatePhysicalCardDispatchAttempt rpc to create dispatch request post shipping address update is successful.
	ProcessShippingAddressUpdateAndDispatchPhysicalCard(context.Context, *ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest) (*ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse, error)
	// rpc ProcessCardsDispatchedCsvFile process csv file received from vendor for with details of cards dispatched
	// this rpc is invoked via s3 event which is fired when vendor sends csv file to our sftp
	ProcessCardsDispatchedCsvFile(context.Context, *ProcessCardsDispatchedCsvFileRequest) (*ProcessCardsDispatchedCsvFileResponse, error)
	ProcessCardAmcChargesEligibleUserFile(context.Context, *ProcessCardAmcChargesEligibleUserFileRequest) (*ProcessCardAmcChargesEligibleUserFileResponse, error)
	// This consumer will send a critical in-app notification to the user after 7 days of onboarding to order a physical debit card
	OrderPhysicalCardCriticalNotification(context.Context, *OrderPhysicalCardCriticalNotificationRequest) (*OrderPhysicalCardCriticalNotificationResponse, error)
	// ProcessUserDevicePropertiesUpdateEvent is a consumer rpc which processes the packet published to user device update topic
	// It processes user location update event and checks if the current updated location is India or not,
	// if it's not india (i.e. international location) then publish a rudder event and store the location in transient storage
	ProcessUserDevicePropertiesUpdateEvent(context.Context, *event.UserDevicePropertyUpdateEvent) (*ProcessUserDevicePropertiesUpdateEventResponse, error)
}

// UnimplementedCardConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedCardConsumerServer struct {
}

func (UnimplementedCardConsumerServer) ProcessCardCreation(context.Context, *ProcessCardCreationRequest) (*ProcessCardCreationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardCreation not implemented")
}
func (UnimplementedCardConsumerServer) ProcessCardPiCreation(context.Context, *ProcessCardPiCreationRequest) (*ProcessCardPiCreationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardPiCreation not implemented")
}
func (UnimplementedCardConsumerServer) ProcessCardPinSetEvent(context.Context, *ProcessCardPinSetEventRequest) (*ProcessCardPinSetEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardPinSetEvent not implemented")
}
func (UnimplementedCardConsumerServer) ProcessCardRenewalEvent(context.Context, *ProcessCardRenewalEventRequest) (*ProcessCardRenewalEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardRenewalEvent not implemented")
}
func (UnimplementedCardConsumerServer) ProcessCardOnboardingEvent(context.Context, *onboarding.OnboardingStageUpdate) (*ProcessCardOnboardingEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardOnboardingEvent not implemented")
}
func (UnimplementedCardConsumerServer) ProcessAuthFactorUpdateEvent(context.Context, *notification.AuthFactorUpdateEvent) (*ProcessAuthFactorUpdateEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessAuthFactorUpdateEvent not implemented")
}
func (UnimplementedCardConsumerServer) ProcessCardShipmentRegisterEvent(context.Context, *ProcessCardShipmentRegisterEventRequest) (*ProcessCardShipmentRegisterEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardShipmentRegisterEvent not implemented")
}
func (UnimplementedCardConsumerServer) ProcessCardDeliveryDelayEvent(context.Context, *ProcessCardDeliveryDelayEventRequest) (*ProcessCardDeliveryDelayEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardDeliveryDelayEvent not implemented")
}
func (UnimplementedCardConsumerServer) ProcessCardDeliveredEvent(context.Context, *ProcessCardDeliveredEventRequest) (*ProcessCardDeliveredEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardDeliveredEvent not implemented")
}
func (UnimplementedCardConsumerServer) GetTrackingDetails(context.Context, *GetTrackingDetailsRequest) (*GetTrackingDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTrackingDetails not implemented")
}
func (UnimplementedCardConsumerServer) ProcessCardDispatch(context.Context, *ProcessCardDispatchRequest) (*ProcessCardDispatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardDispatch not implemented")
}
func (UnimplementedCardConsumerServer) ProcessShippingAddressUpdateAndDispatchPhysicalCard(context.Context, *ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest) (*ProcessShippingAddressUpdateAndDispatchPhysicalCardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessShippingAddressUpdateAndDispatchPhysicalCard not implemented")
}
func (UnimplementedCardConsumerServer) ProcessCardsDispatchedCsvFile(context.Context, *ProcessCardsDispatchedCsvFileRequest) (*ProcessCardsDispatchedCsvFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardsDispatchedCsvFile not implemented")
}
func (UnimplementedCardConsumerServer) ProcessCardAmcChargesEligibleUserFile(context.Context, *ProcessCardAmcChargesEligibleUserFileRequest) (*ProcessCardAmcChargesEligibleUserFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardAmcChargesEligibleUserFile not implemented")
}
func (UnimplementedCardConsumerServer) OrderPhysicalCardCriticalNotification(context.Context, *OrderPhysicalCardCriticalNotificationRequest) (*OrderPhysicalCardCriticalNotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderPhysicalCardCriticalNotification not implemented")
}
func (UnimplementedCardConsumerServer) ProcessUserDevicePropertiesUpdateEvent(context.Context, *event.UserDevicePropertyUpdateEvent) (*ProcessUserDevicePropertiesUpdateEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessUserDevicePropertiesUpdateEvent not implemented")
}

// UnsafeCardConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CardConsumerServer will
// result in compilation errors.
type UnsafeCardConsumerServer interface {
	mustEmbedUnimplementedCardConsumerServer()
}

func RegisterCardConsumerServer(s grpc.ServiceRegistrar, srv CardConsumerServer) {
	s.RegisterService(&CardConsumer_ServiceDesc, srv)
}

func _CardConsumer_ProcessCardCreation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCardCreationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).ProcessCardCreation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_ProcessCardCreation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).ProcessCardCreation(ctx, req.(*ProcessCardCreationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_ProcessCardPiCreation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCardPiCreationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).ProcessCardPiCreation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_ProcessCardPiCreation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).ProcessCardPiCreation(ctx, req.(*ProcessCardPiCreationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_ProcessCardPinSetEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCardPinSetEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).ProcessCardPinSetEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_ProcessCardPinSetEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).ProcessCardPinSetEvent(ctx, req.(*ProcessCardPinSetEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_ProcessCardRenewalEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCardRenewalEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).ProcessCardRenewalEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_ProcessCardRenewalEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).ProcessCardRenewalEvent(ctx, req.(*ProcessCardRenewalEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_ProcessCardOnboardingEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(onboarding.OnboardingStageUpdate)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).ProcessCardOnboardingEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_ProcessCardOnboardingEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).ProcessCardOnboardingEvent(ctx, req.(*onboarding.OnboardingStageUpdate))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_ProcessAuthFactorUpdateEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(notification.AuthFactorUpdateEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).ProcessAuthFactorUpdateEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_ProcessAuthFactorUpdateEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).ProcessAuthFactorUpdateEvent(ctx, req.(*notification.AuthFactorUpdateEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_ProcessCardShipmentRegisterEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCardShipmentRegisterEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).ProcessCardShipmentRegisterEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_ProcessCardShipmentRegisterEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).ProcessCardShipmentRegisterEvent(ctx, req.(*ProcessCardShipmentRegisterEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_ProcessCardDeliveryDelayEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCardDeliveryDelayEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).ProcessCardDeliveryDelayEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_ProcessCardDeliveryDelayEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).ProcessCardDeliveryDelayEvent(ctx, req.(*ProcessCardDeliveryDelayEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_ProcessCardDeliveredEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCardDeliveredEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).ProcessCardDeliveredEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_ProcessCardDeliveredEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).ProcessCardDeliveredEvent(ctx, req.(*ProcessCardDeliveredEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_GetTrackingDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTrackingDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).GetTrackingDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_GetTrackingDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).GetTrackingDetails(ctx, req.(*GetTrackingDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_ProcessCardDispatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCardDispatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).ProcessCardDispatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_ProcessCardDispatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).ProcessCardDispatch(ctx, req.(*ProcessCardDispatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_ProcessShippingAddressUpdateAndDispatchPhysicalCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).ProcessShippingAddressUpdateAndDispatchPhysicalCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_ProcessShippingAddressUpdateAndDispatchPhysicalCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).ProcessShippingAddressUpdateAndDispatchPhysicalCard(ctx, req.(*ProcessShippingAddressUpdateAndDispatchPhysicalCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_ProcessCardsDispatchedCsvFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCardsDispatchedCsvFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).ProcessCardsDispatchedCsvFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_ProcessCardsDispatchedCsvFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).ProcessCardsDispatchedCsvFile(ctx, req.(*ProcessCardsDispatchedCsvFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_ProcessCardAmcChargesEligibleUserFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCardAmcChargesEligibleUserFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).ProcessCardAmcChargesEligibleUserFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_ProcessCardAmcChargesEligibleUserFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).ProcessCardAmcChargesEligibleUserFile(ctx, req.(*ProcessCardAmcChargesEligibleUserFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_OrderPhysicalCardCriticalNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderPhysicalCardCriticalNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).OrderPhysicalCardCriticalNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_OrderPhysicalCardCriticalNotification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).OrderPhysicalCardCriticalNotification(ctx, req.(*OrderPhysicalCardCriticalNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardConsumer_ProcessUserDevicePropertiesUpdateEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(event.UserDevicePropertyUpdateEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardConsumerServer).ProcessUserDevicePropertiesUpdateEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardConsumer_ProcessUserDevicePropertiesUpdateEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardConsumerServer).ProcessUserDevicePropertiesUpdateEvent(ctx, req.(*event.UserDevicePropertyUpdateEvent))
	}
	return interceptor(ctx, in, info, handler)
}

// CardConsumer_ServiceDesc is the grpc.ServiceDesc for CardConsumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CardConsumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "card.provisioning.CardConsumer",
	HandlerType: (*CardConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessCardCreation",
			Handler:    _CardConsumer_ProcessCardCreation_Handler,
		},
		{
			MethodName: "ProcessCardPiCreation",
			Handler:    _CardConsumer_ProcessCardPiCreation_Handler,
		},
		{
			MethodName: "ProcessCardPinSetEvent",
			Handler:    _CardConsumer_ProcessCardPinSetEvent_Handler,
		},
		{
			MethodName: "ProcessCardRenewalEvent",
			Handler:    _CardConsumer_ProcessCardRenewalEvent_Handler,
		},
		{
			MethodName: "ProcessCardOnboardingEvent",
			Handler:    _CardConsumer_ProcessCardOnboardingEvent_Handler,
		},
		{
			MethodName: "ProcessAuthFactorUpdateEvent",
			Handler:    _CardConsumer_ProcessAuthFactorUpdateEvent_Handler,
		},
		{
			MethodName: "ProcessCardShipmentRegisterEvent",
			Handler:    _CardConsumer_ProcessCardShipmentRegisterEvent_Handler,
		},
		{
			MethodName: "ProcessCardDeliveryDelayEvent",
			Handler:    _CardConsumer_ProcessCardDeliveryDelayEvent_Handler,
		},
		{
			MethodName: "ProcessCardDeliveredEvent",
			Handler:    _CardConsumer_ProcessCardDeliveredEvent_Handler,
		},
		{
			MethodName: "GetTrackingDetails",
			Handler:    _CardConsumer_GetTrackingDetails_Handler,
		},
		{
			MethodName: "ProcessCardDispatch",
			Handler:    _CardConsumer_ProcessCardDispatch_Handler,
		},
		{
			MethodName: "ProcessShippingAddressUpdateAndDispatchPhysicalCard",
			Handler:    _CardConsumer_ProcessShippingAddressUpdateAndDispatchPhysicalCard_Handler,
		},
		{
			MethodName: "ProcessCardsDispatchedCsvFile",
			Handler:    _CardConsumer_ProcessCardsDispatchedCsvFile_Handler,
		},
		{
			MethodName: "ProcessCardAmcChargesEligibleUserFile",
			Handler:    _CardConsumer_ProcessCardAmcChargesEligibleUserFile_Handler,
		},
		{
			MethodName: "OrderPhysicalCardCriticalNotification",
			Handler:    _CardConsumer_OrderPhysicalCardCriticalNotification_Handler,
		},
		{
			MethodName: "ProcessUserDevicePropertiesUpdateEvent",
			Handler:    _CardConsumer_ProcessUserDevicePropertiesUpdateEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/card/provisioning/card_consumer.proto",
}
