package provisioning

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x CardDeliveryTrackingState) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *CardDeliveryTrackingState) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := CardDeliveryTrackingState_value[val]
	*x = CardDeliveryTrackingState(valInt)
	return nil
}

// Valuer interface implementation for storing the data in string format in DB
func (m *CardQRData) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	return protojson.Marshal(m)
}

// Scanner interface implementation for parsing data while reading from DB
func (m *CardQRData) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, m)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}
