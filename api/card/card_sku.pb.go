// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/card_sku.proto

package card

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CardSKUType represents the card variants offered by us
type CardSKUType int32

const (
	CardSKUType_CARD_SKU_TYPE_UNSPECIFIED CardSKUType = 0
	CardSKUType_CLASSIC                   CardSKUType = 1
)

// Enum value maps for CardSKUType.
var (
	CardSKUType_name = map[int32]string{
		0: "CARD_SKU_TYPE_UNSPECIFIED",
		1: "CLASSIC",
	}
	CardSKUType_value = map[string]int32{
		"CARD_SKU_TYPE_UNSPECIFIED": 0,
		"CLASSIC":                   1,
	}
)

func (x CardSKUType) Enum() *CardSKUType {
	p := new(CardSKUType)
	*p = x
	return p
}

func (x CardSKUType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardSKUType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_sku_proto_enumTypes[0].Descriptor()
}

func (CardSKUType) Type() protoreflect.EnumType {
	return &file_api_card_card_sku_proto_enumTypes[0]
}

func (x CardSKUType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardSKUType.Descriptor instead.
func (CardSKUType) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_sku_proto_rawDescGZIP(), []int{0}
}

// Added model for card SKU which is the epifi representation of card types which can be issued.
// Any card SKU can be supported by one or more vendors
// Against each SKU a number for card related information can be mapped.
// To start with we will have information regarding number of free replacements, and vendor specific information to be used in case of free and chargeable cards.
type CardSKU struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// CardSKUType represents the card variants offered by us
	CardSkuType CardSKUType `protobuf:"varint,1,opt,name=card_sku_type,json=cardSkuType,proto3,enum=card.CardSKUType" json:"card_sku_type,omitempty"`
	// Number of card replacements which will be free for the user.
	FreeCardReplacements int32 `protobuf:"varint,2,opt,name=free_card_replacements,json=freeCardReplacements,proto3" json:"free_card_replacements,omitempty"`
	// Stores vendor specific information for a given CardSKUType.
	VendorCardSku *VendorCardSKU `protobuf:"bytes,3,opt,name=vendor_card_sku,json=vendorCardSku,proto3" json:"vendor_card_sku,omitempty"`
}

func (x *CardSKU) Reset() {
	*x = CardSKU{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_sku_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardSKU) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardSKU) ProtoMessage() {}

func (x *CardSKU) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_sku_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardSKU.ProtoReflect.Descriptor instead.
func (*CardSKU) Descriptor() ([]byte, []int) {
	return file_api_card_card_sku_proto_rawDescGZIP(), []int{0}
}

func (x *CardSKU) GetCardSkuType() CardSKUType {
	if x != nil {
		return x.CardSkuType
	}
	return CardSKUType_CARD_SKU_TYPE_UNSPECIFIED
}

func (x *CardSKU) GetFreeCardReplacements() int32 {
	if x != nil {
		return x.FreeCardReplacements
	}
	return 0
}

func (x *CardSKU) GetVendorCardSku() *VendorCardSKU {
	if x != nil {
		return x.VendorCardSku
	}
	return nil
}

// We can add multiple vendor specific information here for a given CardSKUType.
type VendorCardSKU struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Federal *FederalCardSKU `protobuf:"bytes,1,opt,name=federal,proto3" json:"federal,omitempty"`
}

func (x *VendorCardSKU) Reset() {
	*x = VendorCardSKU{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_sku_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VendorCardSKU) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VendorCardSKU) ProtoMessage() {}

func (x *VendorCardSKU) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_sku_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VendorCardSKU.ProtoReflect.Descriptor instead.
func (*VendorCardSKU) Descriptor() ([]byte, []int) {
	return file_api_card_card_sku_proto_rawDescGZIP(), []int{1}
}

func (x *VendorCardSKU) GetFederal() *FederalCardSKU {
	if x != nil {
		return x.Federal
	}
	return nil
}

// FederalCardSKU stores federal specific data about card variants.
type FederalCardSKU struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FreeReplacementCardSubType string `protobuf:"bytes,1,opt,name=free_replacement_card_sub_type,json=freeReplacementCardSubType,proto3" json:"free_replacement_card_sub_type,omitempty"`
	ChargeableCardSubType      string `protobuf:"bytes,2,opt,name=chargeable_card_sub_type,json=chargeableCardSubType,proto3" json:"chargeable_card_sub_type,omitempty"`
}

func (x *FederalCardSKU) Reset() {
	*x = FederalCardSKU{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_sku_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FederalCardSKU) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FederalCardSKU) ProtoMessage() {}

func (x *FederalCardSKU) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_sku_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FederalCardSKU.ProtoReflect.Descriptor instead.
func (*FederalCardSKU) Descriptor() ([]byte, []int) {
	return file_api_card_card_sku_proto_rawDescGZIP(), []int{2}
}

func (x *FederalCardSKU) GetFreeReplacementCardSubType() string {
	if x != nil {
		return x.FreeReplacementCardSubType
	}
	return ""
}

func (x *FederalCardSKU) GetChargeableCardSubType() string {
	if x != nil {
		return x.ChargeableCardSubType
	}
	return ""
}

var File_api_card_card_sku_proto protoreflect.FileDescriptor

var file_api_card_card_sku_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x73, 0x6b, 0x75, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x63, 0x61, 0x72, 0x64, 0x22,
	0xb3, 0x01, 0x0a, 0x07, 0x43, 0x61, 0x72, 0x64, 0x53, 0x4b, 0x55, 0x12, 0x35, 0x0a, 0x0d, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x73, 0x6b, 0x75, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x53, 0x4b,
	0x55, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x53, 0x6b, 0x75, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x72, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x14, 0x66, 0x72, 0x65, 0x65, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c,
	0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3b, 0x0a, 0x0f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x6b, 0x75, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x43,
	0x61, 0x72, 0x64, 0x53, 0x4b, 0x55, 0x52, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x43, 0x61,
	0x72, 0x64, 0x53, 0x6b, 0x75, 0x22, 0x3f, 0x0a, 0x0d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x43,
	0x61, 0x72, 0x64, 0x53, 0x4b, 0x55, 0x12, 0x2e, 0x0a, 0x07, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x46,
	0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x53, 0x4b, 0x55, 0x52, 0x07, 0x66,
	0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x22, 0x8d, 0x01, 0x0a, 0x0e, 0x46, 0x65, 0x64, 0x65, 0x72,
	0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x53, 0x4b, 0x55, 0x12, 0x42, 0x0a, 0x1e, 0x66, 0x72, 0x65,
	0x65, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x1a, 0x66, 0x72, 0x65, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a,
	0x18, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x15, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x61, 0x72, 0x64, 0x53,
	0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x2a, 0x39, 0x0a, 0x0b, 0x43, 0x61, 0x72, 0x64, 0x53, 0x4b,
	0x55, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x4b,
	0x55, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x49, 0x43, 0x10,
	0x01, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x61, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_card_sku_proto_rawDescOnce sync.Once
	file_api_card_card_sku_proto_rawDescData = file_api_card_card_sku_proto_rawDesc
)

func file_api_card_card_sku_proto_rawDescGZIP() []byte {
	file_api_card_card_sku_proto_rawDescOnce.Do(func() {
		file_api_card_card_sku_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_card_sku_proto_rawDescData)
	})
	return file_api_card_card_sku_proto_rawDescData
}

var file_api_card_card_sku_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_card_card_sku_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_card_card_sku_proto_goTypes = []interface{}{
	(CardSKUType)(0),       // 0: card.CardSKUType
	(*CardSKU)(nil),        // 1: card.CardSKU
	(*VendorCardSKU)(nil),  // 2: card.VendorCardSKU
	(*FederalCardSKU)(nil), // 3: card.FederalCardSKU
}
var file_api_card_card_sku_proto_depIdxs = []int32{
	0, // 0: card.CardSKU.card_sku_type:type_name -> card.CardSKUType
	2, // 1: card.CardSKU.vendor_card_sku:type_name -> card.VendorCardSKU
	3, // 2: card.VendorCardSKU.federal:type_name -> card.FederalCardSKU
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_card_card_sku_proto_init() }
func file_api_card_card_sku_proto_init() {
	if File_api_card_card_sku_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_card_sku_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardSKU); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_card_sku_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VendorCardSKU); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_card_sku_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FederalCardSKU); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_card_sku_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_card_sku_proto_goTypes,
		DependencyIndexes: file_api_card_card_sku_proto_depIdxs,
		EnumInfos:         file_api_card_card_sku_proto_enumTypes,
		MessageInfos:      file_api_card_card_sku_proto_msgTypes,
	}.Build()
	File_api_card_card_sku_proto = out.File
	file_api_card_card_sku_proto_rawDesc = nil
	file_api_card_card_sku_proto_goTypes = nil
	file_api_card_card_sku_proto_depIdxs = nil
}
