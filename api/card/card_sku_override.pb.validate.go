// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/card_sku_override.proto

package card

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CardSKUOverride with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CardSKUOverride) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardSKUOverride with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardSKUOverrideMultiError, or nil if none found.
func (m *CardSKUOverride) ValidateAll() error {
	return m.validate(true)
}

func (m *CardSKUOverride) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for CardSkuType

	// no validation rules for FreeCardReplacements

	// no validation rules for FreeCardReplacementsIssued

	if len(errors) > 0 {
		return CardSKUOverrideMultiError(errors)
	}

	return nil
}

// CardSKUOverrideMultiError is an error wrapping multiple validation errors
// returned by CardSKUOverride.ValidateAll() if the designated constraints
// aren't met.
type CardSKUOverrideMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardSKUOverrideMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardSKUOverrideMultiError) AllErrors() []error { return m }

// CardSKUOverrideValidationError is the validation error returned by
// CardSKUOverride.Validate if the designated constraints aren't met.
type CardSKUOverrideValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardSKUOverrideValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardSKUOverrideValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardSKUOverrideValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardSKUOverrideValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardSKUOverrideValidationError) ErrorName() string { return "CardSKUOverrideValidationError" }

// Error satisfies the builtin error interface
func (e CardSKUOverrideValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardSKUOverride.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardSKUOverrideValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardSKUOverrideValidationError{}
