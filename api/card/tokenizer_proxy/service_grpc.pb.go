// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/card/tokenizer_proxy/service.proto

package tokenizer_proxy

import (
	context "context"
	tokenizer "github.com/epifi/gamma/api/tokenizer"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	TokenizerProxy_GetPaginatedTokens_FullMethodName = "/card.tokenizer_proxy.TokenizerProxy/GetPaginatedTokens"
	TokenizerProxy_ReTokenize_FullMethodName         = "/card.tokenizer_proxy.TokenizerProxy/ReTokenize"
	TokenizerProxy_DeleteToken_FullMethodName        = "/card.tokenizer_proxy.TokenizerProxy/DeleteToken"
	TokenizerProxy_GetTokenInfo_FullMethodName       = "/card.tokenizer_proxy.TokenizerProxy/GetTokenInfo"
)

// TokenizerProxyClient is the client API for TokenizerProxy service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TokenizerProxyClient interface {
	// GetPaginatedTokens returns paginated list of token ids
	GetPaginatedTokens(ctx context.Context, in *tokenizer.GetPaginatedTokensRequest, opts ...grpc.CallOption) (*tokenizer.GetPaginatedTokensResponse, error)
	// ReTokenize re-encrypt the sensitive data strings and returns failed ids with error
	ReTokenize(ctx context.Context, in *tokenizer.ReTokenizeRequest, opts ...grpc.CallOption) (*tokenizer.ReTokenizeResponse, error)
	// DeleteToken deletes data for provided token id
	DeleteToken(ctx context.Context, in *tokenizer.DeleteTokenRequest, opts ...grpc.CallOption) (*tokenizer.DeleteTokenResponse, error)
	// GetTokenInfo will get data from s3 and write the corresponding token to the s3
	// this is for VISA use case only
	GetTokenInfo(ctx context.Context, in *tokenizer.GetTokenInfoRequest, opts ...grpc.CallOption) (*tokenizer.GetTokenInfoResponse, error)
}

type tokenizerProxyClient struct {
	cc grpc.ClientConnInterface
}

func NewTokenizerProxyClient(cc grpc.ClientConnInterface) TokenizerProxyClient {
	return &tokenizerProxyClient{cc}
}

func (c *tokenizerProxyClient) GetPaginatedTokens(ctx context.Context, in *tokenizer.GetPaginatedTokensRequest, opts ...grpc.CallOption) (*tokenizer.GetPaginatedTokensResponse, error) {
	out := new(tokenizer.GetPaginatedTokensResponse)
	err := c.cc.Invoke(ctx, TokenizerProxy_GetPaginatedTokens_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenizerProxyClient) ReTokenize(ctx context.Context, in *tokenizer.ReTokenizeRequest, opts ...grpc.CallOption) (*tokenizer.ReTokenizeResponse, error) {
	out := new(tokenizer.ReTokenizeResponse)
	err := c.cc.Invoke(ctx, TokenizerProxy_ReTokenize_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenizerProxyClient) DeleteToken(ctx context.Context, in *tokenizer.DeleteTokenRequest, opts ...grpc.CallOption) (*tokenizer.DeleteTokenResponse, error) {
	out := new(tokenizer.DeleteTokenResponse)
	err := c.cc.Invoke(ctx, TokenizerProxy_DeleteToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenizerProxyClient) GetTokenInfo(ctx context.Context, in *tokenizer.GetTokenInfoRequest, opts ...grpc.CallOption) (*tokenizer.GetTokenInfoResponse, error) {
	out := new(tokenizer.GetTokenInfoResponse)
	err := c.cc.Invoke(ctx, TokenizerProxy_GetTokenInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TokenizerProxyServer is the server API for TokenizerProxy service.
// All implementations should embed UnimplementedTokenizerProxyServer
// for forward compatibility
type TokenizerProxyServer interface {
	// GetPaginatedTokens returns paginated list of token ids
	GetPaginatedTokens(context.Context, *tokenizer.GetPaginatedTokensRequest) (*tokenizer.GetPaginatedTokensResponse, error)
	// ReTokenize re-encrypt the sensitive data strings and returns failed ids with error
	ReTokenize(context.Context, *tokenizer.ReTokenizeRequest) (*tokenizer.ReTokenizeResponse, error)
	// DeleteToken deletes data for provided token id
	DeleteToken(context.Context, *tokenizer.DeleteTokenRequest) (*tokenizer.DeleteTokenResponse, error)
	// GetTokenInfo will get data from s3 and write the corresponding token to the s3
	// this is for VISA use case only
	GetTokenInfo(context.Context, *tokenizer.GetTokenInfoRequest) (*tokenizer.GetTokenInfoResponse, error)
}

// UnimplementedTokenizerProxyServer should be embedded to have forward compatible implementations.
type UnimplementedTokenizerProxyServer struct {
}

func (UnimplementedTokenizerProxyServer) GetPaginatedTokens(context.Context, *tokenizer.GetPaginatedTokensRequest) (*tokenizer.GetPaginatedTokensResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaginatedTokens not implemented")
}
func (UnimplementedTokenizerProxyServer) ReTokenize(context.Context, *tokenizer.ReTokenizeRequest) (*tokenizer.ReTokenizeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReTokenize not implemented")
}
func (UnimplementedTokenizerProxyServer) DeleteToken(context.Context, *tokenizer.DeleteTokenRequest) (*tokenizer.DeleteTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteToken not implemented")
}
func (UnimplementedTokenizerProxyServer) GetTokenInfo(context.Context, *tokenizer.GetTokenInfoRequest) (*tokenizer.GetTokenInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTokenInfo not implemented")
}

// UnsafeTokenizerProxyServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TokenizerProxyServer will
// result in compilation errors.
type UnsafeTokenizerProxyServer interface {
	mustEmbedUnimplementedTokenizerProxyServer()
}

func RegisterTokenizerProxyServer(s grpc.ServiceRegistrar, srv TokenizerProxyServer) {
	s.RegisterService(&TokenizerProxy_ServiceDesc, srv)
}

func _TokenizerProxy_GetPaginatedTokens_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tokenizer.GetPaginatedTokensRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenizerProxyServer).GetPaginatedTokens(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TokenizerProxy_GetPaginatedTokens_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenizerProxyServer).GetPaginatedTokens(ctx, req.(*tokenizer.GetPaginatedTokensRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TokenizerProxy_ReTokenize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tokenizer.ReTokenizeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenizerProxyServer).ReTokenize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TokenizerProxy_ReTokenize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenizerProxyServer).ReTokenize(ctx, req.(*tokenizer.ReTokenizeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TokenizerProxy_DeleteToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tokenizer.DeleteTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenizerProxyServer).DeleteToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TokenizerProxy_DeleteToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenizerProxyServer).DeleteToken(ctx, req.(*tokenizer.DeleteTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TokenizerProxy_GetTokenInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tokenizer.GetTokenInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenizerProxyServer).GetTokenInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TokenizerProxy_GetTokenInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenizerProxyServer).GetTokenInfo(ctx, req.(*tokenizer.GetTokenInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TokenizerProxy_ServiceDesc is the grpc.ServiceDesc for TokenizerProxy service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TokenizerProxy_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "card.tokenizer_proxy.TokenizerProxy",
	HandlerType: (*TokenizerProxyServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPaginatedTokens",
			Handler:    _TokenizerProxy_GetPaginatedTokens_Handler,
		},
		{
			MethodName: "ReTokenize",
			Handler:    _TokenizerProxy_ReTokenize_Handler,
		},
		{
			MethodName: "DeleteToken",
			Handler:    _TokenizerProxy_DeleteToken_Handler,
		},
		{
			MethodName: "GetTokenInfo",
			Handler:    _TokenizerProxy_GetTokenInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/card/tokenizer_proxy/service.proto",
}
