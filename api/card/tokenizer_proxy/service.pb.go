// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/tokenizer_proxy/service.proto

package tokenizer_proxy

import (
	tokenizer "github.com/epifi/gamma/api/tokenizer"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_api_card_tokenizer_proxy_service_proto protoreflect.FileDescriptor

var file_api_card_tokenizer_proxy_service_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x69, 0x7a, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x1a, 0x1b,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x72, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xe5, 0x02, 0x0a, 0x0e,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x12, 0x63,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x73, 0x12, 0x24, 0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x0a, 0x52, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a,
	0x65, 0x12, 0x1c, 0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1d, 0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x4e, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x1d, 0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e,
	0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x51, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1e, 0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1f, 0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x42, 0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_api_card_tokenizer_proxy_service_proto_goTypes = []interface{}{
	(*tokenizer.GetPaginatedTokensRequest)(nil),  // 0: tokenizer.GetPaginatedTokensRequest
	(*tokenizer.ReTokenizeRequest)(nil),          // 1: tokenizer.ReTokenizeRequest
	(*tokenizer.DeleteTokenRequest)(nil),         // 2: tokenizer.DeleteTokenRequest
	(*tokenizer.GetTokenInfoRequest)(nil),        // 3: tokenizer.GetTokenInfoRequest
	(*tokenizer.GetPaginatedTokensResponse)(nil), // 4: tokenizer.GetPaginatedTokensResponse
	(*tokenizer.ReTokenizeResponse)(nil),         // 5: tokenizer.ReTokenizeResponse
	(*tokenizer.DeleteTokenResponse)(nil),        // 6: tokenizer.DeleteTokenResponse
	(*tokenizer.GetTokenInfoResponse)(nil),       // 7: tokenizer.GetTokenInfoResponse
}
var file_api_card_tokenizer_proxy_service_proto_depIdxs = []int32{
	0, // 0: card.tokenizer_proxy.TokenizerProxy.GetPaginatedTokens:input_type -> tokenizer.GetPaginatedTokensRequest
	1, // 1: card.tokenizer_proxy.TokenizerProxy.ReTokenize:input_type -> tokenizer.ReTokenizeRequest
	2, // 2: card.tokenizer_proxy.TokenizerProxy.DeleteToken:input_type -> tokenizer.DeleteTokenRequest
	3, // 3: card.tokenizer_proxy.TokenizerProxy.GetTokenInfo:input_type -> tokenizer.GetTokenInfoRequest
	4, // 4: card.tokenizer_proxy.TokenizerProxy.GetPaginatedTokens:output_type -> tokenizer.GetPaginatedTokensResponse
	5, // 5: card.tokenizer_proxy.TokenizerProxy.ReTokenize:output_type -> tokenizer.ReTokenizeResponse
	6, // 6: card.tokenizer_proxy.TokenizerProxy.DeleteToken:output_type -> tokenizer.DeleteTokenResponse
	7, // 7: card.tokenizer_proxy.TokenizerProxy.GetTokenInfo:output_type -> tokenizer.GetTokenInfoResponse
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_card_tokenizer_proxy_service_proto_init() }
func file_api_card_tokenizer_proxy_service_proto_init() {
	if File_api_card_tokenizer_proxy_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_tokenizer_proxy_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_card_tokenizer_proxy_service_proto_goTypes,
		DependencyIndexes: file_api_card_tokenizer_proxy_service_proto_depIdxs,
	}.Build()
	File_api_card_tokenizer_proxy_service_proto = out.File
	file_api_card_tokenizer_proxy_service_proto_rawDesc = nil
	file_api_card_tokenizer_proxy_service_proto_goTypes = nil
	file_api_card_tokenizer_proxy_service_proto_depIdxs = nil
}
