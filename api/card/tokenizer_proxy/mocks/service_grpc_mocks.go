// Code generated by MockGen. DO NOT EDIT.
// Source: api/card/tokenizer_proxy/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	tokenizer "github.com/epifi/gamma/api/tokenizer"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockTokenizerProxyClient is a mock of TokenizerProxyClient interface.
type MockTokenizerProxyClient struct {
	ctrl     *gomock.Controller
	recorder *MockTokenizerProxyClientMockRecorder
}

// MockTokenizerProxyClientMockRecorder is the mock recorder for MockTokenizerProxyClient.
type MockTokenizerProxyClientMockRecorder struct {
	mock *MockTokenizerProxyClient
}

// NewMockTokenizerProxyClient creates a new mock instance.
func NewMockTokenizerProxyClient(ctrl *gomock.Controller) *MockTokenizerProxyClient {
	mock := &MockTokenizerProxyClient{ctrl: ctrl}
	mock.recorder = &MockTokenizerProxyClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTokenizerProxyClient) EXPECT() *MockTokenizerProxyClientMockRecorder {
	return m.recorder
}

// DeleteToken mocks base method.
func (m *MockTokenizerProxyClient) DeleteToken(ctx context.Context, in *tokenizer.DeleteTokenRequest, opts ...grpc.CallOption) (*tokenizer.DeleteTokenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteToken", varargs...)
	ret0, _ := ret[0].(*tokenizer.DeleteTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteToken indicates an expected call of DeleteToken.
func (mr *MockTokenizerProxyClientMockRecorder) DeleteToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteToken", reflect.TypeOf((*MockTokenizerProxyClient)(nil).DeleteToken), varargs...)
}

// GetPaginatedTokens mocks base method.
func (m *MockTokenizerProxyClient) GetPaginatedTokens(ctx context.Context, in *tokenizer.GetPaginatedTokensRequest, opts ...grpc.CallOption) (*tokenizer.GetPaginatedTokensResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPaginatedTokens", varargs...)
	ret0, _ := ret[0].(*tokenizer.GetPaginatedTokensResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaginatedTokens indicates an expected call of GetPaginatedTokens.
func (mr *MockTokenizerProxyClientMockRecorder) GetPaginatedTokens(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaginatedTokens", reflect.TypeOf((*MockTokenizerProxyClient)(nil).GetPaginatedTokens), varargs...)
}

// GetTokenInfo mocks base method.
func (m *MockTokenizerProxyClient) GetTokenInfo(ctx context.Context, in *tokenizer.GetTokenInfoRequest, opts ...grpc.CallOption) (*tokenizer.GetTokenInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTokenInfo", varargs...)
	ret0, _ := ret[0].(*tokenizer.GetTokenInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenInfo indicates an expected call of GetTokenInfo.
func (mr *MockTokenizerProxyClientMockRecorder) GetTokenInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenInfo", reflect.TypeOf((*MockTokenizerProxyClient)(nil).GetTokenInfo), varargs...)
}

// ReTokenize mocks base method.
func (m *MockTokenizerProxyClient) ReTokenize(ctx context.Context, in *tokenizer.ReTokenizeRequest, opts ...grpc.CallOption) (*tokenizer.ReTokenizeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReTokenize", varargs...)
	ret0, _ := ret[0].(*tokenizer.ReTokenizeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReTokenize indicates an expected call of ReTokenize.
func (mr *MockTokenizerProxyClientMockRecorder) ReTokenize(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReTokenize", reflect.TypeOf((*MockTokenizerProxyClient)(nil).ReTokenize), varargs...)
}

// MockTokenizerProxyServer is a mock of TokenizerProxyServer interface.
type MockTokenizerProxyServer struct {
	ctrl     *gomock.Controller
	recorder *MockTokenizerProxyServerMockRecorder
}

// MockTokenizerProxyServerMockRecorder is the mock recorder for MockTokenizerProxyServer.
type MockTokenizerProxyServerMockRecorder struct {
	mock *MockTokenizerProxyServer
}

// NewMockTokenizerProxyServer creates a new mock instance.
func NewMockTokenizerProxyServer(ctrl *gomock.Controller) *MockTokenizerProxyServer {
	mock := &MockTokenizerProxyServer{ctrl: ctrl}
	mock.recorder = &MockTokenizerProxyServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTokenizerProxyServer) EXPECT() *MockTokenizerProxyServerMockRecorder {
	return m.recorder
}

// DeleteToken mocks base method.
func (m *MockTokenizerProxyServer) DeleteToken(arg0 context.Context, arg1 *tokenizer.DeleteTokenRequest) (*tokenizer.DeleteTokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteToken", arg0, arg1)
	ret0, _ := ret[0].(*tokenizer.DeleteTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteToken indicates an expected call of DeleteToken.
func (mr *MockTokenizerProxyServerMockRecorder) DeleteToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteToken", reflect.TypeOf((*MockTokenizerProxyServer)(nil).DeleteToken), arg0, arg1)
}

// GetPaginatedTokens mocks base method.
func (m *MockTokenizerProxyServer) GetPaginatedTokens(arg0 context.Context, arg1 *tokenizer.GetPaginatedTokensRequest) (*tokenizer.GetPaginatedTokensResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaginatedTokens", arg0, arg1)
	ret0, _ := ret[0].(*tokenizer.GetPaginatedTokensResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaginatedTokens indicates an expected call of GetPaginatedTokens.
func (mr *MockTokenizerProxyServerMockRecorder) GetPaginatedTokens(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaginatedTokens", reflect.TypeOf((*MockTokenizerProxyServer)(nil).GetPaginatedTokens), arg0, arg1)
}

// GetTokenInfo mocks base method.
func (m *MockTokenizerProxyServer) GetTokenInfo(arg0 context.Context, arg1 *tokenizer.GetTokenInfoRequest) (*tokenizer.GetTokenInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenInfo", arg0, arg1)
	ret0, _ := ret[0].(*tokenizer.GetTokenInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenInfo indicates an expected call of GetTokenInfo.
func (mr *MockTokenizerProxyServerMockRecorder) GetTokenInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenInfo", reflect.TypeOf((*MockTokenizerProxyServer)(nil).GetTokenInfo), arg0, arg1)
}

// ReTokenize mocks base method.
func (m *MockTokenizerProxyServer) ReTokenize(arg0 context.Context, arg1 *tokenizer.ReTokenizeRequest) (*tokenizer.ReTokenizeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReTokenize", arg0, arg1)
	ret0, _ := ret[0].(*tokenizer.ReTokenizeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReTokenize indicates an expected call of ReTokenize.
func (mr *MockTokenizerProxyServerMockRecorder) ReTokenize(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReTokenize", reflect.TypeOf((*MockTokenizerProxyServer)(nil).ReTokenize), arg0, arg1)
}

// MockUnsafeTokenizerProxyServer is a mock of UnsafeTokenizerProxyServer interface.
type MockUnsafeTokenizerProxyServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTokenizerProxyServerMockRecorder
}

// MockUnsafeTokenizerProxyServerMockRecorder is the mock recorder for MockUnsafeTokenizerProxyServer.
type MockUnsafeTokenizerProxyServerMockRecorder struct {
	mock *MockUnsafeTokenizerProxyServer
}

// NewMockUnsafeTokenizerProxyServer creates a new mock instance.
func NewMockUnsafeTokenizerProxyServer(ctrl *gomock.Controller) *MockUnsafeTokenizerProxyServer {
	mock := &MockUnsafeTokenizerProxyServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTokenizerProxyServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTokenizerProxyServer) EXPECT() *MockUnsafeTokenizerProxyServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTokenizerProxyServer mocks base method.
func (m *MockUnsafeTokenizerProxyServer) mustEmbedUnimplementedTokenizerProxyServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTokenizerProxyServer")
}

// mustEmbedUnimplementedTokenizerProxyServer indicates an expected call of mustEmbedUnimplementedTokenizerProxyServer.
func (mr *MockUnsafeTokenizerProxyServerMockRecorder) mustEmbedUnimplementedTokenizerProxyServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTokenizerProxyServer", reflect.TypeOf((*MockUnsafeTokenizerProxyServer)(nil).mustEmbedUnimplementedTokenizerProxyServer))
}
