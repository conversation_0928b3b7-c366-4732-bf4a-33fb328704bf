// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/card/developer/service.proto

package developer

import (
	context "context"
	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CardDbStates_GetEntityList_FullMethodName    = "/card.developer.CardDbStates/GetEntityList"
	CardDbStates_GetParameterList_FullMethodName = "/card.developer.CardDbStates/GetParameterList"
	CardDbStates_GetData_FullMethodName          = "/card.developer.CardDbStates/GetData"
)

// CardDbStatesClient is the client API for CardDbStates service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CardDbStatesClient interface {
	// service to fetch list of entities for which card can return the data
	GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error)
	// For each entity as defined above, the parameter required to fetch that data will be different
	// This service will return appropriate params based on entity passed in request
	GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error)
	// The actual get data API call where we will make a DB call to get the required data
	GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error)
}

type cardDbStatesClient struct {
	cc grpc.ClientConnInterface
}

func NewCardDbStatesClient(cc grpc.ClientConnInterface) CardDbStatesClient {
	return &cardDbStatesClient{cc}
}

func (c *cardDbStatesClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	out := new(db_state.GetEntityListResponse)
	err := c.cc.Invoke(ctx, CardDbStates_GetEntityList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardDbStatesClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	out := new(db_state.GetParameterListResponse)
	err := c.cc.Invoke(ctx, CardDbStates_GetParameterList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardDbStatesClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	out := new(db_state.GetDataResponse)
	err := c.cc.Invoke(ctx, CardDbStates_GetData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CardDbStatesServer is the server API for CardDbStates service.
// All implementations should embed UnimplementedCardDbStatesServer
// for forward compatibility
type CardDbStatesServer interface {
	// service to fetch list of entities for which card can return the data
	GetEntityList(context.Context, *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error)
	// For each entity as defined above, the parameter required to fetch that data will be different
	// This service will return appropriate params based on entity passed in request
	GetParameterList(context.Context, *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error)
	// The actual get data API call where we will make a DB call to get the required data
	GetData(context.Context, *db_state.GetDataRequest) (*db_state.GetDataResponse, error)
}

// UnimplementedCardDbStatesServer should be embedded to have forward compatible implementations.
type UnimplementedCardDbStatesServer struct {
}

func (UnimplementedCardDbStatesServer) GetEntityList(context.Context, *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntityList not implemented")
}
func (UnimplementedCardDbStatesServer) GetParameterList(context.Context, *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetParameterList not implemented")
}
func (UnimplementedCardDbStatesServer) GetData(context.Context, *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetData not implemented")
}

// UnsafeCardDbStatesServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CardDbStatesServer will
// result in compilation errors.
type UnsafeCardDbStatesServer interface {
	mustEmbedUnimplementedCardDbStatesServer()
}

func RegisterCardDbStatesServer(s grpc.ServiceRegistrar, srv CardDbStatesServer) {
	s.RegisterService(&CardDbStates_ServiceDesc, srv)
}

func _CardDbStates_GetEntityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetEntityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardDbStatesServer).GetEntityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardDbStates_GetEntityList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardDbStatesServer).GetEntityList(ctx, req.(*db_state.GetEntityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardDbStates_GetParameterList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetParameterListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardDbStatesServer).GetParameterList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardDbStates_GetParameterList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardDbStatesServer).GetParameterList(ctx, req.(*db_state.GetParameterListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardDbStates_GetData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardDbStatesServer).GetData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardDbStates_GetData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardDbStatesServer).GetData(ctx, req.(*db_state.GetDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CardDbStates_ServiceDesc is the grpc.ServiceDesc for CardDbStates service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CardDbStates_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "card.developer.CardDbStates",
	HandlerType: (*CardDbStatesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEntityList",
			Handler:    _CardDbStates_GetEntityList_Handler,
		},
		{
			MethodName: "GetParameterList",
			Handler:    _CardDbStates_GetParameterList_Handler,
		},
		{
			MethodName: "GetData",
			Handler:    _CardDbStates_GetData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/card/developer/service.proto",
}
