// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/developer/developer.proto

package developer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	card "github.com/epifi/gamma/api/card"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = card.CardState(0)
)

// Validate checks the field values on Card with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Card) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Card with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CardMultiError, or nil if none found.
func (m *Card) ValidateAll() error {
	return m.validate(true)
}

func (m *Card) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for ActorId

	// no validation rules for State

	// no validation rules for BankIdentifier

	if all {
		switch v := interface{}(m.GetControls()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "Controls",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "Controls",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetControls()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardValidationError{
				field:  "Controls",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GroupId

	// no validation rules for SavingsAccountId

	// no validation rules for PreviousCardId

	if all {
		switch v := interface{}(m.GetCardInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "CardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "CardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardValidationError{
				field:  "CardInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPinSetToken()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "PinSetToken",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "PinSetToken",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPinSetToken()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardValidationError{
				field:  "PinSetToken",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsChargeable

	// no validation rules for CardForm

	if len(errors) > 0 {
		return CardMultiError(errors)
	}

	return nil
}

// CardMultiError is an error wrapping multiple validation errors returned by
// Card.ValidateAll() if the designated constraints aren't met.
type CardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardMultiError) AllErrors() []error { return m }

// CardValidationError is the validation error returned by Card.Validate if the
// designated constraints aren't met.
type CardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardValidationError) ErrorName() string { return "CardValidationError" }

// Error satisfies the builtin error interface
func (e CardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardValidationError{}

// Validate checks the field values on DevActionAttempt with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DevActionAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DevActionAttempt with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DevActionAttemptMultiError, or nil if none found.
func (m *DevActionAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *DevActionAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CardId

	// no validation rules for ActorId

	// no validation rules for Action

	// no validation rules for State

	// no validation rules for VendorResponseCode

	// no validation rules for VendorResponseReason

	// no validation rules for InternalResponseCode

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevActionAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevActionAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevActionAttemptValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevActionAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevActionAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevActionAttemptValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DevActionAttemptMultiError(errors)
	}

	return nil
}

// DevActionAttemptMultiError is an error wrapping multiple validation errors
// returned by DevActionAttempt.ValidateAll() if the designated constraints
// aren't met.
type DevActionAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DevActionAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DevActionAttemptMultiError) AllErrors() []error { return m }

// DevActionAttemptValidationError is the validation error returned by
// DevActionAttempt.Validate if the designated constraints aren't met.
type DevActionAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DevActionAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DevActionAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DevActionAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DevActionAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DevActionAttemptValidationError) ErrorName() string { return "DevActionAttemptValidationError" }

// Error satisfies the builtin error interface
func (e DevActionAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDevActionAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DevActionAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DevActionAttemptValidationError{}

// Validate checks the field values on DevCardForActor with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DevCardForActor) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DevCardForActor with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DevCardForActorMultiError, or nil if none found.
func (m *DevCardForActor) ValidateAll() error {
	return m.validate(true)
}

func (m *DevCardForActor) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for ActorId

	// no validation rules for State

	// no validation rules for BankIdentifier

	if all {
		switch v := interface{}(m.GetControls()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevCardForActorValidationError{
					field:  "Controls",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevCardForActorValidationError{
					field:  "Controls",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetControls()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevCardForActorValidationError{
				field:  "Controls",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GroupId

	// no validation rules for SavingsAccountId

	// no validation rules for PreviousCardId

	if all {
		switch v := interface{}(m.GetCardInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevCardForActorValidationError{
					field:  "CardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevCardForActorValidationError{
					field:  "CardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevCardForActorValidationError{
				field:  "CardInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPinSetToken()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevCardForActorValidationError{
					field:  "PinSetToken",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevCardForActorValidationError{
					field:  "PinSetToken",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPinSetToken()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevCardForActorValidationError{
				field:  "PinSetToken",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevCardForActorValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevCardForActorValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevCardForActorValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevCardForActorValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevCardForActorValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevCardForActorValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsChargeable

	// no validation rules for CardForm

	if len(errors) > 0 {
		return DevCardForActorMultiError(errors)
	}

	return nil
}

// DevCardForActorMultiError is an error wrapping multiple validation errors
// returned by DevCardForActor.ValidateAll() if the designated constraints
// aren't met.
type DevCardForActorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DevCardForActorMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DevCardForActorMultiError) AllErrors() []error { return m }

// DevCardForActorValidationError is the validation error returned by
// DevCardForActor.Validate if the designated constraints aren't met.
type DevCardForActorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DevCardForActorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DevCardForActorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DevCardForActorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DevCardForActorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DevCardForActorValidationError) ErrorName() string { return "DevCardForActorValidationError" }

// Error satisfies the builtin error interface
func (e DevCardForActorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDevCardForActor.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DevCardForActorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DevCardForActorValidationError{}

// Validate checks the field values on DevControlData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DevControlData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DevControlData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DevControlDataMultiError,
// or nil if none found.
func (m *DevControlData) ValidateAll() error {
	return m.validate(true)
}

func (m *DevControlData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDefs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DevControlDataValidationError{
						field:  fmt.Sprintf("Defs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DevControlDataValidationError{
						field:  fmt.Sprintf("Defs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DevControlDataValidationError{
					field:  fmt.Sprintf("Defs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TxnStates

	// no validation rules for LocStates

	// no validation rules for OverallState

	if len(errors) > 0 {
		return DevControlDataMultiError(errors)
	}

	return nil
}

// DevControlDataMultiError is an error wrapping multiple validation errors
// returned by DevControlData.ValidateAll() if the designated constraints
// aren't met.
type DevControlDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DevControlDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DevControlDataMultiError) AllErrors() []error { return m }

// DevControlDataValidationError is the validation error returned by
// DevControlData.Validate if the designated constraints aren't met.
type DevControlDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DevControlDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DevControlDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DevControlDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DevControlDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DevControlDataValidationError) ErrorName() string { return "DevControlDataValidationError" }

// Error satisfies the builtin error interface
func (e DevControlDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDevControlData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DevControlDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DevControlDataValidationError{}

// Validate checks the field values on DevCardAuthAttempt with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DevCardAuthAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DevCardAuthAttempt with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DevCardAuthAttemptMultiError, or nil if none found.
func (m *DevCardAuthAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *DevCardAuthAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttemptId

	// no validation rules for CardId

	// no validation rules for CardAction

	// no validation rules for State

	// no validation rules for AuthType

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevCardAuthAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevCardAuthAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevCardAuthAttemptValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevCardAuthAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevCardAuthAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevCardAuthAttemptValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActionState

	if all {
		switch v := interface{}(m.GetActionStageDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevCardAuthAttemptValidationError{
					field:  "ActionStageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevCardAuthAttemptValidationError{
					field:  "ActionStageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionStageDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevCardAuthAttemptValidationError{
				field:  "ActionStageDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DevCardAuthAttemptMultiError(errors)
	}

	return nil
}

// DevCardAuthAttemptMultiError is an error wrapping multiple validation errors
// returned by DevCardAuthAttempt.ValidateAll() if the designated constraints
// aren't met.
type DevCardAuthAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DevCardAuthAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DevCardAuthAttemptMultiError) AllErrors() []error { return m }

// DevCardAuthAttemptValidationError is the validation error returned by
// DevCardAuthAttempt.Validate if the designated constraints aren't met.
type DevCardAuthAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DevCardAuthAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DevCardAuthAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DevCardAuthAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DevCardAuthAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DevCardAuthAttemptValidationError) ErrorName() string {
	return "DevCardAuthAttemptValidationError"
}

// Error satisfies the builtin error interface
func (e DevCardAuthAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDevCardAuthAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DevCardAuthAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DevCardAuthAttemptValidationError{}

// Validate checks the field values on DevActionStageDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DevActionStageDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DevActionStageDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DevActionStageDetailsMultiError, or nil if none found.
func (m *DevActionStageDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *DevActionStageDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetStageMapping()))
		i := 0
		for key := range m.GetStageMapping() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetStageMapping()[key]
			_ = val

			// no validation rules for StageMapping[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, DevActionStageDetailsValidationError{
							field:  fmt.Sprintf("StageMapping[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, DevActionStageDetailsValidationError{
							field:  fmt.Sprintf("StageMapping[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return DevActionStageDetailsValidationError{
						field:  fmt.Sprintf("StageMapping[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return DevActionStageDetailsMultiError(errors)
	}

	return nil
}

// DevActionStageDetailsMultiError is an error wrapping multiple validation
// errors returned by DevActionStageDetails.ValidateAll() if the designated
// constraints aren't met.
type DevActionStageDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DevActionStageDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DevActionStageDetailsMultiError) AllErrors() []error { return m }

// DevActionStageDetailsValidationError is the validation error returned by
// DevActionStageDetails.Validate if the designated constraints aren't met.
type DevActionStageDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DevActionStageDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DevActionStageDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DevActionStageDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DevActionStageDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DevActionStageDetailsValidationError) ErrorName() string {
	return "DevActionStageDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e DevActionStageDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDevActionStageDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DevActionStageDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DevActionStageDetailsValidationError{}

// Validate checks the field values on DevStageInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DevStageInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DevStageInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DevStageInfoMultiError, or
// nil if none found.
func (m *DevStageInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DevStageInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevStageInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevStageInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevStageInfoValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureCode

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return DevStageInfoMultiError(errors)
	}

	return nil
}

// DevStageInfoMultiError is an error wrapping multiple validation errors
// returned by DevStageInfo.ValidateAll() if the designated constraints aren't met.
type DevStageInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DevStageInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DevStageInfoMultiError) AllErrors() []error { return m }

// DevStageInfoValidationError is the validation error returned by
// DevStageInfo.Validate if the designated constraints aren't met.
type DevStageInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DevStageInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DevStageInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DevStageInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DevStageInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DevStageInfoValidationError) ErrorName() string { return "DevStageInfoValidationError" }

// Error satisfies the builtin error interface
func (e DevStageInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDevStageInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DevStageInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DevStageInfoValidationError{}

// Validate checks the field values on DevPhysicalCardDispatchRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DevPhysicalCardDispatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DevPhysicalCardDispatchRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DevPhysicalCardDispatchRequestMultiError, or nil if none found.
func (m *DevPhysicalCardDispatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DevPhysicalCardDispatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CardId

	// no validation rules for State

	// no validation rules for Retries

	// no validation rules for RequestId

	// no validation rules for FailureResponseCode

	// no validation rules for FailureResponseReason

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevPhysicalCardDispatchRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevPhysicalCardDispatchRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevPhysicalCardDispatchRequestValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevPhysicalCardDispatchRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevPhysicalCardDispatchRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevPhysicalCardDispatchRequestValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientReqId

	// no validation rules for FundTransferClientReqId

	if all {
		switch v := interface{}(m.GetChargesReversalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevPhysicalCardDispatchRequestValidationError{
					field:  "ChargesReversalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevPhysicalCardDispatchRequestValidationError{
					field:  "ChargesReversalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChargesReversalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevPhysicalCardDispatchRequestValidationError{
				field:  "ChargesReversalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestType

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevPhysicalCardDispatchRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevPhysicalCardDispatchRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevPhysicalCardDispatchRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetChargesCollectionWithChargesApiInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevPhysicalCardDispatchRequestValidationError{
					field:  "ChargesCollectionWithChargesApiInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevPhysicalCardDispatchRequestValidationError{
					field:  "ChargesCollectionWithChargesApiInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChargesCollectionWithChargesApiInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevPhysicalCardDispatchRequestValidationError{
				field:  "ChargesCollectionWithChargesApiInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetChargesCollectionWithPayInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DevPhysicalCardDispatchRequestValidationError{
					field:  "ChargesCollectionWithPayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DevPhysicalCardDispatchRequestValidationError{
					field:  "ChargesCollectionWithPayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChargesCollectionWithPayInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DevPhysicalCardDispatchRequestValidationError{
				field:  "ChargesCollectionWithPayInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DevPhysicalCardDispatchRequestMultiError(errors)
	}

	return nil
}

// DevPhysicalCardDispatchRequestMultiError is an error wrapping multiple
// validation errors returned by DevPhysicalCardDispatchRequest.ValidateAll()
// if the designated constraints aren't met.
type DevPhysicalCardDispatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DevPhysicalCardDispatchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DevPhysicalCardDispatchRequestMultiError) AllErrors() []error { return m }

// DevPhysicalCardDispatchRequestValidationError is the validation error
// returned by DevPhysicalCardDispatchRequest.Validate if the designated
// constraints aren't met.
type DevPhysicalCardDispatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DevPhysicalCardDispatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DevPhysicalCardDispatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DevPhysicalCardDispatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DevPhysicalCardDispatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DevPhysicalCardDispatchRequestValidationError) ErrorName() string {
	return "DevPhysicalCardDispatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DevPhysicalCardDispatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDevPhysicalCardDispatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DevPhysicalCardDispatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DevPhysicalCardDispatchRequestValidationError{}
