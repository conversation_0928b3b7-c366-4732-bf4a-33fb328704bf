// Code generated by MockGen. DO NOT EDIT.
// Source: api/card/developer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCardDbStatesClient is a mock of CardDbStatesClient interface.
type MockCardDbStatesClient struct {
	ctrl     *gomock.Controller
	recorder *MockCardDbStatesClientMockRecorder
}

// MockCardDbStatesClientMockRecorder is the mock recorder for MockCardDbStatesClient.
type MockCardDbStatesClientMockRecorder struct {
	mock *MockCardDbStatesClient
}

// NewMockCardDbStatesClient creates a new mock instance.
func NewMockCardDbStatesClient(ctrl *gomock.Controller) *MockCardDbStatesClient {
	mock := &MockCardDbStatesClient{ctrl: ctrl}
	mock.recorder = &MockCardDbStatesClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardDbStatesClient) EXPECT() *MockCardDbStatesClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockCardDbStatesClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockCardDbStatesClientMockRecorder) GetData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockCardDbStatesClient)(nil).GetData), varargs...)
}

// GetEntityList mocks base method.
func (m *MockCardDbStatesClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntityList", varargs...)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockCardDbStatesClientMockRecorder) GetEntityList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockCardDbStatesClient)(nil).GetEntityList), varargs...)
}

// GetParameterList mocks base method.
func (m *MockCardDbStatesClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParameterList", varargs...)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockCardDbStatesClientMockRecorder) GetParameterList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockCardDbStatesClient)(nil).GetParameterList), varargs...)
}

// MockCardDbStatesServer is a mock of CardDbStatesServer interface.
type MockCardDbStatesServer struct {
	ctrl     *gomock.Controller
	recorder *MockCardDbStatesServerMockRecorder
}

// MockCardDbStatesServerMockRecorder is the mock recorder for MockCardDbStatesServer.
type MockCardDbStatesServerMockRecorder struct {
	mock *MockCardDbStatesServer
}

// NewMockCardDbStatesServer creates a new mock instance.
func NewMockCardDbStatesServer(ctrl *gomock.Controller) *MockCardDbStatesServer {
	mock := &MockCardDbStatesServer{ctrl: ctrl}
	mock.recorder = &MockCardDbStatesServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardDbStatesServer) EXPECT() *MockCardDbStatesServerMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockCardDbStatesServer) GetData(arg0 context.Context, arg1 *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockCardDbStatesServerMockRecorder) GetData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockCardDbStatesServer)(nil).GetData), arg0, arg1)
}

// GetEntityList mocks base method.
func (m *MockCardDbStatesServer) GetEntityList(arg0 context.Context, arg1 *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockCardDbStatesServerMockRecorder) GetEntityList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockCardDbStatesServer)(nil).GetEntityList), arg0, arg1)
}

// GetParameterList mocks base method.
func (m *MockCardDbStatesServer) GetParameterList(arg0 context.Context, arg1 *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameterList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockCardDbStatesServerMockRecorder) GetParameterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockCardDbStatesServer)(nil).GetParameterList), arg0, arg1)
}

// MockUnsafeCardDbStatesServer is a mock of UnsafeCardDbStatesServer interface.
type MockUnsafeCardDbStatesServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCardDbStatesServerMockRecorder
}

// MockUnsafeCardDbStatesServerMockRecorder is the mock recorder for MockUnsafeCardDbStatesServer.
type MockUnsafeCardDbStatesServerMockRecorder struct {
	mock *MockUnsafeCardDbStatesServer
}

// NewMockUnsafeCardDbStatesServer creates a new mock instance.
func NewMockUnsafeCardDbStatesServer(ctrl *gomock.Controller) *MockUnsafeCardDbStatesServer {
	mock := &MockUnsafeCardDbStatesServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCardDbStatesServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCardDbStatesServer) EXPECT() *MockUnsafeCardDbStatesServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCardDbStatesServer mocks base method.
func (m *MockUnsafeCardDbStatesServer) mustEmbedUnimplementedCardDbStatesServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCardDbStatesServer")
}

// mustEmbedUnimplementedCardDbStatesServer indicates an expected call of mustEmbedUnimplementedCardDbStatesServer.
func (mr *MockUnsafeCardDbStatesServerMockRecorder) mustEmbedUnimplementedCardDbStatesServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCardDbStatesServer", reflect.TypeOf((*MockUnsafeCardDbStatesServer)(nil).mustEmbedUnimplementedCardDbStatesServer))
}
