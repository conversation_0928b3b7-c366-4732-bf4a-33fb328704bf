// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/developer/developer.proto

package developer

import (
	card "github.com/epifi/gamma/api/card"
	provisioning "github.com/epifi/gamma/api/card/provisioning"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CardEntity int32

const (
	CardEntity_CARD_ENTITY_UNSPECIFIED CardEntity = 0
	// To fetch card by card id
	CardEntity_CARD CardEntity = 1
	// To fetch cards related to an actor
	CardEntity_CARDS_FOR_ACTOR CardEntity = 2
	// To fetch card creation details like request id for card creation
	CardEntity_CARD_CREATION_REQUEST CardEntity = 3
	// To fetch card pin set details like if card pin is set or not
	CardEntity_CARD_PIN CardEntity = 4
	// To fetch card limit details
	CardEntity_CARD_LIMIT CardEntity = 5
	// To fetch card delivery details
	CardEntity_CARD_DELIVERY_TRACKING CardEntity = 6
	// Fetch AWB number, carrier partner, delivery scans and other delivery details
	CardEntity_CARD_TRACKING_DETAILS CardEntity = 7
	// To fetch card action attempt details
	CardEntity_CARD_ACTION_ATTEMPT CardEntity = 8
	// To fetch card auth attempt details
	CardEntity_CARD_AUTH_ATTEMPT CardEntity = 9
	// To fetch physical card dispatch requests
	CardEntity_PHYSICAL_CARD_DISPATCH_REQUESTS CardEntity = 10
	// To fetch forex txn refund details
	CardEntity_DC_FOREX_TXN_REFUND CardEntity = 11
	// To fetch card request details
	CardEntity_CARD_REQUEST CardEntity = 12
	// To fetch card notification details
	CardEntity_CARD_NOTIFICATION CardEntity = 13
	// To fetch card details for debit card mandates
	CardEntity_DEBIT_CARD_MANDATE CardEntity = 14
)

// Enum value maps for CardEntity.
var (
	CardEntity_name = map[int32]string{
		0:  "CARD_ENTITY_UNSPECIFIED",
		1:  "CARD",
		2:  "CARDS_FOR_ACTOR",
		3:  "CARD_CREATION_REQUEST",
		4:  "CARD_PIN",
		5:  "CARD_LIMIT",
		6:  "CARD_DELIVERY_TRACKING",
		7:  "CARD_TRACKING_DETAILS",
		8:  "CARD_ACTION_ATTEMPT",
		9:  "CARD_AUTH_ATTEMPT",
		10: "PHYSICAL_CARD_DISPATCH_REQUESTS",
		11: "DC_FOREX_TXN_REFUND",
		12: "CARD_REQUEST",
		13: "CARD_NOTIFICATION",
		14: "DEBIT_CARD_MANDATE",
	}
	CardEntity_value = map[string]int32{
		"CARD_ENTITY_UNSPECIFIED":         0,
		"CARD":                            1,
		"CARDS_FOR_ACTOR":                 2,
		"CARD_CREATION_REQUEST":           3,
		"CARD_PIN":                        4,
		"CARD_LIMIT":                      5,
		"CARD_DELIVERY_TRACKING":          6,
		"CARD_TRACKING_DETAILS":           7,
		"CARD_ACTION_ATTEMPT":             8,
		"CARD_AUTH_ATTEMPT":               9,
		"PHYSICAL_CARD_DISPATCH_REQUESTS": 10,
		"DC_FOREX_TXN_REFUND":             11,
		"CARD_REQUEST":                    12,
		"CARD_NOTIFICATION":               13,
		"DEBIT_CARD_MANDATE":              14,
	}
)

func (x CardEntity) Enum() *CardEntity {
	p := new(CardEntity)
	*p = x
	return p
}

func (x CardEntity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardEntity) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_developer_developer_proto_enumTypes[0].Descriptor()
}

func (CardEntity) Type() protoreflect.EnumType {
	return &file_api_card_developer_developer_proto_enumTypes[0]
}

func (x CardEntity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardEntity.Descriptor instead.
func (CardEntity) EnumDescriptor() ([]byte, []int) {
	return file_api_card_developer_developer_proto_rawDescGZIP(), []int{0}
}

// Fields which needs to be shown in UI for a corresponding card
type Card struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardId           string               `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	ActorId          string               `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	State            card.CardState       `protobuf:"varint,3,opt,name=state,proto3,enum=card.CardState" json:"state,omitempty"`
	BankIdentifier   string               `protobuf:"bytes,4,opt,name=bank_identifier,json=bankIdentifier,proto3" json:"bank_identifier,omitempty"`
	Controls         *card.ControlData    `protobuf:"bytes,5,opt,name=controls,proto3" json:"controls,omitempty"`
	GroupId          string               `protobuf:"bytes,6,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	SavingsAccountId string               `protobuf:"bytes,7,opt,name=savings_account_id,json=savingsAccountId,proto3" json:"savings_account_id,omitempty"`
	PreviousCardId   string               `protobuf:"bytes,8,opt,name=previous_card_id,json=previousCardId,proto3" json:"previous_card_id,omitempty"`
	CardInfo         *card.BasicCardInfo  `protobuf:"bytes,9,opt,name=card_info,json=cardInfo,proto3" json:"card_info,omitempty"`
	PinSetToken      *card.PinSetOtpToken `protobuf:"bytes,10,opt,name=pin_set_token,json=pinSetToken,proto3" json:"pin_set_token,omitempty"`
	// card creation timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// card update timestamp
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// boolean to represent if the current card was free or chargeable
	IsChargeable bool          `protobuf:"varint,13,opt,name=is_chargeable,json=isChargeable,proto3" json:"is_chargeable,omitempty"`
	CardForm     card.CardForm `protobuf:"varint,14,opt,name=card_form,json=cardForm,proto3,enum=card.CardForm" json:"card_form,omitempty"`
}

func (x *Card) Reset() {
	*x = Card{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_developer_developer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Card) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Card) ProtoMessage() {}

func (x *Card) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_developer_developer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Card.ProtoReflect.Descriptor instead.
func (*Card) Descriptor() ([]byte, []int) {
	return file_api_card_developer_developer_proto_rawDescGZIP(), []int{0}
}

func (x *Card) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *Card) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *Card) GetState() card.CardState {
	if x != nil {
		return x.State
	}
	return card.CardState(0)
}

func (x *Card) GetBankIdentifier() string {
	if x != nil {
		return x.BankIdentifier
	}
	return ""
}

func (x *Card) GetControls() *card.ControlData {
	if x != nil {
		return x.Controls
	}
	return nil
}

func (x *Card) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *Card) GetSavingsAccountId() string {
	if x != nil {
		return x.SavingsAccountId
	}
	return ""
}

func (x *Card) GetPreviousCardId() string {
	if x != nil {
		return x.PreviousCardId
	}
	return ""
}

func (x *Card) GetCardInfo() *card.BasicCardInfo {
	if x != nil {
		return x.CardInfo
	}
	return nil
}

func (x *Card) GetPinSetToken() *card.PinSetOtpToken {
	if x != nil {
		return x.PinSetToken
	}
	return nil
}

func (x *Card) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Card) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Card) GetIsChargeable() bool {
	if x != nil {
		return x.IsChargeable
	}
	return false
}

func (x *Card) GetCardForm() card.CardForm {
	if x != nil {
		return x.CardForm
	}
	return card.CardForm(0)
}

type DevActionAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// unique identifier for each card
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// unique identifier for each actor
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Card action such as CVV Enquiry, Enable e-commerce etc
	Action string `protobuf:"bytes,4,opt,name=action,proto3" json:"action,omitempty"`
	// Terminal action state, was the action performed successfully or failed
	State string `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	// Vendor api response code for the given action
	VendorResponseCode string `protobuf:"bytes,6,opt,name=vendor_response_code,json=vendorResponseCode,proto3" json:"vendor_response_code,omitempty"`
	// Vendor api response response for the given action
	VendorResponseReason string `protobuf:"bytes,7,opt,name=vendor_response_reason,json=vendorResponseReason,proto3" json:"vendor_response_reason,omitempty"`
	// Internal response code corresponding to the vendor response code
	InternalResponseCode string                 `protobuf:"bytes,8,opt,name=internal_response_code,json=internalResponseCode,proto3" json:"internal_response_code,omitempty"`
	CreatedAt            *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt            *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *DevActionAttempt) Reset() {
	*x = DevActionAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_developer_developer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevActionAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevActionAttempt) ProtoMessage() {}

func (x *DevActionAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_developer_developer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevActionAttempt.ProtoReflect.Descriptor instead.
func (*DevActionAttempt) Descriptor() ([]byte, []int) {
	return file_api_card_developer_developer_proto_rawDescGZIP(), []int{1}
}

func (x *DevActionAttempt) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DevActionAttempt) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *DevActionAttempt) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DevActionAttempt) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *DevActionAttempt) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *DevActionAttempt) GetVendorResponseCode() string {
	if x != nil {
		return x.VendorResponseCode
	}
	return ""
}

func (x *DevActionAttempt) GetVendorResponseReason() string {
	if x != nil {
		return x.VendorResponseReason
	}
	return ""
}

func (x *DevActionAttempt) GetInternalResponseCode() string {
	if x != nil {
		return x.InternalResponseCode
	}
	return ""
}

func (x *DevActionAttempt) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DevActionAttempt) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type DevCardForActor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardId           string               `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	ActorId          string               `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	State            string               `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	BankIdentifier   string               `protobuf:"bytes,4,opt,name=bank_identifier,json=bankIdentifier,proto3" json:"bank_identifier,omitempty"`
	Controls         *DevControlData      `protobuf:"bytes,5,opt,name=controls,proto3" json:"controls,omitempty"`
	GroupId          string               `protobuf:"bytes,6,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	SavingsAccountId string               `protobuf:"bytes,7,opt,name=savings_account_id,json=savingsAccountId,proto3" json:"savings_account_id,omitempty"`
	PreviousCardId   string               `protobuf:"bytes,8,opt,name=previous_card_id,json=previousCardId,proto3" json:"previous_card_id,omitempty"`
	CardInfo         *card.BasicCardInfo  `protobuf:"bytes,9,opt,name=card_info,json=cardInfo,proto3" json:"card_info,omitempty"`
	PinSetToken      *card.PinSetOtpToken `protobuf:"bytes,10,opt,name=pin_set_token,json=pinSetToken,proto3" json:"pin_set_token,omitempty"`
	// card creation timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// card update timestamp
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// boolean to represent if the current card was free or chargeable
	IsChargeable bool   `protobuf:"varint,13,opt,name=is_chargeable,json=isChargeable,proto3" json:"is_chargeable,omitempty"`
	CardForm     string `protobuf:"bytes,14,opt,name=card_form,json=cardForm,proto3" json:"card_form,omitempty"`
}

func (x *DevCardForActor) Reset() {
	*x = DevCardForActor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_developer_developer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevCardForActor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevCardForActor) ProtoMessage() {}

func (x *DevCardForActor) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_developer_developer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevCardForActor.ProtoReflect.Descriptor instead.
func (*DevCardForActor) Descriptor() ([]byte, []int) {
	return file_api_card_developer_developer_proto_rawDescGZIP(), []int{2}
}

func (x *DevCardForActor) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *DevCardForActor) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DevCardForActor) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *DevCardForActor) GetBankIdentifier() string {
	if x != nil {
		return x.BankIdentifier
	}
	return ""
}

func (x *DevCardForActor) GetControls() *DevControlData {
	if x != nil {
		return x.Controls
	}
	return nil
}

func (x *DevCardForActor) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *DevCardForActor) GetSavingsAccountId() string {
	if x != nil {
		return x.SavingsAccountId
	}
	return ""
}

func (x *DevCardForActor) GetPreviousCardId() string {
	if x != nil {
		return x.PreviousCardId
	}
	return ""
}

func (x *DevCardForActor) GetCardInfo() *card.BasicCardInfo {
	if x != nil {
		return x.CardInfo
	}
	return nil
}

func (x *DevCardForActor) GetPinSetToken() *card.PinSetOtpToken {
	if x != nil {
		return x.PinSetToken
	}
	return nil
}

func (x *DevCardForActor) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DevCardForActor) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *DevCardForActor) GetIsChargeable() bool {
	if x != nil {
		return x.IsChargeable
	}
	return false
}

func (x *DevCardForActor) GetCardForm() string {
	if x != nil {
		return x.CardForm
	}
	return ""
}

type DevControlData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Defs []*card.ControlDefinition `protobuf:"bytes,1,rep,name=defs,proto3" json:"defs,omitempty"`
	// Represents the current action applied to the card transaction type.
	// Represents a map from CardTransactionType (converted to string) to CardControlAction.
	TxnStates map[string]string `protobuf:"bytes,2,rep,name=txn_states,json=txnStates,proto3" json:"txn_states,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Represents the current action applied to the card usage location type.
	// Represents a map from CardUsageLocationType (converted to string) to CardControlAction.
	LocStates map[string]string `protobuf:"bytes,3,rep,name=loc_states,json=locStates,proto3" json:"loc_states,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// the overall state of the card controls
	OverallState string `protobuf:"bytes,4,opt,name=overall_state,json=overallState,proto3" json:"overall_state,omitempty"`
}

func (x *DevControlData) Reset() {
	*x = DevControlData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_developer_developer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevControlData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevControlData) ProtoMessage() {}

func (x *DevControlData) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_developer_developer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevControlData.ProtoReflect.Descriptor instead.
func (*DevControlData) Descriptor() ([]byte, []int) {
	return file_api_card_developer_developer_proto_rawDescGZIP(), []int{3}
}

func (x *DevControlData) GetDefs() []*card.ControlDefinition {
	if x != nil {
		return x.Defs
	}
	return nil
}

func (x *DevControlData) GetTxnStates() map[string]string {
	if x != nil {
		return x.TxnStates
	}
	return nil
}

func (x *DevControlData) GetLocStates() map[string]string {
	if x != nil {
		return x.LocStates
	}
	return nil
}

func (x *DevControlData) GetOverallState() string {
	if x != nil {
		return x.OverallState
	}
	return ""
}

type DevCardAuthAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttemptId  string `protobuf:"bytes,1,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
	CardId     string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	CardAction string `protobuf:"bytes,3,opt,name=card_action,json=cardAction,proto3" json:"card_action,omitempty"`
	State      string `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	AuthType   string `protobuf:"bytes,5,opt,name=auth_type,json=authType,proto3" json:"auth_type,omitempty"`
	// CardAuthAttempt creation timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// CardAuthAttempt updated at timestamp
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Terminal state corresponding to the card action for which auth was initiated
	ActionState string `protobuf:"bytes,8,opt,name=action_state,json=actionState,proto3" json:"action_state,omitempty"`
	// Stage details corresponding to each stage performed to complete an action along with the
	// failure response codes and reason from vendor and the stage updated at timestamp
	ActionStageDetails *DevActionStageDetails `protobuf:"bytes,9,opt,name=action_stage_details,json=actionStageDetails,proto3" json:"action_stage_details,omitempty"`
}

func (x *DevCardAuthAttempt) Reset() {
	*x = DevCardAuthAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_developer_developer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevCardAuthAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevCardAuthAttempt) ProtoMessage() {}

func (x *DevCardAuthAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_developer_developer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevCardAuthAttempt.ProtoReflect.Descriptor instead.
func (*DevCardAuthAttempt) Descriptor() ([]byte, []int) {
	return file_api_card_developer_developer_proto_rawDescGZIP(), []int{4}
}

func (x *DevCardAuthAttempt) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *DevCardAuthAttempt) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *DevCardAuthAttempt) GetCardAction() string {
	if x != nil {
		return x.CardAction
	}
	return ""
}

func (x *DevCardAuthAttempt) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *DevCardAuthAttempt) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

func (x *DevCardAuthAttempt) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DevCardAuthAttempt) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *DevCardAuthAttempt) GetActionState() string {
	if x != nil {
		return x.ActionState
	}
	return ""
}

func (x *DevCardAuthAttempt) GetActionStageDetails() *DevActionStageDetails {
	if x != nil {
		return x.ActionStageDetails
	}
	return nil
}

type DevActionStageDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Details regarding the states of stage are maintained here
	StageMapping map[string]*DevStageInfo `protobuf:"bytes,1,rep,name=stage_mapping,json=stageMapping,proto3" json:"stage_mapping,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DevActionStageDetails) Reset() {
	*x = DevActionStageDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_developer_developer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevActionStageDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevActionStageDetails) ProtoMessage() {}

func (x *DevActionStageDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_developer_developer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevActionStageDetails.ProtoReflect.Descriptor instead.
func (*DevActionStageDetails) Descriptor() ([]byte, []int) {
	return file_api_card_developer_developer_proto_rawDescGZIP(), []int{5}
}

func (x *DevActionStageDetails) GetStageMapping() map[string]*DevStageInfo {
	if x != nil {
		return x.StageMapping
	}
	return nil
}

type DevStageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// State of the stage
	State     string                 `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// If the stage involved vendor interaction then the response code/reason received from vendor in case if
	// failure scenarios.
	FailureCode   string `protobuf:"bytes,3,opt,name=failure_code,json=failureCode,proto3" json:"failure_code,omitempty"`
	FailureReason string `protobuf:"bytes,4,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *DevStageInfo) Reset() {
	*x = DevStageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_developer_developer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevStageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevStageInfo) ProtoMessage() {}

func (x *DevStageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_developer_developer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevStageInfo.ProtoReflect.Descriptor instead.
func (*DevStageInfo) Descriptor() ([]byte, []int) {
	return file_api_card_developer_developer_proto_rawDescGZIP(), []int{6}
}

func (x *DevStageInfo) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *DevStageInfo) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *DevStageInfo) GetFailureCode() string {
	if x != nil {
		return x.FailureCode
	}
	return ""
}

func (x *DevStageInfo) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type DevPhysicalCardDispatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary identifier for physical card dispatch requests.
	// This is an UUID generated by us and used as primary key for the table
	// Any retry-able error is generally associated with
	// same set of request parameters and doesn't change the id.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Primary key to the card database model. Internal to Epifi.
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// The state of the request in the State Machine.
	// A sample state machines:
	// case 1: QUEUED -> INITIATED -> SUCCESS
	// Happy case. Request was successful at the vendor.
	//
	// QUEUED -> MANUAL_INTERVENTION
	// Request was retried multiple times till retry limit was reached.
	// All the retry attempts to initiate the dispatch request failed.
	//
	// QUEUED -> INITIATED -> FAILED
	// Request was initiated successfully but failed due to an error.
	//
	// QUEUED -> INITIATED -> MANUAL_INTERVENTION
	// Request was initiated successfully. All the tries to fetch the status
	// failed.
	State string `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	// Number of retries already made for the request. If the request
	// is in QUEUED state, an attempt to call the Vendor increments the retries.
	//
	// If the request is in INITIATED state, an attempt to fetch the status also
	// increments the retries.
	Retries uint32 `protobuf:"varint,4,opt,name=retries,proto3" json:"retries,omitempty"`
	// Unique identifier to the request made for card dispatch for the vendor.
	// Any followup on state changes is done using
	// this id as a reference. Any retry-able error is generally associated with
	// same set of request parameters and doesn't change the request id.
	RequestId string `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// internal code mapping of the failure response code sent by vendor
	FailureResponseCode string `protobuf:"bytes,6,opt,name=failure_response_code,json=failureResponseCode,proto3" json:"failure_response_code,omitempty"`
	// failure response reason sent by vendor
	FailureResponseReason string                 `protobuf:"bytes,7,opt,name=failure_response_reason,json=failureResponseReason,proto3" json:"failure_response_reason,omitempty"`
	CreatedAt             *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt             *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// client req id associated with physical card dispatch workflow
	ClientReqId string `protobuf:"bytes,10,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// fund transfer client req id associated with the payment flow (if exists)
	FundTransferClientReqId string `protobuf:"bytes,11,opt,name=fund_transfer_client_req_id,json=fundTransferClientReqId,proto3" json:"fund_transfer_client_req_id,omitempty"`
	// details of reward triggered for reversal of physical card charges
	ChargesReversalDetails *provisioning.ChargesReversalRewardDetails `protobuf:"bytes,12,opt,name=charges_reversal_details,json=chargesReversalDetails,proto3" json:"charges_reversal_details,omitempty"`
	// RequestType implies whether a particular physical card dispatch request was placed for the first by an actor,
	RequestType string `protobuf:"bytes,13,opt,name=request_type,json=requestType,proto3" json:"request_type,omitempty"`
	// amount chargeable for the physical card
	Amount                              *money.Money                                                                  `protobuf:"bytes,14,opt,name=amount,proto3" json:"amount,omitempty"`
	ChargesCollectionWithChargesApiInfo *provisioning.PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo `protobuf:"bytes,15,opt,name=charges_collection_with_charges_api_info,json=chargesCollectionWithChargesApiInfo,proto3" json:"charges_collection_with_charges_api_info,omitempty"`
	ChargesCollectionWithPayInfo        *provisioning.PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo        `protobuf:"bytes,16,opt,name=charges_collection_with_pay_info,json=chargesCollectionWithPayInfo,proto3" json:"charges_collection_with_pay_info,omitempty"`
}

func (x *DevPhysicalCardDispatchRequest) Reset() {
	*x = DevPhysicalCardDispatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_developer_developer_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevPhysicalCardDispatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevPhysicalCardDispatchRequest) ProtoMessage() {}

func (x *DevPhysicalCardDispatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_developer_developer_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevPhysicalCardDispatchRequest.ProtoReflect.Descriptor instead.
func (*DevPhysicalCardDispatchRequest) Descriptor() ([]byte, []int) {
	return file_api_card_developer_developer_proto_rawDescGZIP(), []int{7}
}

func (x *DevPhysicalCardDispatchRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DevPhysicalCardDispatchRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *DevPhysicalCardDispatchRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *DevPhysicalCardDispatchRequest) GetRetries() uint32 {
	if x != nil {
		return x.Retries
	}
	return 0
}

func (x *DevPhysicalCardDispatchRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DevPhysicalCardDispatchRequest) GetFailureResponseCode() string {
	if x != nil {
		return x.FailureResponseCode
	}
	return ""
}

func (x *DevPhysicalCardDispatchRequest) GetFailureResponseReason() string {
	if x != nil {
		return x.FailureResponseReason
	}
	return ""
}

func (x *DevPhysicalCardDispatchRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DevPhysicalCardDispatchRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *DevPhysicalCardDispatchRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *DevPhysicalCardDispatchRequest) GetFundTransferClientReqId() string {
	if x != nil {
		return x.FundTransferClientReqId
	}
	return ""
}

func (x *DevPhysicalCardDispatchRequest) GetChargesReversalDetails() *provisioning.ChargesReversalRewardDetails {
	if x != nil {
		return x.ChargesReversalDetails
	}
	return nil
}

func (x *DevPhysicalCardDispatchRequest) GetRequestType() string {
	if x != nil {
		return x.RequestType
	}
	return ""
}

func (x *DevPhysicalCardDispatchRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *DevPhysicalCardDispatchRequest) GetChargesCollectionWithChargesApiInfo() *provisioning.PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo {
	if x != nil {
		return x.ChargesCollectionWithChargesApiInfo
	}
	return nil
}

func (x *DevPhysicalCardDispatchRequest) GetChargesCollectionWithPayInfo() *provisioning.PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo {
	if x != nil {
		return x.ChargesCollectionWithPayInfo
	}
	return nil
}

var File_api_card_developer_developer_proto protoreflect.FileDescriptor

var file_api_card_developer_developer_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x61, 0x72, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x3a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63,
	0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe0, 0x04, 0x0a, 0x04, 0x43, 0x61, 0x72, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x62,
	0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x2c,
	0x0a, 0x12, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x61, 0x76, 0x69,
	0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10,
	0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73,
	0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x63, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a, 0x0d, 0x70, 0x69, 0x6e, 0x5f,
	0x73, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x4f, 0x74, 0x70,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x0b, 0x70, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x69, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2b, 0x0a,
	0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x0e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x46, 0x6f, 0x72, 0x6d,
	0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x22, 0x98, 0x03, 0x0a, 0x10, 0x44,
	0x65, 0x76, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x30, 0x0a, 0x14, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x16, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xd7, 0x04, 0x0a, 0x0f, 0x44, 0x65, 0x76, 0x43, 0x61, 0x72,
	0x64, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x61,
	0x6e, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x08,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e,
	0x44, 0x65, 0x76, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x72, 0x65,
	0x76, 0x69, 0x6f, 0x75, 0x73, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x09, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x43, 0x61, 0x72, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a,
	0x0d, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x50, 0x69, 0x6e, 0x53,
	0x65, 0x74, 0x4f, 0x74, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x0b, 0x70, 0x69, 0x6e, 0x53,
	0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x23, 0x0a,
	0x0d, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x22,
	0xfa, 0x02, 0x0a, 0x0e, 0x44, 0x65, 0x76, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x65, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x64, 0x65, 0x66, 0x73, 0x12,
	0x4c, 0x0a, 0x0a, 0x74, 0x78, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x54, 0x78, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x09, 0x74, 0x78, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x4c, 0x0a,
	0x0a, 0x6c, 0x6f, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x4c, 0x6f, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x09, 0x6c, 0x6f, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x1a, 0x3c, 0x0a, 0x0e, 0x54, 0x78, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3c,
	0x0a, 0x0e, 0x4c, 0x6f, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x92, 0x03, 0x0a,
	0x12, 0x44, 0x65, 0x76, 0x43, 0x61, 0x72, 0x64, 0x41, 0x75, 0x74, 0x68, 0x41, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x57, 0x0a, 0x14, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x12, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x22, 0xd4, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x76, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5c, 0x0a, 0x0d, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4d,
	0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x73, 0x74, 0x61,
	0x67, 0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x1a, 0x5d, 0x0a, 0x11, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x76, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa9, 0x01, 0x0a, 0x0c, 0x44, 0x65, 0x76,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x22, 0xd8, 0x07, 0x0a, 0x1e, 0x44, 0x65, 0x76, 0x50, 0x68, 0x79, 0x73,
	0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x32, 0x0a, 0x15, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13,
	0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x1b, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x66, 0x75, 0x6e, 0x64,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x49, 0x64, 0x12, 0x69, 0x0a, 0x18, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x72,
	0x65, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x73, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x16, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0xa9, 0x01,
	0x0a, 0x28, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x73, 0x5f, 0x61, 0x70, 0x69, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x52, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72,
	0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x41, 0x70, 0x69,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x23, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x41, 0x70, 0x69, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x93, 0x01, 0x0a, 0x20, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61,
	0x6c, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x1c, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x2a,
	0xe7, 0x02, 0x0a, 0x0a, 0x43, 0x61, 0x72, 0x64, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1b,
	0x0a, 0x17, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x43,
	0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x46,
	0x4f, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x49,
	0x4e, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49,
	0x54, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49,
	0x56, 0x45, 0x52, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x06, 0x12,
	0x19, 0x0a, 0x15, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x07, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50,
	0x54, 0x10, 0x08, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x48,
	0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x10, 0x09, 0x12, 0x23, 0x0a, 0x1f, 0x50, 0x48,
	0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50,
	0x41, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x53, 0x10, 0x0a, 0x12,
	0x17, 0x0a, 0x13, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x54, 0x58, 0x4e, 0x5f,
	0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x0b, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x0c, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x0d, 0x12, 0x16, 0x0a, 0x12, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x10, 0x0e, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_developer_developer_proto_rawDescOnce sync.Once
	file_api_card_developer_developer_proto_rawDescData = file_api_card_developer_developer_proto_rawDesc
)

func file_api_card_developer_developer_proto_rawDescGZIP() []byte {
	file_api_card_developer_developer_proto_rawDescOnce.Do(func() {
		file_api_card_developer_developer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_developer_developer_proto_rawDescData)
	})
	return file_api_card_developer_developer_proto_rawDescData
}

var file_api_card_developer_developer_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_card_developer_developer_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_card_developer_developer_proto_goTypes = []interface{}{
	(CardEntity)(0),                        // 0: card.developer.CardEntity
	(*Card)(nil),                           // 1: card.developer.Card
	(*DevActionAttempt)(nil),               // 2: card.developer.DevActionAttempt
	(*DevCardForActor)(nil),                // 3: card.developer.DevCardForActor
	(*DevControlData)(nil),                 // 4: card.developer.DevControlData
	(*DevCardAuthAttempt)(nil),             // 5: card.developer.DevCardAuthAttempt
	(*DevActionStageDetails)(nil),          // 6: card.developer.DevActionStageDetails
	(*DevStageInfo)(nil),                   // 7: card.developer.DevStageInfo
	(*DevPhysicalCardDispatchRequest)(nil), // 8: card.developer.DevPhysicalCardDispatchRequest
	nil,                                    // 9: card.developer.DevControlData.TxnStatesEntry
	nil,                                    // 10: card.developer.DevControlData.LocStatesEntry
	nil,                                    // 11: card.developer.DevActionStageDetails.StageMappingEntry
	(card.CardState)(0),                    // 12: card.CardState
	(*card.ControlData)(nil),               // 13: card.ControlData
	(*card.BasicCardInfo)(nil),             // 14: card.BasicCardInfo
	(*card.PinSetOtpToken)(nil),            // 15: card.PinSetOtpToken
	(*timestamppb.Timestamp)(nil),          // 16: google.protobuf.Timestamp
	(card.CardForm)(0),                     // 17: card.CardForm
	(*card.ControlDefinition)(nil),         // 18: card.ControlDefinition
	(*provisioning.ChargesReversalRewardDetails)(nil), // 19: card.provisioning.ChargesReversalRewardDetails
	(*money.Money)(nil), // 20: google.type.Money
	(*provisioning.PhysicalCardDispatchRequest_ChargesCollectionWithChargesApiInfo)(nil), // 21: card.provisioning.PhysicalCardDispatchRequest.ChargesCollectionWithChargesApiInfo
	(*provisioning.PhysicalCardDispatchRequest_ChargesCollectionWithPayInfo)(nil),        // 22: card.provisioning.PhysicalCardDispatchRequest.ChargesCollectionWithPayInfo
}
var file_api_card_developer_developer_proto_depIdxs = []int32{
	12, // 0: card.developer.Card.state:type_name -> card.CardState
	13, // 1: card.developer.Card.controls:type_name -> card.ControlData
	14, // 2: card.developer.Card.card_info:type_name -> card.BasicCardInfo
	15, // 3: card.developer.Card.pin_set_token:type_name -> card.PinSetOtpToken
	16, // 4: card.developer.Card.created_at:type_name -> google.protobuf.Timestamp
	16, // 5: card.developer.Card.updated_at:type_name -> google.protobuf.Timestamp
	17, // 6: card.developer.Card.card_form:type_name -> card.CardForm
	16, // 7: card.developer.DevActionAttempt.created_at:type_name -> google.protobuf.Timestamp
	16, // 8: card.developer.DevActionAttempt.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 9: card.developer.DevCardForActor.controls:type_name -> card.developer.DevControlData
	14, // 10: card.developer.DevCardForActor.card_info:type_name -> card.BasicCardInfo
	15, // 11: card.developer.DevCardForActor.pin_set_token:type_name -> card.PinSetOtpToken
	16, // 12: card.developer.DevCardForActor.created_at:type_name -> google.protobuf.Timestamp
	16, // 13: card.developer.DevCardForActor.updated_at:type_name -> google.protobuf.Timestamp
	18, // 14: card.developer.DevControlData.defs:type_name -> card.ControlDefinition
	9,  // 15: card.developer.DevControlData.txn_states:type_name -> card.developer.DevControlData.TxnStatesEntry
	10, // 16: card.developer.DevControlData.loc_states:type_name -> card.developer.DevControlData.LocStatesEntry
	16, // 17: card.developer.DevCardAuthAttempt.created_at:type_name -> google.protobuf.Timestamp
	16, // 18: card.developer.DevCardAuthAttempt.updated_at:type_name -> google.protobuf.Timestamp
	6,  // 19: card.developer.DevCardAuthAttempt.action_stage_details:type_name -> card.developer.DevActionStageDetails
	11, // 20: card.developer.DevActionStageDetails.stage_mapping:type_name -> card.developer.DevActionStageDetails.StageMappingEntry
	16, // 21: card.developer.DevStageInfo.updated_at:type_name -> google.protobuf.Timestamp
	16, // 22: card.developer.DevPhysicalCardDispatchRequest.created_at:type_name -> google.protobuf.Timestamp
	16, // 23: card.developer.DevPhysicalCardDispatchRequest.updated_at:type_name -> google.protobuf.Timestamp
	19, // 24: card.developer.DevPhysicalCardDispatchRequest.charges_reversal_details:type_name -> card.provisioning.ChargesReversalRewardDetails
	20, // 25: card.developer.DevPhysicalCardDispatchRequest.amount:type_name -> google.type.Money
	21, // 26: card.developer.DevPhysicalCardDispatchRequest.charges_collection_with_charges_api_info:type_name -> card.provisioning.PhysicalCardDispatchRequest.ChargesCollectionWithChargesApiInfo
	22, // 27: card.developer.DevPhysicalCardDispatchRequest.charges_collection_with_pay_info:type_name -> card.provisioning.PhysicalCardDispatchRequest.ChargesCollectionWithPayInfo
	7,  // 28: card.developer.DevActionStageDetails.StageMappingEntry.value:type_name -> card.developer.DevStageInfo
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_api_card_developer_developer_proto_init() }
func file_api_card_developer_developer_proto_init() {
	if File_api_card_developer_developer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_developer_developer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Card); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_developer_developer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevActionAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_developer_developer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevCardForActor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_developer_developer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevControlData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_developer_developer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevCardAuthAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_developer_developer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevActionStageDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_developer_developer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevStageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_developer_developer_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevPhysicalCardDispatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_developer_developer_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_developer_developer_proto_goTypes,
		DependencyIndexes: file_api_card_developer_developer_proto_depIdxs,
		EnumInfos:         file_api_card_developer_developer_proto_enumTypes,
		MessageInfos:      file_api_card_developer_developer_proto_msgTypes,
	}.Build()
	File_api_card_developer_developer_proto = out.File
	file_api_card_developer_developer_proto_rawDesc = nil
	file_api_card_developer_developer_proto_goTypes = nil
	file_api_card_developer_developer_proto_depIdxs = nil
}
