// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/action_attempt/card_action_attempt.proto

package action_attempt

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Action int32

const (
	Action_ACTION_UNSPECIFIED Action = 0
	// action to fetch Cvv of the card
	Action_CVV_ENQUIRY Action = 1
	// Suspend card temporarily for all transactions. To resume using the card, user must explicitly unsuspend the card.
	Action_SUSPEND Action = 2
	// Unsuspend the card to continue making the transactions.
	Action_UNSUSPEND Action = 3
	// action to enable ecommerce payment
	Action_ENABLE_ECOMM Action = 4
	// action to disable ecommerce payment
	Action_DISABLE_ECOMM Action = 5
	// Enable NFC transactions on the app.
	Action_ENABLE_NFC Action = 6
	// Disable NFC transactions on the app.
	Action_DISABLE_NFC Action = 7
	// action to enable atm withdrawals
	Action_ENABLE_ATM_WITHDRAWALS Action = 8
	// action to disable ATM withdrawals
	Action_DISABLE_ATM_WITHDRAWALS Action = 9
	// action to enable pos payment
	Action_ENABLE_POS Action = 10
	// action to disable pos payment
	Action_DISABLE_POS Action = 11
	// action to enable international usage
	Action_ENABLE_INTERNATIONAL_USAGE Action = 12
	// action to disable international usage
	Action_DISABLE_INTERNATIONAL_USAGE Action = 13
	// Update card limits
	Action_LIMIT_UPDATE Action = 14
	// Physical card activation via QR code scan
	Action_QR_CODE_ACTIVATION Action = 15
	// Block card
	Action_BLOCK_CARD Action = 16
)

// Enum value maps for Action.
var (
	Action_name = map[int32]string{
		0:  "ACTION_UNSPECIFIED",
		1:  "CVV_ENQUIRY",
		2:  "SUSPEND",
		3:  "UNSUSPEND",
		4:  "ENABLE_ECOMM",
		5:  "DISABLE_ECOMM",
		6:  "ENABLE_NFC",
		7:  "DISABLE_NFC",
		8:  "ENABLE_ATM_WITHDRAWALS",
		9:  "DISABLE_ATM_WITHDRAWALS",
		10: "ENABLE_POS",
		11: "DISABLE_POS",
		12: "ENABLE_INTERNATIONAL_USAGE",
		13: "DISABLE_INTERNATIONAL_USAGE",
		14: "LIMIT_UPDATE",
		15: "QR_CODE_ACTIVATION",
		16: "BLOCK_CARD",
	}
	Action_value = map[string]int32{
		"ACTION_UNSPECIFIED":          0,
		"CVV_ENQUIRY":                 1,
		"SUSPEND":                     2,
		"UNSUSPEND":                   3,
		"ENABLE_ECOMM":                4,
		"DISABLE_ECOMM":               5,
		"ENABLE_NFC":                  6,
		"DISABLE_NFC":                 7,
		"ENABLE_ATM_WITHDRAWALS":      8,
		"DISABLE_ATM_WITHDRAWALS":     9,
		"ENABLE_POS":                  10,
		"DISABLE_POS":                 11,
		"ENABLE_INTERNATIONAL_USAGE":  12,
		"DISABLE_INTERNATIONAL_USAGE": 13,
		"LIMIT_UPDATE":                14,
		"QR_CODE_ACTIVATION":          15,
		"BLOCK_CARD":                  16,
	}
)

func (x Action) Enum() *Action {
	p := new(Action)
	*p = x
	return p
}

func (x Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Action) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_action_attempt_card_action_attempt_proto_enumTypes[0].Descriptor()
}

func (Action) Type() protoreflect.EnumType {
	return &file_api_card_action_attempt_card_action_attempt_proto_enumTypes[0]
}

func (x Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Action.Descriptor instead.
func (Action) EnumDescriptor() ([]byte, []int) {
	return file_api_card_action_attempt_card_action_attempt_proto_rawDescGZIP(), []int{0}
}

type CardActionState int32

const (
	CardActionState_CARD_ACTION_STATE_UNSPECIFIED CardActionState = 0
	CardActionState_CARD_ACTION_STATE_SUCCESS     CardActionState = 1
	CardActionState_CARD_ACTION_STATE_FAILURE     CardActionState = 2
)

// Enum value maps for CardActionState.
var (
	CardActionState_name = map[int32]string{
		0: "CARD_ACTION_STATE_UNSPECIFIED",
		1: "CARD_ACTION_STATE_SUCCESS",
		2: "CARD_ACTION_STATE_FAILURE",
	}
	CardActionState_value = map[string]int32{
		"CARD_ACTION_STATE_UNSPECIFIED": 0,
		"CARD_ACTION_STATE_SUCCESS":     1,
		"CARD_ACTION_STATE_FAILURE":     2,
	}
)

func (x CardActionState) Enum() *CardActionState {
	p := new(CardActionState)
	*p = x
	return p
}

func (x CardActionState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardActionState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_action_attempt_card_action_attempt_proto_enumTypes[1].Descriptor()
}

func (CardActionState) Type() protoreflect.EnumType {
	return &file_api_card_action_attempt_card_action_attempt_proto_enumTypes[1]
}

func (x CardActionState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardActionState.Descriptor instead.
func (CardActionState) EnumDescriptor() ([]byte, []int) {
	return file_api_card_action_attempt_card_action_attempt_proto_rawDescGZIP(), []int{1}
}

type ActionAttemptFieldMask int32

const (
	ActionAttemptFieldMask_ACTION_ATTEMPT_FIELD_MASK_UNSPECIFIED ActionAttemptFieldMask = 0
	ActionAttemptFieldMask_ACTION_ATTEMPT_ID                     ActionAttemptFieldMask = 1
	ActionAttemptFieldMask_ACTION_ATTEMPT_CARD_ID                ActionAttemptFieldMask = 2
	ActionAttemptFieldMask_ACTION_ATTEMPT_ACTOR_ID               ActionAttemptFieldMask = 3
	ActionAttemptFieldMask_ACTION_ATTEMPT_ACTION                 ActionAttemptFieldMask = 4
	ActionAttemptFieldMask_ACTION_ATTEMPT_STATE                  ActionAttemptFieldMask = 5
	ActionAttemptFieldMask_ACTION_ATTEMPT_VENDOR_RESPONSE_CODE   ActionAttemptFieldMask = 6
	ActionAttemptFieldMask_ACTION_ATTEMPT_VENDOR_RESPONSE_REASON ActionAttemptFieldMask = 7
	ActionAttemptFieldMask_ACTION_ATTEMPT_INTERNAL_STATUS_CODE   ActionAttemptFieldMask = 8
	ActionAttemptFieldMask_ACTION_ATTEMPT_CREATED_AT             ActionAttemptFieldMask = 9
	ActionAttemptFieldMask_ACTION_ATTEMPT_UPDATED_AT             ActionAttemptFieldMask = 10
	ActionAttemptFieldMask_ACTION_ATTEMPT_DELETED_AT             ActionAttemptFieldMask = 11
)

// Enum value maps for ActionAttemptFieldMask.
var (
	ActionAttemptFieldMask_name = map[int32]string{
		0:  "ACTION_ATTEMPT_FIELD_MASK_UNSPECIFIED",
		1:  "ACTION_ATTEMPT_ID",
		2:  "ACTION_ATTEMPT_CARD_ID",
		3:  "ACTION_ATTEMPT_ACTOR_ID",
		4:  "ACTION_ATTEMPT_ACTION",
		5:  "ACTION_ATTEMPT_STATE",
		6:  "ACTION_ATTEMPT_VENDOR_RESPONSE_CODE",
		7:  "ACTION_ATTEMPT_VENDOR_RESPONSE_REASON",
		8:  "ACTION_ATTEMPT_INTERNAL_STATUS_CODE",
		9:  "ACTION_ATTEMPT_CREATED_AT",
		10: "ACTION_ATTEMPT_UPDATED_AT",
		11: "ACTION_ATTEMPT_DELETED_AT",
	}
	ActionAttemptFieldMask_value = map[string]int32{
		"ACTION_ATTEMPT_FIELD_MASK_UNSPECIFIED": 0,
		"ACTION_ATTEMPT_ID":                     1,
		"ACTION_ATTEMPT_CARD_ID":                2,
		"ACTION_ATTEMPT_ACTOR_ID":               3,
		"ACTION_ATTEMPT_ACTION":                 4,
		"ACTION_ATTEMPT_STATE":                  5,
		"ACTION_ATTEMPT_VENDOR_RESPONSE_CODE":   6,
		"ACTION_ATTEMPT_VENDOR_RESPONSE_REASON": 7,
		"ACTION_ATTEMPT_INTERNAL_STATUS_CODE":   8,
		"ACTION_ATTEMPT_CREATED_AT":             9,
		"ACTION_ATTEMPT_UPDATED_AT":             10,
		"ACTION_ATTEMPT_DELETED_AT":             11,
	}
)

func (x ActionAttemptFieldMask) Enum() *ActionAttemptFieldMask {
	p := new(ActionAttemptFieldMask)
	*p = x
	return p
}

func (x ActionAttemptFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionAttemptFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_action_attempt_card_action_attempt_proto_enumTypes[2].Descriptor()
}

func (ActionAttemptFieldMask) Type() protoreflect.EnumType {
	return &file_api_card_action_attempt_card_action_attempt_proto_enumTypes[2]
}

func (x ActionAttemptFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActionAttemptFieldMask.Descriptor instead.
func (ActionAttemptFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_card_action_attempt_card_action_attempt_proto_rawDescGZIP(), []int{2}
}

type ActionAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// unique identifier for each card
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// unique identifier for each actor
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Card action such as CVV Enquiry, Enable e-commerce etc
	Action Action `protobuf:"varint,4,opt,name=action,proto3,enum=card.action_attempt.Action" json:"action,omitempty"`
	// Terminal action state, was the action performed successfully or failed
	State CardActionState `protobuf:"varint,5,opt,name=state,proto3,enum=card.action_attempt.CardActionState" json:"state,omitempty"`
	// Vendor api response code for the given action
	VendorResponseCode string `protobuf:"bytes,6,opt,name=vendor_response_code,json=vendorResponseCode,proto3" json:"vendor_response_code,omitempty"`
	// Vendor api response response for the given action
	VendorResponseReason string `protobuf:"bytes,7,opt,name=vendor_response_reason,json=vendorResponseReason,proto3" json:"vendor_response_reason,omitempty"`
	// Internal response code corresponding to the vendor response code
	InternalResponseCode string                 `protobuf:"bytes,8,opt,name=internal_response_code,json=internalResponseCode,proto3" json:"internal_response_code,omitempty"`
	CreatedAt            *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt            *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt            *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *ActionAttempt) Reset() {
	*x = ActionAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_action_attempt_card_action_attempt_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActionAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionAttempt) ProtoMessage() {}

func (x *ActionAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_action_attempt_card_action_attempt_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionAttempt.ProtoReflect.Descriptor instead.
func (*ActionAttempt) Descriptor() ([]byte, []int) {
	return file_api_card_action_attempt_card_action_attempt_proto_rawDescGZIP(), []int{0}
}

func (x *ActionAttempt) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ActionAttempt) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *ActionAttempt) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ActionAttempt) GetAction() Action {
	if x != nil {
		return x.Action
	}
	return Action_ACTION_UNSPECIFIED
}

func (x *ActionAttempt) GetState() CardActionState {
	if x != nil {
		return x.State
	}
	return CardActionState_CARD_ACTION_STATE_UNSPECIFIED
}

func (x *ActionAttempt) GetVendorResponseCode() string {
	if x != nil {
		return x.VendorResponseCode
	}
	return ""
}

func (x *ActionAttempt) GetVendorResponseReason() string {
	if x != nil {
		return x.VendorResponseReason
	}
	return ""
}

func (x *ActionAttempt) GetInternalResponseCode() string {
	if x != nil {
		return x.InternalResponseCode
	}
	return ""
}

func (x *ActionAttempt) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ActionAttempt) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *ActionAttempt) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_card_action_attempt_card_action_attempt_proto protoreflect.FileDescriptor

var file_api_card_action_attempt_card_action_attempt_proto_rawDesc = []byte{
	0x0a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x13, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x93, 0x04, 0x0a, 0x0d, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61,
	0x72, 0x64, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x33, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x30, 0x0a, 0x14, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x14, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x16, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a,
	0xe8, 0x02, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x56, 0x56, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49, 0x52,
	0x59, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x10, 0x02,
	0x12, 0x0d, 0x0a, 0x09, 0x55, 0x4e, 0x53, 0x55, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x10, 0x03, 0x12,
	0x10, 0x0a, 0x0c, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x45, 0x43, 0x4f, 0x4d, 0x4d, 0x10,
	0x04, 0x12, 0x11, 0x0a, 0x0d, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x45, 0x43, 0x4f,
	0x4d, 0x4d, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e,
	0x46, 0x43, 0x10, 0x06, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x5f,
	0x4e, 0x46, 0x43, 0x10, 0x07, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f,
	0x41, 0x54, 0x4d, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x4c, 0x53, 0x10,
	0x08, 0x12, 0x1b, 0x0a, 0x17, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x41, 0x54, 0x4d,
	0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x4c, 0x53, 0x10, 0x09, 0x12, 0x0e,
	0x0a, 0x0a, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x0a, 0x12, 0x0f,
	0x0a, 0x0b, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x0b, 0x12,
	0x1e, 0x0a, 0x1a, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x53, 0x41, 0x47, 0x45, 0x10, 0x0c, 0x12,
	0x1f, 0x0a, 0x1b, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x53, 0x41, 0x47, 0x45, 0x10, 0x0d,
	0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x10, 0x0e, 0x12, 0x16, 0x0a, 0x12, 0x51, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0f, 0x12, 0x0e, 0x0a, 0x0a, 0x42, 0x4c,
	0x4f, 0x43, 0x4b, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x10, 0x2a, 0x72, 0x0a, 0x0f, 0x43, 0x61,
	0x72, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a,
	0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1d, 0x0a, 0x19, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12,
	0x1d, 0x0a, 0x19, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x2a, 0xa2,
	0x03, 0x0a, 0x16, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f,
	0x49, 0x44, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12,
	0x18, 0x0a, 0x14, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x05, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44,
	0x4f, 0x52, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x10, 0x06, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54,
	0x45, 0x4d, 0x50, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x53, 0x50,
	0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x27, 0x0a,
	0x23, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x43, 0x4f, 0x44, 0x45, 0x10, 0x08, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44,
	0x5f, 0x41, 0x54, 0x10, 0x09, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f,
	0x41, 0x54, 0x10, 0x0a, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41,
	0x54, 0x10, 0x0b, 0x42, 0x60, 0x0a, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x5a, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_action_attempt_card_action_attempt_proto_rawDescOnce sync.Once
	file_api_card_action_attempt_card_action_attempt_proto_rawDescData = file_api_card_action_attempt_card_action_attempt_proto_rawDesc
)

func file_api_card_action_attempt_card_action_attempt_proto_rawDescGZIP() []byte {
	file_api_card_action_attempt_card_action_attempt_proto_rawDescOnce.Do(func() {
		file_api_card_action_attempt_card_action_attempt_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_action_attempt_card_action_attempt_proto_rawDescData)
	})
	return file_api_card_action_attempt_card_action_attempt_proto_rawDescData
}

var file_api_card_action_attempt_card_action_attempt_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_card_action_attempt_card_action_attempt_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_card_action_attempt_card_action_attempt_proto_goTypes = []interface{}{
	(Action)(0),                   // 0: card.action_attempt.Action
	(CardActionState)(0),          // 1: card.action_attempt.CardActionState
	(ActionAttemptFieldMask)(0),   // 2: card.action_attempt.ActionAttemptFieldMask
	(*ActionAttempt)(nil),         // 3: card.action_attempt.ActionAttempt
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
}
var file_api_card_action_attempt_card_action_attempt_proto_depIdxs = []int32{
	0, // 0: card.action_attempt.ActionAttempt.action:type_name -> card.action_attempt.Action
	1, // 1: card.action_attempt.ActionAttempt.state:type_name -> card.action_attempt.CardActionState
	4, // 2: card.action_attempt.ActionAttempt.created_at:type_name -> google.protobuf.Timestamp
	4, // 3: card.action_attempt.ActionAttempt.updated_at:type_name -> google.protobuf.Timestamp
	4, // 4: card.action_attempt.ActionAttempt.deleted_at:type_name -> google.protobuf.Timestamp
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_card_action_attempt_card_action_attempt_proto_init() }
func file_api_card_action_attempt_card_action_attempt_proto_init() {
	if File_api_card_action_attempt_card_action_attempt_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_action_attempt_card_action_attempt_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActionAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_action_attempt_card_action_attempt_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_action_attempt_card_action_attempt_proto_goTypes,
		DependencyIndexes: file_api_card_action_attempt_card_action_attempt_proto_depIdxs,
		EnumInfos:         file_api_card_action_attempt_card_action_attempt_proto_enumTypes,
		MessageInfos:      file_api_card_action_attempt_card_action_attempt_proto_msgTypes,
	}.Build()
	File_api_card_action_attempt_card_action_attempt_proto = out.File
	file_api_card_action_attempt_card_action_attempt_proto_rawDesc = nil
	file_api_card_action_attempt_card_action_attempt_proto_goTypes = nil
	file_api_card_action_attempt_card_action_attempt_proto_depIdxs = nil
}
