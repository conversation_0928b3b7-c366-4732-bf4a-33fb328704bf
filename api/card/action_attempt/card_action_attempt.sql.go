// nolint:dupl
package action_attempt

import (
	"database/sql/driver"
	"fmt"
)

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x Action) Value() (driver.Value, error) {
	return x.String(), nil
}

// <PERSON>an implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *Action) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := Action_value[val]
	*x = Action(valInt)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x CardActionState) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *CardActionState) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := CardActionState_value[val]
	*x = CardActionState(valInt)
	return nil
}
