// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/card/card_notifications.pb.go

package card

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing DetailedStatus while reading from DB
func (a *DetailedStatus) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return backport.SafeUnmarshal(unmarshalOptions.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the DetailedStatus in string format in DB
func (a *DetailedStatus) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for DetailedStatus
func (a *DetailedStatus) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for DetailedStatus
func (a *DetailedStatus) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing NotificationDetails while reading from DB
func (a *NotificationDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return backport.SafeUnmarshal(unmarshalOptions.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the NotificationDetails in string format in DB
func (a *NotificationDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for NotificationDetails
func (a *NotificationDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for NotificationDetails
func (a *NotificationDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing MerchantDetails while reading from DB
func (a *MerchantDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return backport.SafeUnmarshal(unmarshalOptions.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the MerchantDetails in string format in DB
func (a *MerchantDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for MerchantDetails
func (a *MerchantDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for MerchantDetails
func (a *MerchantDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing AuthSwitchDetails while reading from DB
func (a *AuthSwitchDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return backport.SafeUnmarshal(unmarshalOptions.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the AuthSwitchDetails in string format in DB
func (a *AuthSwitchDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for AuthSwitchDetails
func (a *AuthSwitchDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for AuthSwitchDetails
func (a *AuthSwitchDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing RemitterDetails while reading from DB
func (a *RemitterDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return backport.SafeUnmarshal(unmarshalOptions.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the RemitterDetails in string format in DB
func (a *RemitterDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for RemitterDetails
func (a *RemitterDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for RemitterDetails
func (a *RemitterDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
