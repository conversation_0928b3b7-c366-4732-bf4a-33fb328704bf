// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/control/service.proto

package control

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	card "github.com/epifi/gamma/api/card"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CardControlWorkflow is to mark the type of card control request.
type CardControlWorkflow int32

const (
	CardControlWorkflow_CARD_CTRL_WF_UNSPECIFIED CardControlWorkflow = 0
	// for enabling control via otp auth/credblock
	CardControlWorkflow_CARD_CTRL_SECURE_PIN_WF CardControlWorkflow = 1
	// For enabling control via token. It will be used while onboarding only.
	CardControlWorkflow_CARD_CTRL_TOKEN_WF CardControlWorkflow = 2
)

// Enum value maps for CardControlWorkflow.
var (
	CardControlWorkflow_name = map[int32]string{
		0: "CARD_CTRL_WF_UNSPECIFIED",
		1: "CARD_CTRL_SECURE_PIN_WF",
		2: "CARD_CTRL_TOKEN_WF",
	}
	CardControlWorkflow_value = map[string]int32{
		"CARD_CTRL_WF_UNSPECIFIED": 0,
		"CARD_CTRL_SECURE_PIN_WF":  1,
		"CARD_CTRL_TOKEN_WF":       2,
	}
)

func (x CardControlWorkflow) Enum() *CardControlWorkflow {
	p := new(CardControlWorkflow)
	*p = x
	return p
}

func (x CardControlWorkflow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardControlWorkflow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_service_proto_enumTypes[0].Descriptor()
}

func (CardControlWorkflow) Type() protoreflect.EnumType {
	return &file_api_card_control_service_proto_enumTypes[0]
}

func (x CardControlWorkflow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardControlWorkflow.Descriptor instead.
func (CardControlWorkflow) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{0}
}

type ControlActionWorkflow int32

const (
	ControlActionWorkflow_CONTROL_ACTION_WORKFLOW_UNSPECIFIED ControlActionWorkflow = 0
	ControlActionWorkflow_USER_INITIATED                      ControlActionWorkflow = 1
	ControlActionWorkflow_INTERNAL                            ControlActionWorkflow = 2
)

// Enum value maps for ControlActionWorkflow.
var (
	ControlActionWorkflow_name = map[int32]string{
		0: "CONTROL_ACTION_WORKFLOW_UNSPECIFIED",
		1: "USER_INITIATED",
		2: "INTERNAL",
	}
	ControlActionWorkflow_value = map[string]int32{
		"CONTROL_ACTION_WORKFLOW_UNSPECIFIED": 0,
		"USER_INITIATED":                      1,
		"INTERNAL":                            2,
	}
)

func (x ControlActionWorkflow) Enum() *ControlActionWorkflow {
	p := new(ControlActionWorkflow)
	*p = x
	return p
}

func (x ControlActionWorkflow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ControlActionWorkflow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_service_proto_enumTypes[1].Descriptor()
}

func (ControlActionWorkflow) Type() protoreflect.EnumType {
	return &file_api_card_control_service_proto_enumTypes[1]
}

func (x ControlActionWorkflow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ControlActionWorkflow.Descriptor instead.
func (ControlActionWorkflow) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{1}
}

type SuspendCardResponse_Status int32

const (
	SuspendCardResponse_OK                   SuspendCardResponse_Status = 0
	SuspendCardResponse_TRANSIENT_FAILURE    SuspendCardResponse_Status = 100
	SuspendCardResponse_PERMANENT_FAILURE    SuspendCardResponse_Status = 101
	SuspendCardResponse_PIN_RETRIES_EXCEEDED SuspendCardResponse_Status = 201
	SuspendCardResponse_INVALID_SECURE_PIN   SuspendCardResponse_Status = 202
	// Cred block failures includes all the failures related to cred block such as invalid cred block,
	// deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
	SuspendCardResponse_CRED_BLOCK_FAILURE SuspendCardResponse_Status = 203
)

// Enum value maps for SuspendCardResponse_Status.
var (
	SuspendCardResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "TRANSIENT_FAILURE",
		101: "PERMANENT_FAILURE",
		201: "PIN_RETRIES_EXCEEDED",
		202: "INVALID_SECURE_PIN",
		203: "CRED_BLOCK_FAILURE",
	}
	SuspendCardResponse_Status_value = map[string]int32{
		"OK":                   0,
		"TRANSIENT_FAILURE":    100,
		"PERMANENT_FAILURE":    101,
		"PIN_RETRIES_EXCEEDED": 201,
		"INVALID_SECURE_PIN":   202,
		"CRED_BLOCK_FAILURE":   203,
	}
)

func (x SuspendCardResponse_Status) Enum() *SuspendCardResponse_Status {
	p := new(SuspendCardResponse_Status)
	*p = x
	return p
}

func (x SuspendCardResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SuspendCardResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_service_proto_enumTypes[2].Descriptor()
}

func (SuspendCardResponse_Status) Type() protoreflect.EnumType {
	return &file_api_card_control_service_proto_enumTypes[2]
}

func (x SuspendCardResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SuspendCardResponse_Status.Descriptor instead.
func (SuspendCardResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{11, 0}
}

type LocationOnOffResponse_Status int32

const (
	LocationOnOffResponse_OK                   LocationOnOffResponse_Status = 0
	LocationOnOffResponse_TRANSIENT_FAILURE    LocationOnOffResponse_Status = 100
	LocationOnOffResponse_PERMANENT_FAILURE    LocationOnOffResponse_Status = 101
	LocationOnOffResponse_PIN_RETRIES_EXCEEDED LocationOnOffResponse_Status = 201
	LocationOnOffResponse_INVALID_SECURE_PIN   LocationOnOffResponse_Status = 202
	// Cred block validation failures includes all the failures related to cred block such as invalid cred block,
	// deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
	LocationOnOffResponse_CRED_BLOCK_FAILURE LocationOnOffResponse_Status = 203
)

// Enum value maps for LocationOnOffResponse_Status.
var (
	LocationOnOffResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "TRANSIENT_FAILURE",
		101: "PERMANENT_FAILURE",
		201: "PIN_RETRIES_EXCEEDED",
		202: "INVALID_SECURE_PIN",
		203: "CRED_BLOCK_FAILURE",
	}
	LocationOnOffResponse_Status_value = map[string]int32{
		"OK":                   0,
		"TRANSIENT_FAILURE":    100,
		"PERMANENT_FAILURE":    101,
		"PIN_RETRIES_EXCEEDED": 201,
		"INVALID_SECURE_PIN":   202,
		"CRED_BLOCK_FAILURE":   203,
	}
)

func (x LocationOnOffResponse_Status) Enum() *LocationOnOffResponse_Status {
	p := new(LocationOnOffResponse_Status)
	*p = x
	return p
}

func (x LocationOnOffResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LocationOnOffResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_service_proto_enumTypes[3].Descriptor()
}

func (LocationOnOffResponse_Status) Type() protoreflect.EnumType {
	return &file_api_card_control_service_proto_enumTypes[3]
}

func (x LocationOnOffResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LocationOnOffResponse_Status.Descriptor instead.
func (LocationOnOffResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{14, 0}
}

type ECommerceOnOffResponse_Status int32

const (
	ECommerceOnOffResponse_OK                   ECommerceOnOffResponse_Status = 0
	ECommerceOnOffResponse_TRANSIENT_FAILURE    ECommerceOnOffResponse_Status = 100
	ECommerceOnOffResponse_PERMANENT_FAILURE    ECommerceOnOffResponse_Status = 101
	ECommerceOnOffResponse_PIN_RETRIES_EXCEEDED ECommerceOnOffResponse_Status = 201
	ECommerceOnOffResponse_INVALID_SECURE_PIN   ECommerceOnOffResponse_Status = 202
	// Cred block validation failures includes all the failures related to cred block such as invalid cred block,
	// deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
	ECommerceOnOffResponse_CRED_BLOCK_FAILURE ECommerceOnOffResponse_Status = 203
)

// Enum value maps for ECommerceOnOffResponse_Status.
var (
	ECommerceOnOffResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "TRANSIENT_FAILURE",
		101: "PERMANENT_FAILURE",
		201: "PIN_RETRIES_EXCEEDED",
		202: "INVALID_SECURE_PIN",
		203: "CRED_BLOCK_FAILURE",
	}
	ECommerceOnOffResponse_Status_value = map[string]int32{
		"OK":                   0,
		"TRANSIENT_FAILURE":    100,
		"PERMANENT_FAILURE":    101,
		"PIN_RETRIES_EXCEEDED": 201,
		"INVALID_SECURE_PIN":   202,
		"CRED_BLOCK_FAILURE":   203,
	}
)

func (x ECommerceOnOffResponse_Status) Enum() *ECommerceOnOffResponse_Status {
	p := new(ECommerceOnOffResponse_Status)
	*p = x
	return p
}

func (x ECommerceOnOffResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ECommerceOnOffResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_service_proto_enumTypes[4].Descriptor()
}

func (ECommerceOnOffResponse_Status) Type() protoreflect.EnumType {
	return &file_api_card_control_service_proto_enumTypes[4]
}

func (x ECommerceOnOffResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ECommerceOnOffResponse_Status.Descriptor instead.
func (ECommerceOnOffResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{17, 0}
}

type ATMOnOffResponse_Status int32

const (
	ATMOnOffResponse_OK                   ATMOnOffResponse_Status = 0
	ATMOnOffResponse_TRANSIENT_FAILURE    ATMOnOffResponse_Status = 100
	ATMOnOffResponse_PERMANENT_FAILURE    ATMOnOffResponse_Status = 101
	ATMOnOffResponse_PIN_RETRIES_EXCEEDED ATMOnOffResponse_Status = 201
	ATMOnOffResponse_INVALID_SECURE_PIN   ATMOnOffResponse_Status = 202
	// Cred block validation failures includes all the failures related to cred block such as invalid cred block,
	// deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
	ATMOnOffResponse_CRED_BLOCK_FAILURE ATMOnOffResponse_Status = 203
)

// Enum value maps for ATMOnOffResponse_Status.
var (
	ATMOnOffResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "TRANSIENT_FAILURE",
		101: "PERMANENT_FAILURE",
		201: "PIN_RETRIES_EXCEEDED",
		202: "INVALID_SECURE_PIN",
		203: "CRED_BLOCK_FAILURE",
	}
	ATMOnOffResponse_Status_value = map[string]int32{
		"OK":                   0,
		"TRANSIENT_FAILURE":    100,
		"PERMANENT_FAILURE":    101,
		"PIN_RETRIES_EXCEEDED": 201,
		"INVALID_SECURE_PIN":   202,
		"CRED_BLOCK_FAILURE":   203,
	}
)

func (x ATMOnOffResponse_Status) Enum() *ATMOnOffResponse_Status {
	p := new(ATMOnOffResponse_Status)
	*p = x
	return p
}

func (x ATMOnOffResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ATMOnOffResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_service_proto_enumTypes[5].Descriptor()
}

func (ATMOnOffResponse_Status) Type() protoreflect.EnumType {
	return &file_api_card_control_service_proto_enumTypes[5]
}

func (x ATMOnOffResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ATMOnOffResponse_Status.Descriptor instead.
func (ATMOnOffResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{19, 0}
}

type POSOnOffResponse_Status int32

const (
	POSOnOffResponse_OK                   POSOnOffResponse_Status = 0
	POSOnOffResponse_TRANSIENT_FAILURE    POSOnOffResponse_Status = 100
	POSOnOffResponse_PERMANENT_FAILURE    POSOnOffResponse_Status = 101
	POSOnOffResponse_PIN_RETRIES_EXCEEDED POSOnOffResponse_Status = 201
	POSOnOffResponse_INVALID_SECURE_PIN   POSOnOffResponse_Status = 202
	// Cred block validation failures includes all the failures related to cred block such as invalid cred block,
	// deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
	POSOnOffResponse_CRED_BLOCK_FAILURE POSOnOffResponse_Status = 203
)

// Enum value maps for POSOnOffResponse_Status.
var (
	POSOnOffResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "TRANSIENT_FAILURE",
		101: "PERMANENT_FAILURE",
		201: "PIN_RETRIES_EXCEEDED",
		202: "INVALID_SECURE_PIN",
		203: "CRED_BLOCK_FAILURE",
	}
	POSOnOffResponse_Status_value = map[string]int32{
		"OK":                   0,
		"TRANSIENT_FAILURE":    100,
		"PERMANENT_FAILURE":    101,
		"PIN_RETRIES_EXCEEDED": 201,
		"INVALID_SECURE_PIN":   202,
		"CRED_BLOCK_FAILURE":   203,
	}
)

func (x POSOnOffResponse_Status) Enum() *POSOnOffResponse_Status {
	p := new(POSOnOffResponse_Status)
	*p = x
	return p
}

func (x POSOnOffResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (POSOnOffResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_service_proto_enumTypes[6].Descriptor()
}

func (POSOnOffResponse_Status) Type() protoreflect.EnumType {
	return &file_api_card_control_service_proto_enumTypes[6]
}

func (x POSOnOffResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use POSOnOffResponse_Status.Descriptor instead.
func (POSOnOffResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{22, 0}
}

type NfcOnOffResponse_Status int32

const (
	NfcOnOffResponse_OK                   NfcOnOffResponse_Status = 0
	NfcOnOffResponse_TRANSIENT_FAILURE    NfcOnOffResponse_Status = 100
	NfcOnOffResponse_PERMANENT_FAILURE    NfcOnOffResponse_Status = 101
	NfcOnOffResponse_PIN_RETRIES_EXCEEDED NfcOnOffResponse_Status = 201
	NfcOnOffResponse_INVALID_SECURE_PIN   NfcOnOffResponse_Status = 202
	// Cred block validation failures includes all the failures related to cred block such as invalid cred block,
	// deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
	NfcOnOffResponse_CRED_BLOCK_FAILURE NfcOnOffResponse_Status = 203
)

// Enum value maps for NfcOnOffResponse_Status.
var (
	NfcOnOffResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "TRANSIENT_FAILURE",
		101: "PERMANENT_FAILURE",
		201: "PIN_RETRIES_EXCEEDED",
		202: "INVALID_SECURE_PIN",
		203: "CRED_BLOCK_FAILURE",
	}
	NfcOnOffResponse_Status_value = map[string]int32{
		"OK":                   0,
		"TRANSIENT_FAILURE":    100,
		"PERMANENT_FAILURE":    101,
		"PIN_RETRIES_EXCEEDED": 201,
		"INVALID_SECURE_PIN":   202,
		"CRED_BLOCK_FAILURE":   203,
	}
)

func (x NfcOnOffResponse_Status) Enum() *NfcOnOffResponse_Status {
	p := new(NfcOnOffResponse_Status)
	*p = x
	return p
}

func (x NfcOnOffResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NfcOnOffResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_service_proto_enumTypes[7].Descriptor()
}

func (NfcOnOffResponse_Status) Type() protoreflect.EnumType {
	return &file_api_card_control_service_proto_enumTypes[7]
}

func (x NfcOnOffResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NfcOnOffResponse_Status.Descriptor instead.
func (NfcOnOffResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{26, 0}
}

// List of status codes returned
type GetCardLimitsResponse_Status int32

const (
	// Returned an success
	GetCardLimitsResponse_OK GetCardLimitsResponse_Status = 0
	// Requested entity i.e., card-id or controls not found
	// One of the reasons could be card doesn't exist.
	GetCardLimitsResponse_NOT_FOUND GetCardLimitsResponse_Status = 5
	// System faced internal errors while processing the request
	GetCardLimitsResponse_INTERNAL GetCardLimitsResponse_Status = 13
)

// Enum value maps for GetCardLimitsResponse_Status.
var (
	GetCardLimitsResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetCardLimitsResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetCardLimitsResponse_Status) Enum() *GetCardLimitsResponse_Status {
	p := new(GetCardLimitsResponse_Status)
	*p = x
	return p
}

func (x GetCardLimitsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetCardLimitsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_service_proto_enumTypes[8].Descriptor()
}

func (GetCardLimitsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_card_control_service_proto_enumTypes[8]
}

func (x GetCardLimitsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetCardLimitsResponse_Status.Descriptor instead.
func (GetCardLimitsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{31, 0}
}

// List of status codes returned
type UpdateCardLimitsResponse_Status int32

const (
	// Returned an success
	UpdateCardLimitsResponse_OK UpdateCardLimitsResponse_Status = 0
	// Requested entity i.e., card-id or controls not found
	// One of the reasons could be card doesn't exist.
	UpdateCardLimitsResponse_NOT_FOUND UpdateCardLimitsResponse_Status = 5
	// System faced internal errors while processing the request
	UpdateCardLimitsResponse_INTERNAL UpdateCardLimitsResponse_Status = 13
	// pre condition to mark a card is not in state to make update card limits.
	UpdateCardLimitsResponse_FAILED_PRECONDITION  UpdateCardLimitsResponse_Status = 9
	UpdateCardLimitsResponse_TRANSIENT_FAILURE    UpdateCardLimitsResponse_Status = 100
	UpdateCardLimitsResponse_PERMANENT_FAILURE    UpdateCardLimitsResponse_Status = 101
	UpdateCardLimitsResponse_PIN_RETRIES_EXCEEDED UpdateCardLimitsResponse_Status = 201
	UpdateCardLimitsResponse_INVALID_SECURE_PIN   UpdateCardLimitsResponse_Status = 202
	// Cred block validation failures includes all the failures related to cred block such as invalid cred block,
	// deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
	UpdateCardLimitsResponse_CRED_BLOCK_FAILURE UpdateCardLimitsResponse_Status = 203
)

// Enum value maps for UpdateCardLimitsResponse_Status.
var (
	UpdateCardLimitsResponse_Status_name = map[int32]string{
		0:   "OK",
		5:   "NOT_FOUND",
		13:  "INTERNAL",
		9:   "FAILED_PRECONDITION",
		100: "TRANSIENT_FAILURE",
		101: "PERMANENT_FAILURE",
		201: "PIN_RETRIES_EXCEEDED",
		202: "INVALID_SECURE_PIN",
		203: "CRED_BLOCK_FAILURE",
	}
	UpdateCardLimitsResponse_Status_value = map[string]int32{
		"OK":                   0,
		"NOT_FOUND":            5,
		"INTERNAL":             13,
		"FAILED_PRECONDITION":  9,
		"TRANSIENT_FAILURE":    100,
		"PERMANENT_FAILURE":    101,
		"PIN_RETRIES_EXCEEDED": 201,
		"INVALID_SECURE_PIN":   202,
		"CRED_BLOCK_FAILURE":   203,
	}
)

func (x UpdateCardLimitsResponse_Status) Enum() *UpdateCardLimitsResponse_Status {
	p := new(UpdateCardLimitsResponse_Status)
	*p = x
	return p
}

func (x UpdateCardLimitsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateCardLimitsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_service_proto_enumTypes[9].Descriptor()
}

func (UpdateCardLimitsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_card_control_service_proto_enumTypes[9]
}

func (x UpdateCardLimitsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateCardLimitsResponse_Status.Descriptor instead.
func (UpdateCardLimitsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{33, 0}
}

type FetchCardLimitsResponse_Status int32

const (
	FetchCardLimitsResponse_OK FetchCardLimitsResponse_Status = 0
)

// Enum value maps for FetchCardLimitsResponse_Status.
var (
	FetchCardLimitsResponse_Status_name = map[int32]string{
		0: "OK",
	}
	FetchCardLimitsResponse_Status_value = map[string]int32{
		"OK": 0,
	}
)

func (x FetchCardLimitsResponse_Status) Enum() *FetchCardLimitsResponse_Status {
	p := new(FetchCardLimitsResponse_Status)
	*p = x
	return p
}

func (x FetchCardLimitsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FetchCardLimitsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_service_proto_enumTypes[10].Descriptor()
}

func (FetchCardLimitsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_card_control_service_proto_enumTypes[10]
}

func (x FetchCardLimitsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FetchCardLimitsResponse_Status.Descriptor instead.
func (FetchCardLimitsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{36, 0}
}

type ConsolidatedCardControlOnOffResponse_Status int32

const (
	ConsolidatedCardControlOnOffResponse_OK        ConsolidatedCardControlOnOffResponse_Status = 0
	ConsolidatedCardControlOnOffResponse_NOT_FOUND ConsolidatedCardControlOnOffResponse_Status = 5
	ConsolidatedCardControlOnOffResponse_INTERNAL  ConsolidatedCardControlOnOffResponse_Status = 13
	// user has entered incorrect pin more than x times
	ConsolidatedCardControlOnOffResponse_PIN_RETRIES_EXCEEDED ConsolidatedCardControlOnOffResponse_Status = 201
	// invalid secure(upi) pin entered by user
	ConsolidatedCardControlOnOffResponse_INVALID_SECURE_PIN ConsolidatedCardControlOnOffResponse_Status = 202
)

// Enum value maps for ConsolidatedCardControlOnOffResponse_Status.
var (
	ConsolidatedCardControlOnOffResponse_Status_name = map[int32]string{
		0:   "OK",
		5:   "NOT_FOUND",
		13:  "INTERNAL",
		201: "PIN_RETRIES_EXCEEDED",
		202: "INVALID_SECURE_PIN",
	}
	ConsolidatedCardControlOnOffResponse_Status_value = map[string]int32{
		"OK":                   0,
		"NOT_FOUND":            5,
		"INTERNAL":             13,
		"PIN_RETRIES_EXCEEDED": 201,
		"INVALID_SECURE_PIN":   202,
	}
)

func (x ConsolidatedCardControlOnOffResponse_Status) Enum() *ConsolidatedCardControlOnOffResponse_Status {
	p := new(ConsolidatedCardControlOnOffResponse_Status)
	*p = x
	return p
}

func (x ConsolidatedCardControlOnOffResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConsolidatedCardControlOnOffResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_service_proto_enumTypes[11].Descriptor()
}

func (ConsolidatedCardControlOnOffResponse_Status) Type() protoreflect.EnumType {
	return &file_api_card_control_service_proto_enumTypes[11]
}

func (x ConsolidatedCardControlOnOffResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConsolidatedCardControlOnOffResponse_Status.Descriptor instead.
func (ConsolidatedCardControlOnOffResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{38, 0}
}

type SetTravelModeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId          string                `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	EnableTravelMode bool                  `protobuf:"varint,2,opt,name=enable_travel_mode,json=enableTravelMode,proto3" json:"enable_travel_mode,omitempty"`
	WorkflowType     ControlActionWorkflow `protobuf:"varint,3,opt,name=workflow_type,json=workflowType,proto3,enum=card.control.ControlActionWorkflow" json:"workflow_type,omitempty"`
}

func (x *SetTravelModeRequest) Reset() {
	*x = SetTravelModeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTravelModeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTravelModeRequest) ProtoMessage() {}

func (x *SetTravelModeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTravelModeRequest.ProtoReflect.Descriptor instead.
func (*SetTravelModeRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{0}
}

func (x *SetTravelModeRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *SetTravelModeRequest) GetEnableTravelMode() bool {
	if x != nil {
		return x.EnableTravelMode
	}
	return false
}

func (x *SetTravelModeRequest) GetWorkflowType() ControlActionWorkflow {
	if x != nil {
		return x.WorkflowType
	}
	return ControlActionWorkflow_CONTROL_ACTION_WORKFLOW_UNSPECIFIED
}

type SetTravelModeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	IsTravelModeOn bool        `protobuf:"varint,2,opt,name=is_travel_mode_on,json=isTravelModeOn,proto3" json:"is_travel_mode_on,omitempty"`
}

func (x *SetTravelModeResponse) Reset() {
	*x = SetTravelModeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTravelModeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTravelModeResponse) ProtoMessage() {}

func (x *SetTravelModeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTravelModeResponse.ProtoReflect.Descriptor instead.
func (*SetTravelModeResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{1}
}

func (x *SetTravelModeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *SetTravelModeResponse) GetIsTravelModeOn() bool {
	if x != nil {
		return x.IsTravelModeOn
	}
	return false
}

type GetTravelModeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetTravelModeRequest) Reset() {
	*x = GetTravelModeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTravelModeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTravelModeRequest) ProtoMessage() {}

func (x *GetTravelModeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTravelModeRequest.ProtoReflect.Descriptor instead.
func (*GetTravelModeRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetTravelModeRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetTravelModeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	IsTravelModeOn bool        `protobuf:"varint,2,opt,name=is_travel_mode_on,json=isTravelModeOn,proto3" json:"is_travel_mode_on,omitempty"`
	// this identifies the workflow via which travel mode was enabled
	// will be populated only if the travel is active
	EnableWorkflow ControlActionWorkflow `protobuf:"varint,3,opt,name=enable_workflow,json=enableWorkflow,proto3,enum=card.control.ControlActionWorkflow" json:"enable_workflow,omitempty"`
}

func (x *GetTravelModeResponse) Reset() {
	*x = GetTravelModeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTravelModeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTravelModeResponse) ProtoMessage() {}

func (x *GetTravelModeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTravelModeResponse.ProtoReflect.Descriptor instead.
func (*GetTravelModeResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetTravelModeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTravelModeResponse) GetIsTravelModeOn() bool {
	if x != nil {
		return x.IsTravelModeOn
	}
	return false
}

func (x *GetTravelModeResponse) GetEnableWorkflow() ControlActionWorkflow {
	if x != nil {
		return x.EnableWorkflow
	}
	return ControlActionWorkflow_CONTROL_ACTION_WORKFLOW_UNSPECIFIED
}

type GetInternationalAtmLimitsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to GetBy:
	//
	//	*GetInternationalAtmLimitsRequest_CountryCodes_
	//	*GetInternationalAtmLimitsRequest_GetAll
	GetBy isGetInternationalAtmLimitsRequest_GetBy `protobuf_oneof:"GetBy"`
	// Optional: will be used for getting user's current location country; international only
	ActorId string `protobuf:"bytes,7,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetInternationalAtmLimitsRequest) Reset() {
	*x = GetInternationalAtmLimitsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInternationalAtmLimitsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInternationalAtmLimitsRequest) ProtoMessage() {}

func (x *GetInternationalAtmLimitsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInternationalAtmLimitsRequest.ProtoReflect.Descriptor instead.
func (*GetInternationalAtmLimitsRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{4}
}

func (m *GetInternationalAtmLimitsRequest) GetGetBy() isGetInternationalAtmLimitsRequest_GetBy {
	if m != nil {
		return m.GetBy
	}
	return nil
}

func (x *GetInternationalAtmLimitsRequest) GetCountryCodes() *GetInternationalAtmLimitsRequest_CountryCodes {
	if x, ok := x.GetGetBy().(*GetInternationalAtmLimitsRequest_CountryCodes_); ok {
		return x.CountryCodes
	}
	return nil
}

func (x *GetInternationalAtmLimitsRequest) GetGetAll() bool {
	if x, ok := x.GetGetBy().(*GetInternationalAtmLimitsRequest_GetAll); ok {
		return x.GetAll
	}
	return false
}

func (x *GetInternationalAtmLimitsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type isGetInternationalAtmLimitsRequest_GetBy interface {
	isGetInternationalAtmLimitsRequest_GetBy()
}

type GetInternationalAtmLimitsRequest_CountryCodes_ struct {
	CountryCodes *GetInternationalAtmLimitsRequest_CountryCodes `protobuf:"bytes,1,opt,name=country_codes,json=countryCodes,proto3,oneof"`
}

type GetInternationalAtmLimitsRequest_GetAll struct {
	GetAll bool `protobuf:"varint,2,opt,name=get_all,json=getAll,proto3,oneof"`
}

func (*GetInternationalAtmLimitsRequest_CountryCodes_) isGetInternationalAtmLimitsRequest_GetBy() {}

func (*GetInternationalAtmLimitsRequest_GetAll) isGetInternationalAtmLimitsRequest_GetBy() {}

type GetInternationalAtmLimitsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of ATM limits for different countries
	InternationalAtmLimits []*InternationalAtmLimit `protobuf:"bytes,2,rep,name=international_atm_limits,json=internationalAtmLimits,proto3" json:"international_atm_limits,omitempty"`
	// Indicates if the user is currently at an international location
	IsUserAtInternationalLocation bool `protobuf:"varint,3,opt,name=is_user_at_international_location,json=isUserAtInternationalLocation,proto3" json:"is_user_at_international_location,omitempty"`
	// Name of the international country where the user is currently visiting
	UserCountryName string `protobuf:"bytes,4,opt,name=user_country_name,json=userCountryName,proto3" json:"user_country_name,omitempty"`
}

func (x *GetInternationalAtmLimitsResponse) Reset() {
	*x = GetInternationalAtmLimitsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInternationalAtmLimitsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInternationalAtmLimitsResponse) ProtoMessage() {}

func (x *GetInternationalAtmLimitsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInternationalAtmLimitsResponse.ProtoReflect.Descriptor instead.
func (*GetInternationalAtmLimitsResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetInternationalAtmLimitsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetInternationalAtmLimitsResponse) GetInternationalAtmLimits() []*InternationalAtmLimit {
	if x != nil {
		return x.InternationalAtmLimits
	}
	return nil
}

func (x *GetInternationalAtmLimitsResponse) GetIsUserAtInternationalLocation() bool {
	if x != nil {
		return x.IsUserAtInternationalLocation
	}
	return false
}

func (x *GetInternationalAtmLimitsResponse) GetUserCountryName() string {
	if x != nil {
		return x.UserCountryName
	}
	return ""
}

// Represents ATM withdrawal limit details for a specific country
type InternationalAtmLimit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CountryName string `protobuf:"bytes,1,opt,name=country_name,json=countryName,proto3" json:"country_name,omitempty"`
	// ISO code of the country
	CountryCode string `protobuf:"bytes,2,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// emoji for the country's flag
	CountryFlag string `protobuf:"bytes,3,opt,name=country_flag,json=countryFlag,proto3" json:"country_flag,omitempty"`
	// Maximum ATM withdrawal limit in the indian currency
	MaxAtmWithdrawalLimit *money.Money `protobuf:"bytes,4,opt,name=max_atm_withdrawal_limit,json=maxAtmWithdrawalLimit,proto3" json:"max_atm_withdrawal_limit,omitempty"`
}

func (x *InternationalAtmLimit) Reset() {
	*x = InternationalAtmLimit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternationalAtmLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternationalAtmLimit) ProtoMessage() {}

func (x *InternationalAtmLimit) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternationalAtmLimit.ProtoReflect.Descriptor instead.
func (*InternationalAtmLimit) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{6}
}

func (x *InternationalAtmLimit) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *InternationalAtmLimit) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *InternationalAtmLimit) GetCountryFlag() string {
	if x != nil {
		return x.CountryFlag
	}
	return ""
}

func (x *InternationalAtmLimit) GetMaxAtmWithdrawalLimit() *money.Money {
	if x != nil {
		return x.MaxAtmWithdrawalLimit
	}
	return nil
}

type BlockCardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardIds             []string             `protobuf:"bytes,1,rep,name=card_ids,json=cardIds,proto3" json:"card_ids,omitempty"`
	Vendor              vendorgateway.Vendor `protobuf:"varint,2,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	BlockCardReason     string               `protobuf:"bytes,3,opt,name=block_card_reason,json=blockCardReason,proto3" json:"block_card_reason,omitempty"`
	BlockCardProvenance card.Provenance      `protobuf:"varint,4,opt,name=block_card_provenance,json=blockCardProvenance,proto3,enum=card.Provenance" json:"block_card_provenance,omitempty"`
	// When we want to block the card only at our end, e.x. when the card is already blocked at vendor end like hotmarked cards
	SkipVendorCall bool `protobuf:"varint,5,opt,name=skip_vendor_call,json=skipVendorCall,proto3" json:"skip_vendor_call,omitempty"`
}

func (x *BlockCardRequest) Reset() {
	*x = BlockCardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockCardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockCardRequest) ProtoMessage() {}

func (x *BlockCardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockCardRequest.ProtoReflect.Descriptor instead.
func (*BlockCardRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{7}
}

func (x *BlockCardRequest) GetCardIds() []string {
	if x != nil {
		return x.CardIds
	}
	return nil
}

func (x *BlockCardRequest) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *BlockCardRequest) GetBlockCardReason() string {
	if x != nil {
		return x.BlockCardReason
	}
	return ""
}

func (x *BlockCardRequest) GetBlockCardProvenance() card.Provenance {
	if x != nil {
		return x.BlockCardProvenance
	}
	return card.Provenance(0)
}

func (x *BlockCardRequest) GetSkipVendorCall() bool {
	if x != nil {
		return x.SkipVendorCall
	}
	return false
}

type BlockCardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	BlockedCards map[string]*card.Card `protobuf:"bytes,2,rep,name=blocked_cards,json=blockedCards,proto3" json:"blocked_cards,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BlockCardResponse) Reset() {
	*x = BlockCardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockCardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockCardResponse) ProtoMessage() {}

func (x *BlockCardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockCardResponse.ProtoReflect.Descriptor instead.
func (*BlockCardResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{8}
}

func (x *BlockCardResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *BlockCardResponse) GetBlockedCards() map[string]*card.Card {
	if x != nil {
		return x.BlockedCards
	}
	return nil
}

type SuspendCardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardIds   []string               `protobuf:"bytes,1,rep,name=card_ids,json=cardIds,proto3" json:"card_ids,omitempty"`
	Action    card.CardControlAction `protobuf:"varint,2,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
	Vendor    vendorgateway.Vendor   `protobuf:"varint,3,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	CredBlock string                 `protobuf:"bytes,5,opt,name=cred_block,json=credBlock,proto3" json:"cred_block,omitempty"`
	// request/txn-id used as a salt in credblock generation
	RequestId string `protobuf:"bytes,6,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Workflow from where the request was initiated
	ControlActionWorkflow ControlActionWorkflow `protobuf:"varint,7,opt,name=control_action_workflow,json=controlActionWorkflow,proto3,enum=card.control.ControlActionWorkflow" json:"control_action_workflow,omitempty"`
}

func (x *SuspendCardRequest) Reset() {
	*x = SuspendCardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuspendCardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuspendCardRequest) ProtoMessage() {}

func (x *SuspendCardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuspendCardRequest.ProtoReflect.Descriptor instead.
func (*SuspendCardRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{9}
}

func (x *SuspendCardRequest) GetCardIds() []string {
	if x != nil {
		return x.CardIds
	}
	return nil
}

func (x *SuspendCardRequest) GetAction() card.CardControlAction {
	if x != nil {
		return x.Action
	}
	return card.CardControlAction(0)
}

func (x *SuspendCardRequest) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *SuspendCardRequest) GetCredBlock() string {
	if x != nil {
		return x.CredBlock
	}
	return ""
}

func (x *SuspendCardRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *SuspendCardRequest) GetControlActionWorkflow() ControlActionWorkflow {
	if x != nil {
		return x.ControlActionWorkflow
	}
	return ControlActionWorkflow_CONTROL_ACTION_WORKFLOW_UNSPECIFIED
}

type SuspendStatesInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Card   *card.Card             `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	Action card.CardControlAction `protobuf:"varint,2,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
	// Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
	// We will use this status code to convert error response to UI error view which will be shown to the client.
	InternalStatusCode string `protobuf:"bytes,3,opt,name=internal_status_code,json=internalStatusCode,proto3" json:"internal_status_code,omitempty"`
}

func (x *SuspendStatesInfo) Reset() {
	*x = SuspendStatesInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuspendStatesInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuspendStatesInfo) ProtoMessage() {}

func (x *SuspendStatesInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuspendStatesInfo.ProtoReflect.Descriptor instead.
func (*SuspendStatesInfo) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{10}
}

func (x *SuspendStatesInfo) GetCard() *card.Card {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *SuspendStatesInfo) GetAction() card.CardControlAction {
	if x != nil {
		return x.Action
	}
	return card.CardControlAction(0)
}

func (x *SuspendStatesInfo) GetInternalStatusCode() string {
	if x != nil {
		return x.InternalStatusCode
	}
	return ""
}

type SuspendCardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status                   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SuspendStates map[string]*SuspendStatesInfo `protobuf:"bytes,2,rep,name=suspend_states,json=suspendStates,proto3" json:"suspend_states,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SuspendCardResponse) Reset() {
	*x = SuspendCardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuspendCardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuspendCardResponse) ProtoMessage() {}

func (x *SuspendCardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuspendCardResponse.ProtoReflect.Descriptor instead.
func (*SuspendCardResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{11}
}

func (x *SuspendCardResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *SuspendCardResponse) GetSuspendStates() map[string]*SuspendStatesInfo {
	if x != nil {
		return x.SuspendStates
	}
	return nil
}

type LocationOnOffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardIds   []string                   `protobuf:"bytes,1,rep,name=card_ids,json=cardIds,proto3" json:"card_ids,omitempty"`
	LocType   card.CardUsageLocationType `protobuf:"varint,2,opt,name=locType,proto3,enum=card.CardUsageLocationType" json:"locType,omitempty"`
	Action    card.CardControlAction     `protobuf:"varint,3,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
	Vendor    vendorgateway.Vendor       `protobuf:"varint,4,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	CredBlock string                     `protobuf:"bytes,5,opt,name=cred_block,json=credBlock,proto3" json:"cred_block,omitempty"`
	// request/txn-id used as a salt in credblock generation (if credblock is provided for request)
	RequestId string `protobuf:"bytes,6,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Workflow from where the request was initiated
	ControlActionWorkflow ControlActionWorkflow `protobuf:"varint,7,opt,name=control_action_workflow,json=controlActionWorkflow,proto3,enum=card.control.ControlActionWorkflow" json:"control_action_workflow,omitempty"`
}

func (x *LocationOnOffRequest) Reset() {
	*x = LocationOnOffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationOnOffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationOnOffRequest) ProtoMessage() {}

func (x *LocationOnOffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationOnOffRequest.ProtoReflect.Descriptor instead.
func (*LocationOnOffRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{12}
}

func (x *LocationOnOffRequest) GetCardIds() []string {
	if x != nil {
		return x.CardIds
	}
	return nil
}

func (x *LocationOnOffRequest) GetLocType() card.CardUsageLocationType {
	if x != nil {
		return x.LocType
	}
	return card.CardUsageLocationType(0)
}

func (x *LocationOnOffRequest) GetAction() card.CardControlAction {
	if x != nil {
		return x.Action
	}
	return card.CardControlAction(0)
}

func (x *LocationOnOffRequest) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *LocationOnOffRequest) GetCredBlock() string {
	if x != nil {
		return x.CredBlock
	}
	return ""
}

func (x *LocationOnOffRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *LocationOnOffRequest) GetControlActionWorkflow() ControlActionWorkflow {
	if x != nil {
		return x.ControlActionWorkflow
	}
	return ControlActionWorkflow_CONTROL_ACTION_WORKFLOW_UNSPECIFIED
}

type LocationOnOffStatesInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Card   *card.Card             `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	Action card.CardControlAction `protobuf:"varint,2,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
	// Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
	// We will use this status code to convert error response to UI error view which will be shown to the client.
	InternalStatusCode string `protobuf:"bytes,3,opt,name=internal_status_code,json=internalStatusCode,proto3" json:"internal_status_code,omitempty"`
}

func (x *LocationOnOffStatesInfo) Reset() {
	*x = LocationOnOffStatesInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationOnOffStatesInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationOnOffStatesInfo) ProtoMessage() {}

func (x *LocationOnOffStatesInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationOnOffStatesInfo.ProtoReflect.Descriptor instead.
func (*LocationOnOffStatesInfo) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{13}
}

func (x *LocationOnOffStatesInfo) GetCard() *card.Card {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *LocationOnOffStatesInfo) GetAction() card.CardControlAction {
	if x != nil {
		return x.Action
	}
	return card.CardControlAction(0)
}

func (x *LocationOnOffStatesInfo) GetInternalStatusCode() string {
	if x != nil {
		return x.InternalStatusCode
	}
	return ""
}

type LocationOnOffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status              *rpc.Status                         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LocationOnOffStates map[string]*LocationOnOffStatesInfo `protobuf:"bytes,2,rep,name=location_on_off_states,json=locationOnOffStates,proto3" json:"location_on_off_states,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *LocationOnOffResponse) Reset() {
	*x = LocationOnOffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationOnOffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationOnOffResponse) ProtoMessage() {}

func (x *LocationOnOffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationOnOffResponse.ProtoReflect.Descriptor instead.
func (*LocationOnOffResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{14}
}

func (x *LocationOnOffResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *LocationOnOffResponse) GetLocationOnOffStates() map[string]*LocationOnOffStatesInfo {
	if x != nil {
		return x.LocationOnOffStates
	}
	return nil
}

type ECommerceOnOffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardIds   []string               `protobuf:"bytes,1,rep,name=card_ids,json=cardIds,proto3" json:"card_ids,omitempty"`
	Action    card.CardControlAction `protobuf:"varint,2,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
	Vendor    vendorgateway.Vendor   `protobuf:"varint,3,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	CredBlock string                 `protobuf:"bytes,4,opt,name=cred_block,json=credBlock,proto3" json:"cred_block,omitempty"`
	// request/txn-id used as a salt in credblock generation (if credblock is provided for request)
	RequestId string `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// CardControlWorkflow is to mark the type of card control request.
	// Such as secure pin workflow or token workflow.
	// We will pass CARD_CTRL_TOKEN_WF when we enable e-commerce during setting card pin in background
	EcommEnableFlow CardControlWorkflow `protobuf:"varint,6,opt,name=ecomm_enable_flow,json=ecommEnableFlow,proto3,enum=card.control.CardControlWorkflow" json:"ecomm_enable_flow,omitempty"`
	// Workflow from where the request was initiated
	ControlActionWorkflow ControlActionWorkflow `protobuf:"varint,7,opt,name=control_action_workflow,json=controlActionWorkflow,proto3,enum=card.control.ControlActionWorkflow" json:"control_action_workflow,omitempty"`
}

func (x *ECommerceOnOffRequest) Reset() {
	*x = ECommerceOnOffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ECommerceOnOffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ECommerceOnOffRequest) ProtoMessage() {}

func (x *ECommerceOnOffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ECommerceOnOffRequest.ProtoReflect.Descriptor instead.
func (*ECommerceOnOffRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{15}
}

func (x *ECommerceOnOffRequest) GetCardIds() []string {
	if x != nil {
		return x.CardIds
	}
	return nil
}

func (x *ECommerceOnOffRequest) GetAction() card.CardControlAction {
	if x != nil {
		return x.Action
	}
	return card.CardControlAction(0)
}

func (x *ECommerceOnOffRequest) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *ECommerceOnOffRequest) GetCredBlock() string {
	if x != nil {
		return x.CredBlock
	}
	return ""
}

func (x *ECommerceOnOffRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ECommerceOnOffRequest) GetEcommEnableFlow() CardControlWorkflow {
	if x != nil {
		return x.EcommEnableFlow
	}
	return CardControlWorkflow_CARD_CTRL_WF_UNSPECIFIED
}

func (x *ECommerceOnOffRequest) GetControlActionWorkflow() ControlActionWorkflow {
	if x != nil {
		return x.ControlActionWorkflow
	}
	return ControlActionWorkflow_CONTROL_ACTION_WORKFLOW_UNSPECIFIED
}

type ECommerceOnOffStatesInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Card   *card.Card             `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	Action card.CardControlAction `protobuf:"varint,2,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
	// Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
	// We will use this status code to convert error response to UI error view which will be shown to the client.
	InternalStatusCode string `protobuf:"bytes,3,opt,name=internal_status_code,json=internalStatusCode,proto3" json:"internal_status_code,omitempty"`
}

func (x *ECommerceOnOffStatesInfo) Reset() {
	*x = ECommerceOnOffStatesInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ECommerceOnOffStatesInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ECommerceOnOffStatesInfo) ProtoMessage() {}

func (x *ECommerceOnOffStatesInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ECommerceOnOffStatesInfo.ProtoReflect.Descriptor instead.
func (*ECommerceOnOffStatesInfo) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{16}
}

func (x *ECommerceOnOffStatesInfo) GetCard() *card.Card {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *ECommerceOnOffStatesInfo) GetAction() card.CardControlAction {
	if x != nil {
		return x.Action
	}
	return card.CardControlAction(0)
}

func (x *ECommerceOnOffStatesInfo) GetInternalStatusCode() string {
	if x != nil {
		return x.InternalStatusCode
	}
	return ""
}

type ECommerceOnOffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           *rpc.Status                          `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	EcommOnOffStates map[string]*ECommerceOnOffStatesInfo `protobuf:"bytes,2,rep,name=ecomm_on_off_states,json=ecommOnOffStates,proto3" json:"ecomm_on_off_states,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ECommerceOnOffResponse) Reset() {
	*x = ECommerceOnOffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ECommerceOnOffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ECommerceOnOffResponse) ProtoMessage() {}

func (x *ECommerceOnOffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ECommerceOnOffResponse.ProtoReflect.Descriptor instead.
func (*ECommerceOnOffResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{17}
}

func (x *ECommerceOnOffResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ECommerceOnOffResponse) GetEcommOnOffStates() map[string]*ECommerceOnOffStatesInfo {
	if x != nil {
		return x.EcommOnOffStates
	}
	return nil
}

type ATMOnOffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardId    string                 `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	Action    card.CardControlAction `protobuf:"varint,2,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
	CredBlock string                 `protobuf:"bytes,3,opt,name=cred_block,json=credBlock,proto3" json:"cred_block,omitempty"`
	// request/txn-id used as a salt in credblock generation (if credblock is provided for request)
	// Cred block will be required only in case of enabling ATM transactions
	RequestId string `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Workflow from where the request was initiated
	ControlActionWorkflow ControlActionWorkflow `protobuf:"varint,7,opt,name=control_action_workflow,json=controlActionWorkflow,proto3,enum=card.control.ControlActionWorkflow" json:"control_action_workflow,omitempty"`
}

func (x *ATMOnOffRequest) Reset() {
	*x = ATMOnOffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ATMOnOffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ATMOnOffRequest) ProtoMessage() {}

func (x *ATMOnOffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ATMOnOffRequest.ProtoReflect.Descriptor instead.
func (*ATMOnOffRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{18}
}

func (x *ATMOnOffRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *ATMOnOffRequest) GetAction() card.CardControlAction {
	if x != nil {
		return x.Action
	}
	return card.CardControlAction(0)
}

func (x *ATMOnOffRequest) GetCredBlock() string {
	if x != nil {
		return x.CredBlock
	}
	return ""
}

func (x *ATMOnOffRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ATMOnOffRequest) GetControlActionWorkflow() ControlActionWorkflow {
	if x != nil {
		return x.ControlActionWorkflow
	}
	return ControlActionWorkflow_CONTROL_ACTION_WORKFLOW_UNSPECIFIED
}

type ATMOnOffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	AtmOnOffState *ATMOnOffStatesInfo `protobuf:"bytes,2,opt,name=atm_on_off_state,json=atmOnOffState,proto3" json:"atm_on_off_state,omitempty"`
}

func (x *ATMOnOffResponse) Reset() {
	*x = ATMOnOffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ATMOnOffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ATMOnOffResponse) ProtoMessage() {}

func (x *ATMOnOffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ATMOnOffResponse.ProtoReflect.Descriptor instead.
func (*ATMOnOffResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{19}
}

func (x *ATMOnOffResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ATMOnOffResponse) GetAtmOnOffState() *ATMOnOffStatesInfo {
	if x != nil {
		return x.AtmOnOffState
	}
	return nil
}

type ATMOnOffStatesInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Card   *card.Card             `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	Action card.CardControlAction `protobuf:"varint,2,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
	// Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
	// We will use this status code to convert error response to UI error view which will be shown to the client.
	InternalStatusCode string `protobuf:"bytes,3,opt,name=internal_status_code,json=internalStatusCode,proto3" json:"internal_status_code,omitempty"`
}

func (x *ATMOnOffStatesInfo) Reset() {
	*x = ATMOnOffStatesInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ATMOnOffStatesInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ATMOnOffStatesInfo) ProtoMessage() {}

func (x *ATMOnOffStatesInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ATMOnOffStatesInfo.ProtoReflect.Descriptor instead.
func (*ATMOnOffStatesInfo) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{20}
}

func (x *ATMOnOffStatesInfo) GetCard() *card.Card {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *ATMOnOffStatesInfo) GetAction() card.CardControlAction {
	if x != nil {
		return x.Action
	}
	return card.CardControlAction(0)
}

func (x *ATMOnOffStatesInfo) GetInternalStatusCode() string {
	if x != nil {
		return x.InternalStatusCode
	}
	return ""
}

type POSOnOffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardId    string                 `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	Action    card.CardControlAction `protobuf:"varint,2,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
	CredBlock string                 `protobuf:"bytes,3,opt,name=cred_block,json=credBlock,proto3" json:"cred_block,omitempty"`
	// request/txn-id used as a salt in credblock generation (if credblock is provided for request)
	// Cred block will be required only in case of enabling POS transactions
	RequestId string `protobuf:"bytes,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Workflow from where the request was initiated
	ControlActionWorkflow ControlActionWorkflow `protobuf:"varint,7,opt,name=control_action_workflow,json=controlActionWorkflow,proto3,enum=card.control.ControlActionWorkflow" json:"control_action_workflow,omitempty"`
}

func (x *POSOnOffRequest) Reset() {
	*x = POSOnOffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *POSOnOffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*POSOnOffRequest) ProtoMessage() {}

func (x *POSOnOffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use POSOnOffRequest.ProtoReflect.Descriptor instead.
func (*POSOnOffRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{21}
}

func (x *POSOnOffRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *POSOnOffRequest) GetAction() card.CardControlAction {
	if x != nil {
		return x.Action
	}
	return card.CardControlAction(0)
}

func (x *POSOnOffRequest) GetCredBlock() string {
	if x != nil {
		return x.CredBlock
	}
	return ""
}

func (x *POSOnOffRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *POSOnOffRequest) GetControlActionWorkflow() ControlActionWorkflow {
	if x != nil {
		return x.ControlActionWorkflow
	}
	return ControlActionWorkflow_CONTROL_ACTION_WORKFLOW_UNSPECIFIED
}

type POSOnOffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PosOnOffState *POSOnOffStatesInfo `protobuf:"bytes,2,opt,name=pos_on_off_state,json=posOnOffState,proto3" json:"pos_on_off_state,omitempty"`
}

func (x *POSOnOffResponse) Reset() {
	*x = POSOnOffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *POSOnOffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*POSOnOffResponse) ProtoMessage() {}

func (x *POSOnOffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use POSOnOffResponse.ProtoReflect.Descriptor instead.
func (*POSOnOffResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{22}
}

func (x *POSOnOffResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *POSOnOffResponse) GetPosOnOffState() *POSOnOffStatesInfo {
	if x != nil {
		return x.PosOnOffState
	}
	return nil
}

type POSOnOffStatesInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Card   *card.Card             `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	Action card.CardControlAction `protobuf:"varint,2,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
	// Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
	// We will use this status code to convert error response to UI error view which will be shown to the client.
	InternalStatusCode string `protobuf:"bytes,3,opt,name=internal_status_code,json=internalStatusCode,proto3" json:"internal_status_code,omitempty"`
}

func (x *POSOnOffStatesInfo) Reset() {
	*x = POSOnOffStatesInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *POSOnOffStatesInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*POSOnOffStatesInfo) ProtoMessage() {}

func (x *POSOnOffStatesInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use POSOnOffStatesInfo.ProtoReflect.Descriptor instead.
func (*POSOnOffStatesInfo) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{23}
}

func (x *POSOnOffStatesInfo) GetCard() *card.Card {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *POSOnOffStatesInfo) GetAction() card.CardControlAction {
	if x != nil {
		return x.Action
	}
	return card.CardControlAction(0)
}

func (x *POSOnOffStatesInfo) GetInternalStatusCode() string {
	if x != nil {
		return x.InternalStatusCode
	}
	return ""
}

type NfcOnOffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardIds   []string               `protobuf:"bytes,1,rep,name=card_ids,json=cardIds,proto3" json:"card_ids,omitempty"`
	Action    card.CardControlAction `protobuf:"varint,2,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
	Vendor    vendorgateway.Vendor   `protobuf:"varint,3,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	CredBlock string                 `protobuf:"bytes,4,opt,name=cred_block,json=credBlock,proto3" json:"cred_block,omitempty"`
	// request/txn-id used as a salt in credblock generation (if credblock is provided for request)
	RequestId string `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Workflow from where the request was initiated
	ControlActionWorkflow ControlActionWorkflow `protobuf:"varint,7,opt,name=control_action_workflow,json=controlActionWorkflow,proto3,enum=card.control.ControlActionWorkflow" json:"control_action_workflow,omitempty"`
}

func (x *NfcOnOffRequest) Reset() {
	*x = NfcOnOffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NfcOnOffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NfcOnOffRequest) ProtoMessage() {}

func (x *NfcOnOffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NfcOnOffRequest.ProtoReflect.Descriptor instead.
func (*NfcOnOffRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{24}
}

func (x *NfcOnOffRequest) GetCardIds() []string {
	if x != nil {
		return x.CardIds
	}
	return nil
}

func (x *NfcOnOffRequest) GetAction() card.CardControlAction {
	if x != nil {
		return x.Action
	}
	return card.CardControlAction(0)
}

func (x *NfcOnOffRequest) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *NfcOnOffRequest) GetCredBlock() string {
	if x != nil {
		return x.CredBlock
	}
	return ""
}

func (x *NfcOnOffRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *NfcOnOffRequest) GetControlActionWorkflow() ControlActionWorkflow {
	if x != nil {
		return x.ControlActionWorkflow
	}
	return ControlActionWorkflow_CONTROL_ACTION_WORKFLOW_UNSPECIFIED
}

type NfcOnOffStatesInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Card   *card.Card             `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	Action card.CardControlAction `protobuf:"varint,2,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
	// Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
	// We will use this status code to convert error response to UI error view which will be shown to the client.
	InternalStatusCode string `protobuf:"bytes,3,opt,name=internal_status_code,json=internalStatusCode,proto3" json:"internal_status_code,omitempty"`
}

func (x *NfcOnOffStatesInfo) Reset() {
	*x = NfcOnOffStatesInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NfcOnOffStatesInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NfcOnOffStatesInfo) ProtoMessage() {}

func (x *NfcOnOffStatesInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NfcOnOffStatesInfo.ProtoReflect.Descriptor instead.
func (*NfcOnOffStatesInfo) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{25}
}

func (x *NfcOnOffStatesInfo) GetCard() *card.Card {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *NfcOnOffStatesInfo) GetAction() card.CardControlAction {
	if x != nil {
		return x.Action
	}
	return card.CardControlAction(0)
}

func (x *NfcOnOffStatesInfo) GetInternalStatusCode() string {
	if x != nil {
		return x.InternalStatusCode
	}
	return ""
}

type NfcOnOffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status                    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NfcOnOffStates map[string]*NfcOnOffStatesInfo `protobuf:"bytes,2,rep,name=nfc_on_off_states,json=nfcOnOffStates,proto3" json:"nfc_on_off_states,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *NfcOnOffResponse) Reset() {
	*x = NfcOnOffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NfcOnOffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NfcOnOffResponse) ProtoMessage() {}

func (x *NfcOnOffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NfcOnOffResponse.ProtoReflect.Descriptor instead.
func (*NfcOnOffResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{26}
}

func (x *NfcOnOffResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *NfcOnOffResponse) GetNfcOnOffStates() map[string]*NfcOnOffStatesInfo {
	if x != nil {
		return x.NfcOnOffStates
	}
	return nil
}

type ControlOnOffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardIds   []string                   `protobuf:"bytes,1,rep,name=card_ids,json=cardIds,proto3" json:"card_ids,omitempty"`
	LocType   card.CardUsageLocationType `protobuf:"varint,2,opt,name=loc_type,json=locType,proto3,enum=card.CardUsageLocationType" json:"loc_type,omitempty"`
	TxnType   card.CardTransactionType   `protobuf:"varint,3,opt,name=txn_type,json=txnType,proto3,enum=card.CardTransactionType" json:"txn_type,omitempty"`
	Action    card.CardControlAction     `protobuf:"varint,4,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
	Vendor    vendorgateway.Vendor       `protobuf:"varint,5,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	CredBlock string                     `protobuf:"bytes,6,opt,name=cred_block,json=credBlock,proto3" json:"cred_block,omitempty"`
	// request/txn-id used as a salt in credblock generation (if credblock is provided for request)
	RequestId string `protobuf:"bytes,7,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *ControlOnOffRequest) Reset() {
	*x = ControlOnOffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ControlOnOffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlOnOffRequest) ProtoMessage() {}

func (x *ControlOnOffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlOnOffRequest.ProtoReflect.Descriptor instead.
func (*ControlOnOffRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{27}
}

func (x *ControlOnOffRequest) GetCardIds() []string {
	if x != nil {
		return x.CardIds
	}
	return nil
}

func (x *ControlOnOffRequest) GetLocType() card.CardUsageLocationType {
	if x != nil {
		return x.LocType
	}
	return card.CardUsageLocationType(0)
}

func (x *ControlOnOffRequest) GetTxnType() card.CardTransactionType {
	if x != nil {
		return x.TxnType
	}
	return card.CardTransactionType(0)
}

func (x *ControlOnOffRequest) GetAction() card.CardControlAction {
	if x != nil {
		return x.Action
	}
	return card.CardControlAction(0)
}

func (x *ControlOnOffRequest) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *ControlOnOffRequest) GetCredBlock() string {
	if x != nil {
		return x.CredBlock
	}
	return ""
}

func (x *ControlOnOffRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type ControlOnOffStatesInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Card   *card.Card             `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	Action card.CardControlAction `protobuf:"varint,2,opt,name=action,proto3,enum=card.CardControlAction" json:"action,omitempty"`
}

func (x *ControlOnOffStatesInfo) Reset() {
	*x = ControlOnOffStatesInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ControlOnOffStatesInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlOnOffStatesInfo) ProtoMessage() {}

func (x *ControlOnOffStatesInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlOnOffStatesInfo.ProtoReflect.Descriptor instead.
func (*ControlOnOffStatesInfo) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{28}
}

func (x *ControlOnOffStatesInfo) GetCard() *card.Card {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *ControlOnOffStatesInfo) GetAction() card.CardControlAction {
	if x != nil {
		return x.Action
	}
	return card.CardControlAction(0)
}

type ControlOnOffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status             *rpc.Status                        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ControlOnOffStates map[string]*ControlOnOffStatesInfo `protobuf:"bytes,2,rep,name=control_on_off_states,json=controlOnOffStates,proto3" json:"control_on_off_states,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ControlOnOffResponse) Reset() {
	*x = ControlOnOffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ControlOnOffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlOnOffResponse) ProtoMessage() {}

func (x *ControlOnOffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlOnOffResponse.ProtoReflect.Descriptor instead.
func (*ControlOnOffResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{29}
}

func (x *ControlOnOffResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ControlOnOffResponse) GetControlOnOffStates() map[string]*ControlOnOffStatesInfo {
	if x != nil {
		return x.ControlOnOffStates
	}
	return nil
}

type GetCardLimitsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Card-id of enquired card.
	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *GetCardLimitsRequest) Reset() {
	*x = GetCardLimitsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCardLimitsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardLimitsRequest) ProtoMessage() {}

func (x *GetCardLimitsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardLimitsRequest.ProtoReflect.Descriptor instead.
func (*GetCardLimitsRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetCardLimitsRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

type GetCardLimitsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// card limits
	CardLimitData *CardLimitData `protobuf:"bytes,2,opt,name=card_limit_data,json=cardLimitData,proto3" json:"card_limit_data,omitempty"`
	// request-id used to get card limit at vendor.
	// This request id need to be send in update card limit for any update request.
	RequestId string `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Federal mandates that the same request-id be used for both GetLimit and UpdateLimit api calls.
	// During limit update, if credblock is required, masked card number will be used as salt to generate credblock
	MaskedCardNumber string `protobuf:"bytes,4,opt,name=masked_card_number,json=maskedCardNumber,proto3" json:"masked_card_number,omitempty"`
	// maximum daily allowed limits for a card
	DailyAllowedLimits *AllowedLimits `protobuf:"bytes,5,opt,name=daily_allowed_limits,json=dailyAllowedLimits,proto3" json:"daily_allowed_limits,omitempty"`
	// Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
	// We will use this status code to convert error response to UI error view which will be shown to the client.
	InternalStatusCode string `protobuf:"bytes,6,opt,name=internal_status_code,json=internalStatusCode,proto3" json:"internal_status_code,omitempty"`
}

func (x *GetCardLimitsResponse) Reset() {
	*x = GetCardLimitsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCardLimitsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardLimitsResponse) ProtoMessage() {}

func (x *GetCardLimitsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardLimitsResponse.ProtoReflect.Descriptor instead.
func (*GetCardLimitsResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{31}
}

func (x *GetCardLimitsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCardLimitsResponse) GetCardLimitData() *CardLimitData {
	if x != nil {
		return x.CardLimitData
	}
	return nil
}

func (x *GetCardLimitsResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetCardLimitsResponse) GetMaskedCardNumber() string {
	if x != nil {
		return x.MaskedCardNumber
	}
	return ""
}

func (x *GetCardLimitsResponse) GetDailyAllowedLimits() *AllowedLimits {
	if x != nil {
		return x.DailyAllowedLimits
	}
	return nil
}

func (x *GetCardLimitsResponse) GetInternalStatusCode() string {
	if x != nil {
		return x.InternalStatusCode
	}
	return ""
}

type UpdateCardLimitsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Card-id of enquired card.
	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// request-id used to generate cred block
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// cred block (encrypted pin+salt in base64 format)
	CredBlock string `protobuf:"bytes,3,opt,name=cred_block,json=credBlock,proto3" json:"cred_block,omitempty"`
	// list of card limit request to update at vendor
	UpdateCardLimitDetails []*CardLimitDetail `protobuf:"bytes,4,rep,name=update_card_limit_details,json=updateCardLimitDetails,proto3" json:"update_card_limit_details,omitempty"`
	// it should be true if any card limit detail is increased from it's current value.
	IsLimitIncreased bool `protobuf:"varint,5,opt,name=is_limit_increased,json=isLimitIncreased,proto3" json:"is_limit_increased,omitempty"`
	// If limit update is initiated by the user via Card limit screen this flag should be true else false
	IsUserInitiated bool `protobuf:"varint,6,opt,name=is_user_initiated,json=isUserInitiated,proto3" json:"is_user_initiated,omitempty"`
}

func (x *UpdateCardLimitsRequest) Reset() {
	*x = UpdateCardLimitsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCardLimitsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCardLimitsRequest) ProtoMessage() {}

func (x *UpdateCardLimitsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCardLimitsRequest.ProtoReflect.Descriptor instead.
func (*UpdateCardLimitsRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{32}
}

func (x *UpdateCardLimitsRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *UpdateCardLimitsRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *UpdateCardLimitsRequest) GetCredBlock() string {
	if x != nil {
		return x.CredBlock
	}
	return ""
}

func (x *UpdateCardLimitsRequest) GetUpdateCardLimitDetails() []*CardLimitDetail {
	if x != nil {
		return x.UpdateCardLimitDetails
	}
	return nil
}

func (x *UpdateCardLimitsRequest) GetIsLimitIncreased() bool {
	if x != nil {
		return x.IsLimitIncreased
	}
	return false
}

func (x *UpdateCardLimitsRequest) GetIsUserInitiated() bool {
	if x != nil {
		return x.IsUserInitiated
	}
	return false
}

type UpdateCardLimitsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CardLimitData *CardLimitData `protobuf:"bytes,2,opt,name=card_limit_data,json=cardLimitData,proto3" json:"card_limit_data,omitempty"`
	// request-id used to get updated card limit at vendor.
	// This request id need to be send if user again want to update card limit.
	RequestId string `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Federal mandates that the same request-id be used for both GetLimit and UpdateLimit api calls.
	// During limit update, if credblock is required, masked card number will be used as salt to generate credblock
	MaskedCardNumber string `protobuf:"bytes,4,opt,name=masked_card_number,json=maskedCardNumber,proto3" json:"masked_card_number,omitempty"`
	// maximum daily allowed limits for a card
	DailyAllowedLimits *AllowedLimits `protobuf:"bytes,5,opt,name=daily_allowed_limits,json=dailyAllowedLimits,proto3" json:"daily_allowed_limits,omitempty"`
	// Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
	// We will use this status code to convert error response to UI error view which will be shown to the client.
	InternalStatusCode string `protobuf:"bytes,6,opt,name=internal_status_code,json=internalStatusCode,proto3" json:"internal_status_code,omitempty"`
}

func (x *UpdateCardLimitsResponse) Reset() {
	*x = UpdateCardLimitsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCardLimitsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCardLimitsResponse) ProtoMessage() {}

func (x *UpdateCardLimitsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCardLimitsResponse.ProtoReflect.Descriptor instead.
func (*UpdateCardLimitsResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{33}
}

func (x *UpdateCardLimitsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateCardLimitsResponse) GetCardLimitData() *CardLimitData {
	if x != nil {
		return x.CardLimitData
	}
	return nil
}

func (x *UpdateCardLimitsResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *UpdateCardLimitsResponse) GetMaskedCardNumber() string {
	if x != nil {
		return x.MaskedCardNumber
	}
	return ""
}

func (x *UpdateCardLimitsResponse) GetDailyAllowedLimits() *AllowedLimits {
	if x != nil {
		return x.DailyAllowedLimits
	}
	return nil
}

func (x *UpdateCardLimitsResponse) GetInternalStatusCode() string {
	if x != nil {
		return x.InternalStatusCode
	}
	return ""
}

type AllowedLimits struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// max limit for card
	CardMaxLimit *money.Money `protobuf:"bytes,1,opt,name=card_max_limit,json=cardMaxLimit,proto3" json:"card_max_limit,omitempty"`
	// max purchase amount that can be set.
	// Purchase limit is a combined limit for POS, NFC, ECOM. It also includes both domestic and international spending.
	PurchaseMaxLimit *money.Money `protobuf:"bytes,2,opt,name=purchase_max_limit,json=purchaseMaxLimit,proto3" json:"purchase_max_limit,omitempty"`
	// max atm amount that can be set.
	AtmMaxLimit *money.Money `protobuf:"bytes,3,opt,name=atm_max_limit,json=atmMaxLimit,proto3" json:"atm_max_limit,omitempty"`
}

func (x *AllowedLimits) Reset() {
	*x = AllowedLimits{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllowedLimits) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllowedLimits) ProtoMessage() {}

func (x *AllowedLimits) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllowedLimits.ProtoReflect.Descriptor instead.
func (*AllowedLimits) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{34}
}

func (x *AllowedLimits) GetCardMaxLimit() *money.Money {
	if x != nil {
		return x.CardMaxLimit
	}
	return nil
}

func (x *AllowedLimits) GetPurchaseMaxLimit() *money.Money {
	if x != nil {
		return x.PurchaseMaxLimit
	}
	return nil
}

func (x *AllowedLimits) GetAtmMaxLimit() *money.Money {
	if x != nil {
		return x.AtmMaxLimit
	}
	return nil
}

type FetchCardLimitsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Card-id of enquired card.
	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *FetchCardLimitsRequest) Reset() {
	*x = FetchCardLimitsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchCardLimitsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchCardLimitsRequest) ProtoMessage() {}

func (x *FetchCardLimitsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchCardLimitsRequest.ProtoReflect.Descriptor instead.
func (*FetchCardLimitsRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{35}
}

func (x *FetchCardLimitsRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

type FetchCardLimitsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// card limits
	CardLimitData *CardLimitData `protobuf:"bytes,2,opt,name=card_limit_data,json=cardLimitData,proto3" json:"card_limit_data,omitempty"`
}

func (x *FetchCardLimitsResponse) Reset() {
	*x = FetchCardLimitsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchCardLimitsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchCardLimitsResponse) ProtoMessage() {}

func (x *FetchCardLimitsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchCardLimitsResponse.ProtoReflect.Descriptor instead.
func (*FetchCardLimitsResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{36}
}

func (x *FetchCardLimitsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchCardLimitsResponse) GetCardLimitData() *CardLimitData {
	if x != nil {
		return x.CardLimitData
	}
	return nil
}

type ConsolidatedCardControlOnOffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier of the card for which controls needs to be changed
	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// map<CardControlType, CardControlAction>. Specifies the action to be performed for each control
	ControlActions map[int32]card.CardControlAction `protobuf:"bytes,2,rep,name=control_actions,json=controlActions,proto3" json:"control_actions,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=card.CardControlAction"`
	// unique identifier for the request
	RequestId string `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// encrypted pin. Required when any action is to be enabled
	CredBlock string `protobuf:"bytes,4,opt,name=cred_block,json=credBlock,proto3" json:"cred_block,omitempty"`
	// workflow for card control request.
	// We will pass CARD_CTRL_TOKEN_WF when we enable e-commerce during setting card pin in background
	ControlWorkflow CardControlWorkflow `protobuf:"varint,5,opt,name=control_workflow,json=controlWorkflow,proto3,enum=card.control.CardControlWorkflow" json:"control_workflow,omitempty"`
	// Workflow from where the request was initiated
	// This is to not initiate any sort of communication to user if done from internal process
	ControlActionWorkflow ControlActionWorkflow `protobuf:"varint,6,opt,name=control_action_workflow,json=controlActionWorkflow,proto3,enum=card.control.ControlActionWorkflow" json:"control_action_workflow,omitempty"`
}

func (x *ConsolidatedCardControlOnOffRequest) Reset() {
	*x = ConsolidatedCardControlOnOffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsolidatedCardControlOnOffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsolidatedCardControlOnOffRequest) ProtoMessage() {}

func (x *ConsolidatedCardControlOnOffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsolidatedCardControlOnOffRequest.ProtoReflect.Descriptor instead.
func (*ConsolidatedCardControlOnOffRequest) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{37}
}

func (x *ConsolidatedCardControlOnOffRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *ConsolidatedCardControlOnOffRequest) GetControlActions() map[int32]card.CardControlAction {
	if x != nil {
		return x.ControlActions
	}
	return nil
}

func (x *ConsolidatedCardControlOnOffRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ConsolidatedCardControlOnOffRequest) GetCredBlock() string {
	if x != nil {
		return x.CredBlock
	}
	return ""
}

func (x *ConsolidatedCardControlOnOffRequest) GetControlWorkflow() CardControlWorkflow {
	if x != nil {
		return x.ControlWorkflow
	}
	return CardControlWorkflow_CARD_CTRL_WF_UNSPECIFIED
}

func (x *ConsolidatedCardControlOnOffRequest) GetControlActionWorkflow() ControlActionWorkflow {
	if x != nil {
		return x.ControlActionWorkflow
	}
	return ControlActionWorkflow_CONTROL_ACTION_WORKFLOW_UNSPECIFIED
}

type ConsolidatedCardControlOnOffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Internal response code returned by VG API corresponding to vendor returned code.
	// We will use this status code to convert error response to client error view which will be shown on the UI.
	InternalResponseCode string `protobuf:"bytes,2,opt,name=internal_response_code,json=internalResponseCode,proto3" json:"internal_response_code,omitempty"`
}

func (x *ConsolidatedCardControlOnOffResponse) Reset() {
	*x = ConsolidatedCardControlOnOffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsolidatedCardControlOnOffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsolidatedCardControlOnOffResponse) ProtoMessage() {}

func (x *ConsolidatedCardControlOnOffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsolidatedCardControlOnOffResponse.ProtoReflect.Descriptor instead.
func (*ConsolidatedCardControlOnOffResponse) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{38}
}

func (x *ConsolidatedCardControlOnOffResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ConsolidatedCardControlOnOffResponse) GetInternalResponseCode() string {
	if x != nil {
		return x.InternalResponseCode
	}
	return ""
}

type GetInternationalAtmLimitsRequest_CountryCodes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Codes []string `protobuf:"bytes,1,rep,name=codes,proto3" json:"codes,omitempty"`
}

func (x *GetInternationalAtmLimitsRequest_CountryCodes) Reset() {
	*x = GetInternationalAtmLimitsRequest_CountryCodes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInternationalAtmLimitsRequest_CountryCodes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInternationalAtmLimitsRequest_CountryCodes) ProtoMessage() {}

func (x *GetInternationalAtmLimitsRequest_CountryCodes) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInternationalAtmLimitsRequest_CountryCodes.ProtoReflect.Descriptor instead.
func (*GetInternationalAtmLimitsRequest_CountryCodes) Descriptor() ([]byte, []int) {
	return file_api_card_control_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *GetInternationalAtmLimitsRequest_CountryCodes) GetCodes() []string {
	if x != nil {
		return x.Codes
	}
	return nil
}

var File_api_card_control_service_proto protoreflect.FileDescriptor

var file_api_card_control_service_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x1a, 0x13,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64,
	0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e,
	0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xb2, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c,
	0x0a, 0x12, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x48, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x22, 0x67, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x54, 0x72, 0x61,
	0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x76, 0x65,
	0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x69, 0x73, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x4f, 0x6e, 0x22,
	0x3a, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xb5, 0x01, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x11, 0x69, 0x73,
	0x5f, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d,
	0x6f, 0x64, 0x65, 0x4f, 0x6e, 0x12, 0x4c, 0x0a, 0x0f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x52, 0x0e, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x22, 0xeb, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x74, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x62, 0x0a, 0x0d, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x47,
	0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41,
	0x74, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x48, 0x00, 0x52, 0x0c,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x07,
	0x67, 0x65, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52,
	0x06, 0x67, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x1a, 0x24, 0x0a, 0x0c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x47, 0x65, 0x74, 0x42,
	0x79, 0x22, 0x9d, 0x02, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x74, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5d, 0x0a, 0x18,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x74,
	0x6d, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x74, 0x6d, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x52, 0x16, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x41, 0x74, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x48, 0x0a, 0x21, 0x69,
	0x73, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1d, 0x69, 0x73, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0xcd, 0x01, 0x0a, 0x15, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x41, 0x74, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x66, 0x6c, 0x61,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x46, 0x6c, 0x61, 0x67, 0x12, 0x4b, 0x0a, 0x18, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x74, 0x6d, 0x5f,
	0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x15, 0x6d, 0x61, 0x78, 0x41,
	0x74, 0x6d, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x22, 0xf8, 0x01, 0x0a, 0x10, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x73, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x12, 0x2a, 0x0a, 0x11, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x15,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x13, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x73, 0x6b,
	0x69, 0x70, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x22, 0xdd, 0x01, 0x0a,
	0x11, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x56, 0x0a, 0x0d, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x65, 0x64, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x73, 0x1a,
	0x4b, 0x0a, 0x11, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x20, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xaa, 0x02, 0x0a,
	0x12, 0x53, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x73, 0x12, 0x2f,
	0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x5b, 0x0a, 0x17,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x96, 0x01, 0x0a, 0x11, 0x53, 0x75,
	0x73, 0x70, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1e, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12,
	0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f,
	0x64, 0x65, 0x22, 0x88, 0x03, 0x0a, 0x13, 0x53, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x43, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x5b, 0x0a, 0x0e, 0x73, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x53, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x43, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x75, 0x73, 0x70, 0x65,
	0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x73,
	0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x1a, 0x61, 0x0a, 0x12,
	0x53, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x35, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x53, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x8b, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x45, 0x4e, 0x54, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x64, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52,
	0x4d, 0x41, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x65,
	0x12, 0x19, 0x0a, 0x14, 0x50, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x5f,
	0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0xc9, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x49,
	0x4e, 0x10, 0xca, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x52, 0x45, 0x44, 0x5f, 0x42, 0x4c, 0x4f,
	0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0xcb, 0x01, 0x22, 0xe3, 0x02,
	0x0a, 0x14, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x73, 0x12, 0x35, 0x0a, 0x07, 0x6c, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x55, 0x73,
	0x61, 0x67, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x07, 0x6c, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x64,
	0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x5b, 0x0a, 0x17, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x15, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x22, 0x9c, 0x01, 0x0a, 0x17, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1e, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12,
	0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f,
	0x64, 0x65, 0x22, 0xac, 0x03, 0x0a, 0x15, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f,
	0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x71, 0x0a, 0x16, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x6e,
	0x5f, 0x6f, 0x66, 0x66, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3c, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f,
	0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x13, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x73, 0x1a, 0x6d, 0x0a, 0x18, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x3b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x8b, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49,
	0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x64, 0x12, 0x15, 0x0a,
	0x11, 0x50, 0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55,
	0x52, 0x45, 0x10, 0x65, 0x12, 0x19, 0x0a, 0x14, 0x50, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x54, 0x52,
	0x49, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0xc9, 0x01, 0x12,
	0x17, 0x0a, 0x12, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52,
	0x45, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0xca, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x52, 0x45, 0x44,
	0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0xcb,
	0x01, 0x22, 0xfc, 0x02, 0x0a, 0x15, 0x45, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x4f,
	0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x61, 0x72, 0x64, 0x49, 0x64, 0x73, 0x12, 0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x64,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x11, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x21, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43,
	0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x52, 0x0f, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x46,
	0x6c, 0x6f, 0x77, 0x12, 0x5b, 0x0a, 0x17, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x22, 0x9d, 0x01, 0x0a, 0x18, 0x45, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x4f, 0x6e,
	0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a,
	0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12, 0x2f, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30,
	0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65,
	0x22, 0xa3, 0x03, 0x0a, 0x16, 0x45, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x4f, 0x6e,
	0x4f, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x69, 0x0a, 0x13, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x5f, 0x6f, 0x6e, 0x5f, 0x6f, 0x66, 0x66,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x45, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x45, 0x63, 0x6f, 0x6d, 0x6d, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x1a, 0x6b, 0x0a, 0x15, 0x45,
	0x63, 0x6f, 0x6d, 0x6d, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x45, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x4f, 0x6e,
	0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8b, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x10, 0x64, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45, 0x4e, 0x54, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x65, 0x12, 0x19, 0x0a, 0x14, 0x50, 0x49, 0x4e,
	0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45,
	0x44, 0x10, 0xc9, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0xca, 0x01, 0x12, 0x17, 0x0a,
	0x12, 0x43, 0x52, 0x45, 0x44, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x55, 0x52, 0x45, 0x10, 0xcb, 0x01, 0x22, 0xf6, 0x01, 0x0a, 0x0f, 0x41, 0x54, 0x4d, 0x4f, 0x6e,
	0x4f, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x64, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x5b, 0x0a, 0x17, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22,
	0x90, 0x02, 0x0a, 0x10, 0x41, 0x54, 0x4d, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x10, 0x61, 0x74, 0x6d,
	0x5f, 0x6f, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x41, 0x54, 0x4d, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x61, 0x74, 0x6d, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x22, 0x8b, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x49, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x64, 0x12, 0x15,
	0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x55, 0x52, 0x45, 0x10, 0x65, 0x12, 0x19, 0x0a, 0x14, 0x50, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x54,
	0x52, 0x49, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0xc9, 0x01,
	0x12, 0x17, 0x0a, 0x12, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x45, 0x43, 0x55,
	0x52, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0xca, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x52, 0x45,
	0x44, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10,
	0xcb, 0x01, 0x22, 0x97, 0x01, 0x0a, 0x12, 0x41, 0x54, 0x4d, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x04, 0x63, 0x61, 0x72,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xf6, 0x01, 0x0a,
	0x0f, 0x50, 0x4f, 0x53, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x5b, 0x0a, 0x17, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x15,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x90, 0x02, 0x0a, 0x10, 0x50, 0x4f, 0x53, 0x4f, 0x6e, 0x4f,
	0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x49, 0x0a, 0x10, 0x70, 0x6f, 0x73, 0x5f, 0x6f, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x50, 0x4f, 0x53, 0x4f, 0x6e, 0x4f, 0x66,
	0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x70, 0x6f, 0x73,
	0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x8b, 0x01, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x15, 0x0a,
	0x11, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55,
	0x52, 0x45, 0x10, 0x64, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45, 0x4e,
	0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x65, 0x12, 0x19, 0x0a, 0x14, 0x50,
	0x49, 0x4e, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45,
	0x44, 0x45, 0x44, 0x10, 0xc9, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0xca, 0x01, 0x12,
	0x17, 0x0a, 0x12, 0x43, 0x52, 0x45, 0x44, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0xcb, 0x01, 0x22, 0x97, 0x01, 0x0a, 0x12, 0x50, 0x4f, 0x53,
	0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1e, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12,
	0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f,
	0x64, 0x65, 0x22, 0xa7, 0x02, 0x0a, 0x0f, 0x4e, 0x66, 0x63, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x73, 0x12, 0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x5b, 0x0a, 0x17, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x97, 0x01, 0x0a,
	0x12, 0x4e, 0x66, 0x63, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x04, 0x63,
	0x61, 0x72, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x89, 0x03, 0x0a, 0x10, 0x4e, 0x66, 0x63, 0x4f, 0x6e,
	0x4f, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x5d, 0x0a, 0x11, 0x6e, 0x66, 0x63, 0x5f, 0x6f, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x4e, 0x66, 0x63, 0x4f, 0x6e,
	0x4f, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4e, 0x66, 0x63, 0x4f,
	0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0e, 0x6e, 0x66, 0x63, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x1a,
	0x63, 0x0a, 0x13, 0x4e, 0x66, 0x63, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x36, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x4e, 0x66, 0x63, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x8b, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x49, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x64, 0x12, 0x15,
	0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x55, 0x52, 0x45, 0x10, 0x65, 0x12, 0x19, 0x0a, 0x14, 0x50, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x54,
	0x52, 0x49, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0xc9, 0x01,
	0x12, 0x17, 0x0a, 0x12, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x45, 0x43, 0x55,
	0x52, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0xca, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x52, 0x45,
	0x44, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10,
	0xcb, 0x01, 0x22, 0xbc, 0x02, 0x0a, 0x13, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x6e,
	0x4f, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61,
	0x72, 0x64, 0x49, 0x64, 0x73, 0x12, 0x36, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43,
	0x61, 0x72, 0x64, 0x55, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x6c, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a,
	0x08, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x19, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x74, 0x78, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x22, 0x69, 0x0a, 0x16, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x6e, 0x4f, 0x66,
	0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x04, 0x63,
	0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x97, 0x02, 0x0a,
	0x14, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x6d, 0x0a, 0x15, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x6f, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x6e,
	0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x1a, 0x6b, 0x0a, 0x17, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x6e, 0x4f, 0x66,
	0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x2f, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x22, 0xfe, 0x02, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43,
	0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x63, 0x61,
	0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x61,
	0x73, 0x6b, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x43, 0x61,
	0x72, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x14, 0x64, 0x61, 0x69, 0x6c,
	0x79, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x73, 0x52, 0x12, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e,
	0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xa4, 0x02, 0x0a, 0x17, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x58, 0x0a, 0x19, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x16, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x5f, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x10, 0x69, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61,
	0x73, 0x65, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f,
	0x69, 0x73, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x22,
	0x96, 0x04, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x43, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x14, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x12,
	0x64, 0x61, 0x69, 0x6c, 0x79, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x43, 0x6f, 0x64, 0x65, 0x22, 0xc1, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x0d, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x50,
	0x52, 0x45, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x15, 0x0a,
	0x11, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55,
	0x52, 0x45, 0x10, 0x64, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45, 0x4e,
	0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x65, 0x12, 0x19, 0x0a, 0x14, 0x50,
	0x49, 0x4e, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45,
	0x44, 0x45, 0x44, 0x10, 0xc9, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0xca, 0x01, 0x12,
	0x17, 0x0a, 0x12, 0x43, 0x52, 0x45, 0x44, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0xcb, 0x01, 0x22, 0xc3, 0x01, 0x0a, 0x0d, 0x41, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x38, 0x0a, 0x0e, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x78, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x40, 0x0a, 0x12, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4d, 0x61,
	0x78, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x36, 0x0a, 0x0d, 0x61, 0x74, 0x6d, 0x5f, 0x6d, 0x61,
	0x78, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0b, 0x61, 0x74, 0x6d, 0x4d, 0x61, 0x78, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x31,
	0x0a, 0x16, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49,
	0x64, 0x22, 0x95, 0x01, 0x0a, 0x17, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43, 0x61, 0x72, 0x64, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x43, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x10, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x22, 0xf3, 0x03, 0x0a, 0x23, 0x43, 0x6f,
	0x6e, 0x73, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x6e, 0x0a, 0x0f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x64, 0x43,
	0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x4c, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x5b, 0x0a, 0x17, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x15, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x1a, 0x5a, 0x0a, 0x13, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xe4, 0x01, 0x0a, 0x24, 0x43, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x6e, 0x4f, 0x66, 0x66,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a,
	0x16, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x22, 0x61, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c,
	0x10, 0x0d, 0x12, 0x19, 0x0a, 0x14, 0x50, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45,
	0x53, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0xc9, 0x01, 0x12, 0x17, 0x0a,
	0x12, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f,
	0x50, 0x49, 0x4e, 0x10, 0xca, 0x01, 0x2a, 0x68, 0x0a, 0x13, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x1c, 0x0a,
	0x18, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x54, 0x52, 0x4c, 0x5f, 0x57, 0x46, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x43, 0x54, 0x52, 0x4c, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f,
	0x50, 0x49, 0x4e, 0x5f, 0x57, 0x46, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x43, 0x54, 0x52, 0x4c, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x57, 0x46, 0x10, 0x02,
	0x2a, 0x62, 0x0a, 0x15, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x4f, 0x4e,
	0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x4f, 0x52, 0x4b,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49,
	0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x02, 0x32, 0x8d, 0x0b, 0x0a, 0x0b, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x12, 0x4e, 0x0a, 0x09, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x61, 0x72,
	0x64, 0x12, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x54, 0x0a, 0x0b, 0x53, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x43,
	0x61, 0x72, 0x64, 0x12, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x53, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x53, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0d, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x12, 0x22, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0e, 0x45, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x72, 0x63, 0x65, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x12, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x45, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63,
	0x65, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x45, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x08, 0x4e, 0x66, 0x63, 0x4f, 0x6e, 0x4f, 0x66,
	0x66, 0x12, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x4e, 0x66, 0x63, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e,
	0x4e, 0x66, 0x63, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x57, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x6e, 0x4f,
	0x66, 0x66, 0x12, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x6e, 0x4f, 0x66,
	0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0d, 0x47,
	0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x22, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x25, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x08,
	0x41, 0x54, 0x4d, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x12, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x41, 0x54, 0x4d, 0x4f, 0x6e, 0x4f, 0x66, 0x66,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x41, 0x54, 0x4d, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x08, 0x50, 0x4f, 0x53,
	0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x12, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x50, 0x4f, 0x53, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x2e, 0x50, 0x4f, 0x53, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x0f, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43,
	0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43, 0x61,
	0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x25, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x1c, 0x43, 0x6f, 0x6e,
	0x73, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x12, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x6f, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x6f, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x7c, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x74, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12,
	0x2e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x47,
	0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41,
	0x74, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x47,
	0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41,
	0x74, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x58, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x53, 0x65, 0x74, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x53, 0x65, 0x74, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x6f,
	0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x0d, 0x47, 0x65,
	0x74, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72,
	0x61, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x47,
	0x65, 0x74, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x52, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5a,
	0x27, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64,
	0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_control_service_proto_rawDescOnce sync.Once
	file_api_card_control_service_proto_rawDescData = file_api_card_control_service_proto_rawDesc
)

func file_api_card_control_service_proto_rawDescGZIP() []byte {
	file_api_card_control_service_proto_rawDescOnce.Do(func() {
		file_api_card_control_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_control_service_proto_rawDescData)
	})
	return file_api_card_control_service_proto_rawDescData
}

var file_api_card_control_service_proto_enumTypes = make([]protoimpl.EnumInfo, 12)
var file_api_card_control_service_proto_msgTypes = make([]protoimpl.MessageInfo, 47)
var file_api_card_control_service_proto_goTypes = []interface{}{
	(CardControlWorkflow)(0),                              // 0: card.control.CardControlWorkflow
	(ControlActionWorkflow)(0),                            // 1: card.control.ControlActionWorkflow
	(SuspendCardResponse_Status)(0),                       // 2: card.control.SuspendCardResponse.Status
	(LocationOnOffResponse_Status)(0),                     // 3: card.control.LocationOnOffResponse.Status
	(ECommerceOnOffResponse_Status)(0),                    // 4: card.control.ECommerceOnOffResponse.Status
	(ATMOnOffResponse_Status)(0),                          // 5: card.control.ATMOnOffResponse.Status
	(POSOnOffResponse_Status)(0),                          // 6: card.control.POSOnOffResponse.Status
	(NfcOnOffResponse_Status)(0),                          // 7: card.control.NfcOnOffResponse.Status
	(GetCardLimitsResponse_Status)(0),                     // 8: card.control.GetCardLimitsResponse.Status
	(UpdateCardLimitsResponse_Status)(0),                  // 9: card.control.UpdateCardLimitsResponse.Status
	(FetchCardLimitsResponse_Status)(0),                   // 10: card.control.FetchCardLimitsResponse.Status
	(ConsolidatedCardControlOnOffResponse_Status)(0),      // 11: card.control.ConsolidatedCardControlOnOffResponse.Status
	(*SetTravelModeRequest)(nil),                          // 12: card.control.SetTravelModeRequest
	(*SetTravelModeResponse)(nil),                         // 13: card.control.SetTravelModeResponse
	(*GetTravelModeRequest)(nil),                          // 14: card.control.GetTravelModeRequest
	(*GetTravelModeResponse)(nil),                         // 15: card.control.GetTravelModeResponse
	(*GetInternationalAtmLimitsRequest)(nil),              // 16: card.control.GetInternationalAtmLimitsRequest
	(*GetInternationalAtmLimitsResponse)(nil),             // 17: card.control.GetInternationalAtmLimitsResponse
	(*InternationalAtmLimit)(nil),                         // 18: card.control.InternationalAtmLimit
	(*BlockCardRequest)(nil),                              // 19: card.control.BlockCardRequest
	(*BlockCardResponse)(nil),                             // 20: card.control.BlockCardResponse
	(*SuspendCardRequest)(nil),                            // 21: card.control.SuspendCardRequest
	(*SuspendStatesInfo)(nil),                             // 22: card.control.SuspendStatesInfo
	(*SuspendCardResponse)(nil),                           // 23: card.control.SuspendCardResponse
	(*LocationOnOffRequest)(nil),                          // 24: card.control.LocationOnOffRequest
	(*LocationOnOffStatesInfo)(nil),                       // 25: card.control.LocationOnOffStatesInfo
	(*LocationOnOffResponse)(nil),                         // 26: card.control.LocationOnOffResponse
	(*ECommerceOnOffRequest)(nil),                         // 27: card.control.ECommerceOnOffRequest
	(*ECommerceOnOffStatesInfo)(nil),                      // 28: card.control.ECommerceOnOffStatesInfo
	(*ECommerceOnOffResponse)(nil),                        // 29: card.control.ECommerceOnOffResponse
	(*ATMOnOffRequest)(nil),                               // 30: card.control.ATMOnOffRequest
	(*ATMOnOffResponse)(nil),                              // 31: card.control.ATMOnOffResponse
	(*ATMOnOffStatesInfo)(nil),                            // 32: card.control.ATMOnOffStatesInfo
	(*POSOnOffRequest)(nil),                               // 33: card.control.POSOnOffRequest
	(*POSOnOffResponse)(nil),                              // 34: card.control.POSOnOffResponse
	(*POSOnOffStatesInfo)(nil),                            // 35: card.control.POSOnOffStatesInfo
	(*NfcOnOffRequest)(nil),                               // 36: card.control.NfcOnOffRequest
	(*NfcOnOffStatesInfo)(nil),                            // 37: card.control.NfcOnOffStatesInfo
	(*NfcOnOffResponse)(nil),                              // 38: card.control.NfcOnOffResponse
	(*ControlOnOffRequest)(nil),                           // 39: card.control.ControlOnOffRequest
	(*ControlOnOffStatesInfo)(nil),                        // 40: card.control.ControlOnOffStatesInfo
	(*ControlOnOffResponse)(nil),                          // 41: card.control.ControlOnOffResponse
	(*GetCardLimitsRequest)(nil),                          // 42: card.control.GetCardLimitsRequest
	(*GetCardLimitsResponse)(nil),                         // 43: card.control.GetCardLimitsResponse
	(*UpdateCardLimitsRequest)(nil),                       // 44: card.control.UpdateCardLimitsRequest
	(*UpdateCardLimitsResponse)(nil),                      // 45: card.control.UpdateCardLimitsResponse
	(*AllowedLimits)(nil),                                 // 46: card.control.AllowedLimits
	(*FetchCardLimitsRequest)(nil),                        // 47: card.control.FetchCardLimitsRequest
	(*FetchCardLimitsResponse)(nil),                       // 48: card.control.FetchCardLimitsResponse
	(*ConsolidatedCardControlOnOffRequest)(nil),           // 49: card.control.ConsolidatedCardControlOnOffRequest
	(*ConsolidatedCardControlOnOffResponse)(nil),          // 50: card.control.ConsolidatedCardControlOnOffResponse
	(*GetInternationalAtmLimitsRequest_CountryCodes)(nil), // 51: card.control.GetInternationalAtmLimitsRequest.CountryCodes
	nil,                             // 52: card.control.BlockCardResponse.BlockedCardsEntry
	nil,                             // 53: card.control.SuspendCardResponse.SuspendStatesEntry
	nil,                             // 54: card.control.LocationOnOffResponse.LocationOnOffStatesEntry
	nil,                             // 55: card.control.ECommerceOnOffResponse.EcommOnOffStatesEntry
	nil,                             // 56: card.control.NfcOnOffResponse.NfcOnOffStatesEntry
	nil,                             // 57: card.control.ControlOnOffResponse.ControlOnOffStatesEntry
	nil,                             // 58: card.control.ConsolidatedCardControlOnOffRequest.ControlActionsEntry
	(*rpc.Status)(nil),              // 59: rpc.Status
	(*money.Money)(nil),             // 60: google.type.Money
	(vendorgateway.Vendor)(0),       // 61: vendorgateway.Vendor
	(card.Provenance)(0),            // 62: card.Provenance
	(card.CardControlAction)(0),     // 63: card.CardControlAction
	(*card.Card)(nil),               // 64: card.Card
	(card.CardUsageLocationType)(0), // 65: card.CardUsageLocationType
	(card.CardTransactionType)(0),   // 66: card.CardTransactionType
	(*CardLimitData)(nil),           // 67: card.control.CardLimitData
	(*CardLimitDetail)(nil),         // 68: card.control.CardLimitDetail
}
var file_api_card_control_service_proto_depIdxs = []int32{
	1,   // 0: card.control.SetTravelModeRequest.workflow_type:type_name -> card.control.ControlActionWorkflow
	59,  // 1: card.control.SetTravelModeResponse.status:type_name -> rpc.Status
	59,  // 2: card.control.GetTravelModeResponse.status:type_name -> rpc.Status
	1,   // 3: card.control.GetTravelModeResponse.enable_workflow:type_name -> card.control.ControlActionWorkflow
	51,  // 4: card.control.GetInternationalAtmLimitsRequest.country_codes:type_name -> card.control.GetInternationalAtmLimitsRequest.CountryCodes
	59,  // 5: card.control.GetInternationalAtmLimitsResponse.status:type_name -> rpc.Status
	18,  // 6: card.control.GetInternationalAtmLimitsResponse.international_atm_limits:type_name -> card.control.InternationalAtmLimit
	60,  // 7: card.control.InternationalAtmLimit.max_atm_withdrawal_limit:type_name -> google.type.Money
	61,  // 8: card.control.BlockCardRequest.vendor:type_name -> vendorgateway.Vendor
	62,  // 9: card.control.BlockCardRequest.block_card_provenance:type_name -> card.Provenance
	59,  // 10: card.control.BlockCardResponse.status:type_name -> rpc.Status
	52,  // 11: card.control.BlockCardResponse.blocked_cards:type_name -> card.control.BlockCardResponse.BlockedCardsEntry
	63,  // 12: card.control.SuspendCardRequest.action:type_name -> card.CardControlAction
	61,  // 13: card.control.SuspendCardRequest.vendor:type_name -> vendorgateway.Vendor
	1,   // 14: card.control.SuspendCardRequest.control_action_workflow:type_name -> card.control.ControlActionWorkflow
	64,  // 15: card.control.SuspendStatesInfo.card:type_name -> card.Card
	63,  // 16: card.control.SuspendStatesInfo.action:type_name -> card.CardControlAction
	59,  // 17: card.control.SuspendCardResponse.status:type_name -> rpc.Status
	53,  // 18: card.control.SuspendCardResponse.suspend_states:type_name -> card.control.SuspendCardResponse.SuspendStatesEntry
	65,  // 19: card.control.LocationOnOffRequest.locType:type_name -> card.CardUsageLocationType
	63,  // 20: card.control.LocationOnOffRequest.action:type_name -> card.CardControlAction
	61,  // 21: card.control.LocationOnOffRequest.vendor:type_name -> vendorgateway.Vendor
	1,   // 22: card.control.LocationOnOffRequest.control_action_workflow:type_name -> card.control.ControlActionWorkflow
	64,  // 23: card.control.LocationOnOffStatesInfo.card:type_name -> card.Card
	63,  // 24: card.control.LocationOnOffStatesInfo.action:type_name -> card.CardControlAction
	59,  // 25: card.control.LocationOnOffResponse.status:type_name -> rpc.Status
	54,  // 26: card.control.LocationOnOffResponse.location_on_off_states:type_name -> card.control.LocationOnOffResponse.LocationOnOffStatesEntry
	63,  // 27: card.control.ECommerceOnOffRequest.action:type_name -> card.CardControlAction
	61,  // 28: card.control.ECommerceOnOffRequest.vendor:type_name -> vendorgateway.Vendor
	0,   // 29: card.control.ECommerceOnOffRequest.ecomm_enable_flow:type_name -> card.control.CardControlWorkflow
	1,   // 30: card.control.ECommerceOnOffRequest.control_action_workflow:type_name -> card.control.ControlActionWorkflow
	64,  // 31: card.control.ECommerceOnOffStatesInfo.card:type_name -> card.Card
	63,  // 32: card.control.ECommerceOnOffStatesInfo.action:type_name -> card.CardControlAction
	59,  // 33: card.control.ECommerceOnOffResponse.status:type_name -> rpc.Status
	55,  // 34: card.control.ECommerceOnOffResponse.ecomm_on_off_states:type_name -> card.control.ECommerceOnOffResponse.EcommOnOffStatesEntry
	63,  // 35: card.control.ATMOnOffRequest.action:type_name -> card.CardControlAction
	1,   // 36: card.control.ATMOnOffRequest.control_action_workflow:type_name -> card.control.ControlActionWorkflow
	59,  // 37: card.control.ATMOnOffResponse.status:type_name -> rpc.Status
	32,  // 38: card.control.ATMOnOffResponse.atm_on_off_state:type_name -> card.control.ATMOnOffStatesInfo
	64,  // 39: card.control.ATMOnOffStatesInfo.card:type_name -> card.Card
	63,  // 40: card.control.ATMOnOffStatesInfo.action:type_name -> card.CardControlAction
	63,  // 41: card.control.POSOnOffRequest.action:type_name -> card.CardControlAction
	1,   // 42: card.control.POSOnOffRequest.control_action_workflow:type_name -> card.control.ControlActionWorkflow
	59,  // 43: card.control.POSOnOffResponse.status:type_name -> rpc.Status
	35,  // 44: card.control.POSOnOffResponse.pos_on_off_state:type_name -> card.control.POSOnOffStatesInfo
	64,  // 45: card.control.POSOnOffStatesInfo.card:type_name -> card.Card
	63,  // 46: card.control.POSOnOffStatesInfo.action:type_name -> card.CardControlAction
	63,  // 47: card.control.NfcOnOffRequest.action:type_name -> card.CardControlAction
	61,  // 48: card.control.NfcOnOffRequest.vendor:type_name -> vendorgateway.Vendor
	1,   // 49: card.control.NfcOnOffRequest.control_action_workflow:type_name -> card.control.ControlActionWorkflow
	64,  // 50: card.control.NfcOnOffStatesInfo.card:type_name -> card.Card
	63,  // 51: card.control.NfcOnOffStatesInfo.action:type_name -> card.CardControlAction
	59,  // 52: card.control.NfcOnOffResponse.status:type_name -> rpc.Status
	56,  // 53: card.control.NfcOnOffResponse.nfc_on_off_states:type_name -> card.control.NfcOnOffResponse.NfcOnOffStatesEntry
	65,  // 54: card.control.ControlOnOffRequest.loc_type:type_name -> card.CardUsageLocationType
	66,  // 55: card.control.ControlOnOffRequest.txn_type:type_name -> card.CardTransactionType
	63,  // 56: card.control.ControlOnOffRequest.action:type_name -> card.CardControlAction
	61,  // 57: card.control.ControlOnOffRequest.vendor:type_name -> vendorgateway.Vendor
	64,  // 58: card.control.ControlOnOffStatesInfo.card:type_name -> card.Card
	63,  // 59: card.control.ControlOnOffStatesInfo.action:type_name -> card.CardControlAction
	59,  // 60: card.control.ControlOnOffResponse.status:type_name -> rpc.Status
	57,  // 61: card.control.ControlOnOffResponse.control_on_off_states:type_name -> card.control.ControlOnOffResponse.ControlOnOffStatesEntry
	59,  // 62: card.control.GetCardLimitsResponse.status:type_name -> rpc.Status
	67,  // 63: card.control.GetCardLimitsResponse.card_limit_data:type_name -> card.control.CardLimitData
	46,  // 64: card.control.GetCardLimitsResponse.daily_allowed_limits:type_name -> card.control.AllowedLimits
	68,  // 65: card.control.UpdateCardLimitsRequest.update_card_limit_details:type_name -> card.control.CardLimitDetail
	59,  // 66: card.control.UpdateCardLimitsResponse.status:type_name -> rpc.Status
	67,  // 67: card.control.UpdateCardLimitsResponse.card_limit_data:type_name -> card.control.CardLimitData
	46,  // 68: card.control.UpdateCardLimitsResponse.daily_allowed_limits:type_name -> card.control.AllowedLimits
	60,  // 69: card.control.AllowedLimits.card_max_limit:type_name -> google.type.Money
	60,  // 70: card.control.AllowedLimits.purchase_max_limit:type_name -> google.type.Money
	60,  // 71: card.control.AllowedLimits.atm_max_limit:type_name -> google.type.Money
	59,  // 72: card.control.FetchCardLimitsResponse.status:type_name -> rpc.Status
	67,  // 73: card.control.FetchCardLimitsResponse.card_limit_data:type_name -> card.control.CardLimitData
	58,  // 74: card.control.ConsolidatedCardControlOnOffRequest.control_actions:type_name -> card.control.ConsolidatedCardControlOnOffRequest.ControlActionsEntry
	0,   // 75: card.control.ConsolidatedCardControlOnOffRequest.control_workflow:type_name -> card.control.CardControlWorkflow
	1,   // 76: card.control.ConsolidatedCardControlOnOffRequest.control_action_workflow:type_name -> card.control.ControlActionWorkflow
	59,  // 77: card.control.ConsolidatedCardControlOnOffResponse.status:type_name -> rpc.Status
	64,  // 78: card.control.BlockCardResponse.BlockedCardsEntry.value:type_name -> card.Card
	22,  // 79: card.control.SuspendCardResponse.SuspendStatesEntry.value:type_name -> card.control.SuspendStatesInfo
	25,  // 80: card.control.LocationOnOffResponse.LocationOnOffStatesEntry.value:type_name -> card.control.LocationOnOffStatesInfo
	28,  // 81: card.control.ECommerceOnOffResponse.EcommOnOffStatesEntry.value:type_name -> card.control.ECommerceOnOffStatesInfo
	37,  // 82: card.control.NfcOnOffResponse.NfcOnOffStatesEntry.value:type_name -> card.control.NfcOnOffStatesInfo
	40,  // 83: card.control.ControlOnOffResponse.ControlOnOffStatesEntry.value:type_name -> card.control.ControlOnOffStatesInfo
	63,  // 84: card.control.ConsolidatedCardControlOnOffRequest.ControlActionsEntry.value:type_name -> card.CardControlAction
	19,  // 85: card.control.CardControl.BlockCard:input_type -> card.control.BlockCardRequest
	21,  // 86: card.control.CardControl.SuspendCard:input_type -> card.control.SuspendCardRequest
	24,  // 87: card.control.CardControl.LocationOnOff:input_type -> card.control.LocationOnOffRequest
	27,  // 88: card.control.CardControl.ECommerceOnOff:input_type -> card.control.ECommerceOnOffRequest
	36,  // 89: card.control.CardControl.NfcOnOff:input_type -> card.control.NfcOnOffRequest
	39,  // 90: card.control.CardControl.ControlOnOff:input_type -> card.control.ControlOnOffRequest
	42,  // 91: card.control.CardControl.GetCardLimits:input_type -> card.control.GetCardLimitsRequest
	44,  // 92: card.control.CardControl.UpdateCardLimits:input_type -> card.control.UpdateCardLimitsRequest
	30,  // 93: card.control.CardControl.ATMOnOff:input_type -> card.control.ATMOnOffRequest
	33,  // 94: card.control.CardControl.POSOnOff:input_type -> card.control.POSOnOffRequest
	47,  // 95: card.control.CardControl.FetchCardLimits:input_type -> card.control.FetchCardLimitsRequest
	49,  // 96: card.control.CardControl.ConsolidatedCardControlOnOff:input_type -> card.control.ConsolidatedCardControlOnOffRequest
	16,  // 97: card.control.CardControl.GetInternationalAtmLimits:input_type -> card.control.GetInternationalAtmLimitsRequest
	12,  // 98: card.control.CardControl.SetTravelMode:input_type -> card.control.SetTravelModeRequest
	14,  // 99: card.control.CardControl.GetTravelMode:input_type -> card.control.GetTravelModeRequest
	20,  // 100: card.control.CardControl.BlockCard:output_type -> card.control.BlockCardResponse
	23,  // 101: card.control.CardControl.SuspendCard:output_type -> card.control.SuspendCardResponse
	26,  // 102: card.control.CardControl.LocationOnOff:output_type -> card.control.LocationOnOffResponse
	29,  // 103: card.control.CardControl.ECommerceOnOff:output_type -> card.control.ECommerceOnOffResponse
	38,  // 104: card.control.CardControl.NfcOnOff:output_type -> card.control.NfcOnOffResponse
	41,  // 105: card.control.CardControl.ControlOnOff:output_type -> card.control.ControlOnOffResponse
	43,  // 106: card.control.CardControl.GetCardLimits:output_type -> card.control.GetCardLimitsResponse
	45,  // 107: card.control.CardControl.UpdateCardLimits:output_type -> card.control.UpdateCardLimitsResponse
	31,  // 108: card.control.CardControl.ATMOnOff:output_type -> card.control.ATMOnOffResponse
	34,  // 109: card.control.CardControl.POSOnOff:output_type -> card.control.POSOnOffResponse
	48,  // 110: card.control.CardControl.FetchCardLimits:output_type -> card.control.FetchCardLimitsResponse
	50,  // 111: card.control.CardControl.ConsolidatedCardControlOnOff:output_type -> card.control.ConsolidatedCardControlOnOffResponse
	17,  // 112: card.control.CardControl.GetInternationalAtmLimits:output_type -> card.control.GetInternationalAtmLimitsResponse
	13,  // 113: card.control.CardControl.SetTravelMode:output_type -> card.control.SetTravelModeResponse
	15,  // 114: card.control.CardControl.GetTravelMode:output_type -> card.control.GetTravelModeResponse
	100, // [100:115] is the sub-list for method output_type
	85,  // [85:100] is the sub-list for method input_type
	85,  // [85:85] is the sub-list for extension type_name
	85,  // [85:85] is the sub-list for extension extendee
	0,   // [0:85] is the sub-list for field type_name
}

func init() { file_api_card_control_service_proto_init() }
func file_api_card_control_service_proto_init() {
	if File_api_card_control_service_proto != nil {
		return
	}
	file_api_card_control_card_limit_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_card_control_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTravelModeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTravelModeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTravelModeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTravelModeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInternationalAtmLimitsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInternationalAtmLimitsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternationalAtmLimit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockCardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockCardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuspendCardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuspendStatesInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuspendCardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationOnOffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationOnOffStatesInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationOnOffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ECommerceOnOffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ECommerceOnOffStatesInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ECommerceOnOffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ATMOnOffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ATMOnOffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ATMOnOffStatesInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*POSOnOffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*POSOnOffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*POSOnOffStatesInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NfcOnOffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NfcOnOffStatesInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NfcOnOffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ControlOnOffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ControlOnOffStatesInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ControlOnOffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCardLimitsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCardLimitsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCardLimitsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCardLimitsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllowedLimits); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchCardLimitsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchCardLimitsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsolidatedCardControlOnOffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsolidatedCardControlOnOffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInternationalAtmLimitsRequest_CountryCodes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_card_control_service_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*GetInternationalAtmLimitsRequest_CountryCodes_)(nil),
		(*GetInternationalAtmLimitsRequest_GetAll)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_control_service_proto_rawDesc,
			NumEnums:      12,
			NumMessages:   47,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_card_control_service_proto_goTypes,
		DependencyIndexes: file_api_card_control_service_proto_depIdxs,
		EnumInfos:         file_api_card_control_service_proto_enumTypes,
		MessageInfos:      file_api_card_control_service_proto_msgTypes,
	}.Build()
	File_api_card_control_service_proto = out.File
	file_api_card_control_service_proto_rawDesc = nil
	file_api_card_control_service_proto_goTypes = nil
	file_api_card_control_service_proto_depIdxs = nil
}
