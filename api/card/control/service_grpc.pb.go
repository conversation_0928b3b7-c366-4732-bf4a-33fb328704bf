// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/card/control/service.proto

package control

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CardControl_BlockCard_FullMethodName                    = "/card.control.CardControl/BlockCard"
	CardControl_SuspendCard_FullMethodName                  = "/card.control.CardControl/SuspendCard"
	CardControl_LocationOnOff_FullMethodName                = "/card.control.CardControl/LocationOnOff"
	CardControl_ECommerceOnOff_FullMethodName               = "/card.control.CardControl/ECommerceOnOff"
	CardControl_NfcOnOff_FullMethodName                     = "/card.control.CardControl/NfcOnOff"
	CardControl_ControlOnOff_FullMethodName                 = "/card.control.CardControl/ControlOnOff"
	CardControl_GetCardLimits_FullMethodName                = "/card.control.CardControl/GetCardLimits"
	CardControl_UpdateCardLimits_FullMethodName             = "/card.control.CardControl/UpdateCardLimits"
	CardControl_ATMOnOff_FullMethodName                     = "/card.control.CardControl/ATMOnOff"
	CardControl_POSOnOff_FullMethodName                     = "/card.control.CardControl/POSOnOff"
	CardControl_FetchCardLimits_FullMethodName              = "/card.control.CardControl/FetchCardLimits"
	CardControl_ConsolidatedCardControlOnOff_FullMethodName = "/card.control.CardControl/ConsolidatedCardControlOnOff"
	CardControl_GetInternationalAtmLimits_FullMethodName    = "/card.control.CardControl/GetInternationalAtmLimits"
	CardControl_SetTravelMode_FullMethodName                = "/card.control.CardControl/SetTravelMode"
	CardControl_GetTravelMode_FullMethodName                = "/card.control.CardControl/GetTravelMode"
)

// CardControlClient is the client API for CardControl service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CardControlClient interface {
	// Rpc blocks the card. Block is a permanent operation i.e. the same card cannot be used after blocking.
	// Users may require to request a new card to make ATM/POS transactions.
	BlockCard(ctx context.Context, in *BlockCardRequest, opts ...grpc.CallOption) (*BlockCardResponse, error)
	// Rpc suspends/un-suspends cards. A suspended card cannot be used for any
	// transactions. Suspend is a temporary state. A card can be un-suspended(OFF) by the user.
	SuspendCard(ctx context.Context, in *SuspendCardRequest, opts ...grpc.CallOption) (*SuspendCardResponse, error)
	// Rpc facilitates to enable/disable cards for domestic/international usages.
	LocationOnOff(ctx context.Context, in *LocationOnOffRequest, opts ...grpc.CallOption) (*LocationOnOffResponse, error)
	// Rpc facilitates to enable/disable cards for e-commerce transactions.
	ECommerceOnOff(ctx context.Context, in *ECommerceOnOffRequest, opts ...grpc.CallOption) (*ECommerceOnOffResponse, error)
	// Rpc facilitates to enable/disable cards for NFC transactions for all locations.
	NfcOnOff(ctx context.Context, in *NfcOnOffRequest, opts ...grpc.CallOption) (*NfcOnOffResponse, error)
	// Rpc facilitates to enable/disable cards for a given combination of transaction type and usage location type.
	ControlOnOff(ctx context.Context, in *ControlOnOffRequest, opts ...grpc.CallOption) (*ControlOnOffResponse, error)
	// RPC to fetch card limit details
	GetCardLimits(ctx context.Context, in *GetCardLimitsRequest, opts ...grpc.CallOption) (*GetCardLimitsResponse, error)
	// RPC to update card limit at vendor
	UpdateCardLimits(ctx context.Context, in *UpdateCardLimitsRequest, opts ...grpc.CallOption) (*UpdateCardLimitsResponse, error)
	// Rpc facilitates to enable/disable cards for atm transactions.
	ATMOnOff(ctx context.Context, in *ATMOnOffRequest, opts ...grpc.CallOption) (*ATMOnOffResponse, error)
	// Rpc facilitates to enable/disable cards for pos transactions.
	POSOnOff(ctx context.Context, in *POSOnOffRequest, opts ...grpc.CallOption) (*POSOnOffResponse, error)
	// Rpc fetches card limits from database table
	FetchCardLimits(ctx context.Context, in *FetchCardLimitsRequest, opts ...grpc.CallOption) (*FetchCardLimitsResponse, error)
	// ConsolidatedCardControlOnOff rpc to enable/disable any number of card controls at once. Caller need to pass enable/disable flag again
	// each card control and it will be set at vendor end accordingly.
	// Cred block is required if any of the control has to be enabled. During onboarding we can use the
	// one time pin set token to enable the controls.
	ConsolidatedCardControlOnOff(ctx context.Context, in *ConsolidatedCardControlOnOffRequest, opts ...grpc.CallOption) (*ConsolidatedCardControlOnOffResponse, error)
	// GetInternationalAtmLimits RPC retrieves the ATM withdrawal limits for various countries.
	// It provides information on the maximum amount a user can withdraw from ATMs in different countries.
	GetInternationalAtmLimits(ctx context.Context, in *GetInternationalAtmLimitsRequest, opts ...grpc.CallOption) (*GetInternationalAtmLimitsResponse, error)
	// RPC to update travel mode for a user
	// Enabling travel mode will enable only the travel mode for the user.
	// Disabling travel mode will disable POS, ATM, Tap n Pay for International transactions
	SetTravelMode(ctx context.Context, in *SetTravelModeRequest, opts ...grpc.CallOption) (*SetTravelModeResponse, error)
	// RPC to get the current travel mode status for a user
	GetTravelMode(ctx context.Context, in *GetTravelModeRequest, opts ...grpc.CallOption) (*GetTravelModeResponse, error)
}

type cardControlClient struct {
	cc grpc.ClientConnInterface
}

func NewCardControlClient(cc grpc.ClientConnInterface) CardControlClient {
	return &cardControlClient{cc}
}

func (c *cardControlClient) BlockCard(ctx context.Context, in *BlockCardRequest, opts ...grpc.CallOption) (*BlockCardResponse, error) {
	out := new(BlockCardResponse)
	err := c.cc.Invoke(ctx, CardControl_BlockCard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardControlClient) SuspendCard(ctx context.Context, in *SuspendCardRequest, opts ...grpc.CallOption) (*SuspendCardResponse, error) {
	out := new(SuspendCardResponse)
	err := c.cc.Invoke(ctx, CardControl_SuspendCard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardControlClient) LocationOnOff(ctx context.Context, in *LocationOnOffRequest, opts ...grpc.CallOption) (*LocationOnOffResponse, error) {
	out := new(LocationOnOffResponse)
	err := c.cc.Invoke(ctx, CardControl_LocationOnOff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardControlClient) ECommerceOnOff(ctx context.Context, in *ECommerceOnOffRequest, opts ...grpc.CallOption) (*ECommerceOnOffResponse, error) {
	out := new(ECommerceOnOffResponse)
	err := c.cc.Invoke(ctx, CardControl_ECommerceOnOff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardControlClient) NfcOnOff(ctx context.Context, in *NfcOnOffRequest, opts ...grpc.CallOption) (*NfcOnOffResponse, error) {
	out := new(NfcOnOffResponse)
	err := c.cc.Invoke(ctx, CardControl_NfcOnOff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardControlClient) ControlOnOff(ctx context.Context, in *ControlOnOffRequest, opts ...grpc.CallOption) (*ControlOnOffResponse, error) {
	out := new(ControlOnOffResponse)
	err := c.cc.Invoke(ctx, CardControl_ControlOnOff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardControlClient) GetCardLimits(ctx context.Context, in *GetCardLimitsRequest, opts ...grpc.CallOption) (*GetCardLimitsResponse, error) {
	out := new(GetCardLimitsResponse)
	err := c.cc.Invoke(ctx, CardControl_GetCardLimits_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardControlClient) UpdateCardLimits(ctx context.Context, in *UpdateCardLimitsRequest, opts ...grpc.CallOption) (*UpdateCardLimitsResponse, error) {
	out := new(UpdateCardLimitsResponse)
	err := c.cc.Invoke(ctx, CardControl_UpdateCardLimits_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardControlClient) ATMOnOff(ctx context.Context, in *ATMOnOffRequest, opts ...grpc.CallOption) (*ATMOnOffResponse, error) {
	out := new(ATMOnOffResponse)
	err := c.cc.Invoke(ctx, CardControl_ATMOnOff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardControlClient) POSOnOff(ctx context.Context, in *POSOnOffRequest, opts ...grpc.CallOption) (*POSOnOffResponse, error) {
	out := new(POSOnOffResponse)
	err := c.cc.Invoke(ctx, CardControl_POSOnOff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardControlClient) FetchCardLimits(ctx context.Context, in *FetchCardLimitsRequest, opts ...grpc.CallOption) (*FetchCardLimitsResponse, error) {
	out := new(FetchCardLimitsResponse)
	err := c.cc.Invoke(ctx, CardControl_FetchCardLimits_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardControlClient) ConsolidatedCardControlOnOff(ctx context.Context, in *ConsolidatedCardControlOnOffRequest, opts ...grpc.CallOption) (*ConsolidatedCardControlOnOffResponse, error) {
	out := new(ConsolidatedCardControlOnOffResponse)
	err := c.cc.Invoke(ctx, CardControl_ConsolidatedCardControlOnOff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardControlClient) GetInternationalAtmLimits(ctx context.Context, in *GetInternationalAtmLimitsRequest, opts ...grpc.CallOption) (*GetInternationalAtmLimitsResponse, error) {
	out := new(GetInternationalAtmLimitsResponse)
	err := c.cc.Invoke(ctx, CardControl_GetInternationalAtmLimits_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardControlClient) SetTravelMode(ctx context.Context, in *SetTravelModeRequest, opts ...grpc.CallOption) (*SetTravelModeResponse, error) {
	out := new(SetTravelModeResponse)
	err := c.cc.Invoke(ctx, CardControl_SetTravelMode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardControlClient) GetTravelMode(ctx context.Context, in *GetTravelModeRequest, opts ...grpc.CallOption) (*GetTravelModeResponse, error) {
	out := new(GetTravelModeResponse)
	err := c.cc.Invoke(ctx, CardControl_GetTravelMode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CardControlServer is the server API for CardControl service.
// All implementations should embed UnimplementedCardControlServer
// for forward compatibility
type CardControlServer interface {
	// Rpc blocks the card. Block is a permanent operation i.e. the same card cannot be used after blocking.
	// Users may require to request a new card to make ATM/POS transactions.
	BlockCard(context.Context, *BlockCardRequest) (*BlockCardResponse, error)
	// Rpc suspends/un-suspends cards. A suspended card cannot be used for any
	// transactions. Suspend is a temporary state. A card can be un-suspended(OFF) by the user.
	SuspendCard(context.Context, *SuspendCardRequest) (*SuspendCardResponse, error)
	// Rpc facilitates to enable/disable cards for domestic/international usages.
	LocationOnOff(context.Context, *LocationOnOffRequest) (*LocationOnOffResponse, error)
	// Rpc facilitates to enable/disable cards for e-commerce transactions.
	ECommerceOnOff(context.Context, *ECommerceOnOffRequest) (*ECommerceOnOffResponse, error)
	// Rpc facilitates to enable/disable cards for NFC transactions for all locations.
	NfcOnOff(context.Context, *NfcOnOffRequest) (*NfcOnOffResponse, error)
	// Rpc facilitates to enable/disable cards for a given combination of transaction type and usage location type.
	ControlOnOff(context.Context, *ControlOnOffRequest) (*ControlOnOffResponse, error)
	// RPC to fetch card limit details
	GetCardLimits(context.Context, *GetCardLimitsRequest) (*GetCardLimitsResponse, error)
	// RPC to update card limit at vendor
	UpdateCardLimits(context.Context, *UpdateCardLimitsRequest) (*UpdateCardLimitsResponse, error)
	// Rpc facilitates to enable/disable cards for atm transactions.
	ATMOnOff(context.Context, *ATMOnOffRequest) (*ATMOnOffResponse, error)
	// Rpc facilitates to enable/disable cards for pos transactions.
	POSOnOff(context.Context, *POSOnOffRequest) (*POSOnOffResponse, error)
	// Rpc fetches card limits from database table
	FetchCardLimits(context.Context, *FetchCardLimitsRequest) (*FetchCardLimitsResponse, error)
	// ConsolidatedCardControlOnOff rpc to enable/disable any number of card controls at once. Caller need to pass enable/disable flag again
	// each card control and it will be set at vendor end accordingly.
	// Cred block is required if any of the control has to be enabled. During onboarding we can use the
	// one time pin set token to enable the controls.
	ConsolidatedCardControlOnOff(context.Context, *ConsolidatedCardControlOnOffRequest) (*ConsolidatedCardControlOnOffResponse, error)
	// GetInternationalAtmLimits RPC retrieves the ATM withdrawal limits for various countries.
	// It provides information on the maximum amount a user can withdraw from ATMs in different countries.
	GetInternationalAtmLimits(context.Context, *GetInternationalAtmLimitsRequest) (*GetInternationalAtmLimitsResponse, error)
	// RPC to update travel mode for a user
	// Enabling travel mode will enable only the travel mode for the user.
	// Disabling travel mode will disable POS, ATM, Tap n Pay for International transactions
	SetTravelMode(context.Context, *SetTravelModeRequest) (*SetTravelModeResponse, error)
	// RPC to get the current travel mode status for a user
	GetTravelMode(context.Context, *GetTravelModeRequest) (*GetTravelModeResponse, error)
}

// UnimplementedCardControlServer should be embedded to have forward compatible implementations.
type UnimplementedCardControlServer struct {
}

func (UnimplementedCardControlServer) BlockCard(context.Context, *BlockCardRequest) (*BlockCardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BlockCard not implemented")
}
func (UnimplementedCardControlServer) SuspendCard(context.Context, *SuspendCardRequest) (*SuspendCardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuspendCard not implemented")
}
func (UnimplementedCardControlServer) LocationOnOff(context.Context, *LocationOnOffRequest) (*LocationOnOffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LocationOnOff not implemented")
}
func (UnimplementedCardControlServer) ECommerceOnOff(context.Context, *ECommerceOnOffRequest) (*ECommerceOnOffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ECommerceOnOff not implemented")
}
func (UnimplementedCardControlServer) NfcOnOff(context.Context, *NfcOnOffRequest) (*NfcOnOffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NfcOnOff not implemented")
}
func (UnimplementedCardControlServer) ControlOnOff(context.Context, *ControlOnOffRequest) (*ControlOnOffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ControlOnOff not implemented")
}
func (UnimplementedCardControlServer) GetCardLimits(context.Context, *GetCardLimitsRequest) (*GetCardLimitsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardLimits not implemented")
}
func (UnimplementedCardControlServer) UpdateCardLimits(context.Context, *UpdateCardLimitsRequest) (*UpdateCardLimitsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCardLimits not implemented")
}
func (UnimplementedCardControlServer) ATMOnOff(context.Context, *ATMOnOffRequest) (*ATMOnOffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ATMOnOff not implemented")
}
func (UnimplementedCardControlServer) POSOnOff(context.Context, *POSOnOffRequest) (*POSOnOffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method POSOnOff not implemented")
}
func (UnimplementedCardControlServer) FetchCardLimits(context.Context, *FetchCardLimitsRequest) (*FetchCardLimitsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchCardLimits not implemented")
}
func (UnimplementedCardControlServer) ConsolidatedCardControlOnOff(context.Context, *ConsolidatedCardControlOnOffRequest) (*ConsolidatedCardControlOnOffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsolidatedCardControlOnOff not implemented")
}
func (UnimplementedCardControlServer) GetInternationalAtmLimits(context.Context, *GetInternationalAtmLimitsRequest) (*GetInternationalAtmLimitsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInternationalAtmLimits not implemented")
}
func (UnimplementedCardControlServer) SetTravelMode(context.Context, *SetTravelModeRequest) (*SetTravelModeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetTravelMode not implemented")
}
func (UnimplementedCardControlServer) GetTravelMode(context.Context, *GetTravelModeRequest) (*GetTravelModeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTravelMode not implemented")
}

// UnsafeCardControlServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CardControlServer will
// result in compilation errors.
type UnsafeCardControlServer interface {
	mustEmbedUnimplementedCardControlServer()
}

func RegisterCardControlServer(s grpc.ServiceRegistrar, srv CardControlServer) {
	s.RegisterService(&CardControl_ServiceDesc, srv)
}

func _CardControl_BlockCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).BlockCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_BlockCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).BlockCard(ctx, req.(*BlockCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardControl_SuspendCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SuspendCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).SuspendCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_SuspendCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).SuspendCard(ctx, req.(*SuspendCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardControl_LocationOnOff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LocationOnOffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).LocationOnOff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_LocationOnOff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).LocationOnOff(ctx, req.(*LocationOnOffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardControl_ECommerceOnOff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ECommerceOnOffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).ECommerceOnOff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_ECommerceOnOff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).ECommerceOnOff(ctx, req.(*ECommerceOnOffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardControl_NfcOnOff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NfcOnOffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).NfcOnOff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_NfcOnOff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).NfcOnOff(ctx, req.(*NfcOnOffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardControl_ControlOnOff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ControlOnOffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).ControlOnOff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_ControlOnOff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).ControlOnOff(ctx, req.(*ControlOnOffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardControl_GetCardLimits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardLimitsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).GetCardLimits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_GetCardLimits_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).GetCardLimits(ctx, req.(*GetCardLimitsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardControl_UpdateCardLimits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCardLimitsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).UpdateCardLimits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_UpdateCardLimits_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).UpdateCardLimits(ctx, req.(*UpdateCardLimitsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardControl_ATMOnOff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ATMOnOffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).ATMOnOff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_ATMOnOff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).ATMOnOff(ctx, req.(*ATMOnOffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardControl_POSOnOff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(POSOnOffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).POSOnOff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_POSOnOff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).POSOnOff(ctx, req.(*POSOnOffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardControl_FetchCardLimits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchCardLimitsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).FetchCardLimits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_FetchCardLimits_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).FetchCardLimits(ctx, req.(*FetchCardLimitsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardControl_ConsolidatedCardControlOnOff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConsolidatedCardControlOnOffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).ConsolidatedCardControlOnOff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_ConsolidatedCardControlOnOff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).ConsolidatedCardControlOnOff(ctx, req.(*ConsolidatedCardControlOnOffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardControl_GetInternationalAtmLimits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInternationalAtmLimitsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).GetInternationalAtmLimits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_GetInternationalAtmLimits_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).GetInternationalAtmLimits(ctx, req.(*GetInternationalAtmLimitsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardControl_SetTravelMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTravelModeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).SetTravelMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_SetTravelMode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).SetTravelMode(ctx, req.(*SetTravelModeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardControl_GetTravelMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTravelModeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardControlServer).GetTravelMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardControl_GetTravelMode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardControlServer).GetTravelMode(ctx, req.(*GetTravelModeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CardControl_ServiceDesc is the grpc.ServiceDesc for CardControl service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CardControl_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "card.control.CardControl",
	HandlerType: (*CardControlServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BlockCard",
			Handler:    _CardControl_BlockCard_Handler,
		},
		{
			MethodName: "SuspendCard",
			Handler:    _CardControl_SuspendCard_Handler,
		},
		{
			MethodName: "LocationOnOff",
			Handler:    _CardControl_LocationOnOff_Handler,
		},
		{
			MethodName: "ECommerceOnOff",
			Handler:    _CardControl_ECommerceOnOff_Handler,
		},
		{
			MethodName: "NfcOnOff",
			Handler:    _CardControl_NfcOnOff_Handler,
		},
		{
			MethodName: "ControlOnOff",
			Handler:    _CardControl_ControlOnOff_Handler,
		},
		{
			MethodName: "GetCardLimits",
			Handler:    _CardControl_GetCardLimits_Handler,
		},
		{
			MethodName: "UpdateCardLimits",
			Handler:    _CardControl_UpdateCardLimits_Handler,
		},
		{
			MethodName: "ATMOnOff",
			Handler:    _CardControl_ATMOnOff_Handler,
		},
		{
			MethodName: "POSOnOff",
			Handler:    _CardControl_POSOnOff_Handler,
		},
		{
			MethodName: "FetchCardLimits",
			Handler:    _CardControl_FetchCardLimits_Handler,
		},
		{
			MethodName: "ConsolidatedCardControlOnOff",
			Handler:    _CardControl_ConsolidatedCardControlOnOff_Handler,
		},
		{
			MethodName: "GetInternationalAtmLimits",
			Handler:    _CardControl_GetInternationalAtmLimits_Handler,
		},
		{
			MethodName: "SetTravelMode",
			Handler:    _CardControl_SetTravelMode_Handler,
		},
		{
			MethodName: "GetTravelMode",
			Handler:    _CardControl_GetTravelMode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/card/control/service.proto",
}
