// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/control/service.proto

package control

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	card "github.com/epifi/gamma/api/card"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = card.Provenance(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on SetTravelModeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetTravelModeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetTravelModeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetTravelModeRequestMultiError, or nil if none found.
func (m *SetTravelModeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetTravelModeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := SetTravelModeRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EnableTravelMode

	// no validation rules for WorkflowType

	if len(errors) > 0 {
		return SetTravelModeRequestMultiError(errors)
	}

	return nil
}

// SetTravelModeRequestMultiError is an error wrapping multiple validation
// errors returned by SetTravelModeRequest.ValidateAll() if the designated
// constraints aren't met.
type SetTravelModeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetTravelModeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetTravelModeRequestMultiError) AllErrors() []error { return m }

// SetTravelModeRequestValidationError is the validation error returned by
// SetTravelModeRequest.Validate if the designated constraints aren't met.
type SetTravelModeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetTravelModeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetTravelModeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetTravelModeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetTravelModeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetTravelModeRequestValidationError) ErrorName() string {
	return "SetTravelModeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetTravelModeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetTravelModeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetTravelModeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetTravelModeRequestValidationError{}

// Validate checks the field values on SetTravelModeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetTravelModeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetTravelModeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetTravelModeResponseMultiError, or nil if none found.
func (m *SetTravelModeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SetTravelModeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetTravelModeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetTravelModeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetTravelModeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsTravelModeOn

	if len(errors) > 0 {
		return SetTravelModeResponseMultiError(errors)
	}

	return nil
}

// SetTravelModeResponseMultiError is an error wrapping multiple validation
// errors returned by SetTravelModeResponse.ValidateAll() if the designated
// constraints aren't met.
type SetTravelModeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetTravelModeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetTravelModeResponseMultiError) AllErrors() []error { return m }

// SetTravelModeResponseValidationError is the validation error returned by
// SetTravelModeResponse.Validate if the designated constraints aren't met.
type SetTravelModeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetTravelModeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetTravelModeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetTravelModeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetTravelModeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetTravelModeResponseValidationError) ErrorName() string {
	return "SetTravelModeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SetTravelModeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetTravelModeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetTravelModeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetTravelModeResponseValidationError{}

// Validate checks the field values on GetTravelModeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTravelModeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTravelModeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTravelModeRequestMultiError, or nil if none found.
func (m *GetTravelModeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTravelModeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetTravelModeRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetTravelModeRequestMultiError(errors)
	}

	return nil
}

// GetTravelModeRequestMultiError is an error wrapping multiple validation
// errors returned by GetTravelModeRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTravelModeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTravelModeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTravelModeRequestMultiError) AllErrors() []error { return m }

// GetTravelModeRequestValidationError is the validation error returned by
// GetTravelModeRequest.Validate if the designated constraints aren't met.
type GetTravelModeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTravelModeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTravelModeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTravelModeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTravelModeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTravelModeRequestValidationError) ErrorName() string {
	return "GetTravelModeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTravelModeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTravelModeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTravelModeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTravelModeRequestValidationError{}

// Validate checks the field values on GetTravelModeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTravelModeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTravelModeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTravelModeResponseMultiError, or nil if none found.
func (m *GetTravelModeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTravelModeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTravelModeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTravelModeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTravelModeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsTravelModeOn

	// no validation rules for EnableWorkflow

	if len(errors) > 0 {
		return GetTravelModeResponseMultiError(errors)
	}

	return nil
}

// GetTravelModeResponseMultiError is an error wrapping multiple validation
// errors returned by GetTravelModeResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTravelModeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTravelModeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTravelModeResponseMultiError) AllErrors() []error { return m }

// GetTravelModeResponseValidationError is the validation error returned by
// GetTravelModeResponse.Validate if the designated constraints aren't met.
type GetTravelModeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTravelModeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTravelModeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTravelModeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTravelModeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTravelModeResponseValidationError) ErrorName() string {
	return "GetTravelModeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTravelModeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTravelModeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTravelModeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTravelModeResponseValidationError{}

// Validate checks the field values on GetInternationalAtmLimitsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetInternationalAtmLimitsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInternationalAtmLimitsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetInternationalAtmLimitsRequestMultiError, or nil if none found.
func (m *GetInternationalAtmLimitsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInternationalAtmLimitsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	switch v := m.GetBy.(type) {
	case *GetInternationalAtmLimitsRequest_CountryCodes_:
		if v == nil {
			err := GetInternationalAtmLimitsRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCountryCodes()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetInternationalAtmLimitsRequestValidationError{
						field:  "CountryCodes",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetInternationalAtmLimitsRequestValidationError{
						field:  "CountryCodes",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCountryCodes()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetInternationalAtmLimitsRequestValidationError{
					field:  "CountryCodes",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetInternationalAtmLimitsRequest_GetAll:
		if v == nil {
			err := GetInternationalAtmLimitsRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for GetAll
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetInternationalAtmLimitsRequestMultiError(errors)
	}

	return nil
}

// GetInternationalAtmLimitsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetInternationalAtmLimitsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetInternationalAtmLimitsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInternationalAtmLimitsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInternationalAtmLimitsRequestMultiError) AllErrors() []error { return m }

// GetInternationalAtmLimitsRequestValidationError is the validation error
// returned by GetInternationalAtmLimitsRequest.Validate if the designated
// constraints aren't met.
type GetInternationalAtmLimitsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInternationalAtmLimitsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInternationalAtmLimitsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInternationalAtmLimitsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInternationalAtmLimitsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInternationalAtmLimitsRequestValidationError) ErrorName() string {
	return "GetInternationalAtmLimitsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInternationalAtmLimitsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInternationalAtmLimitsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInternationalAtmLimitsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInternationalAtmLimitsRequestValidationError{}

// Validate checks the field values on GetInternationalAtmLimitsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetInternationalAtmLimitsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInternationalAtmLimitsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetInternationalAtmLimitsResponseMultiError, or nil if none found.
func (m *GetInternationalAtmLimitsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInternationalAtmLimitsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInternationalAtmLimitsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInternationalAtmLimitsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInternationalAtmLimitsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInternationalAtmLimits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetInternationalAtmLimitsResponseValidationError{
						field:  fmt.Sprintf("InternationalAtmLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetInternationalAtmLimitsResponseValidationError{
						field:  fmt.Sprintf("InternationalAtmLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetInternationalAtmLimitsResponseValidationError{
					field:  fmt.Sprintf("InternationalAtmLimits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsUserAtInternationalLocation

	// no validation rules for UserCountryName

	if len(errors) > 0 {
		return GetInternationalAtmLimitsResponseMultiError(errors)
	}

	return nil
}

// GetInternationalAtmLimitsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetInternationalAtmLimitsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetInternationalAtmLimitsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInternationalAtmLimitsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInternationalAtmLimitsResponseMultiError) AllErrors() []error { return m }

// GetInternationalAtmLimitsResponseValidationError is the validation error
// returned by GetInternationalAtmLimitsResponse.Validate if the designated
// constraints aren't met.
type GetInternationalAtmLimitsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInternationalAtmLimitsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInternationalAtmLimitsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInternationalAtmLimitsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInternationalAtmLimitsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInternationalAtmLimitsResponseValidationError) ErrorName() string {
	return "GetInternationalAtmLimitsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInternationalAtmLimitsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInternationalAtmLimitsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInternationalAtmLimitsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInternationalAtmLimitsResponseValidationError{}

// Validate checks the field values on InternationalAtmLimit with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InternationalAtmLimit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InternationalAtmLimit with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InternationalAtmLimitMultiError, or nil if none found.
func (m *InternationalAtmLimit) ValidateAll() error {
	return m.validate(true)
}

func (m *InternationalAtmLimit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CountryName

	// no validation rules for CountryCode

	// no validation rules for CountryFlag

	if all {
		switch v := interface{}(m.GetMaxAtmWithdrawalLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InternationalAtmLimitValidationError{
					field:  "MaxAtmWithdrawalLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InternationalAtmLimitValidationError{
					field:  "MaxAtmWithdrawalLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxAtmWithdrawalLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InternationalAtmLimitValidationError{
				field:  "MaxAtmWithdrawalLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InternationalAtmLimitMultiError(errors)
	}

	return nil
}

// InternationalAtmLimitMultiError is an error wrapping multiple validation
// errors returned by InternationalAtmLimit.ValidateAll() if the designated
// constraints aren't met.
type InternationalAtmLimitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InternationalAtmLimitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InternationalAtmLimitMultiError) AllErrors() []error { return m }

// InternationalAtmLimitValidationError is the validation error returned by
// InternationalAtmLimit.Validate if the designated constraints aren't met.
type InternationalAtmLimitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InternationalAtmLimitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InternationalAtmLimitValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InternationalAtmLimitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InternationalAtmLimitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InternationalAtmLimitValidationError) ErrorName() string {
	return "InternationalAtmLimitValidationError"
}

// Error satisfies the builtin error interface
func (e InternationalAtmLimitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInternationalAtmLimit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InternationalAtmLimitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InternationalAtmLimitValidationError{}

// Validate checks the field values on BlockCardRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BlockCardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlockCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BlockCardRequestMultiError, or nil if none found.
func (m *BlockCardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BlockCardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vendor

	// no validation rules for BlockCardReason

	// no validation rules for BlockCardProvenance

	// no validation rules for SkipVendorCall

	if len(errors) > 0 {
		return BlockCardRequestMultiError(errors)
	}

	return nil
}

// BlockCardRequestMultiError is an error wrapping multiple validation errors
// returned by BlockCardRequest.ValidateAll() if the designated constraints
// aren't met.
type BlockCardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlockCardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlockCardRequestMultiError) AllErrors() []error { return m }

// BlockCardRequestValidationError is the validation error returned by
// BlockCardRequest.Validate if the designated constraints aren't met.
type BlockCardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlockCardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlockCardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlockCardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlockCardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlockCardRequestValidationError) ErrorName() string { return "BlockCardRequestValidationError" }

// Error satisfies the builtin error interface
func (e BlockCardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlockCardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlockCardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlockCardRequestValidationError{}

// Validate checks the field values on BlockCardResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BlockCardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlockCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BlockCardResponseMultiError, or nil if none found.
func (m *BlockCardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BlockCardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BlockCardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BlockCardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BlockCardResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetBlockedCards()))
		i := 0
		for key := range m.GetBlockedCards() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetBlockedCards()[key]
			_ = val

			// no validation rules for BlockedCards[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, BlockCardResponseValidationError{
							field:  fmt.Sprintf("BlockedCards[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, BlockCardResponseValidationError{
							field:  fmt.Sprintf("BlockedCards[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return BlockCardResponseValidationError{
						field:  fmt.Sprintf("BlockedCards[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return BlockCardResponseMultiError(errors)
	}

	return nil
}

// BlockCardResponseMultiError is an error wrapping multiple validation errors
// returned by BlockCardResponse.ValidateAll() if the designated constraints
// aren't met.
type BlockCardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlockCardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlockCardResponseMultiError) AllErrors() []error { return m }

// BlockCardResponseValidationError is the validation error returned by
// BlockCardResponse.Validate if the designated constraints aren't met.
type BlockCardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlockCardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlockCardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlockCardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlockCardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlockCardResponseValidationError) ErrorName() string {
	return "BlockCardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BlockCardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlockCardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlockCardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlockCardResponseValidationError{}

// Validate checks the field values on SuspendCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SuspendCardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SuspendCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SuspendCardRequestMultiError, or nil if none found.
func (m *SuspendCardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SuspendCardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Action

	// no validation rules for Vendor

	// no validation rules for CredBlock

	// no validation rules for RequestId

	// no validation rules for ControlActionWorkflow

	if len(errors) > 0 {
		return SuspendCardRequestMultiError(errors)
	}

	return nil
}

// SuspendCardRequestMultiError is an error wrapping multiple validation errors
// returned by SuspendCardRequest.ValidateAll() if the designated constraints
// aren't met.
type SuspendCardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SuspendCardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SuspendCardRequestMultiError) AllErrors() []error { return m }

// SuspendCardRequestValidationError is the validation error returned by
// SuspendCardRequest.Validate if the designated constraints aren't met.
type SuspendCardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SuspendCardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SuspendCardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SuspendCardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SuspendCardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SuspendCardRequestValidationError) ErrorName() string {
	return "SuspendCardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SuspendCardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSuspendCardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SuspendCardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SuspendCardRequestValidationError{}

// Validate checks the field values on SuspendStatesInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SuspendStatesInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SuspendStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SuspendStatesInfoMultiError, or nil if none found.
func (m *SuspendStatesInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *SuspendStatesInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SuspendStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SuspendStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SuspendStatesInfoValidationError{
				field:  "Card",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return SuspendStatesInfoMultiError(errors)
	}

	return nil
}

// SuspendStatesInfoMultiError is an error wrapping multiple validation errors
// returned by SuspendStatesInfo.ValidateAll() if the designated constraints
// aren't met.
type SuspendStatesInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SuspendStatesInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SuspendStatesInfoMultiError) AllErrors() []error { return m }

// SuspendStatesInfoValidationError is the validation error returned by
// SuspendStatesInfo.Validate if the designated constraints aren't met.
type SuspendStatesInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SuspendStatesInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SuspendStatesInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SuspendStatesInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SuspendStatesInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SuspendStatesInfoValidationError) ErrorName() string {
	return "SuspendStatesInfoValidationError"
}

// Error satisfies the builtin error interface
func (e SuspendStatesInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSuspendStatesInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SuspendStatesInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SuspendStatesInfoValidationError{}

// Validate checks the field values on SuspendCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SuspendCardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SuspendCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SuspendCardResponseMultiError, or nil if none found.
func (m *SuspendCardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SuspendCardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SuspendCardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SuspendCardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SuspendCardResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetSuspendStates()))
		i := 0
		for key := range m.GetSuspendStates() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetSuspendStates()[key]
			_ = val

			// no validation rules for SuspendStates[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, SuspendCardResponseValidationError{
							field:  fmt.Sprintf("SuspendStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, SuspendCardResponseValidationError{
							field:  fmt.Sprintf("SuspendStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return SuspendCardResponseValidationError{
						field:  fmt.Sprintf("SuspendStates[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return SuspendCardResponseMultiError(errors)
	}

	return nil
}

// SuspendCardResponseMultiError is an error wrapping multiple validation
// errors returned by SuspendCardResponse.ValidateAll() if the designated
// constraints aren't met.
type SuspendCardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SuspendCardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SuspendCardResponseMultiError) AllErrors() []error { return m }

// SuspendCardResponseValidationError is the validation error returned by
// SuspendCardResponse.Validate if the designated constraints aren't met.
type SuspendCardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SuspendCardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SuspendCardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SuspendCardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SuspendCardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SuspendCardResponseValidationError) ErrorName() string {
	return "SuspendCardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SuspendCardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSuspendCardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SuspendCardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SuspendCardResponseValidationError{}

// Validate checks the field values on LocationOnOffRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LocationOnOffRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LocationOnOffRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LocationOnOffRequestMultiError, or nil if none found.
func (m *LocationOnOffRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LocationOnOffRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LocType

	// no validation rules for Action

	// no validation rules for Vendor

	// no validation rules for CredBlock

	// no validation rules for RequestId

	// no validation rules for ControlActionWorkflow

	if len(errors) > 0 {
		return LocationOnOffRequestMultiError(errors)
	}

	return nil
}

// LocationOnOffRequestMultiError is an error wrapping multiple validation
// errors returned by LocationOnOffRequest.ValidateAll() if the designated
// constraints aren't met.
type LocationOnOffRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LocationOnOffRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LocationOnOffRequestMultiError) AllErrors() []error { return m }

// LocationOnOffRequestValidationError is the validation error returned by
// LocationOnOffRequest.Validate if the designated constraints aren't met.
type LocationOnOffRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LocationOnOffRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LocationOnOffRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LocationOnOffRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LocationOnOffRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LocationOnOffRequestValidationError) ErrorName() string {
	return "LocationOnOffRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LocationOnOffRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLocationOnOffRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LocationOnOffRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LocationOnOffRequestValidationError{}

// Validate checks the field values on LocationOnOffStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LocationOnOffStatesInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LocationOnOffStatesInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LocationOnOffStatesInfoMultiError, or nil if none found.
func (m *LocationOnOffStatesInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *LocationOnOffStatesInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LocationOnOffStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LocationOnOffStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LocationOnOffStatesInfoValidationError{
				field:  "Card",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return LocationOnOffStatesInfoMultiError(errors)
	}

	return nil
}

// LocationOnOffStatesInfoMultiError is an error wrapping multiple validation
// errors returned by LocationOnOffStatesInfo.ValidateAll() if the designated
// constraints aren't met.
type LocationOnOffStatesInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LocationOnOffStatesInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LocationOnOffStatesInfoMultiError) AllErrors() []error { return m }

// LocationOnOffStatesInfoValidationError is the validation error returned by
// LocationOnOffStatesInfo.Validate if the designated constraints aren't met.
type LocationOnOffStatesInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LocationOnOffStatesInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LocationOnOffStatesInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LocationOnOffStatesInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LocationOnOffStatesInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LocationOnOffStatesInfoValidationError) ErrorName() string {
	return "LocationOnOffStatesInfoValidationError"
}

// Error satisfies the builtin error interface
func (e LocationOnOffStatesInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLocationOnOffStatesInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LocationOnOffStatesInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LocationOnOffStatesInfoValidationError{}

// Validate checks the field values on LocationOnOffResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LocationOnOffResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LocationOnOffResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LocationOnOffResponseMultiError, or nil if none found.
func (m *LocationOnOffResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LocationOnOffResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LocationOnOffResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LocationOnOffResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LocationOnOffResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetLocationOnOffStates()))
		i := 0
		for key := range m.GetLocationOnOffStates() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetLocationOnOffStates()[key]
			_ = val

			// no validation rules for LocationOnOffStates[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, LocationOnOffResponseValidationError{
							field:  fmt.Sprintf("LocationOnOffStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, LocationOnOffResponseValidationError{
							field:  fmt.Sprintf("LocationOnOffStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return LocationOnOffResponseValidationError{
						field:  fmt.Sprintf("LocationOnOffStates[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return LocationOnOffResponseMultiError(errors)
	}

	return nil
}

// LocationOnOffResponseMultiError is an error wrapping multiple validation
// errors returned by LocationOnOffResponse.ValidateAll() if the designated
// constraints aren't met.
type LocationOnOffResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LocationOnOffResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LocationOnOffResponseMultiError) AllErrors() []error { return m }

// LocationOnOffResponseValidationError is the validation error returned by
// LocationOnOffResponse.Validate if the designated constraints aren't met.
type LocationOnOffResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LocationOnOffResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LocationOnOffResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LocationOnOffResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LocationOnOffResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LocationOnOffResponseValidationError) ErrorName() string {
	return "LocationOnOffResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LocationOnOffResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLocationOnOffResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LocationOnOffResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LocationOnOffResponseValidationError{}

// Validate checks the field values on ECommerceOnOffRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ECommerceOnOffRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ECommerceOnOffRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ECommerceOnOffRequestMultiError, or nil if none found.
func (m *ECommerceOnOffRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ECommerceOnOffRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Action

	// no validation rules for Vendor

	// no validation rules for CredBlock

	// no validation rules for RequestId

	// no validation rules for EcommEnableFlow

	// no validation rules for ControlActionWorkflow

	if len(errors) > 0 {
		return ECommerceOnOffRequestMultiError(errors)
	}

	return nil
}

// ECommerceOnOffRequestMultiError is an error wrapping multiple validation
// errors returned by ECommerceOnOffRequest.ValidateAll() if the designated
// constraints aren't met.
type ECommerceOnOffRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ECommerceOnOffRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ECommerceOnOffRequestMultiError) AllErrors() []error { return m }

// ECommerceOnOffRequestValidationError is the validation error returned by
// ECommerceOnOffRequest.Validate if the designated constraints aren't met.
type ECommerceOnOffRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ECommerceOnOffRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ECommerceOnOffRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ECommerceOnOffRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ECommerceOnOffRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ECommerceOnOffRequestValidationError) ErrorName() string {
	return "ECommerceOnOffRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ECommerceOnOffRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sECommerceOnOffRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ECommerceOnOffRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ECommerceOnOffRequestValidationError{}

// Validate checks the field values on ECommerceOnOffStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ECommerceOnOffStatesInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ECommerceOnOffStatesInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ECommerceOnOffStatesInfoMultiError, or nil if none found.
func (m *ECommerceOnOffStatesInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ECommerceOnOffStatesInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ECommerceOnOffStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ECommerceOnOffStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ECommerceOnOffStatesInfoValidationError{
				field:  "Card",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return ECommerceOnOffStatesInfoMultiError(errors)
	}

	return nil
}

// ECommerceOnOffStatesInfoMultiError is an error wrapping multiple validation
// errors returned by ECommerceOnOffStatesInfo.ValidateAll() if the designated
// constraints aren't met.
type ECommerceOnOffStatesInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ECommerceOnOffStatesInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ECommerceOnOffStatesInfoMultiError) AllErrors() []error { return m }

// ECommerceOnOffStatesInfoValidationError is the validation error returned by
// ECommerceOnOffStatesInfo.Validate if the designated constraints aren't met.
type ECommerceOnOffStatesInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ECommerceOnOffStatesInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ECommerceOnOffStatesInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ECommerceOnOffStatesInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ECommerceOnOffStatesInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ECommerceOnOffStatesInfoValidationError) ErrorName() string {
	return "ECommerceOnOffStatesInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ECommerceOnOffStatesInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sECommerceOnOffStatesInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ECommerceOnOffStatesInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ECommerceOnOffStatesInfoValidationError{}

// Validate checks the field values on ECommerceOnOffResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ECommerceOnOffResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ECommerceOnOffResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ECommerceOnOffResponseMultiError, or nil if none found.
func (m *ECommerceOnOffResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ECommerceOnOffResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ECommerceOnOffResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ECommerceOnOffResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ECommerceOnOffResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetEcommOnOffStates()))
		i := 0
		for key := range m.GetEcommOnOffStates() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetEcommOnOffStates()[key]
			_ = val

			// no validation rules for EcommOnOffStates[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ECommerceOnOffResponseValidationError{
							field:  fmt.Sprintf("EcommOnOffStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ECommerceOnOffResponseValidationError{
							field:  fmt.Sprintf("EcommOnOffStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ECommerceOnOffResponseValidationError{
						field:  fmt.Sprintf("EcommOnOffStates[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return ECommerceOnOffResponseMultiError(errors)
	}

	return nil
}

// ECommerceOnOffResponseMultiError is an error wrapping multiple validation
// errors returned by ECommerceOnOffResponse.ValidateAll() if the designated
// constraints aren't met.
type ECommerceOnOffResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ECommerceOnOffResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ECommerceOnOffResponseMultiError) AllErrors() []error { return m }

// ECommerceOnOffResponseValidationError is the validation error returned by
// ECommerceOnOffResponse.Validate if the designated constraints aren't met.
type ECommerceOnOffResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ECommerceOnOffResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ECommerceOnOffResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ECommerceOnOffResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ECommerceOnOffResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ECommerceOnOffResponseValidationError) ErrorName() string {
	return "ECommerceOnOffResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ECommerceOnOffResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sECommerceOnOffResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ECommerceOnOffResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ECommerceOnOffResponseValidationError{}

// Validate checks the field values on ATMOnOffRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ATMOnOffRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ATMOnOffRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ATMOnOffRequestMultiError, or nil if none found.
func (m *ATMOnOffRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ATMOnOffRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for Action

	// no validation rules for CredBlock

	// no validation rules for RequestId

	// no validation rules for ControlActionWorkflow

	if len(errors) > 0 {
		return ATMOnOffRequestMultiError(errors)
	}

	return nil
}

// ATMOnOffRequestMultiError is an error wrapping multiple validation errors
// returned by ATMOnOffRequest.ValidateAll() if the designated constraints
// aren't met.
type ATMOnOffRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ATMOnOffRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ATMOnOffRequestMultiError) AllErrors() []error { return m }

// ATMOnOffRequestValidationError is the validation error returned by
// ATMOnOffRequest.Validate if the designated constraints aren't met.
type ATMOnOffRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ATMOnOffRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ATMOnOffRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ATMOnOffRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ATMOnOffRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ATMOnOffRequestValidationError) ErrorName() string { return "ATMOnOffRequestValidationError" }

// Error satisfies the builtin error interface
func (e ATMOnOffRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sATMOnOffRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ATMOnOffRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ATMOnOffRequestValidationError{}

// Validate checks the field values on ATMOnOffResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ATMOnOffResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ATMOnOffResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ATMOnOffResponseMultiError, or nil if none found.
func (m *ATMOnOffResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ATMOnOffResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ATMOnOffResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ATMOnOffResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ATMOnOffResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAtmOnOffState()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ATMOnOffResponseValidationError{
					field:  "AtmOnOffState",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ATMOnOffResponseValidationError{
					field:  "AtmOnOffState",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAtmOnOffState()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ATMOnOffResponseValidationError{
				field:  "AtmOnOffState",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ATMOnOffResponseMultiError(errors)
	}

	return nil
}

// ATMOnOffResponseMultiError is an error wrapping multiple validation errors
// returned by ATMOnOffResponse.ValidateAll() if the designated constraints
// aren't met.
type ATMOnOffResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ATMOnOffResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ATMOnOffResponseMultiError) AllErrors() []error { return m }

// ATMOnOffResponseValidationError is the validation error returned by
// ATMOnOffResponse.Validate if the designated constraints aren't met.
type ATMOnOffResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ATMOnOffResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ATMOnOffResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ATMOnOffResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ATMOnOffResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ATMOnOffResponseValidationError) ErrorName() string { return "ATMOnOffResponseValidationError" }

// Error satisfies the builtin error interface
func (e ATMOnOffResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sATMOnOffResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ATMOnOffResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ATMOnOffResponseValidationError{}

// Validate checks the field values on ATMOnOffStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ATMOnOffStatesInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ATMOnOffStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ATMOnOffStatesInfoMultiError, or nil if none found.
func (m *ATMOnOffStatesInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ATMOnOffStatesInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ATMOnOffStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ATMOnOffStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ATMOnOffStatesInfoValidationError{
				field:  "Card",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return ATMOnOffStatesInfoMultiError(errors)
	}

	return nil
}

// ATMOnOffStatesInfoMultiError is an error wrapping multiple validation errors
// returned by ATMOnOffStatesInfo.ValidateAll() if the designated constraints
// aren't met.
type ATMOnOffStatesInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ATMOnOffStatesInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ATMOnOffStatesInfoMultiError) AllErrors() []error { return m }

// ATMOnOffStatesInfoValidationError is the validation error returned by
// ATMOnOffStatesInfo.Validate if the designated constraints aren't met.
type ATMOnOffStatesInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ATMOnOffStatesInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ATMOnOffStatesInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ATMOnOffStatesInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ATMOnOffStatesInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ATMOnOffStatesInfoValidationError) ErrorName() string {
	return "ATMOnOffStatesInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ATMOnOffStatesInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sATMOnOffStatesInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ATMOnOffStatesInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ATMOnOffStatesInfoValidationError{}

// Validate checks the field values on POSOnOffRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *POSOnOffRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on POSOnOffRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// POSOnOffRequestMultiError, or nil if none found.
func (m *POSOnOffRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *POSOnOffRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for Action

	// no validation rules for CredBlock

	// no validation rules for RequestId

	// no validation rules for ControlActionWorkflow

	if len(errors) > 0 {
		return POSOnOffRequestMultiError(errors)
	}

	return nil
}

// POSOnOffRequestMultiError is an error wrapping multiple validation errors
// returned by POSOnOffRequest.ValidateAll() if the designated constraints
// aren't met.
type POSOnOffRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m POSOnOffRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m POSOnOffRequestMultiError) AllErrors() []error { return m }

// POSOnOffRequestValidationError is the validation error returned by
// POSOnOffRequest.Validate if the designated constraints aren't met.
type POSOnOffRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e POSOnOffRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e POSOnOffRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e POSOnOffRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e POSOnOffRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e POSOnOffRequestValidationError) ErrorName() string { return "POSOnOffRequestValidationError" }

// Error satisfies the builtin error interface
func (e POSOnOffRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPOSOnOffRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = POSOnOffRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = POSOnOffRequestValidationError{}

// Validate checks the field values on POSOnOffResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *POSOnOffResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on POSOnOffResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// POSOnOffResponseMultiError, or nil if none found.
func (m *POSOnOffResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *POSOnOffResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, POSOnOffResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, POSOnOffResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return POSOnOffResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPosOnOffState()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, POSOnOffResponseValidationError{
					field:  "PosOnOffState",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, POSOnOffResponseValidationError{
					field:  "PosOnOffState",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPosOnOffState()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return POSOnOffResponseValidationError{
				field:  "PosOnOffState",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return POSOnOffResponseMultiError(errors)
	}

	return nil
}

// POSOnOffResponseMultiError is an error wrapping multiple validation errors
// returned by POSOnOffResponse.ValidateAll() if the designated constraints
// aren't met.
type POSOnOffResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m POSOnOffResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m POSOnOffResponseMultiError) AllErrors() []error { return m }

// POSOnOffResponseValidationError is the validation error returned by
// POSOnOffResponse.Validate if the designated constraints aren't met.
type POSOnOffResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e POSOnOffResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e POSOnOffResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e POSOnOffResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e POSOnOffResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e POSOnOffResponseValidationError) ErrorName() string { return "POSOnOffResponseValidationError" }

// Error satisfies the builtin error interface
func (e POSOnOffResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPOSOnOffResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = POSOnOffResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = POSOnOffResponseValidationError{}

// Validate checks the field values on POSOnOffStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *POSOnOffStatesInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on POSOnOffStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// POSOnOffStatesInfoMultiError, or nil if none found.
func (m *POSOnOffStatesInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *POSOnOffStatesInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, POSOnOffStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, POSOnOffStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return POSOnOffStatesInfoValidationError{
				field:  "Card",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return POSOnOffStatesInfoMultiError(errors)
	}

	return nil
}

// POSOnOffStatesInfoMultiError is an error wrapping multiple validation errors
// returned by POSOnOffStatesInfo.ValidateAll() if the designated constraints
// aren't met.
type POSOnOffStatesInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m POSOnOffStatesInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m POSOnOffStatesInfoMultiError) AllErrors() []error { return m }

// POSOnOffStatesInfoValidationError is the validation error returned by
// POSOnOffStatesInfo.Validate if the designated constraints aren't met.
type POSOnOffStatesInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e POSOnOffStatesInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e POSOnOffStatesInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e POSOnOffStatesInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e POSOnOffStatesInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e POSOnOffStatesInfoValidationError) ErrorName() string {
	return "POSOnOffStatesInfoValidationError"
}

// Error satisfies the builtin error interface
func (e POSOnOffStatesInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPOSOnOffStatesInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = POSOnOffStatesInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = POSOnOffStatesInfoValidationError{}

// Validate checks the field values on NfcOnOffRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NfcOnOffRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NfcOnOffRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NfcOnOffRequestMultiError, or nil if none found.
func (m *NfcOnOffRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *NfcOnOffRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Action

	// no validation rules for Vendor

	// no validation rules for CredBlock

	// no validation rules for RequestId

	// no validation rules for ControlActionWorkflow

	if len(errors) > 0 {
		return NfcOnOffRequestMultiError(errors)
	}

	return nil
}

// NfcOnOffRequestMultiError is an error wrapping multiple validation errors
// returned by NfcOnOffRequest.ValidateAll() if the designated constraints
// aren't met.
type NfcOnOffRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NfcOnOffRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NfcOnOffRequestMultiError) AllErrors() []error { return m }

// NfcOnOffRequestValidationError is the validation error returned by
// NfcOnOffRequest.Validate if the designated constraints aren't met.
type NfcOnOffRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NfcOnOffRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NfcOnOffRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NfcOnOffRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NfcOnOffRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NfcOnOffRequestValidationError) ErrorName() string { return "NfcOnOffRequestValidationError" }

// Error satisfies the builtin error interface
func (e NfcOnOffRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNfcOnOffRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NfcOnOffRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NfcOnOffRequestValidationError{}

// Validate checks the field values on NfcOnOffStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NfcOnOffStatesInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NfcOnOffStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NfcOnOffStatesInfoMultiError, or nil if none found.
func (m *NfcOnOffStatesInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NfcOnOffStatesInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NfcOnOffStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NfcOnOffStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NfcOnOffStatesInfoValidationError{
				field:  "Card",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return NfcOnOffStatesInfoMultiError(errors)
	}

	return nil
}

// NfcOnOffStatesInfoMultiError is an error wrapping multiple validation errors
// returned by NfcOnOffStatesInfo.ValidateAll() if the designated constraints
// aren't met.
type NfcOnOffStatesInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NfcOnOffStatesInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NfcOnOffStatesInfoMultiError) AllErrors() []error { return m }

// NfcOnOffStatesInfoValidationError is the validation error returned by
// NfcOnOffStatesInfo.Validate if the designated constraints aren't met.
type NfcOnOffStatesInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NfcOnOffStatesInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NfcOnOffStatesInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NfcOnOffStatesInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NfcOnOffStatesInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NfcOnOffStatesInfoValidationError) ErrorName() string {
	return "NfcOnOffStatesInfoValidationError"
}

// Error satisfies the builtin error interface
func (e NfcOnOffStatesInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNfcOnOffStatesInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NfcOnOffStatesInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NfcOnOffStatesInfoValidationError{}

// Validate checks the field values on NfcOnOffResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NfcOnOffResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NfcOnOffResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NfcOnOffResponseMultiError, or nil if none found.
func (m *NfcOnOffResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *NfcOnOffResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NfcOnOffResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NfcOnOffResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NfcOnOffResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetNfcOnOffStates()))
		i := 0
		for key := range m.GetNfcOnOffStates() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetNfcOnOffStates()[key]
			_ = val

			// no validation rules for NfcOnOffStates[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, NfcOnOffResponseValidationError{
							field:  fmt.Sprintf("NfcOnOffStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, NfcOnOffResponseValidationError{
							field:  fmt.Sprintf("NfcOnOffStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return NfcOnOffResponseValidationError{
						field:  fmt.Sprintf("NfcOnOffStates[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return NfcOnOffResponseMultiError(errors)
	}

	return nil
}

// NfcOnOffResponseMultiError is an error wrapping multiple validation errors
// returned by NfcOnOffResponse.ValidateAll() if the designated constraints
// aren't met.
type NfcOnOffResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NfcOnOffResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NfcOnOffResponseMultiError) AllErrors() []error { return m }

// NfcOnOffResponseValidationError is the validation error returned by
// NfcOnOffResponse.Validate if the designated constraints aren't met.
type NfcOnOffResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NfcOnOffResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NfcOnOffResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NfcOnOffResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NfcOnOffResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NfcOnOffResponseValidationError) ErrorName() string { return "NfcOnOffResponseValidationError" }

// Error satisfies the builtin error interface
func (e NfcOnOffResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNfcOnOffResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NfcOnOffResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NfcOnOffResponseValidationError{}

// Validate checks the field values on ControlOnOffRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ControlOnOffRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ControlOnOffRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ControlOnOffRequestMultiError, or nil if none found.
func (m *ControlOnOffRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ControlOnOffRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LocType

	// no validation rules for TxnType

	// no validation rules for Action

	// no validation rules for Vendor

	// no validation rules for CredBlock

	// no validation rules for RequestId

	if len(errors) > 0 {
		return ControlOnOffRequestMultiError(errors)
	}

	return nil
}

// ControlOnOffRequestMultiError is an error wrapping multiple validation
// errors returned by ControlOnOffRequest.ValidateAll() if the designated
// constraints aren't met.
type ControlOnOffRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ControlOnOffRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ControlOnOffRequestMultiError) AllErrors() []error { return m }

// ControlOnOffRequestValidationError is the validation error returned by
// ControlOnOffRequest.Validate if the designated constraints aren't met.
type ControlOnOffRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ControlOnOffRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ControlOnOffRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ControlOnOffRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ControlOnOffRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ControlOnOffRequestValidationError) ErrorName() string {
	return "ControlOnOffRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ControlOnOffRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sControlOnOffRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ControlOnOffRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ControlOnOffRequestValidationError{}

// Validate checks the field values on ControlOnOffStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ControlOnOffStatesInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ControlOnOffStatesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ControlOnOffStatesInfoMultiError, or nil if none found.
func (m *ControlOnOffStatesInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ControlOnOffStatesInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ControlOnOffStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ControlOnOffStatesInfoValidationError{
					field:  "Card",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ControlOnOffStatesInfoValidationError{
				field:  "Card",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	if len(errors) > 0 {
		return ControlOnOffStatesInfoMultiError(errors)
	}

	return nil
}

// ControlOnOffStatesInfoMultiError is an error wrapping multiple validation
// errors returned by ControlOnOffStatesInfo.ValidateAll() if the designated
// constraints aren't met.
type ControlOnOffStatesInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ControlOnOffStatesInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ControlOnOffStatesInfoMultiError) AllErrors() []error { return m }

// ControlOnOffStatesInfoValidationError is the validation error returned by
// ControlOnOffStatesInfo.Validate if the designated constraints aren't met.
type ControlOnOffStatesInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ControlOnOffStatesInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ControlOnOffStatesInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ControlOnOffStatesInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ControlOnOffStatesInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ControlOnOffStatesInfoValidationError) ErrorName() string {
	return "ControlOnOffStatesInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ControlOnOffStatesInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sControlOnOffStatesInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ControlOnOffStatesInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ControlOnOffStatesInfoValidationError{}

// Validate checks the field values on ControlOnOffResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ControlOnOffResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ControlOnOffResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ControlOnOffResponseMultiError, or nil if none found.
func (m *ControlOnOffResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ControlOnOffResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ControlOnOffResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ControlOnOffResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ControlOnOffResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetControlOnOffStates()))
		i := 0
		for key := range m.GetControlOnOffStates() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetControlOnOffStates()[key]
			_ = val

			// no validation rules for ControlOnOffStates[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ControlOnOffResponseValidationError{
							field:  fmt.Sprintf("ControlOnOffStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ControlOnOffResponseValidationError{
							field:  fmt.Sprintf("ControlOnOffStates[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ControlOnOffResponseValidationError{
						field:  fmt.Sprintf("ControlOnOffStates[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return ControlOnOffResponseMultiError(errors)
	}

	return nil
}

// ControlOnOffResponseMultiError is an error wrapping multiple validation
// errors returned by ControlOnOffResponse.ValidateAll() if the designated
// constraints aren't met.
type ControlOnOffResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ControlOnOffResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ControlOnOffResponseMultiError) AllErrors() []error { return m }

// ControlOnOffResponseValidationError is the validation error returned by
// ControlOnOffResponse.Validate if the designated constraints aren't met.
type ControlOnOffResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ControlOnOffResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ControlOnOffResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ControlOnOffResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ControlOnOffResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ControlOnOffResponseValidationError) ErrorName() string {
	return "ControlOnOffResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ControlOnOffResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sControlOnOffResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ControlOnOffResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ControlOnOffResponseValidationError{}

// Validate checks the field values on GetCardLimitsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardLimitsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardLimitsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCardLimitsRequestMultiError, or nil if none found.
func (m *GetCardLimitsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardLimitsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	if len(errors) > 0 {
		return GetCardLimitsRequestMultiError(errors)
	}

	return nil
}

// GetCardLimitsRequestMultiError is an error wrapping multiple validation
// errors returned by GetCardLimitsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCardLimitsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardLimitsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardLimitsRequestMultiError) AllErrors() []error { return m }

// GetCardLimitsRequestValidationError is the validation error returned by
// GetCardLimitsRequest.Validate if the designated constraints aren't met.
type GetCardLimitsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardLimitsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardLimitsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardLimitsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardLimitsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardLimitsRequestValidationError) ErrorName() string {
	return "GetCardLimitsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardLimitsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardLimitsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardLimitsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardLimitsRequestValidationError{}

// Validate checks the field values on GetCardLimitsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardLimitsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardLimitsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCardLimitsResponseMultiError, or nil if none found.
func (m *GetCardLimitsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardLimitsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardLimitsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardLimitsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardLimitsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardLimitData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardLimitsResponseValidationError{
					field:  "CardLimitData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardLimitsResponseValidationError{
					field:  "CardLimitData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardLimitData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardLimitsResponseValidationError{
				field:  "CardLimitData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for MaskedCardNumber

	if all {
		switch v := interface{}(m.GetDailyAllowedLimits()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardLimitsResponseValidationError{
					field:  "DailyAllowedLimits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardLimitsResponseValidationError{
					field:  "DailyAllowedLimits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDailyAllowedLimits()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardLimitsResponseValidationError{
				field:  "DailyAllowedLimits",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return GetCardLimitsResponseMultiError(errors)
	}

	return nil
}

// GetCardLimitsResponseMultiError is an error wrapping multiple validation
// errors returned by GetCardLimitsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCardLimitsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardLimitsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardLimitsResponseMultiError) AllErrors() []error { return m }

// GetCardLimitsResponseValidationError is the validation error returned by
// GetCardLimitsResponse.Validate if the designated constraints aren't met.
type GetCardLimitsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardLimitsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardLimitsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardLimitsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardLimitsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardLimitsResponseValidationError) ErrorName() string {
	return "GetCardLimitsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardLimitsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardLimitsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardLimitsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardLimitsResponseValidationError{}

// Validate checks the field values on UpdateCardLimitsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCardLimitsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCardLimitsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCardLimitsRequestMultiError, or nil if none found.
func (m *UpdateCardLimitsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCardLimitsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for RequestId

	// no validation rules for CredBlock

	for idx, item := range m.GetUpdateCardLimitDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateCardLimitsRequestValidationError{
						field:  fmt.Sprintf("UpdateCardLimitDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateCardLimitsRequestValidationError{
						field:  fmt.Sprintf("UpdateCardLimitDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateCardLimitsRequestValidationError{
					field:  fmt.Sprintf("UpdateCardLimitDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsLimitIncreased

	// no validation rules for IsUserInitiated

	if len(errors) > 0 {
		return UpdateCardLimitsRequestMultiError(errors)
	}

	return nil
}

// UpdateCardLimitsRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateCardLimitsRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateCardLimitsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCardLimitsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCardLimitsRequestMultiError) AllErrors() []error { return m }

// UpdateCardLimitsRequestValidationError is the validation error returned by
// UpdateCardLimitsRequest.Validate if the designated constraints aren't met.
type UpdateCardLimitsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCardLimitsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCardLimitsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCardLimitsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCardLimitsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCardLimitsRequestValidationError) ErrorName() string {
	return "UpdateCardLimitsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCardLimitsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCardLimitsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCardLimitsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCardLimitsRequestValidationError{}

// Validate checks the field values on UpdateCardLimitsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCardLimitsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCardLimitsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCardLimitsResponseMultiError, or nil if none found.
func (m *UpdateCardLimitsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCardLimitsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCardLimitsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCardLimitsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCardLimitsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardLimitData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCardLimitsResponseValidationError{
					field:  "CardLimitData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCardLimitsResponseValidationError{
					field:  "CardLimitData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardLimitData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCardLimitsResponseValidationError{
				field:  "CardLimitData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for MaskedCardNumber

	if all {
		switch v := interface{}(m.GetDailyAllowedLimits()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCardLimitsResponseValidationError{
					field:  "DailyAllowedLimits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCardLimitsResponseValidationError{
					field:  "DailyAllowedLimits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDailyAllowedLimits()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCardLimitsResponseValidationError{
				field:  "DailyAllowedLimits",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return UpdateCardLimitsResponseMultiError(errors)
	}

	return nil
}

// UpdateCardLimitsResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateCardLimitsResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateCardLimitsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCardLimitsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCardLimitsResponseMultiError) AllErrors() []error { return m }

// UpdateCardLimitsResponseValidationError is the validation error returned by
// UpdateCardLimitsResponse.Validate if the designated constraints aren't met.
type UpdateCardLimitsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCardLimitsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCardLimitsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCardLimitsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCardLimitsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCardLimitsResponseValidationError) ErrorName() string {
	return "UpdateCardLimitsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCardLimitsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCardLimitsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCardLimitsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCardLimitsResponseValidationError{}

// Validate checks the field values on AllowedLimits with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AllowedLimits) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AllowedLimits with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AllowedLimitsMultiError, or
// nil if none found.
func (m *AllowedLimits) ValidateAll() error {
	return m.validate(true)
}

func (m *AllowedLimits) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCardMaxLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AllowedLimitsValidationError{
					field:  "CardMaxLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AllowedLimitsValidationError{
					field:  "CardMaxLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardMaxLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AllowedLimitsValidationError{
				field:  "CardMaxLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPurchaseMaxLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AllowedLimitsValidationError{
					field:  "PurchaseMaxLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AllowedLimitsValidationError{
					field:  "PurchaseMaxLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPurchaseMaxLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AllowedLimitsValidationError{
				field:  "PurchaseMaxLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAtmMaxLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AllowedLimitsValidationError{
					field:  "AtmMaxLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AllowedLimitsValidationError{
					field:  "AtmMaxLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAtmMaxLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AllowedLimitsValidationError{
				field:  "AtmMaxLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AllowedLimitsMultiError(errors)
	}

	return nil
}

// AllowedLimitsMultiError is an error wrapping multiple validation errors
// returned by AllowedLimits.ValidateAll() if the designated constraints
// aren't met.
type AllowedLimitsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AllowedLimitsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AllowedLimitsMultiError) AllErrors() []error { return m }

// AllowedLimitsValidationError is the validation error returned by
// AllowedLimits.Validate if the designated constraints aren't met.
type AllowedLimitsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AllowedLimitsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AllowedLimitsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AllowedLimitsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AllowedLimitsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AllowedLimitsValidationError) ErrorName() string { return "AllowedLimitsValidationError" }

// Error satisfies the builtin error interface
func (e AllowedLimitsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAllowedLimits.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AllowedLimitsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AllowedLimitsValidationError{}

// Validate checks the field values on FetchCardLimitsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchCardLimitsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardLimitsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchCardLimitsRequestMultiError, or nil if none found.
func (m *FetchCardLimitsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardLimitsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	if len(errors) > 0 {
		return FetchCardLimitsRequestMultiError(errors)
	}

	return nil
}

// FetchCardLimitsRequestMultiError is an error wrapping multiple validation
// errors returned by FetchCardLimitsRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchCardLimitsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardLimitsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardLimitsRequestMultiError) AllErrors() []error { return m }

// FetchCardLimitsRequestValidationError is the validation error returned by
// FetchCardLimitsRequest.Validate if the designated constraints aren't met.
type FetchCardLimitsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardLimitsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardLimitsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardLimitsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardLimitsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardLimitsRequestValidationError) ErrorName() string {
	return "FetchCardLimitsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardLimitsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardLimitsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardLimitsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardLimitsRequestValidationError{}

// Validate checks the field values on FetchCardLimitsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchCardLimitsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCardLimitsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchCardLimitsResponseMultiError, or nil if none found.
func (m *FetchCardLimitsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCardLimitsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCardLimitsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCardLimitsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCardLimitsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardLimitData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCardLimitsResponseValidationError{
					field:  "CardLimitData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCardLimitsResponseValidationError{
					field:  "CardLimitData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardLimitData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCardLimitsResponseValidationError{
				field:  "CardLimitData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchCardLimitsResponseMultiError(errors)
	}

	return nil
}

// FetchCardLimitsResponseMultiError is an error wrapping multiple validation
// errors returned by FetchCardLimitsResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchCardLimitsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCardLimitsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCardLimitsResponseMultiError) AllErrors() []error { return m }

// FetchCardLimitsResponseValidationError is the validation error returned by
// FetchCardLimitsResponse.Validate if the designated constraints aren't met.
type FetchCardLimitsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCardLimitsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCardLimitsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCardLimitsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCardLimitsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCardLimitsResponseValidationError) ErrorName() string {
	return "FetchCardLimitsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCardLimitsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCardLimitsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCardLimitsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCardLimitsResponseValidationError{}

// Validate checks the field values on ConsolidatedCardControlOnOffRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ConsolidatedCardControlOnOffRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsolidatedCardControlOnOffRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ConsolidatedCardControlOnOffRequestMultiError, or nil if none found.
func (m *ConsolidatedCardControlOnOffRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsolidatedCardControlOnOffRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for ControlActions

	// no validation rules for RequestId

	// no validation rules for CredBlock

	// no validation rules for ControlWorkflow

	// no validation rules for ControlActionWorkflow

	if len(errors) > 0 {
		return ConsolidatedCardControlOnOffRequestMultiError(errors)
	}

	return nil
}

// ConsolidatedCardControlOnOffRequestMultiError is an error wrapping multiple
// validation errors returned by
// ConsolidatedCardControlOnOffRequest.ValidateAll() if the designated
// constraints aren't met.
type ConsolidatedCardControlOnOffRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsolidatedCardControlOnOffRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsolidatedCardControlOnOffRequestMultiError) AllErrors() []error { return m }

// ConsolidatedCardControlOnOffRequestValidationError is the validation error
// returned by ConsolidatedCardControlOnOffRequest.Validate if the designated
// constraints aren't met.
type ConsolidatedCardControlOnOffRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsolidatedCardControlOnOffRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsolidatedCardControlOnOffRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsolidatedCardControlOnOffRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsolidatedCardControlOnOffRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsolidatedCardControlOnOffRequestValidationError) ErrorName() string {
	return "ConsolidatedCardControlOnOffRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConsolidatedCardControlOnOffRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsolidatedCardControlOnOffRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsolidatedCardControlOnOffRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsolidatedCardControlOnOffRequestValidationError{}

// Validate checks the field values on ConsolidatedCardControlOnOffResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ConsolidatedCardControlOnOffResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsolidatedCardControlOnOffResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ConsolidatedCardControlOnOffResponseMultiError, or nil if none found.
func (m *ConsolidatedCardControlOnOffResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsolidatedCardControlOnOffResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsolidatedCardControlOnOffResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsolidatedCardControlOnOffResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsolidatedCardControlOnOffResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternalResponseCode

	if len(errors) > 0 {
		return ConsolidatedCardControlOnOffResponseMultiError(errors)
	}

	return nil
}

// ConsolidatedCardControlOnOffResponseMultiError is an error wrapping multiple
// validation errors returned by
// ConsolidatedCardControlOnOffResponse.ValidateAll() if the designated
// constraints aren't met.
type ConsolidatedCardControlOnOffResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsolidatedCardControlOnOffResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsolidatedCardControlOnOffResponseMultiError) AllErrors() []error { return m }

// ConsolidatedCardControlOnOffResponseValidationError is the validation error
// returned by ConsolidatedCardControlOnOffResponse.Validate if the designated
// constraints aren't met.
type ConsolidatedCardControlOnOffResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsolidatedCardControlOnOffResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsolidatedCardControlOnOffResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsolidatedCardControlOnOffResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsolidatedCardControlOnOffResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsolidatedCardControlOnOffResponseValidationError) ErrorName() string {
	return "ConsolidatedCardControlOnOffResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConsolidatedCardControlOnOffResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsolidatedCardControlOnOffResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsolidatedCardControlOnOffResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsolidatedCardControlOnOffResponseValidationError{}

// Validate checks the field values on
// GetInternationalAtmLimitsRequest_CountryCodes with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetInternationalAtmLimitsRequest_CountryCodes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetInternationalAtmLimitsRequest_CountryCodes with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetInternationalAtmLimitsRequest_CountryCodesMultiError, or nil if none found.
func (m *GetInternationalAtmLimitsRequest_CountryCodes) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInternationalAtmLimitsRequest_CountryCodes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetInternationalAtmLimitsRequest_CountryCodesMultiError(errors)
	}

	return nil
}

// GetInternationalAtmLimitsRequest_CountryCodesMultiError is an error wrapping
// multiple validation errors returned by
// GetInternationalAtmLimitsRequest_CountryCodes.ValidateAll() if the
// designated constraints aren't met.
type GetInternationalAtmLimitsRequest_CountryCodesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInternationalAtmLimitsRequest_CountryCodesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInternationalAtmLimitsRequest_CountryCodesMultiError) AllErrors() []error { return m }

// GetInternationalAtmLimitsRequest_CountryCodesValidationError is the
// validation error returned by
// GetInternationalAtmLimitsRequest_CountryCodes.Validate if the designated
// constraints aren't met.
type GetInternationalAtmLimitsRequest_CountryCodesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInternationalAtmLimitsRequest_CountryCodesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInternationalAtmLimitsRequest_CountryCodesValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetInternationalAtmLimitsRequest_CountryCodesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInternationalAtmLimitsRequest_CountryCodesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInternationalAtmLimitsRequest_CountryCodesValidationError) ErrorName() string {
	return "GetInternationalAtmLimitsRequest_CountryCodesValidationError"
}

// Error satisfies the builtin error interface
func (e GetInternationalAtmLimitsRequest_CountryCodesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInternationalAtmLimitsRequest_CountryCodes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInternationalAtmLimitsRequest_CountryCodesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInternationalAtmLimitsRequest_CountryCodesValidationError{}
