// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/control/card_limit.proto

package control

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	card "github.com/epifi/gamma/api/card"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = card.CardTransactionType(0)
)

// Validate checks the field values on CardLimit with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CardLimit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardLimit with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CardLimitMultiError, or nil
// if none found.
func (m *CardLimit) ValidateAll() error {
	return m.validate(true)
}

func (m *CardLimit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	if all {
		switch v := interface{}(m.GetCardLimitData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardLimitValidationError{
					field:  "CardLimitData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardLimitValidationError{
					field:  "CardLimitData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardLimitData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardLimitValidationError{
				field:  "CardLimitData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardLimitState

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardLimitValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardLimitValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardLimitValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardLimitValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardLimitValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardLimitValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardLimitValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardLimitValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardLimitValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardLimitMultiError(errors)
	}

	return nil
}

// CardLimitMultiError is an error wrapping multiple validation errors returned
// by CardLimit.ValidateAll() if the designated constraints aren't met.
type CardLimitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardLimitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardLimitMultiError) AllErrors() []error { return m }

// CardLimitValidationError is the validation error returned by
// CardLimit.Validate if the designated constraints aren't met.
type CardLimitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardLimitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardLimitValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardLimitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardLimitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardLimitValidationError) ErrorName() string { return "CardLimitValidationError" }

// Error satisfies the builtin error interface
func (e CardLimitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardLimit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardLimitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardLimitValidationError{}

// Validate checks the field values on CardLimitData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CardLimitData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardLimitData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CardLimitDataMultiError, or
// nil if none found.
func (m *CardLimitData) ValidateAll() error {
	return m.validate(true)
}

func (m *CardLimitData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCardLimitDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CardLimitDataValidationError{
						field:  fmt.Sprintf("CardLimitDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CardLimitDataValidationError{
						field:  fmt.Sprintf("CardLimitDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CardLimitDataValidationError{
					field:  fmt.Sprintf("CardLimitDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CardLimitDataMultiError(errors)
	}

	return nil
}

// CardLimitDataMultiError is an error wrapping multiple validation errors
// returned by CardLimitData.ValidateAll() if the designated constraints
// aren't met.
type CardLimitDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardLimitDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardLimitDataMultiError) AllErrors() []error { return m }

// CardLimitDataValidationError is the validation error returned by
// CardLimitData.Validate if the designated constraints aren't met.
type CardLimitDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardLimitDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardLimitDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardLimitDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardLimitDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardLimitDataValidationError) ErrorName() string { return "CardLimitDataValidationError" }

// Error satisfies the builtin error interface
func (e CardLimitDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardLimitData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardLimitDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardLimitDataValidationError{}

// Validate checks the field values on CardLimitDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CardLimitDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardLimitDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardLimitDetailMultiError, or nil if none found.
func (m *CardLimitDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *CardLimitDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnType

	// no validation rules for LocType

	if all {
		switch v := interface{}(m.GetCurrentAllowedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardLimitDetailValidationError{
					field:  "CurrentAllowedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardLimitDetailValidationError{
					field:  "CurrentAllowedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentAllowedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardLimitDetailValidationError{
				field:  "CurrentAllowedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxAllowedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardLimitDetailValidationError{
					field:  "MaxAllowedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardLimitDetailValidationError{
					field:  "MaxAllowedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxAllowedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardLimitDetailValidationError{
				field:  "MaxAllowedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardLimitDetailMultiError(errors)
	}

	return nil
}

// CardLimitDetailMultiError is an error wrapping multiple validation errors
// returned by CardLimitDetail.ValidateAll() if the designated constraints
// aren't met.
type CardLimitDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardLimitDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardLimitDetailMultiError) AllErrors() []error { return m }

// CardLimitDetailValidationError is the validation error returned by
// CardLimitDetail.Validate if the designated constraints aren't met.
type CardLimitDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardLimitDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardLimitDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardLimitDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardLimitDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardLimitDetailValidationError) ErrorName() string { return "CardLimitDetailValidationError" }

// Error satisfies the builtin error interface
func (e CardLimitDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardLimitDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardLimitDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardLimitDetailValidationError{}
