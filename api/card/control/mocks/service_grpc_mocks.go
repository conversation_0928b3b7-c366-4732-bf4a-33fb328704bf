// Code generated by MockGen. DO NOT EDIT.
// Source: api/card/control/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	control "github.com/epifi/gamma/api/card/control"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCardControlClient is a mock of CardControlClient interface.
type MockCardControlClient struct {
	ctrl     *gomock.Controller
	recorder *MockCardControlClientMockRecorder
}

// MockCardControlClientMockRecorder is the mock recorder for MockCardControlClient.
type MockCardControlClientMockRecorder struct {
	mock *MockCardControlClient
}

// NewMockCardControlClient creates a new mock instance.
func NewMockCardControlClient(ctrl *gomock.Controller) *MockCardControlClient {
	mock := &MockCardControlClient{ctrl: ctrl}
	mock.recorder = &MockCardControlClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardControlClient) EXPECT() *MockCardControlClientMockRecorder {
	return m.recorder
}

// ATMOnOff mocks base method.
func (m *MockCardControlClient) ATMOnOff(ctx context.Context, in *control.ATMOnOffRequest, opts ...grpc.CallOption) (*control.ATMOnOffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ATMOnOff", varargs...)
	ret0, _ := ret[0].(*control.ATMOnOffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ATMOnOff indicates an expected call of ATMOnOff.
func (mr *MockCardControlClientMockRecorder) ATMOnOff(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ATMOnOff", reflect.TypeOf((*MockCardControlClient)(nil).ATMOnOff), varargs...)
}

// BlockCard mocks base method.
func (m *MockCardControlClient) BlockCard(ctx context.Context, in *control.BlockCardRequest, opts ...grpc.CallOption) (*control.BlockCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BlockCard", varargs...)
	ret0, _ := ret[0].(*control.BlockCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BlockCard indicates an expected call of BlockCard.
func (mr *MockCardControlClientMockRecorder) BlockCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BlockCard", reflect.TypeOf((*MockCardControlClient)(nil).BlockCard), varargs...)
}

// ConsolidatedCardControlOnOff mocks base method.
func (m *MockCardControlClient) ConsolidatedCardControlOnOff(ctx context.Context, in *control.ConsolidatedCardControlOnOffRequest, opts ...grpc.CallOption) (*control.ConsolidatedCardControlOnOffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConsolidatedCardControlOnOff", varargs...)
	ret0, _ := ret[0].(*control.ConsolidatedCardControlOnOffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConsolidatedCardControlOnOff indicates an expected call of ConsolidatedCardControlOnOff.
func (mr *MockCardControlClientMockRecorder) ConsolidatedCardControlOnOff(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConsolidatedCardControlOnOff", reflect.TypeOf((*MockCardControlClient)(nil).ConsolidatedCardControlOnOff), varargs...)
}

// ControlOnOff mocks base method.
func (m *MockCardControlClient) ControlOnOff(ctx context.Context, in *control.ControlOnOffRequest, opts ...grpc.CallOption) (*control.ControlOnOffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ControlOnOff", varargs...)
	ret0, _ := ret[0].(*control.ControlOnOffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ControlOnOff indicates an expected call of ControlOnOff.
func (mr *MockCardControlClientMockRecorder) ControlOnOff(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ControlOnOff", reflect.TypeOf((*MockCardControlClient)(nil).ControlOnOff), varargs...)
}

// ECommerceOnOff mocks base method.
func (m *MockCardControlClient) ECommerceOnOff(ctx context.Context, in *control.ECommerceOnOffRequest, opts ...grpc.CallOption) (*control.ECommerceOnOffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ECommerceOnOff", varargs...)
	ret0, _ := ret[0].(*control.ECommerceOnOffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ECommerceOnOff indicates an expected call of ECommerceOnOff.
func (mr *MockCardControlClientMockRecorder) ECommerceOnOff(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ECommerceOnOff", reflect.TypeOf((*MockCardControlClient)(nil).ECommerceOnOff), varargs...)
}

// FetchCardLimits mocks base method.
func (m *MockCardControlClient) FetchCardLimits(ctx context.Context, in *control.FetchCardLimitsRequest, opts ...grpc.CallOption) (*control.FetchCardLimitsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchCardLimits", varargs...)
	ret0, _ := ret[0].(*control.FetchCardLimitsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCardLimits indicates an expected call of FetchCardLimits.
func (mr *MockCardControlClientMockRecorder) FetchCardLimits(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCardLimits", reflect.TypeOf((*MockCardControlClient)(nil).FetchCardLimits), varargs...)
}

// GetCardLimits mocks base method.
func (m *MockCardControlClient) GetCardLimits(ctx context.Context, in *control.GetCardLimitsRequest, opts ...grpc.CallOption) (*control.GetCardLimitsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCardLimits", varargs...)
	ret0, _ := ret[0].(*control.GetCardLimitsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardLimits indicates an expected call of GetCardLimits.
func (mr *MockCardControlClientMockRecorder) GetCardLimits(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardLimits", reflect.TypeOf((*MockCardControlClient)(nil).GetCardLimits), varargs...)
}

// GetInternationalAtmLimits mocks base method.
func (m *MockCardControlClient) GetInternationalAtmLimits(ctx context.Context, in *control.GetInternationalAtmLimitsRequest, opts ...grpc.CallOption) (*control.GetInternationalAtmLimitsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInternationalAtmLimits", varargs...)
	ret0, _ := ret[0].(*control.GetInternationalAtmLimitsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInternationalAtmLimits indicates an expected call of GetInternationalAtmLimits.
func (mr *MockCardControlClientMockRecorder) GetInternationalAtmLimits(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInternationalAtmLimits", reflect.TypeOf((*MockCardControlClient)(nil).GetInternationalAtmLimits), varargs...)
}

// GetTravelMode mocks base method.
func (m *MockCardControlClient) GetTravelMode(ctx context.Context, in *control.GetTravelModeRequest, opts ...grpc.CallOption) (*control.GetTravelModeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTravelMode", varargs...)
	ret0, _ := ret[0].(*control.GetTravelModeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTravelMode indicates an expected call of GetTravelMode.
func (mr *MockCardControlClientMockRecorder) GetTravelMode(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTravelMode", reflect.TypeOf((*MockCardControlClient)(nil).GetTravelMode), varargs...)
}

// LocationOnOff mocks base method.
func (m *MockCardControlClient) LocationOnOff(ctx context.Context, in *control.LocationOnOffRequest, opts ...grpc.CallOption) (*control.LocationOnOffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LocationOnOff", varargs...)
	ret0, _ := ret[0].(*control.LocationOnOffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LocationOnOff indicates an expected call of LocationOnOff.
func (mr *MockCardControlClientMockRecorder) LocationOnOff(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LocationOnOff", reflect.TypeOf((*MockCardControlClient)(nil).LocationOnOff), varargs...)
}

// NfcOnOff mocks base method.
func (m *MockCardControlClient) NfcOnOff(ctx context.Context, in *control.NfcOnOffRequest, opts ...grpc.CallOption) (*control.NfcOnOffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "NfcOnOff", varargs...)
	ret0, _ := ret[0].(*control.NfcOnOffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NfcOnOff indicates an expected call of NfcOnOff.
func (mr *MockCardControlClientMockRecorder) NfcOnOff(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NfcOnOff", reflect.TypeOf((*MockCardControlClient)(nil).NfcOnOff), varargs...)
}

// POSOnOff mocks base method.
func (m *MockCardControlClient) POSOnOff(ctx context.Context, in *control.POSOnOffRequest, opts ...grpc.CallOption) (*control.POSOnOffResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "POSOnOff", varargs...)
	ret0, _ := ret[0].(*control.POSOnOffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// POSOnOff indicates an expected call of POSOnOff.
func (mr *MockCardControlClientMockRecorder) POSOnOff(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "POSOnOff", reflect.TypeOf((*MockCardControlClient)(nil).POSOnOff), varargs...)
}

// SetTravelMode mocks base method.
func (m *MockCardControlClient) SetTravelMode(ctx context.Context, in *control.SetTravelModeRequest, opts ...grpc.CallOption) (*control.SetTravelModeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetTravelMode", varargs...)
	ret0, _ := ret[0].(*control.SetTravelModeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetTravelMode indicates an expected call of SetTravelMode.
func (mr *MockCardControlClientMockRecorder) SetTravelMode(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTravelMode", reflect.TypeOf((*MockCardControlClient)(nil).SetTravelMode), varargs...)
}

// SuspendCard mocks base method.
func (m *MockCardControlClient) SuspendCard(ctx context.Context, in *control.SuspendCardRequest, opts ...grpc.CallOption) (*control.SuspendCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SuspendCard", varargs...)
	ret0, _ := ret[0].(*control.SuspendCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SuspendCard indicates an expected call of SuspendCard.
func (mr *MockCardControlClientMockRecorder) SuspendCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SuspendCard", reflect.TypeOf((*MockCardControlClient)(nil).SuspendCard), varargs...)
}

// UpdateCardLimits mocks base method.
func (m *MockCardControlClient) UpdateCardLimits(ctx context.Context, in *control.UpdateCardLimitsRequest, opts ...grpc.CallOption) (*control.UpdateCardLimitsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCardLimits", varargs...)
	ret0, _ := ret[0].(*control.UpdateCardLimitsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCardLimits indicates an expected call of UpdateCardLimits.
func (mr *MockCardControlClientMockRecorder) UpdateCardLimits(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCardLimits", reflect.TypeOf((*MockCardControlClient)(nil).UpdateCardLimits), varargs...)
}

// MockCardControlServer is a mock of CardControlServer interface.
type MockCardControlServer struct {
	ctrl     *gomock.Controller
	recorder *MockCardControlServerMockRecorder
}

// MockCardControlServerMockRecorder is the mock recorder for MockCardControlServer.
type MockCardControlServerMockRecorder struct {
	mock *MockCardControlServer
}

// NewMockCardControlServer creates a new mock instance.
func NewMockCardControlServer(ctrl *gomock.Controller) *MockCardControlServer {
	mock := &MockCardControlServer{ctrl: ctrl}
	mock.recorder = &MockCardControlServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardControlServer) EXPECT() *MockCardControlServerMockRecorder {
	return m.recorder
}

// ATMOnOff mocks base method.
func (m *MockCardControlServer) ATMOnOff(arg0 context.Context, arg1 *control.ATMOnOffRequest) (*control.ATMOnOffResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ATMOnOff", arg0, arg1)
	ret0, _ := ret[0].(*control.ATMOnOffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ATMOnOff indicates an expected call of ATMOnOff.
func (mr *MockCardControlServerMockRecorder) ATMOnOff(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ATMOnOff", reflect.TypeOf((*MockCardControlServer)(nil).ATMOnOff), arg0, arg1)
}

// BlockCard mocks base method.
func (m *MockCardControlServer) BlockCard(arg0 context.Context, arg1 *control.BlockCardRequest) (*control.BlockCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BlockCard", arg0, arg1)
	ret0, _ := ret[0].(*control.BlockCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BlockCard indicates an expected call of BlockCard.
func (mr *MockCardControlServerMockRecorder) BlockCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BlockCard", reflect.TypeOf((*MockCardControlServer)(nil).BlockCard), arg0, arg1)
}

// ConsolidatedCardControlOnOff mocks base method.
func (m *MockCardControlServer) ConsolidatedCardControlOnOff(arg0 context.Context, arg1 *control.ConsolidatedCardControlOnOffRequest) (*control.ConsolidatedCardControlOnOffResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConsolidatedCardControlOnOff", arg0, arg1)
	ret0, _ := ret[0].(*control.ConsolidatedCardControlOnOffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConsolidatedCardControlOnOff indicates an expected call of ConsolidatedCardControlOnOff.
func (mr *MockCardControlServerMockRecorder) ConsolidatedCardControlOnOff(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConsolidatedCardControlOnOff", reflect.TypeOf((*MockCardControlServer)(nil).ConsolidatedCardControlOnOff), arg0, arg1)
}

// ControlOnOff mocks base method.
func (m *MockCardControlServer) ControlOnOff(arg0 context.Context, arg1 *control.ControlOnOffRequest) (*control.ControlOnOffResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ControlOnOff", arg0, arg1)
	ret0, _ := ret[0].(*control.ControlOnOffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ControlOnOff indicates an expected call of ControlOnOff.
func (mr *MockCardControlServerMockRecorder) ControlOnOff(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ControlOnOff", reflect.TypeOf((*MockCardControlServer)(nil).ControlOnOff), arg0, arg1)
}

// ECommerceOnOff mocks base method.
func (m *MockCardControlServer) ECommerceOnOff(arg0 context.Context, arg1 *control.ECommerceOnOffRequest) (*control.ECommerceOnOffResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ECommerceOnOff", arg0, arg1)
	ret0, _ := ret[0].(*control.ECommerceOnOffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ECommerceOnOff indicates an expected call of ECommerceOnOff.
func (mr *MockCardControlServerMockRecorder) ECommerceOnOff(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ECommerceOnOff", reflect.TypeOf((*MockCardControlServer)(nil).ECommerceOnOff), arg0, arg1)
}

// FetchCardLimits mocks base method.
func (m *MockCardControlServer) FetchCardLimits(arg0 context.Context, arg1 *control.FetchCardLimitsRequest) (*control.FetchCardLimitsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchCardLimits", arg0, arg1)
	ret0, _ := ret[0].(*control.FetchCardLimitsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchCardLimits indicates an expected call of FetchCardLimits.
func (mr *MockCardControlServerMockRecorder) FetchCardLimits(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchCardLimits", reflect.TypeOf((*MockCardControlServer)(nil).FetchCardLimits), arg0, arg1)
}

// GetCardLimits mocks base method.
func (m *MockCardControlServer) GetCardLimits(arg0 context.Context, arg1 *control.GetCardLimitsRequest) (*control.GetCardLimitsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardLimits", arg0, arg1)
	ret0, _ := ret[0].(*control.GetCardLimitsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardLimits indicates an expected call of GetCardLimits.
func (mr *MockCardControlServerMockRecorder) GetCardLimits(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardLimits", reflect.TypeOf((*MockCardControlServer)(nil).GetCardLimits), arg0, arg1)
}

// GetInternationalAtmLimits mocks base method.
func (m *MockCardControlServer) GetInternationalAtmLimits(arg0 context.Context, arg1 *control.GetInternationalAtmLimitsRequest) (*control.GetInternationalAtmLimitsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInternationalAtmLimits", arg0, arg1)
	ret0, _ := ret[0].(*control.GetInternationalAtmLimitsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInternationalAtmLimits indicates an expected call of GetInternationalAtmLimits.
func (mr *MockCardControlServerMockRecorder) GetInternationalAtmLimits(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInternationalAtmLimits", reflect.TypeOf((*MockCardControlServer)(nil).GetInternationalAtmLimits), arg0, arg1)
}

// GetTravelMode mocks base method.
func (m *MockCardControlServer) GetTravelMode(arg0 context.Context, arg1 *control.GetTravelModeRequest) (*control.GetTravelModeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTravelMode", arg0, arg1)
	ret0, _ := ret[0].(*control.GetTravelModeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTravelMode indicates an expected call of GetTravelMode.
func (mr *MockCardControlServerMockRecorder) GetTravelMode(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTravelMode", reflect.TypeOf((*MockCardControlServer)(nil).GetTravelMode), arg0, arg1)
}

// LocationOnOff mocks base method.
func (m *MockCardControlServer) LocationOnOff(arg0 context.Context, arg1 *control.LocationOnOffRequest) (*control.LocationOnOffResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LocationOnOff", arg0, arg1)
	ret0, _ := ret[0].(*control.LocationOnOffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LocationOnOff indicates an expected call of LocationOnOff.
func (mr *MockCardControlServerMockRecorder) LocationOnOff(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LocationOnOff", reflect.TypeOf((*MockCardControlServer)(nil).LocationOnOff), arg0, arg1)
}

// NfcOnOff mocks base method.
func (m *MockCardControlServer) NfcOnOff(arg0 context.Context, arg1 *control.NfcOnOffRequest) (*control.NfcOnOffResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NfcOnOff", arg0, arg1)
	ret0, _ := ret[0].(*control.NfcOnOffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NfcOnOff indicates an expected call of NfcOnOff.
func (mr *MockCardControlServerMockRecorder) NfcOnOff(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NfcOnOff", reflect.TypeOf((*MockCardControlServer)(nil).NfcOnOff), arg0, arg1)
}

// POSOnOff mocks base method.
func (m *MockCardControlServer) POSOnOff(arg0 context.Context, arg1 *control.POSOnOffRequest) (*control.POSOnOffResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "POSOnOff", arg0, arg1)
	ret0, _ := ret[0].(*control.POSOnOffResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// POSOnOff indicates an expected call of POSOnOff.
func (mr *MockCardControlServerMockRecorder) POSOnOff(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "POSOnOff", reflect.TypeOf((*MockCardControlServer)(nil).POSOnOff), arg0, arg1)
}

// SetTravelMode mocks base method.
func (m *MockCardControlServer) SetTravelMode(arg0 context.Context, arg1 *control.SetTravelModeRequest) (*control.SetTravelModeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetTravelMode", arg0, arg1)
	ret0, _ := ret[0].(*control.SetTravelModeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetTravelMode indicates an expected call of SetTravelMode.
func (mr *MockCardControlServerMockRecorder) SetTravelMode(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTravelMode", reflect.TypeOf((*MockCardControlServer)(nil).SetTravelMode), arg0, arg1)
}

// SuspendCard mocks base method.
func (m *MockCardControlServer) SuspendCard(arg0 context.Context, arg1 *control.SuspendCardRequest) (*control.SuspendCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SuspendCard", arg0, arg1)
	ret0, _ := ret[0].(*control.SuspendCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SuspendCard indicates an expected call of SuspendCard.
func (mr *MockCardControlServerMockRecorder) SuspendCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SuspendCard", reflect.TypeOf((*MockCardControlServer)(nil).SuspendCard), arg0, arg1)
}

// UpdateCardLimits mocks base method.
func (m *MockCardControlServer) UpdateCardLimits(arg0 context.Context, arg1 *control.UpdateCardLimitsRequest) (*control.UpdateCardLimitsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCardLimits", arg0, arg1)
	ret0, _ := ret[0].(*control.UpdateCardLimitsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCardLimits indicates an expected call of UpdateCardLimits.
func (mr *MockCardControlServerMockRecorder) UpdateCardLimits(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCardLimits", reflect.TypeOf((*MockCardControlServer)(nil).UpdateCardLimits), arg0, arg1)
}

// MockUnsafeCardControlServer is a mock of UnsafeCardControlServer interface.
type MockUnsafeCardControlServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCardControlServerMockRecorder
}

// MockUnsafeCardControlServerMockRecorder is the mock recorder for MockUnsafeCardControlServer.
type MockUnsafeCardControlServerMockRecorder struct {
	mock *MockUnsafeCardControlServer
}

// NewMockUnsafeCardControlServer creates a new mock instance.
func NewMockUnsafeCardControlServer(ctrl *gomock.Controller) *MockUnsafeCardControlServer {
	mock := &MockUnsafeCardControlServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCardControlServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCardControlServer) EXPECT() *MockUnsafeCardControlServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCardControlServer mocks base method.
func (m *MockUnsafeCardControlServer) mustEmbedUnimplementedCardControlServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCardControlServer")
}

// mustEmbedUnimplementedCardControlServer indicates an expected call of mustEmbedUnimplementedCardControlServer.
func (mr *MockUnsafeCardControlServerMockRecorder) mustEmbedUnimplementedCardControlServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCardControlServer", reflect.TypeOf((*MockUnsafeCardControlServer)(nil).mustEmbedUnimplementedCardControlServer))
}
