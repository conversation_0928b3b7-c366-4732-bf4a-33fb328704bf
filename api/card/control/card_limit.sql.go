package control

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

//type CardLimitDetails []*card.CardLimitDetail

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x CardLimitState) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *CardLimitState) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := CardLimitState_value[val]
	*x = CardLimitState(valInt)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (c *CardLimitData) Value() (driver.Value, error) {
	if c == nil {
		return nil, nil
	}
	return protojson.Marshal(c)
}

// <PERSON><PERSON> implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (c *CardLimitData) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, c)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}
