// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/control/card_limit.proto

package control

import (
	card "github.com/epifi/gamma/api/card"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CardLimitState int32

const (
	// unspecified limit state
	CardLimitState_LIMIT_STATE_UNSPECIFIED CardLimitState = 0
	// state to represent stale limit data. There is possibility that card limit data is not updated.
	// When data stale, backend need to refresh card limit data with vendor.
	CardLimitState_STALE CardLimitState = 1
	// State to represent that card limit data in database is updated and we can directly use this.
	CardLimitState_UPDATED CardLimitState = 2
)

// Enum value maps for CardLimitState.
var (
	CardLimitState_name = map[int32]string{
		0: "LIMIT_STATE_UNSPECIFIED",
		1: "STALE",
		2: "UPDATED",
	}
	CardLimitState_value = map[string]int32{
		"LIMIT_STATE_UNSPECIFIED": 0,
		"STALE":                   1,
		"UPDATED":                 2,
	}
)

func (x CardLimitState) Enum() *CardLimitState {
	p := new(CardLimitState)
	*p = x
	return p
}

func (x CardLimitState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardLimitState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_card_limit_proto_enumTypes[0].Descriptor()
}

func (CardLimitState) Type() protoreflect.EnumType {
	return &file_api_card_control_card_limit_proto_enumTypes[0]
}

func (x CardLimitState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardLimitState.Descriptor instead.
func (CardLimitState) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_card_limit_proto_rawDescGZIP(), []int{0}
}

type CardLimitFieldMask int32

const (
	CardLimitFieldMask_CARD_LIMIT_FIELD_MASK_UNSPECIFIED CardLimitFieldMask = 0
	CardLimitFieldMask_CARD_LIMIT_STATE                  CardLimitFieldMask = 1
	CardLimitFieldMask_CARD_LIMIT_DATA                   CardLimitFieldMask = 2
	CardLimitFieldMask_CARD_LIMIT_CARD_ID                CardLimitFieldMask = 3
	CardLimitFieldMask_CARD_LIMIT_CREATED_AT             CardLimitFieldMask = 4
	CardLimitFieldMask_CARD_LIMIT_UPDATED_AT             CardLimitFieldMask = 5
	CardLimitFieldMask_CARD_LIMIT_DELETED_AT             CardLimitFieldMask = 6
)

// Enum value maps for CardLimitFieldMask.
var (
	CardLimitFieldMask_name = map[int32]string{
		0: "CARD_LIMIT_FIELD_MASK_UNSPECIFIED",
		1: "CARD_LIMIT_STATE",
		2: "CARD_LIMIT_DATA",
		3: "CARD_LIMIT_CARD_ID",
		4: "CARD_LIMIT_CREATED_AT",
		5: "CARD_LIMIT_UPDATED_AT",
		6: "CARD_LIMIT_DELETED_AT",
	}
	CardLimitFieldMask_value = map[string]int32{
		"CARD_LIMIT_FIELD_MASK_UNSPECIFIED": 0,
		"CARD_LIMIT_STATE":                  1,
		"CARD_LIMIT_DATA":                   2,
		"CARD_LIMIT_CARD_ID":                3,
		"CARD_LIMIT_CREATED_AT":             4,
		"CARD_LIMIT_UPDATED_AT":             5,
		"CARD_LIMIT_DELETED_AT":             6,
	}
)

func (x CardLimitFieldMask) Enum() *CardLimitFieldMask {
	p := new(CardLimitFieldMask)
	*p = x
	return p
}

func (x CardLimitFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardLimitFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_control_card_limit_proto_enumTypes[1].Descriptor()
}

func (CardLimitFieldMask) Type() protoreflect.EnumType {
	return &file_api_card_control_card_limit_proto_enumTypes[1]
}

func (x CardLimitFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardLimitFieldMask.Descriptor instead.
func (CardLimitFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_card_control_card_limit_proto_rawDescGZIP(), []int{1}
}

// Domain proto for card limit in the backend system.
// Contains details for a card limit. Limit will be applied to all kinds of transaction associated with card.
type CardLimit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Primary identifier of a card in database model.
	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// Card limit detail having all limit config applied on the card.
	CardLimitData *CardLimitData `protobuf:"bytes,2,opt,name=card_limit_data,json=cardLimitData,proto3" json:"card_limit_data,omitempty"`
	// state of card limit
	CardLimitState CardLimitState `protobuf:"varint,3,opt,name=card_limit_state,json=cardLimitState,proto3,enum=card.control.CardLimitState" json:"card_limit_state,omitempty"`
	// card creation timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// card update timestamp
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *CardLimit) Reset() {
	*x = CardLimit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_card_limit_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardLimit) ProtoMessage() {}

func (x *CardLimit) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_card_limit_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardLimit.ProtoReflect.Descriptor instead.
func (*CardLimit) Descriptor() ([]byte, []int) {
	return file_api_card_control_card_limit_proto_rawDescGZIP(), []int{0}
}

func (x *CardLimit) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CardLimit) GetCardLimitData() *CardLimitData {
	if x != nil {
		return x.CardLimitData
	}
	return nil
}

func (x *CardLimit) GetCardLimitState() CardLimitState {
	if x != nil {
		return x.CardLimitState
	}
	return CardLimitState_LIMIT_STATE_UNSPECIFIED
}

func (x *CardLimit) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CardLimit) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CardLimit) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

// proto wrapper for json array of card limit details.
type CardLimitData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardLimitDetails []*CardLimitDetail `protobuf:"bytes,1,rep,name=card_limit_details,json=cardLimitDetails,proto3" json:"card_limit_details,omitempty"`
}

func (x *CardLimitData) Reset() {
	*x = CardLimitData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_card_limit_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardLimitData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardLimitData) ProtoMessage() {}

func (x *CardLimitData) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_card_limit_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardLimitData.ProtoReflect.Descriptor instead.
func (*CardLimitData) Descriptor() ([]byte, []int) {
	return file_api_card_control_card_limit_proto_rawDescGZIP(), []int{1}
}

func (x *CardLimitData) GetCardLimitDetails() []*CardLimitDetail {
	if x != nil {
		return x.CardLimitDetails
	}
	return nil
}

// CardLimitDetail maintain data for limit applied on card transaction.
// A CardLimitDetail defined by card transaction type , usage location type , limit type and allowed limit amount.
type CardLimitDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// txn type on which this limit will be applicable
	TxnType card.CardTransactionType `protobuf:"varint,1,opt,name=txn_type,json=txnType,proto3,enum=card.CardTransactionType" json:"txn_type,omitempty"`
	// applicable limit location type
	LocType card.CardUsageLocationType `protobuf:"varint,2,opt,name=loc_type,json=locType,proto3,enum=card.CardUsageLocationType" json:"loc_type,omitempty"`
	// current amount set for the limit. User can't use money more than this amount on txnType+locType channel.
	CurrentAllowedAmount *money.Money `protobuf:"bytes,3,opt,name=current_allowed_amount,json=currentAllowedAmount,proto3" json:"current_allowed_amount,omitempty"`
	// max amount that can be set in current amount for limit.
	MaxAllowedAmount *money.Money `protobuf:"bytes,4,opt,name=max_allowed_amount,json=maxAllowedAmount,proto3" json:"max_allowed_amount,omitempty"`
}

func (x *CardLimitDetail) Reset() {
	*x = CardLimitDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_control_card_limit_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardLimitDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardLimitDetail) ProtoMessage() {}

func (x *CardLimitDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_control_card_limit_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardLimitDetail.ProtoReflect.Descriptor instead.
func (*CardLimitDetail) Descriptor() ([]byte, []int) {
	return file_api_card_control_card_limit_proto_rawDescGZIP(), []int{2}
}

func (x *CardLimitDetail) GetTxnType() card.CardTransactionType {
	if x != nil {
		return x.TxnType
	}
	return card.CardTransactionType(0)
}

func (x *CardLimitDetail) GetLocType() card.CardUsageLocationType {
	if x != nil {
		return x.LocType
	}
	return card.CardUsageLocationType(0)
}

func (x *CardLimitDetail) GetCurrentAllowedAmount() *money.Money {
	if x != nil {
		return x.CurrentAllowedAmount
	}
	return nil
}

func (x *CardLimitDetail) GetMaxAllowedAmount() *money.Money {
	if x != nil {
		return x.MaxAllowedAmount
	}
	return nil
}

var File_api_card_control_card_limit_proto protoreflect.FileDescriptor

var file_api_card_control_card_limit_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xe2, 0x02, 0x0a, 0x09, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x46, 0x0a, 0x10, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x63, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x5c, 0x0a, 0x0d, 0x43, 0x61, 0x72, 0x64,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4b, 0x0a, 0x12, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x10, 0x63, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x8b, 0x02, 0x0a, 0x0f, 0x43, 0x61, 0x72, 0x64, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x34, 0x0a, 0x08, 0x74, 0x78,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x74, 0x78, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x36, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x55, 0x73,
	0x61, 0x67, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x07, 0x6c, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x48, 0x0a, 0x16, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x40, 0x0a, 0x12, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x10, 0x6d, 0x61, 0x78, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x2a, 0x45, 0x0a, 0x0e, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x54, 0x41, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0b,
	0x0a, 0x07, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x2a, 0xcf, 0x01, 0x0a, 0x12,
	0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12,
	0x13, 0x0a, 0x0f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4c, 0x49, 0x4d,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x04, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54,
	0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54,
	0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x06, 0x42, 0x52, 0x0a,
	0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_control_card_limit_proto_rawDescOnce sync.Once
	file_api_card_control_card_limit_proto_rawDescData = file_api_card_control_card_limit_proto_rawDesc
)

func file_api_card_control_card_limit_proto_rawDescGZIP() []byte {
	file_api_card_control_card_limit_proto_rawDescOnce.Do(func() {
		file_api_card_control_card_limit_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_control_card_limit_proto_rawDescData)
	})
	return file_api_card_control_card_limit_proto_rawDescData
}

var file_api_card_control_card_limit_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_card_control_card_limit_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_card_control_card_limit_proto_goTypes = []interface{}{
	(CardLimitState)(0),             // 0: card.control.CardLimitState
	(CardLimitFieldMask)(0),         // 1: card.control.CardLimitFieldMask
	(*CardLimit)(nil),               // 2: card.control.CardLimit
	(*CardLimitData)(nil),           // 3: card.control.CardLimitData
	(*CardLimitDetail)(nil),         // 4: card.control.CardLimitDetail
	(*timestamppb.Timestamp)(nil),   // 5: google.protobuf.Timestamp
	(card.CardTransactionType)(0),   // 6: card.CardTransactionType
	(card.CardUsageLocationType)(0), // 7: card.CardUsageLocationType
	(*money.Money)(nil),             // 8: google.type.Money
}
var file_api_card_control_card_limit_proto_depIdxs = []int32{
	3,  // 0: card.control.CardLimit.card_limit_data:type_name -> card.control.CardLimitData
	0,  // 1: card.control.CardLimit.card_limit_state:type_name -> card.control.CardLimitState
	5,  // 2: card.control.CardLimit.created_at:type_name -> google.protobuf.Timestamp
	5,  // 3: card.control.CardLimit.updated_at:type_name -> google.protobuf.Timestamp
	5,  // 4: card.control.CardLimit.deleted_at:type_name -> google.protobuf.Timestamp
	4,  // 5: card.control.CardLimitData.card_limit_details:type_name -> card.control.CardLimitDetail
	6,  // 6: card.control.CardLimitDetail.txn_type:type_name -> card.CardTransactionType
	7,  // 7: card.control.CardLimitDetail.loc_type:type_name -> card.CardUsageLocationType
	8,  // 8: card.control.CardLimitDetail.current_allowed_amount:type_name -> google.type.Money
	8,  // 9: card.control.CardLimitDetail.max_allowed_amount:type_name -> google.type.Money
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_api_card_control_card_limit_proto_init() }
func file_api_card_control_card_limit_proto_init() {
	if File_api_card_control_card_limit_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_control_card_limit_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardLimit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_card_limit_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardLimitData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_control_card_limit_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardLimitDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_control_card_limit_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_control_card_limit_proto_goTypes,
		DependencyIndexes: file_api_card_control_card_limit_proto_depIdxs,
		EnumInfos:         file_api_card_control_card_limit_proto_enumTypes,
		MessageInfos:      file_api_card_control_card_limit_proto_msgTypes,
	}.Build()
	File_api_card_control_card_limit_proto = out.File
	file_api_card_control_card_limit_proto_rawDesc = nil
	file_api_card_control_card_limit_proto_goTypes = nil
	file_api_card_control_card_limit_proto_depIdxs = nil
}
