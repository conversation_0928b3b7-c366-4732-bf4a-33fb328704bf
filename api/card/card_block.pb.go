// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/card_block.proto

package card

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BlockCardState int32

const (
	BlockCardState_BLOCK_CARD_STATE_UNSPECIFIED BlockCardState = 0
	// BLOCK_CARD_QUEUED - request is queued to the retry queue and will be processed shortly
	BlockCardState_BLOCK_CARD_QUEUED BlockCardState = 1
	// BLOCK_CARD_INITIATED - request is successfully initiated with the partner bank. Awaiting update.
	BlockCardState_BLOCK_CARD_INITIATED BlockCardState = 2
	// BLOCK_CARD_SUCCESS - request was successfully processed at the partner bank.
	BlockCardState_BLOCK_CARD_SUCCESS BlockCardState = 3
	// BLOCK_CARD_FAILED - request failed at the partner bank. Denotes a non-retry-able failure.
	BlockCardState_BLOCK_CARD_FAILED BlockCardState = 4
	// BLOCK_CARD_MANUAL_INTERVENTION- System has exhausted all the retries post transient errors so this needs attention from a human.
	BlockCardState_BLOCK_CARD_MANUAL_INTERVENTION BlockCardState = 6
)

// Enum value maps for BlockCardState.
var (
	BlockCardState_name = map[int32]string{
		0: "BLOCK_CARD_STATE_UNSPECIFIED",
		1: "BLOCK_CARD_QUEUED",
		2: "BLOCK_CARD_INITIATED",
		3: "BLOCK_CARD_SUCCESS",
		4: "BLOCK_CARD_FAILED",
		6: "BLOCK_CARD_MANUAL_INTERVENTION",
	}
	BlockCardState_value = map[string]int32{
		"BLOCK_CARD_STATE_UNSPECIFIED":   0,
		"BLOCK_CARD_QUEUED":              1,
		"BLOCK_CARD_INITIATED":           2,
		"BLOCK_CARD_SUCCESS":             3,
		"BLOCK_CARD_FAILED":              4,
		"BLOCK_CARD_MANUAL_INTERVENTION": 6,
	}
)

func (x BlockCardState) Enum() *BlockCardState {
	p := new(BlockCardState)
	*p = x
	return p
}

func (x BlockCardState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BlockCardState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_block_proto_enumTypes[0].Descriptor()
}

func (BlockCardState) Type() protoreflect.EnumType {
	return &file_api_card_card_block_proto_enumTypes[0]
}

func (x BlockCardState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BlockCardState.Descriptor instead.
func (BlockCardState) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_block_proto_rawDescGZIP(), []int{0}
}

// Provenance: the beginning of something's existence; something's origin.
// For example : Card can be blocked in the system from different entry points
// e.g. APP (user initiated), SHERLOCK (agents initiated)
// This enum represents different entry provenance of request in the system
type Provenance int32

const (
	Provenance_PROVENANCE_UNSPECIFIED Provenance = 0
	Provenance_USER_APP               Provenance = 1
	Provenance_SHERLOCK               Provenance = 2
	// When the card was blocked by the bank, e.x. hotmarked card
	Provenance_BANK Provenance = 3
	Provenance_IVR  Provenance = 4
)

// Enum value maps for Provenance.
var (
	Provenance_name = map[int32]string{
		0: "PROVENANCE_UNSPECIFIED",
		1: "USER_APP",
		2: "SHERLOCK",
		3: "BANK",
		4: "IVR",
	}
	Provenance_value = map[string]int32{
		"PROVENANCE_UNSPECIFIED": 0,
		"USER_APP":               1,
		"SHERLOCK":               2,
		"BANK":                   3,
		"IVR":                    4,
	}
)

func (x Provenance) Enum() *Provenance {
	p := new(Provenance)
	*p = x
	return p
}

func (x Provenance) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Provenance) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_block_proto_enumTypes[1].Descriptor()
}

func (Provenance) Type() protoreflect.EnumType {
	return &file_api_card_card_block_proto_enumTypes[1]
}

func (x Provenance) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Provenance.Descriptor instead.
func (Provenance) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_block_proto_rawDescGZIP(), []int{1}
}

type CardBlockDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Primary identifier of a card in database model.
	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// Reason for blocking the card
	Reason     string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	Provenance Provenance             `protobuf:"varint,3,opt,name=provenance,proto3,enum=card.Provenance" json:"provenance,omitempty"`
	State      BlockCardState         `protobuf:"varint,4,opt,name=state,proto3,enum=card.BlockCardState" json:"state,omitempty"`
	CreatedAt  *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	DeletedAt  *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *CardBlockDetail) Reset() {
	*x = CardBlockDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_block_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardBlockDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardBlockDetail) ProtoMessage() {}

func (x *CardBlockDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_block_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardBlockDetail.ProtoReflect.Descriptor instead.
func (*CardBlockDetail) Descriptor() ([]byte, []int) {
	return file_api_card_card_block_proto_rawDescGZIP(), []int{0}
}

func (x *CardBlockDetail) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CardBlockDetail) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *CardBlockDetail) GetProvenance() Provenance {
	if x != nil {
		return x.Provenance
	}
	return Provenance_PROVENANCE_UNSPECIFIED
}

func (x *CardBlockDetail) GetState() BlockCardState {
	if x != nil {
		return x.State
	}
	return BlockCardState_BLOCK_CARD_STATE_UNSPECIFIED
}

func (x *CardBlockDetail) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CardBlockDetail) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_card_card_block_proto protoreflect.FileDescriptor

var file_api_card_card_block_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x63, 0x61, 0x72,
	0x64, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x96, 0x02, 0x0a, 0x0f, 0x43, 0x61, 0x72, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0xb6, 0x01, 0x0a, 0x0e,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x20,
	0x0a, 0x1c, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x15, 0x0a, 0x11, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x51,
	0x55, 0x45, 0x55, 0x45, 0x44, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x42, 0x4c, 0x4f, 0x43, 0x4b,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10,
	0x02, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x42, 0x4c, 0x4f,
	0x43, 0x4b, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04,
	0x12, 0x22, 0x0a, 0x1e, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4d,
	0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x06, 0x2a, 0x57, 0x0a, 0x0a, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08,
	0x53, 0x48, 0x45, 0x52, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x42, 0x41,
	0x4e, 0x4b, 0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x56, 0x52, 0x10, 0x04, 0x42, 0x42, 0x0a,
	0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72,
	0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_card_block_proto_rawDescOnce sync.Once
	file_api_card_card_block_proto_rawDescData = file_api_card_card_block_proto_rawDesc
)

func file_api_card_card_block_proto_rawDescGZIP() []byte {
	file_api_card_card_block_proto_rawDescOnce.Do(func() {
		file_api_card_card_block_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_card_block_proto_rawDescData)
	})
	return file_api_card_card_block_proto_rawDescData
}

var file_api_card_card_block_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_card_card_block_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_card_card_block_proto_goTypes = []interface{}{
	(BlockCardState)(0),           // 0: card.BlockCardState
	(Provenance)(0),               // 1: card.Provenance
	(*CardBlockDetail)(nil),       // 2: card.CardBlockDetail
	(*timestamppb.Timestamp)(nil), // 3: google.protobuf.Timestamp
}
var file_api_card_card_block_proto_depIdxs = []int32{
	1, // 0: card.CardBlockDetail.provenance:type_name -> card.Provenance
	0, // 1: card.CardBlockDetail.state:type_name -> card.BlockCardState
	3, // 2: card.CardBlockDetail.created_at:type_name -> google.protobuf.Timestamp
	3, // 3: card.CardBlockDetail.deleted_at:type_name -> google.protobuf.Timestamp
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_card_card_block_proto_init() }
func file_api_card_card_block_proto_init() {
	if File_api_card_card_block_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_card_block_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardBlockDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_card_block_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_card_block_proto_goTypes,
		DependencyIndexes: file_api_card_card_block_proto_depIdxs,
		EnumInfos:         file_api_card_card_block_proto_enumTypes,
		MessageInfos:      file_api_card_card_block_proto_msgTypes,
	}.Build()
	File_api_card_card_block_proto = out.File
	file_api_card_card_block_proto_rawDesc = nil
	file_api_card_card_block_proto_goTypes = nil
	file_api_card_card_block_proto_depIdxs = nil
}
