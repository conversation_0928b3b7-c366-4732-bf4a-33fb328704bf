// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/card_block.proto

package card

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CardBlockDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CardBlockDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardBlockDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardBlockDetailMultiError, or nil if none found.
func (m *CardBlockDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *CardBlockDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for Reason

	// no validation rules for Provenance

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardBlockDetailValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardBlockDetailValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardBlockDetailValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardBlockDetailValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardBlockDetailValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardBlockDetailValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardBlockDetailMultiError(errors)
	}

	return nil
}

// CardBlockDetailMultiError is an error wrapping multiple validation errors
// returned by CardBlockDetail.ValidateAll() if the designated constraints
// aren't met.
type CardBlockDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardBlockDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardBlockDetailMultiError) AllErrors() []error { return m }

// CardBlockDetailValidationError is the validation error returned by
// CardBlockDetail.Validate if the designated constraints aren't met.
type CardBlockDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardBlockDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardBlockDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardBlockDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardBlockDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardBlockDetailValidationError) ErrorName() string { return "CardBlockDetailValidationError" }

// Error satisfies the builtin error interface
func (e CardBlockDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardBlockDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardBlockDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardBlockDetailValidationError{}
