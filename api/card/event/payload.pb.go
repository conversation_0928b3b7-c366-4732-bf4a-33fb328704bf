// protolint:disable MAX_LINE_LENGTH
//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/event/payload.proto

package event

import (
	queue "github.com/epifi/be-common/api/queue"
	common "github.com/epifi/be-common/api/typesv2/common"
	card "github.com/epifi/gamma/api/card"
	control "github.com/epifi/gamma/api/card/control"
	provisioning "github.com/epifi/gamma/api/card/provisioning"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DebitCardUpdateEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	Payload       *DebitCardEventPayload       `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
}

func (x *DebitCardUpdateEvent) Reset() {
	*x = DebitCardUpdateEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_event_payload_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebitCardUpdateEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebitCardUpdateEvent) ProtoMessage() {}

func (x *DebitCardUpdateEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_event_payload_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebitCardUpdateEvent.ProtoReflect.Descriptor instead.
func (*DebitCardUpdateEvent) Descriptor() ([]byte, []int) {
	return file_api_card_event_payload_proto_rawDescGZIP(), []int{0}
}

func (x *DebitCardUpdateEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *DebitCardUpdateEvent) GetPayload() *DebitCardEventPayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

// exhaustive data for any type of debit card action that has taken place based on which
// processing decisions will be made in the consumer
// CURRENTlY USED ONLY IN CARD ACTIVATION EVENT
type DebitCardEventPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardId  string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// details regarding various types of limits like atm, pos, etc.
	// it will be null in case the event does not concern card limits
	CardLimitData *control.CardLimitData `protobuf:"bytes,3,opt,name=card_limit_data,json=cardLimitData,proto3" json:"card_limit_data,omitempty"`
	// contains enabled/disabled config for all card controls.
	// it will be null in case the event does not concern card controls
	CardControlData *card.ControlData `protobuf:"bytes,4,opt,name=card_control_data,json=cardControlData,proto3" json:"card_control_data,omitempty"`
	// boolean denoting whether physical card activation has been done for the card .
	// It will be BOOLEAN_ENUM_UNSPECIFIED in case the event is not for card activation.
	PhysicalCardActivated common.BooleanEnum `protobuf:"varint,5,opt,name=physical_card_activated,json=physicalCardActivated,proto3,enum=api.typesv2.common.BooleanEnum" json:"physical_card_activated,omitempty"`
	// current state of the card . it will be  CARD_STATE_UNSPECIFIED in case event does not concern
	// card state
	CardState card.CardState `protobuf:"varint,6,opt,name=card_state,json=cardState,proto3,enum=card.CardState" json:"card_state,omitempty"`
	// delivery status of physical debit card. CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED in case the
	// event does not concern card delivery
	CardDeliveryStatus provisioning.CardTrackingDeliveryState `protobuf:"varint,7,opt,name=card_delivery_status,json=cardDeliveryStatus,proto3,enum=card.provisioning.CardTrackingDeliveryState" json:"card_delivery_status,omitempty"`
	// timestamp of update
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *DebitCardEventPayload) Reset() {
	*x = DebitCardEventPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_event_payload_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebitCardEventPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebitCardEventPayload) ProtoMessage() {}

func (x *DebitCardEventPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_event_payload_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebitCardEventPayload.ProtoReflect.Descriptor instead.
func (*DebitCardEventPayload) Descriptor() ([]byte, []int) {
	return file_api_card_event_payload_proto_rawDescGZIP(), []int{1}
}

func (x *DebitCardEventPayload) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *DebitCardEventPayload) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DebitCardEventPayload) GetCardLimitData() *control.CardLimitData {
	if x != nil {
		return x.CardLimitData
	}
	return nil
}

func (x *DebitCardEventPayload) GetCardControlData() *card.ControlData {
	if x != nil {
		return x.CardControlData
	}
	return nil
}

func (x *DebitCardEventPayload) GetPhysicalCardActivated() common.BooleanEnum {
	if x != nil {
		return x.PhysicalCardActivated
	}
	return common.BooleanEnum(0)
}

func (x *DebitCardEventPayload) GetCardState() card.CardState {
	if x != nil {
		return x.CardState
	}
	return card.CardState(0)
}

func (x *DebitCardEventPayload) GetCardDeliveryStatus() provisioning.CardTrackingDeliveryState {
	if x != nil {
		return x.CardDeliveryStatus
	}
	return provisioning.CardTrackingDeliveryState(0)
}

func (x *DebitCardEventPayload) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_api_card_event_payload_proto protoreflect.FileDescriptor

var file_api_card_event_payload_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x2f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x16, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72,
	0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e,
	0x67, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61,
	0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x98, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x3b, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x44,
	0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0xf3, 0x03,
	0x0a, 0x15, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x3d, 0x0a, 0x11, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0f,
	0x63, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x57, 0x0a, 0x17, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75,
	0x6d, 0x52, 0x15, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x12, 0x2e, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x09, 0x63,
	0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x5e, 0x0a, 0x14, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x12, 0x63, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5a, 0x25, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_event_payload_proto_rawDescOnce sync.Once
	file_api_card_event_payload_proto_rawDescData = file_api_card_event_payload_proto_rawDesc
)

func file_api_card_event_payload_proto_rawDescGZIP() []byte {
	file_api_card_event_payload_proto_rawDescOnce.Do(func() {
		file_api_card_event_payload_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_event_payload_proto_rawDescData)
	})
	return file_api_card_event_payload_proto_rawDescData
}

var file_api_card_event_payload_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_card_event_payload_proto_goTypes = []interface{}{
	(*DebitCardUpdateEvent)(nil),                // 0: card.event.DebitCardUpdateEvent
	(*DebitCardEventPayload)(nil),               // 1: card.event.DebitCardEventPayload
	(*queue.ConsumerRequestHeader)(nil),         // 2: queue.ConsumerRequestHeader
	(*control.CardLimitData)(nil),               // 3: card.control.CardLimitData
	(*card.ControlData)(nil),                    // 4: card.ControlData
	(common.BooleanEnum)(0),                     // 5: api.typesv2.common.BooleanEnum
	(card.CardState)(0),                         // 6: card.CardState
	(provisioning.CardTrackingDeliveryState)(0), // 7: card.provisioning.CardTrackingDeliveryState
	(*timestamppb.Timestamp)(nil),               // 8: google.protobuf.Timestamp
}
var file_api_card_event_payload_proto_depIdxs = []int32{
	2, // 0: card.event.DebitCardUpdateEvent.request_header:type_name -> queue.ConsumerRequestHeader
	1, // 1: card.event.DebitCardUpdateEvent.payload:type_name -> card.event.DebitCardEventPayload
	3, // 2: card.event.DebitCardEventPayload.card_limit_data:type_name -> card.control.CardLimitData
	4, // 3: card.event.DebitCardEventPayload.card_control_data:type_name -> card.ControlData
	5, // 4: card.event.DebitCardEventPayload.physical_card_activated:type_name -> api.typesv2.common.BooleanEnum
	6, // 5: card.event.DebitCardEventPayload.card_state:type_name -> card.CardState
	7, // 6: card.event.DebitCardEventPayload.card_delivery_status:type_name -> card.provisioning.CardTrackingDeliveryState
	8, // 7: card.event.DebitCardEventPayload.updated_at:type_name -> google.protobuf.Timestamp
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_card_event_payload_proto_init() }
func file_api_card_event_payload_proto_init() {
	if File_api_card_event_payload_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_event_payload_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebitCardUpdateEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_event_payload_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebitCardEventPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_event_payload_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_event_payload_proto_goTypes,
		DependencyIndexes: file_api_card_event_payload_proto_depIdxs,
		MessageInfos:      file_api_card_event_payload_proto_msgTypes,
	}.Build()
	File_api_card_event_payload_proto = out.File
	file_api_card_event_payload_proto_rawDesc = nil
	file_api_card_event_payload_proto_goTypes = nil
	file_api_card_event_payload_proto_depIdxs = nil
}
