// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/event/payload.proto

package event

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	card "github.com/epifi/gamma/api/card"

	common "github.com/epifi/be-common/api/typesv2/common"

	provisioning "github.com/epifi/gamma/api/card/provisioning"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = card.CardState(0)

	_ = common.BooleanEnum(0)

	_ = provisioning.CardTrackingDeliveryState(0)
)

// Validate checks the field values on DebitCardUpdateEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DebitCardUpdateEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DebitCardUpdateEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DebitCardUpdateEventMultiError, or nil if none found.
func (m *DebitCardUpdateEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *DebitCardUpdateEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DebitCardUpdateEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DebitCardUpdateEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DebitCardUpdateEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DebitCardUpdateEventValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DebitCardUpdateEventValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DebitCardUpdateEventValidationError{
				field:  "Payload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DebitCardUpdateEventMultiError(errors)
	}

	return nil
}

// DebitCardUpdateEventMultiError is an error wrapping multiple validation
// errors returned by DebitCardUpdateEvent.ValidateAll() if the designated
// constraints aren't met.
type DebitCardUpdateEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DebitCardUpdateEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DebitCardUpdateEventMultiError) AllErrors() []error { return m }

// DebitCardUpdateEventValidationError is the validation error returned by
// DebitCardUpdateEvent.Validate if the designated constraints aren't met.
type DebitCardUpdateEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DebitCardUpdateEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DebitCardUpdateEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DebitCardUpdateEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DebitCardUpdateEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DebitCardUpdateEventValidationError) ErrorName() string {
	return "DebitCardUpdateEventValidationError"
}

// Error satisfies the builtin error interface
func (e DebitCardUpdateEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDebitCardUpdateEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DebitCardUpdateEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DebitCardUpdateEventValidationError{}

// Validate checks the field values on DebitCardEventPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DebitCardEventPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DebitCardEventPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DebitCardEventPayloadMultiError, or nil if none found.
func (m *DebitCardEventPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *DebitCardEventPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetCardLimitData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DebitCardEventPayloadValidationError{
					field:  "CardLimitData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DebitCardEventPayloadValidationError{
					field:  "CardLimitData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardLimitData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DebitCardEventPayloadValidationError{
				field:  "CardLimitData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardControlData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DebitCardEventPayloadValidationError{
					field:  "CardControlData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DebitCardEventPayloadValidationError{
					field:  "CardControlData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardControlData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DebitCardEventPayloadValidationError{
				field:  "CardControlData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PhysicalCardActivated

	// no validation rules for CardState

	// no validation rules for CardDeliveryStatus

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DebitCardEventPayloadValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DebitCardEventPayloadValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DebitCardEventPayloadValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DebitCardEventPayloadMultiError(errors)
	}

	return nil
}

// DebitCardEventPayloadMultiError is an error wrapping multiple validation
// errors returned by DebitCardEventPayload.ValidateAll() if the designated
// constraints aren't met.
type DebitCardEventPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DebitCardEventPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DebitCardEventPayloadMultiError) AllErrors() []error { return m }

// DebitCardEventPayloadValidationError is the validation error returned by
// DebitCardEventPayload.Validate if the designated constraints aren't met.
type DebitCardEventPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DebitCardEventPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DebitCardEventPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DebitCardEventPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DebitCardEventPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DebitCardEventPayloadValidationError) ErrorName() string {
	return "DebitCardEventPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e DebitCardEventPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDebitCardEventPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DebitCardEventPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DebitCardEventPayloadValidationError{}
