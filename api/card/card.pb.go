// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/card.proto

package card

import (
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Consolidated card state.
type CardState int32

const (
	// Card creation was not even requested.
	CardState_CARD_STATE_UNSPECIFIED CardState = 0
	// INITIATED - Card creation was requested and is under processing.
	// The actual request could be either waiting in the Queue or
	// pending at the partner bank.
	CardState_INITIATED CardState = 1
	// CREATED - the card was successfully created.
	CardState_CREATED CardState = 2
	// ACTIVATED - the card was successfully activated. A card must be
	// created before activation.
	CardState_ACTIVATED CardState = 3
	// SUSPENDED - card was suspended. It is temporarily un-usable.
	CardState_SUSPENDED CardState = 4
	// BLOCK - the card was permanently blocked and cannot be used.
	CardState_BLOCKED CardState = 5
	// EXPIRED - the card has expired and cannot be used.
	CardState_EXPIRED CardState = 6
	// INVALID - if card creation fails then we will move the card to INVALID STATE
	CardState_INVALID CardState = 7
	// CLOSED - card was closed. Card is marked closed when savings account is closed.
	CardState_CLOSED CardState = 8
)

// Enum value maps for CardState.
var (
	CardState_name = map[int32]string{
		0: "CARD_STATE_UNSPECIFIED",
		1: "INITIATED",
		2: "CREATED",
		3: "ACTIVATED",
		4: "SUSPENDED",
		5: "BLOCKED",
		6: "EXPIRED",
		7: "INVALID",
		8: "CLOSED",
	}
	CardState_value = map[string]int32{
		"CARD_STATE_UNSPECIFIED": 0,
		"INITIATED":              1,
		"CREATED":                2,
		"ACTIVATED":              3,
		"SUSPENDED":              4,
		"BLOCKED":                5,
		"EXPIRED":                6,
		"INVALID":                7,
		"CLOSED":                 8,
	}
)

func (x CardState) Enum() *CardState {
	p := new(CardState)
	*p = x
	return p
}

func (x CardState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_proto_enumTypes[0].Descriptor()
}

func (CardState) Type() protoreflect.EnumType {
	return &file_api_card_card_proto_enumTypes[0]
}

func (x CardState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardState.Descriptor instead.
func (CardState) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_proto_rawDescGZIP(), []int{0}
}

// CardType is used to distinguish between DEBIT and CREDIT card api.typesv2.
type CardType int32

const (
	CardType_CARD_TYPE_UNSPECIFIED CardType = 0
	CardType_DEBIT                 CardType = 1
	CardType_CREDIT                CardType = 2
)

// Enum value maps for CardType.
var (
	CardType_name = map[int32]string{
		0: "CARD_TYPE_UNSPECIFIED",
		1: "DEBIT",
		2: "CREDIT",
	}
	CardType_value = map[string]int32{
		"CARD_TYPE_UNSPECIFIED": 0,
		"DEBIT":                 1,
		"CREDIT":                2,
	}
)

func (x CardType) Enum() *CardType {
	p := new(CardType)
	*p = x
	return p
}

func (x CardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_proto_enumTypes[1].Descriptor()
}

func (CardType) Type() protoreflect.EnumType {
	return &file_api_card_card_proto_enumTypes[1]
}

func (x CardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardType.Descriptor instead.
func (CardType) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_proto_rawDescGZIP(), []int{1}
}

// TODO: should form be internal to service? Instead should we work with TxnType here?
// Decide above after product clarifies whether user will have knowledge of two card forms.
// We will launch with 2 debit cards being provisioned for every user.
// The physical card and the digital card.
// The physical card will be used for ATM/POS/NFC transactions while the
// digital card is used only for online, e-commerce transactions.
type CardForm int32

const (
	CardForm_CARD_FORM_UNSPECIFIED CardForm = 0
	// PHYSICAL card to be used for ATM, POS transactions
	CardForm_PHYSICAL CardForm = 1
	// DIGITAL card to be used for online/e-com transactions
	CardForm_DIGITAL CardForm = 2
)

// Enum value maps for CardForm.
var (
	CardForm_name = map[int32]string{
		0: "CARD_FORM_UNSPECIFIED",
		1: "PHYSICAL",
		2: "DIGITAL",
	}
	CardForm_value = map[string]int32{
		"CARD_FORM_UNSPECIFIED": 0,
		"PHYSICAL":              1,
		"DIGITAL":               2,
	}
)

func (x CardForm) Enum() *CardForm {
	p := new(CardForm)
	*p = x
	return p
}

func (x CardForm) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardForm) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_proto_enumTypes[2].Descriptor()
}

func (CardForm) Type() protoreflect.EnumType {
	return &file_api_card_card_proto_enumTypes[2]
}

func (x CardForm) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardForm.Descriptor instead.
func (CardForm) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_proto_rawDescGZIP(), []int{2}
}

// CardNetworkType denotes the card network of the underlying card.
// Card networks are financial services companies that enables, processes and
// settles payments between card issuing banks and merchant banks worldwide.
// Examples are Visa, MasterCard, Discover, RuPay etc.
type CardNetworkType int32

const (
	CardNetworkType_CARD_NETWORK_TYPE_UNSPECIFIED CardNetworkType = 0
	CardNetworkType_VISA                          CardNetworkType = 1
	CardNetworkType_MASTER                        CardNetworkType = 2
	CardNetworkType_DISCOVER                      CardNetworkType = 3
	CardNetworkType_AMEX                          CardNetworkType = 4
	CardNetworkType_RUPAY                         CardNetworkType = 5
	CardNetworkType_DINERS                        CardNetworkType = 6
)

// Enum value maps for CardNetworkType.
var (
	CardNetworkType_name = map[int32]string{
		0: "CARD_NETWORK_TYPE_UNSPECIFIED",
		1: "VISA",
		2: "MASTER",
		3: "DISCOVER",
		4: "AMEX",
		5: "RUPAY",
		6: "DINERS",
	}
	CardNetworkType_value = map[string]int32{
		"CARD_NETWORK_TYPE_UNSPECIFIED": 0,
		"VISA":                          1,
		"MASTER":                        2,
		"DISCOVER":                      3,
		"AMEX":                          4,
		"RUPAY":                         5,
		"DINERS":                        6,
	}
)

func (x CardNetworkType) Enum() *CardNetworkType {
	p := new(CardNetworkType)
	*p = x
	return p
}

func (x CardNetworkType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardNetworkType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_proto_enumTypes[3].Descriptor()
}

func (CardNetworkType) Type() protoreflect.EnumType {
	return &file_api_card_card_proto_enumTypes[3]
}

func (x CardNetworkType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardNetworkType.Descriptor instead.
func (CardNetworkType) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_proto_rawDescGZIP(), []int{3}
}

// CardFieldMask is the enum representation of all the card fields.
// Meant to be used as field mask to help with database updates.
type CardFieldMask int32

const (
	CardFieldMask_CARD_FIELD_MASK_UNSPECIFIED CardFieldMask = 0
	CardFieldMask_CARD_STATE                  CardFieldMask = 1
	CardFieldMask_CARD_TYPE                   CardFieldMask = 2
	CardFieldMask_CARD_FORM                   CardFieldMask = 3
	CardFieldMask_CARD_NETWORK_TYPE           CardFieldMask = 4
	CardFieldMask_CARD_ISSUER_BANK            CardFieldMask = 5
	CardFieldMask_CARD_BANK_IDENTIFIER        CardFieldMask = 6
	CardFieldMask_CARD_CONTROLS               CardFieldMask = 7
	CardFieldMask_CARD_BASIC_INFO             CardFieldMask = 8
	CardFieldMask_CARD_CATEGORY               CardFieldMask = 9
	CardFieldMask_CARD_PIN_SET_OTP_TOKEN      CardFieldMask = 10
	CardFieldMask_CARD_PREVIOUS_CARD_ID       CardFieldMask = 11
	CardFieldMask_CARD_CREATED_AT             CardFieldMask = 12
	CardFieldMask_CARD_UPDATED_AT             CardFieldMask = 13
	CardFieldMask_CARD_ISSUANCE_FEE           CardFieldMask = 14
	CardFieldMask_CARD_ID                     CardFieldMask = 15
	CardFieldMask_CARD_ACTOR_ID               CardFieldMask = 16
	CardFieldMask_CARD_DELETED_AT             CardFieldMask = 17
	CardFieldMask_CARD_GROUP_ID               CardFieldMask = 18
	CardFieldMask_CARD_SAVINGS_ACCOUNT_ID     CardFieldMask = 19
	CardFieldMask_CARD_SKU_TYPE               CardFieldMask = 20
)

// Enum value maps for CardFieldMask.
var (
	CardFieldMask_name = map[int32]string{
		0:  "CARD_FIELD_MASK_UNSPECIFIED",
		1:  "CARD_STATE",
		2:  "CARD_TYPE",
		3:  "CARD_FORM",
		4:  "CARD_NETWORK_TYPE",
		5:  "CARD_ISSUER_BANK",
		6:  "CARD_BANK_IDENTIFIER",
		7:  "CARD_CONTROLS",
		8:  "CARD_BASIC_INFO",
		9:  "CARD_CATEGORY",
		10: "CARD_PIN_SET_OTP_TOKEN",
		11: "CARD_PREVIOUS_CARD_ID",
		12: "CARD_CREATED_AT",
		13: "CARD_UPDATED_AT",
		14: "CARD_ISSUANCE_FEE",
		15: "CARD_ID",
		16: "CARD_ACTOR_ID",
		17: "CARD_DELETED_AT",
		18: "CARD_GROUP_ID",
		19: "CARD_SAVINGS_ACCOUNT_ID",
		20: "CARD_SKU_TYPE",
	}
	CardFieldMask_value = map[string]int32{
		"CARD_FIELD_MASK_UNSPECIFIED": 0,
		"CARD_STATE":                  1,
		"CARD_TYPE":                   2,
		"CARD_FORM":                   3,
		"CARD_NETWORK_TYPE":           4,
		"CARD_ISSUER_BANK":            5,
		"CARD_BANK_IDENTIFIER":        6,
		"CARD_CONTROLS":               7,
		"CARD_BASIC_INFO":             8,
		"CARD_CATEGORY":               9,
		"CARD_PIN_SET_OTP_TOKEN":      10,
		"CARD_PREVIOUS_CARD_ID":       11,
		"CARD_CREATED_AT":             12,
		"CARD_UPDATED_AT":             13,
		"CARD_ISSUANCE_FEE":           14,
		"CARD_ID":                     15,
		"CARD_ACTOR_ID":               16,
		"CARD_DELETED_AT":             17,
		"CARD_GROUP_ID":               18,
		"CARD_SAVINGS_ACCOUNT_ID":     19,
		"CARD_SKU_TYPE":               20,
	}
)

func (x CardFieldMask) Enum() *CardFieldMask {
	p := new(CardFieldMask)
	*p = x
	return p
}

func (x CardFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_proto_enumTypes[4].Descriptor()
}

func (CardFieldMask) Type() protoreflect.EnumType {
	return &file_api_card_card_proto_enumTypes[4]
}

func (x CardFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardFieldMask.Descriptor instead.
func (CardFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_proto_rawDescGZIP(), []int{4}
}

// Specifies the type of card issue request.
type CardIssueType int32

const (
	CardIssueType_CARD_CREATION_REQUEST_TYPE_UNSPECIFIED CardIssueType = 0
	// FRESH - when is the card is to be created for the first time.
	// This must be just after Savings bank account is opened successfully.
	CardIssueType_FRESH CardIssueType = 1
	// RENEW - request for a new card. This may happen if a card is lost,
	// blocked or expired
	CardIssueType_RENEW CardIssueType = 2
)

// Enum value maps for CardIssueType.
var (
	CardIssueType_name = map[int32]string{
		0: "CARD_CREATION_REQUEST_TYPE_UNSPECIFIED",
		1: "FRESH",
		2: "RENEW",
	}
	CardIssueType_value = map[string]int32{
		"CARD_CREATION_REQUEST_TYPE_UNSPECIFIED": 0,
		"FRESH":                                  1,
		"RENEW":                                  2,
	}
)

func (x CardIssueType) Enum() *CardIssueType {
	p := new(CardIssueType)
	*p = x
	return p
}

func (x CardIssueType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardIssueType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_proto_enumTypes[5].Descriptor()
}

func (CardIssueType) Type() protoreflect.EnumType {
	return &file_api_card_card_proto_enumTypes[5]
}

func (x CardIssueType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardIssueType.Descriptor instead.
func (CardIssueType) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_proto_rawDescGZIP(), []int{5}
}

// Domain proto for the card in the backend system.
type Card struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Primary identifier to a card database model.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Primary identifier to the actor table. The card is provisioned for this entity.
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// The state of the card in the State Machine.
	// Few sample state machines:
	// case 1: INITIATED -> CREATED -> ACTIVATED -> EXPIRED
	// Happy case.
	//
	// INITIATED -> CREATED -> ACTIVATED -> SUSPENDED
	// Card was suspended due to an temporary situation.
	//
	// INITIATED -> CREATED -> EXPIRED
	// User never used the card and it expired.
	State CardState `protobuf:"varint,3,opt,name=state,proto3,enum=card.CardState" json:"state,omitempty"`
	// Type of card that can be provisioned for the user.
	// Currently, we are only supporting Debit cards.
	Type CardType `protobuf:"varint,4,opt,name=type,proto3,enum=card.CardType" json:"type,omitempty"`
	// The card form. Physical or Digital
	Form CardForm `protobuf:"varint,5,opt,name=form,proto3,enum=card.CardForm" json:"form,omitempty"`
	// Network of the underlying card.
	NetworkType CardNetworkType `protobuf:"varint,6,opt,name=networkType,proto3,enum=card.CardNetworkType" json:"networkType,omitempty"`
	// Issuer bank. The banking partner which issues the card. It must be the same
	// bank where the savings bank account is opened.
	IssuerBank vendorgateway.Vendor `protobuf:"varint,7,opt,name=issuer_bank,json=issuerBank,proto3,enum=vendorgateway.Vendor" json:"issuer_bank,omitempty"`
	// Indicates if this was a renewed card or a fresh card.
	IssueType CardIssueType `protobuf:"varint,8,opt,name=issue_type,json=issueType,proto3,enum=card.CardIssueType" json:"issue_type,omitempty"`
	// A unique identifier received from the Issuer to refer to the issued card.
	// Available in the api response when the card is successfully created with the issuer bank.
	BankIdentifier string `protobuf:"bytes,9,opt,name=bank_identifier,json=bankIdentifier,proto3" json:"bank_identifier,omitempty"`
	// Indicates the category of the card. This is returned in the card creation response as of now.
	// Can contain values like VISA Infinity, VISA Gold etc
	// TODO(anand): shouldn't the user be choosing the card category to be provisioned?
	// TODO(anand): move this to enum once sufficient clarity?
	CardCategory string `protobuf:"bytes,10,opt,name=card_category,json=cardCategory,proto3" json:"card_category,omitempty"`
	// 16/4/3 of the card.
	BasicInfo *BasicCardInfo `protobuf:"bytes,11,opt,name=basic_info,json=basicInfo,proto3" json:"basic_info,omitempty"`
	// each individual item here denotes a control set for the card for a given transaction_type and location_type
	// For example: for a card to be BLOCKED for ALL transactions and all location type, there must be 4 entries here:
	// txnType, locType
	// - ATM, DOMESTIC
	// - ATM, INTERNATIONAL
	// - ECOMM, DOMESTIC
	// - ECOMM, INTERNATIONAL
	Controls *ControlData `protobuf:"bytes,12,opt,name=controls,proto3" json:"controls,omitempty"`
	// groupId denotes a unique identifier for a collection of cards that belong together.
	// For dual card strategy i.e. if a physical and a digital card are provisioned, both the cards will have the
	// same groupId.
	// For single card strategies, groupId is mapped to the only card.
	GroupId string `protobuf:"bytes,13,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	// account id to which the card is associated with. FK reference in savings_account table.
	SavingsAccountId string `protobuf:"bytes,14,opt,name=savings_account_id,json=savingsAccountId,proto3" json:"savings_account_id,omitempty"`
	// It will be used to set pin while onboarding and valid for one time till expiry.
	// If expire time is passed new OTP need to be trigger and capture from UI and pass it to vendor.
	// This will become invalid and can't be used further.
	PinSetOtpToken *PinSetOtpToken `protobuf:"bytes,15,opt,name=pin_set_otp_token,json=pinSetOtpToken,proto3" json:"pin_set_otp_token,omitempty"`
	// card id of the blocked/expired card for which new card creation is requested.
	// this will be empty for a fresh card issue type
	PreviousCardId string `protobuf:"bytes,16,opt,name=previous_card_id,json=previousCardId,proto3" json:"previous_card_id,omitempty"`
	// card creation timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// card creation timestamp
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// CardSKUType represents the card variant offered.
	CardSkuType CardSKUType `protobuf:"varint,19,opt,name=card_sku_type,json=cardSkuType,proto3,enum=card.CardSKUType" json:"card_sku_type,omitempty"`
	// amount to be charged for issuing the current card
	IssuanceFee *money.Money           `protobuf:"bytes,20,opt,name=issuance_fee,json=issuanceFee,proto3" json:"issuance_fee,omitempty"`
	DeletedAt   *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *Card) Reset() {
	*x = Card{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Card) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Card) ProtoMessage() {}

func (x *Card) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Card.ProtoReflect.Descriptor instead.
func (*Card) Descriptor() ([]byte, []int) {
	return file_api_card_card_proto_rawDescGZIP(), []int{0}
}

func (x *Card) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Card) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *Card) GetState() CardState {
	if x != nil {
		return x.State
	}
	return CardState_CARD_STATE_UNSPECIFIED
}

func (x *Card) GetType() CardType {
	if x != nil {
		return x.Type
	}
	return CardType_CARD_TYPE_UNSPECIFIED
}

func (x *Card) GetForm() CardForm {
	if x != nil {
		return x.Form
	}
	return CardForm_CARD_FORM_UNSPECIFIED
}

func (x *Card) GetNetworkType() CardNetworkType {
	if x != nil {
		return x.NetworkType
	}
	return CardNetworkType_CARD_NETWORK_TYPE_UNSPECIFIED
}

func (x *Card) GetIssuerBank() vendorgateway.Vendor {
	if x != nil {
		return x.IssuerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *Card) GetIssueType() CardIssueType {
	if x != nil {
		return x.IssueType
	}
	return CardIssueType_CARD_CREATION_REQUEST_TYPE_UNSPECIFIED
}

func (x *Card) GetBankIdentifier() string {
	if x != nil {
		return x.BankIdentifier
	}
	return ""
}

func (x *Card) GetCardCategory() string {
	if x != nil {
		return x.CardCategory
	}
	return ""
}

func (x *Card) GetBasicInfo() *BasicCardInfo {
	if x != nil {
		return x.BasicInfo
	}
	return nil
}

func (x *Card) GetControls() *ControlData {
	if x != nil {
		return x.Controls
	}
	return nil
}

func (x *Card) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *Card) GetSavingsAccountId() string {
	if x != nil {
		return x.SavingsAccountId
	}
	return ""
}

func (x *Card) GetPinSetOtpToken() *PinSetOtpToken {
	if x != nil {
		return x.PinSetOtpToken
	}
	return nil
}

func (x *Card) GetPreviousCardId() string {
	if x != nil {
		return x.PreviousCardId
	}
	return ""
}

func (x *Card) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Card) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Card) GetCardSkuType() CardSKUType {
	if x != nil {
		return x.CardSkuType
	}
	return CardSKUType_CARD_SKU_TYPE_UNSPECIFIED
}

func (x *Card) GetIssuanceFee() *money.Money {
	if x != nil {
		return x.IssuanceFee
	}
	return nil
}

func (x *Card) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

type BasicCardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// masked card number. Only the last 4 and first 6 digits are unmasked.
	MaskedCardNumber string `protobuf:"bytes,1,opt,name=masked_card_number,json=maskedCardNumber,proto3" json:"masked_card_number,omitempty"`
	// the data bunker token for the 16 digit card number.
	CardNumber string `protobuf:"bytes,2,opt,name=card_number,json=cardNumber,proto3" json:"card_number,omitempty"`
	// Due to compliance regulations we cannot have raw expiry flowing through BE systems
	// we will use expiry token to pass the expiry which will be converted to actual expiry date by tokenizer
	//
	// Deprecated: Marked as deprecated in api/card/card.proto.
	Expiry       string `protobuf:"bytes,3,opt,name=expiry,proto3" json:"expiry,omitempty"`
	Cvv          string `protobuf:"bytes,4,opt,name=cvv,proto3" json:"cvv,omitempty"`
	CustomerName string `protobuf:"bytes,5,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	ExpiryToken  string `protobuf:"bytes,6,opt,name=expiry_token,json=expiryToken,proto3" json:"expiry_token,omitempty"`
}

func (x *BasicCardInfo) Reset() {
	*x = BasicCardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BasicCardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BasicCardInfo) ProtoMessage() {}

func (x *BasicCardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BasicCardInfo.ProtoReflect.Descriptor instead.
func (*BasicCardInfo) Descriptor() ([]byte, []int) {
	return file_api_card_card_proto_rawDescGZIP(), []int{1}
}

func (x *BasicCardInfo) GetMaskedCardNumber() string {
	if x != nil {
		return x.MaskedCardNumber
	}
	return ""
}

func (x *BasicCardInfo) GetCardNumber() string {
	if x != nil {
		return x.CardNumber
	}
	return ""
}

// Deprecated: Marked as deprecated in api/card/card.proto.
func (x *BasicCardInfo) GetExpiry() string {
	if x != nil {
		return x.Expiry
	}
	return ""
}

func (x *BasicCardInfo) GetCvv() string {
	if x != nil {
		return x.Cvv
	}
	return ""
}

func (x *BasicCardInfo) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *BasicCardInfo) GetExpiryToken() string {
	if x != nil {
		return x.ExpiryToken
	}
	return ""
}

type PinSetOtpToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// One time pin set otp token.
	Otp string `protobuf:"bytes,1,opt,name=otp,proto3" json:"otp,omitempty"`
	// expire time
	ExpireAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
}

func (x *PinSetOtpToken) Reset() {
	*x = PinSetOtpToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinSetOtpToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinSetOtpToken) ProtoMessage() {}

func (x *PinSetOtpToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinSetOtpToken.ProtoReflect.Descriptor instead.
func (*PinSetOtpToken) Descriptor() ([]byte, []int) {
	return file_api_card_card_proto_rawDescGZIP(), []int{2}
}

func (x *PinSetOtpToken) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *PinSetOtpToken) GetExpireAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireAt
	}
	return nil
}

var File_api_card_card_proto protoreflect.FileDescriptor

var file_api_card_card_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x63, 0x61, 0x72, 0x64, 0x1a, 0x17, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x6b, 0x75, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc9, 0x07, 0x0a, 0x04, 0x43, 0x61, 0x72, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x22, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x46,
	0x6f, 0x72, 0x6d, 0x52, 0x04, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x37, 0x0a, 0x0b, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x36, 0x0a, 0x0b, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e,
	0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0a,
	0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x32, 0x0a, 0x0a, 0x69, 0x73,
	0x73, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x49, 0x73, 0x73, 0x75, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x6b, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x63, 0x61, 0x72, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x32, 0x0a, 0x0a,
	0x62, 0x61, 0x73, 0x69, 0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x43, 0x61, 0x72,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x62, 0x61, 0x73, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x2d, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x61,
	0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x11, 0x70, 0x69, 0x6e, 0x5f,
	0x73, 0x65, 0x74, 0x5f, 0x6f, 0x74, 0x70, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x50, 0x69, 0x6e, 0x53, 0x65,
	0x74, 0x4f, 0x74, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x0e, 0x70, 0x69, 0x6e, 0x53, 0x65,
	0x74, 0x4f, 0x74, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x72, 0x65,
	0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x43, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x35, 0x0a, 0x0d, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x73, 0x6b, 0x75, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x53, 0x4b, 0x55, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x53, 0x6b, 0x75, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x35, 0x0a, 0x0c, 0x69, 0x73, 0x73, 0x75, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x69, 0x73, 0x73, 0x75,
	0x61, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x22, 0xd4, 0x01, 0x0a, 0x0d, 0x42, 0x61, 0x73, 0x69, 0x63, 0x43, 0x61, 0x72, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x63, 0x76, 0x76, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x76,
	0x76, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x79, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x5b, 0x0a, 0x0e, 0x50, 0x69, 0x6e,
	0x53, 0x65, 0x74, 0x4f, 0x74, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6f,
	0x74, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x37, 0x0a,
	0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x41, 0x74, 0x2a, 0x94, 0x01, 0x0a, 0x09, 0x43, 0x61, 0x72, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12,
	0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x53,
	0x55, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x42, 0x4c,
	0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50, 0x49, 0x52,
	0x45, 0x44, 0x10, 0x06, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10,
	0x07, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x08, 0x2a, 0x3c, 0x0a,
	0x08, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x45, 0x42, 0x49, 0x54, 0x10, 0x01, 0x12,
	0x0a, 0x0a, 0x06, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10, 0x02, 0x2a, 0x40, 0x0a, 0x08, 0x43,
	0x61, 0x72, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x01,
	0x12, 0x0b, 0x0a, 0x07, 0x44, 0x49, 0x47, 0x49, 0x54, 0x41, 0x4c, 0x10, 0x02, 0x2a, 0x79, 0x0a,
	0x0f, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x56, 0x49, 0x53, 0x41, 0x10, 0x01, 0x12, 0x0a, 0x0a,
	0x06, 0x4d, 0x41, 0x53, 0x54, 0x45, 0x52, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x49, 0x53,
	0x43, 0x4f, 0x56, 0x45, 0x52, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x41, 0x4d, 0x45, 0x58, 0x10,
	0x04, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x55, 0x50, 0x41, 0x59, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06,
	0x44, 0x49, 0x4e, 0x45, 0x52, 0x53, 0x10, 0x06, 0x2a, 0xd0, 0x03, 0x0a, 0x0d, 0x43, 0x61, 0x72,
	0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x04,
	0x12, 0x14, 0x0a, 0x10, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x52, 0x5f,
	0x42, 0x41, 0x4e, 0x4b, 0x10, 0x05, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x42,
	0x41, 0x4e, 0x4b, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x10, 0x06,
	0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c,
	0x53, 0x10, 0x07, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x42, 0x41, 0x53, 0x49,
	0x43, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x08, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x10, 0x09, 0x12, 0x1a, 0x0a, 0x16, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x4f, 0x54, 0x50, 0x5f,
	0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x0a, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x50, 0x52, 0x45, 0x56, 0x49, 0x4f, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x44,
	0x10, 0x0b, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0c, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0d, 0x12, 0x15, 0x0a, 0x11,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x46, 0x45,
	0x45, 0x10, 0x0e, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x44, 0x10, 0x0f,
	0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49,
	0x44, 0x10, 0x10, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x45,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x11, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x49, 0x44, 0x10, 0x12, 0x12, 0x1b, 0x0a, 0x17, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x13, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x53, 0x4b, 0x55, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x14, 0x2a, 0x51, 0x0a, 0x0d, 0x43,
	0x61, 0x72, 0x64, 0x49, 0x73, 0x73, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x26,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x52, 0x45, 0x53,
	0x48, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45, 0x4e, 0x45, 0x57, 0x10, 0x02, 0x42, 0x42,
	0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61,
	0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_card_proto_rawDescOnce sync.Once
	file_api_card_card_proto_rawDescData = file_api_card_card_proto_rawDesc
)

func file_api_card_card_proto_rawDescGZIP() []byte {
	file_api_card_card_proto_rawDescOnce.Do(func() {
		file_api_card_card_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_card_proto_rawDescData)
	})
	return file_api_card_card_proto_rawDescData
}

var file_api_card_card_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_card_card_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_card_card_proto_goTypes = []interface{}{
	(CardState)(0),                // 0: card.CardState
	(CardType)(0),                 // 1: card.CardType
	(CardForm)(0),                 // 2: card.CardForm
	(CardNetworkType)(0),          // 3: card.CardNetworkType
	(CardFieldMask)(0),            // 4: card.CardFieldMask
	(CardIssueType)(0),            // 5: card.CardIssueType
	(*Card)(nil),                  // 6: card.Card
	(*BasicCardInfo)(nil),         // 7: card.BasicCardInfo
	(*PinSetOtpToken)(nil),        // 8: card.PinSetOtpToken
	(vendorgateway.Vendor)(0),     // 9: vendorgateway.Vendor
	(*ControlData)(nil),           // 10: card.ControlData
	(*timestamppb.Timestamp)(nil), // 11: google.protobuf.Timestamp
	(CardSKUType)(0),              // 12: card.CardSKUType
	(*money.Money)(nil),           // 13: google.type.Money
}
var file_api_card_card_proto_depIdxs = []int32{
	0,  // 0: card.Card.state:type_name -> card.CardState
	1,  // 1: card.Card.type:type_name -> card.CardType
	2,  // 2: card.Card.form:type_name -> card.CardForm
	3,  // 3: card.Card.networkType:type_name -> card.CardNetworkType
	9,  // 4: card.Card.issuer_bank:type_name -> vendorgateway.Vendor
	5,  // 5: card.Card.issue_type:type_name -> card.CardIssueType
	7,  // 6: card.Card.basic_info:type_name -> card.BasicCardInfo
	10, // 7: card.Card.controls:type_name -> card.ControlData
	8,  // 8: card.Card.pin_set_otp_token:type_name -> card.PinSetOtpToken
	11, // 9: card.Card.created_at:type_name -> google.protobuf.Timestamp
	11, // 10: card.Card.updated_at:type_name -> google.protobuf.Timestamp
	12, // 11: card.Card.card_sku_type:type_name -> card.CardSKUType
	13, // 12: card.Card.issuance_fee:type_name -> google.type.Money
	11, // 13: card.Card.deleted_at:type_name -> google.protobuf.Timestamp
	11, // 14: card.PinSetOtpToken.expire_at:type_name -> google.protobuf.Timestamp
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_api_card_card_proto_init() }
func file_api_card_card_proto_init() {
	if File_api_card_card_proto != nil {
		return
	}
	file_api_card_card_sku_proto_init()
	file_api_card_control_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_card_card_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Card); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_card_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BasicCardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_card_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PinSetOtpToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_card_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_card_proto_goTypes,
		DependencyIndexes: file_api_card_card_proto_depIdxs,
		EnumInfos:         file_api_card_card_proto_enumTypes,
		MessageInfos:      file_api_card_card_proto_msgTypes,
	}.Build()
	File_api_card_card_proto = out.File
	file_api_card_card_proto_rawDesc = nil
	file_api_card_card_proto_goTypes = nil
	file_api_card_card_proto_depIdxs = nil
}
