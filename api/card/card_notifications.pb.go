// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=NotificationDetails,MerchantDetails,AuthSwitchDetails,RemitterDetails,DetailedStatus

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/card_notifications.proto

package card

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	enums "github.com/epifi/gamma/api/card/enums"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CardNotification is generic proto model for debit card notifications,
// notifications can come from various sources eg - switch, acs etc., and
// can be for a financial and non-financial transaction
type CardNotification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Primary identifier to a notification database model.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Card id for which notification was received and reference to cards table
	CardId                   string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	RetrievalReferenceNumber string `protobuf:"bytes,3,opt,name=retrieval_reference_number,json=retrievalReferenceNumber,proto3" json:"retrieval_reference_number,omitempty"`
	// unique reference id sent by vendor
	RequestId           string                 `protobuf:"bytes,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	NotificationDetails *NotificationDetails   `protobuf:"bytes,5,opt,name=notification_details,json=notificationDetails,proto3" json:"notification_details,omitempty"`
	NotificationType    enums.NotificationType `protobuf:"varint,6,opt,name=notification_type,json=notificationType,proto3,enum=card.enums.NotificationType" json:"notification_type,omitempty"`
	// merchant involved in the notification
	MerchantDetails   *MerchantDetails   `protobuf:"bytes,7,opt,name=merchant_details,json=merchantDetails,proto3" json:"merchant_details,omitempty"`
	AuthSwitchDetails *AuthSwitchDetails `protobuf:"bytes,8,opt,name=auth_switch_details,json=authSwitchDetails,proto3" json:"auth_switch_details,omitempty"`
	RemitterDetails   *RemitterDetails   `protobuf:"bytes,9,opt,name=remitter_details,json=remitterDetails,proto3" json:"remitter_details,omitempty"`
	// High level status of the transaction(financial/non-financial) for which notification was received
	Status             enums.TransactionState   `protobuf:"varint,10,opt,name=status,proto3,enum=card.enums.TransactionState" json:"status,omitempty"`
	DetailedStatus     *DetailedStatus          `protobuf:"bytes,11,opt,name=detailed_status,json=detailedStatus,proto3" json:"detailed_status,omitempty"`
	NotificationSource enums.NotificationSource `protobuf:"varint,12,opt,name=notification_source,json=notificationSource,proto3,enum=card.enums.NotificationSource" json:"notification_source,omitempty"`
	// unique transaction reference to uniquely identify a notification. It can be a combination of multiple parameters
	// received from a notification.
	// We will transform the DedupeId message to sha 256 hash after doing proto.Marshal and keep this into our db. It will
	// be considered as unique identifier for de-duping notifications.
	DedupeId *DedupeId `protobuf:"bytes,13,opt,name=dedupe_id,json=dedupeId,proto3" json:"dedupe_id,omitempty"`
	// time at which notification was created at the end of vendor
	// definition of notification_event_time might differ based on the notification type,
	// for eg - financial switch notification, notification_event_time will be same as the time of the txn.
	NotificationEventTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=notification_event_time,json=notificationEventTime,proto3" json:"notification_event_time,omitempty"`
	CreatedAt             *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt             *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt             *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// ExternalRefId can be used to link or map a cardNotification entry with an external entity.
	// For example one of the cases is to map a notification with an order, order will store the ExternalRefId as ClientReqId .
	ExternalRefId string `protobuf:"bytes,18,opt,name=external_ref_id,json=externalRefId,proto3" json:"external_ref_id,omitempty"`
}

func (x *CardNotification) Reset() {
	*x = CardNotification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_notifications_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardNotification) ProtoMessage() {}

func (x *CardNotification) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_notifications_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardNotification.ProtoReflect.Descriptor instead.
func (*CardNotification) Descriptor() ([]byte, []int) {
	return file_api_card_card_notifications_proto_rawDescGZIP(), []int{0}
}

func (x *CardNotification) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CardNotification) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CardNotification) GetRetrievalReferenceNumber() string {
	if x != nil {
		return x.RetrievalReferenceNumber
	}
	return ""
}

func (x *CardNotification) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CardNotification) GetNotificationDetails() *NotificationDetails {
	if x != nil {
		return x.NotificationDetails
	}
	return nil
}

func (x *CardNotification) GetNotificationType() enums.NotificationType {
	if x != nil {
		return x.NotificationType
	}
	return enums.NotificationType(0)
}

func (x *CardNotification) GetMerchantDetails() *MerchantDetails {
	if x != nil {
		return x.MerchantDetails
	}
	return nil
}

func (x *CardNotification) GetAuthSwitchDetails() *AuthSwitchDetails {
	if x != nil {
		return x.AuthSwitchDetails
	}
	return nil
}

func (x *CardNotification) GetRemitterDetails() *RemitterDetails {
	if x != nil {
		return x.RemitterDetails
	}
	return nil
}

func (x *CardNotification) GetStatus() enums.TransactionState {
	if x != nil {
		return x.Status
	}
	return enums.TransactionState(0)
}

func (x *CardNotification) GetDetailedStatus() *DetailedStatus {
	if x != nil {
		return x.DetailedStatus
	}
	return nil
}

func (x *CardNotification) GetNotificationSource() enums.NotificationSource {
	if x != nil {
		return x.NotificationSource
	}
	return enums.NotificationSource(0)
}

func (x *CardNotification) GetDedupeId() *DedupeId {
	if x != nil {
		return x.DedupeId
	}
	return nil
}

func (x *CardNotification) GetNotificationEventTime() *timestamppb.Timestamp {
	if x != nil {
		return x.NotificationEventTime
	}
	return nil
}

func (x *CardNotification) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CardNotification) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CardNotification) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *CardNotification) GetExternalRefId() string {
	if x != nil {
		return x.ExternalRefId
	}
	return ""
}

// Detailed status of the transaction for eg - ResponseCode, ResponseReason, InternalResponseCode
type DetailedStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StatusCode           string `protobuf:"bytes,1,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"`
	StatusDescription    string `protobuf:"bytes,2,opt,name=status_description,json=statusDescription,proto3" json:"status_description,omitempty"`
	RawStatusCode        string `protobuf:"bytes,3,opt,name=raw_status_code,json=rawStatusCode,proto3" json:"raw_status_code,omitempty"`
	RawStatusDescription string `protobuf:"bytes,4,opt,name=raw_status_description,json=rawStatusDescription,proto3" json:"raw_status_description,omitempty"`
}

func (x *DetailedStatus) Reset() {
	*x = DetailedStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_notifications_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailedStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailedStatus) ProtoMessage() {}

func (x *DetailedStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_notifications_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailedStatus.ProtoReflect.Descriptor instead.
func (*DetailedStatus) Descriptor() ([]byte, []int) {
	return file_api_card_card_notifications_proto_rawDescGZIP(), []int{1}
}

func (x *DetailedStatus) GetStatusCode() string {
	if x != nil {
		return x.StatusCode
	}
	return ""
}

func (x *DetailedStatus) GetStatusDescription() string {
	if x != nil {
		return x.StatusDescription
	}
	return ""
}

func (x *DetailedStatus) GetRawStatusCode() string {
	if x != nil {
		return x.RawStatusCode
	}
	return ""
}

func (x *DetailedStatus) GetRawStatusDescription() string {
	if x != nil {
		return x.RawStatusDescription
	}
	return ""
}

// Notification transaction details for which switch notification was received
type NotificationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// dump of complete raw notification
	RawNotificationData string            `protobuf:"bytes,1,opt,name=raw_notification_data,json=rawNotificationData,proto3" json:"raw_notification_data,omitempty"`
	MessageType         enums.MessageType `protobuf:"varint,2,opt,name=message_type,json=messageType,proto3,enum=card.enums.MessageType" json:"message_type,omitempty"`
	// Types that are assignable to Details:
	//
	//	*NotificationDetails_FinancialSwitchNotificationDetails
	//	*NotificationDetails_NonFinancialSwitchNotificationDetails
	Details isNotificationDetails_Details `protobuf_oneof:"Details"`
}

func (x *NotificationDetails) Reset() {
	*x = NotificationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_notifications_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotificationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationDetails) ProtoMessage() {}

func (x *NotificationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_notifications_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationDetails.ProtoReflect.Descriptor instead.
func (*NotificationDetails) Descriptor() ([]byte, []int) {
	return file_api_card_card_notifications_proto_rawDescGZIP(), []int{2}
}

func (x *NotificationDetails) GetRawNotificationData() string {
	if x != nil {
		return x.RawNotificationData
	}
	return ""
}

func (x *NotificationDetails) GetMessageType() enums.MessageType {
	if x != nil {
		return x.MessageType
	}
	return enums.MessageType(0)
}

func (m *NotificationDetails) GetDetails() isNotificationDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *NotificationDetails) GetFinancialSwitchNotificationDetails() *FinancialSwitchNotificationDetails {
	if x, ok := x.GetDetails().(*NotificationDetails_FinancialSwitchNotificationDetails); ok {
		return x.FinancialSwitchNotificationDetails
	}
	return nil
}

func (x *NotificationDetails) GetNonFinancialSwitchNotificationDetails() *NonFinancialSwitchNotificationDetails {
	if x, ok := x.GetDetails().(*NotificationDetails_NonFinancialSwitchNotificationDetails); ok {
		return x.NonFinancialSwitchNotificationDetails
	}
	return nil
}

type isNotificationDetails_Details interface {
	isNotificationDetails_Details()
}

type NotificationDetails_FinancialSwitchNotificationDetails struct {
	FinancialSwitchNotificationDetails *FinancialSwitchNotificationDetails `protobuf:"bytes,3,opt,name=financial_switch_notification_details,json=financialSwitchNotificationDetails,proto3,oneof"`
}

type NotificationDetails_NonFinancialSwitchNotificationDetails struct {
	NonFinancialSwitchNotificationDetails *NonFinancialSwitchNotificationDetails `protobuf:"bytes,4,opt,name=non_financial_switch_notification_details,json=nonFinancialSwitchNotificationDetails,proto3,oneof"`
}

func (*NotificationDetails_FinancialSwitchNotificationDetails) isNotificationDetails_Details() {}

func (*NotificationDetails_NonFinancialSwitchNotificationDetails) isNotificationDetails_Details() {}

// Data object to store transaction details for notification associated with financial transaction
type FinancialSwitchNotificationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnCategory              enums.CardTransactionCategory `protobuf:"varint,1,opt,name=txn_category,json=txnCategory,proto3,enum=card.enums.CardTransactionCategory" json:"txn_category,omitempty"`
	TxnTime                  *timestamppb.Timestamp        `protobuf:"bytes,2,opt,name=txn_time,json=txnTime,proto3" json:"txn_time,omitempty"`
	TxnDate                  *date.Date                    `protobuf:"bytes,3,opt,name=txn_date,json=txnDate,proto3" json:"txn_date,omitempty"`
	TxnAmount                *money.Money                  `protobuf:"bytes,4,opt,name=txn_amount,json=txnAmount,proto3" json:"txn_amount,omitempty"`
	TxnRemarks               string                        `protobuf:"bytes,5,opt,name=txn_remarks,json=txnRemarks,proto3" json:"txn_remarks,omitempty"`
	CountryCode              string                        `protobuf:"bytes,6,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	IsForexMarkupTransaction common.BooleanEnum            `protobuf:"varint,7,opt,name=is_forex_markup_transaction,json=isForexMarkupTransaction,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_forex_markup_transaction,omitempty"`
	IsDccTransaction         common.BooleanEnum            `protobuf:"varint,8,opt,name=is_dcc_transaction,json=isDccTransaction,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_dcc_transaction,omitempty"`
	IsCardTapTransaction     common.BooleanEnum            `protobuf:"varint,9,opt,name=is_card_tap_transaction,json=isCardTapTransaction,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_card_tap_transaction,omitempty"`
	IsDeviceTapTransaction   common.BooleanEnum            `protobuf:"varint,10,opt,name=is_device_tap_transaction,json=isDeviceTapTransaction,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_device_tap_transaction,omitempty"`
	TxnAmountInOrgCurrency   *money.Money                  `protobuf:"bytes,11,opt,name=txn_amount_in_org_currency,json=txnAmountInOrgCurrency,proto3" json:"txn_amount_in_org_currency,omitempty"`
	// pincode of the ATM location, it will be only available for ATM transactions
	AtmPincode string `protobuf:"bytes,12,opt,name=atm_pincode,json=atmPincode,proto3" json:"atm_pincode,omitempty"`
}

func (x *FinancialSwitchNotificationDetails) Reset() {
	*x = FinancialSwitchNotificationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_notifications_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinancialSwitchNotificationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinancialSwitchNotificationDetails) ProtoMessage() {}

func (x *FinancialSwitchNotificationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_notifications_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinancialSwitchNotificationDetails.ProtoReflect.Descriptor instead.
func (*FinancialSwitchNotificationDetails) Descriptor() ([]byte, []int) {
	return file_api_card_card_notifications_proto_rawDescGZIP(), []int{3}
}

func (x *FinancialSwitchNotificationDetails) GetTxnCategory() enums.CardTransactionCategory {
	if x != nil {
		return x.TxnCategory
	}
	return enums.CardTransactionCategory(0)
}

func (x *FinancialSwitchNotificationDetails) GetTxnTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TxnTime
	}
	return nil
}

func (x *FinancialSwitchNotificationDetails) GetTxnDate() *date.Date {
	if x != nil {
		return x.TxnDate
	}
	return nil
}

func (x *FinancialSwitchNotificationDetails) GetTxnAmount() *money.Money {
	if x != nil {
		return x.TxnAmount
	}
	return nil
}

func (x *FinancialSwitchNotificationDetails) GetTxnRemarks() string {
	if x != nil {
		return x.TxnRemarks
	}
	return ""
}

func (x *FinancialSwitchNotificationDetails) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *FinancialSwitchNotificationDetails) GetIsForexMarkupTransaction() common.BooleanEnum {
	if x != nil {
		return x.IsForexMarkupTransaction
	}
	return common.BooleanEnum(0)
}

func (x *FinancialSwitchNotificationDetails) GetIsDccTransaction() common.BooleanEnum {
	if x != nil {
		return x.IsDccTransaction
	}
	return common.BooleanEnum(0)
}

func (x *FinancialSwitchNotificationDetails) GetIsCardTapTransaction() common.BooleanEnum {
	if x != nil {
		return x.IsCardTapTransaction
	}
	return common.BooleanEnum(0)
}

func (x *FinancialSwitchNotificationDetails) GetIsDeviceTapTransaction() common.BooleanEnum {
	if x != nil {
		return x.IsDeviceTapTransaction
	}
	return common.BooleanEnum(0)
}

func (x *FinancialSwitchNotificationDetails) GetTxnAmountInOrgCurrency() *money.Money {
	if x != nil {
		return x.TxnAmountInOrgCurrency
	}
	return nil
}

func (x *FinancialSwitchNotificationDetails) GetAtmPincode() string {
	if x != nil {
		return x.AtmPincode
	}
	return ""
}

// Data object to store transaction details for notification associated with non-financial transaction
type NonFinancialSwitchNotificationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NonFinancialSwitchNotificationDetails) Reset() {
	*x = NonFinancialSwitchNotificationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_notifications_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFinancialSwitchNotificationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFinancialSwitchNotificationDetails) ProtoMessage() {}

func (x *NonFinancialSwitchNotificationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_notifications_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFinancialSwitchNotificationDetails.ProtoReflect.Descriptor instead.
func (*NonFinancialSwitchNotificationDetails) Descriptor() ([]byte, []int) {
	return file_api_card_card_notifications_proto_rawDescGZIP(), []int{4}
}

type MerchantDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MerchantName     string `protobuf:"bytes,1,opt,name=merchant_name,json=merchantName,proto3" json:"merchant_name,omitempty"`
	MerchantLocation string `protobuf:"bytes,2,opt,name=merchant_location,json=merchantLocation,proto3" json:"merchant_location,omitempty"`
	MerchantId       string `protobuf:"bytes,3,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	SubMerchantId    string `protobuf:"bytes,4,opt,name=sub_merchant_id,json=subMerchantId,proto3" json:"sub_merchant_id,omitempty"`
	Mcc              string `protobuf:"bytes,5,opt,name=mcc,proto3" json:"mcc,omitempty"`
	TerminalId       string `protobuf:"bytes,6,opt,name=terminal_id,json=terminalId,proto3" json:"terminal_id,omitempty"`
}

func (x *MerchantDetails) Reset() {
	*x = MerchantDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_notifications_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerchantDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantDetails) ProtoMessage() {}

func (x *MerchantDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_notifications_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantDetails.ProtoReflect.Descriptor instead.
func (*MerchantDetails) Descriptor() ([]byte, []int) {
	return file_api_card_card_notifications_proto_rawDescGZIP(), []int{5}
}

func (x *MerchantDetails) GetMerchantName() string {
	if x != nil {
		return x.MerchantName
	}
	return ""
}

func (x *MerchantDetails) GetMerchantLocation() string {
	if x != nil {
		return x.MerchantLocation
	}
	return ""
}

func (x *MerchantDetails) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

func (x *MerchantDetails) GetSubMerchantId() string {
	if x != nil {
		return x.SubMerchantId
	}
	return ""
}

func (x *MerchantDetails) GetMcc() string {
	if x != nil {
		return x.Mcc
	}
	return ""
}

func (x *MerchantDetails) GetTerminalId() string {
	if x != nil {
		return x.TerminalId
	}
	return ""
}

type AuthSwitchDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthCode            string                    `protobuf:"bytes,1,opt,name=auth_code,json=authCode,proto3" json:"auth_code,omitempty"`
	AuthorizationSwitch enums.AuthorizationSwitch `protobuf:"varint,2,opt,name=authorization_switch,json=authorizationSwitch,proto3,enum=card.enums.AuthorizationSwitch" json:"authorization_switch,omitempty"`
}

func (x *AuthSwitchDetails) Reset() {
	*x = AuthSwitchDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_notifications_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthSwitchDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthSwitchDetails) ProtoMessage() {}

func (x *AuthSwitchDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_notifications_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthSwitchDetails.ProtoReflect.Descriptor instead.
func (*AuthSwitchDetails) Descriptor() ([]byte, []int) {
	return file_api_card_card_notifications_proto_rawDescGZIP(), []int{6}
}

func (x *AuthSwitchDetails) GetAuthCode() string {
	if x != nil {
		return x.AuthCode
	}
	return ""
}

func (x *AuthSwitchDetails) GetAuthorizationSwitch() enums.AuthorizationSwitch {
	if x != nil {
		return x.AuthorizationSwitch
	}
	return enums.AuthorizationSwitch(0)
}

type RemitterDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RemitterInstrumentType string `protobuf:"bytes,1,opt,name=remitter_instrument_type,json=remitterInstrumentType,proto3" json:"remitter_instrument_type,omitempty"`
	RemitterCode           string `protobuf:"bytes,2,opt,name=remitter_code,json=remitterCode,proto3" json:"remitter_code,omitempty"`
	PaymentGateway         string `protobuf:"bytes,3,opt,name=payment_gateway,json=paymentGateway,proto3" json:"payment_gateway,omitempty"`
}

func (x *RemitterDetails) Reset() {
	*x = RemitterDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_notifications_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemitterDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemitterDetails) ProtoMessage() {}

func (x *RemitterDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_notifications_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemitterDetails.ProtoReflect.Descriptor instead.
func (*RemitterDetails) Descriptor() ([]byte, []int) {
	return file_api_card_card_notifications_proto_rawDescGZIP(), []int{7}
}

func (x *RemitterDetails) GetRemitterInstrumentType() string {
	if x != nil {
		return x.RemitterInstrumentType
	}
	return ""
}

func (x *RemitterDetails) GetRemitterCode() string {
	if x != nil {
		return x.RemitterCode
	}
	return ""
}

func (x *RemitterDetails) GetPaymentGateway() string {
	if x != nil {
		return x.PaymentGateway
	}
	return ""
}

type DedupeId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId                string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	RetrievalReferenceNumber string                 `protobuf:"bytes,2,opt,name=retrieval_reference_number,json=retrievalReferenceNumber,proto3" json:"retrieval_reference_number,omitempty"`
	TxnTime                  *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=txn_time,json=txnTime,proto3" json:"txn_time,omitempty"`
	CardId                   string                 `protobuf:"bytes,4,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *DedupeId) Reset() {
	*x = DedupeId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_notifications_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DedupeId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DedupeId) ProtoMessage() {}

func (x *DedupeId) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_notifications_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DedupeId.ProtoReflect.Descriptor instead.
func (*DedupeId) Descriptor() ([]byte, []int) {
	return file_api_card_card_notifications_proto_rawDescGZIP(), []int{8}
}

func (x *DedupeId) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DedupeId) GetRetrievalReferenceNumber() string {
	if x != nil {
		return x.RetrievalReferenceNumber
	}
	return ""
}

func (x *DedupeId) GetTxnTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TxnTime
	}
	return nil
}

func (x *DedupeId) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

var File_api_card_card_notifications_proto protoreflect.FileDescriptor

var file_api_card_card_notifications_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x04, 0x63, 0x61, 0x72, 0x64, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x61, 0x72, 0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x9e, 0x08, 0x0a, 0x10, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x3c,
	0x0a, 0x1a, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x18, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x14, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x13, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x49, 0x0a, 0x11, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x10, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x10, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x47, 0x0a, 0x13, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x61, 0x75,
	0x74, 0x68, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x40, 0x0a, 0x10, 0x72, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x52, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x0f, 0x72, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x34, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x0f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4f, 0x0a, 0x13, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x12, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2b, 0x0a, 0x09, 0x64, 0x65, 0x64, 0x75, 0x70,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x52, 0x08, 0x64, 0x65, 0x64, 0x75,
	0x70, 0x65, 0x49, 0x64, 0x12, 0x52, 0x0a, 0x17, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x15, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x49,
	0x64, 0x22, 0xbe, 0x01, 0x0a, 0x0e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x61, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72,
	0x61, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x16,
	0x72, 0x61, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x72, 0x61,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x99, 0x03, 0x0a, 0x13, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x72, 0x61,
	0x77, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x72, 0x61, 0x77, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3a,
	0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x7d, 0x0a, 0x25, 0x66, 0x69,
	0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x22, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x87, 0x01, 0x0a, 0x29, 0x6e, 0x6f,
	0x6e, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61,
	0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x25, 0x6e, 0x6f,
	0x6e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x9c,
	0x06, 0x0a, 0x22, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x46, 0x0a, 0x0c, 0x74, 0x78, 0x6e, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x52, 0x0b, 0x74, 0x78, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x35, 0x0a,
	0x08, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x74, 0x78, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x74, 0x78, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x07, 0x74, 0x78, 0x6e, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x74, 0x78, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x74, 0x78, 0x6e, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x78, 0x6e, 0x5f, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x78, 0x6e, 0x52,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x5e, 0x0a, 0x1b, 0x69, 0x73, 0x5f,
	0x66, 0x6f, 0x72, 0x65, 0x78, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x75, 0x70, 0x5f, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x18, 0x69, 0x73, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x4d, 0x61, 0x72, 0x6b, 0x75, 0x70, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x12, 0x69, 0x73, 0x5f,
	0x64, 0x63, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65,
	0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x10, 0x69, 0x73, 0x44, 0x63, 0x63, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x56, 0x0a, 0x17, 0x69, 0x73, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x74, 0x61, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42,
	0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x14, 0x69, 0x73, 0x43, 0x61,
	0x72, 0x64, 0x54, 0x61, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x5a, 0x0a, 0x19, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x61,
	0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x16, 0x69, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x61,
	0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x1a,
	0x74, 0x78, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x72,
	0x67, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x16, 0x74, 0x78, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x6e, 0x4f, 0x72, 0x67, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1f, 0x0a, 0x0b,
	0x61, 0x74, 0x6d, 0x5f, 0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x61, 0x74, 0x6d, 0x50, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x27, 0x0a,
	0x25, 0x4e, 0x6f, 0x6e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xdf, 0x01, 0x0a, 0x0f, 0x4d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2b, 0x0a, 0x11, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0f, 0x73, 0x75, 0x62, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x4d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x63, 0x63, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x63, 0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x22, 0x84, 0x01, 0x0a, 0x11, 0x41, 0x75, 0x74,
	0x68, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x52, 0x0a, 0x14, 0x61,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x13, 0x61, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x22,
	0x99, 0x01, 0x0a, 0x0f, 0x52, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x72, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f,
	0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x72, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x49,
	0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x72, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x22, 0xb7, 0x01, 0x0a, 0x08,
	0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x1a, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x72, 0x65, 0x74,
	0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x08, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x07, 0x74, 0x78, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x61, 0x72, 0x64, 0x49, 0x64, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_card_card_notifications_proto_rawDescOnce sync.Once
	file_api_card_card_notifications_proto_rawDescData = file_api_card_card_notifications_proto_rawDesc
)

func file_api_card_card_notifications_proto_rawDescGZIP() []byte {
	file_api_card_card_notifications_proto_rawDescOnce.Do(func() {
		file_api_card_card_notifications_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_card_notifications_proto_rawDescData)
	})
	return file_api_card_card_notifications_proto_rawDescData
}

var file_api_card_card_notifications_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_card_card_notifications_proto_goTypes = []interface{}{
	(*CardNotification)(nil),                      // 0: card.CardNotification
	(*DetailedStatus)(nil),                        // 1: card.DetailedStatus
	(*NotificationDetails)(nil),                   // 2: card.NotificationDetails
	(*FinancialSwitchNotificationDetails)(nil),    // 3: card.FinancialSwitchNotificationDetails
	(*NonFinancialSwitchNotificationDetails)(nil), // 4: card.NonFinancialSwitchNotificationDetails
	(*MerchantDetails)(nil),                       // 5: card.MerchantDetails
	(*AuthSwitchDetails)(nil),                     // 6: card.AuthSwitchDetails
	(*RemitterDetails)(nil),                       // 7: card.RemitterDetails
	(*DedupeId)(nil),                              // 8: card.DedupeId
	(enums.NotificationType)(0),                   // 9: card.enums.NotificationType
	(enums.TransactionState)(0),                   // 10: card.enums.TransactionState
	(enums.NotificationSource)(0),                 // 11: card.enums.NotificationSource
	(*timestamppb.Timestamp)(nil),                 // 12: google.protobuf.Timestamp
	(enums.MessageType)(0),                        // 13: card.enums.MessageType
	(enums.CardTransactionCategory)(0),            // 14: card.enums.CardTransactionCategory
	(*date.Date)(nil),                             // 15: google.type.Date
	(*money.Money)(nil),                           // 16: google.type.Money
	(common.BooleanEnum)(0),                       // 17: api.typesv2.common.BooleanEnum
	(enums.AuthorizationSwitch)(0),                // 18: card.enums.AuthorizationSwitch
}
var file_api_card_card_notifications_proto_depIdxs = []int32{
	2,  // 0: card.CardNotification.notification_details:type_name -> card.NotificationDetails
	9,  // 1: card.CardNotification.notification_type:type_name -> card.enums.NotificationType
	5,  // 2: card.CardNotification.merchant_details:type_name -> card.MerchantDetails
	6,  // 3: card.CardNotification.auth_switch_details:type_name -> card.AuthSwitchDetails
	7,  // 4: card.CardNotification.remitter_details:type_name -> card.RemitterDetails
	10, // 5: card.CardNotification.status:type_name -> card.enums.TransactionState
	1,  // 6: card.CardNotification.detailed_status:type_name -> card.DetailedStatus
	11, // 7: card.CardNotification.notification_source:type_name -> card.enums.NotificationSource
	8,  // 8: card.CardNotification.dedupe_id:type_name -> card.DedupeId
	12, // 9: card.CardNotification.notification_event_time:type_name -> google.protobuf.Timestamp
	12, // 10: card.CardNotification.created_at:type_name -> google.protobuf.Timestamp
	12, // 11: card.CardNotification.updated_at:type_name -> google.protobuf.Timestamp
	12, // 12: card.CardNotification.deleted_at:type_name -> google.protobuf.Timestamp
	13, // 13: card.NotificationDetails.message_type:type_name -> card.enums.MessageType
	3,  // 14: card.NotificationDetails.financial_switch_notification_details:type_name -> card.FinancialSwitchNotificationDetails
	4,  // 15: card.NotificationDetails.non_financial_switch_notification_details:type_name -> card.NonFinancialSwitchNotificationDetails
	14, // 16: card.FinancialSwitchNotificationDetails.txn_category:type_name -> card.enums.CardTransactionCategory
	12, // 17: card.FinancialSwitchNotificationDetails.txn_time:type_name -> google.protobuf.Timestamp
	15, // 18: card.FinancialSwitchNotificationDetails.txn_date:type_name -> google.type.Date
	16, // 19: card.FinancialSwitchNotificationDetails.txn_amount:type_name -> google.type.Money
	17, // 20: card.FinancialSwitchNotificationDetails.is_forex_markup_transaction:type_name -> api.typesv2.common.BooleanEnum
	17, // 21: card.FinancialSwitchNotificationDetails.is_dcc_transaction:type_name -> api.typesv2.common.BooleanEnum
	17, // 22: card.FinancialSwitchNotificationDetails.is_card_tap_transaction:type_name -> api.typesv2.common.BooleanEnum
	17, // 23: card.FinancialSwitchNotificationDetails.is_device_tap_transaction:type_name -> api.typesv2.common.BooleanEnum
	16, // 24: card.FinancialSwitchNotificationDetails.txn_amount_in_org_currency:type_name -> google.type.Money
	18, // 25: card.AuthSwitchDetails.authorization_switch:type_name -> card.enums.AuthorizationSwitch
	12, // 26: card.DedupeId.txn_time:type_name -> google.protobuf.Timestamp
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_api_card_card_notifications_proto_init() }
func file_api_card_card_notifications_proto_init() {
	if File_api_card_card_notifications_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_card_notifications_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardNotification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_card_notifications_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailedStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_card_notifications_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotificationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_card_notifications_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinancialSwitchNotificationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_card_notifications_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFinancialSwitchNotificationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_card_notifications_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MerchantDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_card_notifications_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthSwitchDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_card_notifications_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemitterDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_card_notifications_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DedupeId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_card_card_notifications_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*NotificationDetails_FinancialSwitchNotificationDetails)(nil),
		(*NotificationDetails_NonFinancialSwitchNotificationDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_card_notifications_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_card_notifications_proto_goTypes,
		DependencyIndexes: file_api_card_card_notifications_proto_depIdxs,
		MessageInfos:      file_api_card_card_notifications_proto_msgTypes,
	}.Build()
	File_api_card_card_notifications_proto = out.File
	file_api_card_card_notifications_proto_rawDesc = nil
	file_api_card_card_notifications_proto_goTypes = nil
	file_api_card_card_notifications_proto_depIdxs = nil
}
