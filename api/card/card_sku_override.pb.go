// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/card_sku_override.proto

package card

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CardSKUOverrideFieldMask int32

const (
	CardSKUOverrideFieldMask_CARD_SKU_OVERRIDE_FIELD_MASK_UNSPECIFIED        CardSKUOverrideFieldMask = 0
	CardSKUOverrideFieldMask_CARD_SKU_OVERRIDE_FREE_CARD_REPLACEMENTS_ISSUED CardSKUOverrideFieldMask = 1
	CardSKUOverrideFieldMask_CARD_SKU_OVERRIDE_FREE_CARD_REPLACEMENTS        CardSKUOverrideFieldMask = 2
	CardSKUOverrideFieldMask_CARD_SKU_OVERRIDE_ACTOR_ID                      CardSKUOverrideFieldMask = 3
	CardSKUOverrideFieldMask_CARD_SKU_OVERRIDE_CARD_SKU_TYPE                 CardSKUOverrideFieldMask = 4
	CardSKUOverrideFieldMask_CARD_SKU_OVERRIDE_CREATED_AT                    CardSKUOverrideFieldMask = 5
	CardSKUOverrideFieldMask_CARD_SKU_OVERRIDE_UPDATED_AT                    CardSKUOverrideFieldMask = 6
)

// Enum value maps for CardSKUOverrideFieldMask.
var (
	CardSKUOverrideFieldMask_name = map[int32]string{
		0: "CARD_SKU_OVERRIDE_FIELD_MASK_UNSPECIFIED",
		1: "CARD_SKU_OVERRIDE_FREE_CARD_REPLACEMENTS_ISSUED",
		2: "CARD_SKU_OVERRIDE_FREE_CARD_REPLACEMENTS",
		3: "CARD_SKU_OVERRIDE_ACTOR_ID",
		4: "CARD_SKU_OVERRIDE_CARD_SKU_TYPE",
		5: "CARD_SKU_OVERRIDE_CREATED_AT",
		6: "CARD_SKU_OVERRIDE_UPDATED_AT",
	}
	CardSKUOverrideFieldMask_value = map[string]int32{
		"CARD_SKU_OVERRIDE_FIELD_MASK_UNSPECIFIED":        0,
		"CARD_SKU_OVERRIDE_FREE_CARD_REPLACEMENTS_ISSUED": 1,
		"CARD_SKU_OVERRIDE_FREE_CARD_REPLACEMENTS":        2,
		"CARD_SKU_OVERRIDE_ACTOR_ID":                      3,
		"CARD_SKU_OVERRIDE_CARD_SKU_TYPE":                 4,
		"CARD_SKU_OVERRIDE_CREATED_AT":                    5,
		"CARD_SKU_OVERRIDE_UPDATED_AT":                    6,
	}
)

func (x CardSKUOverrideFieldMask) Enum() *CardSKUOverrideFieldMask {
	p := new(CardSKUOverrideFieldMask)
	*p = x
	return p
}

func (x CardSKUOverrideFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardSKUOverrideFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_card_card_sku_override_proto_enumTypes[0].Descriptor()
}

func (CardSKUOverrideFieldMask) Type() protoreflect.EnumType {
	return &file_api_card_card_sku_override_proto_enumTypes[0]
}

func (x CardSKUOverrideFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardSKUOverrideFieldMask.Descriptor instead.
func (CardSKUOverrideFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_card_card_sku_override_proto_rawDescGZIP(), []int{0}
}

// Card SKU data is defined across actors. For example: number of free cards is a property of a card variant and
// applies to all actors. However, there could be exceptions per actor. For eg: higher free card for privileged users.
// An entry in this card_sku_by_actor table overrides the global SKU data for an actor.
//
// We will be storing information for actors for which we issued the same card skew type but we want to
// change the SKU param's because of any product/CX/Technical requirement.
//
// For ex: For few users we ended by creating multiple cards during onboarding because of some savings account creation
// queue retries issue, so to incorporate change of free_replacements for such particular users, this is added.
// Another case is for users we failed to dispatch their first cards and asked them to create a new card so we want to
// provide them an additional free card. This table will store data for such exception cases.
type CardSKUOverride struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// CardSKUType represents the card variants offered by us
	CardSkuType CardSKUType `protobuf:"varint,2,opt,name=card_sku_type,json=cardSkuType,proto3,enum=card.CardSKUType" json:"card_sku_type,omitempty"`
	// Number of card replacements which will be free for the user.
	FreeCardReplacements int32 `protobuf:"varint,3,opt,name=free_card_replacements,json=freeCardReplacements,proto3" json:"free_card_replacements,omitempty"`
	// Number of free card replacements already issued to the user.
	FreeCardReplacementsIssued int32 `protobuf:"varint,4,opt,name=free_card_replacements_issued,json=freeCardReplacementsIssued,proto3" json:"free_card_replacements_issued,omitempty"`
}

func (x *CardSKUOverride) Reset() {
	*x = CardSKUOverride{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_card_sku_override_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardSKUOverride) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardSKUOverride) ProtoMessage() {}

func (x *CardSKUOverride) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_card_sku_override_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardSKUOverride.ProtoReflect.Descriptor instead.
func (*CardSKUOverride) Descriptor() ([]byte, []int) {
	return file_api_card_card_sku_override_proto_rawDescGZIP(), []int{0}
}

func (x *CardSKUOverride) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CardSKUOverride) GetCardSkuType() CardSKUType {
	if x != nil {
		return x.CardSkuType
	}
	return CardSKUType_CARD_SKU_TYPE_UNSPECIFIED
}

func (x *CardSKUOverride) GetFreeCardReplacements() int32 {
	if x != nil {
		return x.FreeCardReplacements
	}
	return 0
}

func (x *CardSKUOverride) GetFreeCardReplacementsIssued() int32 {
	if x != nil {
		return x.FreeCardReplacementsIssued
	}
	return 0
}

var File_api_card_card_sku_override_proto protoreflect.FileDescriptor

var file_api_card_card_sku_override_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x73, 0x6b, 0x75, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x04, 0x63, 0x61, 0x72, 0x64, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61,
	0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x6b, 0x75, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xdc, 0x01, 0x0a, 0x0f, 0x43, 0x61, 0x72, 0x64, 0x53, 0x4b, 0x55, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x35, 0x0a, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x6b, 0x75, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43,
	0x61, 0x72, 0x64, 0x53, 0x4b, 0x55, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x64,
	0x53, 0x6b, 0x75, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x66, 0x72, 0x65, 0x65, 0x5f,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x66, 0x72, 0x65, 0x65, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x41, 0x0a,
	0x1d, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x1a, 0x66, 0x72, 0x65, 0x65, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x70, 0x6c, 0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x49, 0x73, 0x73, 0x75, 0x65, 0x64,
	0x2a, 0xb4, 0x02, 0x0a, 0x18, 0x43, 0x61, 0x72, 0x64, 0x53, 0x4b, 0x55, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2c, 0x0a,
	0x28, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x4b, 0x55, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x52, 0x49,
	0x44, 0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x33, 0x0a, 0x2f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x53, 0x4b, 0x55, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x52, 0x49, 0x44, 0x45,
	0x5f, 0x46, 0x52, 0x45, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x50, 0x4c, 0x41,
	0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x44, 0x10, 0x01,
	0x12, 0x2c, 0x0a, 0x28, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x4b, 0x55, 0x5f, 0x4f, 0x56, 0x45,
	0x52, 0x52, 0x49, 0x44, 0x45, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x52, 0x45, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x02, 0x12, 0x1e,
	0x0a, 0x1a, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x4b, 0x55, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x52,
	0x49, 0x44, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x23,
	0x0a, 0x1f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x4b, 0x55, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x52,
	0x49, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x4b, 0x55, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x4b, 0x55, 0x5f,
	0x4f, 0x56, 0x45, 0x52, 0x52, 0x49, 0x44, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44,
	0x5f, 0x41, 0x54, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x4b,
	0x55, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x52, 0x49, 0x44, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x06, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_card_card_sku_override_proto_rawDescOnce sync.Once
	file_api_card_card_sku_override_proto_rawDescData = file_api_card_card_sku_override_proto_rawDesc
)

func file_api_card_card_sku_override_proto_rawDescGZIP() []byte {
	file_api_card_card_sku_override_proto_rawDescOnce.Do(func() {
		file_api_card_card_sku_override_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_card_sku_override_proto_rawDescData)
	})
	return file_api_card_card_sku_override_proto_rawDescData
}

var file_api_card_card_sku_override_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_card_card_sku_override_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_card_card_sku_override_proto_goTypes = []interface{}{
	(CardSKUOverrideFieldMask)(0), // 0: card.CardSKUOverrideFieldMask
	(*CardSKUOverride)(nil),       // 1: card.CardSKUOverride
	(CardSKUType)(0),              // 2: card.CardSKUType
}
var file_api_card_card_sku_override_proto_depIdxs = []int32{
	2, // 0: card.CardSKUOverride.card_sku_type:type_name -> card.CardSKUType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_card_card_sku_override_proto_init() }
func file_api_card_card_sku_override_proto_init() {
	if File_api_card_card_sku_override_proto != nil {
		return
	}
	file_api_card_card_sku_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_card_card_sku_override_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardSKUOverride); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_card_sku_override_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_card_sku_override_proto_goTypes,
		DependencyIndexes: file_api_card_card_sku_override_proto_depIdxs,
		EnumInfos:         file_api_card_card_sku_override_proto_enumTypes,
		MessageInfos:      file_api_card_card_sku_override_proto_msgTypes,
	}.Build()
	File_api_card_card_sku_override_proto = out.File
	file_api_card_card_sku_override_proto_rawDesc = nil
	file_api_card_card_sku_override_proto_goTypes = nil
	file_api_card_card_sku_override_proto_depIdxs = nil
}
