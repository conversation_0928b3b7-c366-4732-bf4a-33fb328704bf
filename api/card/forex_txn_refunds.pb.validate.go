// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/forex_txn_refunds.proto

package card

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/card/enums"

	external "github.com/epifi/gamma/api/tiering/external"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.RefundStatus(0)

	_ = external.Tier(0)
)

// Validate checks the field values on DcForexTxnRefund with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DcForexTxnRefund) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DcForexTxnRefund with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DcForexTxnRefundMultiError, or nil if none found.
func (m *DcForexTxnRefund) ValidateAll() error {
	return m.validate(true)
}

func (m *DcForexTxnRefund) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for TxnId

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetTotalTxnAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DcForexTxnRefundValidationError{
					field:  "TotalTxnAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DcForexTxnRefundValidationError{
					field:  "TotalTxnAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalTxnAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DcForexTxnRefundValidationError{
				field:  "TotalTxnAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRefundAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DcForexTxnRefundValidationError{
					field:  "RefundAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DcForexTxnRefundValidationError{
					field:  "RefundAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRefundAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DcForexTxnRefundValidationError{
				field:  "RefundAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTxnTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DcForexTxnRefundValidationError{
					field:  "TxnTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DcForexTxnRefundValidationError{
					field:  "TxnTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DcForexTxnRefundValidationError{
				field:  "TxnTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RefundStatus

	// no validation rules for ProcessIdentifier

	// no validation rules for TxnTimeUserTier

	// no validation rules for RefundProcessingMode

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DcForexTxnRefundValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DcForexTxnRefundValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DcForexTxnRefundValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DcForexTxnRefundValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DcForexTxnRefundValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DcForexTxnRefundValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DcForexTxnRefundValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DcForexTxnRefundValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DcForexTxnRefundValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrchId

	// no validation rules for TxnType

	// no validation rules for RefundTxnId

	// no validation rules for RefundSubStatus

	if all {
		switch v := interface{}(m.GetForexChargesInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DcForexTxnRefundValidationError{
					field:  "ForexChargesInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DcForexTxnRefundValidationError{
					field:  "ForexChargesInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForexChargesInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DcForexTxnRefundValidationError{
				field:  "ForexChargesInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DedupeIdentifier

	// no validation rules for OriginalTransaction

	if len(errors) > 0 {
		return DcForexTxnRefundMultiError(errors)
	}

	return nil
}

// DcForexTxnRefundMultiError is an error wrapping multiple validation errors
// returned by DcForexTxnRefund.ValidateAll() if the designated constraints
// aren't met.
type DcForexTxnRefundMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DcForexTxnRefundMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DcForexTxnRefundMultiError) AllErrors() []error { return m }

// DcForexTxnRefundValidationError is the validation error returned by
// DcForexTxnRefund.Validate if the designated constraints aren't met.
type DcForexTxnRefundValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DcForexTxnRefundValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DcForexTxnRefundValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DcForexTxnRefundValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DcForexTxnRefundValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DcForexTxnRefundValidationError) ErrorName() string { return "DcForexTxnRefundValidationError" }

// Error satisfies the builtin error interface
func (e DcForexTxnRefundValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDcForexTxnRefund.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DcForexTxnRefundValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DcForexTxnRefundValidationError{}

// Validate checks the field values on ForexChargesInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ForexChargesInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForexChargesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForexChargesInfoMultiError, or nil if none found.
func (m *ForexChargesInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ForexChargesInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CountryCode

	// no validation rules for ForexPercentage

	if all {
		switch v := interface{}(m.GetForexChargeAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForexChargesInfoValidationError{
					field:  "ForexChargeAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForexChargesInfoValidationError{
					field:  "ForexChargeAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForexChargeAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForexChargesInfoValidationError{
				field:  "ForexChargeAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSentToBankAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForexChargesInfoValidationError{
					field:  "SentToBankAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForexChargesInfoValidationError{
					field:  "SentToBankAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSentToBankAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForexChargesInfoValidationError{
				field:  "SentToBankAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ForexChargesInfoMultiError(errors)
	}

	return nil
}

// ForexChargesInfoMultiError is an error wrapping multiple validation errors
// returned by ForexChargesInfo.ValidateAll() if the designated constraints
// aren't met.
type ForexChargesInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForexChargesInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForexChargesInfoMultiError) AllErrors() []error { return m }

// ForexChargesInfoValidationError is the validation error returned by
// ForexChargesInfo.Validate if the designated constraints aren't met.
type ForexChargesInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForexChargesInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForexChargesInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForexChargesInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForexChargesInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForexChargesInfoValidationError) ErrorName() string { return "ForexChargesInfoValidationError" }

// Error satisfies the builtin error interface
func (e ForexChargesInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForexChargesInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForexChargesInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForexChargesInfoValidationError{}

// Validate checks the field values on ForexRefundAggregate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForexRefundAggregate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForexRefundAggregate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForexRefundAggregateMultiError, or nil if none found.
func (m *ForexRefundAggregate) ValidateAll() error {
	return m.validate(true)
}

func (m *ForexRefundAggregate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnTimeUserTier

	// no validation rules for RefundAmountUnitsSum

	// no validation rules for RefundAmountNanosSum

	// no validation rules for RefundCount

	if len(errors) > 0 {
		return ForexRefundAggregateMultiError(errors)
	}

	return nil
}

// ForexRefundAggregateMultiError is an error wrapping multiple validation
// errors returned by ForexRefundAggregate.ValidateAll() if the designated
// constraints aren't met.
type ForexRefundAggregateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForexRefundAggregateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForexRefundAggregateMultiError) AllErrors() []error { return m }

// ForexRefundAggregateValidationError is the validation error returned by
// ForexRefundAggregate.Validate if the designated constraints aren't met.
type ForexRefundAggregateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForexRefundAggregateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForexRefundAggregateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForexRefundAggregateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForexRefundAggregateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForexRefundAggregateValidationError) ErrorName() string {
	return "ForexRefundAggregateValidationError"
}

// Error satisfies the builtin error interface
func (e ForexRefundAggregateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForexRefundAggregate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForexRefundAggregateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForexRefundAggregateValidationError{}
