package card

import (
	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/nulltypes"
)

// Scanner interface implementation for parsing DedupeId while reading from DB
func (a *DedupeId) Scan(src interface{}) error {
	var (
		err error
	)

	if src == nil {
		return nil
	}
	val, ok := src.(string)
	if !ok {
		err = fmt.Errorf("expected []byte, got %T", src)
		return err
	}

	if val == "" {
		return nil
	}

	err = idgen.DecodeProtoFromStdBase64(val, a)
	if err != nil {
		return fmt.Errorf("error in unmarshal %w", err)
	}
	return nil
}

// Valuer interface implementation for storing the DedupeId in string format in DB
func (a *DedupeId) Value() (driver.Value, error) {
	if a == nil {
		return nulltypes.NewNullString(""), nil
	}

	base64EncodedString, err := a.getBase64EncodedString()
	if err != nil {
		return nil, fmt.Errorf("error in base64 conversion %w", err)
	}

	return nulltypes.NewNullString(base64EncodedString), nil
}

func (a *DedupeId) IsEqual(otherId *DedupeId) (bool, error) {
	selfBase64EncodedString, err := a.getBase64EncodedString()
	if err != nil {
		return false, fmt.Errorf("error in base64 conversion %w", err)
	}

	otherBase64EncodedString, err := otherId.getBase64EncodedString()
	if err != nil {
		return false, fmt.Errorf("error in base64 conversion %w", err)
	}

	return selfBase64EncodedString == otherBase64EncodedString, nil
}

// Marshaler interface implementation for DedupeId
func (a *DedupeId) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for DedupeId
func (a *DedupeId) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

func (a *DedupeId) getBase64EncodedString() (string, error) {
	return idgen.EncodeProtoToStdBase64(a)
}
