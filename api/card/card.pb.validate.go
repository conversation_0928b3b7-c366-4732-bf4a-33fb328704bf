// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/card.proto

package card

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on Card with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Card) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Card with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CardMultiError, or nil if none found.
func (m *Card) ValidateAll() error {
	return m.validate(true)
}

func (m *Card) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for State

	// no validation rules for Type

	// no validation rules for Form

	// no validation rules for NetworkType

	// no validation rules for IssuerBank

	// no validation rules for IssueType

	// no validation rules for BankIdentifier

	// no validation rules for CardCategory

	if all {
		switch v := interface{}(m.GetBasicInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "BasicInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "BasicInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBasicInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardValidationError{
				field:  "BasicInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetControls()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "Controls",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "Controls",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetControls()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardValidationError{
				field:  "Controls",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GroupId

	// no validation rules for SavingsAccountId

	if all {
		switch v := interface{}(m.GetPinSetOtpToken()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "PinSetOtpToken",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "PinSetOtpToken",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPinSetOtpToken()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardValidationError{
				field:  "PinSetOtpToken",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PreviousCardId

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardSkuType

	if all {
		switch v := interface{}(m.GetIssuanceFee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "IssuanceFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "IssuanceFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIssuanceFee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardValidationError{
				field:  "IssuanceFee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardMultiError(errors)
	}

	return nil
}

// CardMultiError is an error wrapping multiple validation errors returned by
// Card.ValidateAll() if the designated constraints aren't met.
type CardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardMultiError) AllErrors() []error { return m }

// CardValidationError is the validation error returned by Card.Validate if the
// designated constraints aren't met.
type CardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardValidationError) ErrorName() string { return "CardValidationError" }

// Error satisfies the builtin error interface
func (e CardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardValidationError{}

// Validate checks the field values on BasicCardInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BasicCardInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BasicCardInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BasicCardInfoMultiError, or
// nil if none found.
func (m *BasicCardInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *BasicCardInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MaskedCardNumber

	// no validation rules for CardNumber

	// no validation rules for Expiry

	// no validation rules for Cvv

	// no validation rules for CustomerName

	// no validation rules for ExpiryToken

	if len(errors) > 0 {
		return BasicCardInfoMultiError(errors)
	}

	return nil
}

// BasicCardInfoMultiError is an error wrapping multiple validation errors
// returned by BasicCardInfo.ValidateAll() if the designated constraints
// aren't met.
type BasicCardInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BasicCardInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BasicCardInfoMultiError) AllErrors() []error { return m }

// BasicCardInfoValidationError is the validation error returned by
// BasicCardInfo.Validate if the designated constraints aren't met.
type BasicCardInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BasicCardInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BasicCardInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BasicCardInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BasicCardInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BasicCardInfoValidationError) ErrorName() string { return "BasicCardInfoValidationError" }

// Error satisfies the builtin error interface
func (e BasicCardInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBasicCardInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BasicCardInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BasicCardInfoValidationError{}

// Validate checks the field values on PinSetOtpToken with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PinSetOtpToken) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PinSetOtpToken with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PinSetOtpTokenMultiError,
// or nil if none found.
func (m *PinSetOtpToken) ValidateAll() error {
	return m.validate(true)
}

func (m *PinSetOtpToken) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Otp

	if all {
		switch v := interface{}(m.GetExpireAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PinSetOtpTokenValidationError{
					field:  "ExpireAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PinSetOtpTokenValidationError{
					field:  "ExpireAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpireAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PinSetOtpTokenValidationError{
				field:  "ExpireAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PinSetOtpTokenMultiError(errors)
	}

	return nil
}

// PinSetOtpTokenMultiError is an error wrapping multiple validation errors
// returned by PinSetOtpToken.ValidateAll() if the designated constraints
// aren't met.
type PinSetOtpTokenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PinSetOtpTokenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PinSetOtpTokenMultiError) AllErrors() []error { return m }

// PinSetOtpTokenValidationError is the validation error returned by
// PinSetOtpToken.Validate if the designated constraints aren't met.
type PinSetOtpTokenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PinSetOtpTokenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PinSetOtpTokenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PinSetOtpTokenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PinSetOtpTokenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PinSetOtpTokenValidationError) ErrorName() string { return "PinSetOtpTokenValidationError" }

// Error satisfies the builtin error interface
func (e PinSetOtpTokenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPinSetOtpToken.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PinSetOtpTokenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PinSetOtpTokenValidationError{}
