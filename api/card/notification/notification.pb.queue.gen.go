// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/card/notification
package notification

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessCardSwitchFinancialNotificationsMethod    = "ProcessCardSwitchFinancialNotifications"
	ProcessCardSwitchNonFinancialNotificationsMethod = "ProcessCardSwitchNonFinancialNotifications"
	ProcessForexTransactionsRefundMethod             = "ProcessForexTransactionsRefund"
	ProcessCardTransactionsMethod                    = "ProcessCardTransactions"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &ProcessCardSwitchFinancialNotificationsRequest{}
var _ queue.ConsumerRequest = &ProcessCardSwitchNonFinancialNotificationsRequest{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCardSwitchFinancialNotificationsRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessCardSwitchNonFinancialNotificationsRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterProcessCardSwitchFinancialNotificationsMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardSwitchFinancialNotificationsMethodToSubscriber(subscriber queue.Subscriber, srv NotificationConsumerServiceServer) {
	subscriber.RegisterService(&NotificationConsumerService_ServiceDesc, srv, ProcessCardSwitchFinancialNotificationsMethod)
}

// RegisterProcessCardSwitchNonFinancialNotificationsMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardSwitchNonFinancialNotificationsMethodToSubscriber(subscriber queue.Subscriber, srv NotificationConsumerServiceServer) {
	subscriber.RegisterService(&NotificationConsumerService_ServiceDesc, srv, ProcessCardSwitchNonFinancialNotificationsMethod)
}

// RegisterProcessForexTransactionsRefundMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessForexTransactionsRefundMethodToSubscriber(subscriber queue.Subscriber, srv NotificationConsumerServiceServer) {
	subscriber.RegisterService(&NotificationConsumerService_ServiceDesc, srv, ProcessForexTransactionsRefundMethod)
}

// RegisterProcessCardTransactionsMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCardTransactionsMethodToSubscriber(subscriber queue.Subscriber, srv NotificationConsumerServiceServer) {
	subscriber.RegisterService(&NotificationConsumerService_ServiceDesc, srv, ProcessCardTransactionsMethod)
}
