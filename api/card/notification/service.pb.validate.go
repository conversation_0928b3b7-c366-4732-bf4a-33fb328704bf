// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/notification/service.proto

package notification

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/card/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.CardTransactionCategory(0)
)

// Validate checks the field values on ProcessCardTransactionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessCardTransactionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardTransactionsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessCardTransactionsResponseMultiError, or nil if none found.
func (m *ProcessCardTransactionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardTransactionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardTransactionsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardTransactionsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardTransactionsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardTransactionsResponseMultiError(errors)
	}

	return nil
}

// ProcessCardTransactionsResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessCardTransactionsResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessCardTransactionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardTransactionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardTransactionsResponseMultiError) AllErrors() []error { return m }

// ProcessCardTransactionsResponseValidationError is the validation error
// returned by ProcessCardTransactionsResponse.Validate if the designated
// constraints aren't met.
type ProcessCardTransactionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardTransactionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardTransactionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardTransactionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardTransactionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardTransactionsResponseValidationError) ErrorName() string {
	return "ProcessCardTransactionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardTransactionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardTransactionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardTransactionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardTransactionsResponseValidationError{}

// Validate checks the field values on ProcessForexTransactionsRefundResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessForexTransactionsRefundResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessForexTransactionsRefundResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ProcessForexTransactionsRefundResponseMultiError, or nil if none found.
func (m *ProcessForexTransactionsRefundResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessForexTransactionsRefundResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessForexTransactionsRefundResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessForexTransactionsRefundResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessForexTransactionsRefundResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessForexTransactionsRefundResponseMultiError(errors)
	}

	return nil
}

// ProcessForexTransactionsRefundResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessForexTransactionsRefundResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessForexTransactionsRefundResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessForexTransactionsRefundResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessForexTransactionsRefundResponseMultiError) AllErrors() []error { return m }

// ProcessForexTransactionsRefundResponseValidationError is the validation
// error returned by ProcessForexTransactionsRefundResponse.Validate if the
// designated constraints aren't met.
type ProcessForexTransactionsRefundResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessForexTransactionsRefundResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessForexTransactionsRefundResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessForexTransactionsRefundResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessForexTransactionsRefundResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessForexTransactionsRefundResponseValidationError) ErrorName() string {
	return "ProcessForexTransactionsRefundResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessForexTransactionsRefundResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessForexTransactionsRefundResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessForexTransactionsRefundResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessForexTransactionsRefundResponseValidationError{}

// Validate checks the field values on
// ProcessCardSwitchFinancialNotificationsRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessCardSwitchFinancialNotificationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessCardSwitchFinancialNotificationsRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ProcessCardSwitchFinancialNotificationsRequestMultiError, or nil if none found.
func (m *ProcessCardSwitchFinancialNotificationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardSwitchFinancialNotificationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardSwitchFinancialNotificationsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardSwitchFinancialNotificationsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardSwitchFinancialNotificationsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardTransactionCategory

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetExecutedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardSwitchFinancialNotificationsRequestValidationError{
					field:  "ExecutedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardSwitchFinancialNotificationsRequestValidationError{
					field:  "ExecutedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExecutedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardSwitchFinancialNotificationsRequestValidationError{
				field:  "ExecutedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountNumber

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardSwitchFinancialNotificationsRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardSwitchFinancialNotificationsRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardSwitchFinancialNotificationsRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NotificationType

	// no validation rules for MessageType

	// no validation rules for Arn

	// no validation rules for OriginalTransactionId

	// no validation rules for VendorCardId

	// no validation rules for CountryCode

	// no validation rules for TransactionRemarks

	// no validation rules for MerchantName

	// no validation rules for MerchantId

	// no validation rules for PaymentGateway

	// no validation rules for SubMerchantId

	// no validation rules for TerminalId

	// no validation rules for AcquiringBank

	// no validation rules for Mcc

	// no validation rules for MerchantLocation

	// no validation rules for AuthorisationSwitch

	// no validation rules for TransactionResponseCode

	// no validation rules for TransactionEntryMode

	// no validation rules for AuthId

	// no validation rules for TransactionState

	// no validation rules for SwitchNotificationResponse

	// no validation rules for RemitterCode

	// no validation rules for RemitterInstrumentType

	// no validation rules for RawNotificationData

	// no validation rules for IsForexMarkupTransaction

	if all {
		switch v := interface{}(m.GetTransactionTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardSwitchFinancialNotificationsRequestValidationError{
					field:  "TransactionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardSwitchFinancialNotificationsRequestValidationError{
					field:  "TransactionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardSwitchFinancialNotificationsRequestValidationError{
				field:  "TransactionTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsDccTransaction

	if all {
		switch v := interface{}(m.GetTxnAmountInOrgCurrency()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardSwitchFinancialNotificationsRequestValidationError{
					field:  "TxnAmountInOrgCurrency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardSwitchFinancialNotificationsRequestValidationError{
					field:  "TxnAmountInOrgCurrency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnAmountInOrgCurrency()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardSwitchFinancialNotificationsRequestValidationError{
				field:  "TxnAmountInOrgCurrency",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsCardTap

	// no validation rules for IsDeviceTap

	// no validation rules for AtmPincode

	if len(errors) > 0 {
		return ProcessCardSwitchFinancialNotificationsRequestMultiError(errors)
	}

	return nil
}

// ProcessCardSwitchFinancialNotificationsRequestMultiError is an error
// wrapping multiple validation errors returned by
// ProcessCardSwitchFinancialNotificationsRequest.ValidateAll() if the
// designated constraints aren't met.
type ProcessCardSwitchFinancialNotificationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardSwitchFinancialNotificationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardSwitchFinancialNotificationsRequestMultiError) AllErrors() []error { return m }

// ProcessCardSwitchFinancialNotificationsRequestValidationError is the
// validation error returned by
// ProcessCardSwitchFinancialNotificationsRequest.Validate if the designated
// constraints aren't met.
type ProcessCardSwitchFinancialNotificationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardSwitchFinancialNotificationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardSwitchFinancialNotificationsRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ProcessCardSwitchFinancialNotificationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardSwitchFinancialNotificationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardSwitchFinancialNotificationsRequestValidationError) ErrorName() string {
	return "ProcessCardSwitchFinancialNotificationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardSwitchFinancialNotificationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardSwitchFinancialNotificationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardSwitchFinancialNotificationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardSwitchFinancialNotificationsRequestValidationError{}

// Validate checks the field values on
// ProcessCardSwitchFinancialNotificationsResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessCardSwitchFinancialNotificationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessCardSwitchFinancialNotificationsResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ProcessCardSwitchFinancialNotificationsResponseMultiError, or nil if none found.
func (m *ProcessCardSwitchFinancialNotificationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardSwitchFinancialNotificationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardSwitchFinancialNotificationsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardSwitchFinancialNotificationsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardSwitchFinancialNotificationsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardSwitchFinancialNotificationsResponseMultiError(errors)
	}

	return nil
}

// ProcessCardSwitchFinancialNotificationsResponseMultiError is an error
// wrapping multiple validation errors returned by
// ProcessCardSwitchFinancialNotificationsResponse.ValidateAll() if the
// designated constraints aren't met.
type ProcessCardSwitchFinancialNotificationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardSwitchFinancialNotificationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardSwitchFinancialNotificationsResponseMultiError) AllErrors() []error { return m }

// ProcessCardSwitchFinancialNotificationsResponseValidationError is the
// validation error returned by
// ProcessCardSwitchFinancialNotificationsResponse.Validate if the designated
// constraints aren't met.
type ProcessCardSwitchFinancialNotificationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardSwitchFinancialNotificationsResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ProcessCardSwitchFinancialNotificationsResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ProcessCardSwitchFinancialNotificationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardSwitchFinancialNotificationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardSwitchFinancialNotificationsResponseValidationError) ErrorName() string {
	return "ProcessCardSwitchFinancialNotificationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardSwitchFinancialNotificationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardSwitchFinancialNotificationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardSwitchFinancialNotificationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardSwitchFinancialNotificationsResponseValidationError{}

// Validate checks the field values on
// ProcessCardSwitchNonFinancialNotificationsRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessCardSwitchNonFinancialNotificationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessCardSwitchNonFinancialNotificationsRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ProcessCardSwitchNonFinancialNotificationsRequestMultiError, or nil if none found.
func (m *ProcessCardSwitchNonFinancialNotificationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardSwitchNonFinancialNotificationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardSwitchNonFinancialNotificationsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardSwitchNonFinancialNotificationsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardSwitchNonFinancialNotificationsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardSwitchNonFinancialNotificationsRequestValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardSwitchNonFinancialNotificationsRequestValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardSwitchNonFinancialNotificationsRequestValidationError{
				field:  "CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NotificationType

	// no validation rules for VendorCardId

	// no validation rules for RequestId

	// no validation rules for RawNotificationData

	if len(errors) > 0 {
		return ProcessCardSwitchNonFinancialNotificationsRequestMultiError(errors)
	}

	return nil
}

// ProcessCardSwitchNonFinancialNotificationsRequestMultiError is an error
// wrapping multiple validation errors returned by
// ProcessCardSwitchNonFinancialNotificationsRequest.ValidateAll() if the
// designated constraints aren't met.
type ProcessCardSwitchNonFinancialNotificationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardSwitchNonFinancialNotificationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardSwitchNonFinancialNotificationsRequestMultiError) AllErrors() []error { return m }

// ProcessCardSwitchNonFinancialNotificationsRequestValidationError is the
// validation error returned by
// ProcessCardSwitchNonFinancialNotificationsRequest.Validate if the
// designated constraints aren't met.
type ProcessCardSwitchNonFinancialNotificationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardSwitchNonFinancialNotificationsRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ProcessCardSwitchNonFinancialNotificationsRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ProcessCardSwitchNonFinancialNotificationsRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ProcessCardSwitchNonFinancialNotificationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardSwitchNonFinancialNotificationsRequestValidationError) ErrorName() string {
	return "ProcessCardSwitchNonFinancialNotificationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardSwitchNonFinancialNotificationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardSwitchNonFinancialNotificationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardSwitchNonFinancialNotificationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardSwitchNonFinancialNotificationsRequestValidationError{}

// Validate checks the field values on
// ProcessCardSwitchNonFinancialNotificationsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProcessCardSwitchNonFinancialNotificationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessCardSwitchNonFinancialNotificationsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ProcessCardSwitchNonFinancialNotificationsResponseMultiError, or nil if
// none found.
func (m *ProcessCardSwitchNonFinancialNotificationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardSwitchNonFinancialNotificationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardSwitchNonFinancialNotificationsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardSwitchNonFinancialNotificationsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardSwitchNonFinancialNotificationsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardSwitchNonFinancialNotificationsResponseMultiError(errors)
	}

	return nil
}

// ProcessCardSwitchNonFinancialNotificationsResponseMultiError is an error
// wrapping multiple validation errors returned by
// ProcessCardSwitchNonFinancialNotificationsResponse.ValidateAll() if the
// designated constraints aren't met.
type ProcessCardSwitchNonFinancialNotificationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardSwitchNonFinancialNotificationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardSwitchNonFinancialNotificationsResponseMultiError) AllErrors() []error { return m }

// ProcessCardSwitchNonFinancialNotificationsResponseValidationError is the
// validation error returned by
// ProcessCardSwitchNonFinancialNotificationsResponse.Validate if the
// designated constraints aren't met.
type ProcessCardSwitchNonFinancialNotificationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardSwitchNonFinancialNotificationsResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ProcessCardSwitchNonFinancialNotificationsResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ProcessCardSwitchNonFinancialNotificationsResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ProcessCardSwitchNonFinancialNotificationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardSwitchNonFinancialNotificationsResponseValidationError) ErrorName() string {
	return "ProcessCardSwitchNonFinancialNotificationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardSwitchNonFinancialNotificationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardSwitchNonFinancialNotificationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardSwitchNonFinancialNotificationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardSwitchNonFinancialNotificationsResponseValidationError{}
