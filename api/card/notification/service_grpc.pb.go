// protolint:disable MAX_LINE_LENGTH
//go:generate gen_queue_pb

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/card/notification/service.proto

package notification

import (
	context "context"
	order "github.com/epifi/gamma/api/order"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NotificationConsumerService_ProcessCardSwitchFinancialNotifications_FullMethodName    = "/card.notification.NotificationConsumerService/ProcessCardSwitchFinancialNotifications"
	NotificationConsumerService_ProcessCardSwitchNonFinancialNotifications_FullMethodName = "/card.notification.NotificationConsumerService/ProcessCardSwitchNonFinancialNotifications"
	NotificationConsumerService_ProcessForexTransactionsRefund_FullMethodName             = "/card.notification.NotificationConsumerService/ProcessForexTransactionsRefund"
	NotificationConsumerService_ProcessCardTransactions_FullMethodName                    = "/card.notification.NotificationConsumerService/ProcessCardTransactions"
)

// NotificationConsumerServiceClient is the client API for NotificationConsumerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NotificationConsumerServiceClient interface {
	// ProcessCardSwitchFinancialNotifications processes notification related to card transactions, this includes both
	// successful as well as failure transactions, we get various details for a transaction such as transaction_mode,
	// execution timestamp, amount, transaction type (recurring/one time) in the notifications. We will parse all these
	// information and create order and transactions for it. Payment service will take care of the dedupe logic in case
	// of duplicate notifications, dedupe will be done on combination of execution timestamp and arn. In case of
	// notification miss for successful transaction we will record the transaction via recon but won't be able
	// to record declined transactions as we don't have any other entry point for declined transactions. Future goals is
	// to send notifications for these card transactions.
	ProcessCardSwitchFinancialNotifications(ctx context.Context, in *ProcessCardSwitchFinancialNotificationsRequest, opts ...grpc.CallOption) (*ProcessCardSwitchFinancialNotificationsResponse, error)
	// ProcessCardSwitchNonFinancialNotifications processes notifications related to non financial card operations such as
	// limit update, card status change, card pin set/change, we will process these notification and update the
	// states on our end and send corresponding communications to the user.
	ProcessCardSwitchNonFinancialNotifications(ctx context.Context, in *ProcessCardSwitchNonFinancialNotificationsRequest, opts ...grpc.CallOption) (*ProcessCardSwitchNonFinancialNotificationsResponse, error)
	// ProcessForexTransactionsRefund RPC will  consume messages from a queue subscribing to order-update-topic and based on the preconditions for forex txn
	// refunds and then initiate the workflow to process refunds
	ProcessForexTransactionsRefund(ctx context.Context, in *order.OrderUpdate, opts ...grpc.CallOption) (*ProcessForexTransactionsRefundResponse, error)
	// ProcessCardTransactions RPC will consume events from order-update topic, it will consume card txn and ignore the protocol related txns.
	ProcessCardTransactions(ctx context.Context, in *order.OrderUpdate, opts ...grpc.CallOption) (*ProcessCardTransactionsResponse, error)
}

type notificationConsumerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNotificationConsumerServiceClient(cc grpc.ClientConnInterface) NotificationConsumerServiceClient {
	return &notificationConsumerServiceClient{cc}
}

func (c *notificationConsumerServiceClient) ProcessCardSwitchFinancialNotifications(ctx context.Context, in *ProcessCardSwitchFinancialNotificationsRequest, opts ...grpc.CallOption) (*ProcessCardSwitchFinancialNotificationsResponse, error) {
	out := new(ProcessCardSwitchFinancialNotificationsResponse)
	err := c.cc.Invoke(ctx, NotificationConsumerService_ProcessCardSwitchFinancialNotifications_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationConsumerServiceClient) ProcessCardSwitchNonFinancialNotifications(ctx context.Context, in *ProcessCardSwitchNonFinancialNotificationsRequest, opts ...grpc.CallOption) (*ProcessCardSwitchNonFinancialNotificationsResponse, error) {
	out := new(ProcessCardSwitchNonFinancialNotificationsResponse)
	err := c.cc.Invoke(ctx, NotificationConsumerService_ProcessCardSwitchNonFinancialNotifications_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationConsumerServiceClient) ProcessForexTransactionsRefund(ctx context.Context, in *order.OrderUpdate, opts ...grpc.CallOption) (*ProcessForexTransactionsRefundResponse, error) {
	out := new(ProcessForexTransactionsRefundResponse)
	err := c.cc.Invoke(ctx, NotificationConsumerService_ProcessForexTransactionsRefund_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationConsumerServiceClient) ProcessCardTransactions(ctx context.Context, in *order.OrderUpdate, opts ...grpc.CallOption) (*ProcessCardTransactionsResponse, error) {
	out := new(ProcessCardTransactionsResponse)
	err := c.cc.Invoke(ctx, NotificationConsumerService_ProcessCardTransactions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NotificationConsumerServiceServer is the server API for NotificationConsumerService service.
// All implementations should embed UnimplementedNotificationConsumerServiceServer
// for forward compatibility
type NotificationConsumerServiceServer interface {
	// ProcessCardSwitchFinancialNotifications processes notification related to card transactions, this includes both
	// successful as well as failure transactions, we get various details for a transaction such as transaction_mode,
	// execution timestamp, amount, transaction type (recurring/one time) in the notifications. We will parse all these
	// information and create order and transactions for it. Payment service will take care of the dedupe logic in case
	// of duplicate notifications, dedupe will be done on combination of execution timestamp and arn. In case of
	// notification miss for successful transaction we will record the transaction via recon but won't be able
	// to record declined transactions as we don't have any other entry point for declined transactions. Future goals is
	// to send notifications for these card transactions.
	ProcessCardSwitchFinancialNotifications(context.Context, *ProcessCardSwitchFinancialNotificationsRequest) (*ProcessCardSwitchFinancialNotificationsResponse, error)
	// ProcessCardSwitchNonFinancialNotifications processes notifications related to non financial card operations such as
	// limit update, card status change, card pin set/change, we will process these notification and update the
	// states on our end and send corresponding communications to the user.
	ProcessCardSwitchNonFinancialNotifications(context.Context, *ProcessCardSwitchNonFinancialNotificationsRequest) (*ProcessCardSwitchNonFinancialNotificationsResponse, error)
	// ProcessForexTransactionsRefund RPC will  consume messages from a queue subscribing to order-update-topic and based on the preconditions for forex txn
	// refunds and then initiate the workflow to process refunds
	ProcessForexTransactionsRefund(context.Context, *order.OrderUpdate) (*ProcessForexTransactionsRefundResponse, error)
	// ProcessCardTransactions RPC will consume events from order-update topic, it will consume card txn and ignore the protocol related txns.
	ProcessCardTransactions(context.Context, *order.OrderUpdate) (*ProcessCardTransactionsResponse, error)
}

// UnimplementedNotificationConsumerServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNotificationConsumerServiceServer struct {
}

func (UnimplementedNotificationConsumerServiceServer) ProcessCardSwitchFinancialNotifications(context.Context, *ProcessCardSwitchFinancialNotificationsRequest) (*ProcessCardSwitchFinancialNotificationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardSwitchFinancialNotifications not implemented")
}
func (UnimplementedNotificationConsumerServiceServer) ProcessCardSwitchNonFinancialNotifications(context.Context, *ProcessCardSwitchNonFinancialNotificationsRequest) (*ProcessCardSwitchNonFinancialNotificationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardSwitchNonFinancialNotifications not implemented")
}
func (UnimplementedNotificationConsumerServiceServer) ProcessForexTransactionsRefund(context.Context, *order.OrderUpdate) (*ProcessForexTransactionsRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessForexTransactionsRefund not implemented")
}
func (UnimplementedNotificationConsumerServiceServer) ProcessCardTransactions(context.Context, *order.OrderUpdate) (*ProcessCardTransactionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCardTransactions not implemented")
}

// UnsafeNotificationConsumerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NotificationConsumerServiceServer will
// result in compilation errors.
type UnsafeNotificationConsumerServiceServer interface {
	mustEmbedUnimplementedNotificationConsumerServiceServer()
}

func RegisterNotificationConsumerServiceServer(s grpc.ServiceRegistrar, srv NotificationConsumerServiceServer) {
	s.RegisterService(&NotificationConsumerService_ServiceDesc, srv)
}

func _NotificationConsumerService_ProcessCardSwitchFinancialNotifications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCardSwitchFinancialNotificationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationConsumerServiceServer).ProcessCardSwitchFinancialNotifications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationConsumerService_ProcessCardSwitchFinancialNotifications_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationConsumerServiceServer).ProcessCardSwitchFinancialNotifications(ctx, req.(*ProcessCardSwitchFinancialNotificationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationConsumerService_ProcessCardSwitchNonFinancialNotifications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCardSwitchNonFinancialNotificationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationConsumerServiceServer).ProcessCardSwitchNonFinancialNotifications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationConsumerService_ProcessCardSwitchNonFinancialNotifications_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationConsumerServiceServer).ProcessCardSwitchNonFinancialNotifications(ctx, req.(*ProcessCardSwitchNonFinancialNotificationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationConsumerService_ProcessForexTransactionsRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(order.OrderUpdate)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationConsumerServiceServer).ProcessForexTransactionsRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationConsumerService_ProcessForexTransactionsRefund_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationConsumerServiceServer).ProcessForexTransactionsRefund(ctx, req.(*order.OrderUpdate))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationConsumerService_ProcessCardTransactions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(order.OrderUpdate)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationConsumerServiceServer).ProcessCardTransactions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationConsumerService_ProcessCardTransactions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationConsumerServiceServer).ProcessCardTransactions(ctx, req.(*order.OrderUpdate))
	}
	return interceptor(ctx, in, info, handler)
}

// NotificationConsumerService_ServiceDesc is the grpc.ServiceDesc for NotificationConsumerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NotificationConsumerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "card.notification.NotificationConsumerService",
	HandlerType: (*NotificationConsumerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessCardSwitchFinancialNotifications",
			Handler:    _NotificationConsumerService_ProcessCardSwitchFinancialNotifications_Handler,
		},
		{
			MethodName: "ProcessCardSwitchNonFinancialNotifications",
			Handler:    _NotificationConsumerService_ProcessCardSwitchNonFinancialNotifications_Handler,
		},
		{
			MethodName: "ProcessForexTransactionsRefund",
			Handler:    _NotificationConsumerService_ProcessForexTransactionsRefund_Handler,
		},
		{
			MethodName: "ProcessCardTransactions",
			Handler:    _NotificationConsumerService_ProcessCardTransactions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/card/notification/service.proto",
}
