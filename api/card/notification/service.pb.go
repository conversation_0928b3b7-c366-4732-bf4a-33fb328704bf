// protolint:disable MAX_LINE_LENGTH
//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/notification/service.proto

package notification

import (
	queue "github.com/epifi/be-common/api/queue"
	enums "github.com/epifi/gamma/api/card/enums"
	order "github.com/epifi/gamma/api/order"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessCardTransactionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardTransactionsResponse) Reset() {
	*x = ProcessCardTransactionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_notification_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardTransactionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardTransactionsResponse) ProtoMessage() {}

func (x *ProcessCardTransactionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_notification_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardTransactionsResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardTransactionsResponse) Descriptor() ([]byte, []int) {
	return file_api_card_notification_service_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessCardTransactionsResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessForexTransactionsRefundResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessForexTransactionsRefundResponse) Reset() {
	*x = ProcessForexTransactionsRefundResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_notification_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessForexTransactionsRefundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessForexTransactionsRefundResponse) ProtoMessage() {}

func (x *ProcessForexTransactionsRefundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_notification_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessForexTransactionsRefundResponse.ProtoReflect.Descriptor instead.
func (*ProcessForexTransactionsRefundResponse) Descriptor() ([]byte, []int) {
	return file_api_card_notification_service_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessForexTransactionsRefundResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardSwitchFinancialNotificationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Mode of Transaction :  ECOMM, POS, ATM or NFC
	CardTransactionCategory enums.CardTransactionCategory `protobuf:"varint,2,opt,name=card_transaction_category,json=cardTransactionCategory,proto3,enum=card.enums.CardTransactionCategory" json:"card_transaction_category,omitempty"`
	// unique reference id
	RequestId string `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// timestamp at which financial event got executed
	ExecutedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=executed_at,json=executedAt,proto3" json:"executed_at,omitempty"`
	// account number of the user
	AccountNumber string `protobuf:"bytes,5,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// transaction amount
	Amount *money.Money `protobuf:"bytes,6,opt,name=amount,proto3" json:"amount,omitempty"`
	// type of notification
	NotificationType enums.NotificationType `protobuf:"varint,7,opt,name=notification_type,json=notificationType,proto3,enum=card.enums.NotificationType" json:"notification_type,omitempty"`
	// Type of message such as reversal/advice/log only
	MessageType enums.MessageType `protobuf:"varint,8,opt,name=message_type,json=messageType,proto3,enum=card.enums.MessageType" json:"message_type,omitempty"`
	// unique identifier for a transaction
	Arn string `protobuf:"bytes,9,opt,name=arn,proto3" json:"arn,omitempty"`
	// in case of reversal original transaction details will be present. If not available then it will be blank. For
	// refunds the same will not be available
	OriginalTransactionId string `protobuf:"bytes,10,opt,name=original_transaction_id,json=originalTransactionId,proto3" json:"original_transaction_id,omitempty"`
	// unique identifier for a card
	VendorCardId string `protobuf:"bytes,11,opt,name=vendor_card_id,json=vendorCardId,proto3" json:"vendor_card_id,omitempty"`
	// country code of the merchant
	CountryCode string `protobuf:"bytes,12,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// remarks of the transaction
	TransactionRemarks string `protobuf:"bytes,13,opt,name=transaction_remarks,json=transactionRemarks,proto3" json:"transaction_remarks,omitempty"`
	// merchant name for the transaction
	MerchantName string `protobuf:"bytes,14,opt,name=merchant_name,json=merchantName,proto3" json:"merchant_name,omitempty"`
	// merchant id
	MerchantId     string `protobuf:"bytes,15,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	PaymentGateway string `protobuf:"bytes,16,opt,name=payment_gateway,json=paymentGateway,proto3" json:"payment_gateway,omitempty"`
	SubMerchantId  string `protobuf:"bytes,17,opt,name=sub_merchant_id,json=subMerchantId,proto3" json:"sub_merchant_id,omitempty"`
	TerminalId     string `protobuf:"bytes,18,opt,name=terminal_id,json=terminalId,proto3" json:"terminal_id,omitempty"`
	// acquiring bank for the transactions
	AcquiringBank string `protobuf:"bytes,19,opt,name=acquiring_bank,json=acquiringBank,proto3" json:"acquiring_bank,omitempty"`
	// mcc of the merchant
	Mcc string `protobuf:"bytes,20,opt,name=mcc,proto3" json:"mcc,omitempty"`
	// merchant location
	MerchantLocation string `protobuf:"bytes,21,opt,name=merchant_location,json=merchantLocation,proto3" json:"merchant_location,omitempty"`
	// TODO(priyansh) : Check with Prateek/Federal regarding what this field denotes
	AuthorisationSwitch enums.AuthorizationSwitch `protobuf:"varint,22,opt,name=authorisation_switch,json=authorisationSwitch,proto3,enum=card.enums.AuthorizationSwitch" json:"authorisation_switch,omitempty"`
	// response code for txn
	TransactionResponseCode string `protobuf:"bytes,23,opt,name=transaction_response_code,json=transactionResponseCode,proto3" json:"transaction_response_code,omitempty"`
	// entry mode for the transaction
	TransactionEntryMode enums.TransactionEntryMode `protobuf:"varint,24,opt,name=transaction_entry_mode,json=transactionEntryMode,proto3,enum=card.enums.TransactionEntryMode" json:"transaction_entry_mode,omitempty"`
	AuthId               string                     `protobuf:"bytes,25,opt,name=auth_id,json=authId,proto3" json:"auth_id,omitempty"`
	// transaction state to determine if the transaction is successful or failed
	TransactionState enums.TransactionState `protobuf:"varint,26,opt,name=transaction_state,json=transactionState,proto3,enum=card.enums.TransactionState" json:"transaction_state,omitempty"`
	// transaction response description
	SwitchNotificationResponse enums.SwitchNotificationResponse `protobuf:"varint,27,opt,name=switch_notification_response,json=switchNotificationResponse,proto3,enum=card.enums.SwitchNotificationResponse" json:"switch_notification_response,omitempty"`
	RemitterCode               string                           `protobuf:"bytes,28,opt,name=remitter_code,json=remitterCode,proto3" json:"remitter_code,omitempty"`
	RemitterInstrumentType     string                           `protobuf:"bytes,29,opt,name=remitter_instrument_type,json=remitterInstrumentType,proto3" json:"remitter_instrument_type,omitempty"`
	// dump complete notification processing request as a string
	RawNotificationData string `protobuf:"bytes,30,opt,name=raw_notification_data,json=rawNotificationData,proto3" json:"raw_notification_data,omitempty"`
	// whether forex markup was charged for the transaction
	IsForexMarkupTransaction bool                   `protobuf:"varint,31,opt,name=is_forex_markup_transaction,json=isForexMarkupTransaction,proto3" json:"is_forex_markup_transaction,omitempty"`
	TransactionTime          *timestamppb.Timestamp `protobuf:"bytes,32,opt,name=transaction_time,json=transactionTime,proto3" json:"transaction_time,omitempty"`
	// whether DCC(Direct currency conversion) charges were applied for the transaction
	IsDccTransaction bool `protobuf:"varint,33,opt,name=is_dcc_transaction,json=isDccTransaction,proto3" json:"is_dcc_transaction,omitempty"`
	// transaction amount in the original currency in which the transaction happened.
	TxnAmountInOrgCurrency *money.Money `protobuf:"bytes,34,opt,name=txn_amount_in_org_currency,json=txnAmountInOrgCurrency,proto3" json:"txn_amount_in_org_currency,omitempty"`
	// Whether it's a card tap transaction or nor.
	IsCardTap bool `protobuf:"varint,35,opt,name=is_card_tap,json=isCardTap,proto3" json:"is_card_tap,omitempty"`
	// Whether it's a device tap transaction or nor.
	IsDeviceTap bool `protobuf:"varint,36,opt,name=is_device_tap,json=isDeviceTap,proto3" json:"is_device_tap,omitempty"`
	// will be populated only for ATM transactions.
	AtmPincode string `protobuf:"bytes,37,opt,name=atm_pincode,json=atmPincode,proto3" json:"atm_pincode,omitempty"`
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) Reset() {
	*x = ProcessCardSwitchFinancialNotificationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_notification_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardSwitchFinancialNotificationsRequest) ProtoMessage() {}

func (x *ProcessCardSwitchFinancialNotificationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_notification_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardSwitchFinancialNotificationsRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardSwitchFinancialNotificationsRequest) Descriptor() ([]byte, []int) {
	return file_api_card_notification_service_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetCardTransactionCategory() enums.CardTransactionCategory {
	if x != nil {
		return x.CardTransactionCategory
	}
	return enums.CardTransactionCategory(0)
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetExecutedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExecutedAt
	}
	return nil
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetNotificationType() enums.NotificationType {
	if x != nil {
		return x.NotificationType
	}
	return enums.NotificationType(0)
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetMessageType() enums.MessageType {
	if x != nil {
		return x.MessageType
	}
	return enums.MessageType(0)
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetArn() string {
	if x != nil {
		return x.Arn
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetOriginalTransactionId() string {
	if x != nil {
		return x.OriginalTransactionId
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetVendorCardId() string {
	if x != nil {
		return x.VendorCardId
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetTransactionRemarks() string {
	if x != nil {
		return x.TransactionRemarks
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetMerchantName() string {
	if x != nil {
		return x.MerchantName
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetPaymentGateway() string {
	if x != nil {
		return x.PaymentGateway
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetSubMerchantId() string {
	if x != nil {
		return x.SubMerchantId
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetTerminalId() string {
	if x != nil {
		return x.TerminalId
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetAcquiringBank() string {
	if x != nil {
		return x.AcquiringBank
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetMcc() string {
	if x != nil {
		return x.Mcc
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetMerchantLocation() string {
	if x != nil {
		return x.MerchantLocation
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetAuthorisationSwitch() enums.AuthorizationSwitch {
	if x != nil {
		return x.AuthorisationSwitch
	}
	return enums.AuthorizationSwitch(0)
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetTransactionResponseCode() string {
	if x != nil {
		return x.TransactionResponseCode
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetTransactionEntryMode() enums.TransactionEntryMode {
	if x != nil {
		return x.TransactionEntryMode
	}
	return enums.TransactionEntryMode(0)
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetAuthId() string {
	if x != nil {
		return x.AuthId
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetTransactionState() enums.TransactionState {
	if x != nil {
		return x.TransactionState
	}
	return enums.TransactionState(0)
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetSwitchNotificationResponse() enums.SwitchNotificationResponse {
	if x != nil {
		return x.SwitchNotificationResponse
	}
	return enums.SwitchNotificationResponse(0)
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetRemitterCode() string {
	if x != nil {
		return x.RemitterCode
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetRemitterInstrumentType() string {
	if x != nil {
		return x.RemitterInstrumentType
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetRawNotificationData() string {
	if x != nil {
		return x.RawNotificationData
	}
	return ""
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetIsForexMarkupTransaction() bool {
	if x != nil {
		return x.IsForexMarkupTransaction
	}
	return false
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetTransactionTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionTime
	}
	return nil
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetIsDccTransaction() bool {
	if x != nil {
		return x.IsDccTransaction
	}
	return false
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetTxnAmountInOrgCurrency() *money.Money {
	if x != nil {
		return x.TxnAmountInOrgCurrency
	}
	return nil
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetIsCardTap() bool {
	if x != nil {
		return x.IsCardTap
	}
	return false
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetIsDeviceTap() bool {
	if x != nil {
		return x.IsDeviceTap
	}
	return false
}

func (x *ProcessCardSwitchFinancialNotificationsRequest) GetAtmPincode() string {
	if x != nil {
		return x.AtmPincode
	}
	return ""
}

type ProcessCardSwitchFinancialNotificationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardSwitchFinancialNotificationsResponse) Reset() {
	*x = ProcessCardSwitchFinancialNotificationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_notification_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardSwitchFinancialNotificationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardSwitchFinancialNotificationsResponse) ProtoMessage() {}

func (x *ProcessCardSwitchFinancialNotificationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_notification_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardSwitchFinancialNotificationsResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardSwitchFinancialNotificationsResponse) Descriptor() ([]byte, []int) {
	return file_api_card_notification_service_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessCardSwitchFinancialNotificationsResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardSwitchNonFinancialNotificationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// timestamp at which event got completed
	CompletedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	// type of event
	NotificationType enums.NotificationType `protobuf:"varint,3,opt,name=notification_type,json=notificationType,proto3,enum=card.enums.NotificationType" json:"notification_type,omitempty"`
	// vendor card unique id
	VendorCardId string `protobuf:"bytes,4,opt,name=vendor_card_id,json=vendorCardId,proto3" json:"vendor_card_id,omitempty"`
	// request id
	RequestId string `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// dump complete notification processing request as a string
	RawNotificationData string `protobuf:"bytes,6,opt,name=raw_notification_data,json=rawNotificationData,proto3" json:"raw_notification_data,omitempty"`
}

func (x *ProcessCardSwitchNonFinancialNotificationsRequest) Reset() {
	*x = ProcessCardSwitchNonFinancialNotificationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_notification_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardSwitchNonFinancialNotificationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardSwitchNonFinancialNotificationsRequest) ProtoMessage() {}

func (x *ProcessCardSwitchNonFinancialNotificationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_notification_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardSwitchNonFinancialNotificationsRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardSwitchNonFinancialNotificationsRequest) Descriptor() ([]byte, []int) {
	return file_api_card_notification_service_proto_rawDescGZIP(), []int{4}
}

func (x *ProcessCardSwitchNonFinancialNotificationsRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardSwitchNonFinancialNotificationsRequest) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *ProcessCardSwitchNonFinancialNotificationsRequest) GetNotificationType() enums.NotificationType {
	if x != nil {
		return x.NotificationType
	}
	return enums.NotificationType(0)
}

func (x *ProcessCardSwitchNonFinancialNotificationsRequest) GetVendorCardId() string {
	if x != nil {
		return x.VendorCardId
	}
	return ""
}

func (x *ProcessCardSwitchNonFinancialNotificationsRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ProcessCardSwitchNonFinancialNotificationsRequest) GetRawNotificationData() string {
	if x != nil {
		return x.RawNotificationData
	}
	return ""
}

type ProcessCardSwitchNonFinancialNotificationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardSwitchNonFinancialNotificationsResponse) Reset() {
	*x = ProcessCardSwitchNonFinancialNotificationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_notification_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardSwitchNonFinancialNotificationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardSwitchNonFinancialNotificationsResponse) ProtoMessage() {}

func (x *ProcessCardSwitchNonFinancialNotificationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_notification_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardSwitchNonFinancialNotificationsResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardSwitchNonFinancialNotificationsResponse) Descriptor() ([]byte, []int) {
	return file_api_card_notification_service_proto_rawDescGZIP(), []int{5}
}

func (x *ProcessCardSwitchNonFinancialNotificationsResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_card_notification_service_proto protoreflect.FileDescriptor

var file_api_card_notification_service_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61,
	0x72, 0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x61, 0x70, 0x69,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x69,
	0x0a, 0x1f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x70, 0x0a, 0x26, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xa0, 0x0f, 0x0a, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43,
	0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x5f, 0x0a, 0x19, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x17, 0x63, 0x61, 0x72,
	0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x11, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a,
	0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x72,
	0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x72, 0x6e, 0x12, 0x36, 0x0a, 0x17,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2f, 0x0a,
	0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x23,
	0x0a, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x26, 0x0a,
	0x0f, 0x73, 0x75, 0x62, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x4d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x63, 0x63, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x63, 0x63, 0x12,
	0x2b, 0x0a, 0x11, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x52, 0x0a, 0x14,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x13, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x12, 0x3a, 0x0a, 0x19, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x17, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x56, 0x0a, 0x16,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x14,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x69, 0x64, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x75, 0x74, 0x68, 0x49, 0x64, 0x12, 0x49, 0x0a,
	0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x68, 0x0a, 0x1c, 0x73, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x1a, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6d, 0x69, 0x74,
	0x74, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x38, 0x0a, 0x18, 0x72, 0x65, 0x6d, 0x69, 0x74,
	0x74, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x72, 0x65, 0x6d, 0x69, 0x74,
	0x74, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x32, 0x0a, 0x15, 0x72, 0x61, 0x77, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x72, 0x61, 0x77, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x1b, 0x69, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x65,
	0x78, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x75, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x18, 0x69, 0x73, 0x46, 0x6f,
	0x72, 0x65, 0x78, 0x4d, 0x61, 0x72, 0x6b, 0x75, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x69,
	0x73, 0x5f, 0x64, 0x63, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x21, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x44, 0x63, 0x63, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x1a, 0x74, 0x78, 0x6e,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x72, 0x67, 0x5f, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x16, 0x74, 0x78, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x4f, 0x72,
	0x67, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x73, 0x5f,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x61, 0x70, 0x18, 0x23, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x69, 0x73, 0x43, 0x61, 0x72, 0x64, 0x54, 0x61, 0x70, 0x12, 0x22, 0x0a, 0x0d, 0x69, 0x73, 0x5f,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x61, 0x70, 0x18, 0x24, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0b, 0x69, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x61, 0x70, 0x12, 0x1f, 0x0a,
	0x0b, 0x61, 0x74, 0x6d, 0x5f, 0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x25, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x74, 0x6d, 0x50, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x79,
	0x0a, 0x2f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xfb, 0x02, 0x0a, 0x31, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4e,
	0x6f, 0x6e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e,
	0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x49, 0x0a, 0x11, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24,
	0x0a, 0x0e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x43, 0x61,
	0x72, 0x64, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x72, 0x61, 0x77, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x72, 0x61, 0x77, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x22, 0x7c, 0x0a, 0x32, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x6e, 0x46,
	0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a,
	0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0xe4, 0x04, 0x0a, 0x1b, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xb2, 0x01, 0x0a, 0x27, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x43, 0x61, 0x72, 0x64, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x46, 0x69, 0x6e, 0x61, 0x6e,
	0x63, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x41, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72,
	0x64, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x42, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x43, 0x61, 0x72, 0x64, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63,
	0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xbb, 0x01, 0x0a, 0x2a, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x4e, 0x6f, 0x6e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x44, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4e,
	0x6f, 0x6e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x45, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x6e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61,
	0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6f, 0x0a, 0x1e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x12, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x1a, 0x39,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x61, 0x0a, 0x17, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x12, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x1a, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x5c, 0x0a, 0x2c,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x2c, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_card_notification_service_proto_rawDescOnce sync.Once
	file_api_card_notification_service_proto_rawDescData = file_api_card_notification_service_proto_rawDesc
)

func file_api_card_notification_service_proto_rawDescGZIP() []byte {
	file_api_card_notification_service_proto_rawDescOnce.Do(func() {
		file_api_card_notification_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_notification_service_proto_rawDescData)
	})
	return file_api_card_notification_service_proto_rawDescData
}

var file_api_card_notification_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_card_notification_service_proto_goTypes = []interface{}{
	(*ProcessCardTransactionsResponse)(nil),                    // 0: card.notification.ProcessCardTransactionsResponse
	(*ProcessForexTransactionsRefundResponse)(nil),             // 1: card.notification.ProcessForexTransactionsRefundResponse
	(*ProcessCardSwitchFinancialNotificationsRequest)(nil),     // 2: card.notification.ProcessCardSwitchFinancialNotificationsRequest
	(*ProcessCardSwitchFinancialNotificationsResponse)(nil),    // 3: card.notification.ProcessCardSwitchFinancialNotificationsResponse
	(*ProcessCardSwitchNonFinancialNotificationsRequest)(nil),  // 4: card.notification.ProcessCardSwitchNonFinancialNotificationsRequest
	(*ProcessCardSwitchNonFinancialNotificationsResponse)(nil), // 5: card.notification.ProcessCardSwitchNonFinancialNotificationsResponse
	(*queue.ConsumerResponseHeader)(nil),                       // 6: queue.ConsumerResponseHeader
	(*queue.ConsumerRequestHeader)(nil),                        // 7: queue.ConsumerRequestHeader
	(enums.CardTransactionCategory)(0),                         // 8: card.enums.CardTransactionCategory
	(*timestamppb.Timestamp)(nil),                              // 9: google.protobuf.Timestamp
	(*money.Money)(nil),                                        // 10: google.type.Money
	(enums.NotificationType)(0),                                // 11: card.enums.NotificationType
	(enums.MessageType)(0),                                     // 12: card.enums.MessageType
	(enums.AuthorizationSwitch)(0),                             // 13: card.enums.AuthorizationSwitch
	(enums.TransactionEntryMode)(0),                            // 14: card.enums.TransactionEntryMode
	(enums.TransactionState)(0),                                // 15: card.enums.TransactionState
	(enums.SwitchNotificationResponse)(0),                      // 16: card.enums.SwitchNotificationResponse
	(*order.OrderUpdate)(nil),                                  // 17: order.OrderUpdate
}
var file_api_card_notification_service_proto_depIdxs = []int32{
	6,  // 0: card.notification.ProcessCardTransactionsResponse.response_header:type_name -> queue.ConsumerResponseHeader
	6,  // 1: card.notification.ProcessForexTransactionsRefundResponse.response_header:type_name -> queue.ConsumerResponseHeader
	7,  // 2: card.notification.ProcessCardSwitchFinancialNotificationsRequest.request_header:type_name -> queue.ConsumerRequestHeader
	8,  // 3: card.notification.ProcessCardSwitchFinancialNotificationsRequest.card_transaction_category:type_name -> card.enums.CardTransactionCategory
	9,  // 4: card.notification.ProcessCardSwitchFinancialNotificationsRequest.executed_at:type_name -> google.protobuf.Timestamp
	10, // 5: card.notification.ProcessCardSwitchFinancialNotificationsRequest.amount:type_name -> google.type.Money
	11, // 6: card.notification.ProcessCardSwitchFinancialNotificationsRequest.notification_type:type_name -> card.enums.NotificationType
	12, // 7: card.notification.ProcessCardSwitchFinancialNotificationsRequest.message_type:type_name -> card.enums.MessageType
	13, // 8: card.notification.ProcessCardSwitchFinancialNotificationsRequest.authorisation_switch:type_name -> card.enums.AuthorizationSwitch
	14, // 9: card.notification.ProcessCardSwitchFinancialNotificationsRequest.transaction_entry_mode:type_name -> card.enums.TransactionEntryMode
	15, // 10: card.notification.ProcessCardSwitchFinancialNotificationsRequest.transaction_state:type_name -> card.enums.TransactionState
	16, // 11: card.notification.ProcessCardSwitchFinancialNotificationsRequest.switch_notification_response:type_name -> card.enums.SwitchNotificationResponse
	9,  // 12: card.notification.ProcessCardSwitchFinancialNotificationsRequest.transaction_time:type_name -> google.protobuf.Timestamp
	10, // 13: card.notification.ProcessCardSwitchFinancialNotificationsRequest.txn_amount_in_org_currency:type_name -> google.type.Money
	6,  // 14: card.notification.ProcessCardSwitchFinancialNotificationsResponse.response_header:type_name -> queue.ConsumerResponseHeader
	7,  // 15: card.notification.ProcessCardSwitchNonFinancialNotificationsRequest.request_header:type_name -> queue.ConsumerRequestHeader
	9,  // 16: card.notification.ProcessCardSwitchNonFinancialNotificationsRequest.completed_at:type_name -> google.protobuf.Timestamp
	11, // 17: card.notification.ProcessCardSwitchNonFinancialNotificationsRequest.notification_type:type_name -> card.enums.NotificationType
	6,  // 18: card.notification.ProcessCardSwitchNonFinancialNotificationsResponse.response_header:type_name -> queue.ConsumerResponseHeader
	2,  // 19: card.notification.NotificationConsumerService.ProcessCardSwitchFinancialNotifications:input_type -> card.notification.ProcessCardSwitchFinancialNotificationsRequest
	4,  // 20: card.notification.NotificationConsumerService.ProcessCardSwitchNonFinancialNotifications:input_type -> card.notification.ProcessCardSwitchNonFinancialNotificationsRequest
	17, // 21: card.notification.NotificationConsumerService.ProcessForexTransactionsRefund:input_type -> order.OrderUpdate
	17, // 22: card.notification.NotificationConsumerService.ProcessCardTransactions:input_type -> order.OrderUpdate
	3,  // 23: card.notification.NotificationConsumerService.ProcessCardSwitchFinancialNotifications:output_type -> card.notification.ProcessCardSwitchFinancialNotificationsResponse
	5,  // 24: card.notification.NotificationConsumerService.ProcessCardSwitchNonFinancialNotifications:output_type -> card.notification.ProcessCardSwitchNonFinancialNotificationsResponse
	1,  // 25: card.notification.NotificationConsumerService.ProcessForexTransactionsRefund:output_type -> card.notification.ProcessForexTransactionsRefundResponse
	0,  // 26: card.notification.NotificationConsumerService.ProcessCardTransactions:output_type -> card.notification.ProcessCardTransactionsResponse
	23, // [23:27] is the sub-list for method output_type
	19, // [19:23] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_api_card_notification_service_proto_init() }
func file_api_card_notification_service_proto_init() {
	if File_api_card_notification_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_notification_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardTransactionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_notification_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessForexTransactionsRefundResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_notification_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardSwitchFinancialNotificationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_notification_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardSwitchFinancialNotificationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_notification_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardSwitchNonFinancialNotificationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_notification_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardSwitchNonFinancialNotificationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_notification_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_card_notification_service_proto_goTypes,
		DependencyIndexes: file_api_card_notification_service_proto_depIdxs,
		MessageInfos:      file_api_card_notification_service_proto_msgTypes,
	}.Build()
	File_api_card_notification_service_proto = out.File
	file_api_card_notification_service_proto_rawDesc = nil
	file_api_card_notification_service_proto_goTypes = nil
	file_api_card_notification_service_proto_depIdxs = nil
}
