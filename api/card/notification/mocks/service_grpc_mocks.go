// Code generated by MockGen. DO NOT EDIT.
// Source: api/card/notification/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	notification "github.com/epifi/gamma/api/card/notification"
	order "github.com/epifi/gamma/api/order"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockNotificationConsumerServiceClient is a mock of NotificationConsumerServiceClient interface.
type MockNotificationConsumerServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockNotificationConsumerServiceClientMockRecorder
}

// MockNotificationConsumerServiceClientMockRecorder is the mock recorder for MockNotificationConsumerServiceClient.
type MockNotificationConsumerServiceClientMockRecorder struct {
	mock *MockNotificationConsumerServiceClient
}

// NewMockNotificationConsumerServiceClient creates a new mock instance.
func NewMockNotificationConsumerServiceClient(ctrl *gomock.Controller) *MockNotificationConsumerServiceClient {
	mock := &MockNotificationConsumerServiceClient{ctrl: ctrl}
	mock.recorder = &MockNotificationConsumerServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNotificationConsumerServiceClient) EXPECT() *MockNotificationConsumerServiceClientMockRecorder {
	return m.recorder
}

// ProcessCardSwitchFinancialNotifications mocks base method.
func (m *MockNotificationConsumerServiceClient) ProcessCardSwitchFinancialNotifications(ctx context.Context, in *notification.ProcessCardSwitchFinancialNotificationsRequest, opts ...grpc.CallOption) (*notification.ProcessCardSwitchFinancialNotificationsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardSwitchFinancialNotifications", varargs...)
	ret0, _ := ret[0].(*notification.ProcessCardSwitchFinancialNotificationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardSwitchFinancialNotifications indicates an expected call of ProcessCardSwitchFinancialNotifications.
func (mr *MockNotificationConsumerServiceClientMockRecorder) ProcessCardSwitchFinancialNotifications(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardSwitchFinancialNotifications", reflect.TypeOf((*MockNotificationConsumerServiceClient)(nil).ProcessCardSwitchFinancialNotifications), varargs...)
}

// ProcessCardSwitchNonFinancialNotifications mocks base method.
func (m *MockNotificationConsumerServiceClient) ProcessCardSwitchNonFinancialNotifications(ctx context.Context, in *notification.ProcessCardSwitchNonFinancialNotificationsRequest, opts ...grpc.CallOption) (*notification.ProcessCardSwitchNonFinancialNotificationsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardSwitchNonFinancialNotifications", varargs...)
	ret0, _ := ret[0].(*notification.ProcessCardSwitchNonFinancialNotificationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardSwitchNonFinancialNotifications indicates an expected call of ProcessCardSwitchNonFinancialNotifications.
func (mr *MockNotificationConsumerServiceClientMockRecorder) ProcessCardSwitchNonFinancialNotifications(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardSwitchNonFinancialNotifications", reflect.TypeOf((*MockNotificationConsumerServiceClient)(nil).ProcessCardSwitchNonFinancialNotifications), varargs...)
}

// ProcessCardTransactions mocks base method.
func (m *MockNotificationConsumerServiceClient) ProcessCardTransactions(ctx context.Context, in *order.OrderUpdate, opts ...grpc.CallOption) (*notification.ProcessCardTransactionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCardTransactions", varargs...)
	ret0, _ := ret[0].(*notification.ProcessCardTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardTransactions indicates an expected call of ProcessCardTransactions.
func (mr *MockNotificationConsumerServiceClientMockRecorder) ProcessCardTransactions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardTransactions", reflect.TypeOf((*MockNotificationConsumerServiceClient)(nil).ProcessCardTransactions), varargs...)
}

// ProcessForexTransactionsRefund mocks base method.
func (m *MockNotificationConsumerServiceClient) ProcessForexTransactionsRefund(ctx context.Context, in *order.OrderUpdate, opts ...grpc.CallOption) (*notification.ProcessForexTransactionsRefundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessForexTransactionsRefund", varargs...)
	ret0, _ := ret[0].(*notification.ProcessForexTransactionsRefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessForexTransactionsRefund indicates an expected call of ProcessForexTransactionsRefund.
func (mr *MockNotificationConsumerServiceClientMockRecorder) ProcessForexTransactionsRefund(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessForexTransactionsRefund", reflect.TypeOf((*MockNotificationConsumerServiceClient)(nil).ProcessForexTransactionsRefund), varargs...)
}

// MockNotificationConsumerServiceServer is a mock of NotificationConsumerServiceServer interface.
type MockNotificationConsumerServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockNotificationConsumerServiceServerMockRecorder
}

// MockNotificationConsumerServiceServerMockRecorder is the mock recorder for MockNotificationConsumerServiceServer.
type MockNotificationConsumerServiceServerMockRecorder struct {
	mock *MockNotificationConsumerServiceServer
}

// NewMockNotificationConsumerServiceServer creates a new mock instance.
func NewMockNotificationConsumerServiceServer(ctrl *gomock.Controller) *MockNotificationConsumerServiceServer {
	mock := &MockNotificationConsumerServiceServer{ctrl: ctrl}
	mock.recorder = &MockNotificationConsumerServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNotificationConsumerServiceServer) EXPECT() *MockNotificationConsumerServiceServerMockRecorder {
	return m.recorder
}

// ProcessCardSwitchFinancialNotifications mocks base method.
func (m *MockNotificationConsumerServiceServer) ProcessCardSwitchFinancialNotifications(arg0 context.Context, arg1 *notification.ProcessCardSwitchFinancialNotificationsRequest) (*notification.ProcessCardSwitchFinancialNotificationsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardSwitchFinancialNotifications", arg0, arg1)
	ret0, _ := ret[0].(*notification.ProcessCardSwitchFinancialNotificationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardSwitchFinancialNotifications indicates an expected call of ProcessCardSwitchFinancialNotifications.
func (mr *MockNotificationConsumerServiceServerMockRecorder) ProcessCardSwitchFinancialNotifications(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardSwitchFinancialNotifications", reflect.TypeOf((*MockNotificationConsumerServiceServer)(nil).ProcessCardSwitchFinancialNotifications), arg0, arg1)
}

// ProcessCardSwitchNonFinancialNotifications mocks base method.
func (m *MockNotificationConsumerServiceServer) ProcessCardSwitchNonFinancialNotifications(arg0 context.Context, arg1 *notification.ProcessCardSwitchNonFinancialNotificationsRequest) (*notification.ProcessCardSwitchNonFinancialNotificationsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardSwitchNonFinancialNotifications", arg0, arg1)
	ret0, _ := ret[0].(*notification.ProcessCardSwitchNonFinancialNotificationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardSwitchNonFinancialNotifications indicates an expected call of ProcessCardSwitchNonFinancialNotifications.
func (mr *MockNotificationConsumerServiceServerMockRecorder) ProcessCardSwitchNonFinancialNotifications(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardSwitchNonFinancialNotifications", reflect.TypeOf((*MockNotificationConsumerServiceServer)(nil).ProcessCardSwitchNonFinancialNotifications), arg0, arg1)
}

// ProcessCardTransactions mocks base method.
func (m *MockNotificationConsumerServiceServer) ProcessCardTransactions(arg0 context.Context, arg1 *order.OrderUpdate) (*notification.ProcessCardTransactionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCardTransactions", arg0, arg1)
	ret0, _ := ret[0].(*notification.ProcessCardTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCardTransactions indicates an expected call of ProcessCardTransactions.
func (mr *MockNotificationConsumerServiceServerMockRecorder) ProcessCardTransactions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCardTransactions", reflect.TypeOf((*MockNotificationConsumerServiceServer)(nil).ProcessCardTransactions), arg0, arg1)
}

// ProcessForexTransactionsRefund mocks base method.
func (m *MockNotificationConsumerServiceServer) ProcessForexTransactionsRefund(arg0 context.Context, arg1 *order.OrderUpdate) (*notification.ProcessForexTransactionsRefundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessForexTransactionsRefund", arg0, arg1)
	ret0, _ := ret[0].(*notification.ProcessForexTransactionsRefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessForexTransactionsRefund indicates an expected call of ProcessForexTransactionsRefund.
func (mr *MockNotificationConsumerServiceServerMockRecorder) ProcessForexTransactionsRefund(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessForexTransactionsRefund", reflect.TypeOf((*MockNotificationConsumerServiceServer)(nil).ProcessForexTransactionsRefund), arg0, arg1)
}

// MockUnsafeNotificationConsumerServiceServer is a mock of UnsafeNotificationConsumerServiceServer interface.
type MockUnsafeNotificationConsumerServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeNotificationConsumerServiceServerMockRecorder
}

// MockUnsafeNotificationConsumerServiceServerMockRecorder is the mock recorder for MockUnsafeNotificationConsumerServiceServer.
type MockUnsafeNotificationConsumerServiceServerMockRecorder struct {
	mock *MockUnsafeNotificationConsumerServiceServer
}

// NewMockUnsafeNotificationConsumerServiceServer creates a new mock instance.
func NewMockUnsafeNotificationConsumerServiceServer(ctrl *gomock.Controller) *MockUnsafeNotificationConsumerServiceServer {
	mock := &MockUnsafeNotificationConsumerServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeNotificationConsumerServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeNotificationConsumerServiceServer) EXPECT() *MockUnsafeNotificationConsumerServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedNotificationConsumerServiceServer mocks base method.
func (m *MockUnsafeNotificationConsumerServiceServer) mustEmbedUnimplementedNotificationConsumerServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedNotificationConsumerServiceServer")
}

// mustEmbedUnimplementedNotificationConsumerServiceServer indicates an expected call of mustEmbedUnimplementedNotificationConsumerServiceServer.
func (mr *MockUnsafeNotificationConsumerServiceServerMockRecorder) mustEmbedUnimplementedNotificationConsumerServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedNotificationConsumerServiceServer", reflect.TypeOf((*MockUnsafeNotificationConsumerServiceServer)(nil).mustEmbedUnimplementedNotificationConsumerServiceServer))
}
