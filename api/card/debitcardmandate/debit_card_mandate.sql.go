package debitcardmandate

import (
	"database/sql/driver"
	"fmt"

	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/nulltypes"

	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing DedupeId while reading from DB
func (a *DebitCardMandateDedupeId) Scan(src interface{}) error {
	var (
		err error
	)

	if src == nil {
		return nil
	}
	val, ok := src.(string)
	if !ok {
		err = fmt.Errorf("expected []byte, got %T", src)
		return err
	}

	if val == "" {
		return nil
	}

	err = idgen.DecodeProtoFromStdBase64(val, a)
	if err != nil {
		return fmt.Errorf("error in unmarshal %w", err)
	}
	return nil
}

// Valuer interface implementation for storing the DedupeId in string format in DB
func (a *DebitCardMandateDedupeId) Value() (driver.Value, error) {
	if a == nil {
		return nulltypes.NewNullString(""), nil
	}

	base64EncodedString, err := a.getBase64EncodedString()
	if err != nil {
		return nil, fmt.Errorf("error in base64 conversion %w", err)
	}

	return nulltypes.NewNullString(base64EncodedString), nil
}

func (a *DebitCardMandateDedupeId) IsEqual(otherId *DebitCardMandateDedupeId) (bool, error) {
	selfBase64EncodedString, err := a.getBase64EncodedString()
	if err != nil {
		return false, fmt.Errorf("error in base64 conversion %w", err)
	}

	otherBase64EncodedString, err := otherId.getBase64EncodedString()
	if err != nil {
		return false, fmt.Errorf("error in base64 conversion %w", err)
	}

	return selfBase64EncodedString == otherBase64EncodedString, nil
}

// Marshaler interface implementation for DedupeId
func (a *DebitCardMandateDedupeId) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for DedupeId
func (a *DebitCardMandateDedupeId) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

func (a *DebitCardMandateDedupeId) getBase64EncodedString() (string, error) {
	return idgen.EncodeProtoToStdBase64(a)
}
