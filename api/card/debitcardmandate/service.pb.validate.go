// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/debitcardmandate/service.proto

package debitcardmandate

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/card/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.DebitCardMandateRegistrationProvenance(0)
)

// Validate checks the field values on GetDebitCardMandateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDebitCardMandateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDebitCardMandateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDebitCardMandateRequestMultiError, or nil if none found.
func (m *GetDebitCardMandateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDebitCardMandateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *GetDebitCardMandateRequest_DedupeId:
		if v == nil {
			err := GetDebitCardMandateRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDedupeId()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetDebitCardMandateRequestValidationError{
						field:  "DedupeId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetDebitCardMandateRequestValidationError{
						field:  "DedupeId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDedupeId()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetDebitCardMandateRequestValidationError{
					field:  "DedupeId",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetDebitCardMandateRequest_RecurringPaymentId:
		if v == nil {
			err := GetDebitCardMandateRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for RecurringPaymentId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetDebitCardMandateRequestMultiError(errors)
	}

	return nil
}

// GetDebitCardMandateRequestMultiError is an error wrapping multiple
// validation errors returned by GetDebitCardMandateRequest.ValidateAll() if
// the designated constraints aren't met.
type GetDebitCardMandateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDebitCardMandateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDebitCardMandateRequestMultiError) AllErrors() []error { return m }

// GetDebitCardMandateRequestValidationError is the validation error returned
// by GetDebitCardMandateRequest.Validate if the designated constraints aren't met.
type GetDebitCardMandateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDebitCardMandateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDebitCardMandateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDebitCardMandateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDebitCardMandateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDebitCardMandateRequestValidationError) ErrorName() string {
	return "GetDebitCardMandateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDebitCardMandateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDebitCardMandateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDebitCardMandateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDebitCardMandateRequestValidationError{}

// Validate checks the field values on GetDebitCardMandateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDebitCardMandateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDebitCardMandateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDebitCardMandateResponseMultiError, or nil if none found.
func (m *GetDebitCardMandateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDebitCardMandateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDebitCardMandateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDebitCardMandateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDebitCardMandateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDebitCardMandate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDebitCardMandateResponseValidationError{
					field:  "DebitCardMandate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDebitCardMandateResponseValidationError{
					field:  "DebitCardMandate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDebitCardMandate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDebitCardMandateResponseValidationError{
				field:  "DebitCardMandate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDebitCardMandateResponseMultiError(errors)
	}

	return nil
}

// GetDebitCardMandateResponseMultiError is an error wrapping multiple
// validation errors returned by GetDebitCardMandateResponse.ValidateAll() if
// the designated constraints aren't met.
type GetDebitCardMandateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDebitCardMandateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDebitCardMandateResponseMultiError) AllErrors() []error { return m }

// GetDebitCardMandateResponseValidationError is the validation error returned
// by GetDebitCardMandateResponse.Validate if the designated constraints
// aren't met.
type GetDebitCardMandateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDebitCardMandateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDebitCardMandateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDebitCardMandateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDebitCardMandateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDebitCardMandateResponseValidationError) ErrorName() string {
	return "GetDebitCardMandateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDebitCardMandateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDebitCardMandateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDebitCardMandateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDebitCardMandateResponseValidationError{}

// Validate checks the field values on CreateOffAppDebitCardMandateRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateOffAppDebitCardMandateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOffAppDebitCardMandateRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateOffAppDebitCardMandateRequestMultiError, or nil if none found.
func (m *CreateOffAppDebitCardMandateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOffAppDebitCardMandateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetCardId()) < 1 {
		err := CreateOffAppDebitCardMandateRequestValidationError{
			field:  "CardId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetMerchantId()) < 1 {
		err := CreateOffAppDebitCardMandateRequestValidationError{
			field:  "MerchantId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetRecurringPaymentId()) < 1 {
		err := CreateOffAppDebitCardMandateRequestValidationError{
			field:  "RecurringPaymentId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateOffAppDebitCardMandateRequest_Provenance_NotInLookup[m.GetProvenance()]; ok {
		err := CreateOffAppDebitCardMandateRequestValidationError{
			field:  "Provenance",
			reason: "value must not be in list [DEBIT_CARD_MANDATE_REGISTRATION_PROVENANCE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateOffAppDebitCardMandateRequestMultiError(errors)
	}

	return nil
}

// CreateOffAppDebitCardMandateRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateOffAppDebitCardMandateRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateOffAppDebitCardMandateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOffAppDebitCardMandateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOffAppDebitCardMandateRequestMultiError) AllErrors() []error { return m }

// CreateOffAppDebitCardMandateRequestValidationError is the validation error
// returned by CreateOffAppDebitCardMandateRequest.Validate if the designated
// constraints aren't met.
type CreateOffAppDebitCardMandateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOffAppDebitCardMandateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOffAppDebitCardMandateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOffAppDebitCardMandateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOffAppDebitCardMandateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOffAppDebitCardMandateRequestValidationError) ErrorName() string {
	return "CreateOffAppDebitCardMandateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOffAppDebitCardMandateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOffAppDebitCardMandateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOffAppDebitCardMandateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOffAppDebitCardMandateRequestValidationError{}

var _CreateOffAppDebitCardMandateRequest_Provenance_NotInLookup = map[enums.DebitCardMandateRegistrationProvenance]struct{}{
	0: {},
}

// Validate checks the field values on CreateOffAppDebitCardMandateResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateOffAppDebitCardMandateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOffAppDebitCardMandateResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateOffAppDebitCardMandateResponseMultiError, or nil if none found.
func (m *CreateOffAppDebitCardMandateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOffAppDebitCardMandateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOffAppDebitCardMandateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOffAppDebitCardMandateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOffAppDebitCardMandateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDebitCardMandate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOffAppDebitCardMandateResponseValidationError{
					field:  "DebitCardMandate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOffAppDebitCardMandateResponseValidationError{
					field:  "DebitCardMandate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDebitCardMandate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOffAppDebitCardMandateResponseValidationError{
				field:  "DebitCardMandate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateOffAppDebitCardMandateResponseMultiError(errors)
	}

	return nil
}

// CreateOffAppDebitCardMandateResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateOffAppDebitCardMandateResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateOffAppDebitCardMandateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOffAppDebitCardMandateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOffAppDebitCardMandateResponseMultiError) AllErrors() []error { return m }

// CreateOffAppDebitCardMandateResponseValidationError is the validation error
// returned by CreateOffAppDebitCardMandateResponse.Validate if the designated
// constraints aren't met.
type CreateOffAppDebitCardMandateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOffAppDebitCardMandateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOffAppDebitCardMandateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOffAppDebitCardMandateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOffAppDebitCardMandateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOffAppDebitCardMandateResponseValidationError) ErrorName() string {
	return "CreateOffAppDebitCardMandateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOffAppDebitCardMandateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOffAppDebitCardMandateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOffAppDebitCardMandateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOffAppDebitCardMandateResponseValidationError{}
