// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/debitcardmandate/debit_card_mandate.proto

package debitcardmandate

import (
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	enums "github.com/epifi/gamma/api/card/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DebitCardMandate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the internal unique identifier for a dc mandate
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// denotes the id of recurring payment for which dc mandate is created
	// relationship between recurring payment and dc mandate is 1 to 1 i.e only one dc mandate entry would exist for a given recurring payment entry.
	RecurringPaymentId string `protobuf:"bytes,2,opt,name=recurring_payment_id,json=recurringPaymentId,proto3" json:"recurring_payment_id,omitempty"`
	// card_id is unique identifier of the card across epifi systems for which mandate is created.
	CardId string `protobuf:"bytes,3,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// dedupe_id is unique identifier for dc mandates at the source of creation of the mandate, in this case it's debit card switch service
	DedupeId *DebitCardMandateDedupeId `protobuf:"bytes,4,opt,name=dedupe_id,json=dedupeId,proto3" json:"dedupe_id,omitempty"`
	// denotes the source from where the dc registration was performed by the user,
	// e.g FI_APP is registration was performed on Fi App, will be EXTERNAL if dc registration was not performed on Fi App but on some external portal.
	RegistrationProvenance enums.DebitCardMandateRegistrationProvenance `protobuf:"varint,5,opt,name=registration_provenance,json=registrationProvenance,proto3,enum=card.enums.DebitCardMandateRegistrationProvenance" json:"registration_provenance,omitempty"`
	// vendor who is fulfilling the mandate creation e.g FEDERAL_BANK, DIGIO etc
	Vendor    vendorgateway.Vendor   `protobuf:"varint,6,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *DebitCardMandate) Reset() {
	*x = DebitCardMandate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_debitcardmandate_debit_card_mandate_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebitCardMandate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebitCardMandate) ProtoMessage() {}

func (x *DebitCardMandate) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_debitcardmandate_debit_card_mandate_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebitCardMandate.ProtoReflect.Descriptor instead.
func (*DebitCardMandate) Descriptor() ([]byte, []int) {
	return file_api_card_debitcardmandate_debit_card_mandate_proto_rawDescGZIP(), []int{0}
}

func (x *DebitCardMandate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DebitCardMandate) GetRecurringPaymentId() string {
	if x != nil {
		return x.RecurringPaymentId
	}
	return ""
}

func (x *DebitCardMandate) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *DebitCardMandate) GetDedupeId() *DebitCardMandateDedupeId {
	if x != nil {
		return x.DedupeId
	}
	return nil
}

func (x *DebitCardMandate) GetRegistrationProvenance() enums.DebitCardMandateRegistrationProvenance {
	if x != nil {
		return x.RegistrationProvenance
	}
	return enums.DebitCardMandateRegistrationProvenance(0)
}

func (x *DebitCardMandate) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *DebitCardMandate) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DebitCardMandate) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type DebitCardMandateDedupeId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// primary identifier of a merchant across EPIFI systems
	MerchantId string `protobuf:"bytes,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
}

func (x *DebitCardMandateDedupeId) Reset() {
	*x = DebitCardMandateDedupeId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_debitcardmandate_debit_card_mandate_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebitCardMandateDedupeId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebitCardMandateDedupeId) ProtoMessage() {}

func (x *DebitCardMandateDedupeId) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_debitcardmandate_debit_card_mandate_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebitCardMandateDedupeId.ProtoReflect.Descriptor instead.
func (*DebitCardMandateDedupeId) Descriptor() ([]byte, []int) {
	return file_api_card_debitcardmandate_debit_card_mandate_proto_rawDescGZIP(), []int{1}
}

func (x *DebitCardMandateDedupeId) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *DebitCardMandateDedupeId) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

var File_api_card_debitcardmandate_debit_card_mandate_proto protoreflect.FileDescriptor

var file_api_card_debitcardmandate_debit_card_mandate_proto_rawDesc = []byte{
	0x0a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x64, 0x65, 0x62, 0x69, 0x74,
	0x63, 0x61, 0x72, 0x64, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x64, 0x65, 0x62, 0x69,
	0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65, 0x62, 0x69, 0x74,
	0x63, 0x61, 0x72, 0x64, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x64, 0x65, 0x62,
	0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcd, 0x03, 0x0a, 0x10, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x09, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x64, 0x65, 0x62, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x52, 0x08, 0x64, 0x65, 0x64, 0x75, 0x70,
	0x65, 0x49, 0x64, 0x12, 0x6b, 0x0a, 0x17, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72,
	0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x16, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12,
	0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x54, 0x0a, 0x18, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x42, 0x64, 0x0a, 0x30, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64,
	0x65, 0x62, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5a,
	0x30, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64,
	0x2f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_debitcardmandate_debit_card_mandate_proto_rawDescOnce sync.Once
	file_api_card_debitcardmandate_debit_card_mandate_proto_rawDescData = file_api_card_debitcardmandate_debit_card_mandate_proto_rawDesc
)

func file_api_card_debitcardmandate_debit_card_mandate_proto_rawDescGZIP() []byte {
	file_api_card_debitcardmandate_debit_card_mandate_proto_rawDescOnce.Do(func() {
		file_api_card_debitcardmandate_debit_card_mandate_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_debitcardmandate_debit_card_mandate_proto_rawDescData)
	})
	return file_api_card_debitcardmandate_debit_card_mandate_proto_rawDescData
}

var file_api_card_debitcardmandate_debit_card_mandate_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_card_debitcardmandate_debit_card_mandate_proto_goTypes = []interface{}{
	(*DebitCardMandate)(nil),                          // 0: card.debitcardmandate.DebitCardMandate
	(*DebitCardMandateDedupeId)(nil),                  // 1: card.debitcardmandate.DebitCardMandateDedupeId
	(enums.DebitCardMandateRegistrationProvenance)(0), // 2: card.enums.DebitCardMandateRegistrationProvenance
	(vendorgateway.Vendor)(0),                         // 3: vendorgateway.Vendor
	(*timestamppb.Timestamp)(nil),                     // 4: google.protobuf.Timestamp
}
var file_api_card_debitcardmandate_debit_card_mandate_proto_depIdxs = []int32{
	1, // 0: card.debitcardmandate.DebitCardMandate.dedupe_id:type_name -> card.debitcardmandate.DebitCardMandateDedupeId
	2, // 1: card.debitcardmandate.DebitCardMandate.registration_provenance:type_name -> card.enums.DebitCardMandateRegistrationProvenance
	3, // 2: card.debitcardmandate.DebitCardMandate.vendor:type_name -> vendorgateway.Vendor
	4, // 3: card.debitcardmandate.DebitCardMandate.created_at:type_name -> google.protobuf.Timestamp
	4, // 4: card.debitcardmandate.DebitCardMandate.updated_at:type_name -> google.protobuf.Timestamp
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_card_debitcardmandate_debit_card_mandate_proto_init() }
func file_api_card_debitcardmandate_debit_card_mandate_proto_init() {
	if File_api_card_debitcardmandate_debit_card_mandate_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_debitcardmandate_debit_card_mandate_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebitCardMandate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_debitcardmandate_debit_card_mandate_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebitCardMandateDedupeId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_debitcardmandate_debit_card_mandate_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_debitcardmandate_debit_card_mandate_proto_goTypes,
		DependencyIndexes: file_api_card_debitcardmandate_debit_card_mandate_proto_depIdxs,
		MessageInfos:      file_api_card_debitcardmandate_debit_card_mandate_proto_msgTypes,
	}.Build()
	File_api_card_debitcardmandate_debit_card_mandate_proto = out.File
	file_api_card_debitcardmandate_debit_card_mandate_proto_rawDesc = nil
	file_api_card_debitcardmandate_debit_card_mandate_proto_goTypes = nil
	file_api_card_debitcardmandate_debit_card_mandate_proto_depIdxs = nil
}
