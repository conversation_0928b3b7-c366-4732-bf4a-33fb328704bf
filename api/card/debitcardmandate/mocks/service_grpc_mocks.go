// Code generated by MockGen. DO NOT EDIT.
// Source: api/card/debitcardmandate/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	debitcardmandate "github.com/epifi/gamma/api/card/debitcardmandate"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockDebitCardMandateServiceClient is a mock of DebitCardMandateServiceClient interface.
type MockDebitCardMandateServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockDebitCardMandateServiceClientMockRecorder
}

// MockDebitCardMandateServiceClientMockRecorder is the mock recorder for MockDebitCardMandateServiceClient.
type MockDebitCardMandateServiceClientMockRecorder struct {
	mock *MockDebitCardMandateServiceClient
}

// NewMockDebitCardMandateServiceClient creates a new mock instance.
func NewMockDebitCardMandateServiceClient(ctrl *gomock.Controller) *MockDebitCardMandateServiceClient {
	mock := &MockDebitCardMandateServiceClient{ctrl: ctrl}
	mock.recorder = &MockDebitCardMandateServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDebitCardMandateServiceClient) EXPECT() *MockDebitCardMandateServiceClientMockRecorder {
	return m.recorder
}

// CreateOffAppDebitCardMandate mocks base method.
func (m *MockDebitCardMandateServiceClient) CreateOffAppDebitCardMandate(ctx context.Context, in *debitcardmandate.CreateOffAppDebitCardMandateRequest, opts ...grpc.CallOption) (*debitcardmandate.CreateOffAppDebitCardMandateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateOffAppDebitCardMandate", varargs...)
	ret0, _ := ret[0].(*debitcardmandate.CreateOffAppDebitCardMandateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOffAppDebitCardMandate indicates an expected call of CreateOffAppDebitCardMandate.
func (mr *MockDebitCardMandateServiceClientMockRecorder) CreateOffAppDebitCardMandate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOffAppDebitCardMandate", reflect.TypeOf((*MockDebitCardMandateServiceClient)(nil).CreateOffAppDebitCardMandate), varargs...)
}

// GetDebitCardMandate mocks base method.
func (m *MockDebitCardMandateServiceClient) GetDebitCardMandate(ctx context.Context, in *debitcardmandate.GetDebitCardMandateRequest, opts ...grpc.CallOption) (*debitcardmandate.GetDebitCardMandateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDebitCardMandate", varargs...)
	ret0, _ := ret[0].(*debitcardmandate.GetDebitCardMandateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDebitCardMandate indicates an expected call of GetDebitCardMandate.
func (mr *MockDebitCardMandateServiceClientMockRecorder) GetDebitCardMandate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDebitCardMandate", reflect.TypeOf((*MockDebitCardMandateServiceClient)(nil).GetDebitCardMandate), varargs...)
}

// MockDebitCardMandateServiceServer is a mock of DebitCardMandateServiceServer interface.
type MockDebitCardMandateServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockDebitCardMandateServiceServerMockRecorder
}

// MockDebitCardMandateServiceServerMockRecorder is the mock recorder for MockDebitCardMandateServiceServer.
type MockDebitCardMandateServiceServerMockRecorder struct {
	mock *MockDebitCardMandateServiceServer
}

// NewMockDebitCardMandateServiceServer creates a new mock instance.
func NewMockDebitCardMandateServiceServer(ctrl *gomock.Controller) *MockDebitCardMandateServiceServer {
	mock := &MockDebitCardMandateServiceServer{ctrl: ctrl}
	mock.recorder = &MockDebitCardMandateServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDebitCardMandateServiceServer) EXPECT() *MockDebitCardMandateServiceServerMockRecorder {
	return m.recorder
}

// CreateOffAppDebitCardMandate mocks base method.
func (m *MockDebitCardMandateServiceServer) CreateOffAppDebitCardMandate(arg0 context.Context, arg1 *debitcardmandate.CreateOffAppDebitCardMandateRequest) (*debitcardmandate.CreateOffAppDebitCardMandateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOffAppDebitCardMandate", arg0, arg1)
	ret0, _ := ret[0].(*debitcardmandate.CreateOffAppDebitCardMandateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOffAppDebitCardMandate indicates an expected call of CreateOffAppDebitCardMandate.
func (mr *MockDebitCardMandateServiceServerMockRecorder) CreateOffAppDebitCardMandate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOffAppDebitCardMandate", reflect.TypeOf((*MockDebitCardMandateServiceServer)(nil).CreateOffAppDebitCardMandate), arg0, arg1)
}

// GetDebitCardMandate mocks base method.
func (m *MockDebitCardMandateServiceServer) GetDebitCardMandate(arg0 context.Context, arg1 *debitcardmandate.GetDebitCardMandateRequest) (*debitcardmandate.GetDebitCardMandateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDebitCardMandate", arg0, arg1)
	ret0, _ := ret[0].(*debitcardmandate.GetDebitCardMandateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDebitCardMandate indicates an expected call of GetDebitCardMandate.
func (mr *MockDebitCardMandateServiceServerMockRecorder) GetDebitCardMandate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDebitCardMandate", reflect.TypeOf((*MockDebitCardMandateServiceServer)(nil).GetDebitCardMandate), arg0, arg1)
}

// MockUnsafeDebitCardMandateServiceServer is a mock of UnsafeDebitCardMandateServiceServer interface.
type MockUnsafeDebitCardMandateServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeDebitCardMandateServiceServerMockRecorder
}

// MockUnsafeDebitCardMandateServiceServerMockRecorder is the mock recorder for MockUnsafeDebitCardMandateServiceServer.
type MockUnsafeDebitCardMandateServiceServerMockRecorder struct {
	mock *MockUnsafeDebitCardMandateServiceServer
}

// NewMockUnsafeDebitCardMandateServiceServer creates a new mock instance.
func NewMockUnsafeDebitCardMandateServiceServer(ctrl *gomock.Controller) *MockUnsafeDebitCardMandateServiceServer {
	mock := &MockUnsafeDebitCardMandateServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeDebitCardMandateServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeDebitCardMandateServiceServer) EXPECT() *MockUnsafeDebitCardMandateServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedDebitCardMandateServiceServer mocks base method.
func (m *MockUnsafeDebitCardMandateServiceServer) mustEmbedUnimplementedDebitCardMandateServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedDebitCardMandateServiceServer")
}

// mustEmbedUnimplementedDebitCardMandateServiceServer indicates an expected call of mustEmbedUnimplementedDebitCardMandateServiceServer.
func (mr *MockUnsafeDebitCardMandateServiceServerMockRecorder) mustEmbedUnimplementedDebitCardMandateServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedDebitCardMandateServiceServer", reflect.TypeOf((*MockUnsafeDebitCardMandateServiceServer)(nil).mustEmbedUnimplementedDebitCardMandateServiceServer))
}
