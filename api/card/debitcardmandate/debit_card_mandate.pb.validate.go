// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/debitcardmandate/debit_card_mandate.proto

package debitcardmandate

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/card/enums"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.DebitCardMandateRegistrationProvenance(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on DebitCardMandate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DebitCardMandate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DebitCardMandate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DebitCardMandateMultiError, or nil if none found.
func (m *DebitCardMandate) ValidateAll() error {
	return m.validate(true)
}

func (m *DebitCardMandate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for RecurringPaymentId

	// no validation rules for CardId

	if all {
		switch v := interface{}(m.GetDedupeId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DebitCardMandateValidationError{
					field:  "DedupeId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DebitCardMandateValidationError{
					field:  "DedupeId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDedupeId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DebitCardMandateValidationError{
				field:  "DedupeId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RegistrationProvenance

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DebitCardMandateValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DebitCardMandateValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DebitCardMandateValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DebitCardMandateValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DebitCardMandateValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DebitCardMandateValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DebitCardMandateMultiError(errors)
	}

	return nil
}

// DebitCardMandateMultiError is an error wrapping multiple validation errors
// returned by DebitCardMandate.ValidateAll() if the designated constraints
// aren't met.
type DebitCardMandateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DebitCardMandateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DebitCardMandateMultiError) AllErrors() []error { return m }

// DebitCardMandateValidationError is the validation error returned by
// DebitCardMandate.Validate if the designated constraints aren't met.
type DebitCardMandateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DebitCardMandateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DebitCardMandateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DebitCardMandateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DebitCardMandateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DebitCardMandateValidationError) ErrorName() string { return "DebitCardMandateValidationError" }

// Error satisfies the builtin error interface
func (e DebitCardMandateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDebitCardMandate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DebitCardMandateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DebitCardMandateValidationError{}

// Validate checks the field values on DebitCardMandateDedupeId with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DebitCardMandateDedupeId) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DebitCardMandateDedupeId with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DebitCardMandateDedupeIdMultiError, or nil if none found.
func (m *DebitCardMandateDedupeId) ValidateAll() error {
	return m.validate(true)
}

func (m *DebitCardMandateDedupeId) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	// no validation rules for MerchantId

	if len(errors) > 0 {
		return DebitCardMandateDedupeIdMultiError(errors)
	}

	return nil
}

// DebitCardMandateDedupeIdMultiError is an error wrapping multiple validation
// errors returned by DebitCardMandateDedupeId.ValidateAll() if the designated
// constraints aren't met.
type DebitCardMandateDedupeIdMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DebitCardMandateDedupeIdMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DebitCardMandateDedupeIdMultiError) AllErrors() []error { return m }

// DebitCardMandateDedupeIdValidationError is the validation error returned by
// DebitCardMandateDedupeId.Validate if the designated constraints aren't met.
type DebitCardMandateDedupeIdValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DebitCardMandateDedupeIdValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DebitCardMandateDedupeIdValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DebitCardMandateDedupeIdValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DebitCardMandateDedupeIdValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DebitCardMandateDedupeIdValidationError) ErrorName() string {
	return "DebitCardMandateDedupeIdValidationError"
}

// Error satisfies the builtin error interface
func (e DebitCardMandateDedupeIdValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDebitCardMandateDedupeId.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DebitCardMandateDedupeIdValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DebitCardMandateDedupeIdValidationError{}
