// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/card/debitcardmandate/service.proto

package debitcardmandate

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DebitCardMandateService_CreateOffAppDebitCardMandate_FullMethodName = "/card.debitcardmandate.DebitCardMandateService/CreateOffAppDebitCardMandate"
	DebitCardMandateService_GetDebitCardMandate_FullMethodName          = "/card.debitcardmandate.DebitCardMandateService/GetDebitCardMandate"
)

// DebitCardMandateServiceClient is the client API for DebitCardMandateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DebitCardMandateServiceClient interface {
	// CreateOffAppDcMandate idempotant rpc used to persist dc mandate that is initiated from 3rd party app/off-app like Groww
	CreateOffAppDebitCardMandate(ctx context.Context, in *CreateOffAppDebitCardMandateRequest, opts ...grpc.CallOption) (*CreateOffAppDebitCardMandateResponse, error)
	// GetDebitCardMandate fetches the dc mandate using the given identifier
	GetDebitCardMandate(ctx context.Context, in *GetDebitCardMandateRequest, opts ...grpc.CallOption) (*GetDebitCardMandateResponse, error)
}

type debitCardMandateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDebitCardMandateServiceClient(cc grpc.ClientConnInterface) DebitCardMandateServiceClient {
	return &debitCardMandateServiceClient{cc}
}

func (c *debitCardMandateServiceClient) CreateOffAppDebitCardMandate(ctx context.Context, in *CreateOffAppDebitCardMandateRequest, opts ...grpc.CallOption) (*CreateOffAppDebitCardMandateResponse, error) {
	out := new(CreateOffAppDebitCardMandateResponse)
	err := c.cc.Invoke(ctx, DebitCardMandateService_CreateOffAppDebitCardMandate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debitCardMandateServiceClient) GetDebitCardMandate(ctx context.Context, in *GetDebitCardMandateRequest, opts ...grpc.CallOption) (*GetDebitCardMandateResponse, error) {
	out := new(GetDebitCardMandateResponse)
	err := c.cc.Invoke(ctx, DebitCardMandateService_GetDebitCardMandate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DebitCardMandateServiceServer is the server API for DebitCardMandateService service.
// All implementations should embed UnimplementedDebitCardMandateServiceServer
// for forward compatibility
type DebitCardMandateServiceServer interface {
	// CreateOffAppDcMandate idempotant rpc used to persist dc mandate that is initiated from 3rd party app/off-app like Groww
	CreateOffAppDebitCardMandate(context.Context, *CreateOffAppDebitCardMandateRequest) (*CreateOffAppDebitCardMandateResponse, error)
	// GetDebitCardMandate fetches the dc mandate using the given identifier
	GetDebitCardMandate(context.Context, *GetDebitCardMandateRequest) (*GetDebitCardMandateResponse, error)
}

// UnimplementedDebitCardMandateServiceServer should be embedded to have forward compatible implementations.
type UnimplementedDebitCardMandateServiceServer struct {
}

func (UnimplementedDebitCardMandateServiceServer) CreateOffAppDebitCardMandate(context.Context, *CreateOffAppDebitCardMandateRequest) (*CreateOffAppDebitCardMandateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOffAppDebitCardMandate not implemented")
}
func (UnimplementedDebitCardMandateServiceServer) GetDebitCardMandate(context.Context, *GetDebitCardMandateRequest) (*GetDebitCardMandateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDebitCardMandate not implemented")
}

// UnsafeDebitCardMandateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DebitCardMandateServiceServer will
// result in compilation errors.
type UnsafeDebitCardMandateServiceServer interface {
	mustEmbedUnimplementedDebitCardMandateServiceServer()
}

func RegisterDebitCardMandateServiceServer(s grpc.ServiceRegistrar, srv DebitCardMandateServiceServer) {
	s.RegisterService(&DebitCardMandateService_ServiceDesc, srv)
}

func _DebitCardMandateService_CreateOffAppDebitCardMandate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOffAppDebitCardMandateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebitCardMandateServiceServer).CreateOffAppDebitCardMandate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DebitCardMandateService_CreateOffAppDebitCardMandate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebitCardMandateServiceServer).CreateOffAppDebitCardMandate(ctx, req.(*CreateOffAppDebitCardMandateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DebitCardMandateService_GetDebitCardMandate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDebitCardMandateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebitCardMandateServiceServer).GetDebitCardMandate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DebitCardMandateService_GetDebitCardMandate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebitCardMandateServiceServer).GetDebitCardMandate(ctx, req.(*GetDebitCardMandateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DebitCardMandateService_ServiceDesc is the grpc.ServiceDesc for DebitCardMandateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DebitCardMandateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "card.debitcardmandate.DebitCardMandateService",
	HandlerType: (*DebitCardMandateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateOffAppDebitCardMandate",
			Handler:    _DebitCardMandateService_CreateOffAppDebitCardMandate_Handler,
		},
		{
			MethodName: "GetDebitCardMandate",
			Handler:    _DebitCardMandateService_GetDebitCardMandate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/card/debitcardmandate/service.proto",
}
