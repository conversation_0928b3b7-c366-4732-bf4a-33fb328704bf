// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/debitcardmandate/service.proto

package debitcardmandate

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	enums "github.com/epifi/gamma/api/card/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetDebitCardMandateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetDebitCardMandateRequest_DedupeId
	//	*GetDebitCardMandateRequest_RecurringPaymentId
	Identifier isGetDebitCardMandateRequest_Identifier `protobuf_oneof:"Identifier"`
}

func (x *GetDebitCardMandateRequest) Reset() {
	*x = GetDebitCardMandateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_debitcardmandate_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDebitCardMandateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDebitCardMandateRequest) ProtoMessage() {}

func (x *GetDebitCardMandateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_debitcardmandate_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDebitCardMandateRequest.ProtoReflect.Descriptor instead.
func (*GetDebitCardMandateRequest) Descriptor() ([]byte, []int) {
	return file_api_card_debitcardmandate_service_proto_rawDescGZIP(), []int{0}
}

func (m *GetDebitCardMandateRequest) GetIdentifier() isGetDebitCardMandateRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetDebitCardMandateRequest) GetDedupeId() *DebitCardMandateDedupeId {
	if x, ok := x.GetIdentifier().(*GetDebitCardMandateRequest_DedupeId); ok {
		return x.DedupeId
	}
	return nil
}

func (x *GetDebitCardMandateRequest) GetRecurringPaymentId() string {
	if x, ok := x.GetIdentifier().(*GetDebitCardMandateRequest_RecurringPaymentId); ok {
		return x.RecurringPaymentId
	}
	return ""
}

type isGetDebitCardMandateRequest_Identifier interface {
	isGetDebitCardMandateRequest_Identifier()
}

type GetDebitCardMandateRequest_DedupeId struct {
	DedupeId *DebitCardMandateDedupeId `protobuf:"bytes,1,opt,name=dedupe_id,json=dedupeId,proto3,oneof"`
}

type GetDebitCardMandateRequest_RecurringPaymentId struct {
	RecurringPaymentId string `protobuf:"bytes,2,opt,name=recurring_payment_id,json=recurringPaymentId,proto3,oneof"`
}

func (*GetDebitCardMandateRequest_DedupeId) isGetDebitCardMandateRequest_Identifier() {}

func (*GetDebitCardMandateRequest_RecurringPaymentId) isGetDebitCardMandateRequest_Identifier() {}

type GetDebitCardMandateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           *rpc.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	DebitCardMandate *DebitCardMandate `protobuf:"bytes,2,opt,name=debit_card_mandate,json=debitCardMandate,proto3" json:"debit_card_mandate,omitempty"`
}

func (x *GetDebitCardMandateResponse) Reset() {
	*x = GetDebitCardMandateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_debitcardmandate_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDebitCardMandateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDebitCardMandateResponse) ProtoMessage() {}

func (x *GetDebitCardMandateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_debitcardmandate_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDebitCardMandateResponse.ProtoReflect.Descriptor instead.
func (*GetDebitCardMandateResponse) Descriptor() ([]byte, []int) {
	return file_api_card_debitcardmandate_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetDebitCardMandateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetDebitCardMandateResponse) GetDebitCardMandate() *DebitCardMandate {
	if x != nil {
		return x.DebitCardMandate
	}
	return nil
}

type CreateOffAppDebitCardMandateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardId             string                                       `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	MerchantId         string                                       `protobuf:"bytes,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	RecurringPaymentId string                                       `protobuf:"bytes,3,opt,name=recurring_payment_id,json=recurringPaymentId,proto3" json:"recurring_payment_id,omitempty"`
	Provenance         enums.DebitCardMandateRegistrationProvenance `protobuf:"varint,4,opt,name=provenance,proto3,enum=card.enums.DebitCardMandateRegistrationProvenance" json:"provenance,omitempty"`
}

func (x *CreateOffAppDebitCardMandateRequest) Reset() {
	*x = CreateOffAppDebitCardMandateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_debitcardmandate_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOffAppDebitCardMandateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOffAppDebitCardMandateRequest) ProtoMessage() {}

func (x *CreateOffAppDebitCardMandateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_debitcardmandate_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOffAppDebitCardMandateRequest.ProtoReflect.Descriptor instead.
func (*CreateOffAppDebitCardMandateRequest) Descriptor() ([]byte, []int) {
	return file_api_card_debitcardmandate_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateOffAppDebitCardMandateRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CreateOffAppDebitCardMandateRequest) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

func (x *CreateOffAppDebitCardMandateRequest) GetRecurringPaymentId() string {
	if x != nil {
		return x.RecurringPaymentId
	}
	return ""
}

func (x *CreateOffAppDebitCardMandateRequest) GetProvenance() enums.DebitCardMandateRegistrationProvenance {
	if x != nil {
		return x.Provenance
	}
	return enums.DebitCardMandateRegistrationProvenance(0)
}

type CreateOffAppDebitCardMandateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           *rpc.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	DebitCardMandate *DebitCardMandate `protobuf:"bytes,2,opt,name=debit_card_mandate,json=debitCardMandate,proto3" json:"debit_card_mandate,omitempty"`
}

func (x *CreateOffAppDebitCardMandateResponse) Reset() {
	*x = CreateOffAppDebitCardMandateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_debitcardmandate_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOffAppDebitCardMandateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOffAppDebitCardMandateResponse) ProtoMessage() {}

func (x *CreateOffAppDebitCardMandateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_debitcardmandate_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOffAppDebitCardMandateResponse.ProtoReflect.Descriptor instead.
func (*CreateOffAppDebitCardMandateResponse) Descriptor() ([]byte, []int) {
	return file_api_card_debitcardmandate_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreateOffAppDebitCardMandateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateOffAppDebitCardMandateResponse) GetDebitCardMandate() *DebitCardMandate {
	if x != nil {
		return x.DebitCardMandate
	}
	return nil
}

var File_api_card_debitcardmandate_service_proto protoreflect.FileDescriptor

var file_api_card_debitcardmandate_service_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x64, 0x65, 0x62, 0x69, 0x74,
	0x63, 0x61, 0x72, 0x64, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x64, 0x65, 0x62, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x1a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x64, 0x65, 0x62, 0x69, 0x74,
	0x63, 0x61, 0x72, 0x64, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x64, 0x65, 0x62, 0x69,
	0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xae, 0x01, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4e, 0x0a, 0x09, 0x64,
	0x65, 0x64, 0x75, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65, 0x62, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x48,
	0x00, 0x52, 0x08, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x14, 0x72,
	0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x12, 0x72, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x42,
	0x0c, 0x0a, 0x0a, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x99, 0x01,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x55, 0x0a, 0x12, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65, 0x62, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x10, 0x64, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x22, 0x8a, 0x02, 0x0a, 0x23, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x41, 0x70, 0x70, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x20, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x63, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x39, 0x0a,
	0x14, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x12, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x5c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x22, 0xa2, 0x01, 0x0a, 0x24, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4f, 0x66, 0x66, 0x41, 0x70, 0x70, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x55, 0x0a, 0x12, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65, 0x62, 0x69, 0x74, 0x63, 0x61, 0x72,
	0x64, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x10, 0x64, 0x65, 0x62, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x32, 0xb5, 0x02, 0x0a, 0x17,
	0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4f, 0x66, 0x66, 0x41, 0x70, 0x70, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x12, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x64, 0x65, 0x62, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x41, 0x70, 0x70, 0x44, 0x65, 0x62,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65, 0x62, 0x69,
	0x74, 0x63, 0x61, 0x72, 0x64, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x41, 0x70, 0x70, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x12, 0x31, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x64, 0x65, 0x62, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x6d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65, 0x62, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64, 0x6d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x42, 0x64, 0x0a, 0x30, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x64, 0x65, 0x62, 0x69, 0x74, 0x63, 0x61, 0x72, 0x64,
	0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5a, 0x30, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x63, 0x61,
	0x72, 0x64, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_card_debitcardmandate_service_proto_rawDescOnce sync.Once
	file_api_card_debitcardmandate_service_proto_rawDescData = file_api_card_debitcardmandate_service_proto_rawDesc
)

func file_api_card_debitcardmandate_service_proto_rawDescGZIP() []byte {
	file_api_card_debitcardmandate_service_proto_rawDescOnce.Do(func() {
		file_api_card_debitcardmandate_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_debitcardmandate_service_proto_rawDescData)
	})
	return file_api_card_debitcardmandate_service_proto_rawDescData
}

var file_api_card_debitcardmandate_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_card_debitcardmandate_service_proto_goTypes = []interface{}{
	(*GetDebitCardMandateRequest)(nil),                // 0: card.debitcardmandate.GetDebitCardMandateRequest
	(*GetDebitCardMandateResponse)(nil),               // 1: card.debitcardmandate.GetDebitCardMandateResponse
	(*CreateOffAppDebitCardMandateRequest)(nil),       // 2: card.debitcardmandate.CreateOffAppDebitCardMandateRequest
	(*CreateOffAppDebitCardMandateResponse)(nil),      // 3: card.debitcardmandate.CreateOffAppDebitCardMandateResponse
	(*DebitCardMandateDedupeId)(nil),                  // 4: card.debitcardmandate.DebitCardMandateDedupeId
	(*rpc.Status)(nil),                                // 5: rpc.Status
	(*DebitCardMandate)(nil),                          // 6: card.debitcardmandate.DebitCardMandate
	(enums.DebitCardMandateRegistrationProvenance)(0), // 7: card.enums.DebitCardMandateRegistrationProvenance
}
var file_api_card_debitcardmandate_service_proto_depIdxs = []int32{
	4, // 0: card.debitcardmandate.GetDebitCardMandateRequest.dedupe_id:type_name -> card.debitcardmandate.DebitCardMandateDedupeId
	5, // 1: card.debitcardmandate.GetDebitCardMandateResponse.status:type_name -> rpc.Status
	6, // 2: card.debitcardmandate.GetDebitCardMandateResponse.debit_card_mandate:type_name -> card.debitcardmandate.DebitCardMandate
	7, // 3: card.debitcardmandate.CreateOffAppDebitCardMandateRequest.provenance:type_name -> card.enums.DebitCardMandateRegistrationProvenance
	5, // 4: card.debitcardmandate.CreateOffAppDebitCardMandateResponse.status:type_name -> rpc.Status
	6, // 5: card.debitcardmandate.CreateOffAppDebitCardMandateResponse.debit_card_mandate:type_name -> card.debitcardmandate.DebitCardMandate
	2, // 6: card.debitcardmandate.DebitCardMandateService.CreateOffAppDebitCardMandate:input_type -> card.debitcardmandate.CreateOffAppDebitCardMandateRequest
	0, // 7: card.debitcardmandate.DebitCardMandateService.GetDebitCardMandate:input_type -> card.debitcardmandate.GetDebitCardMandateRequest
	3, // 8: card.debitcardmandate.DebitCardMandateService.CreateOffAppDebitCardMandate:output_type -> card.debitcardmandate.CreateOffAppDebitCardMandateResponse
	1, // 9: card.debitcardmandate.DebitCardMandateService.GetDebitCardMandate:output_type -> card.debitcardmandate.GetDebitCardMandateResponse
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_card_debitcardmandate_service_proto_init() }
func file_api_card_debitcardmandate_service_proto_init() {
	if File_api_card_debitcardmandate_service_proto != nil {
		return
	}
	file_api_card_debitcardmandate_debit_card_mandate_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_card_debitcardmandate_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDebitCardMandateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_debitcardmandate_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDebitCardMandateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_debitcardmandate_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOffAppDebitCardMandateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_debitcardmandate_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOffAppDebitCardMandateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_card_debitcardmandate_service_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*GetDebitCardMandateRequest_DedupeId)(nil),
		(*GetDebitCardMandateRequest_RecurringPaymentId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_debitcardmandate_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_card_debitcardmandate_service_proto_goTypes,
		DependencyIndexes: file_api_card_debitcardmandate_service_proto_depIdxs,
		MessageInfos:      file_api_card_debitcardmandate_service_proto_msgTypes,
	}.Build()
	File_api_card_debitcardmandate_service_proto = out.File
	file_api_card_debitcardmandate_service_proto_rawDesc = nil
	file_api_card_debitcardmandate_service_proto_goTypes = nil
	file_api_card_debitcardmandate_service_proto_depIdxs = nil
}
