// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=ForexChargesInfo

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/forex_txn_refunds.proto

package card

import (
	enums "github.com/epifi/gamma/api/card/enums"
	external "github.com/epifi/gamma/api/tiering/external"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// object to store the record and details of all the refunds for all the  international transactions
type DcForexTxnRefund struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// id of the txn for which the refund evaluation is being done
	TxnId   string `protobuf:"bytes,2,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Total amount of the full forex charges including GST
	TotalTxnAmount *money.Money `protobuf:"bytes,4,opt,name=total_txn_amount,json=totalTxnAmount,proto3" json:"total_txn_amount,omitempty"`
	// refund amount to be credited to the user
	RefundAmount *money.Money `protobuf:"bytes,5,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount,omitempty"`
	// time of the original transaction taking place
	TxnTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=txn_time,json=txnTime,proto3" json:"txn_time,omitempty"`
	// Status of the refund which can go from CREATED to APPROVED/REJECTED and finally PROCESSED if approved
	RefundStatus enums.RefundStatus `protobuf:"varint,7,opt,name=refund_status,json=refundStatus,proto3,enum=card.enums.RefundStatus" json:"refund_status,omitempty"`
	// Identifier to track the status of process being followed for refund
	ProcessIdentifier string `protobuf:"bytes,8,opt,name=process_identifier,json=processIdentifier,proto3" json:"process_identifier,omitempty"`
	// Tier of the user at the time of the original transaction which will be taken into account for refund calculation
	TxnTimeUserTier external.Tier `protobuf:"varint,9,opt,name=txn_time_user_tier,json=txnTimeUserTier,proto3,enum=tiering.external.Tier" json:"txn_time_user_tier,omitempty"`
	// Manner in which the refund will be processed i.e whether it will be in real time or asynchronous
	RefundProcessingMode enums.RefundProcessingMode `protobuf:"varint,10,opt,name=refund_processing_mode,json=refundProcessingMode,proto3,enum=card.enums.RefundProcessingMode" json:"refund_processing_mode,omitempty"`
	CreatedAt            *timestamppb.Timestamp     `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt            *timestamppb.Timestamp     `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt            *timestamppb.Timestamp     `protobuf:"bytes,13,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// id to maintain a state of the workflow in parallel to temporal/celestial
	OrchId string `protobuf:"bytes,14,opt,name=orch_id,json=orchId,proto3" json:"orch_id,omitempty"`
	// Type of txn for which the refund is being considered. Could be either DEBIT or CREDIT
	TxnType enums.TransactionType `protobuf:"varint,15,opt,name=txn_type,json=txnType,proto3,enum=card.enums.TransactionType" json:"txn_type,omitempty"`
	// txn id of the txn in which this refund came
	RefundTxnId string `protobuf:"bytes,16,opt,name=refund_txn_id,json=refundTxnId,proto3" json:"refund_txn_id,omitempty"`
	// enums storing granular status of refund failure/success
	RefundSubStatus enums.RefundSubStatus `protobuf:"varint,17,opt,name=refund_sub_status,json=refundSubStatus,proto3,enum=card.enums.RefundSubStatus" json:"refund_sub_status,omitempty"`
	// structure to keep the data sent to the bank stored in the db as well
	ForexChargesInfo *ForexChargesInfo `protobuf:"bytes,18,opt,name=forex_charges_info,json=forexChargesInfo,proto3" json:"forex_charges_info,omitempty"`
	// a unique identifier that is common to only parent & child transactions, e.x. forex, DCC, TCS
	DedupeIdentifier string `protobuf:"bytes,19,opt,name=dedupe_identifier,json=dedupeIdentifier,proto3" json:"dedupe_identifier,omitempty"`
	// to store the ID of the parent transaction
	OriginalTransaction string `protobuf:"bytes,20,opt,name=original_transaction,json=originalTransaction,proto3" json:"original_transaction,omitempty"`
}

func (x *DcForexTxnRefund) Reset() {
	*x = DcForexTxnRefund{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_forex_txn_refunds_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DcForexTxnRefund) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DcForexTxnRefund) ProtoMessage() {}

func (x *DcForexTxnRefund) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_forex_txn_refunds_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DcForexTxnRefund.ProtoReflect.Descriptor instead.
func (*DcForexTxnRefund) Descriptor() ([]byte, []int) {
	return file_api_card_forex_txn_refunds_proto_rawDescGZIP(), []int{0}
}

func (x *DcForexTxnRefund) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DcForexTxnRefund) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

func (x *DcForexTxnRefund) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DcForexTxnRefund) GetTotalTxnAmount() *money.Money {
	if x != nil {
		return x.TotalTxnAmount
	}
	return nil
}

func (x *DcForexTxnRefund) GetRefundAmount() *money.Money {
	if x != nil {
		return x.RefundAmount
	}
	return nil
}

func (x *DcForexTxnRefund) GetTxnTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TxnTime
	}
	return nil
}

func (x *DcForexTxnRefund) GetRefundStatus() enums.RefundStatus {
	if x != nil {
		return x.RefundStatus
	}
	return enums.RefundStatus(0)
}

func (x *DcForexTxnRefund) GetProcessIdentifier() string {
	if x != nil {
		return x.ProcessIdentifier
	}
	return ""
}

func (x *DcForexTxnRefund) GetTxnTimeUserTier() external.Tier {
	if x != nil {
		return x.TxnTimeUserTier
	}
	return external.Tier(0)
}

func (x *DcForexTxnRefund) GetRefundProcessingMode() enums.RefundProcessingMode {
	if x != nil {
		return x.RefundProcessingMode
	}
	return enums.RefundProcessingMode(0)
}

func (x *DcForexTxnRefund) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DcForexTxnRefund) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *DcForexTxnRefund) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *DcForexTxnRefund) GetOrchId() string {
	if x != nil {
		return x.OrchId
	}
	return ""
}

func (x *DcForexTxnRefund) GetTxnType() enums.TransactionType {
	if x != nil {
		return x.TxnType
	}
	return enums.TransactionType(0)
}

func (x *DcForexTxnRefund) GetRefundTxnId() string {
	if x != nil {
		return x.RefundTxnId
	}
	return ""
}

func (x *DcForexTxnRefund) GetRefundSubStatus() enums.RefundSubStatus {
	if x != nil {
		return x.RefundSubStatus
	}
	return enums.RefundSubStatus(0)
}

func (x *DcForexTxnRefund) GetForexChargesInfo() *ForexChargesInfo {
	if x != nil {
		return x.ForexChargesInfo
	}
	return nil
}

func (x *DcForexTxnRefund) GetDedupeIdentifier() string {
	if x != nil {
		return x.DedupeIdentifier
	}
	return ""
}

func (x *DcForexTxnRefund) GetOriginalTransaction() string {
	if x != nil {
		return x.OriginalTransaction
	}
	return ""
}

type ForexChargesInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// country code of the country in which the txn was done
	CountryCode string `protobuf:"bytes,1,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// percentage forex charged on the txn
	ForexPercentage float32 `protobuf:"fixed32,2,opt,name=forex_percentage,json=forexPercentage,proto3" json:"forex_percentage,omitempty"`
	// forex charge applied to the user
	ForexChargeAmount *money.Money `protobuf:"bytes,3,opt,name=forex_charge_amount,json=forexChargeAmount,proto3" json:"forex_charge_amount,omitempty"`
	// time at which refund was processed at Fi's end and was sent to bank for further action
	SentToBankAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=sent_to_bank_at,json=sentToBankAt,proto3" json:"sent_to_bank_at,omitempty"`
}

func (x *ForexChargesInfo) Reset() {
	*x = ForexChargesInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_forex_txn_refunds_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForexChargesInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForexChargesInfo) ProtoMessage() {}

func (x *ForexChargesInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_forex_txn_refunds_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForexChargesInfo.ProtoReflect.Descriptor instead.
func (*ForexChargesInfo) Descriptor() ([]byte, []int) {
	return file_api_card_forex_txn_refunds_proto_rawDescGZIP(), []int{1}
}

func (x *ForexChargesInfo) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *ForexChargesInfo) GetForexPercentage() float32 {
	if x != nil {
		return x.ForexPercentage
	}
	return 0
}

func (x *ForexChargesInfo) GetForexChargeAmount() *money.Money {
	if x != nil {
		return x.ForexChargeAmount
	}
	return nil
}

func (x *ForexChargesInfo) GetSentToBankAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SentToBankAt
	}
	return nil
}

type ForexRefundAggregate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tier where the forex refund is grouped by
	TxnTimeUserTier external.Tier `protobuf:"varint,1,opt,name=txn_time_user_tier,json=txnTimeUserTier,proto3,enum=tiering.external.Tier" json:"txn_time_user_tier,omitempty"`
	// aggregate money units for refund amount
	RefundAmountUnitsSum int64 `protobuf:"varint,2,opt,name=refund_amount_units_sum,json=refundAmountUnitsSum,proto3" json:"refund_amount_units_sum,omitempty"`
	// aggregate money nanos for refund amount
	RefundAmountNanosSum int64 `protobuf:"varint,3,opt,name=refund_amount_nanos_sum,json=refundAmountNanosSum,proto3" json:"refund_amount_nanos_sum,omitempty"`
	// count of refunds added in aggregate
	RefundCount int32 `protobuf:"varint,4,opt,name=refund_count,json=refundCount,proto3" json:"refund_count,omitempty"`
}

func (x *ForexRefundAggregate) Reset() {
	*x = ForexRefundAggregate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_forex_txn_refunds_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForexRefundAggregate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForexRefundAggregate) ProtoMessage() {}

func (x *ForexRefundAggregate) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_forex_txn_refunds_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForexRefundAggregate.ProtoReflect.Descriptor instead.
func (*ForexRefundAggregate) Descriptor() ([]byte, []int) {
	return file_api_card_forex_txn_refunds_proto_rawDescGZIP(), []int{2}
}

func (x *ForexRefundAggregate) GetTxnTimeUserTier() external.Tier {
	if x != nil {
		return x.TxnTimeUserTier
	}
	return external.Tier(0)
}

func (x *ForexRefundAggregate) GetRefundAmountUnitsSum() int64 {
	if x != nil {
		return x.RefundAmountUnitsSum
	}
	return 0
}

func (x *ForexRefundAggregate) GetRefundAmountNanosSum() int64 {
	if x != nil {
		return x.RefundAmountNanosSum
	}
	return 0
}

func (x *ForexRefundAggregate) GetRefundCount() int32 {
	if x != nil {
		return x.RefundCount
	}
	return 0
}

var File_api_card_forex_txn_refunds_proto protoreflect.FileDescriptor

var file_api_card_forex_txn_refunds_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x66, 0x6f, 0x72, 0x65, 0x78,
	0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x04, 0x63, 0x61, 0x72, 0x64, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61,
	0x72, 0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xa2, 0x08, 0x0a, 0x10, 0x44, 0x63, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x54,
	0x78, 0x6e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x10, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x54,
	0x78, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x35, 0x0a, 0x08, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x07, 0x74, 0x78, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x18, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x12, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x0f, 0x74, 0x78, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x69, 0x65, 0x72, 0x12, 0x56, 0x0a, 0x16, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x14, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x4d,
	0x6f, 0x64, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x63, 0x68, 0x49, 0x64, 0x12, 0x36, 0x0a,
	0x08, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x74, 0x78,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x11, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x44, 0x0a, 0x12, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x5f, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x0a, 0x11, 0x64, 0x65, 0x64, 0x75,
	0x70, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x14, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61,
	0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x13, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe7, 0x01, 0x0a, 0x10, 0x46, 0x6f, 0x72,
	0x65, 0x78, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a,
	0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x29, 0x0a, 0x10, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x66, 0x6f, 0x72, 0x65,
	0x78, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x42, 0x0a, 0x13, 0x66,
	0x6f, 0x72, 0x65, 0x78, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x66, 0x6f,
	0x72, 0x65, 0x78, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x41, 0x0a, 0x0f, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x73, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x42, 0x61, 0x6e, 0x6b,
	0x41, 0x74, 0x22, 0xec, 0x01, 0x0a, 0x14, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x12, 0x43, 0x0a, 0x12, 0x74,
	0x78, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52,
	0x0f, 0x74, 0x78, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x69, 0x65, 0x72,
	0x12, 0x35, 0x0a, 0x17, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x14, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x55,
	0x6e, 0x69, 0x74, 0x73, 0x53, 0x75, 0x6d, 0x12, 0x35, 0x0a, 0x17, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6e, 0x6f, 0x73, 0x5f, 0x73,
	0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6e, 0x6f, 0x73, 0x53, 0x75, 0x6d, 0x12, 0x21,
	0x0a, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x61, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_forex_txn_refunds_proto_rawDescOnce sync.Once
	file_api_card_forex_txn_refunds_proto_rawDescData = file_api_card_forex_txn_refunds_proto_rawDesc
)

func file_api_card_forex_txn_refunds_proto_rawDescGZIP() []byte {
	file_api_card_forex_txn_refunds_proto_rawDescOnce.Do(func() {
		file_api_card_forex_txn_refunds_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_forex_txn_refunds_proto_rawDescData)
	})
	return file_api_card_forex_txn_refunds_proto_rawDescData
}

var file_api_card_forex_txn_refunds_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_card_forex_txn_refunds_proto_goTypes = []interface{}{
	(*DcForexTxnRefund)(nil),        // 0: card.DcForexTxnRefund
	(*ForexChargesInfo)(nil),        // 1: card.ForexChargesInfo
	(*ForexRefundAggregate)(nil),    // 2: card.ForexRefundAggregate
	(*money.Money)(nil),             // 3: google.type.Money
	(*timestamppb.Timestamp)(nil),   // 4: google.protobuf.Timestamp
	(enums.RefundStatus)(0),         // 5: card.enums.RefundStatus
	(external.Tier)(0),              // 6: tiering.external.Tier
	(enums.RefundProcessingMode)(0), // 7: card.enums.RefundProcessingMode
	(enums.TransactionType)(0),      // 8: card.enums.TransactionType
	(enums.RefundSubStatus)(0),      // 9: card.enums.RefundSubStatus
}
var file_api_card_forex_txn_refunds_proto_depIdxs = []int32{
	3,  // 0: card.DcForexTxnRefund.total_txn_amount:type_name -> google.type.Money
	3,  // 1: card.DcForexTxnRefund.refund_amount:type_name -> google.type.Money
	4,  // 2: card.DcForexTxnRefund.txn_time:type_name -> google.protobuf.Timestamp
	5,  // 3: card.DcForexTxnRefund.refund_status:type_name -> card.enums.RefundStatus
	6,  // 4: card.DcForexTxnRefund.txn_time_user_tier:type_name -> tiering.external.Tier
	7,  // 5: card.DcForexTxnRefund.refund_processing_mode:type_name -> card.enums.RefundProcessingMode
	4,  // 6: card.DcForexTxnRefund.created_at:type_name -> google.protobuf.Timestamp
	4,  // 7: card.DcForexTxnRefund.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 8: card.DcForexTxnRefund.deleted_at:type_name -> google.protobuf.Timestamp
	8,  // 9: card.DcForexTxnRefund.txn_type:type_name -> card.enums.TransactionType
	9,  // 10: card.DcForexTxnRefund.refund_sub_status:type_name -> card.enums.RefundSubStatus
	1,  // 11: card.DcForexTxnRefund.forex_charges_info:type_name -> card.ForexChargesInfo
	3,  // 12: card.ForexChargesInfo.forex_charge_amount:type_name -> google.type.Money
	4,  // 13: card.ForexChargesInfo.sent_to_bank_at:type_name -> google.protobuf.Timestamp
	6,  // 14: card.ForexRefundAggregate.txn_time_user_tier:type_name -> tiering.external.Tier
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_api_card_forex_txn_refunds_proto_init() }
func file_api_card_forex_txn_refunds_proto_init() {
	if File_api_card_forex_txn_refunds_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_forex_txn_refunds_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DcForexTxnRefund); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_forex_txn_refunds_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForexChargesInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_forex_txn_refunds_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForexRefundAggregate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_forex_txn_refunds_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_forex_txn_refunds_proto_goTypes,
		DependencyIndexes: file_api_card_forex_txn_refunds_proto_depIdxs,
		MessageInfos:      file_api_card_forex_txn_refunds_proto_msgTypes,
	}.Build()
	File_api_card_forex_txn_refunds_proto = out.File
	file_api_card_forex_txn_refunds_proto_rawDesc = nil
	file_api_card_forex_txn_refunds_proto_goTypes = nil
	file_api_card_forex_txn_refunds_proto_depIdxs = nil
}
