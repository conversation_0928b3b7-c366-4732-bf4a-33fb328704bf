// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/comms/action_data.proto

package comms

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	control "github.com/epifi/gamma/api/card/control"
	enums "github.com/epifi/gamma/api/card/enums"
	provisioning "github.com/epifi/gamma/api/card/provisioning"
	order "github.com/epifi/gamma/api/order"
	payment "github.com/epifi/gamma/api/order/payment"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id to which card is associated with
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// txn amount
	// this will be nil in case comm to be sent is not related to financial txn
	TxnAmount *money.Money `protobuf:"bytes,2,opt,name=txn_amount,json=txnAmount,proto3" json:"txn_amount,omitempty"`
	// card switch notification response
	// enums mapping is done in VN
	// this is used as the conditional identifier to send comms related to card switch notifications
	// this will be UNSPECIFIED if comm to be sent is not related to switch notifications
	SwitchNotificationResponse enums.SwitchNotificationResponse `protobuf:"varint,3,opt,name=switch_notification_response,json=switchNotificationResponse,proto3,enum=card.enums.SwitchNotificationResponse" json:"switch_notification_response,omitempty"`
	// unique identifier of the card
	CardId string `protobuf:"bytes,4,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// last four digits of the card number
	CardLastFourDigits string `protobuf:"bytes,5,opt,name=card_last_four_digits,json=cardLastFourDigits,proto3" json:"card_last_four_digits,omitempty"`
	// delivery tracking state of the card
	// this is used as conditional identifier to send comms related to  card delivery tracking
	DeliveryTrackingState provisioning.CardTrackingDeliveryState `protobuf:"varint,6,opt,name=delivery_tracking_state,json=deliveryTrackingState,proto3,enum=card.provisioning.CardTrackingDeliveryState" json:"delivery_tracking_state,omitempty"`
	// name of card holder
	CardHolderName *common.Name `protobuf:"bytes,7,opt,name=card_holder_name,json=cardHolderName,proto3" json:"card_holder_name,omitempty"`
	// card order request state will be populated only when card order success/failed related comms needs to be triggered
	// In case comms to be sent is not related to physical card order this will be UNSPECIFIED
	PhysicalCardOrderRequestState provisioning.RequestState `protobuf:"varint,8,opt,name=physical_card_order_request_state,json=physicalCardOrderRequestState,proto3,enum=card.provisioning.RequestState" json:"physical_card_order_request_state,omitempty"`
	// awb number of shipment
	// this will be empty string if comm to be sent is not related to delivery tracking
	Awb string `protobuf:"bytes,9,opt,name=awb,proto3" json:"awb,omitempty"`
	// delivery partner name
	// this will be empty string if comm to be sent is not related to delivery tracking
	Carrier string `protobuf:"bytes,10,opt,name=carrier,proto3" json:"carrier,omitempty"`
	// card printing vendor, this should be populated when we need to send delivery related comms and need to do some prior computation
	// which is dependent on printing vendor, currently is being used to send pin-code base tat in dc physical order success
	CardPrintingVendor provisioning.CardPrintingVendor `protobuf:"varint,11,opt,name=card_printing_vendor,json=cardPrintingVendor,proto3,enum=card.provisioning.CardPrintingVendor" json:"card_printing_vendor,omitempty"`
	// expected delivery date for physical card
	ExpectedDeliveryDate *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=expected_delivery_date,json=expectedDeliveryDate,proto3" json:"expected_delivery_date,omitempty"`
	// Types that are assignable to Data:
	//
	//	*ActionData_TxnActionData
	//	*ActionData_SwitchNotificationData
	//	*ActionData_ForexNotificationData
	Data isActionData_Data `protobuf_oneof:"data"`
}

func (x *ActionData) Reset() {
	*x = ActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_comms_action_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionData) ProtoMessage() {}

func (x *ActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_comms_action_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionData.ProtoReflect.Descriptor instead.
func (*ActionData) Descriptor() ([]byte, []int) {
	return file_api_card_comms_action_data_proto_rawDescGZIP(), []int{0}
}

func (x *ActionData) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ActionData) GetTxnAmount() *money.Money {
	if x != nil {
		return x.TxnAmount
	}
	return nil
}

func (x *ActionData) GetSwitchNotificationResponse() enums.SwitchNotificationResponse {
	if x != nil {
		return x.SwitchNotificationResponse
	}
	return enums.SwitchNotificationResponse(0)
}

func (x *ActionData) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *ActionData) GetCardLastFourDigits() string {
	if x != nil {
		return x.CardLastFourDigits
	}
	return ""
}

func (x *ActionData) GetDeliveryTrackingState() provisioning.CardTrackingDeliveryState {
	if x != nil {
		return x.DeliveryTrackingState
	}
	return provisioning.CardTrackingDeliveryState(0)
}

func (x *ActionData) GetCardHolderName() *common.Name {
	if x != nil {
		return x.CardHolderName
	}
	return nil
}

func (x *ActionData) GetPhysicalCardOrderRequestState() provisioning.RequestState {
	if x != nil {
		return x.PhysicalCardOrderRequestState
	}
	return provisioning.RequestState(0)
}

func (x *ActionData) GetAwb() string {
	if x != nil {
		return x.Awb
	}
	return ""
}

func (x *ActionData) GetCarrier() string {
	if x != nil {
		return x.Carrier
	}
	return ""
}

func (x *ActionData) GetCardPrintingVendor() provisioning.CardPrintingVendor {
	if x != nil {
		return x.CardPrintingVendor
	}
	return provisioning.CardPrintingVendor(0)
}

func (x *ActionData) GetExpectedDeliveryDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpectedDeliveryDate
	}
	return nil
}

func (m *ActionData) GetData() isActionData_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *ActionData) GetTxnActionData() *TxnActionData {
	if x, ok := x.GetData().(*ActionData_TxnActionData); ok {
		return x.TxnActionData
	}
	return nil
}

func (x *ActionData) GetSwitchNotificationData() *SwitchNotificationData {
	if x, ok := x.GetData().(*ActionData_SwitchNotificationData); ok {
		return x.SwitchNotificationData
	}
	return nil
}

func (x *ActionData) GetForexNotificationData() *ForexNotificationData {
	if x, ok := x.GetData().(*ActionData_ForexNotificationData); ok {
		return x.ForexNotificationData
	}
	return nil
}

type isActionData_Data interface {
	isActionData_Data()
}

type ActionData_TxnActionData struct {
	TxnActionData *TxnActionData `protobuf:"bytes,12,opt,name=txn_action_data,json=txnActionData,proto3,oneof"`
}

type ActionData_SwitchNotificationData struct {
	SwitchNotificationData *SwitchNotificationData `protobuf:"bytes,13,opt,name=switch_notification_data,json=switchNotificationData,proto3,oneof"`
}

type ActionData_ForexNotificationData struct {
	ForexNotificationData *ForexNotificationData `protobuf:"bytes,14,opt,name=forex_notification_data,json=forexNotificationData,proto3,oneof"`
}

func (*ActionData_TxnActionData) isActionData_Data() {}

func (*ActionData_SwitchNotificationData) isActionData_Data() {}

func (*ActionData_ForexNotificationData) isActionData_Data() {}

type ForexNotificationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=txn_time,json=txnTime,proto3" json:"txn_time,omitempty"`
}

func (x *ForexNotificationData) Reset() {
	*x = ForexNotificationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_comms_action_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForexNotificationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForexNotificationData) ProtoMessage() {}

func (x *ForexNotificationData) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_comms_action_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForexNotificationData.ProtoReflect.Descriptor instead.
func (*ForexNotificationData) Descriptor() ([]byte, []int) {
	return file_api_card_comms_action_data_proto_rawDescGZIP(), []int{1}
}

func (x *ForexNotificationData) GetTxnTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TxnTime
	}
	return nil
}

type TxnActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnAmount       *money.Money              `protobuf:"bytes,1,opt,name=txn_amount,json=txnAmount,proto3" json:"txn_amount,omitempty"`
	TxnCountry      string                    `protobuf:"bytes,2,opt,name=txn_country,json=txnCountry,proto3" json:"txn_country,omitempty"`
	OrderProvenance order.OrderProvenance     `protobuf:"varint,3,opt,name=order_provenance,json=orderProvenance,proto3,enum=order.OrderProvenance" json:"order_provenance,omitempty"`
	PaymentProtocol payment.PaymentProtocol   `protobuf:"varint,4,opt,name=payment_protocol,json=paymentProtocol,proto3,enum=order.payment.PaymentProtocol" json:"payment_protocol,omitempty"`
	TxnStatus       payment.TransactionStatus `protobuf:"varint,5,opt,name=txn_status,json=txnStatus,proto3,enum=order.payment.TransactionStatus" json:"txn_status,omitempty"`
	// Represents transaction limits on a card
	CardTxnsLimitData *control.CardLimitData `protobuf:"bytes,6,opt,name=card_txns_limit_data,json=cardTxnsLimitData,proto3" json:"card_txns_limit_data,omitempty"`
	// flag to be set by caller when txn is an international txn
	IsInternationalTxn bool `protobuf:"varint,7,opt,name=is_international_txn,json=isInternationalTxn,proto3" json:"is_international_txn,omitempty"`
}

func (x *TxnActionData) Reset() {
	*x = TxnActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_comms_action_data_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TxnActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxnActionData) ProtoMessage() {}

func (x *TxnActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_comms_action_data_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxnActionData.ProtoReflect.Descriptor instead.
func (*TxnActionData) Descriptor() ([]byte, []int) {
	return file_api_card_comms_action_data_proto_rawDescGZIP(), []int{2}
}

func (x *TxnActionData) GetTxnAmount() *money.Money {
	if x != nil {
		return x.TxnAmount
	}
	return nil
}

func (x *TxnActionData) GetTxnCountry() string {
	if x != nil {
		return x.TxnCountry
	}
	return ""
}

func (x *TxnActionData) GetOrderProvenance() order.OrderProvenance {
	if x != nil {
		return x.OrderProvenance
	}
	return order.OrderProvenance(0)
}

func (x *TxnActionData) GetPaymentProtocol() payment.PaymentProtocol {
	if x != nil {
		return x.PaymentProtocol
	}
	return payment.PaymentProtocol(0)
}

func (x *TxnActionData) GetTxnStatus() payment.TransactionStatus {
	if x != nil {
		return x.TxnStatus
	}
	return payment.TransactionStatus(0)
}

func (x *TxnActionData) GetCardTxnsLimitData() *control.CardLimitData {
	if x != nil {
		return x.CardTxnsLimitData
	}
	return nil
}

func (x *TxnActionData) GetIsInternationalTxn() bool {
	if x != nil {
		return x.IsInternationalTxn
	}
	return false
}

type SwitchNotificationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId                    string                           `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	CardId                     string                           `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	LastFourDigits             string                           `protobuf:"bytes,3,opt,name=last_four_digits,json=lastFourDigits,proto3" json:"last_four_digits,omitempty"`
	CardHolderName             *common.Name                     `protobuf:"bytes,4,opt,name=card_holder_name,json=cardHolderName,proto3" json:"card_holder_name,omitempty"`
	SwitchNotificationResponse enums.SwitchNotificationResponse `protobuf:"varint,5,opt,name=switch_notification_response,json=switchNotificationResponse,proto3,enum=card.enums.SwitchNotificationResponse" json:"switch_notification_response,omitempty"`
	// txn amount will be empty in case of non-financial txn notification
	TxnAmount      *money.Money                  `protobuf:"bytes,6,opt,name=txn_amount,json=txnAmount,proto3" json:"txn_amount,omitempty"`
	TxnTime        *timestamppb.Timestamp        `protobuf:"bytes,7,opt,name=txn_time,json=txnTime,proto3" json:"txn_time,omitempty"`
	TxnCountryCode string                        `protobuf:"bytes,8,opt,name=txn_country_code,json=txnCountryCode,proto3" json:"txn_country_code,omitempty"`
	TxnCategory    enums.CardTransactionCategory `protobuf:"varint,9,opt,name=txn_category,json=txnCategory,proto3,enum=card.enums.CardTransactionCategory" json:"txn_category,omitempty"`
	Merchant       string                        `protobuf:"bytes,10,opt,name=merchant,proto3" json:"merchant,omitempty"`
}

func (x *SwitchNotificationData) Reset() {
	*x = SwitchNotificationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_comms_action_data_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchNotificationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchNotificationData) ProtoMessage() {}

func (x *SwitchNotificationData) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_comms_action_data_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchNotificationData.ProtoReflect.Descriptor instead.
func (*SwitchNotificationData) Descriptor() ([]byte, []int) {
	return file_api_card_comms_action_data_proto_rawDescGZIP(), []int{3}
}

func (x *SwitchNotificationData) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *SwitchNotificationData) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *SwitchNotificationData) GetLastFourDigits() string {
	if x != nil {
		return x.LastFourDigits
	}
	return ""
}

func (x *SwitchNotificationData) GetCardHolderName() *common.Name {
	if x != nil {
		return x.CardHolderName
	}
	return nil
}

func (x *SwitchNotificationData) GetSwitchNotificationResponse() enums.SwitchNotificationResponse {
	if x != nil {
		return x.SwitchNotificationResponse
	}
	return enums.SwitchNotificationResponse(0)
}

func (x *SwitchNotificationData) GetTxnAmount() *money.Money {
	if x != nil {
		return x.TxnAmount
	}
	return nil
}

func (x *SwitchNotificationData) GetTxnTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TxnTime
	}
	return nil
}

func (x *SwitchNotificationData) GetTxnCountryCode() string {
	if x != nil {
		return x.TxnCountryCode
	}
	return ""
}

func (x *SwitchNotificationData) GetTxnCategory() enums.CardTransactionCategory {
	if x != nil {
		return x.TxnCategory
	}
	return enums.CardTransactionCategory(0)
}

func (x *SwitchNotificationData) GetMerchant() string {
	if x != nil {
		return x.Merchant
	}
	return ""
}

var File_api_card_comms_action_data_proto protoreflect.FileDescriptor

var file_api_card_comms_action_data_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73,
	0x2f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x1a, 0x21,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72,
	0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x86, 0x08, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0a, 0x74, 0x78,
	0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x09, 0x74, 0x78, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x68, 0x0a,
	0x1c, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x1a, 0x73, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x12, 0x31, 0x0a, 0x15, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x6f,
	0x75, 0x72, 0x5f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x63, 0x61, 0x72, 0x64, 0x4c, 0x61, 0x73, 0x74, 0x46, 0x6f, 0x75, 0x72, 0x44, 0x69, 0x67,
	0x69, 0x74, 0x73, 0x12, 0x64, 0x0a, 0x17, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x15, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x42, 0x0a, 0x10, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0e, 0x63,
	0x61, 0x72, 0x64, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x69, 0x0a,
	0x21, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x1d, 0x70, 0x68, 0x79, 0x73, 0x69,
	0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x77, 0x62, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x77, 0x62, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x12, 0x57, 0x0a, 0x14, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x69,
	0x6e, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x50, 0x72, 0x69, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x12, 0x63, 0x61, 0x72, 0x64, 0x50,
	0x72, 0x69, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x50, 0x0a,
	0x16, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x65, 0x78, 0x70, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x43, 0x0a, 0x0f, 0x74, 0x78, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x54, 0x78, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0d, 0x74, 0x78, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x5e, 0x0a, 0x18, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x73, 0x2e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x16, 0x73, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x5b, 0x0a, 0x17, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x5f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x15, 0x66, 0x6f, 0x72, 0x65,
	0x78, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x4e, 0x0a, 0x15, 0x46, 0x6f, 0x72,
	0x65, 0x78, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x35, 0x0a, 0x08, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x07, 0x74, 0x78, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xb2, 0x03, 0x0a, 0x0d, 0x54, 0x78,
	0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x31, 0x0a, 0x0a, 0x74,
	0x78, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x09, 0x74, 0x78, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x78, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x78, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x41, 0x0a, 0x10, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61,
	0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x49, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x0f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x3f, 0x0a,
	0x0a, 0x74, 0x78, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x09, 0x74, 0x78, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4c,
	0x0a, 0x14, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x78, 0x6e, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x11, 0x63, 0x61, 0x72, 0x64, 0x54,
	0x78, 0x6e, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x30, 0x0a, 0x14,
	0x69, 0x73, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x74, 0x78, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x54, 0x78, 0x6e, 0x22, 0x9c,
	0x04, 0x0a, 0x16, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x28, 0x0a,
	0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x6f, 0x75, 0x72, 0x5f, 0x64, 0x69, 0x67, 0x69, 0x74,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x46, 0x6f, 0x75,
	0x72, 0x44, 0x69, 0x67, 0x69, 0x74, 0x73, 0x12, 0x42, 0x0a, 0x10, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0e, 0x63, 0x61, 0x72,
	0x64, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x68, 0x0a, 0x1c, 0x73,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x1a, 0x73, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x74, 0x78, 0x6e, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x74,
	0x78, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x74, 0x78, 0x6e, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x74, 0x78, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x28, 0x0a, 0x10, 0x74, 0x78, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x78, 0x6e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x46, 0x0a, 0x0c, 0x74, 0x78, 0x6e,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72,
	0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x52, 0x0b, 0x74, 0x78, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x42, 0x4e, 0x0a,
	0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_comms_action_data_proto_rawDescOnce sync.Once
	file_api_card_comms_action_data_proto_rawDescData = file_api_card_comms_action_data_proto_rawDesc
)

func file_api_card_comms_action_data_proto_rawDescGZIP() []byte {
	file_api_card_comms_action_data_proto_rawDescOnce.Do(func() {
		file_api_card_comms_action_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_comms_action_data_proto_rawDescData)
	})
	return file_api_card_comms_action_data_proto_rawDescData
}

var file_api_card_comms_action_data_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_card_comms_action_data_proto_goTypes = []interface{}{
	(*ActionData)(nil),                          // 0: card.comms.ActionData
	(*ForexNotificationData)(nil),               // 1: card.comms.ForexNotificationData
	(*TxnActionData)(nil),                       // 2: card.comms.TxnActionData
	(*SwitchNotificationData)(nil),              // 3: card.comms.SwitchNotificationData
	(*money.Money)(nil),                         // 4: google.type.Money
	(enums.SwitchNotificationResponse)(0),       // 5: card.enums.SwitchNotificationResponse
	(provisioning.CardTrackingDeliveryState)(0), // 6: card.provisioning.CardTrackingDeliveryState
	(*common.Name)(nil),                         // 7: api.typesv2.common.Name
	(provisioning.RequestState)(0),              // 8: card.provisioning.RequestState
	(provisioning.CardPrintingVendor)(0),        // 9: card.provisioning.CardPrintingVendor
	(*timestamppb.Timestamp)(nil),               // 10: google.protobuf.Timestamp
	(order.OrderProvenance)(0),                  // 11: order.OrderProvenance
	(payment.PaymentProtocol)(0),                // 12: order.payment.PaymentProtocol
	(payment.TransactionStatus)(0),              // 13: order.payment.TransactionStatus
	(*control.CardLimitData)(nil),               // 14: card.control.CardLimitData
	(enums.CardTransactionCategory)(0),          // 15: card.enums.CardTransactionCategory
}
var file_api_card_comms_action_data_proto_depIdxs = []int32{
	4,  // 0: card.comms.ActionData.txn_amount:type_name -> google.type.Money
	5,  // 1: card.comms.ActionData.switch_notification_response:type_name -> card.enums.SwitchNotificationResponse
	6,  // 2: card.comms.ActionData.delivery_tracking_state:type_name -> card.provisioning.CardTrackingDeliveryState
	7,  // 3: card.comms.ActionData.card_holder_name:type_name -> api.typesv2.common.Name
	8,  // 4: card.comms.ActionData.physical_card_order_request_state:type_name -> card.provisioning.RequestState
	9,  // 5: card.comms.ActionData.card_printing_vendor:type_name -> card.provisioning.CardPrintingVendor
	10, // 6: card.comms.ActionData.expected_delivery_date:type_name -> google.protobuf.Timestamp
	2,  // 7: card.comms.ActionData.txn_action_data:type_name -> card.comms.TxnActionData
	3,  // 8: card.comms.ActionData.switch_notification_data:type_name -> card.comms.SwitchNotificationData
	1,  // 9: card.comms.ActionData.forex_notification_data:type_name -> card.comms.ForexNotificationData
	10, // 10: card.comms.ForexNotificationData.txn_time:type_name -> google.protobuf.Timestamp
	4,  // 11: card.comms.TxnActionData.txn_amount:type_name -> google.type.Money
	11, // 12: card.comms.TxnActionData.order_provenance:type_name -> order.OrderProvenance
	12, // 13: card.comms.TxnActionData.payment_protocol:type_name -> order.payment.PaymentProtocol
	13, // 14: card.comms.TxnActionData.txn_status:type_name -> order.payment.TransactionStatus
	14, // 15: card.comms.TxnActionData.card_txns_limit_data:type_name -> card.control.CardLimitData
	7,  // 16: card.comms.SwitchNotificationData.card_holder_name:type_name -> api.typesv2.common.Name
	5,  // 17: card.comms.SwitchNotificationData.switch_notification_response:type_name -> card.enums.SwitchNotificationResponse
	4,  // 18: card.comms.SwitchNotificationData.txn_amount:type_name -> google.type.Money
	10, // 19: card.comms.SwitchNotificationData.txn_time:type_name -> google.protobuf.Timestamp
	15, // 20: card.comms.SwitchNotificationData.txn_category:type_name -> card.enums.CardTransactionCategory
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_api_card_comms_action_data_proto_init() }
func file_api_card_comms_action_data_proto_init() {
	if File_api_card_comms_action_data_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_comms_action_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_comms_action_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForexNotificationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_comms_action_data_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TxnActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_comms_action_data_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SwitchNotificationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_card_comms_action_data_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*ActionData_TxnActionData)(nil),
		(*ActionData_SwitchNotificationData)(nil),
		(*ActionData_ForexNotificationData)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_comms_action_data_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_comms_action_data_proto_goTypes,
		DependencyIndexes: file_api_card_comms_action_data_proto_depIdxs,
		MessageInfos:      file_api_card_comms_action_data_proto_msgTypes,
	}.Build()
	File_api_card_comms_action_data_proto = out.File
	file_api_card_comms_action_data_proto_rawDesc = nil
	file_api_card_comms_action_data_proto_goTypes = nil
	file_api_card_comms_action_data_proto_depIdxs = nil
}
