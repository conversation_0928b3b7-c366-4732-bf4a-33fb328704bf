// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/comms/action_data.proto

package comms

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/card/enums"

	order "github.com/epifi/gamma/api/order"

	payment "github.com/epifi/gamma/api/order/payment"

	provisioning "github.com/epifi/gamma/api/card/provisioning"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.SwitchNotificationResponse(0)

	_ = order.OrderProvenance(0)

	_ = payment.PaymentProtocol(0)

	_ = provisioning.CardTrackingDeliveryState(0)
)

// Validate checks the field values on ActionData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActionData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ActionDataMultiError, or
// nil if none found.
func (m *ActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *ActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetTxnAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionDataValidationError{
					field:  "TxnAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionDataValidationError{
					field:  "TxnAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionDataValidationError{
				field:  "TxnAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SwitchNotificationResponse

	// no validation rules for CardId

	// no validation rules for CardLastFourDigits

	// no validation rules for DeliveryTrackingState

	if all {
		switch v := interface{}(m.GetCardHolderName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionDataValidationError{
					field:  "CardHolderName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionDataValidationError{
					field:  "CardHolderName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardHolderName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionDataValidationError{
				field:  "CardHolderName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PhysicalCardOrderRequestState

	// no validation rules for Awb

	// no validation rules for Carrier

	// no validation rules for CardPrintingVendor

	if all {
		switch v := interface{}(m.GetExpectedDeliveryDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionDataValidationError{
					field:  "ExpectedDeliveryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionDataValidationError{
					field:  "ExpectedDeliveryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectedDeliveryDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionDataValidationError{
				field:  "ExpectedDeliveryDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Data.(type) {
	case *ActionData_TxnActionData:
		if v == nil {
			err := ActionDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTxnActionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "TxnActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "TxnActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTxnActionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionDataValidationError{
					field:  "TxnActionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionData_SwitchNotificationData:
		if v == nil {
			err := ActionDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSwitchNotificationData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "SwitchNotificationData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "SwitchNotificationData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSwitchNotificationData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionDataValidationError{
					field:  "SwitchNotificationData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionData_ForexNotificationData:
		if v == nil {
			err := ActionDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetForexNotificationData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "ForexNotificationData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "ForexNotificationData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetForexNotificationData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionDataValidationError{
					field:  "ForexNotificationData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ActionDataMultiError(errors)
	}

	return nil
}

// ActionDataMultiError is an error wrapping multiple validation errors
// returned by ActionData.ValidateAll() if the designated constraints aren't met.
type ActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionDataMultiError) AllErrors() []error { return m }

// ActionDataValidationError is the validation error returned by
// ActionData.Validate if the designated constraints aren't met.
type ActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionDataValidationError) ErrorName() string { return "ActionDataValidationError" }

// Error satisfies the builtin error interface
func (e ActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionDataValidationError{}

// Validate checks the field values on ForexNotificationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForexNotificationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForexNotificationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForexNotificationDataMultiError, or nil if none found.
func (m *ForexNotificationData) ValidateAll() error {
	return m.validate(true)
}

func (m *ForexNotificationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTxnTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForexNotificationDataValidationError{
					field:  "TxnTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForexNotificationDataValidationError{
					field:  "TxnTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForexNotificationDataValidationError{
				field:  "TxnTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ForexNotificationDataMultiError(errors)
	}

	return nil
}

// ForexNotificationDataMultiError is an error wrapping multiple validation
// errors returned by ForexNotificationData.ValidateAll() if the designated
// constraints aren't met.
type ForexNotificationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForexNotificationDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForexNotificationDataMultiError) AllErrors() []error { return m }

// ForexNotificationDataValidationError is the validation error returned by
// ForexNotificationData.Validate if the designated constraints aren't met.
type ForexNotificationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForexNotificationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForexNotificationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForexNotificationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForexNotificationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForexNotificationDataValidationError) ErrorName() string {
	return "ForexNotificationDataValidationError"
}

// Error satisfies the builtin error interface
func (e ForexNotificationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForexNotificationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForexNotificationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForexNotificationDataValidationError{}

// Validate checks the field values on TxnActionData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TxnActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TxnActionData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TxnActionDataMultiError, or
// nil if none found.
func (m *TxnActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *TxnActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTxnAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TxnActionDataValidationError{
					field:  "TxnAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TxnActionDataValidationError{
					field:  "TxnAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TxnActionDataValidationError{
				field:  "TxnAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TxnCountry

	// no validation rules for OrderProvenance

	// no validation rules for PaymentProtocol

	// no validation rules for TxnStatus

	if all {
		switch v := interface{}(m.GetCardTxnsLimitData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TxnActionDataValidationError{
					field:  "CardTxnsLimitData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TxnActionDataValidationError{
					field:  "CardTxnsLimitData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardTxnsLimitData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TxnActionDataValidationError{
				field:  "CardTxnsLimitData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsInternationalTxn

	if len(errors) > 0 {
		return TxnActionDataMultiError(errors)
	}

	return nil
}

// TxnActionDataMultiError is an error wrapping multiple validation errors
// returned by TxnActionData.ValidateAll() if the designated constraints
// aren't met.
type TxnActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TxnActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TxnActionDataMultiError) AllErrors() []error { return m }

// TxnActionDataValidationError is the validation error returned by
// TxnActionData.Validate if the designated constraints aren't met.
type TxnActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TxnActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TxnActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TxnActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TxnActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TxnActionDataValidationError) ErrorName() string { return "TxnActionDataValidationError" }

// Error satisfies the builtin error interface
func (e TxnActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTxnActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TxnActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TxnActionDataValidationError{}

// Validate checks the field values on SwitchNotificationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SwitchNotificationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SwitchNotificationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SwitchNotificationDataMultiError, or nil if none found.
func (m *SwitchNotificationData) ValidateAll() error {
	return m.validate(true)
}

func (m *SwitchNotificationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for CardId

	// no validation rules for LastFourDigits

	if all {
		switch v := interface{}(m.GetCardHolderName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwitchNotificationDataValidationError{
					field:  "CardHolderName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwitchNotificationDataValidationError{
					field:  "CardHolderName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardHolderName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwitchNotificationDataValidationError{
				field:  "CardHolderName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SwitchNotificationResponse

	if all {
		switch v := interface{}(m.GetTxnAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwitchNotificationDataValidationError{
					field:  "TxnAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwitchNotificationDataValidationError{
					field:  "TxnAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwitchNotificationDataValidationError{
				field:  "TxnAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTxnTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwitchNotificationDataValidationError{
					field:  "TxnTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwitchNotificationDataValidationError{
					field:  "TxnTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwitchNotificationDataValidationError{
				field:  "TxnTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TxnCountryCode

	// no validation rules for TxnCategory

	// no validation rules for Merchant

	if len(errors) > 0 {
		return SwitchNotificationDataMultiError(errors)
	}

	return nil
}

// SwitchNotificationDataMultiError is an error wrapping multiple validation
// errors returned by SwitchNotificationData.ValidateAll() if the designated
// constraints aren't met.
type SwitchNotificationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SwitchNotificationDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SwitchNotificationDataMultiError) AllErrors() []error { return m }

// SwitchNotificationDataValidationError is the validation error returned by
// SwitchNotificationData.Validate if the designated constraints aren't met.
type SwitchNotificationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SwitchNotificationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SwitchNotificationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SwitchNotificationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SwitchNotificationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SwitchNotificationDataValidationError) ErrorName() string {
	return "SwitchNotificationDataValidationError"
}

// Error satisfies the builtin error interface
func (e SwitchNotificationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSwitchNotificationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SwitchNotificationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SwitchNotificationDataValidationError{}
