// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/activity/process_amc_eligible_users/activity.proto

package processamceligibleusers

import (
	activity "github.com/epifi/be-common/api/celestial/activity"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetAmcUserBaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// s3 path where the file generated through report generation (analytics db) is
	// persisted. We will use this file to apply filters on user row list.
	S3Path      string     `protobuf:"bytes,2,opt,name=s3_path,json=s3Path,proto3" json:"s3_path,omitempty"`
	FileGenDate *date.Date `protobuf:"bytes,3,opt,name=file_gen_date,json=fileGenDate,proto3" json:"file_gen_date,omitempty"`
}

func (x *GetAmcUserBaseRequest) Reset() {
	*x = GetAmcUserBaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAmcUserBaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAmcUserBaseRequest) ProtoMessage() {}

func (x *GetAmcUserBaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAmcUserBaseRequest.ProtoReflect.Descriptor instead.
func (*GetAmcUserBaseRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescGZIP(), []int{0}
}

func (x *GetAmcUserBaseRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *GetAmcUserBaseRequest) GetS3Path() string {
	if x != nil {
		return x.S3Path
	}
	return ""
}

func (x *GetAmcUserBaseRequest) GetFileGenDate() *date.Date {
	if x != nil {
		return x.FileGenDate
	}
	return nil
}

type GetAmcUserBaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	ResponseHeader            *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	CardIds                   []string                 `protobuf:"bytes,2,rep,name=card_ids,json=cardIds,proto3" json:"card_ids,omitempty"`
	EligibleUsersS3PathFormat string                   `protobuf:"bytes,3,opt,name=eligible_users_s3_path_format,json=eligibleUsersS3PathFormat,proto3" json:"eligible_users_s3_path_format,omitempty"`
	FailedUsersS3PathFormat   string                   `protobuf:"bytes,4,opt,name=failed_users_s3_path_format,json=failedUsersS3PathFormat,proto3" json:"failed_users_s3_path_format,omitempty"`
	BatchSize                 int32                    `protobuf:"varint,5,opt,name=batch_size,json=batchSize,proto3" json:"batch_size,omitempty"`
}

func (x *GetAmcUserBaseResponse) Reset() {
	*x = GetAmcUserBaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAmcUserBaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAmcUserBaseResponse) ProtoMessage() {}

func (x *GetAmcUserBaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAmcUserBaseResponse.ProtoReflect.Descriptor instead.
func (*GetAmcUserBaseResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescGZIP(), []int{1}
}

func (x *GetAmcUserBaseResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *GetAmcUserBaseResponse) GetCardIds() []string {
	if x != nil {
		return x.CardIds
	}
	return nil
}

func (x *GetAmcUserBaseResponse) GetEligibleUsersS3PathFormat() string {
	if x != nil {
		return x.EligibleUsersS3PathFormat
	}
	return ""
}

func (x *GetAmcUserBaseResponse) GetFailedUsersS3PathFormat() string {
	if x != nil {
		return x.FailedUsersS3PathFormat
	}
	return ""
}

func (x *GetAmcUserBaseResponse) GetBatchSize() int32 {
	if x != nil {
		return x.BatchSize
	}
	return 0
}

type ProcessAmcUserBatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader      *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	CardIds            []string                `protobuf:"bytes,2,rep,name=card_ids,json=cardIds,proto3" json:"card_ids,omitempty"`
	BatchS3PathDetails *BatchS3PathDetail      `protobuf:"bytes,3,opt,name=batch_s3_path_details,json=batchS3PathDetails,proto3" json:"batch_s3_path_details,omitempty"`
	FileGenDate        *date.Date              `protobuf:"bytes,4,opt,name=file_gen_date,json=fileGenDate,proto3" json:"file_gen_date,omitempty"`
	BatchNumber        int32                   `protobuf:"varint,5,opt,name=batch_number,json=batchNumber,proto3" json:"batch_number,omitempty"`
	// amc user base s3 file path
	UserBaseFileS3Path string `protobuf:"bytes,6,opt,name=user_base_file_s3_path,json=userBaseFileS3Path,proto3" json:"user_base_file_s3_path,omitempty"`
}

func (x *ProcessAmcUserBatchRequest) Reset() {
	*x = ProcessAmcUserBatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessAmcUserBatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessAmcUserBatchRequest) ProtoMessage() {}

func (x *ProcessAmcUserBatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessAmcUserBatchRequest.ProtoReflect.Descriptor instead.
func (*ProcessAmcUserBatchRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessAmcUserBatchRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessAmcUserBatchRequest) GetCardIds() []string {
	if x != nil {
		return x.CardIds
	}
	return nil
}

func (x *ProcessAmcUserBatchRequest) GetBatchS3PathDetails() *BatchS3PathDetail {
	if x != nil {
		return x.BatchS3PathDetails
	}
	return nil
}

func (x *ProcessAmcUserBatchRequest) GetFileGenDate() *date.Date {
	if x != nil {
		return x.FileGenDate
	}
	return nil
}

func (x *ProcessAmcUserBatchRequest) GetBatchNumber() int32 {
	if x != nil {
		return x.BatchNumber
	}
	return 0
}

func (x *ProcessAmcUserBatchRequest) GetUserBaseFileS3Path() string {
	if x != nil {
		return x.UserBaseFileS3Path
	}
	return ""
}

type ProcessAmcUserBatchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessAmcUserBatchResponse) Reset() {
	*x = ProcessAmcUserBatchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessAmcUserBatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessAmcUserBatchResponse) ProtoMessage() {}

func (x *ProcessAmcUserBatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessAmcUserBatchResponse.ProtoReflect.Descriptor instead.
func (*ProcessAmcUserBatchResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessAmcUserBatchResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type CreateAndUploadAmcUsersBatchFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader      *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	CardIds            []string                `protobuf:"bytes,2,rep,name=card_ids,json=cardIds,proto3" json:"card_ids,omitempty"`
	BatchS3PathDetails *BatchS3PathDetail      `protobuf:"bytes,3,opt,name=batch_s3_path_details,json=batchS3PathDetails,proto3" json:"batch_s3_path_details,omitempty"`
	FileGenDate        *date.Date              `protobuf:"bytes,4,opt,name=file_gen_date,json=fileGenDate,proto3" json:"file_gen_date,omitempty"`
}

func (x *CreateAndUploadAmcUsersBatchFileRequest) Reset() {
	*x = CreateAndUploadAmcUsersBatchFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAndUploadAmcUsersBatchFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAndUploadAmcUsersBatchFileRequest) ProtoMessage() {}

func (x *CreateAndUploadAmcUsersBatchFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAndUploadAmcUsersBatchFileRequest.ProtoReflect.Descriptor instead.
func (*CreateAndUploadAmcUsersBatchFileRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescGZIP(), []int{4}
}

func (x *CreateAndUploadAmcUsersBatchFileRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CreateAndUploadAmcUsersBatchFileRequest) GetCardIds() []string {
	if x != nil {
		return x.CardIds
	}
	return nil
}

func (x *CreateAndUploadAmcUsersBatchFileRequest) GetBatchS3PathDetails() *BatchS3PathDetail {
	if x != nil {
		return x.BatchS3PathDetails
	}
	return nil
}

func (x *CreateAndUploadAmcUsersBatchFileRequest) GetFileGenDate() *date.Date {
	if x != nil {
		return x.FileGenDate
	}
	return nil
}

type CreateAndUploadAmcUsersBatchFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *CreateAndUploadAmcUsersBatchFileResponse) Reset() {
	*x = CreateAndUploadAmcUsersBatchFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAndUploadAmcUsersBatchFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAndUploadAmcUsersBatchFileResponse) ProtoMessage() {}

func (x *CreateAndUploadAmcUsersBatchFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAndUploadAmcUsersBatchFileResponse.ProtoReflect.Descriptor instead.
func (*CreateAndUploadAmcUsersBatchFileResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescGZIP(), []int{5}
}

func (x *CreateAndUploadAmcUsersBatchFileResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type PostProcessAmcFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader      *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	BatchS3PathDetails []*BatchS3PathDetail    `protobuf:"bytes,2,rep,name=batch_s3_path_details,json=batchS3PathDetails,proto3" json:"batch_s3_path_details,omitempty"`
	// file gen date on which the file is generated
	FileGenDate *date.Date `protobuf:"bytes,4,opt,name=file_gen_date,json=fileGenDate,proto3" json:"file_gen_date,omitempty"`
}

func (x *PostProcessAmcFileRequest) Reset() {
	*x = PostProcessAmcFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostProcessAmcFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostProcessAmcFileRequest) ProtoMessage() {}

func (x *PostProcessAmcFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostProcessAmcFileRequest.ProtoReflect.Descriptor instead.
func (*PostProcessAmcFileRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescGZIP(), []int{6}
}

func (x *PostProcessAmcFileRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *PostProcessAmcFileRequest) GetBatchS3PathDetails() []*BatchS3PathDetail {
	if x != nil {
		return x.BatchS3PathDetails
	}
	return nil
}

func (x *PostProcessAmcFileRequest) GetFileGenDate() *date.Date {
	if x != nil {
		return x.FileGenDate
	}
	return nil
}

type PostProcessAmcFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *PostProcessAmcFileResponse) Reset() {
	*x = PostProcessAmcFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostProcessAmcFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostProcessAmcFileResponse) ProtoMessage() {}

func (x *PostProcessAmcFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostProcessAmcFileResponse.ProtoReflect.Descriptor instead.
func (*PostProcessAmcFileResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescGZIP(), []int{7}
}

func (x *PostProcessAmcFileResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type BatchS3PathDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// s3 file path where the new file is uploaded and saved for post-processing
	// we will send email to federal bank and upload the file to slack for this.
	EligibleUsersS3Path string `protobuf:"bytes,1,opt,name=eligible_users_s3_path,json=eligibleUsersS3Path,proto3" json:"eligible_users_s3_path,omitempty"`
	// s3 path of the file for which we failed to process amc charges. This could include both
	// transient and permanent failure. we will send this file to slack for manual intervention
	FailedUsersS3Path string `protobuf:"bytes,2,opt,name=failed_users_s3_path,json=failedUsersS3Path,proto3" json:"failed_users_s3_path,omitempty"`
	// batch number for which we are processing current file
	BatchNumber int32 `protobuf:"varint,3,opt,name=batch_number,json=batchNumber,proto3" json:"batch_number,omitempty"`
}

func (x *BatchS3PathDetail) Reset() {
	*x = BatchS3PathDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchS3PathDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchS3PathDetail) ProtoMessage() {}

func (x *BatchS3PathDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchS3PathDetail.ProtoReflect.Descriptor instead.
func (*BatchS3PathDetail) Descriptor() ([]byte, []int) {
	return file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescGZIP(), []int{8}
}

func (x *BatchS3PathDetail) GetEligibleUsersS3Path() string {
	if x != nil {
		return x.EligibleUsersS3Path
	}
	return ""
}

func (x *BatchS3PathDetail) GetFailedUsersS3Path() string {
	if x != nil {
		return x.FailedUsersS3Path
	}
	return ""
}

func (x *BatchS3PathDetail) GetBatchNumber() int32 {
	if x != nil {
		return x.BatchNumber
	}
	return 0
}

type GetAmcBatchParamsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *GetAmcBatchParamsRequest) Reset() {
	*x = GetAmcBatchParamsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAmcBatchParamsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAmcBatchParamsRequest) ProtoMessage() {}

func (x *GetAmcBatchParamsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAmcBatchParamsRequest.ProtoReflect.Descriptor instead.
func (*GetAmcBatchParamsRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescGZIP(), []int{9}
}

func (x *GetAmcBatchParamsRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type GetAmcBatchParamsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	BatchSize      int32                    `protobuf:"varint,2,opt,name=batch_size,json=batchSize,proto3" json:"batch_size,omitempty"`
}

func (x *GetAmcBatchParamsResponse) Reset() {
	*x = GetAmcBatchParamsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAmcBatchParamsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAmcBatchParamsResponse) ProtoMessage() {}

func (x *GetAmcBatchParamsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAmcBatchParamsResponse.ProtoReflect.Descriptor instead.
func (*GetAmcBatchParamsResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescGZIP(), []int{10}
}

func (x *GetAmcBatchParamsResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *GetAmcBatchParamsResponse) GetBatchSize() int32 {
	if x != nil {
		return x.BatchSize
	}
	return 0
}

var File_api_card_activity_process_amc_eligible_users_activity_proto protoreflect.FileDescriptor

var file_api_card_activity_process_amc_eligible_users_activity_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x6d, 0x63, 0x5f,
	0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x25, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x61, 0x6d, 0x63, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74,
	0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xb1, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x6d, 0x63, 0x55, 0x73, 0x65, 0x72,
	0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x33, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x33, 0x50, 0x61, 0x74, 0x68, 0x12, 0x35,
	0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x67, 0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x47, 0x65,
	0x6e, 0x44, 0x61, 0x74, 0x65, 0x22, 0x9f, 0x02, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x6d, 0x63,
	0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a,
	0x08, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x73, 0x12, 0x40, 0x0a, 0x1d, 0x65, 0x6c, 0x69, 0x67,
	0x69, 0x62, 0x6c, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x73, 0x33, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x19, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x53, 0x33,
	0x50, 0x61, 0x74, 0x68, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x3c, 0x0a, 0x1b, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x73, 0x33, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x17, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73, 0x53, 0x33, 0x50, 0x61,
	0x74, 0x68, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x62, 0x61,
	0x74, 0x63, 0x68, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xfc, 0x02, 0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x41, 0x6d, 0x63, 0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x73, 0x12, 0x6b, 0x0a, 0x15, 0x62,
	0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x33, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x61, 0x6d, 0x63, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x75, 0x73, 0x65,
	0x72, 0x73, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x33, 0x50, 0x61, 0x74, 0x68, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x12, 0x62, 0x61, 0x74, 0x63, 0x68, 0x53, 0x33, 0x50, 0x61, 0x74,
	0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x35, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x67, 0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x47, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x62, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x32, 0x0a, 0x16, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x33, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x75, 0x73, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x53, 0x33, 0x50, 0x61, 0x74, 0x68, 0x22, 0x6a, 0x0a, 0x1b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x41, 0x6d, 0x63, 0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x22, 0xb2, 0x02, 0x0a, 0x27, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x6d, 0x63, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48,
	0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69,
	0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x72, 0x64,
	0x49, 0x64, 0x73, 0x12, 0x6b, 0x0a, 0x15, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x33, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x61, 0x6d, 0x63, 0x65, 0x6c, 0x69,
	0x67, 0x69, 0x62, 0x6c, 0x65, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x53, 0x33, 0x50, 0x61, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x12, 0x62, 0x61,
	0x74, 0x63, 0x68, 0x53, 0x33, 0x50, 0x61, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x35, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x67, 0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65,
	0x47, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x22, 0x77, 0x0a, 0x28, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x6e, 0x64, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x6d, 0x63, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63,
	0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x22, 0x89, 0x02, 0x0a, 0x19, 0x50, 0x6f, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x41, 0x6d, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48,
	0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69,
	0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x6b, 0x0a, 0x15, 0x62, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x73, 0x33, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x61,
	0x6d, 0x63, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x33, 0x50, 0x61, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x12, 0x62, 0x61, 0x74, 0x63, 0x68, 0x53, 0x33, 0x50, 0x61, 0x74, 0x68, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x35, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x67, 0x65,
	0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x0b, 0x66, 0x69, 0x6c, 0x65, 0x47, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x22, 0x69, 0x0a, 0x1a,
	0x50, 0x6f, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x6d, 0x63, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x9c, 0x01, 0x0a, 0x11, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x53, 0x33, 0x50, 0x61, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x33, 0x0a,
	0x16, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x5f,
	0x73, 0x33, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x65,
	0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x53, 0x33, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x2f, 0x0a, 0x14, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65,
	0x72, 0x73, 0x5f, 0x73, 0x33, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73, 0x53, 0x33, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x64, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x6d, 0x63,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x87, 0x01, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x41, 0x6d, 0x63, 0x42, 0x61, 0x74, 0x63, 0x68, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x62, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x69, 0x7a, 0x65, 0x42, 0x84, 0x01, 0x0a, 0x40, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x61, 0x6d, 0x63, 0x65, 0x6c,
	0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x75, 0x73, 0x65, 0x72, 0x73, 0x5a, 0x40, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x61, 0x6d, 0x63,
	0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x75, 0x73, 0x65, 0x72, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescOnce sync.Once
	file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescData = file_api_card_activity_process_amc_eligible_users_activity_proto_rawDesc
)

func file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescGZIP() []byte {
	file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescOnce.Do(func() {
		file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescData)
	})
	return file_api_card_activity_process_amc_eligible_users_activity_proto_rawDescData
}

var file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_card_activity_process_amc_eligible_users_activity_proto_goTypes = []interface{}{
	(*GetAmcUserBaseRequest)(nil),                    // 0: card.activity.processamceligibleusers.GetAmcUserBaseRequest
	(*GetAmcUserBaseResponse)(nil),                   // 1: card.activity.processamceligibleusers.GetAmcUserBaseResponse
	(*ProcessAmcUserBatchRequest)(nil),               // 2: card.activity.processamceligibleusers.ProcessAmcUserBatchRequest
	(*ProcessAmcUserBatchResponse)(nil),              // 3: card.activity.processamceligibleusers.ProcessAmcUserBatchResponse
	(*CreateAndUploadAmcUsersBatchFileRequest)(nil),  // 4: card.activity.processamceligibleusers.CreateAndUploadAmcUsersBatchFileRequest
	(*CreateAndUploadAmcUsersBatchFileResponse)(nil), // 5: card.activity.processamceligibleusers.CreateAndUploadAmcUsersBatchFileResponse
	(*PostProcessAmcFileRequest)(nil),                // 6: card.activity.processamceligibleusers.PostProcessAmcFileRequest
	(*PostProcessAmcFileResponse)(nil),               // 7: card.activity.processamceligibleusers.PostProcessAmcFileResponse
	(*BatchS3PathDetail)(nil),                        // 8: card.activity.processamceligibleusers.BatchS3PathDetail
	(*GetAmcBatchParamsRequest)(nil),                 // 9: card.activity.processamceligibleusers.GetAmcBatchParamsRequest
	(*GetAmcBatchParamsResponse)(nil),                // 10: card.activity.processamceligibleusers.GetAmcBatchParamsResponse
	(*activity.RequestHeader)(nil),                   // 11: celestial.activity.RequestHeader
	(*date.Date)(nil),                                // 12: google.type.Date
	(*activity.ResponseHeader)(nil),                  // 13: celestial.activity.ResponseHeader
}
var file_api_card_activity_process_amc_eligible_users_activity_proto_depIdxs = []int32{
	11, // 0: card.activity.processamceligibleusers.GetAmcUserBaseRequest.request_header:type_name -> celestial.activity.RequestHeader
	12, // 1: card.activity.processamceligibleusers.GetAmcUserBaseRequest.file_gen_date:type_name -> google.type.Date
	13, // 2: card.activity.processamceligibleusers.GetAmcUserBaseResponse.response_header:type_name -> celestial.activity.ResponseHeader
	11, // 3: card.activity.processamceligibleusers.ProcessAmcUserBatchRequest.request_header:type_name -> celestial.activity.RequestHeader
	8,  // 4: card.activity.processamceligibleusers.ProcessAmcUserBatchRequest.batch_s3_path_details:type_name -> card.activity.processamceligibleusers.BatchS3PathDetail
	12, // 5: card.activity.processamceligibleusers.ProcessAmcUserBatchRequest.file_gen_date:type_name -> google.type.Date
	13, // 6: card.activity.processamceligibleusers.ProcessAmcUserBatchResponse.response_header:type_name -> celestial.activity.ResponseHeader
	11, // 7: card.activity.processamceligibleusers.CreateAndUploadAmcUsersBatchFileRequest.request_header:type_name -> celestial.activity.RequestHeader
	8,  // 8: card.activity.processamceligibleusers.CreateAndUploadAmcUsersBatchFileRequest.batch_s3_path_details:type_name -> card.activity.processamceligibleusers.BatchS3PathDetail
	12, // 9: card.activity.processamceligibleusers.CreateAndUploadAmcUsersBatchFileRequest.file_gen_date:type_name -> google.type.Date
	13, // 10: card.activity.processamceligibleusers.CreateAndUploadAmcUsersBatchFileResponse.response_header:type_name -> celestial.activity.ResponseHeader
	11, // 11: card.activity.processamceligibleusers.PostProcessAmcFileRequest.request_header:type_name -> celestial.activity.RequestHeader
	8,  // 12: card.activity.processamceligibleusers.PostProcessAmcFileRequest.batch_s3_path_details:type_name -> card.activity.processamceligibleusers.BatchS3PathDetail
	12, // 13: card.activity.processamceligibleusers.PostProcessAmcFileRequest.file_gen_date:type_name -> google.type.Date
	13, // 14: card.activity.processamceligibleusers.PostProcessAmcFileResponse.response_header:type_name -> celestial.activity.ResponseHeader
	11, // 15: card.activity.processamceligibleusers.GetAmcBatchParamsRequest.request_header:type_name -> celestial.activity.RequestHeader
	13, // 16: card.activity.processamceligibleusers.GetAmcBatchParamsResponse.response_header:type_name -> celestial.activity.ResponseHeader
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_api_card_activity_process_amc_eligible_users_activity_proto_init() }
func file_api_card_activity_process_amc_eligible_users_activity_proto_init() {
	if File_api_card_activity_process_amc_eligible_users_activity_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAmcUserBaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAmcUserBaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessAmcUserBatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessAmcUserBatchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAndUploadAmcUsersBatchFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAndUploadAmcUsersBatchFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostProcessAmcFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostProcessAmcFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchS3PathDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAmcBatchParamsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAmcBatchParamsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_activity_process_amc_eligible_users_activity_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_activity_process_amc_eligible_users_activity_proto_goTypes,
		DependencyIndexes: file_api_card_activity_process_amc_eligible_users_activity_proto_depIdxs,
		MessageInfos:      file_api_card_activity_process_amc_eligible_users_activity_proto_msgTypes,
	}.Build()
	File_api_card_activity_process_amc_eligible_users_activity_proto = out.File
	file_api_card_activity_process_amc_eligible_users_activity_proto_rawDesc = nil
	file_api_card_activity_process_amc_eligible_users_activity_proto_goTypes = nil
	file_api_card_activity_process_amc_eligible_users_activity_proto_depIdxs = nil
}
