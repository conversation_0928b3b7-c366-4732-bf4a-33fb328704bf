// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/activity/process_amc_eligible_users/activity.proto

package processamceligibleusers

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetAmcUserBaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAmcUserBaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAmcUserBaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAmcUserBaseRequestMultiError, or nil if none found.
func (m *GetAmcUserBaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAmcUserBaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAmcUserBaseRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAmcUserBaseRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAmcUserBaseRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for S3Path

	if all {
		switch v := interface{}(m.GetFileGenDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAmcUserBaseRequestValidationError{
					field:  "FileGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAmcUserBaseRequestValidationError{
					field:  "FileGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileGenDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAmcUserBaseRequestValidationError{
				field:  "FileGenDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAmcUserBaseRequestMultiError(errors)
	}

	return nil
}

// GetAmcUserBaseRequestMultiError is an error wrapping multiple validation
// errors returned by GetAmcUserBaseRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAmcUserBaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAmcUserBaseRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAmcUserBaseRequestMultiError) AllErrors() []error { return m }

// GetAmcUserBaseRequestValidationError is the validation error returned by
// GetAmcUserBaseRequest.Validate if the designated constraints aren't met.
type GetAmcUserBaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAmcUserBaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAmcUserBaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAmcUserBaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAmcUserBaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAmcUserBaseRequestValidationError) ErrorName() string {
	return "GetAmcUserBaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAmcUserBaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAmcUserBaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAmcUserBaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAmcUserBaseRequestValidationError{}

// Validate checks the field values on GetAmcUserBaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAmcUserBaseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAmcUserBaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAmcUserBaseResponseMultiError, or nil if none found.
func (m *GetAmcUserBaseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAmcUserBaseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAmcUserBaseResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAmcUserBaseResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAmcUserBaseResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EligibleUsersS3PathFormat

	// no validation rules for FailedUsersS3PathFormat

	// no validation rules for BatchSize

	if len(errors) > 0 {
		return GetAmcUserBaseResponseMultiError(errors)
	}

	return nil
}

// GetAmcUserBaseResponseMultiError is an error wrapping multiple validation
// errors returned by GetAmcUserBaseResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAmcUserBaseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAmcUserBaseResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAmcUserBaseResponseMultiError) AllErrors() []error { return m }

// GetAmcUserBaseResponseValidationError is the validation error returned by
// GetAmcUserBaseResponse.Validate if the designated constraints aren't met.
type GetAmcUserBaseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAmcUserBaseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAmcUserBaseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAmcUserBaseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAmcUserBaseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAmcUserBaseResponseValidationError) ErrorName() string {
	return "GetAmcUserBaseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAmcUserBaseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAmcUserBaseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAmcUserBaseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAmcUserBaseResponseValidationError{}

// Validate checks the field values on ProcessAmcUserBatchRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessAmcUserBatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessAmcUserBatchRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessAmcUserBatchRequestMultiError, or nil if none found.
func (m *ProcessAmcUserBatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessAmcUserBatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessAmcUserBatchRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessAmcUserBatchRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessAmcUserBatchRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBatchS3PathDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessAmcUserBatchRequestValidationError{
					field:  "BatchS3PathDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessAmcUserBatchRequestValidationError{
					field:  "BatchS3PathDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBatchS3PathDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessAmcUserBatchRequestValidationError{
				field:  "BatchS3PathDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFileGenDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessAmcUserBatchRequestValidationError{
					field:  "FileGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessAmcUserBatchRequestValidationError{
					field:  "FileGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileGenDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessAmcUserBatchRequestValidationError{
				field:  "FileGenDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BatchNumber

	// no validation rules for UserBaseFileS3Path

	if len(errors) > 0 {
		return ProcessAmcUserBatchRequestMultiError(errors)
	}

	return nil
}

// ProcessAmcUserBatchRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessAmcUserBatchRequest.ValidateAll() if
// the designated constraints aren't met.
type ProcessAmcUserBatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessAmcUserBatchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessAmcUserBatchRequestMultiError) AllErrors() []error { return m }

// ProcessAmcUserBatchRequestValidationError is the validation error returned
// by ProcessAmcUserBatchRequest.Validate if the designated constraints aren't met.
type ProcessAmcUserBatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessAmcUserBatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessAmcUserBatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessAmcUserBatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessAmcUserBatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessAmcUserBatchRequestValidationError) ErrorName() string {
	return "ProcessAmcUserBatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessAmcUserBatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessAmcUserBatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessAmcUserBatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessAmcUserBatchRequestValidationError{}

// Validate checks the field values on ProcessAmcUserBatchResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessAmcUserBatchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessAmcUserBatchResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessAmcUserBatchResponseMultiError, or nil if none found.
func (m *ProcessAmcUserBatchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessAmcUserBatchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessAmcUserBatchResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessAmcUserBatchResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessAmcUserBatchResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessAmcUserBatchResponseMultiError(errors)
	}

	return nil
}

// ProcessAmcUserBatchResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessAmcUserBatchResponse.ValidateAll() if
// the designated constraints aren't met.
type ProcessAmcUserBatchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessAmcUserBatchResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessAmcUserBatchResponseMultiError) AllErrors() []error { return m }

// ProcessAmcUserBatchResponseValidationError is the validation error returned
// by ProcessAmcUserBatchResponse.Validate if the designated constraints
// aren't met.
type ProcessAmcUserBatchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessAmcUserBatchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessAmcUserBatchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessAmcUserBatchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessAmcUserBatchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessAmcUserBatchResponseValidationError) ErrorName() string {
	return "ProcessAmcUserBatchResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessAmcUserBatchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessAmcUserBatchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessAmcUserBatchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessAmcUserBatchResponseValidationError{}

// Validate checks the field values on CreateAndUploadAmcUsersBatchFileRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateAndUploadAmcUsersBatchFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateAndUploadAmcUsersBatchFileRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CreateAndUploadAmcUsersBatchFileRequestMultiError, or nil if none found.
func (m *CreateAndUploadAmcUsersBatchFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAndUploadAmcUsersBatchFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAndUploadAmcUsersBatchFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAndUploadAmcUsersBatchFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAndUploadAmcUsersBatchFileRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBatchS3PathDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAndUploadAmcUsersBatchFileRequestValidationError{
					field:  "BatchS3PathDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAndUploadAmcUsersBatchFileRequestValidationError{
					field:  "BatchS3PathDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBatchS3PathDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAndUploadAmcUsersBatchFileRequestValidationError{
				field:  "BatchS3PathDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFileGenDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAndUploadAmcUsersBatchFileRequestValidationError{
					field:  "FileGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAndUploadAmcUsersBatchFileRequestValidationError{
					field:  "FileGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileGenDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAndUploadAmcUsersBatchFileRequestValidationError{
				field:  "FileGenDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAndUploadAmcUsersBatchFileRequestMultiError(errors)
	}

	return nil
}

// CreateAndUploadAmcUsersBatchFileRequestMultiError is an error wrapping
// multiple validation errors returned by
// CreateAndUploadAmcUsersBatchFileRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAndUploadAmcUsersBatchFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAndUploadAmcUsersBatchFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAndUploadAmcUsersBatchFileRequestMultiError) AllErrors() []error { return m }

// CreateAndUploadAmcUsersBatchFileRequestValidationError is the validation
// error returned by CreateAndUploadAmcUsersBatchFileRequest.Validate if the
// designated constraints aren't met.
type CreateAndUploadAmcUsersBatchFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAndUploadAmcUsersBatchFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAndUploadAmcUsersBatchFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAndUploadAmcUsersBatchFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAndUploadAmcUsersBatchFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAndUploadAmcUsersBatchFileRequestValidationError) ErrorName() string {
	return "CreateAndUploadAmcUsersBatchFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAndUploadAmcUsersBatchFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAndUploadAmcUsersBatchFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAndUploadAmcUsersBatchFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAndUploadAmcUsersBatchFileRequestValidationError{}

// Validate checks the field values on CreateAndUploadAmcUsersBatchFileResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateAndUploadAmcUsersBatchFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateAndUploadAmcUsersBatchFileResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreateAndUploadAmcUsersBatchFileResponseMultiError, or nil if none found.
func (m *CreateAndUploadAmcUsersBatchFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAndUploadAmcUsersBatchFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAndUploadAmcUsersBatchFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAndUploadAmcUsersBatchFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAndUploadAmcUsersBatchFileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAndUploadAmcUsersBatchFileResponseMultiError(errors)
	}

	return nil
}

// CreateAndUploadAmcUsersBatchFileResponseMultiError is an error wrapping
// multiple validation errors returned by
// CreateAndUploadAmcUsersBatchFileResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateAndUploadAmcUsersBatchFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAndUploadAmcUsersBatchFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAndUploadAmcUsersBatchFileResponseMultiError) AllErrors() []error { return m }

// CreateAndUploadAmcUsersBatchFileResponseValidationError is the validation
// error returned by CreateAndUploadAmcUsersBatchFileResponse.Validate if the
// designated constraints aren't met.
type CreateAndUploadAmcUsersBatchFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAndUploadAmcUsersBatchFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAndUploadAmcUsersBatchFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAndUploadAmcUsersBatchFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAndUploadAmcUsersBatchFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAndUploadAmcUsersBatchFileResponseValidationError) ErrorName() string {
	return "CreateAndUploadAmcUsersBatchFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAndUploadAmcUsersBatchFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAndUploadAmcUsersBatchFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAndUploadAmcUsersBatchFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAndUploadAmcUsersBatchFileResponseValidationError{}

// Validate checks the field values on PostProcessAmcFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PostProcessAmcFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostProcessAmcFileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PostProcessAmcFileRequestMultiError, or nil if none found.
func (m *PostProcessAmcFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PostProcessAmcFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostProcessAmcFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostProcessAmcFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostProcessAmcFileRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBatchS3PathDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PostProcessAmcFileRequestValidationError{
						field:  fmt.Sprintf("BatchS3PathDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PostProcessAmcFileRequestValidationError{
						field:  fmt.Sprintf("BatchS3PathDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PostProcessAmcFileRequestValidationError{
					field:  fmt.Sprintf("BatchS3PathDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetFileGenDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostProcessAmcFileRequestValidationError{
					field:  "FileGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostProcessAmcFileRequestValidationError{
					field:  "FileGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileGenDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostProcessAmcFileRequestValidationError{
				field:  "FileGenDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PostProcessAmcFileRequestMultiError(errors)
	}

	return nil
}

// PostProcessAmcFileRequestMultiError is an error wrapping multiple validation
// errors returned by PostProcessAmcFileRequest.ValidateAll() if the
// designated constraints aren't met.
type PostProcessAmcFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostProcessAmcFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostProcessAmcFileRequestMultiError) AllErrors() []error { return m }

// PostProcessAmcFileRequestValidationError is the validation error returned by
// PostProcessAmcFileRequest.Validate if the designated constraints aren't met.
type PostProcessAmcFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostProcessAmcFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostProcessAmcFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostProcessAmcFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostProcessAmcFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostProcessAmcFileRequestValidationError) ErrorName() string {
	return "PostProcessAmcFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PostProcessAmcFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostProcessAmcFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostProcessAmcFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostProcessAmcFileRequestValidationError{}

// Validate checks the field values on PostProcessAmcFileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PostProcessAmcFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostProcessAmcFileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PostProcessAmcFileResponseMultiError, or nil if none found.
func (m *PostProcessAmcFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PostProcessAmcFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostProcessAmcFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostProcessAmcFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostProcessAmcFileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PostProcessAmcFileResponseMultiError(errors)
	}

	return nil
}

// PostProcessAmcFileResponseMultiError is an error wrapping multiple
// validation errors returned by PostProcessAmcFileResponse.ValidateAll() if
// the designated constraints aren't met.
type PostProcessAmcFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostProcessAmcFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostProcessAmcFileResponseMultiError) AllErrors() []error { return m }

// PostProcessAmcFileResponseValidationError is the validation error returned
// by PostProcessAmcFileResponse.Validate if the designated constraints aren't met.
type PostProcessAmcFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostProcessAmcFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostProcessAmcFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostProcessAmcFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostProcessAmcFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostProcessAmcFileResponseValidationError) ErrorName() string {
	return "PostProcessAmcFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PostProcessAmcFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostProcessAmcFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostProcessAmcFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostProcessAmcFileResponseValidationError{}

// Validate checks the field values on BatchS3PathDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BatchS3PathDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchS3PathDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchS3PathDetailMultiError, or nil if none found.
func (m *BatchS3PathDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchS3PathDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EligibleUsersS3Path

	// no validation rules for FailedUsersS3Path

	// no validation rules for BatchNumber

	if len(errors) > 0 {
		return BatchS3PathDetailMultiError(errors)
	}

	return nil
}

// BatchS3PathDetailMultiError is an error wrapping multiple validation errors
// returned by BatchS3PathDetail.ValidateAll() if the designated constraints
// aren't met.
type BatchS3PathDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchS3PathDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchS3PathDetailMultiError) AllErrors() []error { return m }

// BatchS3PathDetailValidationError is the validation error returned by
// BatchS3PathDetail.Validate if the designated constraints aren't met.
type BatchS3PathDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchS3PathDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchS3PathDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchS3PathDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchS3PathDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchS3PathDetailValidationError) ErrorName() string {
	return "BatchS3PathDetailValidationError"
}

// Error satisfies the builtin error interface
func (e BatchS3PathDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchS3PathDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchS3PathDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchS3PathDetailValidationError{}

// Validate checks the field values on GetAmcBatchParamsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAmcBatchParamsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAmcBatchParamsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAmcBatchParamsRequestMultiError, or nil if none found.
func (m *GetAmcBatchParamsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAmcBatchParamsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAmcBatchParamsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAmcBatchParamsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAmcBatchParamsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAmcBatchParamsRequestMultiError(errors)
	}

	return nil
}

// GetAmcBatchParamsRequestMultiError is an error wrapping multiple validation
// errors returned by GetAmcBatchParamsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAmcBatchParamsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAmcBatchParamsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAmcBatchParamsRequestMultiError) AllErrors() []error { return m }

// GetAmcBatchParamsRequestValidationError is the validation error returned by
// GetAmcBatchParamsRequest.Validate if the designated constraints aren't met.
type GetAmcBatchParamsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAmcBatchParamsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAmcBatchParamsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAmcBatchParamsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAmcBatchParamsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAmcBatchParamsRequestValidationError) ErrorName() string {
	return "GetAmcBatchParamsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAmcBatchParamsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAmcBatchParamsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAmcBatchParamsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAmcBatchParamsRequestValidationError{}

// Validate checks the field values on GetAmcBatchParamsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAmcBatchParamsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAmcBatchParamsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAmcBatchParamsResponseMultiError, or nil if none found.
func (m *GetAmcBatchParamsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAmcBatchParamsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAmcBatchParamsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAmcBatchParamsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAmcBatchParamsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BatchSize

	if len(errors) > 0 {
		return GetAmcBatchParamsResponseMultiError(errors)
	}

	return nil
}

// GetAmcBatchParamsResponseMultiError is an error wrapping multiple validation
// errors returned by GetAmcBatchParamsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetAmcBatchParamsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAmcBatchParamsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAmcBatchParamsResponseMultiError) AllErrors() []error { return m }

// GetAmcBatchParamsResponseValidationError is the validation error returned by
// GetAmcBatchParamsResponse.Validate if the designated constraints aren't met.
type GetAmcBatchParamsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAmcBatchParamsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAmcBatchParamsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAmcBatchParamsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAmcBatchParamsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAmcBatchParamsResponseValidationError) ErrorName() string {
	return "GetAmcBatchParamsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAmcBatchParamsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAmcBatchParamsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAmcBatchParamsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAmcBatchParamsResponseValidationError{}
