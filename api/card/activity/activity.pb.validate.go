// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/activity/activity.proto

package activity

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/card/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.CardRequestStatus(0)
)

// Validate checks the field values on BlockCardRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BlockCardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlockCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BlockCardRequestMultiError, or nil if none found.
func (m *BlockCardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BlockCardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BlockCardRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BlockCardRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BlockCardRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	if len(errors) > 0 {
		return BlockCardRequestMultiError(errors)
	}

	return nil
}

// BlockCardRequestMultiError is an error wrapping multiple validation errors
// returned by BlockCardRequest.ValidateAll() if the designated constraints
// aren't met.
type BlockCardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlockCardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlockCardRequestMultiError) AllErrors() []error { return m }

// BlockCardRequestValidationError is the validation error returned by
// BlockCardRequest.Validate if the designated constraints aren't met.
type BlockCardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlockCardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlockCardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlockCardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlockCardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlockCardRequestValidationError) ErrorName() string { return "BlockCardRequestValidationError" }

// Error satisfies the builtin error interface
func (e BlockCardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlockCardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlockCardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlockCardRequestValidationError{}

// Validate checks the field values on BlockCardResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BlockCardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlockCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BlockCardResponseMultiError, or nil if none found.
func (m *BlockCardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BlockCardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BlockCardResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BlockCardResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BlockCardResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BlockCardResponseMultiError(errors)
	}

	return nil
}

// BlockCardResponseMultiError is an error wrapping multiple validation errors
// returned by BlockCardResponse.ValidateAll() if the designated constraints
// aren't met.
type BlockCardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlockCardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlockCardResponseMultiError) AllErrors() []error { return m }

// BlockCardResponseValidationError is the validation error returned by
// BlockCardResponse.Validate if the designated constraints aren't met.
type BlockCardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlockCardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlockCardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlockCardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlockCardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlockCardResponseValidationError) ErrorName() string {
	return "BlockCardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BlockCardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlockCardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlockCardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlockCardResponseValidationError{}

// Validate checks the field values on CardRequestUpdateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardRequestUpdateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardRequestUpdateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardRequestUpdateRequestMultiError, or nil if none found.
func (m *CardRequestUpdateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CardRequestUpdateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestUpdateRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestUpdateRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestUpdateRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestUpdateRequestValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestUpdateRequestValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestUpdateRequestValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetWorkflowStageEnum()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestUpdateRequestValidationError{
					field:  "WorkflowStageEnum",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestUpdateRequestValidationError{
					field:  "WorkflowStageEnum",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWorkflowStageEnum()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestUpdateRequestValidationError{
				field:  "WorkflowStageEnum",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardRequestDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestUpdateRequestValidationError{
					field:  "CardRequestDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestUpdateRequestValidationError{
					field:  "CardRequestDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardRequestDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestUpdateRequestValidationError{
				field:  "CardRequestDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardRequestStage

	if len(errors) > 0 {
		return CardRequestUpdateRequestMultiError(errors)
	}

	return nil
}

// CardRequestUpdateRequestMultiError is an error wrapping multiple validation
// errors returned by CardRequestUpdateRequest.ValidateAll() if the designated
// constraints aren't met.
type CardRequestUpdateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardRequestUpdateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardRequestUpdateRequestMultiError) AllErrors() []error { return m }

// CardRequestUpdateRequestValidationError is the validation error returned by
// CardRequestUpdateRequest.Validate if the designated constraints aren't met.
type CardRequestUpdateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardRequestUpdateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardRequestUpdateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardRequestUpdateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardRequestUpdateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardRequestUpdateRequestValidationError) ErrorName() string {
	return "CardRequestUpdateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CardRequestUpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardRequestUpdateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardRequestUpdateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardRequestUpdateRequestValidationError{}

// Validate checks the field values on CardRequestUpdateResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardRequestUpdateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardRequestUpdateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardRequestUpdateResponseMultiError, or nil if none found.
func (m *CardRequestUpdateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CardRequestUpdateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestUpdateResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestUpdateResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestUpdateResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestUpdateResponseValidationError{
					field:  "CardRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestUpdateResponseValidationError{
					field:  "CardRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestUpdateResponseValidationError{
				field:  "CardRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardRequestUpdateResponseMultiError(errors)
	}

	return nil
}

// CardRequestUpdateResponseMultiError is an error wrapping multiple validation
// errors returned by CardRequestUpdateResponse.ValidateAll() if the
// designated constraints aren't met.
type CardRequestUpdateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardRequestUpdateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardRequestUpdateResponseMultiError) AllErrors() []error { return m }

// CardRequestUpdateResponseValidationError is the validation error returned by
// CardRequestUpdateResponse.Validate if the designated constraints aren't met.
type CardRequestUpdateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardRequestUpdateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardRequestUpdateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardRequestUpdateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardRequestUpdateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardRequestUpdateResponseValidationError) ErrorName() string {
	return "CardRequestUpdateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CardRequestUpdateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardRequestUpdateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardRequestUpdateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardRequestUpdateResponseValidationError{}

// Validate checks the field values on CreateNewCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateNewCardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNewCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateNewCardRequestMultiError, or nil if none found.
func (m *CreateNewCardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNewCardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNewCardRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNewCardRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNewCardRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateNewCardRequestMultiError(errors)
	}

	return nil
}

// CreateNewCardRequestMultiError is an error wrapping multiple validation
// errors returned by CreateNewCardRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateNewCardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNewCardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNewCardRequestMultiError) AllErrors() []error { return m }

// CreateNewCardRequestValidationError is the validation error returned by
// CreateNewCardRequest.Validate if the designated constraints aren't met.
type CreateNewCardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNewCardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNewCardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNewCardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNewCardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNewCardRequestValidationError) ErrorName() string {
	return "CreateNewCardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNewCardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNewCardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNewCardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNewCardRequestValidationError{}

// Validate checks the field values on CreateNewCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateNewCardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNewCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateNewCardResponseMultiError, or nil if none found.
func (m *CreateNewCardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNewCardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNewCardResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNewCardResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNewCardResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NewCardId

	if len(errors) > 0 {
		return CreateNewCardResponseMultiError(errors)
	}

	return nil
}

// CreateNewCardResponseMultiError is an error wrapping multiple validation
// errors returned by CreateNewCardResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateNewCardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNewCardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNewCardResponseMultiError) AllErrors() []error { return m }

// CreateNewCardResponseValidationError is the validation error returned by
// CreateNewCardResponse.Validate if the designated constraints aren't met.
type CreateNewCardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNewCardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNewCardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNewCardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNewCardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNewCardResponseValidationError) ErrorName() string {
	return "CreateNewCardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNewCardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNewCardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNewCardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNewCardResponseValidationError{}

// Validate checks the field values on PollCardCreationStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PollCardCreationStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PollCardCreationStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PollCardCreationStatusRequestMultiError, or nil if none found.
func (m *PollCardCreationStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PollCardCreationStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PollCardCreationStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PollCardCreationStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PollCardCreationStatusRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	if len(errors) > 0 {
		return PollCardCreationStatusRequestMultiError(errors)
	}

	return nil
}

// PollCardCreationStatusRequestMultiError is an error wrapping multiple
// validation errors returned by PollCardCreationStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type PollCardCreationStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PollCardCreationStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PollCardCreationStatusRequestMultiError) AllErrors() []error { return m }

// PollCardCreationStatusRequestValidationError is the validation error
// returned by PollCardCreationStatusRequest.Validate if the designated
// constraints aren't met.
type PollCardCreationStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PollCardCreationStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PollCardCreationStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PollCardCreationStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PollCardCreationStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PollCardCreationStatusRequestValidationError) ErrorName() string {
	return "PollCardCreationStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PollCardCreationStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPollCardCreationStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PollCardCreationStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PollCardCreationStatusRequestValidationError{}

// Validate checks the field values on PollCardCreationStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PollCardCreationStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PollCardCreationStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PollCardCreationStatusResponseMultiError, or nil if none found.
func (m *PollCardCreationStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PollCardCreationStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PollCardCreationStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PollCardCreationStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PollCardCreationStatusResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PollCardCreationStatusResponseMultiError(errors)
	}

	return nil
}

// PollCardCreationStatusResponseMultiError is an error wrapping multiple
// validation errors returned by PollCardCreationStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type PollCardCreationStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PollCardCreationStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PollCardCreationStatusResponseMultiError) AllErrors() []error { return m }

// PollCardCreationStatusResponseValidationError is the validation error
// returned by PollCardCreationStatusResponse.Validate if the designated
// constraints aren't met.
type PollCardCreationStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PollCardCreationStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PollCardCreationStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PollCardCreationStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PollCardCreationStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PollCardCreationStatusResponseValidationError) ErrorName() string {
	return "PollCardCreationStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PollCardCreationStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPollCardCreationStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PollCardCreationStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PollCardCreationStatusResponseValidationError{}

// Validate checks the field values on InitiatePhysicalCardDispatchRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InitiatePhysicalCardDispatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiatePhysicalCardDispatchRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InitiatePhysicalCardDispatchRequestMultiError, or nil if none found.
func (m *InitiatePhysicalCardDispatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiatePhysicalCardDispatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePhysicalCardDispatchRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePhysicalCardDispatchRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePhysicalCardDispatchRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePhysicalCardDispatchRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePhysicalCardDispatchRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePhysicalCardDispatchRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiatePhysicalCardDispatchRequestMultiError(errors)
	}

	return nil
}

// InitiatePhysicalCardDispatchRequestMultiError is an error wrapping multiple
// validation errors returned by
// InitiatePhysicalCardDispatchRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiatePhysicalCardDispatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiatePhysicalCardDispatchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiatePhysicalCardDispatchRequestMultiError) AllErrors() []error { return m }

// InitiatePhysicalCardDispatchRequestValidationError is the validation error
// returned by InitiatePhysicalCardDispatchRequest.Validate if the designated
// constraints aren't met.
type InitiatePhysicalCardDispatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiatePhysicalCardDispatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiatePhysicalCardDispatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiatePhysicalCardDispatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiatePhysicalCardDispatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiatePhysicalCardDispatchRequestValidationError) ErrorName() string {
	return "InitiatePhysicalCardDispatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiatePhysicalCardDispatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiatePhysicalCardDispatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiatePhysicalCardDispatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiatePhysicalCardDispatchRequestValidationError{}

// Validate checks the field values on InitiatePhysicalCardDispatchResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *InitiatePhysicalCardDispatchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiatePhysicalCardDispatchResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InitiatePhysicalCardDispatchResponseMultiError, or nil if none found.
func (m *InitiatePhysicalCardDispatchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiatePhysicalCardDispatchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePhysicalCardDispatchResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePhysicalCardDispatchResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePhysicalCardDispatchResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShouldSkipDispatchStatusPoll

	if len(errors) > 0 {
		return InitiatePhysicalCardDispatchResponseMultiError(errors)
	}

	return nil
}

// InitiatePhysicalCardDispatchResponseMultiError is an error wrapping multiple
// validation errors returned by
// InitiatePhysicalCardDispatchResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiatePhysicalCardDispatchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiatePhysicalCardDispatchResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiatePhysicalCardDispatchResponseMultiError) AllErrors() []error { return m }

// InitiatePhysicalCardDispatchResponseValidationError is the validation error
// returned by InitiatePhysicalCardDispatchResponse.Validate if the designated
// constraints aren't met.
type InitiatePhysicalCardDispatchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiatePhysicalCardDispatchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiatePhysicalCardDispatchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiatePhysicalCardDispatchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiatePhysicalCardDispatchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiatePhysicalCardDispatchResponseValidationError) ErrorName() string {
	return "InitiatePhysicalCardDispatchResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiatePhysicalCardDispatchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiatePhysicalCardDispatchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiatePhysicalCardDispatchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiatePhysicalCardDispatchResponseValidationError{}

// Validate checks the field values on PollPhysicalDispatchStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PollPhysicalDispatchStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PollPhysicalDispatchStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PollPhysicalDispatchStatusRequestMultiError, or nil if none found.
func (m *PollPhysicalDispatchStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PollPhysicalDispatchStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PollPhysicalDispatchStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PollPhysicalDispatchStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PollPhysicalDispatchStatusRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	if len(errors) > 0 {
		return PollPhysicalDispatchStatusRequestMultiError(errors)
	}

	return nil
}

// PollPhysicalDispatchStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// PollPhysicalDispatchStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type PollPhysicalDispatchStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PollPhysicalDispatchStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PollPhysicalDispatchStatusRequestMultiError) AllErrors() []error { return m }

// PollPhysicalDispatchStatusRequestValidationError is the validation error
// returned by PollPhysicalDispatchStatusRequest.Validate if the designated
// constraints aren't met.
type PollPhysicalDispatchStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PollPhysicalDispatchStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PollPhysicalDispatchStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PollPhysicalDispatchStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PollPhysicalDispatchStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PollPhysicalDispatchStatusRequestValidationError) ErrorName() string {
	return "PollPhysicalDispatchStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PollPhysicalDispatchStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPollPhysicalDispatchStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PollPhysicalDispatchStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PollPhysicalDispatchStatusRequestValidationError{}

// Validate checks the field values on PollPhysicalDispatchStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PollPhysicalDispatchStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PollPhysicalDispatchStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PollPhysicalDispatchStatusResponseMultiError, or nil if none found.
func (m *PollPhysicalDispatchStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PollPhysicalDispatchStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PollPhysicalDispatchStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PollPhysicalDispatchStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PollPhysicalDispatchStatusResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PollPhysicalDispatchStatusResponseMultiError(errors)
	}

	return nil
}

// PollPhysicalDispatchStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// PollPhysicalDispatchStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type PollPhysicalDispatchStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PollPhysicalDispatchStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PollPhysicalDispatchStatusResponseMultiError) AllErrors() []error { return m }

// PollPhysicalDispatchStatusResponseValidationError is the validation error
// returned by PollPhysicalDispatchStatusResponse.Validate if the designated
// constraints aren't met.
type PollPhysicalDispatchStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PollPhysicalDispatchStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PollPhysicalDispatchStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PollPhysicalDispatchStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PollPhysicalDispatchStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PollPhysicalDispatchStatusResponseValidationError) ErrorName() string {
	return "PollPhysicalDispatchStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PollPhysicalDispatchStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPollPhysicalDispatchStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PollPhysicalDispatchStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PollPhysicalDispatchStatusResponseValidationError{}
