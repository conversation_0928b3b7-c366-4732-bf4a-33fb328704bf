// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/activity/forextransactionrefund/activity.proto

package forextransactionrefund

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on EnrichForexTxnRecordRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnrichForexTxnRecordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnrichForexTxnRecordRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnrichForexTxnRecordRequestMultiError, or nil if none found.
func (m *EnrichForexTxnRecordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EnrichForexTxnRecordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnrichForexTxnRecordRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnrichForexTxnRecordRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnrichForexTxnRecordRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EnrichForexTxnRecordRequestMultiError(errors)
	}

	return nil
}

// EnrichForexTxnRecordRequestMultiError is an error wrapping multiple
// validation errors returned by EnrichForexTxnRecordRequest.ValidateAll() if
// the designated constraints aren't met.
type EnrichForexTxnRecordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnrichForexTxnRecordRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnrichForexTxnRecordRequestMultiError) AllErrors() []error { return m }

// EnrichForexTxnRecordRequestValidationError is the validation error returned
// by EnrichForexTxnRecordRequest.Validate if the designated constraints
// aren't met.
type EnrichForexTxnRecordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnrichForexTxnRecordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnrichForexTxnRecordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnrichForexTxnRecordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnrichForexTxnRecordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnrichForexTxnRecordRequestValidationError) ErrorName() string {
	return "EnrichForexTxnRecordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EnrichForexTxnRecordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnrichForexTxnRecordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnrichForexTxnRecordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnrichForexTxnRecordRequestValidationError{}

// Validate checks the field values on EnrichForexTxnRecordResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnrichForexTxnRecordResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnrichForexTxnRecordResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnrichForexTxnRecordResponseMultiError, or nil if none found.
func (m *EnrichForexTxnRecordResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EnrichForexTxnRecordResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnrichForexTxnRecordResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnrichForexTxnRecordResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnrichForexTxnRecordResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EnrichForexTxnRecordResponseMultiError(errors)
	}

	return nil
}

// EnrichForexTxnRecordResponseMultiError is an error wrapping multiple
// validation errors returned by EnrichForexTxnRecordResponse.ValidateAll() if
// the designated constraints aren't met.
type EnrichForexTxnRecordResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnrichForexTxnRecordResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnrichForexTxnRecordResponseMultiError) AllErrors() []error { return m }

// EnrichForexTxnRecordResponseValidationError is the validation error returned
// by EnrichForexTxnRecordResponse.Validate if the designated constraints
// aren't met.
type EnrichForexTxnRecordResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnrichForexTxnRecordResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnrichForexTxnRecordResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnrichForexTxnRecordResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnrichForexTxnRecordResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnrichForexTxnRecordResponseValidationError) ErrorName() string {
	return "EnrichForexTxnRecordResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EnrichForexTxnRecordResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnrichForexTxnRecordResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnrichForexTxnRecordResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnrichForexTxnRecordResponseValidationError{}

// Validate checks the field values on ValidateTxnForRefundRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateTxnForRefundRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateTxnForRefundRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateTxnForRefundRequestMultiError, or nil if none found.
func (m *ValidateTxnForRefundRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateTxnForRefundRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateTxnForRefundRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateTxnForRefundRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateTxnForRefundRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderWithTransactions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateTxnForRefundRequestValidationError{
					field:  "OrderWithTransactions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateTxnForRefundRequestValidationError{
					field:  "OrderWithTransactions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderWithTransactions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateTxnForRefundRequestValidationError{
				field:  "OrderWithTransactions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateTxnForRefundRequestMultiError(errors)
	}

	return nil
}

// ValidateTxnForRefundRequestMultiError is an error wrapping multiple
// validation errors returned by ValidateTxnForRefundRequest.ValidateAll() if
// the designated constraints aren't met.
type ValidateTxnForRefundRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateTxnForRefundRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateTxnForRefundRequestMultiError) AllErrors() []error { return m }

// ValidateTxnForRefundRequestValidationError is the validation error returned
// by ValidateTxnForRefundRequest.Validate if the designated constraints
// aren't met.
type ValidateTxnForRefundRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateTxnForRefundRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateTxnForRefundRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateTxnForRefundRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateTxnForRefundRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateTxnForRefundRequestValidationError) ErrorName() string {
	return "ValidateTxnForRefundRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateTxnForRefundRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateTxnForRefundRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateTxnForRefundRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateTxnForRefundRequestValidationError{}

// Validate checks the field values on ValidateTxnForRefundResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateTxnForRefundResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateTxnForRefundResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateTxnForRefundResponseMultiError, or nil if none found.
func (m *ValidateTxnForRefundResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateTxnForRefundResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateTxnForRefundResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateTxnForRefundResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateTxnForRefundResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateTxnForRefundResponseMultiError(errors)
	}

	return nil
}

// ValidateTxnForRefundResponseMultiError is an error wrapping multiple
// validation errors returned by ValidateTxnForRefundResponse.ValidateAll() if
// the designated constraints aren't met.
type ValidateTxnForRefundResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateTxnForRefundResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateTxnForRefundResponseMultiError) AllErrors() []error { return m }

// ValidateTxnForRefundResponseValidationError is the validation error returned
// by ValidateTxnForRefundResponse.Validate if the designated constraints
// aren't met.
type ValidateTxnForRefundResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateTxnForRefundResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateTxnForRefundResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateTxnForRefundResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateTxnForRefundResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateTxnForRefundResponseValidationError) ErrorName() string {
	return "ValidateTxnForRefundResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateTxnForRefundResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateTxnForRefundResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateTxnForRefundResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateTxnForRefundResponseValidationError{}

// Validate checks the field values on CalculateForexTxnRefundRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CalculateForexTxnRefundRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CalculateForexTxnRefundRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CalculateForexTxnRefundRequestMultiError, or nil if none found.
func (m *CalculateForexTxnRefundRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CalculateForexTxnRefundRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateForexTxnRefundRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateForexTxnRefundRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateForexTxnRefundRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CalculateForexTxnRefundRequestMultiError(errors)
	}

	return nil
}

// CalculateForexTxnRefundRequestMultiError is an error wrapping multiple
// validation errors returned by CalculateForexTxnRefundRequest.ValidateAll()
// if the designated constraints aren't met.
type CalculateForexTxnRefundRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CalculateForexTxnRefundRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CalculateForexTxnRefundRequestMultiError) AllErrors() []error { return m }

// CalculateForexTxnRefundRequestValidationError is the validation error
// returned by CalculateForexTxnRefundRequest.Validate if the designated
// constraints aren't met.
type CalculateForexTxnRefundRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CalculateForexTxnRefundRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CalculateForexTxnRefundRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CalculateForexTxnRefundRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CalculateForexTxnRefundRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CalculateForexTxnRefundRequestValidationError) ErrorName() string {
	return "CalculateForexTxnRefundRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CalculateForexTxnRefundRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCalculateForexTxnRefundRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CalculateForexTxnRefundRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CalculateForexTxnRefundRequestValidationError{}

// Validate checks the field values on CalculateForexTxnRefundResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CalculateForexTxnRefundResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CalculateForexTxnRefundResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CalculateForexTxnRefundResponseMultiError, or nil if none found.
func (m *CalculateForexTxnRefundResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CalculateForexTxnRefundResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateForexTxnRefundResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateForexTxnRefundResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateForexTxnRefundResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CalculateForexTxnRefundResponseMultiError(errors)
	}

	return nil
}

// CalculateForexTxnRefundResponseMultiError is an error wrapping multiple
// validation errors returned by CalculateForexTxnRefundResponse.ValidateAll()
// if the designated constraints aren't met.
type CalculateForexTxnRefundResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CalculateForexTxnRefundResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CalculateForexTxnRefundResponseMultiError) AllErrors() []error { return m }

// CalculateForexTxnRefundResponseValidationError is the validation error
// returned by CalculateForexTxnRefundResponse.Validate if the designated
// constraints aren't met.
type CalculateForexTxnRefundResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CalculateForexTxnRefundResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CalculateForexTxnRefundResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CalculateForexTxnRefundResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CalculateForexTxnRefundResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CalculateForexTxnRefundResponseValidationError) ErrorName() string {
	return "CalculateForexTxnRefundResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CalculateForexTxnRefundResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCalculateForexTxnRefundResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CalculateForexTxnRefundResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CalculateForexTxnRefundResponseValidationError{}
