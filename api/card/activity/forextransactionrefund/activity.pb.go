// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/activity/forextransactionrefund/activity.proto

package forextransactionrefund

import (
	activity "github.com/epifi/be-common/api/celestial/activity"
	order "github.com/epifi/gamma/api/order"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EnrichForexTxnRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *EnrichForexTxnRecordRequest) Reset() {
	*x = EnrichForexTxnRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnrichForexTxnRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnrichForexTxnRecordRequest) ProtoMessage() {}

func (x *EnrichForexTxnRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnrichForexTxnRecordRequest.ProtoReflect.Descriptor instead.
func (*EnrichForexTxnRecordRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_forextransactionrefund_activity_proto_rawDescGZIP(), []int{0}
}

func (x *EnrichForexTxnRecordRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type EnrichForexTxnRecordResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *EnrichForexTxnRecordResponse) Reset() {
	*x = EnrichForexTxnRecordResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnrichForexTxnRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnrichForexTxnRecordResponse) ProtoMessage() {}

func (x *EnrichForexTxnRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnrichForexTxnRecordResponse.ProtoReflect.Descriptor instead.
func (*EnrichForexTxnRecordResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_forextransactionrefund_activity_proto_rawDescGZIP(), []int{1}
}

func (x *EnrichForexTxnRecordResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ValidateTxnForRefundRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader         *activity.RequestHeader      `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	OrderWithTransactions *order.OrderWithTransactions `protobuf:"bytes,2,opt,name=order_with_transactions,json=orderWithTransactions,proto3" json:"order_with_transactions,omitempty"`
}

func (x *ValidateTxnForRefundRequest) Reset() {
	*x = ValidateTxnForRefundRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateTxnForRefundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateTxnForRefundRequest) ProtoMessage() {}

func (x *ValidateTxnForRefundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateTxnForRefundRequest.ProtoReflect.Descriptor instead.
func (*ValidateTxnForRefundRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_forextransactionrefund_activity_proto_rawDescGZIP(), []int{2}
}

func (x *ValidateTxnForRefundRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ValidateTxnForRefundRequest) GetOrderWithTransactions() *order.OrderWithTransactions {
	if x != nil {
		return x.OrderWithTransactions
	}
	return nil
}

type ValidateTxnForRefundResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ValidateTxnForRefundResponse) Reset() {
	*x = ValidateTxnForRefundResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateTxnForRefundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateTxnForRefundResponse) ProtoMessage() {}

func (x *ValidateTxnForRefundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateTxnForRefundResponse.ProtoReflect.Descriptor instead.
func (*ValidateTxnForRefundResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_forextransactionrefund_activity_proto_rawDescGZIP(), []int{3}
}

func (x *ValidateTxnForRefundResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type CalculateForexTxnRefundRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *CalculateForexTxnRefundRequest) Reset() {
	*x = CalculateForexTxnRefundRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculateForexTxnRefundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateForexTxnRefundRequest) ProtoMessage() {}

func (x *CalculateForexTxnRefundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateForexTxnRefundRequest.ProtoReflect.Descriptor instead.
func (*CalculateForexTxnRefundRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_forextransactionrefund_activity_proto_rawDescGZIP(), []int{4}
}

func (x *CalculateForexTxnRefundRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type CalculateForexTxnRefundResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *CalculateForexTxnRefundResponse) Reset() {
	*x = CalculateForexTxnRefundResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculateForexTxnRefundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateForexTxnRefundResponse) ProtoMessage() {}

func (x *CalculateForexTxnRefundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateForexTxnRefundResponse.ProtoReflect.Descriptor instead.
func (*CalculateForexTxnRefundResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_forextransactionrefund_activity_proto_rawDescGZIP(), []int{5}
}

func (x *CalculateForexTxnRefundResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_card_activity_forextransactionrefund_activity_proto protoreflect.FileDescriptor

var file_api_card_activity_forextransactionrefund_activity_proto_rawDesc = []byte{
	0x0a, 0x37, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2f, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x24, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x1a,
	0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x67, 0x0a, 0x1b, 0x45,
	0x6e, 0x72, 0x69, 0x63, 0x68, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x54, 0x78, 0x6e, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x22, 0x6b, 0x0a, 0x1c, 0x45, 0x6e, 0x72, 0x69, 0x63, 0x68, 0x46, 0x6f,
	0x72, 0x65, 0x78, 0x54, 0x78, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0xbd, 0x01, 0x0a, 0x1b, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x54, 0x78,
	0x6e, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x54, 0x0a, 0x17, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x15, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x57, 0x69, 0x74, 0x68, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x6b, 0x0a, 0x1c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x54, 0x78, 0x6e,
	0x46, 0x6f, 0x72, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x6a,
	0x0a, 0x1e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x65, 0x78,
	0x54, 0x78, 0x6e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x6e, 0x0a, 0x1f, 0x43, 0x61,
	0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x54, 0x78, 0x6e, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a,
	0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69,
	0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x82, 0x01, 0x0a, 0x3f, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5a, 0x3f,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_activity_forextransactionrefund_activity_proto_rawDescOnce sync.Once
	file_api_card_activity_forextransactionrefund_activity_proto_rawDescData = file_api_card_activity_forextransactionrefund_activity_proto_rawDesc
)

func file_api_card_activity_forextransactionrefund_activity_proto_rawDescGZIP() []byte {
	file_api_card_activity_forextransactionrefund_activity_proto_rawDescOnce.Do(func() {
		file_api_card_activity_forextransactionrefund_activity_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_activity_forextransactionrefund_activity_proto_rawDescData)
	})
	return file_api_card_activity_forextransactionrefund_activity_proto_rawDescData
}

var file_api_card_activity_forextransactionrefund_activity_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_card_activity_forextransactionrefund_activity_proto_goTypes = []interface{}{
	(*EnrichForexTxnRecordRequest)(nil),     // 0: card.activity.forextransactionrefund.EnrichForexTxnRecordRequest
	(*EnrichForexTxnRecordResponse)(nil),    // 1: card.activity.forextransactionrefund.EnrichForexTxnRecordResponse
	(*ValidateTxnForRefundRequest)(nil),     // 2: card.activity.forextransactionrefund.ValidateTxnForRefundRequest
	(*ValidateTxnForRefundResponse)(nil),    // 3: card.activity.forextransactionrefund.ValidateTxnForRefundResponse
	(*CalculateForexTxnRefundRequest)(nil),  // 4: card.activity.forextransactionrefund.CalculateForexTxnRefundRequest
	(*CalculateForexTxnRefundResponse)(nil), // 5: card.activity.forextransactionrefund.CalculateForexTxnRefundResponse
	(*activity.RequestHeader)(nil),          // 6: celestial.activity.RequestHeader
	(*activity.ResponseHeader)(nil),         // 7: celestial.activity.ResponseHeader
	(*order.OrderWithTransactions)(nil),     // 8: order.OrderWithTransactions
}
var file_api_card_activity_forextransactionrefund_activity_proto_depIdxs = []int32{
	6, // 0: card.activity.forextransactionrefund.EnrichForexTxnRecordRequest.request_header:type_name -> celestial.activity.RequestHeader
	7, // 1: card.activity.forextransactionrefund.EnrichForexTxnRecordResponse.response_header:type_name -> celestial.activity.ResponseHeader
	6, // 2: card.activity.forextransactionrefund.ValidateTxnForRefundRequest.request_header:type_name -> celestial.activity.RequestHeader
	8, // 3: card.activity.forextransactionrefund.ValidateTxnForRefundRequest.order_with_transactions:type_name -> order.OrderWithTransactions
	7, // 4: card.activity.forextransactionrefund.ValidateTxnForRefundResponse.response_header:type_name -> celestial.activity.ResponseHeader
	6, // 5: card.activity.forextransactionrefund.CalculateForexTxnRefundRequest.request_header:type_name -> celestial.activity.RequestHeader
	7, // 6: card.activity.forextransactionrefund.CalculateForexTxnRefundResponse.response_header:type_name -> celestial.activity.ResponseHeader
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_card_activity_forextransactionrefund_activity_proto_init() }
func file_api_card_activity_forextransactionrefund_activity_proto_init() {
	if File_api_card_activity_forextransactionrefund_activity_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnrichForexTxnRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnrichForexTxnRecordResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateTxnForRefundRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateTxnForRefundResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculateForexTxnRefundRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_forextransactionrefund_activity_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculateForexTxnRefundResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_activity_forextransactionrefund_activity_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_activity_forextransactionrefund_activity_proto_goTypes,
		DependencyIndexes: file_api_card_activity_forextransactionrefund_activity_proto_depIdxs,
		MessageInfos:      file_api_card_activity_forextransactionrefund_activity_proto_msgTypes,
	}.Build()
	File_api_card_activity_forextransactionrefund_activity_proto = out.File
	file_api_card_activity_forextransactionrefund_activity_proto_rawDesc = nil
	file_api_card_activity_forextransactionrefund_activity_proto_goTypes = nil
	file_api_card_activity_forextransactionrefund_activity_proto_depIdxs = nil
}
