// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/activity/trackcarddelivery/activity.proto

package trackcarddelivery

import (
	activity "github.com/epifi/be-common/api/celestial/activity"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrackShipmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *TrackShipmentResponse) Reset() {
	*x = TrackShipmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_trackcarddelivery_activity_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackShipmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackShipmentResponse) ProtoMessage() {}

func (x *TrackShipmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_trackcarddelivery_activity_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackShipmentResponse.ProtoReflect.Descriptor instead.
func (*TrackShipmentResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_trackcarddelivery_activity_proto_rawDescGZIP(), []int{0}
}

func (x *TrackShipmentResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type TrackShipmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// card id
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *TrackShipmentRequest) Reset() {
	*x = TrackShipmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_trackcarddelivery_activity_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackShipmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackShipmentRequest) ProtoMessage() {}

func (x *TrackShipmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_trackcarddelivery_activity_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackShipmentRequest.ProtoReflect.Descriptor instead.
func (*TrackShipmentRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_trackcarddelivery_activity_proto_rawDescGZIP(), []int{1}
}

func (x *TrackShipmentRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *TrackShipmentRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

var File_api_card_activity_trackcarddelivery_activity_proto protoreflect.FileDescriptor

var file_api_card_activity_trackcarddelivery_activity_proto_rawDesc = []byte{
	0x0a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x63, 0x61, 0x72, 0x64, 0x64, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x63, 0x61, 0x72, 0x64, 0x64, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x64, 0x0a, 0x15, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63,
	0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x22, 0x79, 0x0a, 0x14, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x42, 0x78, 0x0a, 0x3a, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x63, 0x61, 0x72,
	0x64, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5a, 0x3a, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x63, 0x61, 0x72, 0x64, 0x64, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_activity_trackcarddelivery_activity_proto_rawDescOnce sync.Once
	file_api_card_activity_trackcarddelivery_activity_proto_rawDescData = file_api_card_activity_trackcarddelivery_activity_proto_rawDesc
)

func file_api_card_activity_trackcarddelivery_activity_proto_rawDescGZIP() []byte {
	file_api_card_activity_trackcarddelivery_activity_proto_rawDescOnce.Do(func() {
		file_api_card_activity_trackcarddelivery_activity_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_activity_trackcarddelivery_activity_proto_rawDescData)
	})
	return file_api_card_activity_trackcarddelivery_activity_proto_rawDescData
}

var file_api_card_activity_trackcarddelivery_activity_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_card_activity_trackcarddelivery_activity_proto_goTypes = []interface{}{
	(*TrackShipmentResponse)(nil),   // 0: card.activity.trackcarddelivery.TrackShipmentResponse
	(*TrackShipmentRequest)(nil),    // 1: card.activity.trackcarddelivery.TrackShipmentRequest
	(*activity.ResponseHeader)(nil), // 2: celestial.activity.ResponseHeader
	(*activity.RequestHeader)(nil),  // 3: celestial.activity.RequestHeader
}
var file_api_card_activity_trackcarddelivery_activity_proto_depIdxs = []int32{
	2, // 0: card.activity.trackcarddelivery.TrackShipmentResponse.response_header:type_name -> celestial.activity.ResponseHeader
	3, // 1: card.activity.trackcarddelivery.TrackShipmentRequest.request_header:type_name -> celestial.activity.RequestHeader
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_card_activity_trackcarddelivery_activity_proto_init() }
func file_api_card_activity_trackcarddelivery_activity_proto_init() {
	if File_api_card_activity_trackcarddelivery_activity_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_activity_trackcarddelivery_activity_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackShipmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_trackcarddelivery_activity_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackShipmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_activity_trackcarddelivery_activity_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_activity_trackcarddelivery_activity_proto_goTypes,
		DependencyIndexes: file_api_card_activity_trackcarddelivery_activity_proto_depIdxs,
		MessageInfos:      file_api_card_activity_trackcarddelivery_activity_proto_msgTypes,
	}.Build()
	File_api_card_activity_trackcarddelivery_activity_proto = out.File
	file_api_card_activity_trackcarddelivery_activity_proto_rawDesc = nil
	file_api_card_activity_trackcarddelivery_activity_proto_goTypes = nil
	file_api_card_activity_trackcarddelivery_activity_proto_depIdxs = nil
}
