// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/activity/trackcarddelivery/activity.proto

package trackcarddelivery

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TrackShipmentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TrackShipmentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TrackShipmentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TrackShipmentResponseMultiError, or nil if none found.
func (m *TrackShipmentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TrackShipmentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TrackShipmentResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TrackShipmentResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TrackShipmentResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TrackShipmentResponseMultiError(errors)
	}

	return nil
}

// TrackShipmentResponseMultiError is an error wrapping multiple validation
// errors returned by TrackShipmentResponse.ValidateAll() if the designated
// constraints aren't met.
type TrackShipmentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TrackShipmentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TrackShipmentResponseMultiError) AllErrors() []error { return m }

// TrackShipmentResponseValidationError is the validation error returned by
// TrackShipmentResponse.Validate if the designated constraints aren't met.
type TrackShipmentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TrackShipmentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TrackShipmentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TrackShipmentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TrackShipmentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TrackShipmentResponseValidationError) ErrorName() string {
	return "TrackShipmentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TrackShipmentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTrackShipmentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TrackShipmentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TrackShipmentResponseValidationError{}

// Validate checks the field values on TrackShipmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TrackShipmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TrackShipmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TrackShipmentRequestMultiError, or nil if none found.
func (m *TrackShipmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TrackShipmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TrackShipmentRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TrackShipmentRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TrackShipmentRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	if len(errors) > 0 {
		return TrackShipmentRequestMultiError(errors)
	}

	return nil
}

// TrackShipmentRequestMultiError is an error wrapping multiple validation
// errors returned by TrackShipmentRequest.ValidateAll() if the designated
// constraints aren't met.
type TrackShipmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TrackShipmentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TrackShipmentRequestMultiError) AllErrors() []error { return m }

// TrackShipmentRequestValidationError is the validation error returned by
// TrackShipmentRequest.Validate if the designated constraints aren't met.
type TrackShipmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TrackShipmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TrackShipmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TrackShipmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TrackShipmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TrackShipmentRequestValidationError) ErrorName() string {
	return "TrackShipmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TrackShipmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTrackShipmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TrackShipmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TrackShipmentRequestValidationError{}
